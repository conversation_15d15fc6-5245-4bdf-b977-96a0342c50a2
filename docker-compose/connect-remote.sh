#!/bin/bash
#ALL_SERVICES=("nu-bom-kotlin" "nu-bomrads" "nu-ledge" "bom-importer" "nu-cost-models-dca" "nu-cost-models-inj" "nu-cost-models-sand" "nu-cost-turning" "nu-cost-turning-drilling")
ALL_SERVICES=("nu-bomrads" "nu-ledge" "bom-importer" "nu-cost-models-dca" "nu-cost-models-inj" "nu-cost-models-sand" "nu-cost-turning" "nu-static-assets" "gattierungsrechner" "wsoptics" "nu-cost-del")

parseParams(){
  PARAMS=("$@")
  unset PARAMS[0]
  FEATURE_NAME=""
  SERVICES=()
  IN_FEATURE=0
  IN_SERVICE=0
  for var in "${PARAMS[@]}"
  do
    if [ $var == "-f" ]; then
      IN_FEATURE=1
      IN_SERVICE=0
    elif [ $var == "-s" ]; then
      IN_FEATURE=0
      IN_SERVICE=1
    elif [ $IN_FEATURE -eq 1 ]; then
      FEATURE_NAME="-$var"
    elif [ $IN_SERVICE -eq 1 ]; then
      SERVICES+=("$var")
    fi
  done
}

findPort(){
  case $1 in
  "nu-masterdata")
      echo "8104"
      ;;
  "nu-bom-kotlin")
    echo "8082"
    ;;
  "nu-bomrads")
    echo "8087"
    ;;
  "nu-static-assets")
    echo "8090"
    ;;
  "nu-ledge")
    echo "8102"
    ;;
  "bom-importer")
    echo "8103"
    ;;
  "nu-cost-models-dca")
    echo "5001"
    ;;
  "nu-cost-models-inj")
    echo "5002"
    ;;
  "nu-cost-models-sand")
    echo "5003"
    ;;
  "nu-cost-turning")
    echo "3001"
    ;;
  "nu-cost-turning-drilling")
    echo "3002"
    ;;
  "gattierungsrechner")
    echo "5000"
    ;;
  "nu-cost-del")
    echo "3003"
    ;;
  "wsoptics")
    echo "3050"
    ;;
  esac
}

if [ -z "$1" ];
then
  echo "usage: $0 [start|stop] [-f feature-name] [-s nu-bom-kotlin|nu-bomrads|nu-ledge|nu-static-assets]";
  echo "  -f optional parameter for a feature branch, if you are using it, please ensure to prefix the branch with 'feature-', e.g. '-f feature-awesome-feature-branch";
  echo "  -s blank-separated list of services to start, e.g. '-s nu-bomrads nu-static-assets";
else
  case $1 in
  start)
    echo starting port forwards
    parseParams "$@"
    echo "  feature $FEATURE_NAME"
    echo "  services ${SERVICES[@]}"
    SERVICES_TO_FORWARD=(${ALL_SERVICES[@]})
    if [ ${#SERVICES[@]} != 0 ]; then
      SERVICES_TO_FORWARD=(${SERVICES[@]})
    fi
    echo "    services to forward ${SERVICES_TO_FORWARD[@]}"

    for service in "${SERVICES_TO_FORWARD[@]}"
    do
      echo "  Trying to forward port for $service"
      SERVICE_NAME="service/$service$FEATURE_NAME"
      PORT=$(findPort $service)
      FWD="$SERVICE_NAME $PORT:80"
      echo "    forwarding $FWD"
      bash -c "exec -a remoteforward kubectl port-forward --address 0.0.0.0 $FWD &"
    done
    ;;
  stop)
    echo stopping all forwards
    pkill -f remoteforward
    ;;
  *)
    echo "unknown commad, please use either start or stop"
    ;;
  esac
fi

