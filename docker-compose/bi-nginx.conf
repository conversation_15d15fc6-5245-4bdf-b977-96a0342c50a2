client_max_body_size    50m;
client_body_buffer_size 128k;
proxy_connect_timeout   180;
proxy_send_timeout      180;
proxy_read_timeout      180;
proxy_buffers           32 4k;

upstream dockerfrontend {
    server bi-frontend:80;
}

upstream dockerbackend {
    server bi-backend:8080;
}

server {
    listen 80 default_server;

    location / {
        proxy_pass          http://dockerfrontend;
        proxy_redirect      off;
        proxy_set_header    Host $host;
        proxy_set_header    X-Real-IP $remote_addr;
        proxy_set_header    X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header    X-Forwarded-Host $server_name;
    }

    location ~ ^/(api|/v3/api-docs) {
        proxy_pass          http://dockerbackend;
        proxy_redirect      off;
        proxy_set_header    Host $host;
        proxy_set_header    X-Real-IP $remote_addr;
        proxy_set_header    X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header    X-Forwarded-Host $server_name;
    }
}
