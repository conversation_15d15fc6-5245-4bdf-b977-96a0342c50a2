package com.nu.bom.core.fields

import com.nu.bom.core.manufacturing.FieldTestBase
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ManufacturingStep
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeUnit
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.technologies.manufacturings.inj.ManufacturingInjection2
import org.junit.jupiter.api.Nested

class ManufacturingStepFamilyToolingFieldTest {
    @Nested
    inner class PartsPerCycleFamilyToolingFieldTest : FieldTestBase() {
        override fun getEntity(): ManufacturingEntity = ManufacturingStep("test")

        override fun getFieldName(): String = ManufacturingStep::partsPerCycleFamilyTooling.name

        override fun testCases(): List<TestCase> =
            listOf(
                TestCase(
                    inputs =
                        listOf(
                            QuantityUnit(2.0),
                            Num(2.0),
                            SelectableBoolean.TRUE,
                        ),
                    result = QuantityUnit(4.0),
                ),
                TestCase(
                    inputs =
                        listOf(
                            QuantityUnit(2.0),
                            Num(2.0),
                            SelectableBoolean.FALSE,
                        ),
                    result = QuantityUnit(2.0),
                ),
            )
    }

    @Nested
    inner class PartsPerCycleFieldTest : FieldTestBase() {
        override fun getEntity(): ManufacturingEntity = ManufacturingStep("test")

        override fun getFieldName(): String = ManufacturingStep::partsPerCycle.name

        override fun testCases(): List<TestCase> =
            listOf(
                TestCase(
                    inputs =
                        listOf(
                            QuantityUnit(2.0),
                            Num(2.0),
                            SelectableBoolean.TRUE,
                        ),
                    result = QuantityUnit(1.0),
                ),
                TestCase(
                    inputs =
                        listOf(
                            QuantityUnit(2.0),
                            Num(2.0),
                            SelectableBoolean.FALSE,
                        ),
                    result = QuantityUnit(2.0),
                ),
            )
    }

    @Nested
    inner class FamilyAllocationRatioFieldTest : FieldTestBase() {
        override fun getEntity(): ManufacturingEntity = ManufacturingStep("test")

        override fun getFieldName(): String = ManufacturingStep::familyAllocationRatio.name

        override fun testCases(): List<TestCase> =
            listOf(
                TestCase(
                    inputs =
                        listOf(
                            SelectableBoolean.TRUE,
                            Num(3.0),
                        ),
                    result = Rate(0.5),
                ),
                TestCase(
                    inputs =
                        listOf(
                            SelectableBoolean.TRUE,
                            Num(0.0),
                        ),
                    result = Rate(1.0),
                ),
                TestCase(
                    inputs =
                        listOf(
                            SelectableBoolean.FALSE,
                            Num(3.0),
                        ),
                    result = Rate(1.0),
                ),
            )
    }

    @Nested
    inner class ManufacturingTimePerPartFamilyToolingFieldTest : FieldTestBase() {
        override fun getEntity(): ManufacturingEntity = ManufacturingStep("test")

        override fun getFieldName(): String = ManufacturingStep::manufacturingTimePerPartFamilyTooling.name

        override fun testCases(): List<TestCase> =
            listOf(
                TestCase(
                    inputs =
                        listOf(
                            CycleTime(60.0, CycleTimeUnit.SECOND),
                            QuantityUnit(2.0),
                            QuantityUnit(4.0),
                            SelectableBoolean.TRUE,
                        ),
                    result = Time(15.0, TimeUnits.SECOND),
                ),
                TestCase(
                    inputs =
                        listOf(
                            CycleTime(60.0, CycleTimeUnit.SECOND),
                            QuantityUnit(2.0),
                            QuantityUnit(4.0),
                            SelectableBoolean.FALSE,
                        ),
                    result = Time(30.0, TimeUnits.SECOND),
                ),
            )
    }

    @Nested
    inner class InternalCallsPerYearImplFieldTest : FieldTestBase() {
        override fun getEntity(): ManufacturingEntity = ManufacturingInjection2("test")

        override fun getFieldName(): String = ManufacturingInjection2::internalCallsPerYear.name

        override fun testCases(): List<TestCase> =
            listOf(
                TestCase(
                    inputs =
                        listOf(
                            QuantityUnit(15000.0),
                            SelectableBoolean.FALSE,
                            Num(1.0),
                        ),
                    result = QuantityUnit(4.0),
                ),
                TestCase(
                    inputs =
                        listOf(
                            QuantityUnit(15000.0),
                            SelectableBoolean.TRUE,
                            Num(1.0),
                        ),
                    result = QuantityUnit(6.0),
                ),
            )
    }
}
