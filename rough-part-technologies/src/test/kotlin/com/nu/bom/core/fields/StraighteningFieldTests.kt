package com.nu.bom.core.fields

import com.nu.bom.core.manufacturing.FieldTestBase
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeUnit
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Speed
import com.nu.bom.core.manufacturing.fieldTypes.SpeedUnits
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeStraightening
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.technologies.steps.straightening.ManufacturingStepStraightening
import com.nu.bom.core.technologies.steps.straightening.cycletimestep.StraighteningLoadingUnloadingCycleTimeStep
import com.nu.bom.core.technologies.steps.straightening.cycletimestep.StraighteningPassThroughCycleTimeStep
import com.nu.bom.core.technologies.steps.straightening.cycletimestep.StraighteningPressingCycleTimeStep
import org.junit.jupiter.api.Nested

class StraighteningFieldTests {
    @Nested
    inner class StraighteningTemplateFieldTest : FieldTestBase() {
        override fun getEntity(): ManufacturingEntity = ManufacturingStepStraightening("test")
        override fun getFieldName(): String = "templateName"
        override fun testCases(): List<TestCase> {
            return listOf(
                TestCase(
                    inputs = listOf(
                        QuantityUnit(11.0), // netWeightPerPart,
                        StepSubTypeStraightening(StepSubTypeStraightening.Selection.PASS_THROUGH), // straighteningType,
                    ),
                    result = Text("Straightening_PassThrough"),
                ),
                TestCase(
                    inputs = listOf(
                        QuantityUnit(11.0), // netWeightPerPart,
                        StepSubTypeStraightening(StepSubTypeStraightening.Selection.PRESSING), // straighteningType,
                    ),
                    result = Text("Straightening_Pressing_smallParts"),
                ),
                TestCase(
                    inputs = listOf(
                        QuantityUnit(14.0), // netWeightPerPart,
                        StepSubTypeStraightening(StepSubTypeStraightening.Selection.PRESSING), // straighteningType,
                    ),
                    result = Text("Straightening_Pressing_bigParts"),
                ),
            )
        }
    }

    @Nested
    inner class StraighteningPassThroughCycleTimeFieldTest : FieldTestBase() {
        override fun getEntity(): ManufacturingEntity = StraighteningPassThroughCycleTimeStep("test")
        override fun getFieldName(): String = "time"
        override fun testCases(): List<TestCase> {
            return listOf(
                TestCase(
                    inputs = listOf(
                        Length(70.0, LengthUnits.MILLIMETER), // unfoldedPartLength
                        Speed(4.0, SpeedUnits.M_PER_MIN), // beltSpeed
                    ),
                    result = CycleTime(0.0175, CycleTimeUnit.MINUTE),
                ),
                TestCase(
                    inputs = listOf(
                        Length(0.5, LengthUnits.METER), // unfoldedPartLength
                        Speed(5.0, SpeedUnits.M_PER_MIN), // beltSpeed
                    ),
                    result = CycleTime(0.1, CycleTimeUnit.MINUTE),
                ),
            )
        }
    }

    @Nested
    inner class StraighteningPressingCycleTimeFieldTest : FieldTestBase() {
        override fun getEntity(): ManufacturingEntity = StraighteningPressingCycleTimeStep("test")
        override fun getFieldName(): String = "time"
        override fun testCases(): List<TestCase> {
            return listOf(
                TestCase(
                    inputs = listOf(
                        Text("Straightening_Pressing_smallParts"), // templateName
                    ),
                    result = CycleTime(3.0, CycleTimeUnit.SECOND),
                ),
                TestCase(
                    inputs = listOf(
                        Text("Straightening_Pressing_bigParts"), // templateName
                    ),
                    result = CycleTime(5.0, CycleTimeUnit.SECOND),
                ),
            )
        }
    }

    @Nested
    inner class StraighteningLoadingUnloadingCycleTimeFieldTest : FieldTestBase() {
        override fun getEntity(): ManufacturingEntity = StraighteningLoadingUnloadingCycleTimeStep("test")
        override fun getFieldName(): String = "time"
        override fun testCases(): List<TestCase> {
            return listOf(
                TestCase(
                    inputs = listOf(
                        QuantityUnit(10.0), // netWeightPerPart
                    ),
                    result = CycleTime(12.0, CycleTimeUnit.SECOND),
                ),
                TestCase(
                    inputs = listOf(
                        QuantityUnit(15.0), // netWeightPerPart
                    ),
                    result = CycleTime(30.6, CycleTimeUnit.SECOND),
                ),
            )
        }
    }
}
