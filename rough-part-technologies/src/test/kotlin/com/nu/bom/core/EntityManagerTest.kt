package com.nu.bom.core

import com.nu.bom.core.manufacturing.MetaCache
import com.nu.bom.core.manufacturing.entities.RawMaterial
import com.nu.bom.core.manufacturing.entities.RawMaterialBar
import com.nu.bom.core.manufacturing.entities.RawMaterialCastingAlloy
import com.nu.bom.core.manufacturing.entities.RawMaterialLamella
import com.nu.bom.core.manufacturing.entities.RawMaterialPipe
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.technologies.manufacturings.dfor.material.MaterialForgingDfor
import com.nu.bom.core.technologies.manufacturings.fti.material.MaterialDieStamping
import com.nu.bom.core.technologies.manufacturings.inj.material.MaterialPlastic2
import com.nu.bom.core.technologies.manufacturings.inj.material.MaterialPlasticPaint
import com.nu.bom.core.technologies.manufacturings.pbox.material.MaterialPboxInkYellow
import com.nu.bom.core.utils.EntityManager
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream
import kotlin.reflect.KClass
import kotlin.test.assertEquals

class EntityManagerTest {
    private val entityManager: EntityManager
        get() = MetaCache.entityManager

    companion object {
        @JvmStatic
        fun testcasesMaterialsMap(): Stream<Arguments> =
            mapOf(
                RawMaterial::class to null,
                RawMaterialBar::class to MasterDataType.RAW_MATERIAL_BAR,
                RawMaterialCastingAlloy::class to MasterDataType.RAW_MATERIAL_CASTING_ALLOY,
                RawMaterialLamella::class to MasterDataType.RAW_MATERIAL_LAMELLA,
                RawMaterialPipe::class to MasterDataType.RAW_MATERIAL_PIPE,
                MaterialPlasticPaint::class to MasterDataType.RAW_MATERIAL_PAINT,
                MaterialPlastic2::class to MasterDataType.RAW_MATERIAL_PLASTIC_GRANULATE,
                MaterialPboxInkYellow::class to MasterDataType.RAW_MATERIAL_INK,
                MaterialForgingDfor::class to MasterDataType.RAW_MATERIAL_BAR,
                MaterialDieStamping::class to MasterDataType.RAW_MATERIAL_COIL,
            ).map { (key, value) -> Arguments.of(key, value) }.stream()
    }

    @ParameterizedTest
    @MethodSource("testcasesMaterialsMap")
    fun `get correct master data type`(
        input: KClass<*>,
        expected: MasterDataType?,
    ) {
        assertEquals(entityManager.getMasterDataTypeRecursively(input.java.simpleName), expected)
    }
}
