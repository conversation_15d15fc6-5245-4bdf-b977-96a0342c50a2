package com.nu.bom.core.dca

import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.CastingAlloyMaterialGroup
import com.nu.bom.core.manufacturing.fieldTypes.Force
import com.nu.bom.core.manufacturing.fieldTypes.ForceUnits
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.technologies.lookups.DieCastingTemplates
import com.nu.bom.core.technologies.steps.dca.ManufacturingStepDieCasting
import com.nu.bom.core.utils.AccountUtil
import com.nu.bom.core.utils.EntityCalculationVerifier.initMocks
import com.nu.bom.core.utils.EntityCalculationVerifier.mockCalculationContext
import com.nu.bom.core.utils.EntityCalculationVerifier.mockLookup
import com.nu.bom.core.utils.EntityCalculationVerifier.mockLookupObjects
import com.nu.bom.core.utils.EntityCalculationVerifier.verifyError
import com.nu.bom.core.utils.EntityCalculationVerifier.verifyValue
import org.bson.types.ObjectId
import org.junit.jupiter.api.Test

class ManufacturingStepDieCastingTests {
    @Test
    fun templateNameStringStyle() {
        val accessCheck = AccountUtil.dummyAccessCheck()

        val mockTemplates =
            listOf(
                // templateName,lockingForce,vacuum,china,mountingPlateWidth,mountingPlateHeight,magnesium,maxShotWeightPerCycle
                listOf("DieCasting_22000kN_vacuum", "********", "TRUE", "FALSE", "2.16", "2.16", "FALSE", "39"),
                listOf("DieCasting_27000kN_vacuum", "********", "TRUE", "FALSE", "2.39", "2.39", "FALSE", "59"),
                listOf("DieCasting_32000kN_vacuum", "********", "TRUE", "FALSE", "2.62", "2.62", "FALSE", "59"),
                listOf("DieCasting_42000kN_vacuum", "********", "TRUE", "FALSE", "2.88", "2.88", "FALSE", "66"),
            )

        val step =
            ManufacturingStepDieCasting("test")
                .initMocks()
                // using plain rows (templateReader is also tested)
                .mockLookup("ManufacturingStepDieCasting_Templates", mockTemplates)
                .mockCalculationContext(accessCheck = accessCheck)

        val objectId = ObjectId()
        step
            .templateName(
                useVacuum = Bool(true),
                necessaryClampingForce = Force(23000000.0, ForceUnits.NEWTON),
                value = mapOf(objectId to Text("1")),
                costFactorLocations = mapOf(objectId to Text("Egypt")),
                location = Text("Egypt"),
                toolLength = Length(2.5, LengthUnits.METER),
                toolWidth = Length(2.5, LengthUnits.METER),
                materialGroup = CastingAlloyMaterialGroup.N1,
            )
            .verifyValue(Text("DieCasting_32000kN_vacuum"))

        step
            .templateName(
                useVacuum = Bool(true),
                necessaryClampingForce = Force(********.0, ForceUnits.NEWTON),
                value = mapOf(objectId to Text("1")),
                costFactorLocations = mapOf(objectId to Text("Egypt")),
                location = Text("Egypt"),
                toolLength = Length(0.72, LengthUnits.METER),
                toolWidth = Length(0.72, LengthUnits.METER),
                materialGroup = CastingAlloyMaterialGroup.N6,
            )
            .verifyError()
    }

    @Test
    fun templateNameObjectStyle() {
        val accessCheck = AccountUtil.dummyAccessCheck()

        val mockTemplateA =
            DieCastingTemplates(
                templateName = "MockTemplate",
                lockingForce = 2600000.toBigDecimal(),
                vacuum = false,
                china = false,
                mountingPlateWidth = Length(0.83, LengthUnits.METER),
                mountingPlateHeight = Length(0.83, LengthUnits.METER),
                magnesium = true,
            )
        val objectId = ObjectId()
        ManufacturingStepDieCasting("test")
            .initMocks()
            // using object-style mocking (templateReader is bypassed)
            .mockLookupObjects("ManufacturingStepDieCasting_Templates", mockTemplateA)
            .mockCalculationContext(accessCheck)
            .templateName(
                useVacuum = Bool(false),
                necessaryClampingForce = Force(2200000.0, ForceUnits.NEWTON),
                value = mapOf(objectId to Text("1")),
                costFactorLocations = mapOf(objectId to Text("Egypt")),
                location = Text("Egypt"),
                toolLength = Length(0.72, LengthUnits.METER),
                toolWidth = Length(0.72, LengthUnits.METER),
                materialGroup = CastingAlloyMaterialGroup.N6,
            ).verifyValue(Text("MockTemplate"))
    }
}
