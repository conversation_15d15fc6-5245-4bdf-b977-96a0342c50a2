package com.nu.bom.core.technologies.steps

import com.nu.bom.core.manufacturing.fieldTypes.InputGroup
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeCleannessExtension
import com.nu.bom.core.smf.FriedelNestingApiService
import com.nu.bom.core.smf.model.NestorResponse
import com.nu.bom.core.technologies.steps.cleaning.AxisAlignedProjectedAreas
import com.nu.bom.core.technologies.steps.cleaning.AxisAlignedProjectedAreasForNesting
import com.nu.bom.core.technologies.steps.cleaning.CleaningMachineAutomationType
import com.nu.bom.core.technologies.steps.cleaning.CleaningMachineTemplate
import com.nu.bom.core.technologies.steps.cleaning.CleaningMachineType
import com.nu.bom.core.technologies.steps.cleaning.CleaningUtils
import com.nu.bom.core.technologies.steps.cleaning.ProjectionData
import com.nu.bom.core.tsetdel.TsetDelApiService
import com.nu.bom.core.utils.AccountUtil
import com.tset.bom.clients.tsetdel.model.TsetDelAxisAlignedProjectedAreas
import com.tset.bom.clients.tsetdel.model.TsetDelAxisAlignedProjectedAreasResponse
import com.tset.bom.clients.tsetdel.model.TsetDelFileLocation
import com.tset.bom.clients.tsetdel.model.TsetDelPolygonWithLinesAndArcs
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import reactor.core.publisher.Mono
import reactor.test.StepVerifier
import java.math.BigDecimal
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals
import kotlin.test.assertTrue

private class TestException : Exception()

class CleaningUtilsTests {
    private val accessCheck = AccountUtil.dummyAccessCheck()

    @Mock
    private lateinit var friedelNestingApiService: FriedelNestingApiService

    @Mock
    private lateinit var tsetDelApiService: TsetDelApiService

    @BeforeEach
    fun setUp() {
        MockitoAnnotations.openMocks(this)
    }

    @Test
    fun `projected areas`() {
        val part = bigPart()
        val tsetDelResponse =
            TsetDelAxisAlignedProjectedAreasResponse(
                api_version = "1",
                polygons = part.toTsetDel(),
            )
        whenever(tsetDelApiService.axisAlignedProjectedAreas(any(), any()))
            .thenReturn(Mono.just(tsetDelResponse))

        val tsetDelFileLocation =
            TsetDelFileLocation(
                "testBucket",
                "testFileKeyEnv",
                "testFileKeyAllOther",
            )

        val projectedAreas =
            CleaningUtils.calculateAxisAlignedProjectedAreas(
                tsetDelApiService,
                tsetDelFileLocation,
                partInputGroup = InputGroup.CUBOID,
                partLength = part.x.lengthInProjectionDirection,
                partWidth = part.y.lengthInProjectionDirection,
                partHeight = part.z.lengthInProjectionDirection,
                partOuterDiameter = null,
                partUpperWidth = null,
            )

        StepVerifier
            .create(projectedAreas)
            .expectNextMatches { it == part }
            .verifyComplete()
    }

    @Test
    fun `projected areas - from bounding box`() {
        val length = Length(BigDecimal.ONE, LengthUnits.CENTIMETER)
        val width = Length(BigDecimal.TEN, LengthUnits.CENTIMETER)
        val height = length + width

        val projectedAreas = CleaningUtils.createAxisAlignedProjectedAreasFromBoundingBox(length, width, height)

        assertEquals(projectedAreas.x.lengthInProjectionDirection, length)
        assertEquals(projectedAreas.y.lengthInProjectionDirection, width)
        assertEquals(projectedAreas.z.lengthInProjectionDirection, height)

        assertThrows<IllegalArgumentException> {
            CleaningUtils.createAxisAlignedProjectedAreasFromBoundingBox(length, width, Length.ZERO)
        }
        assertThrows<IllegalArgumentException> {
            CleaningUtils.createAxisAlignedProjectedAreasFromBoundingBox(length, Length.ZERO, height)
        }
        assertThrows<IllegalArgumentException> {
            CleaningUtils.createAxisAlignedProjectedAreasFromBoundingBox(Length.ZERO, width, height)
        }
    }

    @Test
    fun `number of parts nesting`() {
        val bigBoxWeight = bigPartWeight
        val smallBoxWeight = smallPartWeight

        assertEquals(0, CleaningUtils.nestingNumberOfParts(0, bigBoxWeight, smallPartWeight))
        assertEquals(0, CleaningUtils.nestingNumberOfParts(0, smallBoxWeight, bigPartWeight))

        assertEquals(2, CleaningUtils.nestingNumberOfParts(2, bigBoxWeight, smallPartWeight))
        assertEquals(0, CleaningUtils.nestingNumberOfParts(2, smallBoxWeight, bigPartWeight))

        assertThrows<IllegalArgumentException> {
            CleaningUtils.nestingNumberOfParts(1, smallPartWeight, QuantityUnit.ZERO)
        }
    }

    @Test
    fun `template selection - exception if no template is big enough according to part dimensions`() {
        whenever(
            friedelNestingApiService.getNestingResponse(any(), any(), any(), any()),
        ).thenReturn(Mono.just(null to NestorResponse(nestedParts = 1)))

        val projectedAreas = bigPart()

        val selection = { ts: List<CleaningMachineTemplate> ->
            CleaningUtils.selectCleaningTemplate(
                accessCheck,
                friedelNestingApiService,
                projectedAreas,
                templates = ts,
                peakUsableProductionVolumePerYear = fewParts,
                netWeightPerPart = bigPartWeight,
                cleanness = StepSubTypeCleannessExtension.ULTRASONIC,
                toolComplexity = Num(BigDecimal.ONE),
                entityId = "whatever",
                exceptionOnNoResult = { TestException() },
            )
        }

        StepVerifier
            .create(selection(bigTemplates()))
            .expectNextCount(1)
            .verifyComplete()

        StepVerifier
            .create(selection(smallTemplates()))
            .expectErrorMatches { it is TestException }
            .verify()
    }

    @Test
    fun `template selection - exception if no template is big enough according to nesting service`() {
        whenever(
            friedelNestingApiService.getNestingResponse(any(), any(), any(), any()),
        ).thenReturn(Mono.just(null to NestorResponse(nestedParts = 0)))

        val projectedAreas = smallPart()
        val templates = bigTemplates()

        val selection =
            CleaningUtils.selectCleaningTemplate(
                accessCheck,
                friedelNestingApiService,
                projectedAreas,
                templates,
                peakUsableProductionVolumePerYear = fewParts,
                netWeightPerPart = smallPartWeight,
                cleanness = StepSubTypeCleannessExtension.ULTRASONIC,
                toolComplexity = Num(BigDecimal.ONE),
                entityId = "whatever",
                exceptionOnNoResult = { TestException() },
            )

        StepVerifier
            .create(selection)
            .expectErrorMatches { it is TestException }
            .verify()
    }

    @Test
    fun `template selection - exception if no template is compatible even if part would fit`() {
        whenever(
            friedelNestingApiService.getNestingResponse(any(), any(), any(), any()),
        ).thenReturn(Mono.just(null to NestorResponse(nestedParts = 1)))

        val projectedAreas = smallPart()
        val templates =
            bigTemplates()
                .filter { it.machineType == CleaningMachineType.Ultrasonic }

        val cleanness = StepSubTypeCleannessExtension.NORMAL

        val selection =
            CleaningUtils.selectCleaningTemplate(
                accessCheck,
                friedelNestingApiService,
                projectedAreas,
                templates,
                peakUsableProductionVolumePerYear = fewParts,
                netWeightPerPart = smallPartWeight,
                cleanness = cleanness,
                toolComplexity = Num(BigDecimal.ONE),
                entityId = "whatever",
                exceptionOnNoResult = { TestException() },
            )

        StepVerifier
            .create(selection)
            .expectErrorMatches { it is TestException }
            .verify()
    }

    @Test
    fun `template selection - depends on original template order`() {
        whenever(
            friedelNestingApiService.getNestingResponse(any(), any(), any(), any()),
        ).thenReturn(Mono.just(null to NestorResponse(nestedParts = 1)))

        val projectedAreas = smallPart()
        val templates = makeBigTemplates(CleaningMachineType.Ultrasonic) + makeSmallTemplates(CleaningMachineType.Ultrasonic)
        val templatesReversed = templates.reversed()

        val selectTemplate = { ts: List<CleaningMachineTemplate> ->
            CleaningUtils.selectCleaningTemplate(
                accessCheck,
                friedelNestingApiService,
                projectedAreas,
                ts,
                peakUsableProductionVolumePerYear = fewParts,
                netWeightPerPart = smallPartWeight,
                cleanness = StepSubTypeCleannessExtension.ULTRASONIC,
                toolComplexity = Num(BigDecimal.ONE),
                entityId = "whatever",
                exceptionOnNoResult = { TestException() },
            )
        }

        StepVerifier
            .create(Mono.zip(selectTemplate(templates), selectTemplate(templatesReversed)))
            .assertNext { result ->
                assertNotEquals(result.t1, result.t2)
                assertTrue(templates.indexOf(result.t1) < templates.indexOf(result.t2))
            }.verifyComplete()
    }

    @Test
    fun `template selection - prefer second of same priority if many parts`() {
        whenever(
            friedelNestingApiService.getNestingResponse(any(), any(), any(), any()),
        ).thenReturn(Mono.just(null to NestorResponse(nestedParts = 1)))

        val projectedAreas = smallPart()

        // will contain 3 matches of highest priority. 2nd and 3rd are the same
        val templates =
            makeSmallTemplates(CleaningMachineType.Ultrasonic) + makeBigTemplates(CleaningMachineType.Ultrasonic) +
                makeBigTemplates(CleaningMachineType.Ultrasonic)

        val selectTemplate = { ts: List<CleaningMachineTemplate>, productionVolume: QuantityUnit ->
            CleaningUtils.selectCleaningTemplate(
                accessCheck,
                friedelNestingApiService,
                projectedAreas,
                ts,
                peakUsableProductionVolumePerYear = productionVolume,
                netWeightPerPart = smallPartWeight,
                cleanness = StepSubTypeCleannessExtension.ULTRASONIC,
                toolComplexity = Num(BigDecimal.ONE),
                entityId = "whatever",
                exceptionOnNoResult = { TestException() },
            )
        }

        StepVerifier
            .create(Mono.zip(selectTemplate(templates, fewParts), selectTemplate(templates, manyParts)))
            .assertNext {
                val selectionFewParts = it.t1
                val selectionManyParts = it.t2

                val indexOfFewParts = templates.indexOf(selectionFewParts)
                val indexOfManyParts = templates.indexOf(selectionManyParts)
                assertTrue(indexOfFewParts < indexOfManyParts)

                // verify that out of the 3 matches of highest priority, the second one was chosen
                val indexOfLastMatchManyParts = templates.indexOfLast { it.templateName == selectionManyParts.templateName }
                assertTrue(indexOfManyParts < indexOfLastMatchManyParts)
            }.verifyComplete()
    }

    @Test
    fun `template selection - ultrasonic`() {
        whenever(
            friedelNestingApiService.getNestingResponse(any(), any(), any(), any()),
        ).thenReturn(Mono.just(null to NestorResponse(nestedParts = 1)))

        val projectedAreas = smallPart()
        val cleanness = StepSubTypeCleannessExtension.ULTRASONIC

        val makeSelection = { ts: List<CleaningMachineTemplate> ->
            CleaningUtils.selectCleaningTemplate(
                accessCheck,
                friedelNestingApiService,
                projectedAreas,
                ts,
                peakUsableProductionVolumePerYear = fewParts,
                netWeightPerPart = smallPartWeight,
                cleanness = cleanness,
                toolComplexity = Num(BigDecimal.ONE),
                entityId = "whatever",
                exceptionOnNoResult = { TestException() },
            )
        }

        val selection1 = makeSelection(bigTemplates())
        StepVerifier
            .create(selection1)
            .assertNext { assertEquals(CleaningMachineType.Ultrasonic, it.machineType) }
            .verifyComplete()

        val selection2 = makeSelection(makeBigTemplates(CleaningMachineType.Chamber))
        StepVerifier
            .create(selection2)
            .expectNextCount(1)
            .verifyComplete()

        val selection3 = makeSelection(makeBigTemplates(CleaningMachineType.Conveyor))
        StepVerifier
            .create(selection3)
            .expectErrorMatches { it is TestException }
            .verify()
    }

    @Test
    fun `template selection - normal`() {
        whenever(
            friedelNestingApiService.getNestingResponse(any(), any(), any(), any()),
        ).thenReturn(Mono.just(null to NestorResponse(nestedParts = 1)))

        val projectedAreas = smallPart()
        val cleanness = StepSubTypeCleannessExtension.NORMAL

        val makeSelection = { ts: List<CleaningMachineTemplate> ->
            CleaningUtils.selectCleaningTemplate(
                accessCheck,
                friedelNestingApiService,
                projectedAreas,
                ts,
                peakUsableProductionVolumePerYear = fewParts,
                netWeightPerPart = smallPartWeight,
                cleanness = cleanness,
                toolComplexity = Num(BigDecimal.ONE),
                entityId = "whatever",
                exceptionOnNoResult = { TestException() },
            )
        }

        val selection1 = makeSelection(bigTemplates())
        StepVerifier
            .create(selection1)
            .assertNext { assertEquals(CleaningMachineType.Chamber, it.machineType) }
            .verifyComplete()

        val selection2 = makeSelection(makeBigTemplates(CleaningMachineType.Conveyor))
        StepVerifier
            .create(selection2)
            .expectNextCount(1)
            .verifyComplete()

        val selection3 = makeSelection(makeBigTemplates(CleaningMachineType.Ultrasonic) + makeBigTemplates(CleaningMachineType.HdCleaning))
        StepVerifier
            .create(selection3)
            .expectErrorMatches { it is TestException }
            .verify()
    }

    @Test
    fun `template selection - hd-cleaning`() {
        whenever(
            friedelNestingApiService.getNestingResponse(any(), any(), any(), any()),
        ).thenReturn(Mono.just(null to NestorResponse(nestedParts = 1)))

        val projectedAreas = smallPart()
        val cleanness = StepSubTypeCleannessExtension.HD_CLEANING

        val makeSelection = { ts: List<CleaningMachineTemplate> ->
            CleaningUtils.selectCleaningTemplate(
                accessCheck,
                friedelNestingApiService,
                projectedAreas,
                ts,
                peakUsableProductionVolumePerYear = fewParts,
                netWeightPerPart = smallPartWeight,
                cleanness = cleanness,
                toolComplexity = Num(BigDecimal.ONE),
                entityId = "whatever",
                exceptionOnNoResult = { TestException() },
            )
        }

        val selection1 = makeSelection(bigTemplates())
        StepVerifier
            .create(selection1)
            .assertNext { assertEquals(it.machineType, CleaningMachineType.HdCleaning) }
            .verifyComplete()

        val selection2 = makeSelection(makeBigTemplates(CleaningMachineType.Chamber))
        StepVerifier
            .create(selection2)
            .expectNextCount(1)
            .verifyComplete()

        val selection3 = makeSelection(makeBigTemplates(CleaningMachineType.Ultrasonic) + makeBigTemplates(CleaningMachineType.Conveyor))
        StepVerifier
            .create(selection3)
            .expectErrorMatches { it is TestException }
            .verify()
    }

    @Test
    fun `template selection - nesting part too big`() {
        whenever(
            friedelNestingApiService.getNestingResponse(any(), any(), any(), any()),
        ).thenReturn(Mono.just(null to NestorResponse(nestedParts = 1)))

        val projectedAreas = AxisAlignedProjectedAreasForNesting(bigPart())

        val nesting =
            CleaningUtils.nesting(
                accessCheck,
                friedelNestingApiService,
                projectedAreas,
                chargeFrameLength = Length(BigDecimal.ONE, LengthUnits.MILLIMETER),
                chargeFrameWidth = Length(BigDecimal.ONE, LengthUnits.MILLIMETER),
                chargeFrameHeight = Length(BigDecimal.ONE, LengthUnits.MILLIMETER),
                distanceBetweenParts = Length.ZERO,
                distanceToSheetEdge = Length.ZERO,
                uploadNestingImage = false,
                entityId = "whatever",
            )

        StepVerifier
            .create(nesting)
            .verifyComplete()
    }

    @Test
    fun `template selection - nesting part fits`() {
        whenever(
            friedelNestingApiService.getNestingResponse(any(), any(), any(), any()),
        ).thenReturn(Mono.just(null to NestorResponse(nestedParts = 1)))

        val projectedAreas = AxisAlignedProjectedAreasForNesting(smallPart())

        val nesting =
            CleaningUtils.nesting(
                accessCheck,
                friedelNestingApiService,
                projectedAreas,
                chargeFrameLength = Length(BigDecimal.ONE, LengthUnits.METER),
                chargeFrameWidth = Length(BigDecimal.ONE, LengthUnits.METER),
                chargeFrameHeight = Length(BigDecimal.ONE, LengthUnits.METER),
                distanceBetweenParts = Length.ZERO,
                distanceToSheetEdge = Length.ZERO,
                uploadNestingImage = false,
                entityId = "whatever",
            )

        StepVerifier
            .create(nesting)
            .expectNextCount(1)
            .verifyComplete()
    }

    companion object {
        private fun AxisAlignedProjectedAreas.toTsetDel() =
            TsetDelAxisAlignedProjectedAreas(
                x = this.x.projectedArea,
                y = this.y.projectedArea,
                z = this.z.projectedArea,
            )

        private fun getBoxAreas(
            length: Double,
            width: Double,
            height: Double,
        ): AxisAlignedProjectedAreas =
            AxisAlignedProjectedAreas(
                x =
                    ProjectionData(
                        projectedArea = listOf(TsetDelPolygonWithLinesAndArcs.rectangle(length, width, false)),
                        Length(height, LengthUnits.MILLIMETER),
                    ),
                y =
                    ProjectionData(
                        projectedArea = listOf(TsetDelPolygonWithLinesAndArcs.rectangle(length, height, false)),
                        Length(width, LengthUnits.MILLIMETER),
                    ),
                z =
                    ProjectionData(
                        projectedArea = listOf(TsetDelPolygonWithLinesAndArcs.rectangle(width, height, false)),
                        Length(length, LengthUnits.MILLIMETER),
                    ),
            )

        private val templateKinds =
            listOf(
                CleaningMachineType.Chamber,
                CleaningMachineType.Conveyor,
                CleaningMachineType.Ultrasonic,
                CleaningMachineType.HdCleaning,
            )

        private fun allTemplates() =
            templateKinds.flatMap {
                makeSmallTemplates(it) + makeBigTemplates(it)
            }

        private val automations =
            listOf(
                CleaningMachineAutomationType.Manual,
                CleaningMachineAutomationType.HalfAutomated,
                CleaningMachineAutomationType.FullyAutomated,
            )

        private fun smallPart() = getBoxAreas(10.0, 10.0, 10.0)

        private fun bigPart() = getBoxAreas(1_000.0, 1_000.0, 1_000.0)

        private val bigPartWeight = QuantityUnit(100.0)
        private val smallPartWeight = QuantityUnit(0.1)
        private val manyParts = QuantityUnit(1_000_000.0)
        private val fewParts = QuantityUnit(10_000.0)

        private fun smallTemplates() = templateKinds.flatMap { makeSmallTemplates(it) }

        private fun bigTemplates() = templateKinds.flatMap { makeBigTemplates(it) }

        private fun makeBigTemplates(kind: CleaningMachineType): List<CleaningMachineTemplate> =
            automations.map { makeBigTemplate(kind, it) }

        private fun makeSmallTemplates(kind: CleaningMachineType): List<CleaningMachineTemplate> =
            automations.map { makeSmallTemplate(kind, it) }

        private fun makeBigTemplate(
            kind: CleaningMachineType,
            automation: CleaningMachineAutomationType,
        ) = makeTemplate(
            kind,
            automation,
            2_000.0,
            2_000.0,
            2_000.0,
            1_000.0,
        )

        private fun makeSmallTemplate(
            kind: CleaningMachineType,
            automation: CleaningMachineAutomationType,
        ) = makeTemplate(
            kind,
            automation,
            100.0,
            100.0,
            100.0,
            1.0,
        )

        private fun makeTemplate(
            kind: CleaningMachineType,
            automation: CleaningMachineAutomationType,
            lengthMm: Double,
            widthMm: Double,
            heightMm: Double,
            weightKg: Double,
        ): CleaningMachineTemplate =
            CleaningMachineTemplate(
                templateName = "$kind-$lengthMm-$weightKg",
                machineType = kind,
                automation = automation,
                chargeWeight = QuantityUnit(weightKg),
                chargeFrameLength = Length(lengthMm, LengthUnits.MILLIMETER),
                chargeFrameWidth = Length(widthMm, LengthUnits.MILLIMETER),
                chargeFrameHeight = Length(heightMm, LengthUnits.MILLIMETER),
                rinsing = 1,
            )
    }
}
