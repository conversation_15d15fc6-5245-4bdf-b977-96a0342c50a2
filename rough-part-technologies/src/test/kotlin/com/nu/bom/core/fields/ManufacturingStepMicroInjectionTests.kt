package com.nu.bom.core.fields

import com.nu.bom.core.manufacturing.FieldTestBase
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.AreaUnits
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.DensityUnits
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.ListOfStrings
import com.nu.bom.core.manufacturing.fieldTypes.PartingLine
import com.nu.bom.core.manufacturing.fieldTypes.Pressure
import com.nu.bom.core.manufacturing.fieldTypes.PressureUnits
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.configuration.InjectionConfigurationField
import com.nu.bom.core.service.configurations.InjectionTsetConfigurationService
import com.nu.bom.core.technologies.steps.microinj.ManufacturingStepMicroInjection
import org.junit.jupiter.api.Nested

class ManufacturingStepMicroInjectionTests {
    private val entity: ManufacturingEntity = ManufacturingStepMicroInjection("testEntity")

    @Nested
    inner class RunnerSystemLengthCommon : FieldTestBase() {
        override fun getEntity(): ManufacturingEntity = entity

        override fun getFieldName(): String = ManufacturingStepMicroInjection::minPpcBasedOnMinShotVolume.name

        override fun testCases(): List<TestCase> =
            listOf(
                TestCase(
                    inputs =
                        listOf(
                            InjectionConfigurationField(InjectionTsetConfigurationService.defaultConfiguration),
                            QuantityUnit(1.0),
                            Density(1053.9, DensityUnits.KILOGRAM_PER_CM),
                            Pressure(50.0, PressureUnits.BAR),
                            Area(0.4, AreaUnits.QCM),
                            PartingLine(PartingLine.Selection.SIMPLE_OFFSET),
                            SelectableBoolean(SelectableBoolean.Selection.TRUE),
                            Length(10.0, LengthUnits.MILLIMETER),
                            Length(30.0, LengthUnits.MILLIMETER),
                            Length(30.0, LengthUnits.MILLIMETER),
                            Length(2.0, LengthUnits.MILLIMETER),
                            Length(2.0, LengthUnits.MILLIMETER),
                            Length(2.0, LengthUnits.MILLIMETER),
                            Length(1.2, LengthUnits.MILLIMETER),
                            QuantityUnit(0.005),
                            ListOfStrings(listOf("1", "2", "4", "8", "16", "32", "64")),
                            Dimension(Dimension.Selection.NUMBER),
                            Text("PIECE"),
                        ),
                    result = QuantityUnit(1.0),
                ),
            )
    }
}
