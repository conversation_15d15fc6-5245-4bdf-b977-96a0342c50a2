package com.nu.bom.core.fields

import com.nu.bom.core.manufacturing.FieldTestBase
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.technologies.steps.aluextrusion.tool.AluExtrusionTool
import org.junit.jupiter.api.Nested
import java.math.BigDecimal

class AluExtrusionToolFieldTests {
    @Nested
    inner class LifeTimePerToolFieldTest : FieldTestBase() {
        override fun getEntity(): ManufacturingEntity = AluExtrusionTool("test")

        override fun getFieldName(): String = AluExtrusionTool::lifetimePerTool.name

        override fun testCases(): List<TestCase> {
            return listOf(
                TestCase(
                    inputs =
                        listOf(
                            Length(BigDecimal("1000"), LengthUnits.METER),
                            Length(BigDecimal("5"), LengthUnits.METER),
                            Pieces(BigDecimal("16")),
                            QuantityUnit(Pieces(BigDecimal("4"))),
                        ),
                    result = Pieces(BigDecimal("800")),
                ),
            )
        }
    }

    @Nested
    inner class ServiceLifeInCyclesFieldTest : FieldTestBase() {
        override fun getEntity(): ManufacturingEntity = AluExtrusionTool("test")

        override fun getFieldName(): String = AluExtrusionTool::serviceLifeInCycles.name

        override fun testCases(): List<TestCase> {
            return listOf(
                TestCase(
                    inputs =
                        listOf(
                            QuantityUnit(Pieces(BigDecimal("8000"))),
                            QuantityUnit(Pieces(BigDecimal("8"))),
                        ),
                    result = Num(1000),
                ),
            )
        }
    }
}
