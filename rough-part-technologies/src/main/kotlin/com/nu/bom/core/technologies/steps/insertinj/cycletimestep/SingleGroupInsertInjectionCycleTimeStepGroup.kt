package com.nu.bom.core.technologies.steps.insertinj.cycletimestep

import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.entities.CycleTimeStepGroup
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeGrouping
import com.nu.bom.core.technologies.steps.inj.cycletimestep.ComponentInjectionAndCoolingCycleTimeStep
import com.nu.bom.core.technologies.steps.plasticinj.cycletimestep.CloseSliderInjectionCycleTimeStep
import com.nu.bom.core.technologies.steps.plasticinj.cycletimestep.OpenSliderInjectionCycleTimeStep

@EntityType(Entities.CYCLETIME_STEP_GROUP)
class SingleGroupInsertInjectionCycleTimeStepGroup(name: String) : ManufacturingEntity(name) {
    override val extends: ManufacturingEntity = CycleTimeStepGroup(name)

    fun grouping() = CycleTimeGrouping.Sequential

    @EntityCreation(Entities.CYCLETIME_STEP)
    fun createCycleTimeSteps(): List<ManufacturingEntity> {
        return listOfNotNull(
            createEntity(
                name = "Close mold",
                clazz = CloseMoldInsertInjectionCycleTimeStep::class,
                entityType = Entities.CYCLETIME_STEP
            ),
            createEntity(
                name = "Close slider",
                clazz = CloseSliderInjectionCycleTimeStep::class,
                entityType = Entities.CYCLETIME_STEP
            ),
            createEntity(
                name = "Component injection and cooling",
                clazz = ComponentInjectionAndCoolingCycleTimeStep::class,
                entityType = Entities.CYCLETIME_STEP
            ),
            createEntity(
                name = "Open slider",
                clazz = OpenSliderInjectionCycleTimeStep::class,
                entityType = Entities.CYCLETIME_STEP
            ),
            createEntity(
                name = "Open mold",
                clazz = OpenMoldInsertInjectionCycleTimeStep::class,
                entityType = Entities.CYCLETIME_STEP
            ),
            createEntity(
                name = "Unload part",
                clazz = UnloadPartInsertInjectionCycleTimeStep::class,
                entityType = Entities.CYCLETIME_STEP
            ),
            createEntity(
                name = "Load insert",
                clazz = LoadInsertInsertInjectionCycleTimeStep::class,
                entityType = Entities.CYCLETIME_STEP
            )
        )
    }
}
