package com.nu.bom.core.technologies.steps.largecasting

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.DynamicQuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeLargeCasting
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.technologies.steps.largecastingmolding.ManufacturingStepLargeCastingMolding
import reactor.core.publisher.Mono

@EntityType(Entities.MANUFACTURING_STEP)
class ManufacturingStepLargeCasting(name: String) : ManufacturingEntity(name) {
    override val extends = ManufacturingStepLargeCastingMolding(name)

    @Input
    fun scrapRate(): Rate = Rate(0.01)

    fun partPerCycleForMaterial(
        partsPerCycle: QuantityUnit,
        manufacturingDimension: Dimension,
        manufacturingQuantityUnit: Text,
    ): DynamicQuantityUnit {
        val manufacturingUnit = manufacturingDimension.res.typeUnit(manufacturingQuantityUnit.res)
        return DynamicQuantityUnit(partsPerCycle.res, manufacturingUnit.baseUnit, null)
            .withInputUnit(manufacturingUnit, null)
    }

    fun partPerCycleForManufacturingSandCasting(partPerCycleForMaterial: DynamicQuantityUnit): DynamicQuantityUnit = partPerCycleForMaterial

    @SpecialLink("Mold upper moldbox", "partsPerCycle")
    fun partsPerCycle(): QuantityUnit? = null

    @Input
    @Parent(Entities.MANUFACTURING)
    fun sandCastingLine(): StepSubTypeLargeCasting = StepSubTypeLargeCasting.SEMI_AUTOMATED

    private class TemplateLookupEntry(
        val templateName: String,
    )

    private val machineTemplateLookupReader: (row: List<String>) -> ManufacturingStepLargeCasting.TemplateLookupEntry = {
        ManufacturingStepLargeCasting.TemplateLookupEntry(
            templateName = it[0],
        )
    }

    fun template() = Text("ManufacturingStepLargeCasting_Templates")

    fun templateName(template: Text): Mono<Text> {
        return services.getLookupTable(template.res, machineTemplateLookupReader)
            .single().map { Text(it.templateName) }
    }
}
