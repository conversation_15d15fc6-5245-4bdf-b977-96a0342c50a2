package com.nu.bom.core.technologies.manufacturings.turn.material

import com.nu.bom.core.manufacturing.annotations.AlternativeNames
import com.nu.bom.core.manufacturing.annotations.EntityProvider
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.ListOfTurningSteps
import com.nu.bom.core.manufacturing.fieldTypes.Rz
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.technologies.steps.turn.ManufacturingStepBarTurning
import com.nu.bom.core.turn.model.BartInputProperties
import com.nu.bom.core.turn.model.Technology
import com.nu.bom.core.turn.model.Tolerance
import com.nu.bom.core.turn.model.ToleranceUnit
import reactor.core.publisher.Mono

@EntityType(Entities.PROCESSED_MATERIAL)
@AlternativeNames(["TurnedMaterial"])
class BarTurnedMaterial(name: String) : TurnedMaterial(name) {
    @Input
    val materialClass = Text(MaterialTurningBart::class.qualifiedName!!)

    // HACK
    val partInputGroup: Text? = null

    @EntityProvider
    fun turningStep(turningSteps: ListOfTurningSteps) =
        if (turningSteps.res.contains(ListOfTurningSteps.Selection.soft)) {
            createEntity(
                name = "ManufacturingStepSoftTurning",
                clazz = ManufacturingStepBarTurning::class,
                entityType = Entities.MANUFACTURING_STEP,
            )
        } else {
            throw Exception("The sketch looks like no soft turning at all is needed, but turning was selected as machining to do.")
        }

    fun turningSteps(
        turningProfileId: Text,
        @Parent(Entities.PROCESSED_MATERIAL) barDiameter: Length,
        @Parent(Entities.PROCESSED_MATERIAL) blankLengthOverhead: Length,
        @Parent(Entities.PROCESSED_MATERIAL) surfaceFinishBar: Rz,
    ): Mono<ListOfTurningSteps> {
        return services.getTurningCalculationSteps(
            turningProfileId = turningProfileId,
            technology =
                Technology(
                    type = "BART",
                    part_input_group = null,
                    properties =
                        BartInputProperties(
                            bar_diameter = barDiameter.inMillimeter.toDouble(),
                            margin_on_side = blankLengthOverhead.inMillimeter.toDouble(),
                            surface_finish = Tolerance(surfaceFinishBar.res.toDouble(), ToleranceUnit.Rz),
                        ),
                ),
            accessCheck = calculationContext().accessCheck,
        ).map { (_, response) ->
            ListOfTurningSteps(
                response.possible_steps.map { step ->
                    ListOfTurningSteps.Selection.valueOf(step)
                },
            )
        }
    }
}
