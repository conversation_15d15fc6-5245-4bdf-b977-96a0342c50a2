package com.nu.bom.core.technologies.lookups

import java.math.BigDecimal

data class ManufacturingStepFettling_FettlingFactors(
    val shapeId: String,
    val technology: String,
    val fettlingFactorLength: BigDecimal,
    val fettlingFactorWidth: BigDecimal,
    val fettlingFactorDiameter: BigDecimal,
    val sprueAndFeederQuantity: BigDecimal,
)

val shapeFettlingFactorReader: (row: List<String>) -> ManufacturingStepFettling_FettlingFactors = { row ->
    ManufacturingStepFettling_FettlingFactors(
        shapeId = row[0],
        technology = row[1],
        fettlingFactorLength = row[2].toBigDecimal(),
        fettlingFactorWidth = row[3].toBigDecimal(),
        fettlingFactorDiameter = row[4].toBigDecimal(),
        sprueAndFeederQuantity = row[5].toBigDecimal(),
    )
}
