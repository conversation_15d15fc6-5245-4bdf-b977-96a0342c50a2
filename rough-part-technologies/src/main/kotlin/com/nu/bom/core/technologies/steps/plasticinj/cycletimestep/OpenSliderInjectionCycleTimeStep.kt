package com.nu.bom.core.technologies.steps.plasticinj.cycletimestep

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.CycleTimeStep
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.*
import java.math.BigDecimal

@EntityType(Entities.CYCLETIME_STEP)
class OpenSliderInjectionCycleTimeStep(name: String) : ManufacturingEntity(name) {

    override val extends = CycleTimeStep(name)

    fun time(@Parent(Entities.MANUFACTURING_STEP) sliderType: SliderType,
             @Parent(Entities.MANUFACTURING_STEP) necessaryClampingForce: Force): CycleTime {

        val seconds = when (sliderType) {
            SliderType.MECHANICAL_SLIDER -> if (necessaryClampingForce.res > 5000000.toBigDecimal()) 1.5 else 0.6
            SliderType.HYDRAULIC_SLIDER -> if (necessaryClampingForce.res > 5000000.toBigDecimal()) 3.0 else 1.5
            SliderType.MECHANICAL_SLIDER_WITH_BUMPER -> if (necessaryClampingForce.res > 5000000.toBigDecimal()) 5.0 else 2.5
            else -> 0.0
        }
        return CycleTime(seconds, CycleTimeUnit.SECOND)
    }

    fun adjustmentRate(@Parent(Entities.MANUFACTURING_STEP) sliderType: SliderType): Rate {

        return when (sliderType.res) {
            SliderType.Selection.NO_SLIDER -> Rate(BigDecimal.ZERO)
            else -> Rate(BigDecimal.ONE)
        }
    }
}
