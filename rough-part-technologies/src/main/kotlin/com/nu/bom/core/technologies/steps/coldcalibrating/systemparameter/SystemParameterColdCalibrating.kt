package com.nu.bom.core.technologies.steps.coldcalibrating.systemparameter

import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Force
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text

@EntityType(Entities.SYSTEM_PARAMETER)
class SystemParameterColdCalibrating(name: String) : ManufacturingEntity(name) {

    @Input
    @ReadOnly
    @ObjectView(ObjectView.SYSTEM, 10)
    val pressType: Text? = null

    @Input
    @ReadOnly
    @ObjectView(ObjectView.SYSTEM, 20)
    val stages: Text? = null

    @Input
    @ReadOnly
    @ObjectView(ObjectView.SYSTEM, 25)
    val automation: Text? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.KILONEWTON)
    @ObjectView(ObjectView.SYSTEM, 30)
    val pressingForce: Force? = null

    @Input
    @ObjectView(ObjectView.SYSTEM, 40)
    @ReadOnly
    val maxStrokes: Num? = null

    @Input
    @ReadOnly
    @ObjectView(ObjectView.SYSTEM, 20)
    val maxWeight: QuantityUnit? = null
}
