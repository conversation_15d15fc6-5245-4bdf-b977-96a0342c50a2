package com.nu.bom.core.technologies.manufacturings.chill

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.Condition
import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.Precompute
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.SummaryView
import com.nu.bom.core.manufacturing.annotations.WizardField
import com.nu.bom.core.manufacturing.defaults.NullProvider
import com.nu.bom.core.manufacturing.entities.BaseModelManufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ShapeBasedCostModuleManufacturing
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.CoreToPartRatio
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.MachiningForTechnology
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeCleanness
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeHeatTreatmentAlu
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.technologies.manufacturings.chill.material.ChillCastedMaterial
import com.nu.bom.core.technologies.manufacturings.chill.material.MaterialChillCasting
import com.nu.bom.core.technologies.service.ShapeLookupReaderService
import com.nu.bom.core.technologies.steps.chillfettling.ManufacturingStepChillFettling
import com.nu.bom.core.technologies.steps.coreremovalsawing.ManufacturingStepCoreRemovalSawing
import com.nu.bom.core.technologies.steps.gca.ManufacturingStepGravityCasting
import com.nu.bom.core.technologies.steps.melting.ManufacturingStepMelting
import com.nu.bom.core.technologies.steps.t4.ManufacturingStepT4
import com.nu.bom.core.technologies.steps.t5.ManufacturingStepT5
import com.nu.bom.core.technologies.steps.t6.ManufacturingStepT6
import java.math.RoundingMode
import kotlin.reflect.KClass

@EntityType(Entities.MANUFACTURING)
class ManufacturingChillCasting(
    name: String,
) : BaseModelManufacturing(name) {
    override val model: Model
        get() = Model.CHILL

    override val modelSteps: List<KClass<out ManufacturingEntity>>
        get() =
            listOf(
                ManufacturingStepMelting::class,
                ManufacturingStepGravityCasting::class,
                ManufacturingStepCoreRemovalSawing::class,
                ManufacturingStepChillFettling::class,
                ManufacturingStepT4::class,
                ManufacturingStepT5::class,
                ManufacturingStepT6::class,
            )

    override val extends = ShapeBasedCostModuleManufacturing(name)

    @Input
    val technology = Text("CHILL")

    // Todo: Might need to be changed to average volume. See COST-50914
    fun internalCallsPerYear(peakUsableProductionVolumePerYear: QuantityUnit): Num =
        Num(max((peakUsableProductionVolumePerYear.res / 25000.toBigDecimal()).setScale(0, RoundingMode.UP), 6.toBigDecimal()))

    @Input
    @Path("/api/model/chill/materialName")
    val materialName: Text? = null // TODO ->  Material ENUM ?

    @WizardField(index = 12)
    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun maxWallThickness(
        @Parent(Entities.PROCESSED_MATERIAL) @Default(NullProvider::class) maxWallThickness: Length?,
    ) = maxWallThickness ?: Length(2.0, LengthUnits.MILLIMETER)

    @WizardField(index = 13)
    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun minWallThickness(
        @Parent(Entities.PROCESSED_MATERIAL) @Default(NullProvider::class) minWallThickness: Length?,
    ) = minWallThickness ?: Length(1.0, LengthUnits.MILLIMETER)

    @Input
    @WizardField(index = 0)
    val fettling: Bool? = null

    @Input
    @Condition(field = "hasCore", value = "true", operator = Condition.EQUALS)
    @WizardField(index = 2)
    val relativeCoreSize: Rate = Rate(0.0)

    // pls do not delete - bct issue
    @Input
    @ReadOnly
    fun hasCore(): SelectableBoolean = SelectableBoolean.FALSE

    @Input
    val coreToPartRatio: CoreToPartRatio = CoreToPartRatio(CoreToPartRatio.Selection.NORMAL_PACKAGE)

    @Input
    @Condition(field = "hasCore", value = "true", operator = Condition.EQUALS)
    @WizardField(index = 3)
    val coreAssembly: SelectableBoolean = SelectableBoolean.FALSE

    @Input
    @WizardField(index = 4)
    @ReadOnly
    val needsShotBlasting: SelectableBoolean = SelectableBoolean.FALSE

    @Input
    @WizardField(index = 5)
    @Precompute
    val stepSubTypeHeatTreatmentAlu = StepSubTypeHeatTreatmentAlu.NO_HEAT_TREATMENT

    @Input
    val materialClass = Text(MaterialChillCasting::class.qualifiedName!!)

    fun inputGroupInternal(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
    ) = lookupReaderService.getInputGroupFromShapeLookup(calculationContext!!.accessCheck, shapeId, Model.CHILL)

    @EntityCreation(Entities.PROCESSED_MATERIAL, childCreations = [Entities.MANUFACTURING_STEP])
    fun createMaterial(): ManufacturingEntity =
        createEntity(
            name = "ChillCastedMaterial",
            clazz = ChillCastedMaterial::class,
            entityType = Entities.MATERIAL,
        )

    @SummaryView(SummaryView.PROCESS, 100)
    fun heatTreatmentSummaryView(stepSubTypeHeatTreatmentAlu: StepSubTypeHeatTreatmentAlu): Text =
        when (stepSubTypeHeatTreatmentAlu.res) {
            StepSubTypeHeatTreatmentAlu.Selection.T4 -> Text("T4 for aluminium parts")
            StepSubTypeHeatTreatmentAlu.Selection.T5 -> Text("T5 for aluminium parts")
            StepSubTypeHeatTreatmentAlu.Selection.T6 -> Text("T6 for aluminium parts")
            StepSubTypeHeatTreatmentAlu.Selection.NO_HEAT_TREATMENT -> Text("No heat treatment")
        }

    @Input
    @WizardField(index = 6)
    fun machining(): MachiningForTechnology = MachiningForTechnology.none

    @Input
    @WizardField(index = 7)
    @Condition(field = "machining", value = "TURNING", operator = Condition.EQUALS)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    val margin: Length? = null

    @Input
    @WizardField(index = 9)
    @Precompute
    @Condition(field = "cleaningNeeded", value = "TRUE", operator = Condition.EQUALS)
    fun cleanness(): StepSubTypeCleanness = StepSubTypeCleanness.NORMAL

    @Input
    @WizardField(index = 8)
    @Precompute
    @Condition(field = "machining", value = "NONE", operator = Condition.EQUALS)
    fun cleaningNeeded(cleaningNeededRawPart: SelectableBoolean) = cleaningNeededRawPart

    @Input
    @WizardField(index = 8)
    @Precompute
    @Condition(field = "machining", value = "NONE", operator = Condition.NOT_EQUALS)
    fun cleaningNeededRawPart() = SelectableBoolean.FALSE
}
