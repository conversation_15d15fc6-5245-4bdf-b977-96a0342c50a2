package com.nu.bom.core.technologies.steps.fettling

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.ExpectedParents
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Modularized
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.Selectable
import com.nu.bom.core.manufacturing.annotations.SubLabel
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.SingleGroupManufacturingStep
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.ManufacturingStepType
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits.SECOND
import com.nu.bom.core.technologies.steps.fettling.systemparameter.SystemParameterFettling
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

@EntityType(Entities.MANUFACTURING_STEP)
@Modularized(
    technologies = [Model.SAND],
    parents = [
        ExpectedParents(model = Model.SAND, type = Entities.MANUFACTURING),
        ExpectedParents(model = Model.SAND, type = Entities.PROCESSED_MATERIAL),
    ],
    dimensions = [Dimension.Selection.NUMBER],
    userCreatable = false,
)
class ManufacturingStepFettling(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = SingleGroupManufacturingStep(name)

    @Input
    @SubLabel
    @ReadOnly
    @Selectable(value = "manufacturingStepType", getDisplayNameFromPathQuery = true)
    fun manufacturingStepType(): Text = Text(ManufacturingStepType.DEBURRING.name)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun netWeightPerPart(): QuantityUnit? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun shapeId(): Text? = null

    @Input
    fun internalPartsPerCycle() = QuantityUnit(1.0)

    @Input
    fun utilizationRate() = Rate(0.85)

    fun scrapRate() = Rate(0.01)

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 20)
    @DefaultUnit(DefaultUnit.SECOND)
    fun grabCycleTime(netWeightPerPart: QuantityUnit): Time {
        val res =
            when {
                netWeightPerPart.res < 2.toBigDecimal() -> 3.toBigDecimal()
                netWeightPerPart.res < 5.toBigDecimal() -> 5.toBigDecimal()
                netWeightPerPart.res > 25.toBigDecimal() -> 15.toBigDecimal()
                else -> 10.toBigDecimal()
            }
        return Time(res, SECOND)
    }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 30)
    @DefaultUnit(DefaultUnit.SECOND)
    fun releaseCycleTime(netWeightPerPart: QuantityUnit): Time {
        val res =
            when {
                netWeightPerPart.res < 2.toBigDecimal() -> 3.toBigDecimal()
                netWeightPerPart.res < 5.toBigDecimal() -> 5.toBigDecimal()
                netWeightPerPart.res > 25.toBigDecimal() -> 15.toBigDecimal()
                else -> 10.toBigDecimal()
            }
        return Time(res, SECOND)
    }

    @EntityCreation(Entities.SYSTEM_PARAMETER)
    fun createSystemParameter(templateName: Text): Mono<SystemParameterFettling> =
        createSystemParameter(
            masterData = MasterDataType.SYSTEM_PARAMETER_FETTLING,
            clazz = SystemParameterFettling::class,
            system = templateName.res,
            year = calculationContext?.year!!,
            location = "Global",
        )

    @EntityCreation(Entities.MACHINE)
    fun createMachines(templateName: Text): Flux<ManufacturingEntity> =
        createEntitiesFromTemplate(
            name = templateName.res + "_Machine",
            location = "Global",
        )

    @EntityCreation(Entities.LABOR)
    fun createLabor(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> =
        createEntitiesFromTemplate(
            name = templateName.res + "_Labor",
            location = locationName.res,
        )

    @EntityCreation(Entities.SETUP)
    fun createSetup(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> =
        createEntitiesFromTemplate(
            name = templateName.res + "_Setup",
            location = locationName.res,
        )

    @EntityCreation(Entities.TOOL)
    fun createTools(templateName: Text): Flux<ManufacturingEntity> =
        createEntitiesFromTemplate(
            name = templateName.res + "_Tools",
            location = "Global",
        )
}
