package com.nu.bom.core.technologies.manufacturings.fti.manufacturing

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.FreezeImplementation
import com.nu.bom.core.manufacturing.annotations.Hidden
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.KeepOld
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.Precompute
import com.nu.bom.core.manufacturing.annotations.WizardField
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.ConfigIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.Deg
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.fti.StripTypeTransfer
import com.nu.bom.core.model.toUUID
import com.nu.bom.core.service.fti.BlankingResponseEvent
import com.nu.bom.core.service.fti.FtiContext
import com.nu.bom.core.service.fti.NestingResponseEvent
import com.nu.bom.core.service.fti.StripDetails
import com.nu.bom.core.service.fti.TechnologyType
import com.nu.bom.core.service.wizard.steps.fti.WizardFtiNestingFieldStep
import com.nu.bom.core.service.wizard.steps.fti.WizardFtiWamFieldStep
import com.nu.bom.core.technologies.manufacturings.fti.material.MaterialTransferStamping
import com.nu.bom.core.technologies.manufacturings.fti.material.TransferStampedMaterial
import reactor.core.publisher.Mono
import kotlin.reflect.KClass

@EntityType(Entities.MANUFACTURING)
@Suppress("unused")
class ManufacturingTransferDieStamping(name: String) : ManufacturingFti(name) {
    override val model = Model.FTITDS

    override val modelSteps: List<KClass<out ManufacturingEntity>>
        get() = emptyList()

    override fun technologyType(): TechnologyType = TechnologyType.TRANSFER_DIE

    // To be removed, see https://tsetplatform.atlassian.net/browse/COST-63217
    @Input
    fun costModuleConfigurationIdentifier(): ConfigIdentifier = ConfigIdentifier(ConfigurationIdentifier.empty())

    @Input
    val technology = Text("FTITDS")

    @Input
    @WizardField(WizardFtiWamFieldStep::class, 2)
    @Path("/api/model/ftitds/materialName")
    val materialName: Text? = null

    @Input
    fun materialClass() = Text(MaterialTransferStamping::class.qualifiedName!!)

    @Input
    @MandatoryForEntity(section = "right", refresh = true)
    @WizardField(WizardFtiNestingFieldStep::class, 4)
    @Precompute
    fun stripType(): StripTypeTransfer = StripTypeTransfer(StripTypeTransfer.Selection.ONE_UP)

    // A dimension
    @Input
    @WizardField(WizardFtiNestingFieldStep::class, 5)
    @Precompute
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun distanceBetweenBlanks(blankingResponse: BlankingResponseEvent): Length? =
        blankingResponse.defaultStripDetails.distanceBetweenBlanks?.let {
            Length(it, LengthUnits.MILLIMETER)
        }

    // B dimension
    @Input
    @WizardField(WizardFtiNestingFieldStep::class, 6)
    @Precompute
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun blankToCoilEdgeDistance(blankingResponse: BlankingResponseEvent): Length? =
        blankingResponse.defaultStripDetails.blankToCoilEdgeDistance?.let {
            Length(it, LengthUnits.MILLIMETER)
        }

    // Fti process response heavy calculation, should be initiated when nestingInitiated field is set only (by default yes, switched off when we start the wizard)
    @WizardField(WizardFtiNestingFieldStep::class)
    @Hidden
    @Precompute
    @KeepOld
    @FreezeImplementation
    fun nestingResponse(
        blankingFtcFileId: Text,
        stripType: StripTypeTransfer,
        distanceBetweenBlanks: Length,
        blankToCoilEdgeDistance: Length,
        rotationAngle: Deg?,
        nestingInitiated: Bool,
    ): Mono<NestingResponseEvent>? {
        if (nestingInitiated.isFalse()) {
            return null
        }
        return services.getFtiService().calculateNesting(
            ftiContext =
                FtiContext(
                    accessCheck = services.accessCheck,
                    technologyType = technologyType(),
                ),
            ftcFileUploadId = blankingFtcFileId.res.toUUID(),
            stripType = stripType.res.name,
            stripDetails =
                StripDetails(
                    // TODO fix unit to inMillimeter when fti service is adopted
                    distanceBetweenBlanks.inMeter,
                    blankToCoilEdgeDistance.inMeter,
                    null,
                    null,
                    null,
                ),
            rotationAngles = rotationAngle?.let { listOf(it.res) } ?: emptyList(),
        )
    }

    @EntityCreation(Entities.PROCESSED_MATERIAL, childCreations = [Entities.MANUFACTURING_STEP])
    fun createMaterial(): ManufacturingEntity {
        return createEntity(
            name = "TransferStampedMaterial",
            clazz = TransferStampedMaterial::class,
            entityType = Entities.PROCESSED_MATERIAL,
        )
    }
}
