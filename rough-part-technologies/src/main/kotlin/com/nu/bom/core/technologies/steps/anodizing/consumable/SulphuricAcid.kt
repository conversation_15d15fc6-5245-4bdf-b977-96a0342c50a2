package com.nu.bom.core.technologies.steps.anodizing.consumable

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Hidden
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Sibling
import com.nu.bom.core.manufacturing.entities.Consumable
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.*

@EntityType(Entities.CONSUMABLE)
class SulphuricAcid(name: String) : ManufacturingEntity(name) {

    override val extends = Consumable(name)

    @Input
    fun reuseOfScrap() = Bool(false)

    @Hidden
    fun quantity(): Num? = null

    @Input
    @ObjectView(ObjectView.MATERIAL, 50)
    fun consumableRate() = Rate(0.05)

    fun costPerPart(
        consumableRate: Rate,
        @Sibling(Entities.MACHINE)
        totalInvest: Money
    ): Money {
        return Money(consumableRate.res * totalInvest.res)
    }
}
