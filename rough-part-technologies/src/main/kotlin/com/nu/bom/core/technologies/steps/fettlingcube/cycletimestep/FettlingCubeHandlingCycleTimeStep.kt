package com.nu.bom.core.technologies.steps.fettlingcube.cycletimestep

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.CycleTimeStep
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeUnit
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import java.math.BigDecimal

@EntityType(Entities.CYCLETIME_STEP)
class FettlingCubeHandlingCycleTimeStep(name: String) : ManufacturingEntity(name) {

    override val extends = CycleTimeStep(name)

    @Input
    fun time(@Parent(Entities.PROCESSED_MATERIAL) netWeightPerPart: QuantityUnit) =
        CycleTime(if (netWeightPerPart.res < BigDecimal(13.0)) 12.0 else 30.6, CycleTimeUnit.SECOND)
}
