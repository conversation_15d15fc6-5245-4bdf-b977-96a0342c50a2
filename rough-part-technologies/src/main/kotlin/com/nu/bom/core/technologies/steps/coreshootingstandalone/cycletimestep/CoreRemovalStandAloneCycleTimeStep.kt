package com.nu.bom.core.technologies.steps.coreshootingstandalone.cycletimestep

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.entities.CycleTimeStep
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import java.math.BigDecimal

@EntityType(Entities.CYCLETIME_STEP)
class CoreRemovalStandAloneCycleTimeStep(name: String) : ManufacturingEntity(name) {

    override val extends: ManufacturingEntity? = CycleTimeStep(name)

    @Input
    fun adjustmentRate() = Rate(BigDecimal.ONE)

    fun time() = CycleTime(5.0, CycleTimeUnit.SECOND)
}



