package com.nu.bom.core.technologies.manufacturings.what.material

import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityProvider
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.OrderedEntityCreation
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.SummaryView
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ShapedMaterial
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataCategory
import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.AreaUnits
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.MaterialSubstances
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeCleanness
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeHeatTreatmentSteel
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.technologies.behaviours.CleaningBehaviour
import com.nu.bom.core.technologies.behaviours.SteelHeatTreatmentBehaviour
import com.nu.bom.core.technologies.steps.coldcalibrating.ManufacturingStepColdCalibrating
import com.nu.bom.core.technologies.steps.shotblastingdeburring.ManufacturingStepShotBlastingDeburring
import com.nu.bom.core.technologies.steps.what.ManufacturingStepHateburHot
import reactor.core.publisher.Mono
import java.math.BigDecimal

@EntityType(Entities.PROCESSED_MATERIAL)
class HateburHotForgedMaterial(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = ShapedMaterial(name)

    override val behaviours: List<ManufacturingEntity> =
        listOf(
            SteelHeatTreatmentBehaviour("SteelHeatTreatmentBehaviour"),
            CleaningBehaviour("CleaningBehaviour"),
        )

    @ObjectView(ObjectView.NONE, 0)
    fun displayDesignation(
        designation: Text?,
        entityDesignation: Text,
    ): Text = designation ?: entityDesignation

    @Input
    val coefficientBulkMaterialLookupKey = Text("WHAT")

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    val materialName: Text? = null

    fun density(materialName: Text): Mono<Density> =
        services
            .findMaterialDensity(
                accessCheck = calculationContext().accessCheck,
                materialKey = materialName.res,
            )

    fun materialSubstances(materialName: Text): Mono<MaterialSubstances> =
        services
            .getMasterData(
                accessCheck = calculationContext().accessCheck,
                category = MasterDataCategory.MATERIAL,
                key = materialName.res,
                location = "Global",
                year = calculationContext!!.year,
            ).map {
                it.getField<MaterialSubstances>("materialSubstances")
            }

    @Input
    val materialClass: Text = Text("MaterialForgingWhat")

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun cleanness(): StepSubTypeCleanness? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @SummaryView(SummaryView.PROCESS, 300, "cleaning")
    fun cleaningNeeded(): SelectableBoolean = SelectableBoolean.FALSE

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun netWeightPerPart(): QuantityUnit? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    val partLength: Length = Length(BigDecimal.ZERO, LengthUnits.METER)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    val partOuterDiameter: Length = Length(BigDecimal.ZERO, LengthUnits.METER)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    val partHeight: Length = Length(BigDecimal.ZERO, LengthUnits.METER)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    val partInnerDiameter: Length = Length(BigDecimal.ZERO, LengthUnits.METER)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun maxWallThickness(): Length? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    val stepSubTypeHeatTreatment: StepSubTypeHeatTreatmentSteel? = null

    @Suppress("ktlint:standard:property-naming")
    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    val CHD: Length = Length(0.toBigDecimal(), LengthUnits.METER)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    val coldCalibrating: SelectableBoolean? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    val projectedAreaPerPart: Area = Area(BigDecimal.ZERO, AreaUnits.QCM)

    @OrderedEntityCreation
    fun manufacturingSteps() = arrayOf("hateburHot", "steelHeatTreatment", "shotBlasting", "coldCalibratingStep", "cleaning")

    @EntityProvider
    fun hateburHot() =
        createEntity(
            name = "ManufacturingStepHateburHot",
            clazz = ManufacturingStepHateburHot::class,
            entityType = Entities.MANUFACTURING_STEP,
        )

    @EntityProvider
    fun shotBlasting() =
        createEntity(
            name = "ManufacturingStepShotBlasting",
            clazz = ManufacturingStepShotBlastingDeburring::class,
            entityType = Entities.MANUFACTURING_STEP,
        )

    @EntityProvider
    fun coldCalibratingStep(coldCalibrating: SelectableBoolean) =
        if (coldCalibrating == SelectableBoolean.TRUE) {
            createEntity(
                name = "ManufacturingStepColdCalibrating",
                clazz = ManufacturingStepColdCalibrating::class,
                entityType = Entities.MANUFACTURING_STEP,
            )
        } else {
            null
        }
}
