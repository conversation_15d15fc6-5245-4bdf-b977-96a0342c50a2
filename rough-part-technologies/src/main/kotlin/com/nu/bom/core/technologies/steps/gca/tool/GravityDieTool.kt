package com.nu.bom.core.technologies.steps.gca.tool

import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityCreationChildrenScope
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.FieldName
import com.nu.bom.core.manufacturing.annotations.FilteredChildren
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Sibling
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.annotations.StaticDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.TranslationLabel
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.Tool
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_COUNTRY_ID_ENTITY_NAME
import com.nu.bom.core.manufacturing.entities.toolmaintenance.DetailedToolMaintenanceCalculationEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.StaticUnitOverride
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.SliderComplexityChillCasting
import com.nu.bom.core.manufacturing.fieldTypes.SliderSizeChillCasting
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.ToolAllocationMode
import com.nu.bom.core.manufacturing.fieldTypes.ToolComplexityChillCasting
import com.nu.bom.core.manufacturing.fieldTypes.ToolMaintenanceType
import com.nu.bom.core.technologies.lookups.gravityCastingBigMaintenanceReader
import com.nu.bom.core.technologies.lookups.gravityCastingCyclesBigMaintenanceReader
import com.nu.bom.core.technologies.lookups.gravityCastingSmallAndBigMaintenanceReader
import com.nu.bom.core.technologies.lookups.gravityCastingToolCostReader
import com.nu.bom.core.utils.CostFactorUtils.getCostFactorForCurrentStepId
import org.bson.types.ObjectId
import reactor.core.publisher.Mono
import java.math.BigDecimal
import java.math.RoundingMode

@EntityType(Entities.TOOL)
class GravityDieTool(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = Tool(name)

    @SpecialLink("MaterialCasting", "shotWeightPerMold")
    fun shotWeightPerMold(): QuantityUnit? = null

    @SpecialLink("ManufacturingChillCasting.location", "countryId")
    val countryId: Text? = null

    fun allocationRatio(
        quantity: Num,
        toolAllocationMode: ToolAllocationMode,
        @Sibling(Entities.SYSTEM_PARAMETER)
        frames: Num,
    ): Rate {
        val payOne =
            when {
                frames.res < 6.toBigDecimal() -> Rate(BigDecimal.ONE) - (Num(BigDecimal.ONE) * frames / quantity)
                else -> Rate(BigDecimal.ONE) - (Num(BigDecimal.ONE) / quantity)
            }

        return when (toolAllocationMode.res) {
            ToolAllocationMode.Mode.PAY_NONE -> Rate(BigDecimal.ONE)
            ToolAllocationMode.Mode.PAY_ONE -> payOne
            ToolAllocationMode.Mode.PAY_ALL -> Rate(BigDecimal.ZERO)
            else -> throw IllegalArgumentException("ToolAllocationModeConstants must be 1 (pay one),2 (pay none) or 3 (pay all)!")
        }
    }

    @TranslationLabel("toolQuantity")
    fun quantity(
        serviceLifeInCycles: Num,
        @Parent(Entities.MANUFACTURING_STEP)
        cyclesOverLifeTime: Num,
        @Sibling(Entities.SYSTEM_PARAMETER)
        frames: Num,
    ): Num =
        when {
            frames.res < 6.toBigDecimal() ->
                Num(
                    max(
                        (cyclesOverLifeTime / serviceLifeInCycles).res.setScale(
                            0,
                            RoundingMode.CEILING,
                        ),
                        frames.res,
                    ),
                )

            else -> Num((cyclesOverLifeTime / serviceLifeInCycles).res.setScale(0, RoundingMode.CEILING))
        }

    fun investPerTool(
        @Parent(Entities.PROCESSED_MATERIAL)
        shapeId: Text,
        shotWeightPerMold: QuantityUnit,
        @Parent(Entities.MANUFACTURING_STEP)
        partsPerMold: Pieces,
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_COUNTRY_INFO,
            nameFilter = TSET_COST_COUNTRY_ID_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        ) value: Map<ObjectId, Text>,
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_COUNTRY_INFO,
            nameFilter = TSET_COST_COUNTRY_ID_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        @FieldName("location")
        costFactorLocations: Map<ObjectId, Text>,
        @Parent(Entities.MANUFACTURING_STEP)
        location: Text,
    ): Mono<Money> {
        val costFactorForCurrentStepId = getCostFactorForCurrentStepId(costFactorLocations, location)
        val countryId =
            requireNotNull(value[costFactorForCurrentStepId]) {
                "CountryId for $costFactorForCurrentStepId not found"
            }
        val isChinaOrIndia = countryId.res == "10" || countryId.res == "18"
        return services
            .getLookupTable("ManufacturingStepGravityCasting_ToolCost", gravityCastingToolCostReader)
            .filter {
                it.shapeId == shapeId.res &&
                    shotWeightPerMold.res < it.maxWeight
            }.elementAt(0)
            .map {
                when {
                    partsPerMold.res.compareTo(8.toBigDecimal()) == 0 && isChinaOrIndia -> Money(it.asia8Cavities)
                    partsPerMold.res.compareTo(8.toBigDecimal()) == 0 -> Money(it.europe8Cavities)
                    partsPerMold.res.compareTo(4.toBigDecimal()) == 0 && isChinaOrIndia -> Money(it.asia4Cavities)
                    partsPerMold.res.compareTo(4.toBigDecimal()) == 0 -> Money(it.europe4Cavities)
                    partsPerMold.res.compareTo(2.toBigDecimal()) == 0 && isChinaOrIndia -> Money(it.asia2Cavities)
                    partsPerMold.res.compareTo(2.toBigDecimal()) == 0 -> Money(it.europe2Cavities)
                    isChinaOrIndia -> Money(it.asia1Cavities)
                    else -> Money(it.europe1Cavities)
                }
            }
    }

    fun toolMaintenanceType(): ToolMaintenanceType = ToolMaintenanceType.DETAILED_TOOL_MAINTENANCE

    @DefaultUnit(DefaultUnit.HOUR)
    fun smallMaintenanceHours(
        @Parent(Entities.MANUFACTURING_STEP)
        complexityChillCasting: ToolComplexityChillCasting,
        shotWeightPerMold: QuantityUnit,
    ): Mono<Time> =
        services
            .getLookupTable(
                "ManufacturingStepGravityCasting_SmallAndBigMaintenance",
                gravityCastingSmallAndBigMaintenanceReader,
            ).filter {
                it.maxWeight > shotWeightPerMold.res &&
                    it.complexityChillCasting == complexityChillCasting
            }.elementAt(0)
            .map {
                Time(it.smallMaintenanceHours, TimeUnits.HOUR)
            }

    fun cyclesSmallMaintenance(
        @Parent(Entities.MANUFACTURING_STEP)
        complexityChillCasting: ToolComplexityChillCasting,
        shotWeightPerMold: QuantityUnit,
    ): Mono<Num> =
        services
            .getLookupTable(
                "ManufacturingStepGravityCasting_SmallAndBigMaintenance",
                gravityCastingSmallAndBigMaintenanceReader,
            ).filter {
                it.maxWeight > shotWeightPerMold.res &&
                    it.complexityChillCasting == complexityChillCasting
            }.elementAt(0)
            .map {
                Num(it.cyclesSmallMaintenance)
            }

    fun bigMaintenanceHoursFactor(
        @Parent(Entities.MANUFACTURING_STEP)
        complexityChillCasting: ToolComplexityChillCasting,
        shotWeightPerMold: QuantityUnit,
    ): Mono<Num> =
        services
            .getLookupTable(
                "ManufacturingStepGravityCasting_SmallAndBigMaintenance",
                gravityCastingSmallAndBigMaintenanceReader,
            ).filter {
                it.maxWeight > shotWeightPerMold.res &&
                    it.complexityChillCasting == complexityChillCasting
            }.elementAt(0)
            .map {
                Num(it.bigMaintenanceFactor)
            }

    @DefaultUnit(DefaultUnit.HOUR)
    fun bigMaintenanceHours(
        @Parent(Entities.MANUFACTURING_STEP)
        sliderComplexity: SliderComplexityChillCasting,
        @Parent(Entities.MANUFACTURING_STEP)
        sliderSize: SliderSizeChillCasting,
        bigMaintenanceHoursFactor: Num,
    ): Mono<Time> =
        services
            .getLookupTable(
                "ManufacturingStepGravityCasting_BigMaintenance",
                gravityCastingBigMaintenanceReader,
            ).filter {
                it.sliderSize == sliderSize &&
                    it.sliderComplexity == sliderComplexity
            }.elementAt(0)
            .map {
                Time(it.bigMaintenanceTime + bigMaintenanceHoursFactor.res, TimeUnits.HOUR)
            }

    fun cyclesBigMaintenance(bigMaintenanceHours: Time): Mono<Num> =
        services
            .getLookupTable(
                "ManufacturingStepGravityCasting_CyclesBigMaintenance",
                gravityCastingCyclesBigMaintenanceReader,
            ).filter {
                it.bigMaintenanceHours.compareTo(bigMaintenanceHours.inHours) == 0
            }.elementAt(0)
            .map {
                Num(it.cyclesBigMaintenance)
            }

    fun bigMaintenanceMaterialCosts(bigMaintenanceHours: Time): Money =
        Money(100.toBigDecimal() + (bigMaintenanceHours.inHours - 6.toBigDecimal()) * 50.toBigDecimal())

    @StaticDenominatorUnit(StaticUnitOverride.YEAR)
    fun numberOfSmallMaintenancePerYear(
        numberOfBigMaintenancePerYear: Num,
        cyclesSmallMaintenance: Num,
        serviceLifeInCycles: Num,
    ): Num = Num((serviceLifeInCycles / cyclesSmallMaintenance).res.setScale(0, RoundingMode.FLOOR)) - numberOfBigMaintenancePerYear

    @StaticDenominatorUnit(StaticUnitOverride.YEAR)
    fun numberOfBigMaintenancePerYear(
        cyclesBigMaintenance: Num,
        serviceLifeInCycles: Num,
    ): Num = Num((serviceLifeInCycles / cyclesBigMaintenance).res.setScale(0, RoundingMode.FLOOR))

    @EntityCreation(Entities.DETAILED_TOOL_MAINTENANCE_CALCULATOR, entityCreationChildrenScope = EntityCreationChildrenScope.ALL)
    fun createMaintenances(
        bigMaintenanceMaterialCosts: Money,
        numberOfSmallMaintenancePerYear: Num,
        numberOfBigMaintenancePerYear: Num,
        smallMaintenanceHours: Time,
        bigMaintenanceHours: Time,
    ): List<DetailedToolMaintenanceCalculationEntity> =
        listOf(
            createEntity(
                "Small Maintenance",
                Entities.DETAILED_TOOL_MAINTENANCE_CALCULATOR,
                fields =
                    mapOf(
                        DetailedToolMaintenanceCalculationEntity::maintenanceHours.name to smallMaintenanceHours,
                        DetailedToolMaintenanceCalculationEntity::numberOfMaintenancePerYear.name to numberOfSmallMaintenancePerYear,
                    ),
            ),
            createEntity(
                "Big Maintenance",
                Entities.DETAILED_TOOL_MAINTENANCE_CALCULATOR,
                fields =
                    mapOf(
                        DetailedToolMaintenanceCalculationEntity::maintenanceHours.name to bigMaintenanceHours,
                        DetailedToolMaintenanceCalculationEntity::numberOfMaintenancePerYear.name to numberOfBigMaintenancePerYear,
                        DetailedToolMaintenanceCalculationEntity::materialCostPerMaintenance.name to bigMaintenanceMaterialCosts,
                    ),
            ),
        )
}
