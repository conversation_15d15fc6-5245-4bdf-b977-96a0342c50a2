package com.nu.bom.core.technologies.steps.bendingcube.cycletimestep

import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.CycleTimeStepGroup
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Quantity

@EntityType(Entities.CYCLETIME_STEP_GROUP)
class BendingCycleTimeStepGroupCube(name: String) : ManufacturingEntity(name) {
    override val extends = CycleTimeStepGroup(name)

    @EntityCreation(Entities.CYCLETIME_STEP)
    fun createCycleTimeSteps(
        @Parent(Entities.MANUFACTURING) numberOfBends: Quantity
    ): List<ManufacturingEntity> {
        val pickUp = listOf(
            createEntity(
                name = "Pick up part",
                clazz = PickupAndSetDownBendingCycleTimeStepCube::class,
                entityType = Entities.CYCLETIME_STEP
            )
        )

        val bends =
            List(
                numberOfBends.res.toInt()
            ) { i ->
                createEntity(
                    name = "Bending ${i + 1}",
                    clazz = BendingCycleTimeStepCube::class,
                    entityType = Entities.CYCLETIME_STEP
                )
            }

        val setDown = listOf(
            createEntity(
                name = "Set down part",
                clazz = PickupAndSetDownBendingCycleTimeStepCube::class,
                entityType = Entities.CYCLETIME_STEP
            )
        )
        return pickUp + bends + setDown
    }
}
