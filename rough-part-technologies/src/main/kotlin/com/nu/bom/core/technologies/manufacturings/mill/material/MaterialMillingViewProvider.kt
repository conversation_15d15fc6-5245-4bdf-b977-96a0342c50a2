package com.nu.bom.core.technologies.manufacturings.mill.material

import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.specifics.material.MaterialViewProviderBase
import com.nu.bom.core.manufacturing.fieldTypes.MaterialViewConfig
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.FieldConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.FieldSectionConfigFeDto
import org.springframework.stereotype.Service

@Service
object MaterialMillingViewProvider : MaterialViewProviderBase() {
    override val materialView = MaterialViewConfig.MaterialView.MATERIAL_MILLING

    override val substanceImageName: String = "MaterialChip.png"

    override val materialSpecificCardCost: FieldConfigFeDto =
        FieldConfigFeDto(
            left =
                listOf(
                    FieldSectionConfigFeDto(
                        listOf(
                            MaterialMilling::partLength.name,
                            MaterialMilling::partHeight.name,
                            MaterialMilling::partWidth.name,
                        ),
                        title = "rawPart",
                    ),
                    FieldSectionConfigFeDto(
                        listOf(
                            MaterialMilling::millingWeightPerPart.name,
                        ),
                        title = "rawPartMilling",
                    ),
                ),
            right =
                listOf(
                    FieldSectionConfigFeDto(
                        listOf(
                            MaterialMilling::netWeightPerPart.name,
                            MaterialMilling::surchargeLossWeight.name,
                            MaterialMilling::deployedWeightPerPart.name,
                        ),
                        title = "deployedWeight",
                    ),
                ),
        )
}
