package com.nu.bom.core.technologies.steps.coating.cycletimestep

import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.entities.CycleTimeStepGroup
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities

@EntityType(Entities.CYCLETIME_STEP_GROUP)
class FlippingCycleTimeStepGroup(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = CycleTimeStepGroup(name)

    @EntityCreation(Entities.CYCLETIME_STEP)
    fun createCycleTimeSteps() = listOfNotNull(createCycleTimeStep(FlippingCoatingCycleTimeStep::class))
}
