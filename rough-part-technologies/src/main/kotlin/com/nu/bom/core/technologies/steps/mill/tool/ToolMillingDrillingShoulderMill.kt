package com.nu.bom.core.technologies.steps.mill.tool

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Pressure
import com.nu.bom.core.manufacturing.fieldTypes.Speed
import com.nu.bom.core.manufacturing.fieldTypes.ToolDetailViewConfig

@EntityType(Entities.TOOL)
class ToolMillingDrillingShoulderMill(name: String) : ManufacturingEntity(name) {
    override val extends = ToolMilling(name)

    @ReadOnly
    @Input
    val toolDiameter: Length? = null

    @ReadOnly
    @Input
    val maxCuttingDepth: Length? = null

    @ReadOnly
    @Input
    val bladeRadius: Length? = null

    @ReadOnly
    @Input
    val numberOfBlades: Num? = null

    @ReadOnly
    @Input
    val maxFeedRatePerRevolutionFull: Length? = null

    @ReadOnly
    @Input
    val maxFeedRatePerRevolutionLow: Length? = null

    @ReadOnly
    @Input
    val maxCuttingSpeedFull: Speed? = null

    @ReadOnly
    @Input
    val maxCuttingSpeedOptimal: Speed? = null

    @ReadOnly
    @Input
    val maxCuttingSpeedLow: Speed? = null

    @ReadOnly
    @Input
    val cuttingEdgesPerBlade: Num? = null

    fun toolDetailView(): ToolDetailViewConfig = ToolDetailViewConfig.MILLING_SHOULDER_MILL_TOOL

    @ReadOnly
    @Input
    val shearStrengthFull: Pressure? = null

    @ReadOnly
    @Input
    val shearStrengthOptimal: Pressure? = null

    @ReadOnly
    @Input
    val shearStrengthLow: Pressure? = null
}
