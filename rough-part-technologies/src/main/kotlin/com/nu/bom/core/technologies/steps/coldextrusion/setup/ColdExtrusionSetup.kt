package com.nu.bom.core.technologies.steps.coldextrusion.setup

import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.Setup
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Time
import java.math.BigDecimal

@EntityType(Entities.SETUP)
class ColdExtrusionSetup(name: String) : ManufacturingEntity(name) {
    override val extends = Setup(name)

    @Input
    @ObjectView(ObjectView.SETUP, 60)
    @DefaultUnit(DefaultUnit.MINUTE)
    val setupTimeCalculated: Time? = null

    @Input
    @ObjectView(ObjectView.SETUP, 10)
    @DefaultUnit(DefaultUnit.MINUTE)
    fun setupTime(
        @Parent(Entities.MANUFACTURING_STEP)
        stages: Num,
        setupTimeCalculated: Time
    ): Time {
        val factor = if (stages.res <= BigDecimal("3")) 0.5 else 1.0
        return setupTimeCalculated * factor
    }
}
