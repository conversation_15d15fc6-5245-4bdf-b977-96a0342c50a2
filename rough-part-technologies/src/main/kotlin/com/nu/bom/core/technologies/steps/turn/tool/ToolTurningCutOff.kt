package com.nu.bom.core.technologies.steps.turn.tool

import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Speed
import com.nu.bom.core.manufacturing.fieldTypes.Text

@EntityType(Entities.TOOL)
open class ToolTurningCutOff(name: String) : ManufacturingEntity(name) {

    override val extends = UsualTurningTool(name)

    @ReadOnly
    @Input
    val geometry: Text? = null
    @ReadOnly
    @Input
    val maxCuttingSpeed: Speed? = null
    @ReadOnly
    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    val cuttingWidth: Length? = null
    @ReadOnly
    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    val maxFeedWayPerRevolution: Length? = null
    @ReadOnly
    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    val maxCutOffDepthRadius: Length? = null
}
