package com.nu.bom.core.technologies.manufacturings.alex

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.BehaviourCreation
import com.nu.bom.core.manufacturing.annotations.Condition
import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.Precompute
import com.nu.bom.core.manufacturing.annotations.WizardField
import com.nu.bom.core.manufacturing.defaults.NullProvider
import com.nu.bom.core.manufacturing.entities.BaseModelManufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ShapeBasedCostModuleManufacturing
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.BendingType
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.MachiningSpeed
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeCleanness
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeHeatTreatmentAlu
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.service.behaviour.DynamicBehaviour
import com.nu.bom.core.manufacturing.service.behaviour.EntityBasedDynamicBehaviour
import com.nu.bom.core.technologies.manufacturings.alex.material.AluExtrudedMaterial
import com.nu.bom.core.technologies.service.ShapeLookupReaderService
import com.nu.bom.core.technologies.steps.alexmilling.ManufacturingStepAlexMilling
import com.nu.bom.core.technologies.steps.alexsawing.ManufacturingStepAlexSawing
import com.nu.bom.core.technologies.steps.alexwelding.ManufacturingStepAlexWelding
import com.nu.bom.core.technologies.steps.aluextrusion.ManufacturingStepAluExtrusion
import com.nu.bom.core.technologies.steps.anodizing.ManufacturingStepAnodizing
import com.nu.bom.core.technologies.steps.cleaning.ManufacturingStepCleaning
import com.nu.bom.core.technologies.steps.heattreatment.ManufacturingStepHeatTreatment
import com.nu.bom.core.technologies.steps.precisionstretching.ManufacturingStepPrecisionStretching
import com.nu.bom.core.technologies.steps.stretchbending.ManufacturingStepStretchBending
import com.nu.bom.core.utils.annotations.TsetSuppress
import java.math.BigDecimal
import java.math.RoundingMode
import kotlin.reflect.KClass

@EntityType(Entities.MANUFACTURING)
class ManufacturingAluExtrusion(
    name: String,
) : BaseModelManufacturing(name) {
    override val extends: ManufacturingEntity = ShapeBasedCostModuleManufacturing(name)

    override val model: Model
        get() = Model.ALEX

    override val modelSteps: List<KClass<out ManufacturingEntity>>
        get() =
            listOf(
                ManufacturingStepAluExtrusion::class,
                ManufacturingStepAlexSawing::class,
                ManufacturingStepAlexSawing::class,
                ManufacturingStepHeatTreatment::class,
                ManufacturingStepAnodizing::class,
                ManufacturingStepAlexMilling::class,
                ManufacturingStepAlexSawing::class,
                ManufacturingStepAlexWelding::class,
                ManufacturingStepStretchBending::class,
                ManufacturingStepPrecisionStretching::class,
                ManufacturingStepCleaning::class,
            )

    @BehaviourCreation
    fun createPartBehaviour(): DynamicBehaviour = EntityBasedDynamicBehaviour(this, ShapeBasedAluExtrusionBehaviorLookup(name))

    fun inputGroupInternal(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
    ) = lookupReaderService.getInputGroupFromShapeLookup(calculationContext!!.accessCheck, shapeId, Model.ALEX)

    @Input
    @TsetSuppress("tset:engine:field-override-configuration-value")
    fun technology() = Text("ALEX")

    fun internalCallsPerYear() = Num(12.0)

    @Input
    @Path("/api/model/alex/materialName")
    fun materialName(): Text? = null

    @Input
    @WizardField(index = 61)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @Condition(field = "stepSubTypeHeatTreatmentAlu", value = "NO_HEAT_TREATMENT", operator = Condition.NOT_EQUALS)
    fun maxWallThickness(
        @Parent(Entities.PROCESSED_MATERIAL) @Default(NullProvider::class)
        maxWallThickness: Length?,
    ) = maxWallThickness ?: Length(2.0, LengthUnits.MILLIMETER)

    @Input
    @Precompute
    @WizardField(index = 50)
    fun needsAnodizing(): SelectableBoolean = SelectableBoolean.FALSE

    @Input
    @Precompute
    @WizardField(index = 52)
    @Condition(field = "needsAnodizing", value = "TRUE", operator = Condition.EQUALS)
    fun needsColoring(): SelectableBoolean = SelectableBoolean.FALSE

    @Input
    @Precompute
    @WizardField(index = 51)
    @DefaultUnit(DefaultUnit.MICROMETER)
    @Condition(field = "needsAnodizing", value = "TRUE", operator = Condition.EQUALS)
    fun anodizingThickness(): Length? = null

    @Input
    @Precompute
    @WizardField(index = 70)
    fun extrusionMachining(): SelectableBoolean = SelectableBoolean.FALSE

    @Input
    @Precompute
    @WizardField(index = 71)
    @Condition(field = "extrusionMachining", value = "TRUE", operator = Condition.EQUALS)
    fun machiningSpeed(): MachiningSpeed = MachiningSpeed.NORMAL

    @Input
    @Precompute
    @WizardField(index = 72)
    @Condition(field = "extrusionMachining", value = "true", operator = Condition.EQUALS)
    fun numOfDrilledHoles(): QuantityUnit = QuantityUnit(0.0)

    @Input
    @Precompute
    @WizardField(index = 73)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @Condition(field = "extrusionMachining", value = "true", operator = Condition.EQUALS)
    fun millingLength(): Length = Length(0.0, LengthUnits.METER)

    @Input
    @Precompute
    @WizardField(index = 74)
    @Condition(field = "extrusionMachining", value = "true", operator = Condition.EQUALS)
    fun numOfToolChanges(): QuantityUnit? = null

    @Input
    @Precompute
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @Condition(field = "extrusionMachining", value = "true", operator = Condition.EQUALS)
    fun rapidTraverseLength(): Length = Length(0.0, LengthUnits.METER)

    @Input
    @Precompute
    @WizardField(index = 80)
    fun bendingType(): BendingType = BendingType.NO_BENDING

    @Input
    @Precompute
    @WizardField(index = 90)
    fun needsPrecisionStretching(): SelectableBoolean = SelectableBoolean.FALSE

    @Input
    @Precompute
    @WizardField(index = 100)
    fun needsAluExtrusionWelding(): SelectableBoolean = SelectableBoolean.FALSE

    @Input
    @Precompute
    @WizardField(index = 110)
    @Condition(field = "needsAluExtrusionWelding", value = "true", operator = Condition.EQUALS)
    fun numOfWeldingPoints(): QuantityUnit = QuantityUnit(0.0)

    @Input
    @WizardField(index = 60)
    fun stepSubTypeHeatTreatmentAlu() = StepSubTypeHeatTreatmentAlu.NO_HEAT_TREATMENT

    @Input
    fun coefficientBulkMaterialLookupKey(): Text = Text("ALEX")

    @Input
    fun materialClass(): Text = Text("MaterialAluExtrusion")

    @Input
    fun preheated(): Bool? = null

    @EntityCreation(Entities.PROCESSED_MATERIAL, childCreations = [Entities.MANUFACTURING_STEP])
    fun createMaterial(): ManufacturingEntity =
        createEntity(
            name = "AluExtrudedMaterial",
            clazz = AluExtrudedMaterial::class,
            entityType = Entities.MATERIAL,
        )

    @Input
    @WizardField(index = 121)
    @Condition(field = "cleaningNeeded", value = "true", operator = Condition.EQUALS)
    fun cleanness(): StepSubTypeCleanness = StepSubTypeCleanness.NORMAL

    @Input
    @WizardField(index = 120)
    fun cleaningNeeded(): SelectableBoolean = SelectableBoolean.FALSE

    fun defaultMaxExtrudedLength() = Length(7.0, LengthUnits.METER)

    fun defaultMaxNumberOfPartsPerOneMaxExtrudedLength(
        defaultMaxExtrudedLength: Length,
        grossLengthPerPart: Length,
    ): QuantityUnit = QuantityUnit((defaultMaxExtrudedLength / grossLengthPerPart).res.setScale(0, RoundingMode.DOWN))

    // length we use to optimize
    fun grossLengthPerPart(
        secondaryProcessScrap: Length,
        secondarySawingScrap: Length,
        partLength: Length,
    ): Length = secondaryProcessScrap + secondarySawingScrap + partLength

    fun secondaryProcessScrap(
        bendingType: BendingType,
        scrapBending: Length,
        needsPrecisionStretching: SelectableBoolean,
        scrapStretching: Length,
    ): Length {
        val bending =
            when {
                bendingType != BendingType.NO_BENDING -> Length(scrapBending.res, LengthUnits.METER)
                else -> Length(BigDecimal.ZERO, LengthUnits.METER)
            }
        val stretching =
            when {
                needsPrecisionStretching == SelectableBoolean.TRUE -> Length(scrapStretching.res, LengthUnits.METER)
                else -> Length(BigDecimal.ZERO, LengthUnits.METER)
            }
        return bending + stretching
    }

    fun scrapBending() = Length(60.0, LengthUnits.MILLIMETER)

    fun scrapStretching() = Length(120.0, LengthUnits.MILLIMETER)

    fun secondarySawingScrap() = Length(6.0, LengthUnits.MILLIMETER)
}
