package com.nu.bom.core.technologies.steps.coating

import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.FreezeImplementation
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.Selectable
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.annotations.SubLabel
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.MissingInputError
import com.nu.bom.core.manufacturing.entities.SingleGroupManufacturingStep
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.ManufacturingStepType
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.AreaUnits
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeGrouping
import com.nu.bom.core.manufacturing.fieldTypes.ExchangeRatesField
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.NozzleTypeCoating
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.Speed
import com.nu.bom.core.manufacturing.fieldTypes.SpeedUnits
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeCoating
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.ToolAllocationMode
import com.nu.bom.core.manufacturing.fieldTypes.quot
import com.nu.bom.core.technologies.steps.coating.cycletimestep.CoatingBottomCycleTimeStepGroup
import com.nu.bom.core.technologies.steps.coating.cycletimestep.CoatingTopCycleTimeStepGroup
import com.nu.bom.core.technologies.steps.coating.cycletimestep.FlippingCycleTimeStepGroup
import com.nu.bom.core.utils.annotations.TsetSuppress
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.math.BigDecimal
import java.math.RoundingMode

@EntityType(Entities.MANUFACTURING_STEP)
class ManufacturingStepCoating(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = SingleGroupManufacturingStep(name)

    fun grouping(numberCoatingMachines: Pieces): CycleTimeGrouping =
        when {
            numberCoatingMachines.res > BigDecimal.ONE -> CycleTimeGrouping.Parallel
            else -> CycleTimeGrouping.Sequential
        }

    @Input
    @SubLabel
    @ReadOnly
    @Selectable(value = "manufacturingStepType", getDisplayNameFromPathQuery = true)
    fun manufacturingStepType(): Text = Text(ManufacturingStepType.ELECTRONICS_PCBA.name)

    @Input
    val scrapRate: Rate = Rate(0.001)

    @Input
    val utilizationRate: Rate = Rate(0.85.toBigDecimal())

    @TsetSuppress("tset:engine:field-override-configuration-value")
    fun toolAllocationMode(): ToolAllocationMode = ToolAllocationMode(ToolAllocationMode.Mode.PAY_NONE)

    val frameLength = Length(0.5, LengthUnits.METER)
    val frameWidth = Length(0.5, LengthUnits.METER)

    val nestingFactor = Rate(0.8)

    fun filmThickness(
        @Parent(Entities.MANUFACTURING)
        stepSubTypeCoating: StepSubTypeCoating,
    ): Length =
        when (stepSubTypeCoating.res) {
            StepSubTypeCoating.Selection.SILICONE -> Length(0.15.toBigDecimal(), LengthUnits.MILLIMETER)
            StepSubTypeCoating.Selection.ACRYLIC, StepSubTypeCoating.Selection.EPOXY, StepSubTypeCoating.Selection.URETHANE ->
                Length(
                    0.09.toBigDecimal(),
                    LengthUnits.MILLIMETER,
                )
        }

    fun nozzleType(
        @Parent(Entities.MANUFACTURING)
        stepSubTypeCoating: StepSubTypeCoating,
    ): NozzleTypeCoating =
        when (stepSubTypeCoating.res) {
            StepSubTypeCoating.Selection.EPOXY -> NozzleTypeCoating.TWO_COMPONENT_SYSTEM
            StepSubTypeCoating.Selection.SILICONE -> NozzleTypeCoating.SCREW_FEEDER
            StepSubTypeCoating.Selection.URETHANE, StepSubTypeCoating.Selection.ACRYLIC -> NozzleTypeCoating.CURTAIN_NOZZLE
        }

    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun sprayingWidth(nozzleType: NozzleTypeCoating): Length =
        when (nozzleType.res) {
            NozzleTypeCoating.Selection.CURTAIN_NOZZLE -> Length(12.0.toBigDecimal(), LengthUnits.MILLIMETER)
            NozzleTypeCoating.Selection.SCREW_FEEDER, NozzleTypeCoating.Selection.TWO_COMPONENT_SYSTEM ->
                Length(
                    10.0.toBigDecimal(),
                    LengthUnits.MILLIMETER,
                )
        }

    @DefaultUnit(DefaultUnit.MM_PER_SEC)
    fun sprayingSpeed(nozzleType: NozzleTypeCoating): Speed =
        when (nozzleType.res) {
            NozzleTypeCoating.Selection.CURTAIN_NOZZLE -> Speed(400.0.toBigDecimal(), SpeedUnits.MM_PER_SEC)
            NozzleTypeCoating.Selection.TWO_COMPONENT_SYSTEM, NozzleTypeCoating.Selection.SCREW_FEEDER ->
                Speed(
                    40.0.toBigDecimal(),
                    SpeedUnits.MM_PER_SEC,
                )
        }

    fun internalPartsPerCycle(
        @Parent(Entities.MANUFACTURING)
        pcbsPerPanel: Pieces,
        frameLength: Length,
        frameWidth: Length,
        @Parent(Entities.MANUFACTURING)
        pcbLength: Length,
        @Parent(Entities.MANUFACTURING)
        pcbWidth: Length,
        @Parent(Entities.MANUFACTURING)
        coatingAfterSingulation: SelectableBoolean,
        nestingFactor: Rate,
    ): QuantityUnit {
        val pcbsPerLength = quot(frameLength, pcbLength).res.setScale(0, RoundingMode.DOWN)
        val pcbsPerWidth = quot(frameWidth, pcbWidth).res.setScale(0, RoundingMode.DOWN)

        return when (coatingAfterSingulation) {
            SelectableBoolean.FALSE -> QuantityUnit((pcbsPerPanel).res.setScale(0, RoundingMode.DOWN))
            else -> QuantityUnit((nestingFactor * pcbsPerLength * pcbsPerWidth).res.setScale(0, RoundingMode.DOWN))
        }
    }

    @DefaultUnit(DefaultUnit.SECOND)
    val movingTimeFrame = Time(3.0, TimeUnits.SECOND)

    @DefaultUnit(DefaultUnit.SECOND)
    val movingTimeSprayNozzle = Time(1.0, TimeUnits.SECOND)

    @DefaultUnit(DefaultUnit.QMM)
    fun coatingAreaTop(
        @Parent(Entities.MANUFACTURING)
        pcbLength: Length,
        @Parent(Entities.MANUFACTURING)
        pcbWidth: Length,
        @Parent(Entities.MANUFACTURING)
        coatingAreaTopSide: Rate,
    ): Area = Area(pcbLength.inMillimeter * pcbWidth.inMillimeter * coatingAreaTopSide.res, AreaUnits.QMM)

    @DefaultUnit(DefaultUnit.QMM)
    fun coatingAreaTopTotal(
        coatingAreaTop: Area,
        partsPerCycle: QuantityUnit,
    ): Area = Area(coatingAreaTop.inQmm * partsPerCycle.res, AreaUnits.QMM)

    @DefaultUnit(DefaultUnit.QMM)
    fun coatingAreaBottom(
        @Parent(Entities.MANUFACTURING)
        pcbLength: Length,
        @Parent(Entities.MANUFACTURING)
        pcbWidth: Length,
        @Parent(Entities.MANUFACTURING)
        coatingAreaBottomSide: Rate,
    ): Area = Area(pcbLength.inMillimeter * pcbWidth.inMillimeter * coatingAreaBottomSide.res, AreaUnits.QMM)

    @DefaultUnit(DefaultUnit.QMM)
    fun coatingAreaBottomTotal(
        coatingAreaBottom: Area,
        partsPerCycle: QuantityUnit,
    ): Area = Area(coatingAreaBottom.inQmm * partsPerCycle.res, AreaUnits.QMM)

    fun movingTime(
        movingTimeFrame: Time,
        movingTimeSprayNozzle: Time,
        @Parent(Entities.MANUFACTURING)
        pcbsPerPanel: Pieces,
    ): Time = movingTimeFrame + movingTimeSprayNozzle * pcbsPerPanel

    fun sprayingLengthTopSidePerPCB(
        coatingAreaTop: Area,
        sprayingWidth: Length,
    ): Length = Length(coatingAreaTop.inQmm / sprayingWidth.inMillimeter, LengthUnits.MILLIMETER)

    fun sprayingLengthBottomSidePerPCB(
        coatingAreaBottom: Area,
        sprayingWidth: Length,
    ): Length = Length(coatingAreaBottom.inQmm / sprayingWidth.inMillimeter, LengthUnits.MILLIMETER)

    fun sprayingTimeTopSidePerPCB(
        sprayingLengthTopSidePerPCB: Length,
        sprayingSpeed: Speed,
    ): Time = Time(sprayingLengthTopSidePerPCB.inMillimeter / sprayingSpeed.inMMPerSec, TimeUnits.SECOND)

    fun sprayingTimeBottomSidePerPCB(
        sprayingLengthBottomSidePerPCB: Length,
        sprayingSpeed: Speed,
    ): Time = Time(sprayingLengthBottomSidePerPCB.inMillimeter / sprayingSpeed.inMMPerSec, TimeUnits.SECOND)

    fun processTimeTopSide(
        sprayingTimeTopSidePerPCB: Time,
        @Parent(Entities.MANUFACTURING)
        coatingAreaTopSide: Rate,
        partsPerCycle: QuantityUnit,
    ): Time =
        when {
            coatingAreaTopSide.res > BigDecimal.ZERO -> sprayingTimeTopSidePerPCB * partsPerCycle
            else -> Time(BigDecimal.ZERO, TimeUnits.SECOND)
        }

    @Input
    @ReadOnly
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 2)
    @SpecialLink("SL-940", "quantity")
    fun numberCoatingMachines(): Pieces = throw MissingInputError()

    @DefaultUnit(DefaultUnit.SECOND)
    fun flippingTime(
        @Parent(Entities.MANUFACTURING)
        coatingAreaTopSide: Rate,
        @Parent(Entities.MANUFACTURING)
        coatingAreaBottomSide: Rate,
    ): Time =
        when {
            coatingAreaBottomSide.res > BigDecimal.ZERO && coatingAreaTopSide.res > BigDecimal.ZERO ->
                Time(
                    2.0,
                    TimeUnits.SECOND,
                )

            else -> Time(BigDecimal.ZERO, TimeUnits.SECOND)
        }

    fun processTimeBottomSide(
        sprayingTimeBottomSidePerPCB: Time,
        @Parent(Entities.MANUFACTURING)
        coatingAreaBottomSide: Rate,
        partsPerCycle: QuantityUnit,
    ): Time =
        when {
            coatingAreaBottomSide.res > BigDecimal.ZERO -> sprayingTimeBottomSidePerPCB * partsPerCycle
            else -> Time(BigDecimal.ZERO, TimeUnits.SECOND)
        }

    private class TemplateLookupEntry(
        val templateName: String,
        val numberCoatingMachines: Pieces,
        val AOI: Bool,
    )

    fun templateName(
        @Parent(Entities.MANUFACTURING)
        coatingAreaTopSide: Rate,
        @Parent(Entities.MANUFACTURING)
        coatingAreaBottomSide: Rate,
        @Parent(Entities.MANUFACTURING)
        coatingAOI: Bool,
    ): Mono<Text> {
        val hasCoatingTopSide = coatingAreaTopSide.res > BigDecimal.ZERO
        val hasCoatingBottomSide = coatingAreaBottomSide.res > BigDecimal.ZERO
        val initialNumberCoatingMachines =
            when {
                hasCoatingTopSide && hasCoatingBottomSide -> Pieces(2.0)
                hasCoatingTopSide || hasCoatingBottomSide -> Pieces(1.0)
                else -> throw IllegalArgumentException("no matching number of coating machines")
            }

        return services
            .getLookupTable("ManufacturingStepCoating_Templates") {
                ManufacturingStepCoating.TemplateLookupEntry(
                    templateName = it[0],
                    numberCoatingMachines = Pieces(it[1]),
                    AOI = Bool(it[2]),
                )
            }.filter {
                val diffCoatingSides = initialNumberCoatingMachines.res.compareTo(it.numberCoatingMachines.res) == 0
                val diffAOI = (coatingAOI == it.AOI)
                diffCoatingSides && diffAOI
            }.single()
            .map { Text(it.templateName) }
    }

    @EntityCreation(Entities.CYCLETIME_STEP_GROUP)
    fun createCycleTimeStepGroup(
        flippingTime: Time,
        @Parent(Entities.MANUFACTURING)
        coatingAreaTopSide: Rate,
        @Parent(Entities.MANUFACTURING)
        coatingAreaBottomSide: Rate,
    ): List<ManufacturingEntity> {
        val results = mutableListOf<ManufacturingEntity>()

        if (coatingAreaTopSide.res > BigDecimal.ZERO) {
            results.add(
                createEntity(
                    name = CoatingTopCycleTimeStepGroup::class.simpleName!!,
                    clazz = CoatingTopCycleTimeStepGroup::class,
                    entityType = Entities.CYCLETIME_STEP_GROUP,
                ),
            )
        }

        if (
            flippingTime.inSeconds > BigDecimal.ZERO &&
            coatingAreaBottomSide.res > BigDecimal.ZERO &&
            coatingAreaTopSide.res > BigDecimal.ZERO
        ) {
            results.add(
                createEntity(
                    name = FlippingCycleTimeStepGroup::class.simpleName!!,
                    clazz = FlippingCycleTimeStepGroup::class,
                    entityType = Entities.CYCLETIME_STEP_GROUP,
                ),
            )
        }

        if (coatingAreaBottomSide.res > BigDecimal.ZERO) {
            results.add(
                createEntity(
                    name = CoatingBottomCycleTimeStepGroup::class.simpleName!!,
                    clazz = CoatingBottomCycleTimeStepGroup::class,
                    entityType = Entities.CYCLETIME_STEP_GROUP,
                ),
            )
        }

        return results
    }

    @EntityCreation(Entities.MACHINE)
    fun createMachines(
        templateName: Text,
        @Parent(Entities.MANUFACTURING) exchangeRates: ExchangeRatesField,
    ): Flux<ManufacturingEntity> = createMachinesFromTemplate(templateName)

    @EntityCreation(Entities.LABOR)
    fun createLabor(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> = createLaborFromTemplate(templateName, locationName)

    @EntityCreation(Entities.SETUP)
    fun createSetup(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> = createSetupFromTemplate(templateName, locationName)

    @EntityCreation(Entities.TOOL)
    fun createTools(
        templateName: Text,
        @Parent(Entities.MANUFACTURING) exchangeRates: ExchangeRatesField,
    ): Flux<ManufacturingEntity> = createToolFromTemplate(templateName)

    @EntityCreation(Entities.MATERIAL)
    @FreezeImplementation
    fun createConsumables(
        @Parent(Entities.MANUFACTURING)
        stepSubTypeCoating: StepSubTypeCoating,
    ): Mono<ManufacturingEntity> {
        val name =
            when (stepSubTypeCoating.res) {
                StepSubTypeCoating.Selection.EPOXY -> "Epoxy-RAW_MATERIAL_COATING_PCBA"
                StepSubTypeCoating.Selection.SILICONE -> "Silicone-RAW_MATERIAL_COATING_PCBA"
                StepSubTypeCoating.Selection.ACRYLIC -> "Acrylic-RAW_MATERIAL_COATING_PCBA"
                StepSubTypeCoating.Selection.URETHANE -> "Urethane-RAW_MATERIAL_COATING_PCBA"
            }
        return createEntityWithNewMasterdata(
            name = name,
            entityType = Entities.MATERIAL,
            clazz = "MaterialPcbaCoating",
            masterDataType = MasterDataType.RAW_MATERIAL_COATING_PCBA,
            masterDataKey = name,
        )
    }
}
