package com.nu.bom.core.technologies.steps.turn.cycletimestep

import com.nu.bom.core.manufacturing.annotations.Condition
import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntitySubtypes
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.Max
import com.nu.bom.core.manufacturing.annotations.Min
import com.nu.bom.core.manufacturing.annotations.Nocalc
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.defaults.NullProvider
import com.nu.bom.core.manufacturing.entities.CycleTimeStep
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.Deg
import com.nu.bom.core.manufacturing.fieldTypes.GearHobInfoFieldResult
import com.nu.bom.core.manufacturing.fieldTypes.GearHobbingOperationInfoResponseFieldResult
import com.nu.bom.core.manufacturing.fieldTypes.GearHobbingToothTypeFieldResult
import com.nu.bom.core.manufacturing.fieldTypes.GearHobbingToothingInfoFieldResult
import com.nu.bom.core.manufacturing.fieldTypes.GearingCutStrategy
import com.nu.bom.core.manufacturing.fieldTypes.GearingToothType
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Revolution
import com.nu.bom.core.manufacturing.fieldTypes.RevolutionUnits
import com.nu.bom.core.manufacturing.fieldTypes.SingleCutRequestFieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Speed
import com.nu.bom.core.manufacturing.fieldTypes.SpeedUnits
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.turn.DoubleCutRequest
import com.nu.bom.core.manufacturing.fieldTypes.turn.GearHobInfo
import com.nu.bom.core.manufacturing.fieldTypes.turn.GearHobbingCuttingMethod
import com.nu.bom.core.manufacturing.fieldTypes.turn.GearHobbingToothingInfo
import com.nu.bom.core.manufacturing.fieldTypes.turn.Helical
import com.nu.bom.core.manufacturing.fieldTypes.turn.SingleCutRequest
import com.nu.bom.core.manufacturing.fieldTypes.turn.Spur
import com.nu.bom.core.manufacturing.fieldTypes.turn.cut1
import com.nu.bom.core.manufacturing.fieldTypes.turn.cut2
import com.nu.bom.core.turn.model.GearHobbingOperationInfo
import com.nu.bom.core.turn.model.toDomain
import reactor.core.publisher.Mono
import kotlin.math.PI

@EntityType(Entities.CYCLETIME_STEP, userCreatable = true)
@EntitySubtypes(["TURNING"])
class ManualGearHobbing(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = CycleTimeStep(name)

    private companion object {
        val MODULE_THRESHOLD = Length(3.0, LengthUnits.MILLIMETER)
        val LOADING_AND_UNLOADING_TIME = Time(5.0, TimeUnits.SECOND)
    }

    //region basic inputs
    @Input
    @MandatoryForEntity(index = 1, refresh = true)
    fun toothType() = GearingToothType.SPUR

    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @MandatoryForEntity(index = 10, refresh = true)
    @Condition(field = "toothType", value = "HELICAL", operator = Condition.EQUALS)
    fun helixAngle(): Deg = Deg(15.0)

    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @MandatoryForEntity(index = 20, refresh = true)
    fun module(): Length? = null

    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @MandatoryForEntity(index = 30, refresh = true)
    fun numberOfTeeth(): Num? = null

    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @MandatoryForEntity(index = 40, refresh = true)
    fun toothWidth(): Length? = null

    @Input
    @MandatoryForEntity(index = 50, refresh = true)
    @Max(180.0)
    @Min(0.0)
    fun pressureAngle(): Deg? = null

    @MandatoryForEntity(index = 60, refresh = true, computed = true, showSystemValue = true)
    fun cuttingStrategy(module: Length): GearingCutStrategy =
        if (module > MODULE_THRESHOLD) {
            GearingCutStrategy.TWO_CUT
        } else {
            GearingCutStrategy.ONE_CUT
        }

    @Condition(field = "cuttingStrategy", value = "TWO_CUT", operator = Condition.EQUALS)
    @MandatoryForEntity(index = 70, refresh = true, computed = true, showSystemValue = true)
    fun ratioCut1(): Rate = Rate(0.8)

    //endregion
    //region hob
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @MandatoryForEntity(index = 80, refresh = true, section = "right", computed = true, showSystemValue = true)
    fun hobTipDiameter(module: Length): Length =
        if (module > MODULE_THRESHOLD) {
            Length(110.0, LengthUnits.MILLIMETER)
        } else {
            Length(60.0, LengthUnits.MILLIMETER)
        }

    @Input
    @MandatoryForEntity(index = 90, refresh = true, section = "right")
    fun hobNumberOfStarts(): Num? = null

    @MandatoryForEntity(index = 100, section = "right", refresh = true, computed = true, showSystemValue = true)
    fun hobNumberOfTeeth(initialTurningResponse: GearHobbingOperationInfoResponseFieldResult): Num? =
        initialTurningResponse.res.gearHobInfo.hobNumberOfTeeth
            ?.toBigDecimal()
            ?.let { Num(it) }

    //endregion
    //region cut-1
    @DefaultUnit(DefaultUnit.M_PER_MIN)
    @MandatoryForEntity(index = 110, refresh = true, section = "right", computed = true, showSystemValue = true)
    fun cuttingSpeedCut1(module: Length): Speed =
        if (module > MODULE_THRESHOLD) {
            Speed(120.0, SpeedUnits.M_PER_MIN)
        } else {
            Speed(300.0, SpeedUnits.M_PER_MIN)
        }

    @DefaultUnit(DefaultUnit.MILLIMETER)
    @MandatoryForEntity(index = 120, refresh = true, section = "right", computed = true, showSystemValue = true)
    fun maxChipLoadCut1(
        cuttingStrategy: GearingCutStrategy,
        module: Length,
    ): Length =
        when (cuttingStrategy.res) {
            GearingCutStrategy.Selection.ONE_CUT -> Length(0.2, LengthUnits.MILLIMETER)
            GearingCutStrategy.Selection.TWO_CUT ->
                if (module > MODULE_THRESHOLD) {
                    Length(0.4, LengthUnits.MILLIMETER)
                } else {
                    Length(0.2, LengthUnits.MILLIMETER)
                }
        }

    @DefaultUnit(DefaultUnit.MILLIMETER)
    @MandatoryForEntity(index = 130, refresh = true, section = "right", computed = true, showSystemValue = true)
    fun maxDepthFeedMarkingCut1(cuttingStrategy: GearingCutStrategy): Length =
        when (cuttingStrategy.res) {
            GearingCutStrategy.Selection.ONE_CUT -> Length(0.02, LengthUnits.MILLIMETER)
            GearingCutStrategy.Selection.TWO_CUT -> Length(0.05, LengthUnits.MILLIMETER)
        }

    @DefaultUnit(DefaultUnit.MILLIMETER)
    @MandatoryForEntity(index = 140, section = "right", refresh = true, computed = true, showSystemValue = true)
    fun feedRatePerRevolutionCut1(initialTurningResponse: GearHobbingOperationInfoResponseFieldResult): Length =
        Length(
            initialTurningResponse.res.method
                .cut1()
                .feedRatePerRevolutionMm,
            LengthUnits.MILLIMETER,
        )

    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun actualChipLoadCut1(turningResponse: GearHobbingOperationInfoResponseFieldResult): Length =
        Length(
            turningResponse.res.method
                .cut1()
                .chipLoadMm,
            LengthUnits.MILLIMETER,
        )

    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun actualDepthFeedMarkingCut1(turningResponse: GearHobbingOperationInfoResponseFieldResult): Length =
        Length(
            turningResponse.res.method
                .cut1()
                .depthFeedMarkingMm,
            LengthUnits.MILLIMETER,
        )

    @ReadOnly
    fun rotationalSpeedCut1(turningResponse: GearHobbingOperationInfoResponseFieldResult): Revolution =
        Revolution(
            getRotationalSpeed(
                turningResponse.res.method
                    .cut1()
                    .cuttingSpeedMpmin,
                turningResponse.res.gearHobInfo.hobTipDiameterMm,
            ),
            RevolutionUnits.PER_MIN,
        )

    //endregion
    //region cut-2
    @DefaultUnit(DefaultUnit.M_PER_MIN)
    @Condition(field = "cuttingStrategy", value = "TWO_CUT", operator = Condition.EQUALS)
    @MandatoryForEntity(index = 150, refresh = true, section = "right", computed = true, showSystemValue = true)
    fun cuttingSpeedCut2(): Speed = Speed(120.0, SpeedUnits.M_PER_MIN)

    @DefaultUnit(DefaultUnit.MILLIMETER)
    @Condition(field = "cuttingStrategy", value = "TWO_CUT", operator = Condition.EQUALS)
    @MandatoryForEntity(index = 160, refresh = true, section = "right", computed = true, showSystemValue = true)
    fun maxChipLoadCut2(): Length = Length(0.2, LengthUnits.MILLIMETER)

    @DefaultUnit(DefaultUnit.MILLIMETER)
    @Condition(field = "cuttingStrategy", value = "TWO_CUT", operator = Condition.EQUALS)
    @MandatoryForEntity(index = 170, refresh = true, section = "right", computed = true, showSystemValue = true)
    fun maxDepthFeedMarkingCut2(): Length = Length(0.02, LengthUnits.MILLIMETER)

    @DefaultUnit(DefaultUnit.MILLIMETER)
    @Condition(field = "cuttingStrategy", value = "TWO_CUT", operator = Condition.EQUALS)
    @MandatoryForEntity(index = 180, refresh = true, section = "right", computed = true, showSystemValue = true)
    fun feedRatePerRevolutionCut2(initialTurningResponse: GearHobbingOperationInfoResponseFieldResult): Length? =
        initialTurningResponse.res.method
            .cut2()
            ?.feedRatePerRevolutionMm
            ?.let { Length(it, LengthUnits.MILLIMETER) }

    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @Condition(field = "cuttingStrategy", value = "TWO_CUT", operator = Condition.EQUALS)
    fun actualChipLoadCut2(turningResponse: GearHobbingOperationInfoResponseFieldResult): Length? =
        turningResponse.res.method
            .cut2()
            ?.chipLoadMm
            ?.let { Length(it, LengthUnits.MILLIMETER) }

    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @Condition(field = "cuttingStrategy", value = "TWO_CUT", operator = Condition.EQUALS)
    fun actualDepthFeedMarkingCut2(turningResponse: GearHobbingOperationInfoResponseFieldResult): Length? =
        turningResponse.res.method
            .cut2()
            ?.depthFeedMarkingMm
            ?.let { Length(it, LengthUnits.MILLIMETER) }

    @ReadOnly
    @Condition(field = "cuttingStrategy", value = "TWO_CUT", operator = Condition.EQUALS)
    fun rotationalSpeedCut2(turningResponse: GearHobbingOperationInfoResponseFieldResult): Revolution =
        Revolution(
            turningResponse.res.method
                .cut2()
                ?.cuttingSpeedMpmin
                ?.let {
                    getRotationalSpeed(
                        it,
                        turningResponse.res.gearHobInfo.hobTipDiameterMm,
                    )
                } ?: 0.0,
            RevolutionUnits.PER_MIN,
        )

    //endregion
    //region helper methods
    fun toothingType(
        toothType: GearingToothType,
        helixAngle: Deg,
    ): GearHobbingToothTypeFieldResult =
        when (toothType.res) {
            GearingToothType.Selection.SPUR -> GearHobbingToothTypeFieldResult(Spur)
            GearingToothType.Selection.HELICAL -> GearHobbingToothTypeFieldResult(Helical(helixAngle))
        }

    fun gearHobInfo(
        hobTipDiameter: Length,
        hobNumberOfTeeth: Num,
        hobNumberOfStarts: Num,
    ) = GearHobInfoFieldResult(GearHobInfo(hobTipDiameter, hobNumberOfTeeth, hobNumberOfStarts))

    fun toothingInfo(
        toothingType: GearHobbingToothTypeFieldResult,
        numberOfTeeth: Num,
        pressureAngle: Deg,
        toothWidth: Length,
    ): GearHobbingToothingInfoFieldResult =
        GearHobbingToothingInfoFieldResult(
            GearHobbingToothingInfo(
                numberOfTeeth,
                pressureAngle.rad,
                toothingType.res,
                toothWidth,
            ),
        )

    fun initialSingleCut(
        cuttingSpeedCut1: Speed,
        maxChipLoadCut1: Length,
        maxDepthFeedMarkingCut1: Length,
    ): SingleCutRequestFieldResult =
        SingleCutRequestFieldResult(
            SingleCutRequest(
                cuttingSpeedCut1,
                null,
                maxChipLoadCut1,
                maxDepthFeedMarkingCut1,
            ),
        )

    fun singleCut(
        cuttingSpeedCut1: Speed,
        feedRatePerRevolutionCut1: Length?,
        maxChipLoadCut1: Length,
        maxDepthFeedMarkingCut1: Length,
    ): SingleCutRequestFieldResult =
        SingleCutRequestFieldResult(
            SingleCutRequest(
                cuttingSpeedCut1,
                feedRatePerRevolutionCut1,
                maxChipLoadCut1,
                maxDepthFeedMarkingCut1,
            ),
        )

    //endregion
    //region turning call

    /**
     * Initial Turning call without feedRate and hobNumberOfTeeth
     * feedRate and hobNumberOfTeeth will be pre-calculated in this step and can later be overwritten
     * @return turning response where feedRate and hobNumberOfTeeth will be fetched
     */
    fun initialTurningResponse(
        module: Length,
        toothingInfo: GearHobbingToothingInfoFieldResult,
        hobTipDiameter: Length,
        hobNumberOfStarts: Num,
        initialSingleCut: SingleCutRequestFieldResult,
        cuttingStrategy: GearingCutStrategy,
        ratioCut1: Rate,
        cuttingSpeedCut2: Speed,
        maxChipLoadCut2: Length,
        maxDepthFeedMarkingCut2: Length,
    ): Mono<GearHobbingOperationInfoResponseFieldResult> {
        val method =
            getGearHobbingCuttingMethod(
                cuttingStrategy,
                initialSingleCut,
                cuttingSpeedCut2,
                null,
                maxChipLoadCut2,
                maxDepthFeedMarkingCut2,
                ratioCut1,
            )
        val initialHobInfo = GearHobInfo(hobTipDiameter, null, hobNumberOfStarts)
        val request = GearHobbingOperationInfo.fromParams(module, toothingInfo.res, initialHobInfo, method)
        return this.services.getTurningOperationCycleTime(request).map {
            val response = it.data as com.nu.bom.core.turn.model.GearHobbingOperationInfoResponse
            GearHobbingOperationInfoResponseFieldResult(
                response.toDomain(),
            )
        }
    }

    /**
     * Main Turning call with the already pre-filled (or-overwritten) values
     * @return cycle times
     */
    fun turningResponse(
        module: Length,
        toothingInfo: GearHobbingToothingInfoFieldResult,
        gearHobInfo: GearHobInfoFieldResult,
        cuttingStrategy: GearingCutStrategy,
        singleCut: SingleCutRequestFieldResult,
        ratioCut1: Rate,
        cuttingSpeedCut2: Speed,
        @Default(NullProvider::class) feedRatePerRevolutionCut2: Length?,
        maxChipLoadCut2: Length,
        maxDepthFeedMarkingCut2: Length,
    ): Mono<GearHobbingOperationInfoResponseFieldResult> {
        val method =
            getGearHobbingCuttingMethod(
                cuttingStrategy,
                singleCut,
                cuttingSpeedCut2,
                feedRatePerRevolutionCut2,
                maxChipLoadCut2,
                maxDepthFeedMarkingCut2,
                ratioCut1,
            )
        val turningServiceRequest = GearHobbingOperationInfo.fromParams(module, toothingInfo.res, gearHobInfo.res, method)
        return this.services.getTurningOperationCycleTime(turningServiceRequest).map {
            val response = it.data as com.nu.bom.core.turn.model.GearHobbingOperationInfoResponse
            GearHobbingOperationInfoResponseFieldResult(
                response.toDomain(),
            )
        }
    }

    @Nocalc
    private fun getGearHobbingCuttingMethod(
        cuttingStrategy: GearingCutStrategy,
        singleCut: SingleCutRequestFieldResult,
        cuttingSpeedCut2: Speed,
        feedRatePerRevolutionCut2: Length?,
        maxChipLoadCut2: Length,
        maxDepthFeedMarkingCut2: Length,
        ratioCut1: Rate,
    ): GearHobbingCuttingMethod {
        val method =
            when (cuttingStrategy.res) {
                GearingCutStrategy.Selection.ONE_CUT -> singleCut.res
                GearingCutStrategy.Selection.TWO_CUT -> {
                    val cut2 =
                        SingleCutRequest(
                            cuttingSpeedCut2,
                            feedRatePerRevolutionCut2,
                            maxChipLoadCut2,
                            maxDepthFeedMarkingCut2,
                        )
                    DoubleCutRequest(singleCut.res, cut2, ratioCut1.res.toDouble())
                }
            }
        return method
    }

    @Nocalc
    private fun getRotationalSpeed(
        cuttingSpeed: Double,
        hobDiameter: Double,
    ): Double = cuttingSpeed / (hobDiameter * PI)

    //endregion
    //region kpi's

    @ReadOnly
    @MandatoryForEntity(index = 1, computed = true, section = "kpi", readOnly = true)
    fun primaryTime(turningResponse: GearHobbingOperationInfoResponseFieldResult) =
        Time(turningResponse.res.cycleTimes.primaryTime, TimeUnits.SECOND)

    @MandatoryForEntity(index = 2, section = "kpi", readOnly = true, computed = true, showSystemValue = true)
    fun secondaryTime(turningResponse: GearHobbingOperationInfoResponseFieldResult) =
        Time(turningResponse.res.cycleTimes.secondaryTime, TimeUnits.SECOND) + LOADING_AND_UNLOADING_TIME

    @ReadOnly
    @Input(active = false)
    @MandatoryForEntity(index = 3, computed = true, section = "kpi", readOnly = true)
    fun time(
        primaryTime: Time,
        secondaryTime: Time,
    ): CycleTime = CycleTime(primaryTime + secondaryTime)
    //endregion
}
