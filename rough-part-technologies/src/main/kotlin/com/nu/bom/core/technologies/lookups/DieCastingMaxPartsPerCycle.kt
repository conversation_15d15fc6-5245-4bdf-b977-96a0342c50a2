package com.nu.bom.core.technologies.lookups

import java.math.BigDecimal

data class DieCastingMaxPartsPerCycle(
    val toolId: String,
    val maxPartsPerCycle: BigDecimal,
    val maxPartsPerCycleChina: BigDecimal,
)

val diecastingMaxPartsPerCycleReader: (row: List<String>) -> DieCastingMaxPartsPerCycle = { row ->
    DieCastingMaxPartsPerCycle(
        toolId = row[0],
        maxPartsPerCycle = row[1].toBigDecimal(),
        maxPartsPerCycleChina = row[2].toBigDecimal(),
    )
}
