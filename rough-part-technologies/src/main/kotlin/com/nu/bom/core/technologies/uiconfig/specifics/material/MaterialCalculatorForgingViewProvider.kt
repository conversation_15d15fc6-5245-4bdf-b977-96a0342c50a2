package com.nu.bom.core.technologies.uiconfig.specifics.material

import com.nu.bom.core.manufacturing.entities.MaterialCalculatorForging
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.FieldConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.FieldSectionConfigFeDto
import com.nu.bom.core.technologies.behaviours.BurrWeightBehavior
import com.nu.bom.core.technologies.manufacturings.dfor.material.MaterialForgingDfor

abstract class MaterialCalculatorForgingViewProvider : MaterialCalculatorBarViewProvider() {
    val materialCalculatorBarMirrorSection =
        FieldSectionConfigFeDto(
            listOf(
                MaterialCalculatorForging::mirrorInnerDiameter.name,
                MaterialCalculatorForging::mirrorHeight.name,
                MaterialCalculatorForging::mirrorWeight.name,
                MaterialCalculatorForging::fillInWeight.name,
            ),
            title = "mirror",
        )

    open val burrSection: FieldSectionConfigFeDto
        get() =
            FieldSectionConfigFeDto(
                listOf(
                    BurrWeightBehavior::burrWidth.name,
                    BurrWeightBehavior::burrHeight.name,
                    BurrWeightBehavior::burrLength.name,
                    MaterialForgingDfor::partOuterDiameter.name,
                    MaterialForgingDfor::burrWeight.name,
                ),
                title = "burr",
            )

    private val deployedWeightSection =
        FieldSectionConfigFeDto(
            listOf(
                MaterialCalculatorForging::netWeightPerPart.name,
                MaterialCalculatorForging::mirrorLossWeight.name,
                MaterialCalculatorForging::fillInLossWeight.name,
                MaterialCalculatorForging::burrLossWeight.name,
            ) + deployedWeightCommonFields,
            title = "deployedWeight",
        )

    override val materialSpecificCardCost: FieldConfigFeDto
        get() =
            FieldConfigFeDto(
                left =
                    listOf(
                        materialCalculatorBarMirrorSection,
                        burrSection,
                        deployedWeightSection,
                    ),
                right = materialCalculatorBarBlankSection,
            )
}
