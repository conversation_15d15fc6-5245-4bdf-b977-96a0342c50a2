package com.nu.bom.core.technologies.steps.hardening.systemparameter

import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.SystemParameter
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Weight

@EntityType(Entities.SYSTEM_PARAMETER)
class SystemParameterHardeningTempering(name: String) : ManufacturingEntity(name) {

    override val extends = SystemParameter(name)

    @Input
    @ReadOnly
    @ObjectView(ObjectView.SYSTEM, 5)
    val bulkMaterial: Bool? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.KILOGRAM)
    @ObjectView(ObjectView.SYSTEM, 10)
    val chargeWeight: Weight? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.SYSTEM, 20)
    val chargeFrameLength: Length? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.SYSTEM, 30)
    val chargeFrameWidth: Length? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.SYSTEM, 40)
    val chargeFrameHeight: Length? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.SYSTEM, 50)
    val lotHeatingSoaking: Num? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.SYSTEM, 60)
    val lotQuenching: Num? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.SYSTEM, 70)
    val lotCleaning: Num? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.SYSTEM, 80)
    val lotTemperingHeatingSoaking: Num? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.SYSTEM, 90)
    val lotTemperingCooling: Num? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.LITER)
    @ObjectView(ObjectView.SYSTEM, 100)
    val hardeningOil: Num? = null

    @Input
    @DefaultUnit(DefaultUnit.LITER)
    val propane: Num? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.SYSTEM, 110)
    val endomat: Num? = null
}
