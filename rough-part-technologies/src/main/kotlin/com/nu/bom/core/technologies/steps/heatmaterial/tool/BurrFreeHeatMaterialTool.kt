package com.nu.bom.core.technologies.steps.heatmaterial.tool

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.Tool
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Num

@EntityType(Entities.TOOL)
class BurrFreeHeatMaterialTool(name: String) : ManufacturingEntity(name) {

    override val extends = Tool(name)

    fun serviceLifeInCycles(
        @Parent(Entities.MANUFACTURING_STEP)
        cyclesOverLifeTime: Num
    ) = cyclesOverLifeTime
}
