package com.nu.bom.core.technologies.manufacturings.pbox

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.DisableEntityCreationInModularizedEntity
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityProvider
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Nocalc
import com.nu.bom.core.manufacturing.annotations.OrderedEntityCreation
import com.nu.bom.core.manufacturing.annotations.Parents
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.CustomProcurementTypeHelper
import com.nu.bom.core.manufacturing.defaults.NullProvider
import com.nu.bom.core.manufacturing.entities.BaseModelManufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.SubManufacturing
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.CO2CalculationOperationsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.CostCalculationOperationsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.CustomProcurementType
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.PiecesUnits
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.SurfaceDensity
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.technologies.manufacturings.pbox.material.MaterialPboxInkBlack
import com.nu.bom.core.technologies.manufacturings.pbox.material.MaterialPboxInkCyan
import com.nu.bom.core.technologies.manufacturings.pbox.material.MaterialPboxInkMagenta
import com.nu.bom.core.technologies.manufacturings.pbox.material.MaterialPboxInkYellow
import com.nu.bom.core.technologies.manufacturings.pbox.material.MaterialPboxOuterLiner
import com.nu.bom.core.technologies.manufacturings.pbox.material.MaterialPboxVarnish
import com.nu.bom.core.technologies.steps.pboxcomputetoprint.ManufacturingStepPboxComputeToPrint
import com.nu.bom.core.technologies.steps.pboxoffsetprinting.ManufacturingStepPboxOffsetPrinting
import com.nu.bom.core.utils.annotations.TsetSuppress
import reactor.core.publisher.Mono
import kotlin.reflect.KClass

@EntityType(Entities.MANUFACTURING)
@Suppress("unused")
class SubManufacturingPrintedCardboardBoxOuterLayerPrinted(
    name: String,
) : BaseModelManufacturing(name) {
    override val extends = SubManufacturing(name)

    override val model: Model
        get() = Model.PBOX

    override val modelSteps: List<KClass<out ManufacturingEntity>>
        get() =
            listOf()

    companion object {
        const val DEFAULT_INK_CYAN_MASTERDATA_KEY = "Pbox ink Cyan-RAW_MATERIAL_INK"
        const val DEFAULT_INK_BLACK_MASTERDATA_KEY = "Pbox ink Black-RAW_MATERIAL_INK"
        const val DEFAULT_INK_MAGENTA_MASTERDATA_KEY = "Pbox ink Magenta-RAW_MATERIAL_INK"
        const val DEFAULT_INK_YELLOW_MASTERDATA_KEY = "Pbox ink Yellow-RAW_MATERIAL_INK"
        const val DEFAULT_VARNISH_MASTERDATA_KEY = "Pbox Varnish-RAW_MATERIAL_VARNISH"
    }

    @TsetSuppress("tset:engine:field-override-configuration-value")
    fun technology() = Text(model.name)

    @Input
    fun costUnit() = Text(PiecesUnits.THOUSAND_PIECES.name)

    @Input
    fun customProcurementType(
        costCalculationOperationKey: CostCalculationOperationsConfigurationKey,
        @Default(NullProvider::class)
        cO2CalculationOperationKey: CO2CalculationOperationsConfigurationKey?,
    ): Mono<CustomProcurementType> =
        CustomProcurementTypeHelper
            .getDefaultInhouseProcurementType(costCalculationOperationKey, cO2CalculationOperationKey, services)

    // region Fields from parent Manufacturing through BomEntry

    @Input
    fun internalCallsPerYear(
        @Parents(Entities.BOM_ENTRY) internalCallsPerYear: List<Num>,
    ): Num = internalCallsPerYear.single()

    @Input
    @TsetSuppress("tset:engine:field-override-configuration-value")
    fun callsPerYear(
        @Parents(Entities.BOM_ENTRY) callsPerYear: List<Num>,
    ): Num = callsPerYear.single()

    fun materialKeyOuterLiner(
        @Parents(Entities.BOM_ENTRY) materialKeyOuterLiner: List<Text>,
    ): Text = materialKeyOuterLiner.single()

    fun varnishWeight(
        @Parents(Entities.BOM_ENTRY) varnishWeight: List<SurfaceDensity>,
    ): SurfaceDensity = varnishWeight.single()

    fun cyanShare(
        @Parents(Entities.BOM_ENTRY) cyanShare: List<Rate>,
    ): Rate = cyanShare.single()

    fun cyanPrintingWeight(
        @Parents(Entities.BOM_ENTRY) cyanPrintingWeight: List<SurfaceDensity>,
    ): SurfaceDensity = cyanPrintingWeight.single()

    fun magentaShare(
        @Parents(Entities.BOM_ENTRY) magentaShare: List<Rate>,
    ): Rate = magentaShare.single()

    fun magentaPrintingWeight(
        @Parents(Entities.BOM_ENTRY) magentaPrintingWeight: List<SurfaceDensity>,
    ): SurfaceDensity = magentaPrintingWeight.single()

    fun yellowShare(
        @Parents(Entities.BOM_ENTRY) yellowShare: List<Rate>,
    ): Rate = yellowShare.single()

    fun yellowPrintingWeight(
        @Parents(Entities.BOM_ENTRY) yellowPrintingWeight: List<SurfaceDensity>,
    ): SurfaceDensity = yellowPrintingWeight.single()

    fun blackShare(
        @Parents(Entities.BOM_ENTRY) blackShare: List<Rate>,
    ): Rate = blackShare.single()

    fun blackPrintingWeight(
        @Parents(Entities.BOM_ENTRY) blackPrintingWeight: List<SurfaceDensity>,
    ): SurfaceDensity = blackPrintingWeight.single()

    fun boxSurfaceArea(
        @Parents(Entities.BOM_ENTRY) boxSurfaceArea: List<Area>,
    ): Area = boxSurfaceArea.single()

    fun paintingAreaOutside(
        @Parents(Entities.BOM_ENTRY) paintingAreaOutside: List<Rate>,
    ): Rate = paintingAreaOutside.single()

    fun nestingResultSheetLength(
        @Parents(Entities.BOM_ENTRY) nestingResultSheetLength: List<Length>,
    ): Length = nestingResultSheetLength.single()

    fun nestingResultSheetWidth(
        @Parents(Entities.BOM_ENTRY) nestingResultSheetWidth: List<Length>,
    ): Length = nestingResultSheetWidth.single()

    fun nestingResultArea(
        @Parents(Entities.BOM_ENTRY) nestingResultArea: List<Area>,
    ): Area = nestingResultArea.single()

    fun nestingResultParts(
        @Parents(Entities.BOM_ENTRY) nestingResultParts: List<QuantityUnit>,
    ): QuantityUnit = nestingResultParts.single()

    // endregion

    @OrderedEntityCreation
    fun manufacturingSteps() =
        arrayOf(
            ::computeToPrint.name,
            ::offsetPrinting.name,
        )

    @EntityProvider
    fun computeToPrint() = createStepForCalculationModule(ManufacturingStepPboxComputeToPrint::class)

    @EntityProvider
    fun offsetPrinting() = createStepForCalculationModule(ManufacturingStepPboxOffsetPrinting::class)

    @DisableEntityCreationInModularizedEntity
    @EntityCreation(Entities.MATERIAL)
    fun createOuterLiner(materialKeyOuterLiner: Text) =
        createMaterialFromMasterdata(MaterialPboxOuterLiner::class, MasterDataType.RAW_MATERIAL_PAPER_SHEET, materialKeyOuterLiner.res)

    @Nocalc
    private fun <K : ManufacturingEntity> createMaterialInk(
        clazz: KClass<K>,
        masterDataKey: String,
    ) = createMaterialFromMasterdata(clazz, MasterDataType.RAW_MATERIAL_INK, masterDataKey)

    @DisableEntityCreationInModularizedEntity
    @EntityCreation(Entities.MATERIAL)
    fun createMaterialCyan() = createMaterialInk(MaterialPboxInkCyan::class, DEFAULT_INK_CYAN_MASTERDATA_KEY)

    @DisableEntityCreationInModularizedEntity
    @EntityCreation(Entities.MATERIAL)
    fun createMaterialBlack() = createMaterialInk(MaterialPboxInkBlack::class, DEFAULT_INK_BLACK_MASTERDATA_KEY)

    @DisableEntityCreationInModularizedEntity
    @EntityCreation(Entities.MATERIAL)
    fun createMaterialMagenta() = createMaterialInk(MaterialPboxInkMagenta::class, DEFAULT_INK_MAGENTA_MASTERDATA_KEY)

    @DisableEntityCreationInModularizedEntity
    @EntityCreation(Entities.MATERIAL)
    fun createMaterialYellow() = createMaterialInk(MaterialPboxInkYellow::class, DEFAULT_INK_YELLOW_MASTERDATA_KEY)

    @DisableEntityCreationInModularizedEntity
    @EntityCreation(Entities.MATERIAL)
    fun createMaterialVarnish() =
        createMaterialFromMasterdata(MaterialPboxVarnish::class, MasterDataType.RAW_MATERIAL_VARNISH, DEFAULT_VARNISH_MASTERDATA_KEY)
}
