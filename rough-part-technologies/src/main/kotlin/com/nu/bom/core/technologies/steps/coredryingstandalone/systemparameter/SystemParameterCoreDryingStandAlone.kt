package com.nu.bom.core.technologies.steps.coredryingstandalone.systemparameter

import com.nu.bom.core.manufacturing.annotations.*
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.SystemParameter
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Time

@EntityType(Entities.SYSTEM_PARAMETER)
class SystemParameterCoreDryingStandAlone(name: String) : ManufacturingEntity(name) {

    override val extends: ManufacturingEntity? = SystemParameter(name)

    @Input
    @ReadOnly
    @ObjectView(ObjectView.SYSTEM, 10)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun furnaceBeltWidth(): Length? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.SYSTEM, 20)
    fun furnaceBeltLength(): Length? = null

    @Input
    fun fixedCycleTime(): Time? = null
}
