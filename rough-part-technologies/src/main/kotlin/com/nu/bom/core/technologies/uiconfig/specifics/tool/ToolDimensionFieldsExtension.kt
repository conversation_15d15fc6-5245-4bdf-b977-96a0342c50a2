package com.nu.bom.core.technologies.uiconfig.specifics.tool

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Extends
import com.nu.bom.core.manufacturing.annotations.InterfaceField
import com.nu.bom.core.manufacturing.entities.ManufacturingEntityExtension
import com.nu.bom.core.manufacturing.entities.MissingInputError
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.extension.TOOL_CALCULATION_PACKAGE
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.technologies.steps.afor.tool.extension.DieForgingAluToolCO2Extension
import com.nu.bom.core.technologies.steps.dca.tool.extension.DieCastingToolChinaCO2Extension
import com.nu.bom.core.technologies.steps.dfor.tool.extension.DieForgingToolCO2Extension
import com.nu.bom.core.technologies.steps.inj.tool.extension.MoldInjectionToolCO2Extension
import com.nu.bom.core.technologies.steps.rrol.tool.extension.RingRollingToolCO2Extension
import com.nu.bom.core.technologies.steps.sandcasting.tool.extension.ResinModelSandCastingToolCO2Extension
import com.nu.bom.core.technologies.steps.sandcasting.tool.extension.SteelModelSandCastingToolCO2Extension

@Extends(
    [
        DieForgingAluToolCO2Extension::class,
        RingRollingToolCO2Extension::class,
        DieCastingToolChinaCO2Extension::class,
        DieForgingToolCO2Extension::class,
        ResinModelSandCastingToolCO2Extension::class,
        SteelModelSandCastingToolCO2Extension::class,
        MoldInjectionToolCO2Extension::class,
    ],
    TOOL_CALCULATION_PACKAGE,
)
@EntityType(Entities.NONE)
class ToolDimensionFieldsExtension(name: String) : ManufacturingEntityExtension(name) {
    @InterfaceField
    fun cO2ToolLength(): Length = throw MissingInputError()

    @InterfaceField
    fun cO2ToolWidth(): Length = throw MissingInputError()

    @InterfaceField
    fun cO2ToolHeight(): Length = throw MissingInputError()

    @InterfaceField
    fun cO2ToolWeight(): Weight = throw MissingInputError()
}
