package com.nu.bom.core.technologies.steps.coreassembly.systemparameter

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.SystemParameter
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time

@EntityType(Entities.SYSTEM_PARAMETER)
class SystemParameterCoreAssembly(name: String) : ManufacturingEntity(name) {

    override val extends = SystemParameter(name)

    @Input
    val coreSize: Text? = null

    @Input
    val fixedCycleTime: Time? = null
}
