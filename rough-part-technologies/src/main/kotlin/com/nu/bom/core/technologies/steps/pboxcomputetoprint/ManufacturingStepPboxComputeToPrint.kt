package com.nu.bom.core.technologies.steps.pboxcomputetoprint

import com.nu.bom.core.manufacturing.annotations.Children
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.MissingInputError
import com.nu.bom.core.manufacturing.entities.SingleGroupManufacturingStep
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.ManufacturingStepType
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.technologies.steps.pboxcomputetoprint.systemparameter.SystemParameterPboxComputeToPrint

@EntityType(Entities.MANUFACTURING_STEP)
@Suppress("unused")
class ManufacturingStepPboxComputeToPrint(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = SingleGroupManufacturingStep(name)

    companion object {
        val DEFAULT_TEMPLATE_NAME = Text("ComputeToPrint")
    }

    fun manufacturingStepType(): Text = Text(ManufacturingStepType.PRINTING.name)

    // region Fields from MANUFACTURING

    @Parent(Entities.MANUFACTURING)
    fun key(): Text? = null

    // endregion

    @Children(Entities.SYSTEM_PARAMETER)
    fun numberOfPlates(): QuantityUnit = throw MissingInputError()

    @Children(Entities.SYSTEM_PARAMETER)
    fun timePerPlate(): CycleTime = throw MissingInputError()

    fun partsPerCycle() = QuantityUnit(1.0)

    fun utilizationRate() = Rate(0.80)

    fun templateName() = DEFAULT_TEMPLATE_NAME

    @EntityCreation(Entities.MACHINE)
    fun createMachines(templateName: Text) = createMachinesFromTemplate(templateName)

    @EntityCreation(Entities.TOOL)
    fun createTools(templateName: Text) = createToolFromTemplate(templateName)

    @EntityCreation(Entities.SYSTEM_PARAMETER)
    fun createSystemParameter(templateName: Text) =
        createSystemParameter(
            masterData = MasterDataType.SYSTEM_PARAMETER_PBOX_COMPUTE_TO_PRINT,
            clazz = SystemParameterPboxComputeToPrint::class,
            system = templateName.res,
            year = calculationContext?.year!!,
            location = "Global",
        )
}
