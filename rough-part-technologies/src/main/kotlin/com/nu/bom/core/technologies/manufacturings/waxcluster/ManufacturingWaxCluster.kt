package com.nu.bom.core.technologies.manufacturings.waxcluster

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.CalculationPreview
import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.SummaryView
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.CustomProcurementTypeHelper
import com.nu.bom.core.manufacturing.defaults.NullProvider
import com.nu.bom.core.manufacturing.entities.BaseModelManufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.SubManufacturing
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CO2CalculationOperationsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.CostCalculationOperationsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.CustomProcurementType
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.technologies.manufacturings.waxcluster.material.WaxClusteredMaterial
import reactor.core.publisher.Mono
import kotlin.reflect.KClass

@EntityType(Entities.MANUFACTURING)
class ManufacturingWaxCluster(
    name: String,
) : BaseModelManufacturing(name) {
    override val model: Model
        get() = Model.WAXCLUSTER

    override val modelSteps: List<KClass<out ManufacturingEntity>>
        get() = emptyList()

    override val extends = SubManufacturing(name)

    fun shapeTechnologyGroup() = Text("")

    // Todo: Might need to be changed to average volume. See COST-50914
    fun internalCallsPerYear(peakUsableProductionVolumePerYear: QuantityUnit): Num =
        Num(min(peakUsableProductionVolumePerYear.res, 12.toBigDecimal()))

    fun parentMaterialName(
        @Parent(Entities.MANUFACTURING) parentMaterialName: Text,
    ) = parentMaterialName

    @SummaryView(SummaryView.PART, 100, "materialName")
    @CalculationPreview(1, "materialName")
    fun materialNameForOverview(): Text = Text("No automatically created material")

    @Input
    fun customProcurementType(
        costCalculationOperationKey: CostCalculationOperationsConfigurationKey,
        @Default(NullProvider::class)
        cO2CalculationOperationKey: CO2CalculationOperationsConfigurationKey?,
    ): Mono<CustomProcurementType> =
        CustomProcurementTypeHelper
            .getDefaultInhouseProcurementType(costCalculationOperationKey, cO2CalculationOperationKey, services)

    @EntityCreation(Entities.PROCESSED_MATERIAL, childCreations = [Entities.MANUFACTURING_STEP])
    fun createMaterial(): ManufacturingEntity =
        createEntity(
            name = "WaxClusteredMaterial",
            clazz = WaxClusteredMaterial::class,
            entityType = Entities.MATERIAL,
        )
}
