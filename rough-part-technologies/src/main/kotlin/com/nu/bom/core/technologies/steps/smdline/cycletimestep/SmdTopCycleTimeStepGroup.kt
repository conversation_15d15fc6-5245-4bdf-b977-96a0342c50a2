package com.nu.bom.core.technologies.steps.smdline.cycletimestep

import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.entities.CycleTimeStepGroup
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeGrouping

@EntityType(Entities.CYCLETIME_STEP_GROUP)
class SmdTopCycleTimeStepGroup(name: String) : ManufacturingEntity(name) {
    override val extends = CycleTimeStepGroup(name)

    val grouping = CycleTimeGrouping.Sequential

    @EntityCreation(Entities.CYCLETIME_STEP)
    fun createCycleTimeSteps(): List<ManufacturingEntity> {
        return listOfNotNull(
            createEntity(
                name = "Pick and place time",
                clazz = SmdTopPickAndPlaceCycleTimeStep::class,
                entityType = Entities.CYCLETIME_STEP,
            ),
            createEntity(
                name = "Secondary time",
                clazz = SmdSecondaryTimeCycleTimeStep::class,
                entityType = Entities.CYCLETIME_STEP,
            ),
        )
    }
}
