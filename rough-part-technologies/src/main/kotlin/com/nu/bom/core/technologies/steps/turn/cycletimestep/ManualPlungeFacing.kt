package com.nu.bom.core.technologies.steps.turn.cycletimestep

import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntitySubtypes
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Speed
import com.nu.bom.core.manufacturing.fieldTypes.SpeedUnits
import com.nu.bom.core.manufacturing.fieldTypes.Text

@EntityType(Entities.CYCLETIME_STEP, userCreatable = true)
@EntitySubtypes(["TURNING"])
class ManualPlungeFacing(name: String) : ManufacturingEntity(name) {
    override val extends = ManualVerticalTurningLayer(name)

    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @MandatoryForEntity(section = "right", refresh = true)
    val toolWidth = Length(3.0, LengthUnits.MILLIMETER)

    @Input
    @MandatoryForEntity(section = "right", refresh = true)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    val feedRatePerRevolution = Length(0.2, LengthUnits.MILLIMETER)

    @Input
    @MandatoryForEntity(section = "right", refresh = true)
    val cuttingSpeed = Speed(180.0, SpeedUnits.M_PER_MIN)

    @ReadOnly
    @Input(active = false)
    fun layerLength(
        verticalLength: Length,
        toolWidth: Length
    ) = verticalLength - toolWidth // TODO: comparison for units to check positive here. --jgr/21/02

    @Input
    val designation = Text("Plunge Facing")
}
