package com.nu.bom.core.technologies.manufacturings.minj.material

import com.nu.bom.core.exception.userException.TemplateNotFoundCauseType
import com.nu.bom.core.exception.userException.TemplateNotFoundException
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityLinkField
import com.nu.bom.core.manufacturing.annotations.EntityLinkProvider
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Hidden
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Deg
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.DynamicQuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.EntityRef
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.MaterialViewConfig
import com.nu.bom.core.manufacturing.fieldTypes.PartQuality
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.RunnerSystems
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Volume
import com.nu.bom.core.manufacturing.fieldTypes.VolumeUnits
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import com.nu.bom.core.technologies.lookups.microInjectionScrewDiameterReader
import com.nu.bom.core.technologies.manufacturings.inj.material.MaterialPlastic2
import com.nu.bom.core.technologies.service.ShapeLookupReaderService
import com.nu.bom.core.technologies.steps.microinj.ManufacturingStepMicroInjection
import reactor.core.publisher.Mono
import java.math.BigDecimal
import kotlin.math.tan

@EntityType(Entities.MATERIAL)
class MaterialMicroPlastic(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = MaterialPlastic2(name)

    @Hidden
    @ObjectView(ObjectView.MATERIAL, 165)
    fun runnerSystems() = RunnerSystems.COLD_RUNNER

    @Input
    @EntityLinkProvider(["ManufacturingStepMicroInjection"], entityClasses = [ManufacturingStepMicroInjection::class])
    fun linkedStep(): EntityRef? = getLinkedStep(ManufacturingStepMicroInjection::class)

    @Input
    @EntityLinkField(providerField = "linkedStep", "gapBetweenParts")
    fun gapBetweenParts(): Length? = null

    @Input
    @EntityLinkField(providerField = "linkedStep", "templateName")
    fun templateNameMicro(): Text? = null

    fun sprueRateForInjectionTime(sprueRate: Rate) = Rate(BigDecimal.ONE + sprueRate.res)

    fun inputGroup(
        lookupReaderService: ShapeLookupReaderService,
        @Parent(Entities.PROCESSED_MATERIAL) shapeId: Text,
    ) = lookupReaderService.getInputGroupFromShapeLookup(calculationContext!!.accessCheck, shapeId, Model.MINJ)

    fun newLength(
        inputGroup: Text,
        @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
        partOuterDiameter: Length,
        @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
        partLength: Length,
    ): Length = if (inputGroup.res == "cylinder") partOuterDiameter else partLength

    fun newWidth(
        inputGroup: Text,
        @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
        partOuterDiameter: Length,
        @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
        partWidth: Length,
    ): Length = if (inputGroup.res == "cuboid") partWidth else partOuterDiameter

    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun runnerSystemLengthCommon(
        partPerCycleFamilyToolingForMaterial: DynamicQuantityUnit,
        gapBetweenParts: Length,
        newLength: Length,
        newWidth: Length,
    ): Length =
        services.getInjectionUtilsService().runnerSystemLengthCommonImpl(
            partPerCycleFamilyToolingForMaterial,
            gapBetweenParts,
            newLength,
            newWidth,
        )

    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun runnerSystemLengthAdditional(
        @Parent(Entities.MANUFACTURING)
        partQuality: PartQuality,
    ): Length =
        when (partQuality.res) {
            PartQuality.Selection.PRECISE -> Length(BigDecimal.ZERO, LengthUnits.METER)
            PartQuality.Selection.STANDARD,
            PartQuality.Selection.TECHNICAL,
            PartQuality.Selection.PRECISE_VERY_TIGHT_TOLERANCES,
            -> Length(0.04, LengthUnits.METER)
        }

    fun runnerSystemLength(
        runnerSystemLengthCommon: Length,
        runnerSystemLengthAdditional: Length,
    ) = runnerSystemLengthCommon + runnerSystemLengthAdditional

    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun runnerSystemTopDiameter() = Length(0.002, LengthUnits.METER)

    fun draftAngle(): Deg = Deg(1.0)

    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun runnerSystemBaseDiameter(
        runnerSystemLengthAdditional: Length,
        draftAngle: Deg,
        runnerSystemTopDiameter: Length,
    ): Length {
        val oneSideSpillingLength = runnerSystemLengthAdditional * tan(draftAngle.rad.res.toDouble())
        return runnerSystemTopDiameter + oneSideSpillingLength.times(2.0)
    }

    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun spreaderDiameter(
        @Parent(Entities.MANUFACTURING)
        maxWallThickness: Length,
    ): Length = Length(min(maxWallThickness.inMillimeter.times(BigDecimal(2.0)), BigDecimal(2.0)), LengthUnits.MILLIMETER)

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun runnerSystemWeightPerCycle(
        runnerVolume: Volume,
        density: Density,
    ): DynamicQuantityUnit = services.getInjectionUtilsService().runnerSystemWeightPerCycleImpl(runnerVolume, density)

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun excessSprue(
        @Parent(Entities.MANUFACTURING_STEP) wastedShotVolume: Volume,
        density: Density,
    ): DynamicQuantityUnit =
        DynamicQuantityUnit(wastedShotVolume) * DynamicQuantityUnit(density.inKgPerCm, WeightUnits.KILOGRAM, VolumeUnits.CM)

    fun runnerVolume(
        runnerSystemLengthCommon: Length,
        runnerSystemLengthAdditional: Length,
        runnerSystemTopDiameter: Length,
        runnerSystemBaseDiameter: Length,
        spreaderDiameter: Length,
    ): Volume =
        services.getInjectionUtilsService().runnerVolumeImpl(
            runnerSystemLengthCommon,
            runnerSystemLengthAdditional,
            runnerSystemTopDiameter,
            runnerSystemBaseDiameter,
            spreaderDiameter,
        )

    fun neededShotWeightPerCycle(
        netWeightPerCycle: QuantityUnit,
        runnerSystemWeightPerCycle: DynamicQuantityUnit,
    ): QuantityUnit = services.getInjectionUtilsService().neededShotWeightPerCycleImpl(netWeightPerCycle, runnerSystemWeightPerCycle)

    fun minShotVolume(templateNameMicro: Text): Mono<Volume> =
        services
            .getLookupTable(
                "ManufacturingStepMicroInjection_ScrewDiameter",
                microInjectionScrewDiameterReader,
            ).filter { templateNameMicro == it.templateName }
            .elementAt(0)
            .mapNotNull { it.minShotVolume }

    fun screwDiameter(
        templateNameMicro: Text,
        minShotVolume: Volume,
    ): Mono<Length> {
        val lookupName = "ManufacturingStepMicroInjection_ScrewDiameter"
        return services
            .getLookupTable(lookupName, microInjectionScrewDiameterReader)
            .filter { templateNameMicro == it.templateName && minShotVolume == it.minShotVolume }
            .singleOrEmpty()
            .map { it.screwDiameter }
            .switchIfEmpty(
                Mono.error(
                    TemplateNotFoundException(
                        causeType = TemplateNotFoundCauseType.SHOT_WEIGHT_UNDER_MIN,
                        lookupName = lookupName,
                        lookupInputs =
                            mapOf(
                                "templateNameMicro" to templateNameMicro.toString(),
                                "minShotVolume" to minShotVolume.toString(),
                            ),
                    ),
                ),
            )
    }

    fun minShotWeightPerCycle(
        minShotVolume: Volume,
        density: Density,
    ): QuantityUnit = QuantityUnit(minShotVolume * density)

    fun shotVolume(
        neededShotWeightPerCycle: QuantityUnit,
        density: Density,
    ) = Volume.create(neededShotWeightPerCycle, density)

    @ReadOnly
    fun sprueRate(
        sprueWeightPerCycle: QuantityUnit,
        deployedWeightPerCycle: QuantityUnit,
    ): Rate = Rate((sprueWeightPerCycle / deployedWeightPerCycle).res)

    fun sprueWeightPerCycle(
        runnerSystemWeightPerCycle: DynamicQuantityUnit,
        excessSprue: DynamicQuantityUnit,
    ): DynamicQuantityUnit = runnerSystemWeightPerCycle + excessSprue

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun sprueWeightPerPart(
        sprueWeightPerCycle: DynamicQuantityUnit,
        partPerCycleFamilyToolingForMaterial: DynamicQuantityUnit,
    ): DynamicQuantityUnit = sprueWeightPerCycle / partPerCycleFamilyToolingForMaterial

    fun materialView(): MaterialViewConfig = MaterialViewConfig.MATERIAL_MICRO_PLASTIC
}
