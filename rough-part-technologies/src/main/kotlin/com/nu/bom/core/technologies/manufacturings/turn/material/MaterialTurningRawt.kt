package com.nu.bom.core.technologies.manufacturings.turn.material

import com.nu.bom.core.manufacturing.annotations.CalculationPreview
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.SummaryView
import com.nu.bom.core.manufacturing.annotations.TranslationSection
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.RawMaterial
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.MaterialCostMode
import com.nu.bom.core.manufacturing.fieldTypes.MaterialViewConfig
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import java.math.BigDecimal

@EntityType(Entities.MATERIAL)
class MaterialTurningRawt(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = RawMaterial(name)

    @Input
    fun reuseOfScrap() = Bool(false)

    @TranslationSection(TranslationSection.DEEP)
    fun displayDesignation() = Text("chips")

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun pricePerUnit() = Money(BigDecimal.ZERO)

    // override only to disable the annotation (SummaryView name for Rawt material is handled in the Step)
    @SummaryView(SummaryView.NONE, 100, "materialName")
    @CalculationPreview(1, "materialName", disabled = true)
    fun materialNameForSummary(displayDesignation: Text): Text = displayDesignation

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 1)
    fun materialName(): Text? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @TranslationSection(TranslationSection.DEEP)
    @ReadOnly
    fun rawPartTechnology(): Text? = null

    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ReadOnly
    @Parent(Entities.PROCESSED_MATERIAL)
    fun rawPartLength(): Length? = null

    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ReadOnly
    @Parent(Entities.PROCESSED_MATERIAL)
    fun rawPartInnerDiameter(): Length? = null

    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ReadOnly
    @Parent(Entities.PROCESSED_MATERIAL)
    fun rawPartOuterDiameter(): Length? = null

    fun materialWastePrice(): Money = Money(0.15.toBigDecimal())

    fun materialRecyclingPrice(pricePerUnit: Money): Money = Money(0.4.toBigDecimal() * pricePerUnit.res)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 3)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun netWeightPerPart(): QuantityUnit? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun scrapWeightPerPart(): QuantityUnit? = null

    @Input
    fun recyclingRate() = Rate(0.0)

    @Input
    @ReadOnly
    @Parent(Entities.PROCESSED_MATERIAL)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun rawWeightPerPart(): QuantityUnit? = null

    @ReadOnly
    fun surchargeWeight(
        netWeightPerPart: QuantityUnit,
        rawWeightPerPart: QuantityUnit,
    ): QuantityUnit = rawWeightPerPart - netWeightPerPart

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun surchargeLossWeight(
        surchargeWeight: QuantityUnit,
        recyclingRate: Rate,
    ): QuantityUnit = surchargeWeight * (1.toBigDecimal() - recyclingRate.res)

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun deployedWeightPerPart(
        netWeightPerPart: QuantityUnit,
        surchargeLossWeight: QuantityUnit,
    ): QuantityUnit = netWeightPerPart + surchargeLossWeight

    @ReadOnly
    fun irretrievableScrapPerPart(surchargeLossWeight: QuantityUnit): QuantityUnit = surchargeLossWeight

    @ReadOnly
    fun retrievableScrapPerPart(
        surchargeWeight: QuantityUnit,
        recyclingRate: Rate,
    ): QuantityUnit = surchargeWeight * recyclingRate

    /****** RawMaterials override **********/

    fun materialCostMode(): MaterialCostMode = MaterialCostMode.SELL_IRRETRIEVABLE

    fun quantityForExport(deployedWeightPerPart: QuantityUnit): QuantityUnit = deployedWeightPerPart

    fun materialView(): MaterialViewConfig = MaterialViewConfig.MATERIAL_TURNING_RAWT
}
