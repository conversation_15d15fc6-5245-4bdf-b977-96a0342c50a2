package com.nu.bom.core.technologies.manufacturings.pbox.material

import com.nu.bom.core.manufacturing.fieldTypes.MaterialViewConfig
import org.springframework.stereotype.Service

@Service
object MaterialPboxInkBlackViewProvider : MaterialPboxInkViewProvider() {
    override val materialView = MaterialViewConfig.MaterialView.MATERIAL_PBOX_INK_BLACK

    override val shareAndWeightFields: List<String> =
        listOf(
            MaterialPboxInkBlack::blackShare.name,
            MaterialPboxInkBlack::blackPrintingWeight.name,
        )
}
