package com.nu.bom.core.technologies.steps.waxInjectionMolding.tool

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.Tool
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.ToolComplexityWaxMoldInjection
import com.nu.bom.core.technologies.lookups.waxInjectionMoldToolReader
import reactor.core.publisher.Mono

@EntityType(Entities.TOOL)
class WaxInjectionMoldSteelTool(name: String) : ManufacturingEntity(name) {
    override val extends = Tool(name)

    fun investPerTool(
        @Parent(Entities.MANUFACTURING_STEP)
        toolComplexityWaxMold: ToolComplexityWaxMoldInjection,
        @Parent(Entities.MANUFACTURING_STEP)
        templateName: Text,
    ): Mono<Money>? {
        return services.getLookupTable(
            "ManufacturingStepWaxInjectionMolding_Tool",
            waxInjectionMoldToolReader,
        ).filter {
            toolComplexityWaxMold.res.compareTo(it.toolComplexityWaxMold.res) == 0
        }.single().map {
            when {
                templateName.res.contains("WaxInjectionMolding_SteelModel") -> it.steelHighCostCountry
                templateName.res.contains("WaxInjectionMolding_LowCostCountry_SteelModel") -> it.steelLowCostCountry
                else -> throw IllegalArgumentException("not supported tool")
            }
        }
    }

    fun serviceLifeInCycles(
        @Parent(Entities.MANUFACTURING_STEP)
        cyclesOverLifeTime: Num,
    ) = cyclesOverLifeTime
}
