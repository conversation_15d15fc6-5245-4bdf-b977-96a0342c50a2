package com.nu.bom.core.technologies.steps.heattreatment.cycletimestep

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.CycleTimeStepInMinutes
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeUnit
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Time
import java.math.BigDecimal

@EntityType(Entities.CYCLETIME_STEP)
class ControlledCoolingRoomCycleTimeStep(name: String) : ManufacturingEntity(name) {

    override val extends = CycleTimeStepInMinutes(name)

    @Input
    val adjustmentRate = Rate(BigDecimal.ONE)

    fun time(
        @Parent(Entities.MANUFACTURING_STEP)
        coolingTimePerArea: Time,
        @Parent(Entities.MANUFACTURING_STEP)
        lotCooling: Num
    ): CycleTime {
        return CycleTime((coolingTimePerArea / lotCooling).inSeconds, CycleTimeUnit.SECOND)
    }
}
