package com.nu.bom.core.technologies.lookups

import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.SliderConceptIdInjection
import com.nu.bom.core.manufacturing.fieldTypes.ToolComplexityWaxMoldInjection
import java.math.BigDecimal

data class PRECShape(
    override val shapeId: String,
    override val shapeTechnologyGroup: String,
    override val tech: String,
    override val inputGroup: String,
    val displayShape: String,
    val yieldRate: Rate,
    val minPartsPerCycleWaxMold: QuantityUnit,
    val maxPartsPerCycleWaxMold: QuantityUnit,
    val cycleTimeWaxMold: BigDecimal,
    val toolComplexityWaxMold: ToolComplexityWaxMoldInjection,
    val sliderConceptId: SliderConceptIdInjection,
    val minWeight: QuantityUnit,
    val maxWeight: QuantityUnit,
    val partLength: BigDecimal?,
    val partWidth: BigDecimal?,
    val partHeight: BigDecimal?,
    val partOuterDiameter: BigDecimal?,
    val partInnerDiameter: BigDecimal?,
    val shapeActive: Boolean,
) : LookupShape

val PREC_ShapeReader: (row: List<String>) -> PRECShape = { row ->
    PRECShape(
        shapeId = row[0],
        shapeTechnologyGroup = row[1],
        tech = row[2],
        inputGroup = row[3],
        displayShape = row[4],
        yieldRate = Rate(row[5]),
        minPartsPerCycleWaxMold = QuantityUnit(row[6]),
        maxPartsPerCycleWaxMold = QuantityUnit(row[7]),
        cycleTimeWaxMold = row[8].toBigDecimal(),
        toolComplexityWaxMold = ToolComplexityWaxMoldInjection(ToolComplexityWaxMoldInjection.Selection.valueOf(row[9])),
        sliderConceptId = SliderConceptIdInjection.valueOf(row[10]),
        minWeight = QuantityUnit(row[11].toBigDecimal()),
        maxWeight = QuantityUnit(row[12].toBigDecimal()),
        partLength = row[13].takeIf { it.isNotEmpty() }?.toBigDecimal(),
        partWidth = row[14].takeIf { it.isNotEmpty() }?.toBigDecimal(),
        partHeight = row[15].takeIf { it.isNotEmpty() }?.toBigDecimal(),
        partOuterDiameter = row[16].takeIf { it.isNotEmpty() }?.toBigDecimal(),
        partInnerDiameter = row[17].takeIf { it.isNotEmpty() }?.toBigDecimal(),
        shapeActive = row[18].toBoolean(),
    )
}
