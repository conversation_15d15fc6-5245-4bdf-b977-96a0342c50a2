package com.nu.bom.core.technologies.steps.turn.cycletimestep

import com.nu.bom.core.manufacturing.annotations.EntitySubtypes
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Text

@EntityType(Entities.CYCLETIME_STEP, userCreatable = true)
@EntitySubtypes(["TURNING"])
class ManualExternalFacing(name: String) : ManufacturingEntity(name) {
    override val extends = ManualVerticalTurningLayer(name)

    @Input
    val designation = Text("External Facing")
}
