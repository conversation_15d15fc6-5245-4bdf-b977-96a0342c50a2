package com.nu.bom.core.technologies.lookups

import java.math.BigDecimal

data class InjectionToolMaitenance(
    val shapeId: String,
    val toolComplexity: Double,
    val maintenanceHours: BigDecimal,
)

val injectionToolMaitenanceReader: (row: List<String>) -> InjectionToolMaitenance = { row ->
    InjectionToolMaitenance(
        shapeId = row[0],
        toolComplexity = row[1].toDouble(),
        maintenanceHours = row[2].toBigDecimal(),
    )
}
