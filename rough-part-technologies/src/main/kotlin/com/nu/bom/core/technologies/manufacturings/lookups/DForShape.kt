package com.nu.bom.core.technologies.manufacturings.lookups

import com.nu.bom.core.manufacturing.fieldTypes.BlankAlignmentForging
import com.nu.bom.core.manufacturing.fieldTypes.BlankDiameterAdjustmentType
import com.nu.bom.core.manufacturing.fieldTypes.BurrGeometryGroupType
import com.nu.bom.core.manufacturing.fieldTypes.ToolComplexityDieForging
import com.nu.bom.core.technologies.lookups.LookupShape
import java.math.BigDecimal

data class DForShape(
    override val shapeId: String,
    override val shapeTechnologyGroup: String,
    override val tech: String,
    override val inputGroup: String,
    val displayShape: String,
    val burrGeometry: BurrGeometryGroupType,
    val burrFactor: BigDecimal,
    val burrLength: String,
    val burrWidth: String,
    val burrDiameter: String,
    val blankAlignment: BlankAlignmentForging,
    val blankDiameterBase: String,
    // depending on the actual forging operation, the diameter of the bar is quite different from the final part
    val blankDiameterAdjustmentType: BlankDiameterAdjustmentType.Selection,
    val blankDiameterAdjustment: BigDecimal,
    val complexity: ToolComplexityDieForging,
    val cycleTimeAddition: BigDecimal,
    val coldCalibrationUsed: Boolean,
) : LookupShape

val shapeLookupMapper = fun(row: List<String>): DForShape =
    DForShape(
        shapeId = row[0],
        shapeTechnologyGroup = row[1],
        tech = row[2],
        inputGroup = row[3],
        displayShape = row[4],
        burrGeometry = BurrGeometryGroupType.valueOf(row[5]),
        burrFactor = row[6].toBigDecimal(),
        burrLength = row[7],
        burrWidth = row[8],
        burrDiameter = row[9],
        blankAlignment = BlankAlignmentForging.valueOf(row[10]),
        blankDiameterBase = row[11],
        blankDiameterAdjustmentType = BlankDiameterAdjustmentType.Selection.valueOf(row[12]),
        blankDiameterAdjustment = row[13].toBigDecimal(),
        complexity = ToolComplexityDieForging.valueOf(row[22]),
        cycleTimeAddition = row[23].toBigDecimal(),
        coldCalibrationUsed = row[28].toBoolean(),
    )
