package com.nu.bom.core.technologies.steps.rinj.cycletimestep

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.entities.CycleTimeStep
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.RunnerSystemsRubber
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.technologies.service.ShapeLookupReaderService
import reactor.core.publisher.Mono
import java.math.BigDecimal

@EntityType(Entities.CYCLETIME_STEP)
class RubberLoadingUnloadingCycleTimeStep(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = CycleTimeStep(name)

    companion object {
        const val BRUSHING_TIME = 4.0
        const val FAST_YANK_TIME = 4.0
        const val SLOW_YANK_TIME = 10.0
    }

    @SpecialLink("MaterialRubber", "runnerSystems")
    fun runnerSystems(): RunnerSystemsRubber? = null

    fun hasComplexFeatures(
        lookupReaderService: ShapeLookupReaderService,
        @Parent(Entities.PROCESSED_MATERIAL) shapeId: Text,
    ): Mono<Bool> =
        lookupReaderService.getRinjShapeData(calculationContext!!.accessCheck, shapeId).map {
            it.complexFeatures
        }

    fun hasFlatParameters(
        lookupReaderService: ShapeLookupReaderService,
        @Parent(Entities.PROCESSED_MATERIAL) shapeId: Text,
    ): Mono<Bool> =
        lookupReaderService.getRinjShapeData(calculationContext!!.accessCheck, shapeId).map {
            it.flatParameters
        }

    fun timeBasedOnWeightAndComplexity(
        @Parent(Entities.MANUFACTURING)
        netWeightPerPart: QuantityUnit,
        hasComplexFeatures: Bool,
    ): Time {
        val timeRes =
            when {
                netWeightPerPart.res <= 5.0.toBigDecimal() -> if (hasComplexFeatures.isTrue()) 1.0 else 0.5
                netWeightPerPart.res <= 50.0.toBigDecimal() -> if (hasComplexFeatures.isTrue()) 1.5 else 0.5
                netWeightPerPart.res <= 100.0.toBigDecimal() -> if (hasComplexFeatures.isTrue()) 2.0 else 1.0
                netWeightPerPart.res <= 250.0.toBigDecimal() -> if (hasComplexFeatures.isTrue()) 2.5 else 1.0
                else -> if (hasComplexFeatures.isTrue()) 3.0 else 1.5
            }
        return Time(timeRes, TimeUnits.SECOND)
    }

    fun time(
        hasComplexFeatures: Bool,
        hasFlatParameters: Bool,
        runnerSystems: RunnerSystemsRubber,
        @Parent(Entities.MANUFACTURING_STEP)
        partsPerCycleFamilyTooling: QuantityUnit,
        timeBasedOnWeightAndComplexity: Time,
    ): Time {
        require(!(hasComplexFeatures.isTrue() && hasFlatParameters.isTrue())) {
            "No use case for complex features and flat parameters. Check shape attributes in Lookup_RINJ_Shape"
        }

        val timeRes =
            when (runnerSystems.res) {
                RunnerSystemsRubber.Selection.COLD_RUNNER ->
                    if (hasFlatParameters.isTrue()) {
                        BRUSHING_TIME
                    } else {
                        timeBasedOnWeightAndComplexity.times(partsPerCycleFamilyTooling).res.toDouble()
                    }

                RunnerSystemsRubber.Selection.LOST_RUNNER ->
                    when {
                        !hasComplexFeatures.isTrue() && hasFlatParameters.isTrue() -> FAST_YANK_TIME
                        !hasComplexFeatures.isTrue() && !hasFlatParameters.isTrue() -> FAST_YANK_TIME * 1.5
                        else -> timeBasedOnWeightAndComplexity.times(partsPerCycleFamilyTooling).res.toDouble() / 2.0
                    }
            }
        return Time(timeRes, TimeUnits.SECOND)
    }

    fun adjustmentRate(): Rate = Rate(BigDecimal.ONE)
}
