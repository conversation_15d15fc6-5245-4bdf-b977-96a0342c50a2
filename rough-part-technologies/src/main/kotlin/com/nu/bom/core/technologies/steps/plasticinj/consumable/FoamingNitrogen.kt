package com.nu.bom.core.technologies.steps.plasticinj.consumable

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.Consumable
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.DensityUnits
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate

@EntityType(Entities.CONSUMABLE)
class FoamingNitrogen(name: String) : ManufacturingEntity(name) {

    override val extends = Consumable(name)

    @Input
    fun reuseOfScrap(): Bool = Bool(false)

    fun quantity(
        @Parent(Entities.MANUFACTURING)
        netWeightPerPart: QuantityUnit,
        @Parent(Entities.MANUFACTURING)
        foamingDensity: Density,
        @Parent(Entities.MANUFACTURING)
        emptySpace: Rate
    ): QuantityUnit {
        val expandedVolume = netWeightPerPart / foamingDensity
        val nitrogenVolume = expandedVolume * emptySpace
        val nitrogenDensity = Density(1.1606, DensityUnits.KILOGRAM_PER_CM)
        return QuantityUnit((nitrogenDensity * nitrogenVolume).res)
    }
}
