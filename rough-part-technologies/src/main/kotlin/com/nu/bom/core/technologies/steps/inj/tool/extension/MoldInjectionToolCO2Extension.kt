package com.nu.bom.core.technologies.steps.inj.tool.extension
import com.nu.bom.core.manufacturing.annotations.Extends
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.ManufacturingEntityExtension
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.extension.CO2_EXTENSION_PACKAGE
import com.nu.bom.core.manufacturing.extension.ToolCO2Extension
import com.nu.bom.core.manufacturing.fieldTypes.*
import com.nu.bom.core.technologies.lookups.injectionToolMaitenanceReader
import com.nu.bom.core.technologies.steps.inj.tool.MoldInjectionTool
import com.nu.bom.core.technologies.steps.inj.tool.MoldInjectionTool2
import reactor.core.publisher.Mono
import java.math.BigDecimal

@Extends([MoldInjectionTool::class, MoldInjectionTool2::class], CO2_EXTENSION_PACKAGE)
class MoldInjectionToolCO2Extension(
    name: String,
) : ManufacturingEntityExtension(name) {
    override val extends = ToolCO2Extension(name)

    fun cO2ToolDetailView(): ToolDetailViewConfig = ToolDetailViewConfig.CO2_DIMENSION_TOOL

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    val shapeId: Text? = null

    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    val partOuterDiameter: Length? = null

    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    val partLength: Length? = null

    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    val partWidth: Length? = null

    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    val partHeight: Length? = null

    fun toolPartLength(
        @Parent(Entities.MANUFACTURING_STEP)
        inputGroup: Text,
        partOuterDiameter: Length,
        partLength: Length,
    ): Length =
        when (
            inputGroup.res
        ) {
            "cylinder" -> Length(partOuterDiameter.res, LengthUnits.METER)
            else -> Length(partLength.res, LengthUnits.METER)
        }

    fun toolPartWidth(
        @Parent(Entities.MANUFACTURING_STEP)
        inputGroup: Text,
        partOuterDiameter: Length,
        partWidth: Length,
    ): Length =
        when (inputGroup.res) {
            "cuboid" -> Length(partWidth.res, LengthUnits.METER)
            else -> Length(partOuterDiameter.res, LengthUnits.METER)
        }

    fun toolPartHeight(
        @Parent(Entities.MANUFACTURING_STEP)
        inputGroup: Text,
        partOuterDiameter: Length,
        partHeight: Length,
    ): Length =
        when (inputGroup.res) {
            "pipe" -> Length(partOuterDiameter.res, LengthUnits.METER)
            else -> Length(partHeight.res, LengthUnits.METER)
        }

    fun toolGap(
        toolPartWidth: Length,
        toolPartLength: Length,
    ): Length =
        when {
            toolPartWidth.res < 0.02.toBigDecimal() && toolPartLength.res < 0.02.toBigDecimal() ->
                Length(
                    0.02.toBigDecimal(),
                    LengthUnits.METER,
                )
            toolPartWidth.res < 0.3.toBigDecimal() && toolPartLength.res < 0.3.toBigDecimal() ->
                Length(
                    0.05.toBigDecimal(),
                    LengthUnits.METER,
                )
            toolPartWidth.res < 0.8.toBigDecimal() && toolPartLength.res < 0.8.toBigDecimal() ->
                Length(
                    0.1.toBigDecimal(),
                    LengthUnits.METER,
                )
            else -> Length(0.15.toBigDecimal(), LengthUnits.METER)
        }

    fun cO2ToolLength(
        toolGap: Length,
        toolPartLength: Length,
        @Parent(Entities.MANUFACTURING_STEP)
        cavityLength: Num,
    ): Length = Length(0.3.toBigDecimal() + toolGap.res + (toolPartLength.res + toolGap.res) * cavityLength.res, LengthUnits.METER)

    fun cO2ToolWidth(
        toolGap: Length,
        toolPartWidth: Length,
        @Parent(Entities.MANUFACTURING_STEP)
        cavityWidth: Num,
    ): Length = Length(0.3.toBigDecimal() + toolGap.res + (toolPartWidth.res + toolGap.res) * cavityWidth.res, LengthUnits.METER)

    fun cO2ToolHeight(toolPartHeight: Length): Length = toolPartHeight + 0.15.toBigDecimal()

    fun cO2ToolWeight(
        cO2ToolLength: Length,
        cO2ToolWidth: Length,
        cO2ToolHeight: Length,
        toolMaterialDensity: Density,
    ): Weight =
        Weight(
            2.toBigDecimal() * cO2ToolHeight.res * cO2ToolWidth.res * cO2ToolLength.res * toolMaterialDensity.inKgPerCm,
            WeightUnits.KILOGRAM,
        )

    fun cO2MachineFactor(shapeId: Text): Mono<Rate>? =
        services
            .getLookupTable(
                "ManufacturingStepInjection_ToolMaitenance",
                injectionToolMaitenanceReader,
            ).filter {
                shapeId.res == it.shapeId
            }.single()
            .map {
                when {
                    Rate(it.toolComplexity).res.compareTo((0.03.toBigDecimal())) <= 0 -> Rate(0.5.toBigDecimal())
                    Rate(it.toolComplexity).res.compareTo((0.04.toBigDecimal())) == 0 -> Rate(0.7.toBigDecimal())
                    Rate(it.toolComplexity).res.compareTo((0.05.toBigDecimal())) == 0 -> Rate(BigDecimal.ONE)
                    else -> throw IllegalArgumentException("not supported toolComplexity ${it.toolComplexity}")
                }
            }
}
