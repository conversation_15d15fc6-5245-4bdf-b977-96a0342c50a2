package com.nu.bom.core.technologies.manufacturings.cube.material

import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityProvider
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.OrderedEntityCreation
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.SummaryView
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.MissingInputError
import com.nu.bom.core.manufacturing.entities.ShapedMaterial
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.FettlingCubeType
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.PositiveQuantity
import com.nu.bom.core.manufacturing.fieldTypes.Quantity
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.SheetSize
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeCleanness
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeStraightening
import com.nu.bom.core.manufacturing.fieldTypes.SystemType
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.technologies.behaviours.CleaningBehaviour
import com.nu.bom.core.technologies.steps.beltgrinding.ManufacturingStepBeltGrinding
import com.nu.bom.core.technologies.steps.bendingcube.ManufacturingStepBendingCube
import com.nu.bom.core.technologies.steps.cuttingcube.ManufacturingStepCuttingCube
import com.nu.bom.core.technologies.steps.fettlingcube.ManufacturingStepFettlingCube
import com.nu.bom.core.technologies.steps.shotblastingdeburring.ManufacturingStepShotBlastingDeburring
import com.nu.bom.core.technologies.steps.straightening.ManufacturingStepStraightening
import java.math.BigInteger

@EntityType(Entities.PROCESSED_MATERIAL)
class BentMaterial(name: String) : ManufacturingEntity(name) {

    override val extends = ShapedMaterial(name)

    @ObjectView(ObjectView.NONE, 0)
    fun displayDesignation(designation: Text?, entityDesignation: Text): Text {
        return designation ?: entityDesignation
    }

    override val behaviours: List<ManufacturingEntity> = listOf(
        CleaningBehaviour("CleaningBehaviour"),
    )

    @Input
    fun materialClass() = Text(MaterialBending::class.qualifiedName!!)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun netWeightPerPart(): QuantityUnit = throw MissingInputError()

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun sheetThickness(): Length = throw MissingInputError()

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun sheetSize(): SheetSize = throw MissingInputError()

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun sheetLength(): Length = throw MissingInputError()

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun sheetWidth(): Length = throw MissingInputError()

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun numberOfPartsPerSheet(): PositiveQuantity = throw MissingInputError()

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun nestingDistance(): Length = throw MissingInputError()

    @Input
    fun materialType() = Text("MATERIAL_SHEET")

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun cleanness(): StepSubTypeCleanness? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @SummaryView(SummaryView.PROCESS, 300, "cleaning")
    fun cleaningNeeded(): SelectableBoolean = SelectableBoolean.FALSE

    @OrderedEntityCreation
    fun manufacturingSteps() =
        arrayOf("cutting", "fettlingOptional", "beltGrindingOptional", "shotBlastingOptional", "straighteningOptional", "bendingOptional", "cleaning")

    @EntityProvider
    fun cutting() = createEntity(
        name = "ManufacturingStepCuttingCube",
        clazz = ManufacturingStepCuttingCube::class,
        entityType = Entities.MANUFACTURING_STEP,
    )

    @EntityProvider
    fun fettlingOptional(
        @Parent(Entities.PROCESSED_MATERIAL)
        fettlingCubeType: FettlingCubeType
    ) =
        if (fettlingCubeType != FettlingCubeType.NO_FETTLING) {
            createEntity(
                name = "ManufacturingStepFettling",
                clazz = ManufacturingStepFettlingCube::class,
                entityType = Entities.MANUFACTURING_STEP,
            )
        } else null

    @EntityProvider
    fun beltGrindingOptional(
        @Parent(Entities.MANUFACTURING)
        beltGrinding: SelectableBoolean
    ) =
        if (beltGrinding == SelectableBoolean.TRUE) {
            createEntity(
                name = "ManufacturingStepBeltGrinding",
                clazz = ManufacturingStepBeltGrinding::class,
                entityType = Entities.MANUFACTURING_STEP,
            )
        } else null

    @EntityProvider
    fun shotBlastingOptional(
        @Parent(Entities.MANUFACTURING)
        shotBlastingNeeded: SelectableBoolean,
        @Parent(Entities.MANUFACTURING)
        unfoldedPartLength: Length,
        @Parent(Entities.MANUFACTURING)
        unfoldedPartWidth: Length,
        @Parent(Entities.MANUFACTURING)
        sheetThickness: Length,
    ) = if (shotBlastingNeeded.toBoolean()) {
        createEntity(
            name = "ManufacturingStepShotBlastingDeburring",
            clazz = ManufacturingStepShotBlastingDeburring::class,
            entityType = Entities.MANUFACTURING_STEP,
            fields = mapOf(
                "stepSubTypeShotBlasting" to SystemType.CONTINUOUS,
                "shapeBulkMaterial" to Bool(true),
                "coefficientBulkMaterial" to Rate(0.6),
                "newPartLength" to unfoldedPartLength,
                "newPartWidth" to unfoldedPartWidth,
                "newPartHeight" to sheetThickness,
            ),
        )
    } else null

    @EntityProvider
    fun straighteningOptional(
        @Parent(Entities.PROCESSED_MATERIAL)
        straighteningType: StepSubTypeStraightening
    ) =
        if (straighteningType != StepSubTypeStraightening.NO_STRAIGHTENING) {
            createEntity(
                name = "ManufacturingStepStraightening",
                clazz = ManufacturingStepStraightening::class,
                entityType = Entities.MANUFACTURING_STEP,
            )
        } else null

    @EntityProvider
    fun bendingOptional(
        @Parent(Entities.PROCESSED_MATERIAL)
        numberOfBends: Quantity
    ) =
        if (numberOfBends.res > BigInteger.ZERO) {
            createEntity(
                name = "ManufacturingStepBendingCube",
                clazz = ManufacturingStepBendingCube::class,
                entityType = Entities.MANUFACTURING_STEP,
            )
        } else null
}
