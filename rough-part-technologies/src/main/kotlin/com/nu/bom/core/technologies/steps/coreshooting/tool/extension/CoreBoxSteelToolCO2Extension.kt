package com.nu.bom.core.technologies.steps.coreshooting.tool.extension

import com.nu.bom.core.manufacturing.annotations.Extends
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Sibling
import com.nu.bom.core.manufacturing.entities.ManufacturingEntityExtension
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.extension.CO2_EXTENSION_PACKAGE
import com.nu.bom.core.manufacturing.extension.ToolCO2Extension
import com.nu.bom.core.manufacturing.fieldTypes.*
import com.nu.bom.core.technologies.steps.coreshooting.tool.CoreBoxSteelTool

// TODO soll auch für TOOL ResingModel funktionieren
// TODO mit Tom anschaun warum der SCHEIß nicht rechnet im Test
@Extends([CoreBoxSteelTool::class], CO2_EXTENSION_PACKAGE)
class CoreBoxSteelToolCO2Extension(
    name: String,
) : ManufacturingEntityExtension(name) {
    override val extends = ToolCO2Extension(name)

    fun cO2ToolWeight(
        @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false) partHeight: Length,
        @Sibling(Entities.SYSTEM_PARAMETER) boxDepth: Length,
        @Sibling(Entities.SYSTEM_PARAMETER) boxHeight: Length,
        toolMaterialDensity: Density,
    ): Weight {
        val volume = (partHeight + 0.2.toBigDecimal()) * boxDepth.res * boxHeight.res
        return Weight(2.toBigDecimal() * volume.res * toolMaterialDensity.inKgPerCm, WeightUnits.KILOGRAM)
    }
}
