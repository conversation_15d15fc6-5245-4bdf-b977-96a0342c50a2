package com.nu.bom.core.technologies.steps.rinj.tool

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.annotations.StaticDenominatorUnit
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.Tool
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.StaticUnitOverride
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.RunnerSystemsRubber
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Volume
import com.nu.bom.core.manufacturing.fieldTypes.VolumeUnits
import com.nu.bom.core.technologies.service.ShapeLookupReaderService
import reactor.core.publisher.Mono
import java.math.RoundingMode

@EntityType(Entities.TOOL)
class RubberInjectionTool(
    name: String,
) : ManufacturingEntity(name) {
    override val extends: ManufacturingEntity = Tool(name)

    @SpecialLink("MaterialRubber", "density")
    fun density(): Density? = null

    @SpecialLink("MaterialRubber", "runnerSystems")
    fun runnerSystems(): RunnerSystemsRubber? = null

    @SpecialLink("MaterialRubber", "runnerDiameter")
    fun runnerDiameter(): Length? = null

    fun costPerSlider() = Money(500.0)

    fun costColdRunner() = Money(1000.0)

    fun toolVolume(
        @Parent(Entities.MANUFACTURING_STEP)
        calculatedToolWidth: Length,
        @Parent(Entities.MANUFACTURING_STEP)
        calculatedToolHeight: Length,
        runnerDiameter: Length,
    ): Volume = Volume(calculatedToolWidth.res * calculatedToolHeight.res * runnerDiameter.res, VolumeUnits.CM)

    fun toolWeight(
        toolVolume: Volume,
        density: Density,
    ): QuantityUnit = QuantityUnit(toolVolume.res * density.res)

    // Todo: https://tsetplatform.atlassian.net/browse/COST-38062
    @StaticDenominatorUnit(StaticUnitOverride.KILOGRAM)
    fun toolSteelPrice() = Money(5.0)

    fun materialCost(
        toolSteelPrice: Money,
        toolWeight: QuantityUnit,
    ): Money = Money(toolSteelPrice.res * toolWeight.res)

    private class TemplateLookupEntry(
        val numberOfCavities: QuantityUnit,
        val machiningCostPerCavity: Money,
    )

    fun machiningCost(
        @Parent(Entities.MANUFACTURING_STEP)
        partsPerCycleFamilyTooling: QuantityUnit,
    ): Mono<Money> =
        services
            .getLookupTable("ManufacturingStepRubberInjection_MachiningTool") {
                TemplateLookupEntry(
                    numberOfCavities = QuantityUnit(it[0]),
                    machiningCostPerCavity = Money(it[1]),
                )
            }.filter {
                it.numberOfCavities == partsPerCycleFamilyTooling
            }.single()
            .map {
                it.machiningCostPerCavity * it.numberOfCavities
            }

    fun numberOfSliders(
        lookupReaderService: ShapeLookupReaderService,
        @Parent(Entities.PROCESSED_MATERIAL)
        shapeId: Text,
    ): Mono<Num> =
        lookupReaderService.getRinjShapeData(calculationContext!!.accessCheck, shapeId).map {
            it.numberOfSliders
        }

    private data class RubberRunnerStressPointsLookup(
        val partsPerCycle: QuantityUnit,
        val numberOfStressPoints: Num,
    )

    fun materialAndMachiningCost(
        @Parent(Entities.MANUFACTURING_STEP)
        partsPerCycleFamilyTooling: QuantityUnit,
        numberOfSliders: Num,
        costColdRunner: Money,
        costPerSlider: Money,
        runnerSystems: RunnerSystemsRubber,
        materialCost: Money,
        machiningCost: Money,
    ): Mono<Money> =
        services
            .getLookupTable("ManufacturingStepRubberInjection_RunnerStressPoints") {
                RubberRunnerStressPointsLookup(
                    partsPerCycle = QuantityUnit(it[0]),
                    numberOfStressPoints = Num(it[1]),
                )
            }.filter {
                partsPerCycleFamilyTooling == it.partsPerCycle
            }.single()
            .map {
                val sliderCost = partsPerCycleFamilyTooling.res * numberOfSliders.res * costPerSlider.res
                val totalCostColdRunner = partsPerCycleFamilyTooling.res * costColdRunner.res / it.numberOfStressPoints.res

                val adjustedMachiningCost =
                    when (runnerSystems.res) {
                        RunnerSystemsRubber.Selection.COLD_RUNNER -> machiningCost + sliderCost + totalCostColdRunner
                        RunnerSystemsRubber.Selection.LOST_RUNNER -> machiningCost + sliderCost
                    }
                materialCost + adjustedMachiningCost
            }

    fun assemblyAndTestingCost(materialAndMachiningCost: Money): Money = materialAndMachiningCost * 0.5

    fun overheadCost(materialAndMachiningCost: Money): Money = materialAndMachiningCost * 0.2

    fun investPerTool(
        materialAndMachiningCost: Money,
        assemblyAndTestingCost: Money,
        overheadCost: Money,
    ): Money {
        val res = materialAndMachiningCost + assemblyAndTestingCost + overheadCost
        return Money(res.res.setScale(0, RoundingMode.HALF_EVEN))
    }

    fun maintenanceRate(): Rate = Rate(0.01)
}
