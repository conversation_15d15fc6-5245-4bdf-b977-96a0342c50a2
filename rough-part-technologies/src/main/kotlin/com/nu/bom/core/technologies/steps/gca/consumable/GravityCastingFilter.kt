package com.nu.bom.core.technologies.steps.gca.consumable

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.Consumable
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import java.math.BigDecimal

@EntityType(Entities.CONSUMABLE)
class GravityCastingFilter(name: String) : ManufacturingEntity(name) {

    override val extends = Consumable(name)

    @Input
    val basePrice: Money? = null

    @Input
    val reuseOfScrap: Bool = Bool(false)

    fun quantity(
        @Parent(Entities.MANUFACTURING_STEP)
        partsPerMold: Pieces
    ): QuantityUnit {
        return QuantityUnit(BigDecimal.ONE) / partsPerMold
    }
}
