package com.nu.bom.core.technologies.steps.turn.cycletimestep

import com.nu.bom.core.manufacturing.fieldTypes.Acceleration
import com.nu.bom.core.manufacturing.fieldTypes.AccelerationUnits
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Speed
import com.nu.bom.core.manufacturing.fieldTypes.SpeedUnits
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.compareTo
import com.nu.bom.core.manufacturing.fieldTypes.prod
import com.nu.bom.core.manufacturing.fieldTypes.quot
import java.math.BigDecimal
import java.math.MathContext

// TODO: Default value are manual CTS specific, so should not be here, since this code is now also used for milling.
internal fun timeToMoveTool(
    distance: Length,
    maxToolSpeed: Speed = Speed(20.0, SpeedUnits.M_PER_MIN),
    toolAcceleration: Acceleration = Acceleration(1.0, AccelerationUnits.M_PER_S2)
): Time {
    if (distance.res.compareTo(BigDecimal.ZERO) < 0) { throw IllegalArgumentException("negative distances") }

    val timeToReachMaxSpeed = quot(maxToolSpeed, toolAcceleration)
    val distanceToReachMaxSpeed = prod(0.5, prod(prod(toolAcceleration, timeToReachMaxSpeed), timeToReachMaxSpeed))

    if (distance > prod(2, distanceToReachMaxSpeed)) {
        val timeAtMaxSpeed = quot(distance - prod(2, distanceToReachMaxSpeed), maxToolSpeed)
        return prod(2, timeToReachMaxSpeed) + timeAtMaxSpeed
    }

    val timeToReachPeakSpeed = Time(
        (distance.to(LengthUnits.METER) / toolAcceleration.to(AccelerationUnits.M_PER_S2)).sqrt(MathContext(10)),
        TimeUnits.SECOND
    )
    return prod(2, timeToReachPeakSpeed)
}
