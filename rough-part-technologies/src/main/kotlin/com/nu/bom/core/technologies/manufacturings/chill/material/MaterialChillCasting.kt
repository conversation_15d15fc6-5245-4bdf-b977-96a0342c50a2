package com.nu.bom.core.technologies.manufacturings.chill.material

import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.annotations.SummaryView
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.RawMaterialCastingAlloy
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.MaterialCostMode
import com.nu.bom.core.manufacturing.fieldTypes.MaterialViewConfig
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.technologies.service.ShapeLookupReaderService
import reactor.core.publisher.Mono
import java.math.BigDecimal

@EntityType(Entities.MATERIAL)
class MaterialChillCasting(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = RawMaterialCastingAlloy(name)

    @Input
    @ObjectView(ObjectView.MATERIAL, 20)
    fun reuseOfScrap(): Bool = Bool(true)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 7)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun netWeightPerPart(): QuantityUnit? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun scrapWeightPerPart(): QuantityUnit? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun shapeId(): Text? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @ObjectView(ObjectView.MATERIAL, 1)
    @ReadOnly
    fun materialName(): Text? = null

    @Input
    @SpecialLink("SystemParameterGravityCasting", "castingType")
    fun castingType(): Text? = null

    @Input
    @ReadOnly
    @SpecialLink("ManufacturingStepGravityCasting", "partsPerMold")
    @ObjectView(ObjectView.MATERIAL, 5)
    fun partsPerMold(): Pieces? = null

    @Input
    @ObjectView(ObjectView.MATERIAL, 19)
    fun sprueAndOverflowLossRate(): Rate = Rate(0.01.toBigDecimal())

    @ObjectView(ObjectView.MATERIAL, 25)
    fun materialRecyclingPrice(pricePerUnit: Money): Money = pricePerUnit

    @Input
    @ObjectView(ObjectView.MATERIAL, 10)
    fun irretrievableLossRate(): Rate = Rate(0.04.toBigDecimal())

    @ObjectView(ObjectView.MATERIAL, 7)
    @SummaryView(SummaryView.PROCESS, 10)
    fun sprueAndOverflowRatePerMold(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
        castingType: Text,
    ): Mono<Rate> =
        lookupReaderService
            .getChillShapeData(calculationContext!!.accessCheck, shapeId)
            .map {
                Rate(it.sprueAndOverflowFactor)
            }.flatMap { sprueAndOverflowFactor ->
                services
                    .lookupValue(
                        "ManufacturingStepGravityCasting_SprueAndOverflowRatePerMold",
                        castingType.res,
                    ).map {
                        sprueAndOverflowFactor * (it?.toBigDecimal() ?: BigDecimal.ONE)
                    }
            }

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 6)
    fun netWeightPerMold(
        netWeightPerPart: QuantityUnit,
        partsPerMold: Pieces,
    ): QuantityUnit = netWeightPerPart * partsPerMold

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 9)
    fun sprueAndOverflowWeightPerMold(
        netWeightPerMold: QuantityUnit,
        sprueAndOverflowRatePerMold: Rate,
    ): QuantityUnit = netWeightPerMold * sprueAndOverflowRatePerMold

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 8)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun sprueAndOverflowWeightPerPart(
        sprueAndOverflowWeightPerMold: QuantityUnit,
        partsPerMold: Pieces,
    ): QuantityUnit = sprueAndOverflowWeightPerMold / partsPerMold

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 14)
    fun shotWeightPerMold(
        netWeightPerMold: QuantityUnit,
        sprueAndOverflowWeightPerMold: QuantityUnit,
    ): QuantityUnit = netWeightPerMold + sprueAndOverflowWeightPerMold

    @ObjectView(ObjectView.MATERIAL, 13)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun shotWeightPerPart(
        shotWeightPerMold: QuantityUnit,
        partsPerMold: Pieces,
    ): QuantityUnit = shotWeightPerMold / partsPerMold

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 18)
    fun materialInCirculationPerMold(
        sprueAndOverflowWeightPerMold: QuantityUnit,
        sprueAndOverflowLossWeightPerMold: QuantityUnit,
    ): QuantityUnit = sprueAndOverflowWeightPerMold - sprueAndOverflowLossWeightPerMold

    @ObjectView(ObjectView.MATERIAL, 17)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun materialInCirculationPerPart(
        materialInCirculationPerMold: QuantityUnit,
        partsPerMold: Pieces,
    ): QuantityUnit = materialInCirculationPerMold / partsPerMold

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 21)
    fun sprueAndOverflowLossWeightPerMold(
        sprueAndOverflowWeightPerMold: QuantityUnit,
        sprueAndOverflowLossRate: Rate,
    ): QuantityUnit = sprueAndOverflowWeightPerMold * sprueAndOverflowLossRate

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 20)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun sprueAndOverflowLossWeightPerPart(
        sprueAndOverflowLossWeightPerMold: QuantityUnit,
        partsPerMold: Pieces,
    ): QuantityUnit = sprueAndOverflowLossWeightPerMold / partsPerMold

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun negativeSprueAndOverflowLossWeightPerPart(sprueAndOverflowLossWeightPerPart: QuantityUnit): QuantityUnit =
        sprueAndOverflowLossWeightPerPart * (-1.0)

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 12)
    fun irretrievableLossWeightPerMold(
        shotWeightPerMold: QuantityUnit,
        irretrievableLossRate: Rate,
    ): QuantityUnit = shotWeightPerMold * irretrievableLossRate

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 11)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun irretrievableLossWeightPerPart(
        irretrievableLossWeightPerMold: QuantityUnit,
        partsPerMold: Pieces,
    ): QuantityUnit = irretrievableLossWeightPerMold / partsPerMold

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 23)
    fun deployedWeightPerMold(
        netWeightPerMold: QuantityUnit,
        irretrievableLossWeightPerMold: QuantityUnit,
        sprueAndOverflowLossWeightPerMold: QuantityUnit,
    ): QuantityUnit = netWeightPerMold + irretrievableLossWeightPerMold + sprueAndOverflowLossWeightPerMold

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 22)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun deployedWeightPerPart(
        netWeightPerPart: QuantityUnit,
        sprueAndOverflowLossWeightPerPart: QuantityUnit,
        irretrievableLossWeightPerPart: QuantityUnit,
    ): QuantityUnit = netWeightPerPart + sprueAndOverflowLossWeightPerPart + irretrievableLossWeightPerPart

    @ReadOnly
    fun irretrievableScrapPerPart(
        sprueAndOverflowLossWeightPerPart: QuantityUnit,
        irretrievableLossWeightPerPart: QuantityUnit,
    ): QuantityUnit = sprueAndOverflowLossWeightPerPart + irretrievableLossWeightPerPart

    @ReadOnly
    fun retrievableScrapPerPart(
        sprueAndOverflowWeightPerPart: QuantityUnit,
        negativeSprueAndOverflowLossWeightPerPart: QuantityUnit,
    ): QuantityUnit = sprueAndOverflowWeightPerPart + negativeSprueAndOverflowLossWeightPerPart

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 16)
    fun meltingShotWeightPerMold(
        shotWeightPerMold: QuantityUnit,
        irretrievableLossWeightPerMold: QuantityUnit,
    ): QuantityUnit = shotWeightPerMold + irretrievableLossWeightPerMold

    @ObjectView(ObjectView.MATERIAL, 15)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun meltingShotWeightPerPart(
        meltingShotWeightPerMold: QuantityUnit,
        partsPerMold: Pieces,
    ): QuantityUnit = meltingShotWeightPerMold / partsPerMold

    fun materialView(): MaterialViewConfig = MaterialViewConfig.MATERIAL_CHILL_CASTING

    /****** RawMaterials override **********/
    fun materialCostMode(): MaterialCostMode = MaterialCostMode.SELL_NOTHING

    fun materialWastePrice(pricePerUnit: Money): Money = Money(0.2.toBigDecimal() * pricePerUnit.res)

    fun quantityForExport(deployedWeightPerPart: QuantityUnit): QuantityUnit = deployedWeightPerPart

    fun density(headerKey: Text?): Mono<Density>? =
        headerKey?.let {
            services.findMaterialDensity(
                accessCheck = calculationContext().accessCheck,
                materialKey = headerKey.res,
            )
        }
}
