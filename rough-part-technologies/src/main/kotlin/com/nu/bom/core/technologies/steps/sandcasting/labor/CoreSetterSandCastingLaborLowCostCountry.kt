package com.nu.bom.core.technologies.steps.sandcasting.labor

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.Labor
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Time
import java.math.BigDecimal
import java.math.RoundingMode

@EntityType(Entities.LABOR)
class CoreSetterSandCastingLaborLowCostCountry(name: String) : ManufacturingEntity(name) {
    override val extends = Labor(name)

    fun requiredLabor(
        @Parent(Entities.MANUFACTURING_STEP)
        insertTimePerCore: Time,
        @Parent(Entities.MANUFACTURING_STEP)
        partsPerCycle: QuantityUnit,
        @Parent(Entities.MANUFACTURING_STEP)
        cycleTime: CycleTime,
    ): Num {
        return when {
            cycleTime.res.compareTo(BigDecimal.ZERO) == 0 -> Num(0)
            else -> Num(min(4.0.toBigDecimal(), (partsPerCycle.res * insertTimePerCore.res / cycleTime.res).setScale(0, RoundingMode.UP)))
        }
    }
}
