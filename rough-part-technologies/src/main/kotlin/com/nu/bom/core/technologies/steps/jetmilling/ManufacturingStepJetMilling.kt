package com.nu.bom.core.technologies.steps.jetmilling

import com.nu.bom.core.manufacturing.annotations.CalculationPreview
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.Selectable
import com.nu.bom.core.manufacturing.annotations.SubLabel
import com.nu.bom.core.manufacturing.annotations.SummaryView
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.SingleGroupManufacturingStep
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.ManufacturingStepType
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

@EntityType(Entities.MANUFACTURING_STEP)
class ManufacturingStepJetMilling(name: String) : ManufacturingEntity(name) {
    override val extends = SingleGroupManufacturingStep(name)

    @Input
    @SubLabel
    @ReadOnly
    @Selectable(value = "manufacturingStepType", getDisplayNameFromPathQuery = true)
    fun manufacturingStepType(): Text = Text(ManufacturingStepType.ELECTRONICS_MAGNETS.name)

    @Input
    fun utilizationRate(): Rate = Rate(0.85.toBigDecimal())

    fun scrapRate(): Rate = Rate(0.005.toBigDecimal())

    fun internalPartsPerCycle() = QuantityUnit(1.0)

    @SummaryView(SummaryView.PROCESS, 20, "configurationJetMilling")
    @CalculationPreview(3, "configurationJetMilling")
    fun templateName(): Mono<Text> {
        return services.getLookupTable("ManufacturingStepJetMilling_Templates") {
            it[0]
        }.elementAt(0).map { Text(it) }
    }

    @EntityCreation(Entities.MACHINE)
    fun createMachines(templateName: Text): Flux<ManufacturingEntity> {
        return createEntitiesFromTemplate(
            name = templateName.res + "_Machine",
            location = "Global",
        )
    }

    @EntityCreation(Entities.LABOR)
    fun createLabor(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> {
        return createEntitiesFromTemplate(
            name = templateName.res + "_Labor",
            location = locationName.res,
        )
    }

    @EntityCreation(Entities.SETUP)
    fun createSetup(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> {
        return createEntitiesFromTemplate(
            name = templateName.res + "_Setup",
            location = locationName.res,
        )
    }

    @EntityCreation(Entities.TOOL)
    fun createTools(templateName: Text): Flux<ManufacturingEntity> {
        return createEntitiesFromTemplate(
            name = templateName.res + "_Tools",
            location = "Global",
        )
    }
}
