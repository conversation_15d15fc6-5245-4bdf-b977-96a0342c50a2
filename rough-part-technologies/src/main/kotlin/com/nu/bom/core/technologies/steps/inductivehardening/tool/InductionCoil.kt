package com.nu.bom.core.technologies.steps.inductivehardening.tool

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.Tool
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Pieces

@EntityType(Entities.TOOL)
class InductionCoil(name: String) : ManufacturingEntity(name) {

    override val extends = Tool(name)

    fun serviceLifeInCycles(): Num {
        return Num(1000000)
    }

    fun quantity(
        @Parent(Entities.MANUFACTURING_STEP)
        numberOfCoilsNeeded: Pieces
    ): Pieces {
        return numberOfCoilsNeeded
    }
}
