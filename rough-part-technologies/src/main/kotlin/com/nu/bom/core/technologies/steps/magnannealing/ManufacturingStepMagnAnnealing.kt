package com.nu.bom.core.technologies.steps.magnannealing

import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.FieldName
import com.nu.bom.core.manufacturing.annotations.FilteredChildren
import com.nu.bom.core.manufacturing.annotations.Hidden
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.StaticDenominatorUnit
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_ELECTRICITY_PRICE_ENTITY_NAME
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_NATURAL_GAS_PRICE_ENTITY_NAME
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.StaticUnitOverride
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Diffusivity
import com.nu.bom.core.manufacturing.fieldTypes.DiffusivityUnits
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Temperature
import com.nu.bom.core.manufacturing.fieldTypes.TemperatureUnits
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.technologies.steps.normalizing.ManufacturingStepNormalizing
import com.nu.bom.core.utils.getResultWithSelectedAttributes
import org.bson.types.ObjectId
import java.math.RoundingMode

@EntityType(Entities.MANUFACTURING_STEP)
class ManufacturingStepMagnAnnealing(name: String) : ManufacturingEntity(name) {
    override val extends = ManufacturingStepNormalizing(name)

    @Hidden
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 180)
    @StaticDenominatorUnit(StaticUnitOverride.KILOWATTHOUR)
    @ReadOnly
    fun heatingEnergyCostsElectricityPerKwh(
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_ELECTRICITY_PRICE,
            nameFilter = TSET_COST_ELECTRICITY_PRICE_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        ) value: Map<ObjectId, Money>?,
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_ELECTRICITY_PRICE,
            nameFilter = TSET_COST_ELECTRICITY_PRICE_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        @FieldName("location")
        costFactorLocations: Map<ObjectId, Text>,
        location: Text,
    ): Money? {
        return getResultWithSelectedAttributes(value, costFactorLocations, location)
    }

    @Hidden
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 200)
    @StaticDenominatorUnit(StaticUnitOverride.CM)
    @ReadOnly
    fun heatingEnergyCostsNaturalGasPerQm(
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_NATURAL_GAS_PRICE,
            nameFilter = TSET_COST_NATURAL_GAS_PRICE_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        ) value: Map<ObjectId, Money>?,
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_NATURAL_GAS_PRICE,
            nameFilter = TSET_COST_NATURAL_GAS_PRICE_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        @FieldName("location")
        costFactorLocations: Map<ObjectId, Text>,
        location: Text,
    ): Money? {
        return getResultWithSelectedAttributes(value, costFactorLocations, location)
    }

    fun template() = Text("ManufacturingStepMagnAnnealing_Templates")

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 5)
    @ReadOnly
    fun shapeBulkMaterial() = Bool(true)

    fun partsPerCycle(internalPartsPerCycle: QuantityUnit): QuantityUnit =
        QuantityUnit(internalPartsPerCycle.res.setScale(0, RoundingMode.DOWN))

    @DefaultUnit(DefaultUnit.CELSIUS)
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 30)
    fun soakingTemperature() = Temperature(800.0, TemperatureUnits.CELSIUS)

    @DefaultUnit(DefaultUnit.MINUTE)
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 20)
    fun firstSoakingTimePerArea() = Time(12.toBigDecimal(), TimeUnits.HOUR)

    @DefaultUnit(DefaultUnit.MINUTE)
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 40)
    fun coolingTimePerArea(
        @Parent(Entities.PROCESSED_MATERIAL)
        maxWallThickness: Length,
    ) = Time(43.491.toBigDecimal() + maxWallThickness.inMillimeter * 1.724.toBigDecimal(), TimeUnits.MINUTE)

    fun specificThermalCapacity() = Diffusivity(0.44, DiffusivityUnits.QMM_PER_SECONDS)
}
