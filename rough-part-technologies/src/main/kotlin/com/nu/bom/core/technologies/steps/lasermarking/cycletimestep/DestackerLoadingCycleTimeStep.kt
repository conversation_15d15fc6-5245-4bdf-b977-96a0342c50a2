package com.nu.bom.core.technologies.steps.lasermarking.cycletimestep

import com.nu.bom.core.manufacturing.annotations.*
import com.nu.bom.core.manufacturing.entities.CycleTimeStep
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.*
import java.math.BigDecimal

@EntityType(Entities.CYCLETIME_STEP)
class DestackerLoadingCycleTimeStep(name: String) : ManufacturingEntity(name) {

    override val extends = CycleTimeStep(name)

    @Input
    val adjustmentRate = Rate(BigDecimal.ONE)

    val time = Time(30.0,TimeUnits.SECOND)

}




