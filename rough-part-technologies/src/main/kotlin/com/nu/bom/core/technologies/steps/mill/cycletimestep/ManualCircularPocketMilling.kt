package com.nu.bom.core.technologies.steps.mill.cycletimestep

import com.nu.bom.core.exception.userException.LimitType
import com.nu.bom.core.exception.userException.NumericInputExceedsLimitException
import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntitySubtypes
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Hidden
import com.nu.bom.core.manufacturing.annotations.IgnoreMasterData
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.OnChangeClear
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.SourceDataInput
import com.nu.bom.core.manufacturing.defaults.NullProvider
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Deg
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.PositiveQuantity
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Revolution
import com.nu.bom.core.manufacturing.fieldTypes.Speed
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.compareTo
import com.nu.bom.core.manufacturing.fieldTypes.maxOf
import com.nu.bom.core.manufacturing.fieldTypes.prod
import com.nu.bom.core.manufacturing.fieldTypes.quot
import com.nu.bom.core.technologies.steps.turn.cycletimestep.timeToMoveTool
import java.math.BigInteger
import kotlin.math.sin

@EntityType(Entities.CYCLETIME_STEP, userCreatable = true)
@EntitySubtypes(["MILLING"])
@com.nu.bom.core.manufacturing.annotations.MasterDataType(MasterDataType.TOOL_SHOULDER_MILLING)
class ManualCircularPocketMilling(name: String) : ManufacturingEntity(name) {
    override val extends = ManualMilling(name)

    @Input
    @MandatoryForEntity
    @IgnoreMasterData
    fun displayDesignation(): Text = Text("Circular Pocket Milling")

    @Hidden
    fun allowEqual(): Bool = Bool(false)

    @Hidden
    @MandatoryForEntity(refresh = true)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @OnChangeClear(["selectedTool"])
    fun calculatedToolDiameter(
        @Default(NullProvider::class) diameter: Length?,
    ): Length? = diameter

    @Input
    @MandatoryForEntity(section = "right", index = 1, refresh = true, showSystemValue = true)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @SourceDataInput("maxFeedRatePerRevolution${ManualShoulderMilling.CUTTING_PARAMETER}")
    fun feedRatePerTooth(): Length? = null

    @Input
    @MandatoryForEntity(section = "right", index = 1, refresh = true, showSystemValue = true, computed = true)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @IgnoreMasterData
    fun rampFeedRatePerTooth(feedRatePerTooth: Length): Length {
        return feedRatePerTooth * 0.5.toBigDecimal()
    }

    @Input
    @MandatoryForEntity(index = 8, refresh = true)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun depth(): Length? = null

    @Input
    @MandatoryForEntity(index = 7, refresh = true)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @OnChangeClear(["selectedTool"])
    fun diameter(): Length? = null

    @Input
    @MandatoryForEntity(index = 9, refresh = true)
    fun plungeAngle(): Deg? = null

    @Input
    @MandatoryForEntity(section = "right", index = 4, refresh = true)
    fun cuttingWidthFactor(): Rate = Rate(0.8.toBigDecimal())

    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun maxCuttingWidth(
        toolDiameter: Length,
        cuttingWidthFactor: Rate,
    ): Length = toolDiameter * cuttingWidthFactor.res

    @ReadOnly
    fun numberOfDepthLayers(
        depth: Length,
        maxCuttingDepth: Length,
    ): PositiveQuantity {
        return PositiveQuantity(quot(depth, maxCuttingDepth).ceilToBigInteger)
    }

    @ReadOnly
    fun numberOfWidthLayers(
        diameter: Length,
        toolDiameter: Length,
        maxCuttingWidth: Length,
    ): PositiveQuantity {
        if (diameter <= toolDiameter) {
            throw NumericInputExceedsLimitException("diameter", LimitType.LESS_EQ, "toolDiameter")
        }
        if (toolDiameter < maxCuttingWidth) {
            throw NumericInputExceedsLimitException("toolDiameter", LimitType.LESS, "maxCuttingWidth")
        }
        return PositiveQuantity(
            BigInteger.ONE +
                quot(
                    maxOf(diameter - toolDiameter, 0.0),
                    maxCuttingWidth,
                ).ceilToBigInteger,
        )
    }

    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun realCuttingDepth(
        depth: Length,
        numberOfDepthLayers: PositiveQuantity,
    ): Length {
        return quot(depth, numberOfDepthLayers)
    }

    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun realCuttingWidth(
        diameter: Length,
        numberOfWidthLayers: PositiveQuantity,
        toolDiameter: Length,
    ): Length {
        return if (numberOfWidthLayers.res == BigInteger.ONE) {
            Length(0.0, LengthUnits.MILLIMETER)
        } else {
            quot(diameter - toolDiameter, numberOfWidthLayers.res - BigInteger.ONE)
        }
    }

    fun cuttingLengthFirstLayers(
        numberOfWidthLayers: PositiveQuantity,
        depth: Length,
        realCuttingDepth: Length,
        plungeAngle: Deg,
        numberOfDepthLayers: PositiveQuantity,
        safety: Length,
    ): Length {
        return if (numberOfWidthLayers.res == BigInteger.ONE) {
            depth * sin(plungeAngle.rad.res.toDouble()) * numberOfDepthLayers.res.toBigDecimal() + safety
        } else {
            realCuttingDepth * sin(plungeAngle.rad.res.toDouble()) * numberOfDepthLayers.res.toBigDecimal() + safety
        }
    }

    fun innerDiameter(
        cuttingWidthFactor: Rate,
        toolDiameter: Length,
    ): Length {
        return toolDiameter * 2.toBigDecimal() * cuttingWidthFactor
    }

    fun distanceBetweenWidthLayers(
        innerDiameter: Length,
        diameter: Length,
        numberOfWidthLayers: PositiveQuantity,
    ): Length {
        return ((diameter - innerDiameter) / (2.toBigDecimal() * numberOfWidthLayers.res.toBigDecimal()))
    }

    private val shoulderMilling = ManualShoulderMillingCircular("this")

    fun cuttingLengthRestLayer(
        safety: Length,
        numberOfWidthLayers: PositiveQuantity,
        distanceBetweenWidthLayers: Length,
        innerDiameter: Length,
        numberOfDepthLayers: PositiveQuantity,
    ): Length {
        return shoulderMilling.cuttingLengthInnerOuter(
            safety,
            numberOfWidthLayers,
            distanceBetweenWidthLayers,
            innerDiameter,
            2.toBigDecimal(),
            numberOfDepthLayers,
        )
    }

    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun millingLength(
        cuttingLengthFirstLayers: Length,
        cuttingLengthRestLayer: Length,
    ): Length {
        return cuttingLengthFirstLayers + cuttingLengthRestLayer
    }

    @ReadOnly
    @DefaultUnit(DefaultUnit.MM_PER_MIN)
    fun rampFeedRate(
        rampFeedRatePerTooth: Length,
        numberOfTeeth: PositiveQuantity,
        rotationalSpeed: Revolution,
    ): Speed {
        return prod(prod(rampFeedRatePerTooth, numberOfTeeth), rotationalSpeed)
    }

    @DefaultUnit(DefaultUnit.SECOND)
    fun primaryTimePerOperation(
        cuttingLengthFirstLayers: Length,
        cuttingLengthRestLayer: Length,
        rampFeedRate: Speed,
        feedRate: Speed,
    ): Time {
        val firstLayerTime = quot(cuttingLengthFirstLayers, rampFeedRate)
        val restLayerTime = quot(cuttingLengthRestLayer, feedRate)
        return firstLayerTime + restLayerTime
    }

    @DefaultUnit(DefaultUnit.SECOND)
    fun secondaryTimePerOperation(
        depth: Length,
        cuttingSpeed: Speed,
        safety: Length,
    ): Time {
        return timeToMoveTool(depth + safety, cuttingSpeed)
    }
}
