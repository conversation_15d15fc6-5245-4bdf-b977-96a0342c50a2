package com.nu.bom.core.technologies.steps.mill.cycletimestep

import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Speed
import com.nu.bom.core.manufacturing.fieldTypes.Text

@EntityType(Entities.CYCLETIME_STEP)
class MillingThreadingCycleTimeStep(name: String): ManufacturingEntity(name) {

    override val extends = MillingPartCycleTimeStep(name)

    @Input
    @ReadOnly
    val vc: Speed? =null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    val pitch: Length? = null

    @Input
    @ReadOnly
    val rotationalSpeed: Speed? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    val minorDiameter: Length? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    val majorDiameter: Length? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    val cuttingLength: Length? = null

    @Input
    @ReadOnly
    val threadingDesignation: Text? = null
}
