package com.nu.bom.core.technologies.steps.mill.cycletimestep

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Text

@EntityType(Entities.CYCLETIME_STEP)
class MillingToolChangeCycleTimeStep(name: String): ManufacturingEntity(name){

    override val extends = MillingCycleTimeStep(name)

    @Input
    @ReadOnly
    val fromTool: Text? = null

    @Input
    @ReadOnly
    val toTool: Text? = null

    @Input
    val toolName: Text? = Text("No tool")

}
