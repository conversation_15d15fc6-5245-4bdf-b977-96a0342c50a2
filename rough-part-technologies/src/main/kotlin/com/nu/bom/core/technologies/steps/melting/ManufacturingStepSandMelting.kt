package com.nu.bom.core.technologies.steps.melting

import com.nu.bom.core.exception.userException.TemplateNotFoundException
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityLinkField
import com.nu.bom.core.manufacturing.annotations.EntityLinkProvider
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.ExpectedParents
import com.nu.bom.core.manufacturing.annotations.ExternalDependency
import com.nu.bom.core.manufacturing.annotations.FieldIndex
import com.nu.bom.core.manufacturing.annotations.FieldName
import com.nu.bom.core.manufacturing.annotations.FilteredChildren
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Modularized
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CastingAlloyMaterialGroup
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.EntityRef
import com.nu.bom.core.manufacturing.fieldTypes.FurnaceType
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import com.nu.bom.core.technologies.lookups.sandMeltingTemplatesReader
import com.nu.bom.core.technologies.manufacturings.sand.material.MaterialSandCasting
import com.nu.bom.core.utils.CostFactorUtils
import com.nu.bom.core.utils.doOnEmpty
import org.bson.types.ObjectId
import reactor.core.publisher.Mono
import java.math.BigDecimal

@EntityType(Entities.MANUFACTURING_STEP)
@Modularized(
    technologies = [Model.SAND],
    parents = [
        ExpectedParents(model = Model.SAND, type = Entities.MANUFACTURING),
        ExpectedParents(model = Model.SAND, type = Entities.PROCESSED_MATERIAL),
    ],
    dimensions = [Dimension.Selection.NUMBER],
    userCreatable = true,
)
class ManufacturingStepSandMelting(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = ManufacturingStepMelting(name)

    @ExternalDependency(ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 0)
    @EntityLinkProvider(entityRef = ["MaterialCasting"], entityClasses = [MaterialSandCasting::class])
    @Path("/api/link{bomPath}{branchPath}?entityClasses=MaterialSandCasting")
    fun linkedMaterial(): EntityRef? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterial", fieldName = "meltingShotWeightPerCycle")
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun meltingShotWeightPerCycle(): QuantityUnit? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterial", fieldName = "partsPerCycle")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    fun partsPerCycleMaterialSandCasting(): QuantityUnit? = null

    fun templateName(
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_COUNTRY_INFO,
            nameFilter = MasterdataCostFactorParent.TSET_COST_COUNTRY_ID_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        ) value: Map<ObjectId, Text>,
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_COUNTRY_INFO,
            nameFilter = MasterdataCostFactorParent.TSET_COST_COUNTRY_ID_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        @FieldName("location")
        costFactorLocations: Map<ObjectId, Text>,
        location: Text,
        furnaceType: FurnaceType,
        materialGroup: CastingAlloyMaterialGroup,
        meltingShotWeightPerPart: QuantityUnit,
        partsPerCycleMaterialSandCasting: QuantityUnit,
    ): Mono<Text> {
        val costFactorForCurrentStepId = CostFactorUtils.getCostFactorForCurrentStepId(costFactorLocations, location)
        val meltingShotWeightPerCycle = meltingShotWeightPerPart * partsPerCycleMaterialSandCasting

        val lookupName = "ManufacturingStepSandMelting_Templates"
        val countryId =
            requireNotNull(value[costFactorForCurrentStepId]) {
                "CountryId for $costFactorForCurrentStepId not found"
            }
        val isChinaOrIndia = countryId.res == "10" || countryId.res == "18"

        return services.getLookupTable(lookupName, sandMeltingTemplatesReader).filter {
            val diffFurnace = furnaceType == FurnaceType.valueOf(it.furnaceType)
            val diffChinaOrIndia = it.lowCostCountry == isChinaOrIndia
            val diffMagnesium = it.magnesium == materialGroup.isMagnesiumBasedAlloy
            val meltingWeightPerHour = it.meltingWeightPerHour.resultIn(WeightUnits.KILOGRAM, TimeUnits.HOUR)
            val diffMeltingWeight =
                when {
                    meltingShotWeightPerCycle.res <= meltingWeightPerHour * BigDecimal(0.5) -> true
                    meltingWeightPerHour.compareTo(BigDecimal(20000.0)) == 0 -> true
                    else -> false
                }
            diffChinaOrIndia && diffFurnace && diffMagnesium && diffMeltingWeight && it.calculationModuleCanSelect
        }
            .doOnEmpty {
                throw TemplateNotFoundException(
                    lookupName = lookupName,
                    lookupInputs =
                        mapOf(
                            "countryId" to countryId.res,
                            "furnaceType" to furnaceType.toString(),
                            "materialGroup" to materialGroup.toString(),
                            "meltingShotWeightPerCycle" to meltingShotWeightPerCycle.toString(),
                        ),
                )
            }.elementAt(0)
            .map { Text(it.templateName) }
    }
}
