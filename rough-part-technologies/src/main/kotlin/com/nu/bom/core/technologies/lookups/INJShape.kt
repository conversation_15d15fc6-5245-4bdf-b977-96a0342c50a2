package com.nu.bom.core.technologies.lookups

import com.nu.bom.core.manufacturing.fieldTypes.RunnerSystems
import com.nu.bom.core.manufacturing.fieldTypes.SliderConceptIdInjection

data class INJShape(
    override val shapeId: String,
    override val shapeTechnologyGroup: String,
    override val tech: String,
    override val inputGroup: String,
    val slilderConceptId: SliderConceptIdInjection,
    val runnerSystems: RunnerSystems?,
) : LookupShape

val INJShapeReader: (row: List<String>) -> INJShape = { row ->
    INJShape(
        shapeId = row[0],
        shapeTechnologyGroup = row[1],
        tech = row[2],
        inputGroup = row[3],
        slilderConceptId = SliderConceptIdInjection.valueOf(row[5]),
        runnerSystems = row[6].takeIf { it.isNotEmpty() }?.let { RunnerSystems.valueOf(it) },
    )
}
