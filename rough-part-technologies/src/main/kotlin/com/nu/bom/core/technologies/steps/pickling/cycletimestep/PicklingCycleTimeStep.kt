package com.nu.bom.core.technologies.steps.pickling.cycletimestep

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.entities.CycleTimeStep
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate

@EntityType(Entities.CYCLETIME_STEP)
class PicklingCycleTimeStep(name: String) : ManufacturingEntity(name) {
    override val extends: ManufacturingEntity = CycleTimeStep(name)

    fun time(): CycleTime = CycleTime(3600.toBigDecimal(), CycleTimeUnit.SECOND)

    fun adjustmentRate(): Rate = Rate(1.0.toBigDecimal())
}
