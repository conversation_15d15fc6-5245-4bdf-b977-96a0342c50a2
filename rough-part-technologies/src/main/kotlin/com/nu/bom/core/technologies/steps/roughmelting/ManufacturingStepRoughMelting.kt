package com.nu.bom.core.technologies.steps.roughmelting

import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.RoughManufacturingStep
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import com.nu.bom.core.utils.annotations.TsetSuppress
import reactor.core.publisher.Flux

@EntityType(Entities.MANUFACTURING_STEP)
class ManufacturingStepRoughMelting(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = RoughManufacturingStep(name)

    @Input
    @SpecialLink("MagnTestMaterial", "grossWeightPerPart")
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun grossWeightPerPart(): QuantityUnit? = null

    @Input
    @ReadOnly
    @MandatoryForEntity(index = 220, refresh = true)
    fun dimension() = Dimension(Dimension.Selection.MASS)

    @Input
    @Path("/api/type/units?dimension={dimension}")
    @TsetSuppress("tset:engine:field-override-configuration-value")
    fun quantityUnit() = Text(WeightUnits.GRAM.name)

    @Input
    @MandatoryForEntity(index = 225)
    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun pricePerManufacturingStepUnit() = Money(0.5)

    @Input
    @MandatoryForEntity(index = 224)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun quantityOfManuStep(grossWeightPerPart: QuantityUnit) = grossWeightPerPart

    @EntityCreation(Entities.MATERIAL)
    fun createMaterial(
        @Parent(Entities.MANUFACTURING)
        materialTemplate: Text,
    ): Flux<ManufacturingEntity> =
        createEntitiesFromTemplate(
            name = materialTemplate.res + "_Material",
            location = "Global",
        )
}
