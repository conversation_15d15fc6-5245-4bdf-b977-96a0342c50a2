package com.nu.bom.core.technologies.lookups

import java.math.BigDecimal

data class CoefficientBulkMaterial(
    val shapeId: String,
    val shapeTechnologyGroup: String,
    val result: BigDecimal,
)

val coefficientBulkMaterialReader: (row: List<String>) -> CoefficientBulkMaterial = { row ->
    CoefficientBulkMaterial(
        shapeId = row[0],
        shapeTechnologyGroup = row[1],
        result = BigDecimal(row[3]),
    )
}
