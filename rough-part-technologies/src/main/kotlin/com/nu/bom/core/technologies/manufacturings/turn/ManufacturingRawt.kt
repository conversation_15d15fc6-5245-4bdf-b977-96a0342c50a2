package com.nu.bom.core.technologies.manufacturings.turn

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.BehaviourCreation
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EngineTransient
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.StaticDenominatorUnit
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.MissingInputError
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.StaticUnitOverride
import com.nu.bom.core.manufacturing.fieldTypes.ConfigIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.MachiningForTechnology
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeCleannessExtension
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeTolerancesDfor
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.configuration.VersionedCostModuleField
import com.nu.bom.core.manufacturing.service.behaviour.DynamicBehaviour
import com.nu.bom.core.manufacturing.service.behaviour.EntityBasedDynamicBehaviour
import com.nu.bom.core.technologies.manufacturings.turn.material.RawTurnedMaterial
import reactor.core.publisher.Mono
import java.math.BigDecimal
import java.util.Locale
import kotlin.reflect.KClass

@EntityType(Entities.MANUFACTURING)
class ManufacturingRawt(
    name: String,
) : ManufacturingTurning(name) {
    override val model: Model
        get() = Model.RAWT

    override val modelSteps: List<KClass<out ManufacturingEntity>>
        get() = emptyList()

    @BehaviourCreation
    fun createPartBehaviour(): DynamicBehaviour = EntityBasedDynamicBehaviour(this, ShapeBasedRawtBehaviorLookup(name))

    @Input
    override fun shapeTechnologyGroup(
        shapeId: Text,
        rawPartTechnology: Text,
    ): Mono<Text> {
        val techLookup = Model.valueOf(rawPartTechnology.res).path.uppercase(Locale.getDefault())
        return getLookupTable(techLookup + "_Shape") {
            it
        }.onErrorResume {
            Mono.empty()
        }.filter {
            it[0] == shapeId.res
        }.single()
            .map {
                Text(it[1])
            }
    }

    fun lengthForLookup(rawPartLength: Length) = rawPartLength

    fun diameterForLookup(rawPartOuterDiameter: Length) = rawPartOuterDiameter

    @Input
    val technology = Text("RAWT")

    fun cleaningNeeded(): SelectableBoolean = SelectableBoolean.TRUE

    fun cleanness(): StepSubTypeCleannessExtension = StepSubTypeCleannessExtension.NORMAL

    @Input
    val rawPartTechnology: Text? = null

    @Input
    fun rawCostModuleConfigurationIdentifier(): ConfigIdentifier = throw MissingInputError()

    /**
     * Returns object that holds versions for cost module for this entity.
     * Most of the time extensions belong to only one cost module.
     */
    @EngineTransient
    fun costModuleVersionProvider(costModuleConfigurationIdentifier: ConfigIdentifier): Mono<VersionedCostModuleField> =
        services.getCostModuleConfigurationField(
            model.name,
            costModuleConfigurationIdentifier.res.data,
        )

    @Input
    @StaticDenominatorUnit(StaticUnitOverride.YEAR)
    val callsPerYear: Num = Num(12.toBigDecimal())

    @Input
    @Path("/api/model/dfor/materialName")
    val materialName: Text? = null

    @Input
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    val rawWeightPerPart: QuantityUnit? = null

    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @Suppress("ktlint:standard:property-naming")
    val CHD: Length = Length(2.toBigDecimal(), LengthUnits.MILLIMETER)

//    // TODO: Is this set correctly?
//    @Input
//    val stepSubTypeHeatTreatment: StepSubTypeHeatTreatmentSteel? = null

    @Input
    val rawPartLength: Length? = null

    @Input
    val rawPartInnerDiameter: Length? = null

    @Input
    val rawPartOuterDiameter: Length? = null

    @Input
    val rawPartMaxWallThickness: Length? = null

    @Input
    val netPartLength: Length? = null

    @Input
    val netPartInnerDiameter: Length? = null

    @Input
    val netPartOuterDiameter: Length? = null

    @Input
    val netPartMaxWallThickness: Length? = null

    @Input
    val machining: MachiningForTechnology = MachiningForTechnology.turning

    @Input
    fun maxWallThickness(netPartMaxWallThickness: Length?) = netPartMaxWallThickness

    @Input
    val tolerance: StepSubTypeTolerancesDfor = StepSubTypeTolerancesDfor.STANDARD_DFOR

    @Input
    val margin: Length = Length(BigDecimal(3), LengthUnits.MILLIMETER)

    @EntityCreation(Entities.PROCESSED_MATERIAL, childCreations = [Entities.MANUFACTURING_STEP])
    fun createMaterial(): ManufacturingEntity =
        createEntity(
            name = "RawTurnedMaterial",
            clazz = RawTurnedMaterial::class,
            entityType = Entities.PROCESSED_MATERIAL,
        )
}
