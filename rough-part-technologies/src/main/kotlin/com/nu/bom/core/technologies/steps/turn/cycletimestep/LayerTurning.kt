package com.nu.bom.core.technologies.steps.turn.cycletimestep

import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.entities.CycleTimeStep
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeUnit
import com.nu.bom.core.manufacturing.fieldTypes.Frequency
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Speed
import com.nu.bom.core.manufacturing.fieldTypes.prod
import com.nu.bom.core.manufacturing.fieldTypes.quot
import java.lang.Math.PI

@EntityType(Entities.CYCLETIME_STEP)
class LayerTurning(name: String) : ManufacturingEntity(name) {

    //TurningCycleTimeStep provides the common interface
    //all turning cycle time steps need to have, but this is not a turning cycle time step,
    //just a layer inside of turning cycle time steps, so it has the interface of a general CycleTimeStep.
    override val extends = CycleTimeStep(name)

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    val feedRatePerRevolution: Length? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    val diameterAtMidpoint: Length? = null

    @Input
    @ReadOnly
    val cuttingSpeedAtMidpoint: Speed? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    val length: Length? = null

    @ReadOnly
    fun circumferenceAtMidpoint(diameterAtMidpoint: Length) = prod(PI, diameterAtMidpoint)

    //We calculate with constant rotational speed for the entire layer.
    //So we can apply the formula for rotational speed at the midpoint of the layer
    //to get this constant.
    @ReadOnly
    fun rotationalSpeed(
        cuttingSpeedAtMidpoint: Speed,
        circumferenceAtMidpoint: Length
    ) = quot(cuttingSpeedAtMidpoint, circumferenceAtMidpoint)

    @ReadOnly
    fun time(
        length: Length,
        rotationalSpeed: Frequency,
        feedRatePerRevolution: Length
    ) = CycleTime(quot(length, prod(feedRatePerRevolution, rotationalSpeed)).inSeconds, CycleTimeUnit.SECOND)
}
