package com.nu.bom.core.technologies.steps.waxcluster.cycletimestep

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.CycleTimeStep
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeUnit
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Time

@EntityType(Entities.CYCLETIME_STEP)
class WaxClusterAssemblyCycleTimeStep(name:String):ManufacturingEntity(name) {

    override val extends = CycleTimeStep(name)

    fun time(
        @Parent(Entities.PROCESSED_MATERIAL)
            numberOfPartsPerTree:Pieces,
        @Parent(Entities.MANUFACTURING_STEP)
            waxClusterAssemblyTimePerPart:Time
    ):CycleTime{return CycleTime(waxClusterAssemblyTimePerPart.res*numberOfPartsPerTree.res, CycleTimeUnit.SECOND)}

    val adjustmentRate: Rate = Rate(1.0.toBigDecimal())

}
