package com.nu.bom.core.technologies.manufacturings.pbox.material

import com.nu.bom.core.manufacturing.fieldTypes.MaterialViewConfig
import org.springframework.stereotype.Service

@Service
object MaterialPboxInkMagentaViewProvider : MaterialPboxInkViewProvider() {
    override val materialView = MaterialViewConfig.MaterialView.MATERIAL_PBOX_INK_MAGENTA

    override val shareAndWeightFields: List<String> =
        listOf(
            MaterialPboxInkMagenta::magentaShare.name,
            MaterialPboxInkMagenta::magentaPrintingWeight.name,
        )
}
