package com.nu.bom.core.technologies.steps.pcb

import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.HasNavigation
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.Selectable
import com.nu.bom.core.manufacturing.annotations.SubLabel
import com.nu.bom.core.manufacturing.annotations.TranslationLabel
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.SingleGroupManufacturingStep
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.ManufacturingStepType
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import reactor.core.publisher.Mono
import java.math.BigDecimal

@EntityType(Entities.MANUFACTURING_STEP)
@Deprecated("Dead code which we cannot delete due to BCT, see COST-73273")
class ManufacturingStepPcb(name: String) : ManufacturingEntity(name) {
    override val extends = SingleGroupManufacturingStep(name)

    @Input
    @SubLabel
    @ReadOnly
    @Selectable(value = "manufacturingStepType", getDisplayNameFromPathQuery = true)
    fun manufacturingStepType(): Text = Text(ManufacturingStepType.ELECTRONICS_PCBA.name)

    @Input
    @MandatoryForEntity(index = 1)
    fun partsPerCycle() = QuantityUnit(BigDecimal.ONE)

    @Input
    @MandatoryForEntity(index = 3)
    fun utilizationRate() = Rate(0.8)

    fun scrapRate() = Rate(BigDecimal.ZERO)

    @ReadOnly
    @HasNavigation(ObjectView.NONE)
    @TranslationLabel("manualStep")
    fun templateName(): Text = Text("NONE")

    @EntityCreation(Entities.MATERIAL)
    fun createMaterial(
        @Parent(Entities.PROCESSED_MATERIAL)
        materialName: Text,
        @Parent(Entities.PROCESSED_MATERIAL)
        materialClass: Text,
    ): Mono<ManufacturingEntity> {
        return createEntityWithNewMasterdata(
            name = "MaterialPcb",
            entityType = Entities.MATERIAL,
            clazz = materialClass.res,
            masterDataType = MasterDataType.RAW_MATERIAL_PCB,
            masterDataKey = materialName.res,
        )
    }
}
