package com.nu.bom.core.technologies.manufacturings.pbox.material

import com.nu.bom.core.manufacturing.fieldTypes.MaterialViewConfig
import org.springframework.stereotype.Service

@Service
object MaterialPboxInkCyanViewProvider : MaterialPboxInkViewProvider() {
    override val materialView = MaterialViewConfig.MaterialView.MATERIAL_PBOX_INK_CYAN

    override val shareAndWeightFields: List<String> =
        listOf(
            MaterialPboxInkCyan::cyanShare.name,
            MaterialPboxInkCyan::cyanPrintingWeight.name,
        )
}
