package com.nu.bom.core.technologies.steps.inductivehardening.cycletimestep

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.entities.CycleTimeStep
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate

@EntityType(Entities.CYCLETIME_STEP)
class LoadingUnloadingCycleTimeStep(name: String): ManufacturingEntity(name) {

    override val extends = CycleTimeStep(name)

    val time: CycleTime = CycleTime(5.toBigDecimal(), CycleTimeUnit.SECOND)

    val adjustmentRate: Rate = Rate(1.0.toBigDecimal())
}
