package com.nu.bom.core.technologies.masterdata

import com.nu.bom.core.api.dtos.AutocompleteContainer
import com.nu.bom.core.api.dtos.AutocompleteResponse
import com.nu.bom.core.manufacturing.fieldTypes.CardboardBoxType
import com.nu.bom.core.service.masterdata.MdDetailCrudService
import com.nu.bom.core.service.masterdata.MdHeaderTypes
import com.nu.bom.core.technologies.manufacturings.pbox.PrintedCardboardBoxUtils
import com.nu.bom.core.user.AccessCheckProvider
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Mono

@RestController
@RequestMapping("/api/masterdataSelectable")
class MasterDataSelectableController(
    private val accessCheckProvider: AccessCheckProvider,
    private val mdDetailCrudService: MdDetailCrudService,
) {
    @GetMapping("/cardboardBox")
    fun getCardboardBoxes(
        @AuthenticationPrincipal jwt: Jwt?,
        @RequestParam boxSubtype: String,
    ): Mono<AutocompleteContainer> {
        val boxSubTypeMdShortName = CardboardBoxType.valueOf(boxSubtype).mdShortName
        val boxSubTypeFullName = "tset.ref.lov-entry.$boxSubTypeMdShortName"
        return accessCheckProvider.doAs(jwt) { accessCheck ->
            mdDetailCrudService
                .postAllDetailEntries(
                    accessCheck = accessCheck,
                    headerTypeKey = SimpleKeyDto(MdHeaderTypes.MATERIAL),
                    detailQueryDto = PrintedCardboardBoxUtils.getDetailQueryFromBoxType(boxSubTypeFullName),
                ).map { detailQueryResponseDto ->
                    detailQueryResponseDto.content
                        .map { headerAndDetailDto ->
                            headerAndDetailDto.headerDto
                        }.map { headerDto ->
                            AutocompleteResponse(
                                name = headerDto.name,
                                key = (headerDto.key as SimpleKeyDto).key,
                                null,
                            )
                        }
                }.map {
                    AutocompleteContainer(null, it)
                }
        }
    }
}
