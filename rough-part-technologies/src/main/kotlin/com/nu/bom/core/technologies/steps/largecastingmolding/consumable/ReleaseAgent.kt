package com.nu.bom.core.technologies.steps.largecastingmolding.consumable

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.entities.Consumable
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit

@EntityType(Entities.CONSUMABLE)
class ReleaseAgent(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = Consumable(name)

    @Input
    val reuseOfScrap: Bool = Bool(false)

    fun quantity() = QuantityUnit(0.1)
}
