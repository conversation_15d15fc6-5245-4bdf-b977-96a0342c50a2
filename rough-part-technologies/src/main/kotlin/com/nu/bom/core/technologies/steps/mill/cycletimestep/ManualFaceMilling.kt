package com.nu.bom.core.technologies.steps.mill.cycletimestep

import com.nu.bom.core.exception.userException.LimitType
import com.nu.bom.core.exception.userException.NumericInputExceedsLimitException
import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntitySubtypes
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Hidden
import com.nu.bom.core.manufacturing.annotations.IgnoreMasterData
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.Nocalc
import com.nu.bom.core.manufacturing.annotations.OnChangeClear
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.SourceDataInput
import com.nu.bom.core.manufacturing.defaults.NullProvider
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.PositiveQuantity
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Speed
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.prod
import com.nu.bom.core.manufacturing.fieldTypes.quot
import com.nu.bom.core.technologies.steps.turn.cycletimestep.timeToMoveTool
import java.math.BigDecimal
import java.math.BigInteger

@EntityType(Entities.CYCLETIME_STEP, userCreatable = true)
@EntitySubtypes(["MILLING"])
@com.nu.bom.core.manufacturing.annotations.MasterDataType(MasterDataType.TOOL_FACE_MILLING)
class ManualFaceMilling(name: String) : ManufacturingEntity(name) {
    override val extends = ManualMilling(name)

    @Input
    @MandatoryForEntity
    @IgnoreMasterData
    fun displayDesignation(): Text = Text("Face Milling")

    @Hidden
    @MandatoryForEntity(refresh = true)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @OnChangeClear(["selectedTool"])
    fun calculatedToolDiameter(
        @Default(NullProvider::class)
        width: Length?,
    ): Length? = width?.let { it * 1.2 }

    @Input
    @MandatoryForEntity(index = 4, refresh = true)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun length(): Length? = null

    @Input
    @MandatoryForEntity(index = 5, refresh = true)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @OnChangeClear(["calculatedToolDiameter"], keepUserInput = true)
    fun width(): Length? = null

    @Input
    @MandatoryForEntity(index = 6, refresh = true)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun depth(): Length? = null

    @Input
    @MandatoryForEntity(section = "right", index = 4, refresh = true)
    fun cuttingWidthFactor(): Rate = Rate(0.8.toBigDecimal())

    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun maxCuttingWidth(
        toolDiameter: Length,
        cuttingWidthFactor: Rate,
    ): Length = toolDiameter * cuttingWidthFactor.res

    @ReadOnly
    fun numberOfDepthLayers(
        depth: Length,
        maxCuttingDepth: Length,
    ): PositiveQuantity {
        if (depth.res <= BigDecimal.ZERO) {
            throw NumericInputExceedsLimitException("depth", LimitType.LESS_EQ, BigDecimal.ZERO)
        }
        if (maxCuttingDepth.res <= BigDecimal.ZERO) {
            throw NumericInputExceedsLimitException("maxCuttingDepth", LimitType.LESS_EQ, BigDecimal.ZERO)
        }
        return if (depth.res <= maxCuttingDepth.res) {
            PositiveQuantity(BigInteger.ONE)
        } else {
            PositiveQuantity(quot(depth, maxCuttingDepth).ceilToBigInteger)
        }
    }

    @ReadOnly
    fun numberOfWidthLayers(
        width: Length,
        toolDiameter: Length,
        maxCuttingWidth: Length,
    ): PositiveQuantity {
        if (width.res <= BigDecimal.ZERO) {
            throw NumericInputExceedsLimitException("width", LimitType.LESS_EQ, BigDecimal.ZERO)
        }
        if (maxCuttingWidth.res <= BigDecimal.ZERO) {
            throw NumericInputExceedsLimitException("maxCuttingWidth", LimitType.LESS_EQ, BigDecimal.ZERO)
        }
        if (toolDiameter.res < maxCuttingWidth.res) {
            throw NumericInputExceedsLimitException("toolDiameter", LimitType.LESS, "maxCuttingWidth")
        }
        return if (width.res <= maxCuttingWidth.res) {
            PositiveQuantity(BigInteger.ONE)
        } else {
            PositiveQuantity(quot(width, maxCuttingWidth).ceilToBigInteger)
        }
    }

    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun realCuttingDepth(
        depth: Length,
        numberOfDepthLayers: PositiveQuantity,
    ): Length = quot(depth, numberOfDepthLayers)

    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun realCuttingWidth(
        width: Length,
        numberOfWidthLayers: PositiveQuantity,
    ): Length = quot(width, numberOfWidthLayers)

    @ReadOnly
    fun toolRadius(toolDiameter: Length): Length = toolDiameter * 0.5

    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun millingLength(
        length: Length,
        toolRadius: Length,
        realCuttingWidth: Length,
        realCuttingDepth: Length,
        numberOfWidthLayers: PositiveQuantity,
        numberOfDepthLayers: PositiveQuantity,
        safety: Length,
    ): Length {
        return if (numberOfWidthLayers.res == BigInteger.ONE && numberOfDepthLayers.res == BigInteger.ONE) {
            cuttingLengthSingleWidthLayer(length, toolRadius, safety)
        } else if (numberOfDepthLayers.res == BigInteger.ONE) {
            cuttingLengthSingleDepthLayer(length, toolRadius, realCuttingWidth, numberOfWidthLayers, safety)
        } else {
            cuttingLengthMultipleDepthLayers(
                cuttingLengthSingleDepthLayer(length, toolRadius, realCuttingWidth, numberOfWidthLayers, safety),
                toolRadius,
                realCuttingDepth,
                numberOfDepthLayers,
                safety,
            )
        }
    }

    @DefaultUnit(DefaultUnit.SECOND)
    fun primaryTimePerOperation(
        millingLength: Length,
        feedRate: Speed,
    ): Time {
        return quot(millingLength, feedRate)
    }

    @DefaultUnit(DefaultUnit.SECOND)
    fun secondaryTimePerOperation(
        toolRadius: Length,
        realCuttingDepth: Length,
        numberOfDepthLayers: PositiveQuantity,
        cuttingSpeed: Speed,
        safety: Length,
    ): Time {
        val timeToMoveToolOutOfLayer = timeToMoveTool(toolRadius + safety, cuttingSpeed)
        val timeToMoveToolBetweenLayers = timeToMoveTool(realCuttingDepth, cuttingSpeed)
        return prod(
            timeToMoveToolOutOfLayer + timeToMoveToolBetweenLayers,
            maxOf(numberOfDepthLayers.res.toDouble() - 1.0, 0.0),
        )
    }

    @Nocalc
    private fun cuttingLengthSingleWidthLayer(
        length: Length,
        toolRadius: Length,
        safety: Length,
    ): Length {
        return (toolRadius + safety) * 2.0 + length
    }

    @Nocalc
    private fun cuttingLengthSingleDepthLayer(
        length: Length,
        toolRadius: Length,
        layerWidth: Length,
        numberOfWidthLayers: PositiveQuantity,
        safety: Length,
    ): Length {
        return prod(toolRadius + safety * 2.0 + length, numberOfWidthLayers) +
            prod(layerWidth, maxOf(numberOfWidthLayers.res.toDouble() - 1.0, 0.0))
    }

    @Nocalc
    private fun cuttingLengthMultipleDepthLayers(
        cuttingLengthSingleDepthLayer: Length,
        toolRadius: Length,
        realCuttingDepth: Length,
        numberOfDepthLayers: PositiveQuantity,
        safety: Length,
    ): Length {
        return prod(toolRadius + safety * 2.0 + cuttingLengthSingleDepthLayer, numberOfDepthLayers) +
            prod(realCuttingDepth, maxOf(numberOfDepthLayers.res.toDouble() - 1.0, 0.0))
    }

    @Input
    @MandatoryForEntity(section = "right", index = 5, showSystemValue = true, refresh = true)
    @DefaultUnit(DefaultUnit.M_PER_MIN)
    @SourceDataInput("maxCuttingSpeed$CUTTING_PARAMETER")
    fun cuttingSpeed(): Speed? = null

    @Input
    @MandatoryForEntity(section = "right", index = 1, showSystemValue = true, refresh = true)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @SourceDataInput("maxFeedRatePerRevolution$CUTTING_PARAMETER")
    fun feedRatePerTooth(): Length? = null

    companion object {
        const val CUTTING_PARAMETER = "Optimal"
    }
}
