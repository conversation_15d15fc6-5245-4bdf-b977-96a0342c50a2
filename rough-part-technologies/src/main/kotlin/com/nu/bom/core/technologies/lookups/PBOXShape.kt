package com.nu.bom.core.technologies.lookups

import com.nu.bom.core.manufacturing.fieldTypes.CardboardBoxType

data class PBOXShape(
    override val shapeId: String,
    override val shapeTechnologyGroup: String,
    override val tech: String,
    override val inputGroup: String,
    val boxSubtype: CardboardBoxType,
) : LookupShape

val pboxShapeReader: (row: List<String>) -> PBOXShape = { row ->
    PBOXShape(
        shapeId = row[0],
        shapeTechnologyGroup = row[1],
        tech = row[2],
        inputGroup = row[3],
        boxSubtype = CardboardBoxType.valueOf(row[4]),
    )
}
