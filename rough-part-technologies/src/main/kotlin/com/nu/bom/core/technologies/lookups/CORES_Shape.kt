package com.nu.bom.core.technologies.lookups

import com.nu.bom.core.manufacturing.fieldTypes.ToolComplexityCoreShooting

data class CORES_Shape(
    override val shapeId: String,
    override val shapeTechnologyGroup: String,
    override val tech: String,
    override val inputGroup: String,
    val displayShape: String,
    val toolComplexity: ToolComplexityCoreShooting,
    val ignoreSlider: <PERSON>olean,
) : LookupShape

val coresShapeReader: (row: List<String>) -> CORES_Shape = { row ->
    CORES_Shape(
        shapeId = row[0],
        shapeTechnologyGroup = row[1],
        tech = row[2],
        inputGroup = row[3],
        displayShape = row[4],
        toolComplexity = ToolComplexityCoreShooting.valueOf(row[5]),
        ignoreSlider = row[6].toBoolean(),
    )
}
