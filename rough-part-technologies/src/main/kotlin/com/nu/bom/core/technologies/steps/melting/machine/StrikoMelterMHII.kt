package com.nu.bom.core.technologies.steps.melting.machine

import com.nu.bom.core.manufacturing.annotations.*
import com.nu.bom.core.manufacturing.entities.Machine
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.*

@EntityType(Entities.MACHINE)
class StrikoMelterMHII(name: String) : ManufacturingEntity(name) {

    override val extends = Machine(name)

   /* @Input
    @SpecialLink("SystemParameterMelting", "gasConsumptionMelting")
    val gasConsumptionMelting: Num? = null
    @Input
    @SpecialLink("SystemParameterMelting", "gasConsumptionKeepWarm")
    val gasConsumptionKeepWarm: Num? = null

    @Sibling(Entities.LOCATION)
    val gasPrice: Money = Money(0.45)

    @Sibling(Entities.LOCATION)
    val naturalGasEmission: CO2? = null

    fun consumableRate(
            gasConsumptionMelting: Num,
            gasConsumptionKeepWarm: Num,
            @Parent(Entities.MANUFACTURING_STEP)
            manufacturingTimePerPart: Time,
    ): Rate {
        return Rate(((gasConsumptionKeepWarm + gasConsumptionMelting) * manufacturingTimePerPart * (gasPrice.res * (naturalGasEmission?.inGramm?: 0.toBigDecimal()))).res)
    }

    val machineType: Text = Text("Gas furnace")
*/
}
