package com.nu.bom.core.technologies.steps.smdlinebottom.cycletimestep

import com.nu.bom.core.manufacturing.annotations.*
import com.nu.bom.core.manufacturing.entities.CycleTimeStep
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.*
import java.math.BigDecimal

@EntityType(Entities.CYCLETIME_STEP)
class SmdBottomPickAndPlaceCycleTimeStep(name: String) : ManufacturingEntity(name) {

    override val extends = CycleTimeStep(name)

    @Input
    val adjustmentRate = Rate(BigDecimal.ONE)

    val fractionOfMaximumProductionCapacity = Rate(0.80.toBigDecimal())

    @Input
    @ReadOnly
    @SpecialLink("SystemParameterSmdLine2", "maximumProductionCapacity")
    fun maximumProductionCapacity(): Pieces? = null

    @Input
    @ReadOnly
    @Parent(Entities.MANUFACTURING)
    val pcbsPerPanel: Pieces? = null

    @ReadOnly
    fun actualProductionCapacity(
        maximumProductionCapacity: Pieces,
        fractionOfMaximumProductionCapacity: Rate
    ): Pieces {
        return Pieces(maximumProductionCapacity.res * fractionOfMaximumProductionCapacity.res)
    }

    fun time(
        @Parent(Entities.MANUFACTURING_STEP)
        partsPerCycle: QuantityUnit,
        @Parent(Entities.MANUFACTURING)
        smdPartsBottomSide: Pieces,
        actualProductionCapacity: Pieces
    ): Time {
        return Time(3600.toBigDecimal() * (smdPartsBottomSide / actualProductionCapacity).res * partsPerCycle.res, TimeUnits.SECOND)
    }
}
