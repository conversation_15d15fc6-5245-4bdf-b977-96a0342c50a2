package com.nu.bom.core.technologies.steps.plasticcoating.consumables

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.Consumable
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Weight

@EntityType(Entities.CONSUMABLE)
class PaintingClearCoat(name: String) : ManufacturingEntity(name) {
    override val extends = Consumable(name)

    @Parent(Entities.MANUFACTURING_STEP)
    fun deployedWeightClear(): Weight? = null

    @Parent(Entities.MANUFACTURING_STEP)
    fun mixMaterialUnitCostClear(): Money? = null

    fun quantity(deployedWeightClear: Weight) = QuantityUnit(deployedWeightClear)

    fun pricePerUnit(mixMaterialUnitCostClear: Money) = mixMaterialUnitCostClear
}
