package com.nu.bom.core.technologies.steps.plasticcoating.systemparameter

import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.SystemParameter
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.Length

@EntityType(Entities.SYSTEM_PARAMETER)
class SystemParameterPlasticPainting(name: String) : ManufacturingEntity(name) {

    override val extends = SystemParameter(name)

    @Input
    @ReadOnly
    @ObjectView(ObjectView.SYSTEM, 1)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun skidLength(): Length? = null

    @Input
    @ReadOnly
    @ObjectView(ObjectView.SYSTEM, 2)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun skidWidth(): Length? = null

    @Input
    @ReadOnly
    @ObjectView(ObjectView.SYSTEM, 3)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun skidHeight(): Length? = null

    @Input
    @ReadOnly
    @ObjectView(ObjectView.SYSTEM, 4)
    @DefaultUnit(DefaultUnit.SECOND)
    fun fixedCycleTime(): CycleTime? = null
}
