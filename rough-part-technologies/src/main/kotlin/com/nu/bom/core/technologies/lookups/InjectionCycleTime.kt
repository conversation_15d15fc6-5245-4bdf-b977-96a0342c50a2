package com.nu.bom.core.technologies.lookups

import com.nu.bom.core.manufacturing.fieldTypes.Contour
import com.nu.bom.core.manufacturing.fieldTypes.PartingLine
import com.nu.bom.core.manufacturing.fieldTypes.RiskOfInternalStress

data class InjectionCycleTime(
    val shapeId: String,
    val contour: Contour,
    val riskOfInternalStress: RiskOfInternalStress,
    val partingLine: PartingLine,
)

val injectionCycleTimeReader: (row: List<String>) -> InjectionCycleTime = { row ->
    InjectionCycleTime(
        shapeId = row[0],
        contour = Contour.valueOf(row[1]),
        riskOfInternalStress = RiskOfInternalStress.valueOf(row[2]),
        partingLine = PartingLine.valueOf(row[3]),
    )
}
