package com.nu.bom.core.technologies.manufacturings.corestandalone.consumable

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.Consumable
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate

@EntityType(Entities.CONSUMABLE)
class CoreStandAloneConsumable(name: String) : ManufacturingEntity(name) {

    override val extends = Consumable(name)

    @Input
    fun reuseOfScrap(): Bool = Bool(false)

    fun quantity(
        @Parent(Entities.PROCESSED_MATERIAL)
        netWeightPerPart: QuantityUnit,
        ratio: Rate
    ): QuantityUnit {
        return QuantityUnit((ratio * netWeightPerPart).res)
    }
}
