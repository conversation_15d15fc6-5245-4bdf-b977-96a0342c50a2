package com.nu.bom.core.technologies.lookups

import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Pieces

class SandCastingTemplateLookupEntry(
    val templateName: String,
    val hasCore: Boolean,
    val sandCastingType: String,
    val toolModel: String,
    val frameLength: Length,
    val frameWidth: Length,
    val frameBottomHeight: Length,
    val frameTopHeight: Length,
    val lowCostCountry: Boolean,
    val numberOfCoolingStations: Pieces,
)

val sandCastingTemplatesReader: (row: List<String>) -> SandCastingTemplateLookupEntry = { row ->
    SandCastingTemplateLookupEntry(
        templateName = row[0],
        hasCore = row[1].toBoolean(),
        sandCastingType = row[2],
        toolModel = row[3],
        frameLength = Length(row[4].toBigDecimal(), LengthUnits.METER),
        frameWidth = Length(row[5].toBigDecimal(), LengthUnits.METER),
        frameBottomHeight = Length(row[6].toBigDecimal(), LengthUnits.METER),
        frameTopHeight = Length(row[7].toBigDecimal(), LengthUnits.METER),
        lowCostCountry = row[8].toBoolean(),
        numberOfCoolingStations = Pieces(row[9]),
    )
}
