package com.nu.bom.core.technologies.manufacturings.pbox.material

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.MissingInputError
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.MaterialViewConfig
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.SurfaceDensity

@EntityType(Entities.MATERIAL)
@Suppress("unused")
class MaterialPboxInkMagenta(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = MaterialPboxInk(name)

    @Input
    @ReadOnly
    @Parent(Entities.MANUFACTURING)
    fun magentaShare(): Rate = throw MissingInputError()

    @Input
    @ReadOnly
    @Parent(Entities.MANUFACTURING)
    fun magentaPrintingWeight(): SurfaceDensity = throw MissingInputError()

    fun printingShare(magentaShare: Rate) = magentaShare

    fun printingWeight(magentaPrintingWeight: SurfaceDensity) = magentaPrintingWeight

    fun materialView(): MaterialViewConfig = MaterialViewConfig.MATERIAL_PBOX_INK_MAGENTA
}
