package com.nu.bom.core.technologies.steps.insertinj.cycletimestep

import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.entities.CycleTimeStepGroup
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeGrouping

@EntityType(Entities.CYCLETIME_STEP_GROUP)
class TwoStationsInsertUnloadInsertInjectionCycleTimeStepGroup(name: String) : ManufacturingEntity(name) {
    override val extends: ManufacturingEntity = CycleTimeStepGroup(name)

    fun grouping() = CycleTimeGrouping.Sequential

    @EntityCreation(Entities.CYCLETIME_STEP)
    fun createCycleTimeSteps(): List<ManufacturingEntity> {
        return listOfNotNull(
            createEntity(
                name = "Unload part",
                clazz = UnloadPartInsertInjectionCycleTimeStep::class,
                entityType = Entities.CYCLETIME_STEP
            ),
            createEntity(
                name = "Load insert",
                clazz = LoadInsertInsertInjectionCycleTimeStep::class,
                entityType = Entities.CYCLETIME_STEP
            ),
            createEntity(
                name = "Table rotation",
                clazz = TableRotationInsertInjectionCycleTimeStep::class,
                entityType = Entities.CYCLETIME_STEP
            )
        )
    }
}
