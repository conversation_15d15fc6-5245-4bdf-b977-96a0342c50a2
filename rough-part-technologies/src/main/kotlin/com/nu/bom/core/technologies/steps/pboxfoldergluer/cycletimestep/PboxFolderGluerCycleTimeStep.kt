package com.nu.bom.core.technologies.steps.pboxfoldergluer.cycletimestep

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.CycleTimeStep
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Speed

@EntityType(Entities.CYCLETIME_STEP)
@Suppress("unused")
class PboxFolderGluerCycleTimeStep(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = CycleTimeStep(name)

    fun time(
        @Parent(Entities.MANUFACTURING_STEP) processingSpeed: Speed,
        @Parent(Entities.MANUFACTURING_STEP) unfoldedBoxLength: Length,
        @Parent(Entities.MANUFACTURING_STEP) unfoldedBoxWidthW2: Length,
    ) = CycleTime.create(min(unfoldedBoxLength, unfoldedBoxWidthW2), processingSpeed)
}
