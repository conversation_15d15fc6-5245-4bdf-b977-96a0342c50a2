package com.nu.bom.core.technologies.lookups

import java.math.BigDecimal

data class FettlingMaintenancePerYear(
    val toolDesignation: String,
    val maintenancePerYear: BigDecimal,
)

val fettlingMaintenancePerYearReader: (row: List<String>) -> FettlingMaintenancePerYear = { row ->
    FettlingMaintenancePerYear(
        toolDesignation = row[0],
        maintenancePerYear = row[1].toBigDecimal(),
    )
}
