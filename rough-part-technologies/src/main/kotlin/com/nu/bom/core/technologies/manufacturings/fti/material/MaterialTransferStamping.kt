package com.nu.bom.core.technologies.manufacturings.fti.material

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.ExpectedParents
import com.nu.bom.core.manufacturing.annotations.Modularized
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.MaterialViewConfig

@EntityType(Entities.MATERIAL)
@Modularized(
    technologies = [Model.FTITDS],
    parents = [
        ExpectedParents(model = Model.FTITDS, type = Entities.MANUFACTURING),
        ExpectedParents(model = Model.FTITDS, type = Entities.PROCESSED_MATERIAL),
    ],
    dimensions = [Dimension.Selection.NUMBER],
    userCreatable = true,
)
@Suppress("unused")
class MaterialTransferStamping(
    name: String,
) : FtiMaterial(name) {
    fun materialView(): MaterialViewConfig = MaterialViewConfig.MATERIAL_TRANSFER_STAMPING
}
