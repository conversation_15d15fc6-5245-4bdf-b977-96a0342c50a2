package com.nu.bom.core.technologies.steps.galvanizing.systemparameter

import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.MissingInputError
import com.nu.bom.core.manufacturing.entities.SystemParameter
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.ElectricCurrent
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Num

@EntityType(Entities.SYSTEM_PARAMETER)
class SystemParameterGalvanizing(name: String) : ManufacturingEntity(name) {

    override val extends = SystemParameter(name)

    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.SYSTEM, 10)
    fun poolLength(): Length = throw MissingInputError()

    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.SYSTEM, 20)
    fun poolWidth(): Length = throw MissingInputError()

    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.SYSTEM, 30)
    fun poolHeight(): Length = throw MissingInputError()

    @Input
    @ObjectView(ObjectView.SYSTEM, 40)
    fun numberOfOperations(): Num = throw MissingInputError()

    @Input
    @ReadOnly
    @ObjectView(ObjectView.SYSTEM, 50)
    fun maxCurrent(): ElectricCurrent = throw MissingInputError()
}
