package com.nu.bom.core.technologies.steps.welding

import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.Selectable
import com.nu.bom.core.manufacturing.annotations.SubLabel
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.SingleGroupManufacturingStep
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.ManufacturingStepType
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.technologies.steps.welding.cycletimestep.WeldingCycleTimeStepGroup
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

@EntityType(Entities.MANUFACTURING_STEP)
class ManufacturingStepWelding(name: String) : ManufacturingEntity(name) {
    override val extends: ManufacturingEntity = SingleGroupManufacturingStep(name)

    @Input
    @SubLabel
    @ReadOnly
    @Selectable(value = "manufacturingStepType", getDisplayNameFromPathQuery = true)
    fun manufacturingStepType(): Text = Text(ManufacturingStepType.ASSEMBLY_WELDING.name)

    @Input
    fun internalPartsPerCycle(): QuantityUnit = QuantityUnit(1.toBigDecimal())

    @Input
    fun utilizationRate(): Rate = Rate(0.85.toBigDecimal())

    fun scrapRate(): Rate = Rate(0.0005.toBigDecimal())

    private class TemplateLookupEntry(val templateName: String)

    private val machineTemplateLookupReader: (row: List<String>) -> TemplateLookupEntry = {
        TemplateLookupEntry(
            templateName = it[0],
        )
    }

    fun templateName(): Mono<Text> {
        return services.getLookupTable("ManufacturingStepWelding_Templates", machineTemplateLookupReader)
            .single().map { Text(it.templateName) }
    }

    @EntityCreation(Entities.CYCLETIME_STEP_GROUP)
    @Suppress("unused")
    fun createCycleTimeStepGroup() =
        createEntity(
            name = "Welding cycle time step group",
            clazz = WeldingCycleTimeStepGroup::class,
            entityType = Entities.CYCLETIME_STEP_GROUP,
        )

    @EntityCreation(Entities.MACHINE)
    fun createMachines(templateName: Text): Flux<ManufacturingEntity> {
        return createEntitiesFromTemplate(
            name = templateName.res + "_Machine",
            location = "Global",
        )
    }

    @EntityCreation(Entities.LABOR)
    fun createLabor(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> {
        return createEntitiesFromTemplate(
            name = templateName.res + "_Labor",
            location = locationName.res,
        )
    }

    @EntityCreation(Entities.SETUP)
    fun createSetup(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> {
        return createEntitiesFromTemplate(
            name = templateName.res + "_Setup",
            location = locationName.res,
        )
    }

    @EntityCreation(Entities.TOOL)
    fun createTools(templateName: Text): Flux<ManufacturingEntity> {
        return createEntitiesFromTemplate(
            name = templateName.res + "_Tools",
            location = "Global",
        )
    }
}
