package com.nu.bom.core.technologies.steps.turn.cycletimestep

import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Quantity
import com.nu.bom.core.manufacturing.fieldTypes.Speed
import com.nu.bom.core.manufacturing.fieldTypes.prod

@EntityType(Entities.CYCLETIME_STEP)
class ManualHorizontalTurningLayer(name: String) : ManufacturingEntity(name) {

    override val extends = ManualTurningLayer(name)

    //Warning: Plunge turnings will overwrite this!
    @ReadOnly
    fun layerLength(
        horizontalLength: Length
    ) = horizontalLength

    @ReadOnly
    fun lengthOrthogonalToLayers(
        verticalLength: Length
    ) = verticalLength

    @EntityCreation(Entities.CYCLETIME_STEP)
    fun createLayers(
        minDiameter: Length,
        numberOfLayers: Quantity,
        layerWidth: Length,
        feedRatePerRevolution: Length,
        cuttingSpeed: Speed,
        layerLength: Length,
    ): List<ManufacturingEntity> {
        val result = mutableListOf<ManufacturingEntity>();

        val fields = mutableMapOf<String, FieldResultStar>(
            "feedRatePerRevolution" to feedRatePerRevolution,
            "cuttingSpeedAtMidpoint" to cuttingSpeed,
            "length" to layerLength
        )

        for (layerNumber in 0 until numberOfLayers.res.toInt()) {
            fields["diameterAtMidpoint"] = minDiameter + prod(2 * (layerNumber + .5), layerWidth)
            result.add(createEntity(
                name = "Layer ${layerNumber + 1}",
                entityType = Entities.CYCLETIME_STEP,
                clazz = LayerTurning::class,
                fields = fields.mapValues { it.value.copy(source = FieldResult.SOURCE.I) }
            ))
        }
        return result
    }
}
