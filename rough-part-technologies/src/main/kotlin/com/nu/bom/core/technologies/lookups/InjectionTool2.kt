package com.nu.bom.core.technologies.lookups

import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import java.math.BigDecimal

data class InjectionToolInvest2(
    val shapeId: String,
    val maxWeight: QuantityUnit,
    val toolCost: Map<Int, BigDecimal?>,
    val maxPartsPerCycle: BigDecimal,
)

val injectionTool2InvestReader: (row: List<String>) -> InjectionToolInvest2 = { row ->
    InjectionToolInvest2(
        shapeId = row[0],
        maxWeight = QuantityUnit(row[2].toBigDecimal()),
        toolCost =
            mapOf(
                1 to row[3].takeIf { it.isNotEmpty() }?.toBigDecimal(),
                2 to row[4].takeIf { it.isNotEmpty() }?.toBigDecimal(),
                3 to row[5].takeIf { it.isNotEmpty() }?.toBigDecimal(),
                4 to row[6].takeIf { it.isNotEmpty() }?.toBigDecimal(),
                5 to row[7].takeIf { it.isNotEmpty() }?.toBigDecimal(),
                6 to row[8].takeIf { it.isNotEmpty() }?.toBigDecimal(),
                7 to row[9].takeIf { it.isNotEmpty() }?.toBigDecimal(),
                8 to row[10].takeIf { it.isNotEmpty() }?.toBigDecimal(),
                10 to row[11].takeIf { it.isNotEmpty() }?.toBigDecimal(),
                12 to row[12].takeIf { it.isNotEmpty() }?.toBigDecimal(),
                14 to row[13].takeIf { it.isNotEmpty() }?.toBigDecimal(),
                16 to row[14].takeIf { it.isNotEmpty() }?.toBigDecimal(),
                24 to row[15].takeIf { it.isNotEmpty() }?.toBigDecimal(),
                32 to row[16].takeIf { it.isNotEmpty() }?.toBigDecimal(),
                64 to row[17].takeIf { it.isNotEmpty() }?.toBigDecimal(),
                96 to row[18].takeIf { it.isNotEmpty() }?.toBigDecimal(),
                128 to row[19].takeIf { it.isNotEmpty() }?.toBigDecimal(),
                192 to row[20].takeIf { it.isNotEmpty() }?.toBigDecimal(),
                256 to row[21].takeIf { it.isNotEmpty() }?.toBigDecimal(),
            ),
        maxPartsPerCycle = row[22].toBigDecimal(),
    )
}
