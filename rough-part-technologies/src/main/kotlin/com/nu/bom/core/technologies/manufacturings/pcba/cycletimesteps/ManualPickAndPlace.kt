package com.nu.bom.core.technologies.manufacturings.pcba.cycletimesteps

import com.nu.bom.core.manufacturing.annotations.Condition
import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntitySubtypes
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.StaticDenominatorUnit
import com.nu.bom.core.manufacturing.defaults.NoTime
import com.nu.bom.core.manufacturing.entities.CycleTimeStep
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.StaticUnitOverride
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.pcba.PcbaStationSetup
import java.math.BigDecimal

@EntityType(Entities.CYCLETIME_STEP, userCreatable = true)
@EntitySubtypes(["OTHERS"])
class ManualPickAndPlace(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = CycleTimeStep(name)

    companion object {
        private fun allocateNonExclusiveComponents(
            remainingSmdsForTargetStation: Pieces,
            remainingSmdsForOtherStation: Pieces,
            nonExclusiveComponents: Pieces,
        ): Pieces {
            val proportionalCapacity =
                remainingSmdsForTargetStation / (remainingSmdsForTargetStation + remainingSmdsForOtherStation)
            return nonExclusiveComponents * proportionalCapacity
        }
    }

    //region Basic Parameters
    @Input
    fun designation() = Text(this::class.java.simpleName)

    @Input
    @MandatoryForEntity(index = 10, refresh = true)
    fun stationSetup(): PcbaStationSetup = PcbaStationSetup.SINGLE

    fun numberOfPanels() = Pieces.ONE

    @Input
    @MandatoryForEntity(index = 20, refresh = true)
    fun numberOfPcbsPerPanel(): Pieces? = null

    fun numberOfPcbs(
        numberOfPanels: Pieces,
        numberOfPcbsPerPanel: Pieces,
    ): Pieces = numberOfPanels * numberOfPcbsPerPanel

    @Input
    @MandatoryForEntity(index = 30, refresh = true)
    fun smdsPerPcb(): Pieces? = null

    @Input
    @MandatoryForEntity(index = 40, refresh = true)
    @Condition(field = "stationSetup", value = "DUAL", operator = Condition.EQUALS)
    fun exclusiveSmdsStation1(): Pieces = Pieces.ZERO

    @Input
    @Condition(field = "stationSetup", value = "DUAL", operator = Condition.EQUALS)
    @MandatoryForEntity(index = 50, refresh = true)
    fun exclusiveSmdsStation2(): Pieces = Pieces.ZERO

    @ReadOnly
    fun totalSmdsProcessed(
        numberOfPcbs: Pieces,
        smdsPerPcb: Pieces,
    ): Pieces = numberOfPcbs * smdsPerPcb
    // endregion

    //region Station parameters
    @Input
    @MandatoryForEntity(index = 70, refresh = true)
    @StaticDenominatorUnit(StaticUnitOverride.HOUR)
    fun maxProductionCapacityStation1(): Pieces = Pieces.ZERO

    @Input
    @MandatoryForEntity(index = 75, refresh = true)
    fun utilizationFactorStation1(): Rate = Rate(0.75)

    @ReadOnly
    @StaticDenominatorUnit(StaticUnitOverride.HOUR)
    fun usedProductionCapacityStation1(
        maxProductionCapacityStation1: Pieces,
        utilizationFactorStation1: Rate,
    ): Pieces = maxProductionCapacityStation1 * utilizationFactorStation1

    @Input
    @Condition(field = "stationSetup", value = "DUAL", operator = Condition.EQUALS)
    @MandatoryForEntity(index = 76, refresh = true)
    @StaticDenominatorUnit(StaticUnitOverride.HOUR)
    fun maxProductionCapacityStation2(): Pieces = Pieces.ZERO

    @Input
    @Condition(field = "stationSetup", value = "DUAL", operator = Condition.EQUALS)
    @MandatoryForEntity(index = 77, refresh = true)
    fun utilizationFactorStation2(): Rate = Rate(0.75)

    @ReadOnly
    @Condition(field = "stationSetup", value = "DUAL", operator = Condition.EQUALS)
    @StaticDenominatorUnit(StaticUnitOverride.HOUR)
    fun usedProductionCapacityStation2(
        maxProductionCapacityStation2: Pieces,
        utilizationFactorStation2: Rate,
    ): Pieces = maxProductionCapacityStation2 * utilizationFactorStation2
    // endregion

    //region Station 1
    @ReadOnly
    @Condition(field = "stationSetup", value = "DUAL", operator = Condition.EQUALS)
    fun exclusiveSmdsProcessedStation1(
        numberOfPcbs: Pieces,
        exclusiveSmdsStation1: Pieces,
    ): Pieces = numberOfPcbs * exclusiveSmdsStation1

    @ReadOnly
    @Condition(field = "stationSetup", value = "DUAL", operator = Condition.EQUALS)
    fun otherSmdsProcessedStation1(
        stationSetup: PcbaStationSetup,
        totalSmdsProcessed: Pieces,
        exclusiveSmdsStation1: Pieces,
        allocatedNonExclusiveComponentsStation1: Pieces,
    ): Pieces =
        when (stationSetup.res) {
            PcbaStationSetup.Selection.SINGLE -> totalSmdsProcessed - exclusiveSmdsStation1
            PcbaStationSetup.Selection.DUAL -> allocatedNonExclusiveComponentsStation1.atLeast(BigDecimal.ZERO)
        }

    @ReadOnly
    fun totalSmdsProcessedStation1(
        exclusiveSmdsProcessedStation1: Pieces?,
        otherSmdsProcessedStation1: Pieces?,
        stationSetup: PcbaStationSetup,
        totalSmdsProcessed: Pieces,
    ): Pieces =
        when (stationSetup.res) {
            PcbaStationSetup.Selection.SINGLE -> totalSmdsProcessed
            PcbaStationSetup.Selection.DUAL -> exclusiveSmdsProcessedStation1!! + otherSmdsProcessedStation1!!
        }

    @ReadOnly
    @Condition(field = "stationSetup", value = "DUAL", operator = Condition.EQUALS)
    @DefaultUnit(DefaultUnit.SECOND)
    fun primaryTimeStation1(
        totalSmdsProcessedStation1: Pieces,
        usedProductionCapacityStation1: QuantityUnit,
    ): Time = Time.create(totalSmdsProcessedStation1, usedProductionCapacityStation1)
    //endregion

    //region Station 2
    @ReadOnly
    @Condition(field = "stationSetup", value = "DUAL", operator = Condition.EQUALS)
    fun exclusiveSmdsProcessedStation2(
        numberOfPcbs: Pieces,
        exclusiveSmdsStation2: Pieces,
    ): Pieces = numberOfPcbs * exclusiveSmdsStation2

    @ReadOnly
    @Condition(field = "stationSetup", value = "DUAL", operator = Condition.EQUALS)
    fun otherSmdsProcessedStation2(allocatedNonExclusiveComponentsStation2: Pieces): Pieces =
        allocatedNonExclusiveComponentsStation2.atLeast(BigDecimal.ZERO)

    @ReadOnly
    @Condition(field = "stationSetup", value = "DUAL", operator = Condition.EQUALS)
    fun totalSmdsProcessedStation2(
        exclusiveSmdsProcessedStation2: Pieces,
        otherSmdsProcessedStation2: Pieces,
    ): Pieces = exclusiveSmdsProcessedStation2 + otherSmdsProcessedStation2

    @ReadOnly
    @Condition(field = "stationSetup", value = "DUAL", operator = Condition.EQUALS)
    @DefaultUnit(DefaultUnit.SECOND)
    fun primaryTimeStation2(
        totalSmdsProcessedStation2: Pieces,
        usedProductionCapacityStation2: QuantityUnit,
    ): Time = Time.create(totalSmdsProcessedStation2, usedProductionCapacityStation2)
    // endregion

    //region Secondary time inputs
    @Input
    @MandatoryForEntity(index = 120, refresh = true)
    @DefaultUnit(DefaultUnit.SECOND)
    fun pcbaFeedRate(): Time = Time(2.0, TimeUnits.SECOND)

    @Input
    @MandatoryForEntity(index = 130, refresh = true)
    fun numberOfCrossMarks(): Num = Num(2)

    @Input
    @MandatoryForEntity(index = 140, refresh = true)
    @DefaultUnit(DefaultUnit.SECOND)
    fun timePerCrossMark(): Time = Time(1.5, TimeUnits.SECOND)
    // endregion

    // region Helper computations
    fun nonExclusiveComponents(
        totalSmdsProcessed: Pieces,
        numberOfPcbs: Pieces,
        exclusiveSmdsStation1: Pieces,
        exclusiveSmdsStation2: Pieces,
    ): Pieces = totalSmdsProcessed - numberOfPcbs * (exclusiveSmdsStation1 + exclusiveSmdsStation2)

    /**
     * This represents the SMDs that Station 1 (and 2) CAN STILL process after accounting the exclusive ones.
     */

    fun assignableSmdsStation1AfterExclusiveSmds(
        usedProductionCapacityStation1: Pieces,
        usedProductionCapacityStation2: Pieces,
        totalSmdsProcessed: Pieces,
        exclusiveSmdsStation1: Pieces,
        numberOfPcbs: Pieces,
    ): Pieces {
        val proportionalCapacityStation1 =
            usedProductionCapacityStation1 / (usedProductionCapacityStation1 + usedProductionCapacityStation2)
        val proportionalSmdsStation1 = proportionalCapacityStation1 * totalSmdsProcessed
        return (proportionalSmdsStation1 - exclusiveSmdsStation1 * numberOfPcbs).atLeast(BigDecimal.ZERO)
    }

    fun assignableSmdsStation2AfterExclusiveSmds(
        usedProductionCapacityStation1: Pieces,
        usedProductionCapacityStation2: Pieces,
        totalSmdsProcessed: Pieces,
        exclusiveSmdsStation2: Pieces,
        numberOfPcbs: Pieces,
    ): Pieces {
        val propCapacityBeforeExclusiveComponentsStation2 =
            usedProductionCapacityStation2 / (usedProductionCapacityStation1 + usedProductionCapacityStation2)
        val weightedTotalAssemblyStation2 = propCapacityBeforeExclusiveComponentsStation2 * totalSmdsProcessed
        return (weightedTotalAssemblyStation2 - exclusiveSmdsStation2 * numberOfPcbs).atLeast(BigDecimal.ZERO)
    }

    /**
     * Distributes non-exclusive components to Station 1 (and 2) based on its proportional capacity
     * AFTER exclusive components are taken into account
     */
    fun allocatedNonExclusiveComponentsStation1(
        assignableSmdsStation1AfterExclusiveSmds: Pieces,
        assignableSmdsStation2AfterExclusiveSmds: Pieces,
        nonExclusiveComponents: Pieces,
    ): Pieces =
        allocateNonExclusiveComponents(
            assignableSmdsStation1AfterExclusiveSmds,
            assignableSmdsStation2AfterExclusiveSmds,
            nonExclusiveComponents,
        )

    fun allocatedNonExclusiveComponentsStation2(
        assignableSmdsStation2AfterExclusiveSmds: Pieces,
        assignableSmdsStation1AfterExclusiveSmds: Pieces,
        nonExclusiveComponents: Pieces,
    ): Pieces =
        allocateNonExclusiveComponents(
            assignableSmdsStation2AfterExclusiveSmds,
            assignableSmdsStation1AfterExclusiveSmds,
            nonExclusiveComponents,
        )

    // endregion

    // region KPI's
    @ReadOnly
    @MandatoryForEntity(index = 1, computed = true, section = "kpi", readOnly = true)
    fun primaryTime(
        primaryTimeStation1: Time,
        @Default(NoTime::class)
        primaryTimeStation2: Time,
    ): Time = max(primaryTimeStation1, primaryTimeStation2)

    @ReadOnly
    @MandatoryForEntity(index = 2, computed = true, section = "kpi", readOnly = true)
    fun secondaryTime(
        pcbaFeedRate: Time,
        numberOfCrossMarks: Pieces,
        timePerCrossMark: Time,
    ): Time = pcbaFeedRate + timePerCrossMark * numberOfCrossMarks

    @ReadOnly
    @Input(active = false)
    @MandatoryForEntity(index = 3, computed = true, section = "kpi", readOnly = true)
    fun time(
        primaryTime: Time,
        secondaryTime: Time,
    ): CycleTime = CycleTime(primaryTime + secondaryTime)
    // endregion
}
