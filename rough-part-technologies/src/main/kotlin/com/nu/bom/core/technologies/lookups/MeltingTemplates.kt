package com.nu.bom.core.technologies.lookups

data class MeltingTemplates(
    val furnaceType: String,
    val templateName: String,
    val lowCostCountry: Boolean,
    val magnesium: Boolean,
)

val meltingTemplatesReader: (List<String>) -> MeltingTemplates = { row ->
    MeltingTemplates(
        templateName = row[0],
        furnaceType = row[1],
        lowCostCountry = row[2].toBoolean(),
        magnesium = row[3].toBoolean(),
    )
}
