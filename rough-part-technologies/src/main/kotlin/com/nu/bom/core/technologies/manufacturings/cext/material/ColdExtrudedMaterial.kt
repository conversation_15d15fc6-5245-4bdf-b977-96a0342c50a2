package com.nu.bom.core.technologies.manufacturings.cext.material

import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityProvider
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.OrderedEntityCreation
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.SummaryView
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ShapedMaterial
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeCleanness
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeHeatTreatmentAfterCext
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeTolerancesDfor
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.technologies.behaviours.CleaningBehaviour
import com.nu.bom.core.technologies.service.ShapeLookupReaderService
import com.nu.bom.core.technologies.steps.cleaningphosphating.ManufacturingStepCleaningPhosphating
import com.nu.bom.core.technologies.steps.cleaningsecondphosphating.ManufacturingStepCleaningSecondPhosphating
import com.nu.bom.core.technologies.steps.coarsegraintempering.ManufacturingStepCoarseGrainTempering
import com.nu.bom.core.technologies.steps.coldextrusion.ManufacturingStepColdExtrusion
import com.nu.bom.core.technologies.steps.cuttolength.ManufacturingStepCutToLength
import com.nu.bom.core.technologies.steps.hardening.ManufacturingStepHardeningTempering
import com.nu.bom.core.technologies.steps.recrystallisation.ManufacturingStepRecrystallisation
import com.nu.bom.core.technologies.steps.secondcoldextrusion.ManufacturingStepSecondColdExtrusion
import com.nu.bom.core.technologies.steps.softannealing.ManufacturingStepSoftAnnealing
import com.nu.bom.core.technologies.steps.stressrelieving.ManufacturingStepStressRelieving
import reactor.core.publisher.Mono
import java.math.BigDecimal

@EntityType(Entities.PROCESSED_MATERIAL)
class ColdExtrudedMaterial(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = ShapedMaterial(name)

    @ObjectView(ObjectView.NONE, 0)
    fun displayDesignation(
        designation: Text?,
        entityDesignation: Text,
    ): Text = designation ?: entityDesignation

    override val behaviours: List<ManufacturingEntity> =
        listOf(
            CleaningBehaviour("CleaningBehaviour"),
        )

    @Input
    val tolerance = StepSubTypeTolerancesDfor.STANDARD_DFOR

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun materialName(): Text? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun netWeightPerPart(): QuantityUnit? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    fun partOuterDiameter(): Length = Length(BigDecimal.ZERO, LengthUnits.METER)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    fun partLength(): Length = Length(BigDecimal.ZERO, LengthUnits.METER)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    fun partWidth(): Length = Length(BigDecimal.ZERO, LengthUnits.METER)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    fun partHeight(): Length = Length(BigDecimal.ZERO, LengthUnits.METER)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    fun partInnerDiameter(): Length = Length(BigDecimal.ZERO, LengthUnits.METER)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun maxWallThickness(): Length? = null

    @Input
    // @SummaryView(SummaryView.PROCESS,20,"heatTreatmentAfterCext")
    @Parent(Entities.PROCESSED_MATERIAL)
    fun heatTreatmentAfterColdExtrusion(): StepSubTypeHeatTreatmentAfterCext? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun cleanness(): StepSubTypeCleanness? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @SummaryView(SummaryView.PROCESS, 300, "cleaning")
    fun cleaningNeeded(): SelectableBoolean = SelectableBoolean.FALSE

    @Input
    val materialClass: Text = Text("MaterialForgingCext")

    @SummaryView(SummaryView.PROCESS, 30, "secondColdExtrusion")
    fun recrystallationBool(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
    ): Mono<Bool> =
        lookupReaderService.getCextShapeData(calculationContext!!.accessCheck, shapeId).map {
            Bool(it.recristallisationAndPhosphating)
        }

    fun preheatedMaterial(
        @Parent(Entities.MANUFACTURING)
        materialName: Text,
    ): Mono<Bool> =
        services
            .findPreheatedMaterial(
                accessCheck = calculationContext().accessCheck,
                materialKey = materialName.res,
            )

    @OrderedEntityCreation
    fun manufacturingSteps() =
        arrayOf(
            "cutToLength",
            "softannealing",
            "phosphating",
            "cext",
            "recrystallisation",
            "secondphosphating",
            "secondCext",
            "stressRelieving",
            "coarseGrainTempering",
            "quenchingTempering",
            "cleaning",
        )

    @EntityProvider
    fun cutToLength() =
        createEntity(
            name = "ManufacturingStepCutToLength",
            clazz = ManufacturingStepCutToLength::class,
            entityType = Entities.MANUFACTURING_STEP,
        )

    @EntityProvider
    fun softannealing(preheatedMaterial: Bool) =
        if (preheatedMaterial.isFalse()) {
            createEntity(
                name = "ManufacturingStepSoftAnnealing",
                clazz = ManufacturingStepSoftAnnealing::class,
                entityType = Entities.MANUFACTURING_STEP,
            )
        } else {
            null
        }

    @EntityProvider
    fun phosphating() =
        createEntity(
            name = "ManufacturingStepCleaningPhosphating",
            clazz = ManufacturingStepCleaningPhosphating::class,
            entityType = Entities.MANUFACTURING_STEP,
        )

    @EntityProvider
    fun cext() =
        createEntity(
            name = "ManufacturingStepColdExtrusion",
            clazz = ManufacturingStepColdExtrusion::class,
            entityType = Entities.MANUFACTURING_STEP,
        )

    @EntityProvider
    fun recrystallisation(recrystallationBool: Bool) =
        if (recrystallationBool.isTrue()) {
            createEntity(
                name = "ManufacturingStepRecrystallisation",
                clazz = ManufacturingStepRecrystallisation::class,
                entityType = Entities.MANUFACTURING_STEP,
            )
        } else {
            null
        }

    @EntityProvider
    fun secondphosphating(recrystallationBool: Bool) =
        if (recrystallationBool.isTrue()) {
            createEntity(
                name = "ManufacturingStepCleaningSecondPhosphating",
                clazz = ManufacturingStepCleaningSecondPhosphating::class,
                entityType = Entities.MANUFACTURING_STEP,
            )
        } else {
            null
        }

    @EntityProvider
    fun secondCext(recrystallationBool: Bool) =
        if (recrystallationBool.isTrue()) {
            createEntity(
                name = "ManufacturingStepSecondColdExtrusion",
                clazz = ManufacturingStepSecondColdExtrusion::class,
                entityType = Entities.MANUFACTURING_STEP,
            )
        } else {
            null
        }

    @EntityProvider
    fun stressRelieving(heatTreatmentAfterColdExtrusion: StepSubTypeHeatTreatmentAfterCext) =
        if (heatTreatmentAfterColdExtrusion == StepSubTypeHeatTreatmentAfterCext.STRESS_RELIEVING) {
            createEntity(
                name = "ManufacturingStepStressRelieving",
                clazz = ManufacturingStepStressRelieving::class,
                entityType = Entities.MANUFACTURING_STEP,
            )
        } else {
            null
        }

    @EntityProvider
    fun coarseGrainTempering(heatTreatmentAfterColdExtrusion: StepSubTypeHeatTreatmentAfterCext) =
        if (heatTreatmentAfterColdExtrusion == StepSubTypeHeatTreatmentAfterCext.COARSE_GRAIN_TEMPERING) {
            createEntity(
                name = "ManufacturingStepCoarseGrainTempering",
                clazz = ManufacturingStepCoarseGrainTempering::class,
                entityType = Entities.MANUFACTURING_STEP,
            )
        } else {
            null
        }

    @EntityProvider
    fun quenchingTempering(heatTreatmentAfterColdExtrusion: StepSubTypeHeatTreatmentAfterCext) =
        if (heatTreatmentAfterColdExtrusion == StepSubTypeHeatTreatmentAfterCext.HARDENING_TEMPERING) {
            createEntity(
                name = "ManufacturingStepHardeningTempering",
                clazz = ManufacturingStepHardeningTempering::class,
                entityType = Entities.MANUFACTURING_STEP,
            )
        } else {
            null
        }
}
