package com.nu.bom.core.technologies.lookups

import com.nu.bom.core.manufacturing.fieldTypes.Temperature
import com.nu.bom.core.manufacturing.fieldTypes.TemperatureUnits
import com.nu.bom.core.manufacturing.fieldTypes.Text

data class HeatSoakingTemperature(
    val materialName: Text,
    val hardening: Temperature,
    val warmformed: Temperature,
    val normalizing: Temperature,
    val softAnnealing: Temperature,
    val FPAnnealing: Temperature,
    val caseHardening: Temperature,
    val stressRelieving: Temperature,
    val recrystallization: Temperature,
    val tempering: Temperature,
)

val heatSoakingTemperatureReader: (row: List<String>) -> HeatSoakingTemperature = { row ->
    HeatSoakingTemperature(
        materialName = Text(row[0]),
        hardening = Temperature(row[1].toBigDecimal(), TemperatureUnits.CELSIUS),
        warmformed = Temperature(row[2].toBigDecimal(), TemperatureUnits.CELSIUS),
        normalizing = Temperature(row[3].toBigDecimal(), TemperatureUnits.CELSIUS),
        softAnnealing = Temperature(row[4].toBigDecimal(), TemperatureUnits.CELSIUS),
        FPAnnealing = Temperature(row[5].toBigDecimal(), TemperatureUnits.CELSIUS),
        caseHardening = Temperature(row[6].toBigDecimal(), TemperatureUnits.CELSIUS),
        stressRelieving = Temperature(row[7].toBigDecimal(), TemperatureUnits.CELSIUS),
        recrystallization = Temperature(row[8].toBigDecimal(), TemperatureUnits.CELSIUS),
        tempering = Temperature(row[9].toBigDecimal(), TemperatureUnits.CELSIUS),
    )
}
