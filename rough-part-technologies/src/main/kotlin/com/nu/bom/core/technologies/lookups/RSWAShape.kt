package com.nu.bom.core.technologies.lookups

import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import java.math.BigDecimal

data class RSWAShape(
    override val shapeId: String,
    override val shapeTechnologyGroup: String,
    override val tech: String,
    override val inputGroup: String,
    val displayShape: String,
    val minWeight: Weight,
    val maxWeight: Weight,
    val shapeActive: Boolean,
    val cycleTimeRotarySwaging: Time,
    val cycleTimeGearing: Time,
    val aximus: Boolean,
    val multipleMachines: Boolean,
    val stagesRotarySwaging: Num,
    val stagesGearing: Num,
    val toolCostsDieSegmentPerStage: Money,
    val toolCostsGearingPerStage: Money,
    val serviceLifePerStage: BigDecimal,
) : LookupShape

val rswaShapeReader: (row: List<String>) -> RSWAShape = { row ->
    RSWAShape(
        shapeId = row[0],
        shapeTechnologyGroup = row[1],
        tech = row[2],
        inputGroup = row[3],
        displayShape = row[4],
        minWeight = Weight(row[5].toBigDecimal(), WeightUnits.KILOGRAM),
        maxWeight = Weight(row[6].toBigDecimal(), WeightUnits.KILOGRAM),
        shapeActive = row[7].toBoolean(),
        cycleTimeRotarySwaging = Time(row[10].toBigDecimal(), TimeUnits.SECOND),
        cycleTimeGearing = Time(row[11].toBigDecimal(), TimeUnits.SECOND),
        aximus = row[12].toBoolean(),
        multipleMachines = row[13].toBoolean(),
        stagesRotarySwaging = Num(row[14]),
        stagesGearing = Num(row[15]),
        toolCostsDieSegmentPerStage = Money(row[16].toBigDecimal()),
        toolCostsGearingPerStage = Money(row[17].toBigDecimal()),
        serviceLifePerStage = row[18].toBigDecimal(),
    )
}
