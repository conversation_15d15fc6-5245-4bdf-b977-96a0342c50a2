package com.nu.bom.core.technologies.steps.cleaning.machine

import com.nu.bom.core.manufacturing.annotations.*
import com.nu.bom.core.manufacturing.entities.Machine
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.*
import java.lang.IllegalArgumentException

@EntityType(Entities.MACHINE)
class CleaningMachine(name: String) : ManufacturingEntity(name) {
    override val extends = Machine(name)

    fun consumableRate(
        @Parent(Entities.MANUFACTURING_STEP)
        toolComplexity: Num
    ): Rate {
        return when (toolComplexity) {
            Num(1.0) -> Rate(0.04)
            Num(2.0) -> Rate(0.045)
            Num(3.0) -> Rate(0.05)
            Num(4.0) -> Rate(0.06)
            Num(5.0) -> Rate(0.07)
            else -> throw IllegalArgumentException("not supported toolcomplexity")
        }
    }
}


