package com.nu.bom.core.technologies.steps.pboxstampingcutting

import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.MissingInputError
import com.nu.bom.core.manufacturing.entities.SingleGroupManufacturingStep
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.ManufacturingStepType
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Speed
import com.nu.bom.core.manufacturing.fieldTypes.SpeedUnits
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.ToolAllocationMode
import com.nu.bom.core.technologies.steps.ManufacturingStepUtils.minByOrTemplateNotFoundException
import com.nu.bom.core.technologies.steps.pboxstampingcutting.systemparameter.SystemParameterPboxStampingCutting
import com.nu.bom.core.utils.annotations.TsetSuppress
import reactor.core.publisher.Mono

@EntityType(Entities.MANUFACTURING_STEP)
@Suppress("unused")
class ManufacturingStepPboxStampingCutting(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = SingleGroupManufacturingStep(name)

    companion object {
        const val TEMPLATE_LOOKUP_NAME = "ManufacturingStepPboxStampingCutting_Templates"

        class TemplateLookupEntry(
            val templateName: String,
            val maxProcessingSpeed: Speed,
        )

        val templateLookupReader: (row: List<String>) -> TemplateLookupEntry = { row ->
            TemplateLookupEntry(
                templateName = row[0],
                maxProcessingSpeed = Speed(row[1].toBigDecimal(), SpeedUnits.M_PER_MIN),
            )
        }
    }

    fun manufacturingStepType(): Text = Text(ManufacturingStepType.STAMPING_CUTTING.name)

    // region Fields from MANUFACTURING

    @Parent(Entities.MANUFACTURING)
    fun key(): Text? = null

    @Input
    @Parent(Entities.MANUFACTURING)
    fun nestingResultParts(): QuantityUnit = throw MissingInputError()

    // endregion

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 20)
    fun processingSpeed() = Speed(300.0, SpeedUnits.M_PER_MIN)

    fun partsPerCycle(nestingResultParts: QuantityUnit) = nestingResultParts

    fun utilizationRate() = Rate(0.9)

    fun templateName(processingSpeed: Speed): Mono<Text> =
        services
            .getLookupTable(TEMPLATE_LOOKUP_NAME, templateLookupReader)
            .filter {
                processingSpeed.res <= it.maxProcessingSpeed.res
            }.minByOrTemplateNotFoundException(
                selector = { it.maxProcessingSpeed.res },
                lookupName = TEMPLATE_LOOKUP_NAME,
                lookupInputs =
                    mapOf(
                        this::processingSpeed.name to processingSpeed,
                    ),
            ).map { Text(it.templateName) }

    @Input
    @TsetSuppress("tset:engine:field-override-configuration-value")
    fun toolAllocationMode() = ToolAllocationMode(ToolAllocationMode.Mode.PAY_ALL)

    @EntityCreation(Entities.MACHINE)
    fun createMachines(templateName: Text) = createMachinesFromTemplate(templateName)

    @EntityCreation(Entities.LABOR)
    fun createLabor(
        templateName: Text,
        locationName: Text,
    ) = createLaborFromTemplate(templateName, locationName)

    @EntityCreation(Entities.TOOL)
    fun createTools(templateName: Text) = createToolFromTemplate(templateName)

    @EntityCreation(Entities.SYSTEM_PARAMETER)
    fun createSystemParameter(templateName: Text) =
        createSystemParameter(
            masterData = MasterDataType.SYSTEM_PARAMETER_PBOX_STAMPING_CUTTING,
            clazz = SystemParameterPboxStampingCutting::class,
            system = templateName.res,
            year = calculationContext?.year!!,
            location = "Global",
        )
}
