package com.nu.bom.core.technologies.lookups

import com.nu.bom.core.manufacturing.fieldTypes.Force
import com.nu.bom.core.manufacturing.fieldTypes.ForceUnits
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Volume
import com.nu.bom.core.manufacturing.fieldTypes.VolumeUnits

data class RubberInjectionTemplates(
    val templateName: String,
    val rubberOrSilicone: Text,
    val openingType: Text,
    val minInjectionVolume: Volume,
    val maxInjectionVolume: Volume,
    val injectionVolume: Volume,
    val lockingForce: Force,
    val mountingPlateWidth: Length,
    val mountingPlateHeight: Length,
)

val rubberInjectionTemplatesReader: (List<String>) -> RubberInjectionTemplates = { row ->
    RubberInjectionTemplates(
        templateName = row[0],
        rubberOrSilicone = Text(row[1]),
        openingType = Text(row[2]),
        minInjectionVolume = Volume(row[3].toBigDecimal(), VolumeUnits.CM),
        maxInjectionVolume = Volume(row[4].toBigDecimal(), VolumeUnits.CM),
        injectionVolume = Volume(row[5].toBigDecimal(), VolumeUnits.CM),
        lockingForce = Force(row[8].toBigDecimal(), ForceUnits.NEWTON),
        mountingPlateWidth = Length(row[9].toBigDecimal(), LengthUnits.METER),
        mountingPlateHeight = Length(row[10].toBigDecimal(), LengthUnits.METER),
    )
}
