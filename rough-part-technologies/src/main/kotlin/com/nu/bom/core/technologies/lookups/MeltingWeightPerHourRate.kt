package com.nu.bom.core.technologies.lookups

import java.math.BigDecimal

data class MeltingWeightPerHourRate(
    val meltingWeightPerHourRate: BigDecimal,
    val countryId: String,
)

val meltingWeightPerHourRateReader: (row: List<String>) -> MeltingWeightPerHourRate = { row ->
    MeltingWeightPerHourRate(
        countryId = row[0],
        meltingWeightPerHourRate = row[1].toBigDecimal(),
    )
}
