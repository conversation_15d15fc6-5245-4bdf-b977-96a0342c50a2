package com.nu.bom.core.technologies.manufacturings.cext.material

import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.MaterialCalculatorForging
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.MaterialViewConfig
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.technologies.service.ShapeLookupReaderService
import reactor.core.publisher.Mono
import java.math.BigDecimal

@EntityType(Entities.MATERIAL)
class MaterialForgingCext(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = MaterialCalculatorForging(name)

    @Input
    fun reuseOfScrap() = Bool(false)

    fun blankDiameterBase(
        lookupReaderService: ShapeLookupReaderService,
        @Parent(Entities.PROCESSED_MATERIAL)
        shapeId: Text,
    ): Mono<Text> =
        lookupReaderService.getCextShapeData(calculationContext!!.accessCheck, shapeId).map {
            Text(it.blankDiameterBase)
        }

    fun blankDiameterFactor(
        lookupReaderService: ShapeLookupReaderService,
        @Parent(Entities.PROCESSED_MATERIAL)
        shapeId: Text,
    ): Mono<Rate> =
        lookupReaderService.getCextShapeData(calculationContext!!.accessCheck, shapeId).map {
            Rate(it.blankDiameterFactor)
        }

    fun blankDiameterCalculated(
        blankDiameterBase: Text,
        blankDiameterFactor: Rate,
        @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
        partInnerDiameter: Length,
        @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
        partOuterDiameter: Length,
        @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
        partWidth: Length,
    ): Length {
        val r =
            when (blankDiameterBase.res) {
                "width" -> partWidth
                "innerDiameter" -> partInnerDiameter
                else -> partOuterDiameter
            }
        return r * blankDiameterFactor
    }

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun burrWeight(): QuantityUnit = QuantityUnit(BigDecimal.ZERO)

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun burrLossWeight(): QuantityUnit = QuantityUnit(BigDecimal.ZERO)

    fun materialView(): MaterialViewConfig = MaterialViewConfig.MATERIAL_FORGING_CEXT
}
