package com.nu.bom.core.technologies.steps.stamping

import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.Selectable
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.annotations.SubLabel
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.SingleGroupManufacturingStep
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.ManufacturingStepType
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeLaminationStack
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.technologies.lookups.stampingPropertiesReader
import com.nu.bom.core.technologies.service.ShapeLookupReaderService
import com.nu.bom.core.technologies.steps.stamping.systemparameter.SystemParameterStamping
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

@EntityType(Entities.MANUFACTURING_STEP)
class ManufacturingStepStamping(
    name: String,
) : ManufacturingEntity(name) {
    override val extends: ManufacturingEntity = SingleGroupManufacturingStep(name)

    @Input
    @SubLabel
    @ReadOnly
    @Selectable(value = "manufacturingStepType", getDisplayNameFromPathQuery = true)
    fun manufacturingStepType(): Text = Text(ManufacturingStepType.SHEET_METAL_FORMING.name)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun shapeId(): Text? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun netWeightPerPart(): QuantityUnit? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    fun partLength(): Length? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    fun partWidth(): Length? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    fun partOuterDiameter(): Length? = null

    @Input
    fun utilizationRate(): Rate = Rate(0.85.toBigDecimal())

    fun scrapRate(): Rate = Rate(0.005.toBigDecimal())

    @Input
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 5)
    @SpecialLink("MaterialLaminationStack", "neededLamellaPerStack")
    fun neededLamellaPerStack(): QuantityUnit? = null

    @Input
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 10)
    @SpecialLink("MaterialLaminationStack", "neededLamella")
    fun neededLamella(): QuantityUnit? = null

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 15)
    fun numberOfStampingSteps(
        lookupReaderService: ShapeLookupReaderService,
        @Parent(Entities.PROCESSED_MATERIAL)
        shapeId: Text,
    ): Mono<Num> =
        lookupReaderService.getLastShapeData(calculationContext!!.accessCheck, shapeId).map {
            it.numberOfStampingSteps
        }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 20)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun additionalToolWidth() = Length(10.0, LengthUnits.CENTIMETER)

    fun internalPartsPerCycle(
        @Parent(Entities.MANUFACTURING)
        laminationStackType: StepSubTypeLaminationStack,
        @Parent(Entities.MANUFACTURING)
        blankDiameter: Length,
        neededLamella: QuantityUnit,
    ): Mono<QuantityUnit> {
        val type = laminationStackType == StepSubTypeLaminationStack.ROTOR_STATOR_NESTED
        return services
            .getLookupTable("ManufacturingStepStamping_Properties", stampingPropertiesReader)
            .filter {
                type == it.rotorStatorNested &&
                    blankDiameter.inMillimeter <= it.maxPartOuterDiameter.inMillimeter &&
                    neededLamella.res <= it.maxNeededLamella.res
            }.elementAt(0)
            .map { QuantityUnit(it.selectedPartsPerCycle) }
    }

    private class TemplateLookupEntry(
        val templateName: String,
        val maxCoilWidth: Length,
        val maxCoilLength: Length,
    )

    private val machineTemplateLookupReader: (row: List<String>) -> TemplateLookupEntry = {
        TemplateLookupEntry(
            templateName = it[0],
            maxCoilWidth = Length(it[1].toBigDecimal(), LengthUnits.METER),
            maxCoilLength = Length(it[2].toBigDecimal(), LengthUnits.METER),
        )
    }

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 50)
    @SpecialLink("MaterialLaminationStack", "coilWidth")
    fun coilWidth(): Length? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 60)
    @SpecialLink("MaterialLaminationStack", "requiredLength")
    fun requiredLength(): Length? = null

    fun templateName(
        coilWidth: Length,
        requiredLength: Length,
    ): Mono<Text> =
        services
            .getLookupTable(
                "ManufacturingStepStamping_Templates",
                machineTemplateLookupReader,
            ).filter {
                val diffCoilWidth = coilWidth.inMillimeter <= it.maxCoilWidth.inMillimeter * 0.8.toBigDecimal()

                val diffCoilLength = requiredLength.inMillimeter <= it.maxCoilLength.inMillimeter * 0.9.toBigDecimal()

                diffCoilLength && diffCoilWidth
            }.elementAt(0)
            .map { Text(it.templateName) }

    @EntityCreation(Entities.SYSTEM_PARAMETER)
    fun createSystemParameter(templateName: Text): Mono<SystemParameterStamping> =
        createSystemParameter(
            masterData = MasterDataType.SYSTEM_PARAMETER_STAMPING,
            clazz = SystemParameterStamping::class,
            system = templateName.res,
            year = calculationContext?.year!!,
            location = "Global",
        )

    @EntityCreation(Entities.MACHINE)
    fun createMachines(templateName: Text): Flux<ManufacturingEntity> =
        createEntitiesFromTemplate(
            name = templateName.res + "_Machine",
            location = "Global",
        )

    @EntityCreation(Entities.LABOR)
    fun createLabor(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> =
        createEntitiesFromTemplate(
            name = templateName.res + "_Labor",
            location = locationName.res,
        )

    @EntityCreation(Entities.SETUP)
    fun createSetup(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> =
        createEntitiesFromTemplate(
            name = templateName.res + "_Setup",
            location = locationName.res,
        )

    @EntityCreation(Entities.TOOL)
    fun createTools(templateName: Text): Flux<ManufacturingEntity> =
        createEntitiesFromTemplate(
            name = templateName.res + "_Tools",
            location = "Global",
        )

    @EntityCreation(Entities.MATERIAL)
    fun createMaterial(
        @Parent(Entities.PROCESSED_MATERIAL)
        materialName: Text,
        @Parent(Entities.PROCESSED_MATERIAL)
        materialClass: Text,
    ): Mono<ManufacturingEntity> =
        createEntityWithNewMasterdata(
            name = "MaterialLaminationStack",
            entityType = Entities.MATERIAL,
            clazz = materialClass.res,
            masterDataType = MasterDataType.RAW_MATERIAL_LAMELLA,
            masterDataKey = materialName.res,
        )
}
