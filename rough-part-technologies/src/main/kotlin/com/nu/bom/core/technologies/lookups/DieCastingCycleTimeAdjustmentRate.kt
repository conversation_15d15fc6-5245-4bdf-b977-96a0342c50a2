package com.nu.bom.core.technologies.lookups

import com.nu.bom.core.manufacturing.fieldTypes.Rate

data class DieCastingCycleTimeAdjustmentRate(
    val shapeId: String,
    val removeEjector: Rate,
    val closeMold: Rate,
    val fillMaterial: Rate,
    val shot: Rate,
    val openMold: Rate,
    val removeCast: Rate,
    val sprayMoldWithReleaseAgent: Rate,
    val blowOutMold: Rate,
    val retractEjector: Rate,
    val solidificationTime: Rate,
    val openSlider: Rate,
    val closeSlider: Rate,
)

val dieCastingCycleTimeAdjustmentRateReader: (row: List<String>) -> DieCastingCycleTimeAdjustmentRate = { row ->
    DieCastingCycleTimeAdjustmentRate(
        shapeId = row[0],
        removeEjector = Rate(row[1].toBigDecimal()),
        closeMold = Rate(row[2].toBigDecimal()),
        fillMaterial = Rate(row[3].toBigDecimal()),
        shot = Rate(row[4].toBigDecimal()),
        openMold = Rate(row[5].toBigDecimal()),
        removeCast = Rate(row[6].toBigDecimal()),
        sprayMoldWithReleaseAgent = Rate(row[7].toBigDecimal()),
        blowOutMold = Rate(row[8].toBigDecimal()),
        retractEjector = Rate(row[9].toBigDecimal()),
        solidificationTime = Rate(row[10].toBigDecimal()),
        openSlider = Rate(row[11].toBigDecimal()),
        closeSlider = Rate(row[12].toBigDecimal()),
    )
}
