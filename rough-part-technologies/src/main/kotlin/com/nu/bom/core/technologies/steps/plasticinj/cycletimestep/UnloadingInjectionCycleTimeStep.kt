package com.nu.bom.core.technologies.steps.plasticinj.cycletimestep

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.entities.CycleTimeStep
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.MissingInputError
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeUnit
import com.nu.bom.core.manufacturing.fieldTypes.Force
import com.nu.bom.core.manufacturing.fieldTypes.PartRemovalType
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits

@EntityType(Entities.CYCLETIME_STEP)
class UnloadingInjectionCycleTimeStep(name: String) : ManufacturingEntity(name) {

    override val extends = CycleTimeStep(name)

    @Input
    @SpecialLink("SystemParameterInjection", "partRemoval")
    fun partRemoval(): PartRemovalType = throw MissingInputError()

    fun manualUnloadingTimePerPart(@Parent(Entities.MANUFACTURING_STEP) netWeightPerPart: QuantityUnit): Time {
        val timePerPartRes = when {
            netWeightPerPart.res < 0.4.toBigDecimal() -> 3.0
            netWeightPerPart.res < 0.8.toBigDecimal() -> 4.0
            netWeightPerPart.res < 1.2.toBigDecimal() -> 6.0
            else -> 10.0
        }
        return Time(timePerPartRes, TimeUnits.SECOND)
    }

    fun time(
        partRemoval: PartRemovalType,
        @Parent(Entities.MANUFACTURING_STEP)
        necessaryClampingForce: Force,
        @Parent(Entities.MANUFACTURING_STEP)
        partsPerCycle: QuantityUnit,
        manualUnloadingTimePerPart: Time,
    ): CycleTime {
        val seconds = when (partRemoval.res) {
            PartRemovalType.Selection.FREE_FALLING -> 2.0.toBigDecimal()
            PartRemovalType.Selection.SPRUE_PICKER -> 3.8.toBigDecimal()
            PartRemovalType.Selection.ROBOT_UNLOAD ->
                if (necessaryClampingForce.res > 5000000.toBigDecimal()) 7.2.toBigDecimal() else 4.8.toBigDecimal()
            PartRemovalType.Selection.MANUAL_REMOVAL -> (manualUnloadingTimePerPart * partsPerCycle).inSeconds
        }
        return CycleTime(seconds, CycleTimeUnit.SECOND)
    }
}
