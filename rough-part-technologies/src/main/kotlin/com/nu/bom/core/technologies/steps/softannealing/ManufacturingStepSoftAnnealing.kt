package com.nu.bom.core.technologies.steps.softannealing

import com.nu.bom.core.manufacturing.annotations.Children
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.Temperature
import com.nu.bom.core.manufacturing.fieldTypes.TemperatureUnits
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.technologies.lookups.heatSoakingTemperatureReader
import com.nu.bom.core.technologies.steps.heattreatment.ManufacturingStepHeatTreatment
import com.nu.bom.core.technologies.steps.softannealing.systemparameter.SystemParameterSoftAnnealing
import reactor.core.publisher.Mono
import java.math.BigDecimal
import java.math.RoundingMode
import kotlin.math.roundToInt

@EntityType(Entities.MANUFACTURING_STEP)
class ManufacturingStepSoftAnnealing(name: String) : ManufacturingEntity(name) {
    override val extends = ManufacturingStepHeatTreatment(name)

    @Input
    val scrapRate: Rate = Rate(BigDecimal.ZERO)

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 100)
    @ReadOnly
    @Input
    @SpecialLink("MaterialForging", "blankDiameter")
    val blankDiameter: Length? = null

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 100)
    @ReadOnly
    @Input
    @SpecialLink("MaterialForging", "blankDiameter")
    val blankLength: Length? = null

    fun template(): Text {
        return Text("ManufacturingStepSoftAnnealing_Templates")
    }

    fun lotCooling(
        @Children(Entities.SYSTEM_PARAMETER)
        lotCooling: Num,
    ): Num {
        return lotCooling
    }

    fun lotHeatingSoaking(
        @Children(Entities.SYSTEM_PARAMETER)
        lotHeatingSoaking: Num,
    ): Num {
        return lotHeatingSoaking
    }

    fun partsPerCycle(
        templateName: Text,
        internalPartsPerCycle: QuantityUnit,
        // Todo: Might need to be changed to average volume. See COST-50914
        peakLotSize: QuantityUnit,
    ): QuantityUnit {
        return when {
            templateName.res.contains("Belt") -> QuantityUnit(internalPartsPerCycle.res)
            else -> QuantityUnit(min(internalPartsPerCycle.res, peakLotSize.res.setScale(0, RoundingMode.DOWN)))
        }
    }

    @DefaultUnit(DefaultUnit.MINUTE)
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 10)
    fun initialHeatingTimePerArea(
        blankDiameter: Length,
        soakingTemperature: Temperature,
        warmformed: SelectableBoolean,
    ): Mono<Time> {
        val soakingTemperatureRounded =
            (
                soakingTemperature
                    .inCelsius
                    .toDouble() / 5.0
            ).roundToInt() * 5

        val wallThicknessRounded =
            blankDiameter
                .inMillimeter
                .toDouble().roundToInt()

        val lookupName = "ManufacturingStepHeatTreatment_HeatUpTemperature_modelled"

        return services.getLookupRows(lookupName) { row ->
            row["maxWallThickness"] to
                (
                    row["$soakingTemperatureRounded"]
                        ?: error("Did not find column $soakingTemperatureRounded in $lookupName")
                )
        }
            .filter { it.first == "$wallThicknessRounded" }
            .single()
            .map { it.second }
            .map { time ->
                Time(time.toBigDecimal(), TimeUnits.MINUTE) *
                    when (warmformed) {
                        SelectableBoolean.TRUE -> 0.toBigDecimal()
                        else -> 1.toBigDecimal()
                    }
            }
    }

    @DefaultUnit(DefaultUnit.CELSIUS)
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 30)
    fun soakingTemperature(materialName: Text): Mono<Temperature> {
        return services.getLookupTable(
            "ManufacturingStepHeatTreatment_SoakingTemperature",
            heatSoakingTemperatureReader,
        ).filter {
            materialName.res == it.materialName.res
        }.map { it.softAnnealing }.single(Temperature(710.0, TemperatureUnits.CELSIUS))
    }

    @DefaultUnit(DefaultUnit.MINUTE)
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 20)
    val firstSoakingTimePerArea = Time(240.toBigDecimal(), TimeUnits.MINUTE)

    fun numberOfLots(
        @Children(Entities.SYSTEM_PARAMETER)
        lotHeatingSoaking: Num,
        @Children(Entities.SYSTEM_PARAMETER)
        lotCooling: Num,
    ): Num {
        return lotHeatingSoaking + lotCooling
    }

    @DefaultUnit(DefaultUnit.MINUTE)
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 40)
    fun coolingTimePerArea(blankDiameter: Length): Time {
        return Time(43.491.toBigDecimal() + blankDiameter.inMillimeter * 1.724.toBigDecimal(), TimeUnits.MINUTE)
    }

    @DefaultUnit(DefaultUnit.CELSIUS)
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 7)
    @ReadOnly
    fun numberOfStages(
        @Children(Entities.SYSTEM_PARAMETER)
        chargeFrameHeight: Length,
        blankLength: Length,
    ): Num {
        val gap = 0.01.toBigDecimal()
        return Num(max(1.toBigDecimal(), ((chargeFrameHeight - gap) / (blankLength.res + gap)).res.setScale(0, RoundingMode.DOWN)))
    }

    fun internalPartsPerCycle(
        templateName: Text,
        blankDiameter: Length,
        @Children(Entities.SYSTEM_PARAMETER)
        bulkMaterial: Bool,
        netWeightPerPart: QuantityUnit,
        @Children(Entities.SYSTEM_PARAMETER)
        chargeFrameLength: Length,
        @Children(Entities.SYSTEM_PARAMETER)
        chargeFrameWidth: Length,
        @Children(Entities.SYSTEM_PARAMETER)
        chargeWeight: QuantityUnit,
        numberOfStages: Num,
    ): QuantityUnit {
        val factorCylinder = 0.9.toBigDecimal()
        val piecesOfBulkMaterial =
            ((0.9.toBigDecimal() * chargeWeight.res * factorCylinder) / netWeightPerPart.res).setScale(
                0,
                RoundingMode.DOWN,
            )

        if (templateName.res.contains("Belt")) {
            val gap = 0.01.toBigDecimal()
            val partsPerLength =
                max(
                    1.toBigDecimal(),
                    (((chargeFrameWidth - gap) / (blankDiameter.res + gap)).res),
                ).setScale(0, RoundingMode.DOWN).toInt()
            val partsPartPerWidth =
                max(
                    1.toBigDecimal(),
                    (((chargeFrameLength - gap) / (blankDiameter.res + gap)).res.setScale(0, RoundingMode.DOWN)),
                ).toInt()
            val partsPerCycleBelt =
                (1.2.toBigDecimal() * partsPerLength.toBigDecimal() * partsPartPerWidth.toBigDecimal()).setScale(
                    0,
                    RoundingMode.HALF_UP,
                )
            return QuantityUnit(min(piecesOfBulkMaterial, partsPerCycleBelt))
        } else if (bulkMaterial.isTrue()) {
            return QuantityUnit(piecesOfBulkMaterial)
        } else {
            val gap = 0.01.toBigDecimal()
            val diameter = 0.02.toBigDecimal()
            val partsPerLength =
                max(
                    1.toBigDecimal(),
                    (
                        (
                            (0.5.toBigDecimal() * chargeFrameWidth.res - 1.5.toBigDecimal() * diameter - gap) /
                                (blankDiameter.res + gap)
                        ) * 2.toBigDecimal()
                    ),
                ).setScale(0, RoundingMode.DOWN).toInt()
            val partsPartPerWidth =
                max(
                    1.toBigDecimal(),
                    (
                        (
                            (0.5.toBigDecimal() * chargeFrameLength.res - 1.5.toBigDecimal() * diameter - gap) /
                                (blankDiameter.res + gap)
                        ) * 2.toBigDecimal()
                    ),
                ).setScale(0, RoundingMode.DOWN).toInt()
            val partsPerCyclePerStage =
                (1.2.toBigDecimal() * partsPerLength.toBigDecimal() * partsPartPerWidth.toBigDecimal()).setScale(
                    0,
                    RoundingMode.HALF_UP,
                )

            return QuantityUnit(min(piecesOfBulkMaterial, (partsPerCyclePerStage * numberOfStages.res)))
        }
    }

    @EntityCreation(Entities.SYSTEM_PARAMETER)
    fun createSystemParameter(templateName: Text): Mono<SystemParameterSoftAnnealing> {
        return createSystemParameter(
            masterData = MasterDataType.SYSTEM_PARAMETER_SOFT_ANNEALING,
            clazz = SystemParameterSoftAnnealing::class,
            system = templateName.res,
            year = calculationContext?.year!!,
            location = "Global",
        )
    }
}
