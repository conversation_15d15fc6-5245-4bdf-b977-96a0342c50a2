package com.nu.bom.core.technologies.steps.galvanizing.labor

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.Labor
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.MissingInputError
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Time
import java.math.RoundingMode

@EntityType(Entities.LABOR)
class PlasticGalvanizingLabor(name: String) : ManufacturingEntity(name) {
    override val extends = Labor(name)

    @Input
    @Parent(Entities.MANUFACTURING_STEP)
    fun galvanizingCycleTime(): CycleTime = throw MissingInputError()

    fun bottleneckCycleTime(galvanizingCycleTime: CycleTime) = galvanizingCycleTime

    @Input
    fun timePerPart(): Time = throw MissingInputError()

    fun timePerCycle(
        @Parent(Entities.MANUFACTURING_STEP) partsPerCycle: QuantityUnit,
        timePerPart: Time,
    ) = timePerPart.times(partsPerCycle)

    fun requiredLaborPerLine(
        timePerCycle: Time,
        bottleneckCycleTime: CycleTime,
    ) = Num(timePerCycle.div(bottleneckCycleTime).res.setScale(0, RoundingMode.CEILING))

    fun requiredLabor(requiredLaborPerLine: Num) = requiredLaborPerLine
}
