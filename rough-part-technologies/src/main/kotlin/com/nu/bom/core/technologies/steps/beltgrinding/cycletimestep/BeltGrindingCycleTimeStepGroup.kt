package com.nu.bom.core.technologies.steps.beltgrinding.cycletimestep

import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.entities.CycleTimeStepGroup
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeGrouping

@EntityType(Entities.CYCLETIME_STEP_GROUP)
class BeltGrindingCycleTimeStepGroup(name: String) : ManufacturingEntity(name) {
    override val extends: ManufacturingEntity = CycleTimeStepGroup(name)

    fun grouping() = CycleTimeGrouping.Parallel

    @EntityCreation(Entities.CYCLETIME_STEP)
    fun createCycleTimeSteps(): List<ManufacturingEntity> {
        return listOf(
            createEntity(
                name = "Loading",
                clazz = BeltGrindingLoadingUnloadingCycleTimeStep::class,
                entityType = Entities.CYCLETIME_STEP,
            ),
            createEntity(
                name = "Belt grinding",
                clazz = BeltGrindingCycleTimeStep::class,
                entityType = Entities.CYCLETIME_STEP,
            ),
            createEntity(
                name = "Unloading",
                clazz = BeltGrindingLoadingUnloadingCycleTimeStep::class,
                entityType = Entities.CYCLETIME_STEP,
            ),
        )
    }
}
