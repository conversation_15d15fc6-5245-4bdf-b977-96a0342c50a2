package com.nu.bom.core.technologies.steps.turn.tool.extension
import com.nu.bom.core.manufacturing.annotations.Extends
import com.nu.bom.core.manufacturing.entities.ManufacturingEntityExtension
import com.nu.bom.core.manufacturing.extension.CO2_EXTENSION_PACKAGE
import com.nu.bom.core.manufacturing.extension.ToolCO2Extension
import com.nu.bom.core.manufacturing.fieldTypes.ToolDetailViewConfig
import com.nu.bom.core.technologies.steps.turn.tool.ToolTurningDrill

@Extends([ToolTurningDrill::class], CO2_EXTENSION_PACKAGE)
class ToolTurningDrillCO2Extension(name: String) : ManufacturingEntityExtension(name) {
    override val extends = ToolCO2Extension(name)

    fun cO2ToolDetailView(toolDetailView: ToolDetailViewConfig): ToolDetailViewConfig = toolDetailView
}
