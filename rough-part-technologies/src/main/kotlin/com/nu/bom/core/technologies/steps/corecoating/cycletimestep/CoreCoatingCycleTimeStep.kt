package com.nu.bom.core.technologies.steps.corecoating.cycletimestep

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.entities.CycleTimeStep
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Time
import java.math.BigDecimal

@EntityType(Entities.CYCLETIME_STEP)
class CoreCoatingCycleTimeStep(name: String) : ManufacturingEntity(name) {
    override val extends: ManufacturingEntity? = CycleTimeStep(name)

    @Input
    fun adjustmentRate() = Rate(BigDecimal.ONE)

    @Input
    @SpecialLink("SystemParameterCoreCoating", "fixedCycleTime")
    fun fixedCycleTime(): Time? = null

    fun time(fixedCycleTime: Time) = CycleTime(fixedCycleTime.inSeconds, CycleTimeUnit.SECOND)
}
