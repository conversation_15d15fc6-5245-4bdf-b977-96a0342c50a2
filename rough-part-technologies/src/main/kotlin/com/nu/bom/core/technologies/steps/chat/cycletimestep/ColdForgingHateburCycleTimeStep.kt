package com.nu.bom.core.technologies.steps.chat.cycletimestep

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.CycleTimeStep
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeUnit
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Rate

@EntityType(Entities.CYCLETIME_STEP)
class ColdForgingHateburCycleTimeStep(name: String) : ManufacturingEntity(name) {

    override val extends = CycleTimeStep(name)

    fun time(
        @Parent(Entities.MANUFACTURING_STEP)
        strokesPerMin: Num
    ): CycleTime {
        return CycleTime(Num(60).res / strokesPerMin.res, CycleTimeUnit.SECOND)
    }

    @Input
    val adjustmentRate: Rate = Rate(1.0)
}
