package com.nu.bom.core.technologies.lookups

import com.nu.bom.core.manufacturing.fieldTypes.*

data class SawingCycleTime(
    val stepSubTypeSaw: StepSubTypeSaw,
    val stepSubTypeMaterial: StepSubTypeMaterial,
    val feedPerTooth: Length,
    val cuttingSpeed: Length,
    val maxCuttingSurface: Area,
)

val sawingCycleTimeReader: (row: List<String>) -> SawingCycleTime = { row ->
    SawingCycleTime(
        stepSubTypeSaw = StepSubTypeSaw.valueOf(row[1]),
        stepSubTypeMaterial = StepSubTypeMaterial.valueOf(row[0]),
        feedPerTooth = Length(row[2].toBigDecimal(), LengthUnits.MILLIMETER),
        cuttingSpeed = Length(row[3].toBigDecimal(), LengthUnits.METER),
        maxCuttingSurface = Area(row[4].toBigDecimal(), AreaUnits.QMM),
    )
}
