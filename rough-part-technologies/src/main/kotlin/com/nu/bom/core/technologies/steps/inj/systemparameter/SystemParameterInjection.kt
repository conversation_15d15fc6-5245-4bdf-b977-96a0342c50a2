package com.nu.bom.core.technologies.steps.inj.systemparameter

import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.StaticDenominatorUnit
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.SystemParameter
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.StaticUnitOverride
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Force
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.PartRemovalType
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.Volume
import com.nu.bom.core.manufacturing.fieldTypes.Weight

@EntityType(Entities.SYSTEM_PARAMETER)
class SystemParameterInjection(name: String) : ManufacturingEntity(name) {

    override val extends = SystemParameter(name)

    @Input
    @ReadOnly
    @ObjectView(ObjectView.SYSTEM, 2)
    @DefaultUnit(DefaultUnit.SECOND)
    fun dryRunningTime(): Time? = null

    @Input
    @ReadOnly
    @ObjectView(ObjectView.SYSTEM, 3)
    @DefaultUnit(DefaultUnit.KILONEWTON)
    fun lockingForce(): Force? = null

    // Only for BCT
    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.CCM)
    @StaticDenominatorUnit(StaticUnitOverride.SECOND)
    fun injectionVolumePerTime(): Volume? = null

    @Input
    @ReadOnly
    @ObjectView(ObjectView.SYSTEM, 5)
    @DefaultUnit(DefaultUnit.GRAM)
    @StaticDenominatorUnit(StaticUnitOverride.SECOND)
    fun plasticisingMassPerTime(): Weight? = null

    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun mountingTableWidth(): Length? = null

    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun mountingTableHeight(): Length? = null

    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun tieBarDistanceHori(): Length? = null

    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun tieBarDistanceVerti(): Length? = null

    @Input
    // @ObjectView(ObjectView.SYSTEM,1)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun partDistance(): Length? = null

    @Input
    @ReadOnly
    @ObjectView(ObjectView.SYSTEM, 6)
    fun partRemoval(): PartRemovalType? = null

    @Input
    @ReadOnly
    @ObjectView(ObjectView.SYSTEM, 7)
    fun hasTieBars(): Bool? = null

    @Input
    @ReadOnly
    @ObjectView(ObjectView.SYSTEM, 8)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun maximumToolHeight(mountingTableHeight: Length?): Length? {
        return mountingTableHeight
    }

    @Input
    @ReadOnly
    @ObjectView(ObjectView.SYSTEM, 9)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun maximumToolWidth(
        hasTieBars: Bool?,
        tieBarDistanceHori: Length?,
        mountingTableWidth: Length?,
    ): Length? {
        return when (hasTieBars?.res) {
            true -> tieBarDistanceHori
            else -> mountingTableWidth
        }
    }
}
