package com.nu.bom.core.technologies.steps.gca.tool

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.StaticDenominatorUnit
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.Tool
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.StaticUnitOverride
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.ToolMaintenanceType
import com.nu.bom.core.technologies.lookups.gravityCastingMaintenancePerYearReader
import reactor.core.publisher.Mono

@EntityType(Entities.TOOL)
class RobotGripperTool(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = Tool(name)

    fun toolMaintenanceType(): ToolMaintenanceType = ToolMaintenanceType.PERCENT_YEAR

    fun serviceLifeInCycles(
        @Parent(Entities.MANUFACTURING_STEP)
        cyclesOverLifeTime: Num,
    ) = cyclesOverLifeTime

    @StaticDenominatorUnit(StaticUnitOverride.YEAR)
    fun maintenanceRatePerYear(entityDesignation: Text): Mono<Rate> =
        services
            .getLookupTable(
                "ManufacturingStepGravityCasting_MaintenancePerYear",
                gravityCastingMaintenancePerYearReader,
            ).filter {
                it.toolDesignation == entityDesignation.res
            }.single()
            .map {
                Rate(it.maintenancePerYear)
            }
}
