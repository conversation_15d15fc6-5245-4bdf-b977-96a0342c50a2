package com.nu.bom.core.technologies.manufacturings.rinj

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.Condition
import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.Precompute
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.annotations.WizardField
import com.nu.bom.core.manufacturing.defaults.NullProvider
import com.nu.bom.core.manufacturing.entities.BaseModelManufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ShapeBasedCostModuleManufacturing
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeCleanness
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.technologies.manufacturings.rinj.material.RubberInjectedMaterial
import com.nu.bom.core.technologies.service.ShapeLookupReaderService
import com.nu.bom.core.technologies.steps.rinj.ManufacturingStepRubberInjection
import kotlin.reflect.KClass

@EntityType(Entities.MANUFACTURING)
class ManufacturingRubberInjection(
    name: String,
) : BaseModelManufacturing(name) {
    override val extends = ShapeBasedCostModuleManufacturing(name)

    override val model: Model
        get() = Model.RINJ

    override val modelSteps: List<KClass<out ManufacturingEntity>>
        get() =
            listOf(
                ManufacturingStepRubberInjection::class,
            )

    fun inputGroupInternal(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
    ) = lookupReaderService.getInputGroupFromShapeLookup(calculationContext!!.accessCheck, shapeId, Model.RINJ)

    @Input
    fun materialClass(): Text = Text("MaterialRubber")

    @Input
    @Path("/api/model/rinj/materialName")
    fun materialName(): Text? = null // TODO ->  Material ENUM ?

    @WizardField(index = 7)
    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun maxWallThickness(
        @Parent(Entities.PROCESSED_MATERIAL) @Default(NullProvider::class)
        maxWallThickness: Length?,
    ) = maxWallThickness ?: Length(2.0, LengthUnits.MILLIMETER)

    @Input
    @WizardField
    @Condition(field = "cleaningNeeded", value = "true", operator = Condition.EQUALS)
    fun cleanness(): StepSubTypeCleanness = StepSubTypeCleanness.NORMAL

    @Input
    @WizardField
    fun cleaningNeeded(): SelectableBoolean = SelectableBoolean.FALSE

    @Input
    @WizardField(index = 50)
    fun insertsNeeded(): SelectableBoolean? = null

    @Input
    @Precompute
    @WizardField(index = 50)
    @Condition(field = "insertsNeeded", value = "true", operator = Condition.EQUALS)
    fun createInsertsAutomatically(): SelectableBoolean = SelectableBoolean.TRUE

    @Input
    @Precompute
    @WizardField(index = 51)
    @Condition(field = "insertsNeeded", value = "true", operator = Condition.EQUALS)
    fun numberOfInserts(): Num = Num(1.0)

    @Input
    @SpecialLink("ManufacturingRubberInjection.location", "countryId")
    fun countryId(): Text? = null

    @SpecialLink("ManufacturingStepRubberInjection", "familyTooling")
    fun familyTooling(): SelectableBoolean? = null

    @SpecialLink("ManufacturingStepRubberInjection", "numberOfAdditionalFamilyParts")
    fun numberOfAdditionalFamilyParts(): Num? = null

    fun internalCallsPerYear(
        // Todo: Might need to be changed to average volume. See COST-50914
        peakUsableProductionVolumePerYear: QuantityUnit,
        familyTooling: SelectableBoolean,
        numberOfAdditionalFamilyParts: Num,
    ): Num =
        services.getInjectionUtilsService().internalCallsPerYearImpl(
            peakUsableProductionVolumePerYear,
            familyTooling,
            numberOfAdditionalFamilyParts,
        )

    fun productionVolumeMode(
        // Todo: Might need to be changed to average volume. See COST-50914
        peakUsableProductionVolumePerYear: QuantityUnit,
        familyTooling: SelectableBoolean,
        numberOfAdditionalFamilyParts: Num,
    ): Num =
        services.getInjectionUtilsService().productionVolumeModeImpl(
            peakUsableProductionVolumePerYear,
            familyTooling,
            numberOfAdditionalFamilyParts,
        )

    @EntityCreation(Entities.PROCESSED_MATERIAL, childCreations = [Entities.MANUFACTURING_STEP])
    fun createMaterial(): ManufacturingEntity =
        createEntity(
            name = "RubberInjectedMaterial",
            clazz = RubberInjectedMaterial::class,
            entityType = Entities.MATERIAL,
        )
}
