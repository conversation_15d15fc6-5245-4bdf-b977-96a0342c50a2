package com.nu.bom.core.technologies.steps.stamping.systemparameter

import com.nu.bom.core.manufacturing.annotations.*
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Text

@EntityType(Entities.SYSTEM_PARAMETER)
class SystemParameterStamping(name: String) : ManufacturingEntity(name) {

    @Input
    @ObjectView(ObjectView.SYSTEM, 1)
    fun system(): Text? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.SYSTEM, 5)
    fun maxCoilWidth(): Length? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.SYSTEM, 10)
    fun maxCoilLength(): Length? = null
}
