package com.nu.bom.core.technologies.steps.ict.cycletimestep

import com.nu.bom.core.manufacturing.annotations.*
import com.nu.bom.core.manufacturing.entities.CycleTimeStep
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.*
import java.math.BigDecimal

@EntityType(Entities.CYCLETIME_STEP)
class IctUnloadPanelCycleTimeStep(name: String) : ManufacturingEntity(name) {

    override val extends = CycleTimeStep(name)

    @Input
    val adjustmentRate = Rate(BigDecimal.ONE)

    fun time() = Time(15.0, TimeUnits.SECOND)
}
