package com.nu.bom.core.technologies.steps.normalizing.systemparameter

import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Weight

@EntityType(Entities.SYSTEM_PARAMETER)
class SystemParameterNormalizing(name: String) : ManufacturingEntity(name) {

    @Input
    fun system(): Text? = null

    @Input
    @ReadOnly
    @ObjectView(ObjectView.SYSTEM, 5)
    fun bulkMaterial(): Bool? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.KILOGRAM)
    @ObjectView(ObjectView.SYSTEM, 10)
    fun chargeWeight(): Weight? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.SYSTEM, 20)
    fun chargeFrameLength(): Length? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.SYSTEM, 30)
    fun chargeFrameWidth(): Length? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.SYSTEM, 40)
    fun chargeFrameHeight(): Length? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.SYSTEM, 50)
    fun lotHeatingSoaking(): Num? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.SYSTEM, 60)
    fun lotCooling(): Num? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.SYSTEM, 70)
    fun endomat(): Num? = null

    @Input
    @DefaultUnit(DefaultUnit.LITER)
    fun hardeningOil(): Num? = null

    @Input
    @DefaultUnit(DefaultUnit.LITER)
    fun propane(): Num? = null
}
