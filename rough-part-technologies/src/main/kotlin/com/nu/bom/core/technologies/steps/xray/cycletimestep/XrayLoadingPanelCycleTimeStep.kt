package com.nu.bom.core.technologies.steps.xray.cycletimestep

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.entities.CycleTimeStep
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import java.math.BigDecimal

@EntityType(Entities.CYCLETIME_STEP)
class XrayLoadingPanelCycleTimeStep(name: String) : ManufacturingEntity(name) {

    override val extends = CycleTimeStep(name)

    @Input
    val adjustmentRate = Rate(BigDecimal.ONE)

    fun time(): Time { return Time(15.0, TimeUnits.SECOND) }
}
