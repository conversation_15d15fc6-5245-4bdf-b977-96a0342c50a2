package com.nu.bom.core.technologies.lookups

import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeMagnet

data class MAGNShape(
    override val shapeId: String,
    override val shapeTechnologyGroup: String,
    override val tech: String,
    override val inputGroup: String,
    val magnetType: StepSubTypeMagnet,
) : LookupShape

val magnShapeReader: (row: List<String>) -> MAGNShape = { row ->
    MAGNShape(
        shapeId = row[0],
        shapeTechnologyGroup = row[1],
        tech = row[2],
        inputGroup = row[3],
        magnetType = StepSubTypeMagnet(StepSubTypeMagnet.Selection.valueOf(row[4])),
    )
}
