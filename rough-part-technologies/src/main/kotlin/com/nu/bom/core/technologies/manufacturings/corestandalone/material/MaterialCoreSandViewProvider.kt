package com.nu.bom.core.technologies.manufacturings.corestandalone.material

import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.specifics.material.MaterialViewProviderBase
import com.nu.bom.core.manufacturing.fieldTypes.MaterialViewConfig
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.FieldConfigFeDto
import org.springframework.stereotype.Service

@Service
object MaterialCoreSandViewProvider : MaterialViewProviderBase() {
    override val materialView = MaterialViewConfig.MaterialView.MATERIAL_CORE_SAND

    override val substanceImageName: String = "MaterialSand.png"

    override val materialSpecificCardCost: FieldConfigFeDto? = null
}
