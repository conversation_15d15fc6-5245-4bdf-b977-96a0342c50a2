package com.nu.bom.core.technologies.wizard

import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.toMetaInfoEntry
import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.AreaUnits
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.service.ExternalStorageService
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.service.ProjectService
import com.nu.bom.core.smf.FeUnfolded
import com.nu.bom.core.smf.FeUnfoldedGeometry
import com.nu.bom.core.tsetdel.TsetDelApiService
import com.nu.bom.core.tsetdel.TsetDelUtils
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.user.AccessCheckProvider
import com.tset.bom.clients.tsetdel.model.TsetDelPolygonUnionAreaRequest
import org.bson.types.ObjectId
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Mono

@RestController
@RequestMapping("/api/{projectId}/tsetdel")
class TsetDelController(
    private val accessCheckProvider: AccessCheckProvider,
    private val tsetDelApiService: TsetDelApiService,
    private val externalStorageService: ExternalStorageService,
    private val noCalcService: ExternalStorageService,
    private val projectService: ProjectService,
) {
    private fun getFeUnfolded(
        id: ObjectId,
        accessCheck: AccessCheck,
    ): Mono<FeUnfolded> {
        val accountId = accessCheck.asAccountId()
        return noCalcService.retrieve(id, accountId).map {
            it as? FeUnfolded
                ?: throw IllegalArgumentException("ObjectId does not correspond to a FeUnfolded.")
        }
    }

    @GetMapping("/projectedAreaAsPolygons/{projectedAreaAsPolygonsId}")
    fun getFeUnfolded(
        @PathVariable projectId: ObjectId,
        @PathVariable projectedAreaAsPolygonsId: ObjectId,
        @AuthenticationPrincipal jwt: Jwt?,
    ): Mono<FeUnfolded> =
        accessCheckProvider.doAs(jwt) {
            getFeUnfolded(projectedAreaAsPolygonsId, it)
        }

    data class GeometricFeaturesFromSketchForFe(
        val polygonUnionArea: FieldParameter,
        val sketchWidth: FieldParameter,
        val sketchHeight: FieldParameter,
    )

    @PostMapping("/polygonUnionAreaWidthHeight")
    fun polygonUnionAreaWidthHeight(
        @PathVariable projectId: ProjectId,
        @RequestBody feUnfoldedGeometries: List<FeUnfoldedGeometry>,
        @AuthenticationPrincipal jwt: Jwt?,
    ): Mono<GeometricFeaturesFromSketchForFe> =
        accessCheckProvider.doAs(jwt) {
            projectService.getProject(it, projectId).flatMap {
                val polygons = TsetDelUtils.feUnfoldedToTsetDelPolygons(feUnfoldedGeometries)
                tsetDelApiService
                    .polygonUnionArea(
                        projectId.toString(),
                        TsetDelPolygonUnionAreaRequest(
                            polygons = polygons,
                        ),
                    ).map {
                        GeometricFeaturesFromSketchForFe(
                            polygonUnionArea = temporaryProjectedArea(it.polygon_union_area),
                            sketchWidth = temporarySketchWidth(it.polygon_union_width),
                            sketchHeight = temporarySketchHeight(it.polygon_union_height),
                        )
                    }
            }
        }

    data class FeUnfoldedWithOriginalFieldParameter(
        val unfoldedPartField: FieldParameter,
        val currentSketchData: FeUnfolded,
    )

    @PostMapping("/save/projectedAreaAsPolygons")
    fun saveFeUnfolded(
        @PathVariable projectId: ObjectId,
        @RequestBody feUnfoldedWithOriginalFieldParameter: FeUnfoldedWithOriginalFieldParameter,
        @AuthenticationPrincipal jwt: Jwt?,
    ): Mono<FieldParameter> =
        accessCheckProvider.doAs(jwt) {
            val fieldParameter = feUnfoldedWithOriginalFieldParameter.unfoldedPartField
            val newSketch = feUnfoldedWithOriginalFieldParameter.currentSketchData
            if (!ObjectId.isValid(fieldParameter.value as String)) {
                throw IllegalArgumentException("unfoldedPartField's value is required to be an ObjectId.")
            }
            getFeUnfolded(ObjectId(fieldParameter.value as String), it).flatMap { originalSketch ->
                if (originalSketch == newSketch) {
                    Mono.just(fieldParameter)
                } else {
                    externalStorageService.persist(newSketch, it.asAccountId()).map { id ->
                        fieldParameter.copy(
                            value = id.toHexString(),
                            source = FieldResult.SOURCE.I.toString(),
                        )
                    }
                }
            }
        }

    private fun temporaryProjectedArea(value: Double): FieldParameter {
        val area = Area(value, AreaUnits.QMM)
        return FieldParameter(
            name = "temporaryProjectedArea",
            type = "Area",
            unit = AreaUnits.QM.toString(),
            value = area.inQm,
            denominatorUnit = null,
            metaInfo = mapOf(toMetaInfoEntry(DefaultUnit.QCM), ReadOnly.META_INFO to true),
            valueInDefaultUnit = area.inQcm,
        )
    }

    private fun temporarySketchWidth(value: Double): FieldParameter {
        val width = Length(value, LengthUnits.MILLIMETER)
        return FieldParameter(
            name = "temporarySketchWidth",
            type = "Length",
            unit = LengthUnits.METER.toString(),
            value = width.inMeter,
            denominatorUnit = null,
            metaInfo = mapOf(toMetaInfoEntry(DefaultUnit.MILLIMETER), ReadOnly.META_INFO to true),
            valueInDefaultUnit = width.inMillimeter,
        )
    }

    private fun temporarySketchHeight(value: Double): FieldParameter {
        val height = Length(value, LengthUnits.MILLIMETER)
        return FieldParameter(
            name = "temporarySketchHeight",
            type = "Length",
            unit = LengthUnits.METER.toString(),
            value = height.inMeter,
            denominatorUnit = null,
            metaInfo = mapOf(toMetaInfoEntry(DefaultUnit.MILLIMETER), ReadOnly.META_INFO to true),
            valueInDefaultUnit = height.inMillimeter,
        )
    }
}
