Merge branch '%{source_branch}' into '%{target_branch}'

# What does this MR Do?

<details>
<summary>Enter any helpful description here if necessary</summary>

_For merge requests towards `master` please add the following information:_

- _Ticket/Epic link_
- _link to other services that should be merged with this merge request_

</details>

## Check Lists

<details>
<summary> Reviewer</summary>

- [ ] :triangular_flag_on_post: Quality is fine?
  - [ ] Commits adhere to policies
  - [ ] All newly introduced variable names are understandable
  - [ ] No duplicated code was introduced
  - [ ] Commit does not contain commented code - if we don’t need it, delete it (you can find it in git history later)
  - [ ] Cyclomatic complexity is fine (avoid "huge functions")
- [ ] :triangular_flag_on_post: Quality Assurance is fine?
  - [ ] New functionality is tested?
- [ ] :triangular_flag_on_post: Security is fine?
  - [ ] `AccessCheck` object is used on Controller level
    - [ ] `accessCheck` is passed down, and not summoned out of thin air.
  - [ ] All persisted objects have 'accountId' in it
- [ ] :triangular_flag_on_post: Backwards compatibility is fine? 
  - [ ] Only nullable fields are added to MongoDB entities - otherwise we can't load old data
  - [ ] Have been changes made that need a migration?
- [ ] **if** public API has been modified
  - [ ] [openapi.yaml](../../neumann-core/src/main/resources/public-api/v1/openapi.yaml) was adapted accordingly (deployed on https://feature-branch.cost.feature.tset.cloud/webjars/swagger-ui/index.html#/; search for `/v1/openapi.yaml`)
  - [ ] [API test repository](https://git.tset.cloud/qa/nu-api-test) was adapted accordingly
</details>
