apiVersion: batch/v1
kind: Job
metadata:
  name: "{{ include "nu-bom-kotlin.name" . }}-job"
  annotations:
{{ include "nu-bom-kotlin.annotations" . | indent 4 }}
    helm.sh/hook: pre-install,pre-upgrade
    helm.sh/hook-weight: "-1"
  labels:
{{ include "nu-bom-kotlin.labels" . | indent 4 }}
{{ include "datadog.labels" . | indent 4 }}
    tset.com/use-db: "yes"
spec:
  ttlSecondsAfterFinished: 3600
  activeDeadlineSeconds: 2400
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
{{ include "datadog.labels" . | indent 8 }}
    spec:
      serviceAccountName: service-client
      containers:
      - name: "{{ .Chart.Name }}"
        image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        env:
        - name: DD_LOGS_INJECTION
          value: "true"

        - name: DD_TRACE_ENABLED
          value: {{ include "datadog-tracing" . | quote }}

        - name: CLAMAV_HOST
          valueFrom:
            secretKeyRef:
              name: clamav
              key: host

        - name: CLAMAV_PORT
          valueFrom:
            secretKeyRef:
              name: clamav
              key: port

        - name: JAVA_OPTS
          value: {{ .Values.java.opts }}

{{- if .Values.mongodb.hosted }}
        - name: MONGODB_SCHEME
          value: "mongodb://"

        - name: MONGODB_HOST
          value: {{ include "nu-bom-kotlin.name" .}}.{{ .Release.Namespace }}.db.local

        - name: MONGODB_OPTIONS
          value: "ssl=false&authMechanism=SCRAM-SHA-256&authSource=admin"

{{- else }}
        - name: MONGODB_SCHEME
          value: "mongodb+srv://"

        - name: MONGODB_HOST
          valueFrom:
            secretKeyRef:
              name: mongodb
              key: host

        - name: MONGODB_OPTIONS
          valueFrom:
            secretKeyRef:
              name: mongodb
              key: options

{{- end }}
        - name: MONGODB_USERNAME
          valueFrom:
            secretKeyRef:
              name: mongodb
              key: admin-username

        - name: MONGODB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mongodb
              key: admin-password

        - name: MONGODB_DATABASE
          value: {{ .Values.mongodb.db_name }}

        - name: SPRING_PROFILES_ACTIVE
          value: cloud,data-deployment

        - name: SPRING_MAIN_BANNER_MODE
          value: "off"

        - name: SPRING_RABBITMQ_USERNAME
          valueFrom:
            secretKeyRef:
              name: rabbitmq
              key: username

        - name: SPRING_RABBITMQ_PASSWORD
          valueFrom:
            secretKeyRef:
              name: rabbitmq
              key: password

        - name: SPRING_RABBITMQ_HOST
          valueFrom:
            secretKeyRef:
              name: rabbitmq
              key: host

        - name: AWS_S3_FILES_BUCKET
          valueFrom:
            secretKeyRef:
              name: nu-bom-kotlin-s3-files
              key: bucket-name

        - name: AWS_S3_FILES_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: nu-bom-kotlin-s3-files
              key: access-key

        - name: AWS_S3_FILES_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: nu-bom-kotlin-s3-files
              key: secret-key

        - name: AWS_S3_REPORTS_BUCKET
          valueFrom:
            secretKeyRef:
              name: nu-bom-kotlin-s3-reports
              key: bucket-name

        - name: AWS_S3_REPORTS_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: nu-bom-kotlin-s3-reports
              key: access-key

        - name: AWS_S3_REPORTS_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: nu-bom-kotlin-s3-reports
              key: secret-key

        - name: AWS_S3_EXPORTS_BUCKET
          valueFrom:
            secretKeyRef:
              name: nu-bom-kotlin-s3-exports
              key: bucket-name

        - name: AWS_S3_EXPORTS_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: nu-bom-kotlin-s3-exports
              key: access-key

        - name: AWS_S3_EXPORTS_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: nu-bom-kotlin-s3-exports
              key: secret-key

        - name: AWS_S3_BLOBS_BUCKET
          valueFrom:
            secretKeyRef:
              name: nu-bom-kotlin-s3-blobs
              key: bucket-name

        - name: AWS_S3_BLOBS_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: nu-bom-kotlin-s3-blobs
              key: access-key

        - name: AWS_S3_BLOBS_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: nu-bom-kotlin-s3-blobs
              key: secret-key

        - name: PREDICTION_API_SERVICES_DCA_URL
          value:  "{{ .Values.prediction.dca.url }}.{{ .Release.Namespace }}.svc.cluster.local"

        - name: PREDICTION_API_SERVICES_INJ_URL
          value:  "{{ .Values.prediction.inj.url }}.{{ .Release.Namespace }}.svc.cluster.local"

        - name: PREDICTION_API_SERVICES_SAND_URL
          value:  "{{ .Values.prediction.sand.url }}.{{ .Release.Namespace }}.svc.cluster.local"

        - name: NU_GENERIC_DISABLE_CLEANUP
          value:  "true"

        - name: KC_SERVER_URL
          valueFrom:
            secretKeyRef:
              name: keycloak
              key: base-url

        - name: KC_REALM
          valueFrom:
            secretKeyRef:
              name: nu-backend-client
              key: realm

        - name: KC_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: keycloak-backend-client
              key: client-id

        - name: KC_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: nu-backend-client
              key: client-secret

        - name: NU_SECURITY_REALM_HOST
          valueFrom:
            secretKeyRef:
              name: keycloak
              key: host

        - name: SPRING_ZIPKIN_ENABLED
          value: "false"

{{- if .Values.feature.name }}
        - name: SPRING_BOOT_ADMIN_CLIENT_INSTANCE_METADATA_TAGS_FEATURE
          value: {{ include "nu-bom-kotlin.feature-name" . }}
{{- end }}

      restartPolicy: Never
  backoffLimit: 4
