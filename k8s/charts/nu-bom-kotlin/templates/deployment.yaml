apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "nu-bom-kotlin.name" . }}
  annotations:
{{ include "nu-bom-kotlin.annotations" . | indent 4 }}
  labels:
{{ include "nu-bom-kotlin.labels" . | indent 4 }}
{{ include "datadog.labels" . | indent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 50%
  revisionHistoryLimit: 1
  selector:
    matchLabels:
{{ include "nu-bom-kotlin.selectorLabels" . | indent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
{{ include "nu-bom-kotlin.selectorLabels" . | indent 8 }}
{{ include "datadog.labels" . | indent 8 }}
    spec:
      serviceAccountName: service-client
      dnsConfig:
        options:
          - name: ndots
            value: "1"
{{- if .Values.node }}
      nodeSelector:
        beta.kubernetes.io/instance-type: {{ .Values.node }}
{{- end }}
      containers:
      - name: {{ .Chart.Name }}
        image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        ports:
          - name: http
            containerPort: 8080
            protocol: TCP
          - name: jmx
            containerPort: 1234
            protocol: TCP
          - name: jvmdebug
            containerPort: 5005
            protocol: TCP
        livenessProbe:
          httpGet:
            port: 8081
            path: /actuator/health/liveness
          initialDelaySeconds: 60
          failureThreshold: 20
          periodSeconds: 15
          timeoutSeconds: 10
        readinessProbe:
          httpGet:
            port: 8081
            path: /actuator/health/readiness
          initialDelaySeconds: 90
          failureThreshold: 20
          periodSeconds: 15
        resources:
          requests:
            cpu: {{ .Values.pod_request_cpu | quote }}
            memory: {{ .Values.pod_request_memory | quote }}
          limits:
            memory: {{ .Values.pod_limit_memory | quote }}
        env:
        - name: DD_LOGS_INJECTION
          value: "true"

        - name: DD_TRACE_ENABLED
          value: {{ include "datadog-tracing" . | quote }}

        - name: DD_TRACE_OTEL_ENABLED
          value: "false"

        - name: AWS_HEAPDUMP_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: debug-dump
              key: aws-access-key-id

        - name: AWS_HEAPDUMP_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: debug-dump
              key: aws-secret-access-key

        - name: AWS_HEAPDUMP_BUCKET
          valueFrom:
            secretKeyRef:
              name: debug-dump
              key: aws-bucket

        - name: CLAMAV_HOST
          valueFrom:
            secretKeyRef:
              name: clamav
              key: host

        - name: CLAMAV_PORT
          valueFrom:
            secretKeyRef:
              name: clamav
              key: port

        - name: CLAMAV_TIMEOUT
          valueFrom:
            secretKeyRef:
              name: clamav
              key: timeout

        - name: JAVA_MEMORY_OPTS
          value: {{ .Values.java.memory_opts }}

        - name: NAMESPACE
          value: {{ .Release.Namespace }}

{{- if .Values.mongodb.hosted }}
        - name: MONGODB_SCHEME
          value: "mongodb://"

        - name: MONGODB_HOST
          value: {{ include "nu-bom-kotlin.name" .}}.{{ .Release.Namespace }}.db.local

        - name: MONGODB_OPTIONS
          value: "ssl=false&authMechanism=SCRAM-SHA-256&authSource=admin"

{{- else }}
        - name: MONGODB_SCHEME
          value: "mongodb+srv://"

        - name: MONGODB_HOST
          valueFrom:
            secretKeyRef:
              name: mongodb
              key: host

        - name: MONGODB_OPTIONS
          valueFrom:
            secretKeyRef:
              name: mongodb
              key: options

{{- end }}
        - name: MONGODB_USERNAME
          valueFrom:
            secretKeyRef:
              name: mongodb
              key: admin-username

        - name: MONGODB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mongodb
              key: admin-password

        - name: MONGODB_DATABASE
          value: {{ .Values.mongodb.db_name }}

        - name: SPRING_PROFILES_ACTIVE
          value: {{ .Values.spring.profiles }}

        - name: SPRING_MAIN_BANNER_MODE
          value: "off"

        - name: SPRING_REDIS_HOST
          valueFrom:
            secretKeyRef:
              name: redis
              key: host

        - name: SPRING_REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis
              key: password

        - name: SPRING_RABBITMQ_USERNAME
          valueFrom:
            secretKeyRef:
              name: rabbitmq
              key: username

        - name: SPRING_RABBITMQ_PASSWORD
          valueFrom:
            secretKeyRef:
              name: rabbitmq
              key: password

        - name: SPRING_RABBITMQ_HOST
          valueFrom:
            secretKeyRef:
              name: rabbitmq
              key: host

        - name: RABBITMQ_NAME
          value: {{ .Values.rabbitmq.name }}

        - name: ELASTICSEARCH_HOST
          valueFrom:
            secretKeyRef:
              name: search
              key: host

        - name: ELASTICSEARCH_NAME
          value: {{ .Values.elasticsearch.name }}

        - name: REDIS_NAME
          value: {{ .Values.redis.name }}

        - name: NU_GENERIC_DISABLE_CLEANUP
          value: "true"

        - name: AWS_S3_FILES_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: nu-bom-kotlin-s3-files
              key: access-key

        - name: AWS_S3_FILES_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: nu-bom-kotlin-s3-files
              key: secret-key


{{- if and (.Values.feature.name) (ne .Values.feature.name "develop") }}
        - name: AWS_S3_FILES_BUCKET
          value: {{ include "nu-bom-kotlin.name" . }}.files
{{- else }}
        - name: AWS_S3_FILES_BUCKET
          valueFrom:
            secretKeyRef:
              name: nu-bom-kotlin-s3-files
              key: bucket-name
{{- end }}


{{- if and (.Values.feature.name) (ne .Values.feature.name "develop") }}
        - name: AWS_S3_REPORTS_BUCKET
          value:  {{ include "nu-bom-kotlin.name" . }}.reports
{{- else }}
        - name: AWS_S3_REPORTS_BUCKET
          valueFrom:
            secretKeyRef:
              name: nu-bom-kotlin-s3-reports
              key: bucket-name
{{- end }}
        - name: AWS_S3_REPORTS_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: nu-bom-kotlin-s3-reports
              key: access-key

        - name: AWS_S3_REPORTS_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: nu-bom-kotlin-s3-reports
              key: secret-key

        - name: AWS_S3_EXPORTS_BUCKET
          valueFrom:
            secretKeyRef:
              name: nu-bom-kotlin-s3-exports
              key: bucket-name

        - name: AWS_S3_EXPORTS_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: nu-bom-kotlin-s3-exports
              key: access-key

        - name: AWS_S3_EXPORTS_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: nu-bom-kotlin-s3-exports
              key: secret-key

{{- if and (.Values.feature.name) (ne .Values.feature.name "develop") }}
        - name: AWS_S3_BLOBS_BUCKET
          value:  {{ include "nu-bom-kotlin.name" . }}.blobs
{{- else }}
        - name: AWS_S3_BLOBS_BUCKET
          valueFrom:
            secretKeyRef:
              name: nu-bom-kotlin-s3-blobs
              key: bucket-name
{{- end }}
        - name: AWS_S3_BLOBS_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: nu-bom-kotlin-s3-blobs
              key: access-key

        - name: AWS_S3_BLOBS_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: nu-bom-kotlin-s3-blobs
              key: secret-key

        - name: PREDICTION_API_SERVICES_DCA_URL
          value:  "{{ .Values.prediction.dca.url }}.{{ .Release.Namespace }}.svc.cluster.local"

        - name: PREDICTION_API_SERVICES_INJ_URL
          value:  "{{ .Values.prediction.inj.url }}.{{ .Release.Namespace }}.svc.cluster.local"

        - name: PREDICTION_API_SERVICES_SAND_URL
          value:  "{{ .Values.prediction.sand.url }}.{{ .Release.Namespace }}.svc.cluster.local"

        - name: KC_SERVER_URL
          valueFrom:
            secretKeyRef:
              name: keycloak
              key: internal-url

        - name: KC_REALM
          valueFrom:
            secretKeyRef:
              name: nu-backend-client
              key: realm

        - name: KC_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: nu-backend-client
              key: client-id

        - name: KC_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: nu-backend-client
              key: client-secret

        - name: NU_SECURITY_REALM_HOST
          valueFrom:
            secretKeyRef:
              name: keycloak
              key: host

{{- if .Values.feature.name }}
        - name: SPRING_BOOT_ADMIN_CLIENT_INSTANCE_METADATA_TAGS_FEATURE
          value: {{ include "nu-bom-kotlin.feature-name" . }}
{{- end }}

        - name: SPRING_BOOT_ADMIN_CLIENT_INSTANCE_SERVICE_BASE_URL
          value: "http://{{ include "nu-bom-kotlin.name" . }}.{{ .Release.Namespace }}.svc.cluster.local:{{ .Values.service.port }}"

        - name: SPRING_BOOT_ADMIN_CLIENT_INSTANCE_MANAGEMENT_BASE_URL
          value: "http://{{ include "nu-bom-kotlin.name" .}}.{{ .Release.Namespace }}.svc.cluster.local:{{ .Values.service.management }}"

        - name: SPRING_BOOT_ADMIN_CLIENT_INSTANCE_METADATA_TAGS_ENVIRONMENT
          value: {{ .Release.Namespace }}

        - name: SPRING_BOOT_ADMIN_CLIENT_INSTANCE_MANAGEMENT_CONTEXT_PATH
          value: "/actuator"

        - name: SPRING_BOOT_ADMIN_CLIENT_URL
          valueFrom:
            secretKeyRef:
              name: cluster-admin
              key: internal-url

        - name: SPRING_BOOT_ADMIN_CLIENT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: cluster-admin
              key: password

        - name: SPRING_ZIPKIN_ENABLED
          value: "false"

        - name: USERTSET_URI
          valueFrom:
            secretKeyRef:
              name: jira-user
              key: uri

        - name: USERTSET_USERNAME
          valueFrom:
            secretKeyRef:
              name: jira-user
              key: username

        - name: USERTSET_PASSWORD
          valueFrom:
            secretKeyRef:
              name: jira-user
              key: password

        - name: USERTSET_ISSUES_BUG_ID
          valueFrom:
            secretKeyRef:
              name: usertset
              key: bug-id

        - name: USERTSET_ISSUES_BUG_PROJECT
          valueFrom:
            secretKeyRef:
              name: usertset
              key: bug-project

        - name: USERTSET_ISSUES_FEATURE_ID
          valueFrom:
            secretKeyRef:
              name: usertset
              key: feature-id

        - name: USERTSET_ISSUES_FEATURE_PROJECT
          valueFrom:
            secretKeyRef:
              name: usertset
              key: feature-project

        - name: USERTSET_ISSUES_FEEDBACK_ID
          valueFrom:
            secretKeyRef:
              name: usertset
              key: feedback-id

        - name: USERTSET_ISSUES_FEEDBACK_PROJECT
          valueFrom:
            secretKeyRef:
              name: usertset
              key: feedback-project

        - name: FEATUREBASE_TOKEN
          valueFrom:
            secretKeyRef:
              name: featurebase
              key: secret

        - name: SERVER_ERROR_INCLUDE_MESSAGE
          valueFrom:
            secretKeyRef:
              name: server-error
              key: include-message

        - name: SERVER_ERROR_INCLUDE_BINDING_ERRORS
          valueFrom:
            secretKeyRef:
              name: server-error
              key: include-binding-errors

        - name: SERVER_ERROR_INCLUDE_STACKTRACE
          valueFrom:
            secretKeyRef:
              name: server-error
              key: include-stacktrace

        - name: SERVER_ERROR_INCLUDE_EXCEPTION
          valueFrom:
            secretKeyRef:
              name: server-error
              key: include-exception

        - name: NEXAR_API_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: nexar
              key: client-id

        - name: NEXAR_API_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: nexar
              key: client-secret

        - name: GEOM_SHAPES_S3_BUCKET
          valueFrom:
            secretKeyRef:
              name: geom-shapes-s3
              key: bucket-name

        - name: ZENDESK_API_KEY
          valueFrom:
            secretKeyRef:
              name: zendesk
              key: api-key

        - name: ZENDESK_SHARED_SECRET
          valueFrom:
            secretKeyRef:
              name: zendesk
              key: shared-secret

{{- if .Values.feature.name }}
        - name: GEOM_SHAPES_S3_ENV
          value: {{ .Values.feature.name }}
{{- else }}
        - name: GEOM_SHAPES_S3_ENV
          value: {{ .Release.Namespace }}
{{- end }}

        - name: GITLAB_TOKEN
          valueFrom:
            secretKeyRef:
              name: gitlab
              key: gitbuster-access-token-readonly

        - name: DD_HTTP_CLIENT_ERROR_STATUSES
          value: "499"
