{{- if .Values.feature.name }}
{{- range .Values.s3.buckets }}
---
apiVersion: s3.aws.crossplane.io/v1beta1
kind: Bucket
metadata:
  name: "{{ include "nu-bom-kotlin.name" $ }}.{{ . }}"
  labels:
{{ include "nu-bom-kotlin.labels" $ | indent 4 }}
spec:
  deletionPolicy: Orphan
  forProvider:
    acl: private
    locationConstraint: eu-central-1
    objectOwnership: BucketOwnerEnforced
    publicAccessBlockConfiguration:
      blockPublicAcls: true
      blockPublicPolicy: true
      ignorePublicAcls: true
      restrictPublicBuckets: true
    policy:
      version: "2012-10-17"
      statements:
      - action:
        - s3:*
        effect: Allow
        principal:
          awsPrincipals:
            - iamUserArn: "arn:aws:iam::004274462615:user/develop/nu-bom-kotlin-s3-develop"
            - iamUserArn: "arn:aws:iam::734046496332:user/production/nu-bom-kotlin-s3-production"
            - iamUserArn: "arn:aws:iam::854763838162:user/tc/nu-bom-kotlin-s3-tc"
            - iamUserArn: "arn:aws:iam::123233723471:user/zf/nu-bom-kotlin-s3-zf"
            - iamUserArn: "arn:aws:iam::671546295150:user/bmw/nu-bom-kotlin-s3-bmw"
            - iamUserArn: "arn:aws:iam::004274462615:user/crossplane"
        resource:
          - arn:aws:s3:::{{ include "nu-bom-kotlin.name" $ }}.{{ . }}
          - arn:aws:s3:::{{ include "nu-bom-kotlin.name" $ }}.{{ . }}/*
  providerConfigRef:
    name: default
{{- end }}
{{- end }}
