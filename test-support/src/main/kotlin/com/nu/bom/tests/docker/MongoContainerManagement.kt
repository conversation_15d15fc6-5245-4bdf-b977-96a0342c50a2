package com.nu.bom.tests.docker

import org.slf4j.LoggerFactory
import org.testcontainers.containers.Container.ExecResult
import org.testcontainers.containers.MongoDBContainer
import org.testcontainers.utility.MountableFile
import java.nio.file.Path
import kotlin.io.path.absolutePathString
import kotlin.io.path.isReadable
import kotlin.io.path.isRegularFile

class MongoContainerManagement private constructor(
    private val database: MongoDBContainer,
) {
    fun getMongoUrl(): String = database.getReplicaSetUrl(DB_NAME) + "?minPoolSize=0&maxPoolSize=3&maxIdleTimeMS=10000"

    fun close() {
        logger.debug(printInfo())
        database.stop()
    }

    fun printInfo(): String {
        return "${database.containerId}: ${database.containerName}\n" +
            "URL: ${getMongoUrl()}"
    }

    fun executeDump() {
        log(database.execInContainer("mongodump", "--gzip", "--archive=/$DUMP_FILE"))
        database.copyFileFromContainer("/$DUMP_FILE", dumpFile().first.absolutePathString())
        logger.info("dumped to ${dumpFile().first.toAbsolutePath()}")
    }

    companion object {
        private val logger = LoggerFactory.getLogger(MongoContainerManagement::class.java)
        private var instance: MongoContainerManagement? = null
        private var masterDataInstance: MongoContainerManagement? = null
        private const val DUMP_FILE = "mongo-masterdata-export.archive"
        private const val MONGO_IMAGE = "mongo:7.0.18"
        const val DB_NAME = "test"

        private fun create(
            db: MongoDBContainer,
            initializer: (MongoDBContainer) -> Unit = { },
        ): MongoContainerManagement {
            logger.error("🚀 - Launching Mongo")
            try {
                db.start()
                initializer(db)
                return MongoContainerManagement(db)
            } catch (e: Exception) {
                logger.error("Unable to start Mongo: ${e.message}", e)
                logger.error(db.logs)
                throw e
            }
        }

        @Synchronized
        fun getInstance(): MongoContainerManagement {
            if (instance == null) {
                logger.debug("Creating new MongoDB container...")
                instance = create(MongoDBContainer(MONGO_IMAGE).withReuse(true))
            } else {
                logger.debug("Returning existing MongoDB instance...")
                logger.debug(instance!!.printInfo())
            }
            return instance!!
        }

        private fun dumpFile(): Pair<Path, Boolean> {
            val tmpDir = System.getProperty("java.io.tmpdir") ?: "/tmp"
            val path = Path.of(tmpDir, DUMP_FILE)
            return path to (path.isRegularFile() && path.isReadable())
        }

        private fun log(execResult: ExecResult): ExecResult {
            logger.info("output: \n${execResult.stdout}")
            if (execResult.exitCode != 0) {
                logger.error("error: ${execResult.stderr}")
            }
            return execResult
        }

        @Synchronized
        fun getMasterDataInstance(): MongoContainerManagement {
            if (masterDataInstance == null) {
                logger.info("Creating new MongoDB container for MasterData...")
                masterDataInstance =
                    create(
                        MongoDBContainer(MONGO_IMAGE).withReuse(true),
                    ) { db ->
                        val (path, exists) = dumpFile()
                        if (exists) {
                            logger.error("Dump file exists: ${path.toAbsolutePath()} -> importing into the container")
                            db.copyFileToContainer(MountableFile.forHostPath(path), "/$DUMP_FILE")
                            log(db.execInContainer("mongorestore", "--gzip", "--archive=/$DUMP_FILE"))
                            logger.error("💫 mongorestore finished!")
                        } else {
                            logger.error("Dump file not found at ${path.toAbsolutePath()}")
                        }
                    }
            }
            return masterDataInstance!!
        }

        fun saveSnapshot() {
            masterDataInstance?.let { db ->
                val (path, exists) = dumpFile()
                if (!exists) {
                    logger.error("saving mongoDB dump to {}", path.toAbsolutePath())
                    db.executeDump()
                }
            }
        }

        @JvmStatic
        fun main(args: Array<String>) {
            val instance = getInstance()
            println("press enter to exit!")
            readln()
            instance.close()
        }
    }
}
