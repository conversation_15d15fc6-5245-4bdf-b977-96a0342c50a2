package com.nu.bom.tests.docker

import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.testcontainers.containers.GenericContainer
import org.testcontainers.containers.Network
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.containers.output.Slf4jLogConsumer
import org.testcontainers.containers.wait.strategy.Wait
import org.testcontainers.utility.DockerImageName
import java.time.Duration

private val logger = LoggerFactory.getLogger(MasterdataContainerManagement::class.java)

class MasterdataContainerManagement private constructor(
    private val network: Network,
    private val database: PostgreSQLContainer<*>,
    private val masterdata: GenericContainer<*>,
) {
    fun getJdbcUrl(): String = "jdbc:postgresql://${database.host}:${database.firstMappedPort}/${database.databaseName}"

    fun getMasterdataUrl(): String = "http://${masterdata.host}:${masterdata.getMappedPort(8104)}"

    fun close() {
        masterdata.stop()
        database.stop()
        network.close()
    }

    companion object {
        const val SCHEMA_TENANT_NAME = "nbk_integration_tests"

        private var instance: MasterdataContainerManagement? = null

        private fun create(
            schema: String,
            tagName: String,
        ): MasterdataContainerManagement {
            val network = TestcontainerUtil.createReusableNetwork("masterdata")
            val db = buildPostgresContainer(network)
            logger.info("🚀 - Launching MD Postgres")
            try {
                db.start()
            } catch (e: Exception) {
                logger.info("Unable to start MD Postgres: ${e.message}", e)
                logger.info(db.logs)
                throw e
            }
            val masterdata = buildMasterdataContainer(network, schema, tagName)
            logger.info("🚀🚀 - Launching Masterdata")
            try {
                masterdata.start()
                val logConsumer = Slf4jLogConsumer(logger)
                masterdata.followOutput(logConsumer)
                val setup = MasterdataContainerManagement(network, db, masterdata)
                logger.info("🛰 - MD PostgreSQL runs at ${setup.getJdbcUrl()}")
                logger.info("🛰🛰 - Masterdata runs at ${setup.getMasterdataUrl()}")
                return setup
            } catch (e: Exception) {
                logger.info("Unable to start Masterdata: ${e.message}", e)
                logger.info(masterdata.logs)
                throw e
            }
        }

        // maybe it makes sense to share the same DB with Bomrads to improve the startup time?
        private fun buildPostgresContainer(network: Network): PostgreSQLContainer<*> {
            return PostgreSQLContainer("postgres:14.6")
                .withUsername("postgres")
                .withPassword("postgres")
                .withDatabaseName("data")
                .withNetwork(network)
                .withNetworkAliases("masterdata-postgresql")
                .withReuse(true)
        }

        private fun buildMasterdataContainer(
            networkConfig: Network,
            schema: String,
            tagName: String,
        ): GenericContainer<*> {
            return GenericContainer(
                DockerImageName.parse("684712464887.dkr.ecr.eu-central-1.amazonaws.com/nu-masterdata:$tagName"),
            ).withNetwork(networkConfig)
                // to disable auto provisioning of tenants on masterdata service set NU-MASTERDATA_AUTOPROVISION -> false
                .withNetworkAliases("masterdata")
                .withExposedPorts(8104)
                .withEnv("LOGGING_LEVEL_ROOT", "INFO")
                .withEnv("LOGGING_LEVEL_COM_TSET", "INFO")
                .withEnv("SPRING_PROFILES_ACTIVE", "dev,local,nbk-integration-test")
                .withEnv("SPRING_ZIPKIN_ENABLED", "false")
                .withEnv("POSTGRES_HOST", "masterdata-postgresql")
                .withEnv("POSTGRES_PORT", "5432")
                .withEnv("POSTGRES_SCHEMA", "public")
                .withEnv("POSTGRES_DATABASE", "data")
                .withEnv("POSTGRES_USERNAME", "postgres")
                .withEnv("POSTGRES_PASSWORD", "postgres")
                .withEnv("NU-MASTERDATA_DEFAULTTENANT", schema)
                .withEnv("SERVER_PORT", "8104")
                .withEnv("JAVA_MEMORY_OPTS", "-Xms256m -Xmx2048m")
                .withEnv("MANAGEMENT_SERVER_PORT", "8104")
                .withImagePullPolicy { true }
                .withReuse(true)
                .waitingFor(
                    // without this we might start using masterdata before all migrations are finished
                    Wait.forHttp("/management/health")
                        .forStatusCode(HttpStatus.OK.value()).withStartupTimeout(Duration.ofSeconds(240)),
                )
        }

        @Synchronized
        fun getInstance(tagName: String): MasterdataContainerManagement {
            if (instance == null) {
                logger.info("Creating new Masterdata container {}", tagName)
                instance = create(SCHEMA_TENANT_NAME, tagName)
            }
            return instance!!
        }

        @JvmStatic
        fun main(args: Array<String>) {
            val instance = getInstance("latest")
            println("press enter to exit!")
            readln()
            instance.close()
        }
    }
}
