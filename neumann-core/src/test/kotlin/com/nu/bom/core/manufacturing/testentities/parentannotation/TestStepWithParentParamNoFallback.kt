package com.nu.bom.core.manufacturing.testentities.parentannotation

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Num

@EntityType(Entities.MANUFACTURING_STEP)
class TestStepWithParentParamNoFallback(name: String) : ManufacturingEntity(name) {

    @Input
    fun stepParam(): Num = throw IllegalArgumentException("missing.input.stepParam")

    fun combineWithPrevious(
        @Parent(name = Entities.MANUFACTURING_STEP, fallback = Entities.NONE)
        stepParam: Num
    ): Num {
        return stepParam + 0.07
    }
}
