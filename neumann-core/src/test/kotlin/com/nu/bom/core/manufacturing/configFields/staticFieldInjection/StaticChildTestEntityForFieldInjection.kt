package com.nu.bom.core.manufacturing.configFields.staticFieldInjection

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.entities.BaseEntityFields
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.testentities.TestEntity

@TestEntity
@EntityType(Entities.CONSUMABLE)
class StaticChildTestEntityForFieldInjection(
    name: String,
) : BaseManufacturing(name) {
    override val extends = BaseEntityFields(name)

    // region business logic

    @Input
    fun inputCost(): Money = Money(5.0.toBigDecimal())

    @Input
    fun rate(): Rate = Rate(0.05.toBigDecimal())

    // endregion
}
