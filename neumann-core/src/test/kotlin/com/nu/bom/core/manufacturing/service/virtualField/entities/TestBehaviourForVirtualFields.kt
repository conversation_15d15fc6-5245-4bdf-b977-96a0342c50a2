package com.nu.bom.core.manufacturing.service.virtualField.entities

import com.nu.bom.core.manufacturing.annotations.ChildLoadingTrigger
import com.nu.bom.core.manufacturing.annotations.Children
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.entities.BomNodeReference
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.testentities.TestEntity
import java.math.BigDecimal

@TestEntity
@EntityType(Entities.NONE)
class TestBehaviourForVirtualFields(
    name: String,
) : ManufacturingEntity(name),
    BomNodeReference {
    fun someFieldToAggregateToParent(
        @Children(Entities.MANUFACTURING) someFieldToAggregateToParent: List<Money>?,
    ): Money = Money(someFieldToAggregateToParent?.sumOf { it.res } ?: BigDecimal.ZERO)

    @Input
    @ChildLoadingTrigger
    fun someFieldToPropagateToChild(): QuantityUnit = QuantityUnit(0.0)
}
