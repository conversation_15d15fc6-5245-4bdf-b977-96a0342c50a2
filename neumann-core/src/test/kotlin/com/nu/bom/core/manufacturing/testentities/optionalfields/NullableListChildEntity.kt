package com.nu.bom.core.manufacturing.testentities.optionalfields

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Parents
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Text

@EntityType(Entities.MANUFACTURING_STEP)
class NullableListChildEntity(name: String) : ManufacturingEntity(name) {

    fun calcField(@Parents(Entities.MANUFACTURING) calcField: List<Num>?): Num? = calcField?.first()

    fun inputField(@Parents(Entities.MANUFACTURING) inputField: List<Text>?): Text? = inputField?.first()

    fun dependOnCalcField(@Parents(Entities.MANUFACTURING) dependOnCalcField: List<Num>?): Num? = dependOnCalcField?.first()

    fun dependOnInputField(@Parents(Entities.MANUFACTURING) dependOnInputField: List<Text>?): Text? = dependOnInputField?.first()

    fun inputWithParam(@Parents(Entities.MANUFACTURING) inputWithParam: List<Text>?): Text? = inputWithParam?.first()

    fun valCalcField(@Parents(Entities.MANUFACTURING) valCalcField: List<Num>?): Num? = valCalcField?.first()

    fun valInputField(@Parents(Entities.MANUFACTURING) valInputField: List<Num>?): Num? = valInputField?.first()
}
