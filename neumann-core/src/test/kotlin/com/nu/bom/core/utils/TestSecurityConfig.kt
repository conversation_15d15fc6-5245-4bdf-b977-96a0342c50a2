package com.nu.bom.core.utils

import com.nimbusds.jose.crypto.MACSigner
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.security.config.web.server.ServerHttpSecurity
import org.springframework.security.oauth2.jose.jws.MacAlgorithm
import org.springframework.security.oauth2.jwt.NimbusReactiveJwtDecoder
import org.springframework.security.web.server.SecurityWebFilterChain

@Configuration
class TestSecurityConfig {

    val signer = MACSigner("0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef".toByteArray())

    @MockBean
    @Qualifier("configureLocalSecurity")
    private lateinit var localSecurityConfig: SecurityWebFilterChain

    @Bean
    fun configureTestSecurity(http: ServerHttpSecurity): SecurityWebFilterChain {

        http.authorizeExchange().anyExchange().permitAll()
        http.oauth2ResourceServer()
            .jwt()
            .jwtDecoder(NimbusReactiveJwtDecoder.withSecretKey(signer.secretKey).macAlgorithm(MacAlgorithm.HS512).build())

        http.csrf().disable()

        return http.build()
    }
}
