package com.nu.bom.core.manufacturing.relations.children.entities

import com.nu.bom.core.manufacturing.annotations.BehaviourCreation
import com.nu.bom.core.manufacturing.annotations.Children
import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.FilteredChildren
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.defaults.NullProvider
import com.nu.bom.core.manufacturing.entities.BaseEntityFields
import com.nu.bom.core.manufacturing.entities.BaseManufacturingFields
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.ConfigIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.relations.children.ChildrenFromAncestorTests
import com.nu.bom.core.manufacturing.service.behaviour.DynamicBehaviour
import com.nu.bom.core.manufacturing.service.behaviour.EntityBasedDynamicBehaviour
import com.nu.bom.core.manufacturing.testentities.TestEntity

@TestEntity
@EntityType(Entities.MANUFACTURING)
class ChildrenFromAncestorTestManufacturing(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = BaseManufacturingFields(name)
    override val behaviours = listOf(TestDelayBehaviour(name))

    @Input
    fun costModuleConfigurationIdentifier(): ConfigIdentifier = ConfigIdentifier(ConfigurationIdentifier.empty())

    @EntityCreation(Entities.MATERIAL)
    fun createShallow(delay: Bool): ChildrenFromAncestorTestMaterial =
        createEntity("single", Entities.MATERIAL, ChildrenFromAncestorTestMaterial::class)

    @EntityCreation(Entities.MATERIAL)
    fun createShallowImmediate(): ChildrenFromAncestorTestMaterial? =
        createEntity("immediate", Entities.MATERIAL, ChildrenFromAncestorTestMaterial::class)

    @EntityCreation(
        Entities.CONSUMABLE,
        childCreations = [Entities.MATERIAL],
        onlyDirectChild = false,
    )
    fun createGrandChild(delay: Bool): ChildrenFromAncestorTestParent =
        createEntity("parent", Entities.CONSUMABLE, ChildrenFromAncestorTestParent::class)

    @EntityCreation(
        Entities.MANUFACTURING_STEP,
        childCreations = [Entities.MANUFACTURING_STEP],
        onlyDirectChild = false,
    )
    fun createStep() =
        createEntity(
            ChildrenFromAncestorTests.GEN_STEP,
            Entities.MANUFACTURING_STEP,
            ChildrenFromAncestorTestParentStep::class,
        )
}

@TestEntity
@EntityType(Entities.MANUFACTURING_STEP)
class ChildrenFromAncestorTestStep(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = BaseEntityFields(name)

    fun singleField(
        @FilteredChildren(Entities.MATERIAL, nameFilter = "single", startFromFirstAncestor = Entities.MANUFACTURING)
        one: Num,
    ) = one

    fun optionalSingleField(
        @FilteredChildren(Entities.MATERIAL, nameFilter = "single", startFromFirstAncestor = Entities.MANUFACTURING)
        @Default(NullProvider::class)
        one: Num?,
    ) = one ?: Num(0.0)

    fun multiField(
        @Children(
            Entities.MATERIAL,
            startFromFirstAncestor = Entities.MANUFACTURING,
            skipDependencyWhenFieldDoesNotExist = false,
        )
        one: List<Num>,
    ) = one.reduce { acc, num -> acc + num }

    fun deepField(
        @Children(
            Entities.MATERIAL,
            startFromFirstAncestor = Entities.MANUFACTURING,
            directOnly = false,
            skipDependencyWhenFieldDoesNotExist = false,
        )
        one: List<Num>,
    ) = one.reduce { acc, num -> acc + num }

    fun optionalDeepField(
        @Children(
            Entities.MATERIAL,
            startFromFirstAncestor = Entities.MANUFACTURING,
            directOnly = false,
            skipDependencyWhenFieldDoesNotExist = false,
        )
        @Default(NullProvider::class)
        one: List<Num>?,
    ) = sum(one)

    fun optionalMultiField(
        @Children(
            Entities.MATERIAL,
            startFromFirstAncestor = Entities.MANUFACTURING,
            skipDependencyWhenFieldDoesNotExist = false,
        )
        @Default(NullProvider::class)
        one: List<Num>?,
    ) = sum(one)
}

@TestEntity
@EntityType(Entities.MANUFACTURING_STEP)
class ChildrenFromAncestorTestParentStep(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = ChildrenFromAncestorTestStep(name)

    @EntityCreation(Entities.MANUFACTURING_STEP)
    fun createSubStep() =
        createEntity(
            ChildrenFromAncestorTests.GEN_SUB_STEP,
            Entities.MANUFACTURING_STEP,
            ChildrenFromAncestorTestStep::class,
        )
}

@TestEntity
@EntityType(Entities.MATERIAL)
class ChildrenFromAncestorTestMaterial(
    name: String,
) : ManufacturingEntity(name) {
    fun one() = Num(1)
}

@TestEntity
@EntityType(Entities.CONSUMABLE)
class ChildrenFromAncestorTestParent(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = BaseEntityFields(name)
    override val behaviours = listOf(TestDelayBehaviour(name))

    @EntityCreation(Entities.MATERIAL)
    fun createMaterial(delay: Bool): ChildrenFromAncestorTestMaterial =
        createEntity("grandchild", Entities.MATERIAL, ChildrenFromAncestorTestMaterial::class)
}

@TestEntity
@EntityType(Entities.MATERIAL)
class ChildrenFromAncestorTestMaterialWithBehaviour(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = TestDelayBehaviour(name)

    @BehaviourCreation
    fun createBehaviour(delay: Bool): DynamicBehaviour =
        EntityBasedDynamicBehaviour(this, ChildrenFromAncestorTestBehaviour("childBehaviour"))
}

@TestEntity
@EntityType(Entities.NONE)
class ChildrenFromAncestorTestBehaviour(
    name: String,
) : ManufacturingEntity(name) {
    fun one() = Num(1)
}

@TestEntity
@EntityType(Entities.NONE)
class TestDelayBehaviour(
    name: String,
) : ManufacturingEntity(name) {
    fun delay1() = Bool(true)

    fun delay2(delay1: Bool) = delay1

    // Delay calculation by 3 rounds.
    // We need more than one as (for now) child creations wait for exchange rates to be calculated
    fun delay(delay2: Bool) = delay2
}
