package com.nu.bom.core.manufacturing.commercialcalculation.fieldCalculation.printCommercialResult

import com.nu.bom.core.utils.AnsiColor

class Row(
    val cells: List<Cell>,
    private val colorCode: AnsiColor = AnsiColor.ANSI_RESET,
) {
    fun emptyCells(): List<Int> {
        var pos = 0
        val result = mutableListOf<Int>()
        cells.forEach { cell ->
            if (cell.isEmpty()) {
                result.add(pos)
            }
            pos++
        }
        return result
    }

    fun minColumnWidth(): Map<Int, Int> = cells.withIndex().associate { Pair(it.index, it.value.minWidth()) }

    fun optimize(columnPosToRemove: Set<Int>, columnToMinColumnWidth: Map<Int, Int>): Row {
        val result = mutableListOf<Cell>()
        var pos = 0
        cells.forEach { cell ->
            if (!columnPosToRemove.contains(pos)) {
                cell.columnWidth = columnToMinColumnWidth[pos] ?: cell.columnWidth
                result.add(cell)
            }
            pos++
        }
        return Row(result, colorCode)
    }

    fun dump(stringBuilder: StringBuilder) {
        stringBuilder.append("$colorCode")
        cells.forEach { cell ->
            cell.dump(stringBuilder, colorCode)
            stringBuilder.append(" | ")
        }
    }

    fun print() {
        val stringBuilder = StringBuilder()
        dump(stringBuilder)
        println(stringBuilder.toString())
    }
}
