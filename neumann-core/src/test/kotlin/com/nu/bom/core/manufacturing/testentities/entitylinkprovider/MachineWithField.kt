package com.nu.bom.core.manufacturing.testentities.entitylinkprovider

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.entities.Machine
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Text

@EntityType(Entities.MACHINE)
class MachineWithField(name: String) : ManufacturingEntity(name) {
    override val extends = Machine(name)

    @Input
    fun machineFieldNeeded(): Text? = null
}
