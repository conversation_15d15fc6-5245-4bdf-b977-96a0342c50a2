package com.nu.bom.core.manufacturing.relations.children

import com.nu.bom.core.CalculationTestBase
import com.nu.bom.core.manufacturing.entities.BaseEntityFields
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.relations.children.behaviour.TestDynamicFieldConfigBuilderForChildrenRelation
import com.nu.bom.core.manufacturing.relations.children.entities.ChildEntityForChildrenRelationTests
import com.nu.bom.core.manufacturing.relations.children.entities.ParentEntityForChildrenRelationTests
import org.junit.jupiter.api.Test

class ChildrenTests : CalculationTestBase(
    dynamicEntityFieldConfigBuilders = listOf(TestDynamicFieldConfigBuilderForChildrenRelation()),
) {

    @Test
    fun getFieldOfOneChildCreatedManually() {
        val parent = createParent(createChildren = false)
        createChild(parent, "key1", 1.0)
        createChild(parent, "key2", 2.0)
        createChild(parent, "key3", 3.0)

        calculateAndPrintResult(parent)

        validateResultFieldScaled(parent.name, ParentEntityForChildrenRelationTests::rateOfChild1.name, 1.0, 5)
        validateResultFieldValueIsNull(parent.name, ParentEntityForChildrenRelationTests::rateOfChild6.name)
        validateResultFieldScaled(parent.name, "rateOfKey1", 1.0, 5)
    }

    @Test
    fun getFieldOfOneChildCreatedWithEntityCreation() {
        val parent = createParent(createChildren = true)
        calculateAndPrintResult(parent)

        validateResultFieldScaled(parent.name, ParentEntityForChildrenRelationTests::rateOfChild1.name, 1.0, 5)
        validateResultFieldValueIsNull(parent.name, ParentEntityForChildrenRelationTests::rateOfChild6.name)
        validateResultFieldScaled(parent.name, "rateOfKey1", 1.0, 5)
    }

    @Test
    fun getFieldOfOneChild() {
        val parent = createParent(createChildren = true)
        createChild(parent, "key4", 4.0)
        createChild(parent, "key5", 5.0)
        createChild(parent, "key6", 6.0)

        calculateAndPrintResult(parent)

        validateResultFieldScaled(parent.name, ParentEntityForChildrenRelationTests::rateOfChild1.name, 1.0, 5)
        validateResultFieldScaled(parent.name, ParentEntityForChildrenRelationTests::rateOfChild6.name, 6.0, 5)
        validateResultFieldScaled(parent.name, "rateOfKey1", 1.0, 5)
    }

    @Test
    fun getFieldOfSeveralChildren() {
        val parent = createParent(createChildren = true)
        createChild(parent, "key1", 1.0)
        createChild(parent, "key2", 2.0)
        createChild(parent, "key3", 3.0)

        calculateAndPrintResult(parent)

        validateResultFieldScaled(parent.name, ParentEntityForChildrenRelationTests::rateOfChild1.name, 2.0, 5)
        validateResultFieldValueIsNull(parent.name, ParentEntityForChildrenRelationTests::rateOfChild6.name)
        validateResultFieldScaled(parent.name, "rateOfKey1", 2.0, 5)
    }

    private fun calculateAndPrintResult(entity: ManufacturingEntity) {
        calculate(entity)

        printResults(
            true,
            true,
            hideDependencies = false,
            hideFieldsFromKClasses = setOf(
                ManufacturingEntity::class,
                BaseEntityFields::class,
                BaseManufacturing::class,
            ),
            printSpecialFields = true,
        )
    }

    private fun createParent(createChildren: Boolean): ManufacturingEntity {
        return addObject(
            name = "Parent",
            clazz = ParentEntityForChildrenRelationTests::class.java,
            parent = null,
            parameters = mapOf(
                ParentEntityForChildrenRelationTests::createChildren.name to Bool(createChildren),
            ),
        )
    }

    private fun createChild(parent: ManufacturingEntity, name: String, value: Double): ManufacturingEntity {
        return addObject(
            name = name,
            clazz = ChildEntityForChildrenRelationTests::class.java,
            parent = parent,
            parameters = mapOf(
                ChildEntityForChildrenRelationTests::rate.name to Rate(value.toBigDecimal()),
            ),
        )
    }
}
