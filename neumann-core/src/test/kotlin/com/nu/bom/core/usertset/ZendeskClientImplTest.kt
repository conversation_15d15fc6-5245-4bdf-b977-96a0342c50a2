package com.nu.bom.core.usertset

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ArrayNode
import com.fasterxml.jackson.databind.node.JsonNodeFactory
import com.fasterxml.jackson.databind.node.POJONode
import com.fasterxml.jackson.databind.node.TextNode
import com.nu.bom.core.usertset.exception.ZendeskException
import com.nu.bom.core.usertset.zendesk.Comment
import com.nu.bom.core.usertset.zendesk.CreateUserRequestWrapperObject
import com.nu.bom.core.usertset.zendesk.CustomField
import com.nu.bom.core.usertset.zendesk.TicketRequestWrapperObject
import com.nu.bom.core.usertset.zendesk.ZendeskClientImpl
import com.nu.bom.core.usertset.zendesk.ZendeskOrganizationDto
import com.nu.bom.core.usertset.zendesk.ZendeskTicket
import com.nu.bom.core.usertset.zendesk.ZendeskTicketAddCommentRequestDto
import com.nu.bom.core.usertset.zendesk.ZendeskTicketCreateRequestDto
import com.nu.bom.core.usertset.zendesk.ZendeskUserCreateRequest
import com.nu.bom.core.usertset.zendesk.ZendeskUserDto
import com.nu.bom.core.utils.JacksonTest
import com.nu.http.TsetService
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.reactive.function.client.ClientResponse
import org.springframework.web.util.UriBuilder
import reactor.core.publisher.Mono
import reactor.test.StepVerifier
import java.net.URI

@JacksonTest
@ExtendWith(MockitoExtension::class)
class ZendeskClientImplTest(
    @Autowired
    private val objectMapper: ObjectMapper,
) {
    @Mock
    lateinit var tsetService: TsetService

    lateinit var client: ZendeskClientImpl

    @BeforeEach
    fun setUp() {
        client =
            ZendeskClientImpl(
                "base url",
                "<EMAIL>",
                "api token",
                tsetService,
                objectMapper,
            )
    }

    @Test
    fun `findUser calls tsetService with correct params`() {
        mockTsetServiceForMonoCall()
        val uriCaptor = argumentCaptor<(UriBuilder) -> URI>()
        val uriBuilder = mockURIBuilderFindUser()

        client.findUser("<EMAIL>")

        verify(tsetService).getToMono(
            baseUrl = eq("base url"),
            uri = uriCaptor.capture(),
            headers = eq(mapOf("Authorization" to "Basic ********************************************")),
            jwtToken = anyOrNull(),
            accept = anyOrNull(),
            resilienceName = anyOrNull(),
            errorHandler = any<(ClientResponse) -> Mono<ZendeskUserDto>>(),
            successHandler = any<(ClientResponse) -> Mono<ZendeskUserDto>>(),
            overrideEnvironment = anyOrNull(),
            longRunning = anyOrNull(),
        )

        val resultUri = uriCaptor.firstValue(uriBuilder)
        assertThat(resultUri).isEqualTo(URI("/api/v2/users/search.json?query=type:user%20email:testuser%40tset.com"))
    }

    @Test
    fun `findUser returns correct exception`() {
        mockTsetServiceForMonoCall()
        val bodyToMonoCaptorError = argumentCaptor<(ClientResponse) -> Mono<ZendeskUserDto>>()

        client.findUser("<EMAIL>")

        verify(tsetService).getToMono(
            baseUrl = eq("base url"),
            uri = anyOrNull(),
            headers = eq(mapOf("Authorization" to "Basic ********************************************")),
            jwtToken = anyOrNull(),
            accept = anyOrNull(),
            resilienceName = anyOrNull(),
            errorHandler = bodyToMonoCaptorError.capture(),
            successHandler = any<(ClientResponse) -> Mono<ZendeskUserDto>>(),
            overrideEnvironment = anyOrNull(),
            longRunning = anyOrNull(),
        )

        val mockedResponse = mock<ClientResponse>()
        whenever(mockedResponse.bodyToMono(any<Class<String>>()))
            .thenReturn(Mono.fromSupplier { "Error!" })

        StepVerifier.create(bodyToMonoCaptorError.firstValue(mockedResponse))
            .expectErrorMatches {
                it is ZendeskException &&
                    it.message == "Error from Zendesk while finding user: Error!"
            }
            .verify()
    }

    @Test
    fun `findUser correctly parses return value`() {
        val bodyToMonoCaptorSuccess = argumentCaptor<(ClientResponse) -> Mono<List<ZendeskUserDto>>>()
        mockTsetServiceForMonoCall()

        client.findUser("<EMAIL>")

        verify(tsetService).getToMono(
            baseUrl = eq("base url"),
            uri = anyOrNull(),
            headers = eq(mapOf("Authorization" to "Basic ********************************************")),
            jwtToken = anyOrNull(),
            accept = anyOrNull(),
            resilienceName = anyOrNull(),
            errorHandler = any<(ClientResponse) -> Mono<List<ZendeskUserDto>>>(),
            successHandler = bodyToMonoCaptorSuccess.capture(),
            overrideEnvironment = anyOrNull(),
            longRunning = anyOrNull(),
        )

        val returnedNode = mock<JsonNode>()
        val nodeWithUser = POJONode(ZendeskUserDto("user id", "User name", "email", "org id"))
        // https://tsetplatform.atlassian.net/browse/COST-44961
        val usersNode = ArrayNode(JsonNodeFactory.instance, listOf(nodeWithUser))
        whenever(returnedNode.get("users"))
            .thenReturn(usersNode)

        val mockedResponse = mock<ClientResponse>()
        whenever(mockedResponse.bodyToMono(JsonNode::class.java))
            .thenReturn(Mono.fromSupplier { returnedNode })

        val result = bodyToMonoCaptorSuccess.firstValue(mockedResponse).block()!!

        assertThat(result).isNotEmpty
        assertThat(result.get(0)).isEqualTo(
            ZendeskUserDto(
                "user id",
                "User name",
                "email",
                "org id",
            ),
        )
    }

    private fun mockURIBuilderFindUser(): UriBuilder =
        mock<UriBuilder>().apply {
            whenever(path("/api/v2/users/search.json"))
                .thenReturn(this)
            whenever(query("query=type:user email:{email}"))
                .thenReturn(this)
            whenever(build("<EMAIL>"))
                .thenReturn(URI("/api/v2/users/search.json?query=type:user%20email:testuser%40tset.com"))
        }

    @Test
    fun `findOrgByName calls tsetService with correct params`() {
        val uriCaptor = argumentCaptor<(UriBuilder) -> URI>()

        whenever(
            tsetService.getToMono(
                baseUrl = any(),
                uri = anyOrNull(),
                headers = any(),
                jwtToken = anyOrNull(),
                accept = anyOrNull(),
                resilienceName = anyOrNull(),
                errorHandler = any<(ClientResponse) -> Mono<ZendeskOrganizationDto>>(),
                successHandler = any<(ClientResponse) -> Mono<ZendeskOrganizationDto>>(),
                overrideEnvironment = anyOrNull(),
                longRunning = anyOrNull(),
            ),
        ).thenReturn(Mono.empty())

        client.findOrgByName("organization")

        verify(tsetService).getToMono(
            baseUrl = eq("base url"),
            uri = uriCaptor.capture(),
            headers = eq(mapOf("Authorization" to "Basic ********************************************")),
            jwtToken = anyOrNull(),
            accept = anyOrNull(),
            resilienceName = anyOrNull(),
            errorHandler = any<(ClientResponse) -> Mono<ZendeskOrganizationDto>>(),
            successHandler = any<(ClientResponse) -> Mono<ZendeskOrganizationDto>>(),
            overrideEnvironment = anyOrNull(),
            longRunning = anyOrNull(),
        )

        val uriBuilder = mockURIBuilderFindOrg()
        val resultUri = uriCaptor.firstValue(uriBuilder)
        assertThat(resultUri).isEqualTo(URI("/api/v2/organizations/autocomplete?name=organization"))
    }

    @Test
    fun `findOrgByName returns correct exception`() {
        mockTsetServiceForMonoCall()
        val bodyToMonoCaptorError = argumentCaptor<(ClientResponse) -> Mono<ZendeskOrganizationDto>>()

        client.findOrgByName("organization")

        verify(tsetService).getToMono(
            baseUrl = eq("base url"),
            uri = anyOrNull(),
            headers = eq(mapOf("Authorization" to "Basic ********************************************")),
            jwtToken = anyOrNull(),
            accept = anyOrNull(),
            resilienceName = anyOrNull(),
            errorHandler = bodyToMonoCaptorError.capture(),
            successHandler = any<(ClientResponse) -> Mono<ZendeskOrganizationDto>>(),
            overrideEnvironment = anyOrNull(),
            longRunning = anyOrNull(),
        )

        val mockedResponse = mock<ClientResponse>()
        whenever(mockedResponse.bodyToMono(any<Class<String>>()))
            .thenReturn(Mono.fromSupplier { "Error!" })

        StepVerifier.create(bodyToMonoCaptorError.firstValue(mockedResponse))
            .expectErrorMatches {
                it is ZendeskException &&
                    it.message == "Error from Zendesk while fetching orgs: Error!"
            }
            .verify()
    }

    @Test
    fun `findOrgByName correctly parses return value`() {
        mockTsetServiceForMonoCall()
        val bodyToMonoCaptorSuccess = argumentCaptor<(ClientResponse) -> Mono<List<ZendeskOrganizationDto>>>()

        client.findOrgByName("organization")

        verify(tsetService).getToMono(
            baseUrl = eq("base url"),
            uri = anyOrNull(),
            headers = eq(mapOf("Authorization" to "Basic ********************************************")),
            jwtToken = anyOrNull(),
            accept = anyOrNull(),
            resilienceName = anyOrNull(),
            errorHandler = any<(ClientResponse) -> Mono<List<ZendeskOrganizationDto>>>(),
            successHandler = bodyToMonoCaptorSuccess.capture(),
            overrideEnvironment = anyOrNull(),
            longRunning = anyOrNull(),
        )

        val returnedNode = mock<JsonNode>()
        val nodeWithOrgs = POJONode(ZendeskOrganizationDto("org id", "Org name"))
        // https://tsetplatform.atlassian.net/browse/COST-44961
        val orgNode = ArrayNode(JsonNodeFactory.instance, listOf(nodeWithOrgs))
        whenever(returnedNode.get("organizations"))
            .thenReturn(orgNode)

        val mockedResponse = mock<ClientResponse>()
        whenever(mockedResponse.bodyToMono(JsonNode::class.java))
            .thenReturn(Mono.fromSupplier { returnedNode })

        val result = bodyToMonoCaptorSuccess.firstValue(mockedResponse).block()!!

        assertThat(result).isNotEmpty
        assertThat(result.get(0)).isEqualTo(
            ZendeskOrganizationDto(
                "org id",
                "Org name",
            ),
        )
    }

    private fun mockURIBuilderFindOrg(): UriBuilder =
        mock<UriBuilder>().apply {
            whenever(path("/api/v2/organizations/autocomplete"))
                .thenReturn(this)
            whenever(query("name={orgName}"))
                .thenReturn(this)
            whenever(build("organization"))
                .thenReturn(URI("/api/v2/organizations/autocomplete?name=organization"))
        }

    @Test
    fun `createUser calls tsetService with correct params`() {
        val userCreateRequest =
            ZendeskUserCreateRequest(
                name = "user name",
                email = "email",
                organizationId = "12345",
            )
        val uriCaptor = argumentCaptor<(UriBuilder) -> URI>()
        val uriBuilder = mockURIBuilderCreateUser()

        client.createUser(userCreateRequest)

        verify(tsetService).postToMono(
            eq("base url"),
            eq(CreateUserRequestWrapperObject(user = userCreateRequest)),
            uriCaptor.capture(),
            eq(mapOf("Authorization" to "Basic ********************************************")),
            anyOrNull(),
            anyOrNull(),
            anyOrNull(),
            any<(ClientResponse) -> Mono<ZendeskUserDto>>(),
            any<(ClientResponse) -> Mono<ZendeskUserDto>>(),
            anyOrNull(),
            anyOrNull(),
        )

        val resultUri = uriCaptor.firstValue(uriBuilder)
        assertThat(resultUri).isEqualTo(URI("/api/v2/users"))
    }

    @Test
    fun `createUser returns correct exception`() {
        val bodyToMonoCaptorError = argumentCaptor<(ClientResponse) -> Mono<ZendeskUserDto>>()
        val userCreateRequest =
            ZendeskUserCreateRequest(
                name = "user name",
                email = "email",
                organizationId = "12345",
            )
        val uriCaptor = argumentCaptor<(UriBuilder) -> URI>()
        val uriBuilder = mockURIBuilderCreateUser()

        client.createUser(userCreateRequest)

        verify(tsetService).postToMono(
            eq("base url"),
            eq(CreateUserRequestWrapperObject(user = userCreateRequest)),
            uriCaptor.capture(),
            eq(mapOf("Authorization" to "Basic ********************************************")),
            anyOrNull(),
            anyOrNull(),
            anyOrNull(),
            bodyToMonoCaptorError.capture(),
            any<(ClientResponse) -> Mono<ZendeskUserDto>>(),
            anyOrNull(),
            anyOrNull(),
        )

        val mockedResponse = mock<ClientResponse>()
        whenever(mockedResponse.bodyToMono(any<Class<String>>()))
            .thenReturn(Mono.fromSupplier { "Error!" })

        val resultUri = uriCaptor.firstValue(uriBuilder)
        assertThat(resultUri).isEqualTo(URI("/api/v2/users"))

        StepVerifier.create(bodyToMonoCaptorError.firstValue(mockedResponse))
            .expectErrorMatches {
                it is ZendeskException &&
                    it.message == "Error from Zendesk while creating user: Error!"
            }
            .verify()
    }

    @Test
    fun `createUser correctly parses return value`() {
        val bodyToMonoCaptorSuccess = argumentCaptor<(ClientResponse) -> Mono<ZendeskUserDto>>()
        val userCreateRequest =
            ZendeskUserCreateRequest(
                name = "user name",
                email = "email",
                organizationId = "12345",
            )
        val uriCaptor = argumentCaptor<(UriBuilder) -> URI>()
        val uriBuilder = mockURIBuilderCreateUser()

        client.createUser(userCreateRequest)

        verify(tsetService).postToMono(
            eq("base url"),
            eq(CreateUserRequestWrapperObject(user = userCreateRequest)),
            uriCaptor.capture(),
            eq(mapOf("Authorization" to "Basic ********************************************")),
            anyOrNull(),
            anyOrNull(),
            anyOrNull(),
            any<(ClientResponse) -> Mono<ZendeskUserDto>>(),
            bodyToMonoCaptorSuccess.capture(),
            anyOrNull(),
            anyOrNull(),
        )

        val resultUri = uriCaptor.firstValue(uriBuilder)
        assertThat(resultUri).isEqualTo(URI("/api/v2/users"))

        val returnedNode = mock<JsonNode>()
        val nodeWithUser = POJONode(ZendeskUserDto("user id", "User name", "email", "org id"))
        // https://tsetplatform.atlassian.net/browse/COST-44961
        whenever(returnedNode.get("user"))
            .thenReturn(nodeWithUser)

        val mockedResponse = mock<ClientResponse>()
        whenever(mockedResponse.bodyToMono(JsonNode::class.java))
            .thenReturn(Mono.fromSupplier { returnedNode })

        val result = bodyToMonoCaptorSuccess.firstValue(mockedResponse).block()!!

        assertThat(result).isEqualTo(
            ZendeskUserDto(
                "user id",
                "User name",
                "email",
                "org id",
            ),
        )
    }

    @Test
    fun `createTicket calls  tsetService with correct params`() {
        val uriCaptor = argumentCaptor<(UriBuilder) -> URI>()
        val ticketCreateRequest =
            ZendeskTicketCreateRequestDto(
                requesterId = "1234",
                subject = "subject",
                type = "software_problem",
                comment = Comment("comment body"),
                customFields =
                    listOf(
                        CustomField(ZENDESK_CUSTOM_FIELD_TYPE_ID, "software_problem"),
                        CustomField(ZENDESK_CUSTOM_FIELD_PRODUCT_ID, "cost"),
                    ),
            )

        client.createTicket(ticketCreateRequest)

        verify(tsetService).postToMono(
            baseUrl = eq("base url"),
            requestBody = eq(TicketRequestWrapperObject(ticketCreateRequest)),
            uri = uriCaptor.capture(),
            headers = eq(mapOf("Authorization" to "Basic ********************************************")),
            jwtToken = anyOrNull(),
            accept = anyOrNull(),
            resilienceName = anyOrNull(),
            errorHandler = any<(ClientResponse) -> Mono<String>>(),
            successHandler = any<(ClientResponse) -> Mono<String>>(),
            overrideEnvironment = anyOrNull(),
            longRunning = anyOrNull(),
        )

        val uriBuilder = mockURIBuilderCreateTicket()
        val resultUri = uriCaptor.firstValue(uriBuilder)
        assertThat(resultUri).isEqualTo(URI("/api/v2/tickets"))
    }

    @Test
    fun `createTicket returns correct exception`() {
        val bodyToMonoCaptorError = argumentCaptor<(ClientResponse) -> Mono<String>>()
        val ticketCreateRequest =
            ZendeskTicketCreateRequestDto(
                requesterId = "1234",
                subject = "subject",
                comment = Comment("comment body"),
                type = "software_problem",
                customFields =
                    listOf(
                        CustomField(ZENDESK_CUSTOM_FIELD_TYPE_ID, "software_problem"),
                        CustomField(ZENDESK_CUSTOM_FIELD_PRODUCT_ID, "cost"),
                    ),
            )

        client.createTicket(ticketCreateRequest)

        verify(tsetService).postToMono(
            baseUrl = eq("base url"),
            requestBody = eq(TicketRequestWrapperObject(ticketCreateRequest)),
            uri = anyOrNull(),
            headers = eq(mapOf("Authorization" to "Basic ********************************************")),
            jwtToken = anyOrNull(),
            accept = anyOrNull(),
            resilienceName = anyOrNull(),
            errorHandler = bodyToMonoCaptorError.capture(),
            successHandler = any<(ClientResponse) -> Mono<String>>(),
            overrideEnvironment = anyOrNull(),
            longRunning = anyOrNull(),
        )

        val mockedResponse = mock<ClientResponse>()
        whenever(mockedResponse.bodyToMono(any<Class<String>>()))
            .thenReturn(Mono.fromSupplier { "Error!" })

        StepVerifier.create(bodyToMonoCaptorError.firstValue(mockedResponse))
            .expectErrorMatches {
                it is ZendeskException &&
                    it.message == "Error from Zendesk while creating ticket: Error!"
            }
            .verify()
    }

    @Test
    fun `createTicket correctly parses return value`() {
        val bodyToMonoCaptorSuccess = argumentCaptor<(ClientResponse) -> Mono<ZendeskTicket>>()
        val ticketCreateRequest =
            ZendeskTicketCreateRequestDto(
                requesterId = "1234",
                subject = "subject",
                comment = Comment("comment body"),
                type = "software_problem",
                customFields =
                    listOf(
                        CustomField(ZENDESK_CUSTOM_FIELD_TYPE_ID, "software_problem"),
                        CustomField(ZENDESK_CUSTOM_FIELD_PRODUCT_ID, "cost"),
                    ),
            )

        client.createTicket(ticketCreateRequest)

        verify(tsetService).postToMono(
            baseUrl = eq("base url"),
            requestBody = eq(TicketRequestWrapperObject(ticketCreateRequest)),
            uri = anyOrNull(),
            headers = eq(mapOf("Authorization" to "Basic ********************************************")),
            jwtToken = anyOrNull(),
            accept = anyOrNull(),
            resilienceName = anyOrNull(),
            errorHandler = any<(ClientResponse) -> Mono<ZendeskTicket>>(),
            successHandler = bodyToMonoCaptorSuccess.capture(),
            overrideEnvironment = anyOrNull(),
            longRunning = anyOrNull(),
        )

        val returnedNode = mock<JsonNode>()
        val nodeWithTicket = mock<JsonNode>()
        // https://tsetplatform.atlassian.net/browse/COST-44961
        whenever(returnedNode.get("ticket"))
            .thenReturn(nodeWithTicket)
        whenever(nodeWithTicket.get("id"))
            .thenReturn(TextNode("ticket id"))
        whenever(nodeWithTicket.get("url"))
            .thenReturn(TextNode("ticket URL"))

        val mockedResponse = mock<ClientResponse>()
        whenever(mockedResponse.bodyToMono(JsonNode::class.java))
            .thenReturn(Mono.fromSupplier { returnedNode })

        val result = bodyToMonoCaptorSuccess.firstValue(mockedResponse).block()!!

        assertThat(result.id).isEqualTo("ticket id")
        assertThat(result.url).isEqualTo("ticket URL")
    }

    private fun mockURIBuilderCreateTicket(): UriBuilder =
        mock<UriBuilder>().apply {
            whenever(path("/api/v2/tickets"))
                .thenReturn(this)
            whenever(build())
                .thenReturn(URI("/api/v2/tickets"))
        }

    private fun mockURIBuilderCreateUser(): UriBuilder =
        mock<UriBuilder>().apply {
            whenever(path("/api/v2/users"))
                .thenReturn(this)
            whenever(build())
                .thenReturn(URI("/api/v2/users"))
        }

    private fun mockTsetServiceForMonoCall() {
        whenever(
            tsetService.getToMono(
                baseUrl = any(),
                uri = anyOrNull(),
                headers = any(),
                jwtToken = anyOrNull(),
                accept = anyOrNull(),
                resilienceName = anyOrNull(),
                errorHandler = any<(ClientResponse) -> Mono<ZendeskUserDto>>(),
                successHandler = any<(ClientResponse) -> Mono<ZendeskUserDto>>(),
                overrideEnvironment = anyOrNull(),
                anyOrNull(),
            ),
        ).thenReturn(Mono.empty())
    }

    @Test
    fun `addCommentToTicket returns correct exception`() {
        val bodyToMonoCaptorError = argumentCaptor<(ClientResponse) -> Mono<Void>>()

        client.addCommentToTicket("ticketId", "my comment")

        verify(tsetService).putToMono(
            baseUrl = eq("base url"),
            requestBody =
                eq(
                    TicketRequestWrapperObject(
                        ZendeskTicketAddCommentRequestDto(comment = Comment(body = "my comment")),
                    ),
                ),
            uri = anyOrNull(),
            headers = eq(mapOf("Authorization" to "Basic ********************************************")),
            jwtToken = anyOrNull(),
            accept = anyOrNull(),
            resilienceName = anyOrNull(),
            errorHandler = bodyToMonoCaptorError.capture(),
            successHandler = any<(ClientResponse) -> Mono<Void>>(),
            overrideEnvironment = anyOrNull(),
            longRunning = anyOrNull(),
        )

        val mockedResponse = mock<ClientResponse>()
        whenever(mockedResponse.bodyToMono(any<Class<String>>()))
            .thenReturn(Mono.fromSupplier { "Error!" })

        StepVerifier.create(bodyToMonoCaptorError.firstValue(mockedResponse))
            .expectErrorMatches {
                it is ZendeskException &&
                    it.message == "Error from Zendesk while adding comment to ticket ticketId: Error!"
            }
            .verify()
    }
}
