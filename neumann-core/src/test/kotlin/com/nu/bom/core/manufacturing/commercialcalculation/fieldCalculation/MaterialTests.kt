package com.nu.bom.core.manufacturing.commercialcalculation.fieldCalculation

import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationRole
import com.nu.bom.core.manufacturing.commercialcalculation.enums.MaterialClass
import com.nu.bom.core.manufacturing.commercialcalculation.fieldCalculation.expectation.ExpectedManufacturedMaterial
import com.nu.bom.core.manufacturing.commercialcalculation.fieldCalculation.expectation.ExpectedMaterialUsage
import com.nu.bom.core.manufacturing.commercialcalculation.fieldCalculation.expectation.ExpectedPurchasedMaterial
import com.nu.bom.core.manufacturing.commercialcalculation.fieldCalculation.expectation.ExpectedRoutingEntry
import com.nu.bom.core.manufacturing.commercialcalculation.fieldCalculation.expectation.ExpectedStepDetailed
import com.nu.bom.core.manufacturing.commercialcalculation.fieldCalculation.expectation.LevelRoleKey
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCostCalculationElementType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetProcurementType
import com.nu.bom.core.manufacturing.entities.Consumable
import com.nu.bom.core.manufacturing.entities.ElectronicComponent
import com.nu.bom.core.manufacturing.entities.ManualMaterial
import com.nu.bom.core.manufacturing.entities.ManualMaterialV2
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.RoughManufacturing
import org.junit.jupiter.api.DynamicTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestFactory
import kotlin.reflect.KClass

class MaterialTests : CommercialCalculationTestBase() {
    @TestFactory
    fun `test manufacturing with a simple material`(): List<DynamicTest> {
        // set up test list
        val testEntities =
            listOf(
                Consumable::class,
                ManualMaterial::class,
                ManualMaterialV2::class,
                ElectronicComponent::class,
                RoughManufacturing::class,
            )

        return testEntities.map { materialEntity ->
            DynamicTest.dynamicTest("test manufacturing with material: $materialEntity") {
                // set-up
                val material = ExpectedPurchasedMaterial(2.0, materialEntity)
                val usage =
                    ExpectedMaterialUsage(
                        quantity = 3.0,
                        material,
                        manualMaterialScrapRate = 0.5,
                    )
                val expectedManufacturedMaterial =
                    ExpectedManufacturedMaterial(
                        manualAverageUsableVolume = 1000.0,
                        materialOverheadRate = 0.2,
                        interestRate = 0.1,
                        materialInterestTime = 1.0,
                        routing = listOf(usage),
                    )

                calculateAndValidate(expectedManufacturedMaterial)
            }
        }
    }

    @Test
    fun allMaterialCalculationTest() {
        val testEntities =
            listOf(
                Consumable::class,
                ManualMaterial::class,
                ManualMaterialV2::class,
                ElectronicComponent::class,
                RoughManufacturing::class,
            )

        var price = 2.0
        var quantity = 3.0
        val materialUsages =
            testEntities.map { entity ->
                price++
                quantity += 2
                val material = ExpectedPurchasedMaterial(price, entity)
                ExpectedMaterialUsage(quantity, material, manualMaterialScrapRate = 0.5)
            }

        val expectedManufacturedMaterial =
            ExpectedManufacturedMaterial(
                manualAverageUsableVolume = 1000.0,
                materialOverheadRate = 0.2,
                interestRate = 0.1,
                materialInterestTime = 1.0,
                routing = materialUsages,
            )

        calculateAndValidate(expectedManufacturedMaterial)
    }

    @TestFactory
    fun `test with subpart without any step`(): List<DynamicTest> {
        val procurementTypes =
            listOf(
                TsetProcurementType.PURCHASE,
                TsetProcurementType.INHOUSE,
            )

        return procurementTypes.map { procurementType ->
            DynamicTest.dynamicTest("test with $procurementType subpart without any step") {
                // set-up
                val material = ExpectedPurchasedMaterial(2.0, Consumable::class)
                val materialUsage =
                    ExpectedMaterialUsage(
                        quantity = 3.0,
                        material,
                        manualMaterialScrapRate = 0.5,
                    )

                val expectedSubPart =
                    ExpectedManufacturedMaterial(
                        materialOverheadRate = 0.2,
                        interestRate = 0.1,
                        materialInterestTime = 1.0,
                        routing = listOf(materialUsage),
                        procurementType = procurementType.customProcurementType,
                        name = "sub part",
                    )
                val subPartUsage =
                    ExpectedMaterialUsage(
                        quantity = 2.0,
                        expectedSubPart,
                        manualMaterialScrapRate = 0.2,
                    )

                val expectedManufacturedMaterial =
                    ExpectedManufacturedMaterial(
                        manualAverageUsableVolume = 1000.0,
                        materialOverheadRate = 0.2,
                        interestRate = 0.1,
                        materialInterestTime = 1.0,
                        routing = listOf(subPartUsage),
                    )

                calculateAndValidate(expectedManufacturedMaterial)
            }
        }
    }

    @TestFactory
    fun `test with subpart with a single step`(): List<DynamicTest> {
        val procurementTypes =
            listOf(
                TsetProcurementType.PURCHASE,
                TsetProcurementType.INHOUSE,
            )

        val stepLess = listOf(true, false)

        val result = mutableListOf<DynamicTest>()
        procurementTypes.forEach { procurementType ->
            stepLess.forEach { stepLess ->
                result.add(
                    DynamicTest.dynamicTest("test with ${if (stepLess) "step-less" else ""} $procurementType subpart with a single step") {
                        // set-up
                        val material = ExpectedPurchasedMaterial(2.0, Consumable::class)
                        val materialUsage = ExpectedMaterialUsage(3.0, material)

                        val expectedSubPart =
                            ExpectedManufacturedMaterial(
                                materialOverheadRate = 0.5,
                                interestRate = 0.5,
                                materialInterestTime = 1.0,
                                routing = listOf(materialUsage),
                                procurementType = procurementType.customProcurementType,
                                name = "sub part",
                            )

                        val subPartUsage = ExpectedMaterialUsage(1.0, expectedSubPart, isStepLess = stepLess)
                        val expectedManufacturedMaterial =
                            ExpectedManufacturedMaterial(
                                manualAverageUsableVolume = 1000.0,
                                materialOverheadRate = 0.2,
                                interestRate = 0.2,
                                materialInterestTime = 1.0,
                                routing = listOf(subPartUsage, ExpectedStepDetailed(0.5, scrapRate = 0.5)),
                            )

                        calculateAndValidate(expectedManufacturedMaterial, true)
                    },
                )
            }
        }
        return result
    }

    @TestFactory
    fun `test with step-less sub part and multiple steps with sub part`(): List<DynamicTest> {
        val procurementTypes =
            listOf(
                TsetProcurementType.PURCHASE,
                TsetProcurementType.INHOUSE,
            )

        return procurementTypes.map { procurementType ->
            DynamicTest.dynamicTest("test with step-less $procurementType sub part and multiple steps with $procurementType sub part") {
                // set-up 4 material, first step less
                val routing: MutableList<ExpectedRoutingEntry> = mutableListOf()
                for (i in 0..3) {
                    val material = ExpectedPurchasedMaterial(2.0, Consumable::class, name = "Consumable $i")
                    val materialUsage = ExpectedMaterialUsage(3.0, material)

                    val expectedSubPart =
                        ExpectedManufacturedMaterial(
                            materialOverheadRate = 0.5,
                            interestRate = 0.5,
                            materialInterestTime = 1.0,
                            routing = listOf(materialUsage),
                            procurementType = procurementType.customProcurementType,
                            name = "sub part $i",
                        )

                    routing.add(ExpectedMaterialUsage(2.0, expectedSubPart, isStepLess = (i == 0)))

                    // create steps, except for first material
                    if (i >= 1) {
                        routing.add(ExpectedStepDetailed(scrapRate = 0.5, name = "Step $i"))
                    }
                }

                val expectedManufacturedMaterial =
                    ExpectedManufacturedMaterial(
                        manualAverageUsableVolume = 1000.0,
                        materialOverheadRate = 0.2,
                        interestRate = 0.2,
                        materialInterestTime = 1.0,
                        routing = routing,
                    )

                calculateAndValidate(expectedManufacturedMaterial)
            }
        }
    }

    @TestFactory
    fun `Test material rate and period on manufacturing level`(): List<DynamicTest> {
        val testMaterials =
            listOf(
                // no material at all
                Pair(null, null),
                // single raw material
                Pair(Consumable::class, null),
                // single purchase part
                Pair(ElectronicComponent::class, null),
                // raw material and purchase part
                Pair(RoughManufacturing::class, ManualMaterialV2::class),
            )

        return testMaterials.map { (material1, material2) ->
            DynamicTest.dynamicTest(
                "Material overhead/interest test with ${listOf(
                    material1,
                    material2,
                ).joinToString { if (it != null) it.simpleName!! else "none" }}",
            ) {
                val price1 = 2.0
                val quantity1 = 3.0
                val materialUsage1 =
                    material1?.let {
                        val material = ExpectedPurchasedMaterial(price1, material1)
                        ExpectedMaterialUsage(quantity1, material)
                    }

                val price2 = 5.0
                val quantity2 = 7.0
                val materialUsage2 =
                    material2?.let {
                        val material = ExpectedPurchasedMaterial(price2, material2)
                        ExpectedMaterialUsage(quantity2, material)
                    }

                val materialUsages = listOfNotNull(materialUsage1, materialUsage2)

                val expectedManufacturedMaterial =
                    ExpectedManufacturedMaterial(
                        manualAverageUsableVolume = 1000.0,
                        materialOverheadRate = 0.2,
                        interestRate = 0.1,
                        materialInterestTime = 1.0,
                        routing = materialUsages,
                    )

                calculateAndValidate(expectedManufacturedMaterial)
            }
        }
    }

    @TestFactory
    fun `Test material cost override on manufacturing level`(): List<DynamicTest> {
        val testMaterials =
            listOf(
                // no material at all
                Pair(null, null),
                // single raw material
                Pair(Consumable::class, null),
                // single purchase part
                Pair(ElectronicComponent::class, null),
                // raw material and purchase part
                Pair(RoughManufacturing::class, ManualMaterialV2::class),
            )

        return testMaterials.map { (material1, material2) ->
            DynamicTest.dynamicTest(
                "Material overhead/interest test with ${listOf(
                    material1,
                    material2,
                ).joinToString { if (it != null) it.simpleName!! else "none" }}",
            ) {
                val materialOverheadCostOverride = 1.0
                val materialInterestCostOverride = 0.5

                val expectedMaterialUsage1 = createMaterialAndUsage(material1, 2.0, 3.0)
                val expectedMaterialUsage2 = createMaterialAndUsage(material2, 3.0, 7.0)

                val expectedMaterialUsages = listOfNotNull(expectedMaterialUsage1, expectedMaterialUsage2)

                var rawMaterialCosts = 0.0
                var purchasePartCosts = 0.0
                var directMaterialCosts = 0.0
                expectedMaterialUsages.forEach { expectedUsage ->
                    val cost = expectedUsage.quantity * (expectedUsage.material as ExpectedPurchasedMaterial).purchasePrice
                    directMaterialCosts += cost
                    when (expectedUsage.material.materialClass) {
                        MaterialClass.MANUFACTURED_PART,
                        MaterialClass.PURCHASED_PART,
                        MaterialClass.ELECTRONIC_COMPONENT,
                        -> {
                            check(expectedUsage.material.procurementType != TsetProcurementType.INHOUSE.customProcurementType)
                            purchasePartCosts += cost
                        }
                        MaterialClass.CONSUMABLE,
                        MaterialClass.RAW_MATERIAL,
                        -> rawMaterialCosts += cost
                    }
                }

                // use direct material costs (base) as distribution weight and distribute equally, if base is zero
                val (rawMaterialFactor, purchasePartFactor) =
                    if (directMaterialCosts == 0.0) {
                        Pair(0.5, 0.5)
                    } else {
                        Pair(
                            rawMaterialCosts / directMaterialCosts,
                            purchasePartCosts / directMaterialCosts,
                        )
                    }

                val expectedRawMaterialOverheadCosts = rawMaterialFactor * materialOverheadCostOverride
                val expectedPurchasePartsOverheadCosts = purchasePartFactor * materialOverheadCostOverride

                val expectedRawMaterialInterestCosts = rawMaterialFactor * materialInterestCostOverride
                val expectedPurchasePartsInterestCosts = purchasePartFactor * materialInterestCostOverride

                val manufacturedMaterialTotalCostOverrides =
                    mapOf(
                        TsetCostCalculationElementType.RAW_MATERIAL_OVERHEAD_COSTS.fieldName to expectedRawMaterialOverheadCosts,
                        TsetCostCalculationElementType.PURCHASE_PARTS_OVERHEAD_COSTS.fieldName to expectedPurchasePartsOverheadCosts,
                        TsetCostCalculationElementType.RAW_MATERIAL_INTEREST_COSTS.fieldName to expectedRawMaterialInterestCosts,
                        TsetCostCalculationElementType.PURCHASE_PARTS_INTEREST_COSTS.fieldName to expectedPurchasePartsInterestCosts,
                    ).map {
                        Pair(
                            LevelRoleKey(
                                AggregationLevel.MANUFACTURED_MATERIAL,
                                AggregationRole.TOTAL,
                                it.key,
                            ),
                            it.value,
                        )
                    }.toMap()

                val expectedManufacturedMaterial =
                    ExpectedManufacturedMaterial(
                        manualAverageUsableVolume = 1000.0,
                        materialOverheadRate = 0.2,
                        interestRate = 0.1,
                        materialInterestTime = 1.0,
                        routing = expectedMaterialUsages,
                        costOverrides = manufacturedMaterialTotalCostOverrides,
                    )

                calculateAndValidate(expectedManufacturedMaterial)
            }
        }
    }

    private fun createMaterialAndUsage(
        materialClass: KClass<out ManufacturingEntity>?,
        quantity: Double,
        price: Double,
    ): ExpectedMaterialUsage? {
        return if (materialClass != null) {
            val expectedMaterial = ExpectedPurchasedMaterial(price, materialClass)
            val expectedUsage = ExpectedMaterialUsage(quantity, expectedMaterial)
            expectedUsage
        } else {
            null
        }
    }
}
