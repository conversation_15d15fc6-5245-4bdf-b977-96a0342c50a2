package com.nu.bom.core.manufacturing.testentities

import com.nu.bom.core.manufacturing.annotations.Children
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityProvider
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Group
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.OrderedEntityCreation
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.ConfigIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ObjectIdField
import com.nu.bom.core.manufacturing.fieldTypes.Quantity
import java.math.BigInteger

@EntityType(Entities.GROUP)
class GroupTestEntity(name: String) : ManufacturingEntity(name) {
    @Input
    fun groupId(): ObjectIdField? = null

    @Group
    fun sum(summand: List<Quantity>): Quantity = summand.fold(BigInteger.ZERO) { acc, a -> acc + a.res }.let(::Quantity)
}

@EntityType(Entities.MANUFACTURING)
class GroupTestEntityManufacturing(name: String) : ManufacturingEntity(name) {
    override val extends = Manufacturing(name)

    @Input
    fun costModuleConfigurationIdentifier(): ConfigIdentifier = ConfigIdentifier(ConfigurationIdentifier.empty())

    @EntityCreation(Entities.GROUP)
    fun createGroups(
        @Children(Entities.MANUFACTURING_STEP, directOnly = false)
        groupId: List<ObjectIdField>?,
    ): List<ManufacturingEntity> =
        groupId?.map { it.res }?.distinct()?.map { groupId ->
            createEntity(
                "Group.$groupId",
                Entities.GROUP,
                GroupTestEntity::class,
                entityRef = groupId,
                fields = mapOf("groupId" to ObjectIdField(groupId)),
            )
        } ?: emptyList()

    @OrderedEntityCreation
    fun create() = arrayOf("s1", "s2")

    @EntityProvider
    fun s1(): GroupTestEntityStep {
        return createEntity(
            name = "s1",
            entityType = Entities.MANUFACTURING_STEP,
            clazz = GroupTestEntityStep::class,
            fields =
                mapOf(
                    "summand" to Quantity(BigInteger.ONE),
                ),
        )
    }

    @EntityProvider
    fun s2(): GroupTestEntityStep {
        return createEntity(
            name = "s2",
            entityType = Entities.MANUFACTURING_STEP,
            clazz = GroupTestEntityStep::class,
            fields =
                mapOf(
                    "summand" to Quantity(BigInteger.TWO),
                ),
        )
    }
}

@EntityType(Entities.MANUFACTURING_STEP)
class GroupTestEntityStep(name: String) : ManufacturingEntity(name) {
    override val extends = GroupTestEntity(name)

    @Input
    fun summand(): Quantity = Quantity(BigInteger.ZERO)
}
