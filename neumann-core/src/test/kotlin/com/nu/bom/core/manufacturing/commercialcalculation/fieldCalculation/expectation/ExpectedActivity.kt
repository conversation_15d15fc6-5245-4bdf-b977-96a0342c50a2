package com.nu.bom.core.manufacturing.commercialcalculation.fieldCalculation.expectation

import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.staticElements.StaticActivityElement

class ExpectedActivity(
    val activityType: StaticActivityElement,
    val quantity: Double,
    val costRates: DoubleMap,
) {
    var costs: DoubleMap = DoubleMap()
    var activity: Double? = 0.0

    fun calculate() {
        costs = costRates * quantity
        activity = costs.sum()
    }

    override fun toString(): String {
        return "$activityType: quantity=$quantity, costRates=$costRates, costs=$costs, activity=$activity"
    }
}
