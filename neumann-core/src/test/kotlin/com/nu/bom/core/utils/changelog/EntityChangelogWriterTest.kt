package com.nu.bom.core.utils.changelog

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.io.ByteArrayOutputStream
import java.time.LocalDate

private const val CLASS_NAME = "className"
private const val CLASS_HASH = "hash"
private const val COMMENT = "comment"
private val DATE = LocalDate.of(2018, 7, 11)
private const val DATE_STR = "2018-07-11"
private const val CHANGE_LOG_LINE_WITHOUT_COMMENT = "[$DATE_STR]\t$CLASS_NAME\t$CLASS_HASH\n"
private const val CHANGE_LOG_LINE_WITH_COMMENT = "[$DATE_STR]\t$CLASS_NAME\t$CLASS_HASH\t$COMMENT\n"
private val HASH = EntityHash(CLASS_HASH)
private val ENTRY = EntityChangelogEntry(
    hash = HASH,
    comment = COMMENT,
    changed = DATE,
)

class EntityChangelogWriterTest {

    @Test
    fun `#write writes className,entry`() {
        val output = ByteArrayOutputStream()
        val writer = EntityChangelogWriter(output)

        writer.write(CLASS_NAME, ENTRY)

        assertEquals(CHANGE_LOG_LINE_WITH_COMMENT, output.toUtf8String())
    }

    @Test
    fun `#write writes changes,className,EntityHash(hash),comment`() {
        val output = ByteArrayOutputStream()
        val writer = EntityChangelogWriter(output)

        writer.write(DATE, CLASS_NAME, HASH, COMMENT)

        assertEquals(CHANGE_LOG_LINE_WITH_COMMENT, output.toUtf8String())
    }

    @Test
    fun `#write writes changes,className,hash,comment`() {
        val output = ByteArrayOutputStream()
        val writer = EntityChangelogWriter(output)

        writer.write(DATE, CLASS_NAME, CLASS_HASH, COMMENT)

        assertEquals(CHANGE_LOG_LINE_WITH_COMMENT, output.toUtf8String())
    }

    @Test
    fun `#write writes line`() {
        val output = ByteArrayOutputStream()
        val writer = EntityChangelogWriter(output)
        val line = "line to write"

        writer.write(line)

        assertEquals("$line\n", output.toUtf8String())
    }

    @Test
    fun `#write write two line`() {
        val output = ByteArrayOutputStream()
        val writer = EntityChangelogWriter(output)
        val line = "line to write"

        writer.write(listOf(line, line))

        assertEquals("$line\n$line\n", output.toUtf8String())
    }

    @Test
    fun `#write writes changes,className,hash When comment is null`() {
        val output = ByteArrayOutputStream()
        val writer = EntityChangelogWriter(output)

        writer.write(DATE, CLASS_NAME, CLASS_HASH, comment = null)

        assertEquals(CHANGE_LOG_LINE_WITHOUT_COMMENT, output.toUtf8String())
    }

    private fun ByteArrayOutputStream.toUtf8String() = toString(Charsets.UTF_8)
}
