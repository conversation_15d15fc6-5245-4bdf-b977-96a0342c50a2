package com.nu.bom.core.manufacturing.commercialcalculation.configurationMapping.specificOperationMapping

import com.fasterxml.jackson.databind.ObjectMapper
import com.nu.bom.core.manufacturing.commercialcalculation.configurationMapping.MandatoryOperationCreator.createMandatoryExternalOperations
import com.nu.bom.core.manufacturing.commercialcalculation.configurationMapping.MandatoryOperationCreator.createMandatoryInternalCalculatedOperations
import com.nu.bom.core.manufacturing.commercialcalculation.configurationMapping.MandatoryOperationCreator.createMandatoryInternalInputOperations
import com.nu.bom.core.manufacturing.commercialcalculation.configurationMapping.MandatoryOperationCreator.createMandatoryOperationDtos
import com.nu.bom.core.manufacturing.commercialcalculation.configurationMapping.TestProcurementTypeConfigurationCreator
import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.CalculationConfigurationException
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.CalculationOperationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.UniqueOperationIdentifier
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.dtoMapper.CostOperationConfigurationDtoMappingService
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalCostInputOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalInvestInputOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.staticElements.StaticCostElement
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.staticElements.StaticInvestElement
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externaltointernalmapper.ExternalConfigToInternalConfigMapper.mapToInternalConfig
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.InputSubElement
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.InvestInputOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.SubElementInputOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.CurrentCostOperationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.CurrentCostOperationConfigurationDto
import com.nu.bom.core.manufacturing.commercialcalculation.rollupconfiguration.RollUpConfiguration
import com.nu.bom.core.manufacturing.entities.SpecialDirectCost
import com.nu.bom.core.publicapi.dtos.configurations.enumerations.staticElements.StaticCostElementDto
import com.nu.bom.core.publicapi.dtos.configurations.enumerations.staticElements.StaticInvestElementDto
import com.nu.bom.core.publicapi.dtos.configurations.externalConfigurationOperations.ExternalCostInputOperationDto
import com.nu.bom.core.publicapi.dtos.configurations.externalConfigurationOperations.ExternalInvestInputOperationDto
import com.nu.bom.core.publicapi.dtos.configurations.fieldnamebuilders.AggregationLevelDto
import com.nu.bom.core.utils.JacksonTest
import com.tset.common.testing.assertReversible
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.DynamicTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestFactory
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired

@JacksonTest
class InvestInputOperationMappingTests(
    @Autowired
    private val objectMapper: ObjectMapper,
) {
    private val validOrigins =
        mapOf(
            AggregationLevel.TOOL to listOf(StaticInvestElement.TOOL),
            AggregationLevel.INVEST to
                listOf(
                    StaticInvestElement.DEVELOPMENT,
                    StaticInvestElement.RAMP_UP,
                    StaticInvestElement.PACKAGING_AND_CARRIER,
                ),
        )

    private enum class Keys {
        Cost1,
        Cost2,
        Invest,
    }

    @TestFactory
    fun testValidOrigins(): List<DynamicTest> =
        validOrigins.flatMap { (level, elements) ->
            elements.map { element ->
                DynamicTest.dynamicTest(level.toString()) {
                    val op =
                        ExternalInvestInputOperation(
                            sourceElement = element,
                            destinationElementKey = "Test",
                            origin = level,
                        )
                    Assertions.assertEquals(level, op.origin)
                }
            }
        }

    @TestFactory
    fun testInvalidOrigins(): List<DynamicTest> {
        val all =
            AggregationLevel.values().flatMap { level ->
                StaticInvestElement.values().map {
                    Pair(level, it)
                }
            }
        val valid =
            validOrigins.flatMap { (level, values) ->
                values.map {
                    Pair(level, it)
                }
            }

        return (all - valid).map { (level, element) ->
            DynamicTest.dynamicTest("$level -> $element") {
                assertThrows<CalculationConfigurationException> {
                    ExternalInvestInputOperation(
                        sourceElement = element,
                        destinationElementKey = "Test",
                        origin = level,
                    )
                }.let { exception ->
                    Assertions.assertEquals(
                        CalculationConfigurationException.ErrorCode.INVALID_AGGREGATION_LEVEL,
                        exception.errorCode,
                        "Unexpected error code!",
                    )
                }
            }
        }
    }

    @Test
    fun `mapping succeeds`() {
        val operationConfigMapper = CostOperationConfigurationDtoMappingService()

        val dtos =
            listOf(
                ExternalCostInputOperationDto(
                    StaticCostElementDto.ALLOCATED_COST_PER_QUANTITY_FOR_INVEST,
                    Keys.Cost1.name,
                    AggregationLevelDto.INVEST,
                ),
                ExternalCostInputOperationDto(
                    StaticCostElementDto.INTEREST_COST_PER_QUANTITY_FOR_INVEST,
                    Keys.Cost2.name,
                    AggregationLevelDto.INVEST,
                ),
                ExternalInvestInputOperationDto(
                    StaticInvestElementDto.DEVELOPMENT,
                    Keys.Invest.name,
                    AggregationLevelDto.INVEST,
                ),
            )
        val dto =
            CurrentCostOperationConfigurationDto(
                createMandatoryOperationDtos(AggregationLevelDto.MANUFACTURED_MATERIAL) + dtos,
                TestProcurementTypeConfigurationCreator.createDto(),
            )

        val externals =
            listOf(
                ExternalCostInputOperation(
                    StaticCostElement.ALLOCATED_COST_PER_QUANTITY_FOR_INVEST,
                    Keys.Cost1.name,
                    AggregationLevel.INVEST,
                ),
                ExternalCostInputOperation(
                    StaticCostElement.INTEREST_COST_PER_QUANTITY_FOR_INVEST,
                    Keys.Cost2.name,
                    AggregationLevel.INVEST,
                ),
                ExternalInvestInputOperation(
                    StaticInvestElement.DEVELOPMENT,
                    Keys.Invest.name,
                    AggregationLevel.INVEST,
                ),
            )
        val external =
            CurrentCostOperationConfiguration(
                createMandatoryExternalOperations(AggregationLevel.MANUFACTURED_MATERIAL) + externals,
                TestProcurementTypeConfigurationCreator.createBusinessLogicLevel(),
            )

        val internals =
            listOf(
                SubElementInputOperation(
                    Keys.Cost1.name,
                    AggregationLevel.INVEST,
                    null,
                ),
                SubElementInputOperation(
                    Keys.Cost2.name,
                    AggregationLevel.INVEST,
                    null,
                ),
                InvestInputOperation(
                    Keys.Invest.name,
                    AggregationLevel.INVEST,
                    subElements =
                        mapOf(
                            Keys.Cost1.name to InputSubElement(SpecialDirectCost::developmentInvestPerQuantity.name),
                            Keys.Cost2.name to InputSubElement(SpecialDirectCost::developmentInterestPerQuantity.name),
                        ),
                ),
            )
        val internal =
            CalculationOperationConfiguration(
                ValueType.COST,
                createMandatoryInternalCalculatedOperations(AggregationLevel.MANUFACTURED_MATERIAL),
                createMandatoryInternalInputOperations() +
                    internals.associateBy { UniqueOperationIdentifier.fromOperation(it) },
                TestProcurementTypeConfigurationCreator.createBusinessLogicLevel(),
            )

        // mapping between Operation Config String and Operation Config Dto works as expected
        objectMapper.assertReversible(dto)

        // mapping between Operation Config Dto and Operation Config works as expected
        Assertions.assertEquals(operationConfigMapper.fromDto(dto), external)
        Assertions.assertEquals(operationConfigMapper.toDto(external), dto)

        // mapping between External Config and Internal Config works as expected
        val createdInternalConfig =
            mapToInternalConfig(
                ValueType.COST,
                external,
                RollUpConfiguration(),
            )
        Assertions.assertEquals(createdInternalConfig, internal)
    }

    @Test
    fun `mapping fails because of missing input operation`() {
        val operation =
            ExternalInvestInputOperation(
                StaticInvestElement.DEVELOPMENT,
                "key",
                AggregationLevel.INVEST,
            )

        val externalOperationConfig =
            CurrentCostOperationConfiguration(
                listOf(operation) +
                    createMandatoryExternalOperations(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                    ),
                TestProcurementTypeConfigurationCreator.createBusinessLogicLevel(),
            )

        assertThrows<CalculationConfigurationException> {
            mapToInternalConfig(
                ValueType.COST,
                externalOperationConfig,
                RollUpConfiguration(),
            )
        }.let { exception ->
            Assertions.assertEquals(
                CalculationConfigurationException.ErrorCode.MISSING_INPUT,
                exception.errorCode,
                "Unexpected error code!",
            )
        }
    }
}
