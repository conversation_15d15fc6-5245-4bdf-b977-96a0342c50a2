package com.nu.bom.core.manufacturing.testentities.optionalfields

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.entities.BaseEntityFields
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Currency
import com.nu.bom.core.manufacturing.fieldTypes.ExchangeRatesField
import com.nu.bom.core.manufacturing.fieldTypes.Text
import org.springframework.data.annotation.Transient
import java.math.BigDecimal
import com.tset.core.service.domain.Currency as ApiCurrency

@EntityType(Entities.MACHINE)
class TestMachineWithoutField(name: String) : ManufacturingEntity(name) {

    @Transient
    override val extends = BaseEntityFields(name)

    fun nonCalculatedField(missingDependency: Text): Text {
        return missingDependency
    }

    fun baseCurrency(): Currency = Currency("EUR")

    val masterdataBaseCurrency: Currency = Currency("EUR")

    fun exchangeRates(): ExchangeRatesField = ExchangeRatesField(mapOf(ApiCurrency("EUR") to BigDecimal.ONE))
}
