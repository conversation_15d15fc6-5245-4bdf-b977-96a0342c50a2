package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.entities.Labor
import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingStep
import com.nu.bom.core.manufacturing.entities.RawMaterial
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.TsetDefaultSkillType
import com.nu.bom.core.model.MasterDataSelector
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.utils.simpleName
import com.nu.bom.core.utils.visitTree
import org.assertj.core.api.Assertions.assertThat
import org.bson.types.ObjectId
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.util.UUID

private val RANDOM_UUID = UUID.randomUUID().toString()

class LocationCostFactorsMigrationTest {
    @Test
    fun shouldMigrateExistingLocationToCostFactors() {
        val entity = createEntity()
        val migratedEntity = LocationFieldMigration().map(entity)
        validateEntity(migratedEntity)
    }

    @Test
    fun shouldNotMigrateIfCostFactorParentPresent() {
        val entity = createWithLocationEntity()
        val migratedEntity = LocationFieldMigration().map(entity)
        validateExistingCFParentEntity(migratedEntity)
    }

    @Test
    fun `migrate tree should keep location and locationName fields`() {
        val entity = createTreeWithLocationEntity()
        val migratedEntity = LocationFieldMigration().map(entity)
        validateLocationFieldsOnTree(migratedEntity)
    }

    @Test
    fun `test classificationLocationKey with all values`() {
        val selector = MasterDataSelector(MasterDataType.LOCATION, "key1", 2024, "Location1")
        val result = LocationFieldMigration().classificationLocationKey(selector)
        Assertions.assertEquals("key1", result)
    }

    @Test
    fun `test classificationLocationKey with default year and location`() {
        val selector = MasterDataSelector(MasterDataType.LOCATION, "key2")
        val result = LocationFieldMigration().classificationLocationKey(selector)
        Assertions.assertEquals("key2", result)
    }

    @Test
    fun `test classificationLocationKey with default location`() {
        val selector = MasterDataSelector(MasterDataType.LOCATION, "key3", 2022)
        val result = LocationFieldMigration().classificationLocationKey(selector)
        Assertions.assertEquals("key3", result)
    }

    @Test
    fun `test classificationLocationKey with default year`() {
        val selector = MasterDataSelector(MasterDataType.LOCATION, "key4", location = "Location4")
        val result = LocationFieldMigration().classificationLocationKey(selector)
        Assertions.assertEquals("key4", result)
    }

    private fun validateEntity(entity: ManufacturingModelEntity) {
        assertThat(entity.children.size).isEqualTo(5)
        assertThat(entity.children.filter { it.type == "LOCATION" }.size).isEqualTo(0)
        assertThat(entity.children.filter { it.type == "WAGE" }.size).isEqualTo(0)

        val costFactorParentEntities =
            entity.visitTree(
                func = { node, _ ->
                    if (node.type == Entities.MD_COSTFACTORS_PARENT.name) {
                        node
                    } else {
                        null
                    }
                },
                children = { node -> node.children },
            )
        assertThat(costFactorParentEntities.size).isEqualTo(2)
        val mdCostFactorParent = entity.children.firstOrNull { it.type == Entities.MD_COSTFACTORS_PARENT.name }
        assertThat(mdCostFactorParent).isNotNull
        assertThat(mdCostFactorParent!!.children.size).isEqualTo(4)
        assertThat(mdCostFactorParent.children.all { it.fieldWithResults.contains("#request_effectivity_tset-ref-field-region") }).isTrue
        assertThat(
            mdCostFactorParent.children.all {
                it.fieldWithResults.contains("#request_effectivity_tset-ref-field-region_display_name")
            },
        ).isTrue
        assertThat(
            mdCostFactorParent.children[0]
                .fieldWithResults["#request_effectivity_tset-ref-field-region"]!!
                .value
                .toString(),
        ).isEqualTo("$RANDOM_UUID")

        assertThat(mdCostFactorParent.children.filter { it.type == Entities.MD_COSTFACTORS_WAGE.name }.size)
            .isEqualTo(2)
        assertThat(
            mdCostFactorParent.children
                .first { it.type == Entities.MD_COSTFACTORS_WAGE.name }
                .fieldWithResults["#request_effectivity_tset-ref-field-region_display_name"]!!
                .value
                .toString(),
        ).isEqualTo(RANDOM_UUID)
        assertThat(
            mdCostFactorParent.children
                .first { it.type == Entities.MD_COSTFACTORS_WAGE.name }
                .fieldWithResults["lookupImplementationVersion"]!!
                .value
                .toString(),
        ).isEqualTo("1")
        assertThat(
            mdCostFactorParent.children
                .first { it.type == Entities.MD_COSTFACTORS_WAGE.name }
                .fieldWithResults["executeLookup"]!!
                .value
                .toString(),
        ).isEqualTo("false")
        val skillTypes =
            mdCostFactorParent.children
                .filter { it.type == Entities.MD_COSTFACTORS_WAGE.name }
                .map { it.fieldWithResults["skillType"]?.value }
        assertThat(skillTypes).isNotNull
        val parentLocation =
            entity.children
                .firstOrNull { it.type.simpleName() == "MANUFACTURING" }!!
                .fieldWithResults["location"]
                ?.value
        assertThat(parentLocation).isEqualTo("Belgium")
    }

    private fun validateExistingCFParentEntity(entity: ManufacturingModelEntity) {
        assertThat(entity.children.size).isEqualTo(2)
        assertThat(entity.children.any { it.clazz == MasterdataCostFactorParent::class.simpleName }).isTrue()
        assertThat(
            entity.children
                .find { it.clazz == MasterdataCostFactorParent::class.simpleName }!!
                .children.size,
        ).isEqualTo(0)
        assertThat(entity.children.any { it.type == "Location" }).isFalse()
    }

    private fun validateLocationFieldsOnTree(entity: ManufacturingModelEntity) {
        val step = entity.children.find { it.clazz == ManufacturingStep::class.simpleName }!!
        assertThat(step.fieldWithResults["location"]?.value).isEqualTo("Malta key")
        assertThat(step.fieldWithResults["locationName"]?.value).isEqualTo("Malta")
        val material = step.children.find { it.clazz == RawMaterial::class.simpleName }!!
        assertThat(material.fieldWithResults["location"]?.value).isEqualTo("Iceland key")
    }

    private fun createEntity() =
        ManufacturingModelEntity(
            id = ObjectId.get(),
            name = "Manu-1",
            type = "MANUFACTURING",
            clazz = ManualManufacturing::class.simpleName!!,
            args = emptyMap(),
            fieldWithResults =
                mapOf(
                    "calculationDate" to
                        FieldResultModel(
                            0,
                            0,
                            "Date",
                            LocalDate.of(2020, 7, 11),
                            FieldResult.SOURCE.C.name,
                        ),
                    "location" to
                        FieldResultModel(
                            0,
                            0,
                            "Text",
                            "Belgium",
                            FieldResult.SOURCE.C.name,
                        ),
                ),
            initialFieldWithResults = emptyMap(),
            children =
                listOf(
                    ManufacturingModelEntity(
                        id = ObjectId.get(),
                        name = "step",
                        type = "MANUFACTURING_STEP",
                        clazz = "ManufacturingStep",
                        args = emptyMap(),
                        fieldWithResults =
                            mapOf(
                                "shiftsPerDay" to
                                    FieldResultModel(
                                        0,
                                        0,
                                        "Num",
                                        "3",
                                        FieldResult.SOURCE.C.name,
                                    ),
                                "location" to
                                    FieldResultModel(
                                        0,
                                        0,
                                        "Text",
                                        "Italy",
                                        FieldResult.SOURCE.C.name,
                                    ),
                                "locationName" to
                                    FieldResultModel(
                                        0,
                                        0,
                                        "Text",
                                        "Italy",
                                        FieldResult.SOURCE.C.name,
                                    ),
                            ),
                        initialFieldWithResults = emptyMap(),
                    ),
                    ManufacturingModelEntity(
                        id = ObjectId.get(),
                        name = "Location",
                        type = "LOCATION",
                        clazz = "Location",
                        args = emptyMap(),
                        fieldWithResults = createLocationMap("Italy"),
                        initialFieldWithResults = emptyMap(),
                    ).apply {
                        masterDataSelector =
                            MasterDataSelector(
                                type = MasterDataType.LOCATION,
                                key = RANDOM_UUID,
                                year = 2024,
                                location = "Italy",
                            )
                    },
                    laborEntity(TsetDefaultSkillType.SKILLED_WORKER.name),
                    laborEntity(TsetDefaultSkillType.UNSKILLED_WORKER.name),
                    ManufacturingModelEntity(
                        id = ObjectId.get(),
                        name = "Manu-2",
                        type = "MANUFACTURING",
                        clazz = ManualManufacturing::class.simpleName!!,
                        args = emptyMap(),
                        fieldWithResults =
                            mapOf(
                                "calculationDate" to
                                    FieldResultModel(
                                        0,
                                        0,
                                        "Date",
                                        LocalDate.of(2020, 7, 11),
                                        FieldResult.SOURCE.C.name,
                                    ),
                                "location" to
                                    FieldResultModel(
                                        0,
                                        0,
                                        "Text",
                                        "Belgium",
                                        FieldResult.SOURCE.C.name,
                                    ),
                            ),
                        initialFieldWithResults = emptyMap(),
                        children =
                            listOf(
                                ManufacturingModelEntity(
                                    id = ObjectId.get(),
                                    name = "Location",
                                    type = "LOCATION",
                                    clazz = "Location",
                                    args = emptyMap(),
                                    fieldWithResults = createLocationMap("Belgium"),
                                    initialFieldWithResults = emptyMap(),
                                ).apply {
                                    masterDataSelector =
                                        MasterDataSelector(
                                            type = MasterDataType.LOCATION,
                                            key = RANDOM_UUID,
                                            year = 2024,
                                            location = "Belgium",
                                        )
                                },
                            ),
                    ),
                ),
        )

    private fun createWithLocationEntity() =
        ManufacturingModelEntity(
            id = ObjectId.get(),
            name = "Manu-1",
            type = "MANUFACTURING",
            clazz = ManualManufacturing::class.simpleName!!,
            args = emptyMap(),
            fieldWithResults = mapOf(createTextField(Manufacturing::location.name, "Italy")),
            initialFieldWithResults = emptyMap(),
            children =
                listOf(
                    ManufacturingModelEntity(
                        id = ObjectId.get(),
                        name = "Location",
                        type = "LOCATION",
                        clazz = "Location",
                        args = emptyMap(),
                        fieldWithResults = createLocationMap("Italy"),
                        initialFieldWithResults = emptyMap(),
                    ),
                    laborEntity(TsetDefaultSkillType.SKILLED_WORKER.name),
                    ManufacturingModelEntity(
                        id = ObjectId.get(),
                        name = "CF-Parent",
                        type = "MasterdataCostFactorParent",
                        clazz = MasterdataCostFactorParent::class.simpleName!!,
                        args = emptyMap(),
                        fieldWithResults = emptyMap(),
                        initialFieldWithResults = emptyMap(),
                        children = emptyList(),
                    ),
                ),
        )

    private fun createTreeWithLocationEntity() =
        ManufacturingModelEntity(
            id = ObjectId.get(),
            name = "Manu",
            type = "MANUFACTURING",
            clazz = ManualManufacturing::class.simpleName!!,
            args = emptyMap(),
            fieldWithResults =
                mapOf(
                    createTextField(Manufacturing::location.name, "Italy key"),
                    "calculationDate" to
                        FieldResultModel(
                            0,
                            0,
                            "Date",
                            LocalDate.of(2020, 7, 11),
                            FieldResult.SOURCE.C.name,
                        ),
                ),
            initialFieldWithResults = emptyMap(),
            children =
                listOf(
                    ManufacturingModelEntity(
                        id = ObjectId.get(),
                        name = "Location",
                        type = "LOCATION",
                        clazz = "Location",
                        args = emptyMap(),
                        fieldWithResults = createLocationMap("Italy"),
                        initialFieldWithResults = emptyMap(),
                    ),
                    ManufacturingModelEntity(
                        id = ObjectId.get(),
                        name = "ManufacturingStep",
                        type = Entities.MANUFACTURING_STEP.name,
                        clazz = ManufacturingStep::class.simpleName!!,
                        args = emptyMap(),
                        fieldWithResults =
                            mapOf(
                                createTextField(Manufacturing::location.name, "Malta key"),
                                createTextField(Manufacturing::locationName.name, "Malta"),
                                "shiftsPerDay" to
                                    FieldResultModel(
                                        0,
                                        0,
                                        "Num",
                                        "3",
                                        FieldResult.SOURCE.C.name,
                                    ),
                            ),
                        initialFieldWithResults = emptyMap(),
                        children =
                            listOf(
                                ManufacturingModelEntity(
                                    id = ObjectId.get(),
                                    name = "Location",
                                    type = "LOCATION",
                                    clazz = "Location",
                                    args = emptyMap(),
                                    fieldWithResults = createLocationMap("Malta"),
                                    initialFieldWithResults = emptyMap(),
                                ).apply {
                                    masterDataSelector =
                                        MasterDataSelector(
                                            type = MasterDataType.LOCATION,
                                            key = RANDOM_UUID,
                                            year = 2024,
                                            location = "Malta",
                                        )
                                },
                                ManufacturingModelEntity(
                                    id = ObjectId.get(),
                                    name = "Material",
                                    type = Entities.MATERIAL.name,
                                    clazz = RawMaterial::class.simpleName!!,
                                    args = emptyMap(),
                                    fieldWithResults =
                                        mapOf(createTextField(Manufacturing::location.name, "Iceland key")),
                                    initialFieldWithResults = emptyMap(),
                                    children =
                                        listOf(
                                            ManufacturingModelEntity(
                                                id = ObjectId.get(),
                                                name = "Location",
                                                type = "LOCATION",
                                                clazz = "Location",
                                                args = emptyMap(),
                                                fieldWithResults = createLocationMap("Iceland"),
                                                initialFieldWithResults = emptyMap(),
                                            ).apply {
                                                masterDataSelector =
                                                    MasterDataSelector(
                                                        type = MasterDataType.LOCATION,
                                                        key = RANDOM_UUID,
                                                        year = 2024,
                                                        location = "Iceland",
                                                    )
                                            },
                                        ),
                                ),
                            ),
                    ).apply {
                        masterDataSelector =
                            MasterDataSelector(
                                type = MasterDataType.LOCATION,
                                key = RANDOM_UUID,
                                year = 2024,
                                location = "Malta",
                            )
                    },
                ),
        )

    private fun createTextField(
        fieldName: String,
        fieldValue: String,
    ): Pair<String, FieldResultModel> =
        fieldName to
            FieldResultModel(
                version = 0,
                newVersion = 0,
                type = Text::class.java.simpleName,
                source = FieldResult.SOURCE.C.name,
                value = fieldValue,
            )

    private fun laborEntity(skillType: String) =
        ManufacturingModelEntity(
            id = ObjectId.get(),
            name = "Labor",
            type = Entities.LABOR.name,
            clazz = Labor::class.simpleName!!,
            args = emptyMap(),
            fieldWithResults =
                mapOf(
                    "location" to
                        FieldResultModel(
                            0,
                            0,
                            "Text",
                            "blah blah",
                            FieldResult.SOURCE.C.name,
                        ),
                    "skillType" to
                        FieldResultModel(
                            0,
                            0,
                            "skillType",
                            skillType,
                            FieldResult.SOURCE.C.name,
                            unit = "",
                            systemValue = "0.40121",
                        ),
                    "wagePerHour" to
                        FieldResultModel(
                            0,
                            0,
                            "Money",
                            "1076",
                            FieldResult.SOURCE.C.name,
                            systemValue = "0.894",
                        ),
                    "wage" to
                        FieldResultModel(
                            0,
                            0,
                            "Money",
                            "1076",
                            FieldResult.SOURCE.C.name,
                            systemValue = "0.894",
                        ),
                ),
            initialFieldWithResults = emptyMap(),
        )

    private fun createLocationMap(location: String): Map<String, FieldResultModel> =
        mapOf(
            "designation" to
                FieldResultModel(
                    0,
                    0,
                    "Text",
                    location,
                    FieldResult.SOURCE.C.name,
                ),
            "displayDesignation" to
                FieldResultModel(
                    0,
                    0,
                    "Text",
                    location,
                    FieldResult.SOURCE.C.name,
                ),
            "electricityCarbon" to
                FieldResultModel(
                    0,
                    0,
                    "Emission",
                    "0.40121",
                    FieldResult.SOURCE.C.name,
                    unit = "KILOGRAM_CO2E",
                    systemValue = "0.40121",
                ),
            "energyCost" to
                FieldResultModel(
                    0,
                    0,
                    "Money",
                    "0.176",
                    FieldResult.SOURCE.C.name,
                    systemValue = "0.894",
                ),
            "laborBurden" to
                FieldResultModel(
                    0,
                    0,
                    "Rate",
                    "0.373342403628118",
                    FieldResult.SOURCE.C.name,
                ),
            "leasingFeeProduction" to
                FieldResultModel(
                    0,
                    0,
                    "Money",
                    "5.25",
                    FieldResult.SOURCE.C.name,
                ),
            "primaryAluEmissionPerKg" to
                FieldResultModel(
                    0,
                    0,
                    "Emission",
                    "0.40121",
                    FieldResult.SOURCE.C.name,
                    unit = "KILOGRAM_CO2E",
                    systemValue = "0.40121",
                ),
            "secondaryAluEmissionPerKg" to
                FieldResultModel(
                    0,
                    0,
                    "Emission",
                    "0.40121",
                    FieldResult.SOURCE.C.name,
                    unit = "KILOGRAM_CO2E",
                    systemValue = "0.40121",
                ),
            "oxygenPrice" to
                FieldResultModel(
                    0,
                    0,
                    "Money",
                    "876",
                    FieldResult.SOURCE.C.name,
                    systemValue = "5.94",
                ),
        )
}
