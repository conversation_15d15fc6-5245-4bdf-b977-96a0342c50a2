package com.nu.bom.core.lookup

import com.nu.bom.core.model.Lookup
import com.nu.bom.core.service.imports.LookupReader
import com.opencsv.CSVReader
import java.io.StringReader

open class BaseLookupTest {
    fun getLookupByPath(
        path: String,
        name: String,
        typeInHeader: Boolean = false,
        accountIdentifier: String = "ts",
    ): Lookup {
        val lookupFile = this::class.java.getResource("$path/$name")?.readText()
        val lookupReader = CSVReader(lookupFile?.let { StringReader(it) })
        return LookupReader.readLookup(lookupReader, typeInHeader, name, accountIdentifier)
    }
}
