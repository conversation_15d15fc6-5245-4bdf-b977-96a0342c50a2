package com.nu.bom.core.service.bomrads

import com.nu.bom.core.service.bomrads.TestBranchViewDto.someBranchViewDto
import com.nu.bom.core.service.bomrads.TestNodeSnapshotDto.someExternalParent
import com.nu.bom.core.service.bomrads.TestNodeSnapshotDto.someInternalChild
import com.nu.bom.core.service.bomrads.TestNodeSnapshotDto.someNodeSnapshotDto
import com.nu.bomrads.dto.BranchViewDTO
import com.nu.bomrads.dto.ExternalParent
import com.nu.bomrads.dto.InternalChild
import com.nu.bomrads.dto.MinimalBranchDTO
import com.nu.bomrads.dto.MinimalChangesetDTO
import com.nu.bomrads.dto.NodeSnapshotDTO
import com.nu.bomrads.enumeration.BomNodeStatus
import com.nu.bomrads.enumeration.ChangeType
import com.nu.bomrads.id.BomEntryId
import com.nu.bomrads.id.BomNodeId
import com.nu.bomrads.id.BranchId
import com.nu.bomrads.id.ChangesetId
import com.nu.bomrads.id.ManufacturingTreeId
import com.nu.bomrads.id.ProjectId
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Test
import java.time.Instant
import java.util.UUID.randomUUID

class LoadingModeTest {
    @Test
    fun `should return snapshots in loading mode All`() {
        // given
        val snapshots = listOf(someNodeSnapshotDto(), someNodeSnapshotDto())
        val branchViewDto = someBranchViewDto(snapshots = snapshots)

        // when
        val result = All.filter(branchViewDto)

        // then
        assertEquals(snapshots, result, "all returned")
    }

    @Test
    fun `should return one snapshot in loading mode BranchRoot`() {
        // given
        val snapshots = listOf(someNodeSnapshotDto(), someNodeSnapshotDto())
        val branchViewDto = someBranchViewDto(snapshots = snapshots, branchBomNodeRoot = snapshots[0].bomNodeId)

        // when
        val result = BranchRoot.filter(branchViewDto)

        // then
        assertEquals(listOf(snapshots[0]), result, "just the root returned")
    }

    @Test
    fun `should return no snapshots in loading mode BranchRoot when the branch doesnt have root`() {
        // given
        val snapshots = listOf(someNodeSnapshotDto(), someNodeSnapshotDto())
        val branchViewDto = someBranchViewDto(snapshots = snapshots)

        // when
        val result = BranchRoot.filter(branchViewDto)

        // then
        assertEquals(emptyList<NodeSnapshotDTO>(), result, "just the root returned")
    }

    @Test
    fun `should return one snapshot in loading mode SingleNode`() {
        // given
        val snapshots = listOf(someNodeSnapshotDto(), someNodeSnapshotDto())
        val branchViewDto = someBranchViewDto(snapshots = snapshots)

        // when
        val result = SingleNode(snapshots[0].bomNodeId).filter(branchViewDto)

        // then
        assertEquals(listOf(snapshots[0]), result, "just one node returned")
    }

    @Test
    fun `should throw an error in loading mode SingleNode, when node is not found`() {
        // given
        val snapshots = listOf(someNodeSnapshotDto(), someNodeSnapshotDto())
        val branchViewDto = someBranchViewDto(snapshots = snapshots)

        // then
        assertThrows(IllegalStateException::class.java) {
            SingleNode(BomNodeId()).filter(branchViewDto)
        }
    }

    @Test
    fun `should return direct parent and self snapshots in loading mode DirectParent`() {
        // given
        val parent = someNodeSnapshotDto()
        val root = someNodeSnapshotDto(externalParents = listOf(someExternalParent(parent.bomNodeId)))
        val snapshots = listOf(root, parent)
        val branchViewDto =
            someBranchViewDto(snapshots = snapshots + someNodeSnapshotDto(), branchBomNodeRoot = root.bomNodeId)

        // when
        val result = DirectParents(root.bomNodeId).filter(branchViewDto)

        // then
        assertEquals(snapshots, result.toList(), "all returned")
    }

    @Test
    fun `should return direct parent and children snapshots in loading mode DirectParentAndAllChildren`() {
        // given
        val parent = someNodeSnapshotDto()
        val children = getChildren(3)
        val childrenNodes = getChildrenNodes(children)
        val root =
            someNodeSnapshotDto(
                externalParents = listOf(someExternalParent(parent.bomNodeId)),
                children = children,
            )
        val snapshots = listOf(root, parent) + childrenNodes
        val branchViewDto =
            someBranchViewDto(snapshots = snapshots + someNodeSnapshotDto(), branchBomNodeRoot = root.bomNodeId)

        // when
        val result = DirectParentAndAllChildren(root.bomNodeId).filter(branchViewDto)

        // then
        assertEquals(snapshots, result.toList(), "all returned")
    }

    private fun getChildrenNodes(children: List<InternalChild>) = children.map { someNodeSnapshotDto(it.bomNodeId) }

    private fun getChildren(quantity: Int) = (1..quantity).map { someInternalChild() }
}

object TestBranchViewDto {
    fun someBranchViewDto(
        snapshots: List<NodeSnapshotDTO>,
        branchBomNodeRoot: BomNodeId? = null,
    ) = BranchViewDTO(
        projectId = ProjectId(randomUUID()),
        branch =
            MinimalBranchDTO(
                id = BranchId(randomUUID()),
                main = true,
                global = true,
                published = true,
                creator = "creator",
                fromMaster = true,
                undoable = true,
                redoable = true,
                changesetId = null,
                sourceId = null,
                bomNodeId = branchBomNodeRoot,
            ),
        changeset =
            MinimalChangesetDTO(
                id = ChangesetId(randomUUID()),
                type = ChangeType.CREATION,
                creatorUser = "creator",
                created = Instant.EPOCH,
                variantName = "variantName",
                isPreview = false,
                previousChangesetId = null,
                mergedChangesetId = null,
                trigger = null,
            ),
        snapshots = snapshots,
        sourceChangeset = null,
    )
}

object TestNodeSnapshotDto {
    fun someNodeSnapshotDto(
        bomNodeId: BomNodeId = BomNodeId(randomUUID()),
        externalParents: List<ExternalParent> = emptyList(),
        children: List<InternalChild> = emptyList(),
    ) = NodeSnapshotDTO(
        bomNodeId = bomNodeId,
        calculationRoot = BomNodeId(randomUUID()),
        name = "name",
        year = 1970,
        title = "title",
        status = BomNodeStatus.DONE,
        root = true,
        legacyPreviousId = null,
        archived = false,
        treeId = ManufacturingTreeId(randomUUID()),
        externalParents = externalParents,
        children = children,
    )

    fun someExternalParent(bomNodeId: BomNodeId = BomNodeId(randomUUID())) =
        ExternalParent(
            bomNodeId = bomNodeId,
            bomEntryId = BomEntryId(randomUUID()),
            treeId = ManufacturingTreeId(randomUUID()),
        )

    fun someInternalChild(bomNodeId: BomNodeId = BomNodeId(randomUUID())) =
        InternalChild(
            bomNodeId = bomNodeId,
            bomEntryId = BomEntryId(randomUUID()),
            treeId = ManufacturingTreeId(randomUUID()),
        )
}
