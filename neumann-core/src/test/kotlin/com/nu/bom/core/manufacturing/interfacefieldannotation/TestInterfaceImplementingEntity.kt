package com.nu.bom.core.manufacturing.interfacefieldannotation

import com.nu.bom.core.manufacturing.annotations.Extends
import com.nu.bom.core.manufacturing.entities.ManufacturingEntityExtension
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.testentities.TestEntity

@TestEntity
@Extends
class TestInterfaceImplementingEntity(
    name: String,
) : ManufacturingEntityExtension(name) {
    fun field1() = Money.ZERO
}
