package com.nu.bom.core.manufacturing.testentities

import com.nu.bom.core.manufacturing.annotations.Children
import com.nu.bom.core.manufacturing.annotations.EnginePrivateFieldMetaInfo
import com.nu.bom.core.manufacturing.annotations.EngineTransient
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Parents
import com.nu.bom.core.manufacturing.annotations.Sibling
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Text

@EntityType(Entities.MACHINE)
class TestAnnotationEntity(name: String) : ManufacturingEntity(name) {
    @Children(Entities.ANY)
    fun child1(): Text? = null

    @Children(Entities.ANY)
    val child2: Text? = null

    @Parent(Entities.ANY)
    fun parent1(): Text? = null

    @Parent(Entities.ANY)
    val parent2: Text? = null

    @SpecialLink("x", "a")
    fun specialLink1(): Text? = null

    @SpecialLink("x", "a")
    val specialLink2: Text? = null

    @Sibling(Entities.ANY)
    val siblingLink1: Text? = null

    @Sibling(Entities.ANY)
    fun siblingLink2(): Text? = null

    fun lotOfParameters(
        @Parent(Entities.ANY) parent: Text,
        @Parents(Entities.ANY) parents: List<Text>,
        @Children(Entities.ANY) child: Text,
        @SpecialLink("y", "b") specialLink: Text,
        @Sibling(Entities.ANY) sibling: Text,
    ): Text? = null

    private var numberOfCalcsLevel1 = 0
    private var numberOfCalcsLevel2 = 0

    @EnginePrivateFieldMetaInfo(availableOutsideTheEngine = false)
    fun notPersistedField() = Text("Test")

    @EngineTransient
    fun engineTransient() = Text("Test")

    fun useEngineTransient(engineTransient: Text) = engineTransient

    fun dependentFieldLevel1ThatChanges(notPersistedField: Text): Text {
        numberOfCalcsLevel1 += 1
        return Text(notPersistedField.res + numberOfCalcsLevel1)
    }

    fun dependentFieldLevel1ThatDoesNotChange(notPersistedField: Text): Text {
        return Text(notPersistedField.res + "Level1")
    }

    fun dependentFieldLevel2(dependentFieldLevel1ThatDoesNotChange: Text): Text {
        numberOfCalcsLevel2 += 1
        return Text(dependentFieldLevel1ThatDoesNotChange.res + numberOfCalcsLevel2)
    }
}
