package com.nu.bom.core.manufacturing.service

import com.nu.bom.core.CalculationTestBase
import com.nu.bom.core.manufacturing.testentities.TestAnnotationEntity
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class EngineTransientProcessorTest : CalculationTestBase() {
    @Test
    fun `verify engine transient field are filtered out`() {
        val entity =
            addObject(
                name = "root",
                clazz = TestAnnotationEntity::class.java,
            )
        calculate(entity)
        assertFalse(entity.fieldWithResults.map { it.name.name }.contains(TestAnnotationEntity::engineTransient.name))
        assertTrue(entity.fieldWithResults.map { it.name.name }.contains(TestAnnotationEntity::useEngineTransient.name))
    }
}
