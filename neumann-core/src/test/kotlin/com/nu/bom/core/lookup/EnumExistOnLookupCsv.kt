package com.nu.bom.core.lookup

import com.nu.bom.core.manufacturing.fieldTypes.LocationToolCategory
import com.nu.bom.core.manufacturing.fieldTypes.PcbType
import com.nu.bom.core.manufacturing.fieldTypes.SliderConceptIdInjection
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeMaterial
import com.nu.bom.core.manufacturing.fieldTypes.ToolMaterial
import com.nu.bom.core.manufacturing.fieldTypes.fti.OperationContentType
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream
import kotlin.enums.EnumEntries

class EnumExistOnLookupCsv : BaseLookupTest() {
    private val lookupPath: String = "/shapedata/ts/lookup"

    @ParameterizedTest(name = "enum associated with Lookup_{0}")
    @MethodSource("enumTestArguments")
    fun <E : Enum<E>> `test all enum values exist on respective lookup`(
        csvName: String,
        enumValues: EnumEntries<E>,
    ) {
        val csvValues = getLookupByPath(lookupPath, "Lookup_$csvName.csv").rows.map { it[0] }
        enumValues.forEach {
            assert(it.name in csvValues) {
                "${it.javaClass.canonicalName} '${it.name}' does not exist in '$csvName'"
            }
        }
    }

    private companion object {
        @JvmStatic
        fun enumTestArguments(): Stream<Arguments> =
            Stream.of(
                Arguments.of("CO2_ToolMaterial", ToolMaterial.Selection.entries),
                Arguments.of("LocationToolCategory", LocationToolCategory.Selection.entries),
                Arguments.of("TransferDie_ToolRates", OperationContentType.Selection.entries),
                Arguments.of("ManufacturingPrintedCircuitBoard_PricePerUnit", PcbType.Selection.entries),
                Arguments.of("ManufacturingStepInjection_Cavities", SliderConceptIdInjection.Selection.entries),
                Arguments.of("ManufacturingStepCutToLength_CycleTimeSawing", StepSubTypeMaterial.Selection.entries),
            )
    }
}
