package com.nu.bom.core.manufacturing.testentities

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.service.InjectableEngineService

class TestService : InjectableEngineService {
    fun plus(a: Pieces, b: Pieces) = a.plus(b)
}

class TestService2 : InjectableEngineService {
    fun minus(a: Pieces, b: Pieces) = a.minus(b)
}

@EntityType(Entities.MANUFACTURING)
class TestManufacturingWithServiceInjection(name: String) : BaseManufacturing(name) {

    val a: Pieces = Pieces(1.0)

    val b: Pieces = Pieces(1.0)

    fun serviceOnly(service: TestService) = service.plus(Pieces(1.0), Pieces(2.0))
    fun serviceFirstFieldSecond(service: TestService, a: Pieces) = service.plus(Pieces(1.0), a)
    fun serviceFirst2FieldsSecond(service: TestService, a: Pieces, b: Pieces) = service.plus(a, b)
    fun serviceSecond2FieldsFirst(a: Pieces, b: Pieces, service: TestService) = service.plus(a, b)
    fun serviceInTheMiddle(a: Pieces, service: TestService, b: Pieces) = service.plus(b, a)
    fun fields2Services(a: Pieces, service: TestService, b: Pieces, service2: TestService2) =
        service2.minus(Pieces(100.0), service.plus(b, a))
}
