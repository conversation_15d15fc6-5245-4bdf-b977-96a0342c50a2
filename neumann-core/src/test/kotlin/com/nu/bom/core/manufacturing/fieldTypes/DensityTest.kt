package com.nu.bom.core.manufacturing.fieldTypes

import java.math.BigDecimal
import org.junit.jupiter.api.Test

class DensityTest : NumericFieldResultsWithUnitsTestBase() {

    @Test
    fun convertGrammPerCCMtoKgPerCm() {
        assertResult(5.0, Density(BigDecimal(5000.0), DensityUnits.KILOGRAM_PER_CM).inGPerCcm)
        assertResult(5000.0, Density(BigDecimal(5000.0), DensityUnits.KILOGRAM_PER_CM).inKgPerCm)
    }

    @Test
    fun convertKgPerCubicMetertoGramPerCubicCentiMeter() {
        assertResult(3000.0, Density(BigDecimal(3.0), DensityUnits.GRAM_PER_CCM).inKgPerCm)
        assertResult(3.0, Density(BigDecimal(3.0), DensityUnits.GRAM_PER_CCM).inGPerCcm)
    }
}
