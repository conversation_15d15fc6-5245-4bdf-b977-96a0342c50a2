package com.nu.bom.core.manufacturing.commercialcalculation.uiconfig

import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.SpecificUiConfigFeDto
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.UiConfigFeDto
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.ToolUiConfigurationIdentifiers
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.UiConfigurationIdentifiers
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.keys.ToolUiConfigIdentifierKey
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.keys.UiConfigIdentifierKey
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.values.ProcurementTypeWrapper
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.values.ToolDetailViewWrapper
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.values.ToolMaintenanceTypeWrapper
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.ToolDetailViewConfig
import com.nu.bom.core.manufacturing.fieldTypes.ToolMaintenanceType
import com.nu.bom.core.model.configurations.SemanticVersion
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.CardIdentifier
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.TableName
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.ValueTypeFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.CardConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.FieldConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.FieldSectionConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityLocator
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableColumnDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableFieldDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableRowDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EqualCriteria
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableColumnDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableRowDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.InCriteria
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.LocatorType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.LookupCellFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.TableConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.ValueCellFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.ValueCellField
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.options.ColumnOptionsFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.options.TableOptionsFeDto

object TestUiConfigFeDtoCreator {
    fun createTestTopLevelUiConfigFeDto(): UiConfigFeDto {
        return UiConfigFeDto(
            createTopLevelKey(),
            createCards(),
            createTableConfigs(),
        )
    }

    fun createTestSpecificUiConfigFeDto(): SpecificUiConfigFeDto {
        return SpecificUiConfigFeDto(
            createSpecificToolKey(),
            createCards(),
            createTableConfigs(),
        )
    }

    // region key

    fun createTopLevelKey(procurementType: ManufacturingType = ManufacturingType.PURCHASE): UiConfigurationIdentifiers {
        return UiConfigurationIdentifiers(
            mapOf(
                UiConfigIdentifierKey.COST_OPERATION_ID to ConfigurationIdentifier("1", SemanticVersion.initialVersion()),
                UiConfigIdentifierKey.CO2_OPERATION_ID to ConfigurationIdentifier("1", SemanticVersion.initialVersion()),
                UiConfigIdentifierKey.PROCUREMENT_TYPE_ID to ProcurementTypeWrapper(procurementType.res),
            ),
        )
    }

    fun createSpecificToolKey(): ToolUiConfigurationIdentifiers {
        return ToolUiConfigurationIdentifiers(
            ToolUiConfigIdentifierKey.TOOL_MAINTENANCE_TYPE_ID to
                ToolMaintenanceTypeWrapper(
                    ToolMaintenanceType.Selection.DETAILED_TOOL_MAINTENANCE,
                ),
            ToolUiConfigIdentifierKey.TOOL_DETAIL_VIEW_ID to
                ToolDetailViewWrapper(
                    ToolDetailViewConfig(Text("ToolConfig")).res,
                    ToolDetailViewConfig(Text("cO2ToolConfig")).res,
                ),
        )
    }

    // endregion

    // region card

    fun createCards(): Map<CardIdentifier, CardConfigFeDto> {
        return mapOf(
            "overhead" to createOverheadCard(),
            "overheadIncoTerms" to createIncotermsCard(),
        )
    }

    private fun createOverheadCard(): CardConfigFeDto {
        return CardConfigFeDto(
            setOf(ValueTypeFeDto.COST, ValueTypeFeDto.CO2),
            mapOf(
                ValueTypeFeDto.COST to "Overhead",
                ValueTypeFeDto.CO2 to "Overhead",
            ),
            mapOf(
                ValueTypeFeDto.COST to "costPerPart",
                ValueTypeFeDto.CO2 to "co2PerPart",
            ),
            mapOf(
                ValueTypeFeDto.COST to
                    FieldConfigFeDto(
                        listOf(
                            FieldSectionConfigFeDto(listOf("costPerPart")),
                            FieldSectionConfigFeDto(listOf("costPerPart2", "moreFields"), "TITLE", "IMAGE/CODE"),
                        ),
                        listOf(FieldSectionConfigFeDto(listOf("test"))),
                    ),
                ValueTypeFeDto.CO2 to
                    FieldConfigFeDto(
                        listOf(FieldSectionConfigFeDto(listOf("co2PerPart"))),
                    ),
            ),
            mapOf(
                ValueTypeFeDto.COST to listOf(),
                ValueTypeFeDto.CO2 to listOf("default"),
            ),
        )
    }

    private fun createIncotermsCard(): CardConfigFeDto {
        return CardConfigFeDto(
            setOf(ValueTypeFeDto.COST, ValueTypeFeDto.CO2),
            mapOf(
                ValueTypeFeDto.COST to "Incoterms",
                ValueTypeFeDto.CO2 to "Incoterms",
            ),
            mapOf(
                ValueTypeFeDto.COST to "costPerPartIncoterms",
                ValueTypeFeDto.CO2 to "cO2Incoterms",
            ),
            tableVariations =
                mapOf(
                    ValueTypeFeDto.COST to listOf("costOpt"),
                    ValueTypeFeDto.CO2 to listOf(),
                ),
        )
    }

    // endregion

    // region tables

    private fun createTableConfigs(): Map<TableName, TableConfigFeDto> {
        return mapOf(
            TableName(
                "overhead",
                ValueTypeFeDto.CO2,
                "default",
            ) to createFieldTableCO2(),
            TableName(
                "overheadIncoTerms",
                ValueTypeFeDto.COST,
                "costOpt",
            ) to createEntityTableCost(),
        )
    }

    fun createFieldTableCO2(): FieldTableConfigFeDto {
        return FieldTableConfigFeDto(
            listOf("fixedCO2", "variableCO2"),
            mapOf(
                "fixedCO2" to
                    FieldTableRowDefinitionFeDto(
                        "fixedCO2",
                        listOf(
                            ValueCellFeDto(
                                "1",
                                ValueCellField("displayDesignation", "Fixed emission", "Text"),
                            ),
                            LookupCellFeDto(
                                "2",
                                EntityLocator(type = LocatorType.SELF),
                                "fixedCO2PerYear",
                            ),
                            LookupCellFeDto(
                                "3",
                                EntityLocator(type = LocatorType.SELF),
                                "fixedCO2PerHour",
                            ),
                        ),
                    ),
                "variableCO2" to
                    FieldTableRowDefinitionFeDto(
                        "variableCO2",
                        listOf(
                            ValueCellFeDto(
                                "1",
                                ValueCellField("displayDesignation", "Variable emission", "Text"),
                            ),
                            LookupCellFeDto(
                                "2",
                                EntityLocator(type = LocatorType.SELF),
                                "variableCO2PerYear",
                            ),
                            LookupCellFeDto(
                                "3",
                                EntityLocator(type = LocatorType.SELF),
                                "variableCO2PerHour",
                            ),
                        ),
                        listOf("electricEnergyCO2"),
                    ),
                "electricEnergyCO2" to
                    FieldTableRowDefinitionFeDto(
                        "electricEnergyCO2",
                        listOf(
                            ValueCellFeDto(
                                "1",
                                ValueCellField("displayDesignation", "Electric energy emission", "Text"),
                            ),
                            LookupCellFeDto(
                                "2",
                                EntityLocator(type = LocatorType.SELF),
                                "electricEnergyCO2PerYear",
                            ),
                            LookupCellFeDto(
                                "3",
                                EntityLocator(type = LocatorType.SELF),
                                "electricEnergyCO2PerHour",
                            ),
                        ),
                        navigateToEntityLocator =
                            EntityLocator(
                                type = LocatorType.CHILD,
                                criteria = listOf(EqualCriteria("key", "value")),
                            ),
                    ),
            ),
            listOf(
                FieldTableColumnDefinitionFeDto(
                    "1",
                    ColumnOptionsFeDto(
                        displayDesignation = "Designation",
                        mainColumn = true,
                    ),
                ),
                FieldTableColumnDefinitionFeDto(
                    "2",
                    ColumnOptionsFeDto(
                        displayDesignation = "per year",
                        hasTotal = true,
                    ),
                ),
                FieldTableColumnDefinitionFeDto(
                    "3",
                    ColumnOptionsFeDto(
                        displayDesignation = "per part",
                        editable = false,
                        widthGrow = 3,
                    ),
                ),
                FieldTableColumnDefinitionFeDto(
                    "4",
                ),
            ),
            TableOptionsFeDto(true),
        )
    }

    fun createEntityTableCost(): EntityTableConfigFeDto {
        return EntityTableConfigFeDto(
            listOf("default"),
            mapOf(
                "default" to
                    EntityTableRowDefinitionFeDto(
                        "default",
                        EntityLocator(
                            listOf(
                                EqualCriteria("type", "entityType"),
                            ),
                            LocatorType.CHILD,
                        ),
                        rows = listOf("other"),
                    ),
                "other" to
                    EntityTableRowDefinitionFeDto(
                        "other",
                        EntityLocator(
                            listOf(
                                InCriteria("sense", listOf("humor")),
                            ),
                            LocatorType.ANCESTOR,
                        ),
                        specialColumns =
                            mapOf(
                                "1" to EntityTableFieldDefinitionFeDto("specialFieldName"),
                            ),
                    ),
            ),
            listOf(
                EntityTableColumnDefinitionFeDto(
                    "1",
                    "displayDesignation",
                ),
                EntityTableColumnDefinitionFeDto(
                    "2",
                    "cO2PerPart",
                    ColumnOptionsFeDto(
                        hasTotal = true,
                        mainColumn = true,
                    ),
                ),
            ),
        )
    }

    // endregion
}
