package com.nu.bom.core.manufacturing.testentities

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Nocalc
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import java.math.BigDecimal

@EntityType(Entities.MANUFACTURING)
class TestSimulationEntity(name: String) : ManufacturingEntity(name) {
    @Nocalc
    var aPlus1Counter: Int = 0

    @Nocalc
    var bPlus1Counter: Int = 0

    @Nocalc
    var costPerPartCounter: Int = 0

    @Input
    val valueA: Num? = null

    @Input
    val valueB: Num? = null

    @Input
    fun peakUsableProductionVolumePerYear(): QuantityUnit {
        return QuantityUnit(1.0)
    }

    @Input
    fun lengthOfAWhale(): Length {
        return Length(0.0, LengthUnits.METER)
    }

    fun aPlus1(valueA: Num): Num {
        aPlus1Counter++
        return valueA.plus(BigDecimal.ONE)
    }

    fun bPlus1(valueB: Num): Num {
        bPlus1Counter++
        return valueB.plus(BigDecimal.ONE)
    }

    fun costPerPart(
        aPlus1: Num,
        bPlus1: Num,
        peakUsableProductionVolumePerYear: QuantityUnit,
        lengthOfAWhale: Length,
    ): Num {
        costPerPartCounter++
        return Num(lengthOfAWhale.inCentimeter) + aPlus1.times(bPlus1).times(peakUsableProductionVolumePerYear.res)
    }
}
