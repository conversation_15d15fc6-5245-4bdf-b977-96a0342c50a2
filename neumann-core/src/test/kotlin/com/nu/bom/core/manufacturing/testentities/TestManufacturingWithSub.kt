package com.nu.bom.core.manufacturing.testentities

import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.BomEntry
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.ExchangeRatesField
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.testentities.multicurrency.EntityWithDefaultEurCurrency
import com.nu.bom.core.manufacturing.testentities.multicurrency.TestExchangeRates
import org.springframework.data.annotation.Transient
import java.math.BigDecimal

@EntityType(Entities.MANUFACTURING)
class TestManufacturingWithSub(name: String) : BaseManufacturing(name) {
    override val behaviours: List<ManufacturingEntity> =
        listOf(EntityWithDefaultEurCurrency(name))

    @Input
    fun exchangeRates(): ExchangeRatesField = TestExchangeRates.default()

    @Transient
    @Input
    val peakUsableProductionVolumePerYear: Pieces = Pieces(BigDecimal.ZERO)

    @EntityCreation(Entities.MANUFACTURING_STEP)
    fun createStepWithSubMan(): List<ManufacturingEntity> {
        val step =
            createEntity(
                name = "TestManufacturingStep",
                clazz = TestManufacturingStep::class,
                entityType = Entities.MANUFACTURING_STEP,
            )

        val bomEntry =
            createEntity(
                name = BomEntry::class.simpleName!!,
                clazz = BomEntry::class,
                overwrites = mapOf("quantity" to Pieces(1.toBigDecimal())),
                entityType = Entities.BOM_ENTRY,
            )

        val subManufacturing =
            createEntity(
                name = TestManufacturing::class.simpleName!!,
                clazz = TestManufacturing::class,
                args = mapOf("isPart" to true),
                entityType = Entities.MANUFACTURING,
            )

        bomEntry.addChild(subManufacturing)
        step.addChild(bomEntry)

        return listOf(step)
    }
}
