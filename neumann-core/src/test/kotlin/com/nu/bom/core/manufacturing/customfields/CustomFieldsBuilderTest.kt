package com.nu.bom.core.manufacturing.customfields

import com.nu.bom.core.CalculationTestBase
import com.nu.bom.core.manufacturing.fieldTypes.Null
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.model.configurations.CustomCostField
import com.nu.bom.core.model.configurations.CustomFieldSection
import com.nu.bom.core.model.configurations.CustomFieldsConfiguration
import com.nu.bom.core.model.configurations.CustomMasterdataClassificationField
import com.nu.bom.core.model.configurations.CustomMasterdataLovField
import com.nu.bom.core.service.configurations.ConfigType
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.whenever
import reactor.core.publisher.Mono

class CustomFieldsBuilderTest : CalculationTestBase() {
    companion object {
        private val lovField = CustomMasterdataLovField("lov", "Lov", Text("NO_OVERHEADS"), lovTypeKey = "lovtype")
        private val classificationField =
            CustomMasterdataClassificationField(
                "classification",
                "Classification",
                Text("Vienna"),
                CustomFieldSection.PART,
                "classificationType",
                false,
            )
        private val textField = CustomCostField("text", "Text", Text("default"), CustomFieldSection.PART, Text::class.simpleName!!, null)
        private val timeField =
            CustomCostField("time", "Time", Time(1.0, TimeUnits.HOUR), CustomFieldSection.PART, Time::class.simpleName!!, null)
        private val nullTextField = CustomCostField("null", "null", null, CustomFieldSection.PART, Text::class.simpleName!!, null)

        private val config = CustomFieldsConfiguration(listOf(lovField, classificationField, textField, timeField, nullTextField))
    }

    @BeforeEach
    fun setupConfigMocks() {
        whenever(
            mockConfigurationManagementService.getConfigurationById(any(), eq(ConfigType.CustomFields), any()),
        ).then { Mono.just(config) }

        whenever(
            mockConfigurationManagementService.getDefaultConfiguration(any(), eq(ConfigType.CustomFields)),
        ).then { Mono.just(config) }
    }

    @Test
    fun `custom fields are present and correct`() {
        val manu = createManufacturing("manu")
        calculate(manu)

        config.customFields.forEach { field ->
            validateEntityField(manu, "manu", field.fieldKey, field.defaultValue ?: Null())
        }
        // default from mock, we don't really care about the particular result, just that it's fetched from the service
        validateEntityField(manu, "manu", "lovName", Text("No overheads"))
    }

    @Test
    fun `updating custom field works`() {
        val manu = createManufacturing("manu")
        calculate(manu)
        val newText = Text("new text")

        manu.addOrUpdateField("text") { newText }
        calculate(manu)
        validateEntityField(manu, "manu", "text", newText)
    }
}
