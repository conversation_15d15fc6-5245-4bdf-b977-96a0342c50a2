package com.nu.bom.core.manufacturing.testentities.optionalfields

import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.testentities.TestEntityWithNullableFields

@EntityType(Entities.MANUFACTURING_STEP)
class NullableChildEntity(name: String) : ManufacturingEntity(name) {

    fun calcField(@Parent(Entities.MANUFACTURING) calcField: Num?): Num? = calcField

    fun inputField(@Parent(Entities.MANUFACTURING) inputField: Text?): Text? = inputField

    fun dependOnCalcField(@Parent(Entities.MANUFACTURING) dependOnCalcField: Num?): Num? = dependOnCalcField

    fun dependOnInputField(@Parent(Entities.MANUFACTURING) dependOnInputField: Text?): Text? = dependOnInputField

    fun inputWithParam(@Parent(Entities.MANUFACTURING) inputWithParam: Text?): Text? = inputWithParam

    fun valCalcField(@Parent(Entities.MANUFACTURING) valCalcField: Num?): Num? = valCalcField

    fun valInputField(@Parent(Entities.MANUFACTURING) valInputField: Num?): Num? = valInputField

    fun nullableWithDefaultAnnotation(@Parent(Entities.MANUFACTURING) @Default(TestEntityWithNullableFields.Give8::class) calcField: Num?): Num? = calcField

    fun nonNullWithDefaultAnnotation(@Parent(Entities.MANUFACTURING) @Default(TestEntityWithNullableFields.Give8::class) calcField: Num): Num? = calcField

    fun requiredNonNullWithDefaultAnnotation(@Parent(Entities.MANUFACTURING) @Default(TestEntityWithNullableFields.Give8::class) calcField: Num): Num = calcField

    fun nullableWithDefaultAnnotationAndWrongReference(@Parent(Entities.MANUFACTURING) @Default(TestEntityWithNullableFields.Give8::class) somethingUnexpected: Num?): Num? = somethingUnexpected

    fun nonNullWithDefaultAnnotationAndWrongReference(@Parent(Entities.MANUFACTURING) @Default(TestEntityWithNullableFields.Give8::class) somethingUnexpected: Num): Num? = somethingUnexpected

    fun requiredNonNullWithDefaultAnnotationAndWrongReference(@Parent(Entities.MANUFACTURING) @Default(TestEntityWithNullableFields.Give8::class) somethingUnexpected: Num): Num = somethingUnexpected
}
