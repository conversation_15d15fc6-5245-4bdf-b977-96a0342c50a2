package com.nu.bom.core.manufacturing.configFields.masterdata

import com.nu.bom.core.manufacturing.commercialcalculation.fieldCalculation.CommercialCalculationTestBase
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost.CommercialCalculationCostManufacturedMaterial
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost.CommercialCalculationCostMaterialUsage
import com.nu.bom.core.manufacturing.entities.BaseManufacturingFields
import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.extension.ManufacturingDimensionExtension
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.manufacturing.fieldTypes.MasterdataConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYears
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYearsUnit
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.masterdata.tsetdefaultconfiguration.TsetOverheadMethod
import com.nu.bom.core.model.configurations.SemanticVersion
import com.nu.bom.core.utils.assertBigDecimalsEquals
import org.junit.jupiter.api.Test
import java.math.BigDecimal

class OverheadTestMasterdata : CommercialCalculationTestBase() {
    @Test
    fun lookupTest() {
        val overheadHostParent = createManufacturing() // createOverheadHost()

        calculate(overheadHostParent)
        printResults(true, true)

        val overheadRateField = overheadHostParent.getField("#Cost_Profit-Rm_Rate")
        assertBigDecimalsEquals(
            BigDecimal("0.024242424200"),
            overheadRateField?.result?.res,
            "Cost_Profit-Rm_Rate not the expected value from masterdata",
        )
    }

    private fun createManufacturing(): ManualManufacturing {
        val rootParameters =
            mapOf(
                Manufacturing::location.name to Text("Germany"),
                BaseManufacturingFields::peakUsableProductionVolumePerYear.name to QuantityUnit(100.0),
                BaseManufacturingFields::averageUsableProductionVolumePerYear.name to QuantityUnit(100.0),
                Manufacturing::masterdataConfigurationKey.name to
                    MasterdataConfigurationKey(
                        ConfigurationIdentifier.tset("testMdOverheadsConfig", SemanticVersion.initialVersion()),
                    ),
            )

        val manufacturing =
            addObject(
                name = "manufacturedMaterial",
                clazz = ManualManufacturing::class.java,
                parent = null,
                parameters =
                    rootParameters +
                        mapOf(
                            Manufacturing::partNumber.name to Text("1234"),
                            Manufacturing::partDesignation.name to Text("manufacturedMaterial"),
                            ManufacturingDimensionExtension::dimension.name to Dimension(Dimension.Selection.NUMBER),
                            Manufacturing::callsPerYear.name to Num(12),
                            Manufacturing::shiftsPerDay.name to Num(3.0.toBigDecimal()),
                            CommercialCalculationCostMaterialUsage::procurementType.name to ManufacturingType.INHOUSE,
                            Manufacturing::lifeTime.name to TimeInYears(8.0.toBigDecimal(), TimeInYearsUnit.YEAR),
                            Manufacturing::productionHoursPerYear.name to Time(6800.0.toBigDecimal(), TimeUnits.HOUR),
                            Manufacturing::overheadMethod.name to Text(TsetOverheadMethod.BUILD_TO_PRINT_AUTO.name),
                            CommercialCalculationCostManufacturedMaterial::interestRate.name to Rate(3.0),
                        ),
                // overrides = overheadParameters,
            ) as ManualManufacturing

        return manufacturing
    }
}
