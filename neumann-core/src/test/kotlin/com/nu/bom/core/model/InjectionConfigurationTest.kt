package com.nu.bom.core.model

import com.nu.bom.core.model.configurations.InjectionConfiguration
import com.nu.bom.core.model.configurations.InjectionConfigurationTypes
import com.nu.bom.core.model.configurations.InjectionConfigurationTypes.PressureCorrectionType
import com.nu.bom.core.model.configurations.InjectionConfigurationTypes.TemperatureCorrectionType
import com.nu.bom.core.model.configurations.InjectionConfigurationV2
import com.nu.bom.core.model.configurations.InjectionConfigurationV3
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.math.BigDecimal

class InjectionConfigurationTest {
    private lateinit var injectionConfiguration: InjectionConfiguration
    private lateinit var injectionConfigurationV2: InjectionConfigurationV2
    private lateinit var injectionConfigurationV3: InjectionConfigurationV3

    @BeforeEach
    fun setUp() {
        injectionConfiguration = createInjectionConfiguration()
        injectionConfigurationV2 = createInjectionConfigurationV2()
        injectionConfigurationV3 = createInjectionConfigurationV3()
    }

    @Test
    fun testEqualsReflexive() {
        assertTrue(injectionConfiguration.equals(injectionConfiguration), "Base config should equal itself")
        assertTrue(injectionConfigurationV2.equals(injectionConfigurationV2), "Config V2 should equal itself")
        assertTrue(injectionConfigurationV3.equals(injectionConfigurationV3), "Config V3 should equal itself")
    }

    @Test
    fun testEqualsTransitivity() {
        assertTrue(injectionConfiguration.equals(injectionConfigurationV2), "Base config should equal derived config V2")
        assertTrue(injectionConfigurationV2.equals(injectionConfigurationV3), "Config V2 should equal derived config V3")
        assertTrue(injectionConfiguration.equals(injectionConfigurationV3), "Base config should equal derived config V3")

        assertFalse(injectionConfigurationV3.equals(injectionConfiguration), "Derived config V3 should not be equal base config")
        assertFalse(injectionConfigurationV3.equals(injectionConfigurationV2), "Derived config V3 should not be equal config V2")
        assertFalse(injectionConfigurationV2.equals(injectionConfiguration), "Config V2 should not be equal base config")
    }

    @Test
    fun testEqualsContentAndSymmetry() {
        val identicalInjectionConfigurationV3 = createInjectionConfigurationV3()
        val differentInjectionConfigurationV3 = createInjectionConfigurationV3(PressureCorrectionType.DEFAULT)

        assertTrue(injectionConfigurationV3.equals(identicalInjectionConfigurationV3), "Identical configurations should be equal")
        assertTrue(identicalInjectionConfigurationV3.equals(injectionConfigurationV3), "Equality should be symmetric")

        assertFalse(injectionConfigurationV3.equals(differentInjectionConfigurationV3), "Different config contents should not be equal")
        assertFalse(differentInjectionConfigurationV3.equals(injectionConfigurationV3), "Equality should not be symmetric")
    }

    @Test
    fun testEqualsClassMismatchAndNull() {
        val differentClass = Object()
        assertFalse(injectionConfiguration.equals(differentClass), "Configuration should not be equal to a different clas")
        assertFalse(injectionConfiguration.equals(null), "Configuration should not be equal to null")
    }

    @Test
    fun testHashCodeImplementation() {
        assertEquals(injectionConfiguration.hashCode(), injectionConfiguration.hashCode(), "Configurations hash codes should be equal")
        assertEquals(injectionConfigurationV2.hashCode(), injectionConfigurationV2.hashCode(), "Configurations hash codes should be equal")
        assertEquals(injectionConfigurationV3.hashCode(), injectionConfigurationV3.hashCode(), "Configurations hash codes should be equal")

        assertFalse(injectionConfiguration.hashCode() == injectionConfigurationV3.hashCode(), "Hash codes should not be equal")
        assertEquals(injectionConfigurationV3.hashCode(), createInjectionConfigurationV3().hashCode(), "Hash codes should be equal")
    }

    private fun createInjectionConfiguration() =
        InjectionConfiguration(
            InjectionConfigurationTypes.PartsPerCycleBehaviourInjectionType.WEIGHT_BASED,
            InjectionConfigurationTypes.CoolingTimeBehaviourInjectionType.WEIGHT_BASED,
            InjectionConfigurationTypes.SetupBehaviourInjectionType.MACHINE_SIZE_BASED,
        )

    private fun createInjectionConfigurationV2() =
        InjectionConfigurationV2(
            InjectionConfigurationTypes.PartsPerCycleBehaviourInjectionType.WEIGHT_BASED,
            InjectionConfigurationTypes.CoolingTimeBehaviourInjectionType.WEIGHT_BASED,
            InjectionConfigurationTypes.SetupBehaviourInjectionType.MACHINE_SIZE_BASED,
            BigDecimal.ONE,
            BigDecimal.ONE,
            BigDecimal.ONE,
            BigDecimal.ONE,
        )

    private fun createInjectionConfigurationV3(
        pressureCorrection: PressureCorrectionType = PressureCorrectionType.NO_CORRECTION_FACTOR,
        temperatureCorrection: TemperatureCorrectionType = TemperatureCorrectionType.NO_CORRECTION_FACTOR,
    ) = InjectionConfigurationV3(
        InjectionConfigurationTypes.PartsPerCycleBehaviourInjectionType.WEIGHT_BASED,
        InjectionConfigurationTypes.CoolingTimeBehaviourInjectionType.WEIGHT_BASED,
        InjectionConfigurationTypes.SetupBehaviourInjectionType.MACHINE_SIZE_BASED,
        BigDecimal.ONE,
        BigDecimal.ONE,
        BigDecimal.ONE,
        BigDecimal.ONE,
        temperatureCorrection,
        pressureCorrection,
    )
}
