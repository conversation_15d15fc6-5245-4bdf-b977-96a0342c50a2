package com.nu.bom.core.service.migration.lazy.mappers

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost.CommercialCalculationCostMaterialUsage
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.UiConfigurationIdentifiers
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.UiConfigurationIdentifiersMaybeWithoutAllEntries
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.keys.UiConfigIdentifierKey
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.values.ProcurementTypeWrapper
import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.fieldTypes.CO2CalculationElementsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.CO2CalculationOperationsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.CostCalculationElementsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.CostCalculationOperationsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.manufacturing.fieldTypes.UiConfigKeys
import com.nu.bom.core.model.configurations.SemanticVersion
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.mappers.LazyMigrationTestHelper.withoutOwnObjectIds
import org.bson.types.ObjectId
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class UiConfigKeysMigrationTest {
    @ParameterizedTest
    @MethodSource("getTestCases")
    fun `verify migration works`(testCase: TestCase) {
        Assertions.assertEquals(
            createExpectedOutputEntity(testCase).withoutOwnObjectIds(),
            AddUiConfigurationIdentifierMigration().map(createInputModelEntity(testCase)).withoutOwnObjectIds(),
        )
    }

    data class TestCase(
        val inputUiConfigKeysNewStructure: UiConfigurationIdentifiers? = null,
        val inputUiConfigKeysWithMissingEntries: UiConfigurationIdentifiersMaybeWithoutAllEntries? = null,
        val expectedUiConfigKeyAfterwards: UiConfigurationIdentifiers,
    )

    companion object {
        @JvmStatic
        fun getTestCases() =
            listOf(
                TestCase(createDefaultUiConfigurationIdentifier(), null, createDefaultUiConfigurationIdentifier()),
                TestCase(null, createPartlyUiConfigurationIdentifier(), createDefaultUiConfigurationIdentifier()),
                TestCase(null, null, createDefaultUiConfigurationIdentifier(false)),
            )

        private fun createDefaultUiConfigurationIdentifier(useDefaultConfigId: Boolean = true): UiConfigurationIdentifiers {
            return UiConfigurationIdentifiers(
                UiConfigIdentifierKey.COST_OPERATION_ID to
                    if (useDefaultConfigId) {
                        expectedConfigId
                    } else {
                        ConfigurationIdentifier.tset(
                            "tsetCostOperationsConfiguration",
                            SemanticVersion.initialVersion(),
                        )
                    },
                UiConfigIdentifierKey.CO2_OPERATION_ID to
                    if (useDefaultConfigId) {
                        expectedConfigId
                    } else {
                        ConfigurationIdentifier.tset(
                            "tsetCO2OperationsConfiguration",
                            SemanticVersion.initialVersion(),
                        )
                    },
                UiConfigIdentifierKey.PROCUREMENT_TYPE_ID to
                    if (useDefaultConfigId) {
                        ProcurementTypeWrapper(ManufacturingType.Type.PURCHASE)
                    } else {
                        ProcurementTypeWrapper(ManufacturingType.Type.INHOUSE)
                    },
            )
        }

        private fun createPartlyUiConfigurationIdentifier(): UiConfigurationIdentifiersMaybeWithoutAllEntries {
            @Suppress("DEPRECATION")
            return UiConfigurationIdentifiersMaybeWithoutAllEntries(
                mapOf(
                    UiConfigIdentifierKey.COST_OPERATION_ID to expectedConfigId,
                    UiConfigIdentifierKey.CO2_OPERATION_ID to expectedConfigId,
                    UiConfigIdentifierKey.COST_ELEMENT_ID to expectedConfigId,
                    UiConfigIdentifierKey.CO2_ELEMENT_ID to expectedConfigId,
                ),
            )
        }

        private val expectedConfigId = ConfigurationIdentifier.tset("anyKey", SemanticVersion.initialVersion())
    }

    private fun createInputModelEntity(testCase: TestCase): ManufacturingModelEntity {
        val newFields =
            when {
                testCase.inputUiConfigKeysNewStructure != null -> {
                    require(testCase.inputUiConfigKeysWithMissingEntries == null)
                    mapOf(
                        Manufacturing::uiConfigKeys.name to
                            FieldResultModel(
                                version = 0,
                                newVersion = 0,
                                type = UiConfigKeys::class.simpleName!!,
                                value = jacksonObjectMapper().writeValueAsString(testCase.inputUiConfigKeysNewStructure),
                                source = FieldResult.SOURCE.C.name,
                            ),
                    )
                }
                testCase.inputUiConfigKeysWithMissingEntries != null -> {
                    mapOf(
                        Manufacturing::uiConfigKeys.name to
                            FieldResultModel(
                                version = 0,
                                newVersion = 0,
                                type = UiConfigKeys::class.simpleName!!,
                                value = jacksonObjectMapper().writeValueAsString(testCase.inputUiConfigKeysWithMissingEntries),
                                source = FieldResult.SOURCE.C.name,
                            ),
                    )
                }
                else -> createOldConfigFields()
            }
        return ManufacturingModelEntity(
            id = ObjectId(),
            name = "Manufacturing",
            type = "MANUFACTURING",
            clazz = ManualManufacturing::class.simpleName!!,
            args = emptyMap(),
            fieldWithResults = newFields,
            initialFieldWithResults = emptyMap(),
            children = emptyList(),
        )
    }

    private fun createExpectedOutputEntity(testCase: TestCase): ManufacturingModelEntity {
        val uiConfigField =
            mapOf(
                Manufacturing::uiConfigKeys.name to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = UiConfigKeys::class.simpleName!!,
                        value = testCase.expectedUiConfigKeyAfterwards,
                        source = FieldResult.SOURCE.C.name,
                    ),
            )
        val fields =
            if (testCase.inputUiConfigKeysWithMissingEntries == null && testCase.inputUiConfigKeysNewStructure == null) {
                uiConfigField + createOldConfigFields()
            } else {
                uiConfigField
            }
        return ManufacturingModelEntity(
            id = ObjectId(),
            name = "Manufacturing",
            type = "MANUFACTURING",
            clazz = ManualManufacturing::class.simpleName!!,
            args = emptyMap(),
            fieldWithResults = fields,
            initialFieldWithResults = emptyMap(),
            children = emptyList(),
        )
    }

    private fun createOldConfigFields(): Map<String, FieldResultModel> {
        return mapOf(
            Manufacturing::costCalculationOperationKey.name to
                FieldResultModel(
                    version = 0,
                    newVersion = 0,
                    type = CostCalculationOperationsConfigurationKey::class.simpleName!!,
                    value =
                        jacksonObjectMapper().writeValueAsString(
                            ConfigurationIdentifier.tset(
                                "tsetCostOperationsConfiguration",
                                SemanticVersion.initialVersion(),
                            ),
                        ),
                    source = FieldResult.SOURCE.C.name,
                ),
            Manufacturing::cO2CalculationOperationKey.name to
                FieldResultModel(
                    version = 0,
                    newVersion = 0,
                    type = CO2CalculationOperationsConfigurationKey::class.simpleName!!,
                    value =
                        jacksonObjectMapper().writeValueAsString(
                            ConfigurationIdentifier.tset(
                                "tsetCO2OperationsConfiguration",
                                SemanticVersion.initialVersion(),
                            ),
                        ),
                    source = FieldResult.SOURCE.C.name,
                ),
            "costCalculationElementKey" to
                FieldResultModel(
                    version = 0,
                    newVersion = 0,
                    type = CostCalculationElementsConfigurationKey::class.simpleName!!,
                    value =
                        jacksonObjectMapper().writeValueAsString(
                            ConfigurationIdentifier.tset(
                                "tsetCostElementConfiguration",
                                SemanticVersion.initialVersion(),
                            ),
                        ),
                    source = FieldResult.SOURCE.C.name,
                ),
            "cO2CalculationElementKey" to
                FieldResultModel(
                    version = 0,
                    newVersion = 0,
                    type = CO2CalculationElementsConfigurationKey::class.simpleName!!,
                    value =
                        jacksonObjectMapper().writeValueAsString(
                            ConfigurationIdentifier.tset(
                                "tsetCO2ElementConfiguration",
                                SemanticVersion.initialVersion(),
                            ),
                        ),
                    source = FieldResult.SOURCE.C.name,
                ),
            CommercialCalculationCostMaterialUsage::procurementType.name to
                FieldResultModel(
                    version = 0,
                    newVersion = 0,
                    type = ManufacturingType::class.simpleName!!,
                    value = ManufacturingType.Type.INHOUSE.name,
                    source = FieldResult.SOURCE.C.name,
                ),
        )
    }
}
