package com.nu.bom.core.utils.changelog

import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.utils.EntityCollectorTestImpl
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test

private val CLASSES = arrayListOf<Class<out ManufacturingEntity>>()

class EntityChangelogClassProviderImplTest {

    private val collector = EntityCollectorTestImpl(CLASSES)

    val provider = EntityChangelogClassProviderImpl(collector)

    @Test
    fun `#getClasses returns list of classes`() {
        val providerClasses = provider.getClasses()

        assertEquals(CLASSES, providerClasses)
    }

    @Test
    fun `#getClassPool returns pool where ManufacturingEntity and BaseManufacturing can be read`() {
        val classPool = provider.getClassPool()

        assertNotNull(classPool.get(ManufacturingEntity::class.java.name).classFile)
        assertNotNull(classPool.get(BaseManufacturing::class.java.name).classFile)
    }
}
