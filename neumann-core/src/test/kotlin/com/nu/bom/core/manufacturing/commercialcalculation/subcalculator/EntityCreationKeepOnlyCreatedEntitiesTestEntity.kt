package com.nu.bom.core.manufacturing.commercialcalculation.subcalculator

import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.entities.BomEntry
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.testentities.TestEntity

@TestEntity
@EntityType(Entities.NONE)
class EntityCreationKeepOnlyCreatedEntitiesTestEntity(name: String) : ManufacturingEntity(name) {
    fun input(): Text = throw Exception("Should be initialized")

    @EntityCreation(Entities.BOM_ENTRY)
    fun createBomEntry(input: Text): ManufacturingEntity {
        return createEntity(
            name = "Test",
            entityType = Entities.BOM_ENTRY,
            clazz = BomEntry::class,
            overwrites = mapOf(BomEntry::quantity.name to QuantityUnit(0.0)),
        )
    }
}
