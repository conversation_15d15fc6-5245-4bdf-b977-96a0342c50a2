package com.nu.bom.core.utils

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions

class ShapesDataTest {

    @Test
    fun `get technology fields returns unique fields for ts account`() {
        val shapesData = ShapesData()

        shapesData.addTechnologyField(Constants.TSET_ACCOUNT_IDENTIFIER, "shapeTechnology", "fieldName1")
        shapesData.addTechnologyField(Constants.TSET_ACCOUNT_IDENTIFIER, "shapeTechnology", "fieldName2")

        val fieldsForTechnology = shapesData.getTechnologyFields(Constants.TSET_ACCOUNT_IDENTIFIER, "shapeTechnology")
        Assertions.assertEquals(2, fieldsForTechnology.size)
        Assertions.assertEquals(listOf("fieldName1", "fieldName2"), fieldsForTechnology)
    }
}
