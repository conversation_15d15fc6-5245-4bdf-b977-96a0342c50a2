package com.nu.bom.core.manufacturing.commercialcalculation.configurationMapping.specificOperationMapping

import com.fasterxml.jackson.databind.ObjectMapper
import com.nu.bom.core.manufacturing.commercialcalculation.configurationMapping.MandatoryOperationCreator.createMandatoryExternalOperations
import com.nu.bom.core.manufacturing.commercialcalculation.configurationMapping.MandatoryOperationCreator.createMandatoryInternalCalculatedOperations
import com.nu.bom.core.manufacturing.commercialcalculation.configurationMapping.MandatoryOperationCreator.createMandatoryInternalInputOperations
import com.nu.bom.core.manufacturing.commercialcalculation.configurationMapping.MandatoryOperationCreator.createMandatoryOperationDtos
import com.nu.bom.core.manufacturing.commercialcalculation.configurationMapping.TestProcurementTypeConfigurationCreator
import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.CalculationConfigurationException
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.CalculationOperationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.UniqueOperationIdentifier
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.dtoMapper.CostOperationConfigurationDtoMappingService
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalActivityInputOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalCostInputOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.staticElements.StaticActivityElement
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.staticElements.StaticCostElement
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externaltointernalmapper.ExternalConfigToInternalConfigMapper.mapToInternalConfig
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.ActivityInputOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.InputSubElement
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.SubElementInputOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.CurrentCostOperationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.CurrentCostOperationConfigurationDto
import com.nu.bom.core.manufacturing.commercialcalculation.rollupconfiguration.RollUpConfiguration
import com.nu.bom.core.manufacturing.entities.BaseTool
import com.nu.bom.core.publicapi.dtos.configurations.enumerations.staticElements.StaticActivityElementDto
import com.nu.bom.core.publicapi.dtos.configurations.enumerations.staticElements.StaticCostElementDto
import com.nu.bom.core.publicapi.dtos.configurations.externalConfigurationOperations.ExternalActivityInputOperationDto
import com.nu.bom.core.publicapi.dtos.configurations.externalConfigurationOperations.ExternalCostInputOperationDto
import com.nu.bom.core.publicapi.dtos.configurations.fieldnamebuilders.AggregationLevelDto
import com.nu.bom.core.utils.JacksonTest
import com.tset.common.testing.assertReversible
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.DynamicTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestFactory
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal

@JacksonTest
class ActivityInputOperationMappingTests(
    @Autowired
    private val objectMapper: ObjectMapper,
) {
    private val validOrigins =
        mapOf(
            AggregationLevel.MACHINE to
                listOf(
                    StaticActivityElement.OCCUPANCY_MACHINE_ACTIVITY,
                    StaticActivityElement.NON_OCCUPANCY_MACHINE_ACTIVITY,
                    StaticActivityElement.SETUP_MACHINE_ACTIVITY,
                ),
            AggregationLevel.TOOL to
                listOf(
                    StaticActivityElement.TOOL_MAINTENANCE_ACTIVITY,
                ),
            AggregationLevel.LABOR to
                listOf(
                    StaticActivityElement.PRODUCTION_LABOR_ACTIVITY,
                    StaticActivityElement.SETUP_OPERATOR_ACTIVITY,
                    StaticActivityElement.SETUP_SETUP_WORKER_ACTIVITY,
                ),
            AggregationLevel.ROUGH_PROCESS to
                listOf(
                    StaticActivityElement.ROUGH_PROCESS_ACTIVITY,
                ),
        )

    private enum class Keys {
        Cost,
        Activity,
    }

    @TestFactory
    fun testValidOrigins(): List<DynamicTest> =
        validOrigins.flatMap { (level, elements) ->
            elements.map { element ->
                DynamicTest.dynamicTest(level.toString()) {
                    val op =
                        ExternalActivityInputOperation(
                            sourceElement = element,
                            destinationElementKey = "Test",
                            origin = level,
                        )
                    Assertions.assertEquals(level, op.origin)
                }
            }
        }

    @TestFactory
    fun testInvalidOrigins(): List<DynamicTest> {
        val all =
            AggregationLevel.entries.flatMap { level ->
                StaticActivityElement.entries.map {
                    Pair(level, it)
                }
            }
        val valid =
            validOrigins.flatMap { (level, values) ->
                values.map {
                    Pair(level, it)
                }
            }

        return (all - valid).map { (level, element) ->
            DynamicTest.dynamicTest("$level -> $element") {
                assertThrows<CalculationConfigurationException> {
                    ExternalActivityInputOperation(
                        sourceElement = element,
                        destinationElementKey = "Test",
                        origin = level,
                    )
                }.let { exception ->
                    Assertions.assertEquals(
                        CalculationConfigurationException.ErrorCode.INVALID_AGGREGATION_LEVEL,
                        exception.errorCode,
                        "Unexpected error code!",
                    )
                }
            }
        }
    }

    @Test
    fun `mapping succeeds`() {
        val operationConfigMapper = CostOperationConfigurationDtoMappingService()

        val dtos =
            listOf(
                ExternalCostInputOperationDto(
                    StaticCostElementDto.TOOL_MAINTENANCE_COSTS,
                    Keys.Cost.name,
                    AggregationLevelDto.TOOL,
                ),
                ExternalActivityInputOperationDto(
                    StaticActivityElementDto.TOOL_MAINTENANCE_ACTIVITY,
                    Keys.Activity.name,
                    AggregationLevelDto.TOOL,
                ),
            )
        val dto =
            CurrentCostOperationConfigurationDto(
                createMandatoryOperationDtos(AggregationLevelDto.MANUFACTURED_MATERIAL) + dtos,
                TestProcurementTypeConfigurationCreator.createDto(),
            )

        val externals =
            listOf(
                ExternalCostInputOperation(
                    StaticCostElement.TOOL_MAINTENANCE_COSTS,
                    Keys.Cost.name,
                    AggregationLevel.TOOL,
                ),
                ExternalActivityInputOperation(
                    StaticActivityElement.TOOL_MAINTENANCE_ACTIVITY,
                    Keys.Activity.name,
                    AggregationLevel.TOOL,
                    BigDecimal.ONE,
                ),
            )
        val external =
            CurrentCostOperationConfiguration(
                createMandatoryExternalOperations(AggregationLevel.MANUFACTURED_MATERIAL) + externals,
                TestProcurementTypeConfigurationCreator.createBusinessLogicLevel(),
            )

        val internals =
            listOf(
                SubElementInputOperation(
                    Keys.Cost.name,
                    AggregationLevel.TOOL,
                    null,
                ),
                ActivityInputOperation(
                    Keys.Activity.name,
                    AggregationLevel.TOOL,
                    subElements =
                        mapOf(
                            Keys.Cost.name to
                                InputSubElement(
                                    BaseTool::maintenanceCost.name,
                                ),
                        ),
                ),
            )
        val internal =
            CalculationOperationConfiguration(
                ValueType.COST,
                createMandatoryInternalCalculatedOperations(AggregationLevel.MANUFACTURED_MATERIAL),
                createMandatoryInternalInputOperations() + internals.associateBy { UniqueOperationIdentifier.fromOperation(it) },
                TestProcurementTypeConfigurationCreator.createBusinessLogicLevel(),
            )

        // mapping between Operation Config String and Operation Config Dto works as expected
        objectMapper.assertReversible(dto)

        // mapping between Operation Config Dto and Operation Config works as expected
        Assertions.assertEquals(operationConfigMapper.fromDto(dto), external)
        Assertions.assertEquals(operationConfigMapper.toDto(external), dto)

        // mapping between External Config and Internal Config works as expected
        val createdInternalConfig = mapToInternalConfig(ValueType.COST, external, RollUpConfiguration())
        Assertions.assertEquals(createdInternalConfig, internal)
    }

    @Test
    fun `mapping fails because of missing input operation`() {
        val operation =
            ExternalActivityInputOperation(
                StaticActivityElement.TOOL_MAINTENANCE_ACTIVITY,
                "key",
                AggregationLevel.TOOL,
            )

        val externalOperationConfig =
            CurrentCostOperationConfiguration(
                listOf(operation) +
                    createMandatoryExternalOperations(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                    ),
                TestProcurementTypeConfigurationCreator.createBusinessLogicLevel(),
            )

        assertThrows<CalculationConfigurationException> {
            mapToInternalConfig(
                ValueType.COST,
                externalOperationConfig,
                RollUpConfiguration(),
            )
        }.let { exception ->
            Assertions.assertEquals(
                CalculationConfigurationException.ErrorCode.MISSING_INPUT,
                exception.errorCode,
                "Unexpected error code!",
            )
        }
    }
}
