package com.nu.bom.core.utils

import com.nu.bom.core.manufacturing.fieldTypes.FinishingType
import com.nu.bom.core.manufacturing.fieldTypes.InnerNull
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Null
import com.nu.bom.core.manufacturing.fieldTypes.PositiveQuantity
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.math.BigInteger
import kotlin.jvm.java

class FieldFactoryTest {
    @Test
    fun `field factory should fallback to string for PositiveQuantity`() {
        val result = FieldFactory.createField(PositiveQuantity::class, 5)
        assertThat(result.res).isEqualByComparingTo(BigInteger.valueOf(5))
        assertThat(result::class).isSameAs(PositiveQuantity::class)
    }

    @Test
    fun `field factory via parse should fallback to string`() {
        val result = FieldFactory.parseFieldImpl(PositiveQuantity::class.java, 5)
        assertThat(result.res).isEqualTo(BigInteger.valueOf(5))
        assertThat(result::class).isSameAs(PositiveQuantity::class)
    }

    @Test
    fun `field factory via parse should create money`() {
        val result = FieldFactory.parseFieldImpl(Money::class.java, 5) as Money
        assertThat(result.res).isEqualByComparingTo("5")
        assertThat(result.inputCurrency).isNull()
    }

    @Test
    fun `field factory via parse should create money from long`() {
        val result = FieldFactory.parseFieldImpl(Money::class.java, 5L) as Money
        assertThat(result.res).isEqualByComparingTo("5")
        assertThat(result.inputCurrency).isNull()
    }

    @Test
    fun `field factory via parse should parse null type to null ignoring value`() {
        val result = FieldFactory.parseFieldImpl(Null::class.java, 5) as Null
        assertThat(result.res).isEqualTo(InnerNull)
    }

    @Test
    fun `field factory via parse should parse null value to null ignoring type`() {
        val result = FieldFactory.parseFieldImpl(Money::class.java, null) as Null
        assertThat(result.res).isEqualTo(InnerNull)
    }

    @Test
    fun `field factory via parse should parse value with unit`() {
        val result = FieldFactory.parseFieldImpl(Weight::class.java, 5, "KILOGRAM") as Weight
        assertThat(result.res).isEqualByComparingTo("5")
    }

    @Test
    fun `field factory via parse should parse an enum`() {
        val result = FieldFactory.parseFieldImpl(FinishingType::class.java, FinishingType.Selection.honing) as FinishingType
        assertThat(result).isEqualTo(FinishingType.honing)
    }
}
