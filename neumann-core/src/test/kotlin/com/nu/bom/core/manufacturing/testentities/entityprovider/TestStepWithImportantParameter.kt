package com.nu.bom.core.manufacturing.testentities.entityprovider

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Num

@EntityType(Entities.MANUFACTURING_STEP)
class TestStepWithImportantParameter(name: String) : ManufacturingEntity(name) {
    @Input
    fun importantParameter(): Num? = null
}
