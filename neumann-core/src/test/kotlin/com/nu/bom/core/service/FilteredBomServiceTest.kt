package com.nu.bom.core.service

import com.nu.bom.core.manufacturing.entities.BomEntry
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.Tool
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.service.bomrads.BomNodeLoaderService
import com.nu.bom.core.utils.AccountUtil
import com.nu.bom.core.utils.assertThatMono
import org.assertj.core.groups.Tuple.tuple
import org.bson.types.ObjectId
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import reactor.core.publisher.Mono

class FilteredBomServiceTest {
    private val bomNodeLoaderService = mock<BomNodeLoaderService>()
    private val bomNodeConversionService = mock<BomNodeConversionService>()
    private val sut = FilteredBomService(bomNodeLoaderService, bomNodeConversionService, mock())

    @Test
    fun `filter should only return specific type`() {
        whenever(
            bomNodeLoaderService.getBomNode(any(), any(), anyOrNull(), any()),
        ).thenReturn(Mono.just(BomNodeSnapshot("a", 1882, "title", null, null)))
        whenever(bomNodeConversionService.bomNodeToManufacturingCalculationTree(any(), anyOrNull())).thenReturn(
            sampleBomEntry,
        )
        val findEntitiesOfChildBomEntries = sut.findEntitiesOfChildBomEntries(Companion.dummyBomNodeAccess(), Entities.TOOL, null)
        assertThatMono(
            findEntitiesOfChildBomEntries,
        ).extracting(
            ManufacturingEntity::name,
        ).containsExactlyInAnyOrder(tuple("Tool 1 1"), tuple("Tool 1 1 2"), tuple("Tool 1 1 1"), tuple("Tool 1 2"), tuple("Tool 2 1 2"))
    }

    companion object {
        fun dummyBomNodeAccess() = BomNodeAccessInfo(AccountUtil.dummyAccessCheck(), ObjectId(), ObjectId())

        val sampleBomEntry =
            BomEntry("BOM").apply {
                addChild(
                    BomEntry("Sub 1").apply {
                        addFieldResult("displayDesignation") {
                            Text("Sub 1")
                        }

                        addChild(Tool("Tool 1 1"))
                        addChild(Tool("Tool 1 2"))

                        addChild(
                            BomEntry("Sub 1 1").apply {
                                addFieldResult("displayDesignation") {
                                    Text("Sub 1 1")
                                }
                                addChild(Tool("Tool 1 1 1"))
                                addChild(Tool("Tool 1 1 2"))
                            },
                        )
                    },
                )
                addChild(
                    BomEntry("Sub 2").apply {
                        addFieldResult("displayDesignation") {
                            Text("Sub 2")
                        }
                        addChild(
                            BomEntry("Sub 2 1").apply {

                                addFieldResult("displayDesignation") {
                                    Text("Sub 2 1")
                                }
                                addChild(Tool("Tool 2 1 2"))
                            },
                        )
                    },
                )
            }
    }
}
