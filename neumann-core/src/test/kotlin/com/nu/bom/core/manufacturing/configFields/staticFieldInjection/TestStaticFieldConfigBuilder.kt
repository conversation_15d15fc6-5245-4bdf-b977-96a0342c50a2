package com.nu.bom.core.manufacturing.configFields.staticFieldInjection

import com.nu.bom.core.manufacturing.configFields.behaviourFieldInjection.fieldBuilder.TestContext
import com.nu.bom.core.manufacturing.configurablefields.builder.StaticFieldConfigBuilder
import com.nu.bom.core.manufacturing.configurablefields.config.ChildRelation
import com.nu.bom.core.manufacturing.configurablefields.config.EntityFieldConfigurations
import com.nu.bom.core.manufacturing.configurablefields.config.EntityFieldConfigurationsWithContext
import com.nu.bom.core.manufacturing.configurablefields.config.FieldConfig
import com.nu.bom.core.manufacturing.configurablefields.config.ParameterConfig
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Money

class TestStaticFieldConfigBuilder : StaticFieldConfigBuilder {
    override fun createStaticEntityFieldConfigs(): EntityFieldConfigurationsWithContext {
        val result = EntityFieldConfigurations()

        val dynamicChildFieldName = "dynamicAssignedMoneyField"
        result.add(
            StaticChildTestEntityForFieldInjection::class,
            FieldConfig(
                dynamicChildFieldName,
                TestContext::assignMoneyField,
                "value",
                ParameterConfig(fieldName = StaticChildTestEntityForFieldInjection::inputCost.name),
                entityFieldMetaInfos = listOf(),
                fieldClass = Money::class,
                availableOutsideTheEngine = false,
            ),
        )

        result.add(
            StaticParentTestEntityForFieldInjection::class,
            FieldConfig(
                "dynamicSumMoneyFieldOfDynamicChildFields",
                TestContext::sumOf,
                "summands",
                ParameterConfig(fieldName = dynamicChildFieldName, relation = ChildRelation(Entities.CONSUMABLE)),
                entityFieldMetaInfos = listOf(),
                fieldClass = Money::class,
                availableOutsideTheEngine = false,
            ),
        )

        result.add(
            StaticParentTestEntityForFieldInjection::class,
            FieldConfig(
                "dynamicSumOfInputCostField",
                TestContext::sumOf,
                "summands",
                ParameterConfig(
                    fieldName = StaticChildTestEntityForFieldInjection::inputCost.name,
                    relation = ChildRelation(Entities.CONSUMABLE),
                ),
                entityFieldMetaInfos = listOf(),
                fieldClass = Money::class,
                availableOutsideTheEngine = false,
            ),
        )

        return EntityFieldConfigurationsWithContext(
            result,
            TestContext("TestContext"),
        )
    }
}
