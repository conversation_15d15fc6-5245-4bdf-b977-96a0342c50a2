package com.nu.bom.core.manufacturing.testentities.parentannotation

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Currency
import com.nu.bom.core.manufacturing.fieldTypes.ExchangeRatesField
import com.nu.bom.core.manufacturing.fieldTypes.Num
import java.math.BigDecimal

@EntityType(Entities.MACHINE)
class TestMachineForParentParamWithInput(name: String) : ManufacturingEntity(name) {
    @Input
    fun stepParam(): Num = throw IllegalArgumentException("missing.input.stepParam")

    val baseCurrency: Currency = Currency("EUR")

    val masterdataBaseCurrency: Currency = Currency("EUR")

    val exchangeRates: ExchangeRatesField = ExchangeRatesField(mapOf(com.tset.core.service.domain.Currency("EUR") to BigDecimal.ONE))
}
