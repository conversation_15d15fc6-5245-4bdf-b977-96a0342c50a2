package com.nu.bom.core.utils

import com.nu.bom.core.service.bomrads.BomradsService
import com.nu.bom.core.user.AccessCheck
import com.nu.bomrads.dto.admin.BomNodeDTO
import com.nu.bomrads.dto.admin.BomNodeUpdateDTO
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono

@Service
class BomradRestUtil(
    private val bomrads: BomradsService,
) {
    fun createMockBomNode(
        accessCheck: AccessCheck,
        dto: BomNodeUpdateDTO,
    ): Mono<BomNodeDTO> {
        logger.info("creating mock BomNode for ${accessCheck.accountName} - id: ${dto.id}")
        return bomrads.nuService().withTsetService().postToMono(
            baseUrl = bomrads.nuService().baseUrl(),
            headers = bomrads.nuService().getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            uri = {
                it.path("/api/admin/bom-nodes").build()
            },
            requestBody = dto,
            successHandler = { response -> response.bodyToMono(BomNodeDTO::class.java) },
        )
    }

    companion object {
        private val logger = LoggerFactory.getLogger(BomradRestUtil::class.java)
    }
}
