package com.nu.bom.core.manufacturing.fieldTypes

import org.junit.jupiter.api.Test
import java.math.BigDecimal

class InputGroupTest {
    private val testLength = Length(BigDecimal.TEN, LengthUnits.METER)

    @Test
    fun checkDimensionsForCuboid() {
        assert(
            InputGroup.dimensionsAreGreaterThanZeroForPartInputGroup(
                partInputGroup = InputGroup.CUBOID,
                partLength = testLength,
                partWidth = testLength,
                partHeight = testLength,
                partOuterDiameter = null,
                partUpperWidth = Length.ZERO,
            ),
        )
        assert(
            !InputGroup.dimensionsAreGreaterThanZeroForPartInputGroup(
                partInputGroup = InputGroup.CUBOID,
                partLength = null,
                partWidth = testLength,
                partHeight = testLength,
                partOuterDiameter = null,
                partUpperWidth = Length.ZERO,
            ),
        )
        assert(
            !InputGroup.dimensionsAreGreaterThanZeroForPartInputGroup(
                partInputGroup = InputGroup.CUBOID,
                partLength = Length.ZERO,
                partWidth = testLength,
                partHeight = testLength,
                partOuterDiameter = null,
                partUpperWidth = Length.ZERO,
            ),
        )
    }

    @Test
    fun checkDimensionsForCylinder() =
        assert(
            InputGroup.dimensionsAreGreaterThanZeroForPartInputGroup(
                partInputGroup = InputGroup.CYLINDER,
                partLength = null,
                partWidth = null,
                partHeight = testLength,
                partOuterDiameter = testLength,
                partUpperWidth = Length.ZERO,
            ),
        )

    @Test
    fun checkDimensionsForPipe() =
        assert(
            InputGroup.dimensionsAreGreaterThanZeroForPartInputGroup(
                partInputGroup = InputGroup.PIPE,
                partLength = testLength,
                partWidth = null,
                partHeight = null,
                partOuterDiameter = testLength,
                partUpperWidth = Length.ZERO,
            ),
        )
}
