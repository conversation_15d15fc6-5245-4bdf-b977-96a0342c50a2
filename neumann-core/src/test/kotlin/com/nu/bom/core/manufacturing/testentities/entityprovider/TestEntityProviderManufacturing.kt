package com.nu.bom.core.manufacturing.testentities.entityprovider

import com.nu.bom.core.manufacturing.annotations.EntityProvider
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.OrderedEntityCreation
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Text

@EntityType(Entities.MANUFACTURING)
class TestEntityProviderManufacturing(name: String) : BaseManufacturing(name) {

    @Input
    fun assemblyParameter(): Text = Text("default-value")

    @Input
    fun needCleaning(): Bool = Bool(true)

    @OrderedEntityCreation
    fun childSteps() =
        arrayOf("cleaning", "assembly", "inspection")

    @EntityProvider
    fun cleaning(needCleaning: Bool): ManufacturingEntity? {
        return if (needCleaning.isTrue()) {
            TestStepWithImportantParameter(
                "cleaning-step"
            )
        } else {
            null
        }
    }

    @EntityProvider
    fun assembly(assemblyParameter: Text): ManufacturingEntity =
        TestAssembly("assembly-step-" + assemblyParameter.res)
}
