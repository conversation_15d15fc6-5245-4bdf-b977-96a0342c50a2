package com.nu.bom.core.service

import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.manufacturing.annotations.TranslationSection
import com.nu.bom.core.threedb.ThreeDbFunction
import com.nu.bom.core.threedb.ThreeDbServiceMock
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.Constants.TSET_ACCOUNT_IDENTIFIER
import com.nu.bom.core.utils.ShapesData
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test

private const val TECHNOLOGY = "DCA"
private const val FIELD_NAME = "fieldName"
private const val SHAPE_ID = "shapeId"
private const val FIELD_VALUE = 42
private const val FIELD_TYPE = "Length"
private const val FIELD_UNIT = "KM"
private const val ACCOUNT_IDENTIFIER = TSET_ACCOUNT_IDENTIFIER
private val SHAPE =
    ShapesData.ShapeInfo(
        shapeId = SHAPE_ID,
        technology = TECHNOLOGY.lowercase(),
        inputGroup = "inputGroup",
        shapeTechnologyGroup = "shapeTechnologyGroup",
        active = true,
        fields =
            listOf(
                ShapesData.ShapeField(
                    name = FIELD_NAME,
                    type = FIELD_TYPE,
                    unit = FIELD_UNIT,
                    value = FIELD_VALUE,
                ),
            ),
        accountIdentifier = ACCOUNT_IDENTIFIER,
    )

class ShapesServiceTest {
    private val threeDbService = ThreeDbServiceMock()
    private val shapeData = ShapesData()
    private val service = ShapesService(shapeData, threeDbService)

    @Test
    fun `#getShapeFields returns empty list for empty input`() {
        val result = service.getShapeFields(createAccessCheck(ACCOUNT_IDENTIFIER), "anyTechnology", "anyShapeId")

        assertThat(result).isEmpty()
    }

    @Test
    fun `#getShapeFields returns account specific fields for specific technology shape`() {
        shapeData.addTechnologyField("ktm", TECHNOLOGY, FIELD_NAME)
        shapeData.addShape(SHAPE)

        val result = service.getShapeFields(createAccessCheck("ktm"), TECHNOLOGY, SHAPE_ID)

        assertThat(result).hasSize(1)
            .contains(
                FieldParameter(
                    name = FIELD_NAME,
                    type = FIELD_TYPE,
                    unit = FIELD_UNIT,
                    value = FIELD_VALUE,
                    metaInfo =
                        mapOf(
                            "translationSection" to TranslationSection.SELECTABLES,
                            "translationLabel" to "${FIELD_NAME}_shapeField",
                        ),
                    denominatorUnit = null,
                ),
            )
    }

    @Test
    fun `#getShapeFields returns account specific fields and default for specific technology shape`() {
        shapeData.addTechnologyField("ktm", TECHNOLOGY, FIELD_NAME)
        shapeData.addTechnologyField(ACCOUNT_IDENTIFIER, TECHNOLOGY, FIELD_NAME)
        shapeData.addShape(SHAPE)

        val result = service.getShapeFields(createAccessCheck("ktm"), TECHNOLOGY, SHAPE_ID)

        assertThat(result).hasSize(2)
            .contains(
                FieldParameter(
                    name = FIELD_NAME,
                    type = FIELD_TYPE,
                    unit = FIELD_UNIT,
                    value = FIELD_VALUE,
                    metaInfo =
                        mapOf(
                            "translationSection" to TranslationSection.SELECTABLES,
                            "translationLabel" to "${FIELD_NAME}_shapeField",
                        ),
                    denominatorUnit = null,
                ),
            )
    }

    @Test
    fun `#getShapeFields returns empty list when field does not exists`() {
        shapeData.addTechnologyField(ACCOUNT_IDENTIFIER, TECHNOLOGY, "otherField")
        shapeData.addShape(SHAPE)

        val result = service.getShapeFields(createAccessCheck(ACCOUNT_IDENTIFIER), TECHNOLOGY, SHAPE_ID)

        assertThat(result).isEmpty()
    }

    @Test
    fun `getActiveShapes does not call ThreeDb if no shapes are active on NBK side`() {
        assert(!threeDbService.otherFunctionCalls.containsKey(ThreeDbFunction.GetShapes))
        val noActiveShapes = service.getActiveShapes(createAccessCheck(ACCOUNT_IDENTIFIER), "wizard_id", TECHNOLOGY).block()!!

        assert(noActiveShapes.isEmpty())
        assert(!threeDbService.otherFunctionCalls.containsKey(ThreeDbFunction.GetShapes))

        shapeData.addShape(SHAPE.copy(shapeId = "S_001")) // the threeDbMock returns only S_something

        val oneActiveShape = service.getActiveShapes(createAccessCheck(ACCOUNT_IDENTIFIER), "wizard_id", TECHNOLOGY).block()!!

        Assertions.assertEquals(oneActiveShape.size, 1)
        Assertions.assertEquals(threeDbService.otherFunctionCalls[ThreeDbFunction.GetShapes], 1)
    }

    companion object {
        private fun createAccessCheck(accountName: String): AccessCheck =
            AccessCheck(
                accountId = "accId",
                accountName = accountName,
                accountLabel = "label",
                token = "token",
                realm = "default",
            )
    }
}
