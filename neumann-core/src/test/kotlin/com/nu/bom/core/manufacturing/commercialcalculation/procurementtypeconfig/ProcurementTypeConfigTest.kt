package com.nu.bom.core.manufacturing.commercialcalculation.procurementtypeconfig

import com.nu.bom.core.exception.ConfigurationUserException
import com.nu.bom.core.manufacturing.commercialcalculation.configurationMapping.MandatoryOperationCreator.createMandatoryExternalOperations
import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.MaterialClass
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalProcurementTypeAndMaterialClass
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalTransferOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.CurrentCostOperationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.CustomProcurementTypeWrapper
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.ProcurementTypeConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.ProcurementTypeWithInhouseOrPurchase
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetProcurementType
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class ProcurementTypeConfigTest {
    @Test
    fun `can construct a procurement type config`() {
        ProcurementTypeConfiguration(
            listOf(
                ProcurementTypeWithInhouseOrPurchase(CustomProcurementTypeWrapper("A"), ManufacturingType.Type.INHOUSE),
                ProcurementTypeWithInhouseOrPurchase(CustomProcurementTypeWrapper("B"), ManufacturingType.Type.PURCHASE),
            ),
            MaterialClass.entries.associateWith { CustomProcurementTypeWrapper("A") },
            CustomProcurementTypeWrapper("B"),
            "header",
        )
    }

    @Test
    fun `throws if a material class has no default`() {
        assertThrows<ConfigurationUserException> {
            ProcurementTypeConfiguration(
                listOf(ProcurementTypeWithInhouseOrPurchase(CustomProcurementTypeWrapper("A"), ManufacturingType.Type.PURCHASE)),
                mapOf(MaterialClass.RAW_MATERIAL to CustomProcurementTypeWrapper("A")),
                CustomProcurementTypeWrapper("A"),
                "header",
            )
        }.let { exception ->
            assertThat(exception.message).contains(
                "For all the following material classes a default should be defined: ${MaterialClass.entries}. " +
                    "Got definitions for [RAW_MATERIAL]",
            )
        }
    }

    @Test
    fun `throws if a default is not provided`() {
        assertThrows<ConfigurationUserException> {
            ProcurementTypeConfiguration(
                listOf(ProcurementTypeWithInhouseOrPurchase(CustomProcurementTypeWrapper("A"), ManufacturingType.Type.PURCHASE)),
                MaterialClass.entries.associateWith { CustomProcurementTypeWrapper("B") },
                CustomProcurementTypeWrapper("C"),
                "header",
            )
        }.let { exception ->
            assertThat(exception.message).contains(
                "In the ProcurementTypeConfiguration [A] are provided but [B, C] are used as defaults. " +
                    "Please add the missing types to procurementTypesToInhouseOrPurchase",
            )
        }
    }

    @Test
    fun `throws if external step is defined as inhouse`() {
        assertThrows<ConfigurationUserException> {
            ProcurementTypeConfiguration(
                listOf(ProcurementTypeWithInhouseOrPurchase(CustomProcurementTypeWrapper("A"), ManufacturingType.Type.INHOUSE)),
                MaterialClass.entries.associateWith { CustomProcurementTypeWrapper("A") },
                CustomProcurementTypeWrapper("A"),
                "header",
            )
        }.let { exception ->
            assertThat(exception.message).contains(
                "The external step procurementType `A` can not be inhouse!",
            )
        }
    }

    @Test
    fun `throws if a transfer def is missing`() {
        assertThrows<ConfigurationUserException> {
            CurrentCostOperationConfiguration(
                createMandatoryExternalOperations(AggregationLevel.MATERIAL_USAGE),
                ProcurementTypeConfiguration(
                    listOf(
                        ProcurementTypeWithInhouseOrPurchase(
                            CustomProcurementTypeWrapper("A"),
                            ManufacturingType.Type.INHOUSE,
                        ),
                        ProcurementTypeWithInhouseOrPurchase(
                            CustomProcurementTypeWrapper("B"),
                            ManufacturingType.Type.PURCHASE,
                        ),
                        ProcurementTypeWithInhouseOrPurchase(
                            TsetProcurementType.PURCHASE.customProcurementType,
                            ManufacturingType.Type.PURCHASE,
                        ),
                        ProcurementTypeWithInhouseOrPurchase(
                            TsetProcurementType.PROVIDED.customProcurementType,
                            ManufacturingType.Type.PURCHASE,
                        ),
                        ProcurementTypeWithInhouseOrPurchase(
                            TsetProcurementType.INTER_COMPANY.customProcurementType,
                            ManufacturingType.Type.PURCHASE,
                        ),
                        ProcurementTypeWithInhouseOrPurchase(
                            TsetProcurementType.DIRECTED_BUY.customProcurementType,
                            ManufacturingType.Type.PURCHASE,
                        ),
                        ProcurementTypeWithInhouseOrPurchase(
                            TsetProcurementType.INHOUSE.customProcurementType,
                            ManufacturingType.Type.INHOUSE,
                        ),
                    ),
                    MaterialClass.entries.associateWith { CustomProcurementTypeWrapper("A") },
                    CustomProcurementTypeWrapper("B"),
                    "header",
                ),
            )
        }.let { exception ->
            assertThat(exception.message).contains(
                "The provided, non in-house procurementTypes are " +
                    "[B, tset.ref.lov-entry.purchased, tset.ref.lov-entry.provided, " +
                    "tset.ref.lov-entry.inter-company, tset.ref.lov-entry.directed-buy]. " +
                    "For the material class `PURCHASED_PART` for [tset.ref.lov-entry.purchased, tset.ref.lov-entry.provided, " +
                    "tset.ref.lov-entry.inter-company, tset.ref.lov-entry.directed-buy] " +
                    "transfers are defined. Those two sets should be identical.",
            )
        }
    }

    @Test
    fun `throws if a transfer def is missing for a certain material type`() {
        val ops =
            listOf(
                ExternalTransferOperation(
                    listOf(
                        ExternalProcurementTypeAndMaterialClass("B", MaterialClass.PURCHASED_PART),
                    ),
                    "SOURCE",
                    "TARGET",
                    AggregationLevel.SUB_MATERIAL,
                ),
            )
        assertThrows<ConfigurationUserException> {
            CurrentCostOperationConfiguration(
                createMandatoryExternalOperations(AggregationLevel.MATERIAL_USAGE) + ops,
                ProcurementTypeConfiguration(
                    listOf(
                        ProcurementTypeWithInhouseOrPurchase(
                            CustomProcurementTypeWrapper("B"),
                            ManufacturingType.Type.PURCHASE,
                        ),
                        ProcurementTypeWithInhouseOrPurchase(
                            TsetProcurementType.PURCHASE.customProcurementType,
                            ManufacturingType.Type.PURCHASE,
                        ),
                        ProcurementTypeWithInhouseOrPurchase(
                            TsetProcurementType.PROVIDED.customProcurementType,
                            ManufacturingType.Type.PURCHASE,
                        ),
                        ProcurementTypeWithInhouseOrPurchase(
                            TsetProcurementType.INTER_COMPANY.customProcurementType,
                            ManufacturingType.Type.PURCHASE,
                        ),
                        ProcurementTypeWithInhouseOrPurchase(
                            TsetProcurementType.DIRECTED_BUY.customProcurementType,
                            ManufacturingType.Type.PURCHASE,
                        ),
                        ProcurementTypeWithInhouseOrPurchase(
                            TsetProcurementType.INHOUSE.customProcurementType,
                            ManufacturingType.Type.INHOUSE,
                        ),
                    ),
                    MaterialClass.entries.associateWith { CustomProcurementTypeWrapper("B") },
                    CustomProcurementTypeWrapper("B"),
                    "header",
                ),
            )
        }.let { exception ->
            assertThat(exception.message).contains(
                "The provided, non in-house procurementTypes are " +
                    "[B, tset.ref.lov-entry.purchased, tset.ref.lov-entry.provided, " +
                    "tset.ref.lov-entry.inter-company, tset.ref.lov-entry.directed-buy]. " +
                    "For the material class `CONSUMABLE` for [tset.ref.lov-entry.purchased, tset.ref.lov-entry.provided, " +
                    "tset.ref.lov-entry.inter-company, tset.ref.lov-entry.directed-buy] " +
                    "transfers are defined. Those two sets should be identical.",
            )
        }
    }

    @Test
    fun `throws if a transfer defines an unknown type`() {
        val ops =
            listOf(
                ExternalTransferOperation(
                    listOf(
                        ExternalProcurementTypeAndMaterialClass("A", MaterialClass.RAW_MATERIAL),
                        ExternalProcurementTypeAndMaterialClass("B", null),
                    ),
                    "SOURCE",
                    "TARGET",
                    AggregationLevel.SUB_MATERIAL,
                ),
            )
        assertThrows<ConfigurationUserException> {
            CurrentCostOperationConfiguration(
                createMandatoryExternalOperations(AggregationLevel.MATERIAL_USAGE) + ops,
                ProcurementTypeConfiguration(
                    listOf(
                        ProcurementTypeWithInhouseOrPurchase(
                            CustomProcurementTypeWrapper("A"),
                            ManufacturingType.Type.INHOUSE,
                        ),
                        ProcurementTypeWithInhouseOrPurchase(
                            CustomProcurementTypeWrapper("B"),
                            ManufacturingType.Type.PURCHASE,
                        ),
                        ProcurementTypeWithInhouseOrPurchase(
                            TsetProcurementType.PURCHASE.customProcurementType,
                            ManufacturingType.Type.PURCHASE,
                        ),
                        ProcurementTypeWithInhouseOrPurchase(
                            TsetProcurementType.PROVIDED.customProcurementType,
                            ManufacturingType.Type.PURCHASE,
                        ),
                        ProcurementTypeWithInhouseOrPurchase(
                            TsetProcurementType.INTER_COMPANY.customProcurementType,
                            ManufacturingType.Type.PURCHASE,
                        ),
                        ProcurementTypeWithInhouseOrPurchase(
                            TsetProcurementType.DIRECTED_BUY.customProcurementType,
                            ManufacturingType.Type.PURCHASE,
                        ),
                        ProcurementTypeWithInhouseOrPurchase(
                            TsetProcurementType.INHOUSE.customProcurementType,
                            ManufacturingType.Type.INHOUSE,
                        ),
                    ),
                    MaterialClass.entries.associateWith { CustomProcurementTypeWrapper("A") },
                    CustomProcurementTypeWrapper("B"),
                    "header",
                ),
            )
        }.let { exception ->
            assertThat(exception.message).contains(
                "The provided, non in-house procurementTypes are " +
                    "[B, tset.ref.lov-entry.purchased, tset.ref.lov-entry.provided, " +
                    "tset.ref.lov-entry.inter-company, tset.ref.lov-entry.directed-buy]. " +
                    "For the material class `RAW_MATERIAL` for " +
                    "[A, tset.ref.lov-entry.purchased, tset.ref.lov-entry.provided, " +
                    "tset.ref.lov-entry.inter-company, tset.ref.lov-entry.directed-buy, B] " +
                    "transfers are defined. Those two sets should be identical.",
            )
        }
    }
}
