package com.nu.bom.core.utils

import com.nu.bom.core.manufacturing.utils.safeDivision
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import java.math.BigDecimal

class BigDecimalExtensionTests {
    @Test
    fun `Save division with null divisor should return null`() {
        val result = BigDecimal.ONE.safeDivision(null)
        Assertions.assertNull(result)
    }

    @Test
    fun `Save division with zero divisor should return null`() {
        val result = BigDecimal.ONE.safeDivision(BigDecimal.ZERO)
        Assertions.assertNull(result)
    }

    @Test
    fun `Save division with non zero divisor should return division result`() {
        val result = BigDecimal.ONE.safeDivision(BigDecimal(2.0))
        Assertions.assertTrue(result?.compareTo(BigDecimal(0.5)) == 0)
    }

    @Test
    fun `Save division with null divisor and default should return default value`() {
        val result = BigDecimal.ONE.safeDivision(null, BigDecimal.ONE)
        Assertions.assertEquals(BigDecimal.ONE, result)
    }

    @Test
    fun `Save division with zero divisor and default should return default value`() {
        val result = BigDecimal.ONE.safeDivision(BigDecimal.ZERO, BigDecimal.ONE)
        Assertions.assertEquals(BigDecimal.ONE, result)
    }

    @Test
    fun `Save division with non zero divisor and default should return division result`() {
        val result = BigDecimal.ONE.safeDivision(BigDecimal(2.0), BigDecimal.ZERO)
        Assertions.assertTrue(result.compareTo(BigDecimal(0.5)) == 0)
    }
}
