package com.nu.bom.core.utils.changelog

import com.nu.bom.core.manufacturing.testentities.ChangelogGeneratorTestEntity
import com.nu.bom.core.utils.EntityCollectorImpl
import javassist.ClassClassPath
import javassist.ClassPool
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.time.LocalDate
import java.time.format.DateTimeFormatter

private val TEST_CLASS = ChangelogGeneratorTestEntity::class.java
private val CLASS_NAME = TEST_CLASS.name
private val DATE = LocalDate.of(2020, 7, 11)
private val DATE_STR = DateTimeFormatter.ISO_DATE.format(DATE)
private const val CLASS_HASH = "4a6d2714b41c24b9d0a2b5425df0a6a22402f814e3909144b4f8a4780d5a6e47"
private const val INITIAL_COMMENT = "initial comment"
private val CHANGE_LOG_LINE = "[$DATE_STR]\t$CLASS_NAME\t$CLASS_HASH\n"
private val CHANGE_LOG_LINE_INITIAL = "[$DATE_STR]\t$CLASS_NAME\t$CLASS_HASH\t$INITIAL_COMMENT\n"
private val CHANGE_LOG_LINE_NEW_CLASS = "[$DATE_STR]\t$CLASS_NAME\t$CLASS_HASH\t$ENTITY_CHANGELOG_NEW_CLASS_COMMENT\n"
private val CHANGE_LOG_LINE_WITH_DESCRIPTION = "[$DATE_STR]\t$CLASS_NAME\t$CLASS_HASH\tsome change description\n"
private val CHANGE_LOG_OTHER_HASH = "[$DATE_STR]\t$CLASS_NAME\tsomeOtherHash\tprevious comment\n"
private val CHANGE_LOG_OTHER_CLASS = "[$DATE_STR]\tsomeClass.name\t$CLASS_HASH\n"
private val CHANGE_LOG_OTHER_LINE = "[$DATE_STR]\t${EntityChangelogGeneratorTest::class.java.name}\tbff8746233e97b3728e450044f0cc40f2c059486151d149b3170f85b44fa8c15\n"

class EntityChangelogGeneratorTest {
    private val generator = EntityChangelogGenerator(EntityChangelogClassProviderTestImpl())

    @Test
    fun `#generate makes stream with class name and hash`() {
        val output = ByteArrayOutputStream()

        generator.generate(output, comment = null)

        val line = output.toUtf8String()
        assertEquals(CHANGE_LOG_LINE, line)
    }

    @Test
    fun `#generate makes stream with class name, hash and provided comment`() {
        val output = ByteArrayOutputStream()

        generator.generate(output, INITIAL_COMMENT)

        val line = output.toUtf8String()
        assertEquals(CHANGE_LOG_LINE_INITIAL, line)
    }

    @Test
    fun `#generate makes empty log when requested entity not found `() {
        val generator = EntityChangelogGenerator(NotExistingEntityChangelogClassProviderTestImpl())
        val output = ByteArrayOutputStream()

        generator.generate(output)

        val line = output.toUtf8String()
        assertEquals("", line)
    }

    @Test
    fun `#checkAndUpdate returns 0 changed classes`() {
        val input = CHANGE_LOG_LINE.toInputStream()

        val num = generator.checkAndUpdate(input)

        assertEquals(0, num)
    }

    @Test
    fun `#checkAndUpdate returns 0 changed classes when changelog contains also description`() {
        val input = CHANGE_LOG_LINE_WITH_DESCRIPTION.toInputStream()

        val num = generator.checkAndUpdate(input)

        assertEquals(0, num)
    }

    @Test
    fun `#checkAndUpdate writes the same data to output when nothing changes`() {
        val input = CHANGE_LOG_LINE.toInputStream()
        val output = ByteArrayOutputStream()

        generator.checkAndUpdate(input, output)

        assertEquals(CHANGE_LOG_LINE, output.toUtf8String())
    }

    @Test
    fun `#checkAndUpdate returns 1 of changed classes when hash changes`() {
        val input = CHANGE_LOG_OTHER_HASH.toInputStream()

        val num = generator.checkAndUpdate(input)

        assertEquals(1, num)
    }

    @Test
    fun `#checkAndUpdate writes new changelog entry with new hash when hash changes`() {
        val input = CHANGE_LOG_OTHER_HASH.toInputStream()
        val output = ByteArrayOutputStream()

        generator.checkAndUpdate(input, output)

        assertEquals(CHANGE_LOG_OTHER_HASH + CHANGE_LOG_LINE, output.toUtf8String())
    }

    @Test
    fun `#checkAndUpdate returns 1 of changed classes when class name changes`() {
        val input = CHANGE_LOG_OTHER_CLASS.toInputStream()

        val num = generator.checkAndUpdate(input)

        assertEquals(1, num)
    }

    @Test
    fun `#checkAndUpdate writes new changelog entry when class name changes`() {
        val input = CHANGE_LOG_OTHER_CLASS.toInputStream()
        val output = ByteArrayOutputStream()

        generator.checkAndUpdate(input, output)

        assertEquals(CHANGE_LOG_OTHER_CLASS + CHANGE_LOG_LINE_NEW_CLASS, output.toUtf8String())
    }

    @Test
    fun `#checkAndUpdate returns 1 of changed classes when stream is empty`() {
        val input = "".toInputStream()

        val num = generator.checkAndUpdate(input)

        assertEquals(1, num)
    }

    @Test
    fun `#checkAndUpdate writes new changelog entry when stream is empty`() {
        val input = "".toInputStream()
        val output = ByteArrayOutputStream()

        generator.checkAndUpdate(input, output)

        assertEquals(CHANGE_LOG_LINE_NEW_CLASS, output.toUtf8String())
    }

    @Test
    fun `#checkAndUpdate returns 1 of changed classes when class is missing`() {
        val input = CHANGE_LOG_OTHER_LINE.toInputStream()

        val num = generator.checkAndUpdate(input)

        assertEquals(1, num)
    }

    @Test
    fun `#checkAndUpdate writes new changelog entry when class is missing`() {
        val input = CHANGE_LOG_OTHER_LINE.toInputStream()
        val output = ByteArrayOutputStream()

        generator.checkAndUpdate(input, output)

        assertEquals(CHANGE_LOG_OTHER_LINE + CHANGE_LOG_LINE_NEW_CLASS, output.toUtf8String())
    }

    @Test
    fun `#generate with real implementation finds some existing classes`() {
        val generator = EntityChangelogGenerator(EntityChangelogClassProviderImpl(EntityCollectorImpl()))
        val output = ByteArrayOutputStream()

        generator.generate(output)

        val lines = output.toUtf8String().split("\n")
        assertTrue(lines.size > 10, "assuming that at least 10 lines are in changelog, ${lines.size} returned instead")
    }

    private fun String.toInputStream() = ByteArrayInputStream(toByteArray())

    private fun ByteArrayOutputStream.toUtf8String() = toString(Charsets.UTF_8)
}

private class EntityChangelogClassProviderTestImpl : EntityChangelogClassProvider {
    override fun getClasses() = listOf(TEST_CLASS)

    override fun getClassPool(): ClassPool =
        ClassPool.getDefault().apply {
            insertClassPath(ClassClassPath(TEST_CLASS))
        }

    override fun getToday(): LocalDate = DATE
}

private class NotExistingEntityChangelogClassProviderTestImpl : EntityChangelogClassProvider {
    override fun getClasses() = listOf(TEST_CLASS)

    override fun getClassPool(): ClassPool = ClassPool() // empty class pool
}
