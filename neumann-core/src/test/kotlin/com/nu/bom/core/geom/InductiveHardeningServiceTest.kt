package com.nu.bom.core.geom

import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.exception.readable.ErrorCode
import com.nu.bom.core.geom.TurningPropertiesServiceCompanionTests.Companion.createRectangleSketch
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.turn.InductiveHardeningService.Companion.getInductiveHardeningLines
import com.nu.bom.core.turn.model.Section
import com.tset.bom.clients.exception.UserResponseStatusException
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.math.BigDecimal

class InductiveHardeningServiceTest {
    @Test
    fun `COST-83712 - userException is thrown if no line with inductive hardening exists`() {
        val mockGlobalAttributeSection =
            Section(
                title = "mock",
                fields = listOf(FieldParameter.create<Length, BigDecimal>("hardeningDepth", BigDecimal("0.002"))),
            )

        val sketch =
            createRectangleSketch(length = 20, diameter = 5).copy(
                global_attributes = listOf(mockGlobalAttributeSection),
            )

        try {
            getInductiveHardeningLines(sketch)
        } catch (e: UserResponseStatusException) {
            assertEquals(ErrorCode.TURNING_INDUCTIVE_HARDENING_NO_LINES.name, e.userErrorCode)
        }
    }
}
