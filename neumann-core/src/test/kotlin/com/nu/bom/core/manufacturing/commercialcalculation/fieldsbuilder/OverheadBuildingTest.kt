package com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder

import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationRole
import com.nu.bom.core.manufacturing.commercialcalculation.enums.MaterialClass
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.fieldCalculation.CommercialCalculationTestBase
import com.nu.bom.core.manufacturing.commercialcalculation.fieldnamebuilders.FieldNameBuilderBase
import com.nu.bom.core.manufacturing.commercialcalculation.fieldnamebuilders.RateTimeFieldNameBuilder
import com.nu.bom.core.manufacturing.commercialcalculation.fieldnamebuilders.ValueFieldNameBuilder
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.commercialfieldbuilder.CommercialCostFieldBuilder
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.executioncontext.CommercialCalculationContext
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.InterestOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.OverheadOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.ProcurementTypeAndMaterialClass
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.PurchaseInputOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.TransferOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.WeightedCalculationElement
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetProcurementType
import com.nu.bom.core.manufacturing.commercialcalculation.rollupconfiguration.RollUpConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.rollupconfiguration.RollupIdentifier
import com.nu.bom.core.manufacturing.configurablefields.config.FieldConfig
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.DynamicTest
import org.junit.jupiter.api.TestFactory
import org.opentest4j.AssertionFailedError
import kotlin.reflect.KClass
import kotlin.reflect.KFunction

class OverheadBuildingTest : CommercialCalculationTestBase() {
    private val rollUpConfiguration = RollUpConfiguration()

    private val fieldsConfigBuilderService =
        CommercialCostFieldBuilder(
            RateAndTimeFieldsBuilder(),
            null,
        )

    enum class ElementType {
        Price,
        Cost,
        Overhead,
        Interest,
    }

    data class TestParameters(
        val expectedLevels: Set<AggregationLevel>,
    ) {
        val rateOriginLevel: AggregationLevel = expectedLevels.first()
        val ratePropagationLevels: Set<AggregationLevel> = expectedLevels.drop(1).toSet()

        val costOriginLevel: AggregationLevel = expectedLevels.last()

        override fun toString(): String {
            return "costOrigin=$costOriginLevel, rateOrigin=$rateOriginLevel"
        }
    }

    private val rollUpFunctions =
        setOf(
            CommercialCalculationContext::rollUpAndMultiplyOfMoney,
            CommercialCalculationContext::rollUpOfMoney,
            CommercialCalculationContext::rollUpInHouseMoney,
        )

    val tests =
        listOf(
            TestParameters(
                expectedLevels = setOf(AggregationLevel.MANUFACTURED_MATERIAL),
            ),
            TestParameters(
                expectedLevels =
                    setOf(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationLevel.PROCESSED_MATERIAL,
                        AggregationLevel.MANUFACTURING_STEP,
                        AggregationLevel.MATERIAL_USAGE,
                    ),
            ),
            TestParameters(
                expectedLevels =
                    setOf(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationLevel.PROCESSED_MATERIAL,
                        AggregationLevel.MANUFACTURING_STEP,
                        AggregationLevel.MATERIAL_USAGE,
                        AggregationLevel.SUB_MATERIAL,
                    ),
            ),
            TestParameters(
                expectedLevels = setOf(AggregationLevel.MATERIAL_USAGE),
            ),
            TestParameters(
                expectedLevels = setOf(AggregationLevel.MATERIAL_USAGE, AggregationLevel.SUB_MATERIAL),
            ),
            TestParameters(
                expectedLevels = setOf(AggregationLevel.SUB_MATERIAL),
            ),
            TestParameters(
                expectedLevels = setOf(AggregationLevel.MANUFACTURING_STEP),
            ),
            TestParameters(
                expectedLevels =
                    setOf(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationLevel.PROCESSED_MATERIAL,
                        AggregationLevel.MANUFACTURING_STEP,
                    ),
            ),
            TestParameters(
                expectedLevels = setOf(AggregationLevel.SOLD_MATERIAL),
            ),
        )

    @TestFactory
    fun overheadFieldsBuildingTest(): List<DynamicTest> {
        return tests.map { test ->
            DynamicTest.dynamicTest(test.toString()) {
                val inputOperations =
                    listOf(
                        PurchaseInputOperation(
                            destinationElementKey = ElementType.Price.name,
                            origin = AggregationLevel.SOLD_MATERIAL,
                            purchaseFieldName = "Any",
                        ),
                    )
                val operations =
                    listOf(
                        TransferOperation(
                            procurementAndMaterialClasses =
                                listOf(
                                    ProcurementTypeAndMaterialClass(
                                        TsetProcurementType.PURCHASE.customProcurementType,
                                        MaterialClass.CONSUMABLE,
                                    ),
                                ),
                            sourceElementKey = ElementType.Price.name,
                            destinationElementKey = ElementType.Cost.name,
                            origin = AggregationLevel.SUB_MATERIAL,
                        ),
                        OverheadOperation(
                            destinationElementKey = ElementType.Overhead.name,
                            origin = test.costOriginLevel,
                            baseAddends = listOf(WeightedCalculationElement(ElementType.Cost.name)),
                            rateOrigin = test.rateOriginLevel,
                            subCalculatorWithRole = null,
                        ),
                    )

                val config = TestConfigurationFactory.createInternalCostConfig(inputOperations, operations)

                val fieldConfigs =
                    fieldsConfigBuilderService
                        .createFieldConfigurations(CommercialFieldsBuilderConfiguration(ValueType.COST, config, rollUpConfiguration))
                        .entityFieldConfigs.getAllIncludingInherited(this.entityModelService).mapValues { it.value.values.toList() }

                printFieldConfigs(fieldConfigs, ElementType.Overhead, RateTimeFieldNameBuilder.ExtensionName.RATE)

                // verify
                assertRateOrTimeFieldExistsAndHaveCorrectFunction(
                    fieldConfigs = fieldConfigs,
                    test.rateOriginLevel,
                    CommercialCalculationContext::assignRateField,
                    test.ratePropagationLevels,
                    CommercialCalculationContext::propagateRate,
                    elementType = ElementType.Overhead,
                    extension = RateTimeFieldNameBuilder.ExtensionName.RATE,
                )
                assertFieldsExistAndHaveCorrectFunction(
                    fieldConfigs = fieldConfigs,
                    test.costOriginLevel,
                    CommercialCalculationContext::sumOfMoney,
                    setOf(),
                    elementType = ElementType.Overhead,
                    extension = ValueFieldNameBuilder.ExtensionName.BASE,
                )
                assertFieldsExistAndHaveCorrectFunction(
                    fieldConfigs = fieldConfigs,
                    initialLevel = test.costOriginLevel,
                    initialFunction = getOverheadKFunction(test.costOriginLevel),
                    propagationFunctions = rollUpFunctions,
                    elementType = ElementType.Overhead,
                )
            }
        }
    }

    @TestFactory
    fun interestFieldsBuildingTest(): List<DynamicTest> {
        return tests.map { test ->
            DynamicTest.dynamicTest(test.toString()) {
                val inputOperations =
                    listOf(
                        PurchaseInputOperation(
                            destinationElementKey = ElementType.Price.name,
                            origin = AggregationLevel.SOLD_MATERIAL,
                            purchaseFieldName = "Any",
                        ),
                    )
                val operations =
                    listOf(
                        TransferOperation(
                            procurementAndMaterialClasses =
                                listOf(
                                    ProcurementTypeAndMaterialClass(
                                        TsetProcurementType.PURCHASE.customProcurementType,
                                        MaterialClass.CONSUMABLE,
                                    ),
                                ),
                            sourceElementKey = ElementType.Price.name,
                            destinationElementKey = ElementType.Cost.name,
                            origin = AggregationLevel.SUB_MATERIAL,
                        ),
                        InterestOperation(
                            destinationElementKey = ElementType.Interest.name,
                            origin = test.costOriginLevel,
                            baseAddends = listOf(WeightedCalculationElement(ElementType.Cost.name)),
                            rateOrigin = test.rateOriginLevel,
                            subCalculatorWithRole = null,
                        ),
                    )

                val config = TestConfigurationFactory.createInternalCostConfig(inputOperations, operations)

                val fieldConfigs =
                    fieldsConfigBuilderService
                        .createFieldConfigurations(CommercialFieldsBuilderConfiguration(ValueType.COST, config, rollUpConfiguration))
                        .entityFieldConfigs.getAllIncludingInherited(this.entityModelService).mapValues { it.value.values.toList() }

                // verify
                assertRateOrTimeFieldExistsAndHaveCorrectFunction(
                    fieldConfigs = fieldConfigs,
                    initialLevel = test.rateOriginLevel,
                    initialFunction = CommercialCalculationContext::assignTimeInYearsField,
                    propagationLevels = test.ratePropagationLevels,
                    propagationFunction = CommercialCalculationContext::propagateTimeInYears,
                    elementType = ElementType.Interest,
                    extension = RateTimeFieldNameBuilder.ExtensionName.TIME,
                )
                assertRateOrTimeFieldExistsAndHaveCorrectFunction(
                    fieldConfigs = fieldConfigs,
                    initialLevel = test.costOriginLevel,
                    initialFunction = CommercialCalculationContext::interestOverheadRateOf,
                    propagationLevels = setOf(),
                    propagationFunction = null,
                    elementType = ElementType.Interest,
                    extension = RateTimeFieldNameBuilder.ExtensionName.RATE,
                )
                assertFieldsExistAndHaveCorrectFunction(
                    fieldConfigs = fieldConfigs,
                    initialLevel = test.costOriginLevel,
                    initialFunction = CommercialCalculationContext::sumOfMoney,
                    propagationFunctions = setOf(),
                    elementType = ElementType.Interest,
                    extension = ValueFieldNameBuilder.ExtensionName.BASE,
                )
                assertFieldsExistAndHaveCorrectFunction(
                    fieldConfigs = fieldConfigs,
                    initialLevel = test.costOriginLevel,
                    initialFunction = getOverheadKFunction(test.costOriginLevel),
                    propagationFunctions = rollUpFunctions,
                    elementType = ElementType.Interest,
                )
            }
        }
    }

    private fun getOverheadKFunction(costOriginLevel: AggregationLevel): KFunction<*> {
        return if (costOriginLevel == AggregationLevel.SOLD_MATERIAL) {
            CommercialCalculationContext::overheadOfMoneyWithInhouseOrPurchase
        } else {
            CommercialCalculationContext::overheadOfMoney
        }
    }

    private fun assertRateOrTimeFieldExistsAndHaveCorrectFunction(
        fieldConfigs: Map<KClass<out ManufacturingEntity>, List<FieldConfig>>,
        initialLevel: AggregationLevel,
        initialFunction: KFunction<*>,
        propagationLevels: Set<AggregationLevel>,
        propagationFunction: KFunction<*>?,
        elementType: ElementType,
        extension: RateTimeFieldNameBuilder.ExtensionName,
    ) {
        println(
            "\nChecking element type '${elementType.name}' and extension '$extension' in levels: ${(
                listOf(
                    initialLevel,
                ) + propagationLevels
            ).joinToString()}",
        )
        fieldConfigs.keys.forEach { entityKClass ->
            val entityTypeEnum = getEntityTypeEnum(entityKClass)
            val levelsOfType = levelsOfEntity(entityTypeEnum)

            val functions =
                if (levelsOfType.contains(initialLevel)) {
                    setOf(initialFunction)
                } else if (propagationFunction != null && propagationLevels.intersect(levelsOfType).any()) {
                    setOf(propagationFunction)
                } else {
                    setOf()
                }
            val fieldNameBuilder = RateTimeFieldNameBuilder(ValueType.COST, elementType.name, extension)
            checkFunction(functions, fieldNameBuilder, entityKClass, fieldConfigs)
        }
    }

    private fun assertFieldsExistAndHaveCorrectFunction(
        fieldConfigs: Map<KClass<out ManufacturingEntity>, List<FieldConfig>>,
        initialLevel: AggregationLevel,
        initialFunction: KFunction<*>,
        propagationFunctions: Set<KFunction<*>>,
        elementType: ElementType,
        extension: ValueFieldNameBuilder.ExtensionName? = null,
    ) {
        val originIdentifiers = rollUpConfiguration.getRollUpIdentifiersWithOriginOfLevel(initialLevel)
        Assertions.assertTrue(originIdentifiers.any(), "No origin identifiers!")

        val propagationIdentifier = rollUpConfiguration.getRollUpIdentifiersWhichPerformRollUpOfLevel(initialLevel)

        println(
            "\nChecking element type '${elementType.name}'${if (extension != null) " and extension '$extension'" else ""} in levels: ${(
                listOf(
                    initialLevel,
                )
            ).joinToString()}",
        )
        fieldConfigs.keys.forEach { entityName ->
            val entityTypeEnum = getEntityTypeEnum(entityName)
            val levelsOfType = levelsOfEntity(entityTypeEnum)

            levelsOfType.forEach { level ->
                rollUpConfiguration.getAggregationRolesOfEntityAndLevel(entityTypeEnum, level)
                    .filter { role ->
                        // we do not expect normal overhead operations on this level, only scrap operations
                        role != AggregationRole.INHOUSE_THIS
                    }
                    .forEach { role ->
                        val identifier = RollupIdentifier(entityTypeEnum, level, role)
                        val fieldNameBuilder = ValueFieldNameBuilder(ValueType.COST, level, role, elementType.name, extension)
                        if (originIdentifiers.contains(identifier)) {
                            checkFunction(setOf(initialFunction), fieldNameBuilder, entityName, fieldConfigs)
                        } else if (propagationIdentifier.contains(identifier)) {
                            checkFunction(propagationFunctions, fieldNameBuilder, entityName, fieldConfigs)
                        } else {
                            checkFunction(setOf(), fieldNameBuilder, entityName, fieldConfigs)
                        }
                    }
            }
        }
    }

    private fun getEntityTypeEnum(entityKClass: KClass<out ManufacturingEntity>): Entities {
        val entityName = entityKClass.simpleName!!
        return this.entityManager.getEntityInfo(entityName)?.entityType
            ?: throw Exception("EntityType not found for: '$entityName'")
    }

    private fun levelsOfEntity(entityTypeEnum: Entities): Set<AggregationLevel> {
        return if (entityTypeEnum == Entities.MANUFACTURING) {
            rollUpConfiguration.getAggregationLevels(entityTypeEnum)
        } else {
            // leaf materials have only a sales price, thus exclude them here
            rollUpConfiguration.getAggregationLevels(entityTypeEnum).filter { it != AggregationLevel.SOLD_MATERIAL }.toSet()
        }
    }

    private fun checkFunction(
        functions: Set<KFunction<*>>?,
        fieldNameBuilder: FieldNameBuilderBase,
        entityKClass: KClass<out ManufacturingEntity>,
        fieldConfigs: Map<KClass<out ManufacturingEntity>, List<FieldConfig>>,
    ) {
        if (functions != null && functions.any()) {
            println(
                "Checking if field '${fieldNameBuilder.fieldName}' exists in: ${entityKClass.simpleName}",
            )
            assertFunction(
                functions,
                fieldNameBuilder,
                entityKClass,
                fieldConfigs = fieldConfigs,
            )
        } else {
            // check that no field is present
            assertFieldDoesNotExist(
                fieldNameBuilder,
                entityKClass,
                fieldConfigs = fieldConfigs,
            )
        }
    }

    private fun assertFieldDoesNotExist(
        fieldNameBuilder: FieldNameBuilderBase,
        entityKClass: KClass<out ManufacturingEntity>,
        fieldConfigs: Map<KClass<out ManufacturingEntity>, List<FieldConfig>>,
    ) {
        val fieldName = fieldNameBuilder.fieldName
        val entityConfig = fieldConfigs[entityKClass] ?: throw Exception("No config for entity '${entityKClass.simpleName}' found!")
        Assertions.assertNull(
            entityConfig.firstOrNull {
                it.fieldName == fieldName
            },
            "field '$fieldName' on entity '${entityKClass.simpleName}' should not exist!",
        )
    }

    private fun assertFunction(
        functions: Set<KFunction<*>>,
        fieldNameBuilder: FieldNameBuilderBase,
        entityKClass: KClass<out ManufacturingEntity>,
        fieldConfigs: Map<KClass<out ManufacturingEntity>, List<FieldConfig>>,
    ) {
        val fieldName = fieldNameBuilder.fieldName
        val fieldConfig = getFieldConfig(entityKClass, fieldName, fieldConfigs)
        Assertions.assertTrue(
            functions.contains(fieldConfig.kFunction),
            "Field $fieldName:\n   expected functions: ${functions.joinToString { it.name }}\n  " +
                " actual function: ${fieldConfig.kFunction.name}",
        )
    }

    private fun getFieldConfig(
        entityKClass: KClass<out ManufacturingEntity>,
        fieldName: String,
        fieldConfigs: Map<KClass<out ManufacturingEntity>, List<FieldConfig>>,
    ): FieldConfig {
        val entityConfig =
            fieldConfigs[entityKClass]
                ?: throw AssertionFailedError("No config for entity '${entityKClass.simpleName}' found!")
        return entityConfig.firstOrNull { it.fieldName == fieldName }
            ?: throw AssertionFailedError("No field config for field '$fieldName' on entity '${entityKClass.simpleName}' found!")
    }

    private fun printFieldConfigs(
        fieldConfigs: Map<KClass<out ManufacturingEntity>, List<FieldConfig>>,
        elementType: ElementType? = null,
        extension: RateTimeFieldNameBuilder.ExtensionName? = null,
    ) {
        fieldConfigs.forEach { entry ->
            val fieldsToPrint = mutableListOf<String>()
            entry.value.forEach { fieldConfig ->
                val fieldNameDetails = RateTimeFieldNameBuilder.decomposeOrNull(fieldConfig.fieldName)
                if (fieldNameDetails != null) {
                    val isOfElementType = elementType == null || fieldNameDetails.elementTypeKey == elementType.name
                    val isOfExtension = extension == null || fieldNameDetails.extensionName == extension

                    if (isOfElementType && isOfExtension) {
                        fieldsToPrint.add(fieldConfig.toString())
                    }
                }
            }

            // print
            if (fieldsToPrint.any()) {
                println(entry.key.simpleName)
                fieldsToPrint.forEach {
                    println("    $it")
                }
            }
        }
    }
}
