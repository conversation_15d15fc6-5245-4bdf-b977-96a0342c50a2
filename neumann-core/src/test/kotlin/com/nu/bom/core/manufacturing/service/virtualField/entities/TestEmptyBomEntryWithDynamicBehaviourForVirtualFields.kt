package com.nu.bom.core.manufacturing.service.virtualField.entities

import com.nu.bom.core.manufacturing.annotations.BehaviourCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.entities.BaseEntityFields
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.BomNodeReference
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.service.behaviour.ConfigBasedDynamicBehaviour
import com.nu.bom.core.manufacturing.service.behaviour.DynamicBehaviour
import com.nu.bom.core.manufacturing.service.behaviour.EntityBasedDynamicBehaviour
import com.nu.bom.core.manufacturing.testentities.TestEntity
import com.nu.bom.core.model.configurations.ConfigId
import com.nu.bom.core.model.configurations.SemanticVersion

@TestEntity
@EntityType(Entities.BOM_ENTRY)
class TestEmptyBomEntryWithDynamicBehaviourForVirtualFields(
    name: String,
) : BaseManufacturing(name),
    BomNodeReference {
    override val extends = BaseEntityFields(name)

    fun createEntityBehaviour() = Bool(true)

    @BehaviourCreation
    fun createBehaviour(createEntityBehaviour: Bool): DynamicBehaviour =
        if (createEntityBehaviour.isTrue()) {
            EntityBasedDynamicBehaviour(this, TestBehaviourForVirtualFieldsWithDataSource("behaviour"))
        } else {
            ConfigBasedDynamicBehaviour(
                this,
                ConfigId("TEST", "TEST", this::class.simpleName!!, SemanticVersion.initialVersion()),
            )
        }
}
