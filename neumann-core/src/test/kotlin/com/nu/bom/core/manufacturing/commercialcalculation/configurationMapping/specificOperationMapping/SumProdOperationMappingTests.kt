package com.nu.bom.core.manufacturing.commercialcalculation.configurationMapping.specificOperationMapping

import com.nu.bom.core.exception.ConfigurationUserException
import com.nu.bom.core.manufacturing.commercialcalculation.configurationMapping.MandatoryOperationCreator
import com.nu.bom.core.manufacturing.commercialcalculation.configurationMapping.MandatoryOperationCreator.createMandatoryExternalOperations
import com.nu.bom.core.manufacturing.commercialcalculation.configurationMapping.TestProcurementTypeConfigurationCreator
import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.StandardCalculationValue
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.CalculationConfigurationException
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalCostInputOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalSumProdOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalWeightedCalculationElement
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.staticElements.StaticCostElement
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externaltointernalmapper.ExternalConfigToInternalConfigMapper.mapToInternalConfig
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.CurrentCostOperationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetFactoriesTransferHelper.defineCostTransferOperations
import com.nu.bom.core.manufacturing.commercialcalculation.rollupconfiguration.RollUpConfiguration
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class SumProdOperationMappingTests {
    enum class Keys {
        Cost,
    }

    @Test
    fun `mapping fails because of missing standard Calculation Value`() {
        val operations =
            listOf(
                ExternalCostInputOperation(
                    StaticCostElement.LABOR_COSTS,
                    "base",
                    AggregationLevel.LABOR,
                ),
            ) +
                defineCostTransferOperations() +
                listOf(
                    ExternalSumProdOperation(
                        "key1",
                        AggregationLevel.LABOR,
                        listOf(ExternalWeightedCalculationElement("base")),
                        StandardCalculationValue.TOTAL_MATERIAL_VALUE,
                    ),
                    ExternalSumProdOperation(
                        "key2",
                        AggregationLevel.LABOR,
                        listOf(ExternalWeightedCalculationElement("base")),
                        StandardCalculationValue.TOTAL_MANUFACTURING_VALUE,
                    ),
                    ExternalSumProdOperation(
                        "key3",
                        AggregationLevel.LABOR,
                        listOf(ExternalWeightedCalculationElement("base")),
                        StandardCalculationValue.TOTAL_PRODUCTION_VALUE,
                    ),
                )

        assertThrows<ConfigurationUserException> {
            mapToInternalConfig(
                ValueType.COST,
                CurrentCostOperationConfiguration(operations, TestProcurementTypeConfigurationCreator.createBusinessLogicLevel()),
                RollUpConfiguration(),
            )
        }.let { exception ->
            Assertions.assertEquals(
                ConfigurationUserException.ErrorCode.UNSATISFIED_INVARIANTS.name,
                exception.userErrorCode,
                "Unexpected error code!",
            )
            assertThat(exception.message).contains(
                "Expected an operation for each Standard Calculation Value, missing " +
                    "[TOTAL_DIRECT_MATERIAL_VALUE, TOTAL_OVERHEAD_VALUE, TOTAL_SALE_VALUE, " +
                    "TOTAL_MANUFACTURING_ACTIVITY, TOTAL_DIRECT_MANUFACTURING_VALUE, TOTAL_INVEST_VALUE].",
            )
        }
    }

    @Test
    fun `mapping fails because of duplicated standard Calculation Value`() {
        val operations =
            listOf(
                ExternalCostInputOperation(
                    StaticCostElement.LABOR_COSTS,
                    "base",
                    AggregationLevel.LABOR,
                ),
            ) + defineCostTransferOperations() +
                listOf(
                    ExternalSumProdOperation(
                        "key1",
                        AggregationLevel.LABOR,
                        listOf(ExternalWeightedCalculationElement("base")),
                        StandardCalculationValue.TOTAL_MATERIAL_VALUE,
                    ),
                    ExternalSumProdOperation(
                        "key2",
                        AggregationLevel.LABOR,
                        listOf(ExternalWeightedCalculationElement("base")),
                        StandardCalculationValue.TOTAL_MANUFACTURING_VALUE,
                    ),
                    ExternalSumProdOperation(
                        "key3",
                        AggregationLevel.LABOR,
                        listOf(ExternalWeightedCalculationElement("base")),
                        StandardCalculationValue.TOTAL_PRODUCTION_VALUE,
                    ),
                    ExternalSumProdOperation(
                        "key4",
                        AggregationLevel.LABOR,
                        listOf(ExternalWeightedCalculationElement("base")),
                        StandardCalculationValue.TOTAL_SALE_VALUE,
                    ),
                    ExternalSumProdOperation(
                        "key5",
                        AggregationLevel.LABOR,
                        listOf(ExternalWeightedCalculationElement("base")),
                        StandardCalculationValue.TOTAL_MATERIAL_VALUE,
                    ),
                )

        assertThrows<ConfigurationUserException> {
            mapToInternalConfig(
                ValueType.COST,
                CurrentCostOperationConfiguration(operations, TestProcurementTypeConfigurationCreator.createBusinessLogicLevel()),
                RollUpConfiguration(),
            )
        }.let { exception ->
            Assertions.assertEquals(
                ConfigurationUserException.ErrorCode.UNSATISFIED_INVARIANTS.name,
                exception.userErrorCode,
                "Unexpected error code!",
            )
            assertThat(exception.message).contains(
                "Per Standard Calculation Value there should be exactly one operation. " +
                    "For the following there is more than one: [TOTAL_MATERIAL_VALUE]",
            )
        }
    }

    @Test
    fun `mapping fails because of empty summands`() {
        val emptySum = ExternalSumProdOperation(Keys.Cost.name, AggregationLevel.LABOR, listOf())
        val operations = listOf(emptySum) + createMandatoryExternalOperations(AggregationLevel.MANUFACTURED_MATERIAL)

        assertThrows<CalculationConfigurationException> {
            mapToInternalConfig(
                ValueType.COST,
                CurrentCostOperationConfiguration(operations, TestProcurementTypeConfigurationCreator.createBusinessLogicLevel()),
                RollUpConfiguration(),
            )
        }.let { exception ->
            Assertions.assertEquals(CalculationConfigurationException.ErrorCode.EMPTY_SUM, exception.errorCode, "Unexpected error code!")
        }
    }

    @Test
    fun `mapping fails because of summands on wrong level`() {
        // Sum is trying to add the production costs defined on SoldMaterial on the level ManufacturedMaterial.
        // This sum accesses an undefined summand and is therefore invalid.
        val invalidSum =
            ExternalSumProdOperation(
                Keys.Cost.name,
                AggregationLevel.MANUFACTURED_MATERIAL,
                listOf(ExternalWeightedCalculationElement(MandatoryOperationCreator.MinimalConfigElementKey.ProductionCosts.name)),
            )

        val operations = listOf(invalidSum) + createMandatoryExternalOperations(AggregationLevel.SOLD_MATERIAL)

        assertThrows<CalculationConfigurationException> {
            mapToInternalConfig(
                ValueType.COST,
                CurrentCostOperationConfiguration(operations, TestProcurementTypeConfigurationCreator.createBusinessLogicLevel()),
                RollUpConfiguration(),
            )
        }.let { exception ->
            Assertions.assertEquals(CalculationConfigurationException.ErrorCode.EMPTY_SUM, exception.errorCode, "Unexpected error code!")
        }
    }
}
