package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.model.manufacturing.persistentModel.PersistedBomNode
import org.assertj.core.api.Assertions.assertThat
import org.bson.Document
import org.bson.types.ObjectId
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class ResponsibleUserMigrationTest {
    @ParameterizedTest
    @CsvSource(
        ",,", // Case 1: Both persistedBomNode and responsibleUser are null
        ", entityUser, entityUser", // Case 2: persistedBomNode is null, responsibleUser from entity fields
        "user,, user", // Case 3: persistedBomNode has responsibleUser, entity does not
        "user, entityUser, user", // Case 4: Both have responsibleUser, persistedBomNode takes precedence
    )
    fun `responsible field should be migrated correctly`(
        bomNodeUser: String?,
        entityUser: String?,
        expected: String?,
    ) {
        val mapper = ResponsibleUserMigration()

        val entity =
            createEntity(
                entityUser?.let {
                    mapOf(
                        "responsible" to
                            FieldResultModel(
                                version = 0,
                                newVersion = 0,
                                type = Text::class.simpleName!!,
                                value = it,
                            ),
                    )
                } ?: emptyMap(),
            )

        val persistedBomNode =
            bomNodeUser?.let {
                PersistedBomNode(
                    name = "bomNode",
                    year = 0,
                    title = "bomNode",
                    accountId = null,
                    manufacturing = Document(),
                    partName = null,
                    lastMigrationChangeSetId = null,
                    responsibleUser = it,
                )
            }

        val mapped = mapper.mapAsync(entity, persistedBomNode).block()!!
        val responsibleField = mapped.fieldWithResults["responsible"]

        assertThat(responsibleField?.value).isEqualTo(expected)
    }

    private fun createEntity(fields: Map<String, FieldResultModel>) =
        ManufacturingModelEntity(
            id = ObjectId.get(),
            name = "Manu",
            type = "MANUFACTURING",
            clazz = ManualManufacturing::class.simpleName!!,
            args = emptyMap(),
            fieldWithResults = fields,
            initialFieldWithResults = fields,
        )
}
