package com.nu.bom.core.manufacturing.fieldTypes

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test

class NumericFieldResultWithUnitArithmeticTests {
    @Test
    fun `square root Area`() {
        val area = Area(900.0, AreaUnits.QCM)
        val expected = Length(3.0, LengthUnits.DECIMETER)
        Assertions.assertEquals(expected, sqrt(area))
    }

    @Test
    fun `square root Length`() {
        val length = Length(400.0, LengthUnits.CENTIMETER)
        val expected = Length(20.0, LengthUnits.DECIMETER)
        Assertions.assertEquals(expected, sqrt(length))
    }

    @Test
    fun `prod Speed Time`() {
        val speed = Speed(2.0, SpeedUnits.M_PER_SEC)
        val time = Time(3.0, TimeUnits.MINUTE)
        val expected = Length(3600.0, LengthUnits.DECIMETER)
        Assertions.assertEquals(expected, prod(speed, time))
    }

    @Test
    fun `prod Acceleration Time`() {
        val acc = Acceleration(2.0, AccelerationUnits.M_PER_S2)
        val time = Time(0.5, TimeUnits.MINUTE)
        val expected = Speed(60.0, SpeedUnits.M_PER_SEC)
        Assertions.assertEquals(expected, prod(acc, time))
    }

    @Test
    fun `prod Length Revolution`() {
        val length = Length(400.0, LengthUnits.CENTIMETER)
        val revolution = Revolution(3.0, RevolutionUnits.PER_SEC)
        val expected = Speed(12.0, SpeedUnits.M_PER_SEC)
        Assertions.assertEquals(expected, prod(length, revolution))
    }

    @Test
    fun `quot Speed Length`() {
        val speed = Speed(2.0, SpeedUnits.M_PER_SEC)
        val length = Length(400.0, LengthUnits.CENTIMETER)
        val expected = Revolution(30.0, RevolutionUnits.PER_MIN)
        Assertions.assertEquals(expected, quot(speed, length))
    }

    @Test
    fun `quot Length Speed`() {
        val length = Length(30.0, LengthUnits.DECIMETER)
        val speed = Speed(2.0, SpeedUnits.M_PER_SEC)
        val expected = Time(1.5, TimeUnits.SECOND)
        Assertions.assertEquals(expected, quot(length, speed))
    }

    @Test
    fun `quot Speed Acceleration`() {
        val speed = Speed(30_000.0, SpeedUnits.MM_PER_MIN)
        val acc = Acceleration(5.0, AccelerationUnits.M_PER_S2)
        val expected = Time(0.1, TimeUnits.SECOND)
        Assertions.assertEquals(expected, quot(speed, acc))
    }
}
