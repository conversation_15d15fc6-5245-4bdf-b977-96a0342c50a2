package com.nu.bom.core.manufacturing.testentities.behaviour

import com.nu.bom.core.manufacturing.annotations.Children
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.BaseEntityFields
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text

@EntityType(Entities.NONE)
class TestBehaviour2(name: String) : ManufacturingEntity(name) {
    fun costPerPartFromBehaviour(
        @Children(Entities.MANUFACTURING)
        costPerPart1: List<Money>,
    ): Money = sum(costPerPart1)

    fun customField(): Money {
        return Money(2.0)
    }

    @Input
    fun numberOfParts(): QuantityUnit = QuantityUnit(10.0)

    fun showTransportRoutesAsText(): Text {
        return Text("no routes")
    }

    fun textField(): Text {
        return Text("I am living in the behaviour")
    }

    fun forcedDependency(): Bool {
        return Bool(true)
    }
}

@EntityType(Entities.MANUFACTURING)
class TestBehaviour2Child(name: String) : BaseManufacturing(name) {
    override val extends = BaseEntityFields(name)

    fun getTextFromBehaviour(
        @Parent(Entities.MANUFACTURING)
        forcedDependency: Bool,
        @Parent(Entities.MANUFACTURING)
        textField: Text,
    ): Text {
        return textField
    }

    fun costPerPart1(
        numberOfPartsMultipliedWith2: QuantityUnit,
        costPerPart2: Money,
    ): Money = costPerPart2 * numberOfPartsMultipliedWith2

    fun costPerPart2(): Money = Money(2.0)

    fun numberOfPartsMultipliedWith2(
        @Parent(Entities.MANUFACTURING)
        numberOfParts: QuantityUnit,
    ): QuantityUnit = numberOfParts.times(2.0)
}
