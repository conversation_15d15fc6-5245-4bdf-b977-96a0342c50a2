package com.nu.bom.core.manufacturing

import com.nu.bom.core.CalculationTestBase
import com.nu.bom.core.exception.ConfigurationUserException
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.ToolAllocationMode
import com.nu.bom.core.model.configurations.DefaultValuesConfiguration
import com.nu.bom.core.publicapi.dtos.configurations.DefaultValuesConfigurationDto
import com.nu.bom.core.publicapi.service.configuration.DefaultValuesConfigurationDtoMappingService
import com.nu.bom.core.utils.assertThatThrownBy
import com.tset.common.testing.assertReversible
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.math.BigDecimal

class DefaultValuesConfigurationMappingTest : CalculationTestBase() {
    private val mapper = DefaultValuesConfigurationDtoMappingService(entityManager)

    private val unitMap =
        Dimension.Selection.entries.associateWith { dim -> dim.getDefaultCostUnit() }

    private val unitMapDto =
        Dimension.Selection.entries.associate { dim -> dim.name to dim.getDefaultCostUnit() }

    private val baseDto =
        DefaultValuesConfigurationDto(
            BigDecimal.ONE,
            BigDecimal.ONE,
            BigDecimal.ONE,
            BigDecimal.ONE,
            BigDecimal.ONE,
            unitMapDto,
            unitMapDto,
            BigDecimal.ONE,
            ToolAllocationMode.Mode.PAY_ALL.name,
        )

    private val baseEntity =
        DefaultValuesConfiguration(
            BigDecimal.ONE,
            BigDecimal.ONE,
            BigDecimal.ONE,
            BigDecimal.ONE,
            BigDecimal.ONE,
            unitMap,
            unitMap,
            BigDecimal.ONE,
            ToolAllocationMode.Mode.PAY_ALL,
        )

    @Test
    fun `dto serde is reversible`() {
        objectMapper.assertReversible(baseDto)
    }

    @Test
    fun `basic conversion succeeds`() {
        assertThat(mapper.toDto(baseEntity))
            .isEqualTo(baseDto)

        assertThat(mapper.fromDto(baseDto))
            .isEqualTo(baseEntity)
    }

    @Test
    fun `wrong dimension throws error`() {
        val errorEntry = "Zwetschgenknödel" to "SomeUnit"
        val quantityError = baseDto.copy(quantityUnit = unitMapDto + errorEntry)
        val costError = baseDto.copy(costCo2Unit = unitMapDto + errorEntry)

        assertThatThrownBy<ConfigurationUserException> { mapper.fromDto(quantityError) }
            .hasMessageContaining("not a valid dimension")
        assertThatThrownBy<ConfigurationUserException> { mapper.fromDto(costError) }
            .hasMessageContaining("not a valid dimension")
    }

    @Test
    fun `wrong unit throws error`() {
        val errorEntry = Dimension.Selection.NUMBER.name to "ONE_GOOGOL"
        val quantityError = baseDto.copy(quantityUnit = unitMapDto + errorEntry)
        val costError = baseDto.copy(costCo2Unit = unitMapDto + errorEntry)

        assertThatThrownBy<ConfigurationUserException> { mapper.fromDto(quantityError) }
            .hasMessageContaining("not a valid unit")
        assertThatThrownBy<ConfigurationUserException> { mapper.fromDto(costError) }
            .hasMessageContaining("not a valid unit")
    }

    @Test
    fun `wrong tool allocation`() {
        assertThatThrownBy<ConfigurationUserException> { mapper.fromDto(baseDto.copy(toolAllocationMode = "STEAL_ALL")) }
            .hasMessageContaining("not a valid tool allocation")
    }
}
