package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.MdHeaderInfoFieldData
import com.nu.bom.core.manufacturing.fieldTypes.masterdata.LovFieldDefinition
import com.nu.bom.core.manufacturing.fieldTypes.masterdata.LovFieldSchema
import com.nu.bom.core.model.MasterDataSelector
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.utils.findInTree
import org.assertj.core.api.Assertions.assertThat
import org.bson.types.ObjectId
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.util.UUID

private val RANDOM_UUID = UUID.randomUUID().toString()

class ElectronicComponentMdMigrationTest {
    @Test
    fun shouldMigrateExistingElectronicComponents() {
        val entity = createEntity()
        val migratedEntity = ElectronicComponentMdMigration().map(entity)
        validateEntity(migratedEntity)
    }

    private fun validateEntity(entity: ManufacturingModelEntity) {
        val materialParent =
            entity.findInTree(
                { node -> node.type == Entities.MD_MATERIAL_PARENT.name },
                { node -> node.children },
            )
        assertThat(materialParent).isNotNull
        assertThat(materialParent!!.children.size).isEqualTo(2)
        assertThat(materialParent.children.all { it.fieldWithResults.contains("#request_effectivity_tset-ref-field-region") }).isTrue
        assertThat(
            materialParent.children.all {
                it.fieldWithResults.contains("#request_effectivity_tset-ref-field-region_display_name")
            },
        ).isTrue
        val materialPrice =
            materialParent.children
                .first { it.type == Entities.MD_MATERIAL_PRICE.name }
        val materialEmission =
            materialParent.children
                .first { it.type == Entities.MD_MATERIAL_EMISSION.name }
        assertThat(
            materialPrice
                .fieldWithResults["#request_effectivity_tset-ref-field-region"]!!
                .value
                .toString(),
        ).isEqualTo("Italy")
        assertThat(
            materialParent.children.map { it.fieldWithResults["headerKey"]!!.value }.distinct().single(),
        ).isEqualTo(RANDOM_UUID)

        assertThat(materialPrice.children.size).isEqualTo(1)
        assertThat(materialEmission.children.size).isEqualTo(1)
        validateSchemaAndValue(materialPrice.children[0])
        validateSchemaAndValue(materialEmission.children[0])
    }

    private fun validateSchemaAndValue(materialClassificationSchema: ManufacturingModelEntity) {
        assertThat(materialClassificationSchema.fieldWithResults["headerKey"]!!.value).isEqualTo(RANDOM_UUID)
        val mdHeaderInfo = (materialClassificationSchema.fieldWithResults["mdHeaderInfo"]!!.value as MdHeaderInfoFieldData)
        assertThat(materialClassificationSchema.fieldWithResults["headerTypeKey"]!!.value).isEqualTo("tset.ref.header-type.material")
        assertThat(mdHeaderInfo.classifications).isEqualTo(
            mapOf(
                "tset.ref.classification-type.material" to listOf("tset.ref.classification.electronic-component"),
            ),
        )
        assertThat(mdHeaderInfo.fieldDefinitions).isEqualTo(
            listOf(
                LovFieldDefinition(
                    key = "tset.ref.field.manufacturer",
                    fieldSchema = LovFieldSchema("Manufacturer", "tset.ref.lov-type.elco.manufacturer"),
                ),
                LovFieldDefinition(
                    key = "tset.ref.field.mounting-type",
                    fieldSchema = LovFieldSchema("Mounting type", "tset.ref.lov-type.mounting-type"),
                ),
            ),
        )
        val materialClassificationValue = materialClassificationSchema.children[0]
        assertThat(materialClassificationValue.fieldWithResults["headerKey"]!!.value).isEqualTo(RANDOM_UUID)
        assertThat(
            materialClassificationValue.fieldWithResults["#classification_field_tset-ref-field-manufacturerName"]!!.value,
        ).isEqualTo("Some manufacturer")
        assertThat(
            materialClassificationValue.fieldWithResults["#classification_field_tset-ref-field-mounting-type"]!!.value,
        ).isEqualTo("SMD")
    }

    private fun createEntity() =
        ManufacturingModelEntity(
            id = ObjectId.get(),
            name = "Manu-1",
            type = "MANUFACTURING",
            clazz = ManualManufacturing::class.simpleName!!,
            args = emptyMap(),
            fieldWithResults =
                mapOf(
                    "calculationDate" to
                        FieldResultModel(
                            0,
                            0,
                            "Date",
                            LocalDate.of(2020, 7, 11),
                            FieldResult.SOURCE.C.name,
                        ),
                    "location" to
                        FieldResultModel(
                            0,
                            0,
                            "Text",
                            "Belgium",
                            FieldResult.SOURCE.C.name,
                        ),
                ),
            initialFieldWithResults = emptyMap(),
            children =
                listOf(
                    ManufacturingModelEntity(
                        id = ObjectId.get(),
                        name = "step",
                        type = "MANUFACTURING_STEP",
                        clazz = "ManufacturingStep",
                        args = emptyMap(),
                        fieldWithResults =
                            mapOf(
                                "shiftsPerDay" to
                                    FieldResultModel(
                                        0,
                                        0,
                                        "Num",
                                        "3",
                                        FieldResult.SOURCE.C.name,
                                    ),
                                "location" to
                                    FieldResultModel(
                                        0,
                                        0,
                                        "Text",
                                        "Italy",
                                        FieldResult.SOURCE.C.name,
                                    ),
                                "locationName" to
                                    FieldResultModel(
                                        0,
                                        0,
                                        "Text",
                                        "Italy",
                                        FieldResult.SOURCE.C.name,
                                    ),
                            ),
                        initialFieldWithResults = emptyMap(),
                        children =
                            listOf(
                                ManufacturingModelEntity(
                                    id = ObjectId.get(),
                                    name = "Electronic component",
                                    type = "C_PART",
                                    clazz = "ElectronicComponent",
                                    args = emptyMap(),
                                    fieldWithResults = createInitFields("Italy"),
                                    initialFieldWithResults = emptyMap(),
                                ).apply {
                                    masterDataSelector =
                                        MasterDataSelector(
                                            type = MasterDataType.LOCATION,
                                            key = RANDOM_UUID,
                                            year = 2024,
                                            location = "Italy",
                                        )
                                },
                            ),
                    ),
                ),
        )

    private fun createInitFields(location: String): Map<String, FieldResultModel> {
        return mapOf(
            "location" to
                FieldResultModel(
                    0,
                    0,
                    "Text",
                    location,
                    FieldResult.SOURCE.C.name,
                ),
            "locationName" to
                FieldResultModel(
                    0,
                    0,
                    "Text",
                    location,
                    FieldResult.SOURCE.C.name,
                ),
            "pricePerUnit" to
                FieldResultModel(
                    0,
                    0,
                    "Money",
                    1.5,
                    FieldResult.SOURCE.M.name,
                ),
            "cO2PerUnit" to
                FieldResultModel(
                    0,
                    0,
                    "Emission",
                    2.5,
                    FieldResult.SOURCE.M.name,
                ),
            "manufacturer" to
                FieldResultModel(
                    0,
                    0,
                    "Text",
                    "Some manufacturer",
                    FieldResult.SOURCE.C.name,
                ),
            "mountingType" to
                FieldResultModel(
                    0,
                    0,
                    "Text",
                    "SMD",
                    FieldResult.SOURCE.C.name,
                ),
        )
    }
}
