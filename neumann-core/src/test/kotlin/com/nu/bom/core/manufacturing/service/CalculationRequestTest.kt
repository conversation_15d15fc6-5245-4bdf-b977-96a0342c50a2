package com.nu.bom.core.manufacturing.service

import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.utils.AccountUtil
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class CalculationRequestTest {
    @Test
    fun verifyBomNodeInContextWithNoBoundaries() {
        val calculationRequest =
            CalculationRequest(
                AccountUtil.dummyAccessCheck(),
                null,
                ManufacturingEntity("manufacturingEntity"),
                inputs = emptyMap(),
            )
        assertTrue(calculationRequest.isInContext(BomNodeId()))
    }

    @Test
    fun verifyBomNodeInContextWithBoundaries() {
        val idInBoundaries = BomNodeId()
        val calculationRequest =
            CalculationRequest(
                AccountUtil.dummyAccessCheck(),
                null,
                ManufacturingEntity("manufacturingEntity"),
                inputs = emptyMap(),
                contextBoundaries = setOf(BomNodeId(), idInBoundaries),
            )
        assertFalse(calculationRequest.isInContext(BomNodeId()))
        assertTrue(calculationRequest.isInContext(idInBoundaries))
    }
}
