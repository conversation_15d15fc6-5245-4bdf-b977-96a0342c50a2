package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeUnit
import com.nu.bom.core.manufacturing.fieldTypes.DynamicQuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import org.assertj.core.api.Assertions.assertThat
import org.bson.types.ObjectId
import org.junit.jupiter.api.Test
import java.math.BigDecimal

class ManufacturingStepOeeSystemOccupancyMigrationTest {
    @Test
    fun calculateOee() {
        val inputFields =
            mapOf(
                "productionHoursPerYear" to Time(1000.0, TimeUnits.HOUR),
                "systemDownTime" to Time(10.0, TimeUnits.HOUR),
            )

        val calculatedFields =
            mapOf(
                "oee" to Rate(0.99),
            )

        assertSimpleFieldMapping(
            mapper = ManufacturingStepOeeSystemOccupancyMigration(),
            inputFields = inputFields,
            outputFields = inputFields + calculatedFields,
        )
    }

    @Test
    fun calculatePeakProcessingVolumePerYear() {
        val inputFields =
            mapOf(
                "peakVolumePerYear" to DynamicQuantityUnit(BigDecimal.TEN),
                "lotFraction" to Rate(5.0),
            )

        val calculatedFields =
            mapOf(
                "peakProcessedVolumePerYear" to QuantityUnit(BigDecimal(50.0)),
            )

        assertSimpleFieldMapping(
            mapper = ManufacturingStepOeeSystemOccupancyMigration(),
            inputFields = inputFields,
            outputFields = inputFields + calculatedFields,
        )
    }

    @Test
    fun calculateAverageProcessingVolumePerYear() {
        val inputFields =
            mapOf(
                "averageVolumePerYear" to DynamicQuantityUnit(BigDecimal.TEN),
                "lotFraction" to Rate(5.0),
            )

        val calculatedFields =
            mapOf(
                "averageProcessedVolumePerYear" to QuantityUnit(BigDecimal(50.0)),
            )

        assertSimpleFieldMapping(
            mapper = ManufacturingStepOeeSystemOccupancyMigration(),
            inputFields = inputFields,
            outputFields = inputFields + calculatedFields,
        )
    }

    @Test
    fun calculateInternalManufacturingTimePerPart() {
        val inputFields =
            mapOf(
                "internalCycleTime" to CycleTime(10.0, CycleTimeUnit.SECOND),
                "partsPerCycle" to QuantityUnit(5.0),
            )

        val calculatedFields =
            mapOf(
                "internalManufacturingTimePerPart" to Time(2.0, TimeUnits.SECOND),
            )

        assertSimpleFieldMapping(
            mapper = ManufacturingStepOeeSystemOccupancyMigration(),
            inputFields = inputFields,
            outputFields = inputFields + calculatedFields,
        )
    }

    @Test
    fun calculateSystemOccupancy() {
        val inputFields =
            mapOf(
                "callsPerYear" to Num(5.0),
                "productionHoursPerYear" to Time(1000.0, TimeUnits.HOUR),
                "internalSystemDownTime" to Time(1.0, TimeUnits.HOUR),
                "peakProcessedVolumePerYear" to QuantityUnit(200.0),
                "internalManufacturingTimePerPart" to Time(10.0, TimeUnits.HOUR),
                "utilizationRate" to Rate(1.0),
            )

        val calculatedFields =
            mapOf(
                "peakSystemOccupancy" to Rate(2.005),
            )

        assertSimpleFieldMapping(
            mapper = ManufacturingStepOeeSystemOccupancyMigration(),
            inputFields = inputFields,
            outputFields = inputFields + calculatedFields,
        )
    }

    @Test
    fun calculateAverageSystemOccupancy() {
        val inputFields =
            mapOf(
                "callsPerYear" to Num(5.0),
                "productionHoursPerYear" to Time(1000.0, TimeUnits.HOUR),
                "internalSystemDownTime" to Time(1.0, TimeUnits.HOUR),
                "averageProcessedVolumePerYear" to QuantityUnit(100.0),
                "internalManufacturingTimePerPart" to Time(10.0, TimeUnits.HOUR),
                "utilizationRate" to Rate(1.0),
            )

        val calculatedFields =
            mapOf(
                "averageSystemOccupancy" to Rate(1.005),
            )

        assertSimpleFieldMapping(
            mapper = ManufacturingStepOeeSystemOccupancyMigration(),
            inputFields = inputFields,
            outputFields = inputFields + calculatedFields,
        )
    }

    private fun assertSimpleFieldMapping(
        mapper: ManufacturingModelEntityMapper,
        inputFields: Map<String, FieldResultStar>,
        outputFields: Map<String, FieldResultStar>,
    ) {
        val entity =
            ManufacturingModelEntity(
                id = ObjectId.get(),
                name = "NotRelevant",
                type = "NotRelevant",
                clazz = "NotRelevant",
                args = emptyMap(),
                fieldWithResults = inputFields.mapValues { toFieldModelResult(it.value) },
            )

        val mappedFields = mapper.map(entity).fieldWithResults

        assertThat(mappedFields.keys).isEqualTo(outputFields.keys)
        mappedFields.entries.forEach { (name, field) ->
            assertThat(field).isEqualTo(toFieldModelResult(outputFields[name]!!))
        }
    }

    private fun toFieldModelResult(field: FieldResultStar) =
        FieldResultModel(
            version = 0,
            newVersion = 0,
            type = field.javaClass.simpleName,
            value = field.dbValue(),
            source = field.source.name,
            unit = field.getUnit(),
            systemValue = field.systemValue,
        )
}
