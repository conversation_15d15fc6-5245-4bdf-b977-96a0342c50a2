package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.manufacturing.entities.ManufacturingStepLineFields
import org.bson.types.ObjectId
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import java.math.BigDecimal

class NumericFieldResultTests {
    @Test
    fun `Save division with null divisor should return null`() {
        val result = Money.ONE.safeDivision(null)
        assertNull(result)
    }

    @Test
    fun `Save division with zero divisor should return null`() {
        val result = Money.ONE.safeDivision(Money.ZERO)
        assertNull(result)
    }

    @Test
    fun `Save division with non zero divisor should return division result`() {
        val result = Money.ONE.safeDivision(Money(2.0))
        assertTrue(result!! == Money(0.5))
    }

    @Test
    fun `Save division with null divisor and default should return default value`() {
        val result = Money.ONE.safeDivision(null, Money.ONE)
        assertEquals(Money.ONE, result)
    }

    @Test
    fun `Save division with zero divisor and default should return default value`() {
        val result = Money.ONE.safeDivision(Money.ZERO, Money.ONE)
        assertEquals(Money.ONE, result)
    }

    @Test
    fun `Save division with non zero divisor and default should return division result`() {
        val result = Money.ONE.safeDivision(Money(2.0), Money.ZERO)
        assertTrue(result == Money(0.5))
    }

    @Test
    fun `The ceil of 1 dot 5 should return 2`() {
        val result = Money("1.5").ceil()
        assertTrue(result == Money("2.0"))
    }

    @Test
    fun `The ceil of -1 dot 5 should return 2`() {
        val result = Money("-1.5").ceil()
        assertTrue(result == Money("-1.0"))
    }

    @Test
    fun `The floor of 1 dot 5 should return 1`() {
        val result = Money("1.5").floor()
        assertTrue(result == Money("1.0"))
    }

    @Test
    fun `The floor of -1 dot 5 should return 1`() {
        val result = Money("-1.5").floor()
        assertTrue(result == Money("-2.0"))
    }

    @Test
    fun `atMost of 1 for the value of 5 should return 1`() {
        val result = Money(5.0).atMost(BigDecimal.ONE)
        assertTrue(result == Money("1.0"))
    }

    @Test
    fun `atLeast of 5 for the value of 1 should return 5`() {
        val result = Money.ONE.atLeast(BigDecimal("5.0"))
        assertTrue(result == Money("5.0"))
    }

    @Test
    fun `Division of 5 by 2 should return 2 point 5 `() {
        val result = Money("5.0") / 2
        assertTrue(result == Money("2.5"))
    }

    // TODO(COST-84148): Delete this test
    @Test
    fun `COST-84054 - scrapRate accumulation is numerically stable`() {
        // comparison on BigDecimal level which includes scale is done here on purpose
        val entity = ManufacturingStepLineFields("test")

        val scrapRate = Rate(0.001)

        var scrapRates = mapOf(ObjectId() to scrapRate)
        assertEquals(scrapRate.res, entity.cumulativeGroupScrapRate(scrapRates).res)

        scrapRates =
            mapOf(
                ObjectId() to Rate.ZERO,
                ObjectId() to scrapRate,
                ObjectId() to Rate.ZERO,
            )
        assertEquals(scrapRate.res, entity.cumulativeGroupScrapRate(scrapRates).res)

        scrapRates =
            mapOf(
                ObjectId() to Rate(0.1),
                ObjectId() to Rate(0.1),
            )
        assertEquals(Rate(0.19).res, entity.cumulativeGroupScrapRate(scrapRates).res)
    }

    @Test
    fun `mod returns correct value`() {
        val base = Length(5.7, LengthUnits.METER)
        val mod = Length(100.0, LengthUnits.CENTIMETER)
        val result = Length(0.7, LengthUnits.METER)
        assertTrue(base.mod(mod).rounded == result.rounded)
    }

    @Test
    fun `safeMod returns correct value when valid`() {
        val base = Length(5.7, LengthUnits.METER)
        val mod = Length(100.0, LengthUnits.CENTIMETER)
        val result = Length(0.7, LengthUnits.METER)
        assertTrue(base.safeMod(mod, Length.ZERO).rounded == result.rounded)
    }

    @Test
    fun `safeMod returns fallback value when invalid`() {
        val base = Length(5.7, LengthUnits.METER)
        val default = Length(4.2, LengthUnits.MICROMETER)
        assertTrue(base.safeMod(Length.ZERO, default) == default)
        assertTrue(base.safeMod(null, default) == default)
    }
}
