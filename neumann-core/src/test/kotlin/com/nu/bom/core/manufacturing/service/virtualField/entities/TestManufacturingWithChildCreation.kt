package com.nu.bom.core.manufacturing.service.virtualField.entities

import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.entities.BaseEntityFields
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.service.asEntityClass
import com.nu.bom.core.manufacturing.testentities.TestEntity

@TestEntity
@EntityType(Entities.MANUFACTURING)
class TestManufacturingWithChildCreation(
    name: String,
) : BaseManufacturing(name) {
    override val extends = BaseEntityFields(name)

    fun checkField() = Text("Host")

    @EntityCreation(Entities.BOM_ENTRY)
    fun createBomEntry(): List<ManufacturingEntity> =
        listOf(
            services.createEntity(
                name = "BomEntry by entity creation",
                entityType = Entities.BOM_ENTRY,
                clazz = TestBomEntryForVirtualFields::class.asEntityClass(),
                version = this.version,
                fields =
                    mapOf(
                        TestBomEntryForVirtualFields::someFieldToPropagateToChild.name to QuantityUnit(1000.0),
                    ),
            ),
        )
}
