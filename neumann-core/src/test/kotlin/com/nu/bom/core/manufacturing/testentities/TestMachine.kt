package com.nu.bom.core.manufacturing.testentities

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.BaseEntityFields
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Currency
import com.nu.bom.core.manufacturing.fieldTypes.ExchangeRatesField
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.configuration.VersionedCostModuleField
import java.math.BigDecimal

@EntityType(Entities.MACHINE)
class TestMachine(name: String) : ManufacturingEntity(name) {
    override val extends = BaseEntityFields(name)

    @Input
    fun baseValue(): Money? = null

    @Input
    fun baseRate(): Rate? = null

    fun machineValue(
        baseValue: Money,
        baseRate: Rate,
    ): Money {
        return baseValue * baseRate
    }

    fun weightedMachineValue(
        machineValue: Money,
        @Parent(Entities.MANUFACTURING_STEP)
        stepWeight: Rate,
    ): Money {
        return machineValue * stepWeight
    }

    fun baseCurrency(): Currency = Currency("EUR")

    fun masterdataBaseCurrency(): Currency = Currency("EUR")

    fun exchangeRates() = ExchangeRatesField(mapOf(com.tset.core.service.domain.Currency("EUR") to BigDecimal.ONE))

    fun versionName(
        @Parent(Entities.MANUFACTURING)
        costModuleVersionProvider: VersionedCostModuleField,
    ) = Text(costModuleVersionProvider.res.technology)
}
