package com.nu.bom.core.manufacturing.relations.children.behaviour

import com.nu.bom.core.manufacturing.configurablefields.builder.DynamicFieldConfigBuilder
import com.nu.bom.core.manufacturing.configurablefields.config.ChildRelation
import com.nu.bom.core.manufacturing.configurablefields.config.EntityFieldConfigurations
import com.nu.bom.core.manufacturing.configurablefields.config.EntityFieldConfigurationsWithContext
import com.nu.bom.core.manufacturing.configurablefields.config.FieldConfig
import com.nu.bom.core.manufacturing.configurablefields.config.ParameterConfig
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.relations.children.entities.ChildEntityForChildrenRelationTests
import com.nu.bom.core.manufacturing.relations.children.entities.ParentEntityForChildrenRelationTests
import com.nu.bom.core.manufacturing.service.ConfigurationManagementService
import com.nu.bom.core.user.AccessCheck
import reactor.core.publisher.Mono

class TestDynamicFieldConfigBuilderForChildrenRelation : DynamicFieldConfigBuilder<String> {
    override val groupKey = "TEST"
    override val typeKey = "TEST"

    override fun getConfiguration(
        accessCheck: AccessCheck,
        configurationId: ConfigurationIdentifier,
        configurationManagementService: ConfigurationManagementService,
    ): Mono<String> {
        return Mono.just("Test")
    }

    override fun createFieldConfigurations(configuration: String): EntityFieldConfigurationsWithContext {
        val result = EntityFieldConfigurations()

        result.add(
            ParentEntityForChildrenRelationTests::class,
            FieldConfig(
                "rateOfKey1",
                TestContextForChildrenRelation::sumOf,
                "summands",
                ParameterConfig(
                    fieldName = ChildEntityForChildrenRelationTests::rate.name,
                    relation = ChildRelation(Entities.CONSUMABLE, childNameFilter = "key1"),
                ),
                entityFieldMetaInfos = listOf(),
                fieldClass = Rate::class,
                availableOutsideTheEngine = false,
            ),
        )

        return EntityFieldConfigurationsWithContext(
            result,
            TestContextForChildrenRelation("TestContextForChildrenTest"),
        )
    }
}
