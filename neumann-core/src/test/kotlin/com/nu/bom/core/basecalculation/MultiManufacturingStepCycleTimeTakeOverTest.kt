package com.nu.bom.core.basecalculation

import com.nu.bom.core.CalculationTestBase
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeUnit
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYears
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYearsUnit
import org.junit.jupiter.api.Test
import java.math.BigDecimal

class MultiManufacturingStepCycleTimeTakeOverTest : CalculationTestBase() {
    companion object {
        private val stepParams =
            mapOf(
                "internalPartsPerCycle" to Pieces(1.0),
                "utilizationRate" to Rate(1.0),
                "scrapRate" to Rate(0.0),
            )

        private val manuParams =
            mapOf(
                "materialInterestDays" to TimeInYears(BigDecimal.ZERO, TimeInYearsUnit.YEAR),
                "materialOverheadRate" to Rate(BigDecimal.ZERO),
                "procurementType" to ManufacturingType(ManufacturingType.Type.INHOUSE),
                "callsPerYear" to Num(10.0.toBigDecimal()),
                "averageUsableProductionVolumePerYear" to QuantityUnit(100000.0.toBigDecimal()),
                "peakUsableProductionVolumePerYear" to QuantityUnit(100000.0.toBigDecimal()),
            )
    }

    @Test
    fun useParent() {
        val calculation =
            createManufacturing(
                name = "Manufacturing",
                parameters = manuParams,
            )

        val step1 =
            addObject(
                "Step 1",
                Entities.MANUFACTURING_STEP,
                parent = calculation,
                parameters =
                    stepParams +
                        mapOf(
                            "internalCycleTime" to CycleTime(2.toBigDecimal(), CycleTimeUnit.SECOND),
                        ),
            )

        addObject(
            "Step 2",
            Entities.MANUFACTURING_STEP,
            parent = step1,
            parameters =
                stepParams +
                    mapOf(
                        "isLinkedToParent" to Bool(true),
                        "internalCycleTime" to CycleTime(1.toBigDecimal(), CycleTimeUnit.SECOND),
                    ),
        )

        calculate(calculation)

        printResults()

        validateResultField("Step 2", "cycleTime", 2.0)
    }

    @Test
    fun useOwnIfHigher() {
        val calculation =
            createManufacturing(
                name = "Manufacturing",
                parameters = manuParams,
            )

        val step1 =
            addObject(
                "Step 1",
                Entities.MANUFACTURING_STEP,
                parent = calculation,
                parameters =
                    stepParams +
                        mapOf(
                            "internalCycleTime" to CycleTime(2.toBigDecimal(), CycleTimeUnit.SECOND),
                        ),
            )

        addObject(
            "Step 2",
            Entities.MANUFACTURING_STEP,
            parent = step1,
            parameters =
                stepParams +
                    mapOf(
                        "isLinkedToParent" to Bool(true),
                        "internalCycleTime" to CycleTime(5.toBigDecimal(), CycleTimeUnit.SECOND),
                    ),
        )

        calculate(calculation)

        printResults()

        validateResultField("Step 2", "cycleTime", 5.0)
    }

    @Test
    fun useOwnIfNoParent() {
        val calculation =
            createManufacturing(
                name = "Manufacturing",
                parameters = manuParams,
            )

        addObject(
            "Step 1",
            Entities.MANUFACTURING_STEP,
            parent = calculation,
            parameters =
                stepParams +
                    mapOf(
                        "internalCycleTime" to CycleTime(3.toBigDecimal(), CycleTimeUnit.SECOND),
                        "isLinkedToParent" to Bool(true),
                    ),
        )

        calculate(calculation)

        printResults()

        validateResultField("Step 1", "cycleTime", 3.0)
    }
}
