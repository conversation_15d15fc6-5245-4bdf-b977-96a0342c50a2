package com.nu.bom.core.manufacturing.commercialcalculation.configurationMapping

import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.DisplayableNames
import com.nu.bom.core.publicapi.dtos.configurations.CalculationElementDto
import com.nu.bom.core.publicapi.dtos.configurations.DisplayableNamesDto
import kotlin.reflect.KClass

object TestElementCreator {

    fun createElementsFromEnum(elementTypeEnumClass: KClass<out Enum<*>>): Map<String, DisplayableNames> =
        elementTypeEnumClass.java.enumConstants.associateBy(
            { it.name },
            { DisplayableNames(it.name, it.name) },
        )

    fun createElementDtosFromEnum(elementTypeEnumClass: KClass<out Enum<*>>): List<CalculationElementDto> =
        elementTypeEnumClass.java.enumConstants.map {
            CalculationElementDto(it.name, DisplayableNamesDto(it.name, it.name))
        }

    fun createElementsAsStringFromEnum(elementTypeEnumClass: KClass<out Enum<*>>): String =
        "[${elementTypeEnumClass.java.enumConstants.joinToString(",") {
            "{ \"key\": \"$it\", \"displayNames\": { \"shortName\": \"$it\", \"longName\": \"$it\"} }"
        }}]"
}
