package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.entities.Machine
import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingStep
import com.nu.bom.core.manufacturing.entities.Setup
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.TsetDefaultSkillType
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.utils.mapOfNotNull
import com.nu.bom.core.utils.visitTree
import org.assertj.core.api.Assertions
import org.bson.types.Decimal128
import org.bson.types.ObjectId
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.time.LocalDate

class LocationFieldMigrationTest {
    @Test
    fun shouldMigrateExistingLocationToChildEntities() {
        val entity = createEntity()
        val migratedEntity = LocationFieldMigration().map(entity)
        validateEntity(migratedEntity)
    }

    @Test
    fun shouldNotMigrateLocationIfExists() {
        val entity = createLocationEntity()
        val migratedEntity = LocationFieldMigration().map(entity)
        validateNotMigratedEntity(migratedEntity)
    }

    private fun validateEntity(entity: ManufacturingModelEntity) {
        val manufacturingSteps =
            entity.visitTree(
                func = { node, _ ->
                    if (node.type == Entities.MANUFACTURING_STEP.name) {
                        node
                    } else {
                        null
                    }
                },
                children = { node -> node.children },
            )
        Assertions.assertThat(manufacturingSteps.size).isEqualTo(2)
    }

    private fun validateNotMigratedEntity(entity: ManufacturingModelEntity) {
        val machineLocation = entity.children[0].children[0].fieldWithResults["location"]
        Assertions.assertThat(machineLocation!!.value).isEqualTo("Dont Overwrite me")
    }

    private fun createEntity() =
        ManufacturingModelEntity(
            id = ObjectId.get(),
            name = "Manu-1",
            type = "MANUFACTURING",
            clazz = ManualManufacturing::class.simpleName!!,
            args = emptyMap(),
            fieldWithResults = createLocationField("Italy"),
            initialFieldWithResults = emptyMap(),
            children =
                listOf(
                    ManufacturingModelEntity(
                        id = ObjectId.get(),
                        name = "step 1",
                        type = Entities.MANUFACTURING_STEP.name,
                        clazz = ManufacturingStep::class.simpleName!!,
                        args = emptyMap(),
                        fieldWithResults = createLocationField("Italy"),
                        initialFieldWithResults = emptyMap(),
                        children =
                            listOf(
                                ManufacturingModelEntity(
                                    id = ObjectId.get(),
                                    name = "Machine",
                                    type = Entities.MACHINE.name,
                                    clazz = Machine::class.simpleName!!,
                                    args = emptyMap(),
                                    fieldWithResults = emptyMap(),
                                    initialFieldWithResults = emptyMap(),
                                ),
                                ManufacturingModelEntity(
                                    id = ObjectId.get(),
                                    name = "Setup",
                                    type = Entities.SETUP.name,
                                    clazz = Setup::class.simpleName!!,
                                    args = emptyMap(),
                                    fieldWithResults =
                                        mapOf(
                                            "skillType" to
                                                FieldResultModel(
                                                    0,
                                                    0,
                                                    "SkillType",
                                                    TsetDefaultSkillType.UNSKILLED_WORKER.mdKey,
                                                    FieldResult.SOURCE.C.name,
                                                ),
                                            "wage" to
                                                FieldResultModel(
                                                    0,
                                                    0,
                                                    "Money",
                                                    Money(BigDecimal.TEN),
                                                    FieldResult.SOURCE.C.name,
                                                ),
                                        ),
                                    initialFieldWithResults = emptyMap(),
                                ),
                                ManufacturingModelEntity(
                                    id = ObjectId.get(),
                                    name = "Location",
                                    type = "LOCATION",
                                    clazz = "Location",
                                    args = emptyMap(),
                                    fieldWithResults = createLocationField("Italy"),
                                    initialFieldWithResults = emptyMap(),
                                ),
                            ),
                    ),
                    ManufacturingModelEntity(
                        id = ObjectId.get(),
                        name = "Manu-2",
                        type = "MANUFACTURING",
                        clazz = ManualManufacturing::class.simpleName!!,
                        args = emptyMap(),
                        fieldWithResults =
                            mapOfNotNull(
                                Manufacturing::masterdataTimestamp.name to
                                    FieldResultModel(
                                        0,
                                        0,
                                        "Num",
                                        Decimal128(500),
                                        FieldResult.SOURCE.C.name,
                                    ),
                                "location" to
                                    FieldResultModel(
                                        0,
                                        0,
                                        "Text",
                                        "Belgium",
                                        FieldResult.SOURCE.C.name,
                                    ),
                            ),
                        initialFieldWithResults = emptyMap(),
                        children =
                            listOf(
                                ManufacturingModelEntity(
                                    id = ObjectId.get(),
                                    name = "step 2",
                                    type = Entities.MANUFACTURING_STEP.name,
                                    clazz = ManufacturingStep::class.simpleName!!,
                                    args = emptyMap(),
                                    fieldWithResults = createLocationField("Belgium"),
                                    initialFieldWithResults = emptyMap(),
                                ),
                            ),
                    ),
                ),
        )

    private fun createLocationEntity() =
        ManufacturingModelEntity(
            id = ObjectId.get(),
            name = "Manu-1",
            type = "MANUFACTURING",
            clazz = ManualManufacturing::class.simpleName!!,
            args = emptyMap(),
            fieldWithResults = createLocationField("Italy"),
            initialFieldWithResults = emptyMap(),
            children =
                listOf(
                    ManufacturingModelEntity(
                        id = ObjectId.get(),
                        name = "step 1",
                        type = Entities.MANUFACTURING_STEP.name,
                        clazz = ManufacturingStep::class.simpleName!!,
                        args = emptyMap(),
                        fieldWithResults = createLocationField("Italy"),
                        initialFieldWithResults = emptyMap(),
                        children =
                            listOf(
                                ManufacturingModelEntity(
                                    id = ObjectId.get(),
                                    name = "Machine",
                                    type = Entities.MACHINE.name,
                                    clazz = Machine::class.simpleName!!,
                                    args = emptyMap(),
                                    fieldWithResults = createLocationField("Dont Overwrite me"),
                                    initialFieldWithResults = emptyMap(),
                                ),
                            ),
                    ),
                ),
        )

    private fun createLocationField(location: String): Map<String, FieldResultModel> =
        mapOf(
            "shiftsPerDay" to
                FieldResultModel(
                    0,
                    0,
                    "Num",
                    3,
                    FieldResult.SOURCE.C.name,
                ),
            "location" to
                FieldResultModel(
                    0,
                    0,
                    "Text",
                    location,
                    FieldResult.SOURCE.C.name,
                ),
            "locationName" to
                FieldResultModel(
                    0,
                    0,
                    "Text",
                    location,
                    FieldResult.SOURCE.C.name,
                ),
            "displayDesignation" to
                FieldResultModel(
                    0,
                    0,
                    "Text",
                    location,
                    FieldResult.SOURCE.C.name,
                ),
            "laborBurden" to
                FieldResultModel(
                    0,
                    0,
                    "Rate",
                    Rate(BigDecimal.ONE),
                    FieldResult.SOURCE.C.name,
                ),
            "calculationDate" to
                FieldResultModel(
                    0,
                    0,
                    "Date",
                    LocalDate.of(2020, 7, 11),
                    FieldResult.SOURCE.C.name,
                ),
        )
}
