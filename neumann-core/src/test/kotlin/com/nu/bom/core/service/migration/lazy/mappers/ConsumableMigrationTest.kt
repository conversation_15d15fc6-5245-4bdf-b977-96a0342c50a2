package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.entities.BaseMaterial
import com.nu.bom.core.manufacturing.entities.masterdata.material.MaterialConsumerExtension
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.model.MasterDataSelector
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import org.assertj.core.api.Assertions
import org.bson.types.ObjectId
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class ConsumableMigrationTest {
    companion object {
        @JvmStatic
        fun entities() =
            listOf(
                Entities.CONSUMABLE,
            )
    }

    @ParameterizedTest
    @MethodSource("entities")
    fun shouldMigrateHeaderKey(entityType: Entities) {
        val headerKey = "Water"
        val entity =
            createEntity(
                headerKey = headerKey,
                entityType,
            )
        val migratedEntity = ConsumableMigration().map(entity)
        Assertions.assertThat(migratedEntity.initialFieldWithResults.size).isEqualTo(1)
        Assertions.assertThat(migratedEntity.fieldWithResults.size).isEqualTo(1)

        assertHeaderKeyExists(
            fieldWithResults = migratedEntity.fieldWithResults,
            expectedheaderKey = headerKey,
        )
        assertHeaderKeyExists(
            fieldWithResults = migratedEntity.initialFieldWithResults,
            expectedheaderKey = headerKey,
        )
    }

    @ParameterizedTest
    @MethodSource("entities")
    fun shouldMigrateEmptyHeaderKey(entityType: Entities) {
        val headerKey = null
        val entity =
            createEntity(
                headerKey = headerKey,
                entityType,
            )
        val migratedEntity = ConsumableMigration().map(entity)
        Assertions.assertThat(migratedEntity.initialFieldWithResults.size).isEqualTo(2)
        Assertions.assertThat(migratedEntity.fieldWithResults.size).isEqualTo(1)

        assertEmptyHeaderKeyExists(
            fieldWithResults = migratedEntity.fieldWithResults,
        )
        assertEmptyHeaderKeyExists(
            fieldWithResults = migratedEntity.initialFieldWithResults,
        )
        assertEmissionExists(
            fieldWithResults = migratedEntity.initialFieldWithResults,
        )
    }

    private fun assertHeaderKeyExists(
        fieldWithResults: Map<String, FieldResultModel>,
        expectedheaderKey: String,
    ) {
        val headerKey = fieldWithResults[MaterialConsumerExtension::headerKey.name]
        Assertions.assertThat(headerKey).isNotNull
        Assertions.assertThat(headerKey?.type).isEqualTo(Text::class.simpleName)
        Assertions.assertThat(headerKey?.value).isInstanceOf(Text::class.java)
        Assertions.assertThat(headerKey?.value).isEqualTo(Text(expectedheaderKey))
    }

    private fun assertEmptyHeaderKeyExists(fieldWithResults: Map<String, FieldResultModel>) {
        val headerKey = fieldWithResults[MaterialConsumerExtension::headerKey.name]
        Assertions.assertThat(headerKey).isNotNull
        Assertions.assertThat(headerKey?.type).isEqualTo(Text::class.simpleName)
        Assertions.assertThat(headerKey?.value).isNull()
    }

    private fun assertEmissionExists(fieldWithResults: Map<String, FieldResultModel>) {
        val cO2PerUnit = fieldWithResults[BaseMaterial::cO2PerUnit.name]
        Assertions.assertThat(cO2PerUnit).isNotNull
        Assertions.assertThat(cO2PerUnit?.type).isEqualTo(Emission::class.simpleName)
        Assertions.assertThat(cO2PerUnit?.value).isNotNull()
    }

    private fun createEntity(
        headerKey: String?,
        entityType: Entities,
    ) = ManufacturingModelEntity(
        id = ObjectId.get(),
        name = "entity-1",
        type = entityType.name,
        clazz = entityType.clazz!!.simpleName!!,
        args = emptyMap(),
        fieldWithResults = emptyMap(),
        initialFieldWithResults = emptyMap(),
        children = emptyList(),
    ).apply {
        headerKey?.let {
            masterDataSelector =
                MasterDataSelector(
                    type = MasterDataType.NONE,
                    key = it,
                    year = 2025,
                    location = "Austria",
                )
        }
    }
}
