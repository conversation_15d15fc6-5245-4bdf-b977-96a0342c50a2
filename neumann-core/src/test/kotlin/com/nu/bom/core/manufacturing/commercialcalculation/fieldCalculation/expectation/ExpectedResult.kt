package com.nu.bom.core.manufacturing.commercialcalculation.fieldCalculation.expectation

import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationRole

class ExpectedResult(
    val level: AggregationLevel,
    val role: AggregationRole,
) {
    var result: DoubleMap = DoubleMap()

    override fun toString(): String {
        return "level=$level, role=$role, result=$result"
    }
}
