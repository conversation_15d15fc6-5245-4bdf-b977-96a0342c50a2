package com.nu.bom.core.manufacturing.testentities.optionalfields

import com.nu.bom.core.manufacturing.annotations.Children
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.entities.BaseEntityFields
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Currency
import com.nu.bom.core.manufacturing.fieldTypes.ExchangeRatesField
import com.nu.bom.core.manufacturing.fieldTypes.Money
import org.springframework.data.annotation.Transient
import java.math.BigDecimal

@EntityType(Entities.MANUFACTURING_STEP)
class TestManufacturingStepWithOptionalFieldSummation(name: String) : ManufacturingEntity(name) {

    @Transient
    override val extends = BaseEntityFields(name)

    fun sumOfOptionals(@Children(Entities.MACHINE) optionalField: List<Money>?): Money {
        return sum(optionalField)
    }

    fun sumOfOtherOptionals(@Children(Entities.MACHINE) nonCalculatedField: List<Money>?): Money {
        return sum(nonCalculatedField)
    }

    fun baseCurrency(): Currency = Currency("EUR")

    fun exchangeRates(): ExchangeRatesField = ExchangeRatesField(mapOf(com.tset.core.service.domain.Currency("EUR") to BigDecimal.ONE))
}
