package com.nu.bom.core.manufacturing.commercialcalculation.subcalculator

import com.nu.bom.core.manufacturing.commercialcalculation.fieldCalculation.CommercialCalculationTestBase
import com.nu.bom.core.manufacturing.entities.BomEntry
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.reflect.KClass

class EntityCreationForSubCalculatorTest : CommercialCalculationTestBase() {
    @Test
    fun `add no manual entity and consider only created entities`() {
        createAndRun(EntityCreationKeepOnlyCreatedEntitiesTestEntity::class, false, 1, 0.0)
    }

    @Test
    fun `add no manual entity and consider all entities`() {
        createAndRun(EntityCreationKeepAllEntitiesTestEntity::class, false, 1, 0.0)
    }

    @Test
    fun `add manual entity and consider only created entities`() {
        createAndRun(EntityCreationKeepOnlyCreatedEntitiesTestEntity::class, true, 2, -1.0)
    }

    @Test
    fun `add manual entity and consider all entities`() {
        createAndRun(EntityCreationKeepAllEntitiesTestEntity::class, true, 1, 1.0)
    }

    @Test
    fun `throws when several entities with the same name are created`() {
        assertThrows<AssertionError> {
            val entity =
                builder.addObject(
                    name = "Entity",
                    clazz = EntityCreationKeepAllEntitiesTestEntity::class.java,
                    parameters = mapOf("input" to Text("Old"), "numberOfEntities" to Num(2)),
                )
            calculate(entity)
        }.let { exception ->
            org.assertj.core.api.Assertions.assertThat(exception.message).contains(
                "Cannot have several entities with the same name created!",
            )
        }
    }

    private fun createAndRun(
        clazz: KClass<out ManufacturingEntity>,
        manualChildIsAdded: Boolean,
        expectedChildrenCount: Int,
        expectedQuantity: Double,
    ) {
        val entity = builder.addObject(name = "Entity", clazz = clazz.java, parameters = mapOf("input" to Text("Old")))
        if (manualChildIsAdded) {
            builder.addObject(
                name = "Test",
                clazz = BomEntry::class.java,
                parent = entity,
                overrides = mapOf(BomEntry::quantity.name to QuantityUnit(1.0)),
            )
        }
        calculate(entity)

        val maybeChildren = entity.children.filterIsInstance<BomEntry>()
        Assertions.assertEquals(expectedChildrenCount, maybeChildren.size)
        if (expectedChildrenCount == 1) {
            validateResultFieldScaled("Test", BomEntry::quantity.name, expectedQuantity, 5)
        }
    }
}
