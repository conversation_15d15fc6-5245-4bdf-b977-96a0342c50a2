package com.nu.bom.core.manufacturing.commercialcalculation.uiconfig

import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.TestConfigurationFactory.createInternalTsetDefaultCostConfig
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.UniqueOperationIdentifier
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.InternalConfigurationOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCostCalculationElementType
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.OperationConfigurationInformationExtractor.expandOperationNested
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.TableOperationExpansionLogic
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class OperationConfigurationInformationExtractorTest {
    data class TestCase(
        val tableOperationExpansionLogic: TableOperationExpansionLogic,
        val expectedOperationIds: List<UniqueOperationIdentifier>,
    )

    @ParameterizedTest
    @MethodSource("getTestCases")
    fun ` correct operations are extracted`(testCase: TestCase) {
        val actual =
            expandOperationNested(config, testCase.tableOperationExpansionLogic).map {
                it.operation
            }
        Assertions.assertEquals(
            testCase.expectedOperationIds.map { config.getOperation<InternalConfigurationOperation>(it) },
            actual,
        )
    }

    companion object {
        @JvmStatic
        fun getTestCases() =
            listOf(
                TestCase(
                    TableOperationExpansionLogic(
                        config.getOperation(
                            UniqueOperationIdentifier(TsetCostCalculationElementType.SALES_PRICE.fieldName, AggregationLevel.SOLD_MATERIAL),
                        ),
                        1,
                        takeOnlyLeaves = false,
                        includeTopLevel = true,
                    ),
                    expectedOperationIds =
                        listOf(
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.PRODUCTION_COSTS.fieldName,
                                AggregationLevel.MANUFACTURING_STEP,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.INDIRECT_COSTS_AFTER_PRODUCTION.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.ALLOCATED_AND_INTEREST_COSTS_FOR_INVEST.fieldName,
                                AggregationLevel.INVEST,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.SALES_PRICE.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                        ),
                ),
                TestCase(
                    TableOperationExpansionLogic(
                        config.getOperation(
                            UniqueOperationIdentifier(TsetCostCalculationElementType.SALES_PRICE.fieldName, AggregationLevel.SOLD_MATERIAL),
                        ),
                        1,
                        takeOnlyLeaves = true,
                        includeTopLevel = true,
                    ),
                    expectedOperationIds =
                        listOf(
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.PRODUCTION_COSTS.fieldName,
                                AggregationLevel.MANUFACTURING_STEP,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.INDIRECT_COSTS_AFTER_PRODUCTION.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.ALLOCATED_AND_INTEREST_COSTS_FOR_INVEST.fieldName,
                                AggregationLevel.INVEST,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.SALES_PRICE.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                        ),
                ),
                TestCase(
                    TableOperationExpansionLogic(
                        config.getOperation(
                            UniqueOperationIdentifier(TsetCostCalculationElementType.SALES_PRICE.fieldName, AggregationLevel.SOLD_MATERIAL),
                        ),
                        1,
                        takeOnlyLeaves = true,
                        includeTopLevel = false,
                    ),
                    expectedOperationIds =
                        listOf(
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.PRODUCTION_COSTS.fieldName,
                                AggregationLevel.MANUFACTURING_STEP,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.INDIRECT_COSTS_AFTER_PRODUCTION.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.ALLOCATED_AND_INTEREST_COSTS_FOR_INVEST.fieldName,
                                AggregationLevel.INVEST,
                            ),
                        ),
                ),
                TestCase(
                    TableOperationExpansionLogic(
                        config.getOperation(
                            UniqueOperationIdentifier(TsetCostCalculationElementType.SALES_PRICE.fieldName, AggregationLevel.SOLD_MATERIAL),
                        ),
                        1,
                        takeOnlyLeaves = false,
                        includeTopLevel = false,
                    ),
                    expectedOperationIds =
                        listOf(
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.PRODUCTION_COSTS.fieldName,
                                AggregationLevel.MANUFACTURING_STEP,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.INDIRECT_COSTS_AFTER_PRODUCTION.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.ALLOCATED_AND_INTEREST_COSTS_FOR_INVEST.fieldName,
                                AggregationLevel.INVEST,
                            ),
                        ),
                ),
                TestCase(
                    TableOperationExpansionLogic(
                        config.getOperation(
                            UniqueOperationIdentifier(TsetCostCalculationElementType.SALES_PRICE.fieldName, AggregationLevel.SOLD_MATERIAL),
                        ),
                        2,
                        takeOnlyLeaves = false,
                        includeTopLevel = true,
                    ),
                    expectedOperationIds =
                        listOf(
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.MATERIAL_COSTS.fieldName,
                                AggregationLevel.MANUFACTURED_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.MANUFACTURING_COSTS_3.fieldName,
                                AggregationLevel.MANUFACTURING_STEP,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.PRODUCTION_COSTS.fieldName,
                                AggregationLevel.MANUFACTURING_STEP,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.SPECIAL_DIRECT_COSTS.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.OVERHEADS_AFTER_PC.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.PROFIT.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.TERMS_OF_PAYMENT.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.INCO_TERMS.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.INDIRECT_COSTS_AFTER_PRODUCTION.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.INTEREST_COSTS_FOR_INVEST.fieldName,
                                AggregationLevel.INVEST,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.ALLOCATED_COSTS_FOR_INVEST.fieldName,
                                AggregationLevel.INVEST,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.ALLOCATED_AND_INTEREST_COSTS_FOR_INVEST.fieldName,
                                AggregationLevel.INVEST,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.SALES_PRICE.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                        ),
                ),
                TestCase(
                    TableOperationExpansionLogic(
                        config.getOperation(
                            UniqueOperationIdentifier(TsetCostCalculationElementType.SALES_PRICE.fieldName, AggregationLevel.SOLD_MATERIAL),
                        ),
                        2,
                        takeOnlyLeaves = true,
                        includeTopLevel = true,
                    ),
                    expectedOperationIds =
                        listOf(
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.MATERIAL_COSTS.fieldName,
                                AggregationLevel.MANUFACTURED_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.MANUFACTURING_COSTS_3.fieldName,
                                AggregationLevel.MANUFACTURING_STEP,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.SPECIAL_DIRECT_COSTS.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.OVERHEADS_AFTER_PC.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.PROFIT.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.TERMS_OF_PAYMENT.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.INCO_TERMS.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.INTEREST_COSTS_FOR_INVEST.fieldName,
                                AggregationLevel.INVEST,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.ALLOCATED_COSTS_FOR_INVEST.fieldName,
                                AggregationLevel.INVEST,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.SALES_PRICE.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                        ),
                ),
                TestCase(
                    TableOperationExpansionLogic(
                        config.getOperation(
                            UniqueOperationIdentifier(TsetCostCalculationElementType.SALES_PRICE.fieldName, AggregationLevel.SOLD_MATERIAL),
                        ),
                        2,
                        takeOnlyLeaves = true,
                        includeTopLevel = false,
                    ),
                    expectedOperationIds =
                        listOf(
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.MATERIAL_COSTS.fieldName,
                                AggregationLevel.MANUFACTURED_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.MANUFACTURING_COSTS_3.fieldName,
                                AggregationLevel.MANUFACTURING_STEP,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.SPECIAL_DIRECT_COSTS.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.OVERHEADS_AFTER_PC.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.PROFIT.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.TERMS_OF_PAYMENT.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.INCO_TERMS.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.INTEREST_COSTS_FOR_INVEST.fieldName,
                                AggregationLevel.INVEST,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.ALLOCATED_COSTS_FOR_INVEST.fieldName,
                                AggregationLevel.INVEST,
                            ),
                        ),
                ),
                TestCase(
                    TableOperationExpansionLogic(
                        config.getOperation(
                            UniqueOperationIdentifier(TsetCostCalculationElementType.SALES_PRICE.fieldName, AggregationLevel.SOLD_MATERIAL),
                        ),
                        2,
                        takeOnlyLeaves = false,
                        includeTopLevel = false,
                    ),
                    expectedOperationIds =
                        listOf(
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.MATERIAL_COSTS.fieldName,
                                AggregationLevel.MANUFACTURED_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.MANUFACTURING_COSTS_3.fieldName,
                                AggregationLevel.MANUFACTURING_STEP,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.PRODUCTION_COSTS.fieldName,
                                AggregationLevel.MANUFACTURING_STEP,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.SPECIAL_DIRECT_COSTS.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.OVERHEADS_AFTER_PC.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.PROFIT.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.TERMS_OF_PAYMENT.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.INCO_TERMS.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.INDIRECT_COSTS_AFTER_PRODUCTION.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.INTEREST_COSTS_FOR_INVEST.fieldName,
                                AggregationLevel.INVEST,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.ALLOCATED_COSTS_FOR_INVEST.fieldName,
                                AggregationLevel.INVEST,
                            ),
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.ALLOCATED_AND_INTEREST_COSTS_FOR_INVEST.fieldName,
                                AggregationLevel.INVEST,
                            ),
                        ),
                ),
            )

        private val config = createInternalTsetDefaultCostConfig()
    }
}
