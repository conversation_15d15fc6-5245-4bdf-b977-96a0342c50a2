package com.nu.bom.core.manufacturing.configFields.behaviourFieldInjection

import com.nu.bom.core.CalculationTestBase
import com.nu.bom.core.manufacturing.configFields.behaviourFieldInjection.fieldBuilder.TestDynamicFieldConfigBuilder
import com.nu.bom.core.manufacturing.configFields.behaviourFieldInjection.testEntities.ChildTestEntityForFieldInjection
import com.nu.bom.core.manufacturing.configFields.behaviourFieldInjection.testEntities.ChildTestEntityForFieldInjectionWithDelay
import com.nu.bom.core.manufacturing.configFields.behaviourFieldInjection.testEntities.ParentTestEntityForFieldInjection
import com.nu.bom.core.manufacturing.configFields.behaviourFieldInjection.testEntities.ParentTestEntityForFieldInjectionWithChildCreation
import com.nu.bom.core.manufacturing.entities.BaseEntityFields
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.service.FieldType
import com.nu.bom.core.manufacturing.service.RelType
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

class DynamicFieldsTest : CalculationTestBase(dynamicEntityFieldConfigBuilders = listOf(TestDynamicFieldConfigBuilder())) {
    @Test
    fun simpleEntityWithFieldInjection() {
        val entityForFieldInjection = createEntityForFieldInjection(costValue = 13.0)
        calculateAndPrintResult(entityForFieldInjection)

        val fieldName = "dynamicAssignedMoneyField"
        validateFieldDependencyTo(entityForFieldInjection, fieldName, FieldType.BehaviourCreation, shouldExist = false)
        validateFieldDependencyTo(entityForFieldInjection, fieldName, FieldType.Creation, shouldExist = false)
        validateResult(entityForFieldInjection, fieldName, 13.0)
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun parentChildEntitiesWithFieldInjection(delay: Boolean) {
        val parent = createParentEntityWhichCreatesChildForInjection()
        val child1 = createEntityForFieldInjection(name = "child 1", parent = parent, costValue = 3.0, delay)
        val child2 = createEntityForFieldInjection(name = "child 2", parent = parent, costValue = 7.0, delay)

        calculateAndPrintResult(parent)

        val sumOfStaticFieldsName = "dynamicSumOfInputCostField"
        validateFieldDependencyTo(parent, sumOfStaticFieldsName, FieldType.Creation, shouldExist = false)
//        validateFieldDependencyTo(parent, sumOfStaticFieldsName, FieldType.BEHAVIOUR_CREATION, shouldExist = false, child1)
//        validateFieldDependencyTo(parent, sumOfStaticFieldsName, FieldType.BEHAVIOUR_CREATION, shouldExist = false, child2)
        validateResult(parent, sumOfStaticFieldsName, 10.0)

        val sumOfDynamicFieldsName = "dynamicSumMoneyFieldOfDynamicChildFields"
        validateFieldDependencyTo(parent, sumOfDynamicFieldsName, FieldType.Creation, shouldExist = false)
//        validateFieldDependencyTo(parent, sumOfDynamicFieldsName, FieldType.BEHAVIOUR_CREATION, shouldExist = true, child1)
//        validateFieldDependencyTo(parent, sumOfDynamicFieldsName, FieldType.BEHAVIOUR_CREATION, shouldExist = true, child2)
        validateResult(parent, sumOfDynamicFieldsName, 10.0)
    }

    @Test
    fun parentChildEntitiesWithFieldInjectionAndChildCreatedByEntityCreation() {
        val parent = createParentEntityWhichCreatesChildForInjection(true)
        calculateAndPrintResult(parent)

        val sumOfDynamicFieldsName = "dynamicSumMoneyFieldOfDynamicChildFields"
        // validateFieldDependencyTo(parent, sumOfDynamicFieldsName, FieldType.BEHAVIOUR_CREATION, shouldExist = true)
        validateFieldDependencyTo(parent, sumOfDynamicFieldsName, FieldType.Creation, shouldExist = true)
        validateResult(parent, sumOfDynamicFieldsName, 20.0)

        val sumOfStaticFieldsName = "dynamicSumOfInputCostField"
        // validateFieldDependencyTo(parent, sumOfStaticFieldsName, FieldType.BEHAVIOUR_CREATION, shouldExist = false)
        validateFieldDependencyTo(parent, sumOfStaticFieldsName, FieldType.Creation, shouldExist = true)
        validateResult(parent, sumOfStaticFieldsName, 20.0)
    }

    @Test
    fun parentChildEntitiesWithFieldInjectionAndManualAndDynamicCreation() {
        val parent = createParentEntityWhichCreatesChildForInjection(true)
        val child1 = createEntityForFieldInjection(name = "child 1", parent = parent, costValue = 3.0)
        val child2 = createEntityForFieldInjection(name = "child 2", parent = parent, costValue = 7.0)

        calculateAndPrintResult(parent)

        val sumOfDynamicFieldsName = "dynamicSumMoneyFieldOfDynamicChildFields"
        // validateFieldDependencyTo(parent, sumOfDynamicFieldsName, FieldType.BEHAVIOUR_CREATION, shouldExist = true, child1)
        // validateFieldDependencyTo(parent, sumOfDynamicFieldsName, FieldType.BEHAVIOUR_CREATION, shouldExist = true, child2)
        validateFieldDependencyTo(parent, sumOfDynamicFieldsName, FieldType.Creation, shouldExist = true)
        validateResult(parent, sumOfDynamicFieldsName, 30.0)

        val sumOfStaticFieldsName = "dynamicSumOfInputCostField"
        // validateFieldDependencyTo(parent, sumOfStaticFieldsName, FieldType.BEHAVIOUR_CREATION, shouldExist = false, child1)
        // validateFieldDependencyTo(parent, sumOfStaticFieldsName, FieldType.BEHAVIOUR_CREATION, shouldExist = false, child2)
        validateFieldDependencyTo(parent, sumOfStaticFieldsName, FieldType.Creation, shouldExist = true)
        validateResult(parent, sumOfStaticFieldsName, 30.0)
    }

    private fun validateFieldDependencyTo(
        entity: ManufacturingEntity,
        fieldName: String,
        fieldType: FieldType,
        shouldExist: Boolean,
        targetEntity: ManufacturingEntity = entity,
    ) {
        val field = calculated.singleOrNull { it.name == fieldName && it.entity == entity }
        Assertions.assertNotNull(field, "Could not find field '$fieldName' on entity '${entity.entityId}'")

        val dependency =
            field!!.inputs.singleOrNull {
                it.relType == RelType.CREATION && it.linkedField?.model?.fieldType == fieldType && it.entityId == targetEntity.entityId
            }
        if (shouldExist) {
            Assertions.assertNotNull(
                dependency,
                "Field $fieldName should have a dependency to a $fieldType field\n" +
                    "Existing dependencies are:\n${field.inputs.joinToString("\n")}",
            )
        } else {
            Assertions.assertNull(
                dependency,
                "Field $fieldName should NOT have a dependency to a $fieldType field\n" +
                    "Existing dependencies are:\n${field.inputs.joinToString("\n")}",
            )
        }
    }

    private fun validateResult(
        entity: ManufacturingEntity,
        fieldName: String,
        expectedValue: Double,
    ) {
        validateResultFieldScaled(entity.name, fieldName, expectedValue.toBigDecimal(), 5)
    }

    private fun calculateAndPrintResult(entityForFieldInjection: ManufacturingEntity) {
        calculate(entityForFieldInjection)

        printResults(
            true,
            true,
            hideDependencies = false,
            hideFieldsFromKClasses =
                setOf(
                    ManufacturingEntity::class,
                    BaseEntityFields::class,
                    BaseManufacturing::class,
                ),
            printSpecialFields = true,
        )
    }

    private fun createParentEntityWhichCreatesChildForInjection(
        createChildren: Boolean = false,
        name: String = "Test entity parent for field injection",
        parent: ManufacturingEntity? = null,
    ): ManufacturingEntity {
        return addObject(
            name = name,
            clazz =
                if (createChildren) {
                    ParentTestEntityForFieldInjectionWithChildCreation::class.java
                } else {
                    ParentTestEntityForFieldInjection::class.java
                },
            parent = parent,
        )
    }

    private fun createEntityForFieldInjection(
        name: String = "Test entity for field injection",
        parent: ManufacturingEntity? = null,
        costValue: Double = 13.0,
        delay: Boolean = false,
    ): ManufacturingEntity {
        return addObject(
            name = name,
            clazz = if (delay) ChildTestEntityForFieldInjectionWithDelay::class.java else ChildTestEntityForFieldInjection::class.java,
            parent = parent,
            parameters =
                mapOf(
                    ChildTestEntityForFieldInjection::rate.name to Rate(0.07.toBigDecimal()),
                    ChildTestEntityForFieldInjection::inputCost.name to Money(costValue.toBigDecimal()),
                ),
        )
    }
}
