package com.nu.bom.core.basecalculation

import com.nu.bom.core.CalculationTestBase
import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.fieldTypes.Null
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.testentities.modularization.IsolatedStepWithParent
import org.junit.jupiter.api.Test

class ExternalDependencyParentTest : CalculationTestBase() {

    @Test
    fun testAddingModularizedStepAndProvideParentInputs() {
        val manufacturing = addObject(
            "Generic Manufacturing", ManualManufacturing::class.java
        )
        /** step */
        val isolatedStep = addObject(
            name = "Isolated Step",
            clazz = IsolatedStepWithParent::class.java,
            parent = manufacturing,
        )
        isolatedStep.isolated = true

        calculate(manufacturing)

        // External Dep Fiel Available on parent (source) and child (target) with Null value intially
        validateEntityField(manufacturing, "Isolated Step", "secretValue", Null())
        validateEntityField(manufacturing, "Generic Manufacturing", "secretValue", Null())

        // Provide Value of created dynamic field on parent and check its properly applied and propagated
        builder.clear()
        builder.addInputs(manufacturing, parameters = mapOf("secretValue" to Num(1)))
        calculate(manufacturing)
        validateEntityField(manufacturing, "Isolated Step", "secretValue", Num(1))
        validateEntityField(manufacturing, "Generic Manufacturing", "secretValue", Num(1))
        validateResultField("Isolated Step", "calculatedValue2", 5.0)

        // Override externalDep field directly at child and check that its correctly applied and propagated
        builder.clear()
        builder.addInputs(isolatedStep, parameters = mapOf("secretValue" to Num(2)))
        calculate(manufacturing)
        validateEntityField(manufacturing, "Generic Manufacturing", "secretValue", Num(1))
        validateEntityField(manufacturing, "Isolated Step", "secretValue", Num(2))
        validateResultField("Isolated Step", "calculatedValue2", 10.0)
    }

    @Test
    fun testAddingModularizedStepAndProvideInputsDirectly() {
        val manufacturing = addObject(
            "Generic Manufacturing", ManualManufacturing::class.java
        )

        /** step */
        val isolatedStep = addObject(
            name = "Isolated Step",
            clazz = IsolatedStepWithParent::class.java,
            parent = manufacturing,
        )

        isolatedStep.isolated = true

        calculate(manufacturing)

        // External Dep Fiel Available on parent (source) and child (target) with Null value intially
        validateEntityField(manufacturing, "Isolated Step", "secretValue", Null())
        validateEntityField(manufacturing, "Generic Manufacturing", "secretValue", Null())

        // Override field directy and child and check that its correctly applied and propagated + field at parent and other external/dynamic fields stay untouched
        builder.clear()
        builder.addInputs(isolatedStep, parameters = mapOf("secretValue" to Num(2)))
        calculate(manufacturing)
        validateEntityField(manufacturing, "Generic Manufacturing", "secretValue", Null())
        validateEntityField(manufacturing, "Generic Manufacturing", "secretValue2", Null())
        validateEntityField(manufacturing, "Isolated Step", "secretValue2", Null())
        validateEntityField(manufacturing, "Isolated Step", "secretValue", Num(2))
        validateResultField("Isolated Step", "calculatedValue2", 10.0)
    }
}
