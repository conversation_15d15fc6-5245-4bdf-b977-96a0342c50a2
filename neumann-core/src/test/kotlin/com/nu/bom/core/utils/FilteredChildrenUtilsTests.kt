package com.nu.bom.core.utils

import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Text
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.fail
import org.bson.types.ObjectId
import org.junit.jupiter.api.Test

class FilteredChildrenUtilsTests {
    // getResultWithSelectedAttributes tests //

    // ------------------------------------------------------------------
    // SINGLE ATTRIBUTE TESTS (1 item in attributeMap for normal scenario)
    // ------------------------------------------------------------------

    @Test
    fun `getResultWithSelectedAttributes single attribute - exactly 1 item in each map - single match`() {
        // One key, one item: we have a direct single match
        val key = ObjectId.get()

        val resultMap =
            mapOf(
                key to Text("SingleResult"),
            )
        val attribute1Map =
            mapOf(
                key to Num(42), // 1 item
            )

        val found =
            getResultWithSelectedAttributes(
                resultMap = resultMap,
                attribute1Map = attribute1Map,
                currentAttribute1 = Num(42), // matches exactly
            )

        assertThat(found).isNotNull
        assertThat(found?.res).isEqualTo("SingleResult")
    }

    @Test
    fun ` getResultWithSelectedAttributes single attribute - exactly 1 item in attributeMap - no match returns default`() {
        // 1 item in attribute map, but we ask for something else -> no match => default
        val key = ObjectId.get()

        val resultMap =
            mapOf(
                key to Text("SomeResult"),
            )
        val attribute1Map =
            mapOf(
                key to Num(42),
            )
        val defaultValue = Text("DefaultValue")

        val found =
            getResultWithSelectedAttributes(
                resultMap = resultMap,
                attribute1Map = attribute1Map,
                currentAttribute1 = Num(999), // doesn't match
                defaultValue = defaultValue,
            )

        assertThat(found).isSameAs(defaultValue)
    }

    @Test
    fun `getResultWithSelectedAttributes single attribute - multiple matches throws exception`() {
        // 2 keys both share the same attribute => multiple matches => exception
        val key1 = ObjectId.get()
        val key2 = ObjectId.get()

        val resultMap =
            mapOf(
                key1 to Text("Result1"),
                key2 to Text("Result2"),
            )
        val attribute1Map =
            mapOf(
                key1 to Num(42),
                key2 to Num(42), // same attribute => multiple matches
            )

        try {
            getResultWithSelectedAttributes(
                resultMap = resultMap,
                attribute1Map = attribute1Map,
                currentAttribute1 = Num(42),
            )
            fail("Expected IllegalArgumentException, but no exception was thrown.")
        } catch (ex: IllegalArgumentException) {
            assertThat(ex.message).contains("Multiple matches found")
        }
    }

    @Test
    fun `getResultWithSelectedAttributes single attribute - incomplete map (missing key) yields single match`() {
        // resultMap has 2 keys, but attribute1Map only has 1 key => intersection is just that 1 key
        val key1 = ObjectId.get()
        val key2 = ObjectId.get()

        val resultMap =
            mapOf(
                key1 to Text("Hit"),
                key2 to Text("Ignored because missing attribute"),
            )
        // attribute map has only key1 => key2 is "missing" from attribute1Map
        val attribute1Map =
            mapOf(
                key1 to Num(42),
            )

        val found =
            getResultWithSelectedAttributes(
                resultMap,
                attribute1Map,
                currentAttribute1 = Num(42), // only key1 matches
            )

        assertThat(found).isNotNull
        assertThat(found?.res).isEqualTo("Hit")
    }

    // ------------------------------------------------------------------
    // TWO ATTRIBUTES TESTS (2 items in each attributeMap for normal scenario)
    // ------------------------------------------------------------------

    @Test
    fun `getResultWithSelectedAttributes two attributes - 2 items in each map - single match`() {
        // We'll have 2 items in each attribute map, but only 1 combination that lines up
        val key1 = ObjectId.get()
        val key2 = ObjectId.get()

        // 2 items in the result map
        val resultMap =
            mapOf(
                key1 to Text("MatchMe"),
                key2 to Text("NotMe"),
            )
        // 2 items in attribute1Map
        val attribute1Map =
            mapOf(
                key1 to Num(1),
                key2 to Num(2),
            )
        // 2 items in attribute2Map
        val attribute2Map =
            mapOf(
                key1 to Text("foo"),
                key2 to Text("bar"),
            )

        // We'll pick (1, "foo") => only matches key1
        val found =
            getResultWithSelectedAttributes(
                resultMap = resultMap,
                attribute1Map = attribute1Map,
                attribute2Map = attribute2Map,
                currentAttribute1 = Num(1),
                currentAttribute2 = Text("foo"),
            )

        assertThat(found?.res).isEqualTo("MatchMe")
    }

    @Test
    fun `getResultWithSelectedAttributes two attributes - 2 items in each map - no match returns default`() {
        // We'll pick attribute values that don't line up with any key
        val key1 = ObjectId.get()
        val key2 = ObjectId.get()
        val defaultVal = Text("DefaultVal")

        val resultMap =
            mapOf(
                key1 to Text("ResultA"),
                key2 to Text("ResultB"),
            )
        val attribute1Map =
            mapOf(
                key1 to Num(11),
                key2 to Num(22),
            )
        val attribute2Map =
            mapOf(
                key1 to Text("alpha"),
                key2 to Text("beta"),
            )

        // We'll ask for (999, "xyz") => no matches
        val found =
            getResultWithSelectedAttributes(
                resultMap,
                attribute1Map,
                attribute2Map,
                currentAttribute1 = Num(999),
                currentAttribute2 = Text("xyz"),
                defaultValue = defaultVal,
            )

        assertThat(found).isSameAs(defaultVal)
    }

    @Test
    fun `getResultWithSelectedAttributes two attributes - multiple matches throws exception`() {
        // We'll store the same pair of attribute values for both keys => multiple matches
        val key1 = ObjectId.get()
        val key2 = ObjectId.get()

        val resultMap =
            mapOf(
                key1 to Text("First"),
                key2 to Text("Second"),
            )
        val attribute1Map =
            mapOf(
                key1 to Num(42),
                key2 to Num(42),
            )
        val attribute2Map =
            mapOf(
                key1 to Text("xyz"),
                key2 to Text("xyz"),
            )

        try {
            getResultWithSelectedAttributes(
                resultMap,
                attribute1Map,
                attribute2Map,
                currentAttribute1 = Num(42),
                currentAttribute2 = Text("xyz"),
            )
            fail("Expected IllegalArgumentException, but none was thrown.")
        } catch (ex: IllegalArgumentException) {
            assertThat(ex.message).contains("Multiple matches found")
        }
    }

    @Test
    fun `getResultWithSelectedAttributes two attributes - incomplete map scenario`() {
        // resultMap has 2 keys, but attribute2Map misses key2 => only key1 can match
        val key1 = ObjectId.get()
        val key2 = ObjectId.get()

        val resultMap =
            mapOf(
                key1 to Text("HitThis"),
                key2 to Text("MaybeIgnore"),
            )
        val attribute1Map =
            mapOf(
                key1 to Num(100),
                key2 to Num(200),
            )
        // attribute2Map has only key1 => key2 is missing
        val attribute2Map =
            mapOf(
                key1 to Text("attr2A"),
            )

        val found =
            getResultWithSelectedAttributes(
                resultMap,
                attribute1Map,
                attribute2Map,
                currentAttribute1 = Num(100),
                currentAttribute2 = Text("attr2A"),
            )

        assertThat(found?.res).isEqualTo("HitThis")
    }

    // ------------------------------------------------------------------
    // THREE ATTRIBUTES TESTS (3 items in each attributeMap for normal scenario)
    // ------------------------------------------------------------------

    @Test
    fun `getResultWithSelectedAttributes three attributes - 3 items in each map - single match`() {
        // We'll have 3 items in each attribute map, but exactly one combination lines up
        val key1 = ObjectId.get()
        val key2 = ObjectId.get()
        val key3 = ObjectId.get()

        val resultMap =
            mapOf(
                key1 to Text("Alpha"),
                key2 to Text("Beta"),
                key3 to Text("Gamma"),
            )
        val attribute1Map =
            mapOf(
                key1 to Num(1),
                key2 to Num(1),
                key3 to Num(2),
            )
        val attribute2Map =
            mapOf(
                key1 to Text("foo"),
                key2 to Text("bar"),
                key3 to Text("bar"),
            )
        val attribute3Map =
            mapOf(
                key1 to Num(999),
                key2 to Num(1000),
                key3 to Num(999),
            )

        // We'll pick (1, "foo", 999) => that only matches key1
        val found =
            getResultWithSelectedAttributes(
                resultMap,
                attribute1Map,
                attribute2Map,
                attribute3Map,
                currentAttribute1 = Num(1),
                currentAttribute2 = Text("foo"),
                currentAttribute3 = Num(999),
            )

        assertThat(found?.res).isEqualTo("Alpha")

        // We'll pick (1, "foo", 1000) => that only matches key3
        val found2 =
            getResultWithSelectedAttributes(
                resultMap,
                attribute1Map,
                attribute2Map,
                attribute3Map,
                currentAttribute1 = Num(1),
                currentAttribute2 = Text("bar"),
                currentAttribute3 = Num(1000),
            )

        assertThat(found2?.res).isEqualTo("Beta")

        // We'll pick (2, "bar", 999) => that only matches key3
        val found3 =
            getResultWithSelectedAttributes(
                resultMap,
                attribute1Map,
                attribute2Map,
                attribute3Map,
                currentAttribute1 = Num(2),
                currentAttribute2 = Text("bar"),
                currentAttribute3 = Num(999),
            )

        assertThat(found3?.res).isEqualTo("Gamma")
    }

    @Test
    fun `getResultWithSelectedAttributes three attributes - 3 items in each map - no match returns default`() {
        val key1 = ObjectId.get()
        val key2 = ObjectId.get()
        val key3 = ObjectId.get()
        val defaultVal = Text("DefaultVal")

        val resultMap =
            mapOf(
                key1 to Text("X"),
                key2 to Text("Y"),
                key3 to Text("Z"),
            )
        val attribute1Map =
            mapOf(
                key1 to Num(100),
                key2 to Num(101),
                key3 to Num(102),
            )
        val attribute2Map =
            mapOf(
                key1 to Text("aaa"),
                key2 to Text("bbb"),
                key3 to Text("ccc"),
            )
        val attribute3Map =
            mapOf(
                key1 to Num(999),
                key2 to Num(888),
                key3 to Num(777),
            )

        // We'll ask for something that doesn't exist in any combo
        val found =
            getResultWithSelectedAttributes(
                resultMap,
                attribute1Map,
                attribute2Map,
                attribute3Map,
                currentAttribute1 = Num(9999),
                currentAttribute2 = Text("doesNotExist"),
                currentAttribute3 = Num(9999),
                defaultValue = defaultVal,
            )

        assertThat(found).isSameAs(defaultVal)
    }

    @Test
    fun `getResultWithSelectedAttributes three attributes - multiple matches throws exception`() {
        // We'll replicate the same triple (1, "foo", 9) for multiple keys => multiple matches
        val key1 = ObjectId.get()
        val key2 = ObjectId.get()
        val key3 = ObjectId.get()

        val resultMap =
            mapOf(
                key1 to Text("First"),
                key2 to Text("Second"),
                key3 to Text("Third"),
            )
        val attribute1Map =
            mapOf(
                key1 to Num(1),
                key2 to Num(1),
                key3 to Num(2), // differs for key3, so let's see
            )
        val attribute2Map =
            mapOf(
                key1 to Text("foo"),
                key2 to Text("foo"),
                key3 to Text("foo"),
            )
        val attribute3Map =
            mapOf(
                key1 to Num(9),
                key2 to Num(9),
                key3 to Num(9),
            )

        // keys 1 and 2 share (1, "foo", 9) => multiple matches
        try {
            getResultWithSelectedAttributes(
                resultMap,
                attribute1Map,
                attribute2Map,
                attribute3Map,
                currentAttribute1 = Num(1),
                currentAttribute2 = Text("foo"),
                currentAttribute3 = Num(9),
            )
            fail("Expected IllegalArgumentException for multiple matches, but none thrown.")
        } catch (ex: IllegalArgumentException) {
            assertThat(ex.message).contains("Multiple matches found")
        }
    }

    // transformUniquePermutations tests //

    // ------------------------------------------------------
    // SINGLE ATTRIBUTE TESTS
    // ------------------------------------------------------

    @Test
    fun `transformUniquePermutations single attribute single key`() {
        val key = ObjectId.get()
        val map = mapOf(key to Text("SomeText"))

        val results =
            transformUniquePermutations(map) { text ->
                // text is a FieldResultStar (actually a Text)
                "Result for: ${text.res}"
            }

        assertThat(results).hasSize(1)
        assertThat(results[0]).isEqualTo("Result for: SomeText")
    }

    @Test
    fun `transformUniquePermutations single attribute multiple keys same attribute block called once`() {
        val key1 = ObjectId.get()
        val key2 = ObjectId.get()

        // Both keys share the same attribute (Text("Repeated"))
        val map =
            mapOf(
                key1 to Text("Repeated"),
                key2 to Text("Repeated"),
            )

        val results =
            transformUniquePermutations(map) { text ->
                // This block should only run once for "Repeated"
                "Unique: ${text.res}"
            }

        // Because both keys share the same attribute, we expect only 1 invocation
        assertThat(results).hasSize(1)
        assertThat(results[0]).isEqualTo("Unique: Repeated")
    }

    @Test
    fun `transformUniquePermutations single attribute multiple keys distinct attributes block called for each`() {
        val key1 = ObjectId.get()
        val key2 = ObjectId.get()

        val map =
            mapOf(
                key1 to Text("Foo"),
                key2 to Text("Bar"),
            )

        val results =
            transformUniquePermutations(map) { text ->
                "Got: ${text.res}"
            }

        // Two distinct attributes => two results
        assertThat(results).containsExactlyInAnyOrder("Got: Foo", "Got: Bar")
    }

    @Test
    fun `transformUniquePermutations single attribute no matches due to empty map`() {
        val map = emptyMap<ObjectId, Text>()

        val results =
            transformUniquePermutations(map) { text ->
                "Should not happen"
            }

        // No map entries => no results
        assertThat(results).isEmpty()
    }

    // ------------------------------------------------------
    // TWO ATTRIBUTES TESTS
    // ------------------------------------------------------

    @Test
    fun `transformUniquePermutations two attributes single intersection yields single result`() {
        val key = ObjectId.get()
        val mapA = mapOf(key to Text("TextA"))
        val mapB = mapOf(key to Num(42))

        val results =
            transformUniquePermutations(mapA, mapB) { text, num ->
                // Cast the num.res to Int
                val intVal = num.res.toInt()
                "(${text.res}, $intVal)"
            }

        assertThat(results).containsExactly("(TextA, 42)")
    }

    @Test
    fun `transformUniquePermutations two attributes multiple keys same combination block runs once`() {
        val key1 = ObjectId.get()
        val key2 = ObjectId.get()

        val mapA =
            mapOf(
                key1 to Text("Same"),
                key2 to Text("Same"),
            )
        val mapB =
            mapOf(
                key1 to Num(999),
                key2 to Num(999),
            )

        val results =
            transformUniquePermutations(mapA, mapB) { a, b ->
                val bVal = b.res.toInt()
                "Unique: ${a.res}, $bVal"
            }

        // Expect only one invocation for that combination
        assertThat(results).hasSize(1)
        assertThat(results[0]).isEqualTo("Unique: Same, 999")
    }

    @Test
    fun `transformUniquePermutations two attributes multiple distinct combos yields multiple results`() {
        val key1 = ObjectId.get()
        val key2 = ObjectId.get()
        val key3 = ObjectId.get()

        val mapA =
            mapOf(
                key1 to Text("A1"),
                key2 to Text("A1"),
                key3 to Text("A2"),
            )
        val mapB =
            mapOf(
                key1 to Num(10),
                key2 to Num(10),
                key3 to Num(20),
            )

        // key1 => (A1, 10)
        // key3 => (A2, 20)
        val results =
            transformUniquePermutations(mapA, mapB) { a, b ->
                val bVal = b.res.toInt()
                "(${a.res}, $bVal)"
            }

        assertThat(results).containsExactlyInAnyOrder("(A1, 10)", "(A2, 20)")
    }

    @Test
    fun `transformUniquePermutations two attributes incomplete map scenario yields single intersection`() {
        val key1 = ObjectId.get()
        val key2 = ObjectId.get()

        val mapA =
            mapOf(
                key1 to Text("OnlyFirstKey"),
                key2 to Text("ThisWontIntersect"),
            )
        val mapB =
            mapOf(
                key1 to Num(100),
                // key2 missing => no intersection for key2
            )

        val results =
            transformUniquePermutations(mapA, mapB) { a, b ->
                val bVal = b.res.toInt()
                "${a.res} -> $bVal"
            }

        // Only key1 is present in both => yields a single result
        assertThat(results).containsExactly("OnlyFirstKey -> 100")
    }

    @Test
    fun `transformUniquePermutations two attributes no intersection yields empty`() {
        val key1 = ObjectId.get()
        val key2 = ObjectId.get()

        val mapA = mapOf(key1 to Text("X"))
        val mapB = mapOf(key2 to Num(42))

        // No intersection => no permutations
        val results =
            transformUniquePermutations(mapA, mapB) { _, _ ->
                "Impossible"
            }
        assertThat(results).isEmpty()
    }

    // ------------------------------------------------------
    // THREE ATTRIBUTES TESTS
    // ------------------------------------------------------

    @Test
    fun `transformUniquePermutations three attributes single intersection yields single result`() {
        val key = ObjectId.get()

        val mapA = mapOf(key to Text("A"))
        val mapB = mapOf(key to Num(1))
        val mapC = mapOf(key to Text("C"))

        val results =
            transformUniquePermutations(mapA, mapB, mapC) { a, b, c ->
                val bVal = b.res.toInt()
                "(${a.res}, $bVal, ${c.res})"
            }

        assertThat(results).containsExactly("(A, 1, C)")
    }

    @Test
    fun `transformUniquePermutations three attributes multiple duplicates block runs once per unique triple`() {
        val key1 = ObjectId.get()
        val key2 = ObjectId.get()

        val mapA =
            mapOf(
                key1 to Text("Hello"),
                key2 to Text("Hello"),
            )
        val mapB =
            mapOf(
                key1 to Num(42),
                key2 to Num(42),
            )
        val mapC =
            mapOf(
                key1 to Text("World"),
                key2 to Text("World"),
            )

        // Both keys produce (Hello, 42, World)
        val results =
            transformUniquePermutations(mapA, mapB, mapC) { a, b, c ->
                val bVal = b.res.toInt()
                "${a.res}, $bVal, ${c.res}"
            }

        // Distinct => only 1 triple
        assertThat(results).hasSize(1)
        assertThat(results[0]).isEqualTo("Hello, 42, World")
    }

    @Test
    fun `transformUniquePermutations three attributes multiple distinct triples yields multiple results`() {
        val key1 = ObjectId.get()
        val key2 = ObjectId.get()
        val key3 = ObjectId.get()

        val mapA =
            mapOf(
                key1 to Text("A1"),
                key2 to Text("A1"),
                key3 to Text("A2"),
            )
        val mapB =
            mapOf(
                key1 to Num(10),
                key2 to Num(20),
                key3 to Num(20),
            )
        val mapC =
            mapOf(
                key1 to Text("Z1"),
                key2 to Text("Z2"),
                key3 to Text("Z2"),
            )

        // key1 => (A1, 10, Z1)
        // key2 => (A1, 20, Z2)
        // key3 => (A2, 20, Z2)
        val results =
            transformUniquePermutations(mapA, mapB, mapC) { a, b, c ->
                val bVal = b.res.toInt()
                "${a.res}-$bVal-${c.res}"
            }

        assertThat(results).containsExactlyInAnyOrder(
            "A1-10-Z1",
            "A1-20-Z2",
            "A2-20-Z2",
        )
    }

    @Test
    fun `transformUniquePermutations three attributes incomplete maps yields only intersection`() {
        val key1 = ObjectId.get()
        val key2 = ObjectId.get()
        val key3 = ObjectId.get()

        val mapA =
            mapOf(
                key1 to Text("AttrA1"),
                key2 to Text("AttrA2"),
                key3 to Text("AttrA3"),
            )
        val mapB =
            mapOf(
                key1 to Num(100),
                key2 to Num(200),
                // key3 missing
            )
        val mapC =
            mapOf(
                key1 to Text("C1"),
                // key2 missing
                key3 to Text("C3"),
            )

        // Intersection across all 3 => only key1
        val results =
            transformUniquePermutations(mapA, mapB, mapC) { a, b, c ->
                val bVal = b.res.toInt()
                "${a.res}-$bVal-${c.res}"
            }

        assertThat(results).containsExactly("AttrA1-100-C1")
    }

    @Test
    fun `transformUniquePermutations three attributes no intersection yields empty`() {
        val mapA = mapOf(ObjectId.get() to Text("A"))
        val mapB = mapOf(ObjectId.get() to Num(1))
        val mapC = mapOf(ObjectId.get() to Text("C"))

        // None share the same key => no intersection => no results
        val results =
            transformUniquePermutations(mapA, mapB, mapC) { _, _, _ ->
                "Should not happen"
            }
        assertThat(results).isEmpty()
    }
}
