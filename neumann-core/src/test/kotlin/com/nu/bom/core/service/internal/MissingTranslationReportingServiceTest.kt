package com.nu.bom.core.service.internal

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.nu.bom.core.api.dtos.MissingTranslationDto
import com.nu.bom.core.model.internal.MissingTranslation
import com.nu.bom.core.repository.MissingTranslationRepository
import com.nu.http.EnvironmentNameSupplier
import com.tset.core.module.TranslationService
import com.tset.core.module.TranslationServiceProvider
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.core.io.Resource
import org.springframework.core.io.ResourceLoader
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

class MissingTranslationReportingServiceTest {
    @MockK
    private lateinit var environmentNameSupplier: EnvironmentNameSupplier

    @MockK
    private lateinit var missingTranslationRepository: MissingTranslationRepository

    @MockK
    private lateinit var translationServiceProvider: TranslationServiceProvider

    @MockK
    private lateinit var resourceLoader: ResourceLoader

    @MockK
    private lateinit var resource: Resource

    private lateinit var missingTranslationReportingService: MissingTranslationReportingService

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
        every { resource.inputStream } returns """[{"key": "1"}, {"pattern": "2.*"}]""".byteInputStream()
        every { environmentNameSupplier.getEnv() } returns "environment"
        every { resourceLoader.getResource(any()) } returns resource

        missingTranslationReportingService =
            MissingTranslationReportingService(
                environmentNameSupplier,
                missingTranslationRepository,
                resourceLoader,
                Jackson2ObjectMapperBuilder(),
            )
    }

    @AfterEach
    fun tearDown() = unmockkAll()

    @Test
    fun `report saves new entry if it does not already exist`() {
        every { missingTranslationRepository.findByKeyAndEnvironment(any(), any()) } returns Flux.empty()
        every { missingTranslationRepository.save(any()) } answers {
            Mono.just(it.invocation.args[0] as MissingTranslation)
        }

        missingTranslationReportingService.report(
            MissingTranslationDto(
                key = "key",
                timestamp = "timestamp",
            ),
            stackTrace = listOf(),
        ).block()

        verify(exactly = 1) { missingTranslationRepository.save(any()) }
    }

    @Test
    fun `report does not save new entry if it already exists`() {
        every { missingTranslationRepository.findByKeyAndEnvironment(any(), any()) } returns
            Flux.just(
                MissingTranslation(
                    key = "key",
                    environment = "environment",
                    url = "url",
                    component = "component",
                    parentComponent = "parentComponent",
                    stackTrace = listOf(),
                ),
            )

        missingTranslationReportingService.report(
            MissingTranslationDto(
                key = "key",
                timestamp = "timestamp",
            ),
            listOf(),
        ).block()

        verify(exactly = 0) { missingTranslationRepository.save(any()) }
    }

    @Test
    fun `report does not save new entry if it is baselined`() {
        every { missingTranslationRepository.findByKeyAndEnvironment(any(), any()) } returns Flux.empty()
        missingTranslationReportingService.report(
            MissingTranslationDto(
                key = "1",
                timestamp = "timestamp",
            ),
            listOf(),
        ).block()

        verify(exactly = 0) { missingTranslationRepository.save(any()) }
    }

    @Test
    fun `report does not save new entry if it is baselined as a pattern`() {
        every { missingTranslationRepository.findByKeyAndEnvironment(any(), any()) } returns Flux.empty()
        missingTranslationReportingService.report(
            MissingTranslationDto(
                key = "2222",
                timestamp = "timestamp",
            ),
            listOf(),
        ).block()

        verify(exactly = 0) { missingTranslationRepository.save(any()) }
    }

    @Test
    fun `refresh deletes entries that exist in the JSON source`() {
        val json =
            """
            {
                "foo": "bar",
                "section": {
                    "sub": "sub_foo"
                }
            }
            """.trimIndent()
        val objectMapper =
            ObjectMapper().registerKotlinModule().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        val jsonTree = objectMapper.readTree(json)
        val translationService = TranslationService(jsonTree, missingTranslationReportingService)

        fun mt(key: String): MissingTranslation {
            return MissingTranslation(
                key = key,
                url = "url",
                environment = "environment",
                component = "component",
                parentComponent = "parentComponent",
                stackTrace = listOf(),
            )
        }

        every { translationServiceProvider.getTranslationsService() } returns Mono.just(translationService)
        every { missingTranslationRepository.findByEnvironment(any()) } returns
            Flux.just(
                mt("foo"),
                mt("section.sub"),
                mt("bar"),
            )
        every { missingTranslationRepository.deleteByKeyAndEnvironment(any(), any()) } returns Mono.just(1)

        missingTranslationReportingService.refresh(translationServiceProvider).block()

        verify { missingTranslationRepository.deleteByKeyAndEnvironment("foo", any()) }
        verify { missingTranslationRepository.deleteByKeyAndEnvironment("section.sub", any()) }
        verify(exactly = 0) { missingTranslationRepository.deleteByKeyAndEnvironment("bar", any()) }
    }

    @Test
    fun `java like comments are supported in the baseline json`() {
        every { missingTranslationRepository.findByKeyAndEnvironment(any(), any()) } returns Flux.empty()

        every { resource.inputStream } returns
            """
            [
                // this key is special
                {"key": "special"},
                /* this pattern is dumb */
                {"pattern": "dumb.*"}
            ]
            """.trimIndent().byteInputStream()

        // trigger lazy initialization
        missingTranslationReportingService.report(
            MissingTranslationDto(
                key = "special",
                timestamp = "timestamp",
            ),
            stackTrace = listOf(),
        ).block()

        verify(exactly = 0) { missingTranslationRepository.save(any()) }
    }
}
