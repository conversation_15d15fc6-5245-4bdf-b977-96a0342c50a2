package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.commercialcalculation.enums.SubCalculator
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.SubCalculatorWithRole
import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.entities.transport.TransportCalculatorDetailedCalculation
import com.nu.bom.core.manufacturing.entities.transport.TransportRoute
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.extension.TransportCalculatorDetailedCalculationCO2Extension
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.manufacturing.fieldTypes.EmissionUnits
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYears
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYearsUnit
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.mappers.LazyMigrationTestHelper.withoutOwnObjectIds
import org.bson.types.ObjectId
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.math.BigDecimal

class OverheadEntityRemovalMigrationTest {
    @ParameterizedTest
    @MethodSource("getTestCases")
    fun testDifferentMigrations(testCase: TestCase) {
        val indicatorField = mapOf("createOverhead" to FieldResultModel(0, 0, "", null))
        val modifiedInput = testCase.input.copy(fieldWithResults = testCase.input.fieldWithResults + indicatorField)
        val modifiedExpectation = testCase.expectedOutput.copy(fieldWithResults = testCase.expectedOutput.fieldWithResults + indicatorField)
        Assertions.assertEquals(
            modifiedExpectation.withoutOwnObjectIds(),
            OverheadEntityRemovalMigration().map(modifiedInput).withoutOwnObjectIds(),
        )
    }

    data class TestCase(
        val input: ManufacturingModelEntity,
        val expectedOutput: ManufacturingModelEntity,
    )

    companion object {
        @JvmStatic
        fun getTestCases(): List<TestCase> =
            listOf(
                TestCase(
                    createRoot(),
                    createRoot(),
                ),
                TestCase(
                    createRoot().copyAll(children = listOf(createOverhead())),
                    createRoot(),
                ),
                TestCase(
                    createRoot().copyAll(
                        children =
                            listOf(
                                createOverhead(createTransport(createInitialTransportPackageFields())),
                                createTransportSubCalculator(createInitialTransportPackageFields()),
                            ),
                    ),
                    createRoot().copyAll(children = listOf(createTransportSubCalculator(createInitialTransportPackageFields()))),
                ),
                TestCase(
                    createRoot().copyAll(children = listOf(createOverhead(createTransport(createInitialTransportPackageFields())))),
                    createRoot().copyAll(children = listOf(createTransportSubCalculator(createInitialTransportPackageFields()))),
                ),
                TestCase(
                    createRoot().copyAll(children = listOf(createOverhead(createTransport()))),
                    createRoot().copyAll(children = listOf(createTransportSubCalculator(createInitialTransportPackageFields()))),
                ),
                TestCase(
                    createRoot().copyAll(
                        children = listOf(createOverhead(createTransport(createInitialTransportPackageFields(), createTransportRoute()))),
                    ),
                    createRoot().copyAll(
                        children = listOf(createTransportSubCalculator(createInitialTransportPackageFields(), createTransportRoute())),
                    ),
                ),
                TestCase(
                    createRoot().copyAll(children = listOf(createOverhead(createTransport(route = createTransportRoute())))),
                    createRoot().copyAll(
                        children = listOf(createTransportSubCalculator(createInitialTransportPackageFields(), createTransportRoute())),
                    ),
                ),
                TestCase(
                    createRoot().copyAll(children = listOf(createOverhead(createCustomDuty()))),
                    createRoot().copyAll(fieldWithResults = createCustomDutyFieldsForManufacturing()),
                ),
                TestCase(
                    createRoot().copyAll(children = listOf(createOverhead(createInterestOnFinishProductStock()))),
                    createRoot().copyAll(fieldWithResults = createInterestOnFinishProductStockFieldsForManufacturing()),
                ),
                TestCase(
                    createRoot().copyAll(children = listOf(createOverhead(createProfit()))),
                    createRoot().copyAll(fieldWithResults = createProfitFieldsForManufacturing()),
                ),
                TestCase(
                    createRoot().copyAll(
                        children = listOf(createOverhead(createCustomDuty(), createInterestOnFinishProductStock(), createProfit())),
                    ),
                    createRoot().copyAll(
                        fieldWithResults =
                            createCustomDutyFieldsForManufacturing() +
                                createInterestOnFinishProductStockFieldsForManufacturing() +
                                createProfitFieldsForManufacturing(),
                    ),
                ),
                TestCase(
                    createRoot().copyAll(
                        fieldWithResults = mapOf("field" to createAnyFieldResultModel()),
                        children = listOf(createOverhead(createCustomDuty(), createInterestOnFinishProductStock(), createProfit())),
                    ),
                    createRoot().copyAll(
                        fieldWithResults =
                            mapOf("field" to createAnyFieldResultModel()) +
                                createCustomDutyFieldsForManufacturing() +
                                createInterestOnFinishProductStockFieldsForManufacturing() +
                                createProfitFieldsForManufacturing(),
                    ),
                ),
            )

        private fun createRoot(): ManufacturingModelEntity =
            ManufacturingModelEntity(
                id = ObjectId(),
                name = "Manufacturing",
                type = "MANUFACTURING",
                clazz = ManualManufacturing::class.simpleName!!,
                args = emptyMap(),
                fieldWithResults = emptyMap(),
                initialFieldWithResults = emptyMap(),
                children = emptyList(),
            )

        private fun createOverhead(vararg children: ManufacturingModelEntity): ManufacturingModelEntity =
            ManufacturingModelEntity(
                id = ObjectId(),
                name = "Overheads",
                type = "OVERHEADS",
                clazz = "Overheads",
                args = emptyMap(),
                fieldWithResults = emptyMap(),
                initialFieldWithResults = emptyMap(),
                children = children.toList(),
            )

        private fun createAnyFieldResultModel(): FieldResultModel = FieldResultModel(0, 0, "type", true)

        // region transport

        private fun createTransport(
            packageFields: Map<String, FieldResultModel> = emptyMap(),
            route: ManufacturingModelEntity? = null,
        ): ManufacturingModelEntity =
            ManufacturingModelEntity(
                id = ObjectId(),
                name = "TransportCosts",
                type = "INCO_TERMS",
                clazz = "TransportCosts",
                args = emptyMap(),
                fieldWithResults = packageFields,
                initialFieldWithResults = emptyMap(),
                children = listOfNotNull(route),
            )

        private fun createInitialTransportPackageFields(): Map<String, FieldResultModel> =
            mapOf(
                TransportCalculatorDetailedCalculationCO2Extension::cO2PerPartFromBehaviour.name to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Emission::class.simpleName!!,
                        value = BigDecimal.ZERO,
                        source = FieldResult.SOURCE.C.name,
                        unit = EmissionUnits.KILOGRAM_CO2E.name,
                    ),
                TransportCalculatorDetailedCalculation::costPerPartFromBehaviour.name to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Money::class.simpleName!!,
                        value = BigDecimal.ZERO,
                        source = FieldResult.SOURCE.C.name,
                    ),
                TransportCalculatorDetailedCalculation::partLengthForTransport.name to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Length::class.simpleName!!,
                        value = BigDecimal.ZERO,
                        source = FieldResult.SOURCE.C.name,
                        unit = LengthUnits.METER.name,
                    ),
                TransportCalculatorDetailedCalculation::partHeightForTransport.name to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Length::class.simpleName!!,
                        value = BigDecimal.ZERO,
                        source = FieldResult.SOURCE.C.name,
                        unit = LengthUnits.METER.name,
                    ),
                TransportCalculatorDetailedCalculation::partWidthForTransport.name to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Length::class.simpleName!!,
                        value = BigDecimal.ZERO,
                        source = FieldResult.SOURCE.C.name,
                        unit = LengthUnits.METER.name,
                    ),
                TransportCalculatorDetailedCalculation::netWeightPerPartForTransport.name to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Weight::class.simpleName!!,
                        value = BigDecimal.ZERO,
                        source = FieldResult.SOURCE.C.name,
                        unit = WeightUnits.KILOGRAM.name,
                    ),
                TransportCalculatorDetailedCalculation::amountPerPackage.name to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = QuantityUnit::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                    ),
                TransportCalculatorDetailedCalculation::useTransportCalculator.name to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Bool::class.simpleName!!,
                        value = false,
                        source = FieldResult.SOURCE.C.name,
                    ),
            )

        private fun createTransportRoute(): ManufacturingModelEntity =
            ManufacturingModelEntity(
                id = ObjectId(),
                name = TransportRoute::class.simpleName!!,
                type = Entities.TRANSPORT_ROUTE.name,
                clazz = TransportRoute::class.simpleName!!,
                args = emptyMap(),
                fieldWithResults = createTransportRouteFields(),
            )

        private fun createTransportRouteFields(): Map<String, FieldResultModel> {
            return mapOf(
                TransportRoute::origin.name to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Text::class.simpleName!!,
                        value = "Origin",
                        source = FieldResult.SOURCE.C.name,
                    ),
                TransportRoute::destination.name to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Text::class.simpleName!!,
                        value = "Destination",
                        source = FieldResult.SOURCE.C.name,
                    ),
                TransportRoute::transportDistance.name to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Length::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                        unit = LengthUnits.METER.name,
                    ),
                TransportRoute::costPerKilometer.name to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Money::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                    ),
            )
        }

        private fun createTransportSubCalculator(
            packageFields: Map<String, FieldResultModel> = emptyMap(),
            route: ManufacturingModelEntity? = null,
        ): ManufacturingModelEntity =
            ManufacturingModelEntity(
                id = ObjectId(),
                name = SubCalculatorWithRole(SubCalculator.TRANSPORT_CALCULATOR).getEntityName(),
                type = Entities.OVERHEAD_SUB_CALCULATOR.name,
                clazz = TransportCalculatorDetailedCalculation::class.simpleName!!,
                args = emptyMap(),
                children = listOfNotNull(route),
                fieldWithResults = packageFields,
            )

        // endregion

        // region normal overhead entities

        // region simple overhead

        private fun createCustomDuty(): ManufacturingModelEntity =
            ManufacturingModelEntity(
                id = ObjectId(),
                name = "CustomDuty",
                type = "INCO_TERMS",
                clazz = "Incoterms",
                args = emptyMap(),
                fieldWithResults = createCustomDutyFieldsForOverheadEntity(),
            )

        private fun createCustomDutyFieldsForOverheadEntity(): Map<String, FieldResultModel> =
            mapOf(
                "overheadRate" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Rate::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                    ),
                "costPerPart" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Money::class.simpleName!!,
                        value = BigDecimal.ZERO,
                        source = FieldResult.SOURCE.I.name,
                        systemValue = BigDecimal.ONE,
                    ),
                "cO2OverheadRate" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Rate::class.simpleName!!,
                        value = BigDecimal.ZERO,
                        source = FieldResult.SOURCE.I.name,
                        systemValue = BigDecimal.ONE,
                    ),
                "cO2PerPart" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Emission::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                        unit = EmissionUnits.KILOGRAM_CO2E.name,
                    ),
            )

        private fun createCustomDutyFieldsForManufacturing(): Map<String, FieldResultModel> =
            mapOf(
                "#Cost_CustomsDuty_Rate" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Rate::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                    ),
                "#Cost_SoldMaterial_Total_CustomsDuty" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Money::class.simpleName!!,
                        value = BigDecimal.ZERO,
                        source = FieldResult.SOURCE.I.name,
                        systemValue = BigDecimal.ONE,
                    ),
                "#CO2_CustomsDutyCO2e_Rate" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Rate::class.simpleName!!,
                        value = BigDecimal.ZERO,
                        source = FieldResult.SOURCE.I.name,
                        systemValue = BigDecimal.ONE,
                    ),
                "#CO2_SoldMaterial_Total_CustomsDutyCO2e" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Emission::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                        unit = EmissionUnits.KILOGRAM_CO2E.name,
                    ),
            )

        // endregion

        // region differentiated overhead

        private fun createProfit(): ManufacturingModelEntity =
            ManufacturingModelEntity(
                id = ObjectId(),
                name = "Profit",
                type = "PROFIT",
                clazz = "Profit",
                args = emptyMap(),
                fieldWithResults = createProfitFieldsForOverheadEntity(),
            )

        private fun createProfitFieldsForOverheadEntity(): Map<String, FieldResultModel> =
            mapOf(
                "rawMaterialRate" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Rate::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                    ),
                "bomRate" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Rate::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.I.name,
                        systemValue = BigDecimal.ZERO,
                    ),
                "manufacturingRate" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Rate::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                    ),
                "rawMaterialOverheadCosts" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Money::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                    ),
                "bomOverheadCosts" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Money::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                    ),
                "manufacturingOverheadCosts" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Money::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                    ),
                "costPerPart" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Money::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                    ),
                "cO2RawMaterialRate" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Rate::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                    ),
                "cO2BomRate" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Rate::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                    ),
                "cO2ManufacturingRate" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Rate::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                    ),
                "cO2RawMaterialOverheadCosts" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Emission::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                        unit = EmissionUnits.KILOGRAM_CO2E.name,
                    ),
                "cO2BomOverheadCosts" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Emission::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.I.name,
                        unit = EmissionUnits.KILOGRAM_CO2E.name,
                    ),
                "cO2ManufacturingOverheadCosts" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Emission::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                        unit = EmissionUnits.KILOGRAM_CO2E.name,
                    ),
                "cO2PerPart" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Emission::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                        unit = EmissionUnits.KILOGRAM_CO2E.name,
                    ),
            )

        private fun createProfitFieldsForManufacturing(): Map<String, FieldResultModel> =
            mapOf(
                "#Cost_Profit-Rm_Rate" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Rate::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                    ),
                "#Cost_Profit-Pp_Rate" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Rate::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.I.name,
                        systemValue = BigDecimal.ZERO,
                    ),
                "#Cost_Profit-Mfg_Rate" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Rate::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                    ),
                "#Cost_SoldMaterial_Total_Profit-Rm" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Money::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                    ),
                "#Cost_SoldMaterial_Total_Profit-Pp" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Money::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                    ),
                "#Cost_SoldMaterial_Total_Profit-Mfg" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Money::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                    ),
                "#Cost_SoldMaterial_Total_Profit" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Money::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                    ),
                "#CO2_ProfitCO2e-Rm_Rate" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Rate::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                    ),
                "#CO2_ProfitCO2e-Pp_Rate" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Rate::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                    ),
                "#CO2_ProfitCO2e-Mfg_Rate" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Rate::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                    ),
                "#CO2_SoldMaterial_Total_ProfitCO2e-Rm" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Emission::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                        unit = EmissionUnits.KILOGRAM_CO2E.name,
                    ),
                "#CO2_SoldMaterial_Total_ProfitCO2e-Pp" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Emission::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.I.name,
                        unit = EmissionUnits.KILOGRAM_CO2E.name,
                    ),
                "#CO2_SoldMaterial_Total_ProfitCO2e-Mfg" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Emission::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                        unit = EmissionUnits.KILOGRAM_CO2E.name,
                    ),
                "#CO2_SoldMaterial_Total_ProfitCO2e" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Emission::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                        unit = EmissionUnits.KILOGRAM_CO2E.name,
                    ),
            )

        // endregion

        // region interest

        private fun createInterestOnFinishProductStock(): ManufacturingModelEntity =
            ManufacturingModelEntity(
                id = ObjectId(),
                name = "InterestOnFinishProductStock",
                type = "OVERHEADS_AFTER_PC",
                clazz = "InterestOnFinishProductStock",
                args = emptyMap(),
                fieldWithResults = createInterestOnFinishProductStockFieldsForOverheadEntity(),
            )

        private fun createInterestOnFinishProductStockFieldsForOverheadEntity(): Map<String, FieldResultModel> =
            mapOf(
                "interestPeriod" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = TimeInYears::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                        unit = TimeInYearsUnit.YEAR.name,
                    ),
                "interestRate" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Rate::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                    ),
                "costPerPart" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Money::class.simpleName!!,
                        value = BigDecimal.ZERO,
                        source = FieldResult.SOURCE.I.name,
                        systemValue = BigDecimal.ONE,
                    ),
            )

        private fun createInterestOnFinishProductStockFieldsForManufacturing(): Map<String, FieldResultModel> =
            mapOf(
                "#Cost_InterestOnFinishProductStock_Time" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = TimeInYears::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                        unit = TimeInYearsUnit.YEAR.name,
                    ),
                "#Cost_InterestOnFinishProductStock_InterestRate" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Rate::class.simpleName!!,
                        value = BigDecimal.ONE,
                        source = FieldResult.SOURCE.C.name,
                    ),
                "#Cost_SoldMaterial_Total_InterestOnFinishProductStock" to
                    FieldResultModel(
                        version = 0,
                        newVersion = 0,
                        type = Money::class.simpleName!!,
                        value = BigDecimal.ZERO,
                        source = FieldResult.SOURCE.I.name,
                        systemValue = BigDecimal.ONE,
                    ),
            )

        // endregion

        // endregion
    }
}
