package com.nu.bom.core.manufacturing.exchangeRates

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.entities.BaseEntityFields
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.ExchangeRatesField
import com.nu.bom.core.manufacturing.fieldTypes.PriceComponents
import com.nu.bom.core.manufacturing.testentities.TestEntity

@TestEntity
@EntityType(Entities.MANUFACTURING)
class TestEntityWithOnePriceComponentField(name: String) : ManufacturingEntity(name) {
    override val extends = BaseEntityFields(name)

    @Input
    fun exchangeRates(): ExchangeRatesField = ExchangeRatesField.empty()

    fun myPriceComponents(): PriceComponents {
        return PriceComponents.empty()
    }
}
