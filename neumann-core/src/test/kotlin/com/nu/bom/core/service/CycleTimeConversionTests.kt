package com.nu.bom.core.service

import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeUnit
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.math.RoundingMode

class CycleTimeConversionTests {
    @Test
    fun testToCycleTime_validTimeUnitConversion() {
        val cycleTime = CycleTime(BigDecimal.TEN, CycleTimeUnit.SECOND)
        val otherUnit = CycleTimeUnit.MINUTE
        val expected = BigDecimal.valueOf(0.1666666666666667)
        assertEquals(expected, cycleTime.toCycleTime(otherUnit))
    }

    @Test
    fun testToCycleTime_validStrokeUnitConversion() {
        val cycleTime = CycleTime(BigDecimal.TEN, CycleTimeUnit.STROKES_PER_MINUTES)
        val otherUnit = CycleTimeUnit.STROKES_PER_SECONDS
        val expected = BigDecimal.valueOf(0.16666)
        assertEquals(expected, cycleTime.toCycleTime(otherUnit).setScale(5, RoundingMode.DOWN))
    }

    @Test
    fun testToCycleTime_validDifferentUnitConversion() {
        val cycleTime = CycleTime(BigDecimal.TEN, CycleTimeUnit.STROKES_PER_SECONDS)
        val otherUnit = CycleTimeUnit.STROKES_PER_MINUTES
        val expected = BigDecimal.valueOf(600)
        assertEquals(expected, cycleTime.toCycleTime(otherUnit))
    }

    @Test
    fun `convertDifferentUnits should throw error for different types`() {
        val unit = CycleTime(BigDecimal.ZERO, CycleTimeUnit.STROKES_PER_HOURS)
        val otherUnit = CycleTimeUnit.STROKES_PER_MINUTES
        Assertions.assertThrows(com.nu.bom.core.exception.ErrorCodedException::class.java) {
            unit.toCycleTime(otherUnit)
        }
    }
}
