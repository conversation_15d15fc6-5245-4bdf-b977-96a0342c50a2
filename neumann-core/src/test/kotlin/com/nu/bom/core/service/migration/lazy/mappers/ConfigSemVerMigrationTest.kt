package com.nu.bom.core.service.migration.lazy.mappers

import com.fasterxml.jackson.databind.exc.MismatchedInputException
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.values.UiConfigIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ConfigIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.CustomFieldsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.DynamicBehaviourGeneration
import com.nu.bom.core.model.configurations.ConfigId
import com.nu.bom.core.model.configurations.SemanticVersion
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class ConfigSemVerMigrationTest {
    companion object {
        private const val KEY = "key"

        @JvmStatic
        fun testCases() =
            listOf(
                Pair(true, 0),
                Pair(true, 1),
                Pair(true, 54),
                Pair(false, 0),
                Pair(false, 1),
                Pair(false, 54),
            )
    }

    @ParameterizedTest
    @MethodSource("testCases")
    fun `tset fields are migrated to fixed version`(case: Pair<Boolean, Int>) {
        val (isTset, instanceVersion) = case

        assertPartial(CustomFieldsConfigurationKey(partialIdStr(isTset, instanceVersion)).res, isTset, instanceVersion)
        assertPartial(ConfigIdentifier(jsonDataStr(isTset, instanceVersion)).res.data, isTset, instanceVersion)
        assertFull(DynamicBehaviourGeneration(fullIdStr(isTset, instanceVersion)).res.configurationIdentifier, isTset, instanceVersion)
    }

    @Test
    fun `behaviour without config works`() {
        val base = """{ "hostType": "", "behaviourType": "", "behaviourImplementationClassName": """""
        val withNull = DynamicBehaviourGeneration("""$base, "configurationIdentifier": null }""")
        val withoutProp = DynamicBehaviourGeneration("$base}")
        val invalidType = """$base, "configurationIdentifier": 12 }"""

        val expected = DynamicBehaviourGeneration(DynamicBehaviourGeneration.DynamicBehaviourTypeMapping("", "", "", null))

        assertThat(withNull).isEqualTo(expected)
        assertThat(withoutProp).isEqualTo(expected)

        assertThatThrownBy {
            DynamicBehaviourGeneration(invalidType)
        }.isInstanceOf(MismatchedInputException::class.java)
    }

    private fun assertFull(
        id: ConfigId?,
        isTset: Boolean,
        instanceVersion: Int,
    ) {
        assertThat(id).isNotNull()
        assertThat(id!!.group).isEqualTo(KEY)
        assertThat(id.type).isEqualTo(KEY)
        assertPartial(ConfigurationIdentifier(id), isTset, instanceVersion)
    }

    private fun assertPartial(
        id: UiConfigIdentifier,
        isTset: Boolean,
        instanceVersion: Int,
    ) {
        if (id !is ConfigurationIdentifier) return
        val expected =
            if (isTset) {
                ConfigurationIdentifier.tset(KEY, SemanticVersion.initialVersion())
            } else {
                ConfigurationIdentifier(KEY, SemanticVersion(instanceVersion + 1, 0))
            }

        assertThat(id).isEqualTo(expected)
    }

    private fun partialIdStr(
        isTset: Boolean,
        version: Int,
    ): String = """{ "key": "$KEY", "isTsetConfiguration": $isTset, "instanceVersion": $version }"""

    private fun jsonDataStr(
        isTset: Boolean,
        version: Int,
    ): String {
        val id = partialIdStr(isTset, version)
        return """{ "@class":"com.nu.bom.core.manufacturing.fieldTypes.Json${'$'}Jsondata", "data": $id }"""
    }

    private fun fullIdStr(
        isTset: Boolean,
        version: Int,
    ): String {
        val id =
            """{ "key": "$KEY", "isTestConfiguration": $isTset, "version": $version, "groupKey": "$KEY", "typeKey": "$KEY" }"""
        return """{ "hostType": "", "behaviourType": "", "behaviourImplementationClassName": "", "configurationIdentifier": $id }"""
    }
}
