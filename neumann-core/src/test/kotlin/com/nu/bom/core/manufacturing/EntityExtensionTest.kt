package com.nu.bom.core.manufacturing

import com.nu.bom.core.CalculationTestBase
import com.nu.bom.core.manufacturing.MetaCache.Companion.changelog
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.testentities.TestMachine
import com.nu.bom.core.manufacturing.testentities.TestManufacturing
import com.nu.bom.core.manufacturing.testentities.TestSpecialManufacturingStep
import com.nu.bom.core.manufacturing.testentities.TestSpecialManufacturingStepExtension
import com.nu.bom.core.manufacturing.testentities.TestSpecialSubManufacturingStep
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test

class EntityExtensionTest : CalculationTestBase() {
    @AfterEach
    fun cleanup() {
        extensionManager.removeExtensions(listOf(TestSpecialManufacturingStep::class.java))
    }

    @Test
    fun overrideInputWithCalculationAndAddNewInput() {
        extensionLoader.loadExtension(
            TestSpecialManufacturingStepExtension::class.java,
            "TEST",
            listOf(TestSpecialManufacturingStep::class.java),
            changelog,
        )

        extensionManager.enablePackage("TEST")

        val manufacturing = addObject("Test Manufacturing", TestManufacturing::class.java)
        val step =
            addObject(
                name = "Test Step 1",
                clazz = TestSpecialManufacturingStep::class.java,
                parent = manufacturing,
                parameters =
                    mapOf(
                        "doubleTheWeight" to Rate(1.4.toBigDecimal()),
                    ),
            )
        /**machine*/
        addObject(
            name = "Test Machine 1",
            clazz = TestMachine::class.java,
            parent = step,
            parameters =
                mapOf(
                    "baseRate" to Rate(0.1.toBigDecimal()),
                    "baseValue" to Money(10000.toBigDecimal()),
                ),
        )

        calculate(manufacturing)
        printResults()

        validateResultField("Test Machine 1", "machineValue", 1000.00)
        validateResultField("Test Machine 1", "baseValue", 10000.00)
        validateResultField("Test Machine 1", "weightedMachineValue", 700.00)
        validateResultField("Test Machine 1", "baseRate", 0.10)
        validateResultField("Test Step 1", "stepWeight", 0.70)
        validateResultField("Test Step 1", "totalMachineValues", 700.00)
        validateResultField("Test Step 1", "testFunction", 23.00)
        validateResultField("Test Step 1", "testFunctionWithDependency", 7.00)
        validateResultField("Test Manufacturing", "totalValues", 700.00)
    }

    @Test
    fun extendExtendedEntity() {
        extensionLoader.loadExtension(
            TestSpecialManufacturingStepExtension::class.java,
            "TEST",
            listOf(TestSpecialManufacturingStep::class.java),
            changelog,
        )

        extensionManager.enablePackage("TEST")

        val manufacturing = addObject("Test Manufacturing", TestManufacturing::class.java)
        val step =
            addObject(
                name = "Test Step 1",
                clazz = TestSpecialSubManufacturingStep::class.java,
                parent = manufacturing,
                parameters =
                    mapOf(
                        "doubleTheWeight" to Rate(1.4.toBigDecimal()),
                    ),
            )
        /**machine*/
        addObject(
            name = "Test Machine 1",
            clazz = TestMachine::class.java,
            parent = step,
            parameters =
                mapOf(
                    "baseRate" to Rate(0.1.toBigDecimal()),
                    "baseValue" to Money(10000.toBigDecimal()),
                ),
        )

        calculate(manufacturing)
        printResults()

        //  validateMissingInputs(0)
        validateResultField("Test Machine 1", "machineValue", 1000.00)
        validateResultField("Test Machine 1", "baseValue", 10000.00)
        validateResultField("Test Machine 1", "weightedMachineValue", 700.00)
        validateResultField("Test Machine 1", "baseRate", 0.10)
        validateResultField("Test Step 1", "stepWeight", 0.70)
        validateResultField("Test Step 1", "doubleTheWeight2", 14.00)
        validateResultField("Test Step 1", "testFunction", 23.00)
        validateResultField("Test Step 1", "testFunctionWithDependency", 7.00)
        validateResultField("Test Step 1", "totalMachineValues", 700.00)
        validateResultField("Test Manufacturing", "totalValues", 700.00)
    }
}
