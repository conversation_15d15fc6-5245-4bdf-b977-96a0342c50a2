package com.nu.bom.core.manufacturing.testentities.modularization

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.ExpectedParents
import com.nu.bom.core.manufacturing.annotations.ExternalDependency
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Modularized
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.Num

@EntityType(Entities.MANUFACTURING_STEP)
@Modularized(
    technologies = [Model.INJ2],
    parents = [
        ExpectedParents(model = Model.INJ2, Entities.MANUFACTURING),
    ],
    dimensions = [Dimension.Selection.MASS],
    userCreatable = true,
)
class StepWithOtherDimension(name: String) : ManufacturingEntity(name) {
    @Input
    @Parent(Entities.MANUFACTURING)
    @ExternalDependency
    fun secretValue(): Num? = null
}
