package com.nu.bom.core.service.threeDb

import com.nu.bom.core.manufacturing.fieldTypes.ThreeDbVersionedPart
import com.nu.bom.core.threedb.ThreeDbCallType
import com.nu.bom.core.threedb.ThreeDbFieldType
import com.nu.bom.core.threedb.ThreeDbHeader
import com.nu.bom.core.user.AccessCheck
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class ThreeDbServiceTest : ThreeDbCalculationTestBaseWithTest() {
    private val mockAccessCheck = AccessCheck("a", "b", "v", "t", "r")

    @Test
    fun downloadEverything() {
        val download =
            threeDbService
                .downloadEverything(
                    ThreeDbHeader(mockAccessCheck, ThreeDbVersionedPart()),
                ).block()
                ?.mapValues { (_, v) -> v.toString(Charsets.UTF_8) }

        val expected = mapOf("r1" to "r1", "r2" to "r2", "r3" to "r3")

        assert(download == expected)
    }

    @Test
    fun uploadEverything() {
        val upload =
            mapOf(
                ThreeDbFieldType.Resource.WIDTH.name to "a".toByteArray(Charsets.UTF_8),
                ThreeDbFieldType.Resource.HEIGHT.name to "bb".toByteArray(Charsets.UTF_8),
                ThreeDbFieldType.Resource.VOLUME.name to "ccc".toByteArray(Charsets.UTF_8),
            )

        threeDbService
            .uploadEverything(upload, mockAccessCheck)
            .block()

        assertEquals(1, threeDbService.binaryFunctionCalls[ThreeDbFieldType.Resource.WIDTH.name to ThreeDbCallType.Set])
        assertEquals(1, threeDbService.binaryFunctionCalls[ThreeDbFieldType.Resource.HEIGHT.name to ThreeDbCallType.Set])
        assertEquals(1, threeDbService.binaryFunctionCalls[ThreeDbFieldType.Resource.VOLUME.name to ThreeDbCallType.Set])
        assertEquals(null, threeDbService.binaryFunctionCalls[ThreeDbFieldType.Resource.LENGTH.name to ThreeDbCallType.Set])
    }
}
