package com.nu.bom.core.service.nexar

import com.github.tomakehurst.wiremock.client.WireMock.okJson
import com.github.tomakehurst.wiremock.client.WireMock.post
import com.github.tomakehurst.wiremock.client.WireMock.serverError
import com.github.tomakehurst.wiremock.client.WireMock.stubFor
import com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo
import com.nu.bom.core.exception.readable.ExternalServiceException
import com.tset.bom.clients.FaultToleranceService
import io.github.resilience4j.kotlin.retry.RetryRegistry
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.core.io.ClassPathResource

class NexarQueryServiceTest : NexarApiTestBase() {
    private lateinit var apiClient: NexarApiClient
    private lateinit var queryService: NexarQueryService
    private val retryRegistry = RetryRegistry { }

    @BeforeEach
    fun setUp() {
        apiClient = NexarApiClient(
            apiProperties = apiProperties,
            client = webClientBuilder.baseUrl(apiProperties.apiUrl).build(),
            faultToleranceService = FaultToleranceService(retryRegistry)
        )
        queryService = NexarQueryServiceImpl(
            apiClient,
            properties = NexarServiceProperties()
        )
    }

    @Test
    fun queryPartByIdShouldReturnPartResult() {
        val jsonResponse = ClassPathResource("nexar/query_partid_response.json")

        stubFor(
            post(urlPathEqualTo("/connect/token"))
                .willReturn(
                    okJson(objectMapper.writeValueAsString(validTokenResponse()))
                )
        )
        stubFor(
            post(urlPathEqualTo("/graphql"))
                .willReturn(
                    okJson(jsonResponse.file.readText())
                )
        )

        val partByIdResult = queryService.getPartById("0815").block()
        assertThat(partByIdResult).hasFieldOrPropertyWithValue("id", "665029")
        assertThat(partByIdResult).hasFieldOrPropertyWithValue("mpn", "TPS65150PWPR")
    }

    @Test
    fun translateNexarServiceQueryErrorIntoReadableException() {
        val tokenResponse = validTokenResponse()

        stubFor(
            post(urlPathEqualTo("/connect/token"))
                .willReturn(
                    okJson(objectMapper.writeValueAsString(tokenResponse))
                )
        )
        stubFor(
            post(urlPathEqualTo("/graphql"))
                .willReturn(
                    serverError()
                )
        )

        assertThrows<ExternalServiceException> { queryService.getPartById("0815").block() }
    }
}
