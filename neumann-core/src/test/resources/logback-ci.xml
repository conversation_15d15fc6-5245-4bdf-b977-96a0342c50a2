<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- encoders are assigned the type
             ch.qos.logback.classic.encoder.PatternLayoutEncoder by default -->
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    <logger name="com.nu.bom.core.manufacturing.entities" level="WARN"/>
    <logger name="com.nu.bom.core.CalculationTestBase" level="WARN"/>
    <logger name="com.nu.bom.core.manufacturing.service.TopologicalCalculator" level="INFO"/>
    <logger name="com.nu.bom.core.manufacturing.service.BaseFieldCalculation" level="INFO"/>
    <logger name="com.tngtech.archunit" level="INFO"/>
    <logger name="org.eclipse.jgit" level="INFO"/>

    <root level="INFO">
        <appender-ref ref="STDOUT" />
    </root>

</configuration>
