[{"missingInputs": [{"name": "designation", "entityId": "64e7ce7b32c47367d6bbed0d", "entityType": "INCO_TERMS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Text", "version": 1, "newVersion": 1, "entityRef": "TransportCosts", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbed0d", "entityType": "INCO_TERMS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "TransportCosts", "context": {"deactivatedBy": null}}, {"name": "designation", "entityId": "64e7ce7b32c47367d6bbed0f", "entityType": "INCO_TERMS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Text", "version": 1, "newVersion": 1, "entityRef": "CustomsDuty", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbed0f", "entityType": "INCO_TERMS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "CustomsDuty", "context": {"deactivatedBy": null}}, {"name": "designation", "entityId": "64e7ce7b32c47367d6bbed11", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Text", "version": 1, "newVersion": 1, "entityRef": "SalesAndGeneralAdministrationCosts", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbed11", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "SalesAndGeneralAdministrationCosts", "context": {"deactivatedBy": null}}, {"name": "designation", "entityId": "64e7ce7b32c47367d6bbed13", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Text", "version": 1, "newVersion": 1, "entityRef": "ResearchAndDevelopmentCosts", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbed13", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "ResearchAndDevelopmentCosts", "context": {"deactivatedBy": null}}, {"name": "designation", "entityId": "64e7ce7b32c47367d6bbed15", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Text", "version": 1, "newVersion": 1, "entityRef": "BusinessRiskCosts", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbed15", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "BusinessRiskCosts", "context": {"deactivatedBy": null}}, {"name": "designation", "entityId": "64e7ce7b32c47367d6bbed17", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Text", "version": 1, "newVersion": 1, "entityRef": "InterestOnFinishProductStock", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbed17", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "InterestOnFinishProductStock", "context": {"deactivatedBy": null}}, {"name": "designation", "entityId": "64e7ce7b32c47367d6bbed19", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Text", "version": 1, "newVersion": 1, "entityRef": "OtherExpendituresAfterPC", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbed19", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "OtherExpendituresAfterPC", "context": {"deactivatedBy": null}}, {"name": "designation", "entityId": "64e7ce7b32c47367d6bbed1b", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Text", "version": 1, "newVersion": 1, "entityRef": "DirectOverheadsAfterPC", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbed1b", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "DirectOverheadsAfterPC", "context": {"deactivatedBy": null}}, {"name": "designation", "entityId": "64e7ce7b32c47367d6bbed1d", "entityType": "PROFIT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Text", "version": 1, "newVersion": 1, "entityRef": "Profit", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbed1d", "entityType": "PROFIT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "Profit", "context": {"deactivatedBy": null}}, {"name": "designation", "entityId": "64e7ce7b32c47367d6bbed1f", "entityType": "TERMS_OF_PAYMENT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Text", "version": 1, "newVersion": 1, "entityRef": "InterestForTermsOfPayment", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbed1f", "entityType": "TERMS_OF_PAYMENT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "InterestForTermsOfPayment", "context": {"deactivatedBy": null}}, {"name": "designation", "entityId": "64e7ce7b32c47367d6bbed21", "entityType": "TERMS_OF_PAYMENT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Text", "version": 1, "newVersion": 1, "entityRef": "Discount", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbed21", "entityType": "TERMS_OF_PAYMENT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "Discount", "context": {"deactivatedBy": null}}, {"name": "designation", "entityId": "64e7ce7b32c47367d6bbed07", "entityType": "OVERHEADS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Text", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing.Overheads", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbed07", "entityType": "OVERHEADS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing.Overheads", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbed09", "entityType": "PART", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing.Part", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbecdd", "entityType": "EXCHANGE_RATE", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "exchangeRate.CHF", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbecdf", "entityType": "EXCHANGE_RATE", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "exchangeRate.CNY", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbece1", "entityType": "EXCHANGE_RATE", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "exchangeRate.EUR", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbece3", "entityType": "EXCHANGE_RATE", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "exchangeRate.GBP", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbece5", "entityType": "EXCHANGE_RATE", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "exchangeRate.JPY", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbece7", "entityType": "EXCHANGE_RATE", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "exchangeRate.USD", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbece9", "entityType": "EXCHANGE_RATE", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "exchangeRate.AUD", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbeceb", "entityType": "EXCHANGE_RATE", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "exchangeRate.CAD", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbeced", "entityType": "EXCHANGE_RATE", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "exchangeRate.SEK", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbecef", "entityType": "EXCHANGE_RATE", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "exchangeRate.KRW", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbecf1", "entityType": "EXCHANGE_RATE", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "exchangeRate.MXN", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbecf3", "entityType": "EXCHANGE_RATE", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "exchangeRate.INR", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbecf5", "entityType": "EXCHANGE_RATE", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "exchangeRate.RUB", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbecf7", "entityType": "EXCHANGE_RATE", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "exchangeRate.TRY", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbecf9", "entityType": "EXCHANGE_RATE", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "exchangeRate.BRL", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbecfb", "entityType": "EXCHANGE_RATE", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "exchangeRate.TWD", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbecfd", "entityType": "EXCHANGE_RATE", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "exchangeRate.DKK", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbecff", "entityType": "EXCHANGE_RATE", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "exchangeRate.PLN", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbed01", "entityType": "EXCHANGE_RATE", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "exchangeRate.IDR", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbed03", "entityType": "EXCHANGE_RATE", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "exchangeRate.HUF", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "64e7ce7b32c47367d6bbed05", "entityType": "EXCHANGE_RATE", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "exchangeRate.CZK", "context": {"deactivatedBy": null}}, {"name": "parentExchangeRatesFromManufacturing", "entityId": "************************", "entityType": "EXCHANGE_RATES", "type": "com.nu.bom.core.manufacturing.fieldTypes.ExchangeRatesField", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing.ExchangeRates", "context": {"deactivatedBy": null}}, {"name": "designation", "entityId": "************************", "entityType": "EXCHANGE_RATES", "type": "com.nu.bom.core.manufacturing.fieldTypes.Text", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing.ExchangeRates", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "************************", "entityType": "EXCHANGE_RATES", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing.ExchangeRates", "context": {"deactivatedBy": null}}, {"name": "shapeId", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Text", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "materialBaseCurrency", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Currency", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "materialPriceOverride", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "netWeightPerPart", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "netWeightPerPartForOverview", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Weight", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "netWeightPerPartForWizard", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Weight", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "designation", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Text", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "sortIndex", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Quantity", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "location", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Text", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "partHeight", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Length", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "partInnerDiameter", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Length", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "partLength", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Length", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "partOuterDiameter", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Length", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "partWidth", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Length", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "volume", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Volume", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "additionalCurrentCosts", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "additionalTargetCosts", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "totalCurrentCosts", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "totalTargetCosts", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "cO2AdditionalCurrentCosts", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "cO2AdditionalTargetCosts", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "cO2TotalCurrentCosts", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "cO2TotalTargetCosts", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "averageUsableProductionVolumePerYear", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "peakUsableProductionVolumePerYear", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}], "openCalculations": [{"name": "checkDimension", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Null", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "material<PERSON>abel", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Text", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "materialName", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Text", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "parentCurrenciesFromManufacturing", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.CurrenciesField", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "parentExchangeRatesFromManufacturing", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.ExchangeRatesField", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "scrapRateForMaterialTable", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "costPerPartFromBehaviour", "entityId": "64e7ce7b32c47367d6bbed0d", "entityType": "INCO_TERMS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "TransportCosts", "context": {"deactivatedBy": null}}, {"name": "overheadBase", "entityId": "64e7ce7b32c47367d6bbed0d", "entityType": "INCO_TERMS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "TransportCosts", "context": {"deactivatedBy": null}}, {"name": "overheadRate", "entityId": "64e7ce7b32c47367d6bbed0d", "entityType": "INCO_TERMS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "TransportCosts", "context": {"deactivatedBy": null}}, {"name": "cO2OverheadBase", "entityId": "64e7ce7b32c47367d6bbed0d", "entityType": "INCO_TERMS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "TransportCosts", "context": {"deactivatedBy": null}}, {"name": "cO2OverheadRate", "entityId": "64e7ce7b32c47367d6bbed0d", "entityType": "INCO_TERMS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "TransportCosts", "context": {"deactivatedBy": null}}, {"name": "cO2PerPartFromBehaviour", "entityId": "64e7ce7b32c47367d6bbed0d", "entityType": "INCO_TERMS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "TransportCosts", "context": {"deactivatedBy": null}}, {"name": "costPerPart", "entityId": "64e7ce7b32c47367d6bbed0d", "entityType": "INCO_TERMS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "TransportCosts", "context": {"deactivatedBy": null}}, {"name": "cO2PerPart", "entityId": "64e7ce7b32c47367d6bbed0d", "entityType": "INCO_TERMS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "TransportCosts", "context": {"deactivatedBy": null}}, {"name": "costPerPart", "entityId": "64e7ce7b32c47367d6bbed0f", "entityType": "INCO_TERMS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "CustomsDuty", "context": {"deactivatedBy": null}}, {"name": "overheadBase", "entityId": "64e7ce7b32c47367d6bbed0f", "entityType": "INCO_TERMS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "CustomsDuty", "context": {"deactivatedBy": null}}, {"name": "overheadRate", "entityId": "64e7ce7b32c47367d6bbed0f", "entityType": "INCO_TERMS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "CustomsDuty", "context": {"deactivatedBy": null}}, {"name": "cO2OverheadBase", "entityId": "64e7ce7b32c47367d6bbed0f", "entityType": "INCO_TERMS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "CustomsDuty", "context": {"deactivatedBy": null}}, {"name": "cO2OverheadRate", "entityId": "64e7ce7b32c47367d6bbed0f", "entityType": "INCO_TERMS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "CustomsDuty", "context": {"deactivatedBy": null}}, {"name": "cO2PerPart", "entityId": "64e7ce7b32c47367d6bbed0f", "entityType": "INCO_TERMS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "CustomsDuty", "context": {"deactivatedBy": null}}, {"name": "bomOverheadBase", "entityId": "64e7ce7b32c47367d6bbed11", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "SalesAndGeneralAdministrationCosts", "context": {"deactivatedBy": null}}, {"name": "bomOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed11", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "SalesAndGeneralAdministrationCosts", "context": {"deactivatedBy": null}}, {"name": "bomRate", "entityId": "64e7ce7b32c47367d6bbed11", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "SalesAndGeneralAdministrationCosts", "context": {"deactivatedBy": null}}, {"name": "costPerPart", "entityId": "64e7ce7b32c47367d6bbed11", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "SalesAndGeneralAdministrationCosts", "context": {"deactivatedBy": null}}, {"name": "manufacturingOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed11", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "SalesAndGeneralAdministrationCosts", "context": {"deactivatedBy": null}}, {"name": "manufacturingRate", "entityId": "64e7ce7b32c47367d6bbed11", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "SalesAndGeneralAdministrationCosts", "context": {"deactivatedBy": null}}, {"name": "rawMaterialOverheadBase", "entityId": "64e7ce7b32c47367d6bbed11", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "SalesAndGeneralAdministrationCosts", "context": {"deactivatedBy": null}}, {"name": "rawMaterialOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed11", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "SalesAndGeneralAdministrationCosts", "context": {"deactivatedBy": null}}, {"name": "rawMaterialRate", "entityId": "64e7ce7b32c47367d6bbed11", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "SalesAndGeneralAdministrationCosts", "context": {"deactivatedBy": null}}, {"name": "cO2BomOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed11", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "SalesAndGeneralAdministrationCosts", "context": {"deactivatedBy": null}}, {"name": "cO2BomRate", "entityId": "64e7ce7b32c47367d6bbed11", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "SalesAndGeneralAdministrationCosts", "context": {"deactivatedBy": null}}, {"name": "cO2ManufacturingOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed11", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "SalesAndGeneralAdministrationCosts", "context": {"deactivatedBy": null}}, {"name": "cO2ManufacturingRate", "entityId": "64e7ce7b32c47367d6bbed11", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "SalesAndGeneralAdministrationCosts", "context": {"deactivatedBy": null}}, {"name": "cO2PerPart", "entityId": "64e7ce7b32c47367d6bbed11", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "SalesAndGeneralAdministrationCosts", "context": {"deactivatedBy": null}}, {"name": "cO2RawMaterialOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed11", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "SalesAndGeneralAdministrationCosts", "context": {"deactivatedBy": null}}, {"name": "cO2RawMaterialRate", "entityId": "64e7ce7b32c47367d6bbed11", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "SalesAndGeneralAdministrationCosts", "context": {"deactivatedBy": null}}, {"name": "bomOverheadBase", "entityId": "64e7ce7b32c47367d6bbed13", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ResearchAndDevelopmentCosts", "context": {"deactivatedBy": null}}, {"name": "bomOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed13", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ResearchAndDevelopmentCosts", "context": {"deactivatedBy": null}}, {"name": "bomRate", "entityId": "64e7ce7b32c47367d6bbed13", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "ResearchAndDevelopmentCosts", "context": {"deactivatedBy": null}}, {"name": "costPerPart", "entityId": "64e7ce7b32c47367d6bbed13", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ResearchAndDevelopmentCosts", "context": {"deactivatedBy": null}}, {"name": "manufacturingOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed13", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ResearchAndDevelopmentCosts", "context": {"deactivatedBy": null}}, {"name": "manufacturingRate", "entityId": "64e7ce7b32c47367d6bbed13", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "ResearchAndDevelopmentCosts", "context": {"deactivatedBy": null}}, {"name": "rawMaterialOverheadBase", "entityId": "64e7ce7b32c47367d6bbed13", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ResearchAndDevelopmentCosts", "context": {"deactivatedBy": null}}, {"name": "rawMaterialOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed13", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ResearchAndDevelopmentCosts", "context": {"deactivatedBy": null}}, {"name": "rawMaterialRate", "entityId": "64e7ce7b32c47367d6bbed13", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "ResearchAndDevelopmentCosts", "context": {"deactivatedBy": null}}, {"name": "cO2BomOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed13", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ResearchAndDevelopmentCosts", "context": {"deactivatedBy": null}}, {"name": "cO2BomRate", "entityId": "64e7ce7b32c47367d6bbed13", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "ResearchAndDevelopmentCosts", "context": {"deactivatedBy": null}}, {"name": "cO2ManufacturingOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed13", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ResearchAndDevelopmentCosts", "context": {"deactivatedBy": null}}, {"name": "cO2ManufacturingRate", "entityId": "64e7ce7b32c47367d6bbed13", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "ResearchAndDevelopmentCosts", "context": {"deactivatedBy": null}}, {"name": "cO2PerPart", "entityId": "64e7ce7b32c47367d6bbed13", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ResearchAndDevelopmentCosts", "context": {"deactivatedBy": null}}, {"name": "cO2RawMaterialOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed13", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ResearchAndDevelopmentCosts", "context": {"deactivatedBy": null}}, {"name": "cO2RawMaterialRate", "entityId": "64e7ce7b32c47367d6bbed13", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "ResearchAndDevelopmentCosts", "context": {"deactivatedBy": null}}, {"name": "bomOverheadBase", "entityId": "64e7ce7b32c47367d6bbed15", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "BusinessRiskCosts", "context": {"deactivatedBy": null}}, {"name": "bomOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed15", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "BusinessRiskCosts", "context": {"deactivatedBy": null}}, {"name": "bomRate", "entityId": "64e7ce7b32c47367d6bbed15", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "BusinessRiskCosts", "context": {"deactivatedBy": null}}, {"name": "costPerPart", "entityId": "64e7ce7b32c47367d6bbed15", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "BusinessRiskCosts", "context": {"deactivatedBy": null}}, {"name": "manufacturingOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed15", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "BusinessRiskCosts", "context": {"deactivatedBy": null}}, {"name": "manufacturingRate", "entityId": "64e7ce7b32c47367d6bbed15", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "BusinessRiskCosts", "context": {"deactivatedBy": null}}, {"name": "rawMaterialOverheadBase", "entityId": "64e7ce7b32c47367d6bbed15", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "BusinessRiskCosts", "context": {"deactivatedBy": null}}, {"name": "rawMaterialOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed15", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "BusinessRiskCosts", "context": {"deactivatedBy": null}}, {"name": "rawMaterialRate", "entityId": "64e7ce7b32c47367d6bbed15", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "BusinessRiskCosts", "context": {"deactivatedBy": null}}, {"name": "cO2BomOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed15", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "BusinessRiskCosts", "context": {"deactivatedBy": null}}, {"name": "cO2BomRate", "entityId": "64e7ce7b32c47367d6bbed15", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "BusinessRiskCosts", "context": {"deactivatedBy": null}}, {"name": "cO2ManufacturingOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed15", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "BusinessRiskCosts", "context": {"deactivatedBy": null}}, {"name": "cO2ManufacturingRate", "entityId": "64e7ce7b32c47367d6bbed15", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "BusinessRiskCosts", "context": {"deactivatedBy": null}}, {"name": "cO2PerPart", "entityId": "64e7ce7b32c47367d6bbed15", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "BusinessRiskCosts", "context": {"deactivatedBy": null}}, {"name": "cO2RawMaterialOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed15", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "BusinessRiskCosts", "context": {"deactivatedBy": null}}, {"name": "cO2RawMaterialRate", "entityId": "64e7ce7b32c47367d6bbed15", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "BusinessRiskCosts", "context": {"deactivatedBy": null}}, {"name": "costPerPart", "entityId": "64e7ce7b32c47367d6bbed17", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "InterestOnFinishProductStock", "context": {"deactivatedBy": null}}, {"name": "interestBase", "entityId": "64e7ce7b32c47367d6bbed17", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "InterestOnFinishProductStock", "context": {"deactivatedBy": null}}, {"name": "interestRate", "entityId": "64e7ce7b32c47367d6bbed17", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "InterestOnFinishProductStock", "context": {"deactivatedBy": null}}, {"name": "bomOverheadBase", "entityId": "64e7ce7b32c47367d6bbed19", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "OtherExpendituresAfterPC", "context": {"deactivatedBy": null}}, {"name": "bomOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed19", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "OtherExpendituresAfterPC", "context": {"deactivatedBy": null}}, {"name": "bomRate", "entityId": "64e7ce7b32c47367d6bbed19", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "OtherExpendituresAfterPC", "context": {"deactivatedBy": null}}, {"name": "costPerPart", "entityId": "64e7ce7b32c47367d6bbed19", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "OtherExpendituresAfterPC", "context": {"deactivatedBy": null}}, {"name": "manufacturingOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed19", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "OtherExpendituresAfterPC", "context": {"deactivatedBy": null}}, {"name": "manufacturingRate", "entityId": "64e7ce7b32c47367d6bbed19", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "OtherExpendituresAfterPC", "context": {"deactivatedBy": null}}, {"name": "rawMaterialOverheadBase", "entityId": "64e7ce7b32c47367d6bbed19", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "OtherExpendituresAfterPC", "context": {"deactivatedBy": null}}, {"name": "rawMaterialOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed19", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "OtherExpendituresAfterPC", "context": {"deactivatedBy": null}}, {"name": "rawMaterialRate", "entityId": "64e7ce7b32c47367d6bbed19", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "OtherExpendituresAfterPC", "context": {"deactivatedBy": null}}, {"name": "cO2BomOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed19", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "OtherExpendituresAfterPC", "context": {"deactivatedBy": null}}, {"name": "cO2BomRate", "entityId": "64e7ce7b32c47367d6bbed19", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "OtherExpendituresAfterPC", "context": {"deactivatedBy": null}}, {"name": "cO2ManufacturingOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed19", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "OtherExpendituresAfterPC", "context": {"deactivatedBy": null}}, {"name": "cO2ManufacturingRate", "entityId": "64e7ce7b32c47367d6bbed19", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "OtherExpendituresAfterPC", "context": {"deactivatedBy": null}}, {"name": "cO2PerPart", "entityId": "64e7ce7b32c47367d6bbed19", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "OtherExpendituresAfterPC", "context": {"deactivatedBy": null}}, {"name": "cO2RawMaterialOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed19", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "OtherExpendituresAfterPC", "context": {"deactivatedBy": null}}, {"name": "cO2RawMaterialRate", "entityId": "64e7ce7b32c47367d6bbed19", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "OtherExpendituresAfterPC", "context": {"deactivatedBy": null}}, {"name": "bomOverheadBase", "entityId": "64e7ce7b32c47367d6bbed1b", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "DirectOverheadsAfterPC", "context": {"deactivatedBy": null}}, {"name": "bomOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed1b", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "DirectOverheadsAfterPC", "context": {"deactivatedBy": null}}, {"name": "bomRate", "entityId": "64e7ce7b32c47367d6bbed1b", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "DirectOverheadsAfterPC", "context": {"deactivatedBy": null}}, {"name": "costPerPart", "entityId": "64e7ce7b32c47367d6bbed1b", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "DirectOverheadsAfterPC", "context": {"deactivatedBy": null}}, {"name": "manufacturingOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed1b", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "DirectOverheadsAfterPC", "context": {"deactivatedBy": null}}, {"name": "manufacturingRate", "entityId": "64e7ce7b32c47367d6bbed1b", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "DirectOverheadsAfterPC", "context": {"deactivatedBy": null}}, {"name": "rawMaterialOverheadBase", "entityId": "64e7ce7b32c47367d6bbed1b", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "DirectOverheadsAfterPC", "context": {"deactivatedBy": null}}, {"name": "rawMaterialOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed1b", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "DirectOverheadsAfterPC", "context": {"deactivatedBy": null}}, {"name": "rawMaterialRate", "entityId": "64e7ce7b32c47367d6bbed1b", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "DirectOverheadsAfterPC", "context": {"deactivatedBy": null}}, {"name": "cO2BomOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed1b", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "DirectOverheadsAfterPC", "context": {"deactivatedBy": null}}, {"name": "cO2BomRate", "entityId": "64e7ce7b32c47367d6bbed1b", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "DirectOverheadsAfterPC", "context": {"deactivatedBy": null}}, {"name": "cO2ManufacturingOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed1b", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "DirectOverheadsAfterPC", "context": {"deactivatedBy": null}}, {"name": "cO2ManufacturingRate", "entityId": "64e7ce7b32c47367d6bbed1b", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "DirectOverheadsAfterPC", "context": {"deactivatedBy": null}}, {"name": "cO2PerPart", "entityId": "64e7ce7b32c47367d6bbed1b", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "DirectOverheadsAfterPC", "context": {"deactivatedBy": null}}, {"name": "cO2RawMaterialOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed1b", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "DirectOverheadsAfterPC", "context": {"deactivatedBy": null}}, {"name": "cO2RawMaterialRate", "entityId": "64e7ce7b32c47367d6bbed1b", "entityType": "OVERHEADS_AFTER_PC", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "DirectOverheadsAfterPC", "context": {"deactivatedBy": null}}, {"name": "bomOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed1d", "entityType": "PROFIT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "Profit", "context": {"deactivatedBy": null}}, {"name": "bomRate", "entityId": "64e7ce7b32c47367d6bbed1d", "entityType": "PROFIT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "Profit", "context": {"deactivatedBy": null}}, {"name": "costPerPart", "entityId": "64e7ce7b32c47367d6bbed1d", "entityType": "PROFIT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "Profit", "context": {"deactivatedBy": null}}, {"name": "manufacturingOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed1d", "entityType": "PROFIT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "Profit", "context": {"deactivatedBy": null}}, {"name": "manufacturingRate", "entityId": "64e7ce7b32c47367d6bbed1d", "entityType": "PROFIT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "Profit", "context": {"deactivatedBy": null}}, {"name": "rawMaterialOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed1d", "entityType": "PROFIT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "Profit", "context": {"deactivatedBy": null}}, {"name": "rawMaterialRate", "entityId": "64e7ce7b32c47367d6bbed1d", "entityType": "PROFIT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "Profit", "context": {"deactivatedBy": null}}, {"name": "cO2BomOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed1d", "entityType": "PROFIT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "Profit", "context": {"deactivatedBy": null}}, {"name": "cO2BomRate", "entityId": "64e7ce7b32c47367d6bbed1d", "entityType": "PROFIT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "Profit", "context": {"deactivatedBy": null}}, {"name": "cO2ManufacturingOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed1d", "entityType": "PROFIT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "Profit", "context": {"deactivatedBy": null}}, {"name": "cO2ManufacturingRate", "entityId": "64e7ce7b32c47367d6bbed1d", "entityType": "PROFIT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "Profit", "context": {"deactivatedBy": null}}, {"name": "cO2PerPart", "entityId": "64e7ce7b32c47367d6bbed1d", "entityType": "PROFIT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "Profit", "context": {"deactivatedBy": null}}, {"name": "cO2RawMaterialOverheadCosts", "entityId": "64e7ce7b32c47367d6bbed1d", "entityType": "PROFIT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "Profit", "context": {"deactivatedBy": null}}, {"name": "cO2RawMaterialRate", "entityId": "64e7ce7b32c47367d6bbed1d", "entityType": "PROFIT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "Profit", "context": {"deactivatedBy": null}}, {"name": "costPerPart", "entityId": "64e7ce7b32c47367d6bbed1f", "entityType": "TERMS_OF_PAYMENT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "InterestForTermsOfPayment", "context": {"deactivatedBy": null}}, {"name": "interestPeriod", "entityId": "64e7ce7b32c47367d6bbed1f", "entityType": "TERMS_OF_PAYMENT", "type": "com.nu.bom.core.manufacturing.fieldTypes.TimeInYears", "version": 1, "newVersion": 1, "entityRef": "InterestForTermsOfPayment", "context": {"deactivatedBy": null}}, {"name": "interestRate", "entityId": "64e7ce7b32c47367d6bbed1f", "entityType": "TERMS_OF_PAYMENT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "InterestForTermsOfPayment", "context": {"deactivatedBy": null}}, {"name": "overheadBase", "entityId": "64e7ce7b32c47367d6bbed1f", "entityType": "TERMS_OF_PAYMENT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "InterestForTermsOfPayment", "context": {"deactivatedBy": null}}, {"name": "costPerPart", "entityId": "64e7ce7b32c47367d6bbed21", "entityType": "TERMS_OF_PAYMENT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "Discount", "context": {"deactivatedBy": null}}, {"name": "costPerPartInterestForTermsOfPayment", "entityId": "64e7ce7b32c47367d6bbed21", "entityType": "TERMS_OF_PAYMENT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "Discount", "context": {"deactivatedBy": null}}, {"name": "overheadBase", "entityId": "64e7ce7b32c47367d6bbed21", "entityType": "TERMS_OF_PAYMENT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "Discount", "context": {"deactivatedBy": null}}, {"name": "overheadRate", "entityId": "64e7ce7b32c47367d6bbed21", "entityType": "TERMS_OF_PAYMENT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "Discount", "context": {"deactivatedBy": null}}, {"name": "cO2OverheadBase", "entityId": "64e7ce7b32c47367d6bbed21", "entityType": "TERMS_OF_PAYMENT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "Discount", "context": {"deactivatedBy": null}}, {"name": "cO2OverheadRate", "entityId": "64e7ce7b32c47367d6bbed21", "entityType": "TERMS_OF_PAYMENT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "Discount", "context": {"deactivatedBy": null}}, {"name": "cO2PerPart", "entityId": "64e7ce7b32c47367d6bbed21", "entityType": "TERMS_OF_PAYMENT", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "Discount", "context": {"deactivatedBy": null}}, {"name": "costPerPart", "entityId": "64e7ce7b32c47367d6bbed07", "entityType": "OVERHEADS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing.Overheads", "context": {"deactivatedBy": null}}, {"name": "costPerPartIncoterms", "entityId": "64e7ce7b32c47367d6bbed07", "entityType": "OVERHEADS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing.Overheads", "context": {"deactivatedBy": null}}, {"name": "costPerPartOverheadsAfterPC", "entityId": "64e7ce7b32c47367d6bbed07", "entityType": "OVERHEADS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing.Overheads", "context": {"deactivatedBy": null}}, {"name": "costPerPartProfit", "entityId": "64e7ce7b32c47367d6bbed07", "entityType": "OVERHEADS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing.Overheads", "context": {"deactivatedBy": null}}, {"name": "costPerPartTermsOfPayment", "entityId": "64e7ce7b32c47367d6bbed07", "entityType": "OVERHEADS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing.Overheads", "context": {"deactivatedBy": null}}, {"name": "interestRate", "entityId": "64e7ce7b32c47367d6bbed07", "entityType": "OVERHEADS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing.Overheads", "context": {"deactivatedBy": null}}, {"name": "cO2Incoterms", "entityId": "64e7ce7b32c47367d6bbed07", "entityType": "OVERHEADS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing.Overheads", "context": {"deactivatedBy": null}}, {"name": "cO2Overheads", "entityId": "64e7ce7b32c47367d6bbed07", "entityType": "OVERHEADS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing.Overheads", "context": {"deactivatedBy": null}}, {"name": "cO2OverheadsAfterPC", "entityId": "64e7ce7b32c47367d6bbed07", "entityType": "OVERHEADS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing.Overheads", "context": {"deactivatedBy": null}}, {"name": "cO2Profit", "entityId": "64e7ce7b32c47367d6bbed07", "entityType": "OVERHEADS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing.Overheads", "context": {"deactivatedBy": null}}, {"name": "cO2TermsOfPayment", "entityId": "64e7ce7b32c47367d6bbed07", "entityType": "OVERHEADS", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing.Overheads", "context": {"deactivatedBy": null}}, {"name": "averageLotSize", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "averageUsableVolumePerYear", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "averageVolumeNextStepOrUsableVolumeManufacturing", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "averageVolumePerYear", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "internalPeakVolumeNextStepOrUsableVolumeManufacturing", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "locationName", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Text", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "lotSize", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "materialInterestDays", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.TimeInYears", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "materialOverheadRate", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "materialScrapInterestOverhead", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "netSalesPrice", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "peakUsableVolumePerYear", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "peakVolumeNextStepOrUsableVolumeManufacturing", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "peakVolumePerYear", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "productionVolumeMode", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Num", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "averageVolumeOverLifeTime", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "cO2LightPerAreaPerYear", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "cO2Material", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "cO2MaterialOverhead", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "cO2MaterialOverheadRate", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Rate", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "cO2MaterialScrapInterestOverhead", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "cO2Overhead", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "cO2PerKwh", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "cO2PerPart", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "cO2Production", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "currentGapToCostPerPart", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "currentGapToCostPerPartPct", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.NaNSupport", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "currentGapToTarget", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "currentGapToTargetPct", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.NaNSupport", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "targetGapToCostPerPart", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "targetGapToCostPerPartPct", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.NaNSupport", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "targetGapToCurrent", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "targetGapToCurrentPct", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.NaNSupport", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "cO2CurrentGapToCostPerPart", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "cO2CurrentGapToCostPerPartPct", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.NaNSupport", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "cO2CurrentGapToTarget", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "cO2CurrentGapToTargetPct", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.NaNSupport", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "cO2TargetGapToCostPerPart", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "cO2TargetGapToCostPerPartPct", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.NaNSupport", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "cO2TargetGapToCurrent", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Emission", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "cO2TargetGapToCurrentPct", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.NaNSupport", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "costPerPart", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "overheadCosts", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "productionCosts", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "interestOnMaterialStock", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "interestOnMaterialStockCostConsumable", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "interestOnMaterialStockCostConsumableThis", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "interestOnMaterialStockCostElectronicComponent", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "interestOnMaterialStockCostElectronicComponentThis", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "interestOnMaterialStockCostPurchaseParts", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "interestOnMaterialStockCostPurchasePartsThis", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "interestOnMaterialStockCostRawMaterial", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "interestOnMaterialStockCostRawMaterialThis", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "materialCostConsumable", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "materialCostElectronicComponent", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "materialCostPurchaseParts", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "materialCostRawMaterial", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "materialCosts", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "materialOverheadCostConsumable", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "materialOverheadCostConsumableThis", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "materialOverheadCostElectronicComponent", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "materialOverheadCostElectronicComponentThis", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "materialOverheadCostPurchaseParts", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "materialOverheadCostPurchasePartsThis", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "materialOverheadCostRawMaterial", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "materialOverheadCostRawMaterialThis", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "materialOverheadCosts", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "materialScrapOverheadInterestCosts", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "purchasePartsOverheadBase", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}, {"name": "rawMaterialOverheadBase", "entityId": "************************", "entityType": "MANUFACTURING", "type": "com.nu.bom.core.manufacturing.fieldTypes.Money", "version": 1, "newVersion": 1, "entityRef": "ManualManufacturing", "context": {"deactivatedBy": null}}], "bomNode": {"id": "64e7ce7b32c47367d6bbecce", "parents": [], "subNodes": [], "manufacturing": null, "year": 2023, "status": "TODO", "responsibleUser": null, "kpi": {"costPerPart": {}, "co2PerPart": {}}, "branch": {"id": "58650115d1a74ec68df7c0d2", "name": "ManualManufacturingTitle", "global": true, "latestChangesetId": "64e7ce7b32c47367d6bbed3a", "owner": "mock-branch-user", "master": true, "fromMaster": true, "lastModifiedDate": null, "lastModifiedBy": null, "sourceBranch": null, "undoable": false, "redoable": false, "rootNode": "64e7ce7b32c47367d6bbecce", "partName": null, "published": true, "bomNodeDeleted": null}, "cbd": {"cost": [{"id": 1, "source": "materialCosts", "cost": {"name": "materialCosts", "value": 0, "type": "Money", "unit": null, "source": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}}, "fraction": 0, "nestedTable": [{"id": 1, "source": "directMaterialCosts", "cost": {"name": "directMaterialCosts", "value": 0.0, "type": "Money", "unit": null, "source": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}}, "fraction": 0, "nestedTable": [], "unit": "Money", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}, {"id": 2, "source": "materialScrapCosts", "cost": {"name": "materialScrapCosts", "value": 0.0, "type": "Money", "unit": null, "source": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}}, "fraction": 0, "nestedTable": null, "unit": "Money", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}, {"id": 3, "source": "materialOverheadCosts", "cost": {"name": "materialOverheadCosts", "value": 0, "type": "Money", "unit": null, "source": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}}, "fraction": 0, "nestedTable": null, "unit": "Money", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}, {"id": 4, "source": "interestOnMaterialStock", "cost": {"name": "interestOnMaterialStock", "value": 0, "type": "Money", "unit": null, "source": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}}, "fraction": 0, "nestedTable": null, "unit": "Money", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}], "unit": "Money", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}, {"id": 2, "source": "Manufacturing", "cost": {"name": "manufacturingCosts3", "value": 0.0, "type": "Money", "unit": null, "source": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}}, "fraction": 0, "nestedTable": [{"id": 1, "source": "ManufacturingSteps", "cost": {"name": "manufacturingCosts2Step", "value": 0.0, "type": "Money", "unit": null, "source": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}}, "fraction": 0, "nestedTable": [], "unit": "Money", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}, {"id": 2, "source": "manufacturingCosts2SubManufacturing", "cost": {"name": "manufacturingCosts2SubManufacturing", "value": 0.0, "type": "Money", "unit": null, "source": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}}, "fraction": 0, "nestedTable": null, "unit": "Money", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}, {"id": 3, "source": "interestOnWorkInProgress", "cost": {"name": "interestOnWorkInProgressStep", "value": 0.0, "type": "Money", "unit": null, "source": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}}, "fraction": 0, "nestedTable": null, "unit": "Money", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}], "unit": "Money", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}, {"id": 3, "source": "specialDirectCosts", "cost": {"name": "specialDirectCosts", "value": 0.0, "type": "Money", "unit": null, "source": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}}, "fraction": 0, "nestedTable": [{"id": 1, "source": "developmentCosts", "cost": {"name": "developmentCosts", "value": 0.0, "type": "Money", "unit": null, "source": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}}, "fraction": 0, "nestedTable": null, "unit": "Money", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}, {"id": 2, "source": "rampUpCosts", "cost": {"name": "rampUpCosts", "value": 0.0, "type": "Money", "unit": null, "source": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}}, "fraction": 0, "nestedTable": null, "unit": "Money", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}, {"id": 3, "source": "packageCarrierCosts", "cost": {"name": "packageCarrierCosts", "value": 0.0, "type": "Money", "unit": null, "source": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}}, "fraction": 0, "nestedTable": null, "unit": "Money", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}], "unit": "Money", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}, {"id": 4, "source": "Overheads", "cost": {"name": "overheadCosts", "value": 0, "type": "Money", "unit": null, "source": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}}, "fraction": 0, "nestedTable": [{"id": 1, "source": "costPerPartOverheadsAfterPC", "cost": {"name": "costPerPartOverheadsAfterPC", "value": 0, "type": "Money", "unit": null, "source": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}}, "fraction": 0, "nestedTable": null, "unit": "Money", "entityType": "OVERHEADS", "entityId": "64e7ce7b32c47367d6bbed07", "entityClass": "Overheads", "overwritten": false, "deactivatedBy": []}, {"id": 2, "source": "costPerPartProfit", "cost": {"name": "costPerPartProfit", "value": 0, "type": "Money", "unit": null, "source": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}}, "fraction": 0, "nestedTable": null, "unit": "Money", "entityType": "OVERHEADS", "entityId": "64e7ce7b32c47367d6bbed07", "entityClass": "Overheads", "overwritten": false, "deactivatedBy": []}, {"id": 3, "source": "costPerPartTermsOfPayment", "cost": {"name": "costPerPartTermsOfPayment", "value": 0, "type": "Money", "unit": null, "source": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}}, "fraction": 0, "nestedTable": null, "unit": "Money", "entityType": "OVERHEADS", "entityId": "64e7ce7b32c47367d6bbed07", "entityClass": "Overheads", "overwritten": false, "deactivatedBy": []}, {"id": 4, "source": "costPerPartIncoterms", "cost": {"name": "costPerPartIncoterms", "value": 0, "type": "Money", "unit": null, "source": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}}, "fraction": 0, "nestedTable": null, "unit": "Money", "entityType": "OVERHEADS", "entityId": "64e7ce7b32c47367d6bbed07", "entityClass": "Overheads", "overwritten": false, "deactivatedBy": []}], "unit": "Money", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}], "co2": [{"id": 1, "source": "cO2MaterialCosts", "cost": {"name": "cO2Material", "value": 0, "type": "", "unit": "KILOGRAM_CO2E", "source": null, "denominatorUnit": null}, "fraction": 0, "nestedTable": [{"id": 1, "source": "cO2DirectMaterial", "cost": {"name": "cO2DirectMaterial", "value": 0.0, "type": "Emission", "unit": "KILOGRAM_CO2E", "source": null, "denominatorUnit": null}, "fraction": 0, "nestedTable": [], "unit": "KILOGRAM_CO2E", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}, {"id": 2, "source": "cO2MaterialScrap", "cost": {"name": "cO2MaterialScrap", "value": 0.0, "type": "Emission", "unit": "KILOGRAM_CO2E", "source": null, "denominatorUnit": null}, "fraction": 0, "nestedTable": null, "unit": "KILOGRAM_CO2E", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}, {"id": 3, "source": "cO2MaterialOverhead", "cost": {"name": "cO2MaterialOverhead", "value": 0, "type": "", "unit": "KILOGRAM_CO2E", "source": null, "denominatorUnit": null}, "fraction": 0, "nestedTable": null, "unit": "KILOGRAM_CO2E", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}], "unit": "KILOGRAM_CO2E", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}, {"id": 2, "source": "Manufacturing", "cost": {"name": "cO2Manufacturing3", "value": 0.0, "type": "Emission", "unit": "KILOGRAM_CO2E", "source": null, "denominatorUnit": null}, "fraction": 0, "nestedTable": [{"id": 1, "source": "ManufacturingSteps", "cost": {"name": "cO2Manufacturing3Step", "value": 0.0, "type": "Emission", "unit": "KILOGRAM_CO2E", "source": null, "denominatorUnit": null}, "fraction": 0, "nestedTable": [], "unit": "KILOGRAM_CO2E", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}, {"id": 2, "source": "manufacturingCosts2SubManufacturing", "cost": {"name": "cO2Manufacturing3SubManufacturing", "value": 0.0, "type": "Emission", "unit": "KILOGRAM_CO2E", "source": null, "denominatorUnit": null}, "fraction": 0, "nestedTable": null, "unit": "KILOGRAM_CO2E", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}], "unit": "KILOGRAM_CO2E", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}, {"id": 3, "source": "specialDirectCosts", "cost": {"name": "cO2SpecialDirectCosts", "value": 0.0, "type": "Emission", "unit": "KILOGRAM_CO2E", "source": null, "denominatorUnit": null}, "fraction": 0, "nestedTable": [{"id": 1, "source": "developmentCosts", "cost": {"name": "cO2DevelopmentCosts", "value": 0.0, "type": "Emission", "unit": "KILOGRAM_CO2E", "source": null, "denominatorUnit": null}, "fraction": 0, "nestedTable": null, "unit": "KILOGRAM_CO2E", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}, {"id": 2, "source": "rampUpCosts", "cost": {"name": "cO2RampUpCosts", "value": 0.0, "type": "Emission", "unit": "KILOGRAM_CO2E", "source": null, "denominatorUnit": null}, "fraction": 0, "nestedTable": null, "unit": "KILOGRAM_CO2E", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}, {"id": 3, "source": "packageCarrierCosts", "cost": {"name": "cO2PackageCarrierCosts", "value": 0.0, "type": "Emission", "unit": "KILOGRAM_CO2E", "source": null, "denominatorUnit": null}, "fraction": 0, "nestedTable": null, "unit": "KILOGRAM_CO2E", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}], "unit": "KILOGRAM_CO2E", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}, {"id": 4, "source": "Overheads", "cost": {"name": "cO2Overhead", "value": 0, "type": "", "unit": "KILOGRAM_CO2E", "source": null, "denominatorUnit": null}, "fraction": 0, "nestedTable": [{"id": 1, "source": "costPerPartOverheadsAfterPC", "cost": {"name": "cO2OverheadsAfterPC", "value": 0, "type": "", "unit": "KILOGRAM_CO2E", "source": null, "denominatorUnit": null}, "fraction": 0, "nestedTable": null, "unit": "KILOGRAM_CO2E", "entityType": "OVERHEADS", "entityId": "64e7ce7b32c47367d6bbed07", "entityClass": "Overheads", "overwritten": false, "deactivatedBy": []}, {"id": 2, "source": "costPerPartProfit", "cost": {"name": "cO2Profit", "value": 0, "type": "", "unit": "KILOGRAM_CO2E", "source": null, "denominatorUnit": null}, "fraction": 0, "nestedTable": null, "unit": "KILOGRAM_CO2E", "entityType": "OVERHEADS", "entityId": "64e7ce7b32c47367d6bbed07", "entityClass": "Overheads", "overwritten": false, "deactivatedBy": []}, {"id": 3, "source": "costPerPartTermsOfPayment", "cost": {"name": "cO2TermsOfPayment", "value": 0, "type": "", "unit": "KILOGRAM_CO2E", "source": null, "denominatorUnit": null}, "fraction": 0, "nestedTable": null, "unit": "KILOGRAM_CO2E", "entityType": "OVERHEADS", "entityId": "64e7ce7b32c47367d6bbed07", "entityClass": "Overheads", "overwritten": false, "deactivatedBy": []}, {"id": 4, "source": "costPerPartIncoterms", "cost": {"name": "cO2Incoterms", "value": 0, "type": "", "unit": "KILOGRAM_CO2E", "source": null, "denominatorUnit": null}, "fraction": 0, "nestedTable": null, "unit": "KILOGRAM_CO2E", "entityType": "OVERHEADS", "entityId": "64e7ce7b32c47367d6bbed07", "entityClass": "Overheads", "overwritten": false, "deactivatedBy": []}], "unit": "KILOGRAM_CO2E", "entityType": "MANUFACTURING", "entityId": "************************", "entityClass": "ManualManufacturing", "overwritten": false, "deactivatedBy": []}]}, "openMergesAvailable": [], "waterfall": {"cost": [{"id": "TOTAL", "type": "TOTAL", "designation": "Total", "expendable": false, "value": 0.0, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "level": 1, "drillDownType": "ALL", "quantity": null, "source": null, "displayDesignation": "Total", "_debug_entityType": null, "_debug_cost": null, "_debug_quantity": null, "_debug_debug": null}, {"id": "0dc450c1-5a44-43aa-800f-88838202d41e", "type": "OTHERS", "designation": "Others||0dc450c1-5a44-43aa-800f-88838202d41e", "expendable": false, "value": 0.0, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "level": 1, "drillDownType": "ALL", "quantity": 1.0, "source": "Others", "displayDesignation": "Others", "_debug_entityType": "MANUFACTURING", "_debug_cost": 0.0, "_debug_quantity": null, "_debug_debug": null}], "co2": [{"id": "TOTAL", "type": "TOTAL", "designation": "Total", "expendable": false, "value": 0.0, "currencyInfo": null, "level": 1, "drillDownType": "ALL", "quantity": null, "source": null, "displayDesignation": "Total", "_debug_entityType": null, "_debug_cost": null, "_debug_quantity": null, "_debug_debug": null}, {"id": "2dbb5145-b59a-4845-b76d-68d52dc8521c", "type": "OTHERS", "designation": "Others||2dbb5145-b59a-4845-b76d-68d52dc8521c", "expendable": false, "value": 0.0, "currencyInfo": null, "level": 1, "drillDownType": "ALL", "quantity": 1.0, "source": "Others", "displayDesignation": "Others", "_debug_entityType": "MANUFACTURING", "_debug_cost": 0.0, "_debug_quantity": null, "_debug_debug": null}]}, "title": "ManualManufacturingTitle", "calculationType": {"name": "calculationType", "type": "CalculationType", "unit": null, "value": "MANUAL_CALCULATION", "valueInDefaultUnit": null, "source": null, "metaInfo": {"path": "/api/calculationType?subfalse", "triggerUpdate": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, "internalRevisionId": "64e7ce7b32c47367d6bbed3958650115d1a74ec68df7c0d2", "lastModifiedDate": "2023-08-24T21:41:15.354Z", "dirtyChildLoading": false}, "result": {"id": "************************", "type": "MANUFACTURING", "name": "ManualManufacturing", "ref": "ManualManufacturing", "version": 1, "part": {"id": "", "designation": "WizardTestPart", "number": "123456", "images": [], "createdDate": null, "lastModifiedDate": null, "refKey": null}, "shape": null, "className": "ManualManufacturing", "isolated": false, "fields": [{"name": "CURRENCY_CONVERTER", "type": "ExchangeRatesField", "unit": null, "value": {"CHF": 1.089642, "CNY": 7.774285, "EUR": 1.0, "GBP": 0.859495, "JPY": 133.272021, "USD": 1.216595, "AUD": 1.46, "CAD": 1.38, "SEK": 10.33, "KRW": 1345.02, "MXN": 22.09, "INR": 83.93, "RUB": 144.77, "TRY": 16.34, "BRL": 5.32, "TWD": 31.54, "DKK": 7.44, "PLN": 4.74, "IDR": 15798.27, "HUF": 374.54, "CZK": 24.62}, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "accumulatedMaterialScrapRate", "type": "Rate", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "allocationInterestCostPerPartTool", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "averageVolumeOfStepsPerYear", "type": "Pieces", "unit": "PIECE", "value": 0.0, "valueInDefaultUnit": 0.0, "source": "C", "metaInfo": {"readOnly": true, "defaultUnit": {"unit": "PIECE", "isFixed": true}}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "YEAR", "type": "TIME", "behavior": "WRITE_THROUGH"}}, {"name": "baseCurrency", "type": "<PERSON><PERSON><PERSON><PERSON>", "unit": null, "value": "EUR", "valueInDefaultUnit": null, "source": "C", "metaInfo": {"input": true, "path": "/api/currency/bomNode/64e7ce7b32c47367d6bbecce/branch/58650115d1a74ec68df7c0d2", "remoteSearch": false, "asObject": true, "masterdataCalculation": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "bomCurrentCosts", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "bomTargetCosts", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2BomCurrentCosts", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2BomTargetCosts", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2DepreciationCostPerPartMachine", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2DepreciationPerAreaPerYear", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 7.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "YEAR", "type": "TIME", "behavior": "WRITE_THROUGH"}}, {"name": "cO2DevelopmentCosts", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2DevelopmentCostsIfInHouse", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2DevelopmentCostsInHouseSubcalculationsOfNoStep", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2DevelopmentCostsInHouseSubcalculationsOfSteps", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2DevelopmentCostsThis", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2DirectMaterial", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"costType": "variable"}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2DirectMaterialConsumables", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2DirectMaterialRawMaterial", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"costType": "variable"}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2DirectMaterialRawMaterialExtension", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2DirectMaterialSubManufacturing", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2DirectMaterialSubManufacturingBomEntry", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2DirectMaterialSubManufacturingInHouse", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2DirectMaterialSubManufacturingInHouseBomEntry", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2DirectMaterialSubManufacturingPurchase", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"costType": "variable"}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2DirectMaterialSubManufacturingPurchaseBomEntry", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2FixedCostPerPartMachine", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2HeatPerAreaPerYear", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 16.315, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "QMYEAR", "type": "AREA_TIME", "behavior": "WRITE_THROUGH"}}, {"name": "cO2Manufacturing2", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2Manufacturing2SubManufacturing", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2Manufacturing2SubManufacturingBomEntry", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2Manufacturing3", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2Manufacturing3Step", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2Manufacturing3SubManufacturing", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2Manufacturing3SubManufacturingBomEntry", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2ManufacturingRMOC", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2ManufacturingRMOCSubManufacturing", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2ManufacturingRMOCSubManufacturingBomEntry", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2ManufacturingScrap", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2ManufacturingStep2", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2MaterialConsumables", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2MaterialOverheadConsumables", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2MaterialOverheadRawMaterials", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2MaterialOverheadSubManufacturing", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2MaterialOverheadSubManufacturingInHouse", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2MaterialOverheadSubManufacturingPurchase", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2MaterialRawMaterials", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2MaterialScrap", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"costType": "variable"}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2MaterialScrapBase", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2MaterialScrapBaseSubManufacturingInHouseBomEntry", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2MaterialScrapBaseSubManufacturingPurchaseBomEntry", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2MaterialScrapConsumables", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2MaterialScrapSubManufacturing", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2MaterialScrapSubManufacturingInHouse", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2MaterialScrapSubManufacturingInHouseBomEntry", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2MaterialScrapSubManufacturingPurchase", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2MaterialScrapSubManufacturingPurchaseBomEntry", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2MaterialSubManufacturing", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2MaterialSubManufacturingBomEntry", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2MaterialSubManufacturingInHouse", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2MaterialSubManufacturingInHouseBomEntry", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2MaterialSubManufacturingPurchase", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2MaterialSubManufacturingPurchaseBomEntry", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2PackageCarrierCosts", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2PackageCarrierCostsIfInHouse", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2PackageCarrierCostsInHouseSubcalculationsOfNoStep", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2PackageCarrierCostsInHouseSubcalculationsOfSteps", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2PackageCarrierCostsThis", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2PerManufacturing", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2PerManufacturing3", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2PerManufacturingInHouse", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2PerPartLabor", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2PerPartMachine", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2PerPartSetUp", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2PerPartTool", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2RampUpCosts", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2RampUpCostsIfInHouse", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2RampUpCostsInHouseSubcalculationsOfNoStep", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2RampUpCostsInHouseSubcalculationsOfSteps", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2RampUpCostsThis", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2ScrapBaseRawMaterialsExtension", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2SpecialDirectCosts", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2SurfaceCostPerPartMachine", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2VariableCostPerPartMachine", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "cO2WipMaterialBomEntry", "type": "Emission", "unit": "KILOGRAM_CO2E", "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "calculationQualityConfiguration", "type": "NoCalcFieldResult", "unit": null, "value": "64e7ce7b32c47367d6bbecd9", "valueInDefaultUnit": null, "source": "C", "metaInfo": {"dontExport": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "calculationQualityConfigurationKey", "type": "CalculationQualityConfigurationKey", "unit": null, "value": {"key": "GREENFIELD", "instanceVersion": 0, "isTsetConfiguration": true}, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"dontSortOptions": true, "dontExport": true, "input": true, "path": "/api/calculationQualities", "remoteSearch": true, "asObject": true, "valueType": "CONFIG_IDENTIFIER", "translationLabel": "calculationQuality", "labelField": "calculationQualityDisplayName", "useLabelAsKey": false}, "systemValue": null, "label": "Greenfield", "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "calculationQualityDisplayName", "type": "Text", "unit": null, "value": "Greenfield", "valueInDefaultUnit": null, "source": "C", "metaInfo": {"dontExport": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "callsPerYear", "type": "<PERSON><PERSON>", "unit": null, "value": 12.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"input": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "YEAR", "type": "TIME", "behavior": "WRITE_THROUGH"}}, {"name": "checkDimension", "type": "<PERSON><PERSON>", "unit": null, "value": null, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "childCurrencies", "type": "CurrenciesField", "unit": null, "value": {"data": {"CHF": {"description": "Swiss Franc", "numberFormat": "#,##0.00\" CHF\""}, "CNY": {"description": "Chinese Yuan", "numberFormat": "#,##0.00\" CNY\""}, "EUR": {"description": "Euro", "numberFormat": "#,##0.00\" €\""}, "GBP": {"description": "British Pound Sterling", "numberFormat": "#,##0.00\" £\""}, "JPY": {"description": "Japanese Yen", "numberFormat": "#,##0.00\" JPY\""}, "USD": {"description": "United States Dollar", "numberFormat": "#,##0.00\" $\""}, "AUD": {"description": "Australian Dollar", "numberFormat": "#,##0.00\" A$\""}, "CAD": {"description": "Canadian Dollar", "numberFormat": "#,##0.00\" C$\""}, "SEK": {"description": "Swedish Krona", "numberFormat": "#,##0.00\" kr\""}, "KRW": {"description": "South Korean Won", "numberFormat": "#,##0.00\" ₩\""}, "MXN": {"description": "Mexican Peso", "numberFormat": "#,##0.00\" Mex$\""}, "INR": {"description": "Indian Rupee", "numberFormat": "#,##0.00\" ₹\""}, "RUB": {"description": "Russian Ruble", "numberFormat": "#,##0.00\" ₽\""}, "TRY": {"description": "Turkish Lira", "numberFormat": "#,##0.00\" ₺\""}, "BRL": {"description": "Brazilian Real", "numberFormat": "#,##0.00\" R$\""}, "TWD": {"description": "New Taiwan Dollar", "numberFormat": "#,##0.00\" NT$\""}, "DKK": {"description": "Danish Krone", "numberFormat": "#,##0.00\" Kr\""}, "PLN": {"description": "Polish Zloty", "numberFormat": "#,##0.00\" zł\""}, "IDR": {"description": "Indonesian Rupiah", "numberFormat": "#,##0.00\" Rp\""}, "HUF": {"description": "Hungarian Forint", "numberFormat": "#,##0.00\" Ft\""}, "CZK": {"description": "Czech Koruna", "numberFormat": "#,##0.00\" Kč\""}}}, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "childExchangeRates", "type": "ExchangeRatesField", "unit": null, "value": {"CHF": 1.089642, "CNY": 7.774285, "EUR": 1.0, "GBP": 0.859495, "JPY": 133.272021, "USD": 1.216595, "AUD": 1.46, "CAD": 1.38, "SEK": 10.33, "KRW": 1345.02, "MXN": 22.09, "INR": 83.93, "RUB": 144.77, "TRY": 16.34, "BRL": 5.32, "TWD": 31.54, "DKK": 7.44, "PLN": 4.74, "IDR": 15798.27, "HUF": 374.54, "CZK": 24.62}, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "costPerPartInterestOnWorkInProgress", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "costPerPartLabor", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "costPerPartMachine", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "costPerPartManufacturingScrap", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "costPerPartManufacturingScrapOfInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "costPerPartManufacturingScrapOfStepLessInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "costPerPartManufacturingScrapStep", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "costPerPartRMOC", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "costPerPartRMOCOfInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "costPerPartRMOCOfStepLessInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "costPerPartRMOCStep", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "costPerPartSetup", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "costPerPartSetupDirectLabor", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "costPerPartSetupLabor", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "costPerPartSetupSystem", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "costUnit", "type": "Text", "unit": null, "value": "PIECE", "valueInDefaultUnit": null, "source": "C", "metaInfo": {"input": true, "path": "/api/type/units?dimension=Pieces", "remoteSearch": true, "asObject": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "createAttachments", "type": "Attachment", "unit": null, "value": true, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "createGroups", "type": "ManufacturingEntity", "unit": null, "value": true, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "createOverhead", "type": "ManufacturingEntity", "unit": null, "value": true, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "currencies", "type": "CurrenciesField", "unit": null, "value": {"data": {"CHF": {"description": "Swiss Franc", "numberFormat": "#,##0.00\" CHF\""}, "CNY": {"description": "Chinese Yuan", "numberFormat": "#,##0.00\" CNY\""}, "EUR": {"description": "Euro", "numberFormat": "#,##0.00\" €\""}, "GBP": {"description": "British Pound Sterling", "numberFormat": "#,##0.00\" £\""}, "JPY": {"description": "Japanese Yen", "numberFormat": "#,##0.00\" JPY\""}, "USD": {"description": "United States Dollar", "numberFormat": "#,##0.00\" $\""}, "AUD": {"description": "Australian Dollar", "numberFormat": "#,##0.00\" A$\""}, "CAD": {"description": "Canadian Dollar", "numberFormat": "#,##0.00\" C$\""}, "SEK": {"description": "Swedish Krona", "numberFormat": "#,##0.00\" kr\""}, "KRW": {"description": "South Korean Won", "numberFormat": "#,##0.00\" ₩\""}, "MXN": {"description": "Mexican Peso", "numberFormat": "#,##0.00\" Mex$\""}, "INR": {"description": "Indian Rupee", "numberFormat": "#,##0.00\" ₹\""}, "RUB": {"description": "Russian Ruble", "numberFormat": "#,##0.00\" ₽\""}, "TRY": {"description": "Turkish Lira", "numberFormat": "#,##0.00\" ₺\""}, "BRL": {"description": "Brazilian Real", "numberFormat": "#,##0.00\" R$\""}, "TWD": {"description": "New Taiwan Dollar", "numberFormat": "#,##0.00\" NT$\""}, "DKK": {"description": "Danish Krone", "numberFormat": "#,##0.00\" Kr\""}, "PLN": {"description": "Polish Zloty", "numberFormat": "#,##0.00\" zł\""}, "IDR": {"description": "Indonesian Rupiah", "numberFormat": "#,##0.00\" Rp\""}, "HUF": {"description": "Hungarian Forint", "numberFormat": "#,##0.00\" Ft\""}, "CZK": {"description": "Czech Koruna", "numberFormat": "#,##0.00\" Kč\""}}}, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "daysPerWeek", "type": "<PERSON><PERSON>", "unit": null, "value": 6.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"input": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "WEEK", "type": "TIME", "behavior": "WRITE_THROUGH"}}, {"name": "depreciationCostPerPartMachine", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "designation", "type": "Text", "unit": null, "value": null, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"input": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "developmentCosts", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "developmentCostsIfInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "developmentCostsInHouseSubcalculationsOfNoStep", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "developmentCostsInHouseSubcalculationsOfSteps", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "developmentCostsThis", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "dimension", "type": "Dimension", "unit": null, "value": "NUMBER", "valueInDefaultUnit": null, "source": "C", "metaInfo": {"input": true, "readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "directMaterialCostConsumable", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "directMaterialCostConsumableOfInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "directMaterialCostConsumableOfStepLessInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "directMaterialCostConsumableOfStepLessThis", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "directMaterialCostConsumableThis", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "directMaterialCostElectronicComponent", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "directMaterialCostElectronicComponentOfInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "directMaterialCostElectronicComponentOfStepLessInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "directMaterialCostElectronicComponentOfStepLessThis", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "directMaterialCostElectronicComponentThis", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "directMaterialCostPurchaseParts", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "directMaterialCostPurchasePartsOfInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "directMaterialCostPurchasePartsOfStepLessInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "directMaterialCostPurchasePartsOfStepLessThis", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "directMaterialCostPurchasePartsThis", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "directMaterialCostRawMaterial", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "directMaterialCostRawMaterialOfInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "directMaterialCostRawMaterialOfStepLessInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "directMaterialCostRawMaterialOfStepLessThis", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "directMaterialCostRawMaterialThis", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "directMaterialCosts", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "displayDesignation", "type": "Text", "unit": null, "value": "Display-Designation-Initial-Value", "valueInDefaultUnit": null, "source": "C", "metaInfo": {"input": true, "objectView": "ALL", "fieldIndex": 0, "mode": "cost", "translationSection": "*", "compositeMandatoryForEntity": {"context": {"ANY": {"value": true, "index": 0, "readOnly": false, "section": "default", "refresh": false, "computed": false, "showSystemValue": false}, "CREATE_FROM_MASTERDATA": {"value": true, "index": 0, "readOnly": true, "section": "default", "refresh": false, "computed": false, "showSystemValue": false}}}}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "elcoPurchaseVolume", "type": "ElcoPurchaseVolume", "unit": null, "value": "HIGH", "valueInDefaultUnit": null, "source": "C", "metaInfo": {"input": true, "condition": {"field": "hasElcoParts", "value": "true", "operator": "eq"}}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "entityDesignation", "type": "Text", "unit": null, "value": "ManualManufacturing", "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "environment", "type": "Text", "unit": null, "value": "bp<PERSON><PERSON>", "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "exchangeRates", "type": "ExchangeRatesField", "unit": null, "value": {"CHF": 1.089642, "CNY": 7.774285, "EUR": 1.0, "GBP": 0.859495, "JPY": 133.272021, "USD": 1.216595, "AUD": 1.46, "CAD": 1.38, "SEK": 10.33, "KRW": 1345.02, "MXN": 22.09, "INR": 83.93, "RUB": 144.77, "TRY": 16.34, "BRL": 5.32, "TWD": 31.54, "DKK": 7.44, "PLN": 4.74, "IDR": 15798.27, "HUF": 374.54, "CZK": 24.62}, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"input": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "exchangeRatesDataCreation", "type": "ManufacturingEntity", "unit": null, "value": true, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "extraShifts", "type": "<PERSON><PERSON>", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"input": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "fixedCostPerPartMachine", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "fixedCostPerPartSetup", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "hasElcoParts", "type": "Bool", "unit": null, "value": false, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "interestCostPerPartMachine", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "interestOnMaterialStockCostConsumableOfInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "interestOnMaterialStockCostConsumableOfInHouseStepLess", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "interestOnMaterialStockCostElectronicComponentOfInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "interestOnMaterialStockCostElectronicComponentOfInHouseStepLess", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "interestOnMaterialStockCostPurchasePartsOfInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "interestOnMaterialStockCostPurchasePartsOfInHouseStepLess", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "interestOnMaterialStockCostRawMaterialOfInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "interestOnMaterialStockCostRawMaterialOfInHouseStepLess", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "interestOnWorkInProgressStep", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "interestOnWorkInProgressSubManufacturing", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "internalCallsPerYear", "type": "<PERSON><PERSON>", "unit": null, "value": 12.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "YEAR", "type": "TIME", "behavior": "WRITE_THROUGH"}}, {"name": "isPart", "type": "Bool", "unit": null, "value": true, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "isPartModelConverted", "type": "Bool", "unit": null, "value": false, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "key", "type": "Text", "unit": null, "value": "NEWPROJ", "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "lifeTime", "type": "TimeInYears", "unit": "YEAR", "value": 0.0, "valueInDefaultUnit": 0.0, "source": "C", "metaInfo": {"input": true, "defaultUnit": {"unit": "YEAR", "isFixed": false}}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "maintenanceCostPerPartTool", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "manufacturingCostUnit", "type": "Text", "unit": null, "value": "PIECE", "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "manufacturingCosts1", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "manufacturingCosts1OfInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "manufacturingCosts1OfStepLessInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "manufacturingCosts1Step", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "manufacturingCosts2", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "manufacturingCosts2OfInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "manufacturingCosts2OfStepLessInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "manufacturingCosts2Step", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "manufacturingCosts2SubManufacturing", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"deprecated": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "manufacturingCosts3", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "manufacturingCosts3OfInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "manufacturingCosts3OfStepLessInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "manufacturingCosts3Step", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "manufacturingDimension", "type": "Dimension", "unit": null, "value": "NUMBER", "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "manufacturingQuantityUnit", "type": "Text", "unit": null, "value": "PIECE", "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "manufacturingScrapBaseOfInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "manufacturingScrapBaseOfStepLessInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "manufacturingType", "type": "ManufacturingType", "unit": null, "value": "INHOUSE", "valueInDefaultUnit": null, "source": "C", "metaInfo": {"input": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "masterDataType", "type": "Text", "unit": null, "value": "NONE", "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true, "translationSection": "masterData", "masterdataCalculation": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "masterDataTypeInternal", "type": "Text", "unit": null, "value": "NONE", "valueInDefaultUnit": null, "source": "C", "metaInfo": {"masterdataCalculation": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "materialBaseCurrency", "type": "<PERSON><PERSON><PERSON><PERSON>", "unit": null, "value": null, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"input": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "material<PERSON>abel", "type": "Text", "unit": null, "value": null, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"translationSection": "materials"}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "materialName", "type": "Text", "unit": null, "value": null, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"labelField": "material<PERSON>abel", "useLabelAsKey": false, "wizardField": {"step": "WizardWamFieldStep", "index": 2}}, "systemValue": null, "label": "NULL", "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "materialOverheadConsumableOfInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "materialOverheadConsumableOfInHouseStepLess", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "materialOverheadElectronicComponentOfInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "materialOverheadElectronicComponentOfInHouseStepLess", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "materialOverheadPurchasePartsOfInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "materialOverheadPurchasePartsOfInHouseStepLess", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "materialOverheadRawMaterialOfInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "materialOverheadRawMaterialOfInHouseStepLess", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "materialPriceOverride", "type": "Money", "unit": null, "value": null, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"input": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "materialScrapCosts", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "materialScrapCostsConsumable", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "materialScrapCostsElectronicComponent", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "materialScrapCostsPurchaseParts", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "materialScrapCostsRawMaterials", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "netWeightPerPart", "type": "QuantityUnit", "unit": null, "value": null, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true, "input": true, "defaultUnit": {"unit": "PIECE", "isFixed": true}}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "netWeightPerPartForOverview", "type": "Weight", "unit": null, "value": null, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"input": true, "readOnly": true, "calculationPreview": true, "calculationPreviewIndex": 2, "defaultUnit": {"unit": "GRAM", "isFixed": false}, "summaryView": "PART", "summaryViewIndex": 200}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "netWeightPerPartForWizard", "type": "Weight", "unit": null, "value": null, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"input": true, "wizardField": {"step": "WizardWamFieldStep", "index": 1}, "defaultUnit": {"unit": "GRAM", "isFixed": false}}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "noStep", "type": "Bool", "unit": null, "value": true, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "number", "type": "Text", "unit": null, "value": "0", "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "overheadMethod", "type": "OverheadMethod", "unit": null, "value": "BUILD_TO_PRINT_AUTO", "valueInDefaultUnit": null, "source": "C", "metaInfo": {"input": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "packageCarrierCosts", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "packageCarrierCostsIfInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "packageCarrierCostsInHouseSubcalculationsOfNoStep", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "packageCarrierCostsInHouseSubcalculationsOfSteps", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "packageCarrierCostsThis", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "paidInvests", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "paidInvestsOfSubcalculations", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "paidInvestsOfSubcalculationsOfNoStep", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "paidInvestsOfSubcalculationsOfSteps", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "paidSpecialDirectCosts", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "paidTools", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "parentCurrenciesFromManufacturing", "type": "CurrenciesField", "unit": null, "value": null, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "parentExchangeRatesFromManufacturing", "type": "ExchangeRatesField", "unit": null, "value": null, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "partDataCreation", "type": "Part", "unit": null, "value": true, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "partDescription", "type": "Text", "unit": null, "value": "", "valueInDefaultUnit": null, "source": "C", "metaInfo": {"input": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "partDesignation", "type": "Text", "unit": null, "value": "WizardTestPart", "valueInDefaultUnit": null, "source": "C", "metaInfo": {"mandatory": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "partId", "type": "Text", "unit": null, "value": "64e7ce7b32c47367d6bbecc9", "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "partName", "type": "Text", "unit": null, "value": "", "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "partNumber", "type": "Text", "unit": null, "value": "123456", "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "peakVolumeOfStepsPerYear", "type": "Pieces", "unit": "PIECE", "value": 0.0, "valueInDefaultUnit": 0.0, "source": "C", "metaInfo": {"readOnly": true, "defaultUnit": {"unit": "PIECE", "isFixed": true}}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "YEAR", "type": "TIME", "behavior": "WRITE_THROUGH"}}, {"name": "procurementType", "type": "ManufacturingType", "unit": null, "value": "PURCHASE", "valueInDefaultUnit": null, "source": "C", "metaInfo": {"input": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "productionHoursPerYear", "type": "Time", "unit": "SECOND", "value": 25920000.0, "valueInDefaultUnit": 7200.0, "source": "C", "metaInfo": {"input": true, "defaultUnit": {"unit": "HOUR", "isFixed": false}, "readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "YEAR", "type": "TIME", "behavior": "WRITE_THROUGH"}}, {"name": "quantity", "type": "Pieces", "unit": "PIECE", "value": 1.0, "valueInDefaultUnit": 1.0, "source": "C", "metaInfo": {"defaultUnit": {"unit": "PIECE", "isFixed": true}}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "quantityUnit", "type": "Text", "unit": null, "value": "PIECE", "valueInDefaultUnit": null, "source": "C", "metaInfo": {"input": true, "path": "/api/type/units?dimension=Pieces", "remoteSearch": true, "asObject": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "rampUpCosts", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "rampUpCostsIfInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "rampUpCostsInHouseSubcalculationsOfNoStep", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "rampUpCostsInHouseSubcalculationsOfSteps", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "rampUpCostsThis", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "responsible", "type": "Text", "unit": null, "value": "N/A", "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "scrapBaseConsumable", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "scrapBaseConsumableOfStepLess", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "scrapBaseElectronicComponent", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "scrapBaseElectronicComponentOfStepLess", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "scrapBaseExternalProcess", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "scrapBaseExternalProcessOfStepLess", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "scrapBasePurchaseParts", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "scrapBasePurchasePartsOfStepLess", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "scrapBaseRawMaterial", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "scrapBaseRawMaterialOfStepLess", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "scrapCostConsumableOfStepLessInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "scrapCostElectronicComponentOfStepLessInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "scrapCostPurchasePartsOfStepLessInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "scrapCostRawMaterialOfStepLessInHouse", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "scrapRate", "type": "Rate", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "scrapRateForMaterialTable", "type": "Rate", "unit": null, "value": null, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "shapeId", "type": "Text", "unit": null, "value": null, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"input": true, "wizardField": {"step": "WizardShapeStep", "index": 0}}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "shapeTechnologyGroup", "type": "Text", "unit": null, "value": "none", "valueInDefaultUnit": null, "source": "C", "metaInfo": {"input": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "shiftModelConfiguration", "type": "NoCalcFieldResult", "unit": null, "value": "64e7ce7b32c47367d6bbecdc", "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "shiftModelConfigurationKey", "type": "ShiftModelConfigurationKey", "unit": null, "value": {"key": "3shiftModel", "instanceVersion": 0, "isTsetConfiguration": true}, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "shiftsPerDay", "type": "<PERSON><PERSON>", "unit": null, "value": 3.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"input": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "DAY", "type": "TIME", "behavior": "WRITE_THROUGH"}}, {"name": "sortIndex", "type": "Quantity", "unit": null, "value": null, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"input": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "specialDirectCosts", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "surfaceCostPerPartMachine", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "technologyKey", "type": "Text", "unit": null, "value": "", "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "technologyModel", "type": "Text", "unit": null, "value": "ManualManufacturing", "valueInDefaultUnit": null, "source": "C", "metaInfo": {"readOnly": true, "translationSection": "models"}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "timePerShift", "type": "Time", "unit": "SECOND", "value": 28800.0, "valueInDefaultUnit": 8.0, "source": "C", "metaInfo": {"input": true, "defaultUnit": {"unit": "HOUR", "isFixed": false}}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "userNotes", "type": "Text", "unit": null, "value": "", "valueInDefaultUnit": null, "source": "C", "metaInfo": {"optional": true, "input": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": null}, {"name": "variableCostPerPartMachine", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "variableCostPerPartSetup", "type": "Money", "unit": null, "value": 0.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {}, "systemValue": null, "label": null, "context": null, "currencyInfo": {"CHF": 0.0, "CNY": 0.0, "EUR": 0.0, "GBP": 0.0, "JPY": 0.0, "USD": 0.0, "AUD": 0.0, "CAD": 0.0, "SEK": 0.0, "KRW": 0.0, "MXN": 0.0, "INR": 0.0, "RUB": 0.0, "TRY": 0.0, "BRL": 0.0, "TWD": 0.0, "DKK": 0.0, "PLN": 0.0, "IDR": 0.0, "HUF": 0.0, "CZK": 0.0}, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "PIECE", "type": "PIECES", "behavior": "TRANSFORM"}}, {"name": "weeksPerYear", "type": "<PERSON><PERSON>", "unit": null, "value": 50.0, "valueInDefaultUnit": null, "source": "C", "metaInfo": {"input": true}, "systemValue": null, "label": null, "context": null, "currencyInfo": null, "systemValueCurrencyInfo": null, "currency": null, "denominatorUnit": {"unit": "YEAR", "type": "TIME", "behavior": "WRITE_THROUGH"}}], "children": [], "model": {"name": "Standard", "path": "", "entity": "ManualManufacturing", "displayState": "HIDDEN", "hasShape": true, "cardNames": ["TECH"]}, "createdByField": null, "createdByMocked": false, "createdOnBranch": null, "masterDataKey": null, "title": "ManualManufacturingTitle", "versionChanges": [], "deletable": true, "copyable": true}, "turningRequest": null}]