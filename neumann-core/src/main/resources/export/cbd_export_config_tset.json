{"filename": {"nameParts": ["@_PART_NAME", "@_PART_NUMBER", "_", "@_LAST_CHANGED_DATE"], "dateTimeFormat": "dd.M<PERSON>.yyyy_HH-mm"}, "helper": [{"sourceType": "Direct", "fieldName": "", "valueKey": "@_NOW"}], "images": [{"sourceType": "PartImage", "fieldName": "images", "valueKey": "@_PART_IMAGE"}, {"sourceType": "ShapeImage", "fieldName": "shapes", "valueKey": "@_SHAPE_IMAGE"}], "header": [{"sourceType": "BomNodeSnapshot", "fieldName": "path", "valueKey": "@_PATH"}, {"sourceType": "Part", "fieldName": "designation", "valueKey": "@_PART_NAME"}, {"sourceType": "Part", "fieldName": "number", "valueKey": "@_PART_NUMBER"}, {"sourceType": "BomNodeSnapshot", "fieldName": "title", "valueKey": "@_CALCULATION_TITLE"}, {"sourceType": "Manufacturing", "fieldName": "costPerPart", "valueKey": "@_COST_PER_PART"}, {"sourceType": "BomNode", "fieldName": "status", "valueKey": "@_STATUS"}, {"sourceType": "Manufacturing", "fieldName": "totalCurrentCosts", "valueKey": "@_CURRENT"}, {"sourceType": "Manufacturing", "fieldName": "totalTargetCosts", "valueKey": "@_TARGET"}, {"sourceType": "Manufacturing", "fieldName": "targetGapToCostPerPart", "valueKey": "@_GAP_CALCULATED_TARGET"}, {"sourceType": "Manufacturing", "fieldName": "currentGapToCostPerPart", "valueKey": "@_GAP_CURRENT_CALCULATED"}, {"sourceType": "Manufacturing", "fieldName": "currentGapToTarget", "valueKey": "@_GAP_CURRENT_TARGET"}], "part": [{"sourceType": "Manufacturing", "fieldName": "technologyModel", "valueKey": "@_TECHNOLOGY"}, {"sourceType": "Manufacturing", "fieldName": "material<PERSON>abel", "valueKey": "@_MATERIAL_NAME"}, {"sourceType": "Manufacturing", "fieldName": "netWeightPerPartForWizard", "valueKey": "@_NET_WEIGHT_PER_PART"}, {"sourceType": "Part", "fieldName": "partDimensions", "valueKey": "@_DIMENSIONS"}, {"sourceType": "Manufacturing", "fieldName": "shapeId", "valueKey": "@_SHAPE_NAME"}], "environment": [{"sourceType": "Manufacturing", "fieldName": "locationName", "valueKey": "@_LOCATION"}, {"sourceType": "Manufacturing", "fieldName": "customProcurementTypeName", "valueKey": "@_PROCUREMENT"}, {"sourceType": "Manufacturing", "fieldName": "lifeTime", "valueKey": "@_LIFETIME"}, {"sourceType": "Manufacturing", "fieldName": "averageUsableProductionVolumePerYear", "valueKey": "@_AVG_VOLUME"}, {"sourceType": "Manufacturing", "fieldName": "averageVolumeOverLifeTime", "valueKey": "@_TOTAL_VOLUME"}, {"sourceType": "Manufacturing", "fieldName": "productionHoursPerYear", "valueKey": "@_PRODUCTION_TIME_PER_YEAR"}, {"sourceType": "Manufacturing", "fieldName": "shiftsPerDay", "valueKey": "@_SHIFTS_PER_DAY"}, {"sourceType": "Manufacturing", "fieldName": "callsPerYear", "valueKey": "@_LOTS_PER_YEAR"}, {"sourceType": "Manufacturing", "fieldName": "peakLotSize", "valueKey": "@_LOT_SIZE"}], "footer": [{"sourceType": "BomNodeSnapshot", "fieldName": "createdDate", "valueKey": "@_CREATED_DATE"}, {"sourceType": "BomNodeSnapshot", "fieldName": "created<PERSON>y", "valueKey": "@_CREATED_PERSON"}, {"sourceType": "BomNodeSnapshot", "fieldName": "lastModifiedDate", "valueKey": "@_LAST_CHANGED_DATE"}, {"sourceType": "BomNodeSnapshot", "fieldName": "lastModifiedBy", "valueKey": "@_LAST_CHANGED_PERSON"}], "overheads": [{"fieldName": "#Cost_SoldMaterial_Total_SalesAndGeneralAdministrationCosts", "valueKey": "@_OVERHEADS_SALES_COSTS", "percentageKey": "%_OVERHEADS_SALES_COSTS"}, {"fieldName": "#Cost_SoldMaterial_Total_ResearchAndDevelopmentCosts", "valueKey": "@_OVERHEADS_RD_COSTS", "percentageKey": "%_OVERHEADS_RD_COSTS"}, {"fieldName": "#Cost_SoldMaterial_Total_BusinessRiskCosts", "valueKey": "@_OVERHEADS_RISK_COSTS", "percentageKey": "%_OVERHEADS_RISK_COSTS"}, {"fieldName": "#Cost_SoldMaterial_Total_InterestOnFinishProductStock", "valueKey": "@_OVERHEADS_STOCK_INTEREST", "percentageKey": "%_OVERHEADS_STOCK_INTEREST"}, {"fieldName": "#Cost_SoldMaterial_Total_OtherExpendituresAfterPC", "valueKey": "@_OVERHEADS_AFTER_PROD_EXP", "percentageKey": "%_OVERHEADS_AFTER_PROD_EXP"}, {"fieldName": "#Cost_SoldMaterial_Total_DirectOverheadsAfterPC", "valueKey": "@_OVERHEADS_AFTER_PROD_OVERHEADS", "percentageKey": "%_OVERHEADS_AFTER_PROD_OVERHEADS"}, {"fieldName": "#Cost_SoldMaterial_Total_Profit", "valueKey": "@_OVERHEADS_PROFIT", "percentageKey": "%_OVERHEADS_PROFIT"}, {"fieldName": "#Cost_SoldMaterial_Total_InterestForTermsOfPayment", "valueKey": "@_OVERHEADS_TOP_INTEREST", "percentageKey": "%_OVERHEADS_TOP_INTEREST"}, {"fieldName": "#Cost_SoldMaterial_Total_Discount", "valueKey": "@_OVERHEADS_DISCOUNT", "percentageKey": "%_OVERHEADS_DISCOUNT"}, {"fieldName": "#Cost_SoldMaterial_Total_TransportCosts", "valueKey": "@_OVERHEADS_TRANSPORT_COSTS", "percentageKey": "%_OVERHEADS_TRANSPORT_COSTS"}, {"fieldName": "#Cost_SoldMaterial_Total_CustomsDuty", "valueKey": "@_OVERHEADS_CUSTOMS_DUTY", "percentageKey": "%_OVERHEADS_CUSTOMS_DUTY"}], "cbd": {"meta": {"unit": "EUR", "sheetName": "SUMMARY"}, "totalValue": [{"entityType": "MANUFACTURING", "selector": "FIELD", "select": "costPerPart"}], "rows": [{"valueKey": "$_DIRECTLY_PAID_COSTS", "percentageKey": "", "useDynamicDenominator": false, "value": [{"entityType": "MANUFACTURING_STEP", "selector": "FIELD", "select": "directlyPaidCostPerPartTool"}, {"entityType": "TOOL", "selector": "FIELD", "select": "directlyPaidCostPerPartTool"}, {"entityType": "SPECIAL_DIRECT_COST", "selector": "FIELD", "select": "paidInvest"}]}, {"valueKey": "$_TOTAL", "percentageKey": "%_TOTAL", "value": [{"entityType": "MANUFACTURING", "selector": "FIELD", "select": "costPerPart"}]}, {"valueKey": "$_MATERIAL", "percentageKey": "%_MATERIAL", "value": [{"entityType": "MANUFACTURING", "selector": "FIELD", "select": "materialCosts"}]}, {"valueKey": "$_DIRECT_MATERIAL_COSTS", "percentageKey": "%_DIRECT_MATERIAL_COSTS", "value": [{"entityType": "MANUFACTURING", "selector": "FIELD", "select": "directMaterialCosts"}]}, {"valueKey": "$_MATERIAL_SCRAP_COSTS", "percentageKey": "%_MATERIAL_SCRAP_COSTS", "value": [{"entityType": "MANUFACTURING", "selector": "FIELD", "select": "materialScrapCosts"}]}, {"valueKey": "$_MATERIAL_OVERHEAD_COSTS", "percentageKey": "%_MATERIAL_OVERHEAD_COSTS", "value": [{"entityType": "MANUFACTURING", "selector": "FIELD", "select": "materialOverheadCosts"}]}, {"valueKey": "$_INTEREST_ON_MATERIAL_STOCK", "percentageKey": "%_INTEREST_ON_MATERIAL_STOCK", "value": [{"entityType": "MANUFACTURING", "selector": "FIELD", "select": "interestOnMaterialStock"}]}, {"valueKey": "$_MANUFACTURING", "percentageKey": "%_MANUFACTURING", "value": [{"entityType": "MANUFACTURING", "selector": "FIELD", "select": "manufacturingCosts3"}]}, {"valueKey": "$_STEPS", "percentageKey": "%_STEPS", "value": [{"entityType": "MANUFACTURING", "selector": "FIELD", "select": "manufacturingCosts2Step"}]}, {"valueKey": "$_SUBPARTS", "percentageKey": "%_SUBPARTS", "value": [{"entityType": "MANUFACTURING", "selector": "FIELD", "select": "manufacturingCosts2SubManufacturing"}]}, {"valueKey": "$_INTEREST_ON_WORK_IN_PROGRESS", "percentageKey": "%_INTEREST_ON_WORK_IN_PROGRESS", "value": [{"entityType": "MANUFACTURING", "selector": "FIELD", "select": "costPerPartInterestOnWorkInProgress"}]}, {"valueKey": "$_INVESTMENTS", "percentageKey": "%_INVESTMENTS", "value": [{"entityType": "MANUFACTURING", "selector": "FIELD", "select": "specialDirectCosts"}]}, {"valueKey": "$_DEVELOPMENT_COSTS", "percentageKey": "%_DEVELOPMENT_COSTS", "value": [{"entityType": "MANUFACTURING", "selector": "FIELD", "select": "developmentCosts"}]}, {"valueKey": "$_RAMP_UP_COSTS", "percentageKey": "%_RAMP_UP_COSTS", "value": [{"entityType": "MANUFACTURING", "selector": "FIELD", "select": "rampUpCosts"}]}, {"valueKey": "$_PACKAGE_CARRIER_COSTS", "percentageKey": "%_PACKAGE_CARRIER_COSTS", "value": [{"entityType": "MANUFACTURING", "selector": "FIELD", "select": "packageCarrierCosts"}]}, {"valueKey": "$_OVERHEADS", "percentageKey": "%_OVERHEADS", "value": [{"entityType": "MANUFACTURING", "selector": "FIELD", "select": "overheadCosts"}]}, {"valueKey": "$_OVERHEADS_AFTER_PRODUCTION", "percentageKey": "%_OVERHEADS_AFTER_PRODUCTION", "value": [{"entityType": "MANUFACTURING", "selector": "FIELD", "select": "#Cost_SoldMaterial_Total_OverheadsAfterPC"}]}, {"valueKey": "$_PROFIT", "percentageKey": "%_PROFIT", "value": [{"entityType": "MANUFACTURING", "selector": "FIELD", "select": "#Cost_SoldMaterial_Total_Profit"}]}, {"valueKey": "$_TERMS_OF_PAYMENT", "percentageKey": "%_TERMS_OF_PAYMENT", "value": [{"entityType": "MANUFACTURING", "selector": "FIELD", "select": "#Cost_SoldMaterial_Total_TermsOfPayment"}]}, {"valueKey": "$_INCOTERMS", "percentageKey": "%_INCOTERMS", "value": [{"entityType": "MANUFACTURING", "selector": "FIELD", "select": "#Cost_SoldMaterial_Total_IncoTerms"}]}]}, "sheets": {"Subparts": {"entityType": "BOM_ENTRY", "selector": "SUBPART", "fields": [{"sourceType": "Manufacturing", "fieldName": "partDesignation", "valueKey": "_designation", "columnTitle": "Designation"}, {"sourceType": "Manufacturing", "fieldName": "partNumber", "valueKey": "_part_number", "columnTitle": "Part Number"}, {"sourceType": "BomEntry", "fieldName": "purchasedQuantity", "valueKey": "_quantity", "columnTitle": "Quantity"}, {"sourceType": "Manufacturing", "fieldName": "costPerPart", "valueKey": "_currency_per_unit", "columnTitle": "@currency / unit"}, {"sourceType": "Manufacturing", "fieldName": "customProcurementTypeName", "valueKey": "_procurement", "columnTitle": "Procurement"}, {"sourceType": "Manufacturing", "fieldName": "locationName", "valueKey": "_location", "columnTitle": "Location"}]}, "Materials": {"entityType": "MATERIAL", "fields": [{"sourceType": "Manufacturing", "fieldName": "displayDesignation", "valueKey": "_designation", "columnTitle": "Designation"}, {"sourceType": "Manufacturing", "fieldName": "costPerPart", "valueKey": "_currency_per_pcs", "columnTitle": "@currency / pcs"}, {"sourceType": "TotalCostPct", "fieldName": "costPerPart", "valueKey": "_cost_per_part_pct", "columnTitle": "% cost per part"}, {"sourceType": "ParentStep", "fieldName": "displayDesignation", "valueKey": "_step", "columnTitle": "Step"}, {"sourceType": "Manufacturing", "fieldName": "purchasedQuantity", "valueKey": "_quantity", "columnTitle": "Quantity", "alternativeFieldNames": ["quantity"]}, {"sourceType": "Manufacturing", "fieldName": "pricePerUnit", "valueKey": "_currency_per_unit", "columnTitle": "@currency / unit"}, {"sourceType": "Manufacturing", "fieldName": "reuseOfScrap", "valueKey": "_scrap_reuse", "columnTitle": "Reuse of scrap"}, {"sourceType": "Manufacturing", "fieldName": "masterDataType", "valueKey": "_material_type", "columnTitle": "Material type"}, {"sourceType": "Manufacturing", "fieldName": "pricePerUnit", "valueKey": "_material_purchase_price_per_kilo", "columnTitle": "Material purchase price per kilo"}, {"sourceType": "Manufacturing", "fieldName": "materialRecyclingPrice", "valueKey": "_material_recycling_price_per_kilo", "columnTitle": "Material recycling price per kilo"}, {"sourceType": "Manufacturing", "fieldName": "materialWastePrice", "valueKey": "_material_waste_price", "columnTitle": "Material waste price"}, {"sourceType": "Manufacturing", "fieldName": "materialCostMode", "valueKey": "_material_cost_mode", "columnTitle": "Cost mode"}, {"sourceType": "Manufacturing", "fieldName": "purchasedWeightPerPart", "valueKey": "_weight__purchased_weight_per_part", "columnTitle": "Purchased weight per part (weight)"}, {"sourceType": "Manufacturing", "fieldName": "netWeightPerPart", "valueKey": "_weight_net_weight_per_part", "columnTitle": "Net weight per part (weight)"}, {"sourceType": "Manufacturing", "fieldName": "irretrievableScrapPerPart", "valueKey": "_weight_irretrievable_scrap_per_part", "columnTitle": "Irretrievable scrap per part (weight)"}, {"sourceType": "Manufacturing", "fieldName": "retrievableScrapPerPart", "valueKey": "_weight_retrievable_scrap_per_part", "columnTitle": "Retrievable scrap per part (weight)"}, {"sourceType": "Manufacturing", "fieldName": "recycledRetrievableScrapPerPart", "valueKey": "_weight_recycled_retrievable_scrap_per_part", "columnTitle": "Recycled retrievable scrap per part (weight)"}, {"sourceType": "Manufacturing", "fieldName": "selledIrretrievableScrapPerPart", "valueKey": "_weight_sold_irretrievable_scrap_per_part", "columnTitle": "Sold irretrievable scrap per part (weight)"}, {"sourceType": "Manufacturing", "fieldName": "purchasedWeightCosts", "valueKey": "_cost_purchased_weight_per_part", "columnTitle": "Purchased weight per part (cost)"}, {"sourceType": "Manufacturing", "fieldName": "netWeightCosts", "valueKey": "_cost_net_weight_per_part", "columnTitle": "Net weight per part (cost)"}, {"sourceType": "Manufacturing", "fieldName": "irretrievableScrapCosts", "valueKey": "_cost_irretrievable_scrap_per_part", "columnTitle": "Irretrievable scrap per part (cost)"}, {"sourceType": "Manufacturing", "fieldName": "retrievableScrapCosts", "valueKey": "_cost_retrievable_scrap_per_part", "columnTitle": "Retrievable scrap per part (cost)"}, {"sourceType": "Manufacturing", "fieldName": "recycledRetrievableScrapCosts", "valueKey": "_cost_recycled_retrievable_scrap_per_part", "columnTitle": "Recycled retrievable scrap per part (cost)"}, {"sourceType": "Manufacturing", "fieldName": "selledIrretrievableScrapCosts", "valueKey": "_cost_sold_irretrievable_scrap_per_part", "columnTitle": "Sold irretrievable scrap per part (cost)"}]}, "Consumables": {"entityType": "CONSUMABLE", "fields": [{"sourceType": "Manufacturing", "fieldName": "displayDesignation", "valueKey": "_designation", "columnTitle": "Designation"}, {"sourceType": "Manufacturing", "fieldName": "costPerPart", "valueKey": "_currency_per_pcs", "columnTitle": "@currency / pcs"}, {"sourceType": "TotalCostPct", "fieldName": "costPerPart", "valueKey": "_cost_per_part_pct", "columnTitle": "% cost per part"}, {"sourceType": "ParentStep", "fieldName": "displayDesignation", "valueKey": "_step", "columnTitle": "Step"}, {"sourceType": "Manufacturing", "fieldName": "purchasedQuantity", "valueKey": "_quantity", "columnTitle": "Quantity"}, {"sourceType": "Manufacturing", "fieldName": "pricePerUnit", "valueKey": "_currency_per_unit", "columnTitle": "@currency / unit"}, {"sourceType": "Manufacturing", "fieldName": "reuseOfScrap", "valueKey": "_scrap_reuse", "columnTitle": "Reuse of scrap"}, {"sourceType": "Manufacturing", "fieldName": "unit", "valueKey": "_unit", "columnTitle": "Unit"}]}, "Electronic components": {"entityType": "C_PART", "fields": [{"sourceType": "Manufacturing", "fieldName": "mpn", "valueKey": "_mpn", "columnTitle": "MPN"}, {"sourceType": "Manufacturing", "fieldName": "displayDesignation", "valueKey": "_designation", "columnTitle": "Designation"}, {"sourceType": "Manufacturing", "fieldName": "pricePerUnit", "valueKey": "_currency_per_unit", "columnTitle": "@currency / unit"}, {"sourceType": "ParentStep", "fieldName": "displayDesignation", "valueKey": "_step", "columnTitle": "Step"}, {"sourceType": "Manufacturing", "fieldName": "purchasedQuantity", "valueKey": "_quantity", "columnTitle": "Quantity"}, {"sourceType": "Manufacturing", "fieldName": "mountingType", "valueKey": "_mountingType", "columnTitle": "Mounting type"}, {"sourceType": "Manufacturing", "fieldName": "manufacturer", "valueKey": "_manufacturer", "columnTitle": "Manufacturer"}, {"sourceType": "Manufacturing", "fieldName": "reuseOfScrap", "valueKey": "_reuseOfScrap", "columnTitle": "Reuse of manufacturing scrap"}]}, "Steps": {"entityType": "MANUFACTURING_STEP", "fields": [{"sourceType": "Manufacturing", "fieldName": "displayDesignation", "valueKey": "_designation", "columnTitle": "Designation"}, {"sourceType": "Manufacturing", "fieldName": "cycleTime", "valueKey": "_cycle_time", "columnTitle": "Cycle time"}, {"sourceType": "Manufacturing", "fieldName": "manufacturingCosts2Step", "valueKey": "_currency_per_pcs", "columnTitle": "@currency / pcs"}, {"sourceType": "TotalCostPct", "fieldName": "manufacturingCosts2Step", "valueKey": "_cost_per_part_pct", "columnTitle": "% cost per part"}, {"sourceType": "Manufacturing", "fieldName": "costPerPartMachine", "valueKey": "_system", "columnTitle": "System"}, {"sourceType": "Manufacturing", "fieldName": "costPerPartSetup", "valueKey": "_setup", "columnTitle": "Setup"}, {"sourceType": "Manufacturing", "fieldName": "costPerPartLabor", "valueKey": "_labor", "columnTitle": "Labor"}, {"sourceType": "Manufacturing", "fieldName": "maintenanceCostPerPartTool", "valueKey": "_tool_maintenance", "columnTitle": "Tool maintenance"}, {"sourceType": "Manufacturing", "fieldName": "costPerPartRMOC", "valueKey": "_rmoc", "columnTitle": "RMOC"}, {"sourceType": "Manufacturing", "fieldName": "costPerPartManufacturingScrap", "valueKey": "_scrap", "columnTitle": "Scrap"}, {"sourceType": "Manufacturing", "fieldName": "allocationInterestCostPerPartTool", "valueKey": "_allocated_tool_costs", "columnTitle": "Allocated tool costs"}, {"sourceType": "Manufacturing", "fieldName": "partsPerCycle", "valueKey": "_parts_per_cycle", "columnTitle": "Parts per cycle"}, {"sourceType": "Manufacturing", "fieldName": "templateName", "valueKey": "_configuration", "columnTitle": "Configuration"}, {"sourceType": "Manufacturing", "fieldName": "utilizationRate", "valueKey": "_utilization_rate", "columnTitle": "Utilization rate"}, {"sourceType": "Manufacturing", "fieldName": "manufacturingScrapRate", "valueKey": "_manufacturing_scrap_rate", "columnTitle": "Manufacturing scrap rate"}, {"sourceType": "Manufacturing", "fieldName": "lotFraction", "valueKey": "_lot_fraction", "columnTitle": "Lot fraction"}, {"sourceType": "Manufacturing", "fieldName": "peakLotSize", "valueKey": "_lot_size", "columnTitle": "Lot size"}, {"sourceType": "Manufacturing", "fieldName": "peakUsableLotSize", "valueKey": "_usable_parts", "columnTitle": "Usable parts"}]}, "Tools": {"entityType": "TOOL", "fields": [{"sourceType": "Manufacturing", "fieldName": "displayDesignation", "valueKey": "_designation", "columnTitle": "Designation"}, {"sourceType": "ParentStep", "fieldName": "displayDesignation", "valueKey": "_step", "columnTitle": "Step"}, {"sourceType": "Manufacturing", "fieldName": "investPerTool", "valueKey": "_invest_per_tool", "columnTitle": "Invest per tool"}, {"sourceType": "Manufacturing", "fieldName": "conceptCost", "valueKey": "_concept", "columnTitle": "Concept"}, {"sourceType": "Manufacturing", "fieldName": "proportionalInvest", "valueKey": "_proportional_invest", "columnTitle": "Proportional invest"}, {"sourceType": "Manufacturing", "fieldName": "toolAllocationMode", "valueKey": "_tool_allocation_mode", "columnTitle": "Tool allocation mode"}, {"sourceType": "Manufacturing", "fieldName": "directlyPaidQuantityPerPart", "valueKey": "_directly_paid_quantity", "columnTitle": "Directly paid quantity"}, {"sourceType": "Manufacturing", "fieldName": "directlyPaidInvest", "valueKey": "_directly_paid_costs", "columnTitle": "Directly paid costs"}]}, "Investments": {"entityType": "SPECIAL_DIRECT_COST", "fields": [{"sourceType": "Manufacturing", "fieldName": "displayDesignation", "valueKey": "_designation", "columnTitle": "Designation"}, {"sourceType": "Manufacturing", "fieldName": "specialDirectCostType", "valueKey": "_special_direct_cost_type", "columnTitle": "Investment type"}, {"sourceType": "Manufacturing", "fieldName": "invest", "valueKey": "_invest", "columnTitle": "Investment per unit"}, {"sourceType": "Manufacturing", "fieldName": "quantity", "valueKey": "_quantity", "columnTitle": "Quantity"}, {"sourceType": "Manufacturing", "fieldName": "totalInvest", "valueKey": "_total_invest", "columnTitle": "Investment"}, {"sourceType": "Manufacturing", "fieldName": "allocationRate", "valueKey": "_allocation_rate", "columnTitle": "Allocation rate"}, {"sourceType": "Manufacturing", "fieldName": "allocatedInvest", "valueKey": "_allocated_invest", "columnTitle": "Allocated investment"}, {"sourceType": "Manufacturing", "fieldName": "paidQuantity", "valueKey": "_paid_quantity", "columnTitle": "Directly paid quantity"}, {"sourceType": "Manufacturing", "fieldName": "paidInvest", "valueKey": "_paid_invest", "columnTitle": "Directly paid investment"}]}}}