type: object
properties:
  type:
    type: string
    description: The type of the created object, can also be queried via the `/entities` endpoint.
    example: MANUFACTURING
  name:
    type: string
    description: The name of the created object, can be an arbitrary chosen.
    example: ManualManufacturing
  manufacturingType:
    type: string
    description: The specific type of the created object.
    example: ManualManufacturing
  part:
    type: string
    description: The name of the part that will be created on creation. Only needed for manufacturing creation.
    example: My Part
  inputs:
    type: array
    description: The input values that were set for initial creation.
    items:
      type: object
      properties:
        name:
          type: string
          description: The name of the field
          example: location
        value:
          type: string
          description: The actual value of the field
          example: Austria
        type:
          type: string
          description: The type of the field
          example: Text
        unit:
          type: string
          description: If the type is a type that requires a unit, it is stated in this field
          example: YEARS
      required:
        - name
        - value
        - type
  overrides:
    type: array
    description: The overrides that should be made to the initial values. The items don't contain a source, as all of them are of type `I`.
    items:
      type: object
      properties:
        name:
          type: string
          description: The name of the field
          example: location
        value:
          type: string
          description: The actual value of the field
          example: Germany
        type:
          type: string
          description: The type of the field
          example: Text
        unit:
          type: string
          description: If the type is a type that requires a unit, it is stated in this field (optional).
          example: YEARS
      required:
        - name
        - value
        - type
  children:
    type: array
    description: The children are again of the same type as this element is
    items:
      type: object

  dynamicFields:
    description: The dynamically added fields of the calculation. Used for modularization of entites
    readOnly: TRUE
    type: array
    items:
      type: object
      properties:
        name:
          type: string
          description: The name of the field
          example: location
        isolated:
          type: boolean
        model:
          type: object
        dynamicFields:
          type: array

