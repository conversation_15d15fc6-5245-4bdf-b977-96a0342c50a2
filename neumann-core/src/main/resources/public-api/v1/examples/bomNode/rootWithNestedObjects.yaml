description: This example shows the payload for adding a BomNode with already a nested Sub-BomNode included.
value:
  - partId: 61924dde1b7b84680b4b7794
    calculation:
      type: MANUAL_CALCULATION
      fields:
        - name: calculationTitle
          value: Main
        - name: baseCurrency
          value: EUR
        - name: lifeTime
          value: 10
          unit: YEARS
        - name: peakUsableProductionVolumePerYear
          value: 100000
          unit: PIECES
        - name: averageUsableProductionVolumePerYear
          value: 100000
          unit: PIECES
        - name: location
          value: Austria
        - name: procurementType
          value: PURCHASE
      children:
        - entityType: MANUFACTURING_STEP
          masterDataKey:
            type: MANUFACTURING_STEP
            key: c73f266d-6a4d-4cb3-aed4-2e1ffaf2d2ff
          currency: EUR
          fields:
            - name: displayDesignation
              value: Newly added manufacturing step
            - name: templateName
              value: c520e063-cbe9-4965-8283-ee66c31aecd5
        - entityType: MATERIAL
          currency: EUR
          fields:
            - name: stepId
              value: 61aa7ee1850dac14d0c72e55
            - name: displayDesignation
              value: Newly added Material
            - name: costMode
              value: BUY
            - ...
