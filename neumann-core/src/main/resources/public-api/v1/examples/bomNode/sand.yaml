description: Body for creation of a sand casting calculation
value:
  {
    "type": "MANUFACTURING",
    "name": "ManufacturingSandCasting",
    "manufacturingType": "ManufacturingSandCasting",
    "part": {
        "designation": "Sandcasting",
        "number": "Sandcasting"
    },
    "inputs": [
      {
        "name": "location",
        "value": "Germany",
        "type": "Text"
      },
      {
        "name": "lifeTime",
        "value": 10,
        "type": "TimeInYears",
        "unit": "YEAR"
      },
      {
        "name": "baseCurrency",
        "value": "EUR",
        "type": "Currency"
      },
      {
        "name": "procurementType",
        "value": "PURCHASE",
        "type": "ManufacturingType"
      },
      {
        "name": "peakUsableProductionVolumePerYear",
        "value": 1000,
        "type": "Pieces"
      },
      {
        "name": "averageUsableProductionVolumePerYear",
        "value": 1000,
        "type": "Pieces"
      },
      {
        "name": "calculationDate",
        "value": "2024-01-01",
        "type": "Date"
      },
      {
        "name": "netWeightPerPart",
        "value": 0.1,
        "type": "Weight",
        "unit": "KILOGRAMM"
      },
      {
        "name": "materialName",
        "value": "GJS-400",
        "type": "Text"
      },
      {
        "name": "shapeId",
        "value": "S_206",
        "type": "Text"
      },
      {
        "name": "burrFree",
        "value": "FALSE",
        "type": "SelectableBoolean"
      },
      {
        "name": "hasCore",
        "value": "FALSE",
        "type": "SelectableBoolean"
      },
      {
        "name": "maxWallThickness",
        "value": 0.001,
        "type": "Length",
        "unit": "METER"
      },
      {
        "name": "cleaningNeeded",
        "value": "FALSE",
        "type": "SelectableBoolean"
      },
      {
        "name": "machining",
        "value": "NONE",
        "type": "MachiningForTechnologyNoMilling"
      },
      {
        "name": "margin",
        "value": 0.003,
        "type": "Length",
        "unit": "METER"
      }
    ]
  }
