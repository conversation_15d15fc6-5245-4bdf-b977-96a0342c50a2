get:
  parameters:
    - $ref: '../parameters/path/projectKey.yaml'
    - $ref: '../parameters/path/bomNodeId.yaml'
    - $ref: '../parameters/query/variantId.yaml'
    - $ref: '../parameters/query/recursive.yaml'
  summary: Get a specific BomNode manufacturing
  operationId: get-projects-projectKey-bomNodes-bomNodeId
  description: Returns a specific BomNode manufacturing from the system. Can be either manufacturing from a root BomNode or a Sub-BomNode.
  responses:
    '200':
      content:
        application/json:
          schema:
            $ref: '../schemata/bomnode/getBomnode.yaml'
          examples:
            "Node with recursive=false":
              $ref: '../examples/bomNode/getLevel1.yaml'
            "Node with recursive=true":
              $ref: '../examples/bomNode/getLevel2.yaml'
  tags:
    - bomnodes
