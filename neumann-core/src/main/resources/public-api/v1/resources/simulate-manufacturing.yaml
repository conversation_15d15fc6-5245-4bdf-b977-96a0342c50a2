post:
  summary: Simulates a calculation creation.
  operationId: post-simulate-manufacturing
  description: Simulates a calculation creation, meaning no database stores are happening. For a root calculation without children calculations, it yields the same^1 result as post-projects-projectKey-bomNodes, but returns the manufacturing directly instead of a reference to it since it's only simulated. If children calculations are included in the request, the response is valid but unspecified. ^1(except for the fields "partDesignation", "partId", "partNumber" and "isPart")
  tags:
    - simulation
    - bomnodes
  requestBody:
    content:
      application/json:
        schema:
          $ref: '../schemata/bomnode/object.yaml'
        examples:
          "Sand Casting":
            $ref: '../examples/bomNode/sand.yaml'
          "Precision Casting":
            $ref: '../examples/bomNode/precision.yaml'
  responses:
    '200':
      content:
        application/json:
          schema:
            $ref: '../schemata/bomnode/getBomnode.yaml'
