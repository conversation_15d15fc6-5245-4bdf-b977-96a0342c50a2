delete:
  parameters:
    - $ref: "../parameters/path/mdKey.yaml"
    - $ref: "../parameters/query/typeOptional.yaml"
  summary: Deletes a masterdata record
  operationId: delete-masterdata-mdkey
  description: Either deletes a manually entered masterdata record or deletes an override of a tset record.<br>
    If you don't supply a type via the type request parameter, multiple masterdata records with the same key but different types could be deleted
  tags:
    - masterdata
get:
  parameters:
    - $ref: "../parameters/path/mdKey.yaml"
    - $ref: "../parameters/query/type.yaml"
  summary: Gets a masterdata record
  operationId: get-masterdata-mdkey
  description: Returns a new masterdata record from the system.
  tags:
    - masterdata
