post:
  parameters:
    - $ref: '../parameters/path/projectKey.yaml'
  summary: Add Root BomNode
  operationId: post-projects-projectKey-bomNodes
  description: Adds a new root BomNode to the project. This can be either a "standalone" BomNode or an already nested structure of BomNodes.
  tags:
    - bomnodes
  requestBody:
    content:
      application/json:
        schema:
          $ref: '../schemata/bomnode/object.yaml'
        examples:
          "Empty manual manufacturing":
            $ref: '../examples/bomNode/manualManufacturing.yaml'
          "Empty manual manufacturing w/ override":
            $ref: '../examples/bomNode/manualManufacturingOverride.yaml'
          "Manual manufacturing w/ children":
            $ref: '../examples/bomNode/manualManufacturingChildren.yaml'
          "Manual manufacturing w/ sub calculations":
            $ref: '../examples/bomNode/subcalc.yaml'
          "Rough Manufacturing":
            $ref: '../examples/bomNode/rough.yaml'
          "Sand Casting":
            $ref: '../examples/bomNode/sand.yaml'
          "Precision Casting":
            $ref: '../examples/bomNode/precision.yaml'

  #          "BomNode without calculation module":
#            $ref: '../examples/bomNode/withoutCostModule.yaml'
#          "Rough estimate":
#            $ref: '../examples/bomNode/roughEstimate.yaml'
#          "Simple PrecisionCasting BomNode":
#            $ref: '../examples/bomNode/withoutSpecificSteps.yaml'
#          "Root Node with nested Sub-BomNode":
#            $ref: '../examples/bomNode/rootWithNestedBom.yaml'
#          "Root Node with nested objects":
#            $ref: '../examples/bomNode/rootWithNestedObjects.yaml'
  responses:
    '200':
      content:
        application/json:
          schema:
            type: object
            properties:
          examples:
            "Response with links":
              $ref: '../examples/bomNode/postResponse.yaml'
