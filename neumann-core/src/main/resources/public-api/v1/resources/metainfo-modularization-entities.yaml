get:
  summary: Get all technology objects for a specific technology.
  parameters:
    - $ref: "../parameters/path/technology.yaml"
  operationId: get-metainfo-modularization-entities
  description: Returns all technology objects from a specific calculation module.
  tags:
    - metainfo
  responses:
    '200':
      content:
        application/json:
          schema:
            type: array
            items:
              $ref: '../schemata/metainfo/metaInfoModularizedEntity.yaml'
      description: OK
