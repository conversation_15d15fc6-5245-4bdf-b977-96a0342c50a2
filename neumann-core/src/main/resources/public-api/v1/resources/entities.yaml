get:
  summary: Gets all available entities
  operationId: get-entities
  description: Returns a list of all entities that are existing in the system. This endpoint should be used to find out what can possibly be added to a calculation.
  parameters:
    - $ref: '../parameters/query/entityType.yaml'
  tags:
    - schema
  responses:
    '200':
      content:
        application/json:
          schema:
            $ref: ../schemata/schema/entities.yaml
          examples:
            All entities:
              $ref: ../examples/schema/entities.yaml
