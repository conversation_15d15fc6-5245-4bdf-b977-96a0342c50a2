TemplateName,ObjectType,ObjectName,<PERSON><PERSON><PERSON><PERSON><PERSON>,MasterDataType,FieldName,FieldValue,FieldType,FieldUnit,FieldName_1,FieldValue_1,FieldType_1,FieldUnit_1
Template_CeramicCoating_automated,Machine,Slurry tank and sanding system automated,Slurry tank and sanding system automated,,quantity,1.0000000000000,Pieces,PIECE,,,,
Template_CeramicCoating_automated,Machine,IRB 6650 - 125 / 3.2,IRB 6650 - 125 / 3.2,,quantity,1.0000000000000,Pieces,PIECE,,,,
Template_CeramicCoating_automated,Machine,Automation (wax clusters),Automation (wax clusters),,quantity,1.0000000000000,Pieces,PIECE,,,,
Template_CeramicCoating_automated,CeramicCoatingCycleTimeStep,Ceramic and sand coating,,,,,,,,,,
Template_CeramicCoating_automated,Labor,Machine operator,UNSKILLED_WORKER,,skillType,UNSKILLED_WORKER,Text,,requiredLabor,1.0000000000000,Num,
Template_CeramicCoating_automated,CeramicSlurry,Ceramic slurry,Ceramic slurry,,,,,,,,,
Template_CeramicCoating_automated,NewMoldingSandCera,New molding sand,New molding sand,,,,,,,,,
Template_CeramicCoating_manual,Machine,Slurry tank and sanding system manual,Slurry tank and sanding system manual,,quantity,1.0000000000000,Pieces,PIECE,,,,
Template_CeramicCoating_manual,CeramicCoatingCycleTimeStep,Ceramic and sand coating,,,,,,,,,,
Template_CeramicCoating_manual,Labor,Machine operator,UNSKILLED_WORKER,,skillType,UNSKILLED_WORKER,Text,,requiredLabor,2.5000000000000,Num,
Template_CeramicCoating_manual,CeramicSlurry,Ceramic slurry,Ceramic slurry,,,,,,,,,
Template_CeramicCoating_manual,NewMoldingSandCera,New molding sand,New molding sand,,,,,,,,,
