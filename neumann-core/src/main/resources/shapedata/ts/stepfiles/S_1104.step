ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ (''),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 
'G:/Shared drives/TIP/Validation/SMF KION/Tset SMF Shapes/S_1104.step',

/* time_stamp */ '2021-07-01T17:28:24+02:00',
/* author */ (''),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v18.1',
/* originating_system */ 'Autodesk Translation Framework v10.9.0.1377',

/* authorisation */ '');

FILE_SCHEMA (('AUTOMOTIVE_DESIGN { 1 0 10303 214 3 1 1 }'));
ENDSEC;

DATA;
#10=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#13),#1866);
#11=SHAPE_REPRESENTATION_RELATIONSHIP('SRR','None',#1873,#12);
#12=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#14),#1865);
#13=STYLED_ITEM('',(#1882),#14);
#14=MANIFOLD_SOLID_BREP('Body1',#1094);
#15=FACE_BOUND('',#211,.T.);
#16=FACE_BOUND('',#212,.T.);
#17=FACE_BOUND('',#213,.T.);
#18=FACE_BOUND('',#215,.T.);
#19=FACE_BOUND('',#216,.T.);
#20=FACE_BOUND('',#217,.T.);
#21=CIRCLE('',#1110,5.50000000000004);
#22=CIRCLE('',#1111,5.50000000000004);
#23=CIRCLE('',#1114,5.50000000000004);
#24=CIRCLE('',#1115,5.50000000000004);
#25=CIRCLE('',#1118,5.50000000000004);
#26=CIRCLE('',#1119,5.50000000000004);
#27=CIRCLE('',#1122,5.50000000000004);
#28=CIRCLE('',#1123,5.50000000000004);
#29=CIRCLE('',#1126,50.);
#30=CIRCLE('',#1127,50.);
#31=CIRCLE('',#1130,50.);
#32=CIRCLE('',#1131,50.);
#33=CIRCLE('',#1134,7.00000000000017);
#34=CIRCLE('',#1135,7.00000000000017);
#35=CIRCLE('',#1138,1.99999999999989);
#36=CIRCLE('',#1139,1.99999999999989);
#37=CIRCLE('',#1142,53.5);
#38=CIRCLE('',#1143,53.5);
#39=CIRCLE('',#1146,20.);
#40=CIRCLE('',#1147,20.);
#41=CIRCLE('',#1150,20.);
#42=CIRCLE('',#1151,20.);
#43=CIRCLE('',#1154,54.);
#44=CIRCLE('',#1155,54.);
#45=CIRCLE('',#1158,2.);
#46=CIRCLE('',#1159,2.);
#47=CIRCLE('',#1162,5.49999999999997);
#48=CIRCLE('',#1163,5.49999999999997);
#49=CIRCLE('',#1166,2.00000000000003);
#50=CIRCLE('',#1167,2.00000000000003);
#51=CIRCLE('',#1170,1.99999999999999);
#52=CIRCLE('',#1171,1.99999999999999);
#53=CIRCLE('',#1174,5.49999999999997);
#54=CIRCLE('',#1175,5.49999999999997);
#55=CIRCLE('',#1178,2.00000000000003);
#56=CIRCLE('',#1179,2.00000000000003);
#57=CIRCLE('',#1181,54.0000000000002);
#58=CIRCLE('',#1182,54.0000000000002);
#59=CIRCLE('',#1185,53.4999999999998);
#60=CIRCLE('',#1186,53.4999999999998);
#61=CIRCLE('',#1189,20.0000000000002);
#62=CIRCLE('',#1190,20.0000000000002);
#63=CIRCLE('',#1193,69.9999999999999);
#64=CIRCLE('',#1194,69.9999999999999);
#65=CIRCLE('',#1197,19.9999999999984);
#66=CIRCLE('',#1198,19.9999999999984);
#67=CIRCLE('',#1201,20.);
#68=CIRCLE('',#1202,20.);
#69=CIRCLE('',#1205,2.00000000000003);
#70=CIRCLE('',#1206,2.00000000000003);
#71=CIRCLE('',#1209,2.00000000000042);
#72=CIRCLE('',#1210,2.00000000000042);
#73=CIRCLE('',#1213,6.99999999999997);
#74=CIRCLE('',#1214,6.99999999999997);
#75=CYLINDRICAL_SURFACE('',#1109,5.50000000000004);
#76=CYLINDRICAL_SURFACE('',#1113,5.50000000000004);
#77=CYLINDRICAL_SURFACE('',#1117,5.50000000000004);
#78=CYLINDRICAL_SURFACE('',#1121,5.50000000000004);
#79=CYLINDRICAL_SURFACE('',#1125,50.);
#80=CYLINDRICAL_SURFACE('',#1129,50.);
#81=CYLINDRICAL_SURFACE('',#1133,7.00000000000017);
#82=CYLINDRICAL_SURFACE('',#1137,1.99999999999989);
#83=CYLINDRICAL_SURFACE('',#1141,53.5);
#84=CYLINDRICAL_SURFACE('',#1145,20.);
#85=CYLINDRICAL_SURFACE('',#1149,20.);
#86=CYLINDRICAL_SURFACE('',#1153,54.);
#87=CYLINDRICAL_SURFACE('',#1157,2.);
#88=CYLINDRICAL_SURFACE('',#1161,5.49999999999997);
#89=CYLINDRICAL_SURFACE('',#1165,2.00000000000003);
#90=CYLINDRICAL_SURFACE('',#1169,1.99999999999999);
#91=CYLINDRICAL_SURFACE('',#1173,5.49999999999997);
#92=CYLINDRICAL_SURFACE('',#1177,2.00000000000003);
#93=CYLINDRICAL_SURFACE('',#1180,54.0000000000002);
#94=CYLINDRICAL_SURFACE('',#1184,53.4999999999998);
#95=CYLINDRICAL_SURFACE('',#1188,20.0000000000002);
#96=CYLINDRICAL_SURFACE('',#1192,69.9999999999999);
#97=CYLINDRICAL_SURFACE('',#1196,19.9999999999984);
#98=CYLINDRICAL_SURFACE('',#1200,20.);
#99=CYLINDRICAL_SURFACE('',#1204,2.00000000000003);
#100=CYLINDRICAL_SURFACE('',#1208,2.00000000000042);
#101=CYLINDRICAL_SURFACE('',#1212,6.99999999999997);
#102=FACE_OUTER_BOUND('',#157,.T.);
#103=FACE_OUTER_BOUND('',#158,.T.);
#104=FACE_OUTER_BOUND('',#159,.T.);
#105=FACE_OUTER_BOUND('',#160,.T.);
#106=FACE_OUTER_BOUND('',#161,.T.);
#107=FACE_OUTER_BOUND('',#162,.T.);
#108=FACE_OUTER_BOUND('',#163,.T.);
#109=FACE_OUTER_BOUND('',#164,.T.);
#110=FACE_OUTER_BOUND('',#165,.T.);
#111=FACE_OUTER_BOUND('',#166,.T.);
#112=FACE_OUTER_BOUND('',#167,.T.);
#113=FACE_OUTER_BOUND('',#168,.T.);
#114=FACE_OUTER_BOUND('',#169,.T.);
#115=FACE_OUTER_BOUND('',#170,.T.);
#116=FACE_OUTER_BOUND('',#171,.T.);
#117=FACE_OUTER_BOUND('',#172,.T.);
#118=FACE_OUTER_BOUND('',#173,.T.);
#119=FACE_OUTER_BOUND('',#174,.T.);
#120=FACE_OUTER_BOUND('',#175,.T.);
#121=FACE_OUTER_BOUND('',#176,.T.);
#122=FACE_OUTER_BOUND('',#177,.T.);
#123=FACE_OUTER_BOUND('',#178,.T.);
#124=FACE_OUTER_BOUND('',#179,.T.);
#125=FACE_OUTER_BOUND('',#180,.T.);
#126=FACE_OUTER_BOUND('',#181,.T.);
#127=FACE_OUTER_BOUND('',#182,.T.);
#128=FACE_OUTER_BOUND('',#183,.T.);
#129=FACE_OUTER_BOUND('',#184,.T.);
#130=FACE_OUTER_BOUND('',#185,.T.);
#131=FACE_OUTER_BOUND('',#186,.T.);
#132=FACE_OUTER_BOUND('',#187,.T.);
#133=FACE_OUTER_BOUND('',#188,.T.);
#134=FACE_OUTER_BOUND('',#189,.T.);
#135=FACE_OUTER_BOUND('',#190,.T.);
#136=FACE_OUTER_BOUND('',#191,.T.);
#137=FACE_OUTER_BOUND('',#192,.T.);
#138=FACE_OUTER_BOUND('',#193,.T.);
#139=FACE_OUTER_BOUND('',#194,.T.);
#140=FACE_OUTER_BOUND('',#195,.T.);
#141=FACE_OUTER_BOUND('',#196,.T.);
#142=FACE_OUTER_BOUND('',#197,.T.);
#143=FACE_OUTER_BOUND('',#198,.T.);
#144=FACE_OUTER_BOUND('',#199,.T.);
#145=FACE_OUTER_BOUND('',#200,.T.);
#146=FACE_OUTER_BOUND('',#201,.T.);
#147=FACE_OUTER_BOUND('',#202,.T.);
#148=FACE_OUTER_BOUND('',#203,.T.);
#149=FACE_OUTER_BOUND('',#204,.T.);
#150=FACE_OUTER_BOUND('',#205,.T.);
#151=FACE_OUTER_BOUND('',#206,.T.);
#152=FACE_OUTER_BOUND('',#207,.T.);
#153=FACE_OUTER_BOUND('',#208,.T.);
#154=FACE_OUTER_BOUND('',#209,.T.);
#155=FACE_OUTER_BOUND('',#210,.T.);
#156=FACE_OUTER_BOUND('',#214,.T.);
#157=EDGE_LOOP('',(#693,#694,#695,#696));
#158=EDGE_LOOP('',(#697,#698,#699,#700));
#159=EDGE_LOOP('',(#701,#702,#703,#704));
#160=EDGE_LOOP('',(#705,#706,#707,#708));
#161=EDGE_LOOP('',(#709,#710,#711,#712));
#162=EDGE_LOOP('',(#713,#714,#715,#716));
#163=EDGE_LOOP('',(#717,#718,#719,#720));
#164=EDGE_LOOP('',(#721,#722,#723,#724));
#165=EDGE_LOOP('',(#725,#726,#727,#728));
#166=EDGE_LOOP('',(#729,#730,#731,#732));
#167=EDGE_LOOP('',(#733,#734,#735,#736));
#168=EDGE_LOOP('',(#737,#738,#739,#740));
#169=EDGE_LOOP('',(#741,#742,#743,#744));
#170=EDGE_LOOP('',(#745,#746,#747,#748));
#171=EDGE_LOOP('',(#749,#750,#751,#752));
#172=EDGE_LOOP('',(#753,#754,#755,#756));
#173=EDGE_LOOP('',(#757,#758,#759,#760));
#174=EDGE_LOOP('',(#761,#762,#763,#764));
#175=EDGE_LOOP('',(#765,#766,#767,#768));
#176=EDGE_LOOP('',(#769,#770,#771,#772));
#177=EDGE_LOOP('',(#773,#774,#775,#776));
#178=EDGE_LOOP('',(#777,#778,#779,#780));
#179=EDGE_LOOP('',(#781,#782,#783,#784));
#180=EDGE_LOOP('',(#785,#786,#787,#788));
#181=EDGE_LOOP('',(#789,#790,#791,#792));
#182=EDGE_LOOP('',(#793,#794,#795,#796));
#183=EDGE_LOOP('',(#797,#798,#799,#800));
#184=EDGE_LOOP('',(#801,#802,#803,#804));
#185=EDGE_LOOP('',(#805,#806,#807,#808));
#186=EDGE_LOOP('',(#809,#810,#811,#812));
#187=EDGE_LOOP('',(#813,#814,#815,#816));
#188=EDGE_LOOP('',(#817,#818,#819,#820));
#189=EDGE_LOOP('',(#821,#822,#823,#824));
#190=EDGE_LOOP('',(#825,#826,#827,#828));
#191=EDGE_LOOP('',(#829,#830,#831,#832));
#192=EDGE_LOOP('',(#833,#834,#835,#836));
#193=EDGE_LOOP('',(#837,#838,#839,#840));
#194=EDGE_LOOP('',(#841,#842,#843,#844));
#195=EDGE_LOOP('',(#845,#846,#847,#848));
#196=EDGE_LOOP('',(#849,#850,#851,#852));
#197=EDGE_LOOP('',(#853,#854,#855,#856));
#198=EDGE_LOOP('',(#857,#858,#859,#860));
#199=EDGE_LOOP('',(#861,#862,#863,#864));
#200=EDGE_LOOP('',(#865,#866,#867,#868));
#201=EDGE_LOOP('',(#869,#870,#871,#872));
#202=EDGE_LOOP('',(#873,#874,#875,#876));
#203=EDGE_LOOP('',(#877,#878,#879,#880));
#204=EDGE_LOOP('',(#881,#882,#883,#884));
#205=EDGE_LOOP('',(#885,#886,#887,#888));
#206=EDGE_LOOP('',(#889,#890,#891,#892));
#207=EDGE_LOOP('',(#893,#894,#895,#896));
#208=EDGE_LOOP('',(#897,#898,#899,#900));
#209=EDGE_LOOP('',(#901,#902,#903,#904));
#210=EDGE_LOOP('',(#905,#906,#907,#908,#909,#910,#911,#912,#913,#914,#915,
#916,#917,#918,#919,#920,#921,#922,#923,#924,#925,#926,#927,#928,#929,#930,
#931,#932,#933,#934,#935,#936,#937,#938,#939,#940,#941,#942,#943,#944,#945));
#211=EDGE_LOOP('',(#946,#947,#948,#949));
#212=EDGE_LOOP('',(#950,#951,#952,#953));
#213=EDGE_LOOP('',(#954,#955,#956,#957));
#214=EDGE_LOOP('',(#958,#959,#960,#961,#962,#963,#964,#965,#966,#967,#968,
#969,#970,#971,#972,#973,#974,#975,#976,#977,#978,#979,#980,#981,#982,#983,
#984,#985,#986,#987,#988,#989,#990,#991,#992,#993,#994,#995,#996,#997,#998));
#215=EDGE_LOOP('',(#999,#1000,#1001,#1002));
#216=EDGE_LOOP('',(#1003,#1004,#1005,#1006));
#217=EDGE_LOOP('',(#1007,#1008,#1009,#1010));
#218=LINE('',#1546,#323);
#219=LINE('',#1548,#324);
#220=LINE('',#1550,#325);
#221=LINE('',#1551,#326);
#222=LINE('',#1557,#327);
#223=LINE('',#1560,#328);
#224=LINE('',#1562,#329);
#225=LINE('',#1563,#330);
#226=LINE('',#1570,#331);
#227=LINE('',#1572,#332);
#228=LINE('',#1574,#333);
#229=LINE('',#1575,#334);
#230=LINE('',#1581,#335);
#231=LINE('',#1584,#336);
#232=LINE('',#1586,#337);
#233=LINE('',#1587,#338);
#234=LINE('',#1594,#339);
#235=LINE('',#1596,#340);
#236=LINE('',#1598,#341);
#237=LINE('',#1599,#342);
#238=LINE('',#1605,#343);
#239=LINE('',#1608,#344);
#240=LINE('',#1610,#345);
#241=LINE('',#1611,#346);
#242=LINE('',#1618,#347);
#243=LINE('',#1620,#348);
#244=LINE('',#1622,#349);
#245=LINE('',#1623,#350);
#246=LINE('',#1629,#351);
#247=LINE('',#1632,#352);
#248=LINE('',#1634,#353);
#249=LINE('',#1635,#354);
#250=LINE('',#1641,#355);
#251=LINE('',#1644,#356);
#252=LINE('',#1646,#357);
#253=LINE('',#1647,#358);
#254=LINE('',#1653,#359);
#255=LINE('',#1656,#360);
#256=LINE('',#1658,#361);
#257=LINE('',#1659,#362);
#258=LINE('',#1665,#363);
#259=LINE('',#1668,#364);
#260=LINE('',#1670,#365);
#261=LINE('',#1671,#366);
#262=LINE('',#1677,#367);
#263=LINE('',#1680,#368);
#264=LINE('',#1682,#369);
#265=LINE('',#1683,#370);
#266=LINE('',#1689,#371);
#267=LINE('',#1692,#372);
#268=LINE('',#1694,#373);
#269=LINE('',#1695,#374);
#270=LINE('',#1701,#375);
#271=LINE('',#1704,#376);
#272=LINE('',#1706,#377);
#273=LINE('',#1707,#378);
#274=LINE('',#1713,#379);
#275=LINE('',#1716,#380);
#276=LINE('',#1718,#381);
#277=LINE('',#1719,#382);
#278=LINE('',#1725,#383);
#279=LINE('',#1728,#384);
#280=LINE('',#1730,#385);
#281=LINE('',#1731,#386);
#282=LINE('',#1737,#387);
#283=LINE('',#1740,#388);
#284=LINE('',#1742,#389);
#285=LINE('',#1743,#390);
#286=LINE('',#1749,#391);
#287=LINE('',#1752,#392);
#288=LINE('',#1754,#393);
#289=LINE('',#1755,#394);
#290=LINE('',#1761,#395);
#291=LINE('',#1767,#396);
#292=LINE('',#1770,#397);
#293=LINE('',#1772,#398);
#294=LINE('',#1773,#399);
#295=LINE('',#1779,#400);
#296=LINE('',#1782,#401);
#297=LINE('',#1784,#402);
#298=LINE('',#1785,#403);
#299=LINE('',#1791,#404);
#300=LINE('',#1794,#405);
#301=LINE('',#1796,#406);
#302=LINE('',#1797,#407);
#303=LINE('',#1803,#408);
#304=LINE('',#1806,#409);
#305=LINE('',#1808,#410);
#306=LINE('',#1809,#411);
#307=LINE('',#1815,#412);
#308=LINE('',#1818,#413);
#309=LINE('',#1820,#414);
#310=LINE('',#1821,#415);
#311=LINE('',#1827,#416);
#312=LINE('',#1830,#417);
#313=LINE('',#1832,#418);
#314=LINE('',#1833,#419);
#315=LINE('',#1839,#420);
#316=LINE('',#1842,#421);
#317=LINE('',#1844,#422);
#318=LINE('',#1845,#423);
#319=LINE('',#1851,#424);
#320=LINE('',#1854,#425);
#321=LINE('',#1856,#426);
#322=LINE('',#1857,#427);
#323=VECTOR('',#1221,10.);
#324=VECTOR('',#1222,10.);
#325=VECTOR('',#1223,10.);
#326=VECTOR('',#1224,10.);
#327=VECTOR('',#1231,10.);
#328=VECTOR('',#1234,10.);
#329=VECTOR('',#1235,10.);
#330=VECTOR('',#1236,10.);
#331=VECTOR('',#1245,10.);
#332=VECTOR('',#1246,10.);
#333=VECTOR('',#1247,10.);
#334=VECTOR('',#1248,10.);
#335=VECTOR('',#1255,10.);
#336=VECTOR('',#1258,10.);
#337=VECTOR('',#1259,10.);
#338=VECTOR('',#1260,10.);
#339=VECTOR('',#1269,10.);
#340=VECTOR('',#1270,10.);
#341=VECTOR('',#1271,10.);
#342=VECTOR('',#1272,10.);
#343=VECTOR('',#1279,10.);
#344=VECTOR('',#1282,10.);
#345=VECTOR('',#1283,10.);
#346=VECTOR('',#1284,10.);
#347=VECTOR('',#1293,10.);
#348=VECTOR('',#1294,10.);
#349=VECTOR('',#1295,10.);
#350=VECTOR('',#1296,10.);
#351=VECTOR('',#1303,10.);
#352=VECTOR('',#1306,10.);
#353=VECTOR('',#1307,10.);
#354=VECTOR('',#1308,10.);
#355=VECTOR('',#1315,10.);
#356=VECTOR('',#1318,10.);
#357=VECTOR('',#1319,10.);
#358=VECTOR('',#1320,10.);
#359=VECTOR('',#1327,10.);
#360=VECTOR('',#1330,10.);
#361=VECTOR('',#1331,10.);
#362=VECTOR('',#1332,10.);
#363=VECTOR('',#1339,10.);
#364=VECTOR('',#1342,10.);
#365=VECTOR('',#1343,10.);
#366=VECTOR('',#1344,10.);
#367=VECTOR('',#1351,10.);
#368=VECTOR('',#1354,10.);
#369=VECTOR('',#1355,10.);
#370=VECTOR('',#1356,10.);
#371=VECTOR('',#1363,10.);
#372=VECTOR('',#1366,10.);
#373=VECTOR('',#1367,10.);
#374=VECTOR('',#1368,10.);
#375=VECTOR('',#1375,10.);
#376=VECTOR('',#1378,10.);
#377=VECTOR('',#1379,10.);
#378=VECTOR('',#1380,10.);
#379=VECTOR('',#1387,10.);
#380=VECTOR('',#1390,10.);
#381=VECTOR('',#1391,10.);
#382=VECTOR('',#1392,10.);
#383=VECTOR('',#1399,10.);
#384=VECTOR('',#1402,10.);
#385=VECTOR('',#1403,10.);
#386=VECTOR('',#1404,10.);
#387=VECTOR('',#1411,10.);
#388=VECTOR('',#1414,10.);
#389=VECTOR('',#1415,10.);
#390=VECTOR('',#1416,10.);
#391=VECTOR('',#1423,10.);
#392=VECTOR('',#1426,10.);
#393=VECTOR('',#1427,10.);
#394=VECTOR('',#1428,10.);
#395=VECTOR('',#1435,10.);
#396=VECTOR('',#1442,10.);
#397=VECTOR('',#1445,10.);
#398=VECTOR('',#1446,10.);
#399=VECTOR('',#1447,10.);
#400=VECTOR('',#1454,10.);
#401=VECTOR('',#1457,10.);
#402=VECTOR('',#1458,10.);
#403=VECTOR('',#1459,10.);
#404=VECTOR('',#1466,10.);
#405=VECTOR('',#1469,10.);
#406=VECTOR('',#1470,10.);
#407=VECTOR('',#1471,10.);
#408=VECTOR('',#1478,10.);
#409=VECTOR('',#1481,10.);
#410=VECTOR('',#1482,10.);
#411=VECTOR('',#1483,10.);
#412=VECTOR('',#1490,10.);
#413=VECTOR('',#1493,10.);
#414=VECTOR('',#1494,10.);
#415=VECTOR('',#1495,10.);
#416=VECTOR('',#1502,10.);
#417=VECTOR('',#1505,10.);
#418=VECTOR('',#1506,10.);
#419=VECTOR('',#1507,10.);
#420=VECTOR('',#1514,10.);
#421=VECTOR('',#1517,10.);
#422=VECTOR('',#1518,10.);
#423=VECTOR('',#1519,10.);
#424=VECTOR('',#1526,10.);
#425=VECTOR('',#1529,10.);
#426=VECTOR('',#1530,10.);
#427=VECTOR('',#1531,10.);
#428=VERTEX_POINT('',#1544);
#429=VERTEX_POINT('',#1545);
#430=VERTEX_POINT('',#1547);
#431=VERTEX_POINT('',#1549);
#432=VERTEX_POINT('',#1553);
#433=VERTEX_POINT('',#1555);
#434=VERTEX_POINT('',#1559);
#435=VERTEX_POINT('',#1561);
#436=VERTEX_POINT('',#1568);
#437=VERTEX_POINT('',#1569);
#438=VERTEX_POINT('',#1571);
#439=VERTEX_POINT('',#1573);
#440=VERTEX_POINT('',#1577);
#441=VERTEX_POINT('',#1579);
#442=VERTEX_POINT('',#1583);
#443=VERTEX_POINT('',#1585);
#444=VERTEX_POINT('',#1592);
#445=VERTEX_POINT('',#1593);
#446=VERTEX_POINT('',#1595);
#447=VERTEX_POINT('',#1597);
#448=VERTEX_POINT('',#1601);
#449=VERTEX_POINT('',#1603);
#450=VERTEX_POINT('',#1607);
#451=VERTEX_POINT('',#1609);
#452=VERTEX_POINT('',#1616);
#453=VERTEX_POINT('',#1617);
#454=VERTEX_POINT('',#1619);
#455=VERTEX_POINT('',#1621);
#456=VERTEX_POINT('',#1625);
#457=VERTEX_POINT('',#1627);
#458=VERTEX_POINT('',#1631);
#459=VERTEX_POINT('',#1633);
#460=VERTEX_POINT('',#1637);
#461=VERTEX_POINT('',#1639);
#462=VERTEX_POINT('',#1643);
#463=VERTEX_POINT('',#1645);
#464=VERTEX_POINT('',#1649);
#465=VERTEX_POINT('',#1651);
#466=VERTEX_POINT('',#1655);
#467=VERTEX_POINT('',#1657);
#468=VERTEX_POINT('',#1661);
#469=VERTEX_POINT('',#1663);
#470=VERTEX_POINT('',#1667);
#471=VERTEX_POINT('',#1669);
#472=VERTEX_POINT('',#1673);
#473=VERTEX_POINT('',#1675);
#474=VERTEX_POINT('',#1679);
#475=VERTEX_POINT('',#1681);
#476=VERTEX_POINT('',#1685);
#477=VERTEX_POINT('',#1687);
#478=VERTEX_POINT('',#1691);
#479=VERTEX_POINT('',#1693);
#480=VERTEX_POINT('',#1697);
#481=VERTEX_POINT('',#1699);
#482=VERTEX_POINT('',#1703);
#483=VERTEX_POINT('',#1705);
#484=VERTEX_POINT('',#1709);
#485=VERTEX_POINT('',#1711);
#486=VERTEX_POINT('',#1715);
#487=VERTEX_POINT('',#1717);
#488=VERTEX_POINT('',#1721);
#489=VERTEX_POINT('',#1723);
#490=VERTEX_POINT('',#1727);
#491=VERTEX_POINT('',#1729);
#492=VERTEX_POINT('',#1733);
#493=VERTEX_POINT('',#1735);
#494=VERTEX_POINT('',#1739);
#495=VERTEX_POINT('',#1741);
#496=VERTEX_POINT('',#1745);
#497=VERTEX_POINT('',#1747);
#498=VERTEX_POINT('',#1751);
#499=VERTEX_POINT('',#1753);
#500=VERTEX_POINT('',#1757);
#501=VERTEX_POINT('',#1759);
#502=VERTEX_POINT('',#1763);
#503=VERTEX_POINT('',#1765);
#504=VERTEX_POINT('',#1769);
#505=VERTEX_POINT('',#1771);
#506=VERTEX_POINT('',#1775);
#507=VERTEX_POINT('',#1777);
#508=VERTEX_POINT('',#1781);
#509=VERTEX_POINT('',#1783);
#510=VERTEX_POINT('',#1787);
#511=VERTEX_POINT('',#1789);
#512=VERTEX_POINT('',#1793);
#513=VERTEX_POINT('',#1795);
#514=VERTEX_POINT('',#1799);
#515=VERTEX_POINT('',#1801);
#516=VERTEX_POINT('',#1805);
#517=VERTEX_POINT('',#1807);
#518=VERTEX_POINT('',#1811);
#519=VERTEX_POINT('',#1813);
#520=VERTEX_POINT('',#1817);
#521=VERTEX_POINT('',#1819);
#522=VERTEX_POINT('',#1823);
#523=VERTEX_POINT('',#1825);
#524=VERTEX_POINT('',#1829);
#525=VERTEX_POINT('',#1831);
#526=VERTEX_POINT('',#1835);
#527=VERTEX_POINT('',#1837);
#528=VERTEX_POINT('',#1841);
#529=VERTEX_POINT('',#1843);
#530=VERTEX_POINT('',#1847);
#531=VERTEX_POINT('',#1849);
#532=VERTEX_POINT('',#1853);
#533=VERTEX_POINT('',#1855);
#534=EDGE_CURVE('',#428,#429,#218,.T.);
#535=EDGE_CURVE('',#428,#430,#219,.T.);
#536=EDGE_CURVE('',#431,#430,#220,.T.);
#537=EDGE_CURVE('',#429,#431,#221,.T.);
#538=EDGE_CURVE('',#429,#432,#21,.T.);
#539=EDGE_CURVE('',#433,#431,#22,.T.);
#540=EDGE_CURVE('',#432,#433,#222,.T.);
#541=EDGE_CURVE('',#432,#434,#223,.T.);
#542=EDGE_CURVE('',#435,#433,#224,.T.);
#543=EDGE_CURVE('',#434,#435,#225,.T.);
#544=EDGE_CURVE('',#434,#428,#23,.T.);
#545=EDGE_CURVE('',#430,#435,#24,.T.);
#546=EDGE_CURVE('',#436,#437,#226,.T.);
#547=EDGE_CURVE('',#436,#438,#227,.T.);
#548=EDGE_CURVE('',#439,#438,#228,.T.);
#549=EDGE_CURVE('',#437,#439,#229,.T.);
#550=EDGE_CURVE('',#437,#440,#25,.T.);
#551=EDGE_CURVE('',#441,#439,#26,.T.);
#552=EDGE_CURVE('',#440,#441,#230,.T.);
#553=EDGE_CURVE('',#440,#442,#231,.T.);
#554=EDGE_CURVE('',#443,#441,#232,.T.);
#555=EDGE_CURVE('',#442,#443,#233,.T.);
#556=EDGE_CURVE('',#442,#436,#27,.T.);
#557=EDGE_CURVE('',#438,#443,#28,.T.);
#558=EDGE_CURVE('',#444,#445,#234,.T.);
#559=EDGE_CURVE('',#444,#446,#235,.T.);
#560=EDGE_CURVE('',#447,#446,#236,.T.);
#561=EDGE_CURVE('',#445,#447,#237,.T.);
#562=EDGE_CURVE('',#445,#448,#29,.T.);
#563=EDGE_CURVE('',#449,#447,#30,.T.);
#564=EDGE_CURVE('',#448,#449,#238,.T.);
#565=EDGE_CURVE('',#448,#450,#239,.T.);
#566=EDGE_CURVE('',#451,#449,#240,.T.);
#567=EDGE_CURVE('',#450,#451,#241,.T.);
#568=EDGE_CURVE('',#450,#444,#31,.T.);
#569=EDGE_CURVE('',#446,#451,#32,.T.);
#570=EDGE_CURVE('',#452,#453,#242,.T.);
#571=EDGE_CURVE('',#453,#454,#243,.T.);
#572=EDGE_CURVE('',#455,#454,#244,.T.);
#573=EDGE_CURVE('',#452,#455,#245,.T.);
#574=EDGE_CURVE('',#452,#456,#33,.T.);
#575=EDGE_CURVE('',#457,#455,#34,.T.);
#576=EDGE_CURVE('',#456,#457,#246,.T.);
#577=EDGE_CURVE('',#456,#458,#247,.T.);
#578=EDGE_CURVE('',#459,#457,#248,.T.);
#579=EDGE_CURVE('',#458,#459,#249,.T.);
#580=EDGE_CURVE('',#460,#458,#35,.T.);
#581=EDGE_CURVE('',#461,#459,#36,.T.);
#582=EDGE_CURVE('',#460,#461,#250,.T.);
#583=EDGE_CURVE('',#460,#462,#251,.T.);
#584=EDGE_CURVE('',#463,#461,#252,.T.);
#585=EDGE_CURVE('',#462,#463,#253,.T.);
#586=EDGE_CURVE('',#462,#464,#37,.T.);
#587=EDGE_CURVE('',#465,#463,#38,.T.);
#588=EDGE_CURVE('',#464,#465,#254,.T.);
#589=EDGE_CURVE('',#464,#466,#255,.T.);
#590=EDGE_CURVE('',#467,#465,#256,.T.);
#591=EDGE_CURVE('',#466,#467,#257,.T.);
#592=EDGE_CURVE('',#468,#466,#39,.T.);
#593=EDGE_CURVE('',#469,#467,#40,.T.);
#594=EDGE_CURVE('',#468,#469,#258,.T.);
#595=EDGE_CURVE('',#470,#468,#259,.T.);
#596=EDGE_CURVE('',#471,#469,#260,.T.);
#597=EDGE_CURVE('',#470,#471,#261,.T.);
#598=EDGE_CURVE('',#470,#472,#41,.T.);
#599=EDGE_CURVE('',#473,#471,#42,.T.);
#600=EDGE_CURVE('',#472,#473,#262,.T.);
#601=EDGE_CURVE('',#472,#474,#263,.T.);
#602=EDGE_CURVE('',#475,#473,#264,.T.);
#603=EDGE_CURVE('',#474,#475,#265,.T.);
#604=EDGE_CURVE('',#474,#476,#43,.T.);
#605=EDGE_CURVE('',#477,#475,#44,.T.);
#606=EDGE_CURVE('',#476,#477,#266,.T.);
#607=EDGE_CURVE('',#476,#478,#267,.T.);
#608=EDGE_CURVE('',#479,#477,#268,.T.);
#609=EDGE_CURVE('',#478,#479,#269,.T.);
#610=EDGE_CURVE('',#478,#480,#45,.T.);
#611=EDGE_CURVE('',#481,#479,#46,.T.);
#612=EDGE_CURVE('',#480,#481,#270,.T.);
#613=EDGE_CURVE('',#482,#480,#271,.T.);
#614=EDGE_CURVE('',#483,#481,#272,.T.);
#615=EDGE_CURVE('',#482,#483,#273,.T.);
#616=EDGE_CURVE('',#484,#482,#47,.T.);
#617=EDGE_CURVE('',#485,#483,#48,.T.);
#618=EDGE_CURVE('',#484,#485,#274,.T.);
#619=EDGE_CURVE('',#484,#486,#275,.T.);
#620=EDGE_CURVE('',#487,#485,#276,.T.);
#621=EDGE_CURVE('',#486,#487,#277,.T.);
#622=EDGE_CURVE('',#486,#488,#49,.T.);
#623=EDGE_CURVE('',#489,#487,#50,.T.);
#624=EDGE_CURVE('',#488,#489,#278,.T.);
#625=EDGE_CURVE('',#488,#490,#279,.T.);
#626=EDGE_CURVE('',#491,#489,#280,.T.);
#627=EDGE_CURVE('',#490,#491,#281,.T.);
#628=EDGE_CURVE('',#490,#492,#51,.T.);
#629=EDGE_CURVE('',#493,#491,#52,.T.);
#630=EDGE_CURVE('',#492,#493,#282,.T.);
#631=EDGE_CURVE('',#494,#492,#283,.T.);
#632=EDGE_CURVE('',#495,#493,#284,.T.);
#633=EDGE_CURVE('',#494,#495,#285,.T.);
#634=EDGE_CURVE('',#496,#494,#53,.T.);
#635=EDGE_CURVE('',#497,#495,#54,.T.);
#636=EDGE_CURVE('',#496,#497,#286,.T.);
#637=EDGE_CURVE('',#496,#498,#287,.T.);
#638=EDGE_CURVE('',#499,#497,#288,.T.);
#639=EDGE_CURVE('',#498,#499,#289,.T.);
#640=EDGE_CURVE('',#498,#500,#55,.T.);
#641=EDGE_CURVE('',#501,#499,#56,.T.);
#642=EDGE_CURVE('',#500,#501,#290,.T.);
#643=EDGE_CURVE('',#500,#502,#57,.T.);
#644=EDGE_CURVE('',#503,#501,#58,.T.);
#645=EDGE_CURVE('',#502,#503,#291,.T.);
#646=EDGE_CURVE('',#502,#504,#292,.T.);
#647=EDGE_CURVE('',#505,#503,#293,.T.);
#648=EDGE_CURVE('',#504,#505,#294,.T.);
#649=EDGE_CURVE('',#504,#506,#59,.T.);
#650=EDGE_CURVE('',#507,#505,#60,.T.);
#651=EDGE_CURVE('',#506,#507,#295,.T.);
#652=EDGE_CURVE('',#508,#506,#296,.T.);
#653=EDGE_CURVE('',#509,#507,#297,.T.);
#654=EDGE_CURVE('',#508,#509,#298,.T.);
#655=EDGE_CURVE('',#510,#508,#61,.T.);
#656=EDGE_CURVE('',#511,#509,#62,.T.);
#657=EDGE_CURVE('',#510,#511,#299,.T.);
#658=EDGE_CURVE('',#510,#512,#300,.T.);
#659=EDGE_CURVE('',#513,#511,#301,.T.);
#660=EDGE_CURVE('',#512,#513,#302,.T.);
#661=EDGE_CURVE('',#512,#514,#63,.T.);
#662=EDGE_CURVE('',#515,#513,#64,.T.);
#663=EDGE_CURVE('',#514,#515,#303,.T.);
#664=EDGE_CURVE('',#516,#514,#304,.T.);
#665=EDGE_CURVE('',#517,#515,#305,.T.);
#666=EDGE_CURVE('',#516,#517,#306,.T.);
#667=EDGE_CURVE('',#516,#518,#65,.T.);
#668=EDGE_CURVE('',#519,#517,#66,.T.);
#669=EDGE_CURVE('',#518,#519,#307,.T.);
#670=EDGE_CURVE('',#518,#520,#308,.T.);
#671=EDGE_CURVE('',#521,#519,#309,.T.);
#672=EDGE_CURVE('',#520,#521,#310,.T.);
#673=EDGE_CURVE('',#522,#520,#67,.T.);
#674=EDGE_CURVE('',#523,#521,#68,.T.);
#675=EDGE_CURVE('',#522,#523,#311,.T.);
#676=EDGE_CURVE('',#524,#522,#312,.T.);
#677=EDGE_CURVE('',#525,#523,#313,.T.);
#678=EDGE_CURVE('',#524,#525,#314,.T.);
#679=EDGE_CURVE('',#524,#526,#69,.T.);
#680=EDGE_CURVE('',#527,#525,#70,.T.);
#681=EDGE_CURVE('',#526,#527,#315,.T.);
#682=EDGE_CURVE('',#526,#528,#316,.T.);
#683=EDGE_CURVE('',#529,#527,#317,.T.);
#684=EDGE_CURVE('',#528,#529,#318,.T.);
#685=EDGE_CURVE('',#530,#528,#71,.T.);
#686=EDGE_CURVE('',#531,#529,#72,.T.);
#687=EDGE_CURVE('',#530,#531,#319,.T.);
#688=EDGE_CURVE('',#532,#530,#320,.T.);
#689=EDGE_CURVE('',#533,#531,#321,.T.);
#690=EDGE_CURVE('',#532,#533,#322,.T.);
#691=EDGE_CURVE('',#532,#453,#73,.T.);
#692=EDGE_CURVE('',#454,#533,#74,.T.);
#693=ORIENTED_EDGE('',*,*,#534,.F.);
#694=ORIENTED_EDGE('',*,*,#535,.T.);
#695=ORIENTED_EDGE('',*,*,#536,.F.);
#696=ORIENTED_EDGE('',*,*,#537,.F.);
#697=ORIENTED_EDGE('',*,*,#538,.F.);
#698=ORIENTED_EDGE('',*,*,#537,.T.);
#699=ORIENTED_EDGE('',*,*,#539,.F.);
#700=ORIENTED_EDGE('',*,*,#540,.F.);
#701=ORIENTED_EDGE('',*,*,#541,.F.);
#702=ORIENTED_EDGE('',*,*,#540,.T.);
#703=ORIENTED_EDGE('',*,*,#542,.F.);
#704=ORIENTED_EDGE('',*,*,#543,.F.);
#705=ORIENTED_EDGE('',*,*,#544,.F.);
#706=ORIENTED_EDGE('',*,*,#543,.T.);
#707=ORIENTED_EDGE('',*,*,#545,.F.);
#708=ORIENTED_EDGE('',*,*,#535,.F.);
#709=ORIENTED_EDGE('',*,*,#546,.F.);
#710=ORIENTED_EDGE('',*,*,#547,.T.);
#711=ORIENTED_EDGE('',*,*,#548,.F.);
#712=ORIENTED_EDGE('',*,*,#549,.F.);
#713=ORIENTED_EDGE('',*,*,#550,.F.);
#714=ORIENTED_EDGE('',*,*,#549,.T.);
#715=ORIENTED_EDGE('',*,*,#551,.F.);
#716=ORIENTED_EDGE('',*,*,#552,.F.);
#717=ORIENTED_EDGE('',*,*,#553,.F.);
#718=ORIENTED_EDGE('',*,*,#552,.T.);
#719=ORIENTED_EDGE('',*,*,#554,.F.);
#720=ORIENTED_EDGE('',*,*,#555,.F.);
#721=ORIENTED_EDGE('',*,*,#556,.F.);
#722=ORIENTED_EDGE('',*,*,#555,.T.);
#723=ORIENTED_EDGE('',*,*,#557,.F.);
#724=ORIENTED_EDGE('',*,*,#547,.F.);
#725=ORIENTED_EDGE('',*,*,#558,.F.);
#726=ORIENTED_EDGE('',*,*,#559,.T.);
#727=ORIENTED_EDGE('',*,*,#560,.F.);
#728=ORIENTED_EDGE('',*,*,#561,.F.);
#729=ORIENTED_EDGE('',*,*,#562,.F.);
#730=ORIENTED_EDGE('',*,*,#561,.T.);
#731=ORIENTED_EDGE('',*,*,#563,.F.);
#732=ORIENTED_EDGE('',*,*,#564,.F.);
#733=ORIENTED_EDGE('',*,*,#565,.F.);
#734=ORIENTED_EDGE('',*,*,#564,.T.);
#735=ORIENTED_EDGE('',*,*,#566,.F.);
#736=ORIENTED_EDGE('',*,*,#567,.F.);
#737=ORIENTED_EDGE('',*,*,#568,.F.);
#738=ORIENTED_EDGE('',*,*,#567,.T.);
#739=ORIENTED_EDGE('',*,*,#569,.F.);
#740=ORIENTED_EDGE('',*,*,#559,.F.);
#741=ORIENTED_EDGE('',*,*,#570,.T.);
#742=ORIENTED_EDGE('',*,*,#571,.T.);
#743=ORIENTED_EDGE('',*,*,#572,.F.);
#744=ORIENTED_EDGE('',*,*,#573,.F.);
#745=ORIENTED_EDGE('',*,*,#574,.F.);
#746=ORIENTED_EDGE('',*,*,#573,.T.);
#747=ORIENTED_EDGE('',*,*,#575,.F.);
#748=ORIENTED_EDGE('',*,*,#576,.F.);
#749=ORIENTED_EDGE('',*,*,#577,.F.);
#750=ORIENTED_EDGE('',*,*,#576,.T.);
#751=ORIENTED_EDGE('',*,*,#578,.F.);
#752=ORIENTED_EDGE('',*,*,#579,.F.);
#753=ORIENTED_EDGE('',*,*,#580,.T.);
#754=ORIENTED_EDGE('',*,*,#579,.T.);
#755=ORIENTED_EDGE('',*,*,#581,.F.);
#756=ORIENTED_EDGE('',*,*,#582,.F.);
#757=ORIENTED_EDGE('',*,*,#583,.F.);
#758=ORIENTED_EDGE('',*,*,#582,.T.);
#759=ORIENTED_EDGE('',*,*,#584,.F.);
#760=ORIENTED_EDGE('',*,*,#585,.F.);
#761=ORIENTED_EDGE('',*,*,#586,.F.);
#762=ORIENTED_EDGE('',*,*,#585,.T.);
#763=ORIENTED_EDGE('',*,*,#587,.F.);
#764=ORIENTED_EDGE('',*,*,#588,.F.);
#765=ORIENTED_EDGE('',*,*,#589,.F.);
#766=ORIENTED_EDGE('',*,*,#588,.T.);
#767=ORIENTED_EDGE('',*,*,#590,.F.);
#768=ORIENTED_EDGE('',*,*,#591,.F.);
#769=ORIENTED_EDGE('',*,*,#592,.T.);
#770=ORIENTED_EDGE('',*,*,#591,.T.);
#771=ORIENTED_EDGE('',*,*,#593,.F.);
#772=ORIENTED_EDGE('',*,*,#594,.F.);
#773=ORIENTED_EDGE('',*,*,#595,.T.);
#774=ORIENTED_EDGE('',*,*,#594,.T.);
#775=ORIENTED_EDGE('',*,*,#596,.F.);
#776=ORIENTED_EDGE('',*,*,#597,.F.);
#777=ORIENTED_EDGE('',*,*,#598,.F.);
#778=ORIENTED_EDGE('',*,*,#597,.T.);
#779=ORIENTED_EDGE('',*,*,#599,.F.);
#780=ORIENTED_EDGE('',*,*,#600,.F.);
#781=ORIENTED_EDGE('',*,*,#601,.F.);
#782=ORIENTED_EDGE('',*,*,#600,.T.);
#783=ORIENTED_EDGE('',*,*,#602,.F.);
#784=ORIENTED_EDGE('',*,*,#603,.F.);
#785=ORIENTED_EDGE('',*,*,#604,.F.);
#786=ORIENTED_EDGE('',*,*,#603,.T.);
#787=ORIENTED_EDGE('',*,*,#605,.F.);
#788=ORIENTED_EDGE('',*,*,#606,.F.);
#789=ORIENTED_EDGE('',*,*,#607,.F.);
#790=ORIENTED_EDGE('',*,*,#606,.T.);
#791=ORIENTED_EDGE('',*,*,#608,.F.);
#792=ORIENTED_EDGE('',*,*,#609,.F.);
#793=ORIENTED_EDGE('',*,*,#610,.F.);
#794=ORIENTED_EDGE('',*,*,#609,.T.);
#795=ORIENTED_EDGE('',*,*,#611,.F.);
#796=ORIENTED_EDGE('',*,*,#612,.F.);
#797=ORIENTED_EDGE('',*,*,#613,.T.);
#798=ORIENTED_EDGE('',*,*,#612,.T.);
#799=ORIENTED_EDGE('',*,*,#614,.F.);
#800=ORIENTED_EDGE('',*,*,#615,.F.);
#801=ORIENTED_EDGE('',*,*,#616,.T.);
#802=ORIENTED_EDGE('',*,*,#615,.T.);
#803=ORIENTED_EDGE('',*,*,#617,.F.);
#804=ORIENTED_EDGE('',*,*,#618,.F.);
#805=ORIENTED_EDGE('',*,*,#619,.F.);
#806=ORIENTED_EDGE('',*,*,#618,.T.);
#807=ORIENTED_EDGE('',*,*,#620,.F.);
#808=ORIENTED_EDGE('',*,*,#621,.F.);
#809=ORIENTED_EDGE('',*,*,#622,.F.);
#810=ORIENTED_EDGE('',*,*,#621,.T.);
#811=ORIENTED_EDGE('',*,*,#623,.F.);
#812=ORIENTED_EDGE('',*,*,#624,.F.);
#813=ORIENTED_EDGE('',*,*,#625,.F.);
#814=ORIENTED_EDGE('',*,*,#624,.T.);
#815=ORIENTED_EDGE('',*,*,#626,.F.);
#816=ORIENTED_EDGE('',*,*,#627,.F.);
#817=ORIENTED_EDGE('',*,*,#628,.F.);
#818=ORIENTED_EDGE('',*,*,#627,.T.);
#819=ORIENTED_EDGE('',*,*,#629,.F.);
#820=ORIENTED_EDGE('',*,*,#630,.F.);
#821=ORIENTED_EDGE('',*,*,#631,.T.);
#822=ORIENTED_EDGE('',*,*,#630,.T.);
#823=ORIENTED_EDGE('',*,*,#632,.F.);
#824=ORIENTED_EDGE('',*,*,#633,.F.);
#825=ORIENTED_EDGE('',*,*,#634,.T.);
#826=ORIENTED_EDGE('',*,*,#633,.T.);
#827=ORIENTED_EDGE('',*,*,#635,.F.);
#828=ORIENTED_EDGE('',*,*,#636,.F.);
#829=ORIENTED_EDGE('',*,*,#637,.F.);
#830=ORIENTED_EDGE('',*,*,#636,.T.);
#831=ORIENTED_EDGE('',*,*,#638,.F.);
#832=ORIENTED_EDGE('',*,*,#639,.F.);
#833=ORIENTED_EDGE('',*,*,#640,.F.);
#834=ORIENTED_EDGE('',*,*,#639,.T.);
#835=ORIENTED_EDGE('',*,*,#641,.F.);
#836=ORIENTED_EDGE('',*,*,#642,.F.);
#837=ORIENTED_EDGE('',*,*,#643,.F.);
#838=ORIENTED_EDGE('',*,*,#642,.T.);
#839=ORIENTED_EDGE('',*,*,#644,.F.);
#840=ORIENTED_EDGE('',*,*,#645,.F.);
#841=ORIENTED_EDGE('',*,*,#646,.F.);
#842=ORIENTED_EDGE('',*,*,#645,.T.);
#843=ORIENTED_EDGE('',*,*,#647,.F.);
#844=ORIENTED_EDGE('',*,*,#648,.F.);
#845=ORIENTED_EDGE('',*,*,#649,.F.);
#846=ORIENTED_EDGE('',*,*,#648,.T.);
#847=ORIENTED_EDGE('',*,*,#650,.F.);
#848=ORIENTED_EDGE('',*,*,#651,.F.);
#849=ORIENTED_EDGE('',*,*,#652,.T.);
#850=ORIENTED_EDGE('',*,*,#651,.T.);
#851=ORIENTED_EDGE('',*,*,#653,.F.);
#852=ORIENTED_EDGE('',*,*,#654,.F.);
#853=ORIENTED_EDGE('',*,*,#655,.T.);
#854=ORIENTED_EDGE('',*,*,#654,.T.);
#855=ORIENTED_EDGE('',*,*,#656,.F.);
#856=ORIENTED_EDGE('',*,*,#657,.F.);
#857=ORIENTED_EDGE('',*,*,#658,.F.);
#858=ORIENTED_EDGE('',*,*,#657,.T.);
#859=ORIENTED_EDGE('',*,*,#659,.F.);
#860=ORIENTED_EDGE('',*,*,#660,.F.);
#861=ORIENTED_EDGE('',*,*,#661,.F.);
#862=ORIENTED_EDGE('',*,*,#660,.T.);
#863=ORIENTED_EDGE('',*,*,#662,.F.);
#864=ORIENTED_EDGE('',*,*,#663,.F.);
#865=ORIENTED_EDGE('',*,*,#664,.T.);
#866=ORIENTED_EDGE('',*,*,#663,.T.);
#867=ORIENTED_EDGE('',*,*,#665,.F.);
#868=ORIENTED_EDGE('',*,*,#666,.F.);
#869=ORIENTED_EDGE('',*,*,#667,.F.);
#870=ORIENTED_EDGE('',*,*,#666,.T.);
#871=ORIENTED_EDGE('',*,*,#668,.F.);
#872=ORIENTED_EDGE('',*,*,#669,.F.);
#873=ORIENTED_EDGE('',*,*,#670,.F.);
#874=ORIENTED_EDGE('',*,*,#669,.T.);
#875=ORIENTED_EDGE('',*,*,#671,.F.);
#876=ORIENTED_EDGE('',*,*,#672,.F.);
#877=ORIENTED_EDGE('',*,*,#673,.T.);
#878=ORIENTED_EDGE('',*,*,#672,.T.);
#879=ORIENTED_EDGE('',*,*,#674,.F.);
#880=ORIENTED_EDGE('',*,*,#675,.F.);
#881=ORIENTED_EDGE('',*,*,#676,.T.);
#882=ORIENTED_EDGE('',*,*,#675,.T.);
#883=ORIENTED_EDGE('',*,*,#677,.F.);
#884=ORIENTED_EDGE('',*,*,#678,.F.);
#885=ORIENTED_EDGE('',*,*,#679,.F.);
#886=ORIENTED_EDGE('',*,*,#678,.T.);
#887=ORIENTED_EDGE('',*,*,#680,.F.);
#888=ORIENTED_EDGE('',*,*,#681,.F.);
#889=ORIENTED_EDGE('',*,*,#682,.F.);
#890=ORIENTED_EDGE('',*,*,#681,.T.);
#891=ORIENTED_EDGE('',*,*,#683,.F.);
#892=ORIENTED_EDGE('',*,*,#684,.F.);
#893=ORIENTED_EDGE('',*,*,#685,.T.);
#894=ORIENTED_EDGE('',*,*,#684,.T.);
#895=ORIENTED_EDGE('',*,*,#686,.F.);
#896=ORIENTED_EDGE('',*,*,#687,.F.);
#897=ORIENTED_EDGE('',*,*,#688,.T.);
#898=ORIENTED_EDGE('',*,*,#687,.T.);
#899=ORIENTED_EDGE('',*,*,#689,.F.);
#900=ORIENTED_EDGE('',*,*,#690,.F.);
#901=ORIENTED_EDGE('',*,*,#691,.F.);
#902=ORIENTED_EDGE('',*,*,#690,.T.);
#903=ORIENTED_EDGE('',*,*,#692,.F.);
#904=ORIENTED_EDGE('',*,*,#571,.F.);
#905=ORIENTED_EDGE('',*,*,#691,.T.);
#906=ORIENTED_EDGE('',*,*,#570,.F.);
#907=ORIENTED_EDGE('',*,*,#574,.T.);
#908=ORIENTED_EDGE('',*,*,#577,.T.);
#909=ORIENTED_EDGE('',*,*,#580,.F.);
#910=ORIENTED_EDGE('',*,*,#583,.T.);
#911=ORIENTED_EDGE('',*,*,#586,.T.);
#912=ORIENTED_EDGE('',*,*,#589,.T.);
#913=ORIENTED_EDGE('',*,*,#592,.F.);
#914=ORIENTED_EDGE('',*,*,#595,.F.);
#915=ORIENTED_EDGE('',*,*,#598,.T.);
#916=ORIENTED_EDGE('',*,*,#601,.T.);
#917=ORIENTED_EDGE('',*,*,#604,.T.);
#918=ORIENTED_EDGE('',*,*,#607,.T.);
#919=ORIENTED_EDGE('',*,*,#610,.T.);
#920=ORIENTED_EDGE('',*,*,#613,.F.);
#921=ORIENTED_EDGE('',*,*,#616,.F.);
#922=ORIENTED_EDGE('',*,*,#619,.T.);
#923=ORIENTED_EDGE('',*,*,#622,.T.);
#924=ORIENTED_EDGE('',*,*,#625,.T.);
#925=ORIENTED_EDGE('',*,*,#628,.T.);
#926=ORIENTED_EDGE('',*,*,#631,.F.);
#927=ORIENTED_EDGE('',*,*,#634,.F.);
#928=ORIENTED_EDGE('',*,*,#637,.T.);
#929=ORIENTED_EDGE('',*,*,#640,.T.);
#930=ORIENTED_EDGE('',*,*,#643,.T.);
#931=ORIENTED_EDGE('',*,*,#646,.T.);
#932=ORIENTED_EDGE('',*,*,#649,.T.);
#933=ORIENTED_EDGE('',*,*,#652,.F.);
#934=ORIENTED_EDGE('',*,*,#655,.F.);
#935=ORIENTED_EDGE('',*,*,#658,.T.);
#936=ORIENTED_EDGE('',*,*,#661,.T.);
#937=ORIENTED_EDGE('',*,*,#664,.F.);
#938=ORIENTED_EDGE('',*,*,#667,.T.);
#939=ORIENTED_EDGE('',*,*,#670,.T.);
#940=ORIENTED_EDGE('',*,*,#673,.F.);
#941=ORIENTED_EDGE('',*,*,#676,.F.);
#942=ORIENTED_EDGE('',*,*,#679,.T.);
#943=ORIENTED_EDGE('',*,*,#682,.T.);
#944=ORIENTED_EDGE('',*,*,#685,.F.);
#945=ORIENTED_EDGE('',*,*,#688,.F.);
#946=ORIENTED_EDGE('',*,*,#568,.T.);
#947=ORIENTED_EDGE('',*,*,#558,.T.);
#948=ORIENTED_EDGE('',*,*,#562,.T.);
#949=ORIENTED_EDGE('',*,*,#565,.T.);
#950=ORIENTED_EDGE('',*,*,#556,.T.);
#951=ORIENTED_EDGE('',*,*,#546,.T.);
#952=ORIENTED_EDGE('',*,*,#550,.T.);
#953=ORIENTED_EDGE('',*,*,#553,.T.);
#954=ORIENTED_EDGE('',*,*,#544,.T.);
#955=ORIENTED_EDGE('',*,*,#534,.T.);
#956=ORIENTED_EDGE('',*,*,#538,.T.);
#957=ORIENTED_EDGE('',*,*,#541,.T.);
#958=ORIENTED_EDGE('',*,*,#692,.T.);
#959=ORIENTED_EDGE('',*,*,#689,.T.);
#960=ORIENTED_EDGE('',*,*,#686,.T.);
#961=ORIENTED_EDGE('',*,*,#683,.T.);
#962=ORIENTED_EDGE('',*,*,#680,.T.);
#963=ORIENTED_EDGE('',*,*,#677,.T.);
#964=ORIENTED_EDGE('',*,*,#674,.T.);
#965=ORIENTED_EDGE('',*,*,#671,.T.);
#966=ORIENTED_EDGE('',*,*,#668,.T.);
#967=ORIENTED_EDGE('',*,*,#665,.T.);
#968=ORIENTED_EDGE('',*,*,#662,.T.);
#969=ORIENTED_EDGE('',*,*,#659,.T.);
#970=ORIENTED_EDGE('',*,*,#656,.T.);
#971=ORIENTED_EDGE('',*,*,#653,.T.);
#972=ORIENTED_EDGE('',*,*,#650,.T.);
#973=ORIENTED_EDGE('',*,*,#647,.T.);
#974=ORIENTED_EDGE('',*,*,#644,.T.);
#975=ORIENTED_EDGE('',*,*,#641,.T.);
#976=ORIENTED_EDGE('',*,*,#638,.T.);
#977=ORIENTED_EDGE('',*,*,#635,.T.);
#978=ORIENTED_EDGE('',*,*,#632,.T.);
#979=ORIENTED_EDGE('',*,*,#629,.T.);
#980=ORIENTED_EDGE('',*,*,#626,.T.);
#981=ORIENTED_EDGE('',*,*,#623,.T.);
#982=ORIENTED_EDGE('',*,*,#620,.T.);
#983=ORIENTED_EDGE('',*,*,#617,.T.);
#984=ORIENTED_EDGE('',*,*,#614,.T.);
#985=ORIENTED_EDGE('',*,*,#611,.T.);
#986=ORIENTED_EDGE('',*,*,#608,.T.);
#987=ORIENTED_EDGE('',*,*,#605,.T.);
#988=ORIENTED_EDGE('',*,*,#602,.T.);
#989=ORIENTED_EDGE('',*,*,#599,.T.);
#990=ORIENTED_EDGE('',*,*,#596,.T.);
#991=ORIENTED_EDGE('',*,*,#593,.T.);
#992=ORIENTED_EDGE('',*,*,#590,.T.);
#993=ORIENTED_EDGE('',*,*,#587,.T.);
#994=ORIENTED_EDGE('',*,*,#584,.T.);
#995=ORIENTED_EDGE('',*,*,#581,.T.);
#996=ORIENTED_EDGE('',*,*,#578,.T.);
#997=ORIENTED_EDGE('',*,*,#575,.T.);
#998=ORIENTED_EDGE('',*,*,#572,.T.);
#999=ORIENTED_EDGE('',*,*,#569,.T.);
#1000=ORIENTED_EDGE('',*,*,#566,.T.);
#1001=ORIENTED_EDGE('',*,*,#563,.T.);
#1002=ORIENTED_EDGE('',*,*,#560,.T.);
#1003=ORIENTED_EDGE('',*,*,#557,.T.);
#1004=ORIENTED_EDGE('',*,*,#554,.T.);
#1005=ORIENTED_EDGE('',*,*,#551,.T.);
#1006=ORIENTED_EDGE('',*,*,#548,.T.);
#1007=ORIENTED_EDGE('',*,*,#545,.T.);
#1008=ORIENTED_EDGE('',*,*,#542,.T.);
#1009=ORIENTED_EDGE('',*,*,#539,.T.);
#1010=ORIENTED_EDGE('',*,*,#536,.T.);
#1011=PLANE('',#1108);
#1012=PLANE('',#1112);
#1013=PLANE('',#1116);
#1014=PLANE('',#1120);
#1015=PLANE('',#1124);
#1016=PLANE('',#1128);
#1017=PLANE('',#1132);
#1018=PLANE('',#1136);
#1019=PLANE('',#1140);
#1020=PLANE('',#1144);
#1021=PLANE('',#1148);
#1022=PLANE('',#1152);
#1023=PLANE('',#1156);
#1024=PLANE('',#1160);
#1025=PLANE('',#1164);
#1026=PLANE('',#1168);
#1027=PLANE('',#1172);
#1028=PLANE('',#1176);
#1029=PLANE('',#1183);
#1030=PLANE('',#1187);
#1031=PLANE('',#1191);
#1032=PLANE('',#1195);
#1033=PLANE('',#1199);
#1034=PLANE('',#1203);
#1035=PLANE('',#1207);
#1036=PLANE('',#1211);
#1037=PLANE('',#1215);
#1038=PLANE('',#1216);
#1039=ADVANCED_FACE('',(#102),#1011,.T.);
#1040=ADVANCED_FACE('',(#103),#75,.F.);
#1041=ADVANCED_FACE('',(#104),#1012,.T.);
#1042=ADVANCED_FACE('',(#105),#76,.F.);
#1043=ADVANCED_FACE('',(#106),#1013,.T.);
#1044=ADVANCED_FACE('',(#107),#77,.F.);
#1045=ADVANCED_FACE('',(#108),#1014,.T.);
#1046=ADVANCED_FACE('',(#109),#78,.F.);
#1047=ADVANCED_FACE('',(#110),#1015,.T.);
#1048=ADVANCED_FACE('',(#111),#79,.F.);
#1049=ADVANCED_FACE('',(#112),#1016,.T.);
#1050=ADVANCED_FACE('',(#113),#80,.F.);
#1051=ADVANCED_FACE('',(#114),#1017,.T.);
#1052=ADVANCED_FACE('',(#115),#81,.T.);
#1053=ADVANCED_FACE('',(#116),#1018,.T.);
#1054=ADVANCED_FACE('',(#117),#82,.F.);
#1055=ADVANCED_FACE('',(#118),#1019,.T.);
#1056=ADVANCED_FACE('',(#119),#83,.T.);
#1057=ADVANCED_FACE('',(#120),#1020,.T.);
#1058=ADVANCED_FACE('',(#121),#84,.F.);
#1059=ADVANCED_FACE('',(#122),#1021,.T.);
#1060=ADVANCED_FACE('',(#123),#85,.T.);
#1061=ADVANCED_FACE('',(#124),#1022,.T.);
#1062=ADVANCED_FACE('',(#125),#86,.T.);
#1063=ADVANCED_FACE('',(#126),#1023,.T.);
#1064=ADVANCED_FACE('',(#127),#87,.T.);
#1065=ADVANCED_FACE('',(#128),#1024,.T.);
#1066=ADVANCED_FACE('',(#129),#88,.F.);
#1067=ADVANCED_FACE('',(#130),#1025,.T.);
#1068=ADVANCED_FACE('',(#131),#89,.T.);
#1069=ADVANCED_FACE('',(#132),#1026,.T.);
#1070=ADVANCED_FACE('',(#133),#90,.T.);
#1071=ADVANCED_FACE('',(#134),#1027,.T.);
#1072=ADVANCED_FACE('',(#135),#91,.F.);
#1073=ADVANCED_FACE('',(#136),#1028,.T.);
#1074=ADVANCED_FACE('',(#137),#92,.T.);
#1075=ADVANCED_FACE('',(#138),#93,.T.);
#1076=ADVANCED_FACE('',(#139),#1029,.T.);
#1077=ADVANCED_FACE('',(#140),#94,.T.);
#1078=ADVANCED_FACE('',(#141),#1030,.T.);
#1079=ADVANCED_FACE('',(#142),#95,.F.);
#1080=ADVANCED_FACE('',(#143),#1031,.T.);
#1081=ADVANCED_FACE('',(#144),#96,.T.);
#1082=ADVANCED_FACE('',(#145),#1032,.T.);
#1083=ADVANCED_FACE('',(#146),#97,.T.);
#1084=ADVANCED_FACE('',(#147),#1033,.T.);
#1085=ADVANCED_FACE('',(#148),#98,.F.);
#1086=ADVANCED_FACE('',(#149),#1034,.T.);
#1087=ADVANCED_FACE('',(#150),#99,.T.);
#1088=ADVANCED_FACE('',(#151),#1035,.T.);
#1089=ADVANCED_FACE('',(#152),#100,.F.);
#1090=ADVANCED_FACE('',(#153),#1036,.T.);
#1091=ADVANCED_FACE('',(#154),#101,.T.);
#1092=ADVANCED_FACE('',(#155,#15,#16,#17),#1037,.T.);
#1093=ADVANCED_FACE('',(#156,#18,#19,#20),#1038,.F.);
#1094=CLOSED_SHELL('',(#1039,#1040,#1041,#1042,#1043,#1044,#1045,#1046,
#1047,#1048,#1049,#1050,#1051,#1052,#1053,#1054,#1055,#1056,#1057,#1058,
#1059,#1060,#1061,#1062,#1063,#1064,#1065,#1066,#1067,#1068,#1069,#1070,
#1071,#1072,#1073,#1074,#1075,#1076,#1077,#1078,#1079,#1080,#1081,#1082,
#1083,#1084,#1085,#1086,#1087,#1088,#1089,#1090,#1091,#1092,#1093));
#1095=DERIVED_UNIT_ELEMENT(#1097,1.);
#1096=DERIVED_UNIT_ELEMENT(#1868,-3.);
#1097=(
MASS_UNIT()
NAMED_UNIT(*)
SI_UNIT(.KILO.,.GRAM.)
);
#1098=DERIVED_UNIT((#1095,#1096));
#1099=MEASURE_REPRESENTATION_ITEM('density measure',
POSITIVE_RATIO_MEASURE(7850.),#1098);
#1100=PROPERTY_DEFINITION_REPRESENTATION(#1105,#1102);
#1101=PROPERTY_DEFINITION_REPRESENTATION(#1106,#1103);
#1102=REPRESENTATION('material name',(#1104),#1865);
#1103=REPRESENTATION('density',(#1099),#1865);
#1104=DESCRIPTIVE_REPRESENTATION_ITEM('Steel','Steel');
#1105=PROPERTY_DEFINITION('material property','material name',#1875);
#1106=PROPERTY_DEFINITION('material property','density of part',#1875);
#1107=AXIS2_PLACEMENT_3D('placement',#1542,#1217,#1218);
#1108=AXIS2_PLACEMENT_3D('',#1543,#1219,#1220);
#1109=AXIS2_PLACEMENT_3D('',#1552,#1225,#1226);
#1110=AXIS2_PLACEMENT_3D('',#1554,#1227,#1228);
#1111=AXIS2_PLACEMENT_3D('',#1556,#1229,#1230);
#1112=AXIS2_PLACEMENT_3D('',#1558,#1232,#1233);
#1113=AXIS2_PLACEMENT_3D('',#1564,#1237,#1238);
#1114=AXIS2_PLACEMENT_3D('',#1565,#1239,#1240);
#1115=AXIS2_PLACEMENT_3D('',#1566,#1241,#1242);
#1116=AXIS2_PLACEMENT_3D('',#1567,#1243,#1244);
#1117=AXIS2_PLACEMENT_3D('',#1576,#1249,#1250);
#1118=AXIS2_PLACEMENT_3D('',#1578,#1251,#1252);
#1119=AXIS2_PLACEMENT_3D('',#1580,#1253,#1254);
#1120=AXIS2_PLACEMENT_3D('',#1582,#1256,#1257);
#1121=AXIS2_PLACEMENT_3D('',#1588,#1261,#1262);
#1122=AXIS2_PLACEMENT_3D('',#1589,#1263,#1264);
#1123=AXIS2_PLACEMENT_3D('',#1590,#1265,#1266);
#1124=AXIS2_PLACEMENT_3D('',#1591,#1267,#1268);
#1125=AXIS2_PLACEMENT_3D('',#1600,#1273,#1274);
#1126=AXIS2_PLACEMENT_3D('',#1602,#1275,#1276);
#1127=AXIS2_PLACEMENT_3D('',#1604,#1277,#1278);
#1128=AXIS2_PLACEMENT_3D('',#1606,#1280,#1281);
#1129=AXIS2_PLACEMENT_3D('',#1612,#1285,#1286);
#1130=AXIS2_PLACEMENT_3D('',#1613,#1287,#1288);
#1131=AXIS2_PLACEMENT_3D('',#1614,#1289,#1290);
#1132=AXIS2_PLACEMENT_3D('',#1615,#1291,#1292);
#1133=AXIS2_PLACEMENT_3D('',#1624,#1297,#1298);
#1134=AXIS2_PLACEMENT_3D('',#1626,#1299,#1300);
#1135=AXIS2_PLACEMENT_3D('',#1628,#1301,#1302);
#1136=AXIS2_PLACEMENT_3D('',#1630,#1304,#1305);
#1137=AXIS2_PLACEMENT_3D('',#1636,#1309,#1310);
#1138=AXIS2_PLACEMENT_3D('',#1638,#1311,#1312);
#1139=AXIS2_PLACEMENT_3D('',#1640,#1313,#1314);
#1140=AXIS2_PLACEMENT_3D('',#1642,#1316,#1317);
#1141=AXIS2_PLACEMENT_3D('',#1648,#1321,#1322);
#1142=AXIS2_PLACEMENT_3D('',#1650,#1323,#1324);
#1143=AXIS2_PLACEMENT_3D('',#1652,#1325,#1326);
#1144=AXIS2_PLACEMENT_3D('',#1654,#1328,#1329);
#1145=AXIS2_PLACEMENT_3D('',#1660,#1333,#1334);
#1146=AXIS2_PLACEMENT_3D('',#1662,#1335,#1336);
#1147=AXIS2_PLACEMENT_3D('',#1664,#1337,#1338);
#1148=AXIS2_PLACEMENT_3D('',#1666,#1340,#1341);
#1149=AXIS2_PLACEMENT_3D('',#1672,#1345,#1346);
#1150=AXIS2_PLACEMENT_3D('',#1674,#1347,#1348);
#1151=AXIS2_PLACEMENT_3D('',#1676,#1349,#1350);
#1152=AXIS2_PLACEMENT_3D('',#1678,#1352,#1353);
#1153=AXIS2_PLACEMENT_3D('',#1684,#1357,#1358);
#1154=AXIS2_PLACEMENT_3D('',#1686,#1359,#1360);
#1155=AXIS2_PLACEMENT_3D('',#1688,#1361,#1362);
#1156=AXIS2_PLACEMENT_3D('',#1690,#1364,#1365);
#1157=AXIS2_PLACEMENT_3D('',#1696,#1369,#1370);
#1158=AXIS2_PLACEMENT_3D('',#1698,#1371,#1372);
#1159=AXIS2_PLACEMENT_3D('',#1700,#1373,#1374);
#1160=AXIS2_PLACEMENT_3D('',#1702,#1376,#1377);
#1161=AXIS2_PLACEMENT_3D('',#1708,#1381,#1382);
#1162=AXIS2_PLACEMENT_3D('',#1710,#1383,#1384);
#1163=AXIS2_PLACEMENT_3D('',#1712,#1385,#1386);
#1164=AXIS2_PLACEMENT_3D('',#1714,#1388,#1389);
#1165=AXIS2_PLACEMENT_3D('',#1720,#1393,#1394);
#1166=AXIS2_PLACEMENT_3D('',#1722,#1395,#1396);
#1167=AXIS2_PLACEMENT_3D('',#1724,#1397,#1398);
#1168=AXIS2_PLACEMENT_3D('',#1726,#1400,#1401);
#1169=AXIS2_PLACEMENT_3D('',#1732,#1405,#1406);
#1170=AXIS2_PLACEMENT_3D('',#1734,#1407,#1408);
#1171=AXIS2_PLACEMENT_3D('',#1736,#1409,#1410);
#1172=AXIS2_PLACEMENT_3D('',#1738,#1412,#1413);
#1173=AXIS2_PLACEMENT_3D('',#1744,#1417,#1418);
#1174=AXIS2_PLACEMENT_3D('',#1746,#1419,#1420);
#1175=AXIS2_PLACEMENT_3D('',#1748,#1421,#1422);
#1176=AXIS2_PLACEMENT_3D('',#1750,#1424,#1425);
#1177=AXIS2_PLACEMENT_3D('',#1756,#1429,#1430);
#1178=AXIS2_PLACEMENT_3D('',#1758,#1431,#1432);
#1179=AXIS2_PLACEMENT_3D('',#1760,#1433,#1434);
#1180=AXIS2_PLACEMENT_3D('',#1762,#1436,#1437);
#1181=AXIS2_PLACEMENT_3D('',#1764,#1438,#1439);
#1182=AXIS2_PLACEMENT_3D('',#1766,#1440,#1441);
#1183=AXIS2_PLACEMENT_3D('',#1768,#1443,#1444);
#1184=AXIS2_PLACEMENT_3D('',#1774,#1448,#1449);
#1185=AXIS2_PLACEMENT_3D('',#1776,#1450,#1451);
#1186=AXIS2_PLACEMENT_3D('',#1778,#1452,#1453);
#1187=AXIS2_PLACEMENT_3D('',#1780,#1455,#1456);
#1188=AXIS2_PLACEMENT_3D('',#1786,#1460,#1461);
#1189=AXIS2_PLACEMENT_3D('',#1788,#1462,#1463);
#1190=AXIS2_PLACEMENT_3D('',#1790,#1464,#1465);
#1191=AXIS2_PLACEMENT_3D('',#1792,#1467,#1468);
#1192=AXIS2_PLACEMENT_3D('',#1798,#1472,#1473);
#1193=AXIS2_PLACEMENT_3D('',#1800,#1474,#1475);
#1194=AXIS2_PLACEMENT_3D('',#1802,#1476,#1477);
#1195=AXIS2_PLACEMENT_3D('',#1804,#1479,#1480);
#1196=AXIS2_PLACEMENT_3D('',#1810,#1484,#1485);
#1197=AXIS2_PLACEMENT_3D('',#1812,#1486,#1487);
#1198=AXIS2_PLACEMENT_3D('',#1814,#1488,#1489);
#1199=AXIS2_PLACEMENT_3D('',#1816,#1491,#1492);
#1200=AXIS2_PLACEMENT_3D('',#1822,#1496,#1497);
#1201=AXIS2_PLACEMENT_3D('',#1824,#1498,#1499);
#1202=AXIS2_PLACEMENT_3D('',#1826,#1500,#1501);
#1203=AXIS2_PLACEMENT_3D('',#1828,#1503,#1504);
#1204=AXIS2_PLACEMENT_3D('',#1834,#1508,#1509);
#1205=AXIS2_PLACEMENT_3D('',#1836,#1510,#1511);
#1206=AXIS2_PLACEMENT_3D('',#1838,#1512,#1513);
#1207=AXIS2_PLACEMENT_3D('',#1840,#1515,#1516);
#1208=AXIS2_PLACEMENT_3D('',#1846,#1520,#1521);
#1209=AXIS2_PLACEMENT_3D('',#1848,#1522,#1523);
#1210=AXIS2_PLACEMENT_3D('',#1850,#1524,#1525);
#1211=AXIS2_PLACEMENT_3D('',#1852,#1527,#1528);
#1212=AXIS2_PLACEMENT_3D('',#1858,#1532,#1533);
#1213=AXIS2_PLACEMENT_3D('',#1859,#1534,#1535);
#1214=AXIS2_PLACEMENT_3D('',#1860,#1536,#1537);
#1215=AXIS2_PLACEMENT_3D('',#1861,#1538,#1539);
#1216=AXIS2_PLACEMENT_3D('',#1862,#1540,#1541);
#1217=DIRECTION('axis',(0.,0.,1.));
#1218=DIRECTION('refdir',(1.,0.,0.));
#1219=DIRECTION('center_axis',(0.,-1.,0.));
#1220=DIRECTION('ref_axis',(-1.,0.,0.));
#1221=DIRECTION('',(1.,0.,0.));
#1222=DIRECTION('',(0.,0.,-1.));
#1223=DIRECTION('',(-1.,0.,0.));
#1224=DIRECTION('',(0.,0.,-1.));
#1225=DIRECTION('center_axis',(0.,0.,-1.));
#1226=DIRECTION('ref_axis',(0.,-1.,0.));
#1227=DIRECTION('center_axis',(0.,0.,-1.));
#1228=DIRECTION('ref_axis',(0.,-1.,0.));
#1229=DIRECTION('center_axis',(0.,0.,1.));
#1230=DIRECTION('ref_axis',(0.,-1.,0.));
#1231=DIRECTION('',(0.,0.,-1.));
#1232=DIRECTION('center_axis',(0.,1.,0.));
#1233=DIRECTION('ref_axis',(1.,0.,0.));
#1234=DIRECTION('',(-1.,0.,0.));
#1235=DIRECTION('',(1.,0.,0.));
#1236=DIRECTION('',(0.,0.,-1.));
#1237=DIRECTION('center_axis',(0.,0.,-1.));
#1238=DIRECTION('ref_axis',(0.,1.,0.));
#1239=DIRECTION('center_axis',(0.,0.,-1.));
#1240=DIRECTION('ref_axis',(0.,1.,0.));
#1241=DIRECTION('center_axis',(0.,0.,1.));
#1242=DIRECTION('ref_axis',(0.,1.,0.));
#1243=DIRECTION('center_axis',(0.,1.,0.));
#1244=DIRECTION('ref_axis',(1.,0.,0.));
#1245=DIRECTION('',(-1.,0.,0.));
#1246=DIRECTION('',(0.,0.,-1.));
#1247=DIRECTION('',(1.,0.,0.));
#1248=DIRECTION('',(0.,0.,-1.));
#1249=DIRECTION('center_axis',(0.,0.,-1.));
#1250=DIRECTION('ref_axis',(0.,1.,0.));
#1251=DIRECTION('center_axis',(0.,0.,-1.));
#1252=DIRECTION('ref_axis',(0.,1.,0.));
#1253=DIRECTION('center_axis',(0.,0.,1.));
#1254=DIRECTION('ref_axis',(0.,1.,0.));
#1255=DIRECTION('',(0.,0.,-1.));
#1256=DIRECTION('center_axis',(0.,-1.,0.));
#1257=DIRECTION('ref_axis',(-1.,0.,0.));
#1258=DIRECTION('',(1.,0.,0.));
#1259=DIRECTION('',(-1.,0.,0.));
#1260=DIRECTION('',(0.,0.,-1.));
#1261=DIRECTION('center_axis',(0.,0.,-1.));
#1262=DIRECTION('ref_axis',(0.,-1.,0.));
#1263=DIRECTION('center_axis',(0.,0.,-1.));
#1264=DIRECTION('ref_axis',(0.,-1.,0.));
#1265=DIRECTION('center_axis',(0.,0.,1.));
#1266=DIRECTION('ref_axis',(0.,-1.,0.));
#1267=DIRECTION('center_axis',(-1.,0.,0.));
#1268=DIRECTION('ref_axis',(0.,1.,0.));
#1269=DIRECTION('',(0.,-1.,0.));
#1270=DIRECTION('',(0.,0.,-1.));
#1271=DIRECTION('',(0.,1.,0.));
#1272=DIRECTION('',(0.,0.,-1.));
#1273=DIRECTION('center_axis',(0.,0.,-1.));
#1274=DIRECTION('ref_axis',(-1.,7.8159700933611E-15,0.));
#1275=DIRECTION('center_axis',(0.,0.,-1.));
#1276=DIRECTION('ref_axis',(-1.,7.8159700933611E-15,0.));
#1277=DIRECTION('center_axis',(0.,0.,1.));
#1278=DIRECTION('ref_axis',(-1.,7.8159700933611E-15,0.));
#1279=DIRECTION('',(0.,0.,-1.));
#1280=DIRECTION('center_axis',(1.,0.,0.));
#1281=DIRECTION('ref_axis',(0.,-1.,0.));
#1282=DIRECTION('',(0.,1.,0.));
#1283=DIRECTION('',(0.,-1.,0.));
#1284=DIRECTION('',(0.,0.,-1.));
#1285=DIRECTION('center_axis',(0.,0.,-1.));
#1286=DIRECTION('ref_axis',(1.,0.,0.));
#1287=DIRECTION('center_axis',(0.,0.,-1.));
#1288=DIRECTION('ref_axis',(1.,0.,0.));
#1289=DIRECTION('center_axis',(0.,0.,1.));
#1290=DIRECTION('ref_axis',(1.,0.,0.));
#1291=DIRECTION('center_axis',(1.17294722845652E-14,1.,0.));
#1292=DIRECTION('ref_axis',(1.,-1.17294722845652E-14,0.));
#1293=DIRECTION('',(1.,-1.17294722845652E-14,0.));
#1294=DIRECTION('',(0.,0.,-1.));
#1295=DIRECTION('',(1.,-1.17294722845652E-14,0.));
#1296=DIRECTION('',(0.,0.,-1.));
#1297=DIRECTION('center_axis',(0.,0.,-1.));
#1298=DIRECTION('ref_axis',(2.03012210217167E-14,1.,0.));
#1299=DIRECTION('center_axis',(0.,0.,1.));
#1300=DIRECTION('ref_axis',(2.03012210217167E-14,1.,0.));
#1301=DIRECTION('center_axis',(0.,0.,-1.));
#1302=DIRECTION('ref_axis',(2.03012210217167E-14,1.,0.));
#1303=DIRECTION('',(0.,0.,-1.));
#1304=DIRECTION('center_axis',(-0.965998753076902,0.258546725088272,0.));
#1305=DIRECTION('ref_axis',(0.258546725088272,0.965998753076902,0.));
#1306=DIRECTION('',(-0.258546725088272,-0.965998753076902,0.));
#1307=DIRECTION('',(0.258546725088272,0.965998753076902,0.));
#1308=DIRECTION('',(0.,0.,-1.));
#1309=DIRECTION('center_axis',(0.,0.,-1.));
#1310=DIRECTION('ref_axis',(0.,-1.,0.));
#1311=DIRECTION('center_axis',(0.,0.,1.));
#1312=DIRECTION('ref_axis',(0.,-1.,0.));
#1313=DIRECTION('center_axis',(0.,0.,1.));
#1314=DIRECTION('ref_axis',(0.,-1.,0.));
#1315=DIRECTION('',(0.,0.,-1.));
#1316=DIRECTION('center_axis',(0.,1.,0.));
#1317=DIRECTION('ref_axis',(1.,0.,0.));
#1318=DIRECTION('',(-1.,0.,0.));
#1319=DIRECTION('',(1.,0.,0.));
#1320=DIRECTION('',(0.,0.,-1.));
#1321=DIRECTION('center_axis',(0.,0.,-1.));
#1322=DIRECTION('ref_axis',(0.,1.,0.));
#1323=DIRECTION('center_axis',(0.,0.,1.));
#1324=DIRECTION('ref_axis',(0.,1.,0.));
#1325=DIRECTION('center_axis',(0.,0.,-1.));
#1326=DIRECTION('ref_axis',(0.,1.,0.));
#1327=DIRECTION('',(0.,0.,-1.));
#1328=DIRECTION('center_axis',(-1.,0.,0.));
#1329=DIRECTION('ref_axis',(0.,1.,0.));
#1330=DIRECTION('',(0.,-1.,0.));
#1331=DIRECTION('',(0.,1.,0.));
#1332=DIRECTION('',(0.,0.,-1.));
#1333=DIRECTION('center_axis',(0.,0.,-1.));
#1334=DIRECTION('ref_axis',(0.,-1.,0.));
#1335=DIRECTION('center_axis',(0.,0.,1.));
#1336=DIRECTION('ref_axis',(0.,-1.,0.));
#1337=DIRECTION('center_axis',(0.,0.,1.));
#1338=DIRECTION('ref_axis',(0.,-1.,0.));
#1339=DIRECTION('',(0.,0.,-1.));
#1340=DIRECTION('center_axis',(0.,1.,0.));
#1341=DIRECTION('ref_axis',(1.,0.,0.));
#1342=DIRECTION('',(1.,0.,0.));
#1343=DIRECTION('',(1.,0.,0.));
#1344=DIRECTION('',(0.,0.,-1.));
#1345=DIRECTION('center_axis',(0.,0.,-1.));
#1346=DIRECTION('ref_axis',(7.7715611723761E-16,1.,0.));
#1347=DIRECTION('center_axis',(0.,0.,1.));
#1348=DIRECTION('ref_axis',(7.7715611723761E-16,1.,0.));
#1349=DIRECTION('center_axis',(0.,0.,-1.));
#1350=DIRECTION('ref_axis',(7.7715611723761E-16,1.,0.));
#1351=DIRECTION('',(0.,0.,-1.));
#1352=DIRECTION('center_axis',(-1.,-1.55140335318799E-16,0.));
#1353=DIRECTION('ref_axis',(-1.55140335318799E-16,1.,0.));
#1354=DIRECTION('',(1.55140335318799E-16,-1.,0.));
#1355=DIRECTION('',(-1.55140335318799E-16,1.,0.));
#1356=DIRECTION('',(0.,0.,-1.));
#1357=DIRECTION('center_axis',(0.,0.,-1.));
#1358=DIRECTION('ref_axis',(-1.,4.93432455388959E-16,0.));
#1359=DIRECTION('center_axis',(0.,0.,1.));
#1360=DIRECTION('ref_axis',(-1.,4.93432455388959E-16,0.));
#1361=DIRECTION('center_axis',(0.,0.,-1.));
#1362=DIRECTION('ref_axis',(-1.,4.93432455388959E-16,0.));
#1363=DIRECTION('',(0.,0.,-1.));
#1364=DIRECTION('center_axis',(-8.61244941528686E-18,-1.,0.));
#1365=DIRECTION('ref_axis',(-1.,8.61244941528686E-18,0.));
#1366=DIRECTION('',(1.,-8.61244941528686E-18,0.));
#1367=DIRECTION('',(-1.,8.61244941528686E-18,0.));
#1368=DIRECTION('',(0.,0.,-1.));
#1369=DIRECTION('center_axis',(0.,0.,-1.));
#1370=DIRECTION('ref_axis',(0.,-1.,0.));
#1371=DIRECTION('center_axis',(0.,0.,1.));
#1372=DIRECTION('ref_axis',(0.,-1.,0.));
#1373=DIRECTION('center_axis',(0.,0.,-1.));
#1374=DIRECTION('ref_axis',(0.,-1.,0.));
#1375=DIRECTION('',(0.,0.,-1.));
#1376=DIRECTION('center_axis',(1.,0.,0.));
#1377=DIRECTION('ref_axis',(0.,-1.,0.));
#1378=DIRECTION('',(0.,-1.,0.));
#1379=DIRECTION('',(0.,-1.,0.));
#1380=DIRECTION('',(0.,0.,-1.));
#1381=DIRECTION('center_axis',(0.,0.,-1.));
#1382=DIRECTION('ref_axis',(1.,0.,0.));
#1383=DIRECTION('center_axis',(0.,0.,1.));
#1384=DIRECTION('ref_axis',(1.,0.,0.));
#1385=DIRECTION('center_axis',(0.,0.,1.));
#1386=DIRECTION('ref_axis',(1.,0.,0.));
#1387=DIRECTION('',(0.,0.,-1.));
#1388=DIRECTION('center_axis',(-1.,0.,0.));
#1389=DIRECTION('ref_axis',(0.,1.,0.));
#1390=DIRECTION('',(0.,-1.,0.));
#1391=DIRECTION('',(0.,1.,0.));
#1392=DIRECTION('',(0.,0.,-1.));
#1393=DIRECTION('center_axis',(0.,0.,-1.));
#1394=DIRECTION('ref_axis',(-1.,-1.38777878078143E-16,0.));
#1395=DIRECTION('center_axis',(0.,0.,1.));
#1396=DIRECTION('ref_axis',(-1.,-1.38777878078143E-16,0.));
#1397=DIRECTION('center_axis',(0.,0.,-1.));
#1398=DIRECTION('ref_axis',(-1.,-1.38777878078143E-16,0.));
#1399=DIRECTION('',(0.,0.,-1.));
#1400=DIRECTION('center_axis',(8.05092838741954E-19,-1.,0.));
#1401=DIRECTION('ref_axis',(-1.,-8.05092838741954E-19,0.));
#1402=DIRECTION('',(1.,8.05092838741954E-19,0.));
#1403=DIRECTION('',(-1.,-8.05092838741954E-19,0.));
#1404=DIRECTION('',(0.,0.,-1.));
#1405=DIRECTION('center_axis',(0.,0.,-1.));
#1406=DIRECTION('ref_axis',(0.,-1.,0.));
#1407=DIRECTION('center_axis',(0.,0.,1.));
#1408=DIRECTION('ref_axis',(0.,-1.,0.));
#1409=DIRECTION('center_axis',(0.,0.,-1.));
#1410=DIRECTION('ref_axis',(0.,-1.,0.));
#1411=DIRECTION('',(0.,0.,-1.));
#1412=DIRECTION('center_axis',(1.,0.,0.));
#1413=DIRECTION('ref_axis',(0.,-1.,0.));
#1414=DIRECTION('',(0.,-1.,0.));
#1415=DIRECTION('',(0.,-1.,0.));
#1416=DIRECTION('',(0.,0.,-1.));
#1417=DIRECTION('center_axis',(0.,0.,-1.));
#1418=DIRECTION('ref_axis',(1.,0.,0.));
#1419=DIRECTION('center_axis',(0.,0.,1.));
#1420=DIRECTION('ref_axis',(1.,0.,0.));
#1421=DIRECTION('center_axis',(0.,0.,1.));
#1422=DIRECTION('ref_axis',(1.,0.,0.));
#1423=DIRECTION('',(0.,0.,-1.));
#1424=DIRECTION('center_axis',(-1.,0.,0.));
#1425=DIRECTION('ref_axis',(0.,1.,0.));
#1426=DIRECTION('',(0.,-1.,0.));
#1427=DIRECTION('',(0.,1.,0.));
#1428=DIRECTION('',(0.,0.,-1.));
#1429=DIRECTION('center_axis',(0.,0.,-1.));
#1430=DIRECTION('ref_axis',(-1.,0.,0.));
#1431=DIRECTION('center_axis',(0.,0.,1.));
#1432=DIRECTION('ref_axis',(-1.,0.,0.));
#1433=DIRECTION('center_axis',(0.,0.,-1.));
#1434=DIRECTION('ref_axis',(-1.,0.,0.));
#1435=DIRECTION('',(0.,0.,-1.));
#1436=DIRECTION('center_axis',(0.,0.,-1.));
#1437=DIRECTION('ref_axis',(0.048076923076926,-0.998843636145045,0.));
#1438=DIRECTION('center_axis',(0.,0.,1.));
#1439=DIRECTION('ref_axis',(0.048076923076926,-0.998843636145045,0.));
#1440=DIRECTION('center_axis',(0.,0.,-1.));
#1441=DIRECTION('ref_axis',(0.048076923076926,-0.998843636145045,0.));
#1442=DIRECTION('',(0.,0.,-1.));
#1443=DIRECTION('center_axis',(1.,0.,0.));
#1444=DIRECTION('ref_axis',(0.,-1.,0.));
#1445=DIRECTION('',(0.,1.,0.));
#1446=DIRECTION('',(0.,-1.,0.));
#1447=DIRECTION('',(0.,0.,-1.));
#1448=DIRECTION('center_axis',(0.,0.,-1.));
#1449=DIRECTION('ref_axis',(1.,-3.32029315775749E-15,0.));
#1450=DIRECTION('center_axis',(0.,0.,1.));
#1451=DIRECTION('ref_axis',(1.,-3.32029315775749E-15,0.));
#1452=DIRECTION('center_axis',(0.,0.,-1.));
#1453=DIRECTION('ref_axis',(1.,-3.32029315775749E-15,0.));
#1454=DIRECTION('',(0.,0.,-1.));
#1455=DIRECTION('center_axis',(0.,1.,0.));
#1456=DIRECTION('ref_axis',(1.,0.,0.));
#1457=DIRECTION('',(1.,0.,0.));
#1458=DIRECTION('',(1.,0.,0.));
#1459=DIRECTION('',(0.,0.,-1.));
#1460=DIRECTION('center_axis',(0.,0.,-1.));
#1461=DIRECTION('ref_axis',(-0.955990220048903,-0.293398533007325,0.));
#1462=DIRECTION('center_axis',(0.,0.,1.));
#1463=DIRECTION('ref_axis',(-0.955990220048903,-0.293398533007325,0.));
#1464=DIRECTION('center_axis',(0.,0.,1.));
#1465=DIRECTION('ref_axis',(-0.955990220048903,-0.293398533007325,0.));
#1466=DIRECTION('',(0.,0.,-1.));
#1467=DIRECTION('center_axis',(0.955990220048899,0.293398533007337,0.));
#1468=DIRECTION('ref_axis',(0.293398533007337,-0.955990220048899,0.));
#1469=DIRECTION('',(-0.293398533007337,0.955990220048899,0.));
#1470=DIRECTION('',(0.293398533007337,-0.955990220048899,0.));
#1471=DIRECTION('',(0.,0.,-1.));
#1472=DIRECTION('center_axis',(0.,0.,-1.));
#1473=DIRECTION('ref_axis',(0.955990220048902,0.293398533007327,0.));
#1474=DIRECTION('center_axis',(0.,0.,1.));
#1475=DIRECTION('ref_axis',(0.955990220048902,0.293398533007327,0.));
#1476=DIRECTION('center_axis',(0.,0.,-1.));
#1477=DIRECTION('ref_axis',(0.955990220048902,0.293398533007327,0.));
#1478=DIRECTION('',(0.,0.,-1.));
#1479=DIRECTION('center_axis',(0.378729192559054,0.925507535735699,0.));
#1480=DIRECTION('ref_axis',(0.925507535735699,-0.378729192559054,0.));
#1481=DIRECTION('',(0.925507535735699,-0.378729192559054,0.));
#1482=DIRECTION('',(0.925507535735699,-0.378729192559054,0.));
#1483=DIRECTION('',(0.,0.,-1.));
#1484=DIRECTION('center_axis',(0.,0.,-1.));
#1485=DIRECTION('ref_axis',(0.378729192559084,0.925507535735687,0.));
#1486=DIRECTION('center_axis',(0.,0.,1.));
#1487=DIRECTION('ref_axis',(0.378729192559084,0.925507535735687,0.));
#1488=DIRECTION('center_axis',(0.,0.,-1.));
#1489=DIRECTION('ref_axis',(0.378729192559084,0.925507535735687,0.));
#1490=DIRECTION('',(0.,0.,-1.));
#1491=DIRECTION('center_axis',(-5.24129758362189E-16,1.,0.));
#1492=DIRECTION('ref_axis',(1.,5.24129758362189E-16,0.));
#1493=DIRECTION('',(-1.,-5.24129758362189E-16,0.));
#1494=DIRECTION('',(1.,5.24129758362189E-16,0.));
#1495=DIRECTION('',(0.,0.,-1.));
#1496=DIRECTION('center_axis',(0.,0.,-1.));
#1497=DIRECTION('ref_axis',(-1.,0.,0.));
#1498=DIRECTION('center_axis',(0.,0.,1.));
#1499=DIRECTION('ref_axis',(-1.,0.,0.));
#1500=DIRECTION('center_axis',(0.,0.,1.));
#1501=DIRECTION('ref_axis',(-1.,0.,0.));
#1502=DIRECTION('',(0.,0.,-1.));
#1503=DIRECTION('center_axis',(1.,0.,0.));
#1504=DIRECTION('ref_axis',(0.,-1.,0.));
#1505=DIRECTION('',(0.,-1.,0.));
#1506=DIRECTION('',(0.,-1.,0.));
#1507=DIRECTION('',(0.,0.,-1.));
#1508=DIRECTION('center_axis',(0.,0.,-1.));
#1509=DIRECTION('ref_axis',(1.,0.,0.));
#1510=DIRECTION('center_axis',(0.,0.,1.));
#1511=DIRECTION('ref_axis',(1.,0.,0.));
#1512=DIRECTION('center_axis',(0.,0.,-1.));
#1513=DIRECTION('ref_axis',(1.,0.,0.));
#1514=DIRECTION('',(0.,0.,-1.));
#1515=DIRECTION('center_axis',(0.,1.,0.));
#1516=DIRECTION('ref_axis',(1.,0.,0.));
#1517=DIRECTION('',(-1.,0.,0.));
#1518=DIRECTION('',(1.,0.,0.));
#1519=DIRECTION('',(0.,0.,-1.));
#1520=DIRECTION('center_axis',(0.,0.,-1.));
#1521=DIRECTION('ref_axis',(-0.966040287978499,-0.258391489802622,0.));
#1522=DIRECTION('center_axis',(0.,0.,1.));
#1523=DIRECTION('ref_axis',(-0.966040287978499,-0.258391489802622,0.));
#1524=DIRECTION('center_axis',(0.,0.,1.));
#1525=DIRECTION('ref_axis',(-0.966040287978499,-0.258391489802622,0.));
#1526=DIRECTION('',(0.,0.,-1.));
#1527=DIRECTION('center_axis',(0.966040287978502,0.258391489802612,0.));
#1528=DIRECTION('ref_axis',(0.258391489802612,-0.966040287978502,0.));
#1529=DIRECTION('',(0.258391489802612,-0.966040287978502,0.));
#1530=DIRECTION('',(0.258391489802612,-0.966040287978502,0.));
#1531=DIRECTION('',(0.,0.,-1.));
#1532=DIRECTION('center_axis',(0.,0.,-1.));
#1533=DIRECTION('ref_axis',(0.966040287978501,0.258391489802616,0.));
#1534=DIRECTION('center_axis',(0.,0.,1.));
#1535=DIRECTION('ref_axis',(0.966040287978501,0.258391489802616,0.));
#1536=DIRECTION('center_axis',(0.,0.,-1.));
#1537=DIRECTION('ref_axis',(0.966040287978501,0.258391489802616,0.));
#1538=DIRECTION('center_axis',(0.,0.,1.));
#1539=DIRECTION('ref_axis',(1.,0.,0.));
#1540=DIRECTION('center_axis',(0.,0.,1.));
#1541=DIRECTION('ref_axis',(1.,0.,0.));
#1542=CARTESIAN_POINT('',(0.,0.,0.));
#1543=CARTESIAN_POINT('Origin',(397.5,462.621653992437,0.));
#1544=CARTESIAN_POINT('',(394.5,462.621653992437,0.));
#1545=CARTESIAN_POINT('',(397.5,462.621653992437,0.));
#1546=CARTESIAN_POINT('',(394.5,462.621653992437,0.));
#1547=CARTESIAN_POINT('',(394.5,462.621653992437,-4.));
#1548=CARTESIAN_POINT('',(394.5,462.621653992437,0.));
#1549=CARTESIAN_POINT('',(397.5,462.621653992437,-4.));
#1550=CARTESIAN_POINT('',(394.5,462.621653992437,-4.));
#1551=CARTESIAN_POINT('',(397.5,462.621653992437,0.));
#1552=CARTESIAN_POINT('Origin',(397.5,457.121653992437,0.));
#1553=CARTESIAN_POINT('',(397.5,451.621653992437,0.));
#1554=CARTESIAN_POINT('Origin',(397.5,457.121653992437,0.));
#1555=CARTESIAN_POINT('',(397.5,451.621653992437,-4.));
#1556=CARTESIAN_POINT('Origin',(397.5,457.121653992437,-4.));
#1557=CARTESIAN_POINT('',(397.5,451.621653992437,0.));
#1558=CARTESIAN_POINT('Origin',(394.5,451.621653992437,0.));
#1559=CARTESIAN_POINT('',(394.5,451.621653992437,0.));
#1560=CARTESIAN_POINT('',(397.5,451.621653992437,0.));
#1561=CARTESIAN_POINT('',(394.5,451.621653992437,-4.));
#1562=CARTESIAN_POINT('',(397.5,451.621653992437,-4.));
#1563=CARTESIAN_POINT('',(394.5,451.621653992437,0.));
#1564=CARTESIAN_POINT('Origin',(394.5,457.121653992437,0.));
#1565=CARTESIAN_POINT('Origin',(394.5,457.121653992437,0.));
#1566=CARTESIAN_POINT('Origin',(394.5,457.121653992437,-4.));
#1567=CARTESIAN_POINT('Origin',(624.5,392.,0.));
#1568=CARTESIAN_POINT('',(627.5,392.,0.));
#1569=CARTESIAN_POINT('',(624.5,392.,0.));
#1570=CARTESIAN_POINT('',(627.5,392.,0.));
#1571=CARTESIAN_POINT('',(627.5,392.,-4.));
#1572=CARTESIAN_POINT('',(627.5,392.,0.));
#1573=CARTESIAN_POINT('',(624.5,392.,-4.));
#1574=CARTESIAN_POINT('',(627.5,392.,-4.));
#1575=CARTESIAN_POINT('',(624.5,392.,0.));
#1576=CARTESIAN_POINT('Origin',(624.5,397.5,0.));
#1577=CARTESIAN_POINT('',(624.5,403.,0.));
#1578=CARTESIAN_POINT('Origin',(624.5,397.5,0.));
#1579=CARTESIAN_POINT('',(624.5,403.,-4.));
#1580=CARTESIAN_POINT('Origin',(624.5,397.5,-4.));
#1581=CARTESIAN_POINT('',(624.5,403.,0.));
#1582=CARTESIAN_POINT('Origin',(627.5,403.,0.));
#1583=CARTESIAN_POINT('',(627.5,403.,0.));
#1584=CARTESIAN_POINT('',(624.5,403.,0.));
#1585=CARTESIAN_POINT('',(627.5,403.,-4.));
#1586=CARTESIAN_POINT('',(624.5,403.,-4.));
#1587=CARTESIAN_POINT('',(627.5,403.,0.));
#1588=CARTESIAN_POINT('Origin',(627.5,397.5,0.));
#1589=CARTESIAN_POINT('Origin',(627.5,397.5,0.));
#1590=CARTESIAN_POINT('Origin',(627.5,397.5,-4.));
#1591=CARTESIAN_POINT('Origin',(694.,230.5,0.));
#1592=CARTESIAN_POINT('',(694.,330.5,0.));
#1593=CARTESIAN_POINT('',(694.,230.5,0.));
#1594=CARTESIAN_POINT('',(694.,330.5,0.));
#1595=CARTESIAN_POINT('',(694.,330.5,-4.));
#1596=CARTESIAN_POINT('',(694.,330.5,0.));
#1597=CARTESIAN_POINT('',(694.,230.5,-4.));
#1598=CARTESIAN_POINT('',(694.,330.5,-4.));
#1599=CARTESIAN_POINT('',(694.,230.5,0.));
#1600=CARTESIAN_POINT('Origin',(644.,230.5,0.));
#1601=CARTESIAN_POINT('',(594.,230.5,0.));
#1602=CARTESIAN_POINT('Origin',(644.,230.5,0.));
#1603=CARTESIAN_POINT('',(594.,230.5,-4.));
#1604=CARTESIAN_POINT('Origin',(644.,230.5,-4.));
#1605=CARTESIAN_POINT('',(594.,230.5,0.));
#1606=CARTESIAN_POINT('Origin',(594.,330.5,0.));
#1607=CARTESIAN_POINT('',(594.,330.5,0.));
#1608=CARTESIAN_POINT('',(594.,330.5,0.));
#1609=CARTESIAN_POINT('',(594.,330.5,-4.));
#1610=CARTESIAN_POINT('',(594.,330.5,-4.));
#1611=CARTESIAN_POINT('',(594.,330.5,0.));
#1612=CARTESIAN_POINT('Origin',(644.,330.5,0.));
#1613=CARTESIAN_POINT('Origin',(644.,330.5,0.));
#1614=CARTESIAN_POINT('Origin',(644.,330.5,-4.));
#1615=CARTESIAN_POINT('Origin',(389.942244642199,465.621653992437,0.));
#1616=CARTESIAN_POINT('',(389.942244642199,465.621653992437,0.));
#1617=CARTESIAN_POINT('',(402.057755357802,465.621653992437,0.));
#1618=CARTESIAN_POINT('',(389.942244642199,465.621653992437,0.));
#1619=CARTESIAN_POINT('',(402.057755357802,465.621653992437,-4.));
#1620=CARTESIAN_POINT('',(402.057755357802,465.621653992437,0.));
#1621=CARTESIAN_POINT('',(389.942244642199,465.621653992437,-4.));
#1622=CARTESIAN_POINT('',(389.942244642199,465.621653992437,-4.));
#1623=CARTESIAN_POINT('',(389.942244642199,465.621653992437,0.));
#1624=CARTESIAN_POINT('Origin',(389.942244642198,458.621653992437,0.));
#1625=CARTESIAN_POINT('',(383.18025337066,460.431481068055,0.));
#1626=CARTESIAN_POINT('Origin',(389.942244642198,458.621653992437,0.));
#1627=CARTESIAN_POINT('',(383.18025337066,460.431481068055,-4.));
#1628=CARTESIAN_POINT('Origin',(389.942244642198,458.621653992437,-4.));
#1629=CARTESIAN_POINT('',(383.18025337066,460.431481068055,0.));
#1630=CARTESIAN_POINT('Origin',(380.148636406403,449.104560542261,0.));
#1631=CARTESIAN_POINT('',(380.148636406403,449.104560542261,0.));
#1632=CARTESIAN_POINT('',(383.18025337066,460.431481068055,0.));
#1633=CARTESIAN_POINT('',(380.148636406403,449.104560542261,-4.));
#1634=CARTESIAN_POINT('',(383.18025337066,460.431481068055,-4.));
#1635=CARTESIAN_POINT('',(380.148636406403,449.104560542261,0.));
#1636=CARTESIAN_POINT('Origin',(378.216638900249,449.621653992437,0.));
#1637=CARTESIAN_POINT('',(378.216638900249,447.621653992437,0.));
#1638=CARTESIAN_POINT('Origin',(378.216638900249,449.621653992437,0.));
#1639=CARTESIAN_POINT('',(378.216638900249,447.621653992437,-4.));
#1640=CARTESIAN_POINT('Origin',(378.216638900249,449.621653992437,-4.));
#1641=CARTESIAN_POINT('',(378.216638900249,447.621653992437,0.));
#1642=CARTESIAN_POINT('Origin',(327.,447.621653992437,0.));
#1643=CARTESIAN_POINT('',(327.,447.621653992437,0.));
#1644=CARTESIAN_POINT('',(378.216638900249,447.621653992437,0.));
#1645=CARTESIAN_POINT('',(327.,447.621653992437,-4.));
#1646=CARTESIAN_POINT('',(378.216638900249,447.621653992437,-4.));
#1647=CARTESIAN_POINT('',(327.,447.621653992437,0.));
#1648=CARTESIAN_POINT('Origin',(327.,394.121653992437,0.));
#1649=CARTESIAN_POINT('',(273.5,394.121653992437,0.));
#1650=CARTESIAN_POINT('Origin',(327.,394.121653992437,0.));
#1651=CARTESIAN_POINT('',(273.5,394.121653992437,-4.));
#1652=CARTESIAN_POINT('Origin',(327.,394.121653992437,-4.));
#1653=CARTESIAN_POINT('',(273.5,394.121653992437,0.));
#1654=CARTESIAN_POINT('Origin',(273.5,208.5,0.));
#1655=CARTESIAN_POINT('',(273.5,208.5,0.));
#1656=CARTESIAN_POINT('',(273.5,394.121653992437,0.));
#1657=CARTESIAN_POINT('',(273.5,208.5,-4.));
#1658=CARTESIAN_POINT('',(273.5,394.121653992437,-4.));
#1659=CARTESIAN_POINT('',(273.5,208.5,0.));
#1660=CARTESIAN_POINT('Origin',(253.5,208.5,0.));
#1661=CARTESIAN_POINT('',(253.5,188.5,0.));
#1662=CARTESIAN_POINT('Origin',(253.5,208.5,0.));
#1663=CARTESIAN_POINT('',(253.5,188.5,-4.));
#1664=CARTESIAN_POINT('Origin',(253.5,208.5,-4.));
#1665=CARTESIAN_POINT('',(253.5,188.5,0.));
#1666=CARTESIAN_POINT('Origin',(20.,188.5,0.));
#1667=CARTESIAN_POINT('',(20.,188.5,0.));
#1668=CARTESIAN_POINT('',(20.,188.5,0.));
#1669=CARTESIAN_POINT('',(20.,188.5,-4.));
#1670=CARTESIAN_POINT('',(20.,188.5,-4.));
#1671=CARTESIAN_POINT('',(20.,188.5,0.));
#1672=CARTESIAN_POINT('Origin',(20.,168.5,0.));
#1673=CARTESIAN_POINT('',(-1.99840144432528E-14,168.5,0.));
#1674=CARTESIAN_POINT('Origin',(20.,168.5,0.));
#1675=CARTESIAN_POINT('',(0.,168.5,-4.));
#1676=CARTESIAN_POINT('Origin',(20.,168.5,-4.));
#1677=CARTESIAN_POINT('',(-1.99840144432528E-14,168.5,0.));
#1678=CARTESIAN_POINT('Origin',(0.,54.,0.));
#1679=CARTESIAN_POINT('',(0.,54.,0.));
#1680=CARTESIAN_POINT('',(-1.77635683940025E-14,168.5,0.));
#1681=CARTESIAN_POINT('',(0.,54.,-4.));
#1682=CARTESIAN_POINT('',(-1.77635683940025E-14,168.5,-4.));
#1683=CARTESIAN_POINT('',(0.,54.,0.));
#1684=CARTESIAN_POINT('Origin',(54.,54.,0.));
#1685=CARTESIAN_POINT('',(54.,0.,0.));
#1686=CARTESIAN_POINT('Origin',(54.,54.,0.));
#1687=CARTESIAN_POINT('',(54.,0.,-4.));
#1688=CARTESIAN_POINT('Origin',(54.,54.,-4.));
#1689=CARTESIAN_POINT('',(54.,0.,0.));
#1690=CARTESIAN_POINT('Origin',(57.5,-3.01435729535043E-17,0.));
#1691=CARTESIAN_POINT('',(57.5,0.,0.));
#1692=CARTESIAN_POINT('',(54.,0.,0.));
#1693=CARTESIAN_POINT('',(57.5,0.,-4.));
#1694=CARTESIAN_POINT('',(54.,0.,-4.));
#1695=CARTESIAN_POINT('',(57.5,0.,0.));
#1696=CARTESIAN_POINT('Origin',(57.5,2.,0.));
#1697=CARTESIAN_POINT('',(59.5,2.,0.));
#1698=CARTESIAN_POINT('Origin',(57.5,2.,0.));
#1699=CARTESIAN_POINT('',(59.5,2.00000000000003,-4.));
#1700=CARTESIAN_POINT('Origin',(57.5,2.,-4.));
#1701=CARTESIAN_POINT('',(59.5,2.,0.));
#1702=CARTESIAN_POINT('Origin',(59.5,11.5,0.));
#1703=CARTESIAN_POINT('',(59.5,11.5,0.));
#1704=CARTESIAN_POINT('',(59.5,11.5,0.));
#1705=CARTESIAN_POINT('',(59.5,11.5,-4.));
#1706=CARTESIAN_POINT('',(59.5,11.5,-4.));
#1707=CARTESIAN_POINT('',(59.5,11.5,0.));
#1708=CARTESIAN_POINT('Origin',(65.,11.5,0.));
#1709=CARTESIAN_POINT('',(70.5,11.5,0.));
#1710=CARTESIAN_POINT('Origin',(65.,11.5,0.));
#1711=CARTESIAN_POINT('',(70.5,11.5,-4.));
#1712=CARTESIAN_POINT('Origin',(65.,11.5,-4.));
#1713=CARTESIAN_POINT('',(70.5,11.5,0.));
#1714=CARTESIAN_POINT('Origin',(70.5,2.00000000000004,0.));
#1715=CARTESIAN_POINT('',(70.5,2.00000000000004,0.));
#1716=CARTESIAN_POINT('',(70.5,11.5,0.));
#1717=CARTESIAN_POINT('',(70.5,2.00000000000003,-4.));
#1718=CARTESIAN_POINT('',(70.5,11.5,-4.));
#1719=CARTESIAN_POINT('',(70.5,2.00000000000004,0.));
#1720=CARTESIAN_POINT('Origin',(72.5,2.00000000000004,0.));
#1721=CARTESIAN_POINT('',(72.5,4.79765288813228E-14,0.));
#1722=CARTESIAN_POINT('Origin',(72.5,2.00000000000004,0.));
#1723=CARTESIAN_POINT('',(72.5,7.105427357601E-14,-4.));
#1724=CARTESIAN_POINT('Origin',(72.5,2.00000000000004,-4.));
#1725=CARTESIAN_POINT('',(72.5,4.79765288813228E-14,0.));
#1726=CARTESIAN_POINT('Origin',(764.,4.85332505793129E-14,0.));
#1727=CARTESIAN_POINT('',(764.,4.85332505793129E-14,0.));
#1728=CARTESIAN_POINT('',(72.5,4.79765288813228E-14,0.));
#1729=CARTESIAN_POINT('',(764.,7.105427357601E-14,-4.));
#1730=CARTESIAN_POINT('',(72.5,4.79765288813228E-14,-4.));
#1731=CARTESIAN_POINT('',(764.,4.85332505793129E-14,0.));
#1732=CARTESIAN_POINT('Origin',(764.,2.00000000000004,0.));
#1733=CARTESIAN_POINT('',(766.,2.00000000000004,0.));
#1734=CARTESIAN_POINT('Origin',(764.,2.00000000000004,0.));
#1735=CARTESIAN_POINT('',(766.,2.00000000000003,-4.));
#1736=CARTESIAN_POINT('Origin',(764.,2.00000000000004,-4.));
#1737=CARTESIAN_POINT('',(766.,2.00000000000004,0.));
#1738=CARTESIAN_POINT('Origin',(766.,11.5,0.));
#1739=CARTESIAN_POINT('',(766.,11.5,0.));
#1740=CARTESIAN_POINT('',(766.,11.5,0.));
#1741=CARTESIAN_POINT('',(766.,11.5,-4.));
#1742=CARTESIAN_POINT('',(766.,11.5,-4.));
#1743=CARTESIAN_POINT('',(766.,11.5,0.));
#1744=CARTESIAN_POINT('Origin',(771.5,11.5,0.));
#1745=CARTESIAN_POINT('',(777.,11.5,0.));
#1746=CARTESIAN_POINT('Origin',(771.5,11.5,0.));
#1747=CARTESIAN_POINT('',(777.,11.5,-4.));
#1748=CARTESIAN_POINT('Origin',(771.5,11.5,-4.));
#1749=CARTESIAN_POINT('',(777.,11.5,0.));
#1750=CARTESIAN_POINT('Origin',(777.,2.06013092045767,0.));
#1751=CARTESIAN_POINT('',(777.,2.06013092045767,0.));
#1752=CARTESIAN_POINT('',(777.,11.5,0.));
#1753=CARTESIAN_POINT('',(777.,2.06013092045765,-4.));
#1754=CARTESIAN_POINT('',(777.,11.5,-4.));
#1755=CARTESIAN_POINT('',(777.,2.06013092045767,0.));
#1756=CARTESIAN_POINT('Origin',(779.,2.06013092045767,0.));
#1757=CARTESIAN_POINT('',(779.096153846154,0.0624436481675553,0.));
#1758=CARTESIAN_POINT('Origin',(779.,2.06013092045767,0.));
#1759=CARTESIAN_POINT('',(779.096153846154,0.0624436481675872,-4.));
#1760=CARTESIAN_POINT('Origin',(779.,2.06013092045767,-4.));
#1761=CARTESIAN_POINT('',(779.096153846154,0.0624436481675553,0.));
#1762=CARTESIAN_POINT('Origin',(776.5,54.0000000000002,0.));
#1763=CARTESIAN_POINT('',(830.5,54.,0.));
#1764=CARTESIAN_POINT('Origin',(776.5,54.0000000000002,0.));
#1765=CARTESIAN_POINT('',(830.5,54.,-4.));
#1766=CARTESIAN_POINT('Origin',(776.5,54.0000000000002,-4.));
#1767=CARTESIAN_POINT('',(830.5,54.,0.));
#1768=CARTESIAN_POINT('Origin',(830.5,270.5,0.));
#1769=CARTESIAN_POINT('',(830.5,270.5,0.));
#1770=CARTESIAN_POINT('',(830.5,54.,0.));
#1771=CARTESIAN_POINT('',(830.5,270.5,-4.));
#1772=CARTESIAN_POINT('',(830.5,54.,-4.));
#1773=CARTESIAN_POINT('',(830.5,270.5,0.));
#1774=CARTESIAN_POINT('Origin',(777.,270.5,0.));
#1775=CARTESIAN_POINT('',(777.,324.,0.));
#1776=CARTESIAN_POINT('Origin',(777.,270.5,0.));
#1777=CARTESIAN_POINT('',(777.,324.,-4.));
#1778=CARTESIAN_POINT('Origin',(777.,270.5,-4.));
#1779=CARTESIAN_POINT('',(777.,324.,0.));
#1780=CARTESIAN_POINT('Origin',(734.,324.,0.));
#1781=CARTESIAN_POINT('',(734.,324.,0.));
#1782=CARTESIAN_POINT('',(734.,324.,0.));
#1783=CARTESIAN_POINT('',(734.,324.,-4.));
#1784=CARTESIAN_POINT('',(734.,324.,-4.));
#1785=CARTESIAN_POINT('',(734.,324.,0.));
#1786=CARTESIAN_POINT('Origin',(734.,344.,0.));
#1787=CARTESIAN_POINT('',(714.880195599022,338.132029339853,0.));
#1788=CARTESIAN_POINT('Origin',(734.,344.,0.));
#1789=CARTESIAN_POINT('',(714.880195599022,338.132029339853,-4.));
#1790=CARTESIAN_POINT('Origin',(734.,344.,-4.));
#1791=CARTESIAN_POINT('',(714.880195599022,338.132029339853,0.));
#1792=CARTESIAN_POINT('Origin',(710.919315403423,351.037897310513,0.));
#1793=CARTESIAN_POINT('',(710.919315403423,351.037897310513,0.));
#1794=CARTESIAN_POINT('',(714.880195599022,338.132029339853,0.));
#1795=CARTESIAN_POINT('',(710.919315403423,351.037897310513,-4.));
#1796=CARTESIAN_POINT('',(714.880195599022,338.132029339853,-4.));
#1797=CARTESIAN_POINT('',(710.919315403423,351.037897310513,0.));
#1798=CARTESIAN_POINT('Origin',(644.,330.5,0.));
#1799=CARTESIAN_POINT('',(670.511043479133,395.285527501499,0.));
#1800=CARTESIAN_POINT('Origin',(644.,330.5,0.));
#1801=CARTESIAN_POINT('',(670.511043479133,395.285527501499,-4.));
#1802=CARTESIAN_POINT('Origin',(644.,330.5,-4.));
#1803=CARTESIAN_POINT('',(670.511043479133,395.285527501499,0.));
#1804=CARTESIAN_POINT('Origin',(629.64077226613,412.010150714714,0.));
#1805=CARTESIAN_POINT('',(629.64077226613,412.010150714714,0.));
#1806=CARTESIAN_POINT('',(629.64077226613,412.010150714714,0.));
#1807=CARTESIAN_POINT('',(629.64077226613,412.010150714714,-4.));
#1808=CARTESIAN_POINT('',(629.64077226613,412.010150714714,-4.));
#1809=CARTESIAN_POINT('',(629.64077226613,412.010150714714,0.));
#1810=CARTESIAN_POINT('Origin',(622.066188414949,393.500000000002,0.));
#1811=CARTESIAN_POINT('',(622.066188414949,413.5,0.));
#1812=CARTESIAN_POINT('Origin',(622.066188414949,393.500000000002,0.));
#1813=CARTESIAN_POINT('',(622.066188414949,413.5,-4.));
#1814=CARTESIAN_POINT('Origin',(622.066188414949,393.500000000002,-4.));
#1815=CARTESIAN_POINT('',(622.066188414949,413.5,0.));
#1816=CARTESIAN_POINT('Origin',(486.5,413.5,0.));
#1817=CARTESIAN_POINT('',(486.5,413.5,0.));
#1818=CARTESIAN_POINT('',(622.066188414949,413.5,0.));
#1819=CARTESIAN_POINT('',(486.5,413.5,-4.));
#1820=CARTESIAN_POINT('',(622.066188414949,413.5,-4.));
#1821=CARTESIAN_POINT('',(486.5,413.5,0.));
#1822=CARTESIAN_POINT('Origin',(486.5,433.5,0.));
#1823=CARTESIAN_POINT('',(466.5,433.5,0.));
#1824=CARTESIAN_POINT('Origin',(486.5,433.5,0.));
#1825=CARTESIAN_POINT('',(466.5,433.5,-4.));
#1826=CARTESIAN_POINT('Origin',(486.5,433.5,-4.));
#1827=CARTESIAN_POINT('',(466.5,433.5,0.));
#1828=CARTESIAN_POINT('Origin',(466.5,445.621653992437,0.));
#1829=CARTESIAN_POINT('',(466.5,445.621653992437,0.));
#1830=CARTESIAN_POINT('',(466.5,445.621653992437,0.));
#1831=CARTESIAN_POINT('',(466.5,445.621653992437,-4.));
#1832=CARTESIAN_POINT('',(466.5,445.621653992437,-4.));
#1833=CARTESIAN_POINT('',(466.5,445.621653992437,0.));
#1834=CARTESIAN_POINT('Origin',(464.5,445.621653992437,0.));
#1835=CARTESIAN_POINT('',(464.5,447.621653992437,0.));
#1836=CARTESIAN_POINT('Origin',(464.5,445.621653992437,0.));
#1837=CARTESIAN_POINT('',(464.5,447.621653992437,-4.));
#1838=CARTESIAN_POINT('Origin',(464.5,445.621653992437,-4.));
#1839=CARTESIAN_POINT('',(464.5,447.621653992437,0.));
#1840=CARTESIAN_POINT('Origin',(413.781410726175,447.621653992437,0.));
#1841=CARTESIAN_POINT('',(413.781410726175,447.621653992437,0.));
#1842=CARTESIAN_POINT('',(464.5,447.621653992437,0.));
#1843=CARTESIAN_POINT('',(413.781410726175,447.621653992437,-4.));
#1844=CARTESIAN_POINT('',(464.5,447.621653992437,-4.));
#1845=CARTESIAN_POINT('',(413.781410726175,447.621653992437,0.));
#1846=CARTESIAN_POINT('Origin',(413.781410726175,449.621653992438,0.));
#1847=CARTESIAN_POINT('',(411.849330150217,449.104871012832,0.));
#1848=CARTESIAN_POINT('Origin',(413.781410726175,449.621653992438,0.));
#1849=CARTESIAN_POINT('',(411.849330150217,449.104871012832,-4.));
#1850=CARTESIAN_POINT('Origin',(413.781410726175,449.621653992438,-4.));
#1851=CARTESIAN_POINT('',(411.849330150217,449.104871012832,0.));
#1852=CARTESIAN_POINT('Origin',(408.820037373651,460.430394421056,0.));
#1853=CARTESIAN_POINT('',(408.820037373651,460.430394421056,0.));
#1854=CARTESIAN_POINT('',(408.820037373651,460.430394421056,0.));
#1855=CARTESIAN_POINT('',(408.820037373651,460.430394421056,-4.));
#1856=CARTESIAN_POINT('',(408.820037373651,460.430394421056,-4.));
#1857=CARTESIAN_POINT('',(408.820037373651,460.430394421056,0.));
#1858=CARTESIAN_POINT('Origin',(402.057755357802,458.621653992437,0.));
#1859=CARTESIAN_POINT('Origin',(402.057755357802,458.621653992437,0.));
#1860=CARTESIAN_POINT('Origin',(402.057755357802,458.621653992437,-4.));
#1861=CARTESIAN_POINT('Origin',(415.25,232.810826996219,0.));
#1862=CARTESIAN_POINT('Origin',(415.25,232.810826996219,-4.));
#1863=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#1867,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#1864=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#1867,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#1865=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1863))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1867,#1869,#1870))
REPRESENTATION_CONTEXT('','3D')
);
#1866=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1864))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1867,#1869,#1870))
REPRESENTATION_CONTEXT('','3D')
);
#1867=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#1868=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT($,.METRE.)
);
#1869=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#1870=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#1871=SHAPE_DEFINITION_REPRESENTATION(#1872,#1873);
#1872=PRODUCT_DEFINITION_SHAPE('',$,#1875);
#1873=SHAPE_REPRESENTATION('',(#1107),#1865);
#1874=PRODUCT_DEFINITION_CONTEXT('part definition',#1879,'design');
#1875=PRODUCT_DEFINITION('S_1104','S_1104 v1',#1876,#1874);
#1876=PRODUCT_DEFINITION_FORMATION('',$,#1881);
#1877=PRODUCT_RELATED_PRODUCT_CATEGORY('S_1104 v1','S_1104 v1',(#1881));
#1878=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2009,#1879);
#1879=APPLICATION_CONTEXT(
'Core Data for Automotive Mechanical Design Process');
#1880=PRODUCT_CONTEXT('part definition',#1879,'mechanical');
#1881=PRODUCT('S_1104','S_1104 v1',$,(#1880));
#1882=PRESENTATION_STYLE_ASSIGNMENT((#1883));
#1883=SURFACE_STYLE_USAGE(.BOTH.,#1884);
#1884=SURFACE_SIDE_STYLE('',(#1885));
#1885=SURFACE_STYLE_FILL_AREA(#1886);
#1886=FILL_AREA_STYLE('Steel - Satin',(#1887));
#1887=FILL_AREA_STYLE_COLOUR('Steel - Satin',#1888);
#1888=COLOUR_RGB('Steel - Satin',0.627450980392157,0.627450980392157,0.627450980392157);
ENDSEC;
END-ISO-10303-21;
