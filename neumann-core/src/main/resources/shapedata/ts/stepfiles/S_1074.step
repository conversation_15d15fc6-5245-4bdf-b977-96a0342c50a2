ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ (''),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 
'G:/Shared drives/TIP/Validation/SMF Tset Test Shapes/S_1074.step',
/* time_stamp */ '2021-11-25T11:44:33+01:00',
/* author */ (''),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v18.1',
/* originating_system */ 'Autodesk Translation Framework v10.10.0.1391',

/* authorisation */ '');

FILE_SCHEMA (('AUTOMOTIVE_DESIGN { 1 0 10303 214 3 1 1 }'));
ENDSEC;

DATA;
#10=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#13),#2199);
#11=SHAPE_REPRESENTATION_RELATIONSHIP('SRR','None',#2206,#12);
#12=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#14),#2198);
#13=STYLED_ITEM('',(#2215),#14);
#14=MANIFOLD_SOLID_BREP('Body1',#1283);
#15=B_SPLINE_SURFACE_WITH_KNOTS('',3,3,((#1958,#1959,#1960,#1961),(#1962,
#1963,#1964,#1965),(#1966,#1967,#1968,#1969),(#1970,#1971,#1972,#1973)),
 .UNSPECIFIED.,.F.,.F.,.F.,(4,4),(4,4),(0.987026567309782,1.67600430292807),
(0.,0.5),.UNSPECIFIED.);
#16=B_SPLINE_SURFACE_WITH_KNOTS('',3,3,((#2099,#2100,#2101,#2102),(#2103,
#2104,#2105,#2106),(#2107,#2108,#2109,#2110),(#2111,#2112,#2113,#2114),
(#2115,#2116,#2117,#2118),(#2119,#2120,#2121,#2122)),.UNSPECIFIED.,.F.,
 .F.,.F.,(4,1,1,4),(4,4),(0.574639141185481,1.07003304800849,1.40029565255717,
1.73055825710584),(0.,0.5),.UNSPECIFIED.);
#17=B_SPLINE_CURVE_WITH_KNOTS('',3,(#1914,#1915,#1916,#1917),
 .UNSPECIFIED.,.F.,.F.,(4,4),(0.,0.733038285837617),.UNSPECIFIED.);
#18=B_SPLINE_CURVE_WITH_KNOTS('',3,(#1977,#1978,#1979,#1980),
 .UNSPECIFIED.,.F.,.F.,(4,4),(0.,0.733038285837617),.UNSPECIFIED.);
#19=B_SPLINE_CURVE_WITH_KNOTS('',3,(#2041,#2042,#2043,#2044,#2045,#2046,
#2047,#2048,#2049,#2050),.UNSPECIFIED.,.F.,.F.,(4,3,3,4),(0.,0.673198425769241,
1.12199737628207,1.5707963267949),.UNSPECIFIED.);
#20=B_SPLINE_CURVE_WITH_KNOTS('',3,(#2126,#2127,#2128,#2129,#2130,#2131,
#2132,#2133,#2134,#2135),.UNSPECIFIED.,.F.,.F.,(4,3,3,4),(0.,0.67319842576924,
1.12199737628207,1.5707963267949),.UNSPECIFIED.);
#21=FACE_BOUND('',#127,.T.);
#22=FACE_BOUND('',#150,.T.);
#23=FACE_BOUND('',#152,.T.);
#24=FACE_BOUND('',#197,.T.);
#25=CYLINDRICAL_SURFACE('',#1303,5.);
#26=CYLINDRICAL_SURFACE('',#1310,10.);
#27=CYLINDRICAL_SURFACE('',#1318,5.);
#28=CYLINDRICAL_SURFACE('',#1326,10.);
#29=CYLINDRICAL_SURFACE('',#1329,10.);
#30=CYLINDRICAL_SURFACE('',#1340,5.);
#31=CYLINDRICAL_SURFACE('',#1352,5.);
#32=CYLINDRICAL_SURFACE('',#1356,5.);
#33=CYLINDRICAL_SURFACE('',#1373,10.);
#34=CYLINDRICAL_SURFACE('',#1374,5.25);
#35=CYLINDRICAL_SURFACE('',#1376,7.5);
#36=CYLINDRICAL_SURFACE('',#1382,3.);
#37=CYLINDRICAL_SURFACE('',#1384,3.);
#38=CYLINDRICAL_SURFACE('',#1387,2.00000000000068);
#39=CYLINDRICAL_SURFACE('',#1389,2.00000000000008);
#40=CYLINDRICAL_SURFACE('',#1391,1.99999999999963);
#41=CYLINDRICAL_SURFACE('',#1393,10.);
#42=CYLINDRICAL_SURFACE('',#1395,2.0000000000001);
#43=CYLINDRICAL_SURFACE('',#1397,2.0000000000001);
#44=CYLINDRICAL_SURFACE('',#1399,10.);
#45=CYLINDRICAL_SURFACE('',#1405,10.);
#46=CYLINDRICAL_SURFACE('',#1408,10.);
#47=FACE_OUTER_BOUND('',#121,.T.);
#48=FACE_OUTER_BOUND('',#122,.T.);
#49=FACE_OUTER_BOUND('',#123,.T.);
#50=FACE_OUTER_BOUND('',#124,.T.);
#51=FACE_OUTER_BOUND('',#125,.T.);
#52=FACE_OUTER_BOUND('',#126,.T.);
#53=FACE_OUTER_BOUND('',#128,.T.);
#54=FACE_OUTER_BOUND('',#129,.T.);
#55=FACE_OUTER_BOUND('',#130,.T.);
#56=FACE_OUTER_BOUND('',#131,.T.);
#57=FACE_OUTER_BOUND('',#132,.T.);
#58=FACE_OUTER_BOUND('',#133,.T.);
#59=FACE_OUTER_BOUND('',#134,.T.);
#60=FACE_OUTER_BOUND('',#135,.T.);
#61=FACE_OUTER_BOUND('',#136,.T.);
#62=FACE_OUTER_BOUND('',#137,.T.);
#63=FACE_OUTER_BOUND('',#138,.T.);
#64=FACE_OUTER_BOUND('',#139,.T.);
#65=FACE_OUTER_BOUND('',#140,.T.);
#66=FACE_OUTER_BOUND('',#141,.T.);
#67=FACE_OUTER_BOUND('',#142,.T.);
#68=FACE_OUTER_BOUND('',#143,.T.);
#69=FACE_OUTER_BOUND('',#144,.T.);
#70=FACE_OUTER_BOUND('',#145,.T.);
#71=FACE_OUTER_BOUND('',#146,.T.);
#72=FACE_OUTER_BOUND('',#147,.T.);
#73=FACE_OUTER_BOUND('',#148,.T.);
#74=FACE_OUTER_BOUND('',#149,.T.);
#75=FACE_OUTER_BOUND('',#151,.T.);
#76=FACE_OUTER_BOUND('',#153,.T.);
#77=FACE_OUTER_BOUND('',#154,.T.);
#78=FACE_OUTER_BOUND('',#155,.T.);
#79=FACE_OUTER_BOUND('',#156,.T.);
#80=FACE_OUTER_BOUND('',#157,.T.);
#81=FACE_OUTER_BOUND('',#158,.T.);
#82=FACE_OUTER_BOUND('',#159,.T.);
#83=FACE_OUTER_BOUND('',#160,.T.);
#84=FACE_OUTER_BOUND('',#161,.T.);
#85=FACE_OUTER_BOUND('',#162,.T.);
#86=FACE_OUTER_BOUND('',#163,.T.);
#87=FACE_OUTER_BOUND('',#164,.T.);
#88=FACE_OUTER_BOUND('',#165,.T.);
#89=FACE_OUTER_BOUND('',#166,.T.);
#90=FACE_OUTER_BOUND('',#167,.T.);
#91=FACE_OUTER_BOUND('',#168,.T.);
#92=FACE_OUTER_BOUND('',#169,.T.);
#93=FACE_OUTER_BOUND('',#170,.T.);
#94=FACE_OUTER_BOUND('',#171,.T.);
#95=FACE_OUTER_BOUND('',#172,.T.);
#96=FACE_OUTER_BOUND('',#173,.T.);
#97=FACE_OUTER_BOUND('',#174,.T.);
#98=FACE_OUTER_BOUND('',#175,.T.);
#99=FACE_OUTER_BOUND('',#176,.T.);
#100=FACE_OUTER_BOUND('',#177,.T.);
#101=FACE_OUTER_BOUND('',#178,.T.);
#102=FACE_OUTER_BOUND('',#179,.T.);
#103=FACE_OUTER_BOUND('',#180,.T.);
#104=FACE_OUTER_BOUND('',#181,.T.);
#105=FACE_OUTER_BOUND('',#182,.T.);
#106=FACE_OUTER_BOUND('',#183,.T.);
#107=FACE_OUTER_BOUND('',#184,.T.);
#108=FACE_OUTER_BOUND('',#185,.T.);
#109=FACE_OUTER_BOUND('',#186,.T.);
#110=FACE_OUTER_BOUND('',#187,.T.);
#111=FACE_OUTER_BOUND('',#188,.T.);
#112=FACE_OUTER_BOUND('',#189,.T.);
#113=FACE_OUTER_BOUND('',#190,.T.);
#114=FACE_OUTER_BOUND('',#191,.T.);
#115=FACE_OUTER_BOUND('',#192,.T.);
#116=FACE_OUTER_BOUND('',#193,.T.);
#117=FACE_OUTER_BOUND('',#194,.T.);
#118=FACE_OUTER_BOUND('',#195,.T.);
#119=FACE_OUTER_BOUND('',#196,.T.);
#120=FACE_OUTER_BOUND('',#198,.T.);
#121=EDGE_LOOP('',(#799,#800,#801,#802,#803,#804,#805,#806,#807,#808));
#122=EDGE_LOOP('',(#809,#810,#811,#812));
#123=EDGE_LOOP('',(#813,#814,#815,#816));
#124=EDGE_LOOP('',(#817,#818,#819,#820));
#125=EDGE_LOOP('',(#821,#822,#823,#824));
#126=EDGE_LOOP('',(#825,#826,#827,#828));
#127=EDGE_LOOP('',(#829));
#128=EDGE_LOOP('',(#830,#831,#832,#833));
#129=EDGE_LOOP('',(#834,#835,#836,#837));
#130=EDGE_LOOP('',(#838,#839,#840,#841,#842,#843,#844,#845,#846,#847,#848,
#849,#850));
#131=EDGE_LOOP('',(#851,#852,#853,#854));
#132=EDGE_LOOP('',(#855,#856,#857,#858));
#133=EDGE_LOOP('',(#859,#860,#861,#862));
#134=EDGE_LOOP('',(#863,#864,#865,#866));
#135=EDGE_LOOP('',(#867,#868,#869,#870,#871,#872,#873,#874,#875,#876));
#136=EDGE_LOOP('',(#877,#878,#879,#880));
#137=EDGE_LOOP('',(#881,#882,#883,#884));
#138=EDGE_LOOP('',(#885,#886,#887,#888,#889));
#139=EDGE_LOOP('',(#890,#891,#892,#893));
#140=EDGE_LOOP('',(#894,#895,#896,#897));
#141=EDGE_LOOP('',(#898,#899,#900,#901,#902));
#142=EDGE_LOOP('',(#903,#904,#905,#906,#907));
#143=EDGE_LOOP('',(#908,#909,#910,#911));
#144=EDGE_LOOP('',(#912,#913,#914,#915,#916,#917,#918,#919,#920,#921,#922,
#923,#924));
#145=EDGE_LOOP('',(#925,#926,#927,#928,#929,#930));
#146=EDGE_LOOP('',(#931,#932,#933,#934));
#147=EDGE_LOOP('',(#935,#936,#937,#938));
#148=EDGE_LOOP('',(#939,#940,#941,#942));
#149=EDGE_LOOP('',(#943,#944,#945,#946,#947,#948));
#150=EDGE_LOOP('',(#949));
#151=EDGE_LOOP('',(#950,#951,#952,#953,#954,#955));
#152=EDGE_LOOP('',(#956));
#153=EDGE_LOOP('',(#957,#958,#959,#960));
#154=EDGE_LOOP('',(#961,#962,#963,#964));
#155=EDGE_LOOP('',(#965,#966,#967,#968));
#156=EDGE_LOOP('',(#969,#970,#971,#972,#973));
#157=EDGE_LOOP('',(#974,#975,#976,#977));
#158=EDGE_LOOP('',(#978,#979,#980,#981));
#159=EDGE_LOOP('',(#982,#983,#984,#985,#986,#987,#988,#989,#990,#991,#992,
#993));
#160=EDGE_LOOP('',(#994,#995,#996,#997,#998,#999,#1000,#1001,#1002,#1003,
#1004,#1005));
#161=EDGE_LOOP('',(#1006,#1007,#1008,#1009));
#162=EDGE_LOOP('',(#1010,#1011,#1012,#1013));
#163=EDGE_LOOP('',(#1014,#1015,#1016,#1017,#1018,#1019));
#164=EDGE_LOOP('',(#1020,#1021,#1022,#1023));
#165=EDGE_LOOP('',(#1024,#1025,#1026,#1027));
#166=EDGE_LOOP('',(#1028,#1029,#1030,#1031));
#167=EDGE_LOOP('',(#1032,#1033,#1034,#1035));
#168=EDGE_LOOP('',(#1036,#1037,#1038,#1039));
#169=EDGE_LOOP('',(#1040,#1041,#1042,#1043));
#170=EDGE_LOOP('',(#1044,#1045,#1046,#1047));
#171=EDGE_LOOP('',(#1048,#1049,#1050,#1051));
#172=EDGE_LOOP('',(#1052,#1053,#1054,#1055));
#173=EDGE_LOOP('',(#1056,#1057,#1058,#1059));
#174=EDGE_LOOP('',(#1060,#1061,#1062,#1063,#1064));
#175=EDGE_LOOP('',(#1065,#1066,#1067,#1068));
#176=EDGE_LOOP('',(#1069,#1070,#1071,#1072));
#177=EDGE_LOOP('',(#1073,#1074,#1075,#1076,#1077));
#178=EDGE_LOOP('',(#1078,#1079,#1080,#1081));
#179=EDGE_LOOP('',(#1082,#1083,#1084,#1085));
#180=EDGE_LOOP('',(#1086,#1087,#1088,#1089));
#181=EDGE_LOOP('',(#1090,#1091,#1092,#1093));
#182=EDGE_LOOP('',(#1094,#1095,#1096,#1097));
#183=EDGE_LOOP('',(#1098,#1099,#1100,#1101));
#184=EDGE_LOOP('',(#1102,#1103,#1104,#1105));
#185=EDGE_LOOP('',(#1106,#1107,#1108,#1109));
#186=EDGE_LOOP('',(#1110,#1111,#1112,#1113));
#187=EDGE_LOOP('',(#1114,#1115,#1116,#1117));
#188=EDGE_LOOP('',(#1118,#1119,#1120,#1121));
#189=EDGE_LOOP('',(#1122,#1123,#1124,#1125));
#190=EDGE_LOOP('',(#1126,#1127,#1128,#1129));
#191=EDGE_LOOP('',(#1130,#1131,#1132,#1133));
#192=EDGE_LOOP('',(#1134,#1135,#1136,#1137));
#193=EDGE_LOOP('',(#1138,#1139,#1140,#1141));
#194=EDGE_LOOP('',(#1142,#1143,#1144,#1145));
#195=EDGE_LOOP('',(#1146,#1147,#1148,#1149));
#196=EDGE_LOOP('',(#1150,#1151,#1152,#1153));
#197=EDGE_LOOP('',(#1154));
#198=EDGE_LOOP('',(#1155,#1156,#1157,#1158));
#199=CIRCLE('',#1298,3.);
#200=CIRCLE('',#1299,10.);
#201=CIRCLE('',#1301,5.);
#202=CIRCLE('',#1302,10.);
#203=CIRCLE('',#1304,5.00000000000001);
#204=CIRCLE('',#1308,5.25);
#205=CIRCLE('',#1311,10.);
#206=CIRCLE('',#1313,10.);
#207=CIRCLE('',#1314,3.);
#208=CIRCLE('',#1316,5.);
#209=CIRCLE('',#1317,10.);
#210=CIRCLE('',#1319,5.);
#211=CIRCLE('',#1323,10.);
#212=CIRCLE('',#1324,3.);
#213=CIRCLE('',#1327,10.);
#214=CIRCLE('',#1330,10.);
#215=CIRCLE('',#1332,5.);
#216=CIRCLE('',#1337,3.);
#217=CIRCLE('',#1338,10.);
#218=CIRCLE('',#1341,5.);
#219=CIRCLE('',#1342,5.);
#220=CIRCLE('',#1344,10.);
#221=CIRCLE('',#1346,2.00000000000068);
#222=CIRCLE('',#1347,7.5);
#223=CIRCLE('',#1349,2.00000000000068);
#224=CIRCLE('',#1350,7.5);
#225=CIRCLE('',#1354,10.);
#226=CIRCLE('',#1357,5.);
#227=CIRCLE('',#1359,10.);
#228=CIRCLE('',#1361,2.0000000000001);
#229=CIRCLE('',#1362,2.0000000000001);
#230=CIRCLE('',#1363,10.);
#231=CIRCLE('',#1364,1.99999999999963);
#232=CIRCLE('',#1365,2.00000000000008);
#233=CIRCLE('',#1367,2.00000000000008);
#234=CIRCLE('',#1368,1.99999999999963);
#235=CIRCLE('',#1369,10.);
#236=CIRCLE('',#1370,2.0000000000001);
#237=CIRCLE('',#1371,2.0000000000001);
#238=CIRCLE('',#1375,5.25);
#239=LINE('',#1775,#375);
#240=LINE('',#1777,#376);
#241=LINE('',#1781,#377);
#242=LINE('',#1783,#378);
#243=LINE('',#1785,#379);
#244=LINE('',#1789,#380);
#245=LINE('',#1791,#381);
#246=LINE('',#1792,#382);
#247=LINE('',#1795,#383);
#248=LINE('',#1799,#384);
#249=LINE('',#1803,#385);
#250=LINE('',#1806,#386);
#251=LINE('',#1808,#387);
#252=LINE('',#1810,#388);
#253=LINE('',#1811,#389);
#254=LINE('',#1814,#390);
#255=LINE('',#1815,#391);
#256=LINE('',#1818,#392);
#257=LINE('',#1820,#393);
#258=LINE('',#1821,#394);
#259=LINE('',#1826,#395);
#260=LINE('',#1828,#396);
#261=LINE('',#1829,#397);
#262=LINE('',#1831,#398);
#263=LINE('',#1836,#399);
#264=LINE('',#1838,#400);
#265=LINE('',#1840,#401);
#266=LINE('',#1842,#402);
#267=LINE('',#1846,#403);
#268=LINE('',#1848,#404);
#269=LINE('',#1850,#405);
#270=LINE('',#1852,#406);
#271=LINE('',#1854,#407);
#272=LINE('',#1856,#408);
#273=LINE('',#1859,#409);
#274=LINE('',#1862,#410);
#275=LINE('',#1867,#411);
#276=LINE('',#1870,#412);
#277=LINE('',#1872,#413);
#278=LINE('',#1873,#414);
#279=LINE('',#1876,#415);
#280=LINE('',#1877,#416);
#281=LINE('',#1880,#417);
#282=LINE('',#1882,#418);
#283=LINE('',#1886,#419);
#284=LINE('',#1888,#420);
#285=LINE('',#1890,#421);
#286=LINE('',#1894,#422);
#287=LINE('',#1895,#423);
#288=LINE('',#1897,#424);
#289=LINE('',#1902,#425);
#290=LINE('',#1904,#426);
#291=LINE('',#1906,#427);
#292=LINE('',#1908,#428);
#293=LINE('',#1909,#429);
#294=LINE('',#1912,#430);
#295=LINE('',#1920,#431);
#296=LINE('',#1922,#432);
#297=LINE('',#1924,#433);
#298=LINE('',#1926,#434);
#299=LINE('',#1928,#435);
#300=LINE('',#1929,#436);
#301=LINE('',#1932,#437);
#302=LINE('',#1934,#438);
#303=LINE('',#1935,#439);
#304=LINE('',#1937,#440);
#305=LINE('',#1942,#441);
#306=LINE('',#1944,#442);
#307=LINE('',#1946,#443);
#308=LINE('',#1948,#444);
#309=LINE('',#1950,#445);
#310=LINE('',#1952,#446);
#311=LINE('',#1956,#447);
#312=LINE('',#1957,#448);
#313=LINE('',#1975,#449);
#314=LINE('',#1976,#450);
#315=LINE('',#1984,#451);
#316=LINE('',#1986,#452);
#317=LINE('',#1988,#453);
#318=LINE('',#1989,#454);
#319=LINE('',#1992,#455);
#320=LINE('',#1997,#456);
#321=LINE('',#1999,#457);
#322=LINE('',#2001,#458);
#323=LINE('',#2003,#459);
#324=LINE('',#2005,#460);
#325=LINE('',#2007,#461);
#326=LINE('',#2015,#462);
#327=LINE('',#2017,#463);
#328=LINE('',#2018,#464);
#329=LINE('',#2024,#465);
#330=LINE('',#2029,#466);
#331=LINE('',#2031,#467);
#332=LINE('',#2033,#468);
#333=LINE('',#2035,#469);
#334=LINE('',#2036,#470);
#335=LINE('',#2039,#471);
#336=LINE('',#2053,#472);
#337=LINE('',#2055,#473);
#338=LINE('',#2057,#474);
#339=LINE('',#2059,#475);
#340=LINE('',#2063,#476);
#341=LINE('',#2067,#477);
#342=LINE('',#2071,#478);
#343=LINE('',#2075,#479);
#344=LINE('',#2081,#480);
#345=LINE('',#2085,#481);
#346=LINE('',#2089,#482);
#347=LINE('',#2093,#483);
#348=LINE('',#2096,#484);
#349=LINE('',#2124,#485);
#350=LINE('',#2125,#486);
#351=LINE('',#2138,#487);
#352=LINE('',#2141,#488);
#353=LINE('',#2143,#489);
#354=LINE('',#2144,#490);
#355=LINE('',#2146,#491);
#356=LINE('',#2148,#492);
#357=LINE('',#2150,#493);
#358=LINE('',#2152,#494);
#359=LINE('',#2156,#495);
#360=LINE('',#2160,#496);
#361=LINE('',#2163,#497);
#362=LINE('',#2165,#498);
#363=LINE('',#2167,#499);
#364=LINE('',#2169,#500);
#365=LINE('',#2171,#501);
#366=LINE('',#2173,#502);
#367=LINE('',#2175,#503);
#368=LINE('',#2177,#504);
#369=LINE('',#2179,#505);
#370=LINE('',#2182,#506);
#371=LINE('',#2184,#507);
#372=LINE('',#2188,#508);
#373=LINE('',#2190,#509);
#374=LINE('',#2192,#510);
#375=VECTOR('',#1413,10.);
#376=VECTOR('',#1414,10.);
#377=VECTOR('',#1417,10.);
#378=VECTOR('',#1418,10.);
#379=VECTOR('',#1419,10.);
#380=VECTOR('',#1422,10.);
#381=VECTOR('',#1423,10.);
#382=VECTOR('',#1424,10.);
#383=VECTOR('',#1427,10.);
#384=VECTOR('',#1430,10.);
#385=VECTOR('',#1435,10.);
#386=VECTOR('',#1438,10.);
#387=VECTOR('',#1441,10.);
#388=VECTOR('',#1442,10.);
#389=VECTOR('',#1443,10.);
#390=VECTOR('',#1446,10.);
#391=VECTOR('',#1447,10.);
#392=VECTOR('',#1450,10.);
#393=VECTOR('',#1451,10.);
#394=VECTOR('',#1452,10.);
#395=VECTOR('',#1457,10.);
#396=VECTOR('',#1458,10.);
#397=VECTOR('',#1459,10.);
#398=VECTOR('',#1462,10.);
#399=VECTOR('',#1467,10.);
#400=VECTOR('',#1468,10.);
#401=VECTOR('',#1469,10.);
#402=VECTOR('',#1470,10.);
#403=VECTOR('',#1473,10.);
#404=VECTOR('',#1474,10.);
#405=VECTOR('',#1475,10.);
#406=VECTOR('',#1476,10.);
#407=VECTOR('',#1477,10.);
#408=VECTOR('',#1478,10.);
#409=VECTOR('',#1481,10.);
#410=VECTOR('',#1484,10.);
#411=VECTOR('',#1491,10.);
#412=VECTOR('',#1496,10.);
#413=VECTOR('',#1497,10.);
#414=VECTOR('',#1498,10.);
#415=VECTOR('',#1501,10.);
#416=VECTOR('',#1502,10.);
#417=VECTOR('',#1505,10.);
#418=VECTOR('',#1506,10.);
#419=VECTOR('',#1509,10.);
#420=VECTOR('',#1510,10.);
#421=VECTOR('',#1511,10.);
#422=VECTOR('',#1516,10.);
#423=VECTOR('',#1517,10.);
#424=VECTOR('',#1520,10.);
#425=VECTOR('',#1525,10.);
#426=VECTOR('',#1526,10.);
#427=VECTOR('',#1527,10.);
#428=VECTOR('',#1528,10.);
#429=VECTOR('',#1529,10.);
#430=VECTOR('',#1532,10.);
#431=VECTOR('',#1537,10.);
#432=VECTOR('',#1540,10.);
#433=VECTOR('',#1543,10.);
#434=VECTOR('',#1544,10.);
#435=VECTOR('',#1545,10.);
#436=VECTOR('',#1546,10.);
#437=VECTOR('',#1549,10.);
#438=VECTOR('',#1550,10.);
#439=VECTOR('',#1551,10.);
#440=VECTOR('',#1554,10.);
#441=VECTOR('',#1559,10.);
#442=VECTOR('',#1560,10.);
#443=VECTOR('',#1561,10.);
#444=VECTOR('',#1562,10.);
#445=VECTOR('',#1563,10.);
#446=VECTOR('',#1564,10.);
#447=VECTOR('',#1567,10.);
#448=VECTOR('',#1568,10.);
#449=VECTOR('',#1569,10.);
#450=VECTOR('',#1570,10.);
#451=VECTOR('',#1573,10.);
#452=VECTOR('',#1574,10.);
#453=VECTOR('',#1575,10.);
#454=VECTOR('',#1576,10.);
#455=VECTOR('',#1579,10.);
#456=VECTOR('',#1586,10.);
#457=VECTOR('',#1589,10.);
#458=VECTOR('',#1592,10.);
#459=VECTOR('',#1593,10.);
#460=VECTOR('',#1594,10.);
#461=VECTOR('',#1595,10.);
#462=VECTOR('',#1604,10.);
#463=VECTOR('',#1605,10.);
#464=VECTOR('',#1606,10.);
#465=VECTOR('',#1615,10.);
#466=VECTOR('',#1620,10.);
#467=VECTOR('',#1621,10.);
#468=VECTOR('',#1622,10.);
#469=VECTOR('',#1623,10.);
#470=VECTOR('',#1624,10.);
#471=VECTOR('',#1627,10.);
#472=VECTOR('',#1632,10.);
#473=VECTOR('',#1635,10.);
#474=VECTOR('',#1638,10.);
#475=VECTOR('',#1639,10.);
#476=VECTOR('',#1642,10.);
#477=VECTOR('',#1645,10.);
#478=VECTOR('',#1648,10.);
#479=VECTOR('',#1651,10.);
#480=VECTOR('',#1658,10.);
#481=VECTOR('',#1661,10.);
#482=VECTOR('',#1664,10.);
#483=VECTOR('',#1667,10.);
#484=VECTOR('',#1670,10.);
#485=VECTOR('',#1675,10.);
#486=VECTOR('',#1676,10.);
#487=VECTOR('',#1679,5.25);
#488=VECTOR('',#1684,7.5);
#489=VECTOR('',#1687,10.);
#490=VECTOR('',#1688,10.);
#491=VECTOR('',#1691,10.);
#492=VECTOR('',#1694,10.);
#493=VECTOR('',#1697,10.);
#494=VECTOR('',#1700,10.);
#495=VECTOR('',#1707,10.);
#496=VECTOR('',#1714,10.);
#497=VECTOR('',#1719,10.);
#498=VECTOR('',#1722,10.);
#499=VECTOR('',#1725,10.);
#500=VECTOR('',#1728,10.);
#501=VECTOR('',#1731,10.);
#502=VECTOR('',#1734,10.);
#503=VECTOR('',#1737,10.);
#504=VECTOR('',#1740,10.);
#505=VECTOR('',#1743,10.);
#506=VECTOR('',#1748,10.);
#507=VECTOR('',#1751,10.);
#508=VECTOR('',#1758,10.);
#509=VECTOR('',#1761,10.);
#510=VECTOR('',#1764,10.);
#511=VERTEX_POINT('',#1773);
#512=VERTEX_POINT('',#1774);
#513=VERTEX_POINT('',#1776);
#514=VERTEX_POINT('',#1778);
#515=VERTEX_POINT('',#1780);
#516=VERTEX_POINT('',#1782);
#517=VERTEX_POINT('',#1784);
#518=VERTEX_POINT('',#1786);
#519=VERTEX_POINT('',#1788);
#520=VERTEX_POINT('',#1790);
#521=VERTEX_POINT('',#1794);
#522=VERTEX_POINT('',#1796);
#523=VERTEX_POINT('',#1798);
#524=VERTEX_POINT('',#1802);
#525=VERTEX_POINT('',#1804);
#526=VERTEX_POINT('',#1809);
#527=VERTEX_POINT('',#1813);
#528=VERTEX_POINT('',#1817);
#529=VERTEX_POINT('',#1819);
#530=VERTEX_POINT('',#1822);
#531=VERTEX_POINT('',#1825);
#532=VERTEX_POINT('',#1827);
#533=VERTEX_POINT('',#1834);
#534=VERTEX_POINT('',#1835);
#535=VERTEX_POINT('',#1837);
#536=VERTEX_POINT('',#1839);
#537=VERTEX_POINT('',#1841);
#538=VERTEX_POINT('',#1843);
#539=VERTEX_POINT('',#1845);
#540=VERTEX_POINT('',#1847);
#541=VERTEX_POINT('',#1849);
#542=VERTEX_POINT('',#1851);
#543=VERTEX_POINT('',#1853);
#544=VERTEX_POINT('',#1855);
#545=VERTEX_POINT('',#1857);
#546=VERTEX_POINT('',#1861);
#547=VERTEX_POINT('',#1866);
#548=VERTEX_POINT('',#1871);
#549=VERTEX_POINT('',#1875);
#550=VERTEX_POINT('',#1879);
#551=VERTEX_POINT('',#1881);
#552=VERTEX_POINT('',#1883);
#553=VERTEX_POINT('',#1885);
#554=VERTEX_POINT('',#1887);
#555=VERTEX_POINT('',#1889);
#556=VERTEX_POINT('',#1893);
#557=VERTEX_POINT('',#1900);
#558=VERTEX_POINT('',#1901);
#559=VERTEX_POINT('',#1903);
#560=VERTEX_POINT('',#1905);
#561=VERTEX_POINT('',#1907);
#562=VERTEX_POINT('',#1911);
#563=VERTEX_POINT('',#1919);
#564=VERTEX_POINT('',#1925);
#565=VERTEX_POINT('',#1927);
#566=VERTEX_POINT('',#1931);
#567=VERTEX_POINT('',#1933);
#568=VERTEX_POINT('',#1939);
#569=VERTEX_POINT('',#1941);
#570=VERTEX_POINT('',#1943);
#571=VERTEX_POINT('',#1945);
#572=VERTEX_POINT('',#1947);
#573=VERTEX_POINT('',#1949);
#574=VERTEX_POINT('',#1951);
#575=VERTEX_POINT('',#1953);
#576=VERTEX_POINT('',#1955);
#577=VERTEX_POINT('',#1974);
#578=VERTEX_POINT('',#1982);
#579=VERTEX_POINT('',#1983);
#580=VERTEX_POINT('',#1985);
#581=VERTEX_POINT('',#1987);
#582=VERTEX_POINT('',#1991);
#583=VERTEX_POINT('',#1996);
#584=VERTEX_POINT('',#2002);
#585=VERTEX_POINT('',#2004);
#586=VERTEX_POINT('',#2006);
#587=VERTEX_POINT('',#2009);
#588=VERTEX_POINT('',#2012);
#589=VERTEX_POINT('',#2014);
#590=VERTEX_POINT('',#2016);
#591=VERTEX_POINT('',#2019);
#592=VERTEX_POINT('',#2027);
#593=VERTEX_POINT('',#2028);
#594=VERTEX_POINT('',#2030);
#595=VERTEX_POINT('',#2032);
#596=VERTEX_POINT('',#2034);
#597=VERTEX_POINT('',#2038);
#598=VERTEX_POINT('',#2052);
#599=VERTEX_POINT('',#2058);
#600=VERTEX_POINT('',#2060);
#601=VERTEX_POINT('',#2062);
#602=VERTEX_POINT('',#2064);
#603=VERTEX_POINT('',#2066);
#604=VERTEX_POINT('',#2068);
#605=VERTEX_POINT('',#2070);
#606=VERTEX_POINT('',#2072);
#607=VERTEX_POINT('',#2074);
#608=VERTEX_POINT('',#2078);
#609=VERTEX_POINT('',#2080);
#610=VERTEX_POINT('',#2082);
#611=VERTEX_POINT('',#2084);
#612=VERTEX_POINT('',#2086);
#613=VERTEX_POINT('',#2088);
#614=VERTEX_POINT('',#2090);
#615=VERTEX_POINT('',#2092);
#616=VERTEX_POINT('',#2094);
#617=VERTEX_POINT('',#2123);
#618=VERTEX_POINT('',#2137);
#619=EDGE_CURVE('',#511,#512,#239,.T.);
#620=EDGE_CURVE('',#512,#513,#240,.T.);
#621=EDGE_CURVE('',#514,#513,#199,.T.);
#622=EDGE_CURVE('',#515,#514,#241,.T.);
#623=EDGE_CURVE('',#516,#515,#242,.T.);
#624=EDGE_CURVE('',#517,#516,#243,.T.);
#625=EDGE_CURVE('',#518,#517,#200,.T.);
#626=EDGE_CURVE('',#519,#518,#244,.T.);
#627=EDGE_CURVE('',#519,#520,#245,.T.);
#628=EDGE_CURVE('',#520,#511,#246,.T.);
#629=EDGE_CURVE('',#521,#511,#247,.T.);
#630=EDGE_CURVE('',#522,#521,#201,.T.);
#631=EDGE_CURVE('',#522,#523,#248,.T.);
#632=EDGE_CURVE('',#511,#523,#202,.T.);
#633=EDGE_CURVE('',#524,#521,#249,.T.);
#634=EDGE_CURVE('',#524,#525,#203,.T.);
#635=EDGE_CURVE('',#525,#522,#250,.T.);
#636=EDGE_CURVE('',#512,#524,#251,.T.);
#637=EDGE_CURVE('',#526,#524,#252,.T.);
#638=EDGE_CURVE('',#513,#526,#253,.T.);
#639=EDGE_CURVE('',#520,#527,#254,.T.);
#640=EDGE_CURVE('',#521,#527,#255,.T.);
#641=EDGE_CURVE('',#525,#528,#256,.T.);
#642=EDGE_CURVE('',#529,#528,#257,.T.);
#643=EDGE_CURVE('',#529,#522,#258,.T.);
#644=EDGE_CURVE('',#530,#530,#204,.T.);
#645=EDGE_CURVE('',#531,#525,#259,.T.);
#646=EDGE_CURVE('',#532,#531,#260,.T.);
#647=EDGE_CURVE('',#528,#532,#261,.T.);
#648=EDGE_CURVE('',#523,#531,#262,.T.);
#649=EDGE_CURVE('',#531,#512,#205,.T.);
#650=EDGE_CURVE('',#533,#534,#263,.T.);
#651=EDGE_CURVE('',#534,#535,#264,.T.);
#652=EDGE_CURVE('',#536,#535,#265,.T.);
#653=EDGE_CURVE('',#536,#537,#266,.T.);
#654=EDGE_CURVE('',#538,#537,#206,.T.);
#655=EDGE_CURVE('',#539,#538,#267,.T.);
#656=EDGE_CURVE('',#539,#540,#268,.T.);
#657=EDGE_CURVE('',#540,#541,#269,.T.);
#658=EDGE_CURVE('',#542,#541,#270,.T.);
#659=EDGE_CURVE('',#542,#543,#271,.T.);
#660=EDGE_CURVE('',#543,#544,#272,.T.);
#661=EDGE_CURVE('',#545,#544,#207,.T.);
#662=EDGE_CURVE('',#545,#533,#273,.T.);
#663=EDGE_CURVE('',#546,#533,#274,.T.);
#664=EDGE_CURVE('',#528,#546,#208,.T.);
#665=EDGE_CURVE('',#533,#532,#209,.T.);
#666=EDGE_CURVE('',#547,#546,#275,.T.);
#667=EDGE_CURVE('',#547,#529,#210,.T.);
#668=EDGE_CURVE('',#534,#547,#276,.T.);
#669=EDGE_CURVE('',#548,#547,#277,.T.);
#670=EDGE_CURVE('',#535,#548,#278,.T.);
#671=EDGE_CURVE('',#545,#549,#279,.T.);
#672=EDGE_CURVE('',#546,#549,#280,.T.);
#673=EDGE_CURVE('',#527,#550,#281,.T.);
#674=EDGE_CURVE('',#550,#551,#282,.T.);
#675=EDGE_CURVE('',#551,#552,#211,.T.);
#676=EDGE_CURVE('',#552,#553,#283,.T.);
#677=EDGE_CURVE('',#554,#553,#284,.T.);
#678=EDGE_CURVE('',#554,#555,#285,.T.);
#679=EDGE_CURVE('',#555,#526,#212,.T.);
#680=EDGE_CURVE('',#556,#529,#286,.T.);
#681=EDGE_CURVE('',#523,#556,#287,.T.);
#682=EDGE_CURVE('',#532,#556,#288,.T.);
#683=EDGE_CURVE('',#556,#534,#213,.T.);
#684=EDGE_CURVE('',#557,#558,#289,.T.);
#685=EDGE_CURVE('',#558,#559,#290,.T.);
#686=EDGE_CURVE('',#560,#559,#291,.T.);
#687=EDGE_CURVE('',#561,#560,#292,.T.);
#688=EDGE_CURVE('',#561,#557,#293,.T.);
#689=EDGE_CURVE('',#562,#557,#294,.T.);
#690=EDGE_CURVE('',#516,#562,#214,.T.);
#691=EDGE_CURVE('',#557,#515,#17,.T.);
#692=EDGE_CURVE('',#563,#562,#295,.T.);
#693=EDGE_CURVE('',#553,#563,#215,.T.);
#694=EDGE_CURVE('',#553,#516,#296,.T.);
#695=EDGE_CURVE('',#559,#563,#297,.T.);
#696=EDGE_CURVE('',#563,#564,#298,.T.);
#697=EDGE_CURVE('',#565,#564,#299,.T.);
#698=EDGE_CURVE('',#565,#560,#300,.T.);
#699=EDGE_CURVE('',#566,#561,#301,.T.);
#700=EDGE_CURVE('',#567,#566,#302,.T.);
#701=EDGE_CURVE('',#562,#567,#303,.T.);
#702=EDGE_CURVE('',#517,#552,#304,.T.);
#703=EDGE_CURVE('',#549,#568,#216,.T.);
#704=EDGE_CURVE('',#568,#569,#305,.T.);
#705=EDGE_CURVE('',#569,#570,#306,.T.);
#706=EDGE_CURVE('',#570,#571,#307,.T.);
#707=EDGE_CURVE('',#571,#572,#308,.T.);
#708=EDGE_CURVE('',#572,#573,#309,.T.);
#709=EDGE_CURVE('',#573,#574,#310,.T.);
#710=EDGE_CURVE('',#574,#575,#217,.T.);
#711=EDGE_CURVE('',#575,#576,#311,.T.);
#712=EDGE_CURVE('',#576,#548,#312,.T.);
#713=EDGE_CURVE('',#515,#577,#313,.T.);
#714=EDGE_CURVE('',#577,#554,#314,.T.);
#715=EDGE_CURVE('',#559,#554,#18,.T.);
#716=EDGE_CURVE('',#578,#579,#315,.T.);
#717=EDGE_CURVE('',#580,#579,#316,.T.);
#718=EDGE_CURVE('',#581,#580,#317,.T.);
#719=EDGE_CURVE('',#581,#578,#318,.T.);
#720=EDGE_CURVE('',#582,#578,#319,.T.);
#721=EDGE_CURVE('',#567,#582,#218,.T.);
#722=EDGE_CURVE('',#578,#566,#219,.T.);
#723=EDGE_CURVE('',#583,#582,#320,.T.);
#724=EDGE_CURVE('',#564,#583,#220,.T.);
#725=EDGE_CURVE('',#564,#567,#321,.T.);
#726=EDGE_CURVE('',#579,#583,#322,.T.);
#727=EDGE_CURVE('',#583,#584,#323,.T.);
#728=EDGE_CURVE('',#585,#584,#324,.T.);
#729=EDGE_CURVE('',#585,#586,#325,.T.);
#730=EDGE_CURVE('',#586,#580,#221,.T.);
#731=EDGE_CURVE('',#587,#587,#222,.T.);
#732=EDGE_CURVE('',#581,#588,#223,.T.);
#733=EDGE_CURVE('',#589,#588,#326,.T.);
#734=EDGE_CURVE('',#590,#589,#327,.T.);
#735=EDGE_CURVE('',#582,#590,#328,.T.);
#736=EDGE_CURVE('',#591,#591,#224,.T.);
#737=EDGE_CURVE('',#566,#565,#329,.T.);
#738=EDGE_CURVE('',#579,#565,#225,.T.);
#739=EDGE_CURVE('',#592,#593,#330,.T.);
#740=EDGE_CURVE('',#593,#594,#331,.T.);
#741=EDGE_CURVE('',#595,#594,#332,.T.);
#742=EDGE_CURVE('',#596,#595,#333,.T.);
#743=EDGE_CURVE('',#596,#592,#334,.T.);
#744=EDGE_CURVE('',#597,#592,#335,.T.);
#745=EDGE_CURVE('',#590,#597,#226,.T.);
#746=EDGE_CURVE('',#592,#589,#19,.T.);
#747=EDGE_CURVE('',#598,#597,#336,.T.);
#748=EDGE_CURVE('',#584,#598,#227,.T.);
#749=EDGE_CURVE('',#584,#590,#337,.T.);
#750=EDGE_CURVE('',#594,#598,#338,.T.);
#751=EDGE_CURVE('',#598,#599,#339,.T.);
#752=EDGE_CURVE('',#599,#600,#228,.T.);
#753=EDGE_CURVE('',#600,#601,#340,.T.);
#754=EDGE_CURVE('',#601,#602,#229,.T.);
#755=EDGE_CURVE('',#602,#603,#341,.T.);
#756=EDGE_CURVE('',#603,#604,#230,.T.);
#757=EDGE_CURVE('',#604,#605,#342,.T.);
#758=EDGE_CURVE('',#605,#606,#231,.T.);
#759=EDGE_CURVE('',#606,#607,#343,.T.);
#760=EDGE_CURVE('',#607,#595,#232,.T.);
#761=EDGE_CURVE('',#596,#608,#233,.T.);
#762=EDGE_CURVE('',#609,#608,#344,.T.);
#763=EDGE_CURVE('',#609,#610,#234,.T.);
#764=EDGE_CURVE('',#610,#611,#345,.T.);
#765=EDGE_CURVE('',#612,#611,#235,.T.);
#766=EDGE_CURVE('',#613,#612,#346,.T.);
#767=EDGE_CURVE('',#613,#614,#236,.T.);
#768=EDGE_CURVE('',#614,#615,#347,.T.);
#769=EDGE_CURVE('',#615,#616,#237,.T.);
#770=EDGE_CURVE('',#597,#616,#348,.T.);
#771=EDGE_CURVE('',#589,#617,#349,.T.);
#772=EDGE_CURVE('',#617,#585,#350,.T.);
#773=EDGE_CURVE('',#594,#585,#20,.T.);
#774=EDGE_CURVE('',#530,#618,#351,.T.);
#775=EDGE_CURVE('',#618,#618,#238,.T.);
#776=EDGE_CURVE('',#587,#591,#352,.T.);
#777=EDGE_CURVE('',#539,#573,#353,.T.);
#778=EDGE_CURVE('',#540,#572,#354,.T.);
#779=EDGE_CURVE('',#541,#571,#355,.T.);
#780=EDGE_CURVE('',#542,#570,#356,.T.);
#781=EDGE_CURVE('',#543,#569,#357,.T.);
#782=EDGE_CURVE('',#544,#568,#358,.T.);
#783=EDGE_CURVE('',#514,#555,#359,.T.);
#784=EDGE_CURVE('',#588,#586,#360,.T.);
#785=EDGE_CURVE('',#608,#607,#361,.T.);
#786=EDGE_CURVE('',#609,#606,#362,.T.);
#787=EDGE_CURVE('',#610,#605,#363,.T.);
#788=EDGE_CURVE('',#611,#604,#364,.T.);
#789=EDGE_CURVE('',#612,#603,#365,.T.);
#790=EDGE_CURVE('',#613,#602,#366,.T.);
#791=EDGE_CURVE('',#614,#601,#367,.T.);
#792=EDGE_CURVE('',#615,#600,#368,.T.);
#793=EDGE_CURVE('',#616,#599,#369,.T.);
#794=EDGE_CURVE('',#518,#551,#370,.T.);
#795=EDGE_CURVE('',#519,#550,#371,.T.);
#796=EDGE_CURVE('',#536,#576,#372,.T.);
#797=EDGE_CURVE('',#537,#575,#373,.T.);
#798=EDGE_CURVE('',#538,#574,#374,.T.);
#799=ORIENTED_EDGE('',*,*,#619,.T.);
#800=ORIENTED_EDGE('',*,*,#620,.T.);
#801=ORIENTED_EDGE('',*,*,#621,.F.);
#802=ORIENTED_EDGE('',*,*,#622,.F.);
#803=ORIENTED_EDGE('',*,*,#623,.F.);
#804=ORIENTED_EDGE('',*,*,#624,.F.);
#805=ORIENTED_EDGE('',*,*,#625,.F.);
#806=ORIENTED_EDGE('',*,*,#626,.F.);
#807=ORIENTED_EDGE('',*,*,#627,.T.);
#808=ORIENTED_EDGE('',*,*,#628,.T.);
#809=ORIENTED_EDGE('',*,*,#629,.F.);
#810=ORIENTED_EDGE('',*,*,#630,.F.);
#811=ORIENTED_EDGE('',*,*,#631,.T.);
#812=ORIENTED_EDGE('',*,*,#632,.F.);
#813=ORIENTED_EDGE('',*,*,#633,.F.);
#814=ORIENTED_EDGE('',*,*,#634,.T.);
#815=ORIENTED_EDGE('',*,*,#635,.T.);
#816=ORIENTED_EDGE('',*,*,#630,.T.);
#817=ORIENTED_EDGE('',*,*,#636,.T.);
#818=ORIENTED_EDGE('',*,*,#637,.F.);
#819=ORIENTED_EDGE('',*,*,#638,.F.);
#820=ORIENTED_EDGE('',*,*,#620,.F.);
#821=ORIENTED_EDGE('',*,*,#629,.T.);
#822=ORIENTED_EDGE('',*,*,#628,.F.);
#823=ORIENTED_EDGE('',*,*,#639,.T.);
#824=ORIENTED_EDGE('',*,*,#640,.F.);
#825=ORIENTED_EDGE('',*,*,#635,.F.);
#826=ORIENTED_EDGE('',*,*,#641,.T.);
#827=ORIENTED_EDGE('',*,*,#642,.F.);
#828=ORIENTED_EDGE('',*,*,#643,.T.);
#829=ORIENTED_EDGE('',*,*,#644,.T.);
#830=ORIENTED_EDGE('',*,*,#645,.F.);
#831=ORIENTED_EDGE('',*,*,#646,.F.);
#832=ORIENTED_EDGE('',*,*,#647,.F.);
#833=ORIENTED_EDGE('',*,*,#641,.F.);
#834=ORIENTED_EDGE('',*,*,#619,.F.);
#835=ORIENTED_EDGE('',*,*,#632,.T.);
#836=ORIENTED_EDGE('',*,*,#648,.T.);
#837=ORIENTED_EDGE('',*,*,#649,.T.);
#838=ORIENTED_EDGE('',*,*,#650,.T.);
#839=ORIENTED_EDGE('',*,*,#651,.T.);
#840=ORIENTED_EDGE('',*,*,#652,.F.);
#841=ORIENTED_EDGE('',*,*,#653,.T.);
#842=ORIENTED_EDGE('',*,*,#654,.F.);
#843=ORIENTED_EDGE('',*,*,#655,.F.);
#844=ORIENTED_EDGE('',*,*,#656,.T.);
#845=ORIENTED_EDGE('',*,*,#657,.T.);
#846=ORIENTED_EDGE('',*,*,#658,.F.);
#847=ORIENTED_EDGE('',*,*,#659,.T.);
#848=ORIENTED_EDGE('',*,*,#660,.T.);
#849=ORIENTED_EDGE('',*,*,#661,.F.);
#850=ORIENTED_EDGE('',*,*,#662,.T.);
#851=ORIENTED_EDGE('',*,*,#663,.F.);
#852=ORIENTED_EDGE('',*,*,#664,.F.);
#853=ORIENTED_EDGE('',*,*,#647,.T.);
#854=ORIENTED_EDGE('',*,*,#665,.F.);
#855=ORIENTED_EDGE('',*,*,#666,.F.);
#856=ORIENTED_EDGE('',*,*,#667,.T.);
#857=ORIENTED_EDGE('',*,*,#642,.T.);
#858=ORIENTED_EDGE('',*,*,#664,.T.);
#859=ORIENTED_EDGE('',*,*,#668,.T.);
#860=ORIENTED_EDGE('',*,*,#669,.F.);
#861=ORIENTED_EDGE('',*,*,#670,.F.);
#862=ORIENTED_EDGE('',*,*,#651,.F.);
#863=ORIENTED_EDGE('',*,*,#663,.T.);
#864=ORIENTED_EDGE('',*,*,#662,.F.);
#865=ORIENTED_EDGE('',*,*,#671,.T.);
#866=ORIENTED_EDGE('',*,*,#672,.F.);
#867=ORIENTED_EDGE('',*,*,#633,.T.);
#868=ORIENTED_EDGE('',*,*,#640,.T.);
#869=ORIENTED_EDGE('',*,*,#673,.T.);
#870=ORIENTED_EDGE('',*,*,#674,.T.);
#871=ORIENTED_EDGE('',*,*,#675,.T.);
#872=ORIENTED_EDGE('',*,*,#676,.T.);
#873=ORIENTED_EDGE('',*,*,#677,.F.);
#874=ORIENTED_EDGE('',*,*,#678,.T.);
#875=ORIENTED_EDGE('',*,*,#679,.T.);
#876=ORIENTED_EDGE('',*,*,#637,.T.);
#877=ORIENTED_EDGE('',*,*,#631,.F.);
#878=ORIENTED_EDGE('',*,*,#643,.F.);
#879=ORIENTED_EDGE('',*,*,#680,.F.);
#880=ORIENTED_EDGE('',*,*,#681,.F.);
#881=ORIENTED_EDGE('',*,*,#650,.F.);
#882=ORIENTED_EDGE('',*,*,#665,.T.);
#883=ORIENTED_EDGE('',*,*,#682,.T.);
#884=ORIENTED_EDGE('',*,*,#683,.T.);
#885=ORIENTED_EDGE('',*,*,#684,.T.);
#886=ORIENTED_EDGE('',*,*,#685,.T.);
#887=ORIENTED_EDGE('',*,*,#686,.F.);
#888=ORIENTED_EDGE('',*,*,#687,.F.);
#889=ORIENTED_EDGE('',*,*,#688,.T.);
#890=ORIENTED_EDGE('',*,*,#689,.F.);
#891=ORIENTED_EDGE('',*,*,#690,.F.);
#892=ORIENTED_EDGE('',*,*,#623,.T.);
#893=ORIENTED_EDGE('',*,*,#691,.F.);
#894=ORIENTED_EDGE('',*,*,#692,.F.);
#895=ORIENTED_EDGE('',*,*,#693,.F.);
#896=ORIENTED_EDGE('',*,*,#694,.T.);
#897=ORIENTED_EDGE('',*,*,#690,.T.);
#898=ORIENTED_EDGE('',*,*,#695,.T.);
#899=ORIENTED_EDGE('',*,*,#696,.T.);
#900=ORIENTED_EDGE('',*,*,#697,.F.);
#901=ORIENTED_EDGE('',*,*,#698,.T.);
#902=ORIENTED_EDGE('',*,*,#686,.T.);
#903=ORIENTED_EDGE('',*,*,#689,.T.);
#904=ORIENTED_EDGE('',*,*,#688,.F.);
#905=ORIENTED_EDGE('',*,*,#699,.F.);
#906=ORIENTED_EDGE('',*,*,#700,.F.);
#907=ORIENTED_EDGE('',*,*,#701,.F.);
#908=ORIENTED_EDGE('',*,*,#694,.F.);
#909=ORIENTED_EDGE('',*,*,#676,.F.);
#910=ORIENTED_EDGE('',*,*,#702,.F.);
#911=ORIENTED_EDGE('',*,*,#624,.T.);
#912=ORIENTED_EDGE('',*,*,#666,.T.);
#913=ORIENTED_EDGE('',*,*,#672,.T.);
#914=ORIENTED_EDGE('',*,*,#703,.T.);
#915=ORIENTED_EDGE('',*,*,#704,.T.);
#916=ORIENTED_EDGE('',*,*,#705,.T.);
#917=ORIENTED_EDGE('',*,*,#706,.T.);
#918=ORIENTED_EDGE('',*,*,#707,.T.);
#919=ORIENTED_EDGE('',*,*,#708,.T.);
#920=ORIENTED_EDGE('',*,*,#709,.T.);
#921=ORIENTED_EDGE('',*,*,#710,.T.);
#922=ORIENTED_EDGE('',*,*,#711,.T.);
#923=ORIENTED_EDGE('',*,*,#712,.T.);
#924=ORIENTED_EDGE('',*,*,#669,.T.);
#925=ORIENTED_EDGE('',*,*,#684,.F.);
#926=ORIENTED_EDGE('',*,*,#691,.T.);
#927=ORIENTED_EDGE('',*,*,#713,.T.);
#928=ORIENTED_EDGE('',*,*,#714,.T.);
#929=ORIENTED_EDGE('',*,*,#715,.F.);
#930=ORIENTED_EDGE('',*,*,#685,.F.);
#931=ORIENTED_EDGE('',*,*,#716,.T.);
#932=ORIENTED_EDGE('',*,*,#717,.F.);
#933=ORIENTED_EDGE('',*,*,#718,.F.);
#934=ORIENTED_EDGE('',*,*,#719,.T.);
#935=ORIENTED_EDGE('',*,*,#720,.F.);
#936=ORIENTED_EDGE('',*,*,#721,.F.);
#937=ORIENTED_EDGE('',*,*,#700,.T.);
#938=ORIENTED_EDGE('',*,*,#722,.F.);
#939=ORIENTED_EDGE('',*,*,#723,.F.);
#940=ORIENTED_EDGE('',*,*,#724,.F.);
#941=ORIENTED_EDGE('',*,*,#725,.T.);
#942=ORIENTED_EDGE('',*,*,#721,.T.);
#943=ORIENTED_EDGE('',*,*,#726,.T.);
#944=ORIENTED_EDGE('',*,*,#727,.T.);
#945=ORIENTED_EDGE('',*,*,#728,.F.);
#946=ORIENTED_EDGE('',*,*,#729,.T.);
#947=ORIENTED_EDGE('',*,*,#730,.T.);
#948=ORIENTED_EDGE('',*,*,#717,.T.);
#949=ORIENTED_EDGE('',*,*,#731,.T.);
#950=ORIENTED_EDGE('',*,*,#720,.T.);
#951=ORIENTED_EDGE('',*,*,#719,.F.);
#952=ORIENTED_EDGE('',*,*,#732,.T.);
#953=ORIENTED_EDGE('',*,*,#733,.F.);
#954=ORIENTED_EDGE('',*,*,#734,.F.);
#955=ORIENTED_EDGE('',*,*,#735,.F.);
#956=ORIENTED_EDGE('',*,*,#736,.T.);
#957=ORIENTED_EDGE('',*,*,#692,.T.);
#958=ORIENTED_EDGE('',*,*,#701,.T.);
#959=ORIENTED_EDGE('',*,*,#725,.F.);
#960=ORIENTED_EDGE('',*,*,#696,.F.);
#961=ORIENTED_EDGE('',*,*,#695,.F.);
#962=ORIENTED_EDGE('',*,*,#715,.T.);
#963=ORIENTED_EDGE('',*,*,#677,.T.);
#964=ORIENTED_EDGE('',*,*,#693,.T.);
#965=ORIENTED_EDGE('',*,*,#716,.F.);
#966=ORIENTED_EDGE('',*,*,#722,.T.);
#967=ORIENTED_EDGE('',*,*,#737,.T.);
#968=ORIENTED_EDGE('',*,*,#738,.F.);
#969=ORIENTED_EDGE('',*,*,#739,.T.);
#970=ORIENTED_EDGE('',*,*,#740,.T.);
#971=ORIENTED_EDGE('',*,*,#741,.F.);
#972=ORIENTED_EDGE('',*,*,#742,.F.);
#973=ORIENTED_EDGE('',*,*,#743,.T.);
#974=ORIENTED_EDGE('',*,*,#744,.F.);
#975=ORIENTED_EDGE('',*,*,#745,.F.);
#976=ORIENTED_EDGE('',*,*,#734,.T.);
#977=ORIENTED_EDGE('',*,*,#746,.F.);
#978=ORIENTED_EDGE('',*,*,#747,.F.);
#979=ORIENTED_EDGE('',*,*,#748,.F.);
#980=ORIENTED_EDGE('',*,*,#749,.T.);
#981=ORIENTED_EDGE('',*,*,#745,.T.);
#982=ORIENTED_EDGE('',*,*,#750,.T.);
#983=ORIENTED_EDGE('',*,*,#751,.T.);
#984=ORIENTED_EDGE('',*,*,#752,.T.);
#985=ORIENTED_EDGE('',*,*,#753,.T.);
#986=ORIENTED_EDGE('',*,*,#754,.T.);
#987=ORIENTED_EDGE('',*,*,#755,.T.);
#988=ORIENTED_EDGE('',*,*,#756,.T.);
#989=ORIENTED_EDGE('',*,*,#757,.T.);
#990=ORIENTED_EDGE('',*,*,#758,.T.);
#991=ORIENTED_EDGE('',*,*,#759,.T.);
#992=ORIENTED_EDGE('',*,*,#760,.T.);
#993=ORIENTED_EDGE('',*,*,#741,.T.);
#994=ORIENTED_EDGE('',*,*,#744,.T.);
#995=ORIENTED_EDGE('',*,*,#743,.F.);
#996=ORIENTED_EDGE('',*,*,#761,.T.);
#997=ORIENTED_EDGE('',*,*,#762,.F.);
#998=ORIENTED_EDGE('',*,*,#763,.T.);
#999=ORIENTED_EDGE('',*,*,#764,.T.);
#1000=ORIENTED_EDGE('',*,*,#765,.F.);
#1001=ORIENTED_EDGE('',*,*,#766,.F.);
#1002=ORIENTED_EDGE('',*,*,#767,.T.);
#1003=ORIENTED_EDGE('',*,*,#768,.T.);
#1004=ORIENTED_EDGE('',*,*,#769,.T.);
#1005=ORIENTED_EDGE('',*,*,#770,.F.);
#1006=ORIENTED_EDGE('',*,*,#723,.T.);
#1007=ORIENTED_EDGE('',*,*,#735,.T.);
#1008=ORIENTED_EDGE('',*,*,#749,.F.);
#1009=ORIENTED_EDGE('',*,*,#727,.F.);
#1010=ORIENTED_EDGE('',*,*,#726,.F.);
#1011=ORIENTED_EDGE('',*,*,#738,.T.);
#1012=ORIENTED_EDGE('',*,*,#697,.T.);
#1013=ORIENTED_EDGE('',*,*,#724,.T.);
#1014=ORIENTED_EDGE('',*,*,#739,.F.);
#1015=ORIENTED_EDGE('',*,*,#746,.T.);
#1016=ORIENTED_EDGE('',*,*,#771,.T.);
#1017=ORIENTED_EDGE('',*,*,#772,.T.);
#1018=ORIENTED_EDGE('',*,*,#773,.F.);
#1019=ORIENTED_EDGE('',*,*,#740,.F.);
#1020=ORIENTED_EDGE('',*,*,#644,.F.);
#1021=ORIENTED_EDGE('',*,*,#774,.T.);
#1022=ORIENTED_EDGE('',*,*,#775,.F.);
#1023=ORIENTED_EDGE('',*,*,#774,.F.);
#1024=ORIENTED_EDGE('',*,*,#731,.F.);
#1025=ORIENTED_EDGE('',*,*,#776,.T.);
#1026=ORIENTED_EDGE('',*,*,#736,.F.);
#1027=ORIENTED_EDGE('',*,*,#776,.F.);
#1028=ORIENTED_EDGE('',*,*,#656,.F.);
#1029=ORIENTED_EDGE('',*,*,#777,.T.);
#1030=ORIENTED_EDGE('',*,*,#708,.F.);
#1031=ORIENTED_EDGE('',*,*,#778,.F.);
#1032=ORIENTED_EDGE('',*,*,#657,.F.);
#1033=ORIENTED_EDGE('',*,*,#778,.T.);
#1034=ORIENTED_EDGE('',*,*,#707,.F.);
#1035=ORIENTED_EDGE('',*,*,#779,.F.);
#1036=ORIENTED_EDGE('',*,*,#658,.T.);
#1037=ORIENTED_EDGE('',*,*,#779,.T.);
#1038=ORIENTED_EDGE('',*,*,#706,.F.);
#1039=ORIENTED_EDGE('',*,*,#780,.F.);
#1040=ORIENTED_EDGE('',*,*,#659,.F.);
#1041=ORIENTED_EDGE('',*,*,#780,.T.);
#1042=ORIENTED_EDGE('',*,*,#705,.F.);
#1043=ORIENTED_EDGE('',*,*,#781,.F.);
#1044=ORIENTED_EDGE('',*,*,#660,.F.);
#1045=ORIENTED_EDGE('',*,*,#781,.T.);
#1046=ORIENTED_EDGE('',*,*,#704,.F.);
#1047=ORIENTED_EDGE('',*,*,#782,.F.);
#1048=ORIENTED_EDGE('',*,*,#661,.T.);
#1049=ORIENTED_EDGE('',*,*,#782,.T.);
#1050=ORIENTED_EDGE('',*,*,#703,.F.);
#1051=ORIENTED_EDGE('',*,*,#671,.F.);
#1052=ORIENTED_EDGE('',*,*,#636,.F.);
#1053=ORIENTED_EDGE('',*,*,#649,.F.);
#1054=ORIENTED_EDGE('',*,*,#645,.T.);
#1055=ORIENTED_EDGE('',*,*,#634,.F.);
#1056=ORIENTED_EDGE('',*,*,#621,.T.);
#1057=ORIENTED_EDGE('',*,*,#638,.T.);
#1058=ORIENTED_EDGE('',*,*,#679,.F.);
#1059=ORIENTED_EDGE('',*,*,#783,.F.);
#1060=ORIENTED_EDGE('',*,*,#713,.F.);
#1061=ORIENTED_EDGE('',*,*,#622,.T.);
#1062=ORIENTED_EDGE('',*,*,#783,.T.);
#1063=ORIENTED_EDGE('',*,*,#678,.F.);
#1064=ORIENTED_EDGE('',*,*,#714,.F.);
#1065=ORIENTED_EDGE('',*,*,#737,.F.);
#1066=ORIENTED_EDGE('',*,*,#699,.T.);
#1067=ORIENTED_EDGE('',*,*,#687,.T.);
#1068=ORIENTED_EDGE('',*,*,#698,.F.);
#1069=ORIENTED_EDGE('',*,*,#732,.F.);
#1070=ORIENTED_EDGE('',*,*,#718,.T.);
#1071=ORIENTED_EDGE('',*,*,#730,.F.);
#1072=ORIENTED_EDGE('',*,*,#784,.F.);
#1073=ORIENTED_EDGE('',*,*,#771,.F.);
#1074=ORIENTED_EDGE('',*,*,#733,.T.);
#1075=ORIENTED_EDGE('',*,*,#784,.T.);
#1076=ORIENTED_EDGE('',*,*,#729,.F.);
#1077=ORIENTED_EDGE('',*,*,#772,.F.);
#1078=ORIENTED_EDGE('',*,*,#761,.F.);
#1079=ORIENTED_EDGE('',*,*,#742,.T.);
#1080=ORIENTED_EDGE('',*,*,#760,.F.);
#1081=ORIENTED_EDGE('',*,*,#785,.F.);
#1082=ORIENTED_EDGE('',*,*,#762,.T.);
#1083=ORIENTED_EDGE('',*,*,#785,.T.);
#1084=ORIENTED_EDGE('',*,*,#759,.F.);
#1085=ORIENTED_EDGE('',*,*,#786,.F.);
#1086=ORIENTED_EDGE('',*,*,#763,.F.);
#1087=ORIENTED_EDGE('',*,*,#786,.T.);
#1088=ORIENTED_EDGE('',*,*,#758,.F.);
#1089=ORIENTED_EDGE('',*,*,#787,.F.);
#1090=ORIENTED_EDGE('',*,*,#764,.F.);
#1091=ORIENTED_EDGE('',*,*,#787,.T.);
#1092=ORIENTED_EDGE('',*,*,#757,.F.);
#1093=ORIENTED_EDGE('',*,*,#788,.F.);
#1094=ORIENTED_EDGE('',*,*,#765,.T.);
#1095=ORIENTED_EDGE('',*,*,#788,.T.);
#1096=ORIENTED_EDGE('',*,*,#756,.F.);
#1097=ORIENTED_EDGE('',*,*,#789,.F.);
#1098=ORIENTED_EDGE('',*,*,#766,.T.);
#1099=ORIENTED_EDGE('',*,*,#789,.T.);
#1100=ORIENTED_EDGE('',*,*,#755,.F.);
#1101=ORIENTED_EDGE('',*,*,#790,.F.);
#1102=ORIENTED_EDGE('',*,*,#767,.F.);
#1103=ORIENTED_EDGE('',*,*,#790,.T.);
#1104=ORIENTED_EDGE('',*,*,#754,.F.);
#1105=ORIENTED_EDGE('',*,*,#791,.F.);
#1106=ORIENTED_EDGE('',*,*,#768,.F.);
#1107=ORIENTED_EDGE('',*,*,#791,.T.);
#1108=ORIENTED_EDGE('',*,*,#753,.F.);
#1109=ORIENTED_EDGE('',*,*,#792,.F.);
#1110=ORIENTED_EDGE('',*,*,#769,.F.);
#1111=ORIENTED_EDGE('',*,*,#792,.T.);
#1112=ORIENTED_EDGE('',*,*,#752,.F.);
#1113=ORIENTED_EDGE('',*,*,#793,.F.);
#1114=ORIENTED_EDGE('',*,*,#747,.T.);
#1115=ORIENTED_EDGE('',*,*,#770,.T.);
#1116=ORIENTED_EDGE('',*,*,#793,.T.);
#1117=ORIENTED_EDGE('',*,*,#751,.F.);
#1118=ORIENTED_EDGE('',*,*,#625,.T.);
#1119=ORIENTED_EDGE('',*,*,#702,.T.);
#1120=ORIENTED_EDGE('',*,*,#675,.F.);
#1121=ORIENTED_EDGE('',*,*,#794,.F.);
#1122=ORIENTED_EDGE('',*,*,#626,.T.);
#1123=ORIENTED_EDGE('',*,*,#794,.T.);
#1124=ORIENTED_EDGE('',*,*,#674,.F.);
#1125=ORIENTED_EDGE('',*,*,#795,.F.);
#1126=ORIENTED_EDGE('',*,*,#627,.F.);
#1127=ORIENTED_EDGE('',*,*,#795,.T.);
#1128=ORIENTED_EDGE('',*,*,#673,.F.);
#1129=ORIENTED_EDGE('',*,*,#639,.F.);
#1130=ORIENTED_EDGE('',*,*,#668,.F.);
#1131=ORIENTED_EDGE('',*,*,#683,.F.);
#1132=ORIENTED_EDGE('',*,*,#680,.T.);
#1133=ORIENTED_EDGE('',*,*,#667,.F.);
#1134=ORIENTED_EDGE('',*,*,#652,.T.);
#1135=ORIENTED_EDGE('',*,*,#670,.T.);
#1136=ORIENTED_EDGE('',*,*,#712,.F.);
#1137=ORIENTED_EDGE('',*,*,#796,.F.);
#1138=ORIENTED_EDGE('',*,*,#653,.F.);
#1139=ORIENTED_EDGE('',*,*,#796,.T.);
#1140=ORIENTED_EDGE('',*,*,#711,.F.);
#1141=ORIENTED_EDGE('',*,*,#797,.F.);
#1142=ORIENTED_EDGE('',*,*,#654,.T.);
#1143=ORIENTED_EDGE('',*,*,#797,.T.);
#1144=ORIENTED_EDGE('',*,*,#710,.F.);
#1145=ORIENTED_EDGE('',*,*,#798,.F.);
#1146=ORIENTED_EDGE('',*,*,#655,.T.);
#1147=ORIENTED_EDGE('',*,*,#798,.T.);
#1148=ORIENTED_EDGE('',*,*,#709,.F.);
#1149=ORIENTED_EDGE('',*,*,#777,.F.);
#1150=ORIENTED_EDGE('',*,*,#648,.F.);
#1151=ORIENTED_EDGE('',*,*,#681,.T.);
#1152=ORIENTED_EDGE('',*,*,#682,.F.);
#1153=ORIENTED_EDGE('',*,*,#646,.T.);
#1154=ORIENTED_EDGE('',*,*,#775,.T.);
#1155=ORIENTED_EDGE('',*,*,#750,.F.);
#1156=ORIENTED_EDGE('',*,*,#773,.T.);
#1157=ORIENTED_EDGE('',*,*,#728,.T.);
#1158=ORIENTED_EDGE('',*,*,#748,.T.);
#1159=PLANE('',#1297);
#1160=PLANE('',#1300);
#1161=PLANE('',#1305);
#1162=PLANE('',#1306);
#1163=PLANE('',#1307);
#1164=PLANE('',#1309);
#1165=PLANE('',#1312);
#1166=PLANE('',#1315);
#1167=PLANE('',#1320);
#1168=PLANE('',#1321);
#1169=PLANE('',#1322);
#1170=PLANE('',#1325);
#1171=PLANE('',#1328);
#1172=PLANE('',#1331);
#1173=PLANE('',#1333);
#1174=PLANE('',#1334);
#1175=PLANE('',#1335);
#1176=PLANE('',#1336);
#1177=PLANE('',#1339);
#1178=PLANE('',#1343);
#1179=PLANE('',#1345);
#1180=PLANE('',#1348);
#1181=PLANE('',#1351);
#1182=PLANE('',#1353);
#1183=PLANE('',#1355);
#1184=PLANE('',#1358);
#1185=PLANE('',#1360);
#1186=PLANE('',#1366);
#1187=PLANE('',#1372);
#1188=PLANE('',#1377);
#1189=PLANE('',#1378);
#1190=PLANE('',#1379);
#1191=PLANE('',#1380);
#1192=PLANE('',#1381);
#1193=PLANE('',#1383);
#1194=PLANE('',#1385);
#1195=PLANE('',#1386);
#1196=PLANE('',#1388);
#1197=PLANE('',#1390);
#1198=PLANE('',#1392);
#1199=PLANE('',#1394);
#1200=PLANE('',#1396);
#1201=PLANE('',#1398);
#1202=PLANE('',#1400);
#1203=PLANE('',#1401);
#1204=PLANE('',#1402);
#1205=PLANE('',#1403);
#1206=PLANE('',#1404);
#1207=PLANE('',#1406);
#1208=PLANE('',#1407);
#1209=ADVANCED_FACE('',(#47),#1159,.T.);
#1210=ADVANCED_FACE('',(#48),#1160,.T.);
#1211=ADVANCED_FACE('',(#49),#25,.F.);
#1212=ADVANCED_FACE('',(#50),#1161,.T.);
#1213=ADVANCED_FACE('',(#51),#1162,.T.);
#1214=ADVANCED_FACE('',(#52,#21),#1163,.F.);
#1215=ADVANCED_FACE('',(#53),#1164,.T.);
#1216=ADVANCED_FACE('',(#54),#26,.T.);
#1217=ADVANCED_FACE('',(#55),#1165,.T.);
#1218=ADVANCED_FACE('',(#56),#1166,.T.);
#1219=ADVANCED_FACE('',(#57),#27,.F.);
#1220=ADVANCED_FACE('',(#58),#1167,.T.);
#1221=ADVANCED_FACE('',(#59),#1168,.T.);
#1222=ADVANCED_FACE('',(#60),#1169,.F.);
#1223=ADVANCED_FACE('',(#61),#1170,.T.);
#1224=ADVANCED_FACE('',(#62),#28,.T.);
#1225=ADVANCED_FACE('',(#63),#1171,.T.);
#1226=ADVANCED_FACE('',(#64),#29,.T.);
#1227=ADVANCED_FACE('',(#65),#1172,.T.);
#1228=ADVANCED_FACE('',(#66),#1173,.F.);
#1229=ADVANCED_FACE('',(#67),#1174,.T.);
#1230=ADVANCED_FACE('',(#68),#1175,.T.);
#1231=ADVANCED_FACE('',(#69),#1176,.F.);
#1232=ADVANCED_FACE('',(#70),#15,.T.);
#1233=ADVANCED_FACE('',(#71),#1177,.T.);
#1234=ADVANCED_FACE('',(#72),#30,.F.);
#1235=ADVANCED_FACE('',(#73),#1178,.T.);
#1236=ADVANCED_FACE('',(#74,#22),#1179,.F.);
#1237=ADVANCED_FACE('',(#75,#23),#1180,.T.);
#1238=ADVANCED_FACE('',(#76),#1181,.T.);
#1239=ADVANCED_FACE('',(#77),#31,.F.);
#1240=ADVANCED_FACE('',(#78),#1182,.T.);
#1241=ADVANCED_FACE('',(#79),#1183,.T.);
#1242=ADVANCED_FACE('',(#80),#32,.F.);
#1243=ADVANCED_FACE('',(#81),#1184,.T.);
#1244=ADVANCED_FACE('',(#82),#1185,.F.);
#1245=ADVANCED_FACE('',(#83),#1186,.T.);
#1246=ADVANCED_FACE('',(#84),#1187,.T.);
#1247=ADVANCED_FACE('',(#85),#33,.T.);
#1248=ADVANCED_FACE('',(#86),#16,.T.);
#1249=ADVANCED_FACE('',(#87),#34,.F.);
#1250=ADVANCED_FACE('',(#88),#35,.F.);
#1251=ADVANCED_FACE('',(#89),#1188,.T.);
#1252=ADVANCED_FACE('',(#90),#1189,.T.);
#1253=ADVANCED_FACE('',(#91),#1190,.T.);
#1254=ADVANCED_FACE('',(#92),#1191,.T.);
#1255=ADVANCED_FACE('',(#93),#1192,.T.);
#1256=ADVANCED_FACE('',(#94),#36,.F.);
#1257=ADVANCED_FACE('',(#95),#1193,.T.);
#1258=ADVANCED_FACE('',(#96),#37,.F.);
#1259=ADVANCED_FACE('',(#97),#1194,.T.);
#1260=ADVANCED_FACE('',(#98),#1195,.T.);
#1261=ADVANCED_FACE('',(#99),#38,.T.);
#1262=ADVANCED_FACE('',(#100),#1196,.T.);
#1263=ADVANCED_FACE('',(#101),#39,.T.);
#1264=ADVANCED_FACE('',(#102),#1197,.T.);
#1265=ADVANCED_FACE('',(#103),#40,.T.);
#1266=ADVANCED_FACE('',(#104),#1198,.T.);
#1267=ADVANCED_FACE('',(#105),#41,.F.);
#1268=ADVANCED_FACE('',(#106),#1199,.T.);
#1269=ADVANCED_FACE('',(#107),#42,.T.);
#1270=ADVANCED_FACE('',(#108),#1200,.T.);
#1271=ADVANCED_FACE('',(#109),#43,.T.);
#1272=ADVANCED_FACE('',(#110),#1201,.T.);
#1273=ADVANCED_FACE('',(#111),#44,.F.);
#1274=ADVANCED_FACE('',(#112),#1202,.T.);
#1275=ADVANCED_FACE('',(#113),#1203,.T.);
#1276=ADVANCED_FACE('',(#114),#1204,.T.);
#1277=ADVANCED_FACE('',(#115),#1205,.T.);
#1278=ADVANCED_FACE('',(#116),#1206,.T.);
#1279=ADVANCED_FACE('',(#117),#45,.F.);
#1280=ADVANCED_FACE('',(#118),#1207,.T.);
#1281=ADVANCED_FACE('',(#119,#24),#1208,.T.);
#1282=ADVANCED_FACE('',(#120),#46,.T.);
#1283=CLOSED_SHELL('',(#1209,#1210,#1211,#1212,#1213,#1214,#1215,#1216,
#1217,#1218,#1219,#1220,#1221,#1222,#1223,#1224,#1225,#1226,#1227,#1228,
#1229,#1230,#1231,#1232,#1233,#1234,#1235,#1236,#1237,#1238,#1239,#1240,
#1241,#1242,#1243,#1244,#1245,#1246,#1247,#1248,#1249,#1250,#1251,#1252,
#1253,#1254,#1255,#1256,#1257,#1258,#1259,#1260,#1261,#1262,#1263,#1264,
#1265,#1266,#1267,#1268,#1269,#1270,#1271,#1272,#1273,#1274,#1275,#1276,
#1277,#1278,#1279,#1280,#1281,#1282));
#1284=DERIVED_UNIT_ELEMENT(#1286,1.);
#1285=DERIVED_UNIT_ELEMENT(#2201,-3.);
#1286=(
MASS_UNIT()
NAMED_UNIT(*)
SI_UNIT(.KILO.,.GRAM.)
);
#1287=DERIVED_UNIT((#1284,#1285));
#1288=MEASURE_REPRESENTATION_ITEM('density measure',
POSITIVE_RATIO_MEASURE(7850.),#1287);
#1289=PROPERTY_DEFINITION_REPRESENTATION(#1294,#1291);
#1290=PROPERTY_DEFINITION_REPRESENTATION(#1295,#1292);
#1291=REPRESENTATION('material name',(#1293),#2198);
#1292=REPRESENTATION('density',(#1288),#2198);
#1293=DESCRIPTIVE_REPRESENTATION_ITEM('Steel','Steel');
#1294=PROPERTY_DEFINITION('material property','material name',#2208);
#1295=PROPERTY_DEFINITION('material property','density of part',#2208);
#1296=AXIS2_PLACEMENT_3D('placement',#1771,#1409,#1410);
#1297=AXIS2_PLACEMENT_3D('',#1772,#1411,#1412);
#1298=AXIS2_PLACEMENT_3D('',#1779,#1415,#1416);
#1299=AXIS2_PLACEMENT_3D('',#1787,#1420,#1421);
#1300=AXIS2_PLACEMENT_3D('',#1793,#1425,#1426);
#1301=AXIS2_PLACEMENT_3D('',#1797,#1428,#1429);
#1302=AXIS2_PLACEMENT_3D('',#1800,#1431,#1432);
#1303=AXIS2_PLACEMENT_3D('',#1801,#1433,#1434);
#1304=AXIS2_PLACEMENT_3D('',#1805,#1436,#1437);
#1305=AXIS2_PLACEMENT_3D('',#1807,#1439,#1440);
#1306=AXIS2_PLACEMENT_3D('',#1812,#1444,#1445);
#1307=AXIS2_PLACEMENT_3D('',#1816,#1448,#1449);
#1308=AXIS2_PLACEMENT_3D('',#1823,#1453,#1454);
#1309=AXIS2_PLACEMENT_3D('',#1824,#1455,#1456);
#1310=AXIS2_PLACEMENT_3D('',#1830,#1460,#1461);
#1311=AXIS2_PLACEMENT_3D('',#1832,#1463,#1464);
#1312=AXIS2_PLACEMENT_3D('',#1833,#1465,#1466);
#1313=AXIS2_PLACEMENT_3D('',#1844,#1471,#1472);
#1314=AXIS2_PLACEMENT_3D('',#1858,#1479,#1480);
#1315=AXIS2_PLACEMENT_3D('',#1860,#1482,#1483);
#1316=AXIS2_PLACEMENT_3D('',#1863,#1485,#1486);
#1317=AXIS2_PLACEMENT_3D('',#1864,#1487,#1488);
#1318=AXIS2_PLACEMENT_3D('',#1865,#1489,#1490);
#1319=AXIS2_PLACEMENT_3D('',#1868,#1492,#1493);
#1320=AXIS2_PLACEMENT_3D('',#1869,#1494,#1495);
#1321=AXIS2_PLACEMENT_3D('',#1874,#1499,#1500);
#1322=AXIS2_PLACEMENT_3D('',#1878,#1503,#1504);
#1323=AXIS2_PLACEMENT_3D('',#1884,#1507,#1508);
#1324=AXIS2_PLACEMENT_3D('',#1891,#1512,#1513);
#1325=AXIS2_PLACEMENT_3D('',#1892,#1514,#1515);
#1326=AXIS2_PLACEMENT_3D('',#1896,#1518,#1519);
#1327=AXIS2_PLACEMENT_3D('',#1898,#1521,#1522);
#1328=AXIS2_PLACEMENT_3D('',#1899,#1523,#1524);
#1329=AXIS2_PLACEMENT_3D('',#1910,#1530,#1531);
#1330=AXIS2_PLACEMENT_3D('',#1913,#1533,#1534);
#1331=AXIS2_PLACEMENT_3D('',#1918,#1535,#1536);
#1332=AXIS2_PLACEMENT_3D('',#1921,#1538,#1539);
#1333=AXIS2_PLACEMENT_3D('',#1923,#1541,#1542);
#1334=AXIS2_PLACEMENT_3D('',#1930,#1547,#1548);
#1335=AXIS2_PLACEMENT_3D('',#1936,#1552,#1553);
#1336=AXIS2_PLACEMENT_3D('',#1938,#1555,#1556);
#1337=AXIS2_PLACEMENT_3D('',#1940,#1557,#1558);
#1338=AXIS2_PLACEMENT_3D('',#1954,#1565,#1566);
#1339=AXIS2_PLACEMENT_3D('',#1981,#1571,#1572);
#1340=AXIS2_PLACEMENT_3D('',#1990,#1577,#1578);
#1341=AXIS2_PLACEMENT_3D('',#1993,#1580,#1581);
#1342=AXIS2_PLACEMENT_3D('',#1994,#1582,#1583);
#1343=AXIS2_PLACEMENT_3D('',#1995,#1584,#1585);
#1344=AXIS2_PLACEMENT_3D('',#1998,#1587,#1588);
#1345=AXIS2_PLACEMENT_3D('',#2000,#1590,#1591);
#1346=AXIS2_PLACEMENT_3D('',#2008,#1596,#1597);
#1347=AXIS2_PLACEMENT_3D('',#2010,#1598,#1599);
#1348=AXIS2_PLACEMENT_3D('',#2011,#1600,#1601);
#1349=AXIS2_PLACEMENT_3D('',#2013,#1602,#1603);
#1350=AXIS2_PLACEMENT_3D('',#2020,#1607,#1608);
#1351=AXIS2_PLACEMENT_3D('',#2021,#1609,#1610);
#1352=AXIS2_PLACEMENT_3D('',#2022,#1611,#1612);
#1353=AXIS2_PLACEMENT_3D('',#2023,#1613,#1614);
#1354=AXIS2_PLACEMENT_3D('',#2025,#1616,#1617);
#1355=AXIS2_PLACEMENT_3D('',#2026,#1618,#1619);
#1356=AXIS2_PLACEMENT_3D('',#2037,#1625,#1626);
#1357=AXIS2_PLACEMENT_3D('',#2040,#1628,#1629);
#1358=AXIS2_PLACEMENT_3D('',#2051,#1630,#1631);
#1359=AXIS2_PLACEMENT_3D('',#2054,#1633,#1634);
#1360=AXIS2_PLACEMENT_3D('',#2056,#1636,#1637);
#1361=AXIS2_PLACEMENT_3D('',#2061,#1640,#1641);
#1362=AXIS2_PLACEMENT_3D('',#2065,#1643,#1644);
#1363=AXIS2_PLACEMENT_3D('',#2069,#1646,#1647);
#1364=AXIS2_PLACEMENT_3D('',#2073,#1649,#1650);
#1365=AXIS2_PLACEMENT_3D('',#2076,#1652,#1653);
#1366=AXIS2_PLACEMENT_3D('',#2077,#1654,#1655);
#1367=AXIS2_PLACEMENT_3D('',#2079,#1656,#1657);
#1368=AXIS2_PLACEMENT_3D('',#2083,#1659,#1660);
#1369=AXIS2_PLACEMENT_3D('',#2087,#1662,#1663);
#1370=AXIS2_PLACEMENT_3D('',#2091,#1665,#1666);
#1371=AXIS2_PLACEMENT_3D('',#2095,#1668,#1669);
#1372=AXIS2_PLACEMENT_3D('',#2097,#1671,#1672);
#1373=AXIS2_PLACEMENT_3D('',#2098,#1673,#1674);
#1374=AXIS2_PLACEMENT_3D('',#2136,#1677,#1678);
#1375=AXIS2_PLACEMENT_3D('',#2139,#1680,#1681);
#1376=AXIS2_PLACEMENT_3D('',#2140,#1682,#1683);
#1377=AXIS2_PLACEMENT_3D('',#2142,#1685,#1686);
#1378=AXIS2_PLACEMENT_3D('',#2145,#1689,#1690);
#1379=AXIS2_PLACEMENT_3D('',#2147,#1692,#1693);
#1380=AXIS2_PLACEMENT_3D('',#2149,#1695,#1696);
#1381=AXIS2_PLACEMENT_3D('',#2151,#1698,#1699);
#1382=AXIS2_PLACEMENT_3D('',#2153,#1701,#1702);
#1383=AXIS2_PLACEMENT_3D('',#2154,#1703,#1704);
#1384=AXIS2_PLACEMENT_3D('',#2155,#1705,#1706);
#1385=AXIS2_PLACEMENT_3D('',#2157,#1708,#1709);
#1386=AXIS2_PLACEMENT_3D('',#2158,#1710,#1711);
#1387=AXIS2_PLACEMENT_3D('',#2159,#1712,#1713);
#1388=AXIS2_PLACEMENT_3D('',#2161,#1715,#1716);
#1389=AXIS2_PLACEMENT_3D('',#2162,#1717,#1718);
#1390=AXIS2_PLACEMENT_3D('',#2164,#1720,#1721);
#1391=AXIS2_PLACEMENT_3D('',#2166,#1723,#1724);
#1392=AXIS2_PLACEMENT_3D('',#2168,#1726,#1727);
#1393=AXIS2_PLACEMENT_3D('',#2170,#1729,#1730);
#1394=AXIS2_PLACEMENT_3D('',#2172,#1732,#1733);
#1395=AXIS2_PLACEMENT_3D('',#2174,#1735,#1736);
#1396=AXIS2_PLACEMENT_3D('',#2176,#1738,#1739);
#1397=AXIS2_PLACEMENT_3D('',#2178,#1741,#1742);
#1398=AXIS2_PLACEMENT_3D('',#2180,#1744,#1745);
#1399=AXIS2_PLACEMENT_3D('',#2181,#1746,#1747);
#1400=AXIS2_PLACEMENT_3D('',#2183,#1749,#1750);
#1401=AXIS2_PLACEMENT_3D('',#2185,#1752,#1753);
#1402=AXIS2_PLACEMENT_3D('',#2186,#1754,#1755);
#1403=AXIS2_PLACEMENT_3D('',#2187,#1756,#1757);
#1404=AXIS2_PLACEMENT_3D('',#2189,#1759,#1760);
#1405=AXIS2_PLACEMENT_3D('',#2191,#1762,#1763);
#1406=AXIS2_PLACEMENT_3D('',#2193,#1765,#1766);
#1407=AXIS2_PLACEMENT_3D('',#2194,#1767,#1768);
#1408=AXIS2_PLACEMENT_3D('',#2195,#1769,#1770);
#1409=DIRECTION('axis',(0.,0.,1.));
#1410=DIRECTION('refdir',(1.,0.,0.));
#1411=DIRECTION('center_axis',(0.743144825477393,0.,0.66913060635886));
#1412=DIRECTION('ref_axis',(0.66913060635886,0.,-0.743144825477393));
#1413=DIRECTION('',(0.,-1.,0.));
#1414=DIRECTION('',(0.66913060635886,-3.32550941715016E-17,-0.743144825477393));
#1415=DIRECTION('center_axis',(0.743144825477393,0.,0.66913060635886));
#1416=DIRECTION('ref_axis',(0.430108863029516,0.766044443118978,-0.477684286019533));
#1417=DIRECTION('',(-0.512583782722037,0.642787609686539,0.56928196398958));
#1418=DIRECTION('',(0.,-1.,0.));
#1419=DIRECTION('',(0.66913060635886,9.85769611209906E-17,-0.743144825477393));
#1420=DIRECTION('center_axis',(0.743144825477393,0.,0.66913060635886));
#1421=DIRECTION('ref_axis',(-0.66913060635886,0.,0.743144825477393));
#1422=DIRECTION('',(0.,-1.,0.));
#1423=DIRECTION('',(-0.473146789255817,0.707106781186546,0.525482745498759));
#1424=DIRECTION('',(-0.66913060635886,0.,0.743144825477393));
#1425=DIRECTION('center_axis',(0.,1.,0.));
#1426=DIRECTION('ref_axis',(0.,0.,1.));
#1427=DIRECTION('',(0.743144825477393,0.,0.66913060635886));
#1428=DIRECTION('center_axis',(0.,1.,0.));
#1429=DIRECTION('ref_axis',(0.,0.,1.));
#1430=DIRECTION('',(0.,0.,1.));
#1431=DIRECTION('center_axis',(0.,-1.,0.));
#1432=DIRECTION('ref_axis',(0.743144825477392,0.,0.66913060635886));
#1433=DIRECTION('center_axis',(0.,1.,0.));
#1434=DIRECTION('ref_axis',(0.,0.,1.));
#1435=DIRECTION('',(0.,1.,0.));
#1436=DIRECTION('center_axis',(0.,-1.,0.));
#1437=DIRECTION('ref_axis',(0.743144825477393,0.,0.669130606358859));
#1438=DIRECTION('',(0.,1.,0.));
#1439=DIRECTION('center_axis',(-2.22520013274979E-17,-1.,2.47133511543148E-17));
#1440=DIRECTION('ref_axis',(-0.66913060635886,3.32550941715016E-17,0.743144825477393));
#1441=DIRECTION('',(-0.743144825477393,0.,-0.66913060635886));
#1442=DIRECTION('',(-0.66913060635886,3.32550941715016E-17,0.743144825477393));
#1443=DIRECTION('',(-0.743144825477393,0.,-0.66913060635886));
#1444=DIRECTION('center_axis',(0.,1.,0.));
#1445=DIRECTION('ref_axis',(0.66913060635886,0.,-0.743144825477393));
#1446=DIRECTION('',(-0.743144825477393,0.,-0.66913060635886));
#1447=DIRECTION('',(0.66913060635886,0.,-0.743144825477393));
#1448=DIRECTION('center_axis',(0.,0.,1.));
#1449=DIRECTION('ref_axis',(1.,0.,0.));
#1450=DIRECTION('',(-1.,3.32550941715016E-17,0.));
#1451=DIRECTION('',(-3.24943324280533E-16,-1.,0.));
#1452=DIRECTION('',(1.,0.,0.));
#1453=DIRECTION('center_axis',(0.,0.,1.));
#1454=DIRECTION('ref_axis',(1.,0.,0.));
#1455=DIRECTION('center_axis',(-3.32550941715016E-17,-1.,0.));
#1456=DIRECTION('ref_axis',(-1.,3.32550941715016E-17,0.));
#1457=DIRECTION('',(0.,0.,-1.));
#1458=DIRECTION('',(1.,-3.32550941715016E-17,0.));
#1459=DIRECTION('',(0.,0.,1.));
#1460=DIRECTION('center_axis',(0.,1.,0.));
#1461=DIRECTION('ref_axis',(0.,0.,1.));
#1462=DIRECTION('',(0.,-1.,0.));
#1463=DIRECTION('center_axis',(0.,1.,0.));
#1464=DIRECTION('ref_axis',(0.,0.,1.));
#1465=DIRECTION('center_axis',(-1.,3.35161667811368E-16,6.12323399573677E-17));
#1466=DIRECTION('ref_axis',(6.12323399573678E-17,3.35161667811368E-16,1.));
#1467=DIRECTION('',(3.35161667811368E-16,1.,-1.02183435308348E-17));
#1468=DIRECTION('',(-6.12323399573678E-17,-3.35161667811368E-16,-1.));
#1469=DIRECTION('',(2.80292890914986E-16,0.707106781186548,0.707106781186547));
#1470=DIRECTION('',(-3.35161667811368E-16,-1.,3.35161667811368E-16));
#1471=DIRECTION('center_axis',(-1.,3.35161667811368E-16,6.12323399573677E-17));
#1472=DIRECTION('ref_axis',(-3.35161667811368E-16,-1.,3.35161667811368E-16));
#1473=DIRECTION('',(6.12323399573678E-17,3.35161667811368E-16,1.));
#1474=DIRECTION('',(-2.80292890914986E-16,-0.707106781186547,-0.707106781186547));
#1475=DIRECTION('',(-3.35161667811368E-16,-1.,3.35161667811368E-16));
#1476=DIRECTION('',(1.93697285291437E-16,0.707106781186547,-0.707106781186548));
#1477=DIRECTION('',(6.12323399573678E-17,3.35161667811368E-16,1.));
#1478=DIRECTION('',(2.96108122610097E-16,0.766044443118978,0.642787609686539));
#1479=DIRECTION('center_axis',(-1.,3.35161667811368E-16,6.12323399573677E-17));
#1480=DIRECTION('ref_axis',(3.35161667811368E-16,1.,-3.35161667811368E-16));
#1481=DIRECTION('',(6.12323399573678E-17,3.01906573639866E-16,1.));
#1482=DIRECTION('center_axis',(-3.35161667811368E-16,-1.,0.));
#1483=DIRECTION('ref_axis',(0.,0.,-1.));
#1484=DIRECTION('',(-1.,3.35161667811368E-16,6.12323399573677E-17));
#1485=DIRECTION('center_axis',(-3.35161667811368E-16,-1.,0.));
#1486=DIRECTION('ref_axis',(0.,0.,1.));
#1487=DIRECTION('center_axis',(3.35161667811368E-16,1.,0.));
#1488=DIRECTION('ref_axis',(-1.,4.44089209850062E-16,1.11022302462516E-16));
#1489=DIRECTION('center_axis',(-3.35161667811368E-16,-1.,0.));
#1490=DIRECTION('ref_axis',(0.,0.,1.));
#1491=DIRECTION('',(-3.35161667811368E-16,-1.,1.02183435308348E-17));
#1492=DIRECTION('center_axis',(3.35161667811368E-16,1.,0.));
#1493=DIRECTION('ref_axis',(-1.,0.,0.));
#1494=DIRECTION('center_axis',(3.35161667811368E-16,1.,-3.35161667811368E-16));
#1495=DIRECTION('ref_axis',(6.12323399573678E-17,3.35161667811368E-16,1.));
#1496=DIRECTION('',(1.,-3.35161667811368E-16,-6.12323399573677E-17));
#1497=DIRECTION('',(6.12323399573678E-17,3.35161667811368E-16,1.));
#1498=DIRECTION('',(1.,-3.35161667811368E-16,-6.12323399573677E-17));
#1499=DIRECTION('center_axis',(-3.35161667811368E-16,-1.,3.01906573639866E-16));
#1500=DIRECTION('ref_axis',(-6.12323399573678E-17,-3.01906573639866E-16,
-1.));
#1501=DIRECTION('',(1.,-3.35161667811368E-16,-6.12323399573677E-17));
#1502=DIRECTION('',(-6.12323399573678E-17,-3.01906573639866E-16,-1.));
#1503=DIRECTION('center_axis',(0.743144825477393,0.,0.66913060635886));
#1504=DIRECTION('ref_axis',(0.66913060635886,0.,-0.743144825477393));
#1505=DIRECTION('',(0.473146789255817,-0.707106781186546,-0.525482745498759));
#1506=DIRECTION('',(0.,-1.,0.));
#1507=DIRECTION('center_axis',(0.743144825477393,0.,0.66913060635886));
#1508=DIRECTION('ref_axis',(-0.66913060635886,0.,0.743144825477393));
#1509=DIRECTION('',(0.66913060635886,9.85769611209906E-17,-0.743144825477393));
#1510=DIRECTION('',(0.,1.,0.));
#1511=DIRECTION('',(-0.512583782722037,0.642787609686539,0.56928196398958));
#1512=DIRECTION('center_axis',(0.743144825477393,0.,0.66913060635886));
#1513=DIRECTION('ref_axis',(0.430108863029516,0.766044443118978,-0.477684286019533));
#1514=DIRECTION('center_axis',(0.,1.,0.));
#1515=DIRECTION('ref_axis',(1.,0.,0.));
#1516=DIRECTION('',(0.,0.,-1.));
#1517=DIRECTION('',(-1.,0.,0.));
#1518=DIRECTION('center_axis',(-3.35161667811368E-16,-1.,0.));
#1519=DIRECTION('ref_axis',(0.,0.,1.));
#1520=DIRECTION('',(3.24943324280533E-16,1.,0.));
#1521=DIRECTION('center_axis',(-3.35161667811368E-16,-1.,0.));
#1522=DIRECTION('ref_axis',(0.,0.,1.));
#1523=DIRECTION('center_axis',(-1.16573417585641E-15,-0.766044443118978,
0.642787609686539));
#1524=DIRECTION('ref_axis',(-1.27675647831893E-15,0.642787609686539,0.766044443118978));
#1525=DIRECTION('',(-1.,0.,-1.83186799063151E-15));
#1526=DIRECTION('',(-1.,0.,-1.83186799063151E-15));
#1527=DIRECTION('',(-1.27675647831893E-15,0.642787609686539,0.766044443118978));
#1528=DIRECTION('',(-1.,0.,-1.83186799063151E-15));
#1529=DIRECTION('',(-1.27675647831893E-15,0.642787609686539,0.766044443118978));
#1530=DIRECTION('center_axis',(0.,1.,0.));
#1531=DIRECTION('ref_axis',(0.743144825477393,0.,0.66913060635886));
#1532=DIRECTION('',(0.,-1.,0.));
#1533=DIRECTION('center_axis',(0.,1.,0.));
#1534=DIRECTION('ref_axis',(0.743144825477393,0.,0.66913060635886));
#1535=DIRECTION('center_axis',(0.,1.,0.));
#1536=DIRECTION('ref_axis',(0.743144825477393,0.,0.66913060635886));
#1537=DIRECTION('',(1.,0.,1.83186799063151E-15));
#1538=DIRECTION('center_axis',(0.,1.,0.));
#1539=DIRECTION('ref_axis',(0.743144825477393,0.,0.66913060635886));
#1540=DIRECTION('',(0.743144825477393,0.,0.66913060635886));
#1541=DIRECTION('center_axis',(1.,0.,1.83186799063151E-15));
#1542=DIRECTION('ref_axis',(1.83186799063151E-15,0.,-1.));
#1543=DIRECTION('',(0.,1.,0.));
#1544=DIRECTION('',(1.83186799063151E-15,9.85769611209906E-17,-1.));
#1545=DIRECTION('',(0.,1.,0.));
#1546=DIRECTION('',(-1.83186799063151E-15,1.57946422003166E-17,1.));
#1547=DIRECTION('center_axis',(1.,0.,1.83186799063151E-15));
#1548=DIRECTION('ref_axis',(1.83186799063151E-15,0.,-1.));
#1549=DIRECTION('',(-1.83186799063151E-15,1.57946422003166E-17,1.));
#1550=DIRECTION('',(0.,-1.,0.));
#1551=DIRECTION('',(1.83186799063151E-15,9.85769611209906E-17,-1.));
#1552=DIRECTION('center_axis',(-6.59608617679022E-17,1.,7.32569585683503E-17));
#1553=DIRECTION('ref_axis',(0.66913060635886,9.85769611209906E-17,-0.743144825477393));
#1554=DIRECTION('',(-0.743144825477393,0.,-0.66913060635886));
#1555=DIRECTION('center_axis',(-1.,3.35161667811368E-16,6.12323399573677E-17));
#1556=DIRECTION('ref_axis',(6.12323399573678E-17,3.35161667811368E-16,1.));
#1557=DIRECTION('center_axis',(-1.,3.35161667811368E-16,6.12323399573677E-17));
#1558=DIRECTION('ref_axis',(3.35161667811368E-16,1.,-3.35161667811368E-16));
#1559=DIRECTION('',(-2.96108122610097E-16,-0.766044443118978,-0.642787609686539));
#1560=DIRECTION('',(-6.12323399573678E-17,-3.35161667811368E-16,-1.));
#1561=DIRECTION('',(1.93697285291437E-16,0.707106781186547,-0.707106781186548));
#1562=DIRECTION('',(3.35161667811368E-16,1.,-3.35161667811368E-16));
#1563=DIRECTION('',(2.80292890914986E-16,0.707106781186547,0.707106781186547));
#1564=DIRECTION('',(6.12323399573678E-17,3.35161667811368E-16,1.));
#1565=DIRECTION('center_axis',(-1.,3.35161667811368E-16,6.12323399573677E-17));
#1566=DIRECTION('ref_axis',(-3.35161667811368E-16,-1.,3.35161667811368E-16));
#1567=DIRECTION('',(3.35161667811368E-16,1.,-3.35161667811368E-16));
#1568=DIRECTION('',(2.80292890914986E-16,0.707106781186548,0.707106781186547));
#1569=DIRECTION('',(-0.743144825477393,0.,-0.66913060635886));
#1570=DIRECTION('',(-0.743144825477393,0.,-0.66913060635886));
#1571=DIRECTION('center_axis',(-1.57946422003166E-17,-1.,-5.70075263538622E-32));
#1572=DIRECTION('ref_axis',(-1.,1.57946422003166E-17,-3.44169137633799E-15));
#1573=DIRECTION('',(3.44169137633799E-15,0.,-1.));
#1574=DIRECTION('',(-1.,1.57946422003166E-17,-3.44169137633799E-15));
#1575=DIRECTION('',(3.44169137633799E-15,0.,-1.));
#1576=DIRECTION('',(-1.,1.57946422003166E-17,-3.44169137633799E-15));
#1577=DIRECTION('center_axis',(0.,-1.,0.));
#1578=DIRECTION('ref_axis',(-1.,0.,-1.77635683940025E-15));
#1579=DIRECTION('',(0.,-1.,0.));
#1580=DIRECTION('center_axis',(0.,-1.,0.));
#1581=DIRECTION('ref_axis',(-1.,0.,-1.77635683940025E-15));
#1582=DIRECTION('center_axis',(0.,1.,0.));
#1583=DIRECTION('ref_axis',(3.44169137633799E-15,0.,-1.));
#1584=DIRECTION('center_axis',(0.,1.,0.));
#1585=DIRECTION('ref_axis',(1.,0.,1.83186799063151E-15));
#1586=DIRECTION('',(-3.44169137633799E-15,0.,1.));
#1587=DIRECTION('center_axis',(0.,-1.,0.));
#1588=DIRECTION('ref_axis',(-1.,0.,-1.83186799063151E-15));
#1589=DIRECTION('',(1.,0.,1.83186799063151E-15));
#1590=DIRECTION('center_axis',(-3.44169137633799E-15,0.,1.));
#1591=DIRECTION('ref_axis',(1.,0.,3.44169137633799E-15));
#1592=DIRECTION('',(0.,1.,0.));
#1593=DIRECTION('',(1.,9.85769611209906E-17,3.44169137633799E-15));
#1594=DIRECTION('',(0.,1.,0.));
#1595=DIRECTION('',(-0.978419112302535,-0.206630202732124,-3.38618022510673E-15));
#1596=DIRECTION('center_axis',(3.44169137633799E-15,0.,-1.));
#1597=DIRECTION('ref_axis',(5.32907051819893E-14,-1.,1.85382312726874E-28));
#1598=DIRECTION('center_axis',(-3.44169137633799E-15,0.,1.));
#1599=DIRECTION('ref_axis',(1.,0.,3.47869881049216E-15));
#1600=DIRECTION('center_axis',(-3.44169137633799E-15,0.,1.));
#1601=DIRECTION('ref_axis',(1.,0.,3.44169137633799E-15));
#1602=DIRECTION('center_axis',(-3.44169137633799E-15,0.,1.));
#1603=DIRECTION('ref_axis',(5.32907051819893E-14,-1.,1.85382312726874E-28));
#1604=DIRECTION('',(-0.978419112302535,-0.206630202732124,-3.38618022510673E-15));
#1605=DIRECTION('',(0.,-1.,0.));
#1606=DIRECTION('',(1.,9.85769611209906E-17,3.44169137633799E-15));
#1607=DIRECTION('center_axis',(3.44169137633799E-15,0.,-1.));
#1608=DIRECTION('ref_axis',(1.,0.,3.47869881049216E-15));
#1609=DIRECTION('center_axis',(-1.84889274661175E-31,1.,9.85769611209906E-17));
#1610=DIRECTION('ref_axis',(1.83186799063151E-15,9.85769611209906E-17,-1.));
#1611=DIRECTION('center_axis',(0.,1.,0.));
#1612=DIRECTION('ref_axis',(0.743144825477393,0.,0.66913060635886));
#1613=DIRECTION('center_axis',(0.,-1.,0.));
#1614=DIRECTION('ref_axis',(-1.,0.,-1.83186799063151E-15));
#1615=DIRECTION('',(-1.,0.,-1.83186799063151E-15));
#1616=DIRECTION('center_axis',(0.,1.,0.));
#1617=DIRECTION('ref_axis',(3.5527136788005E-15,0.,-1.));
#1618=DIRECTION('center_axis',(-1.08246744900953E-15,-0.978419112302535,
0.206630202732124));
#1619=DIRECTION('ref_axis',(5.10702591327572E-15,-0.206630202732124,-0.978419112302535));
#1620=DIRECTION('',(1.,0.,5.27355936696949E-15));
#1621=DIRECTION('',(1.,0.,5.27355936696949E-15));
#1622=DIRECTION('',(5.10702591327572E-15,-0.206630202732124,-0.978419112302535));
#1623=DIRECTION('',(1.,0.,5.27355936696949E-15));
#1624=DIRECTION('',(5.10702591327572E-15,-0.206630202732124,-0.978419112302535));
#1625=DIRECTION('center_axis',(0.,-1.,0.));
#1626=DIRECTION('ref_axis',(3.49720252756924E-15,0.,-1.));
#1627=DIRECTION('',(0.,-1.,0.));
#1628=DIRECTION('center_axis',(0.,-1.,0.));
#1629=DIRECTION('ref_axis',(3.49720252756924E-15,0.,-1.));
#1630=DIRECTION('center_axis',(0.,1.,0.));
#1631=DIRECTION('ref_axis',(-3.44169137633799E-15,0.,1.));
#1632=DIRECTION('',(-1.,0.,-5.27355936696949E-15));
#1633=DIRECTION('center_axis',(0.,-1.,0.));
#1634=DIRECTION('ref_axis',(3.44169137633799E-15,0.,-1.));
#1635=DIRECTION('',(-3.44169137633799E-15,0.,1.));
#1636=DIRECTION('center_axis',(-1.,0.,-5.27355936696949E-15));
#1637=DIRECTION('ref_axis',(-5.27355936696949E-15,0.,1.));
#1638=DIRECTION('',(0.,1.,0.));
#1639=DIRECTION('',(-5.27355936696949E-15,9.85769611209906E-17,1.));
#1640=DIRECTION('center_axis',(1.,0.,5.27355936696949E-15));
#1641=DIRECTION('ref_axis',(-5.20417042793016E-15,4.4408920985004E-14,1.));
#1642=DIRECTION('',(0.,-1.,0.));
#1643=DIRECTION('center_axis',(1.,0.,5.27355936696949E-15));
#1644=DIRECTION('ref_axis',(-3.74708929979962E-28,-1.,7.10542735760065E-14));
#1645=DIRECTION('',(5.27355936696949E-15,-7.01105673205063E-15,-1.));
#1646=DIRECTION('center_axis',(-1.,0.,-5.27355936696949E-15));
#1647=DIRECTION('ref_axis',(3.7470892997998E-29,1.,-7.10542735760099E-15));
#1648=DIRECTION('',(3.1948866661451E-29,-1.,-6.22027006966178E-15));
#1649=DIRECTION('center_axis',(1.,0.,5.27355936696949E-15));
#1650=DIRECTION('ref_axis',(-2.53269627492661E-15,-0.877028794286187,0.480437814907316));
#1651=DIRECTION('',(4.6074255521944E-15,-0.48043781490742,-0.87702879428613));
#1652=DIRECTION('center_axis',(1.,0.,5.27355936696949E-15));
#1653=DIRECTION('ref_axis',(-1.07552855510558E-15,-0.978419112302532,0.206630202732142));
#1654=DIRECTION('center_axis',(-1.,0.,-5.27355936696949E-15));
#1655=DIRECTION('ref_axis',(-5.27355936696949E-15,0.,1.));
#1656=DIRECTION('center_axis',(-1.,0.,-5.27355936696949E-15));
#1657=DIRECTION('ref_axis',(-1.07552855510558E-15,-0.978419112302532,0.206630202732142));
#1658=DIRECTION('',(4.6074255521944E-15,-0.48043781490742,-0.87702879428613));
#1659=DIRECTION('center_axis',(-1.,0.,-5.27355936696949E-15));
#1660=DIRECTION('ref_axis',(-2.53269627492661E-15,-0.877028794286187,0.480437814907316));
#1661=DIRECTION('',(-3.1948866661451E-29,1.,6.22027006966178E-15));
#1662=DIRECTION('center_axis',(-1.,0.,-5.27355936696949E-15));
#1663=DIRECTION('ref_axis',(3.7470892997998E-29,1.,-7.10542735760099E-15));
#1664=DIRECTION('',(5.27355936696949E-15,-7.01105673205063E-15,-1.));
#1665=DIRECTION('center_axis',(-1.,0.,-5.27355936696949E-15));
#1666=DIRECTION('ref_axis',(-3.74708929979962E-28,-1.,7.10542735760065E-14));
#1667=DIRECTION('',(0.,1.,0.));
#1668=DIRECTION('center_axis',(-1.,0.,-5.27355936696949E-15));
#1669=DIRECTION('ref_axis',(-5.20417042793016E-15,4.4408920985004E-14,1.));
#1670=DIRECTION('',(-5.27355936696949E-15,9.85769611209906E-17,1.));
#1671=DIRECTION('center_axis',(-9.85769611209906E-17,1.,-3.51289621856232E-31));
#1672=DIRECTION('ref_axis',(1.,9.85769611209906E-17,3.44169137633799E-15));
#1673=DIRECTION('center_axis',(0.,-1.,0.));
#1674=DIRECTION('ref_axis',(-1.,0.,-1.77635683940025E-15));
#1675=DIRECTION('',(3.44169137633799E-15,0.,-1.));
#1676=DIRECTION('',(3.44169137633799E-15,0.,-1.));
#1677=DIRECTION('center_axis',(0.,0.,-1.));
#1678=DIRECTION('ref_axis',(1.,0.,0.));
#1679=DIRECTION('',(0.,0.,1.));
#1680=DIRECTION('center_axis',(0.,0.,-1.));
#1681=DIRECTION('ref_axis',(1.,0.,0.));
#1682=DIRECTION('center_axis',(3.44169137633799E-15,0.,-1.));
#1683=DIRECTION('ref_axis',(1.,0.,3.47869881049216E-15));
#1684=DIRECTION('',(-3.44169137633799E-15,0.,1.));
#1685=DIRECTION('center_axis',(1.93697285291437E-16,0.707106781186548,-0.707106781186548));
#1686=DIRECTION('ref_axis',(2.80292890914986E-16,0.707106781186548,0.707106781186548));
#1687=DIRECTION('',(1.,-3.35161667811368E-16,-6.12323399573677E-17));
#1688=DIRECTION('',(1.,-3.35161667811368E-16,-6.12323399573677E-17));
#1689=DIRECTION('center_axis',(-6.12323399573678E-17,-3.35161667811368E-16,
-1.));
#1690=DIRECTION('ref_axis',(3.35161667811368E-16,1.,-3.35161667811368E-16));
#1691=DIRECTION('',(1.,-3.35161667811368E-16,-6.12323399573677E-17));
#1692=DIRECTION('center_axis',(-2.80292890914986E-16,-0.707106781186548,
-0.707106781186547));
#1693=DIRECTION('ref_axis',(1.93697285291437E-16,0.707106781186547,-0.707106781186548));
#1694=DIRECTION('',(1.,-3.35161667811368E-16,-6.12323399573677E-17));
#1695=DIRECTION('center_axis',(-3.35161667811368E-16,-1.,3.35161667811368E-16));
#1696=DIRECTION('ref_axis',(-6.12323399573678E-17,-3.35161667811368E-16,
-1.));
#1697=DIRECTION('',(1.,-3.35161667811368E-16,-6.12323399573677E-17));
#1698=DIRECTION('center_axis',(-1.68531073547509E-16,-0.642787609686539,
0.766044443118978));
#1699=DIRECTION('ref_axis',(-2.96108122610097E-16,-0.766044443118978,-0.642787609686539));
#1700=DIRECTION('',(1.,-3.35161667811368E-16,-6.12323399573677E-17));
#1701=DIRECTION('center_axis',(1.,-3.35161667811368E-16,-6.12323399573677E-17));
#1702=DIRECTION('ref_axis',(3.35161667811368E-16,1.,-3.35161667811368E-16));
#1703=DIRECTION('center_axis',(0.,-1.,0.));
#1704=DIRECTION('ref_axis',(0.,0.,-1.));
#1705=DIRECTION('center_axis',(-0.743144825477393,0.,-0.66913060635886));
#1706=DIRECTION('ref_axis',(0.430108863029516,0.766044443118978,-0.477684286019533));
#1707=DIRECTION('',(-0.743144825477393,0.,-0.66913060635886));
#1708=DIRECTION('center_axis',(-0.430108863029516,-0.766044443118978,0.477684286019533));
#1709=DIRECTION('ref_axis',(-0.512583782722037,0.642787609686539,0.56928196398958));
#1710=DIRECTION('center_axis',(-2.9274135154686E-32,-1.,1.57946422003166E-17));
#1711=DIRECTION('ref_axis',(-1.83186799063151E-15,1.57946422003166E-17,
1.));
#1712=DIRECTION('center_axis',(3.44169137633799E-15,0.,-1.));
#1713=DIRECTION('ref_axis',(5.32907051819893E-14,-1.,1.85382312726874E-28));
#1714=DIRECTION('',(3.44169137633799E-15,0.,-1.));
#1715=DIRECTION('center_axis',(0.206630202732124,-0.978419112302535,7.21644966006352E-16));
#1716=DIRECTION('ref_axis',(-0.978419112302535,-0.206630202732124,-3.38618022510673E-15));
#1717=DIRECTION('center_axis',(1.,0.,5.27355936696949E-15));
#1718=DIRECTION('ref_axis',(-1.07552855510558E-15,-0.978419112302532,0.206630202732142));
#1719=DIRECTION('',(1.,0.,5.27355936696949E-15));
#1720=DIRECTION('center_axis',(-2.4980018054066E-15,-0.87702879428613,0.48043781490742));
#1721=DIRECTION('ref_axis',(4.6074255521944E-15,-0.48043781490742,-0.87702879428613));
#1722=DIRECTION('',(1.,0.,5.27355936696949E-15));
#1723=DIRECTION('center_axis',(1.,0.,5.27355936696949E-15));
#1724=DIRECTION('ref_axis',(-2.53269627492661E-15,-0.877028794286187,0.480437814907316));
#1725=DIRECTION('',(1.,0.,5.27355936696949E-15));
#1726=DIRECTION('center_axis',(-5.27355936696949E-15,-6.22027006966178E-15,
1.));
#1727=DIRECTION('ref_axis',(3.1948866661451E-29,-1.,-6.22027006966178E-15));
#1728=DIRECTION('',(1.,0.,5.27355936696949E-15));
#1729=DIRECTION('center_axis',(1.,0.,5.27355936696949E-15));
#1730=DIRECTION('ref_axis',(3.7470892997998E-29,1.,-7.10542735760099E-15));
#1731=DIRECTION('',(1.,0.,5.27355936696949E-15));
#1732=DIRECTION('center_axis',(-3.6682032092777E-29,-1.,7.01105673205063E-15));
#1733=DIRECTION('ref_axis',(5.27355936696949E-15,-7.01105673205063E-15,
-1.));
#1734=DIRECTION('',(1.,0.,5.27355936696949E-15));
#1735=DIRECTION('center_axis',(1.,0.,5.27355936696949E-15));
#1736=DIRECTION('ref_axis',(-3.74708929979962E-28,-1.,7.10542735760065E-14));
#1737=DIRECTION('',(1.,0.,5.27355936696949E-15));
#1738=DIRECTION('center_axis',(-5.27355936696949E-15,0.,1.));
#1739=DIRECTION('ref_axis',(0.,-1.,0.));
#1740=DIRECTION('',(1.,0.,5.27355936696949E-15));
#1741=DIRECTION('center_axis',(1.,0.,5.27355936696949E-15));
#1742=DIRECTION('ref_axis',(-5.20417042793016E-15,4.4408920985004E-14,1.));
#1743=DIRECTION('',(1.,0.,5.27355936696949E-15));
#1744=DIRECTION('center_axis',(5.17689969051289E-31,1.,-9.85769611209906E-17));
#1745=DIRECTION('ref_axis',(-5.27355936696949E-15,9.85769611209906E-17,
1.));
#1746=DIRECTION('center_axis',(-0.743144825477393,0.,-0.66913060635886));
#1747=DIRECTION('ref_axis',(-0.66913060635886,0.,0.743144825477393));
#1748=DIRECTION('',(-0.743144825477393,0.,-0.66913060635886));
#1749=DIRECTION('center_axis',(0.66913060635886,0.,-0.743144825477393));
#1750=DIRECTION('ref_axis',(0.,-1.,0.));
#1751=DIRECTION('',(-0.743144825477393,0.,-0.66913060635886));
#1752=DIRECTION('center_axis',(0.473146789255816,0.707106781186549,-0.525482745498757));
#1753=DIRECTION('ref_axis',(0.473146789255817,-0.707106781186546,-0.525482745498759));
#1754=DIRECTION('center_axis',(3.35161667811368E-16,1.,0.));
#1755=DIRECTION('ref_axis',(0.,0.,1.));
#1756=DIRECTION('center_axis',(1.93697285291437E-16,0.707106781186547,-0.707106781186548));
#1757=DIRECTION('ref_axis',(2.80292890914986E-16,0.707106781186548,0.707106781186547));
#1758=DIRECTION('',(1.,-3.35161667811368E-16,-6.12323399573677E-17));
#1759=DIRECTION('center_axis',(-6.12323399573678E-17,-3.35161667811368E-16,
-1.));
#1760=DIRECTION('ref_axis',(3.35161667811368E-16,1.,-3.35161667811368E-16));
#1761=DIRECTION('',(1.,-3.35161667811368E-16,-6.12323399573677E-17));
#1762=DIRECTION('center_axis',(1.,-3.35161667811368E-16,-6.12323399573677E-17));
#1763=DIRECTION('ref_axis',(-3.35161667811368E-16,-1.,3.35161667811368E-16));
#1764=DIRECTION('',(1.,-3.35161667811368E-16,-6.12323399573677E-17));
#1765=DIRECTION('center_axis',(3.35161667811368E-16,1.,-3.35161667811368E-16));
#1766=DIRECTION('ref_axis',(6.12323399573678E-17,3.35161667811368E-16,1.));
#1767=DIRECTION('center_axis',(0.,0.,1.));
#1768=DIRECTION('ref_axis',(1.,0.,0.));
#1769=DIRECTION('center_axis',(0.,-1.,0.));
#1770=DIRECTION('ref_axis',(3.49720252756924E-15,0.,-1.));
#1771=CARTESIAN_POINT('',(0.,0.,0.));
#1772=CARTESIAN_POINT('Origin',(156.60095440906,41.,-32.2795046151598));
#1773=CARTESIAN_POINT('',(130.515519307328,82.,-3.30869393641141));
#1774=CARTESIAN_POINT('',(130.515519307328,29.,-3.30869393641141));
#1775=CARTESIAN_POINT('',(130.515519307328,20.5,-3.30869393641141));
#1776=CARTESIAN_POINT('',(168.3595982278,29.,-45.3388015976712));
#1777=CARTESIAN_POINT('',(79.0037597713119,29.,53.9009108651595));
#1778=CARTESIAN_POINT('',(169.649924816888,28.2981333293569,-46.7718544557298));
#1779=CARTESIAN_POINT('Origin',(168.3595982278,26.,-45.3388015976712));
#1780=CARTESIAN_POINT('',(176.448078924779,19.7731479970349,-54.3219694856902));
#1781=CARTESIAN_POINT('',(185.039005179312,9.,-63.863159697949));
#1782=CARTESIAN_POINT('',(176.448078924779,49.5,-54.3219694856902));
#1783=CARTESIAN_POINT('',(176.448078924779,20.5,-54.3219694856902));
#1784=CARTESIAN_POINT('',(147.1662128594,49.5,-21.8011625759286));
#1785=CARTESIAN_POINT('',(147.1662128594,49.5,-21.8011625759286));
#1786=CARTESIAN_POINT('',(140.474906795811,59.5,-14.3697143211546));
#1787=CARTESIAN_POINT('Origin',(147.1662128594,59.5,-21.8011625759286));
#1788=CARTESIAN_POINT('',(140.474906795811,79.,-14.3697143211546));
#1789=CARTESIAN_POINT('',(140.474906795811,79.,-14.3697143211546));
#1790=CARTESIAN_POINT('',(138.467514976735,82.,-12.1402798447225));
#1791=CARTESIAN_POINT('',(140.474906795811,79.,-14.3697143211546));
#1792=CARTESIAN_POINT('',(138.467514976735,82.,-12.1402798447225));
#1793=CARTESIAN_POINT('Origin',(126.799795179941,82.,-6.6543469682057));
#1794=CARTESIAN_POINT('',(126.799795179941,82.,-6.6543469682057));
#1795=CARTESIAN_POINT('',(130.515519307328,82.,-3.30869393641141));
#1796=CARTESIAN_POINT('',(123.084071052554,82.,-5.));
#1797=CARTESIAN_POINT('Origin',(123.084071052554,82.,-10.));
#1798=CARTESIAN_POINT('',(123.084071052554,82.,0.));
#1799=CARTESIAN_POINT('',(123.084071052554,82.,0.));
#1800=CARTESIAN_POINT('Origin',(123.084071052554,82.,-10.));
#1801=CARTESIAN_POINT('Origin',(123.084071052554,55.5,-10.));
#1802=CARTESIAN_POINT('',(126.799795179941,29.,-6.6543469682057));
#1803=CARTESIAN_POINT('',(126.799795179941,20.5,-6.6543469682057));
#1804=CARTESIAN_POINT('',(123.084071052554,29.,-5.));
#1805=CARTESIAN_POINT('Origin',(123.084071052554,29.,-10.));
#1806=CARTESIAN_POINT('',(123.084071052554,20.5,-5.));
#1807=CARTESIAN_POINT('Origin',(168.3595982278,29.,-45.3388015976712));
#1808=CARTESIAN_POINT('',(130.515519307328,29.,-3.30869393641141));
#1809=CARTESIAN_POINT('',(164.643874100413,29.,-48.6844546294655));
#1810=CARTESIAN_POINT('',(75.2880356439249,29.,50.5552578333652));
#1811=CARTESIAN_POINT('',(168.3595982278,29.,-45.3388015976712));
#1812=CARTESIAN_POINT('Origin',(88.9518501061792,82.,42.8524372406046));
#1813=CARTESIAN_POINT('',(134.751790849348,82.,-15.4859328765168));
#1814=CARTESIAN_POINT('',(138.467514976735,82.,-12.1402798447225));
#1815=CARTESIAN_POINT('',(134.751790849348,82.,-15.4859328765168));
#1816=CARTESIAN_POINT('Origin',(168.1,41.,-5.));
#1817=CARTESIAN_POINT('',(85.1548667764616,29.,-5.));
#1818=CARTESIAN_POINT('',(52.1328122786061,29.,-5.));
#1819=CARTESIAN_POINT('',(85.1548667764616,82.,-5.));
#1820=CARTESIAN_POINT('',(85.1548667764616,20.5,-5.));
#1821=CARTESIAN_POINT('',(141.,82.,-5.));
#1822=CARTESIAN_POINT('',(99.65,50.5,-5.));
#1823=CARTESIAN_POINT('Origin',(104.9,50.5,-5.));
#1824=CARTESIAN_POINT('Origin',(185.673017445317,29.,0.));
#1825=CARTESIAN_POINT('',(123.084071052554,29.,0.));
#1826=CARTESIAN_POINT('',(123.084071052554,29.,0.));
#1827=CARTESIAN_POINT('',(85.1548667764616,29.,0.));
#1828=CARTESIAN_POINT('',(52.1328122786061,29.,0.));
#1829=CARTESIAN_POINT('',(85.1548667764616,29.,0.));
#1830=CARTESIAN_POINT('Origin',(123.084071052554,55.5,-10.));
#1831=CARTESIAN_POINT('',(123.084071052554,20.5,0.));
#1832=CARTESIAN_POINT('Origin',(123.084071052554,29.,-10.));
#1833=CARTESIAN_POINT('Origin',(75.1548667764616,41.,84.2548667764616));
#1834=CARTESIAN_POINT('',(75.1548667764616,29.,-10.));
#1835=CARTESIAN_POINT('',(75.1548667764616,82.,-10.));
#1836=CARTESIAN_POINT('',(75.1548667764616,20.5,-10.));
#1837=CARTESIAN_POINT('',(75.1548667764616,82.,-16.8451332235384));
#1838=CARTESIAN_POINT('',(75.1548667764616,82.,57.1548667764616));
#1839=CARTESIAN_POINT('',(75.1548667764616,79.,-19.8451332235384));
#1840=CARTESIAN_POINT('',(75.1548667764616,79.,-19.8451332235384));
#1841=CARTESIAN_POINT('',(75.1548667764616,59.5,-19.8451332235384));
#1842=CARTESIAN_POINT('',(75.1548667764616,79.,-19.8451332235384));
#1843=CARTESIAN_POINT('',(75.1548667764616,49.5,-29.8451332235384));
#1844=CARTESIAN_POINT('Origin',(75.1548667764616,59.5,-29.8451332235384));
#1845=CARTESIAN_POINT('',(75.1548667764616,49.5,-80.8451332235384));
#1846=CARTESIAN_POINT('',(75.1548667764616,49.5,-80.8451332235384));
#1847=CARTESIAN_POINT('',(75.1548667764616,46.5,-83.8451332235384));
#1848=CARTESIAN_POINT('',(75.1548667764616,49.5,-80.8451332235384));
#1849=CARTESIAN_POINT('',(75.1548667764616,2.99999999999997,-83.8451332235384));
#1850=CARTESIAN_POINT('',(75.1548667764616,46.5,-83.8451332235384));
#1851=CARTESIAN_POINT('',(75.1548667764616,-2.56398675875697E-14,-80.8451332235384));
#1852=CARTESIAN_POINT('',(75.1548667764616,-2.56398675875697E-14,-80.8451332235384));
#1853=CARTESIAN_POINT('',(75.1548667764616,-1.77970845607836E-14,-57.4451332235384));
#1854=CARTESIAN_POINT('',(75.1548667764616,-2.56398675875697E-14,-80.8451332235384));
#1855=CARTESIAN_POINT('',(75.1548667764616,27.9283628290596,-34.0104542742892));
#1856=CARTESIAN_POINT('',(75.1548667764616,-1.77970845607836E-14,-57.4451332235384));
#1857=CARTESIAN_POINT('',(75.1548667764616,29.,-31.7123209449323));
#1858=CARTESIAN_POINT('Origin',(75.1548667764616,26.,-31.7123209449323));
#1859=CARTESIAN_POINT('',(75.1548667764616,29.,-31.7123209449323));
#1860=CARTESIAN_POINT('Origin',(80.1548667764616,29.,-10.));
#1861=CARTESIAN_POINT('',(80.1548667764616,29.,-10.));
#1862=CARTESIAN_POINT('',(75.1548667764616,29.,-9.99999999999999));
#1863=CARTESIAN_POINT('Origin',(85.1548667764616,29.,-10.));
#1864=CARTESIAN_POINT('Origin',(85.1548667764616,29.,-10.));
#1865=CARTESIAN_POINT('Origin',(85.1548667764616,55.5,-10.));
#1866=CARTESIAN_POINT('',(80.1548667764616,82.,-10.));
#1867=CARTESIAN_POINT('',(80.1548667764616,20.5,-10.));
#1868=CARTESIAN_POINT('Origin',(85.1548667764616,82.,-10.));
#1869=CARTESIAN_POINT('Origin',(75.1548667764616,82.,-16.8451332235384));
#1870=CARTESIAN_POINT('',(75.1548667764616,82.,-10.));
#1871=CARTESIAN_POINT('',(80.1548667764616,82.,-16.8451332235384));
#1872=CARTESIAN_POINT('',(80.1548667764616,82.,57.1548667764616));
#1873=CARTESIAN_POINT('',(75.1548667764616,82.,-16.8451332235384));
#1874=CARTESIAN_POINT('Origin',(75.1548667764616,29.,101.827884221779));
#1875=CARTESIAN_POINT('',(80.1548667764616,29.,-31.7123209449322));
#1876=CARTESIAN_POINT('',(75.1548667764616,29.,-31.7123209449323));
#1877=CARTESIAN_POINT('',(80.1548667764616,29.,-31.7123209449323));
#1878=CARTESIAN_POINT('Origin',(152.885230281673,41.,-35.6251576469541));
#1879=CARTESIAN_POINT('',(136.759182668424,79.,-17.7153673529489));
#1880=CARTESIAN_POINT('',(136.759182668424,79.,-17.7153673529489));
#1881=CARTESIAN_POINT('',(136.759182668424,59.5,-17.7153673529489));
#1882=CARTESIAN_POINT('',(136.759182668424,79.,-17.7153673529489));
#1883=CARTESIAN_POINT('',(143.450488732013,49.5,-25.1468156077229));
#1884=CARTESIAN_POINT('Origin',(143.450488732013,59.5,-25.1468156077229));
#1885=CARTESIAN_POINT('',(172.732354797392,49.5,-57.6676225174845));
#1886=CARTESIAN_POINT('',(143.450488732013,49.5,-25.1468156077229));
#1887=CARTESIAN_POINT('',(172.732354797392,19.7731479970349,-57.6676225174845));
#1888=CARTESIAN_POINT('',(172.732354797392,20.5,-57.6676225174845));
#1889=CARTESIAN_POINT('',(165.934200689501,28.2981333293569,-50.1175074875241));
#1890=CARTESIAN_POINT('',(181.323281051924,9.,-67.2088127297433));
#1891=CARTESIAN_POINT('Origin',(164.643874100413,26.,-48.6844546294655));
#1892=CARTESIAN_POINT('Origin',(67.,82.,0.));
#1893=CARTESIAN_POINT('',(85.1548667764616,82.,0.));
#1894=CARTESIAN_POINT('',(85.1548667764616,82.,0.));
#1895=CARTESIAN_POINT('',(141.,82.,0.));
#1896=CARTESIAN_POINT('Origin',(85.1548667764616,55.5,-10.));
#1897=CARTESIAN_POINT('',(85.1548667764616,20.5,0.));
#1898=CARTESIAN_POINT('Origin',(85.1548667764616,82.,-10.));
#1899=CARTESIAN_POINT('Origin',(179.016630670005,9.,-68.5743377202634));
#1900=CARTESIAN_POINT('',(179.016630670005,15.3444844789816,-61.0132755492788));
#1901=CARTESIAN_POINT('',(176.216630670005,15.3444844789817,-61.0132755492788));
#1902=CARTESIAN_POINT('',(179.016630670005,15.3444844789817,-61.0132755492788));
#1903=CARTESIAN_POINT('',(174.016630670005,15.3444844789816,-61.0132755492788));
#1904=CARTESIAN_POINT('',(179.016630670005,15.3444844789817,-61.0132755492788));
#1905=CARTESIAN_POINT('',(174.016630670005,9.,-68.5743377202634));
#1906=CARTESIAN_POINT('',(174.016630670005,9.,-68.5743377202634));
#1907=CARTESIAN_POINT('',(179.016630670005,9.,-68.5743377202634));
#1908=CARTESIAN_POINT('',(179.016630670005,9.,-68.5743377202634));
#1909=CARTESIAN_POINT('',(179.016630670005,9.,-68.5743377202634));
#1910=CARTESIAN_POINT('Origin',(169.016630670005,33.5294081190041,-61.0132755492788));
#1911=CARTESIAN_POINT('',(179.016630670005,49.5,-61.0132755492788));
#1912=CARTESIAN_POINT('',(179.016630670005,20.5,-61.0132755492788));
#1913=CARTESIAN_POINT('Origin',(169.016630670005,49.5,-61.0132755492788));
#1914=CARTESIAN_POINT('Ctrl Pts',(179.016630670005,15.3444844789816,-61.0132755492788));
#1915=CARTESIAN_POINT('Ctrl Pts',(179.016630670005,16.8207056516661,-58.5698145964868));
#1916=CARTESIAN_POINT('Ctrl Pts',(178.083073433735,18.2969268243505,-56.1378148490137));
#1917=CARTESIAN_POINT('Ctrl Pts',(176.448078924779,19.7731479970349,-54.3219694856902));
#1918=CARTESIAN_POINT('Origin',(174.016630670005,49.5,-61.0132755492788));
#1919=CARTESIAN_POINT('',(174.016630670005,49.5,-61.0132755492788));
#1920=CARTESIAN_POINT('',(179.016630670005,49.5,-61.0132755492788));
#1921=CARTESIAN_POINT('Origin',(169.016630670005,49.5,-61.0132755492788));
#1922=CARTESIAN_POINT('',(176.448078924779,49.5,-54.3219694856902));
#1923=CARTESIAN_POINT('Origin',(174.016630670005,41.,-26.0743377202634));
#1924=CARTESIAN_POINT('',(174.016630670005,20.5,-61.0132755492788));
#1925=CARTESIAN_POINT('',(174.016630670005,49.5,-97.3194709438018));
#1926=CARTESIAN_POINT('',(174.016630670005,49.5,-11.9743377202634));
#1927=CARTESIAN_POINT('',(174.016630670005,9.,-97.3194709438018));
#1928=CARTESIAN_POINT('',(174.016630670005,20.5,-97.3194709438018));
#1929=CARTESIAN_POINT('',(174.016630670005,9.,-138.865453564781));
#1930=CARTESIAN_POINT('Origin',(179.016630670005,41.,-26.0743377202634));
#1931=CARTESIAN_POINT('',(179.016630670005,9.,-97.3194709438017));
#1932=CARTESIAN_POINT('',(179.016630670005,9.,-138.865453564781));
#1933=CARTESIAN_POINT('',(179.016630670005,49.5,-97.3194709438017));
#1934=CARTESIAN_POINT('',(179.016630670005,20.5,-97.3194709438017));
#1935=CARTESIAN_POINT('',(179.016630670005,49.5,-11.9743377202634));
#1936=CARTESIAN_POINT('Origin',(147.1662128594,49.5,-21.8011625759286));
#1937=CARTESIAN_POINT('',(147.1662128594,49.5,-21.8011625759286));
#1938=CARTESIAN_POINT('Origin',(80.1548667764616,41.,84.2548667764616));
#1939=CARTESIAN_POINT('',(80.1548667764616,27.9283628290596,-34.0104542742892));
#1940=CARTESIAN_POINT('Origin',(80.1548667764616,26.,-31.7123209449323));
#1941=CARTESIAN_POINT('',(80.1548667764616,-1.94728928998405E-14,-57.4451332235384));
#1942=CARTESIAN_POINT('',(80.1548667764616,-1.94728928998405E-14,-57.4451332235384));
#1943=CARTESIAN_POINT('',(80.1548667764616,-2.73156759266265E-14,-80.8451332235384));
#1944=CARTESIAN_POINT('',(80.1548667764616,-2.73156759266265E-14,-80.8451332235384));
#1945=CARTESIAN_POINT('',(80.1548667764616,2.99999999999997,-83.8451332235384));
#1946=CARTESIAN_POINT('',(80.1548667764616,-2.73156759266265E-14,-80.8451332235384));
#1947=CARTESIAN_POINT('',(80.1548667764616,46.5,-83.8451332235384));
#1948=CARTESIAN_POINT('',(80.1548667764616,46.5,-83.8451332235384));
#1949=CARTESIAN_POINT('',(80.1548667764616,49.5,-80.8451332235384));
#1950=CARTESIAN_POINT('',(80.1548667764616,49.5,-80.8451332235384));
#1951=CARTESIAN_POINT('',(80.1548667764616,49.5,-29.8451332235384));
#1952=CARTESIAN_POINT('',(80.1548667764616,49.5,-80.8451332235384));
#1953=CARTESIAN_POINT('',(80.1548667764616,59.5,-19.8451332235384));
#1954=CARTESIAN_POINT('Origin',(80.1548667764616,59.5,-29.8451332235384));
#1955=CARTESIAN_POINT('',(80.1548667764616,79.,-19.8451332235384));
#1956=CARTESIAN_POINT('',(80.1548667764616,79.,-19.8451332235384));
#1957=CARTESIAN_POINT('',(80.1548667764616,79.,-19.8451332235384));
#1958=CARTESIAN_POINT('Ctrl Pts',(179.016630670005,15.3444844789816,-61.0132755492788));
#1959=CARTESIAN_POINT('Ctrl Pts',(177.349964003338,15.3444844789816,-61.0132755492788));
#1960=CARTESIAN_POINT('Ctrl Pts',(175.683297336671,15.3444844789816,-61.0132755492788));
#1961=CARTESIAN_POINT('Ctrl Pts',(174.016630670005,15.3444844789816,-61.0132755492788));
#1962=CARTESIAN_POINT('Ctrl Pts',(179.016630670005,16.8207056516661,-58.5698145964868));
#1963=CARTESIAN_POINT('Ctrl Pts',(177.348590805192,16.8207056516661,-58.9774926519716));
#1964=CARTESIAN_POINT('Ctrl Pts',(175.684670534818,16.8207056516661,-59.383867017398));
#1965=CARTESIAN_POINT('Ctrl Pts',(174.016630670005,16.8207056516661,-59.7915450728828));
#1966=CARTESIAN_POINT('Ctrl Pts',(178.083073433735,18.2969268243505,-56.1378148490137));
#1967=CARTESIAN_POINT('Ctrl Pts',(176.570688375043,18.2969268243505,-56.9509875377927));
#1968=CARTESIAN_POINT('Ctrl Pts',(175.062237110561,18.2969268243505,-57.7623725103673));
#1969=CARTESIAN_POINT('Ctrl Pts',(173.54985205187,18.2969268243505,-58.5755451991463));
#1970=CARTESIAN_POINT('Ctrl Pts',(176.448078924779,19.7731479970349,-54.3219694856902));
#1971=CARTESIAN_POINT('Ctrl Pts',(175.20950421565,19.7731479970349,-55.437187162955));
#1972=CARTESIAN_POINT('Ctrl Pts',(173.970929506521,19.7731479970349,-56.5524048402198));
#1973=CARTESIAN_POINT('Ctrl Pts',(172.732354797392,19.7731479970349,-57.6676225174845));
#1974=CARTESIAN_POINT('',(174.367273413442,19.7731479970349,-56.195535183495));
#1975=CARTESIAN_POINT('',(176.448078924779,19.7731479970349,-54.3219694856902));
#1976=CARTESIAN_POINT('',(176.448078924779,19.7731479970349,-54.3219694856902));
#1977=CARTESIAN_POINT('Ctrl Pts',(174.016630670005,15.3444844789817,-61.0132755492788));
#1978=CARTESIAN_POINT('Ctrl Pts',(174.016630670005,16.8207056516661,-59.7915450728828));
#1979=CARTESIAN_POINT('Ctrl Pts',(173.54985205187,18.2969268243505,-58.5755451991463));
#1980=CARTESIAN_POINT('Ctrl Pts',(172.732354797392,19.7731479970349,-57.6676225174845));
#1981=CARTESIAN_POINT('Origin',(214.25287973806,9.,-102.319470943802));
#1982=CARTESIAN_POINT('',(184.016630670005,9.,-102.319470943802));
#1983=CARTESIAN_POINT('',(184.016630670005,9.,-107.319470943802));
#1984=CARTESIAN_POINT('',(184.016630670005,9.,-102.319470943802));
#1985=CARTESIAN_POINT('',(214.252879738061,9.,-107.319470943802));
#1986=CARTESIAN_POINT('',(214.252879738061,9.,-107.319470943802));
#1987=CARTESIAN_POINT('',(214.25287973806,9.,-102.319470943802));
#1988=CARTESIAN_POINT('',(214.25287973806,9.,-102.319470943802));
#1989=CARTESIAN_POINT('',(214.25287973806,9.,-102.319470943802));
#1990=CARTESIAN_POINT('Origin',(184.016630670005,29.25,-97.3194709438017));
#1991=CARTESIAN_POINT('',(184.016630670005,49.5,-102.319470943802));
#1992=CARTESIAN_POINT('',(184.016630670005,20.5,-102.319470943802));
#1993=CARTESIAN_POINT('Origin',(184.016630670005,49.5,-97.3194709438017));
#1994=CARTESIAN_POINT('Origin',(184.016630670005,9.,-97.3194709438017));
#1995=CARTESIAN_POINT('Origin',(184.016630670005,49.5,-107.319470943802));
#1996=CARTESIAN_POINT('',(184.016630670005,49.5,-107.319470943802));
#1997=CARTESIAN_POINT('',(184.016630670005,49.5,-102.319470943802));
#1998=CARTESIAN_POINT('Origin',(184.016630670005,49.5,-97.3194709438017));
#1999=CARTESIAN_POINT('',(179.016630670005,49.5,-97.3194709438017));
#2000=CARTESIAN_POINT('Origin',(101.461763893543,41.,-107.319470943802));
#2001=CARTESIAN_POINT('',(184.016630670005,20.5,-107.319470943802));
#2002=CARTESIAN_POINT('',(234.806897117082,49.5,-107.319470943802));
#2003=CARTESIAN_POINT('',(87.3617638935432,49.5,-107.319470943802));
#2004=CARTESIAN_POINT('',(234.806897117082,13.2966444029273,-107.319470943802));
#2005=CARTESIAN_POINT('',(234.806897117082,20.5,-107.319470943802));
#2006=CARTESIAN_POINT('',(214.666140143525,9.04316177539494,-107.319470943802));
#2007=CARTESIAN_POINT('',(251.739009854135,16.8725004379826,-107.319470943801));
#2008=CARTESIAN_POINT('Origin',(214.25287973806,11.0000000000007,-107.319470943802));
#2009=CARTESIAN_POINT('',(206.961763893543,27.,-107.319470943802));
#2010=CARTESIAN_POINT('Origin',(214.461763893543,27.,-107.319470943802));
#2011=CARTESIAN_POINT('Origin',(101.461763893543,41.,-102.319470943802));
#2012=CARTESIAN_POINT('',(214.666140143525,9.04316177539494,-102.319470943802));
#2013=CARTESIAN_POINT('Origin',(214.25287973806,11.0000000000007,-102.319470943802));
#2014=CARTESIAN_POINT('',(234.806897117082,13.2966444029273,-102.319470943801));
#2015=CARTESIAN_POINT('',(251.739009854135,16.8725004379826,-102.319470943801));
#2016=CARTESIAN_POINT('',(234.806897117082,49.5,-102.319470943801));
#2017=CARTESIAN_POINT('',(234.806897117082,20.5,-102.319470943801));
#2018=CARTESIAN_POINT('',(87.3617638935432,49.5,-102.319470943802));
#2019=CARTESIAN_POINT('',(206.961763893543,27.,-102.319470943802));
#2020=CARTESIAN_POINT('Origin',(214.461763893543,27.,-102.319470943802));
#2021=CARTESIAN_POINT('Origin',(179.016630670005,49.5,-11.9743377202634));
#2022=CARTESIAN_POINT('Origin',(169.016630670005,33.5294081190041,-61.0132755492788));
#2023=CARTESIAN_POINT('Origin',(184.016630670005,9.,-102.319470943802));
#2024=CARTESIAN_POINT('',(179.016630670005,9.,-97.3194709438017));
#2025=CARTESIAN_POINT('Origin',(184.016630670005,9.,-97.3194709438017));
#2026=CARTESIAN_POINT('Origin',(239.806897117082,16.8725004379826,-91.6970917596716));
#2027=CARTESIAN_POINT('',(239.806897117082,15.6851224155729,-97.3194709438015));
#2028=CARTESIAN_POINT('',(242.006897117082,15.6851224155729,-97.3194709438015));
#2029=CARTESIAN_POINT('',(239.806897117082,15.6851224155729,-97.3194709438015));
#2030=CARTESIAN_POINT('',(244.806897117082,15.6851224155729,-97.3194709438015));
#2031=CARTESIAN_POINT('',(239.806897117082,15.6851224155729,-97.3194709438015));
#2032=CARTESIAN_POINT('',(244.806897117082,16.8725004379826,-91.6970917596716));
#2033=CARTESIAN_POINT('',(244.806897117082,16.8725004379826,-91.6970917596716));
#2034=CARTESIAN_POINT('',(239.806897117082,16.8725004379826,-91.6970917596716));
#2035=CARTESIAN_POINT('',(239.806897117082,16.8725004379826,-91.6970917596716));
#2036=CARTESIAN_POINT('',(239.806897117082,16.8725004379826,-91.6970917596716));
#2037=CARTESIAN_POINT('Origin',(234.806897117082,31.9954417046251,-97.3194709438015));
#2038=CARTESIAN_POINT('',(239.806897117082,49.5,-97.3194709438015));
#2039=CARTESIAN_POINT('',(239.806897117082,20.5,-97.3194709438015));
#2040=CARTESIAN_POINT('Origin',(234.806897117082,49.5,-97.3194709438015));
#2041=CARTESIAN_POINT('Ctrl Pts',(239.806897117082,15.6851224155729,-97.3194709438015));
#2042=CARTESIAN_POINT('Ctrl Pts',(239.806897117082,15.3439112709093,-98.4414683200836));
#2043=CARTESIAN_POINT('Ctrl Pts',(239.413314558007,15.0027001262456,-99.559418040564));
#2044=CARTESIAN_POINT('Ctrl Pts',(238.715004893036,14.661488981582,-100.436645892228));
#2045=CARTESIAN_POINT('Ctrl Pts',(238.249465116388,14.4340148851395,-101.021464460004));
#2046=CARTESIAN_POINT('Ctrl Pts',(237.648491070453,14.2065407886971,-101.499295530528));
#2047=CARTESIAN_POINT('Ctrl Pts',(236.97484863334,13.9790666922546,-101.823797151478));
#2048=CARTESIAN_POINT('Ctrl Pts',(236.301206196227,13.7515925958122,-102.148298772427));
#2049=CARTESIAN_POINT('Ctrl Pts',(235.554895367936,13.5241184993698,-102.319470943802));
#2050=CARTESIAN_POINT('Ctrl Pts',(234.806897117082,13.2966444029273,-102.319470943802));
#2051=CARTESIAN_POINT('Origin',(244.806897117082,49.5,-97.3194709438015));
#2052=CARTESIAN_POINT('',(244.806897117082,49.5,-97.3194709438015));
#2053=CARTESIAN_POINT('',(239.806897117082,49.5,-97.3194709438015));
#2054=CARTESIAN_POINT('Origin',(234.806897117082,49.5,-97.3194709438015));
#2055=CARTESIAN_POINT('',(234.806897117082,49.5,-102.319470943801));
#2056=CARTESIAN_POINT('Origin',(244.806897117082,41.,-241.974337720263));
#2057=CARTESIAN_POINT('',(244.806897117082,20.5,-97.3194709438015));
#2058=CARTESIAN_POINT('',(244.806897117082,49.5,-75.8743377202632));
#2059=CARTESIAN_POINT('',(244.806897117082,49.5,-256.074337720263));
#2060=CARTESIAN_POINT('',(244.806897117082,47.5,-73.8743377202632));
#2061=CARTESIAN_POINT('Origin',(244.806897117082,47.4999999999999,-75.8743377202633));
#2062=CARTESIAN_POINT('',(244.806897117082,43.8788860139263,-73.8743377202632));
#2063=CARTESIAN_POINT('',(244.806897117082,43.8788860139263,-73.8743377202632));
#2064=CARTESIAN_POINT('',(244.806897117082,41.8788860139263,-75.8743377202632));
#2065=CARTESIAN_POINT('Origin',(244.806897117082,43.8788860139264,-75.8743377202633));
#2066=CARTESIAN_POINT('',(244.806897117082,41.8788860139263,-77.1411630415302));
#2067=CARTESIAN_POINT('',(244.806897117082,41.8788860139263,-75.8743377202632));
#2068=CARTESIAN_POINT('',(244.806897117082,31.8788860139263,-87.1411630415301));
#2069=CARTESIAN_POINT('Origin',(244.806897117082,31.8788860139263,-77.1411630415301));
#2070=CARTESIAN_POINT('',(244.806897117082,20.4558653443634,-87.1411630415302));
#2071=CARTESIAN_POINT('',(244.806897117082,20.4558653443634,-87.1411630415302));
#2072=CARTESIAN_POINT('',(244.806897117082,18.7018077557912,-88.1802874117153));
#2073=CARTESIAN_POINT('Origin',(244.806897117082,20.4558653443632,-89.1411630415298));
#2074=CARTESIAN_POINT('',(244.806897117082,17.0752810740154,-91.149476535321));
#2075=CARTESIAN_POINT('',(244.806897117082,18.7018077557912,-88.1802874117153));
#2076=CARTESIAN_POINT('Origin',(244.806897117082,18.8293386625878,-92.1103521651359));
#2077=CARTESIAN_POINT('Origin',(239.806897117082,41.,-241.974337720263));
#2078=CARTESIAN_POINT('',(239.806897117082,17.0752810740154,-91.149476535321));
#2079=CARTESIAN_POINT('Origin',(239.806897117082,18.8293386625878,-92.1103521651359));
#2080=CARTESIAN_POINT('',(239.806897117082,18.7018077557912,-88.1802874117154));
#2081=CARTESIAN_POINT('',(239.806897117082,18.7018077557912,-88.1802874117154));
#2082=CARTESIAN_POINT('',(239.806897117082,20.4558653443634,-87.1411630415302));
#2083=CARTESIAN_POINT('Origin',(239.806897117082,20.4558653443632,-89.1411630415298));
#2084=CARTESIAN_POINT('',(239.806897117082,31.8788860139263,-87.1411630415302));
#2085=CARTESIAN_POINT('',(239.806897117082,20.4558653443634,-87.1411630415302));
#2086=CARTESIAN_POINT('',(239.806897117081,41.8788860139263,-77.1411630415302));
#2087=CARTESIAN_POINT('Origin',(239.806897117082,31.8788860139263,-77.1411630415302));
#2088=CARTESIAN_POINT('',(239.806897117082,41.8788860139263,-75.8743377202632));
#2089=CARTESIAN_POINT('',(239.806897117082,41.8788860139263,-75.8743377202632));
#2090=CARTESIAN_POINT('',(239.806897117082,43.8788860139263,-73.8743377202632));
#2091=CARTESIAN_POINT('Origin',(239.806897117082,43.8788860139264,-75.8743377202634));
#2092=CARTESIAN_POINT('',(239.806897117082,47.5,-73.8743377202632));
#2093=CARTESIAN_POINT('',(239.806897117082,43.8788860139263,-73.8743377202632));
#2094=CARTESIAN_POINT('',(239.806897117082,49.5,-75.8743377202632));
#2095=CARTESIAN_POINT('Origin',(239.806897117082,47.4999999999999,-75.8743377202634));
#2096=CARTESIAN_POINT('',(239.806897117082,49.5,-256.074337720263));
#2097=CARTESIAN_POINT('Origin',(87.3617638935432,49.5,-102.319470943802));
#2098=CARTESIAN_POINT('Origin',(184.016630670005,29.25,-97.3194709438017));
#2099=CARTESIAN_POINT('Ctrl Pts',(239.806897117082,15.6851224155729,-97.3194709438015));
#2100=CARTESIAN_POINT('Ctrl Pts',(241.473563783748,15.6851224155729,-97.3194709438015));
#2101=CARTESIAN_POINT('Ctrl Pts',(243.140230450415,15.6851224155729,-97.3194709438014));
#2102=CARTESIAN_POINT('Ctrl Pts',(244.806897117082,15.6851224155729,-97.3194709438014));
#2103=CARTESIAN_POINT('Ctrl Pts',(239.806897117082,15.3439112709093,-98.4414683200836));
#2104=CARTESIAN_POINT('Ctrl Pts',(241.475297106472,15.3439112709093,-98.8159185833021));
#2105=CARTESIAN_POINT('Ctrl Pts',(243.138497127692,15.3439112709093,-99.189015433147));
#2106=CARTESIAN_POINT('Ctrl Pts',(244.806897117082,15.3439112709093,-99.5634656963655));
#2107=CARTESIAN_POINT('Ctrl Pts',(239.150926185291,14.7752260298032,-100.304717854218));
#2108=CARTESIAN_POINT('Ctrl Pts',(240.599063810441,14.7752260298032,-101.299862557511));
#2109=CARTESIAN_POINT('Ctrl Pts',(242.04681762835,14.7752260298032,-102.294820061341));
#2110=CARTESIAN_POINT('Ctrl Pts',(243.4949552535,14.7752260298032,-103.289964764634));
#2111=CARTESIAN_POINT('Ctrl Pts',(237.047517024518,13.9790666922546,-101.977126601053));
#2112=CARTESIAN_POINT('Ctrl Pts',(237.795059241159,13.9790666922546,-103.529962585016));
#2113=CARTESIAN_POINT('Ctrl Pts',(238.540594715314,13.9790666922546,-105.081946274341));
#2114=CARTESIAN_POINT('Ctrl Pts',(239.288136931955,13.9790666922546,-106.634782258304));
#2115=CARTESIAN_POINT('Ctrl Pts',(235.554895367936,13.5241184993698,-102.319470943802));
#2116=CARTESIAN_POINT('Ctrl Pts',(235.804400696704,13.5241184993698,-103.986393159711));
#2117=CARTESIAN_POINT('Ctrl Pts',(236.053388290024,13.5241184993698,-105.652548727892));
#2118=CARTESIAN_POINT('Ctrl Pts',(236.302893618791,13.5241184993698,-107.319470943802));
#2119=CARTESIAN_POINT('Ctrl Pts',(234.806897117082,13.2966444029273,-102.319470943802));
#2120=CARTESIAN_POINT('Ctrl Pts',(234.806897117082,13.2966444029273,-103.986137610468));
#2121=CARTESIAN_POINT('Ctrl Pts',(234.806897117082,13.2966444029273,-105.652804277135));
#2122=CARTESIAN_POINT('Ctrl Pts',(234.806897117082,13.2966444029273,-107.319470943802));
#2123=CARTESIAN_POINT('',(234.806897117082,13.2966444029273,-104.519470943802));
#2124=CARTESIAN_POINT('',(234.806897117082,13.2966444029273,-102.319470943802));
#2125=CARTESIAN_POINT('',(234.806897117082,13.2966444029273,-102.319470943802));
#2126=CARTESIAN_POINT('Ctrl Pts',(244.806897117082,15.6851224155729,-97.3194709438014));
#2127=CARTESIAN_POINT('Ctrl Pts',(244.806897117082,15.3439112709093,-99.5634656963656));
#2128=CARTESIAN_POINT('Ctrl Pts',(244.019731998933,15.0027001262456,-101.799365137326));
#2129=CARTESIAN_POINT('Ctrl Pts',(242.62311266899,14.661488981582,-103.553820840655));
#2130=CARTESIAN_POINT('Ctrl Pts',(241.692033115695,14.4340148851395,-104.723457976207));
#2131=CARTESIAN_POINT('Ctrl Pts',(240.490085023825,14.2065407886971,-105.679120117255));
#2132=CARTESIAN_POINT('Ctrl Pts',(239.142800149599,13.9790666922546,-106.328123359154));
#2133=CARTESIAN_POINT('Ctrl Pts',(237.795515275373,13.7515925958122,-106.977126601053));
#2134=CARTESIAN_POINT('Ctrl Pts',(236.302893618791,13.5241184993698,-107.319470943802));
#2135=CARTESIAN_POINT('Ctrl Pts',(234.806897117082,13.2966444029273,-107.319470943802));
#2136=CARTESIAN_POINT('Origin',(104.9,50.5,0.));
#2137=CARTESIAN_POINT('',(99.65,50.5,0.));
#2138=CARTESIAN_POINT('',(99.65,50.5,0.));
#2139=CARTESIAN_POINT('Origin',(104.9,50.5,0.));
#2140=CARTESIAN_POINT('Origin',(214.461763893543,27.,-102.319470943802));
#2141=CARTESIAN_POINT('',(206.961763893543,27.,-102.319470943802));
#2142=CARTESIAN_POINT('Origin',(75.1548667764616,46.5,-83.8451332235384));
#2143=CARTESIAN_POINT('',(75.1548667764616,49.5,-80.8451332235384));
#2144=CARTESIAN_POINT('',(75.1548667764616,46.5,-83.8451332235384));
#2145=CARTESIAN_POINT('Origin',(75.1548667764616,2.99999999999997,-83.8451332235384));
#2146=CARTESIAN_POINT('',(75.1548667764616,2.99999999999997,-83.8451332235384));
#2147=CARTESIAN_POINT('Origin',(75.1548667764616,-2.56398675875697E-14,
-80.8451332235384));
#2148=CARTESIAN_POINT('',(75.1548667764616,-2.56398675875697E-14,-80.8451332235384));
#2149=CARTESIAN_POINT('Origin',(75.1548667764616,-1.77970845607836E-14,
-57.4451332235384));
#2150=CARTESIAN_POINT('',(75.1548667764616,-1.77970845607836E-14,-57.4451332235384));
#2151=CARTESIAN_POINT('Origin',(75.1548667764616,27.9283628290596,-34.0104542742892));
#2152=CARTESIAN_POINT('',(75.1548667764616,27.9283628290596,-34.0104542742892));
#2153=CARTESIAN_POINT('Origin',(75.1548667764616,26.,-31.7123209449323));
#2154=CARTESIAN_POINT('Origin',(130.515519307328,29.,-3.3086939364114));
#2155=CARTESIAN_POINT('Origin',(168.3595982278,26.,-45.3388015976712));
#2156=CARTESIAN_POINT('',(169.649924816888,28.2981333293569,-46.7718544557298));
#2157=CARTESIAN_POINT('Origin',(185.039005179312,9.,-63.863159697949));
#2158=CARTESIAN_POINT('Origin',(179.016630670005,9.,-138.865453564781));
#2159=CARTESIAN_POINT('Origin',(214.25287973806,11.0000000000007,-102.319470943802));
#2160=CARTESIAN_POINT('',(214.666140143525,9.04316177539494,-102.319470943802));
#2161=CARTESIAN_POINT('Origin',(251.739009854135,16.8725004379826,-102.319470943801));
#2162=CARTESIAN_POINT('Origin',(239.806897117082,18.8293386625878,-92.1103521651359));
#2163=CARTESIAN_POINT('',(239.806897117082,17.0752810740154,-91.149476535321));
#2164=CARTESIAN_POINT('Origin',(239.806897117082,18.7018077557912,-88.1802874117154));
#2165=CARTESIAN_POINT('',(239.806897117082,18.7018077557912,-88.1802874117154));
#2166=CARTESIAN_POINT('Origin',(239.806897117082,20.4558653443632,-89.1411630415298));
#2167=CARTESIAN_POINT('',(239.806897117082,20.4558653443634,-87.1411630415302));
#2168=CARTESIAN_POINT('Origin',(239.806897117082,31.8788860139263,-87.1411630415302));
#2169=CARTESIAN_POINT('',(239.806897117082,31.8788860139263,-87.1411630415302));
#2170=CARTESIAN_POINT('Origin',(239.806897117082,31.8788860139263,-77.1411630415302));
#2171=CARTESIAN_POINT('',(239.806897117081,41.8788860139263,-77.1411630415302));
#2172=CARTESIAN_POINT('Origin',(239.806897117082,41.8788860139263,-75.8743377202632));
#2173=CARTESIAN_POINT('',(239.806897117082,41.8788860139263,-75.8743377202632));
#2174=CARTESIAN_POINT('Origin',(239.806897117082,43.8788860139264,-75.8743377202634));
#2175=CARTESIAN_POINT('',(239.806897117082,43.8788860139263,-73.8743377202632));
#2176=CARTESIAN_POINT('Origin',(239.806897117082,47.5,-73.8743377202632));
#2177=CARTESIAN_POINT('',(239.806897117082,47.5,-73.8743377202632));
#2178=CARTESIAN_POINT('Origin',(239.806897117082,47.4999999999999,-75.8743377202634));
#2179=CARTESIAN_POINT('',(239.806897117082,49.5,-75.8743377202632));
#2180=CARTESIAN_POINT('Origin',(239.806897117082,49.5,-256.074337720263));
#2181=CARTESIAN_POINT('Origin',(147.1662128594,59.5,-21.8011625759286));
#2182=CARTESIAN_POINT('',(140.474906795811,59.5,-14.3697143211546));
#2183=CARTESIAN_POINT('Origin',(140.474906795811,79.,-14.3697143211546));
#2184=CARTESIAN_POINT('',(140.474906795811,79.,-14.3697143211546));
#2185=CARTESIAN_POINT('Origin',(138.467514976735,82.,-12.1402798447225));
#2186=CARTESIAN_POINT('Origin',(75.1548667764616,82.,-10.));
#2187=CARTESIAN_POINT('Origin',(75.1548667764616,79.,-19.8451332235384));
#2188=CARTESIAN_POINT('',(75.1548667764616,79.,-19.8451332235384));
#2189=CARTESIAN_POINT('Origin',(75.1548667764616,59.5,-19.8451332235384));
#2190=CARTESIAN_POINT('',(75.1548667764616,59.5,-19.8451332235384));
#2191=CARTESIAN_POINT('Origin',(75.1548667764616,59.5,-29.8451332235384));
#2192=CARTESIAN_POINT('',(75.1548667764616,49.5,-29.8451332235384));
#2193=CARTESIAN_POINT('Origin',(75.1548667764616,49.5,-80.8451332235384));
#2194=CARTESIAN_POINT('Origin',(168.1,41.,0.));
#2195=CARTESIAN_POINT('Origin',(234.806897117082,31.9954417046251,-97.3194709438015));
#2196=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#2200,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#2197=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#2200,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#2198=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#2196))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#2200,#2202,#2203))
REPRESENTATION_CONTEXT('','3D')
);
#2199=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#2197))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#2200,#2202,#2203))
REPRESENTATION_CONTEXT('','3D')
);
#2200=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#2201=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT($,.METRE.)
);
#2202=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#2203=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#2204=SHAPE_DEFINITION_REPRESENTATION(#2205,#2206);
#2205=PRODUCT_DEFINITION_SHAPE('',$,#2208);
#2206=SHAPE_REPRESENTATION('',(#1296),#2198);
#2207=PRODUCT_DEFINITION_CONTEXT('part definition',#2212,'design');
#2208=PRODUCT_DEFINITION('S_1074','S_1074 v1',#2209,#2207);
#2209=PRODUCT_DEFINITION_FORMATION('',$,#2214);
#2210=PRODUCT_RELATED_PRODUCT_CATEGORY('S_1074 v1','S_1074 v1',(#2214));
#2211=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2009,#2212);
#2212=APPLICATION_CONTEXT(
'Core Data for Automotive Mechanical Design Process');
#2213=PRODUCT_CONTEXT('part definition',#2212,'mechanical');
#2214=PRODUCT('S_1074','S_1074 v1',$,(#2213));
#2215=PRESENTATION_STYLE_ASSIGNMENT((#2216));
#2216=SURFACE_STYLE_USAGE(.BOTH.,#2217);
#2217=SURFACE_SIDE_STYLE('',(#2218));
#2218=SURFACE_STYLE_FILL_AREA(#2219);
#2219=FILL_AREA_STYLE('Steel - Satin',(#2220));
#2220=FILL_AREA_STYLE_COLOUR('Steel - Satin',#2221);
#2221=COLOUR_RGB('Steel - Satin',0.627450980392157,0.627450980392157,0.627450980392157);
ENDSEC;
END-ISO-10303-21;
