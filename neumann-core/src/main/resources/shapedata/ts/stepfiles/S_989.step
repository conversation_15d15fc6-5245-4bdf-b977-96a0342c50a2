ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ (''),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 
'G:/Shared drives/TIP/Validation/SMF KION/Tset SMF Shapes/S_989.step',
/* time_stamp */ '2021-10-04T17:01:07+02:00',
/* author */ (''),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v18.1',
/* originating_system */ 'Autodesk Translation Framework v10.10.0.1391',

/* authorisation */ '');

FILE_SCHEMA (('AUTOMOTIVE_DESIGN { 1 0 10303 214 3 1 1 }'));
ENDSEC;

DATA;
#10=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#13),#758);
#11=SHAPE_REPRESENTATION_RELATIONSHIP('SRR','None',#765,#12);
#12=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#14),#757);
#13=STYLED_ITEM('',(#774),#14);
#14=MANIFOLD_SOLID_BREP('Body1',#450);
#15=CIRCLE('',#469,10.);
#16=CIRCLE('',#470,10.);
#17=CIRCLE('',#475,9.99999999999998);
#18=CIRCLE('',#476,9.99999999999998);
#19=CIRCLE('',#479,9.99999999999994);
#20=CIRCLE('',#480,9.99999999999994);
#21=CIRCLE('',#482,124.242358392242);
#22=CIRCLE('',#483,124.242358392242);
#23=CIRCLE('',#488,10.);
#24=CIRCLE('',#489,10.);
#25=CIRCLE('',#491,112.242358392243);
#26=CIRCLE('',#492,112.242358392243);
#27=CIRCLE('',#496,10.0000000000001);
#28=CIRCLE('',#497,10.0000000000001);
#29=CYLINDRICAL_SURFACE('',#468,10.);
#30=CYLINDRICAL_SURFACE('',#474,9.99999999999998);
#31=CYLINDRICAL_SURFACE('',#478,9.99999999999994);
#32=CYLINDRICAL_SURFACE('',#481,124.242358392242);
#33=CYLINDRICAL_SURFACE('',#487,10.);
#34=CYLINDRICAL_SURFACE('',#490,112.242358392243);
#35=CYLINDRICAL_SURFACE('',#495,10.0000000000001);
#36=FACE_OUTER_BOUND('',#59,.T.);
#37=FACE_OUTER_BOUND('',#60,.T.);
#38=FACE_OUTER_BOUND('',#61,.T.);
#39=FACE_OUTER_BOUND('',#62,.T.);
#40=FACE_OUTER_BOUND('',#63,.T.);
#41=FACE_OUTER_BOUND('',#64,.T.);
#42=FACE_OUTER_BOUND('',#65,.T.);
#43=FACE_OUTER_BOUND('',#66,.T.);
#44=FACE_OUTER_BOUND('',#67,.T.);
#45=FACE_OUTER_BOUND('',#68,.T.);
#46=FACE_OUTER_BOUND('',#69,.T.);
#47=FACE_OUTER_BOUND('',#70,.T.);
#48=FACE_OUTER_BOUND('',#71,.T.);
#49=FACE_OUTER_BOUND('',#72,.T.);
#50=FACE_OUTER_BOUND('',#73,.T.);
#51=FACE_OUTER_BOUND('',#74,.T.);
#52=FACE_OUTER_BOUND('',#75,.T.);
#53=FACE_OUTER_BOUND('',#76,.T.);
#54=FACE_OUTER_BOUND('',#77,.T.);
#55=FACE_OUTER_BOUND('',#78,.T.);
#56=FACE_OUTER_BOUND('',#79,.T.);
#57=FACE_OUTER_BOUND('',#80,.T.);
#58=FACE_OUTER_BOUND('',#81,.T.);
#59=EDGE_LOOP('',(#285,#286,#287,#288));
#60=EDGE_LOOP('',(#289,#290,#291,#292));
#61=EDGE_LOOP('',(#293,#294,#295,#296));
#62=EDGE_LOOP('',(#297,#298,#299,#300));
#63=EDGE_LOOP('',(#301,#302,#303,#304));
#64=EDGE_LOOP('',(#305,#306,#307,#308));
#65=EDGE_LOOP('',(#309,#310,#311,#312));
#66=EDGE_LOOP('',(#313,#314,#315,#316));
#67=EDGE_LOOP('',(#317,#318,#319,#320));
#68=EDGE_LOOP('',(#321,#322,#323,#324));
#69=EDGE_LOOP('',(#325,#326,#327,#328));
#70=EDGE_LOOP('',(#329,#330,#331,#332));
#71=EDGE_LOOP('',(#333,#334,#335,#336));
#72=EDGE_LOOP('',(#337,#338,#339,#340));
#73=EDGE_LOOP('',(#341,#342,#343,#344));
#74=EDGE_LOOP('',(#345,#346,#347,#348));
#75=EDGE_LOOP('',(#349,#350,#351,#352));
#76=EDGE_LOOP('',(#353,#354,#355,#356));
#77=EDGE_LOOP('',(#357,#358,#359,#360));
#78=EDGE_LOOP('',(#361,#362,#363,#364));
#79=EDGE_LOOP('',(#365,#366,#367,#368));
#80=EDGE_LOOP('',(#369,#370,#371,#372,#373,#374,#375,#376,#377,#378,#379,
#380,#381,#382,#383,#384,#385,#386,#387,#388,#389));
#81=EDGE_LOOP('',(#390,#391,#392,#393,#394,#395,#396,#397,#398,#399,#400,
#401,#402,#403,#404,#405,#406,#407,#408,#409,#410));
#82=LINE('',#630,#131);
#83=LINE('',#632,#132);
#84=LINE('',#634,#133);
#85=LINE('',#635,#134);
#86=LINE('',#638,#135);
#87=LINE('',#640,#136);
#88=LINE('',#641,#137);
#89=LINE('',#644,#138);
#90=LINE('',#646,#139);
#91=LINE('',#647,#140);
#92=LINE('',#650,#141);
#93=LINE('',#652,#142);
#94=LINE('',#653,#143);
#95=LINE('',#659,#144);
#96=LINE('',#662,#145);
#97=LINE('',#664,#146);
#98=LINE('',#665,#147);
#99=LINE('',#668,#148);
#100=LINE('',#670,#149);
#101=LINE('',#671,#150);
#102=LINE('',#674,#151);
#103=LINE('',#676,#152);
#104=LINE('',#677,#153);
#105=LINE('',#683,#154);
#106=LINE('',#686,#155);
#107=LINE('',#688,#156);
#108=LINE('',#689,#157);
#109=LINE('',#695,#158);
#110=LINE('',#701,#159);
#111=LINE('',#704,#160);
#112=LINE('',#706,#161);
#113=LINE('',#707,#162);
#114=LINE('',#710,#163);
#115=LINE('',#712,#164);
#116=LINE('',#713,#165);
#117=LINE('',#716,#166);
#118=LINE('',#718,#167);
#119=LINE('',#719,#168);
#120=LINE('',#725,#169);
#121=LINE('',#731,#170);
#122=LINE('',#734,#171);
#123=LINE('',#736,#172);
#124=LINE('',#737,#173);
#125=LINE('',#740,#174);
#126=LINE('',#742,#175);
#127=LINE('',#743,#176);
#128=LINE('',#749,#177);
#129=LINE('',#751,#178);
#130=LINE('',#752,#179);
#131=VECTOR('',#505,10.);
#132=VECTOR('',#506,10.);
#133=VECTOR('',#507,10.);
#134=VECTOR('',#508,10.);
#135=VECTOR('',#511,10.);
#136=VECTOR('',#512,10.);
#137=VECTOR('',#513,10.);
#138=VECTOR('',#516,10.);
#139=VECTOR('',#517,10.);
#140=VECTOR('',#518,10.);
#141=VECTOR('',#521,10.);
#142=VECTOR('',#522,10.);
#143=VECTOR('',#523,10.);
#144=VECTOR('',#530,10.);
#145=VECTOR('',#533,10.);
#146=VECTOR('',#534,10.);
#147=VECTOR('',#535,10.);
#148=VECTOR('',#538,10.);
#149=VECTOR('',#539,10.);
#150=VECTOR('',#540,10.);
#151=VECTOR('',#543,10.);
#152=VECTOR('',#544,10.);
#153=VECTOR('',#545,10.);
#154=VECTOR('',#552,10.);
#155=VECTOR('',#555,10.);
#156=VECTOR('',#556,10.);
#157=VECTOR('',#557,10.);
#158=VECTOR('',#564,10.);
#159=VECTOR('',#571,10.);
#160=VECTOR('',#574,10.);
#161=VECTOR('',#575,10.);
#162=VECTOR('',#576,10.);
#163=VECTOR('',#579,10.);
#164=VECTOR('',#580,10.);
#165=VECTOR('',#581,10.);
#166=VECTOR('',#584,10.);
#167=VECTOR('',#585,10.);
#168=VECTOR('',#586,10.);
#169=VECTOR('',#593,10.);
#170=VECTOR('',#600,10.);
#171=VECTOR('',#603,10.);
#172=VECTOR('',#604,10.);
#173=VECTOR('',#605,10.);
#174=VECTOR('',#608,10.);
#175=VECTOR('',#609,10.);
#176=VECTOR('',#610,10.);
#177=VECTOR('',#617,10.);
#178=VECTOR('',#620,10.);
#179=VECTOR('',#621,10.);
#180=VERTEX_POINT('',#628);
#181=VERTEX_POINT('',#629);
#182=VERTEX_POINT('',#631);
#183=VERTEX_POINT('',#633);
#184=VERTEX_POINT('',#637);
#185=VERTEX_POINT('',#639);
#186=VERTEX_POINT('',#643);
#187=VERTEX_POINT('',#645);
#188=VERTEX_POINT('',#649);
#189=VERTEX_POINT('',#651);
#190=VERTEX_POINT('',#655);
#191=VERTEX_POINT('',#657);
#192=VERTEX_POINT('',#661);
#193=VERTEX_POINT('',#663);
#194=VERTEX_POINT('',#667);
#195=VERTEX_POINT('',#669);
#196=VERTEX_POINT('',#673);
#197=VERTEX_POINT('',#675);
#198=VERTEX_POINT('',#679);
#199=VERTEX_POINT('',#681);
#200=VERTEX_POINT('',#685);
#201=VERTEX_POINT('',#687);
#202=VERTEX_POINT('',#691);
#203=VERTEX_POINT('',#693);
#204=VERTEX_POINT('',#697);
#205=VERTEX_POINT('',#699);
#206=VERTEX_POINT('',#703);
#207=VERTEX_POINT('',#705);
#208=VERTEX_POINT('',#709);
#209=VERTEX_POINT('',#711);
#210=VERTEX_POINT('',#715);
#211=VERTEX_POINT('',#717);
#212=VERTEX_POINT('',#721);
#213=VERTEX_POINT('',#723);
#214=VERTEX_POINT('',#727);
#215=VERTEX_POINT('',#729);
#216=VERTEX_POINT('',#733);
#217=VERTEX_POINT('',#735);
#218=VERTEX_POINT('',#739);
#219=VERTEX_POINT('',#741);
#220=VERTEX_POINT('',#745);
#221=VERTEX_POINT('',#747);
#222=EDGE_CURVE('',#180,#181,#82,.T.);
#223=EDGE_CURVE('',#180,#182,#83,.T.);
#224=EDGE_CURVE('',#183,#182,#84,.T.);
#225=EDGE_CURVE('',#181,#183,#85,.T.);
#226=EDGE_CURVE('',#184,#181,#86,.T.);
#227=EDGE_CURVE('',#185,#183,#87,.T.);
#228=EDGE_CURVE('',#184,#185,#88,.T.);
#229=EDGE_CURVE('',#186,#184,#89,.T.);
#230=EDGE_CURVE('',#187,#185,#90,.T.);
#231=EDGE_CURVE('',#186,#187,#91,.T.);
#232=EDGE_CURVE('',#188,#186,#92,.T.);
#233=EDGE_CURVE('',#189,#187,#93,.T.);
#234=EDGE_CURVE('',#188,#189,#94,.T.);
#235=EDGE_CURVE('',#190,#188,#15,.T.);
#236=EDGE_CURVE('',#191,#189,#16,.T.);
#237=EDGE_CURVE('',#190,#191,#95,.T.);
#238=EDGE_CURVE('',#192,#190,#96,.T.);
#239=EDGE_CURVE('',#193,#191,#97,.T.);
#240=EDGE_CURVE('',#192,#193,#98,.T.);
#241=EDGE_CURVE('',#194,#192,#99,.T.);
#242=EDGE_CURVE('',#195,#193,#100,.T.);
#243=EDGE_CURVE('',#194,#195,#101,.T.);
#244=EDGE_CURVE('',#196,#194,#102,.T.);
#245=EDGE_CURVE('',#197,#195,#103,.T.);
#246=EDGE_CURVE('',#196,#197,#104,.T.);
#247=EDGE_CURVE('',#198,#196,#17,.T.);
#248=EDGE_CURVE('',#199,#197,#18,.T.);
#249=EDGE_CURVE('',#198,#199,#105,.T.);
#250=EDGE_CURVE('',#200,#198,#106,.T.);
#251=EDGE_CURVE('',#201,#199,#107,.T.);
#252=EDGE_CURVE('',#200,#201,#108,.T.);
#253=EDGE_CURVE('',#202,#200,#19,.T.);
#254=EDGE_CURVE('',#203,#201,#20,.T.);
#255=EDGE_CURVE('',#202,#203,#109,.T.);
#256=EDGE_CURVE('',#204,#202,#21,.T.);
#257=EDGE_CURVE('',#205,#203,#22,.T.);
#258=EDGE_CURVE('',#204,#205,#110,.T.);
#259=EDGE_CURVE('',#206,#204,#111,.T.);
#260=EDGE_CURVE('',#207,#205,#112,.T.);
#261=EDGE_CURVE('',#206,#207,#113,.T.);
#262=EDGE_CURVE('',#206,#208,#114,.T.);
#263=EDGE_CURVE('',#209,#207,#115,.T.);
#264=EDGE_CURVE('',#208,#209,#116,.T.);
#265=EDGE_CURVE('',#208,#210,#117,.T.);
#266=EDGE_CURVE('',#211,#209,#118,.T.);
#267=EDGE_CURVE('',#210,#211,#119,.T.);
#268=EDGE_CURVE('',#212,#210,#23,.T.);
#269=EDGE_CURVE('',#213,#211,#24,.T.);
#270=EDGE_CURVE('',#212,#213,#120,.T.);
#271=EDGE_CURVE('',#212,#214,#25,.T.);
#272=EDGE_CURVE('',#215,#213,#26,.T.);
#273=EDGE_CURVE('',#214,#215,#121,.T.);
#274=EDGE_CURVE('',#214,#216,#122,.T.);
#275=EDGE_CURVE('',#217,#215,#123,.T.);
#276=EDGE_CURVE('',#216,#217,#124,.T.);
#277=EDGE_CURVE('',#216,#218,#125,.T.);
#278=EDGE_CURVE('',#219,#217,#126,.T.);
#279=EDGE_CURVE('',#218,#219,#127,.T.);
#280=EDGE_CURVE('',#220,#218,#27,.T.);
#281=EDGE_CURVE('',#221,#219,#28,.T.);
#282=EDGE_CURVE('',#220,#221,#128,.T.);
#283=EDGE_CURVE('',#220,#180,#129,.T.);
#284=EDGE_CURVE('',#182,#221,#130,.T.);
#285=ORIENTED_EDGE('',*,*,#222,.F.);
#286=ORIENTED_EDGE('',*,*,#223,.T.);
#287=ORIENTED_EDGE('',*,*,#224,.F.);
#288=ORIENTED_EDGE('',*,*,#225,.F.);
#289=ORIENTED_EDGE('',*,*,#226,.T.);
#290=ORIENTED_EDGE('',*,*,#225,.T.);
#291=ORIENTED_EDGE('',*,*,#227,.F.);
#292=ORIENTED_EDGE('',*,*,#228,.F.);
#293=ORIENTED_EDGE('',*,*,#229,.T.);
#294=ORIENTED_EDGE('',*,*,#228,.T.);
#295=ORIENTED_EDGE('',*,*,#230,.F.);
#296=ORIENTED_EDGE('',*,*,#231,.F.);
#297=ORIENTED_EDGE('',*,*,#232,.T.);
#298=ORIENTED_EDGE('',*,*,#231,.T.);
#299=ORIENTED_EDGE('',*,*,#233,.F.);
#300=ORIENTED_EDGE('',*,*,#234,.F.);
#301=ORIENTED_EDGE('',*,*,#235,.T.);
#302=ORIENTED_EDGE('',*,*,#234,.T.);
#303=ORIENTED_EDGE('',*,*,#236,.F.);
#304=ORIENTED_EDGE('',*,*,#237,.F.);
#305=ORIENTED_EDGE('',*,*,#238,.T.);
#306=ORIENTED_EDGE('',*,*,#237,.T.);
#307=ORIENTED_EDGE('',*,*,#239,.F.);
#308=ORIENTED_EDGE('',*,*,#240,.F.);
#309=ORIENTED_EDGE('',*,*,#241,.T.);
#310=ORIENTED_EDGE('',*,*,#240,.T.);
#311=ORIENTED_EDGE('',*,*,#242,.F.);
#312=ORIENTED_EDGE('',*,*,#243,.F.);
#313=ORIENTED_EDGE('',*,*,#244,.T.);
#314=ORIENTED_EDGE('',*,*,#243,.T.);
#315=ORIENTED_EDGE('',*,*,#245,.F.);
#316=ORIENTED_EDGE('',*,*,#246,.F.);
#317=ORIENTED_EDGE('',*,*,#247,.T.);
#318=ORIENTED_EDGE('',*,*,#246,.T.);
#319=ORIENTED_EDGE('',*,*,#248,.F.);
#320=ORIENTED_EDGE('',*,*,#249,.F.);
#321=ORIENTED_EDGE('',*,*,#250,.T.);
#322=ORIENTED_EDGE('',*,*,#249,.T.);
#323=ORIENTED_EDGE('',*,*,#251,.F.);
#324=ORIENTED_EDGE('',*,*,#252,.F.);
#325=ORIENTED_EDGE('',*,*,#253,.T.);
#326=ORIENTED_EDGE('',*,*,#252,.T.);
#327=ORIENTED_EDGE('',*,*,#254,.F.);
#328=ORIENTED_EDGE('',*,*,#255,.F.);
#329=ORIENTED_EDGE('',*,*,#256,.T.);
#330=ORIENTED_EDGE('',*,*,#255,.T.);
#331=ORIENTED_EDGE('',*,*,#257,.F.);
#332=ORIENTED_EDGE('',*,*,#258,.F.);
#333=ORIENTED_EDGE('',*,*,#259,.T.);
#334=ORIENTED_EDGE('',*,*,#258,.T.);
#335=ORIENTED_EDGE('',*,*,#260,.F.);
#336=ORIENTED_EDGE('',*,*,#261,.F.);
#337=ORIENTED_EDGE('',*,*,#262,.F.);
#338=ORIENTED_EDGE('',*,*,#261,.T.);
#339=ORIENTED_EDGE('',*,*,#263,.F.);
#340=ORIENTED_EDGE('',*,*,#264,.F.);
#341=ORIENTED_EDGE('',*,*,#265,.F.);
#342=ORIENTED_EDGE('',*,*,#264,.T.);
#343=ORIENTED_EDGE('',*,*,#266,.F.);
#344=ORIENTED_EDGE('',*,*,#267,.F.);
#345=ORIENTED_EDGE('',*,*,#268,.T.);
#346=ORIENTED_EDGE('',*,*,#267,.T.);
#347=ORIENTED_EDGE('',*,*,#269,.F.);
#348=ORIENTED_EDGE('',*,*,#270,.F.);
#349=ORIENTED_EDGE('',*,*,#271,.F.);
#350=ORIENTED_EDGE('',*,*,#270,.T.);
#351=ORIENTED_EDGE('',*,*,#272,.F.);
#352=ORIENTED_EDGE('',*,*,#273,.F.);
#353=ORIENTED_EDGE('',*,*,#274,.F.);
#354=ORIENTED_EDGE('',*,*,#273,.T.);
#355=ORIENTED_EDGE('',*,*,#275,.F.);
#356=ORIENTED_EDGE('',*,*,#276,.F.);
#357=ORIENTED_EDGE('',*,*,#277,.F.);
#358=ORIENTED_EDGE('',*,*,#276,.T.);
#359=ORIENTED_EDGE('',*,*,#278,.F.);
#360=ORIENTED_EDGE('',*,*,#279,.F.);
#361=ORIENTED_EDGE('',*,*,#280,.T.);
#362=ORIENTED_EDGE('',*,*,#279,.T.);
#363=ORIENTED_EDGE('',*,*,#281,.F.);
#364=ORIENTED_EDGE('',*,*,#282,.F.);
#365=ORIENTED_EDGE('',*,*,#283,.F.);
#366=ORIENTED_EDGE('',*,*,#282,.T.);
#367=ORIENTED_EDGE('',*,*,#284,.F.);
#368=ORIENTED_EDGE('',*,*,#223,.F.);
#369=ORIENTED_EDGE('',*,*,#284,.T.);
#370=ORIENTED_EDGE('',*,*,#281,.T.);
#371=ORIENTED_EDGE('',*,*,#278,.T.);
#372=ORIENTED_EDGE('',*,*,#275,.T.);
#373=ORIENTED_EDGE('',*,*,#272,.T.);
#374=ORIENTED_EDGE('',*,*,#269,.T.);
#375=ORIENTED_EDGE('',*,*,#266,.T.);
#376=ORIENTED_EDGE('',*,*,#263,.T.);
#377=ORIENTED_EDGE('',*,*,#260,.T.);
#378=ORIENTED_EDGE('',*,*,#257,.T.);
#379=ORIENTED_EDGE('',*,*,#254,.T.);
#380=ORIENTED_EDGE('',*,*,#251,.T.);
#381=ORIENTED_EDGE('',*,*,#248,.T.);
#382=ORIENTED_EDGE('',*,*,#245,.T.);
#383=ORIENTED_EDGE('',*,*,#242,.T.);
#384=ORIENTED_EDGE('',*,*,#239,.T.);
#385=ORIENTED_EDGE('',*,*,#236,.T.);
#386=ORIENTED_EDGE('',*,*,#233,.T.);
#387=ORIENTED_EDGE('',*,*,#230,.T.);
#388=ORIENTED_EDGE('',*,*,#227,.T.);
#389=ORIENTED_EDGE('',*,*,#224,.T.);
#390=ORIENTED_EDGE('',*,*,#283,.T.);
#391=ORIENTED_EDGE('',*,*,#222,.T.);
#392=ORIENTED_EDGE('',*,*,#226,.F.);
#393=ORIENTED_EDGE('',*,*,#229,.F.);
#394=ORIENTED_EDGE('',*,*,#232,.F.);
#395=ORIENTED_EDGE('',*,*,#235,.F.);
#396=ORIENTED_EDGE('',*,*,#238,.F.);
#397=ORIENTED_EDGE('',*,*,#241,.F.);
#398=ORIENTED_EDGE('',*,*,#244,.F.);
#399=ORIENTED_EDGE('',*,*,#247,.F.);
#400=ORIENTED_EDGE('',*,*,#250,.F.);
#401=ORIENTED_EDGE('',*,*,#253,.F.);
#402=ORIENTED_EDGE('',*,*,#256,.F.);
#403=ORIENTED_EDGE('',*,*,#259,.F.);
#404=ORIENTED_EDGE('',*,*,#262,.T.);
#405=ORIENTED_EDGE('',*,*,#265,.T.);
#406=ORIENTED_EDGE('',*,*,#268,.F.);
#407=ORIENTED_EDGE('',*,*,#271,.T.);
#408=ORIENTED_EDGE('',*,*,#274,.T.);
#409=ORIENTED_EDGE('',*,*,#277,.T.);
#410=ORIENTED_EDGE('',*,*,#280,.F.);
#411=PLANE('',#464);
#412=PLANE('',#465);
#413=PLANE('',#466);
#414=PLANE('',#467);
#415=PLANE('',#471);
#416=PLANE('',#472);
#417=PLANE('',#473);
#418=PLANE('',#477);
#419=PLANE('',#484);
#420=PLANE('',#485);
#421=PLANE('',#486);
#422=PLANE('',#493);
#423=PLANE('',#494);
#424=PLANE('',#498);
#425=PLANE('',#499);
#426=PLANE('',#500);
#427=ADVANCED_FACE('',(#36),#411,.T.);
#428=ADVANCED_FACE('',(#37),#412,.T.);
#429=ADVANCED_FACE('',(#38),#413,.T.);
#430=ADVANCED_FACE('',(#39),#414,.T.);
#431=ADVANCED_FACE('',(#40),#29,.T.);
#432=ADVANCED_FACE('',(#41),#415,.T.);
#433=ADVANCED_FACE('',(#42),#416,.T.);
#434=ADVANCED_FACE('',(#43),#417,.T.);
#435=ADVANCED_FACE('',(#44),#30,.T.);
#436=ADVANCED_FACE('',(#45),#418,.T.);
#437=ADVANCED_FACE('',(#46),#31,.T.);
#438=ADVANCED_FACE('',(#47),#32,.T.);
#439=ADVANCED_FACE('',(#48),#419,.T.);
#440=ADVANCED_FACE('',(#49),#420,.T.);
#441=ADVANCED_FACE('',(#50),#421,.T.);
#442=ADVANCED_FACE('',(#51),#33,.T.);
#443=ADVANCED_FACE('',(#52),#34,.F.);
#444=ADVANCED_FACE('',(#53),#422,.T.);
#445=ADVANCED_FACE('',(#54),#423,.T.);
#446=ADVANCED_FACE('',(#55),#35,.T.);
#447=ADVANCED_FACE('',(#56),#424,.T.);
#448=ADVANCED_FACE('',(#57),#425,.T.);
#449=ADVANCED_FACE('',(#58),#426,.F.);
#450=CLOSED_SHELL('',(#427,#428,#429,#430,#431,#432,#433,#434,#435,#436,
#437,#438,#439,#440,#441,#442,#443,#444,#445,#446,#447,#448,#449));
#451=DERIVED_UNIT_ELEMENT(#453,1.);
#452=DERIVED_UNIT_ELEMENT(#760,-3.);
#453=(
MASS_UNIT()
NAMED_UNIT(*)
SI_UNIT(.KILO.,.GRAM.)
);
#454=DERIVED_UNIT((#451,#452));
#455=MEASURE_REPRESENTATION_ITEM('density measure',
POSITIVE_RATIO_MEASURE(7850.),#454);
#456=PROPERTY_DEFINITION_REPRESENTATION(#461,#458);
#457=PROPERTY_DEFINITION_REPRESENTATION(#462,#459);
#458=REPRESENTATION('material name',(#460),#757);
#459=REPRESENTATION('density',(#455),#757);
#460=DESCRIPTIVE_REPRESENTATION_ITEM('Steel','Steel');
#461=PROPERTY_DEFINITION('material property','material name',#767);
#462=PROPERTY_DEFINITION('material property','density of part',#767);
#463=AXIS2_PLACEMENT_3D('placement',#626,#501,#502);
#464=AXIS2_PLACEMENT_3D('',#627,#503,#504);
#465=AXIS2_PLACEMENT_3D('',#636,#509,#510);
#466=AXIS2_PLACEMENT_3D('',#642,#514,#515);
#467=AXIS2_PLACEMENT_3D('',#648,#519,#520);
#468=AXIS2_PLACEMENT_3D('',#654,#524,#525);
#469=AXIS2_PLACEMENT_3D('',#656,#526,#527);
#470=AXIS2_PLACEMENT_3D('',#658,#528,#529);
#471=AXIS2_PLACEMENT_3D('',#660,#531,#532);
#472=AXIS2_PLACEMENT_3D('',#666,#536,#537);
#473=AXIS2_PLACEMENT_3D('',#672,#541,#542);
#474=AXIS2_PLACEMENT_3D('',#678,#546,#547);
#475=AXIS2_PLACEMENT_3D('',#680,#548,#549);
#476=AXIS2_PLACEMENT_3D('',#682,#550,#551);
#477=AXIS2_PLACEMENT_3D('',#684,#553,#554);
#478=AXIS2_PLACEMENT_3D('',#690,#558,#559);
#479=AXIS2_PLACEMENT_3D('',#692,#560,#561);
#480=AXIS2_PLACEMENT_3D('',#694,#562,#563);
#481=AXIS2_PLACEMENT_3D('',#696,#565,#566);
#482=AXIS2_PLACEMENT_3D('',#698,#567,#568);
#483=AXIS2_PLACEMENT_3D('',#700,#569,#570);
#484=AXIS2_PLACEMENT_3D('',#702,#572,#573);
#485=AXIS2_PLACEMENT_3D('',#708,#577,#578);
#486=AXIS2_PLACEMENT_3D('',#714,#582,#583);
#487=AXIS2_PLACEMENT_3D('',#720,#587,#588);
#488=AXIS2_PLACEMENT_3D('',#722,#589,#590);
#489=AXIS2_PLACEMENT_3D('',#724,#591,#592);
#490=AXIS2_PLACEMENT_3D('',#726,#594,#595);
#491=AXIS2_PLACEMENT_3D('',#728,#596,#597);
#492=AXIS2_PLACEMENT_3D('',#730,#598,#599);
#493=AXIS2_PLACEMENT_3D('',#732,#601,#602);
#494=AXIS2_PLACEMENT_3D('',#738,#606,#607);
#495=AXIS2_PLACEMENT_3D('',#744,#611,#612);
#496=AXIS2_PLACEMENT_3D('',#746,#613,#614);
#497=AXIS2_PLACEMENT_3D('',#748,#615,#616);
#498=AXIS2_PLACEMENT_3D('',#750,#618,#619);
#499=AXIS2_PLACEMENT_3D('',#753,#622,#623);
#500=AXIS2_PLACEMENT_3D('',#754,#624,#625);
#501=DIRECTION('axis',(0.,0.,1.));
#502=DIRECTION('refdir',(1.,0.,0.));
#503=DIRECTION('center_axis',(1.,0.,0.));
#504=DIRECTION('ref_axis',(0.,1.,0.));
#505=DIRECTION('',(0.,-1.,0.));
#506=DIRECTION('',(0.,0.,1.));
#507=DIRECTION('',(0.,1.,0.));
#508=DIRECTION('',(0.,0.,1.));
#509=DIRECTION('center_axis',(0.,1.,0.));
#510=DIRECTION('ref_axis',(-1.,0.,0.));
#511=DIRECTION('',(-1.,0.,0.));
#512=DIRECTION('',(-1.,0.,0.));
#513=DIRECTION('',(0.,0.,1.));
#514=DIRECTION('center_axis',(1.,0.,0.));
#515=DIRECTION('ref_axis',(0.,1.,0.));
#516=DIRECTION('',(0.,1.,0.));
#517=DIRECTION('',(0.,1.,0.));
#518=DIRECTION('',(0.,0.,1.));
#519=DIRECTION('center_axis',(2.08731135774436E-15,-1.,0.));
#520=DIRECTION('ref_axis',(1.,2.08731135774436E-15,0.));
#521=DIRECTION('',(1.,2.08731135774436E-15,0.));
#522=DIRECTION('',(1.,2.08731135774436E-15,0.));
#523=DIRECTION('',(0.,0.,1.));
#524=DIRECTION('center_axis',(0.,0.,1.));
#525=DIRECTION('ref_axis',(-1.,-4.44089209850062E-16,0.));
#526=DIRECTION('center_axis',(0.,0.,1.));
#527=DIRECTION('ref_axis',(-1.,-4.44089209850062E-16,0.));
#528=DIRECTION('center_axis',(0.,0.,1.));
#529=DIRECTION('ref_axis',(-1.,-4.44089209850062E-16,0.));
#530=DIRECTION('',(0.,0.,1.));
#531=DIRECTION('center_axis',(-1.,0.,0.));
#532=DIRECTION('ref_axis',(0.,-1.,0.));
#533=DIRECTION('',(0.,-1.,0.));
#534=DIRECTION('',(0.,-1.,0.));
#535=DIRECTION('',(0.,0.,1.));
#536=DIRECTION('center_axis',(0.173648177666931,-0.984807753012208,0.));
#537=DIRECTION('ref_axis',(0.984807753012208,0.173648177666931,0.));
#538=DIRECTION('',(0.984807753012208,0.173648177666931,0.));
#539=DIRECTION('',(0.984807753012208,0.173648177666931,0.));
#540=DIRECTION('',(0.,0.,1.));
#541=DIRECTION('center_axis',(0.939692620785909,-0.342020143325668,0.));
#542=DIRECTION('ref_axis',(0.342020143325668,0.939692620785909,0.));
#543=DIRECTION('',(0.342020143325668,0.939692620785909,0.));
#544=DIRECTION('',(0.342020143325668,0.939692620785909,0.));
#545=DIRECTION('',(0.,0.,1.));
#546=DIRECTION('center_axis',(0.,0.,1.));
#547=DIRECTION('ref_axis',(-1.77635683940025E-15,-1.,0.));
#548=DIRECTION('center_axis',(0.,0.,1.));
#549=DIRECTION('ref_axis',(-1.77635683940025E-15,-1.,0.));
#550=DIRECTION('center_axis',(0.,0.,1.));
#551=DIRECTION('ref_axis',(-1.77635683940025E-15,-1.,0.));
#552=DIRECTION('',(0.,0.,1.));
#553=DIRECTION('center_axis',(0.,-1.,0.));
#554=DIRECTION('ref_axis',(1.,0.,0.));
#555=DIRECTION('',(1.,0.,0.));
#556=DIRECTION('',(1.,0.,0.));
#557=DIRECTION('',(0.,0.,1.));
#558=DIRECTION('center_axis',(0.,0.,1.));
#559=DIRECTION('ref_axis',(-0.772819221220048,-0.634626229613021,0.));
#560=DIRECTION('center_axis',(0.,0.,1.));
#561=DIRECTION('ref_axis',(-0.772819221220048,-0.634626229613021,0.));
#562=DIRECTION('center_axis',(0.,0.,1.));
#563=DIRECTION('ref_axis',(-0.772819221220048,-0.634626229613021,0.));
#564=DIRECTION('',(0.,0.,1.));
#565=DIRECTION('center_axis',(0.,0.,1.));
#566=DIRECTION('ref_axis',(-0.948921661717106,-0.315511774620293,0.));
#567=DIRECTION('center_axis',(0.,0.,1.));
#568=DIRECTION('ref_axis',(-0.948921661717106,-0.315511774620293,0.));
#569=DIRECTION('center_axis',(0.,0.,1.));
#570=DIRECTION('ref_axis',(-0.948921661717106,-0.315511774620293,0.));
#571=DIRECTION('',(0.,0.,1.));
#572=DIRECTION('center_axis',(0.,-1.,0.));
#573=DIRECTION('ref_axis',(1.,0.,0.));
#574=DIRECTION('',(1.,0.,0.));
#575=DIRECTION('',(1.,0.,0.));
#576=DIRECTION('',(0.,0.,1.));
#577=DIRECTION('center_axis',(-1.,0.,0.));
#578=DIRECTION('ref_axis',(0.,-1.,0.));
#579=DIRECTION('',(0.,1.,0.));
#580=DIRECTION('',(0.,-1.,0.));
#581=DIRECTION('',(0.,0.,1.));
#582=DIRECTION('center_axis',(-1.07134390473892E-16,1.,0.));
#583=DIRECTION('ref_axis',(-1.,-1.07134390473892E-16,0.));
#584=DIRECTION('',(1.,1.07134390473892E-16,0.));
#585=DIRECTION('',(-1.,-1.07134390473892E-16,0.));
#586=DIRECTION('',(0.,0.,1.));
#587=DIRECTION('center_axis',(0.,0.,1.));
#588=DIRECTION('ref_axis',(0.952572126891767,0.304312903224454,0.));
#589=DIRECTION('center_axis',(0.,0.,1.));
#590=DIRECTION('ref_axis',(0.952572126891767,0.304312903224454,0.));
#591=DIRECTION('center_axis',(0.,0.,1.));
#592=DIRECTION('ref_axis',(0.952572126891767,0.304312903224454,0.));
#593=DIRECTION('',(0.,0.,1.));
#594=DIRECTION('center_axis',(0.,0.,1.));
#595=DIRECTION('ref_axis',(-0.952572126891768,-0.30431290322445,0.));
#596=DIRECTION('center_axis',(0.,0.,1.));
#597=DIRECTION('ref_axis',(-0.952572126891768,-0.30431290322445,0.));
#598=DIRECTION('center_axis',(0.,0.,-1.));
#599=DIRECTION('ref_axis',(-0.952572126891768,-0.30431290322445,0.));
#600=DIRECTION('',(0.,0.,1.));
#601=DIRECTION('center_axis',(-3.41092092567294E-16,1.,0.));
#602=DIRECTION('ref_axis',(-1.,-3.41092092567294E-16,0.));
#603=DIRECTION('',(1.,3.41092092567294E-16,0.));
#604=DIRECTION('',(-1.,-3.41092092567294E-16,0.));
#605=DIRECTION('',(0.,0.,1.));
#606=DIRECTION('center_axis',(-0.939692620785908,0.342020143325669,0.));
#607=DIRECTION('ref_axis',(-0.342020143325669,-0.939692620785908,0.));
#608=DIRECTION('',(0.342020143325669,0.939692620785908,0.));
#609=DIRECTION('',(-0.342020143325669,-0.939692620785908,0.));
#610=DIRECTION('',(0.,0.,1.));
#611=DIRECTION('center_axis',(0.,0.,1.));
#612=DIRECTION('ref_axis',(-0.173648177666933,0.984807753012208,0.));
#613=DIRECTION('center_axis',(0.,0.,1.));
#614=DIRECTION('ref_axis',(-0.173648177666933,0.984807753012208,0.));
#615=DIRECTION('center_axis',(0.,0.,1.));
#616=DIRECTION('ref_axis',(-0.173648177666933,0.984807753012208,0.));
#617=DIRECTION('',(0.,0.,1.));
#618=DIRECTION('center_axis',(-0.173648177666931,0.984807753012208,0.));
#619=DIRECTION('ref_axis',(-0.984807753012208,-0.173648177666931,0.));
#620=DIRECTION('',(0.984807753012208,0.173648177666931,0.));
#621=DIRECTION('',(-0.984807753012208,-0.173648177666931,0.));
#622=DIRECTION('center_axis',(0.,0.,1.));
#623=DIRECTION('ref_axis',(1.,0.,0.));
#624=DIRECTION('center_axis',(0.,0.,1.));
#625=DIRECTION('ref_axis',(1.,0.,0.));
#626=CARTESIAN_POINT('',(0.,0.,0.));
#627=CARTESIAN_POINT('Origin',(148.460596691819,-31.7337417605262,0.));
#628=CARTESIAN_POINT('',(148.460596691819,-5.86517022069997,0.));
#629=CARTESIAN_POINT('',(148.460596691819,-31.7337417605262,0.));
#630=CARTESIAN_POINT('',(148.460596691819,-5.86517022069997,0.));
#631=CARTESIAN_POINT('',(148.460596691819,-5.86517022069997,2.));
#632=CARTESIAN_POINT('',(148.460596691819,-5.86517022069997,0.));
#633=CARTESIAN_POINT('',(148.460596691819,-31.7337417605262,2.));
#634=CARTESIAN_POINT('',(148.460596691819,-5.86517022069997,2.));
#635=CARTESIAN_POINT('',(148.460596691819,-31.7337417605262,0.));
#636=CARTESIAN_POINT('Origin',(150.715728138331,-31.7337417605262,0.));
#637=CARTESIAN_POINT('',(150.715728138331,-31.7337417605262,0.));
#638=CARTESIAN_POINT('',(150.715728138331,-31.7337417605262,0.));
#639=CARTESIAN_POINT('',(150.715728138331,-31.7337417605262,2.));
#640=CARTESIAN_POINT('',(150.715728138331,-31.7337417605262,2.));
#641=CARTESIAN_POINT('',(150.715728138331,-31.7337417605262,0.));
#642=CARTESIAN_POINT('Origin',(150.715728138331,-43.3012701892219,0.));
#643=CARTESIAN_POINT('',(150.715728138331,-43.3012701892219,0.));
#644=CARTESIAN_POINT('',(150.715728138331,-43.3012701892219,0.));
#645=CARTESIAN_POINT('',(150.715728138331,-43.3012701892219,2.));
#646=CARTESIAN_POINT('',(150.715728138331,-43.3012701892219,2.));
#647=CARTESIAN_POINT('',(150.715728138331,-43.3012701892219,0.));
#648=CARTESIAN_POINT('Origin',(146.460596691819,-43.3012701892219,0.));
#649=CARTESIAN_POINT('',(146.460596691819,-43.3012701892219,0.));
#650=CARTESIAN_POINT('',(146.460596691819,-43.3012701892219,0.));
#651=CARTESIAN_POINT('',(146.460596691819,-43.3012701892219,2.));
#652=CARTESIAN_POINT('',(146.460596691819,-43.3012701892219,2.));
#653=CARTESIAN_POINT('',(146.460596691819,-43.3012701892219,0.));
#654=CARTESIAN_POINT('Origin',(146.460596691819,-33.3012701892219,0.));
#655=CARTESIAN_POINT('',(136.460596691819,-33.3012701892219,0.));
#656=CARTESIAN_POINT('Origin',(146.460596691819,-33.3012701892219,0.));
#657=CARTESIAN_POINT('',(136.460596691819,-33.3012701892219,2.));
#658=CARTESIAN_POINT('Origin',(146.460596691819,-33.3012701892219,2.));
#659=CARTESIAN_POINT('',(136.460596691819,-33.3012701892219,0.));
#660=CARTESIAN_POINT('Origin',(136.460596691819,-20.1662133318305,0.));
#661=CARTESIAN_POINT('',(136.460596691819,-20.1662133318305,0.));
#662=CARTESIAN_POINT('',(136.460596691819,-20.1662133318305,0.));
#663=CARTESIAN_POINT('',(136.460596691819,-20.1662133318305,2.));
#664=CARTESIAN_POINT('',(136.460596691819,-20.1662133318305,2.));
#665=CARTESIAN_POINT('',(136.460596691819,-20.1662133318305,0.));
#666=CARTESIAN_POINT('Origin',(111.840402866513,-24.5074177735038,0.));
#667=CARTESIAN_POINT('',(111.840402866513,-24.5074177735038,0.));
#668=CARTESIAN_POINT('',(111.840402866513,-24.5074177735038,0.));
#669=CARTESIAN_POINT('',(111.840402866513,-24.5074177735038,2.));
#670=CARTESIAN_POINT('',(111.840402866513,-24.5074177735038,2.));
#671=CARTESIAN_POINT('',(111.840402866513,-24.5074177735038,0.));
#672=CARTESIAN_POINT('Origin',(107.394850825762,-36.7214716224786,0.));
#673=CARTESIAN_POINT('',(107.394850825762,-36.7214716224786,0.));
#674=CARTESIAN_POINT('',(107.394850825762,-36.7214716224786,0.));
#675=CARTESIAN_POINT('',(107.394850825762,-36.7214716224786,2.));
#676=CARTESIAN_POINT('',(107.394850825762,-36.7214716224786,2.));
#677=CARTESIAN_POINT('',(107.394850825762,-36.7214716224786,0.));
#678=CARTESIAN_POINT('Origin',(97.9979246179029,-33.301270189222,0.));
#679=CARTESIAN_POINT('',(97.9979246179029,-43.3012701892219,0.));
#680=CARTESIAN_POINT('Origin',(97.9979246179029,-33.301270189222,0.));
#681=CARTESIAN_POINT('',(97.9979246179029,-43.3012701892219,2.));
#682=CARTESIAN_POINT('Origin',(97.9979246179029,-33.301270189222,2.));
#683=CARTESIAN_POINT('',(97.9979246179029,-43.3012701892219,0.));
#684=CARTESIAN_POINT('Origin',(69.607574738185,-43.3012701892219,0.));
#685=CARTESIAN_POINT('',(69.607574738185,-43.3012701892219,0.));
#686=CARTESIAN_POINT('',(69.607574738185,-43.3012701892219,0.));
#687=CARTESIAN_POINT('',(69.607574738185,-43.3012701892219,2.));
#688=CARTESIAN_POINT('',(69.607574738185,-43.3012701892219,2.));
#689=CARTESIAN_POINT('',(69.607574738185,-43.3012701892219,0.));
#690=CARTESIAN_POINT('Origin',(69.607574738185,-33.301270189222,0.));
#691=CARTESIAN_POINT('',(61.8793825259845,-39.6475324853522,0.));
#692=CARTESIAN_POINT('Origin',(69.607574738185,-33.301270189222,0.));
#693=CARTESIAN_POINT('',(61.8793825259845,-39.6475324853522,2.));
#694=CARTESIAN_POINT('Origin',(69.607574738185,-33.301270189222,2.));
#695=CARTESIAN_POINT('',(61.8793825259845,-39.6475324853522,0.));
#696=CARTESIAN_POINT('Origin',(157.896265181219,39.1999269793467,0.));
#697=CARTESIAN_POINT('',(40.,0.,0.));
#698=CARTESIAN_POINT('Origin',(157.896265181219,39.1999269793467,0.));
#699=CARTESIAN_POINT('',(40.,0.,2.));
#700=CARTESIAN_POINT('Origin',(157.896265181219,39.1999269793467,2.));
#701=CARTESIAN_POINT('',(40.,0.,0.));
#702=CARTESIAN_POINT('Origin',(0.,0.,0.));
#703=CARTESIAN_POINT('',(0.,0.,0.));
#704=CARTESIAN_POINT('',(0.,0.,0.));
#705=CARTESIAN_POINT('',(0.,0.,2.));
#706=CARTESIAN_POINT('',(0.,0.,2.));
#707=CARTESIAN_POINT('',(0.,0.,0.));
#708=CARTESIAN_POINT('Origin',(0.,12.,0.));
#709=CARTESIAN_POINT('',(0.,12.,0.));
#710=CARTESIAN_POINT('',(0.,0.,0.));
#711=CARTESIAN_POINT('',(0.,12.,2.));
#712=CARTESIAN_POINT('',(0.,0.,2.));
#713=CARTESIAN_POINT('',(0.,12.,0.));
#714=CARTESIAN_POINT('Origin',(41.4516018512547,12.,0.));
#715=CARTESIAN_POINT('',(41.4516018512547,12.,0.));
#716=CARTESIAN_POINT('',(0.,12.,0.));
#717=CARTESIAN_POINT('',(41.4516018512547,12.,2.));
#718=CARTESIAN_POINT('',(0.,12.,2.));
#719=CARTESIAN_POINT('',(41.4516018512547,12.,0.));
#720=CARTESIAN_POINT('Origin',(41.4516018512547,1.99999999999997,0.));
#721=CARTESIAN_POINT('',(50.9773231201724,5.04312903224452,0.));
#722=CARTESIAN_POINT('Origin',(41.4516018512547,1.99999999999997,0.));
#723=CARTESIAN_POINT('',(50.9773231201724,5.04312903224452,2.));
#724=CARTESIAN_POINT('Origin',(41.4516018512547,1.99999999999997,2.));
#725=CARTESIAN_POINT('',(50.9773231201724,5.04312903224452,0.));
#726=CARTESIAN_POINT('Origin',(157.896265181219,39.1999269793471,0.));
#727=CARTESIAN_POINT('',(70.5582538960335,-31.3012701892219,0.));
#728=CARTESIAN_POINT('Origin',(157.896265181219,39.1999269793471,0.));
#729=CARTESIAN_POINT('',(70.5582538960335,-31.3012701892219,2.));
#730=CARTESIAN_POINT('Origin',(157.896265181219,39.1999269793471,2.));
#731=CARTESIAN_POINT('',(70.5582538960335,-31.3012701892219,0.));
#732=CARTESIAN_POINT('Origin',(96.5975095414835,-31.3012701892219,0.));
#733=CARTESIAN_POINT('',(96.5975095414835,-31.3012701892219,0.));
#734=CARTESIAN_POINT('',(70.5582538960335,-31.3012701892219,0.));
#735=CARTESIAN_POINT('',(96.5975095414835,-31.3012701892219,2.));
#736=CARTESIAN_POINT('',(70.5582538960335,-31.3012701892219,2.));
#737=CARTESIAN_POINT('',(96.5975095414835,-31.3012701892219,0.));
#738=CARTESIAN_POINT('Origin',(100.959022260717,-19.3181124784632,0.));
#739=CARTESIAN_POINT('',(100.959022260717,-19.3181124784632,0.));
#740=CARTESIAN_POINT('',(96.5975095414835,-31.3012701892219,0.));
#741=CARTESIAN_POINT('',(100.959022260717,-19.3181124784632,2.));
#742=CARTESIAN_POINT('',(96.5975095414835,-31.3012701892219,2.));
#743=CARTESIAN_POINT('',(100.959022260717,-19.3181124784632,0.));
#744=CARTESIAN_POINT('Origin',(110.355948468576,-22.73831391172,0.));
#745=CARTESIAN_POINT('',(108.619466691907,-12.8902363815978,0.));
#746=CARTESIAN_POINT('Origin',(110.355948468576,-22.73831391172,0.));
#747=CARTESIAN_POINT('',(108.619466691907,-12.8902363815978,2.));
#748=CARTESIAN_POINT('Origin',(110.355948468576,-22.73831391172,2.));
#749=CARTESIAN_POINT('',(108.619466691907,-12.8902363815978,0.));
#750=CARTESIAN_POINT('Origin',(148.460596691819,-5.86517022069997,0.));
#751=CARTESIAN_POINT('',(108.619466691907,-12.8902363815978,0.));
#752=CARTESIAN_POINT('',(108.619466691907,-12.8902363815978,2.));
#753=CARTESIAN_POINT('Origin',(75.3578640691657,-15.650635094611,2.));
#754=CARTESIAN_POINT('Origin',(75.3578640691657,-15.650635094611,0.));
#755=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#759,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#756=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#759,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#757=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#755))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#759,#761,#762))
REPRESENTATION_CONTEXT('','3D')
);
#758=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#756))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#759,#761,#762))
REPRESENTATION_CONTEXT('','3D')
);
#759=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#760=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT($,.METRE.)
);
#761=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#762=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#763=SHAPE_DEFINITION_REPRESENTATION(#764,#765);
#764=PRODUCT_DEFINITION_SHAPE('',$,#767);
#765=SHAPE_REPRESENTATION('',(#463),#757);
#766=PRODUCT_DEFINITION_CONTEXT('part definition',#771,'design');
#767=PRODUCT_DEFINITION('S_989','S_989 v0',#768,#766);
#768=PRODUCT_DEFINITION_FORMATION('',$,#773);
#769=PRODUCT_RELATED_PRODUCT_CATEGORY('S_989 v0','S_989 v0',(#773));
#770=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2009,#771);
#771=APPLICATION_CONTEXT(
'Core Data for Automotive Mechanical Design Process');
#772=PRODUCT_CONTEXT('part definition',#771,'mechanical');
#773=PRODUCT('S_989','S_989 v0',$,(#772));
#774=PRESENTATION_STYLE_ASSIGNMENT((#775));
#775=SURFACE_STYLE_USAGE(.BOTH.,#776);
#776=SURFACE_SIDE_STYLE('',(#777));
#777=SURFACE_STYLE_FILL_AREA(#778);
#778=FILL_AREA_STYLE('Steel - Satin',(#779));
#779=FILL_AREA_STYLE_COLOUR('Steel - Satin',#780);
#780=COLOUR_RGB('Steel - Satin',0.627450980392157,0.627450980392157,0.627450980392157);
ENDSEC;
END-ISO-10303-21;
