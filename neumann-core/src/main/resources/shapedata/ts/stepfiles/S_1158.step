ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ (''),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 
'G:/Shared drives/TIP/Validation/SMF KION/Tset SMF Shapes/S_1158.step',

/* time_stamp */ '2021-07-14T14:03:40+02:00',
/* author */ (''),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v18.1',
/* originating_system */ 'Autodesk Translation Framework v10.9.0.1377',

/* authorisation */ '');

FILE_SCHEMA (('AUTOMOTIVE_DESIGN { 1 0 10303 214 3 1 1 }'));
ENDSEC;

DATA;
#10=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#13),#2160);
#11=SHAPE_REPRESENTATION_RELATIONSHIP('SRR','None',#2167,#12);
#12=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#14),#2159);
#13=STYLED_ITEM('',(#2176),#14);
#14=MANIFOLD_SOLID_BREP('Body1',#1268);
#15=FACE_BOUND('',#200,.T.);
#16=FACE_BOUND('',#201,.T.);
#17=FACE_BOUND('',#202,.T.);
#18=FACE_BOUND('',#203,.T.);
#19=FACE_BOUND('',#204,.T.);
#20=FACE_BOUND('',#205,.T.);
#21=FACE_BOUND('',#206,.T.);
#22=FACE_BOUND('',#207,.T.);
#23=FACE_BOUND('',#208,.T.);
#24=FACE_BOUND('',#209,.T.);
#25=FACE_BOUND('',#210,.T.);
#26=FACE_BOUND('',#211,.T.);
#27=FACE_BOUND('',#212,.T.);
#28=FACE_BOUND('',#213,.T.);
#29=FACE_BOUND('',#214,.T.);
#30=FACE_BOUND('',#215,.T.);
#31=FACE_BOUND('',#216,.T.);
#32=FACE_BOUND('',#217,.T.);
#33=FACE_BOUND('',#218,.T.);
#34=FACE_BOUND('',#219,.T.);
#35=FACE_BOUND('',#220,.T.);
#36=FACE_BOUND('',#222,.T.);
#37=FACE_BOUND('',#223,.T.);
#38=FACE_BOUND('',#224,.T.);
#39=FACE_BOUND('',#225,.T.);
#40=FACE_BOUND('',#226,.T.);
#41=FACE_BOUND('',#227,.T.);
#42=FACE_BOUND('',#228,.T.);
#43=FACE_BOUND('',#229,.T.);
#44=FACE_BOUND('',#230,.T.);
#45=FACE_BOUND('',#231,.T.);
#46=FACE_BOUND('',#232,.T.);
#47=FACE_BOUND('',#233,.T.);
#48=FACE_BOUND('',#234,.T.);
#49=FACE_BOUND('',#235,.T.);
#50=FACE_BOUND('',#236,.T.);
#51=FACE_BOUND('',#237,.T.);
#52=FACE_BOUND('',#238,.T.);
#53=FACE_BOUND('',#239,.T.);
#54=FACE_BOUND('',#240,.T.);
#55=FACE_BOUND('',#241,.T.);
#56=FACE_BOUND('',#242,.T.);
#57=PLANE('',#1309);
#58=PLANE('',#1313);
#59=PLANE('',#1317);
#60=PLANE('',#1321);
#61=PLANE('',#1358);
#62=PLANE('',#1362);
#63=PLANE('',#1366);
#64=PLANE('',#1370);
#65=PLANE('',#1374);
#66=PLANE('',#1378);
#67=PLANE('',#1382);
#68=PLANE('',#1386);
#69=PLANE('',#1390);
#70=PLANE('',#1394);
#71=PLANE('',#1398);
#72=PLANE('',#1402);
#73=PLANE('',#1406);
#74=PLANE('',#1410);
#75=PLANE('',#1414);
#76=PLANE('',#1418);
#77=PLANE('',#1419);
#78=PLANE('',#1420);
#79=FACE_OUTER_BOUND('',#140,.T.);
#80=FACE_OUTER_BOUND('',#141,.T.);
#81=FACE_OUTER_BOUND('',#142,.T.);
#82=FACE_OUTER_BOUND('',#143,.T.);
#83=FACE_OUTER_BOUND('',#144,.T.);
#84=FACE_OUTER_BOUND('',#145,.T.);
#85=FACE_OUTER_BOUND('',#146,.T.);
#86=FACE_OUTER_BOUND('',#147,.T.);
#87=FACE_OUTER_BOUND('',#148,.T.);
#88=FACE_OUTER_BOUND('',#149,.T.);
#89=FACE_OUTER_BOUND('',#150,.T.);
#90=FACE_OUTER_BOUND('',#151,.T.);
#91=FACE_OUTER_BOUND('',#152,.T.);
#92=FACE_OUTER_BOUND('',#153,.T.);
#93=FACE_OUTER_BOUND('',#154,.T.);
#94=FACE_OUTER_BOUND('',#155,.T.);
#95=FACE_OUTER_BOUND('',#156,.T.);
#96=FACE_OUTER_BOUND('',#157,.T.);
#97=FACE_OUTER_BOUND('',#158,.T.);
#98=FACE_OUTER_BOUND('',#159,.T.);
#99=FACE_OUTER_BOUND('',#160,.T.);
#100=FACE_OUTER_BOUND('',#161,.T.);
#101=FACE_OUTER_BOUND('',#162,.T.);
#102=FACE_OUTER_BOUND('',#163,.T.);
#103=FACE_OUTER_BOUND('',#164,.T.);
#104=FACE_OUTER_BOUND('',#165,.T.);
#105=FACE_OUTER_BOUND('',#166,.T.);
#106=FACE_OUTER_BOUND('',#167,.T.);
#107=FACE_OUTER_BOUND('',#168,.T.);
#108=FACE_OUTER_BOUND('',#169,.T.);
#109=FACE_OUTER_BOUND('',#170,.T.);
#110=FACE_OUTER_BOUND('',#171,.T.);
#111=FACE_OUTER_BOUND('',#172,.T.);
#112=FACE_OUTER_BOUND('',#173,.T.);
#113=FACE_OUTER_BOUND('',#174,.T.);
#114=FACE_OUTER_BOUND('',#175,.T.);
#115=FACE_OUTER_BOUND('',#176,.T.);
#116=FACE_OUTER_BOUND('',#177,.T.);
#117=FACE_OUTER_BOUND('',#178,.T.);
#118=FACE_OUTER_BOUND('',#179,.T.);
#119=FACE_OUTER_BOUND('',#180,.T.);
#120=FACE_OUTER_BOUND('',#181,.T.);
#121=FACE_OUTER_BOUND('',#182,.T.);
#122=FACE_OUTER_BOUND('',#183,.T.);
#123=FACE_OUTER_BOUND('',#184,.T.);
#124=FACE_OUTER_BOUND('',#185,.T.);
#125=FACE_OUTER_BOUND('',#186,.T.);
#126=FACE_OUTER_BOUND('',#187,.T.);
#127=FACE_OUTER_BOUND('',#188,.T.);
#128=FACE_OUTER_BOUND('',#189,.T.);
#129=FACE_OUTER_BOUND('',#190,.T.);
#130=FACE_OUTER_BOUND('',#191,.T.);
#131=FACE_OUTER_BOUND('',#192,.T.);
#132=FACE_OUTER_BOUND('',#193,.T.);
#133=FACE_OUTER_BOUND('',#194,.T.);
#134=FACE_OUTER_BOUND('',#195,.T.);
#135=FACE_OUTER_BOUND('',#196,.T.);
#136=FACE_OUTER_BOUND('',#197,.T.);
#137=FACE_OUTER_BOUND('',#198,.T.);
#138=FACE_OUTER_BOUND('',#199,.T.);
#139=FACE_OUTER_BOUND('',#221,.T.);
#140=EDGE_LOOP('',(#814,#815,#816,#817));
#141=EDGE_LOOP('',(#818,#819,#820,#821));
#142=EDGE_LOOP('',(#822,#823,#824,#825));
#143=EDGE_LOOP('',(#826,#827,#828,#829));
#144=EDGE_LOOP('',(#830,#831,#832,#833));
#145=EDGE_LOOP('',(#834,#835,#836,#837));
#146=EDGE_LOOP('',(#838,#839,#840,#841));
#147=EDGE_LOOP('',(#842,#843,#844,#845));
#148=EDGE_LOOP('',(#846,#847,#848,#849));
#149=EDGE_LOOP('',(#850,#851,#852,#853));
#150=EDGE_LOOP('',(#854,#855,#856,#857));
#151=EDGE_LOOP('',(#858,#859,#860,#861));
#152=EDGE_LOOP('',(#862,#863,#864,#865));
#153=EDGE_LOOP('',(#866,#867,#868,#869));
#154=EDGE_LOOP('',(#870,#871,#872,#873));
#155=EDGE_LOOP('',(#874,#875,#876,#877));
#156=EDGE_LOOP('',(#878,#879,#880,#881));
#157=EDGE_LOOP('',(#882,#883,#884,#885));
#158=EDGE_LOOP('',(#886,#887,#888,#889));
#159=EDGE_LOOP('',(#890,#891,#892,#893));
#160=EDGE_LOOP('',(#894,#895,#896,#897));
#161=EDGE_LOOP('',(#898,#899,#900,#901));
#162=EDGE_LOOP('',(#902,#903,#904,#905));
#163=EDGE_LOOP('',(#906,#907,#908,#909));
#164=EDGE_LOOP('',(#910,#911,#912,#913));
#165=EDGE_LOOP('',(#914,#915,#916,#917));
#166=EDGE_LOOP('',(#918,#919,#920,#921));
#167=EDGE_LOOP('',(#922,#923,#924,#925));
#168=EDGE_LOOP('',(#926,#927,#928,#929));
#169=EDGE_LOOP('',(#930,#931,#932,#933));
#170=EDGE_LOOP('',(#934,#935,#936,#937));
#171=EDGE_LOOP('',(#938,#939,#940,#941));
#172=EDGE_LOOP('',(#942,#943,#944,#945));
#173=EDGE_LOOP('',(#946,#947,#948,#949));
#174=EDGE_LOOP('',(#950,#951,#952,#953));
#175=EDGE_LOOP('',(#954,#955,#956,#957));
#176=EDGE_LOOP('',(#958,#959,#960,#961));
#177=EDGE_LOOP('',(#962,#963,#964,#965));
#178=EDGE_LOOP('',(#966,#967,#968,#969));
#179=EDGE_LOOP('',(#970,#971,#972,#973));
#180=EDGE_LOOP('',(#974,#975,#976,#977));
#181=EDGE_LOOP('',(#978,#979,#980,#981));
#182=EDGE_LOOP('',(#982,#983,#984,#985));
#183=EDGE_LOOP('',(#986,#987,#988,#989));
#184=EDGE_LOOP('',(#990,#991,#992,#993));
#185=EDGE_LOOP('',(#994,#995,#996,#997));
#186=EDGE_LOOP('',(#998,#999,#1000,#1001));
#187=EDGE_LOOP('',(#1002,#1003,#1004,#1005));
#188=EDGE_LOOP('',(#1006,#1007,#1008,#1009));
#189=EDGE_LOOP('',(#1010,#1011,#1012,#1013));
#190=EDGE_LOOP('',(#1014,#1015,#1016,#1017));
#191=EDGE_LOOP('',(#1018,#1019,#1020,#1021));
#192=EDGE_LOOP('',(#1022,#1023,#1024,#1025));
#193=EDGE_LOOP('',(#1026,#1027,#1028,#1029));
#194=EDGE_LOOP('',(#1030,#1031,#1032,#1033));
#195=EDGE_LOOP('',(#1034,#1035,#1036,#1037));
#196=EDGE_LOOP('',(#1038,#1039,#1040,#1041));
#197=EDGE_LOOP('',(#1042,#1043,#1044,#1045));
#198=EDGE_LOOP('',(#1046,#1047,#1048,#1049));
#199=EDGE_LOOP('',(#1050,#1051,#1052,#1053,#1054,#1055,#1056,#1057,#1058,
#1059,#1060,#1061,#1062,#1063,#1064,#1065,#1066,#1067,#1068,#1069,#1070,
#1071,#1072,#1073,#1074,#1075,#1076,#1077,#1078,#1079,#1080,#1081));
#200=EDGE_LOOP('',(#1082));
#201=EDGE_LOOP('',(#1083));
#202=EDGE_LOOP('',(#1084));
#203=EDGE_LOOP('',(#1085));
#204=EDGE_LOOP('',(#1086));
#205=EDGE_LOOP('',(#1087));
#206=EDGE_LOOP('',(#1088));
#207=EDGE_LOOP('',(#1089));
#208=EDGE_LOOP('',(#1090));
#209=EDGE_LOOP('',(#1091));
#210=EDGE_LOOP('',(#1092,#1093,#1094,#1095));
#211=EDGE_LOOP('',(#1096,#1097,#1098,#1099));
#212=EDGE_LOOP('',(#1100));
#213=EDGE_LOOP('',(#1101));
#214=EDGE_LOOP('',(#1102));
#215=EDGE_LOOP('',(#1103));
#216=EDGE_LOOP('',(#1104));
#217=EDGE_LOOP('',(#1105));
#218=EDGE_LOOP('',(#1106));
#219=EDGE_LOOP('',(#1107));
#220=EDGE_LOOP('',(#1108));
#221=EDGE_LOOP('',(#1109,#1110,#1111,#1112,#1113,#1114,#1115,#1116,#1117,
#1118,#1119,#1120,#1121,#1122,#1123,#1124,#1125,#1126,#1127,#1128,#1129,
#1130,#1131,#1132,#1133,#1134,#1135,#1136,#1137,#1138,#1139,#1140));
#222=EDGE_LOOP('',(#1141));
#223=EDGE_LOOP('',(#1142));
#224=EDGE_LOOP('',(#1143));
#225=EDGE_LOOP('',(#1144));
#226=EDGE_LOOP('',(#1145));
#227=EDGE_LOOP('',(#1146));
#228=EDGE_LOOP('',(#1147));
#229=EDGE_LOOP('',(#1148));
#230=EDGE_LOOP('',(#1149));
#231=EDGE_LOOP('',(#1150));
#232=EDGE_LOOP('',(#1151,#1152,#1153,#1154));
#233=EDGE_LOOP('',(#1155,#1156,#1157,#1158));
#234=EDGE_LOOP('',(#1159));
#235=EDGE_LOOP('',(#1160));
#236=EDGE_LOOP('',(#1161));
#237=EDGE_LOOP('',(#1162));
#238=EDGE_LOOP('',(#1163));
#239=EDGE_LOOP('',(#1164));
#240=EDGE_LOOP('',(#1165));
#241=EDGE_LOOP('',(#1166));
#242=EDGE_LOOP('',(#1167));
#243=LINE('',#1805,#342);
#244=LINE('',#1811,#343);
#245=LINE('',#1817,#344);
#246=LINE('',#1823,#345);
#247=LINE('',#1829,#346);
#248=LINE('',#1835,#347);
#249=LINE('',#1841,#348);
#250=LINE('',#1847,#349);
#251=LINE('',#1853,#350);
#252=LINE('',#1858,#351);
#253=LINE('',#1860,#352);
#254=LINE('',#1862,#353);
#255=LINE('',#1863,#354);
#256=LINE('',#1869,#355);
#257=LINE('',#1872,#356);
#258=LINE('',#1874,#357);
#259=LINE('',#1875,#358);
#260=LINE('',#1882,#359);
#261=LINE('',#1884,#360);
#262=LINE('',#1886,#361);
#263=LINE('',#1887,#362);
#264=LINE('',#1893,#363);
#265=LINE('',#1896,#364);
#266=LINE('',#1898,#365);
#267=LINE('',#1899,#366);
#268=LINE('',#1907,#367);
#269=LINE('',#1913,#368);
#270=LINE('',#1919,#369);
#271=LINE('',#1925,#370);
#272=LINE('',#1931,#371);
#273=LINE('',#1937,#372);
#274=LINE('',#1943,#373);
#275=LINE('',#1949,#374);
#276=LINE('',#1955,#375);
#277=LINE('',#1961,#376);
#278=LINE('',#1968,#377);
#279=LINE('',#1971,#378);
#280=LINE('',#1974,#379);
#281=LINE('',#1976,#380);
#282=LINE('',#1977,#381);
#283=LINE('',#1983,#382);
#284=LINE('',#1986,#383);
#285=LINE('',#1988,#384);
#286=LINE('',#1989,#385);
#287=LINE('',#1995,#386);
#288=LINE('',#1998,#387);
#289=LINE('',#2000,#388);
#290=LINE('',#2001,#389);
#291=LINE('',#2007,#390);
#292=LINE('',#2010,#391);
#293=LINE('',#2012,#392);
#294=LINE('',#2013,#393);
#295=LINE('',#2019,#394);
#296=LINE('',#2022,#395);
#297=LINE('',#2024,#396);
#298=LINE('',#2025,#397);
#299=LINE('',#2031,#398);
#300=LINE('',#2034,#399);
#301=LINE('',#2036,#400);
#302=LINE('',#2037,#401);
#303=LINE('',#2043,#402);
#304=LINE('',#2046,#403);
#305=LINE('',#2048,#404);
#306=LINE('',#2049,#405);
#307=LINE('',#2055,#406);
#308=LINE('',#2058,#407);
#309=LINE('',#2060,#408);
#310=LINE('',#2061,#409);
#311=LINE('',#2067,#410);
#312=LINE('',#2070,#411);
#313=LINE('',#2072,#412);
#314=LINE('',#2073,#413);
#315=LINE('',#2079,#414);
#316=LINE('',#2082,#415);
#317=LINE('',#2084,#416);
#318=LINE('',#2085,#417);
#319=LINE('',#2091,#418);
#320=LINE('',#2094,#419);
#321=LINE('',#2096,#420);
#322=LINE('',#2097,#421);
#323=LINE('',#2103,#422);
#324=LINE('',#2106,#423);
#325=LINE('',#2108,#424);
#326=LINE('',#2109,#425);
#327=LINE('',#2115,#426);
#328=LINE('',#2118,#427);
#329=LINE('',#2120,#428);
#330=LINE('',#2121,#429);
#331=LINE('',#2127,#430);
#332=LINE('',#2130,#431);
#333=LINE('',#2132,#432);
#334=LINE('',#2133,#433);
#335=LINE('',#2139,#434);
#336=LINE('',#2142,#435);
#337=LINE('',#2144,#436);
#338=LINE('',#2145,#437);
#339=LINE('',#2151,#438);
#340=LINE('',#2153,#439);
#341=LINE('',#2154,#440);
#342=VECTOR('',#1427,5.);
#343=VECTOR('',#1434,2.99999999999997);
#344=VECTOR('',#1441,2.99999999999997);
#345=VECTOR('',#1448,3.99999999999999);
#346=VECTOR('',#1455,4.00000000000006);
#347=VECTOR('',#1462,4.00000000000006);
#348=VECTOR('',#1469,3.99999999999999);
#349=VECTOR('',#1476,3.99999999999999);
#350=VECTOR('',#1483,4.);
#351=VECTOR('',#1488,10.);
#352=VECTOR('',#1489,10.);
#353=VECTOR('',#1490,10.);
#354=VECTOR('',#1491,10.);
#355=VECTOR('',#1498,10.);
#356=VECTOR('',#1501,10.);
#357=VECTOR('',#1502,10.);
#358=VECTOR('',#1503,10.);
#359=VECTOR('',#1512,10.);
#360=VECTOR('',#1513,10.);
#361=VECTOR('',#1514,10.);
#362=VECTOR('',#1515,10.);
#363=VECTOR('',#1522,10.);
#364=VECTOR('',#1525,10.);
#365=VECTOR('',#1526,10.);
#366=VECTOR('',#1527,10.);
#367=VECTOR('',#1538,3.99999999999999);
#368=VECTOR('',#1545,3.99999999999999);
#369=VECTOR('',#1552,3.99999999999999);
#370=VECTOR('',#1559,3.99999999999999);
#371=VECTOR('',#1566,3.99999999999999);
#372=VECTOR('',#1573,3.40000000000003);
#373=VECTOR('',#1580,3.40000000000003);
#374=VECTOR('',#1587,2.99999999999999);
#375=VECTOR('',#1594,5.);
#376=VECTOR('',#1601,3.00000000000001);
#377=VECTOR('',#1608,10.);
#378=VECTOR('',#1611,10.);
#379=VECTOR('',#1614,10.);
#380=VECTOR('',#1615,10.);
#381=VECTOR('',#1616,10.);
#382=VECTOR('',#1623,10.);
#383=VECTOR('',#1626,10.);
#384=VECTOR('',#1627,10.);
#385=VECTOR('',#1628,10.);
#386=VECTOR('',#1635,10.);
#387=VECTOR('',#1638,10.);
#388=VECTOR('',#1639,10.);
#389=VECTOR('',#1640,10.);
#390=VECTOR('',#1647,10.);
#391=VECTOR('',#1650,10.);
#392=VECTOR('',#1651,10.);
#393=VECTOR('',#1652,10.);
#394=VECTOR('',#1659,10.);
#395=VECTOR('',#1662,10.);
#396=VECTOR('',#1663,10.);
#397=VECTOR('',#1664,10.);
#398=VECTOR('',#1671,10.);
#399=VECTOR('',#1674,10.);
#400=VECTOR('',#1675,10.);
#401=VECTOR('',#1676,10.);
#402=VECTOR('',#1683,10.);
#403=VECTOR('',#1686,10.);
#404=VECTOR('',#1687,10.);
#405=VECTOR('',#1688,10.);
#406=VECTOR('',#1695,10.);
#407=VECTOR('',#1698,10.);
#408=VECTOR('',#1699,10.);
#409=VECTOR('',#1700,10.);
#410=VECTOR('',#1707,10.);
#411=VECTOR('',#1710,10.);
#412=VECTOR('',#1711,10.);
#413=VECTOR('',#1712,10.);
#414=VECTOR('',#1719,10.);
#415=VECTOR('',#1722,10.);
#416=VECTOR('',#1723,10.);
#417=VECTOR('',#1724,10.);
#418=VECTOR('',#1731,10.);
#419=VECTOR('',#1734,10.);
#420=VECTOR('',#1735,10.);
#421=VECTOR('',#1736,10.);
#422=VECTOR('',#1743,10.);
#423=VECTOR('',#1746,10.);
#424=VECTOR('',#1747,10.);
#425=VECTOR('',#1748,10.);
#426=VECTOR('',#1755,10.);
#427=VECTOR('',#1758,10.);
#428=VECTOR('',#1759,10.);
#429=VECTOR('',#1760,10.);
#430=VECTOR('',#1767,10.);
#431=VECTOR('',#1770,10.);
#432=VECTOR('',#1771,10.);
#433=VECTOR('',#1772,10.);
#434=VECTOR('',#1779,10.);
#435=VECTOR('',#1782,10.);
#436=VECTOR('',#1783,10.);
#437=VECTOR('',#1784,10.);
#438=VECTOR('',#1791,10.);
#439=VECTOR('',#1794,10.);
#440=VECTOR('',#1795,10.);
#441=CIRCLE('',#1283,5.);
#442=CIRCLE('',#1284,5.);
#443=CIRCLE('',#1286,2.99999999999997);
#444=CIRCLE('',#1287,2.99999999999997);
#445=CIRCLE('',#1289,2.99999999999997);
#446=CIRCLE('',#1290,2.99999999999997);
#447=CIRCLE('',#1292,3.99999999999999);
#448=CIRCLE('',#1293,3.99999999999999);
#449=CIRCLE('',#1295,4.00000000000006);
#450=CIRCLE('',#1296,4.00000000000006);
#451=CIRCLE('',#1298,4.00000000000006);
#452=CIRCLE('',#1299,4.00000000000006);
#453=CIRCLE('',#1301,3.99999999999999);
#454=CIRCLE('',#1302,3.99999999999999);
#455=CIRCLE('',#1304,3.99999999999999);
#456=CIRCLE('',#1305,3.99999999999999);
#457=CIRCLE('',#1307,4.);
#458=CIRCLE('',#1308,4.);
#459=CIRCLE('',#1311,33.);
#460=CIRCLE('',#1312,33.);
#461=CIRCLE('',#1315,33.);
#462=CIRCLE('',#1316,33.);
#463=CIRCLE('',#1319,33.);
#464=CIRCLE('',#1320,33.);
#465=CIRCLE('',#1323,33.);
#466=CIRCLE('',#1324,33.);
#467=CIRCLE('',#1326,3.99999999999999);
#468=CIRCLE('',#1327,3.99999999999999);
#469=CIRCLE('',#1329,3.99999999999999);
#470=CIRCLE('',#1330,3.99999999999999);
#471=CIRCLE('',#1332,3.99999999999999);
#472=CIRCLE('',#1333,3.99999999999999);
#473=CIRCLE('',#1335,3.99999999999999);
#474=CIRCLE('',#1336,3.99999999999999);
#475=CIRCLE('',#1338,3.99999999999999);
#476=CIRCLE('',#1339,3.99999999999999);
#477=CIRCLE('',#1341,3.40000000000003);
#478=CIRCLE('',#1342,3.40000000000003);
#479=CIRCLE('',#1344,3.40000000000003);
#480=CIRCLE('',#1345,3.40000000000003);
#481=CIRCLE('',#1347,2.99999999999999);
#482=CIRCLE('',#1348,2.99999999999999);
#483=CIRCLE('',#1350,5.);
#484=CIRCLE('',#1351,5.);
#485=CIRCLE('',#1353,3.00000000000001);
#486=CIRCLE('',#1354,3.00000000000001);
#487=CIRCLE('',#1356,40.);
#488=CIRCLE('',#1357,40.);
#489=CIRCLE('',#1360,20.);
#490=CIRCLE('',#1361,20.);
#491=CIRCLE('',#1364,2.99999999999997);
#492=CIRCLE('',#1365,2.99999999999997);
#493=CIRCLE('',#1368,2.99999999999997);
#494=CIRCLE('',#1369,2.99999999999997);
#495=CIRCLE('',#1372,20.0000000000003);
#496=CIRCLE('',#1373,20.0000000000003);
#497=CIRCLE('',#1376,20.0000000000005);
#498=CIRCLE('',#1377,20.0000000000005);
#499=CIRCLE('',#1380,20.0000000000023);
#500=CIRCLE('',#1381,20.0000000000023);
#501=CIRCLE('',#1384,20.);
#502=CIRCLE('',#1385,20.);
#503=CIRCLE('',#1388,20.);
#504=CIRCLE('',#1389,20.);
#505=CIRCLE('',#1392,3.);
#506=CIRCLE('',#1393,3.);
#507=CIRCLE('',#1396,3.00000000000011);
#508=CIRCLE('',#1397,3.00000000000011);
#509=CIRCLE('',#1400,20.);
#510=CIRCLE('',#1401,20.);
#511=CIRCLE('',#1404,40.);
#512=CIRCLE('',#1405,40.);
#513=CIRCLE('',#1408,3.00000000000001);
#514=CIRCLE('',#1409,3.00000000000001);
#515=CIRCLE('',#1412,16.);
#516=CIRCLE('',#1413,16.);
#517=CIRCLE('',#1416,3.00000000000004);
#518=CIRCLE('',#1417,3.00000000000004);
#519=VERTEX_POINT('',#1802);
#520=VERTEX_POINT('',#1804);
#521=VERTEX_POINT('',#1808);
#522=VERTEX_POINT('',#1810);
#523=VERTEX_POINT('',#1814);
#524=VERTEX_POINT('',#1816);
#525=VERTEX_POINT('',#1820);
#526=VERTEX_POINT('',#1822);
#527=VERTEX_POINT('',#1826);
#528=VERTEX_POINT('',#1828);
#529=VERTEX_POINT('',#1832);
#530=VERTEX_POINT('',#1834);
#531=VERTEX_POINT('',#1838);
#532=VERTEX_POINT('',#1840);
#533=VERTEX_POINT('',#1844);
#534=VERTEX_POINT('',#1846);
#535=VERTEX_POINT('',#1850);
#536=VERTEX_POINT('',#1852);
#537=VERTEX_POINT('',#1856);
#538=VERTEX_POINT('',#1857);
#539=VERTEX_POINT('',#1859);
#540=VERTEX_POINT('',#1861);
#541=VERTEX_POINT('',#1865);
#542=VERTEX_POINT('',#1867);
#543=VERTEX_POINT('',#1871);
#544=VERTEX_POINT('',#1873);
#545=VERTEX_POINT('',#1880);
#546=VERTEX_POINT('',#1881);
#547=VERTEX_POINT('',#1883);
#548=VERTEX_POINT('',#1885);
#549=VERTEX_POINT('',#1889);
#550=VERTEX_POINT('',#1891);
#551=VERTEX_POINT('',#1895);
#552=VERTEX_POINT('',#1897);
#553=VERTEX_POINT('',#1904);
#554=VERTEX_POINT('',#1906);
#555=VERTEX_POINT('',#1910);
#556=VERTEX_POINT('',#1912);
#557=VERTEX_POINT('',#1916);
#558=VERTEX_POINT('',#1918);
#559=VERTEX_POINT('',#1922);
#560=VERTEX_POINT('',#1924);
#561=VERTEX_POINT('',#1928);
#562=VERTEX_POINT('',#1930);
#563=VERTEX_POINT('',#1934);
#564=VERTEX_POINT('',#1936);
#565=VERTEX_POINT('',#1940);
#566=VERTEX_POINT('',#1942);
#567=VERTEX_POINT('',#1946);
#568=VERTEX_POINT('',#1948);
#569=VERTEX_POINT('',#1952);
#570=VERTEX_POINT('',#1954);
#571=VERTEX_POINT('',#1958);
#572=VERTEX_POINT('',#1960);
#573=VERTEX_POINT('',#1964);
#574=VERTEX_POINT('',#1965);
#575=VERTEX_POINT('',#1967);
#576=VERTEX_POINT('',#1969);
#577=VERTEX_POINT('',#1973);
#578=VERTEX_POINT('',#1975);
#579=VERTEX_POINT('',#1979);
#580=VERTEX_POINT('',#1981);
#581=VERTEX_POINT('',#1985);
#582=VERTEX_POINT('',#1987);
#583=VERTEX_POINT('',#1991);
#584=VERTEX_POINT('',#1993);
#585=VERTEX_POINT('',#1997);
#586=VERTEX_POINT('',#1999);
#587=VERTEX_POINT('',#2003);
#588=VERTEX_POINT('',#2005);
#589=VERTEX_POINT('',#2009);
#590=VERTEX_POINT('',#2011);
#591=VERTEX_POINT('',#2015);
#592=VERTEX_POINT('',#2017);
#593=VERTEX_POINT('',#2021);
#594=VERTEX_POINT('',#2023);
#595=VERTEX_POINT('',#2027);
#596=VERTEX_POINT('',#2029);
#597=VERTEX_POINT('',#2033);
#598=VERTEX_POINT('',#2035);
#599=VERTEX_POINT('',#2039);
#600=VERTEX_POINT('',#2041);
#601=VERTEX_POINT('',#2045);
#602=VERTEX_POINT('',#2047);
#603=VERTEX_POINT('',#2051);
#604=VERTEX_POINT('',#2053);
#605=VERTEX_POINT('',#2057);
#606=VERTEX_POINT('',#2059);
#607=VERTEX_POINT('',#2063);
#608=VERTEX_POINT('',#2065);
#609=VERTEX_POINT('',#2069);
#610=VERTEX_POINT('',#2071);
#611=VERTEX_POINT('',#2075);
#612=VERTEX_POINT('',#2077);
#613=VERTEX_POINT('',#2081);
#614=VERTEX_POINT('',#2083);
#615=VERTEX_POINT('',#2087);
#616=VERTEX_POINT('',#2089);
#617=VERTEX_POINT('',#2093);
#618=VERTEX_POINT('',#2095);
#619=VERTEX_POINT('',#2099);
#620=VERTEX_POINT('',#2101);
#621=VERTEX_POINT('',#2105);
#622=VERTEX_POINT('',#2107);
#623=VERTEX_POINT('',#2111);
#624=VERTEX_POINT('',#2113);
#625=VERTEX_POINT('',#2117);
#626=VERTEX_POINT('',#2119);
#627=VERTEX_POINT('',#2123);
#628=VERTEX_POINT('',#2125);
#629=VERTEX_POINT('',#2129);
#630=VERTEX_POINT('',#2131);
#631=VERTEX_POINT('',#2135);
#632=VERTEX_POINT('',#2137);
#633=VERTEX_POINT('',#2141);
#634=VERTEX_POINT('',#2143);
#635=VERTEX_POINT('',#2147);
#636=VERTEX_POINT('',#2149);
#637=EDGE_CURVE('',#519,#519,#441,.T.);
#638=EDGE_CURVE('',#519,#520,#243,.T.);
#639=EDGE_CURVE('',#520,#520,#442,.T.);
#640=EDGE_CURVE('',#521,#521,#443,.T.);
#641=EDGE_CURVE('',#521,#522,#244,.T.);
#642=EDGE_CURVE('',#522,#522,#444,.T.);
#643=EDGE_CURVE('',#523,#523,#445,.T.);
#644=EDGE_CURVE('',#523,#524,#245,.T.);
#645=EDGE_CURVE('',#524,#524,#446,.T.);
#646=EDGE_CURVE('',#525,#525,#447,.T.);
#647=EDGE_CURVE('',#525,#526,#246,.T.);
#648=EDGE_CURVE('',#526,#526,#448,.T.);
#649=EDGE_CURVE('',#527,#527,#449,.T.);
#650=EDGE_CURVE('',#527,#528,#247,.T.);
#651=EDGE_CURVE('',#528,#528,#450,.T.);
#652=EDGE_CURVE('',#529,#529,#451,.T.);
#653=EDGE_CURVE('',#529,#530,#248,.T.);
#654=EDGE_CURVE('',#530,#530,#452,.T.);
#655=EDGE_CURVE('',#531,#531,#453,.T.);
#656=EDGE_CURVE('',#531,#532,#249,.T.);
#657=EDGE_CURVE('',#532,#532,#454,.T.);
#658=EDGE_CURVE('',#533,#533,#455,.T.);
#659=EDGE_CURVE('',#533,#534,#250,.T.);
#660=EDGE_CURVE('',#534,#534,#456,.T.);
#661=EDGE_CURVE('',#535,#535,#457,.T.);
#662=EDGE_CURVE('',#535,#536,#251,.T.);
#663=EDGE_CURVE('',#536,#536,#458,.T.);
#664=EDGE_CURVE('',#537,#538,#252,.T.);
#665=EDGE_CURVE('',#538,#539,#253,.T.);
#666=EDGE_CURVE('',#540,#539,#254,.T.);
#667=EDGE_CURVE('',#537,#540,#255,.T.);
#668=EDGE_CURVE('',#541,#537,#459,.T.);
#669=EDGE_CURVE('',#542,#540,#460,.T.);
#670=EDGE_CURVE('',#541,#542,#256,.T.);
#671=EDGE_CURVE('',#543,#541,#257,.T.);
#672=EDGE_CURVE('',#544,#542,#258,.T.);
#673=EDGE_CURVE('',#543,#544,#259,.T.);
#674=EDGE_CURVE('',#538,#543,#461,.T.);
#675=EDGE_CURVE('',#539,#544,#462,.T.);
#676=EDGE_CURVE('',#545,#546,#260,.T.);
#677=EDGE_CURVE('',#546,#547,#261,.T.);
#678=EDGE_CURVE('',#548,#547,#262,.T.);
#679=EDGE_CURVE('',#545,#548,#263,.T.);
#680=EDGE_CURVE('',#549,#545,#463,.T.);
#681=EDGE_CURVE('',#550,#548,#464,.T.);
#682=EDGE_CURVE('',#549,#550,#264,.T.);
#683=EDGE_CURVE('',#551,#549,#265,.T.);
#684=EDGE_CURVE('',#552,#550,#266,.T.);
#685=EDGE_CURVE('',#551,#552,#267,.T.);
#686=EDGE_CURVE('',#546,#551,#465,.T.);
#687=EDGE_CURVE('',#547,#552,#466,.T.);
#688=EDGE_CURVE('',#553,#553,#467,.T.);
#689=EDGE_CURVE('',#553,#554,#268,.T.);
#690=EDGE_CURVE('',#554,#554,#468,.T.);
#691=EDGE_CURVE('',#555,#555,#469,.T.);
#692=EDGE_CURVE('',#555,#556,#269,.T.);
#693=EDGE_CURVE('',#556,#556,#470,.T.);
#694=EDGE_CURVE('',#557,#557,#471,.T.);
#695=EDGE_CURVE('',#557,#558,#270,.T.);
#696=EDGE_CURVE('',#558,#558,#472,.T.);
#697=EDGE_CURVE('',#559,#559,#473,.T.);
#698=EDGE_CURVE('',#559,#560,#271,.T.);
#699=EDGE_CURVE('',#560,#560,#474,.T.);
#700=EDGE_CURVE('',#561,#561,#475,.T.);
#701=EDGE_CURVE('',#561,#562,#272,.T.);
#702=EDGE_CURVE('',#562,#562,#476,.T.);
#703=EDGE_CURVE('',#563,#563,#477,.T.);
#704=EDGE_CURVE('',#563,#564,#273,.T.);
#705=EDGE_CURVE('',#564,#564,#478,.T.);
#706=EDGE_CURVE('',#565,#565,#479,.T.);
#707=EDGE_CURVE('',#565,#566,#274,.T.);
#708=EDGE_CURVE('',#566,#566,#480,.T.);
#709=EDGE_CURVE('',#567,#567,#481,.T.);
#710=EDGE_CURVE('',#567,#568,#275,.T.);
#711=EDGE_CURVE('',#568,#568,#482,.T.);
#712=EDGE_CURVE('',#569,#569,#483,.T.);
#713=EDGE_CURVE('',#569,#570,#276,.T.);
#714=EDGE_CURVE('',#570,#570,#484,.T.);
#715=EDGE_CURVE('',#571,#571,#485,.T.);
#716=EDGE_CURVE('',#571,#572,#277,.T.);
#717=EDGE_CURVE('',#572,#572,#486,.T.);
#718=EDGE_CURVE('',#573,#574,#487,.T.);
#719=EDGE_CURVE('',#573,#575,#278,.T.);
#720=EDGE_CURVE('',#576,#575,#488,.T.);
#721=EDGE_CURVE('',#574,#576,#279,.T.);
#722=EDGE_CURVE('',#577,#574,#280,.T.);
#723=EDGE_CURVE('',#578,#576,#281,.T.);
#724=EDGE_CURVE('',#577,#578,#282,.T.);
#725=EDGE_CURVE('',#579,#577,#489,.T.);
#726=EDGE_CURVE('',#580,#578,#490,.T.);
#727=EDGE_CURVE('',#579,#580,#283,.T.);
#728=EDGE_CURVE('',#581,#579,#284,.T.);
#729=EDGE_CURVE('',#582,#580,#285,.T.);
#730=EDGE_CURVE('',#581,#582,#286,.T.);
#731=EDGE_CURVE('',#583,#581,#491,.T.);
#732=EDGE_CURVE('',#584,#582,#492,.T.);
#733=EDGE_CURVE('',#583,#584,#287,.T.);
#734=EDGE_CURVE('',#585,#583,#288,.T.);
#735=EDGE_CURVE('',#586,#584,#289,.T.);
#736=EDGE_CURVE('',#585,#586,#290,.T.);
#737=EDGE_CURVE('',#587,#585,#493,.T.);
#738=EDGE_CURVE('',#588,#586,#494,.T.);
#739=EDGE_CURVE('',#587,#588,#291,.T.);
#740=EDGE_CURVE('',#589,#587,#292,.T.);
#741=EDGE_CURVE('',#590,#588,#293,.T.);
#742=EDGE_CURVE('',#589,#590,#294,.T.);
#743=EDGE_CURVE('',#591,#589,#495,.T.);
#744=EDGE_CURVE('',#592,#590,#496,.T.);
#745=EDGE_CURVE('',#591,#592,#295,.T.);
#746=EDGE_CURVE('',#593,#591,#296,.T.);
#747=EDGE_CURVE('',#594,#592,#297,.T.);
#748=EDGE_CURVE('',#593,#594,#298,.T.);
#749=EDGE_CURVE('',#593,#595,#497,.T.);
#750=EDGE_CURVE('',#596,#594,#498,.T.);
#751=EDGE_CURVE('',#595,#596,#299,.T.);
#752=EDGE_CURVE('',#595,#597,#300,.T.);
#753=EDGE_CURVE('',#598,#596,#301,.T.);
#754=EDGE_CURVE('',#597,#598,#302,.T.);
#755=EDGE_CURVE('',#597,#599,#499,.T.);
#756=EDGE_CURVE('',#600,#598,#500,.T.);
#757=EDGE_CURVE('',#599,#600,#303,.T.);
#758=EDGE_CURVE('',#599,#601,#304,.T.);
#759=EDGE_CURVE('',#602,#600,#305,.T.);
#760=EDGE_CURVE('',#601,#602,#306,.T.);
#761=EDGE_CURVE('',#601,#603,#501,.T.);
#762=EDGE_CURVE('',#604,#602,#502,.T.);
#763=EDGE_CURVE('',#603,#604,#307,.T.);
#764=EDGE_CURVE('',#603,#605,#308,.T.);
#765=EDGE_CURVE('',#606,#604,#309,.T.);
#766=EDGE_CURVE('',#605,#606,#310,.T.);
#767=EDGE_CURVE('',#607,#605,#503,.T.);
#768=EDGE_CURVE('',#608,#606,#504,.T.);
#769=EDGE_CURVE('',#607,#608,#311,.T.);
#770=EDGE_CURVE('',#609,#607,#312,.T.);
#771=EDGE_CURVE('',#610,#608,#313,.T.);
#772=EDGE_CURVE('',#609,#610,#314,.T.);
#773=EDGE_CURVE('',#611,#609,#505,.T.);
#774=EDGE_CURVE('',#612,#610,#506,.T.);
#775=EDGE_CURVE('',#611,#612,#315,.T.);
#776=EDGE_CURVE('',#613,#611,#316,.T.);
#777=EDGE_CURVE('',#614,#612,#317,.T.);
#778=EDGE_CURVE('',#613,#614,#318,.T.);
#779=EDGE_CURVE('',#615,#613,#507,.T.);
#780=EDGE_CURVE('',#616,#614,#508,.T.);
#781=EDGE_CURVE('',#615,#616,#319,.T.);
#782=EDGE_CURVE('',#617,#615,#320,.T.);
#783=EDGE_CURVE('',#618,#616,#321,.T.);
#784=EDGE_CURVE('',#617,#618,#322,.T.);
#785=EDGE_CURVE('',#619,#617,#509,.T.);
#786=EDGE_CURVE('',#620,#618,#510,.T.);
#787=EDGE_CURVE('',#619,#620,#323,.T.);
#788=EDGE_CURVE('',#619,#621,#324,.T.);
#789=EDGE_CURVE('',#622,#620,#325,.T.);
#790=EDGE_CURVE('',#621,#622,#326,.T.);
#791=EDGE_CURVE('',#621,#623,#511,.T.);
#792=EDGE_CURVE('',#624,#622,#512,.T.);
#793=EDGE_CURVE('',#623,#624,#327,.T.);
#794=EDGE_CURVE('',#623,#625,#328,.T.);
#795=EDGE_CURVE('',#626,#624,#329,.T.);
#796=EDGE_CURVE('',#625,#626,#330,.T.);
#797=EDGE_CURVE('',#627,#625,#513,.T.);
#798=EDGE_CURVE('',#628,#626,#514,.T.);
#799=EDGE_CURVE('',#627,#628,#331,.T.);
#800=EDGE_CURVE('',#629,#627,#332,.T.);
#801=EDGE_CURVE('',#630,#628,#333,.T.);
#802=EDGE_CURVE('',#629,#630,#334,.T.);
#803=EDGE_CURVE('',#629,#631,#515,.T.);
#804=EDGE_CURVE('',#632,#630,#516,.T.);
#805=EDGE_CURVE('',#631,#632,#335,.T.);
#806=EDGE_CURVE('',#631,#633,#336,.T.);
#807=EDGE_CURVE('',#634,#632,#337,.T.);
#808=EDGE_CURVE('',#633,#634,#338,.T.);
#809=EDGE_CURVE('',#635,#633,#517,.T.);
#810=EDGE_CURVE('',#636,#634,#518,.T.);
#811=EDGE_CURVE('',#635,#636,#339,.T.);
#812=EDGE_CURVE('',#635,#573,#340,.T.);
#813=EDGE_CURVE('',#575,#636,#341,.T.);
#814=ORIENTED_EDGE('',*,*,#637,.F.);
#815=ORIENTED_EDGE('',*,*,#638,.T.);
#816=ORIENTED_EDGE('',*,*,#639,.T.);
#817=ORIENTED_EDGE('',*,*,#638,.F.);
#818=ORIENTED_EDGE('',*,*,#640,.F.);
#819=ORIENTED_EDGE('',*,*,#641,.T.);
#820=ORIENTED_EDGE('',*,*,#642,.T.);
#821=ORIENTED_EDGE('',*,*,#641,.F.);
#822=ORIENTED_EDGE('',*,*,#643,.F.);
#823=ORIENTED_EDGE('',*,*,#644,.T.);
#824=ORIENTED_EDGE('',*,*,#645,.T.);
#825=ORIENTED_EDGE('',*,*,#644,.F.);
#826=ORIENTED_EDGE('',*,*,#646,.F.);
#827=ORIENTED_EDGE('',*,*,#647,.T.);
#828=ORIENTED_EDGE('',*,*,#648,.T.);
#829=ORIENTED_EDGE('',*,*,#647,.F.);
#830=ORIENTED_EDGE('',*,*,#649,.F.);
#831=ORIENTED_EDGE('',*,*,#650,.T.);
#832=ORIENTED_EDGE('',*,*,#651,.T.);
#833=ORIENTED_EDGE('',*,*,#650,.F.);
#834=ORIENTED_EDGE('',*,*,#652,.F.);
#835=ORIENTED_EDGE('',*,*,#653,.T.);
#836=ORIENTED_EDGE('',*,*,#654,.T.);
#837=ORIENTED_EDGE('',*,*,#653,.F.);
#838=ORIENTED_EDGE('',*,*,#655,.F.);
#839=ORIENTED_EDGE('',*,*,#656,.T.);
#840=ORIENTED_EDGE('',*,*,#657,.T.);
#841=ORIENTED_EDGE('',*,*,#656,.F.);
#842=ORIENTED_EDGE('',*,*,#658,.F.);
#843=ORIENTED_EDGE('',*,*,#659,.T.);
#844=ORIENTED_EDGE('',*,*,#660,.T.);
#845=ORIENTED_EDGE('',*,*,#659,.F.);
#846=ORIENTED_EDGE('',*,*,#661,.F.);
#847=ORIENTED_EDGE('',*,*,#662,.T.);
#848=ORIENTED_EDGE('',*,*,#663,.T.);
#849=ORIENTED_EDGE('',*,*,#662,.F.);
#850=ORIENTED_EDGE('',*,*,#664,.T.);
#851=ORIENTED_EDGE('',*,*,#665,.T.);
#852=ORIENTED_EDGE('',*,*,#666,.F.);
#853=ORIENTED_EDGE('',*,*,#667,.F.);
#854=ORIENTED_EDGE('',*,*,#668,.T.);
#855=ORIENTED_EDGE('',*,*,#667,.T.);
#856=ORIENTED_EDGE('',*,*,#669,.F.);
#857=ORIENTED_EDGE('',*,*,#670,.F.);
#858=ORIENTED_EDGE('',*,*,#671,.T.);
#859=ORIENTED_EDGE('',*,*,#670,.T.);
#860=ORIENTED_EDGE('',*,*,#672,.F.);
#861=ORIENTED_EDGE('',*,*,#673,.F.);
#862=ORIENTED_EDGE('',*,*,#674,.T.);
#863=ORIENTED_EDGE('',*,*,#673,.T.);
#864=ORIENTED_EDGE('',*,*,#675,.F.);
#865=ORIENTED_EDGE('',*,*,#665,.F.);
#866=ORIENTED_EDGE('',*,*,#676,.T.);
#867=ORIENTED_EDGE('',*,*,#677,.T.);
#868=ORIENTED_EDGE('',*,*,#678,.F.);
#869=ORIENTED_EDGE('',*,*,#679,.F.);
#870=ORIENTED_EDGE('',*,*,#680,.T.);
#871=ORIENTED_EDGE('',*,*,#679,.T.);
#872=ORIENTED_EDGE('',*,*,#681,.F.);
#873=ORIENTED_EDGE('',*,*,#682,.F.);
#874=ORIENTED_EDGE('',*,*,#683,.T.);
#875=ORIENTED_EDGE('',*,*,#682,.T.);
#876=ORIENTED_EDGE('',*,*,#684,.F.);
#877=ORIENTED_EDGE('',*,*,#685,.F.);
#878=ORIENTED_EDGE('',*,*,#686,.T.);
#879=ORIENTED_EDGE('',*,*,#685,.T.);
#880=ORIENTED_EDGE('',*,*,#687,.F.);
#881=ORIENTED_EDGE('',*,*,#677,.F.);
#882=ORIENTED_EDGE('',*,*,#688,.F.);
#883=ORIENTED_EDGE('',*,*,#689,.T.);
#884=ORIENTED_EDGE('',*,*,#690,.T.);
#885=ORIENTED_EDGE('',*,*,#689,.F.);
#886=ORIENTED_EDGE('',*,*,#691,.F.);
#887=ORIENTED_EDGE('',*,*,#692,.T.);
#888=ORIENTED_EDGE('',*,*,#693,.T.);
#889=ORIENTED_EDGE('',*,*,#692,.F.);
#890=ORIENTED_EDGE('',*,*,#694,.F.);
#891=ORIENTED_EDGE('',*,*,#695,.T.);
#892=ORIENTED_EDGE('',*,*,#696,.T.);
#893=ORIENTED_EDGE('',*,*,#695,.F.);
#894=ORIENTED_EDGE('',*,*,#697,.F.);
#895=ORIENTED_EDGE('',*,*,#698,.T.);
#896=ORIENTED_EDGE('',*,*,#699,.T.);
#897=ORIENTED_EDGE('',*,*,#698,.F.);
#898=ORIENTED_EDGE('',*,*,#700,.F.);
#899=ORIENTED_EDGE('',*,*,#701,.T.);
#900=ORIENTED_EDGE('',*,*,#702,.T.);
#901=ORIENTED_EDGE('',*,*,#701,.F.);
#902=ORIENTED_EDGE('',*,*,#703,.F.);
#903=ORIENTED_EDGE('',*,*,#704,.T.);
#904=ORIENTED_EDGE('',*,*,#705,.T.);
#905=ORIENTED_EDGE('',*,*,#704,.F.);
#906=ORIENTED_EDGE('',*,*,#706,.F.);
#907=ORIENTED_EDGE('',*,*,#707,.T.);
#908=ORIENTED_EDGE('',*,*,#708,.T.);
#909=ORIENTED_EDGE('',*,*,#707,.F.);
#910=ORIENTED_EDGE('',*,*,#709,.F.);
#911=ORIENTED_EDGE('',*,*,#710,.T.);
#912=ORIENTED_EDGE('',*,*,#711,.T.);
#913=ORIENTED_EDGE('',*,*,#710,.F.);
#914=ORIENTED_EDGE('',*,*,#712,.F.);
#915=ORIENTED_EDGE('',*,*,#713,.T.);
#916=ORIENTED_EDGE('',*,*,#714,.T.);
#917=ORIENTED_EDGE('',*,*,#713,.F.);
#918=ORIENTED_EDGE('',*,*,#715,.F.);
#919=ORIENTED_EDGE('',*,*,#716,.T.);
#920=ORIENTED_EDGE('',*,*,#717,.T.);
#921=ORIENTED_EDGE('',*,*,#716,.F.);
#922=ORIENTED_EDGE('',*,*,#718,.F.);
#923=ORIENTED_EDGE('',*,*,#719,.T.);
#924=ORIENTED_EDGE('',*,*,#720,.F.);
#925=ORIENTED_EDGE('',*,*,#721,.F.);
#926=ORIENTED_EDGE('',*,*,#722,.T.);
#927=ORIENTED_EDGE('',*,*,#721,.T.);
#928=ORIENTED_EDGE('',*,*,#723,.F.);
#929=ORIENTED_EDGE('',*,*,#724,.F.);
#930=ORIENTED_EDGE('',*,*,#725,.T.);
#931=ORIENTED_EDGE('',*,*,#724,.T.);
#932=ORIENTED_EDGE('',*,*,#726,.F.);
#933=ORIENTED_EDGE('',*,*,#727,.F.);
#934=ORIENTED_EDGE('',*,*,#728,.T.);
#935=ORIENTED_EDGE('',*,*,#727,.T.);
#936=ORIENTED_EDGE('',*,*,#729,.F.);
#937=ORIENTED_EDGE('',*,*,#730,.F.);
#938=ORIENTED_EDGE('',*,*,#731,.T.);
#939=ORIENTED_EDGE('',*,*,#730,.T.);
#940=ORIENTED_EDGE('',*,*,#732,.F.);
#941=ORIENTED_EDGE('',*,*,#733,.F.);
#942=ORIENTED_EDGE('',*,*,#734,.T.);
#943=ORIENTED_EDGE('',*,*,#733,.T.);
#944=ORIENTED_EDGE('',*,*,#735,.F.);
#945=ORIENTED_EDGE('',*,*,#736,.F.);
#946=ORIENTED_EDGE('',*,*,#737,.T.);
#947=ORIENTED_EDGE('',*,*,#736,.T.);
#948=ORIENTED_EDGE('',*,*,#738,.F.);
#949=ORIENTED_EDGE('',*,*,#739,.F.);
#950=ORIENTED_EDGE('',*,*,#740,.T.);
#951=ORIENTED_EDGE('',*,*,#739,.T.);
#952=ORIENTED_EDGE('',*,*,#741,.F.);
#953=ORIENTED_EDGE('',*,*,#742,.F.);
#954=ORIENTED_EDGE('',*,*,#743,.T.);
#955=ORIENTED_EDGE('',*,*,#742,.T.);
#956=ORIENTED_EDGE('',*,*,#744,.F.);
#957=ORIENTED_EDGE('',*,*,#745,.F.);
#958=ORIENTED_EDGE('',*,*,#746,.T.);
#959=ORIENTED_EDGE('',*,*,#745,.T.);
#960=ORIENTED_EDGE('',*,*,#747,.F.);
#961=ORIENTED_EDGE('',*,*,#748,.F.);
#962=ORIENTED_EDGE('',*,*,#749,.F.);
#963=ORIENTED_EDGE('',*,*,#748,.T.);
#964=ORIENTED_EDGE('',*,*,#750,.F.);
#965=ORIENTED_EDGE('',*,*,#751,.F.);
#966=ORIENTED_EDGE('',*,*,#752,.F.);
#967=ORIENTED_EDGE('',*,*,#751,.T.);
#968=ORIENTED_EDGE('',*,*,#753,.F.);
#969=ORIENTED_EDGE('',*,*,#754,.F.);
#970=ORIENTED_EDGE('',*,*,#755,.F.);
#971=ORIENTED_EDGE('',*,*,#754,.T.);
#972=ORIENTED_EDGE('',*,*,#756,.F.);
#973=ORIENTED_EDGE('',*,*,#757,.F.);
#974=ORIENTED_EDGE('',*,*,#758,.F.);
#975=ORIENTED_EDGE('',*,*,#757,.T.);
#976=ORIENTED_EDGE('',*,*,#759,.F.);
#977=ORIENTED_EDGE('',*,*,#760,.F.);
#978=ORIENTED_EDGE('',*,*,#761,.F.);
#979=ORIENTED_EDGE('',*,*,#760,.T.);
#980=ORIENTED_EDGE('',*,*,#762,.F.);
#981=ORIENTED_EDGE('',*,*,#763,.F.);
#982=ORIENTED_EDGE('',*,*,#764,.F.);
#983=ORIENTED_EDGE('',*,*,#763,.T.);
#984=ORIENTED_EDGE('',*,*,#765,.F.);
#985=ORIENTED_EDGE('',*,*,#766,.F.);
#986=ORIENTED_EDGE('',*,*,#767,.T.);
#987=ORIENTED_EDGE('',*,*,#766,.T.);
#988=ORIENTED_EDGE('',*,*,#768,.F.);
#989=ORIENTED_EDGE('',*,*,#769,.F.);
#990=ORIENTED_EDGE('',*,*,#770,.T.);
#991=ORIENTED_EDGE('',*,*,#769,.T.);
#992=ORIENTED_EDGE('',*,*,#771,.F.);
#993=ORIENTED_EDGE('',*,*,#772,.F.);
#994=ORIENTED_EDGE('',*,*,#773,.T.);
#995=ORIENTED_EDGE('',*,*,#772,.T.);
#996=ORIENTED_EDGE('',*,*,#774,.F.);
#997=ORIENTED_EDGE('',*,*,#775,.F.);
#998=ORIENTED_EDGE('',*,*,#776,.T.);
#999=ORIENTED_EDGE('',*,*,#775,.T.);
#1000=ORIENTED_EDGE('',*,*,#777,.F.);
#1001=ORIENTED_EDGE('',*,*,#778,.F.);
#1002=ORIENTED_EDGE('',*,*,#779,.T.);
#1003=ORIENTED_EDGE('',*,*,#778,.T.);
#1004=ORIENTED_EDGE('',*,*,#780,.F.);
#1005=ORIENTED_EDGE('',*,*,#781,.F.);
#1006=ORIENTED_EDGE('',*,*,#782,.T.);
#1007=ORIENTED_EDGE('',*,*,#781,.T.);
#1008=ORIENTED_EDGE('',*,*,#783,.F.);
#1009=ORIENTED_EDGE('',*,*,#784,.F.);
#1010=ORIENTED_EDGE('',*,*,#785,.T.);
#1011=ORIENTED_EDGE('',*,*,#784,.T.);
#1012=ORIENTED_EDGE('',*,*,#786,.F.);
#1013=ORIENTED_EDGE('',*,*,#787,.F.);
#1014=ORIENTED_EDGE('',*,*,#788,.F.);
#1015=ORIENTED_EDGE('',*,*,#787,.T.);
#1016=ORIENTED_EDGE('',*,*,#789,.F.);
#1017=ORIENTED_EDGE('',*,*,#790,.F.);
#1018=ORIENTED_EDGE('',*,*,#791,.F.);
#1019=ORIENTED_EDGE('',*,*,#790,.T.);
#1020=ORIENTED_EDGE('',*,*,#792,.F.);
#1021=ORIENTED_EDGE('',*,*,#793,.F.);
#1022=ORIENTED_EDGE('',*,*,#794,.F.);
#1023=ORIENTED_EDGE('',*,*,#793,.T.);
#1024=ORIENTED_EDGE('',*,*,#795,.F.);
#1025=ORIENTED_EDGE('',*,*,#796,.F.);
#1026=ORIENTED_EDGE('',*,*,#797,.T.);
#1027=ORIENTED_EDGE('',*,*,#796,.T.);
#1028=ORIENTED_EDGE('',*,*,#798,.F.);
#1029=ORIENTED_EDGE('',*,*,#799,.F.);
#1030=ORIENTED_EDGE('',*,*,#800,.T.);
#1031=ORIENTED_EDGE('',*,*,#799,.T.);
#1032=ORIENTED_EDGE('',*,*,#801,.F.);
#1033=ORIENTED_EDGE('',*,*,#802,.F.);
#1034=ORIENTED_EDGE('',*,*,#803,.F.);
#1035=ORIENTED_EDGE('',*,*,#802,.T.);
#1036=ORIENTED_EDGE('',*,*,#804,.F.);
#1037=ORIENTED_EDGE('',*,*,#805,.F.);
#1038=ORIENTED_EDGE('',*,*,#806,.F.);
#1039=ORIENTED_EDGE('',*,*,#805,.T.);
#1040=ORIENTED_EDGE('',*,*,#807,.F.);
#1041=ORIENTED_EDGE('',*,*,#808,.F.);
#1042=ORIENTED_EDGE('',*,*,#809,.T.);
#1043=ORIENTED_EDGE('',*,*,#808,.T.);
#1044=ORIENTED_EDGE('',*,*,#810,.F.);
#1045=ORIENTED_EDGE('',*,*,#811,.F.);
#1046=ORIENTED_EDGE('',*,*,#812,.F.);
#1047=ORIENTED_EDGE('',*,*,#811,.T.);
#1048=ORIENTED_EDGE('',*,*,#813,.F.);
#1049=ORIENTED_EDGE('',*,*,#719,.F.);
#1050=ORIENTED_EDGE('',*,*,#813,.T.);
#1051=ORIENTED_EDGE('',*,*,#810,.T.);
#1052=ORIENTED_EDGE('',*,*,#807,.T.);
#1053=ORIENTED_EDGE('',*,*,#804,.T.);
#1054=ORIENTED_EDGE('',*,*,#801,.T.);
#1055=ORIENTED_EDGE('',*,*,#798,.T.);
#1056=ORIENTED_EDGE('',*,*,#795,.T.);
#1057=ORIENTED_EDGE('',*,*,#792,.T.);
#1058=ORIENTED_EDGE('',*,*,#789,.T.);
#1059=ORIENTED_EDGE('',*,*,#786,.T.);
#1060=ORIENTED_EDGE('',*,*,#783,.T.);
#1061=ORIENTED_EDGE('',*,*,#780,.T.);
#1062=ORIENTED_EDGE('',*,*,#777,.T.);
#1063=ORIENTED_EDGE('',*,*,#774,.T.);
#1064=ORIENTED_EDGE('',*,*,#771,.T.);
#1065=ORIENTED_EDGE('',*,*,#768,.T.);
#1066=ORIENTED_EDGE('',*,*,#765,.T.);
#1067=ORIENTED_EDGE('',*,*,#762,.T.);
#1068=ORIENTED_EDGE('',*,*,#759,.T.);
#1069=ORIENTED_EDGE('',*,*,#756,.T.);
#1070=ORIENTED_EDGE('',*,*,#753,.T.);
#1071=ORIENTED_EDGE('',*,*,#750,.T.);
#1072=ORIENTED_EDGE('',*,*,#747,.T.);
#1073=ORIENTED_EDGE('',*,*,#744,.T.);
#1074=ORIENTED_EDGE('',*,*,#741,.T.);
#1075=ORIENTED_EDGE('',*,*,#738,.T.);
#1076=ORIENTED_EDGE('',*,*,#735,.T.);
#1077=ORIENTED_EDGE('',*,*,#732,.T.);
#1078=ORIENTED_EDGE('',*,*,#729,.T.);
#1079=ORIENTED_EDGE('',*,*,#726,.T.);
#1080=ORIENTED_EDGE('',*,*,#723,.T.);
#1081=ORIENTED_EDGE('',*,*,#720,.T.);
#1082=ORIENTED_EDGE('',*,*,#715,.T.);
#1083=ORIENTED_EDGE('',*,*,#712,.T.);
#1084=ORIENTED_EDGE('',*,*,#709,.T.);
#1085=ORIENTED_EDGE('',*,*,#706,.T.);
#1086=ORIENTED_EDGE('',*,*,#703,.T.);
#1087=ORIENTED_EDGE('',*,*,#700,.T.);
#1088=ORIENTED_EDGE('',*,*,#697,.T.);
#1089=ORIENTED_EDGE('',*,*,#694,.T.);
#1090=ORIENTED_EDGE('',*,*,#691,.T.);
#1091=ORIENTED_EDGE('',*,*,#688,.T.);
#1092=ORIENTED_EDGE('',*,*,#687,.T.);
#1093=ORIENTED_EDGE('',*,*,#684,.T.);
#1094=ORIENTED_EDGE('',*,*,#681,.T.);
#1095=ORIENTED_EDGE('',*,*,#678,.T.);
#1096=ORIENTED_EDGE('',*,*,#675,.T.);
#1097=ORIENTED_EDGE('',*,*,#672,.T.);
#1098=ORIENTED_EDGE('',*,*,#669,.T.);
#1099=ORIENTED_EDGE('',*,*,#666,.T.);
#1100=ORIENTED_EDGE('',*,*,#661,.T.);
#1101=ORIENTED_EDGE('',*,*,#658,.T.);
#1102=ORIENTED_EDGE('',*,*,#655,.T.);
#1103=ORIENTED_EDGE('',*,*,#652,.T.);
#1104=ORIENTED_EDGE('',*,*,#649,.T.);
#1105=ORIENTED_EDGE('',*,*,#646,.T.);
#1106=ORIENTED_EDGE('',*,*,#643,.T.);
#1107=ORIENTED_EDGE('',*,*,#640,.T.);
#1108=ORIENTED_EDGE('',*,*,#637,.T.);
#1109=ORIENTED_EDGE('',*,*,#812,.T.);
#1110=ORIENTED_EDGE('',*,*,#718,.T.);
#1111=ORIENTED_EDGE('',*,*,#722,.F.);
#1112=ORIENTED_EDGE('',*,*,#725,.F.);
#1113=ORIENTED_EDGE('',*,*,#728,.F.);
#1114=ORIENTED_EDGE('',*,*,#731,.F.);
#1115=ORIENTED_EDGE('',*,*,#734,.F.);
#1116=ORIENTED_EDGE('',*,*,#737,.F.);
#1117=ORIENTED_EDGE('',*,*,#740,.F.);
#1118=ORIENTED_EDGE('',*,*,#743,.F.);
#1119=ORIENTED_EDGE('',*,*,#746,.F.);
#1120=ORIENTED_EDGE('',*,*,#749,.T.);
#1121=ORIENTED_EDGE('',*,*,#752,.T.);
#1122=ORIENTED_EDGE('',*,*,#755,.T.);
#1123=ORIENTED_EDGE('',*,*,#758,.T.);
#1124=ORIENTED_EDGE('',*,*,#761,.T.);
#1125=ORIENTED_EDGE('',*,*,#764,.T.);
#1126=ORIENTED_EDGE('',*,*,#767,.F.);
#1127=ORIENTED_EDGE('',*,*,#770,.F.);
#1128=ORIENTED_EDGE('',*,*,#773,.F.);
#1129=ORIENTED_EDGE('',*,*,#776,.F.);
#1130=ORIENTED_EDGE('',*,*,#779,.F.);
#1131=ORIENTED_EDGE('',*,*,#782,.F.);
#1132=ORIENTED_EDGE('',*,*,#785,.F.);
#1133=ORIENTED_EDGE('',*,*,#788,.T.);
#1134=ORIENTED_EDGE('',*,*,#791,.T.);
#1135=ORIENTED_EDGE('',*,*,#794,.T.);
#1136=ORIENTED_EDGE('',*,*,#797,.F.);
#1137=ORIENTED_EDGE('',*,*,#800,.F.);
#1138=ORIENTED_EDGE('',*,*,#803,.T.);
#1139=ORIENTED_EDGE('',*,*,#806,.T.);
#1140=ORIENTED_EDGE('',*,*,#809,.F.);
#1141=ORIENTED_EDGE('',*,*,#717,.F.);
#1142=ORIENTED_EDGE('',*,*,#714,.F.);
#1143=ORIENTED_EDGE('',*,*,#711,.F.);
#1144=ORIENTED_EDGE('',*,*,#708,.F.);
#1145=ORIENTED_EDGE('',*,*,#705,.F.);
#1146=ORIENTED_EDGE('',*,*,#702,.F.);
#1147=ORIENTED_EDGE('',*,*,#699,.F.);
#1148=ORIENTED_EDGE('',*,*,#696,.F.);
#1149=ORIENTED_EDGE('',*,*,#693,.F.);
#1150=ORIENTED_EDGE('',*,*,#690,.F.);
#1151=ORIENTED_EDGE('',*,*,#686,.F.);
#1152=ORIENTED_EDGE('',*,*,#676,.F.);
#1153=ORIENTED_EDGE('',*,*,#680,.F.);
#1154=ORIENTED_EDGE('',*,*,#683,.F.);
#1155=ORIENTED_EDGE('',*,*,#674,.F.);
#1156=ORIENTED_EDGE('',*,*,#664,.F.);
#1157=ORIENTED_EDGE('',*,*,#668,.F.);
#1158=ORIENTED_EDGE('',*,*,#671,.F.);
#1159=ORIENTED_EDGE('',*,*,#663,.F.);
#1160=ORIENTED_EDGE('',*,*,#660,.F.);
#1161=ORIENTED_EDGE('',*,*,#657,.F.);
#1162=ORIENTED_EDGE('',*,*,#654,.F.);
#1163=ORIENTED_EDGE('',*,*,#651,.F.);
#1164=ORIENTED_EDGE('',*,*,#648,.F.);
#1165=ORIENTED_EDGE('',*,*,#645,.F.);
#1166=ORIENTED_EDGE('',*,*,#642,.F.);
#1167=ORIENTED_EDGE('',*,*,#639,.F.);
#1168=CYLINDRICAL_SURFACE('',#1282,5.);
#1169=CYLINDRICAL_SURFACE('',#1285,2.99999999999997);
#1170=CYLINDRICAL_SURFACE('',#1288,2.99999999999997);
#1171=CYLINDRICAL_SURFACE('',#1291,3.99999999999999);
#1172=CYLINDRICAL_SURFACE('',#1294,4.00000000000006);
#1173=CYLINDRICAL_SURFACE('',#1297,4.00000000000006);
#1174=CYLINDRICAL_SURFACE('',#1300,3.99999999999999);
#1175=CYLINDRICAL_SURFACE('',#1303,3.99999999999999);
#1176=CYLINDRICAL_SURFACE('',#1306,4.);
#1177=CYLINDRICAL_SURFACE('',#1310,33.);
#1178=CYLINDRICAL_SURFACE('',#1314,33.);
#1179=CYLINDRICAL_SURFACE('',#1318,33.);
#1180=CYLINDRICAL_SURFACE('',#1322,33.);
#1181=CYLINDRICAL_SURFACE('',#1325,3.99999999999999);
#1182=CYLINDRICAL_SURFACE('',#1328,3.99999999999999);
#1183=CYLINDRICAL_SURFACE('',#1331,3.99999999999999);
#1184=CYLINDRICAL_SURFACE('',#1334,3.99999999999999);
#1185=CYLINDRICAL_SURFACE('',#1337,3.99999999999999);
#1186=CYLINDRICAL_SURFACE('',#1340,3.40000000000003);
#1187=CYLINDRICAL_SURFACE('',#1343,3.40000000000003);
#1188=CYLINDRICAL_SURFACE('',#1346,2.99999999999999);
#1189=CYLINDRICAL_SURFACE('',#1349,5.);
#1190=CYLINDRICAL_SURFACE('',#1352,3.00000000000001);
#1191=CYLINDRICAL_SURFACE('',#1355,40.);
#1192=CYLINDRICAL_SURFACE('',#1359,20.);
#1193=CYLINDRICAL_SURFACE('',#1363,2.99999999999997);
#1194=CYLINDRICAL_SURFACE('',#1367,2.99999999999997);
#1195=CYLINDRICAL_SURFACE('',#1371,20.0000000000003);
#1196=CYLINDRICAL_SURFACE('',#1375,20.0000000000005);
#1197=CYLINDRICAL_SURFACE('',#1379,20.0000000000023);
#1198=CYLINDRICAL_SURFACE('',#1383,20.);
#1199=CYLINDRICAL_SURFACE('',#1387,20.);
#1200=CYLINDRICAL_SURFACE('',#1391,3.);
#1201=CYLINDRICAL_SURFACE('',#1395,3.00000000000011);
#1202=CYLINDRICAL_SURFACE('',#1399,20.);
#1203=CYLINDRICAL_SURFACE('',#1403,40.);
#1204=CYLINDRICAL_SURFACE('',#1407,3.00000000000001);
#1205=CYLINDRICAL_SURFACE('',#1411,16.);
#1206=CYLINDRICAL_SURFACE('',#1415,3.00000000000004);
#1207=ADVANCED_FACE('',(#79),#1168,.F.);
#1208=ADVANCED_FACE('',(#80),#1169,.F.);
#1209=ADVANCED_FACE('',(#81),#1170,.F.);
#1210=ADVANCED_FACE('',(#82),#1171,.F.);
#1211=ADVANCED_FACE('',(#83),#1172,.F.);
#1212=ADVANCED_FACE('',(#84),#1173,.F.);
#1213=ADVANCED_FACE('',(#85),#1174,.F.);
#1214=ADVANCED_FACE('',(#86),#1175,.F.);
#1215=ADVANCED_FACE('',(#87),#1176,.F.);
#1216=ADVANCED_FACE('',(#88),#57,.T.);
#1217=ADVANCED_FACE('',(#89),#1177,.F.);
#1218=ADVANCED_FACE('',(#90),#58,.T.);
#1219=ADVANCED_FACE('',(#91),#1178,.F.);
#1220=ADVANCED_FACE('',(#92),#59,.T.);
#1221=ADVANCED_FACE('',(#93),#1179,.F.);
#1222=ADVANCED_FACE('',(#94),#60,.T.);
#1223=ADVANCED_FACE('',(#95),#1180,.F.);
#1224=ADVANCED_FACE('',(#96),#1181,.F.);
#1225=ADVANCED_FACE('',(#97),#1182,.F.);
#1226=ADVANCED_FACE('',(#98),#1183,.F.);
#1227=ADVANCED_FACE('',(#99),#1184,.F.);
#1228=ADVANCED_FACE('',(#100),#1185,.F.);
#1229=ADVANCED_FACE('',(#101),#1186,.F.);
#1230=ADVANCED_FACE('',(#102),#1187,.F.);
#1231=ADVANCED_FACE('',(#103),#1188,.F.);
#1232=ADVANCED_FACE('',(#104),#1189,.F.);
#1233=ADVANCED_FACE('',(#105),#1190,.F.);
#1234=ADVANCED_FACE('',(#106),#1191,.F.);
#1235=ADVANCED_FACE('',(#107),#61,.T.);
#1236=ADVANCED_FACE('',(#108),#1192,.T.);
#1237=ADVANCED_FACE('',(#109),#62,.T.);
#1238=ADVANCED_FACE('',(#110),#1193,.T.);
#1239=ADVANCED_FACE('',(#111),#63,.T.);
#1240=ADVANCED_FACE('',(#112),#1194,.T.);
#1241=ADVANCED_FACE('',(#113),#64,.T.);
#1242=ADVANCED_FACE('',(#114),#1195,.T.);
#1243=ADVANCED_FACE('',(#115),#65,.T.);
#1244=ADVANCED_FACE('',(#116),#1196,.F.);
#1245=ADVANCED_FACE('',(#117),#66,.T.);
#1246=ADVANCED_FACE('',(#118),#1197,.F.);
#1247=ADVANCED_FACE('',(#119),#67,.T.);
#1248=ADVANCED_FACE('',(#120),#1198,.F.);
#1249=ADVANCED_FACE('',(#121),#68,.T.);
#1250=ADVANCED_FACE('',(#122),#1199,.T.);
#1251=ADVANCED_FACE('',(#123),#69,.T.);
#1252=ADVANCED_FACE('',(#124),#1200,.T.);
#1253=ADVANCED_FACE('',(#125),#70,.T.);
#1254=ADVANCED_FACE('',(#126),#1201,.T.);
#1255=ADVANCED_FACE('',(#127),#71,.T.);
#1256=ADVANCED_FACE('',(#128),#1202,.T.);
#1257=ADVANCED_FACE('',(#129),#72,.T.);
#1258=ADVANCED_FACE('',(#130),#1203,.F.);
#1259=ADVANCED_FACE('',(#131),#73,.T.);
#1260=ADVANCED_FACE('',(#132),#1204,.T.);
#1261=ADVANCED_FACE('',(#133),#74,.T.);
#1262=ADVANCED_FACE('',(#134),#1205,.F.);
#1263=ADVANCED_FACE('',(#135),#75,.T.);
#1264=ADVANCED_FACE('',(#136),#1206,.T.);
#1265=ADVANCED_FACE('',(#137),#76,.T.);
#1266=ADVANCED_FACE('',(#138,#15,#16,#17,#18,#19,#20,#21,#22,#23,#24,#25,
#26,#27,#28,#29,#30,#31,#32,#33,#34,#35),#77,.T.);
#1267=ADVANCED_FACE('',(#139,#36,#37,#38,#39,#40,#41,#42,#43,#44,#45,#46,
#47,#48,#49,#50,#51,#52,#53,#54,#55,#56),#78,.F.);
#1268=CLOSED_SHELL('',(#1207,#1208,#1209,#1210,#1211,#1212,#1213,#1214,
#1215,#1216,#1217,#1218,#1219,#1220,#1221,#1222,#1223,#1224,#1225,#1226,
#1227,#1228,#1229,#1230,#1231,#1232,#1233,#1234,#1235,#1236,#1237,#1238,
#1239,#1240,#1241,#1242,#1243,#1244,#1245,#1246,#1247,#1248,#1249,#1250,
#1251,#1252,#1253,#1254,#1255,#1256,#1257,#1258,#1259,#1260,#1261,#1262,
#1263,#1264,#1265,#1266,#1267));
#1269=DERIVED_UNIT_ELEMENT(#1271,1.);
#1270=DERIVED_UNIT_ELEMENT(#2162,-3.);
#1271=(
MASS_UNIT()
NAMED_UNIT(*)
SI_UNIT(.KILO.,.GRAM.)
);
#1272=DERIVED_UNIT((#1269,#1270));
#1273=MEASURE_REPRESENTATION_ITEM('density measure',
POSITIVE_RATIO_MEASURE(7850.),#1272);
#1274=PROPERTY_DEFINITION_REPRESENTATION(#1279,#1276);
#1275=PROPERTY_DEFINITION_REPRESENTATION(#1280,#1277);
#1276=REPRESENTATION('material name',(#1278),#2159);
#1277=REPRESENTATION('density',(#1273),#2159);
#1278=DESCRIPTIVE_REPRESENTATION_ITEM('Steel','Steel');
#1279=PROPERTY_DEFINITION('material property','material name',#2169);
#1280=PROPERTY_DEFINITION('material property','density of part',#2169);
#1281=AXIS2_PLACEMENT_3D('placement',#1800,#1421,#1422);
#1282=AXIS2_PLACEMENT_3D('',#1801,#1423,#1424);
#1283=AXIS2_PLACEMENT_3D('',#1803,#1425,#1426);
#1284=AXIS2_PLACEMENT_3D('',#1806,#1428,#1429);
#1285=AXIS2_PLACEMENT_3D('',#1807,#1430,#1431);
#1286=AXIS2_PLACEMENT_3D('',#1809,#1432,#1433);
#1287=AXIS2_PLACEMENT_3D('',#1812,#1435,#1436);
#1288=AXIS2_PLACEMENT_3D('',#1813,#1437,#1438);
#1289=AXIS2_PLACEMENT_3D('',#1815,#1439,#1440);
#1290=AXIS2_PLACEMENT_3D('',#1818,#1442,#1443);
#1291=AXIS2_PLACEMENT_3D('',#1819,#1444,#1445);
#1292=AXIS2_PLACEMENT_3D('',#1821,#1446,#1447);
#1293=AXIS2_PLACEMENT_3D('',#1824,#1449,#1450);
#1294=AXIS2_PLACEMENT_3D('',#1825,#1451,#1452);
#1295=AXIS2_PLACEMENT_3D('',#1827,#1453,#1454);
#1296=AXIS2_PLACEMENT_3D('',#1830,#1456,#1457);
#1297=AXIS2_PLACEMENT_3D('',#1831,#1458,#1459);
#1298=AXIS2_PLACEMENT_3D('',#1833,#1460,#1461);
#1299=AXIS2_PLACEMENT_3D('',#1836,#1463,#1464);
#1300=AXIS2_PLACEMENT_3D('',#1837,#1465,#1466);
#1301=AXIS2_PLACEMENT_3D('',#1839,#1467,#1468);
#1302=AXIS2_PLACEMENT_3D('',#1842,#1470,#1471);
#1303=AXIS2_PLACEMENT_3D('',#1843,#1472,#1473);
#1304=AXIS2_PLACEMENT_3D('',#1845,#1474,#1475);
#1305=AXIS2_PLACEMENT_3D('',#1848,#1477,#1478);
#1306=AXIS2_PLACEMENT_3D('',#1849,#1479,#1480);
#1307=AXIS2_PLACEMENT_3D('',#1851,#1481,#1482);
#1308=AXIS2_PLACEMENT_3D('',#1854,#1484,#1485);
#1309=AXIS2_PLACEMENT_3D('',#1855,#1486,#1487);
#1310=AXIS2_PLACEMENT_3D('',#1864,#1492,#1493);
#1311=AXIS2_PLACEMENT_3D('',#1866,#1494,#1495);
#1312=AXIS2_PLACEMENT_3D('',#1868,#1496,#1497);
#1313=AXIS2_PLACEMENT_3D('',#1870,#1499,#1500);
#1314=AXIS2_PLACEMENT_3D('',#1876,#1504,#1505);
#1315=AXIS2_PLACEMENT_3D('',#1877,#1506,#1507);
#1316=AXIS2_PLACEMENT_3D('',#1878,#1508,#1509);
#1317=AXIS2_PLACEMENT_3D('',#1879,#1510,#1511);
#1318=AXIS2_PLACEMENT_3D('',#1888,#1516,#1517);
#1319=AXIS2_PLACEMENT_3D('',#1890,#1518,#1519);
#1320=AXIS2_PLACEMENT_3D('',#1892,#1520,#1521);
#1321=AXIS2_PLACEMENT_3D('',#1894,#1523,#1524);
#1322=AXIS2_PLACEMENT_3D('',#1900,#1528,#1529);
#1323=AXIS2_PLACEMENT_3D('',#1901,#1530,#1531);
#1324=AXIS2_PLACEMENT_3D('',#1902,#1532,#1533);
#1325=AXIS2_PLACEMENT_3D('',#1903,#1534,#1535);
#1326=AXIS2_PLACEMENT_3D('',#1905,#1536,#1537);
#1327=AXIS2_PLACEMENT_3D('',#1908,#1539,#1540);
#1328=AXIS2_PLACEMENT_3D('',#1909,#1541,#1542);
#1329=AXIS2_PLACEMENT_3D('',#1911,#1543,#1544);
#1330=AXIS2_PLACEMENT_3D('',#1914,#1546,#1547);
#1331=AXIS2_PLACEMENT_3D('',#1915,#1548,#1549);
#1332=AXIS2_PLACEMENT_3D('',#1917,#1550,#1551);
#1333=AXIS2_PLACEMENT_3D('',#1920,#1553,#1554);
#1334=AXIS2_PLACEMENT_3D('',#1921,#1555,#1556);
#1335=AXIS2_PLACEMENT_3D('',#1923,#1557,#1558);
#1336=AXIS2_PLACEMENT_3D('',#1926,#1560,#1561);
#1337=AXIS2_PLACEMENT_3D('',#1927,#1562,#1563);
#1338=AXIS2_PLACEMENT_3D('',#1929,#1564,#1565);
#1339=AXIS2_PLACEMENT_3D('',#1932,#1567,#1568);
#1340=AXIS2_PLACEMENT_3D('',#1933,#1569,#1570);
#1341=AXIS2_PLACEMENT_3D('',#1935,#1571,#1572);
#1342=AXIS2_PLACEMENT_3D('',#1938,#1574,#1575);
#1343=AXIS2_PLACEMENT_3D('',#1939,#1576,#1577);
#1344=AXIS2_PLACEMENT_3D('',#1941,#1578,#1579);
#1345=AXIS2_PLACEMENT_3D('',#1944,#1581,#1582);
#1346=AXIS2_PLACEMENT_3D('',#1945,#1583,#1584);
#1347=AXIS2_PLACEMENT_3D('',#1947,#1585,#1586);
#1348=AXIS2_PLACEMENT_3D('',#1950,#1588,#1589);
#1349=AXIS2_PLACEMENT_3D('',#1951,#1590,#1591);
#1350=AXIS2_PLACEMENT_3D('',#1953,#1592,#1593);
#1351=AXIS2_PLACEMENT_3D('',#1956,#1595,#1596);
#1352=AXIS2_PLACEMENT_3D('',#1957,#1597,#1598);
#1353=AXIS2_PLACEMENT_3D('',#1959,#1599,#1600);
#1354=AXIS2_PLACEMENT_3D('',#1962,#1602,#1603);
#1355=AXIS2_PLACEMENT_3D('',#1963,#1604,#1605);
#1356=AXIS2_PLACEMENT_3D('',#1966,#1606,#1607);
#1357=AXIS2_PLACEMENT_3D('',#1970,#1609,#1610);
#1358=AXIS2_PLACEMENT_3D('',#1972,#1612,#1613);
#1359=AXIS2_PLACEMENT_3D('',#1978,#1617,#1618);
#1360=AXIS2_PLACEMENT_3D('',#1980,#1619,#1620);
#1361=AXIS2_PLACEMENT_3D('',#1982,#1621,#1622);
#1362=AXIS2_PLACEMENT_3D('',#1984,#1624,#1625);
#1363=AXIS2_PLACEMENT_3D('',#1990,#1629,#1630);
#1364=AXIS2_PLACEMENT_3D('',#1992,#1631,#1632);
#1365=AXIS2_PLACEMENT_3D('',#1994,#1633,#1634);
#1366=AXIS2_PLACEMENT_3D('',#1996,#1636,#1637);
#1367=AXIS2_PLACEMENT_3D('',#2002,#1641,#1642);
#1368=AXIS2_PLACEMENT_3D('',#2004,#1643,#1644);
#1369=AXIS2_PLACEMENT_3D('',#2006,#1645,#1646);
#1370=AXIS2_PLACEMENT_3D('',#2008,#1648,#1649);
#1371=AXIS2_PLACEMENT_3D('',#2014,#1653,#1654);
#1372=AXIS2_PLACEMENT_3D('',#2016,#1655,#1656);
#1373=AXIS2_PLACEMENT_3D('',#2018,#1657,#1658);
#1374=AXIS2_PLACEMENT_3D('',#2020,#1660,#1661);
#1375=AXIS2_PLACEMENT_3D('',#2026,#1665,#1666);
#1376=AXIS2_PLACEMENT_3D('',#2028,#1667,#1668);
#1377=AXIS2_PLACEMENT_3D('',#2030,#1669,#1670);
#1378=AXIS2_PLACEMENT_3D('',#2032,#1672,#1673);
#1379=AXIS2_PLACEMENT_3D('',#2038,#1677,#1678);
#1380=AXIS2_PLACEMENT_3D('',#2040,#1679,#1680);
#1381=AXIS2_PLACEMENT_3D('',#2042,#1681,#1682);
#1382=AXIS2_PLACEMENT_3D('',#2044,#1684,#1685);
#1383=AXIS2_PLACEMENT_3D('',#2050,#1689,#1690);
#1384=AXIS2_PLACEMENT_3D('',#2052,#1691,#1692);
#1385=AXIS2_PLACEMENT_3D('',#2054,#1693,#1694);
#1386=AXIS2_PLACEMENT_3D('',#2056,#1696,#1697);
#1387=AXIS2_PLACEMENT_3D('',#2062,#1701,#1702);
#1388=AXIS2_PLACEMENT_3D('',#2064,#1703,#1704);
#1389=AXIS2_PLACEMENT_3D('',#2066,#1705,#1706);
#1390=AXIS2_PLACEMENT_3D('',#2068,#1708,#1709);
#1391=AXIS2_PLACEMENT_3D('',#2074,#1713,#1714);
#1392=AXIS2_PLACEMENT_3D('',#2076,#1715,#1716);
#1393=AXIS2_PLACEMENT_3D('',#2078,#1717,#1718);
#1394=AXIS2_PLACEMENT_3D('',#2080,#1720,#1721);
#1395=AXIS2_PLACEMENT_3D('',#2086,#1725,#1726);
#1396=AXIS2_PLACEMENT_3D('',#2088,#1727,#1728);
#1397=AXIS2_PLACEMENT_3D('',#2090,#1729,#1730);
#1398=AXIS2_PLACEMENT_3D('',#2092,#1732,#1733);
#1399=AXIS2_PLACEMENT_3D('',#2098,#1737,#1738);
#1400=AXIS2_PLACEMENT_3D('',#2100,#1739,#1740);
#1401=AXIS2_PLACEMENT_3D('',#2102,#1741,#1742);
#1402=AXIS2_PLACEMENT_3D('',#2104,#1744,#1745);
#1403=AXIS2_PLACEMENT_3D('',#2110,#1749,#1750);
#1404=AXIS2_PLACEMENT_3D('',#2112,#1751,#1752);
#1405=AXIS2_PLACEMENT_3D('',#2114,#1753,#1754);
#1406=AXIS2_PLACEMENT_3D('',#2116,#1756,#1757);
#1407=AXIS2_PLACEMENT_3D('',#2122,#1761,#1762);
#1408=AXIS2_PLACEMENT_3D('',#2124,#1763,#1764);
#1409=AXIS2_PLACEMENT_3D('',#2126,#1765,#1766);
#1410=AXIS2_PLACEMENT_3D('',#2128,#1768,#1769);
#1411=AXIS2_PLACEMENT_3D('',#2134,#1773,#1774);
#1412=AXIS2_PLACEMENT_3D('',#2136,#1775,#1776);
#1413=AXIS2_PLACEMENT_3D('',#2138,#1777,#1778);
#1414=AXIS2_PLACEMENT_3D('',#2140,#1780,#1781);
#1415=AXIS2_PLACEMENT_3D('',#2146,#1785,#1786);
#1416=AXIS2_PLACEMENT_3D('',#2148,#1787,#1788);
#1417=AXIS2_PLACEMENT_3D('',#2150,#1789,#1790);
#1418=AXIS2_PLACEMENT_3D('',#2152,#1792,#1793);
#1419=AXIS2_PLACEMENT_3D('',#2155,#1796,#1797);
#1420=AXIS2_PLACEMENT_3D('',#2156,#1798,#1799);
#1421=DIRECTION('axis',(0.,0.,1.));
#1422=DIRECTION('refdir',(1.,0.,0.));
#1423=DIRECTION('center_axis',(0.,0.,1.));
#1424=DIRECTION('ref_axis',(1.,0.,0.));
#1425=DIRECTION('center_axis',(0.,0.,-1.));
#1426=DIRECTION('ref_axis',(1.,0.,0.));
#1427=DIRECTION('',(0.,0.,-1.));
#1428=DIRECTION('center_axis',(0.,0.,-1.));
#1429=DIRECTION('ref_axis',(1.,0.,0.));
#1430=DIRECTION('center_axis',(0.,0.,1.));
#1431=DIRECTION('ref_axis',(1.,0.,0.));
#1432=DIRECTION('center_axis',(0.,0.,-1.));
#1433=DIRECTION('ref_axis',(1.,0.,0.));
#1434=DIRECTION('',(0.,0.,-1.));
#1435=DIRECTION('center_axis',(0.,0.,-1.));
#1436=DIRECTION('ref_axis',(1.,0.,0.));
#1437=DIRECTION('center_axis',(0.,0.,1.));
#1438=DIRECTION('ref_axis',(1.,0.,0.));
#1439=DIRECTION('center_axis',(0.,0.,-1.));
#1440=DIRECTION('ref_axis',(1.,0.,0.));
#1441=DIRECTION('',(0.,0.,-1.));
#1442=DIRECTION('center_axis',(0.,0.,-1.));
#1443=DIRECTION('ref_axis',(1.,0.,0.));
#1444=DIRECTION('center_axis',(0.,0.,1.));
#1445=DIRECTION('ref_axis',(1.,0.,0.));
#1446=DIRECTION('center_axis',(0.,0.,-1.));
#1447=DIRECTION('ref_axis',(1.,0.,0.));
#1448=DIRECTION('',(0.,0.,-1.));
#1449=DIRECTION('center_axis',(0.,0.,-1.));
#1450=DIRECTION('ref_axis',(1.,0.,0.));
#1451=DIRECTION('center_axis',(0.,0.,1.));
#1452=DIRECTION('ref_axis',(1.,0.,0.));
#1453=DIRECTION('center_axis',(0.,0.,-1.));
#1454=DIRECTION('ref_axis',(1.,0.,0.));
#1455=DIRECTION('',(0.,0.,-1.));
#1456=DIRECTION('center_axis',(0.,0.,-1.));
#1457=DIRECTION('ref_axis',(1.,0.,0.));
#1458=DIRECTION('center_axis',(0.,0.,1.));
#1459=DIRECTION('ref_axis',(1.,0.,0.));
#1460=DIRECTION('center_axis',(0.,0.,-1.));
#1461=DIRECTION('ref_axis',(1.,0.,0.));
#1462=DIRECTION('',(0.,0.,-1.));
#1463=DIRECTION('center_axis',(0.,0.,-1.));
#1464=DIRECTION('ref_axis',(1.,0.,0.));
#1465=DIRECTION('center_axis',(0.,0.,1.));
#1466=DIRECTION('ref_axis',(1.,0.,0.));
#1467=DIRECTION('center_axis',(0.,0.,-1.));
#1468=DIRECTION('ref_axis',(1.,0.,0.));
#1469=DIRECTION('',(0.,0.,-1.));
#1470=DIRECTION('center_axis',(0.,0.,-1.));
#1471=DIRECTION('ref_axis',(1.,0.,0.));
#1472=DIRECTION('center_axis',(0.,0.,1.));
#1473=DIRECTION('ref_axis',(1.,0.,0.));
#1474=DIRECTION('center_axis',(0.,0.,-1.));
#1475=DIRECTION('ref_axis',(1.,0.,0.));
#1476=DIRECTION('',(0.,0.,-1.));
#1477=DIRECTION('center_axis',(0.,0.,-1.));
#1478=DIRECTION('ref_axis',(1.,0.,0.));
#1479=DIRECTION('center_axis',(0.,0.,1.));
#1480=DIRECTION('ref_axis',(1.,0.,0.));
#1481=DIRECTION('center_axis',(0.,0.,-1.));
#1482=DIRECTION('ref_axis',(1.,0.,0.));
#1483=DIRECTION('',(0.,0.,-1.));
#1484=DIRECTION('center_axis',(0.,0.,-1.));
#1485=DIRECTION('ref_axis',(1.,0.,0.));
#1486=DIRECTION('center_axis',(0.,1.,0.));
#1487=DIRECTION('ref_axis',(-1.,0.,0.));
#1488=DIRECTION('',(-1.,0.,0.));
#1489=DIRECTION('',(0.,0.,1.));
#1490=DIRECTION('',(-1.,0.,0.));
#1491=DIRECTION('',(0.,0.,1.));
#1492=DIRECTION('center_axis',(0.,0.,1.));
#1493=DIRECTION('ref_axis',(-1.66869884913357E-14,-1.,0.));
#1494=DIRECTION('center_axis',(0.,0.,-1.));
#1495=DIRECTION('ref_axis',(-1.66869884913357E-14,-1.,0.));
#1496=DIRECTION('center_axis',(0.,0.,-1.));
#1497=DIRECTION('ref_axis',(-1.66869884913357E-14,-1.,0.));
#1498=DIRECTION('',(0.,0.,1.));
#1499=DIRECTION('center_axis',(0.,-1.,0.));
#1500=DIRECTION('ref_axis',(1.,0.,0.));
#1501=DIRECTION('',(1.,0.,0.));
#1502=DIRECTION('',(1.,0.,0.));
#1503=DIRECTION('',(0.,0.,1.));
#1504=DIRECTION('center_axis',(0.,0.,1.));
#1505=DIRECTION('ref_axis',(0.,1.,0.));
#1506=DIRECTION('center_axis',(0.,0.,-1.));
#1507=DIRECTION('ref_axis',(0.,1.,0.));
#1508=DIRECTION('center_axis',(0.,0.,-1.));
#1509=DIRECTION('ref_axis',(0.,1.,0.));
#1510=DIRECTION('center_axis',(-1.,0.,0.));
#1511=DIRECTION('ref_axis',(0.,-1.,0.));
#1512=DIRECTION('',(0.,-1.,0.));
#1513=DIRECTION('',(0.,0.,1.));
#1514=DIRECTION('',(0.,-1.,0.));
#1515=DIRECTION('',(0.,0.,1.));
#1516=DIRECTION('center_axis',(0.,0.,1.));
#1517=DIRECTION('ref_axis',(1.,0.,0.));
#1518=DIRECTION('center_axis',(0.,0.,-1.));
#1519=DIRECTION('ref_axis',(1.,0.,0.));
#1520=DIRECTION('center_axis',(0.,0.,-1.));
#1521=DIRECTION('ref_axis',(1.,0.,0.));
#1522=DIRECTION('',(0.,0.,1.));
#1523=DIRECTION('center_axis',(1.,0.,0.));
#1524=DIRECTION('ref_axis',(0.,1.,0.));
#1525=DIRECTION('',(0.,1.,0.));
#1526=DIRECTION('',(0.,1.,0.));
#1527=DIRECTION('',(0.,0.,1.));
#1528=DIRECTION('center_axis',(0.,0.,1.));
#1529=DIRECTION('ref_axis',(-1.,0.,0.));
#1530=DIRECTION('center_axis',(0.,0.,-1.));
#1531=DIRECTION('ref_axis',(-1.,0.,0.));
#1532=DIRECTION('center_axis',(0.,0.,-1.));
#1533=DIRECTION('ref_axis',(-1.,0.,0.));
#1534=DIRECTION('center_axis',(0.,0.,1.));
#1535=DIRECTION('ref_axis',(1.,0.,0.));
#1536=DIRECTION('center_axis',(0.,0.,-1.));
#1537=DIRECTION('ref_axis',(1.,0.,0.));
#1538=DIRECTION('',(0.,0.,-1.));
#1539=DIRECTION('center_axis',(0.,0.,-1.));
#1540=DIRECTION('ref_axis',(1.,0.,0.));
#1541=DIRECTION('center_axis',(0.,0.,1.));
#1542=DIRECTION('ref_axis',(1.,0.,0.));
#1543=DIRECTION('center_axis',(0.,0.,-1.));
#1544=DIRECTION('ref_axis',(1.,0.,0.));
#1545=DIRECTION('',(0.,0.,-1.));
#1546=DIRECTION('center_axis',(0.,0.,-1.));
#1547=DIRECTION('ref_axis',(1.,0.,0.));
#1548=DIRECTION('center_axis',(0.,0.,1.));
#1549=DIRECTION('ref_axis',(1.,0.,0.));
#1550=DIRECTION('center_axis',(0.,0.,-1.));
#1551=DIRECTION('ref_axis',(1.,0.,0.));
#1552=DIRECTION('',(0.,0.,-1.));
#1553=DIRECTION('center_axis',(0.,0.,-1.));
#1554=DIRECTION('ref_axis',(1.,0.,0.));
#1555=DIRECTION('center_axis',(0.,0.,1.));
#1556=DIRECTION('ref_axis',(1.,0.,0.));
#1557=DIRECTION('center_axis',(0.,0.,-1.));
#1558=DIRECTION('ref_axis',(1.,0.,0.));
#1559=DIRECTION('',(0.,0.,-1.));
#1560=DIRECTION('center_axis',(0.,0.,-1.));
#1561=DIRECTION('ref_axis',(1.,0.,0.));
#1562=DIRECTION('center_axis',(0.,0.,1.));
#1563=DIRECTION('ref_axis',(1.,0.,0.));
#1564=DIRECTION('center_axis',(0.,0.,-1.));
#1565=DIRECTION('ref_axis',(1.,0.,0.));
#1566=DIRECTION('',(0.,0.,-1.));
#1567=DIRECTION('center_axis',(0.,0.,-1.));
#1568=DIRECTION('ref_axis',(1.,0.,0.));
#1569=DIRECTION('center_axis',(0.,0.,1.));
#1570=DIRECTION('ref_axis',(1.,0.,0.));
#1571=DIRECTION('center_axis',(0.,0.,-1.));
#1572=DIRECTION('ref_axis',(1.,0.,0.));
#1573=DIRECTION('',(0.,0.,-1.));
#1574=DIRECTION('center_axis',(0.,0.,-1.));
#1575=DIRECTION('ref_axis',(1.,0.,0.));
#1576=DIRECTION('center_axis',(0.,0.,1.));
#1577=DIRECTION('ref_axis',(1.,0.,0.));
#1578=DIRECTION('center_axis',(0.,0.,-1.));
#1579=DIRECTION('ref_axis',(1.,0.,0.));
#1580=DIRECTION('',(0.,0.,-1.));
#1581=DIRECTION('center_axis',(0.,0.,-1.));
#1582=DIRECTION('ref_axis',(1.,0.,0.));
#1583=DIRECTION('center_axis',(0.,0.,1.));
#1584=DIRECTION('ref_axis',(1.,0.,0.));
#1585=DIRECTION('center_axis',(0.,0.,-1.));
#1586=DIRECTION('ref_axis',(1.,0.,0.));
#1587=DIRECTION('',(0.,0.,-1.));
#1588=DIRECTION('center_axis',(0.,0.,-1.));
#1589=DIRECTION('ref_axis',(1.,0.,0.));
#1590=DIRECTION('center_axis',(0.,0.,1.));
#1591=DIRECTION('ref_axis',(1.,0.,0.));
#1592=DIRECTION('center_axis',(0.,0.,-1.));
#1593=DIRECTION('ref_axis',(1.,0.,0.));
#1594=DIRECTION('',(0.,0.,-1.));
#1595=DIRECTION('center_axis',(0.,0.,-1.));
#1596=DIRECTION('ref_axis',(1.,0.,0.));
#1597=DIRECTION('center_axis',(0.,0.,1.));
#1598=DIRECTION('ref_axis',(1.,0.,0.));
#1599=DIRECTION('center_axis',(0.,0.,-1.));
#1600=DIRECTION('ref_axis',(1.,0.,0.));
#1601=DIRECTION('',(0.,0.,-1.));
#1602=DIRECTION('center_axis',(0.,0.,-1.));
#1603=DIRECTION('ref_axis',(1.,0.,0.));
#1604=DIRECTION('center_axis',(0.,0.,1.));
#1605=DIRECTION('ref_axis',(0.,-1.,0.));
#1606=DIRECTION('center_axis',(0.,0.,1.));
#1607=DIRECTION('ref_axis',(0.,-1.,0.));
#1608=DIRECTION('',(0.,0.,1.));
#1609=DIRECTION('center_axis',(0.,0.,-1.));
#1610=DIRECTION('ref_axis',(0.,-1.,0.));
#1611=DIRECTION('',(0.,0.,1.));
#1612=DIRECTION('center_axis',(-1.,0.,0.));
#1613=DIRECTION('ref_axis',(0.,-1.,0.));
#1614=DIRECTION('',(0.,-1.,0.));
#1615=DIRECTION('',(0.,-1.,0.));
#1616=DIRECTION('',(0.,0.,1.));
#1617=DIRECTION('center_axis',(0.,0.,1.));
#1618=DIRECTION('ref_axis',(0.,1.,0.));
#1619=DIRECTION('center_axis',(0.,0.,1.));
#1620=DIRECTION('ref_axis',(0.,1.,0.));
#1621=DIRECTION('center_axis',(0.,0.,1.));
#1622=DIRECTION('ref_axis',(0.,1.,0.));
#1623=DIRECTION('',(0.,0.,1.));
#1624=DIRECTION('center_axis',(8.35932630306006E-15,1.,0.));
#1625=DIRECTION('ref_axis',(-1.,8.35932630306006E-15,0.));
#1626=DIRECTION('',(-1.,8.35932630306006E-15,0.));
#1627=DIRECTION('',(-1.,8.35932630306006E-15,0.));
#1628=DIRECTION('',(0.,0.,1.));
#1629=DIRECTION('center_axis',(0.,0.,1.));
#1630=DIRECTION('ref_axis',(1.,0.,0.));
#1631=DIRECTION('center_axis',(0.,0.,1.));
#1632=DIRECTION('ref_axis',(1.,0.,0.));
#1633=DIRECTION('center_axis',(0.,0.,1.));
#1634=DIRECTION('ref_axis',(1.,0.,0.));
#1635=DIRECTION('',(0.,0.,1.));
#1636=DIRECTION('center_axis',(1.,0.,0.));
#1637=DIRECTION('ref_axis',(0.,1.,0.));
#1638=DIRECTION('',(0.,1.,0.));
#1639=DIRECTION('',(0.,1.,0.));
#1640=DIRECTION('',(0.,0.,1.));
#1641=DIRECTION('center_axis',(0.,0.,1.));
#1642=DIRECTION('ref_axis',(0.,-1.,0.));
#1643=DIRECTION('center_axis',(0.,0.,1.));
#1644=DIRECTION('ref_axis',(0.,-1.,0.));
#1645=DIRECTION('center_axis',(0.,0.,1.));
#1646=DIRECTION('ref_axis',(0.,-1.,0.));
#1647=DIRECTION('',(0.,0.,1.));
#1648=DIRECTION('center_axis',(8.91621903179241E-16,-1.,0.));
#1649=DIRECTION('ref_axis',(1.,8.91621903179241E-16,0.));
#1650=DIRECTION('',(1.,8.91621903179241E-16,0.));
#1651=DIRECTION('',(1.,8.91621903179241E-16,0.));
#1652=DIRECTION('',(0.,0.,1.));
#1653=DIRECTION('center_axis',(0.,0.,1.));
#1654=DIRECTION('ref_axis',(-0.996174723949221,-0.0873837477148532,0.));
#1655=DIRECTION('center_axis',(0.,0.,1.));
#1656=DIRECTION('ref_axis',(-0.996174723949221,-0.0873837477148532,0.));
#1657=DIRECTION('center_axis',(0.,0.,1.));
#1658=DIRECTION('ref_axis',(-0.996174723949221,-0.0873837477148532,0.));
#1659=DIRECTION('',(0.,0.,1.));
#1660=DIRECTION('center_axis',(-0.996174723949222,-0.0873837477148434,0.));
#1661=DIRECTION('ref_axis',(0.0873837477148434,-0.996174723949222,0.));
#1662=DIRECTION('',(0.0873837477148433,-0.996174723949222,0.));
#1663=DIRECTION('',(0.0873837477148433,-0.996174723949222,0.));
#1664=DIRECTION('',(0.,0.,1.));
#1665=DIRECTION('center_axis',(0.,0.,1.));
#1666=DIRECTION('ref_axis',(0.996174723949221,0.0873837477148523,0.));
#1667=DIRECTION('center_axis',(0.,0.,1.));
#1668=DIRECTION('ref_axis',(0.996174723949221,0.0873837477148523,0.));
#1669=DIRECTION('center_axis',(0.,0.,-1.));
#1670=DIRECTION('ref_axis',(0.996174723949221,0.0873837477148523,0.));
#1671=DIRECTION('',(0.,0.,1.));
#1672=DIRECTION('center_axis',(1.91597737077146E-16,-1.,0.));
#1673=DIRECTION('ref_axis',(1.,1.91597737077146E-16,0.));
#1674=DIRECTION('',(-1.,-1.91597737077146E-16,0.));
#1675=DIRECTION('',(1.,1.91597737077146E-16,0.));
#1676=DIRECTION('',(0.,0.,1.));
#1677=DIRECTION('center_axis',(0.,0.,1.));
#1678=DIRECTION('ref_axis',(-1.77635683940005E-14,1.,0.));
#1679=DIRECTION('center_axis',(0.,0.,1.));
#1680=DIRECTION('ref_axis',(-1.77635683940005E-14,1.,0.));
#1681=DIRECTION('center_axis',(0.,0.,-1.));
#1682=DIRECTION('ref_axis',(-1.77635683940005E-14,1.,0.));
#1683=DIRECTION('',(0.,0.,1.));
#1684=DIRECTION('center_axis',(0.34199532867215,-0.939701652210119,0.));
#1685=DIRECTION('ref_axis',(0.939701652210119,0.34199532867215,0.));
#1686=DIRECTION('',(-0.939701652210119,-0.34199532867215,0.));
#1687=DIRECTION('',(0.939701652210119,0.34199532867215,0.));
#1688=DIRECTION('',(0.,0.,1.));
#1689=DIRECTION('center_axis',(0.,0.,1.));
#1690=DIRECTION('ref_axis',(-0.34199532867215,0.939701652210119,0.));
#1691=DIRECTION('center_axis',(0.,0.,1.));
#1692=DIRECTION('ref_axis',(-0.34199532867215,0.939701652210119,0.));
#1693=DIRECTION('center_axis',(0.,0.,-1.));
#1694=DIRECTION('ref_axis',(-0.34199532867215,0.939701652210119,0.));
#1695=DIRECTION('',(0.,0.,1.));
#1696=DIRECTION('center_axis',(1.,0.,0.));
#1697=DIRECTION('ref_axis',(0.,1.,0.));
#1698=DIRECTION('',(0.,-1.,0.));
#1699=DIRECTION('',(0.,1.,0.));
#1700=DIRECTION('',(0.,0.,1.));
#1701=DIRECTION('center_axis',(0.,0.,1.));
#1702=DIRECTION('ref_axis',(-6.66133814775095E-16,-1.,0.));
#1703=DIRECTION('center_axis',(0.,0.,1.));
#1704=DIRECTION('ref_axis',(-6.66133814775095E-16,-1.,0.));
#1705=DIRECTION('center_axis',(0.,0.,1.));
#1706=DIRECTION('ref_axis',(-6.66133814775095E-16,-1.,0.));
#1707=DIRECTION('',(0.,0.,1.));
#1708=DIRECTION('center_axis',(-9.79608551139844E-17,-1.,0.));
#1709=DIRECTION('ref_axis',(1.,-9.79608551139844E-17,0.));
#1710=DIRECTION('',(1.,-9.79608551139844E-17,0.));
#1711=DIRECTION('',(1.,-9.79608551139844E-17,0.));
#1712=DIRECTION('',(0.,0.,1.));
#1713=DIRECTION('center_axis',(0.,0.,1.));
#1714=DIRECTION('ref_axis',(-1.,1.85037170770859E-16,0.));
#1715=DIRECTION('center_axis',(0.,0.,1.));
#1716=DIRECTION('ref_axis',(-1.,1.85037170770859E-16,0.));
#1717=DIRECTION('center_axis',(0.,0.,1.));
#1718=DIRECTION('ref_axis',(-1.,1.85037170770859E-16,0.));
#1719=DIRECTION('',(0.,0.,1.));
#1720=DIRECTION('center_axis',(-1.,3.52157932407537E-49,0.));
#1721=DIRECTION('ref_axis',(-3.52157932407537E-49,-1.,0.));
#1722=DIRECTION('',(-3.52157932407537E-49,-1.,0.));
#1723=DIRECTION('',(-3.52157932407537E-49,-1.,0.));
#1724=DIRECTION('',(0.,0.,1.));
#1725=DIRECTION('center_axis',(0.,0.,1.));
#1726=DIRECTION('ref_axis',(1.85037170770852E-16,1.,0.));
#1727=DIRECTION('center_axis',(0.,0.,1.));
#1728=DIRECTION('ref_axis',(1.85037170770852E-16,1.,0.));
#1729=DIRECTION('center_axis',(0.,0.,1.));
#1730=DIRECTION('ref_axis',(1.85037170770852E-16,1.,0.));
#1731=DIRECTION('',(0.,0.,1.));
#1732=DIRECTION('center_axis',(8.35932630306E-15,1.,0.));
#1733=DIRECTION('ref_axis',(-1.,8.35932630306E-15,0.));
#1734=DIRECTION('',(-1.,8.35932630306E-15,0.));
#1735=DIRECTION('',(-1.,8.35932630306E-15,0.));
#1736=DIRECTION('',(0.,0.,1.));
#1737=DIRECTION('center_axis',(0.,0.,1.));
#1738=DIRECTION('ref_axis',(1.,0.,0.));
#1739=DIRECTION('center_axis',(0.,0.,1.));
#1740=DIRECTION('ref_axis',(1.,0.,0.));
#1741=DIRECTION('center_axis',(0.,0.,1.));
#1742=DIRECTION('ref_axis',(1.,0.,0.));
#1743=DIRECTION('',(0.,0.,1.));
#1744=DIRECTION('center_axis',(1.,0.,0.));
#1745=DIRECTION('ref_axis',(0.,1.,0.));
#1746=DIRECTION('',(0.,-1.,0.));
#1747=DIRECTION('',(0.,1.,0.));
#1748=DIRECTION('',(0.,0.,1.));
#1749=DIRECTION('center_axis',(0.,0.,1.));
#1750=DIRECTION('ref_axis',(-1.,0.,0.));
#1751=DIRECTION('center_axis',(0.,0.,1.));
#1752=DIRECTION('ref_axis',(-1.,0.,0.));
#1753=DIRECTION('center_axis',(0.,0.,-1.));
#1754=DIRECTION('ref_axis',(-1.,0.,0.));
#1755=DIRECTION('',(0.,0.,1.));
#1756=DIRECTION('center_axis',(1.225073682345E-15,1.,0.));
#1757=DIRECTION('ref_axis',(-1.,1.225073682345E-15,0.));
#1758=DIRECTION('',(1.,-1.225073682345E-15,0.));
#1759=DIRECTION('',(-1.,1.225073682345E-15,0.));
#1760=DIRECTION('',(0.,0.,1.));
#1761=DIRECTION('center_axis',(0.,0.,1.));
#1762=DIRECTION('ref_axis',(1.,0.,0.));
#1763=DIRECTION('center_axis',(0.,0.,1.));
#1764=DIRECTION('ref_axis',(1.,0.,0.));
#1765=DIRECTION('center_axis',(0.,0.,1.));
#1766=DIRECTION('ref_axis',(1.,0.,0.));
#1767=DIRECTION('',(0.,0.,1.));
#1768=DIRECTION('center_axis',(1.,0.,0.));
#1769=DIRECTION('ref_axis',(0.,1.,0.));
#1770=DIRECTION('',(0.,1.,0.));
#1771=DIRECTION('',(0.,1.,0.));
#1772=DIRECTION('',(0.,0.,1.));
#1773=DIRECTION('center_axis',(0.,0.,1.));
#1774=DIRECTION('ref_axis',(-1.,0.,0.));
#1775=DIRECTION('center_axis',(0.,0.,1.));
#1776=DIRECTION('ref_axis',(-1.,0.,0.));
#1777=DIRECTION('center_axis',(0.,0.,-1.));
#1778=DIRECTION('ref_axis',(-1.,0.,0.));
#1779=DIRECTION('',(0.,0.,1.));
#1780=DIRECTION('center_axis',(-1.,0.,0.));
#1781=DIRECTION('ref_axis',(0.,-1.,0.));
#1782=DIRECTION('',(0.,1.,0.));
#1783=DIRECTION('',(0.,-1.,0.));
#1784=DIRECTION('',(0.,0.,1.));
#1785=DIRECTION('center_axis',(0.,0.,1.));
#1786=DIRECTION('ref_axis',(0.,1.,0.));
#1787=DIRECTION('center_axis',(0.,0.,1.));
#1788=DIRECTION('ref_axis',(0.,1.,0.));
#1789=DIRECTION('center_axis',(0.,0.,1.));
#1790=DIRECTION('ref_axis',(0.,1.,0.));
#1791=DIRECTION('',(0.,0.,1.));
#1792=DIRECTION('center_axis',(-2.28103606985586E-16,1.,0.));
#1793=DIRECTION('ref_axis',(-1.,-2.28103606985586E-16,0.));
#1794=DIRECTION('',(1.,2.28103606985586E-16,0.));
#1795=DIRECTION('',(-1.,-2.28103606985586E-16,0.));
#1796=DIRECTION('center_axis',(0.,0.,1.));
#1797=DIRECTION('ref_axis',(1.,0.,0.));
#1798=DIRECTION('center_axis',(0.,0.,1.));
#1799=DIRECTION('ref_axis',(1.,0.,0.));
#1800=CARTESIAN_POINT('',(0.,0.,0.));
#1801=CARTESIAN_POINT('Origin',(441.528396530429,220.078878881316,0.));
#1802=CARTESIAN_POINT('',(436.528396530429,220.078878881316,12.));
#1803=CARTESIAN_POINT('Origin',(441.528396530429,220.078878881316,12.));
#1804=CARTESIAN_POINT('',(436.528396530429,220.078878881316,0.));
#1805=CARTESIAN_POINT('',(436.528396530429,220.078878881316,0.));
#1806=CARTESIAN_POINT('Origin',(441.528396530429,220.078878881316,0.));
#1807=CARTESIAN_POINT('Origin',(347.5,490.,0.));
#1808=CARTESIAN_POINT('',(344.5,490.,12.));
#1809=CARTESIAN_POINT('Origin',(347.5,490.,12.));
#1810=CARTESIAN_POINT('',(344.5,490.,0.));
#1811=CARTESIAN_POINT('',(344.5,490.,0.));
#1812=CARTESIAN_POINT('Origin',(347.5,490.,0.));
#1813=CARTESIAN_POINT('Origin',(484.,432.,0.));
#1814=CARTESIAN_POINT('',(481.,432.,12.));
#1815=CARTESIAN_POINT('Origin',(484.,432.,12.));
#1816=CARTESIAN_POINT('',(481.,432.,0.));
#1817=CARTESIAN_POINT('',(481.,432.,0.));
#1818=CARTESIAN_POINT('Origin',(484.,432.,0.));
#1819=CARTESIAN_POINT('Origin',(208.5,237.5,0.));
#1820=CARTESIAN_POINT('',(204.5,237.5,12.));
#1821=CARTESIAN_POINT('Origin',(208.5,237.5,12.));
#1822=CARTESIAN_POINT('',(204.5,237.5,0.));
#1823=CARTESIAN_POINT('',(204.5,237.5,0.));
#1824=CARTESIAN_POINT('Origin',(208.5,237.5,0.));
#1825=CARTESIAN_POINT('Origin',(659.5,222.5,0.));
#1826=CARTESIAN_POINT('',(655.5,222.5,12.));
#1827=CARTESIAN_POINT('Origin',(659.5,222.5,12.));
#1828=CARTESIAN_POINT('',(655.5,222.5,0.));
#1829=CARTESIAN_POINT('',(655.5,222.5,0.));
#1830=CARTESIAN_POINT('Origin',(659.5,222.5,0.));
#1831=CARTESIAN_POINT('Origin',(684.5,222.5,0.));
#1832=CARTESIAN_POINT('',(680.5,222.5,12.));
#1833=CARTESIAN_POINT('Origin',(684.5,222.5,12.));
#1834=CARTESIAN_POINT('',(680.5,222.5,0.));
#1835=CARTESIAN_POINT('',(680.5,222.5,0.));
#1836=CARTESIAN_POINT('Origin',(684.5,222.5,0.));
#1837=CARTESIAN_POINT('Origin',(559.5,340.,0.));
#1838=CARTESIAN_POINT('',(555.5,340.,12.));
#1839=CARTESIAN_POINT('Origin',(559.5,340.,12.));
#1840=CARTESIAN_POINT('',(555.5,340.,0.));
#1841=CARTESIAN_POINT('',(555.5,340.,0.));
#1842=CARTESIAN_POINT('Origin',(559.5,340.,0.));
#1843=CARTESIAN_POINT('Origin',(428.8,358.5,0.));
#1844=CARTESIAN_POINT('',(424.8,358.5,12.));
#1845=CARTESIAN_POINT('Origin',(428.8,358.5,12.));
#1846=CARTESIAN_POINT('',(424.8,358.5,0.));
#1847=CARTESIAN_POINT('',(424.8,358.5,0.));
#1848=CARTESIAN_POINT('Origin',(428.8,358.5,0.));
#1849=CARTESIAN_POINT('Origin',(53.5,392.5,0.));
#1850=CARTESIAN_POINT('',(49.5,392.5,12.));
#1851=CARTESIAN_POINT('Origin',(53.5,392.5,12.));
#1852=CARTESIAN_POINT('',(49.5,392.5,0.));
#1853=CARTESIAN_POINT('',(49.5,392.5,0.));
#1854=CARTESIAN_POINT('Origin',(53.5,392.5,0.));
#1855=CARTESIAN_POINT('Origin',(116.999999999999,314.,0.));
#1856=CARTESIAN_POINT('',(116.999999999999,314.,0.));
#1857=CARTESIAN_POINT('',(107.,314.,0.));
#1858=CARTESIAN_POINT('',(107.,314.,0.));
#1859=CARTESIAN_POINT('',(107.,314.,12.));
#1860=CARTESIAN_POINT('',(107.,314.,0.));
#1861=CARTESIAN_POINT('',(116.999999999999,314.,12.));
#1862=CARTESIAN_POINT('',(107.,314.,12.));
#1863=CARTESIAN_POINT('',(116.999999999999,314.,0.));
#1864=CARTESIAN_POINT('Origin',(117.,347.,0.));
#1865=CARTESIAN_POINT('',(117.000000000001,380.,0.));
#1866=CARTESIAN_POINT('Origin',(117.,347.,0.));
#1867=CARTESIAN_POINT('',(117.000000000001,380.,12.));
#1868=CARTESIAN_POINT('Origin',(117.,347.,12.));
#1869=CARTESIAN_POINT('',(117.000000000001,380.,0.));
#1870=CARTESIAN_POINT('Origin',(107.,380.,0.));
#1871=CARTESIAN_POINT('',(107.,380.,0.));
#1872=CARTESIAN_POINT('',(107.,380.,0.));
#1873=CARTESIAN_POINT('',(107.,380.,12.));
#1874=CARTESIAN_POINT('',(107.,380.,12.));
#1875=CARTESIAN_POINT('',(107.,380.,0.));
#1876=CARTESIAN_POINT('Origin',(107.,347.,0.));
#1877=CARTESIAN_POINT('Origin',(107.,347.,0.));
#1878=CARTESIAN_POINT('Origin',(107.,347.,12.));
#1879=CARTESIAN_POINT('Origin',(502.,392.5,0.));
#1880=CARTESIAN_POINT('',(502.,392.5,0.));
#1881=CARTESIAN_POINT('',(502.,382.5,0.));
#1882=CARTESIAN_POINT('',(502.,382.5,0.));
#1883=CARTESIAN_POINT('',(502.,382.5,12.));
#1884=CARTESIAN_POINT('',(502.,382.5,0.));
#1885=CARTESIAN_POINT('',(502.,392.5,12.));
#1886=CARTESIAN_POINT('',(502.,382.5,12.));
#1887=CARTESIAN_POINT('',(502.,392.5,0.));
#1888=CARTESIAN_POINT('Origin',(469.,392.5,0.));
#1889=CARTESIAN_POINT('',(436.,392.5,0.));
#1890=CARTESIAN_POINT('Origin',(469.,392.5,0.));
#1891=CARTESIAN_POINT('',(436.,392.5,12.));
#1892=CARTESIAN_POINT('Origin',(469.,392.5,12.));
#1893=CARTESIAN_POINT('',(436.,392.5,0.));
#1894=CARTESIAN_POINT('Origin',(436.,382.5,0.));
#1895=CARTESIAN_POINT('',(436.,382.5,0.));
#1896=CARTESIAN_POINT('',(436.,382.5,0.));
#1897=CARTESIAN_POINT('',(436.,382.5,12.));
#1898=CARTESIAN_POINT('',(436.,382.5,12.));
#1899=CARTESIAN_POINT('',(436.,382.5,0.));
#1900=CARTESIAN_POINT('Origin',(469.,382.5,0.));
#1901=CARTESIAN_POINT('Origin',(469.,382.5,0.));
#1902=CARTESIAN_POINT('Origin',(469.,382.5,12.));
#1903=CARTESIAN_POINT('Origin',(279.5,428.,0.));
#1904=CARTESIAN_POINT('',(275.5,428.,12.));
#1905=CARTESIAN_POINT('Origin',(279.5,428.,12.));
#1906=CARTESIAN_POINT('',(275.5,428.,0.));
#1907=CARTESIAN_POINT('',(275.5,428.,0.));
#1908=CARTESIAN_POINT('Origin',(279.5,428.,0.));
#1909=CARTESIAN_POINT('Origin',(238.5,482.5,0.));
#1910=CARTESIAN_POINT('',(234.5,482.5,12.));
#1911=CARTESIAN_POINT('Origin',(238.5,482.5,12.));
#1912=CARTESIAN_POINT('',(234.5,482.5,0.));
#1913=CARTESIAN_POINT('',(234.5,482.5,0.));
#1914=CARTESIAN_POINT('Origin',(238.5,482.5,0.));
#1915=CARTESIAN_POINT('Origin',(344.,480.5,0.));
#1916=CARTESIAN_POINT('',(340.,480.5,12.));
#1917=CARTESIAN_POINT('Origin',(344.,480.5,12.));
#1918=CARTESIAN_POINT('',(340.,480.5,0.));
#1919=CARTESIAN_POINT('',(340.,480.5,0.));
#1920=CARTESIAN_POINT('Origin',(344.,480.5,0.));
#1921=CARTESIAN_POINT('Origin',(417.,493.,0.));
#1922=CARTESIAN_POINT('',(413.,493.,12.));
#1923=CARTESIAN_POINT('Origin',(417.,493.,12.));
#1924=CARTESIAN_POINT('',(413.,493.,0.));
#1925=CARTESIAN_POINT('',(413.,493.,0.));
#1926=CARTESIAN_POINT('Origin',(417.,493.,0.));
#1927=CARTESIAN_POINT('Origin',(587.,493.,0.));
#1928=CARTESIAN_POINT('',(583.,493.,12.));
#1929=CARTESIAN_POINT('Origin',(587.,493.,12.));
#1930=CARTESIAN_POINT('',(583.,493.,0.));
#1931=CARTESIAN_POINT('',(583.,493.,0.));
#1932=CARTESIAN_POINT('Origin',(587.,493.,0.));
#1933=CARTESIAN_POINT('Origin',(759.,520.,0.));
#1934=CARTESIAN_POINT('',(755.6,520.,12.));
#1935=CARTESIAN_POINT('Origin',(759.,520.,12.));
#1936=CARTESIAN_POINT('',(755.6,520.,0.));
#1937=CARTESIAN_POINT('',(755.6,520.,0.));
#1938=CARTESIAN_POINT('Origin',(759.,520.,0.));
#1939=CARTESIAN_POINT('Origin',(759.,550.,0.));
#1940=CARTESIAN_POINT('',(755.6,550.,12.));
#1941=CARTESIAN_POINT('Origin',(759.,550.,12.));
#1942=CARTESIAN_POINT('',(755.6,550.,0.));
#1943=CARTESIAN_POINT('',(755.6,550.,0.));
#1944=CARTESIAN_POINT('Origin',(759.,550.,0.));
#1945=CARTESIAN_POINT('Origin',(157.,332.,0.));
#1946=CARTESIAN_POINT('',(154.,332.,12.));
#1947=CARTESIAN_POINT('Origin',(157.,332.,12.));
#1948=CARTESIAN_POINT('',(154.,332.,0.));
#1949=CARTESIAN_POINT('',(154.,332.,0.));
#1950=CARTESIAN_POINT('Origin',(157.,332.,0.));
#1951=CARTESIAN_POINT('Origin',(216.,138.,0.));
#1952=CARTESIAN_POINT('',(211.,138.,12.));
#1953=CARTESIAN_POINT('Origin',(216.,138.,12.));
#1954=CARTESIAN_POINT('',(211.,138.,0.));
#1955=CARTESIAN_POINT('',(211.,138.,0.));
#1956=CARTESIAN_POINT('Origin',(216.,138.,0.));
#1957=CARTESIAN_POINT('Origin',(215.,468.5,0.));
#1958=CARTESIAN_POINT('',(212.,468.5,12.));
#1959=CARTESIAN_POINT('Origin',(215.,468.5,12.));
#1960=CARTESIAN_POINT('',(212.,468.5,0.));
#1961=CARTESIAN_POINT('',(212.,468.5,0.));
#1962=CARTESIAN_POINT('Origin',(215.,468.5,0.));
#1963=CARTESIAN_POINT('Origin',(799.,660.,0.));
#1964=CARTESIAN_POINT('',(799.,620.,0.));
#1965=CARTESIAN_POINT('',(839.,660.,0.));
#1966=CARTESIAN_POINT('Origin',(799.,660.,0.));
#1967=CARTESIAN_POINT('',(799.,620.,12.));
#1968=CARTESIAN_POINT('',(799.,620.,0.));
#1969=CARTESIAN_POINT('',(839.,660.,12.));
#1970=CARTESIAN_POINT('Origin',(799.,660.,12.));
#1971=CARTESIAN_POINT('',(839.,660.,0.));
#1972=CARTESIAN_POINT('Origin',(839.,680.,0.));
#1973=CARTESIAN_POINT('',(839.,680.,0.));
#1974=CARTESIAN_POINT('',(839.,680.,0.));
#1975=CARTESIAN_POINT('',(839.,680.,12.));
#1976=CARTESIAN_POINT('',(839.,680.,12.));
#1977=CARTESIAN_POINT('',(839.,680.,0.));
#1978=CARTESIAN_POINT('Origin',(859.,680.,0.));
#1979=CARTESIAN_POINT('',(859.,700.,0.));
#1980=CARTESIAN_POINT('Origin',(859.,680.,0.));
#1981=CARTESIAN_POINT('',(859.,700.,12.));
#1982=CARTESIAN_POINT('Origin',(859.,680.,12.));
#1983=CARTESIAN_POINT('',(859.,700.,0.));
#1984=CARTESIAN_POINT('Origin',(876.,700.,0.));
#1985=CARTESIAN_POINT('',(876.,700.,0.));
#1986=CARTESIAN_POINT('',(876.,700.,0.));
#1987=CARTESIAN_POINT('',(876.,700.,12.));
#1988=CARTESIAN_POINT('',(876.,700.,12.));
#1989=CARTESIAN_POINT('',(876.,700.,0.));
#1990=CARTESIAN_POINT('Origin',(876.,697.,0.));
#1991=CARTESIAN_POINT('',(879.,697.,0.));
#1992=CARTESIAN_POINT('Origin',(876.,697.,0.));
#1993=CARTESIAN_POINT('',(879.,697.,12.));
#1994=CARTESIAN_POINT('Origin',(876.,697.,12.));
#1995=CARTESIAN_POINT('',(879.,697.,0.));
#1996=CARTESIAN_POINT('Origin',(879.,3.00000000000001,0.));
#1997=CARTESIAN_POINT('',(879.,3.00000000000001,0.));
#1998=CARTESIAN_POINT('',(879.,3.00000000000001,0.));
#1999=CARTESIAN_POINT('',(879.,2.99999999999997,12.));
#2000=CARTESIAN_POINT('',(879.,3.00000000000001,12.));
#2001=CARTESIAN_POINT('',(879.,3.00000000000001,0.));
#2002=CARTESIAN_POINT('Origin',(876.,3.00000000000004,0.));
#2003=CARTESIAN_POINT('',(876.,6.77236045021345E-14,0.));
#2004=CARTESIAN_POINT('Origin',(876.,3.00000000000004,0.));
#2005=CARTESIAN_POINT('',(876.,7.105427357601E-14,12.));
#2006=CARTESIAN_POINT('Origin',(876.,3.00000000000004,12.));
#2007=CARTESIAN_POINT('',(876.,6.77236045021345E-14,0.));
#2008=CARTESIAN_POINT('Origin',(857.322413334625,5.10702591327572E-14,0.));
#2009=CARTESIAN_POINT('',(857.322413334625,5.10702591327572E-14,0.));
#2010=CARTESIAN_POINT('',(857.322413334625,5.10702591327572E-14,0.));
#2011=CARTESIAN_POINT('',(857.322413334625,7.105427357601E-14,12.));
#2012=CARTESIAN_POINT('',(857.322413334625,5.10702591327572E-14,12.));
#2013=CARTESIAN_POINT('',(857.322413334625,5.10702591327572E-14,0.));
#2014=CARTESIAN_POINT('Origin',(857.322413334625,20.0000000000004,0.));
#2015=CARTESIAN_POINT('',(837.39891885564,18.2523250457033,0.));
#2016=CARTESIAN_POINT('Origin',(857.322413334625,20.0000000000004,0.));
#2017=CARTESIAN_POINT('',(837.39891885564,18.2523250457033,12.));
#2018=CARTESIAN_POINT('Origin',(857.322413334625,20.0000000000004,12.));
#2019=CARTESIAN_POINT('',(837.39891885564,18.2523250457033,0.));
#2020=CARTESIAN_POINT('Origin',(823.10108114436,181.247674954297,0.));
#2021=CARTESIAN_POINT('',(823.10108114436,181.247674954297,0.));
#2022=CARTESIAN_POINT('',(823.10108114436,181.247674954297,0.));
#2023=CARTESIAN_POINT('',(823.10108114436,181.247674954297,12.));
#2024=CARTESIAN_POINT('',(823.10108114436,181.247674954297,12.));
#2025=CARTESIAN_POINT('',(823.10108114436,181.247674954297,0.));
#2026=CARTESIAN_POINT('Origin',(803.177586665375,179.5,0.));
#2027=CARTESIAN_POINT('',(803.177586665376,199.5,0.));
#2028=CARTESIAN_POINT('Origin',(803.177586665375,179.5,0.));
#2029=CARTESIAN_POINT('',(803.177586665376,199.5,12.));
#2030=CARTESIAN_POINT('Origin',(803.177586665375,179.5,12.));
#2031=CARTESIAN_POINT('',(803.177586665376,199.5,0.));
#2032=CARTESIAN_POINT('Origin',(432.326267333767,199.5,0.));
#2033=CARTESIAN_POINT('',(432.326267333767,199.5,0.));
#2034=CARTESIAN_POINT('',(803.177586665376,199.5,0.));
#2035=CARTESIAN_POINT('',(432.326267333767,199.5,12.));
#2036=CARTESIAN_POINT('',(803.177586665376,199.5,12.));
#2037=CARTESIAN_POINT('',(432.326267333767,199.5,0.));
#2038=CARTESIAN_POINT('Origin',(432.326267333768,179.499999999998,0.));
#2039=CARTESIAN_POINT('',(425.486360760324,198.294033044202,0.));
#2040=CARTESIAN_POINT('Origin',(432.326267333768,179.499999999998,0.));
#2041=CARTESIAN_POINT('',(425.486360760324,198.294033044202,12.));
#2042=CARTESIAN_POINT('Origin',(432.326267333768,179.499999999998,12.));
#2043=CARTESIAN_POINT('',(425.486360760324,198.294033044202,0.));
#2044=CARTESIAN_POINT('Origin',(53.160093426557,62.7894887342022,0.));
#2045=CARTESIAN_POINT('',(53.160093426557,62.7894887342023,0.));
#2046=CARTESIAN_POINT('',(425.486360760324,198.294033044202,0.));
#2047=CARTESIAN_POINT('',(53.160093426557,62.7894887342023,12.));
#2048=CARTESIAN_POINT('',(425.486360760324,198.294033044202,12.));
#2049=CARTESIAN_POINT('',(53.160093426557,62.7894887342023,0.));
#2050=CARTESIAN_POINT('Origin',(60.,43.9954556899998,0.));
#2051=CARTESIAN_POINT('',(40.,43.9954556899998,0.));
#2052=CARTESIAN_POINT('Origin',(60.,43.9954556899998,0.));
#2053=CARTESIAN_POINT('',(40.,43.9954556899998,12.));
#2054=CARTESIAN_POINT('Origin',(60.,43.9954556899998,12.));
#2055=CARTESIAN_POINT('',(40.,43.9954556899998,0.));
#2056=CARTESIAN_POINT('Origin',(40.,20.,0.));
#2057=CARTESIAN_POINT('',(40.,20.,0.));
#2058=CARTESIAN_POINT('',(40.,43.9954556899998,0.));
#2059=CARTESIAN_POINT('',(40.,20.,12.));
#2060=CARTESIAN_POINT('',(40.,43.9954556899998,12.));
#2061=CARTESIAN_POINT('',(40.,20.,0.));
#2062=CARTESIAN_POINT('Origin',(20.,20.,0.));
#2063=CARTESIAN_POINT('',(20.,-2.22044604925031E-15,0.));
#2064=CARTESIAN_POINT('Origin',(20.,20.,0.));
#2065=CARTESIAN_POINT('',(20.,0.,12.));
#2066=CARTESIAN_POINT('Origin',(20.,20.,12.));
#2067=CARTESIAN_POINT('',(20.,-2.22044604925031E-15,0.));
#2068=CARTESIAN_POINT('Origin',(3.,-5.55111512312578E-16,0.));
#2069=CARTESIAN_POINT('',(3.,-5.55111512312578E-16,0.));
#2070=CARTESIAN_POINT('',(3.,-5.55111512312578E-16,0.));
#2071=CARTESIAN_POINT('',(3.00000000000001,0.,12.));
#2072=CARTESIAN_POINT('',(3.,-5.55111512312578E-16,12.));
#2073=CARTESIAN_POINT('',(3.,-5.55111512312578E-16,0.));
#2074=CARTESIAN_POINT('Origin',(3.,3.,0.));
#2075=CARTESIAN_POINT('',(0.,3.,0.));
#2076=CARTESIAN_POINT('Origin',(3.,3.,0.));
#2077=CARTESIAN_POINT('',(0.,2.99999999999997,12.));
#2078=CARTESIAN_POINT('Origin',(3.,3.,12.));
#2079=CARTESIAN_POINT('',(0.,3.,0.));
#2080=CARTESIAN_POINT('Origin',(2.4439760509083E-46,697.,0.));
#2081=CARTESIAN_POINT('',(-1.13242748511766E-13,697.,0.));
#2082=CARTESIAN_POINT('',(2.4439760509083E-46,697.,0.));
#2083=CARTESIAN_POINT('',(-1.06581410364015E-13,697.,12.));
#2084=CARTESIAN_POINT('',(2.4439760509083E-46,697.,12.));
#2085=CARTESIAN_POINT('',(-1.13242748511766E-13,697.,0.));
#2086=CARTESIAN_POINT('Origin',(3.,697.,0.));
#2087=CARTESIAN_POINT('',(3.,700.,0.));
#2088=CARTESIAN_POINT('Origin',(3.,697.,0.));
#2089=CARTESIAN_POINT('',(3.00000000000001,700.,12.));
#2090=CARTESIAN_POINT('Origin',(3.,697.,12.));
#2091=CARTESIAN_POINT('',(3.,700.,0.));
#2092=CARTESIAN_POINT('Origin',(20.,700.,0.));
#2093=CARTESIAN_POINT('',(20.,700.,0.));
#2094=CARTESIAN_POINT('',(20.,700.,0.));
#2095=CARTESIAN_POINT('',(20.,700.,12.));
#2096=CARTESIAN_POINT('',(20.,700.,12.));
#2097=CARTESIAN_POINT('',(20.,700.,0.));
#2098=CARTESIAN_POINT('Origin',(20.,680.,0.));
#2099=CARTESIAN_POINT('',(40.,680.,0.));
#2100=CARTESIAN_POINT('Origin',(20.,680.,0.));
#2101=CARTESIAN_POINT('',(40.,680.,12.));
#2102=CARTESIAN_POINT('Origin',(20.,680.,12.));
#2103=CARTESIAN_POINT('',(40.,680.,0.));
#2104=CARTESIAN_POINT('Origin',(40.,660.,0.));
#2105=CARTESIAN_POINT('',(40.,660.,0.));
#2106=CARTESIAN_POINT('',(40.,680.,0.));
#2107=CARTESIAN_POINT('',(40.,660.,12.));
#2108=CARTESIAN_POINT('',(40.,680.,12.));
#2109=CARTESIAN_POINT('',(40.,660.,0.));
#2110=CARTESIAN_POINT('Origin',(80.,660.,0.));
#2111=CARTESIAN_POINT('',(80.,620.,0.));
#2112=CARTESIAN_POINT('Origin',(80.,660.,0.));
#2113=CARTESIAN_POINT('',(80.,620.,12.));
#2114=CARTESIAN_POINT('Origin',(80.,660.,12.));
#2115=CARTESIAN_POINT('',(80.,620.,0.));
#2116=CARTESIAN_POINT('Origin',(138.,620.,0.));
#2117=CARTESIAN_POINT('',(138.,620.,0.));
#2118=CARTESIAN_POINT('',(80.,620.,0.));
#2119=CARTESIAN_POINT('',(138.,620.,12.));
#2120=CARTESIAN_POINT('',(80.,620.,12.));
#2121=CARTESIAN_POINT('',(138.,620.,0.));
#2122=CARTESIAN_POINT('Origin',(138.,617.,0.));
#2123=CARTESIAN_POINT('',(141.,617.,0.));
#2124=CARTESIAN_POINT('Origin',(138.,617.,0.));
#2125=CARTESIAN_POINT('',(141.,617.,12.));
#2126=CARTESIAN_POINT('Origin',(138.,617.,12.));
#2127=CARTESIAN_POINT('',(141.,617.,0.));
#2128=CARTESIAN_POINT('Origin',(141.,604.,0.));
#2129=CARTESIAN_POINT('',(141.,604.,0.));
#2130=CARTESIAN_POINT('',(141.,604.,0.));
#2131=CARTESIAN_POINT('',(141.,604.,12.));
#2132=CARTESIAN_POINT('',(141.,604.,12.));
#2133=CARTESIAN_POINT('',(141.,604.,0.));
#2134=CARTESIAN_POINT('Origin',(157.,604.,0.));
#2135=CARTESIAN_POINT('',(173.,604.,0.));
#2136=CARTESIAN_POINT('Origin',(157.,604.,0.));
#2137=CARTESIAN_POINT('',(173.,604.,12.));
#2138=CARTESIAN_POINT('Origin',(157.,604.,12.));
#2139=CARTESIAN_POINT('',(173.,604.,0.));
#2140=CARTESIAN_POINT('Origin',(173.,617.,0.));
#2141=CARTESIAN_POINT('',(173.,617.,0.));
#2142=CARTESIAN_POINT('',(173.,604.,0.));
#2143=CARTESIAN_POINT('',(173.,617.,12.));
#2144=CARTESIAN_POINT('',(173.,604.,12.));
#2145=CARTESIAN_POINT('',(173.,617.,0.));
#2146=CARTESIAN_POINT('Origin',(176.,617.,0.));
#2147=CARTESIAN_POINT('',(176.,620.,0.));
#2148=CARTESIAN_POINT('Origin',(176.,617.,0.));
#2149=CARTESIAN_POINT('',(176.,620.,12.));
#2150=CARTESIAN_POINT('Origin',(176.,617.,12.));
#2151=CARTESIAN_POINT('',(176.,620.,0.));
#2152=CARTESIAN_POINT('Origin',(799.,620.,0.));
#2153=CARTESIAN_POINT('',(176.,620.,0.));
#2154=CARTESIAN_POINT('',(176.,620.,12.));
#2155=CARTESIAN_POINT('Origin',(439.5,350.,12.));
#2156=CARTESIAN_POINT('Origin',(439.5,350.,0.));
#2157=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#2161,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#2158=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#2161,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#2159=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#2157))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#2161,#2163,#2164))
REPRESENTATION_CONTEXT('','3D')
);
#2160=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#2158))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#2161,#2163,#2164))
REPRESENTATION_CONTEXT('','3D')
);
#2161=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#2162=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT($,.METRE.)
);
#2163=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#2164=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#2165=SHAPE_DEFINITION_REPRESENTATION(#2166,#2167);
#2166=PRODUCT_DEFINITION_SHAPE('',$,#2169);
#2167=SHAPE_REPRESENTATION('',(#1281),#2159);
#2168=PRODUCT_DEFINITION_CONTEXT('part definition',#2173,'design');
#2169=PRODUCT_DEFINITION('S_1158','S_1158 v0',#2170,#2168);
#2170=PRODUCT_DEFINITION_FORMATION('',$,#2175);
#2171=PRODUCT_RELATED_PRODUCT_CATEGORY('S_1158 v0','S_1158 v0',(#2175));
#2172=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2009,#2173);
#2173=APPLICATION_CONTEXT(
'Core Data for Automotive Mechanical Design Process');
#2174=PRODUCT_CONTEXT('part definition',#2173,'mechanical');
#2175=PRODUCT('S_1158','S_1158 v0',$,(#2174));
#2176=PRESENTATION_STYLE_ASSIGNMENT((#2177));
#2177=SURFACE_STYLE_USAGE(.BOTH.,#2178);
#2178=SURFACE_SIDE_STYLE('',(#2179));
#2179=SURFACE_STYLE_FILL_AREA(#2180);
#2180=FILL_AREA_STYLE('Steel - Satin',(#2181));
#2181=FILL_AREA_STYLE_COLOUR('Steel - Satin',#2182);
#2182=COLOUR_RGB('Steel - Satin',0.627450980392157,0.627450980392157,0.627450980392157);
ENDSEC;
END-ISO-10303-21;
