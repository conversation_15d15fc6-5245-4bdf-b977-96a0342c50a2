ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ (''),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 
'G:/Shared drives/TIP/Validation/SMF KION/Tset SMF Shapes/S_1105.step',

/* time_stamp */ '2021-07-01T17:41:37+02:00',
/* author */ (''),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v18.1',
/* originating_system */ 'Autodesk Translation Framework v10.9.0.1377',

/* authorisation */ '');

FILE_SCHEMA (('AUTOMOTIVE_DESIGN { 1 0 10303 214 3 1 1 }'));
ENDSEC;

DATA;
#10=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#13),#383);
#11=SHAPE_REPRESENTATION_RELATIONSHIP('SRR','None',#390,#12);
#12=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#14),#382);
#13=STYLED_ITEM('',(#399),#14);
#14=MANIFOLD_SOLID_BREP('Body1',#231);
#15=CIRCLE('',#247,51.9999999999999);
#16=CIRCLE('',#248,51.9999999999999);
#17=CYLINDRICAL_SURFACE('',#246,51.9999999999999);
#18=FACE_OUTER_BOUND('',#30,.T.);
#19=FACE_OUTER_BOUND('',#31,.T.);
#20=FACE_OUTER_BOUND('',#32,.T.);
#21=FACE_OUTER_BOUND('',#33,.T.);
#22=FACE_OUTER_BOUND('',#34,.T.);
#23=FACE_OUTER_BOUND('',#35,.T.);
#24=FACE_OUTER_BOUND('',#36,.T.);
#25=FACE_OUTER_BOUND('',#37,.T.);
#26=FACE_OUTER_BOUND('',#38,.T.);
#27=FACE_OUTER_BOUND('',#39,.T.);
#28=FACE_OUTER_BOUND('',#40,.T.);
#29=FACE_OUTER_BOUND('',#41,.T.);
#30=EDGE_LOOP('',(#148,#149,#150,#151));
#31=EDGE_LOOP('',(#152,#153,#154,#155));
#32=EDGE_LOOP('',(#156,#157,#158,#159));
#33=EDGE_LOOP('',(#160,#161,#162,#163));
#34=EDGE_LOOP('',(#164,#165,#166,#167));
#35=EDGE_LOOP('',(#168,#169,#170,#171));
#36=EDGE_LOOP('',(#172,#173,#174,#175));
#37=EDGE_LOOP('',(#176,#177,#178,#179));
#38=EDGE_LOOP('',(#180,#181,#182,#183));
#39=EDGE_LOOP('',(#184,#185,#186,#187));
#40=EDGE_LOOP('',(#188,#189,#190,#191,#192,#193,#194,#195,#196,#197));
#41=EDGE_LOOP('',(#198,#199,#200,#201,#202,#203,#204,#205,#206,#207));
#42=LINE('',#321,#70);
#43=LINE('',#323,#71);
#44=LINE('',#325,#72);
#45=LINE('',#326,#73);
#46=LINE('',#332,#74);
#47=LINE('',#335,#75);
#48=LINE('',#337,#76);
#49=LINE('',#338,#77);
#50=LINE('',#341,#78);
#51=LINE('',#343,#79);
#52=LINE('',#344,#80);
#53=LINE('',#347,#81);
#54=LINE('',#349,#82);
#55=LINE('',#350,#83);
#56=LINE('',#353,#84);
#57=LINE('',#355,#85);
#58=LINE('',#356,#86);
#59=LINE('',#359,#87);
#60=LINE('',#361,#88);
#61=LINE('',#362,#89);
#62=LINE('',#365,#90);
#63=LINE('',#367,#91);
#64=LINE('',#368,#92);
#65=LINE('',#371,#93);
#66=LINE('',#373,#94);
#67=LINE('',#374,#95);
#68=LINE('',#376,#96);
#69=LINE('',#377,#97);
#70=VECTOR('',#263,10.);
#71=VECTOR('',#264,10.);
#72=VECTOR('',#265,10.);
#73=VECTOR('',#266,10.);
#74=VECTOR('',#273,10.);
#75=VECTOR('',#276,10.);
#76=VECTOR('',#277,10.);
#77=VECTOR('',#278,10.);
#78=VECTOR('',#281,10.);
#79=VECTOR('',#282,10.);
#80=VECTOR('',#283,10.);
#81=VECTOR('',#286,10.);
#82=VECTOR('',#287,10.);
#83=VECTOR('',#288,10.);
#84=VECTOR('',#291,10.);
#85=VECTOR('',#292,10.);
#86=VECTOR('',#293,10.);
#87=VECTOR('',#296,10.);
#88=VECTOR('',#297,10.);
#89=VECTOR('',#298,10.);
#90=VECTOR('',#301,10.);
#91=VECTOR('',#302,10.);
#92=VECTOR('',#303,10.);
#93=VECTOR('',#306,10.);
#94=VECTOR('',#307,10.);
#95=VECTOR('',#308,10.);
#96=VECTOR('',#311,10.);
#97=VECTOR('',#312,10.);
#98=VERTEX_POINT('',#319);
#99=VERTEX_POINT('',#320);
#100=VERTEX_POINT('',#322);
#101=VERTEX_POINT('',#324);
#102=VERTEX_POINT('',#328);
#103=VERTEX_POINT('',#330);
#104=VERTEX_POINT('',#334);
#105=VERTEX_POINT('',#336);
#106=VERTEX_POINT('',#340);
#107=VERTEX_POINT('',#342);
#108=VERTEX_POINT('',#346);
#109=VERTEX_POINT('',#348);
#110=VERTEX_POINT('',#352);
#111=VERTEX_POINT('',#354);
#112=VERTEX_POINT('',#358);
#113=VERTEX_POINT('',#360);
#114=VERTEX_POINT('',#364);
#115=VERTEX_POINT('',#366);
#116=VERTEX_POINT('',#370);
#117=VERTEX_POINT('',#372);
#118=EDGE_CURVE('',#98,#99,#42,.T.);
#119=EDGE_CURVE('',#99,#100,#43,.T.);
#120=EDGE_CURVE('',#101,#100,#44,.T.);
#121=EDGE_CURVE('',#98,#101,#45,.T.);
#122=EDGE_CURVE('',#98,#102,#15,.T.);
#123=EDGE_CURVE('',#103,#101,#16,.T.);
#124=EDGE_CURVE('',#102,#103,#46,.T.);
#125=EDGE_CURVE('',#104,#102,#47,.T.);
#126=EDGE_CURVE('',#105,#103,#48,.T.);
#127=EDGE_CURVE('',#104,#105,#49,.T.);
#128=EDGE_CURVE('',#104,#106,#50,.T.);
#129=EDGE_CURVE('',#107,#105,#51,.T.);
#130=EDGE_CURVE('',#106,#107,#52,.T.);
#131=EDGE_CURVE('',#108,#106,#53,.T.);
#132=EDGE_CURVE('',#109,#107,#54,.T.);
#133=EDGE_CURVE('',#108,#109,#55,.T.);
#134=EDGE_CURVE('',#110,#108,#56,.T.);
#135=EDGE_CURVE('',#111,#109,#57,.T.);
#136=EDGE_CURVE('',#110,#111,#58,.T.);
#137=EDGE_CURVE('',#110,#112,#59,.T.);
#138=EDGE_CURVE('',#113,#111,#60,.T.);
#139=EDGE_CURVE('',#112,#113,#61,.T.);
#140=EDGE_CURVE('',#114,#112,#62,.T.);
#141=EDGE_CURVE('',#115,#113,#63,.T.);
#142=EDGE_CURVE('',#114,#115,#64,.T.);
#143=EDGE_CURVE('',#116,#114,#65,.T.);
#144=EDGE_CURVE('',#117,#115,#66,.T.);
#145=EDGE_CURVE('',#116,#117,#67,.T.);
#146=EDGE_CURVE('',#99,#116,#68,.T.);
#147=EDGE_CURVE('',#100,#117,#69,.T.);
#148=ORIENTED_EDGE('',*,*,#118,.T.);
#149=ORIENTED_EDGE('',*,*,#119,.T.);
#150=ORIENTED_EDGE('',*,*,#120,.F.);
#151=ORIENTED_EDGE('',*,*,#121,.F.);
#152=ORIENTED_EDGE('',*,*,#122,.F.);
#153=ORIENTED_EDGE('',*,*,#121,.T.);
#154=ORIENTED_EDGE('',*,*,#123,.F.);
#155=ORIENTED_EDGE('',*,*,#124,.F.);
#156=ORIENTED_EDGE('',*,*,#125,.T.);
#157=ORIENTED_EDGE('',*,*,#124,.T.);
#158=ORIENTED_EDGE('',*,*,#126,.F.);
#159=ORIENTED_EDGE('',*,*,#127,.F.);
#160=ORIENTED_EDGE('',*,*,#128,.F.);
#161=ORIENTED_EDGE('',*,*,#127,.T.);
#162=ORIENTED_EDGE('',*,*,#129,.F.);
#163=ORIENTED_EDGE('',*,*,#130,.F.);
#164=ORIENTED_EDGE('',*,*,#131,.T.);
#165=ORIENTED_EDGE('',*,*,#130,.T.);
#166=ORIENTED_EDGE('',*,*,#132,.F.);
#167=ORIENTED_EDGE('',*,*,#133,.F.);
#168=ORIENTED_EDGE('',*,*,#134,.T.);
#169=ORIENTED_EDGE('',*,*,#133,.T.);
#170=ORIENTED_EDGE('',*,*,#135,.F.);
#171=ORIENTED_EDGE('',*,*,#136,.F.);
#172=ORIENTED_EDGE('',*,*,#137,.F.);
#173=ORIENTED_EDGE('',*,*,#136,.T.);
#174=ORIENTED_EDGE('',*,*,#138,.F.);
#175=ORIENTED_EDGE('',*,*,#139,.F.);
#176=ORIENTED_EDGE('',*,*,#140,.T.);
#177=ORIENTED_EDGE('',*,*,#139,.T.);
#178=ORIENTED_EDGE('',*,*,#141,.F.);
#179=ORIENTED_EDGE('',*,*,#142,.F.);
#180=ORIENTED_EDGE('',*,*,#143,.T.);
#181=ORIENTED_EDGE('',*,*,#142,.T.);
#182=ORIENTED_EDGE('',*,*,#144,.F.);
#183=ORIENTED_EDGE('',*,*,#145,.F.);
#184=ORIENTED_EDGE('',*,*,#146,.T.);
#185=ORIENTED_EDGE('',*,*,#145,.T.);
#186=ORIENTED_EDGE('',*,*,#147,.F.);
#187=ORIENTED_EDGE('',*,*,#119,.F.);
#188=ORIENTED_EDGE('',*,*,#147,.T.);
#189=ORIENTED_EDGE('',*,*,#144,.T.);
#190=ORIENTED_EDGE('',*,*,#141,.T.);
#191=ORIENTED_EDGE('',*,*,#138,.T.);
#192=ORIENTED_EDGE('',*,*,#135,.T.);
#193=ORIENTED_EDGE('',*,*,#132,.T.);
#194=ORIENTED_EDGE('',*,*,#129,.T.);
#195=ORIENTED_EDGE('',*,*,#126,.T.);
#196=ORIENTED_EDGE('',*,*,#123,.T.);
#197=ORIENTED_EDGE('',*,*,#120,.T.);
#198=ORIENTED_EDGE('',*,*,#146,.F.);
#199=ORIENTED_EDGE('',*,*,#118,.F.);
#200=ORIENTED_EDGE('',*,*,#122,.T.);
#201=ORIENTED_EDGE('',*,*,#125,.F.);
#202=ORIENTED_EDGE('',*,*,#128,.T.);
#203=ORIENTED_EDGE('',*,*,#131,.F.);
#204=ORIENTED_EDGE('',*,*,#134,.F.);
#205=ORIENTED_EDGE('',*,*,#137,.T.);
#206=ORIENTED_EDGE('',*,*,#140,.F.);
#207=ORIENTED_EDGE('',*,*,#143,.F.);
#208=PLANE('',#245);
#209=PLANE('',#249);
#210=PLANE('',#250);
#211=PLANE('',#251);
#212=PLANE('',#252);
#213=PLANE('',#253);
#214=PLANE('',#254);
#215=PLANE('',#255);
#216=PLANE('',#256);
#217=PLANE('',#257);
#218=PLANE('',#258);
#219=ADVANCED_FACE('',(#18),#208,.T.);
#220=ADVANCED_FACE('',(#19),#17,.F.);
#221=ADVANCED_FACE('',(#20),#209,.T.);
#222=ADVANCED_FACE('',(#21),#210,.T.);
#223=ADVANCED_FACE('',(#22),#211,.T.);
#224=ADVANCED_FACE('',(#23),#212,.T.);
#225=ADVANCED_FACE('',(#24),#213,.T.);
#226=ADVANCED_FACE('',(#25),#214,.T.);
#227=ADVANCED_FACE('',(#26),#215,.T.);
#228=ADVANCED_FACE('',(#27),#216,.T.);
#229=ADVANCED_FACE('',(#28),#217,.T.);
#230=ADVANCED_FACE('',(#29),#218,.F.);
#231=CLOSED_SHELL('',(#219,#220,#221,#222,#223,#224,#225,#226,#227,#228,
#229,#230));
#232=DERIVED_UNIT_ELEMENT(#234,1.);
#233=DERIVED_UNIT_ELEMENT(#385,-3.);
#234=(
MASS_UNIT()
NAMED_UNIT(*)
SI_UNIT(.KILO.,.GRAM.)
);
#235=DERIVED_UNIT((#232,#233));
#236=MEASURE_REPRESENTATION_ITEM('density measure',
POSITIVE_RATIO_MEASURE(7850.),#235);
#237=PROPERTY_DEFINITION_REPRESENTATION(#242,#239);
#238=PROPERTY_DEFINITION_REPRESENTATION(#243,#240);
#239=REPRESENTATION('material name',(#241),#382);
#240=REPRESENTATION('density',(#236),#382);
#241=DESCRIPTIVE_REPRESENTATION_ITEM('Steel','Steel');
#242=PROPERTY_DEFINITION('material property','material name',#392);
#243=PROPERTY_DEFINITION('material property','density of part',#392);
#244=AXIS2_PLACEMENT_3D('placement',#317,#259,#260);
#245=AXIS2_PLACEMENT_3D('',#318,#261,#262);
#246=AXIS2_PLACEMENT_3D('',#327,#267,#268);
#247=AXIS2_PLACEMENT_3D('',#329,#269,#270);
#248=AXIS2_PLACEMENT_3D('',#331,#271,#272);
#249=AXIS2_PLACEMENT_3D('',#333,#274,#275);
#250=AXIS2_PLACEMENT_3D('',#339,#279,#280);
#251=AXIS2_PLACEMENT_3D('',#345,#284,#285);
#252=AXIS2_PLACEMENT_3D('',#351,#289,#290);
#253=AXIS2_PLACEMENT_3D('',#357,#294,#295);
#254=AXIS2_PLACEMENT_3D('',#363,#299,#300);
#255=AXIS2_PLACEMENT_3D('',#369,#304,#305);
#256=AXIS2_PLACEMENT_3D('',#375,#309,#310);
#257=AXIS2_PLACEMENT_3D('',#378,#313,#314);
#258=AXIS2_PLACEMENT_3D('',#379,#315,#316);
#259=DIRECTION('axis',(0.,0.,1.));
#260=DIRECTION('refdir',(1.,0.,0.));
#261=DIRECTION('center_axis',(0.992018250371339,0.126094373110731,0.));
#262=DIRECTION('ref_axis',(-0.126094373110731,0.992018250371339,0.));
#263=DIRECTION('',(-0.126094373110731,0.992018250371339,0.));
#264=DIRECTION('',(0.,0.,1.));
#265=DIRECTION('',(-0.126094373110731,0.992018250371339,0.));
#266=DIRECTION('',(0.,0.,1.));
#267=DIRECTION('center_axis',(0.,0.,1.));
#268=DIRECTION('ref_axis',(-0.992018250371339,-0.12609437311073,0.));
#269=DIRECTION('center_axis',(0.,0.,1.));
#270=DIRECTION('ref_axis',(-0.992018250371339,-0.12609437311073,0.));
#271=DIRECTION('center_axis',(0.,0.,-1.));
#272=DIRECTION('ref_axis',(-0.992018250371339,-0.12609437311073,0.));
#273=DIRECTION('',(0.,0.,1.));
#274=DIRECTION('center_axis',(0.209439411662948,0.97782162629095,0.));
#275=DIRECTION('ref_axis',(-0.97782162629095,0.209439411662948,0.));
#276=DIRECTION('',(-0.97782162629095,0.209439411662948,0.));
#277=DIRECTION('',(-0.97782162629095,0.209439411662948,0.));
#278=DIRECTION('',(0.,0.,1.));
#279=DIRECTION('center_axis',(0.707106781186548,0.707106781186547,0.));
#280=DIRECTION('ref_axis',(-0.707106781186547,0.707106781186548,0.));
#281=DIRECTION('',(0.707106781186547,-0.707106781186548,0.));
#282=DIRECTION('',(-0.707106781186547,0.707106781186548,0.));
#283=DIRECTION('',(0.,0.,1.));
#284=DIRECTION('center_axis',(1.,0.,0.));
#285=DIRECTION('ref_axis',(0.,1.,0.));
#286=DIRECTION('',(0.,1.,0.));
#287=DIRECTION('',(0.,1.,0.));
#288=DIRECTION('',(0.,0.,1.));
#289=DIRECTION('center_axis',(0.,-1.,0.));
#290=DIRECTION('ref_axis',(1.,0.,0.));
#291=DIRECTION('',(1.,0.,0.));
#292=DIRECTION('',(1.,0.,0.));
#293=DIRECTION('',(0.,0.,1.));
#294=DIRECTION('center_axis',(-0.707106781186548,-0.707106781186548,0.));
#295=DIRECTION('ref_axis',(0.707106781186548,-0.707106781186548,0.));
#296=DIRECTION('',(-0.707106781186547,0.707106781186547,0.));
#297=DIRECTION('',(0.707106781186547,-0.707106781186547,0.));
#298=DIRECTION('',(0.,0.,1.));
#299=DIRECTION('center_axis',(-1.,0.,0.));
#300=DIRECTION('ref_axis',(0.,-1.,0.));
#301=DIRECTION('',(0.,-1.,0.));
#302=DIRECTION('',(0.,-1.,0.));
#303=DIRECTION('',(0.,0.,1.));
#304=DIRECTION('center_axis',(0.,1.,0.));
#305=DIRECTION('ref_axis',(-1.,0.,0.));
#306=DIRECTION('',(-1.,0.,0.));
#307=DIRECTION('',(-1.,0.,0.));
#308=DIRECTION('',(0.,0.,1.));
#309=DIRECTION('center_axis',(0.707106781186547,0.707106781186548,0.));
#310=DIRECTION('ref_axis',(-0.707106781186548,0.707106781186547,0.));
#311=DIRECTION('',(-0.707106781186548,0.707106781186547,0.));
#312=DIRECTION('',(-0.707106781186548,0.707106781186547,0.));
#313=DIRECTION('center_axis',(0.,0.,1.));
#314=DIRECTION('ref_axis',(1.,0.,0.));
#315=DIRECTION('center_axis',(0.,0.,1.));
#316=DIRECTION('ref_axis',(1.,0.,0.));
#317=CARTESIAN_POINT('',(0.,0.,0.));
#318=CARTESIAN_POINT('Origin',(23.6225512163612,80.500418052918,0.));
#319=CARTESIAN_POINT('',(23.6225512163612,80.500418052918,0.));
#320=CARTESIAN_POINT('',(20.,109.,0.));
#321=CARTESIAN_POINT('',(23.6225512163612,80.500418052918,0.));
#322=CARTESIAN_POINT('',(20.,109.,10.));
#323=CARTESIAN_POINT('',(20.,109.,0.));
#324=CARTESIAN_POINT('',(23.6225512163612,80.500418052918,10.));
#325=CARTESIAN_POINT('',(23.6225512163612,80.500418052918,10.));
#326=CARTESIAN_POINT('',(23.6225512163612,80.500418052918,0.));
#327=CARTESIAN_POINT('Origin',(75.2075002356707,87.057325454676,0.));
#328=CARTESIAN_POINT('',(64.3166508291975,36.2106008875466,0.));
#329=CARTESIAN_POINT('Origin',(75.2075002356707,87.057325454676,0.));
#330=CARTESIAN_POINT('',(64.3166508291975,36.2106008875466,10.));
#331=CARTESIAN_POINT('Origin',(75.2075002356707,87.057325454676,10.));
#332=CARTESIAN_POINT('',(64.3166508291975,36.2106008875466,0.));
#333=CARTESIAN_POINT('Origin',(140.,20.,0.));
#334=CARTESIAN_POINT('',(140.,20.,0.));
#335=CARTESIAN_POINT('',(140.,20.,0.));
#336=CARTESIAN_POINT('',(140.,20.,10.));
#337=CARTESIAN_POINT('',(140.,20.,10.));
#338=CARTESIAN_POINT('',(140.,20.,0.));
#339=CARTESIAN_POINT('Origin',(146.,14.,0.));
#340=CARTESIAN_POINT('',(146.,14.,0.));
#341=CARTESIAN_POINT('',(140.,20.,0.));
#342=CARTESIAN_POINT('',(146.,14.,10.));
#343=CARTESIAN_POINT('',(140.,20.,10.));
#344=CARTESIAN_POINT('',(146.,14.,0.));
#345=CARTESIAN_POINT('Origin',(146.,0.,0.));
#346=CARTESIAN_POINT('',(146.,0.,0.));
#347=CARTESIAN_POINT('',(146.,0.,0.));
#348=CARTESIAN_POINT('',(146.,0.,10.));
#349=CARTESIAN_POINT('',(146.,0.,10.));
#350=CARTESIAN_POINT('',(146.,0.,0.));
#351=CARTESIAN_POINT('Origin',(20.,0.,0.));
#352=CARTESIAN_POINT('',(20.,0.,0.));
#353=CARTESIAN_POINT('',(20.,0.,0.));
#354=CARTESIAN_POINT('',(20.,0.,10.));
#355=CARTESIAN_POINT('',(20.,0.,10.));
#356=CARTESIAN_POINT('',(20.,0.,0.));
#357=CARTESIAN_POINT('Origin',(0.,20.,0.));
#358=CARTESIAN_POINT('',(0.,20.,0.));
#359=CARTESIAN_POINT('',(20.,0.,0.));
#360=CARTESIAN_POINT('',(0.,20.,10.));
#361=CARTESIAN_POINT('',(20.,0.,10.));
#362=CARTESIAN_POINT('',(0.,20.,0.));
#363=CARTESIAN_POINT('Origin',(0.,115.,0.));
#364=CARTESIAN_POINT('',(0.,115.,0.));
#365=CARTESIAN_POINT('',(0.,115.,0.));
#366=CARTESIAN_POINT('',(0.,115.,10.));
#367=CARTESIAN_POINT('',(0.,115.,10.));
#368=CARTESIAN_POINT('',(0.,115.,0.));
#369=CARTESIAN_POINT('Origin',(14.,115.,0.));
#370=CARTESIAN_POINT('',(14.,115.,0.));
#371=CARTESIAN_POINT('',(14.,115.,0.));
#372=CARTESIAN_POINT('',(14.,115.,10.));
#373=CARTESIAN_POINT('',(14.,115.,10.));
#374=CARTESIAN_POINT('',(14.,115.,0.));
#375=CARTESIAN_POINT('Origin',(20.,109.,0.));
#376=CARTESIAN_POINT('',(20.,109.,0.));
#377=CARTESIAN_POINT('',(20.,109.,10.));
#378=CARTESIAN_POINT('Origin',(73.,57.5,10.));
#379=CARTESIAN_POINT('Origin',(73.,57.5,0.));
#380=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#384,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#381=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#384,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#382=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#380))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#384,#386,#387))
REPRESENTATION_CONTEXT('','3D')
);
#383=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#381))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#384,#386,#387))
REPRESENTATION_CONTEXT('','3D')
);
#384=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#385=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT($,.METRE.)
);
#386=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#387=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#388=SHAPE_DEFINITION_REPRESENTATION(#389,#390);
#389=PRODUCT_DEFINITION_SHAPE('',$,#392);
#390=SHAPE_REPRESENTATION('',(#244),#382);
#391=PRODUCT_DEFINITION_CONTEXT('part definition',#396,'design');
#392=PRODUCT_DEFINITION('S_1105','S_1105 v0',#393,#391);
#393=PRODUCT_DEFINITION_FORMATION('',$,#398);
#394=PRODUCT_RELATED_PRODUCT_CATEGORY('S_1105 v0','S_1105 v0',(#398));
#395=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2009,#396);
#396=APPLICATION_CONTEXT(
'Core Data for Automotive Mechanical Design Process');
#397=PRODUCT_CONTEXT('part definition',#396,'mechanical');
#398=PRODUCT('S_1105','S_1105 v0',$,(#397));
#399=PRESENTATION_STYLE_ASSIGNMENT((#400));
#400=SURFACE_STYLE_USAGE(.BOTH.,#401);
#401=SURFACE_SIDE_STYLE('',(#402));
#402=SURFACE_STYLE_FILL_AREA(#403);
#403=FILL_AREA_STYLE('Steel - Satin',(#404));
#404=FILL_AREA_STYLE_COLOUR('Steel - Satin',#405);
#405=COLOUR_RGB('Steel - Satin',0.627450980392157,0.627450980392157,0.627450980392157);
ENDSEC;
END-ISO-10303-21;
