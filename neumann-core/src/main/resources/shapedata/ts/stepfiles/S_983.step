ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ (''),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 
'G:/Shared drives/TIP/Validation/SMF KION/Tset SMF Shapes/S_983.step',
/* time_stamp */ '2021-10-04T16:18:24+02:00',
/* author */ (''),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v18.1',
/* originating_system */ 'Autodesk Translation Framework v10.10.0.1391',

/* authorisation */ '');

FILE_SCHEMA (('AUTOMOTIVE_DESIGN { 1 0 10303 214 3 1 1 }'));
ENDSEC;

DATA;
#10=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#13),#2187);
#11=SHAPE_REPRESENTATION_RELATIONSHIP('SRR','None',#2194,#12);
#12=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#14),#2186);
#13=STYLED_ITEM('',(#2203),#14);
#14=MANIFOLD_SOLID_BREP('Body1',#1283);
#15=FACE_BOUND('',#185,.T.);
#16=FACE_BOUND('',#186,.T.);
#17=FACE_BOUND('',#187,.T.);
#18=FACE_BOUND('',#188,.T.);
#19=FACE_BOUND('',#189,.T.);
#20=FACE_BOUND('',#190,.T.);
#21=FACE_BOUND('',#192,.T.);
#22=FACE_BOUND('',#193,.T.);
#23=FACE_BOUND('',#194,.T.);
#24=FACE_BOUND('',#195,.T.);
#25=FACE_BOUND('',#196,.T.);
#26=FACE_BOUND('',#197,.T.);
#27=PLANE('',#1306);
#28=PLANE('',#1310);
#29=PLANE('',#1314);
#30=PLANE('',#1318);
#31=PLANE('',#1325);
#32=PLANE('',#1329);
#33=PLANE('',#1333);
#34=PLANE('',#1337);
#35=PLANE('',#1341);
#36=PLANE('',#1345);
#37=PLANE('',#1349);
#38=PLANE('',#1359);
#39=PLANE('',#1363);
#40=PLANE('',#1367);
#41=PLANE('',#1371);
#42=PLANE('',#1375);
#43=PLANE('',#1382);
#44=PLANE('',#1386);
#45=PLANE('',#1390);
#46=PLANE('',#1394);
#47=PLANE('',#1401);
#48=PLANE('',#1405);
#49=PLANE('',#1415);
#50=PLANE('',#1416);
#51=PLANE('',#1417);
#52=PLANE('',#1418);
#53=PLANE('',#1419);
#54=PLANE('',#1420);
#55=PLANE('',#1421);
#56=PLANE('',#1425);
#57=PLANE('',#1426);
#58=FACE_OUTER_BOUND('',#122,.T.);
#59=FACE_OUTER_BOUND('',#123,.T.);
#60=FACE_OUTER_BOUND('',#124,.T.);
#61=FACE_OUTER_BOUND('',#125,.T.);
#62=FACE_OUTER_BOUND('',#126,.T.);
#63=FACE_OUTER_BOUND('',#127,.T.);
#64=FACE_OUTER_BOUND('',#128,.T.);
#65=FACE_OUTER_BOUND('',#129,.T.);
#66=FACE_OUTER_BOUND('',#130,.T.);
#67=FACE_OUTER_BOUND('',#131,.T.);
#68=FACE_OUTER_BOUND('',#132,.T.);
#69=FACE_OUTER_BOUND('',#133,.T.);
#70=FACE_OUTER_BOUND('',#134,.T.);
#71=FACE_OUTER_BOUND('',#135,.T.);
#72=FACE_OUTER_BOUND('',#136,.T.);
#73=FACE_OUTER_BOUND('',#137,.T.);
#74=FACE_OUTER_BOUND('',#138,.T.);
#75=FACE_OUTER_BOUND('',#139,.T.);
#76=FACE_OUTER_BOUND('',#140,.T.);
#77=FACE_OUTER_BOUND('',#141,.T.);
#78=FACE_OUTER_BOUND('',#142,.T.);
#79=FACE_OUTER_BOUND('',#143,.T.);
#80=FACE_OUTER_BOUND('',#144,.T.);
#81=FACE_OUTER_BOUND('',#145,.T.);
#82=FACE_OUTER_BOUND('',#146,.T.);
#83=FACE_OUTER_BOUND('',#147,.T.);
#84=FACE_OUTER_BOUND('',#148,.T.);
#85=FACE_OUTER_BOUND('',#149,.T.);
#86=FACE_OUTER_BOUND('',#150,.T.);
#87=FACE_OUTER_BOUND('',#151,.T.);
#88=FACE_OUTER_BOUND('',#152,.T.);
#89=FACE_OUTER_BOUND('',#153,.T.);
#90=FACE_OUTER_BOUND('',#154,.T.);
#91=FACE_OUTER_BOUND('',#155,.T.);
#92=FACE_OUTER_BOUND('',#156,.T.);
#93=FACE_OUTER_BOUND('',#157,.T.);
#94=FACE_OUTER_BOUND('',#158,.T.);
#95=FACE_OUTER_BOUND('',#159,.T.);
#96=FACE_OUTER_BOUND('',#160,.T.);
#97=FACE_OUTER_BOUND('',#161,.T.);
#98=FACE_OUTER_BOUND('',#162,.T.);
#99=FACE_OUTER_BOUND('',#163,.T.);
#100=FACE_OUTER_BOUND('',#164,.T.);
#101=FACE_OUTER_BOUND('',#165,.T.);
#102=FACE_OUTER_BOUND('',#166,.T.);
#103=FACE_OUTER_BOUND('',#167,.T.);
#104=FACE_OUTER_BOUND('',#168,.T.);
#105=FACE_OUTER_BOUND('',#169,.T.);
#106=FACE_OUTER_BOUND('',#170,.T.);
#107=FACE_OUTER_BOUND('',#171,.T.);
#108=FACE_OUTER_BOUND('',#172,.T.);
#109=FACE_OUTER_BOUND('',#173,.T.);
#110=FACE_OUTER_BOUND('',#174,.T.);
#111=FACE_OUTER_BOUND('',#175,.T.);
#112=FACE_OUTER_BOUND('',#176,.T.);
#113=FACE_OUTER_BOUND('',#177,.T.);
#114=FACE_OUTER_BOUND('',#178,.T.);
#115=FACE_OUTER_BOUND('',#179,.T.);
#116=FACE_OUTER_BOUND('',#180,.T.);
#117=FACE_OUTER_BOUND('',#181,.T.);
#118=FACE_OUTER_BOUND('',#182,.T.);
#119=FACE_OUTER_BOUND('',#183,.T.);
#120=FACE_OUTER_BOUND('',#184,.T.);
#121=FACE_OUTER_BOUND('',#191,.T.);
#122=EDGE_LOOP('',(#814,#815,#816,#817));
#123=EDGE_LOOP('',(#818,#819,#820,#821));
#124=EDGE_LOOP('',(#822,#823,#824,#825));
#125=EDGE_LOOP('',(#826,#827,#828,#829));
#126=EDGE_LOOP('',(#830,#831,#832,#833));
#127=EDGE_LOOP('',(#834,#835,#836,#837));
#128=EDGE_LOOP('',(#838,#839,#840,#841));
#129=EDGE_LOOP('',(#842,#843,#844,#845));
#130=EDGE_LOOP('',(#846,#847,#848,#849));
#131=EDGE_LOOP('',(#850,#851,#852,#853));
#132=EDGE_LOOP('',(#854,#855,#856,#857));
#133=EDGE_LOOP('',(#858,#859,#860,#861));
#134=EDGE_LOOP('',(#862,#863,#864,#865));
#135=EDGE_LOOP('',(#866,#867,#868,#869));
#136=EDGE_LOOP('',(#870,#871,#872,#873));
#137=EDGE_LOOP('',(#874,#875,#876,#877));
#138=EDGE_LOOP('',(#878,#879,#880,#881));
#139=EDGE_LOOP('',(#882,#883,#884,#885));
#140=EDGE_LOOP('',(#886,#887,#888,#889));
#141=EDGE_LOOP('',(#890,#891,#892,#893));
#142=EDGE_LOOP('',(#894,#895,#896,#897));
#143=EDGE_LOOP('',(#898,#899,#900,#901));
#144=EDGE_LOOP('',(#902,#903,#904,#905));
#145=EDGE_LOOP('',(#906,#907,#908,#909));
#146=EDGE_LOOP('',(#910,#911,#912,#913));
#147=EDGE_LOOP('',(#914,#915,#916,#917));
#148=EDGE_LOOP('',(#918,#919,#920,#921));
#149=EDGE_LOOP('',(#922,#923,#924,#925));
#150=EDGE_LOOP('',(#926,#927,#928,#929));
#151=EDGE_LOOP('',(#930,#931,#932,#933));
#152=EDGE_LOOP('',(#934,#935,#936,#937));
#153=EDGE_LOOP('',(#938,#939,#940,#941));
#154=EDGE_LOOP('',(#942,#943,#944,#945));
#155=EDGE_LOOP('',(#946,#947,#948,#949));
#156=EDGE_LOOP('',(#950,#951,#952,#953));
#157=EDGE_LOOP('',(#954,#955,#956,#957));
#158=EDGE_LOOP('',(#958,#959,#960,#961));
#159=EDGE_LOOP('',(#962,#963,#964,#965));
#160=EDGE_LOOP('',(#966,#967,#968,#969));
#161=EDGE_LOOP('',(#970,#971,#972,#973));
#162=EDGE_LOOP('',(#974,#975,#976,#977));
#163=EDGE_LOOP('',(#978,#979,#980,#981));
#164=EDGE_LOOP('',(#982,#983,#984,#985));
#165=EDGE_LOOP('',(#986,#987,#988,#989));
#166=EDGE_LOOP('',(#990,#991,#992,#993));
#167=EDGE_LOOP('',(#994,#995,#996,#997));
#168=EDGE_LOOP('',(#998,#999,#1000,#1001));
#169=EDGE_LOOP('',(#1002,#1003,#1004,#1005));
#170=EDGE_LOOP('',(#1006,#1007,#1008,#1009));
#171=EDGE_LOOP('',(#1010,#1011,#1012,#1013));
#172=EDGE_LOOP('',(#1014,#1015,#1016,#1017));
#173=EDGE_LOOP('',(#1018,#1019,#1020,#1021));
#174=EDGE_LOOP('',(#1022,#1023,#1024,#1025));
#175=EDGE_LOOP('',(#1026,#1027,#1028,#1029));
#176=EDGE_LOOP('',(#1030,#1031,#1032,#1033));
#177=EDGE_LOOP('',(#1034,#1035,#1036,#1037));
#178=EDGE_LOOP('',(#1038,#1039,#1040,#1041));
#179=EDGE_LOOP('',(#1042,#1043,#1044,#1045));
#180=EDGE_LOOP('',(#1046,#1047,#1048,#1049));
#181=EDGE_LOOP('',(#1050,#1051,#1052,#1053));
#182=EDGE_LOOP('',(#1054,#1055,#1056,#1057));
#183=EDGE_LOOP('',(#1058,#1059,#1060,#1061));
#184=EDGE_LOOP('',(#1062,#1063,#1064,#1065,#1066,#1067,#1068,#1069,#1070,
#1071));
#185=EDGE_LOOP('',(#1072));
#186=EDGE_LOOP('',(#1073,#1074,#1075,#1076,#1077,#1078,#1079,#1080,#1081,
#1082,#1083,#1084,#1085,#1086,#1087,#1088,#1089,#1090,#1091,#1092,#1093,
#1094,#1095,#1096));
#187=EDGE_LOOP('',(#1097));
#188=EDGE_LOOP('',(#1098,#1099,#1100,#1101,#1102,#1103,#1104,#1105,#1106,
#1107,#1108,#1109,#1110,#1111,#1112,#1113,#1114,#1115,#1116,#1117,#1118,
#1119,#1120,#1121));
#189=EDGE_LOOP('',(#1122));
#190=EDGE_LOOP('',(#1123));
#191=EDGE_LOOP('',(#1124,#1125,#1126,#1127,#1128,#1129,#1130,#1131,#1132,
#1133));
#192=EDGE_LOOP('',(#1134));
#193=EDGE_LOOP('',(#1135,#1136,#1137,#1138,#1139,#1140,#1141,#1142,#1143,
#1144,#1145,#1146,#1147,#1148,#1149,#1150,#1151,#1152,#1153,#1154,#1155,
#1156,#1157,#1158));
#194=EDGE_LOOP('',(#1159));
#195=EDGE_LOOP('',(#1160,#1161,#1162,#1163,#1164,#1165,#1166,#1167,#1168,
#1169,#1170,#1171,#1172,#1173,#1174,#1175,#1176,#1177,#1178,#1179,#1180,
#1181,#1182,#1183));
#196=EDGE_LOOP('',(#1184));
#197=EDGE_LOOP('',(#1185));
#198=LINE('',#1814,#318);
#199=LINE('',#1820,#319);
#200=LINE('',#1827,#320);
#201=LINE('',#1830,#321);
#202=LINE('',#1833,#322);
#203=LINE('',#1835,#323);
#204=LINE('',#1836,#324);
#205=LINE('',#1842,#325);
#206=LINE('',#1845,#326);
#207=LINE('',#1847,#327);
#208=LINE('',#1848,#328);
#209=LINE('',#1854,#329);
#210=LINE('',#1857,#330);
#211=LINE('',#1859,#331);
#212=LINE('',#1860,#332);
#213=LINE('',#1866,#333);
#214=LINE('',#1869,#334);
#215=LINE('',#1871,#335);
#216=LINE('',#1872,#336);
#217=LINE('',#1878,#337);
#218=LINE('',#1884,#338);
#219=LINE('',#1887,#339);
#220=LINE('',#1889,#340);
#221=LINE('',#1890,#341);
#222=LINE('',#1896,#342);
#223=LINE('',#1899,#343);
#224=LINE('',#1901,#344);
#225=LINE('',#1902,#345);
#226=LINE('',#1908,#346);
#227=LINE('',#1911,#347);
#228=LINE('',#1913,#348);
#229=LINE('',#1914,#349);
#230=LINE('',#1920,#350);
#231=LINE('',#1923,#351);
#232=LINE('',#1925,#352);
#233=LINE('',#1926,#353);
#234=LINE('',#1932,#354);
#235=LINE('',#1935,#355);
#236=LINE('',#1937,#356);
#237=LINE('',#1938,#357);
#238=LINE('',#1944,#358);
#239=LINE('',#1947,#359);
#240=LINE('',#1949,#360);
#241=LINE('',#1950,#361);
#242=LINE('',#1956,#362);
#243=LINE('',#1959,#363);
#244=LINE('',#1961,#364);
#245=LINE('',#1962,#365);
#246=LINE('',#1970,#366);
#247=LINE('',#1977,#367);
#248=LINE('',#1980,#368);
#249=LINE('',#1983,#369);
#250=LINE('',#1985,#370);
#251=LINE('',#1986,#371);
#252=LINE('',#1992,#372);
#253=LINE('',#1995,#373);
#254=LINE('',#1997,#374);
#255=LINE('',#1998,#375);
#256=LINE('',#2004,#376);
#257=LINE('',#2007,#377);
#258=LINE('',#2009,#378);
#259=LINE('',#2010,#379);
#260=LINE('',#2016,#380);
#261=LINE('',#2019,#381);
#262=LINE('',#2021,#382);
#263=LINE('',#2022,#383);
#264=LINE('',#2028,#384);
#265=LINE('',#2031,#385);
#266=LINE('',#2033,#386);
#267=LINE('',#2034,#387);
#268=LINE('',#2040,#388);
#269=LINE('',#2046,#389);
#270=LINE('',#2049,#390);
#271=LINE('',#2051,#391);
#272=LINE('',#2052,#392);
#273=LINE('',#2058,#393);
#274=LINE('',#2061,#394);
#275=LINE('',#2063,#395);
#276=LINE('',#2064,#396);
#277=LINE('',#2070,#397);
#278=LINE('',#2073,#398);
#279=LINE('',#2075,#399);
#280=LINE('',#2076,#400);
#281=LINE('',#2082,#401);
#282=LINE('',#2085,#402);
#283=LINE('',#2087,#403);
#284=LINE('',#2088,#404);
#285=LINE('',#2094,#405);
#286=LINE('',#2100,#406);
#287=LINE('',#2103,#407);
#288=LINE('',#2105,#408);
#289=LINE('',#2106,#409);
#290=LINE('',#2112,#410);
#291=LINE('',#2114,#411);
#292=LINE('',#2115,#412);
#293=LINE('',#2120,#413);
#294=LINE('',#2127,#414);
#295=LINE('',#2130,#415);
#296=LINE('',#2136,#416);
#297=LINE('',#2139,#417);
#298=LINE('',#2141,#418);
#299=LINE('',#2142,#419);
#300=LINE('',#2145,#420);
#301=LINE('',#2147,#421);
#302=LINE('',#2148,#422);
#303=LINE('',#2151,#423);
#304=LINE('',#2153,#424);
#305=LINE('',#2154,#425);
#306=LINE('',#2157,#426);
#307=LINE('',#2159,#427);
#308=LINE('',#2160,#428);
#309=LINE('',#2163,#429);
#310=LINE('',#2165,#430);
#311=LINE('',#2166,#431);
#312=LINE('',#2169,#432);
#313=LINE('',#2171,#433);
#314=LINE('',#2172,#434);
#315=LINE('',#2175,#435);
#316=LINE('',#2177,#436);
#317=LINE('',#2178,#437);
#318=VECTOR('',#1433,0.999999999999996);
#319=VECTOR('',#1440,1.);
#320=VECTOR('',#1447,10.);
#321=VECTOR('',#1450,10.);
#322=VECTOR('',#1453,10.);
#323=VECTOR('',#1454,10.);
#324=VECTOR('',#1455,10.);
#325=VECTOR('',#1462,10.);
#326=VECTOR('',#1465,10.);
#327=VECTOR('',#1466,10.);
#328=VECTOR('',#1467,10.);
#329=VECTOR('',#1474,10.);
#330=VECTOR('',#1477,10.);
#331=VECTOR('',#1478,10.);
#332=VECTOR('',#1479,10.);
#333=VECTOR('',#1486,10.);
#334=VECTOR('',#1489,10.);
#335=VECTOR('',#1490,10.);
#336=VECTOR('',#1491,10.);
#337=VECTOR('',#1498,10.);
#338=VECTOR('',#1505,10.);
#339=VECTOR('',#1508,10.);
#340=VECTOR('',#1509,10.);
#341=VECTOR('',#1510,10.);
#342=VECTOR('',#1517,10.);
#343=VECTOR('',#1520,10.);
#344=VECTOR('',#1521,10.);
#345=VECTOR('',#1522,10.);
#346=VECTOR('',#1529,10.);
#347=VECTOR('',#1532,10.);
#348=VECTOR('',#1533,10.);
#349=VECTOR('',#1534,10.);
#350=VECTOR('',#1541,10.);
#351=VECTOR('',#1544,10.);
#352=VECTOR('',#1545,10.);
#353=VECTOR('',#1546,10.);
#354=VECTOR('',#1553,10.);
#355=VECTOR('',#1556,10.);
#356=VECTOR('',#1557,10.);
#357=VECTOR('',#1558,10.);
#358=VECTOR('',#1565,10.);
#359=VECTOR('',#1568,10.);
#360=VECTOR('',#1569,10.);
#361=VECTOR('',#1570,10.);
#362=VECTOR('',#1577,10.);
#363=VECTOR('',#1580,10.);
#364=VECTOR('',#1581,10.);
#365=VECTOR('',#1582,10.);
#366=VECTOR('',#1593,1.);
#367=VECTOR('',#1600,10.);
#368=VECTOR('',#1603,10.);
#369=VECTOR('',#1606,10.);
#370=VECTOR('',#1607,10.);
#371=VECTOR('',#1608,10.);
#372=VECTOR('',#1615,10.);
#373=VECTOR('',#1618,10.);
#374=VECTOR('',#1619,10.);
#375=VECTOR('',#1620,10.);
#376=VECTOR('',#1627,10.);
#377=VECTOR('',#1630,10.);
#378=VECTOR('',#1631,10.);
#379=VECTOR('',#1632,10.);
#380=VECTOR('',#1639,10.);
#381=VECTOR('',#1642,10.);
#382=VECTOR('',#1643,10.);
#383=VECTOR('',#1644,10.);
#384=VECTOR('',#1651,10.);
#385=VECTOR('',#1654,10.);
#386=VECTOR('',#1655,10.);
#387=VECTOR('',#1656,10.);
#388=VECTOR('',#1663,10.);
#389=VECTOR('',#1670,10.);
#390=VECTOR('',#1673,10.);
#391=VECTOR('',#1674,10.);
#392=VECTOR('',#1675,10.);
#393=VECTOR('',#1682,10.);
#394=VECTOR('',#1685,10.);
#395=VECTOR('',#1686,10.);
#396=VECTOR('',#1687,10.);
#397=VECTOR('',#1694,10.);
#398=VECTOR('',#1697,10.);
#399=VECTOR('',#1698,10.);
#400=VECTOR('',#1699,10.);
#401=VECTOR('',#1706,10.);
#402=VECTOR('',#1709,10.);
#403=VECTOR('',#1710,10.);
#404=VECTOR('',#1711,10.);
#405=VECTOR('',#1718,10.);
#406=VECTOR('',#1725,10.);
#407=VECTOR('',#1728,10.);
#408=VECTOR('',#1729,10.);
#409=VECTOR('',#1730,10.);
#410=VECTOR('',#1737,10.);
#411=VECTOR('',#1740,10.);
#412=VECTOR('',#1741,10.);
#413=VECTOR('',#1746,0.999999999999996);
#414=VECTOR('',#1753,10.);
#415=VECTOR('',#1756,10.);
#416=VECTOR('',#1763,10.);
#417=VECTOR('',#1766,10.);
#418=VECTOR('',#1767,10.);
#419=VECTOR('',#1768,10.);
#420=VECTOR('',#1771,10.);
#421=VECTOR('',#1772,10.);
#422=VECTOR('',#1773,10.);
#423=VECTOR('',#1776,10.);
#424=VECTOR('',#1777,10.);
#425=VECTOR('',#1778,10.);
#426=VECTOR('',#1781,10.);
#427=VECTOR('',#1782,10.);
#428=VECTOR('',#1783,10.);
#429=VECTOR('',#1786,10.);
#430=VECTOR('',#1787,10.);
#431=VECTOR('',#1788,10.);
#432=VECTOR('',#1791,10.);
#433=VECTOR('',#1792,10.);
#434=VECTOR('',#1793,10.);
#435=VECTOR('',#1796,10.);
#436=VECTOR('',#1797,10.);
#437=VECTOR('',#1798,10.);
#438=CIRCLE('',#1298,0.999999999999996);
#439=CIRCLE('',#1299,0.999999999999996);
#440=CIRCLE('',#1301,1.);
#441=CIRCLE('',#1302,1.);
#442=CIRCLE('',#1304,0.249999999999967);
#443=CIRCLE('',#1305,0.249999999999967);
#444=CIRCLE('',#1308,0.250000000000024);
#445=CIRCLE('',#1309,0.250000000000024);
#446=CIRCLE('',#1312,0.249999999999994);
#447=CIRCLE('',#1313,0.249999999999994);
#448=CIRCLE('',#1316,2.99999999993811);
#449=CIRCLE('',#1317,2.99999999993811);
#450=CIRCLE('',#1320,0.250000000000061);
#451=CIRCLE('',#1321,0.250000000000061);
#452=CIRCLE('',#1323,2.99999999999997);
#453=CIRCLE('',#1324,2.99999999999997);
#454=CIRCLE('',#1327,0.249999999999984);
#455=CIRCLE('',#1328,0.249999999999984);
#456=CIRCLE('',#1331,0.249999999993057);
#457=CIRCLE('',#1332,0.249999999993057);
#458=CIRCLE('',#1335,0.499999999999854);
#459=CIRCLE('',#1336,0.499999999999854);
#460=CIRCLE('',#1339,0.250000000000004);
#461=CIRCLE('',#1340,0.250000000000004);
#462=CIRCLE('',#1343,0.249999999999932);
#463=CIRCLE('',#1344,0.249999999999932);
#464=CIRCLE('',#1347,0.249999999999986);
#465=CIRCLE('',#1348,0.249999999999986);
#466=CIRCLE('',#1351,0.150000000000051);
#467=CIRCLE('',#1352,0.150000000000051);
#468=CIRCLE('',#1354,1.);
#469=CIRCLE('',#1355,1.);
#470=CIRCLE('',#1357,0.249999999999915);
#471=CIRCLE('',#1358,0.249999999999915);
#472=CIRCLE('',#1361,0.250000000000039);
#473=CIRCLE('',#1362,0.250000000000039);
#474=CIRCLE('',#1365,0.500000000000114);
#475=CIRCLE('',#1366,0.500000000000114);
#476=CIRCLE('',#1369,0.249999999999842);
#477=CIRCLE('',#1370,0.249999999999842);
#478=CIRCLE('',#1373,0.2500000000005);
#479=CIRCLE('',#1374,0.2500000000005);
#480=CIRCLE('',#1377,2.99999999999997);
#481=CIRCLE('',#1378,2.99999999999997);
#482=CIRCLE('',#1380,0.250000000000021);
#483=CIRCLE('',#1381,0.250000000000021);
#484=CIRCLE('',#1384,3.);
#485=CIRCLE('',#1385,3.);
#486=CIRCLE('',#1388,0.249999999920014);
#487=CIRCLE('',#1389,0.249999999920014);
#488=CIRCLE('',#1392,0.25000000000002);
#489=CIRCLE('',#1393,0.25000000000002);
#490=CIRCLE('',#1396,0.249999999999967);
#491=CIRCLE('',#1397,0.249999999999967);
#492=CIRCLE('',#1399,0.150000000000041);
#493=CIRCLE('',#1400,0.150000000000041);
#494=CIRCLE('',#1403,0.249999999999986);
#495=CIRCLE('',#1404,0.249999999999986);
#496=CIRCLE('',#1407,0.999999999999996);
#497=CIRCLE('',#1408,0.999999999999996);
#498=CIRCLE('',#1410,25.);
#499=CIRCLE('',#1411,25.);
#500=CIRCLE('',#1413,2.);
#501=CIRCLE('',#1414,2.);
#502=CIRCLE('',#1423,2.00000000000001);
#503=CIRCLE('',#1424,2.00000000000001);
#504=VERTEX_POINT('',#1811);
#505=VERTEX_POINT('',#1813);
#506=VERTEX_POINT('',#1817);
#507=VERTEX_POINT('',#1819);
#508=VERTEX_POINT('',#1823);
#509=VERTEX_POINT('',#1824);
#510=VERTEX_POINT('',#1826);
#511=VERTEX_POINT('',#1828);
#512=VERTEX_POINT('',#1832);
#513=VERTEX_POINT('',#1834);
#514=VERTEX_POINT('',#1838);
#515=VERTEX_POINT('',#1840);
#516=VERTEX_POINT('',#1844);
#517=VERTEX_POINT('',#1846);
#518=VERTEX_POINT('',#1850);
#519=VERTEX_POINT('',#1852);
#520=VERTEX_POINT('',#1856);
#521=VERTEX_POINT('',#1858);
#522=VERTEX_POINT('',#1862);
#523=VERTEX_POINT('',#1864);
#524=VERTEX_POINT('',#1868);
#525=VERTEX_POINT('',#1870);
#526=VERTEX_POINT('',#1874);
#527=VERTEX_POINT('',#1876);
#528=VERTEX_POINT('',#1880);
#529=VERTEX_POINT('',#1882);
#530=VERTEX_POINT('',#1886);
#531=VERTEX_POINT('',#1888);
#532=VERTEX_POINT('',#1892);
#533=VERTEX_POINT('',#1894);
#534=VERTEX_POINT('',#1898);
#535=VERTEX_POINT('',#1900);
#536=VERTEX_POINT('',#1904);
#537=VERTEX_POINT('',#1906);
#538=VERTEX_POINT('',#1910);
#539=VERTEX_POINT('',#1912);
#540=VERTEX_POINT('',#1916);
#541=VERTEX_POINT('',#1918);
#542=VERTEX_POINT('',#1922);
#543=VERTEX_POINT('',#1924);
#544=VERTEX_POINT('',#1928);
#545=VERTEX_POINT('',#1930);
#546=VERTEX_POINT('',#1934);
#547=VERTEX_POINT('',#1936);
#548=VERTEX_POINT('',#1940);
#549=VERTEX_POINT('',#1942);
#550=VERTEX_POINT('',#1946);
#551=VERTEX_POINT('',#1948);
#552=VERTEX_POINT('',#1952);
#553=VERTEX_POINT('',#1954);
#554=VERTEX_POINT('',#1958);
#555=VERTEX_POINT('',#1960);
#556=VERTEX_POINT('',#1967);
#557=VERTEX_POINT('',#1969);
#558=VERTEX_POINT('',#1973);
#559=VERTEX_POINT('',#1974);
#560=VERTEX_POINT('',#1976);
#561=VERTEX_POINT('',#1978);
#562=VERTEX_POINT('',#1982);
#563=VERTEX_POINT('',#1984);
#564=VERTEX_POINT('',#1988);
#565=VERTEX_POINT('',#1990);
#566=VERTEX_POINT('',#1994);
#567=VERTEX_POINT('',#1996);
#568=VERTEX_POINT('',#2000);
#569=VERTEX_POINT('',#2002);
#570=VERTEX_POINT('',#2006);
#571=VERTEX_POINT('',#2008);
#572=VERTEX_POINT('',#2012);
#573=VERTEX_POINT('',#2014);
#574=VERTEX_POINT('',#2018);
#575=VERTEX_POINT('',#2020);
#576=VERTEX_POINT('',#2024);
#577=VERTEX_POINT('',#2026);
#578=VERTEX_POINT('',#2030);
#579=VERTEX_POINT('',#2032);
#580=VERTEX_POINT('',#2036);
#581=VERTEX_POINT('',#2038);
#582=VERTEX_POINT('',#2042);
#583=VERTEX_POINT('',#2044);
#584=VERTEX_POINT('',#2048);
#585=VERTEX_POINT('',#2050);
#586=VERTEX_POINT('',#2054);
#587=VERTEX_POINT('',#2056);
#588=VERTEX_POINT('',#2060);
#589=VERTEX_POINT('',#2062);
#590=VERTEX_POINT('',#2066);
#591=VERTEX_POINT('',#2068);
#592=VERTEX_POINT('',#2072);
#593=VERTEX_POINT('',#2074);
#594=VERTEX_POINT('',#2078);
#595=VERTEX_POINT('',#2080);
#596=VERTEX_POINT('',#2084);
#597=VERTEX_POINT('',#2086);
#598=VERTEX_POINT('',#2090);
#599=VERTEX_POINT('',#2092);
#600=VERTEX_POINT('',#2096);
#601=VERTEX_POINT('',#2098);
#602=VERTEX_POINT('',#2102);
#603=VERTEX_POINT('',#2104);
#604=VERTEX_POINT('',#2108);
#605=VERTEX_POINT('',#2110);
#606=VERTEX_POINT('',#2117);
#607=VERTEX_POINT('',#2119);
#608=VERTEX_POINT('',#2123);
#609=VERTEX_POINT('',#2124);
#610=VERTEX_POINT('',#2126);
#611=VERTEX_POINT('',#2128);
#612=VERTEX_POINT('',#2132);
#613=VERTEX_POINT('',#2134);
#614=VERTEX_POINT('',#2138);
#615=VERTEX_POINT('',#2140);
#616=VERTEX_POINT('',#2144);
#617=VERTEX_POINT('',#2146);
#618=VERTEX_POINT('',#2150);
#619=VERTEX_POINT('',#2152);
#620=VERTEX_POINT('',#2156);
#621=VERTEX_POINT('',#2158);
#622=VERTEX_POINT('',#2162);
#623=VERTEX_POINT('',#2164);
#624=VERTEX_POINT('',#2168);
#625=VERTEX_POINT('',#2170);
#626=VERTEX_POINT('',#2174);
#627=VERTEX_POINT('',#2176);
#628=EDGE_CURVE('',#504,#504,#438,.T.);
#629=EDGE_CURVE('',#504,#505,#198,.T.);
#630=EDGE_CURVE('',#505,#505,#439,.T.);
#631=EDGE_CURVE('',#506,#506,#440,.T.);
#632=EDGE_CURVE('',#506,#507,#199,.T.);
#633=EDGE_CURVE('',#507,#507,#441,.T.);
#634=EDGE_CURVE('',#508,#509,#442,.T.);
#635=EDGE_CURVE('',#509,#510,#200,.T.);
#636=EDGE_CURVE('',#511,#510,#443,.T.);
#637=EDGE_CURVE('',#508,#511,#201,.T.);
#638=EDGE_CURVE('',#512,#508,#202,.T.);
#639=EDGE_CURVE('',#513,#511,#203,.T.);
#640=EDGE_CURVE('',#512,#513,#204,.T.);
#641=EDGE_CURVE('',#514,#512,#444,.T.);
#642=EDGE_CURVE('',#515,#513,#445,.T.);
#643=EDGE_CURVE('',#514,#515,#205,.T.);
#644=EDGE_CURVE('',#516,#514,#206,.T.);
#645=EDGE_CURVE('',#517,#515,#207,.T.);
#646=EDGE_CURVE('',#516,#517,#208,.T.);
#647=EDGE_CURVE('',#518,#516,#446,.T.);
#648=EDGE_CURVE('',#519,#517,#447,.T.);
#649=EDGE_CURVE('',#518,#519,#209,.T.);
#650=EDGE_CURVE('',#520,#518,#210,.T.);
#651=EDGE_CURVE('',#521,#519,#211,.T.);
#652=EDGE_CURVE('',#520,#521,#212,.T.);
#653=EDGE_CURVE('',#522,#520,#448,.T.);
#654=EDGE_CURVE('',#523,#521,#449,.T.);
#655=EDGE_CURVE('',#522,#523,#213,.T.);
#656=EDGE_CURVE('',#524,#522,#214,.T.);
#657=EDGE_CURVE('',#525,#523,#215,.T.);
#658=EDGE_CURVE('',#524,#525,#216,.T.);
#659=EDGE_CURVE('',#526,#524,#450,.T.);
#660=EDGE_CURVE('',#527,#525,#451,.T.);
#661=EDGE_CURVE('',#526,#527,#217,.T.);
#662=EDGE_CURVE('',#528,#526,#452,.T.);
#663=EDGE_CURVE('',#529,#527,#453,.T.);
#664=EDGE_CURVE('',#528,#529,#218,.T.);
#665=EDGE_CURVE('',#530,#528,#219,.T.);
#666=EDGE_CURVE('',#531,#529,#220,.T.);
#667=EDGE_CURVE('',#530,#531,#221,.T.);
#668=EDGE_CURVE('',#532,#530,#454,.T.);
#669=EDGE_CURVE('',#533,#531,#455,.T.);
#670=EDGE_CURVE('',#532,#533,#222,.T.);
#671=EDGE_CURVE('',#534,#532,#223,.T.);
#672=EDGE_CURVE('',#535,#533,#224,.T.);
#673=EDGE_CURVE('',#534,#535,#225,.T.);
#674=EDGE_CURVE('',#536,#534,#456,.T.);
#675=EDGE_CURVE('',#537,#535,#457,.T.);
#676=EDGE_CURVE('',#536,#537,#226,.T.);
#677=EDGE_CURVE('',#538,#536,#227,.T.);
#678=EDGE_CURVE('',#539,#537,#228,.T.);
#679=EDGE_CURVE('',#538,#539,#229,.T.);
#680=EDGE_CURVE('',#540,#538,#458,.T.);
#681=EDGE_CURVE('',#541,#539,#459,.T.);
#682=EDGE_CURVE('',#540,#541,#230,.T.);
#683=EDGE_CURVE('',#542,#540,#231,.T.);
#684=EDGE_CURVE('',#543,#541,#232,.T.);
#685=EDGE_CURVE('',#542,#543,#233,.T.);
#686=EDGE_CURVE('',#544,#542,#460,.T.);
#687=EDGE_CURVE('',#545,#543,#461,.T.);
#688=EDGE_CURVE('',#544,#545,#234,.T.);
#689=EDGE_CURVE('',#546,#544,#235,.T.);
#690=EDGE_CURVE('',#547,#545,#236,.T.);
#691=EDGE_CURVE('',#546,#547,#237,.T.);
#692=EDGE_CURVE('',#548,#546,#462,.T.);
#693=EDGE_CURVE('',#549,#547,#463,.T.);
#694=EDGE_CURVE('',#548,#549,#238,.T.);
#695=EDGE_CURVE('',#550,#548,#239,.T.);
#696=EDGE_CURVE('',#551,#549,#240,.T.);
#697=EDGE_CURVE('',#550,#551,#241,.T.);
#698=EDGE_CURVE('',#552,#550,#464,.T.);
#699=EDGE_CURVE('',#553,#551,#465,.T.);
#700=EDGE_CURVE('',#552,#553,#242,.T.);
#701=EDGE_CURVE('',#554,#552,#243,.T.);
#702=EDGE_CURVE('',#555,#553,#244,.T.);
#703=EDGE_CURVE('',#554,#555,#245,.T.);
#704=EDGE_CURVE('',#509,#554,#466,.T.);
#705=EDGE_CURVE('',#510,#555,#467,.T.);
#706=EDGE_CURVE('',#556,#556,#468,.T.);
#707=EDGE_CURVE('',#556,#557,#246,.T.);
#708=EDGE_CURVE('',#557,#557,#469,.T.);
#709=EDGE_CURVE('',#558,#559,#470,.T.);
#710=EDGE_CURVE('',#559,#560,#247,.T.);
#711=EDGE_CURVE('',#561,#560,#471,.T.);
#712=EDGE_CURVE('',#558,#561,#248,.T.);
#713=EDGE_CURVE('',#562,#558,#249,.T.);
#714=EDGE_CURVE('',#563,#561,#250,.T.);
#715=EDGE_CURVE('',#562,#563,#251,.T.);
#716=EDGE_CURVE('',#564,#562,#472,.T.);
#717=EDGE_CURVE('',#565,#563,#473,.T.);
#718=EDGE_CURVE('',#564,#565,#252,.T.);
#719=EDGE_CURVE('',#566,#564,#253,.T.);
#720=EDGE_CURVE('',#567,#565,#254,.T.);
#721=EDGE_CURVE('',#566,#567,#255,.T.);
#722=EDGE_CURVE('',#568,#566,#474,.T.);
#723=EDGE_CURVE('',#569,#567,#475,.T.);
#724=EDGE_CURVE('',#568,#569,#256,.T.);
#725=EDGE_CURVE('',#570,#568,#257,.T.);
#726=EDGE_CURVE('',#571,#569,#258,.T.);
#727=EDGE_CURVE('',#570,#571,#259,.T.);
#728=EDGE_CURVE('',#572,#570,#476,.T.);
#729=EDGE_CURVE('',#573,#571,#477,.T.);
#730=EDGE_CURVE('',#572,#573,#260,.T.);
#731=EDGE_CURVE('',#574,#572,#261,.T.);
#732=EDGE_CURVE('',#575,#573,#262,.T.);
#733=EDGE_CURVE('',#574,#575,#263,.T.);
#734=EDGE_CURVE('',#576,#574,#478,.T.);
#735=EDGE_CURVE('',#577,#575,#479,.T.);
#736=EDGE_CURVE('',#576,#577,#264,.T.);
#737=EDGE_CURVE('',#578,#576,#265,.T.);
#738=EDGE_CURVE('',#579,#577,#266,.T.);
#739=EDGE_CURVE('',#578,#579,#267,.T.);
#740=EDGE_CURVE('',#580,#578,#480,.T.);
#741=EDGE_CURVE('',#581,#579,#481,.T.);
#742=EDGE_CURVE('',#580,#581,#268,.T.);
#743=EDGE_CURVE('',#582,#580,#482,.T.);
#744=EDGE_CURVE('',#583,#581,#483,.T.);
#745=EDGE_CURVE('',#582,#583,#269,.T.);
#746=EDGE_CURVE('',#584,#582,#270,.T.);
#747=EDGE_CURVE('',#585,#583,#271,.T.);
#748=EDGE_CURVE('',#584,#585,#272,.T.);
#749=EDGE_CURVE('',#586,#584,#484,.T.);
#750=EDGE_CURVE('',#587,#585,#485,.T.);
#751=EDGE_CURVE('',#586,#587,#273,.T.);
#752=EDGE_CURVE('',#588,#586,#274,.T.);
#753=EDGE_CURVE('',#589,#587,#275,.T.);
#754=EDGE_CURVE('',#588,#589,#276,.T.);
#755=EDGE_CURVE('',#590,#588,#486,.T.);
#756=EDGE_CURVE('',#591,#589,#487,.T.);
#757=EDGE_CURVE('',#590,#591,#277,.T.);
#758=EDGE_CURVE('',#592,#590,#278,.T.);
#759=EDGE_CURVE('',#593,#591,#279,.T.);
#760=EDGE_CURVE('',#592,#593,#280,.T.);
#761=EDGE_CURVE('',#594,#592,#488,.T.);
#762=EDGE_CURVE('',#595,#593,#489,.T.);
#763=EDGE_CURVE('',#594,#595,#281,.T.);
#764=EDGE_CURVE('',#596,#594,#282,.T.);
#765=EDGE_CURVE('',#597,#595,#283,.T.);
#766=EDGE_CURVE('',#596,#597,#284,.T.);
#767=EDGE_CURVE('',#598,#596,#490,.T.);
#768=EDGE_CURVE('',#599,#597,#491,.T.);
#769=EDGE_CURVE('',#598,#599,#285,.T.);
#770=EDGE_CURVE('',#600,#598,#492,.T.);
#771=EDGE_CURVE('',#601,#599,#493,.T.);
#772=EDGE_CURVE('',#600,#601,#286,.T.);
#773=EDGE_CURVE('',#602,#600,#287,.T.);
#774=EDGE_CURVE('',#603,#601,#288,.T.);
#775=EDGE_CURVE('',#602,#603,#289,.T.);
#776=EDGE_CURVE('',#604,#602,#494,.T.);
#777=EDGE_CURVE('',#605,#603,#495,.T.);
#778=EDGE_CURVE('',#604,#605,#290,.T.);
#779=EDGE_CURVE('',#559,#604,#291,.T.);
#780=EDGE_CURVE('',#560,#605,#292,.T.);
#781=EDGE_CURVE('',#606,#606,#496,.T.);
#782=EDGE_CURVE('',#606,#607,#293,.T.);
#783=EDGE_CURVE('',#607,#607,#497,.T.);
#784=EDGE_CURVE('',#608,#609,#498,.T.);
#785=EDGE_CURVE('',#608,#610,#294,.T.);
#786=EDGE_CURVE('',#611,#610,#499,.T.);
#787=EDGE_CURVE('',#609,#611,#295,.T.);
#788=EDGE_CURVE('',#612,#609,#500,.T.);
#789=EDGE_CURVE('',#613,#611,#501,.T.);
#790=EDGE_CURVE('',#612,#613,#296,.T.);
#791=EDGE_CURVE('',#614,#612,#297,.T.);
#792=EDGE_CURVE('',#615,#613,#298,.T.);
#793=EDGE_CURVE('',#614,#615,#299,.T.);
#794=EDGE_CURVE('',#616,#614,#300,.T.);
#795=EDGE_CURVE('',#617,#615,#301,.T.);
#796=EDGE_CURVE('',#616,#617,#302,.T.);
#797=EDGE_CURVE('',#618,#616,#303,.T.);
#798=EDGE_CURVE('',#619,#617,#304,.T.);
#799=EDGE_CURVE('',#618,#619,#305,.T.);
#800=EDGE_CURVE('',#620,#618,#306,.T.);
#801=EDGE_CURVE('',#621,#619,#307,.T.);
#802=EDGE_CURVE('',#620,#621,#308,.T.);
#803=EDGE_CURVE('',#622,#620,#309,.T.);
#804=EDGE_CURVE('',#623,#621,#310,.T.);
#805=EDGE_CURVE('',#622,#623,#311,.T.);
#806=EDGE_CURVE('',#624,#622,#312,.T.);
#807=EDGE_CURVE('',#625,#623,#313,.T.);
#808=EDGE_CURVE('',#624,#625,#314,.T.);
#809=EDGE_CURVE('',#626,#624,#315,.T.);
#810=EDGE_CURVE('',#627,#625,#316,.T.);
#811=EDGE_CURVE('',#626,#627,#317,.T.);
#812=EDGE_CURVE('',#608,#626,#502,.T.);
#813=EDGE_CURVE('',#610,#627,#503,.T.);
#814=ORIENTED_EDGE('',*,*,#628,.F.);
#815=ORIENTED_EDGE('',*,*,#629,.T.);
#816=ORIENTED_EDGE('',*,*,#630,.T.);
#817=ORIENTED_EDGE('',*,*,#629,.F.);
#818=ORIENTED_EDGE('',*,*,#631,.F.);
#819=ORIENTED_EDGE('',*,*,#632,.T.);
#820=ORIENTED_EDGE('',*,*,#633,.T.);
#821=ORIENTED_EDGE('',*,*,#632,.F.);
#822=ORIENTED_EDGE('',*,*,#634,.T.);
#823=ORIENTED_EDGE('',*,*,#635,.T.);
#824=ORIENTED_EDGE('',*,*,#636,.F.);
#825=ORIENTED_EDGE('',*,*,#637,.F.);
#826=ORIENTED_EDGE('',*,*,#638,.T.);
#827=ORIENTED_EDGE('',*,*,#637,.T.);
#828=ORIENTED_EDGE('',*,*,#639,.F.);
#829=ORIENTED_EDGE('',*,*,#640,.F.);
#830=ORIENTED_EDGE('',*,*,#641,.T.);
#831=ORIENTED_EDGE('',*,*,#640,.T.);
#832=ORIENTED_EDGE('',*,*,#642,.F.);
#833=ORIENTED_EDGE('',*,*,#643,.F.);
#834=ORIENTED_EDGE('',*,*,#644,.T.);
#835=ORIENTED_EDGE('',*,*,#643,.T.);
#836=ORIENTED_EDGE('',*,*,#645,.F.);
#837=ORIENTED_EDGE('',*,*,#646,.F.);
#838=ORIENTED_EDGE('',*,*,#647,.T.);
#839=ORIENTED_EDGE('',*,*,#646,.T.);
#840=ORIENTED_EDGE('',*,*,#648,.F.);
#841=ORIENTED_EDGE('',*,*,#649,.F.);
#842=ORIENTED_EDGE('',*,*,#650,.T.);
#843=ORIENTED_EDGE('',*,*,#649,.T.);
#844=ORIENTED_EDGE('',*,*,#651,.F.);
#845=ORIENTED_EDGE('',*,*,#652,.F.);
#846=ORIENTED_EDGE('',*,*,#653,.T.);
#847=ORIENTED_EDGE('',*,*,#652,.T.);
#848=ORIENTED_EDGE('',*,*,#654,.F.);
#849=ORIENTED_EDGE('',*,*,#655,.F.);
#850=ORIENTED_EDGE('',*,*,#656,.T.);
#851=ORIENTED_EDGE('',*,*,#655,.T.);
#852=ORIENTED_EDGE('',*,*,#657,.F.);
#853=ORIENTED_EDGE('',*,*,#658,.F.);
#854=ORIENTED_EDGE('',*,*,#659,.T.);
#855=ORIENTED_EDGE('',*,*,#658,.T.);
#856=ORIENTED_EDGE('',*,*,#660,.F.);
#857=ORIENTED_EDGE('',*,*,#661,.F.);
#858=ORIENTED_EDGE('',*,*,#662,.T.);
#859=ORIENTED_EDGE('',*,*,#661,.T.);
#860=ORIENTED_EDGE('',*,*,#663,.F.);
#861=ORIENTED_EDGE('',*,*,#664,.F.);
#862=ORIENTED_EDGE('',*,*,#665,.T.);
#863=ORIENTED_EDGE('',*,*,#664,.T.);
#864=ORIENTED_EDGE('',*,*,#666,.F.);
#865=ORIENTED_EDGE('',*,*,#667,.F.);
#866=ORIENTED_EDGE('',*,*,#668,.T.);
#867=ORIENTED_EDGE('',*,*,#667,.T.);
#868=ORIENTED_EDGE('',*,*,#669,.F.);
#869=ORIENTED_EDGE('',*,*,#670,.F.);
#870=ORIENTED_EDGE('',*,*,#671,.T.);
#871=ORIENTED_EDGE('',*,*,#670,.T.);
#872=ORIENTED_EDGE('',*,*,#672,.F.);
#873=ORIENTED_EDGE('',*,*,#673,.F.);
#874=ORIENTED_EDGE('',*,*,#674,.T.);
#875=ORIENTED_EDGE('',*,*,#673,.T.);
#876=ORIENTED_EDGE('',*,*,#675,.F.);
#877=ORIENTED_EDGE('',*,*,#676,.F.);
#878=ORIENTED_EDGE('',*,*,#677,.T.);
#879=ORIENTED_EDGE('',*,*,#676,.T.);
#880=ORIENTED_EDGE('',*,*,#678,.F.);
#881=ORIENTED_EDGE('',*,*,#679,.F.);
#882=ORIENTED_EDGE('',*,*,#680,.T.);
#883=ORIENTED_EDGE('',*,*,#679,.T.);
#884=ORIENTED_EDGE('',*,*,#681,.F.);
#885=ORIENTED_EDGE('',*,*,#682,.F.);
#886=ORIENTED_EDGE('',*,*,#683,.T.);
#887=ORIENTED_EDGE('',*,*,#682,.T.);
#888=ORIENTED_EDGE('',*,*,#684,.F.);
#889=ORIENTED_EDGE('',*,*,#685,.F.);
#890=ORIENTED_EDGE('',*,*,#686,.T.);
#891=ORIENTED_EDGE('',*,*,#685,.T.);
#892=ORIENTED_EDGE('',*,*,#687,.F.);
#893=ORIENTED_EDGE('',*,*,#688,.F.);
#894=ORIENTED_EDGE('',*,*,#689,.T.);
#895=ORIENTED_EDGE('',*,*,#688,.T.);
#896=ORIENTED_EDGE('',*,*,#690,.F.);
#897=ORIENTED_EDGE('',*,*,#691,.F.);
#898=ORIENTED_EDGE('',*,*,#692,.T.);
#899=ORIENTED_EDGE('',*,*,#691,.T.);
#900=ORIENTED_EDGE('',*,*,#693,.F.);
#901=ORIENTED_EDGE('',*,*,#694,.F.);
#902=ORIENTED_EDGE('',*,*,#695,.T.);
#903=ORIENTED_EDGE('',*,*,#694,.T.);
#904=ORIENTED_EDGE('',*,*,#696,.F.);
#905=ORIENTED_EDGE('',*,*,#697,.F.);
#906=ORIENTED_EDGE('',*,*,#698,.T.);
#907=ORIENTED_EDGE('',*,*,#697,.T.);
#908=ORIENTED_EDGE('',*,*,#699,.F.);
#909=ORIENTED_EDGE('',*,*,#700,.F.);
#910=ORIENTED_EDGE('',*,*,#701,.T.);
#911=ORIENTED_EDGE('',*,*,#700,.T.);
#912=ORIENTED_EDGE('',*,*,#702,.F.);
#913=ORIENTED_EDGE('',*,*,#703,.F.);
#914=ORIENTED_EDGE('',*,*,#704,.T.);
#915=ORIENTED_EDGE('',*,*,#703,.T.);
#916=ORIENTED_EDGE('',*,*,#705,.F.);
#917=ORIENTED_EDGE('',*,*,#635,.F.);
#918=ORIENTED_EDGE('',*,*,#706,.F.);
#919=ORIENTED_EDGE('',*,*,#707,.T.);
#920=ORIENTED_EDGE('',*,*,#708,.T.);
#921=ORIENTED_EDGE('',*,*,#707,.F.);
#922=ORIENTED_EDGE('',*,*,#709,.T.);
#923=ORIENTED_EDGE('',*,*,#710,.T.);
#924=ORIENTED_EDGE('',*,*,#711,.F.);
#925=ORIENTED_EDGE('',*,*,#712,.F.);
#926=ORIENTED_EDGE('',*,*,#713,.T.);
#927=ORIENTED_EDGE('',*,*,#712,.T.);
#928=ORIENTED_EDGE('',*,*,#714,.F.);
#929=ORIENTED_EDGE('',*,*,#715,.F.);
#930=ORIENTED_EDGE('',*,*,#716,.T.);
#931=ORIENTED_EDGE('',*,*,#715,.T.);
#932=ORIENTED_EDGE('',*,*,#717,.F.);
#933=ORIENTED_EDGE('',*,*,#718,.F.);
#934=ORIENTED_EDGE('',*,*,#719,.T.);
#935=ORIENTED_EDGE('',*,*,#718,.T.);
#936=ORIENTED_EDGE('',*,*,#720,.F.);
#937=ORIENTED_EDGE('',*,*,#721,.F.);
#938=ORIENTED_EDGE('',*,*,#722,.T.);
#939=ORIENTED_EDGE('',*,*,#721,.T.);
#940=ORIENTED_EDGE('',*,*,#723,.F.);
#941=ORIENTED_EDGE('',*,*,#724,.F.);
#942=ORIENTED_EDGE('',*,*,#725,.T.);
#943=ORIENTED_EDGE('',*,*,#724,.T.);
#944=ORIENTED_EDGE('',*,*,#726,.F.);
#945=ORIENTED_EDGE('',*,*,#727,.F.);
#946=ORIENTED_EDGE('',*,*,#728,.T.);
#947=ORIENTED_EDGE('',*,*,#727,.T.);
#948=ORIENTED_EDGE('',*,*,#729,.F.);
#949=ORIENTED_EDGE('',*,*,#730,.F.);
#950=ORIENTED_EDGE('',*,*,#731,.T.);
#951=ORIENTED_EDGE('',*,*,#730,.T.);
#952=ORIENTED_EDGE('',*,*,#732,.F.);
#953=ORIENTED_EDGE('',*,*,#733,.F.);
#954=ORIENTED_EDGE('',*,*,#734,.T.);
#955=ORIENTED_EDGE('',*,*,#733,.T.);
#956=ORIENTED_EDGE('',*,*,#735,.F.);
#957=ORIENTED_EDGE('',*,*,#736,.F.);
#958=ORIENTED_EDGE('',*,*,#737,.T.);
#959=ORIENTED_EDGE('',*,*,#736,.T.);
#960=ORIENTED_EDGE('',*,*,#738,.F.);
#961=ORIENTED_EDGE('',*,*,#739,.F.);
#962=ORIENTED_EDGE('',*,*,#740,.T.);
#963=ORIENTED_EDGE('',*,*,#739,.T.);
#964=ORIENTED_EDGE('',*,*,#741,.F.);
#965=ORIENTED_EDGE('',*,*,#742,.F.);
#966=ORIENTED_EDGE('',*,*,#743,.T.);
#967=ORIENTED_EDGE('',*,*,#742,.T.);
#968=ORIENTED_EDGE('',*,*,#744,.F.);
#969=ORIENTED_EDGE('',*,*,#745,.F.);
#970=ORIENTED_EDGE('',*,*,#746,.T.);
#971=ORIENTED_EDGE('',*,*,#745,.T.);
#972=ORIENTED_EDGE('',*,*,#747,.F.);
#973=ORIENTED_EDGE('',*,*,#748,.F.);
#974=ORIENTED_EDGE('',*,*,#749,.T.);
#975=ORIENTED_EDGE('',*,*,#748,.T.);
#976=ORIENTED_EDGE('',*,*,#750,.F.);
#977=ORIENTED_EDGE('',*,*,#751,.F.);
#978=ORIENTED_EDGE('',*,*,#752,.T.);
#979=ORIENTED_EDGE('',*,*,#751,.T.);
#980=ORIENTED_EDGE('',*,*,#753,.F.);
#981=ORIENTED_EDGE('',*,*,#754,.F.);
#982=ORIENTED_EDGE('',*,*,#755,.T.);
#983=ORIENTED_EDGE('',*,*,#754,.T.);
#984=ORIENTED_EDGE('',*,*,#756,.F.);
#985=ORIENTED_EDGE('',*,*,#757,.F.);
#986=ORIENTED_EDGE('',*,*,#758,.T.);
#987=ORIENTED_EDGE('',*,*,#757,.T.);
#988=ORIENTED_EDGE('',*,*,#759,.F.);
#989=ORIENTED_EDGE('',*,*,#760,.F.);
#990=ORIENTED_EDGE('',*,*,#761,.T.);
#991=ORIENTED_EDGE('',*,*,#760,.T.);
#992=ORIENTED_EDGE('',*,*,#762,.F.);
#993=ORIENTED_EDGE('',*,*,#763,.F.);
#994=ORIENTED_EDGE('',*,*,#764,.T.);
#995=ORIENTED_EDGE('',*,*,#763,.T.);
#996=ORIENTED_EDGE('',*,*,#765,.F.);
#997=ORIENTED_EDGE('',*,*,#766,.F.);
#998=ORIENTED_EDGE('',*,*,#767,.T.);
#999=ORIENTED_EDGE('',*,*,#766,.T.);
#1000=ORIENTED_EDGE('',*,*,#768,.F.);
#1001=ORIENTED_EDGE('',*,*,#769,.F.);
#1002=ORIENTED_EDGE('',*,*,#770,.T.);
#1003=ORIENTED_EDGE('',*,*,#769,.T.);
#1004=ORIENTED_EDGE('',*,*,#771,.F.);
#1005=ORIENTED_EDGE('',*,*,#772,.F.);
#1006=ORIENTED_EDGE('',*,*,#773,.T.);
#1007=ORIENTED_EDGE('',*,*,#772,.T.);
#1008=ORIENTED_EDGE('',*,*,#774,.F.);
#1009=ORIENTED_EDGE('',*,*,#775,.F.);
#1010=ORIENTED_EDGE('',*,*,#776,.T.);
#1011=ORIENTED_EDGE('',*,*,#775,.T.);
#1012=ORIENTED_EDGE('',*,*,#777,.F.);
#1013=ORIENTED_EDGE('',*,*,#778,.F.);
#1014=ORIENTED_EDGE('',*,*,#779,.T.);
#1015=ORIENTED_EDGE('',*,*,#778,.T.);
#1016=ORIENTED_EDGE('',*,*,#780,.F.);
#1017=ORIENTED_EDGE('',*,*,#710,.F.);
#1018=ORIENTED_EDGE('',*,*,#781,.F.);
#1019=ORIENTED_EDGE('',*,*,#782,.T.);
#1020=ORIENTED_EDGE('',*,*,#783,.T.);
#1021=ORIENTED_EDGE('',*,*,#782,.F.);
#1022=ORIENTED_EDGE('',*,*,#784,.F.);
#1023=ORIENTED_EDGE('',*,*,#785,.T.);
#1024=ORIENTED_EDGE('',*,*,#786,.F.);
#1025=ORIENTED_EDGE('',*,*,#787,.F.);
#1026=ORIENTED_EDGE('',*,*,#788,.T.);
#1027=ORIENTED_EDGE('',*,*,#787,.T.);
#1028=ORIENTED_EDGE('',*,*,#789,.F.);
#1029=ORIENTED_EDGE('',*,*,#790,.F.);
#1030=ORIENTED_EDGE('',*,*,#791,.T.);
#1031=ORIENTED_EDGE('',*,*,#790,.T.);
#1032=ORIENTED_EDGE('',*,*,#792,.F.);
#1033=ORIENTED_EDGE('',*,*,#793,.F.);
#1034=ORIENTED_EDGE('',*,*,#794,.T.);
#1035=ORIENTED_EDGE('',*,*,#793,.T.);
#1036=ORIENTED_EDGE('',*,*,#795,.F.);
#1037=ORIENTED_EDGE('',*,*,#796,.F.);
#1038=ORIENTED_EDGE('',*,*,#797,.T.);
#1039=ORIENTED_EDGE('',*,*,#796,.T.);
#1040=ORIENTED_EDGE('',*,*,#798,.F.);
#1041=ORIENTED_EDGE('',*,*,#799,.F.);
#1042=ORIENTED_EDGE('',*,*,#800,.T.);
#1043=ORIENTED_EDGE('',*,*,#799,.T.);
#1044=ORIENTED_EDGE('',*,*,#801,.F.);
#1045=ORIENTED_EDGE('',*,*,#802,.F.);
#1046=ORIENTED_EDGE('',*,*,#803,.T.);
#1047=ORIENTED_EDGE('',*,*,#802,.T.);
#1048=ORIENTED_EDGE('',*,*,#804,.F.);
#1049=ORIENTED_EDGE('',*,*,#805,.F.);
#1050=ORIENTED_EDGE('',*,*,#806,.T.);
#1051=ORIENTED_EDGE('',*,*,#805,.T.);
#1052=ORIENTED_EDGE('',*,*,#807,.F.);
#1053=ORIENTED_EDGE('',*,*,#808,.F.);
#1054=ORIENTED_EDGE('',*,*,#809,.T.);
#1055=ORIENTED_EDGE('',*,*,#808,.T.);
#1056=ORIENTED_EDGE('',*,*,#810,.F.);
#1057=ORIENTED_EDGE('',*,*,#811,.F.);
#1058=ORIENTED_EDGE('',*,*,#812,.T.);
#1059=ORIENTED_EDGE('',*,*,#811,.T.);
#1060=ORIENTED_EDGE('',*,*,#813,.F.);
#1061=ORIENTED_EDGE('',*,*,#785,.F.);
#1062=ORIENTED_EDGE('',*,*,#813,.T.);
#1063=ORIENTED_EDGE('',*,*,#810,.T.);
#1064=ORIENTED_EDGE('',*,*,#807,.T.);
#1065=ORIENTED_EDGE('',*,*,#804,.T.);
#1066=ORIENTED_EDGE('',*,*,#801,.T.);
#1067=ORIENTED_EDGE('',*,*,#798,.T.);
#1068=ORIENTED_EDGE('',*,*,#795,.T.);
#1069=ORIENTED_EDGE('',*,*,#792,.T.);
#1070=ORIENTED_EDGE('',*,*,#789,.T.);
#1071=ORIENTED_EDGE('',*,*,#786,.T.);
#1072=ORIENTED_EDGE('',*,*,#781,.T.);
#1073=ORIENTED_EDGE('',*,*,#780,.T.);
#1074=ORIENTED_EDGE('',*,*,#777,.T.);
#1075=ORIENTED_EDGE('',*,*,#774,.T.);
#1076=ORIENTED_EDGE('',*,*,#771,.T.);
#1077=ORIENTED_EDGE('',*,*,#768,.T.);
#1078=ORIENTED_EDGE('',*,*,#765,.T.);
#1079=ORIENTED_EDGE('',*,*,#762,.T.);
#1080=ORIENTED_EDGE('',*,*,#759,.T.);
#1081=ORIENTED_EDGE('',*,*,#756,.T.);
#1082=ORIENTED_EDGE('',*,*,#753,.T.);
#1083=ORIENTED_EDGE('',*,*,#750,.T.);
#1084=ORIENTED_EDGE('',*,*,#747,.T.);
#1085=ORIENTED_EDGE('',*,*,#744,.T.);
#1086=ORIENTED_EDGE('',*,*,#741,.T.);
#1087=ORIENTED_EDGE('',*,*,#738,.T.);
#1088=ORIENTED_EDGE('',*,*,#735,.T.);
#1089=ORIENTED_EDGE('',*,*,#732,.T.);
#1090=ORIENTED_EDGE('',*,*,#729,.T.);
#1091=ORIENTED_EDGE('',*,*,#726,.T.);
#1092=ORIENTED_EDGE('',*,*,#723,.T.);
#1093=ORIENTED_EDGE('',*,*,#720,.T.);
#1094=ORIENTED_EDGE('',*,*,#717,.T.);
#1095=ORIENTED_EDGE('',*,*,#714,.T.);
#1096=ORIENTED_EDGE('',*,*,#711,.T.);
#1097=ORIENTED_EDGE('',*,*,#706,.T.);
#1098=ORIENTED_EDGE('',*,*,#705,.T.);
#1099=ORIENTED_EDGE('',*,*,#702,.T.);
#1100=ORIENTED_EDGE('',*,*,#699,.T.);
#1101=ORIENTED_EDGE('',*,*,#696,.T.);
#1102=ORIENTED_EDGE('',*,*,#693,.T.);
#1103=ORIENTED_EDGE('',*,*,#690,.T.);
#1104=ORIENTED_EDGE('',*,*,#687,.T.);
#1105=ORIENTED_EDGE('',*,*,#684,.T.);
#1106=ORIENTED_EDGE('',*,*,#681,.T.);
#1107=ORIENTED_EDGE('',*,*,#678,.T.);
#1108=ORIENTED_EDGE('',*,*,#675,.T.);
#1109=ORIENTED_EDGE('',*,*,#672,.T.);
#1110=ORIENTED_EDGE('',*,*,#669,.T.);
#1111=ORIENTED_EDGE('',*,*,#666,.T.);
#1112=ORIENTED_EDGE('',*,*,#663,.T.);
#1113=ORIENTED_EDGE('',*,*,#660,.T.);
#1114=ORIENTED_EDGE('',*,*,#657,.T.);
#1115=ORIENTED_EDGE('',*,*,#654,.T.);
#1116=ORIENTED_EDGE('',*,*,#651,.T.);
#1117=ORIENTED_EDGE('',*,*,#648,.T.);
#1118=ORIENTED_EDGE('',*,*,#645,.T.);
#1119=ORIENTED_EDGE('',*,*,#642,.T.);
#1120=ORIENTED_EDGE('',*,*,#639,.T.);
#1121=ORIENTED_EDGE('',*,*,#636,.T.);
#1122=ORIENTED_EDGE('',*,*,#631,.T.);
#1123=ORIENTED_EDGE('',*,*,#628,.T.);
#1124=ORIENTED_EDGE('',*,*,#812,.F.);
#1125=ORIENTED_EDGE('',*,*,#784,.T.);
#1126=ORIENTED_EDGE('',*,*,#788,.F.);
#1127=ORIENTED_EDGE('',*,*,#791,.F.);
#1128=ORIENTED_EDGE('',*,*,#794,.F.);
#1129=ORIENTED_EDGE('',*,*,#797,.F.);
#1130=ORIENTED_EDGE('',*,*,#800,.F.);
#1131=ORIENTED_EDGE('',*,*,#803,.F.);
#1132=ORIENTED_EDGE('',*,*,#806,.F.);
#1133=ORIENTED_EDGE('',*,*,#809,.F.);
#1134=ORIENTED_EDGE('',*,*,#783,.F.);
#1135=ORIENTED_EDGE('',*,*,#779,.F.);
#1136=ORIENTED_EDGE('',*,*,#709,.F.);
#1137=ORIENTED_EDGE('',*,*,#713,.F.);
#1138=ORIENTED_EDGE('',*,*,#716,.F.);
#1139=ORIENTED_EDGE('',*,*,#719,.F.);
#1140=ORIENTED_EDGE('',*,*,#722,.F.);
#1141=ORIENTED_EDGE('',*,*,#725,.F.);
#1142=ORIENTED_EDGE('',*,*,#728,.F.);
#1143=ORIENTED_EDGE('',*,*,#731,.F.);
#1144=ORIENTED_EDGE('',*,*,#734,.F.);
#1145=ORIENTED_EDGE('',*,*,#737,.F.);
#1146=ORIENTED_EDGE('',*,*,#740,.F.);
#1147=ORIENTED_EDGE('',*,*,#743,.F.);
#1148=ORIENTED_EDGE('',*,*,#746,.F.);
#1149=ORIENTED_EDGE('',*,*,#749,.F.);
#1150=ORIENTED_EDGE('',*,*,#752,.F.);
#1151=ORIENTED_EDGE('',*,*,#755,.F.);
#1152=ORIENTED_EDGE('',*,*,#758,.F.);
#1153=ORIENTED_EDGE('',*,*,#761,.F.);
#1154=ORIENTED_EDGE('',*,*,#764,.F.);
#1155=ORIENTED_EDGE('',*,*,#767,.F.);
#1156=ORIENTED_EDGE('',*,*,#770,.F.);
#1157=ORIENTED_EDGE('',*,*,#773,.F.);
#1158=ORIENTED_EDGE('',*,*,#776,.F.);
#1159=ORIENTED_EDGE('',*,*,#708,.F.);
#1160=ORIENTED_EDGE('',*,*,#704,.F.);
#1161=ORIENTED_EDGE('',*,*,#634,.F.);
#1162=ORIENTED_EDGE('',*,*,#638,.F.);
#1163=ORIENTED_EDGE('',*,*,#641,.F.);
#1164=ORIENTED_EDGE('',*,*,#644,.F.);
#1165=ORIENTED_EDGE('',*,*,#647,.F.);
#1166=ORIENTED_EDGE('',*,*,#650,.F.);
#1167=ORIENTED_EDGE('',*,*,#653,.F.);
#1168=ORIENTED_EDGE('',*,*,#656,.F.);
#1169=ORIENTED_EDGE('',*,*,#659,.F.);
#1170=ORIENTED_EDGE('',*,*,#662,.F.);
#1171=ORIENTED_EDGE('',*,*,#665,.F.);
#1172=ORIENTED_EDGE('',*,*,#668,.F.);
#1173=ORIENTED_EDGE('',*,*,#671,.F.);
#1174=ORIENTED_EDGE('',*,*,#674,.F.);
#1175=ORIENTED_EDGE('',*,*,#677,.F.);
#1176=ORIENTED_EDGE('',*,*,#680,.F.);
#1177=ORIENTED_EDGE('',*,*,#683,.F.);
#1178=ORIENTED_EDGE('',*,*,#686,.F.);
#1179=ORIENTED_EDGE('',*,*,#689,.F.);
#1180=ORIENTED_EDGE('',*,*,#692,.F.);
#1181=ORIENTED_EDGE('',*,*,#695,.F.);
#1182=ORIENTED_EDGE('',*,*,#698,.F.);
#1183=ORIENTED_EDGE('',*,*,#701,.F.);
#1184=ORIENTED_EDGE('',*,*,#633,.F.);
#1185=ORIENTED_EDGE('',*,*,#630,.F.);
#1186=CYLINDRICAL_SURFACE('',#1297,0.999999999999996);
#1187=CYLINDRICAL_SURFACE('',#1300,1.);
#1188=CYLINDRICAL_SURFACE('',#1303,0.249999999999967);
#1189=CYLINDRICAL_SURFACE('',#1307,0.250000000000024);
#1190=CYLINDRICAL_SURFACE('',#1311,0.249999999999994);
#1191=CYLINDRICAL_SURFACE('',#1315,2.99999999993811);
#1192=CYLINDRICAL_SURFACE('',#1319,0.250000000000061);
#1193=CYLINDRICAL_SURFACE('',#1322,2.99999999999997);
#1194=CYLINDRICAL_SURFACE('',#1326,0.249999999999984);
#1195=CYLINDRICAL_SURFACE('',#1330,0.249999999993057);
#1196=CYLINDRICAL_SURFACE('',#1334,0.499999999999854);
#1197=CYLINDRICAL_SURFACE('',#1338,0.250000000000004);
#1198=CYLINDRICAL_SURFACE('',#1342,0.249999999999932);
#1199=CYLINDRICAL_SURFACE('',#1346,0.249999999999986);
#1200=CYLINDRICAL_SURFACE('',#1350,0.150000000000051);
#1201=CYLINDRICAL_SURFACE('',#1353,1.);
#1202=CYLINDRICAL_SURFACE('',#1356,0.249999999999915);
#1203=CYLINDRICAL_SURFACE('',#1360,0.250000000000039);
#1204=CYLINDRICAL_SURFACE('',#1364,0.500000000000114);
#1205=CYLINDRICAL_SURFACE('',#1368,0.249999999999842);
#1206=CYLINDRICAL_SURFACE('',#1372,0.2500000000005);
#1207=CYLINDRICAL_SURFACE('',#1376,2.99999999999997);
#1208=CYLINDRICAL_SURFACE('',#1379,0.250000000000021);
#1209=CYLINDRICAL_SURFACE('',#1383,3.);
#1210=CYLINDRICAL_SURFACE('',#1387,0.249999999920014);
#1211=CYLINDRICAL_SURFACE('',#1391,0.25000000000002);
#1212=CYLINDRICAL_SURFACE('',#1395,0.249999999999967);
#1213=CYLINDRICAL_SURFACE('',#1398,0.150000000000041);
#1214=CYLINDRICAL_SURFACE('',#1402,0.249999999999986);
#1215=CYLINDRICAL_SURFACE('',#1406,0.999999999999996);
#1216=CYLINDRICAL_SURFACE('',#1409,25.);
#1217=CYLINDRICAL_SURFACE('',#1412,2.);
#1218=CYLINDRICAL_SURFACE('',#1422,2.00000000000001);
#1219=ADVANCED_FACE('',(#58),#1186,.F.);
#1220=ADVANCED_FACE('',(#59),#1187,.F.);
#1221=ADVANCED_FACE('',(#60),#1188,.F.);
#1222=ADVANCED_FACE('',(#61),#27,.T.);
#1223=ADVANCED_FACE('',(#62),#1189,.T.);
#1224=ADVANCED_FACE('',(#63),#28,.T.);
#1225=ADVANCED_FACE('',(#64),#1190,.F.);
#1226=ADVANCED_FACE('',(#65),#29,.T.);
#1227=ADVANCED_FACE('',(#66),#1191,.F.);
#1228=ADVANCED_FACE('',(#67),#30,.T.);
#1229=ADVANCED_FACE('',(#68),#1192,.T.);
#1230=ADVANCED_FACE('',(#69),#1193,.F.);
#1231=ADVANCED_FACE('',(#70),#31,.T.);
#1232=ADVANCED_FACE('',(#71),#1194,.T.);
#1233=ADVANCED_FACE('',(#72),#32,.T.);
#1234=ADVANCED_FACE('',(#73),#1195,.F.);
#1235=ADVANCED_FACE('',(#74),#33,.T.);
#1236=ADVANCED_FACE('',(#75),#1196,.F.);
#1237=ADVANCED_FACE('',(#76),#34,.T.);
#1238=ADVANCED_FACE('',(#77),#1197,.T.);
#1239=ADVANCED_FACE('',(#78),#35,.T.);
#1240=ADVANCED_FACE('',(#79),#1198,.F.);
#1241=ADVANCED_FACE('',(#80),#36,.T.);
#1242=ADVANCED_FACE('',(#81),#1199,.T.);
#1243=ADVANCED_FACE('',(#82),#37,.T.);
#1244=ADVANCED_FACE('',(#83),#1200,.T.);
#1245=ADVANCED_FACE('',(#84),#1201,.F.);
#1246=ADVANCED_FACE('',(#85),#1202,.F.);
#1247=ADVANCED_FACE('',(#86),#38,.T.);
#1248=ADVANCED_FACE('',(#87),#1203,.T.);
#1249=ADVANCED_FACE('',(#88),#39,.T.);
#1250=ADVANCED_FACE('',(#89),#1204,.F.);
#1251=ADVANCED_FACE('',(#90),#40,.T.);
#1252=ADVANCED_FACE('',(#91),#1205,.F.);
#1253=ADVANCED_FACE('',(#92),#41,.T.);
#1254=ADVANCED_FACE('',(#93),#1206,.T.);
#1255=ADVANCED_FACE('',(#94),#42,.T.);
#1256=ADVANCED_FACE('',(#95),#1207,.F.);
#1257=ADVANCED_FACE('',(#96),#1208,.T.);
#1258=ADVANCED_FACE('',(#97),#43,.T.);
#1259=ADVANCED_FACE('',(#98),#1209,.F.);
#1260=ADVANCED_FACE('',(#99),#44,.T.);
#1261=ADVANCED_FACE('',(#100),#1210,.F.);
#1262=ADVANCED_FACE('',(#101),#45,.T.);
#1263=ADVANCED_FACE('',(#102),#1211,.T.);
#1264=ADVANCED_FACE('',(#103),#46,.T.);
#1265=ADVANCED_FACE('',(#104),#1212,.F.);
#1266=ADVANCED_FACE('',(#105),#1213,.T.);
#1267=ADVANCED_FACE('',(#106),#47,.T.);
#1268=ADVANCED_FACE('',(#107),#1214,.T.);
#1269=ADVANCED_FACE('',(#108),#48,.T.);
#1270=ADVANCED_FACE('',(#109),#1215,.F.);
#1271=ADVANCED_FACE('',(#110),#1216,.F.);
#1272=ADVANCED_FACE('',(#111),#1217,.T.);
#1273=ADVANCED_FACE('',(#112),#49,.T.);
#1274=ADVANCED_FACE('',(#113),#50,.T.);
#1275=ADVANCED_FACE('',(#114),#51,.T.);
#1276=ADVANCED_FACE('',(#115),#52,.T.);
#1277=ADVANCED_FACE('',(#116),#53,.T.);
#1278=ADVANCED_FACE('',(#117),#54,.T.);
#1279=ADVANCED_FACE('',(#118),#55,.T.);
#1280=ADVANCED_FACE('',(#119),#1218,.T.);
#1281=ADVANCED_FACE('',(#120,#15,#16,#17,#18,#19,#20),#56,.T.);
#1282=ADVANCED_FACE('',(#121,#21,#22,#23,#24,#25,#26),#57,.F.);
#1283=CLOSED_SHELL('',(#1219,#1220,#1221,#1222,#1223,#1224,#1225,#1226,
#1227,#1228,#1229,#1230,#1231,#1232,#1233,#1234,#1235,#1236,#1237,#1238,
#1239,#1240,#1241,#1242,#1243,#1244,#1245,#1246,#1247,#1248,#1249,#1250,
#1251,#1252,#1253,#1254,#1255,#1256,#1257,#1258,#1259,#1260,#1261,#1262,
#1263,#1264,#1265,#1266,#1267,#1268,#1269,#1270,#1271,#1272,#1273,#1274,
#1275,#1276,#1277,#1278,#1279,#1280,#1281,#1282));
#1284=DERIVED_UNIT_ELEMENT(#1286,1.);
#1285=DERIVED_UNIT_ELEMENT(#2189,-3.);
#1286=(
MASS_UNIT()
NAMED_UNIT(*)
SI_UNIT(.KILO.,.GRAM.)
);
#1287=DERIVED_UNIT((#1284,#1285));
#1288=MEASURE_REPRESENTATION_ITEM('density measure',
POSITIVE_RATIO_MEASURE(7850.),#1287);
#1289=PROPERTY_DEFINITION_REPRESENTATION(#1294,#1291);
#1290=PROPERTY_DEFINITION_REPRESENTATION(#1295,#1292);
#1291=REPRESENTATION('material name',(#1293),#2186);
#1292=REPRESENTATION('density',(#1288),#2186);
#1293=DESCRIPTIVE_REPRESENTATION_ITEM('Steel','Steel');
#1294=PROPERTY_DEFINITION('material property','material name',#2196);
#1295=PROPERTY_DEFINITION('material property','density of part',#2196);
#1296=AXIS2_PLACEMENT_3D('placement',#1809,#1427,#1428);
#1297=AXIS2_PLACEMENT_3D('',#1810,#1429,#1430);
#1298=AXIS2_PLACEMENT_3D('',#1812,#1431,#1432);
#1299=AXIS2_PLACEMENT_3D('',#1815,#1434,#1435);
#1300=AXIS2_PLACEMENT_3D('',#1816,#1436,#1437);
#1301=AXIS2_PLACEMENT_3D('',#1818,#1438,#1439);
#1302=AXIS2_PLACEMENT_3D('',#1821,#1441,#1442);
#1303=AXIS2_PLACEMENT_3D('',#1822,#1443,#1444);
#1304=AXIS2_PLACEMENT_3D('',#1825,#1445,#1446);
#1305=AXIS2_PLACEMENT_3D('',#1829,#1448,#1449);
#1306=AXIS2_PLACEMENT_3D('',#1831,#1451,#1452);
#1307=AXIS2_PLACEMENT_3D('',#1837,#1456,#1457);
#1308=AXIS2_PLACEMENT_3D('',#1839,#1458,#1459);
#1309=AXIS2_PLACEMENT_3D('',#1841,#1460,#1461);
#1310=AXIS2_PLACEMENT_3D('',#1843,#1463,#1464);
#1311=AXIS2_PLACEMENT_3D('',#1849,#1468,#1469);
#1312=AXIS2_PLACEMENT_3D('',#1851,#1470,#1471);
#1313=AXIS2_PLACEMENT_3D('',#1853,#1472,#1473);
#1314=AXIS2_PLACEMENT_3D('',#1855,#1475,#1476);
#1315=AXIS2_PLACEMENT_3D('',#1861,#1480,#1481);
#1316=AXIS2_PLACEMENT_3D('',#1863,#1482,#1483);
#1317=AXIS2_PLACEMENT_3D('',#1865,#1484,#1485);
#1318=AXIS2_PLACEMENT_3D('',#1867,#1487,#1488);
#1319=AXIS2_PLACEMENT_3D('',#1873,#1492,#1493);
#1320=AXIS2_PLACEMENT_3D('',#1875,#1494,#1495);
#1321=AXIS2_PLACEMENT_3D('',#1877,#1496,#1497);
#1322=AXIS2_PLACEMENT_3D('',#1879,#1499,#1500);
#1323=AXIS2_PLACEMENT_3D('',#1881,#1501,#1502);
#1324=AXIS2_PLACEMENT_3D('',#1883,#1503,#1504);
#1325=AXIS2_PLACEMENT_3D('',#1885,#1506,#1507);
#1326=AXIS2_PLACEMENT_3D('',#1891,#1511,#1512);
#1327=AXIS2_PLACEMENT_3D('',#1893,#1513,#1514);
#1328=AXIS2_PLACEMENT_3D('',#1895,#1515,#1516);
#1329=AXIS2_PLACEMENT_3D('',#1897,#1518,#1519);
#1330=AXIS2_PLACEMENT_3D('',#1903,#1523,#1524);
#1331=AXIS2_PLACEMENT_3D('',#1905,#1525,#1526);
#1332=AXIS2_PLACEMENT_3D('',#1907,#1527,#1528);
#1333=AXIS2_PLACEMENT_3D('',#1909,#1530,#1531);
#1334=AXIS2_PLACEMENT_3D('',#1915,#1535,#1536);
#1335=AXIS2_PLACEMENT_3D('',#1917,#1537,#1538);
#1336=AXIS2_PLACEMENT_3D('',#1919,#1539,#1540);
#1337=AXIS2_PLACEMENT_3D('',#1921,#1542,#1543);
#1338=AXIS2_PLACEMENT_3D('',#1927,#1547,#1548);
#1339=AXIS2_PLACEMENT_3D('',#1929,#1549,#1550);
#1340=AXIS2_PLACEMENT_3D('',#1931,#1551,#1552);
#1341=AXIS2_PLACEMENT_3D('',#1933,#1554,#1555);
#1342=AXIS2_PLACEMENT_3D('',#1939,#1559,#1560);
#1343=AXIS2_PLACEMENT_3D('',#1941,#1561,#1562);
#1344=AXIS2_PLACEMENT_3D('',#1943,#1563,#1564);
#1345=AXIS2_PLACEMENT_3D('',#1945,#1566,#1567);
#1346=AXIS2_PLACEMENT_3D('',#1951,#1571,#1572);
#1347=AXIS2_PLACEMENT_3D('',#1953,#1573,#1574);
#1348=AXIS2_PLACEMENT_3D('',#1955,#1575,#1576);
#1349=AXIS2_PLACEMENT_3D('',#1957,#1578,#1579);
#1350=AXIS2_PLACEMENT_3D('',#1963,#1583,#1584);
#1351=AXIS2_PLACEMENT_3D('',#1964,#1585,#1586);
#1352=AXIS2_PLACEMENT_3D('',#1965,#1587,#1588);
#1353=AXIS2_PLACEMENT_3D('',#1966,#1589,#1590);
#1354=AXIS2_PLACEMENT_3D('',#1968,#1591,#1592);
#1355=AXIS2_PLACEMENT_3D('',#1971,#1594,#1595);
#1356=AXIS2_PLACEMENT_3D('',#1972,#1596,#1597);
#1357=AXIS2_PLACEMENT_3D('',#1975,#1598,#1599);
#1358=AXIS2_PLACEMENT_3D('',#1979,#1601,#1602);
#1359=AXIS2_PLACEMENT_3D('',#1981,#1604,#1605);
#1360=AXIS2_PLACEMENT_3D('',#1987,#1609,#1610);
#1361=AXIS2_PLACEMENT_3D('',#1989,#1611,#1612);
#1362=AXIS2_PLACEMENT_3D('',#1991,#1613,#1614);
#1363=AXIS2_PLACEMENT_3D('',#1993,#1616,#1617);
#1364=AXIS2_PLACEMENT_3D('',#1999,#1621,#1622);
#1365=AXIS2_PLACEMENT_3D('',#2001,#1623,#1624);
#1366=AXIS2_PLACEMENT_3D('',#2003,#1625,#1626);
#1367=AXIS2_PLACEMENT_3D('',#2005,#1628,#1629);
#1368=AXIS2_PLACEMENT_3D('',#2011,#1633,#1634);
#1369=AXIS2_PLACEMENT_3D('',#2013,#1635,#1636);
#1370=AXIS2_PLACEMENT_3D('',#2015,#1637,#1638);
#1371=AXIS2_PLACEMENT_3D('',#2017,#1640,#1641);
#1372=AXIS2_PLACEMENT_3D('',#2023,#1645,#1646);
#1373=AXIS2_PLACEMENT_3D('',#2025,#1647,#1648);
#1374=AXIS2_PLACEMENT_3D('',#2027,#1649,#1650);
#1375=AXIS2_PLACEMENT_3D('',#2029,#1652,#1653);
#1376=AXIS2_PLACEMENT_3D('',#2035,#1657,#1658);
#1377=AXIS2_PLACEMENT_3D('',#2037,#1659,#1660);
#1378=AXIS2_PLACEMENT_3D('',#2039,#1661,#1662);
#1379=AXIS2_PLACEMENT_3D('',#2041,#1664,#1665);
#1380=AXIS2_PLACEMENT_3D('',#2043,#1666,#1667);
#1381=AXIS2_PLACEMENT_3D('',#2045,#1668,#1669);
#1382=AXIS2_PLACEMENT_3D('',#2047,#1671,#1672);
#1383=AXIS2_PLACEMENT_3D('',#2053,#1676,#1677);
#1384=AXIS2_PLACEMENT_3D('',#2055,#1678,#1679);
#1385=AXIS2_PLACEMENT_3D('',#2057,#1680,#1681);
#1386=AXIS2_PLACEMENT_3D('',#2059,#1683,#1684);
#1387=AXIS2_PLACEMENT_3D('',#2065,#1688,#1689);
#1388=AXIS2_PLACEMENT_3D('',#2067,#1690,#1691);
#1389=AXIS2_PLACEMENT_3D('',#2069,#1692,#1693);
#1390=AXIS2_PLACEMENT_3D('',#2071,#1695,#1696);
#1391=AXIS2_PLACEMENT_3D('',#2077,#1700,#1701);
#1392=AXIS2_PLACEMENT_3D('',#2079,#1702,#1703);
#1393=AXIS2_PLACEMENT_3D('',#2081,#1704,#1705);
#1394=AXIS2_PLACEMENT_3D('',#2083,#1707,#1708);
#1395=AXIS2_PLACEMENT_3D('',#2089,#1712,#1713);
#1396=AXIS2_PLACEMENT_3D('',#2091,#1714,#1715);
#1397=AXIS2_PLACEMENT_3D('',#2093,#1716,#1717);
#1398=AXIS2_PLACEMENT_3D('',#2095,#1719,#1720);
#1399=AXIS2_PLACEMENT_3D('',#2097,#1721,#1722);
#1400=AXIS2_PLACEMENT_3D('',#2099,#1723,#1724);
#1401=AXIS2_PLACEMENT_3D('',#2101,#1726,#1727);
#1402=AXIS2_PLACEMENT_3D('',#2107,#1731,#1732);
#1403=AXIS2_PLACEMENT_3D('',#2109,#1733,#1734);
#1404=AXIS2_PLACEMENT_3D('',#2111,#1735,#1736);
#1405=AXIS2_PLACEMENT_3D('',#2113,#1738,#1739);
#1406=AXIS2_PLACEMENT_3D('',#2116,#1742,#1743);
#1407=AXIS2_PLACEMENT_3D('',#2118,#1744,#1745);
#1408=AXIS2_PLACEMENT_3D('',#2121,#1747,#1748);
#1409=AXIS2_PLACEMENT_3D('',#2122,#1749,#1750);
#1410=AXIS2_PLACEMENT_3D('',#2125,#1751,#1752);
#1411=AXIS2_PLACEMENT_3D('',#2129,#1754,#1755);
#1412=AXIS2_PLACEMENT_3D('',#2131,#1757,#1758);
#1413=AXIS2_PLACEMENT_3D('',#2133,#1759,#1760);
#1414=AXIS2_PLACEMENT_3D('',#2135,#1761,#1762);
#1415=AXIS2_PLACEMENT_3D('',#2137,#1764,#1765);
#1416=AXIS2_PLACEMENT_3D('',#2143,#1769,#1770);
#1417=AXIS2_PLACEMENT_3D('',#2149,#1774,#1775);
#1418=AXIS2_PLACEMENT_3D('',#2155,#1779,#1780);
#1419=AXIS2_PLACEMENT_3D('',#2161,#1784,#1785);
#1420=AXIS2_PLACEMENT_3D('',#2167,#1789,#1790);
#1421=AXIS2_PLACEMENT_3D('',#2173,#1794,#1795);
#1422=AXIS2_PLACEMENT_3D('',#2179,#1799,#1800);
#1423=AXIS2_PLACEMENT_3D('',#2180,#1801,#1802);
#1424=AXIS2_PLACEMENT_3D('',#2181,#1803,#1804);
#1425=AXIS2_PLACEMENT_3D('',#2182,#1805,#1806);
#1426=AXIS2_PLACEMENT_3D('',#2183,#1807,#1808);
#1427=DIRECTION('axis',(0.,0.,1.));
#1428=DIRECTION('refdir',(1.,0.,0.));
#1429=DIRECTION('center_axis',(0.,0.,1.));
#1430=DIRECTION('ref_axis',(1.,0.,0.));
#1431=DIRECTION('center_axis',(0.,0.,-1.));
#1432=DIRECTION('ref_axis',(1.,0.,0.));
#1433=DIRECTION('',(0.,0.,-1.));
#1434=DIRECTION('center_axis',(0.,0.,-1.));
#1435=DIRECTION('ref_axis',(1.,0.,0.));
#1436=DIRECTION('center_axis',(0.,0.,1.));
#1437=DIRECTION('ref_axis',(1.,0.,0.));
#1438=DIRECTION('center_axis',(0.,0.,-1.));
#1439=DIRECTION('ref_axis',(1.,0.,0.));
#1440=DIRECTION('',(0.,0.,-1.));
#1441=DIRECTION('center_axis',(0.,0.,-1.));
#1442=DIRECTION('ref_axis',(1.,0.,0.));
#1443=DIRECTION('center_axis',(0.,0.,1.));
#1444=DIRECTION('ref_axis',(0.999999999998869,-1.50421747946459E-6,0.));
#1445=DIRECTION('center_axis',(0.,0.,-1.));
#1446=DIRECTION('ref_axis',(0.999999999998869,-1.50421747946459E-6,0.));
#1447=DIRECTION('',(0.,0.,1.));
#1448=DIRECTION('center_axis',(0.,0.,-1.));
#1449=DIRECTION('ref_axis',(0.999999999998869,-1.50421747946459E-6,0.));
#1450=DIRECTION('',(0.,0.,1.));
#1451=DIRECTION('center_axis',(-1.50421747267308E-6,-0.999999999998869,
0.));
#1452=DIRECTION('ref_axis',(0.999999999998869,-1.50421747267308E-6,0.));
#1453=DIRECTION('',(0.999999999998869,-1.50421747267308E-6,0.));
#1454=DIRECTION('',(0.999999999998869,-1.50421747267308E-6,0.));
#1455=DIRECTION('',(0.,0.,1.));
#1456=DIRECTION('center_axis',(0.,0.,1.));
#1457=DIRECTION('ref_axis',(-0.999999999992812,3.79172441000682E-6,0.));
#1458=DIRECTION('center_axis',(0.,0.,1.));
#1459=DIRECTION('ref_axis',(-0.999999999992812,3.79172441000682E-6,0.));
#1460=DIRECTION('center_axis',(0.,0.,1.));
#1461=DIRECTION('ref_axis',(-0.999999999992812,3.79172441000682E-6,0.));
#1462=DIRECTION('',(0.,0.,1.));
#1463=DIRECTION('center_axis',(-0.999999999996297,2.72136692451594E-6,0.));
#1464=DIRECTION('ref_axis',(-2.72136692451594E-6,-0.999999999996297,0.));
#1465=DIRECTION('',(-2.72136692451594E-6,-0.999999999996297,0.));
#1466=DIRECTION('',(-2.72136692451594E-6,-0.999999999996297,0.));
#1467=DIRECTION('',(0.,0.,1.));
#1468=DIRECTION('center_axis',(0.,0.,1.));
#1469=DIRECTION('ref_axis',(0.999983644554755,-0.00571932015091661,0.));
#1470=DIRECTION('center_axis',(0.,0.,-1.));
#1471=DIRECTION('ref_axis',(0.999983644554755,-0.00571932015091661,0.));
#1472=DIRECTION('center_axis',(0.,0.,-1.));
#1473=DIRECTION('ref_axis',(0.999983644554755,-0.00571932015091661,0.));
#1474=DIRECTION('',(0.,0.,1.));
#1475=DIRECTION('center_axis',(0.,-1.,0.));
#1476=DIRECTION('ref_axis',(1.,0.,0.));
#1477=DIRECTION('',(1.,0.,0.));
#1478=DIRECTION('',(1.,0.,0.));
#1479=DIRECTION('',(0.,0.,1.));
#1480=DIRECTION('center_axis',(0.,0.,1.));
#1481=DIRECTION('ref_axis',(-0.999074744723158,0.043007609284358,0.));
#1482=DIRECTION('center_axis',(0.,0.,-1.));
#1483=DIRECTION('ref_axis',(0.0859564834484089,0.996298892377776,0.));
#1484=DIRECTION('center_axis',(0.,0.,-1.));
#1485=DIRECTION('ref_axis',(0.0859564834484089,0.996298892377776,0.));
#1486=DIRECTION('',(0.,0.,1.));
#1487=DIRECTION('center_axis',(0.,1.,0.));
#1488=DIRECTION('ref_axis',(-1.,0.,0.));
#1489=DIRECTION('',(-1.,0.,0.));
#1490=DIRECTION('',(-1.,0.,0.));
#1491=DIRECTION('',(0.,0.,1.));
#1492=DIRECTION('center_axis',(0.,0.,1.));
#1493=DIRECTION('ref_axis',(0.996548438763836,0.0830133073510605,0.));
#1494=DIRECTION('center_axis',(0.,0.,1.));
#1495=DIRECTION('ref_axis',(0.996548438763836,0.0830133073510605,0.));
#1496=DIRECTION('center_axis',(0.,0.,1.));
#1497=DIRECTION('ref_axis',(0.996548438763836,0.0830133073510605,0.));
#1498=DIRECTION('',(0.,0.,1.));
#1499=DIRECTION('center_axis',(0.,0.,1.));
#1500=DIRECTION('ref_axis',(-0.996548438763812,-0.0830133073513481,0.));
#1501=DIRECTION('center_axis',(0.,0.,-1.));
#1502=DIRECTION('ref_axis',(-0.996548438763812,-0.0830133073513481,0.));
#1503=DIRECTION('center_axis',(0.,0.,-1.));
#1504=DIRECTION('ref_axis',(-0.996548438763812,-0.0830133073513481,0.));
#1505=DIRECTION('',(0.,0.,1.));
#1506=DIRECTION('center_axis',(-0.999999999998869,1.50421815394073E-6,0.));
#1507=DIRECTION('ref_axis',(-1.50421815394073E-6,-0.999999999998869,0.));
#1508=DIRECTION('',(-1.50421815394073E-6,-0.999999999998869,0.));
#1509=DIRECTION('',(-1.50421815394073E-6,-0.999999999998869,0.));
#1510=DIRECTION('',(0.,0.,1.));
#1511=DIRECTION('center_axis',(0.,0.,1.));
#1512=DIRECTION('ref_axis',(1.50421755051875E-6,0.999999999998869,0.));
#1513=DIRECTION('center_axis',(0.,0.,1.));
#1514=DIRECTION('ref_axis',(1.50421755051875E-6,0.999999999998869,0.));
#1515=DIRECTION('center_axis',(0.,0.,1.));
#1516=DIRECTION('ref_axis',(1.50421755051875E-6,0.999999999998869,0.));
#1517=DIRECTION('',(0.,0.,1.));
#1518=DIRECTION('center_axis',(1.5042312995231E-6,0.999999999998869,0.));
#1519=DIRECTION('ref_axis',(-0.999999999998869,1.5042312995231E-6,0.));
#1520=DIRECTION('',(-0.999999999998869,1.5042312995231E-6,0.));
#1521=DIRECTION('',(-0.999999999998869,1.5042312995231E-6,0.));
#1522=DIRECTION('',(0.,0.,1.));
#1523=DIRECTION('center_axis',(0.,0.,1.));
#1524=DIRECTION('ref_axis',(-1.50421740845189E-6,-0.999999999998869,0.));
#1525=DIRECTION('center_axis',(0.,0.,-1.));
#1526=DIRECTION('ref_axis',(-1.50421740845189E-6,-0.999999999998869,0.));
#1527=DIRECTION('center_axis',(0.,0.,-1.));
#1528=DIRECTION('ref_axis',(-1.50421740845189E-6,-0.999999999998869,0.));
#1529=DIRECTION('',(0.,0.,1.));
#1530=DIRECTION('center_axis',(-0.999999999998869,1.50421744156322E-6,0.));
#1531=DIRECTION('ref_axis',(-1.50421744156322E-6,-0.999999999998869,0.));
#1532=DIRECTION('',(-1.50421744156322E-6,-0.999999999998869,0.));
#1533=DIRECTION('',(-1.50421744156322E-6,-0.999999999998869,0.));
#1534=DIRECTION('',(0.,0.,1.));
#1535=DIRECTION('center_axis',(0.,0.,1.));
#1536=DIRECTION('ref_axis',(0.00122721758334414,0.999999246968218,0.));
#1537=DIRECTION('center_axis',(0.,0.,-1.));
#1538=DIRECTION('ref_axis',(0.999996987874006,-0.00245443331845579,0.));
#1539=DIRECTION('center_axis',(0.,0.,-1.));
#1540=DIRECTION('ref_axis',(0.999996987874006,-0.00245443331845579,0.));
#1541=DIRECTION('',(0.,0.,1.));
#1542=DIRECTION('center_axis',(1.,-6.26907435430066E-14,0.));
#1543=DIRECTION('ref_axis',(6.26907435430066E-14,1.,0.));
#1544=DIRECTION('',(6.26907435430066E-14,1.,0.));
#1545=DIRECTION('',(6.26907435430066E-14,1.,0.));
#1546=DIRECTION('',(0.,0.,1.));
#1547=DIRECTION('center_axis',(0.,0.,1.));
#1548=DIRECTION('ref_axis',(-5.68434188608072E-13,-1.,0.));
#1549=DIRECTION('center_axis',(0.,0.,1.));
#1550=DIRECTION('ref_axis',(-5.68434188608072E-13,-1.,0.));
#1551=DIRECTION('center_axis',(0.,0.,1.));
#1552=DIRECTION('ref_axis',(-5.68434188608072E-13,-1.,0.));
#1553=DIRECTION('',(0.,0.,1.));
#1554=DIRECTION('center_axis',(7.10542735760166E-14,-1.,0.));
#1555=DIRECTION('ref_axis',(1.,7.10542735760166E-14,0.));
#1556=DIRECTION('',(1.,7.10542735760166E-14,0.));
#1557=DIRECTION('',(1.,7.10542735760166E-14,0.));
#1558=DIRECTION('',(0.,0.,1.));
#1559=DIRECTION('center_axis',(0.,0.,1.));
#1560=DIRECTION('ref_axis',(-7.10542735760292E-14,1.,0.));
#1561=DIRECTION('center_axis',(0.,0.,-1.));
#1562=DIRECTION('ref_axis',(-7.10542735760292E-14,1.,0.));
#1563=DIRECTION('center_axis',(0.,0.,-1.));
#1564=DIRECTION('ref_axis',(-7.10542735760292E-14,1.,0.));
#1565=DIRECTION('',(0.,0.,1.));
#1566=DIRECTION('center_axis',(1.,0.,0.));
#1567=DIRECTION('ref_axis',(0.,1.,0.));
#1568=DIRECTION('',(0.,1.,0.));
#1569=DIRECTION('',(0.,1.,0.));
#1570=DIRECTION('',(0.,0.,1.));
#1571=DIRECTION('center_axis',(0.,0.,1.));
#1572=DIRECTION('ref_axis',(0.,-1.,0.));
#1573=DIRECTION('center_axis',(0.,0.,1.));
#1574=DIRECTION('ref_axis',(0.,-1.,0.));
#1575=DIRECTION('center_axis',(0.,0.,1.));
#1576=DIRECTION('ref_axis',(0.,-1.,0.));
#1577=DIRECTION('',(0.,0.,1.));
#1578=DIRECTION('center_axis',(0.,-1.,0.));
#1579=DIRECTION('ref_axis',(1.,0.,0.));
#1580=DIRECTION('',(1.,0.,0.));
#1581=DIRECTION('',(1.,0.,0.));
#1582=DIRECTION('',(0.,0.,1.));
#1583=DIRECTION('center_axis',(0.,0.,1.));
#1584=DIRECTION('ref_axis',(-0.999999999998869,1.50421731367057E-6,0.));
#1585=DIRECTION('center_axis',(0.,0.,1.));
#1586=DIRECTION('ref_axis',(-0.999999999998869,1.50421731367057E-6,0.));
#1587=DIRECTION('center_axis',(0.,0.,1.));
#1588=DIRECTION('ref_axis',(-0.999999999998869,1.50421731367057E-6,0.));
#1589=DIRECTION('center_axis',(0.,0.,1.));
#1590=DIRECTION('ref_axis',(1.,0.,0.));
#1591=DIRECTION('center_axis',(0.,0.,-1.));
#1592=DIRECTION('ref_axis',(1.,0.,0.));
#1593=DIRECTION('',(0.,0.,-1.));
#1594=DIRECTION('center_axis',(0.,0.,-1.));
#1595=DIRECTION('ref_axis',(1.,0.,0.));
#1596=DIRECTION('center_axis',(0.,0.,1.));
#1597=DIRECTION('ref_axis',(1.,-2.13162820728103E-12,0.));
#1598=DIRECTION('center_axis',(0.,0.,-1.));
#1599=DIRECTION('ref_axis',(1.,-2.13162820728103E-12,0.));
#1600=DIRECTION('',(0.,0.,1.));
#1601=DIRECTION('center_axis',(0.,0.,-1.));
#1602=DIRECTION('ref_axis',(1.,-2.13162820728103E-12,0.));
#1603=DIRECTION('',(0.,0.,1.));
#1604=DIRECTION('center_axis',(-7.10542735760166E-14,-1.,0.));
#1605=DIRECTION('ref_axis',(1.,-7.10542735760166E-14,0.));
#1606=DIRECTION('',(1.,-7.10542735760166E-14,0.));
#1607=DIRECTION('',(1.,-7.10542735760166E-14,0.));
#1608=DIRECTION('',(0.,0.,1.));
#1609=DIRECTION('center_axis',(0.,0.,1.));
#1610=DIRECTION('ref_axis',(-1.,-2.13162820727997E-13,0.));
#1611=DIRECTION('center_axis',(0.,0.,1.));
#1612=DIRECTION('ref_axis',(-1.,-2.13162820727997E-13,0.));
#1613=DIRECTION('center_axis',(0.,0.,1.));
#1614=DIRECTION('ref_axis',(-1.,-2.13162820727997E-13,0.));
#1615=DIRECTION('',(0.,0.,1.));
#1616=DIRECTION('center_axis',(-1.,-6.26907435430066E-14,0.));
#1617=DIRECTION('ref_axis',(6.26907435430066E-14,-1.,0.));
#1618=DIRECTION('',(6.26907435430066E-14,-1.,0.));
#1619=DIRECTION('',(6.26907435430066E-14,-1.,0.));
#1620=DIRECTION('',(0.,0.,1.));
#1621=DIRECTION('center_axis',(0.,0.,1.));
#1622=DIRECTION('ref_axis',(-0.00122721758336157,0.999999246968218,0.));
#1623=DIRECTION('center_axis',(0.,0.,-1.));
#1624=DIRECTION('ref_axis',(1.,0.,0.));
#1625=DIRECTION('center_axis',(0.,0.,-1.));
#1626=DIRECTION('ref_axis',(1.,0.,0.));
#1627=DIRECTION('',(0.,0.,1.));
#1628=DIRECTION('center_axis',(0.999999999998869,1.50421744156322E-6,0.));
#1629=DIRECTION('ref_axis',(-1.50421744156322E-6,0.999999999998869,0.));
#1630=DIRECTION('',(-1.50421744156322E-6,0.999999999998869,0.));
#1631=DIRECTION('',(-1.50421744156322E-6,0.999999999998869,0.));
#1632=DIRECTION('',(0.,0.,1.));
#1633=DIRECTION('center_axis',(0.,0.,1.));
#1634=DIRECTION('ref_axis',(-0.999999999998869,-1.50421747946534E-6,0.));
#1635=DIRECTION('center_axis',(0.,0.,-1.));
#1636=DIRECTION('ref_axis',(-0.999999999998869,-1.50421747946534E-6,0.));
#1637=DIRECTION('center_axis',(0.,0.,-1.));
#1638=DIRECTION('ref_axis',(-0.999999999998869,-1.50421747946534E-6,0.));
#1639=DIRECTION('',(0.,0.,1.));
#1640=DIRECTION('center_axis',(-1.5042312995231E-6,0.999999999998869,0.));
#1641=DIRECTION('ref_axis',(-0.999999999998869,-1.5042312995231E-6,0.));
#1642=DIRECTION('',(-0.999999999998869,-1.5042312995231E-6,0.));
#1643=DIRECTION('',(-0.999999999998869,-1.5042312995231E-6,0.));
#1644=DIRECTION('',(0.,0.,1.));
#1645=DIRECTION('center_axis',(0.,0.,1.));
#1646=DIRECTION('ref_axis',(0.999999999998869,1.50421747946138E-6,0.));
#1647=DIRECTION('center_axis',(0.,0.,1.));
#1648=DIRECTION('ref_axis',(0.999999999998869,1.50421747946138E-6,0.));
#1649=DIRECTION('center_axis',(0.,0.,1.));
#1650=DIRECTION('ref_axis',(0.999999999998869,1.50421747946138E-6,0.));
#1651=DIRECTION('',(0.,0.,1.));
#1652=DIRECTION('center_axis',(0.999999999998869,1.50421815394073E-6,0.));
#1653=DIRECTION('ref_axis',(-1.50421815394073E-6,0.999999999998869,0.));
#1654=DIRECTION('',(-1.50421815394073E-6,0.999999999998869,0.));
#1655=DIRECTION('',(-1.50421815394073E-6,0.999999999998869,0.));
#1656=DIRECTION('',(0.,0.,1.));
#1657=DIRECTION('center_axis',(0.,0.,1.));
#1658=DIRECTION('ref_axis',(-0.999999999998869,-1.50421789394766E-6,0.));
#1659=DIRECTION('center_axis',(0.,0.,-1.));
#1660=DIRECTION('ref_axis',(-0.999999999998869,-1.50421789394766E-6,0.));
#1661=DIRECTION('center_axis',(0.,0.,-1.));
#1662=DIRECTION('ref_axis',(-0.999999999998869,-1.50421789394766E-6,0.));
#1663=DIRECTION('',(0.,0.,1.));
#1664=DIRECTION('center_axis',(0.,0.,1.));
#1665=DIRECTION('ref_axis',(-1.58451030074489E-11,1.,0.));
#1666=DIRECTION('center_axis',(0.,0.,1.));
#1667=DIRECTION('ref_axis',(-1.58451030074489E-11,1.,0.));
#1668=DIRECTION('center_axis',(0.,0.,1.));
#1669=DIRECTION('ref_axis',(-1.58451030074489E-11,1.,0.));
#1670=DIRECTION('',(0.,0.,1.));
#1671=DIRECTION('center_axis',(0.,1.,0.));
#1672=DIRECTION('ref_axis',(-1.,0.,0.));
#1673=DIRECTION('',(-1.,0.,0.));
#1674=DIRECTION('',(-1.,0.,0.));
#1675=DIRECTION('',(0.,0.,1.));
#1676=DIRECTION('center_axis',(0.,0.,1.));
#1677=DIRECTION('ref_axis',(0.999074744723159,0.0430076092843529,0.));
#1678=DIRECTION('center_axis',(0.,0.,-1.));
#1679=DIRECTION('ref_axis',(-2.09283538292008E-5,-0.999999999781002,0.));
#1680=DIRECTION('center_axis',(0.,0.,-1.));
#1681=DIRECTION('ref_axis',(-2.09283538292008E-5,-0.999999999781002,0.));
#1682=DIRECTION('',(0.,0.,1.));
#1683=DIRECTION('center_axis',(0.,-1.,0.));
#1684=DIRECTION('ref_axis',(1.,0.,0.));
#1685=DIRECTION('',(1.,0.,0.));
#1686=DIRECTION('',(1.,0.,0.));
#1687=DIRECTION('',(0.,0.,1.));
#1688=DIRECTION('center_axis',(0.,0.,1.));
#1689=DIRECTION('ref_axis',(4.39825953576221E-11,1.,0.));
#1690=DIRECTION('center_axis',(0.,0.,-1.));
#1691=DIRECTION('ref_axis',(4.39825953576221E-11,1.,0.));
#1692=DIRECTION('center_axis',(0.,0.,-1.));
#1693=DIRECTION('ref_axis',(4.39825953576221E-11,1.,0.));
#1694=DIRECTION('',(0.,0.,1.));
#1695=DIRECTION('center_axis',(0.999999999996297,2.72136692451594E-6,0.));
#1696=DIRECTION('ref_axis',(-2.72136692451594E-6,0.999999999996297,0.));
#1697=DIRECTION('',(-2.72136692451594E-6,0.999999999996297,0.));
#1698=DIRECTION('',(-2.72136692451594E-6,0.999999999996297,0.));
#1699=DIRECTION('',(0.,0.,1.));
#1700=DIRECTION('center_axis',(0.,0.,1.));
#1701=DIRECTION('ref_axis',(1.50421755051854E-6,-0.999999999998869,0.));
#1702=DIRECTION('center_axis',(0.,0.,1.));
#1703=DIRECTION('ref_axis',(1.50421755051854E-6,-0.999999999998869,0.));
#1704=DIRECTION('center_axis',(0.,0.,1.));
#1705=DIRECTION('ref_axis',(1.50421755051854E-6,-0.999999999998869,0.));
#1706=DIRECTION('',(0.,0.,1.));
#1707=DIRECTION('center_axis',(1.50421747267308E-6,-0.999999999998869,0.));
#1708=DIRECTION('ref_axis',(0.999999999998869,1.50421747267308E-6,0.));
#1709=DIRECTION('',(0.999999999998869,1.50421747267308E-6,0.));
#1710=DIRECTION('',(0.999999999998869,1.50421747267308E-6,0.));
#1711=DIRECTION('',(0.,0.,1.));
#1712=DIRECTION('center_axis',(0.,0.,1.));
#1713=DIRECTION('ref_axis',(-1.50421747946459E-6,0.999999999998869,0.));
#1714=DIRECTION('center_axis',(0.,0.,-1.));
#1715=DIRECTION('ref_axis',(-1.50421747946459E-6,0.999999999998869,0.));
#1716=DIRECTION('center_axis',(0.,0.,-1.));
#1717=DIRECTION('ref_axis',(-1.50421747946459E-6,0.999999999998869,0.));
#1718=DIRECTION('',(0.,0.,1.));
#1719=DIRECTION('center_axis',(0.,0.,1.));
#1720=DIRECTION('ref_axis',(2.36847578586635E-13,-1.,0.));
#1721=DIRECTION('center_axis',(0.,0.,1.));
#1722=DIRECTION('ref_axis',(2.36847578586635E-13,-1.,0.));
#1723=DIRECTION('center_axis',(0.,0.,1.));
#1724=DIRECTION('ref_axis',(2.36847578586635E-13,-1.,0.));
#1725=DIRECTION('',(0.,0.,1.));
#1726=DIRECTION('center_axis',(0.,-1.,0.));
#1727=DIRECTION('ref_axis',(1.,0.,0.));
#1728=DIRECTION('',(1.,0.,0.));
#1729=DIRECTION('',(1.,0.,0.));
#1730=DIRECTION('',(0.,0.,1.));
#1731=DIRECTION('center_axis',(0.,0.,1.));
#1732=DIRECTION('ref_axis',(-1.,0.,0.));
#1733=DIRECTION('center_axis',(0.,0.,1.));
#1734=DIRECTION('ref_axis',(-1.,0.,0.));
#1735=DIRECTION('center_axis',(0.,0.,1.));
#1736=DIRECTION('ref_axis',(-1.,0.,0.));
#1737=DIRECTION('',(0.,0.,1.));
#1738=DIRECTION('center_axis',(-1.,0.,0.));
#1739=DIRECTION('ref_axis',(0.,-1.,0.));
#1740=DIRECTION('',(0.,-1.,0.));
#1741=DIRECTION('',(0.,-1.,0.));
#1742=DIRECTION('center_axis',(0.,0.,1.));
#1743=DIRECTION('ref_axis',(1.,0.,0.));
#1744=DIRECTION('center_axis',(0.,0.,-1.));
#1745=DIRECTION('ref_axis',(1.,0.,0.));
#1746=DIRECTION('',(0.,0.,-1.));
#1747=DIRECTION('center_axis',(0.,0.,-1.));
#1748=DIRECTION('ref_axis',(1.,0.,0.));
#1749=DIRECTION('center_axis',(0.,0.,1.));
#1750=DIRECTION('ref_axis',(0.997252742061945,0.0740740740740741,0.));
#1751=DIRECTION('center_axis',(0.,0.,1.));
#1752=DIRECTION('ref_axis',(0.997252742061945,0.0740740740740741,0.));
#1753=DIRECTION('',(0.,0.,1.));
#1754=DIRECTION('center_axis',(0.,0.,-1.));
#1755=DIRECTION('ref_axis',(0.997252742061945,0.0740740740740741,0.));
#1756=DIRECTION('',(0.,0.,1.));
#1757=DIRECTION('center_axis',(0.,0.,1.));
#1758=DIRECTION('ref_axis',(0.,-1.,0.));
#1759=DIRECTION('center_axis',(0.,0.,1.));
#1760=DIRECTION('ref_axis',(0.,-1.,0.));
#1761=DIRECTION('center_axis',(0.,0.,1.));
#1762=DIRECTION('ref_axis',(0.,-1.,0.));
#1763=DIRECTION('',(0.,0.,1.));
#1764=DIRECTION('center_axis',(0.,-1.,0.));
#1765=DIRECTION('ref_axis',(1.,0.,0.));
#1766=DIRECTION('',(1.,0.,0.));
#1767=DIRECTION('',(1.,0.,0.));
#1768=DIRECTION('',(0.,0.,1.));
#1769=DIRECTION('center_axis',(-1.,0.,0.));
#1770=DIRECTION('ref_axis',(0.,-1.,0.));
#1771=DIRECTION('',(0.,-1.,0.));
#1772=DIRECTION('',(0.,-1.,0.));
#1773=DIRECTION('',(0.,0.,1.));
#1774=DIRECTION('center_axis',(0.,-1.,0.));
#1775=DIRECTION('ref_axis',(1.,0.,0.));
#1776=DIRECTION('',(1.,0.,0.));
#1777=DIRECTION('',(1.,0.,0.));
#1778=DIRECTION('',(0.,0.,1.));
#1779=DIRECTION('center_axis',(-1.,0.,0.));
#1780=DIRECTION('ref_axis',(0.,-1.,0.));
#1781=DIRECTION('',(0.,-1.,0.));
#1782=DIRECTION('',(0.,-1.,0.));
#1783=DIRECTION('',(0.,0.,1.));
#1784=DIRECTION('center_axis',(0.,1.,0.));
#1785=DIRECTION('ref_axis',(-1.,0.,0.));
#1786=DIRECTION('',(-1.,0.,0.));
#1787=DIRECTION('',(-1.,0.,0.));
#1788=DIRECTION('',(0.,0.,1.));
#1789=DIRECTION('center_axis',(1.,0.,0.));
#1790=DIRECTION('ref_axis',(0.,1.,0.));
#1791=DIRECTION('',(0.,1.,0.));
#1792=DIRECTION('',(0.,1.,0.));
#1793=DIRECTION('',(0.,0.,1.));
#1794=DIRECTION('center_axis',(0.,-1.,0.));
#1795=DIRECTION('ref_axis',(1.,0.,0.));
#1796=DIRECTION('',(1.,0.,0.));
#1797=DIRECTION('',(1.,0.,0.));
#1798=DIRECTION('',(0.,0.,1.));
#1799=DIRECTION('center_axis',(0.,0.,1.));
#1800=DIRECTION('ref_axis',(-0.997252742061945,-0.0740740740740786,0.));
#1801=DIRECTION('center_axis',(0.,0.,1.));
#1802=DIRECTION('ref_axis',(-0.997252742061945,-0.0740740740740786,0.));
#1803=DIRECTION('center_axis',(0.,0.,1.));
#1804=DIRECTION('ref_axis',(-0.997252742061945,-0.0740740740740786,0.));
#1805=DIRECTION('center_axis',(0.,0.,1.));
#1806=DIRECTION('ref_axis',(1.,0.,0.));
#1807=DIRECTION('center_axis',(0.,0.,1.));
#1808=DIRECTION('ref_axis',(1.,0.,0.));
#1809=CARTESIAN_POINT('',(0.,0.,0.));
#1810=CARTESIAN_POINT('Origin',(112.,3.00000000000001,0.));
#1811=CARTESIAN_POINT('',(111.,3.00000000000001,5.));
#1812=CARTESIAN_POINT('Origin',(112.,3.00000000000001,5.));
#1813=CARTESIAN_POINT('',(111.,3.00000000000001,0.));
#1814=CARTESIAN_POINT('',(111.,3.00000000000001,0.));
#1815=CARTESIAN_POINT('Origin',(112.,3.00000000000001,0.));
#1816=CARTESIAN_POINT('Origin',(-27.,97.,0.));
#1817=CARTESIAN_POINT('',(-28.,97.,5.));
#1818=CARTESIAN_POINT('Origin',(-27.,97.,5.));
#1819=CARTESIAN_POINT('',(-28.,97.,0.));
#1820=CARTESIAN_POINT('',(-28.,97.,0.));
#1821=CARTESIAN_POINT('Origin',(-27.,97.,0.));
#1822=CARTESIAN_POINT('Origin',(33.148555787384,85.5269912263836,0.));
#1823=CARTESIAN_POINT('',(33.1485561634384,85.7769912263832,0.));
#1824=CARTESIAN_POINT('',(33.3985557873837,85.5269908503292,0.));
#1825=CARTESIAN_POINT('Origin',(33.148555787384,85.5269912263836,0.));
#1826=CARTESIAN_POINT('',(33.3985557873837,85.5269908503292,5.));
#1827=CARTESIAN_POINT('',(33.3985557873837,85.5269908503292,0.));
#1828=CARTESIAN_POINT('',(33.1485561634384,85.7769912263832,5.));
#1829=CARTESIAN_POINT('Origin',(33.148555787384,85.5269912263836,5.));
#1830=CARTESIAN_POINT('',(33.1485561634384,85.7769912263832,0.));
#1831=CARTESIAN_POINT('Origin',(31.6485567353174,85.7769934827086,0.));
#1832=CARTESIAN_POINT('',(31.6485567353174,85.7769934827086,0.));
#1833=CARTESIAN_POINT('',(31.6485567353174,85.7769934827086,0.));
#1834=CARTESIAN_POINT('',(31.6485567353174,85.7769934827086,5.));
#1835=CARTESIAN_POINT('',(31.6485567353174,85.7769934827086,5.));
#1836=CARTESIAN_POINT('',(31.6485567353174,85.7769934827086,0.));
#1837=CARTESIAN_POINT('Origin',(31.6485571113718,86.0269934827083,0.));
#1838=CARTESIAN_POINT('',(31.3985571113736,86.0269944306394,0.));
#1839=CARTESIAN_POINT('Origin',(31.6485571113718,86.0269934827083,0.));
#1840=CARTESIAN_POINT('',(31.3985571113736,86.0269944306394,5.));
#1841=CARTESIAN_POINT('Origin',(31.6485571113718,86.0269934827083,5.));
#1842=CARTESIAN_POINT('',(31.3985571113736,86.0269944306394,0.));
#1843=CARTESIAN_POINT('Origin',(31.3985597986316,87.0144607058778,0.));
#1844=CARTESIAN_POINT('',(31.3985597986316,87.0144607058778,0.));
#1845=CARTESIAN_POINT('',(31.3985597986316,87.0144607058778,0.));
#1846=CARTESIAN_POINT('',(31.3985597986316,87.0144607058778,5.));
#1847=CARTESIAN_POINT('',(31.3985597986316,87.0144607058778,5.));
#1848=CARTESIAN_POINT('',(31.3985597986316,87.0144607058778,0.));
#1849=CARTESIAN_POINT('Origin',(31.148563887493,87.0158905359155,0.));
#1850=CARTESIAN_POINT('',(31.148563887482,87.2658905358355,0.));
#1851=CARTESIAN_POINT('Origin',(31.148563887493,87.0158905359155,0.));
#1852=CARTESIAN_POINT('',(31.148563887482,87.2658905358355,5.));
#1853=CARTESIAN_POINT('Origin',(31.148563887493,87.0158905359155,5.));
#1854=CARTESIAN_POINT('',(31.148563887482,87.2658905358355,0.));
#1855=CARTESIAN_POINT('Origin',(29.6564249905199,87.2658905358355,0.));
#1856=CARTESIAN_POINT('',(29.6564249905199,87.2658905358355,0.));
#1857=CARTESIAN_POINT('',(29.6564249905199,87.2658905358355,0.));
#1858=CARTESIAN_POINT('',(29.6564249905199,87.2658905358355,5.));
#1859=CARTESIAN_POINT('',(29.6564249905199,87.2658905358355,5.));
#1860=CARTESIAN_POINT('',(29.6564249905199,87.2658905358355,0.));
#1861=CARTESIAN_POINT('Origin',(29.39855554018,84.2769938587639,0.));
#1862=CARTESIAN_POINT('',(29.3986183252415,81.2769938594828,0.));
#1863=CARTESIAN_POINT('Origin',(29.39855554018,84.2769938587639,0.));
#1864=CARTESIAN_POINT('',(29.3986183252415,81.2769938594828,5.));
#1865=CARTESIAN_POINT('Origin',(29.39855554018,84.2769938587639,5.));
#1866=CARTESIAN_POINT('',(29.3986183252415,81.2769938594828,0.));
#1867=CARTESIAN_POINT('Origin',(32.1597706031019,81.2769938594209,0.));
#1868=CARTESIAN_POINT('',(32.1597706031019,81.2769938594209,0.));
#1869=CARTESIAN_POINT('',(32.1597706031019,81.2769938594209,0.));
#1870=CARTESIAN_POINT('',(32.1597706031019,81.2769938594209,5.));
#1871=CARTESIAN_POINT('',(32.1597706031019,81.2769938594209,5.));
#1872=CARTESIAN_POINT('',(32.1597706031019,81.2769938594209,0.));
#1873=CARTESIAN_POINT('Origin',(32.159770603098,81.0269938594209,0.));
#1874=CARTESIAN_POINT('',(32.408907712789,81.0477471862586,0.));
#1875=CARTESIAN_POINT('Origin',(32.159770603098,81.0269938594209,0.));
#1876=CARTESIAN_POINT('',(32.408907712789,81.0477471862586,5.));
#1877=CARTESIAN_POINT('Origin',(32.159770603098,81.0269938594209,5.));
#1878=CARTESIAN_POINT('',(32.408907712789,81.0477471862586,0.));
#1879=CARTESIAN_POINT('Origin',(35.3985530290804,81.2967871083127,0.));
#1880=CARTESIAN_POINT('',(38.398553029077,81.296782595659,0.));
#1881=CARTESIAN_POINT('Origin',(35.3985530290804,81.2967871083127,0.));
#1882=CARTESIAN_POINT('',(38.398553029077,81.296782595659,5.));
#1883=CARTESIAN_POINT('Origin',(35.3985530290804,81.2967871083127,5.));
#1884=CARTESIAN_POINT('',(38.398553029077,81.296782595659,0.));
#1885=CARTESIAN_POINT('Origin',(38.3985565325252,83.6258651323651,0.));
#1886=CARTESIAN_POINT('',(38.3985565325257,83.6258651323651,0.));
#1887=CARTESIAN_POINT('',(38.398553029077,81.296782595659,0.));
#1888=CARTESIAN_POINT('',(38.3985565325257,83.6258651323651,5.));
#1889=CARTESIAN_POINT('',(38.398553029077,81.296782595659,5.));
#1890=CARTESIAN_POINT('',(38.3985565325257,83.6258651323651,0.));
#1891=CARTESIAN_POINT('Origin',(38.6485565325254,83.6258647563108,0.));
#1892=CARTESIAN_POINT('',(38.6485569085798,83.8758647563105,0.));
#1893=CARTESIAN_POINT('Origin',(38.6485565325254,83.6258647563108,0.));
#1894=CARTESIAN_POINT('',(38.6485569085798,83.8758647563105,5.));
#1895=CARTESIAN_POINT('Origin',(38.6485565325254,83.6258647563108,5.));
#1896=CARTESIAN_POINT('',(38.6485569085798,83.8758647563105,0.));
#1897=CARTESIAN_POINT('Origin',(39.1485569085784,83.8758640041948,0.));
#1898=CARTESIAN_POINT('',(39.1485569085784,83.8758640041948,0.));
#1899=CARTESIAN_POINT('',(38.6485569085798,83.8758647563105,0.));
#1900=CARTESIAN_POINT('',(39.1485569085784,83.8758640041948,5.));
#1901=CARTESIAN_POINT('',(38.6485569085798,83.8758647563105,5.));
#1902=CARTESIAN_POINT('',(39.1485569085784,83.8758640041948,0.));
#1903=CARTESIAN_POINT('Origin',(39.1485572846328,84.1258640041876,0.));
#1904=CARTESIAN_POINT('',(39.3985572846255,84.1258636281332,0.));
#1905=CARTESIAN_POINT('Origin',(39.1485572846328,84.1258640041876,0.));
#1906=CARTESIAN_POINT('',(39.3985572846255,84.1258636281332,5.));
#1907=CARTESIAN_POINT('Origin',(39.1485572846328,84.1258640041876,5.));
#1908=CARTESIAN_POINT('',(39.3985572846255,84.1258636281332,0.));
#1909=CARTESIAN_POINT('Origin',(39.3985629254477,87.8758636281428,0.));
#1910=CARTESIAN_POINT('',(39.3985629254477,87.8758636281428,0.));
#1911=CARTESIAN_POINT('',(39.3985572846323,84.1258636281332,0.));
#1912=CARTESIAN_POINT('',(39.3985629254477,87.8758636281428,5.));
#1913=CARTESIAN_POINT('',(39.3985572846323,84.1258636281332,5.));
#1914=CARTESIAN_POINT('',(39.3985629254477,87.8758636281428,0.));
#1915=CARTESIAN_POINT('Origin',(38.8985644315109,87.8770908448021,0.));
#1916=CARTESIAN_POINT('',(38.398564431511,87.8770908448021,0.));
#1917=CARTESIAN_POINT('Origin',(38.8985644315109,87.8770908448021,0.));
#1918=CARTESIAN_POINT('',(38.398564431511,87.8770908448021,5.));
#1919=CARTESIAN_POINT('Origin',(38.8985644315109,87.8770908448021,5.));
#1920=CARTESIAN_POINT('',(38.398564431511,87.8770908448021,0.));
#1921=CARTESIAN_POINT('Origin',(38.3985644315107,87.0270338480231,0.));
#1922=CARTESIAN_POINT('',(38.3985644315107,87.0270338480231,0.));
#1923=CARTESIAN_POINT('',(38.3985644315108,87.8770908448021,0.));
#1924=CARTESIAN_POINT('',(38.3985644315107,87.0270338480231,5.));
#1925=CARTESIAN_POINT('',(38.3985644315108,87.8770908448021,5.));
#1926=CARTESIAN_POINT('',(38.3985644315107,87.0270338480231,0.));
#1927=CARTESIAN_POINT('Origin',(38.1485644315107,87.0270338480232,0.));
#1928=CARTESIAN_POINT('',(38.1485644315105,86.7770338480232,0.));
#1929=CARTESIAN_POINT('Origin',(38.1485644315107,87.0270338480232,0.));
#1930=CARTESIAN_POINT('',(38.1485644315105,86.7770338480232,5.));
#1931=CARTESIAN_POINT('Origin',(38.1485644315107,87.0270338480232,5.));
#1932=CARTESIAN_POINT('',(38.1485644315105,86.7770338480232,0.));
#1933=CARTESIAN_POINT('Origin',(37.6485644315106,86.7770338480231,0.));
#1934=CARTESIAN_POINT('',(37.6485644315106,86.7770338480231,0.));
#1935=CARTESIAN_POINT('',(38.1485644315105,86.7770338480232,0.));
#1936=CARTESIAN_POINT('',(37.6485644315106,86.7770338480231,5.));
#1937=CARTESIAN_POINT('',(38.1485644315105,86.7770338480232,5.));
#1938=CARTESIAN_POINT('',(37.6485644315106,86.7770338480231,0.));
#1939=CARTESIAN_POINT('Origin',(37.6485644315106,86.5270338480232,0.));
#1940=CARTESIAN_POINT('',(37.3985644315107,86.5270338480227,0.));
#1941=CARTESIAN_POINT('Origin',(37.6485644315106,86.5270338480232,0.));
#1942=CARTESIAN_POINT('',(37.3985644315107,86.5270338480227,5.));
#1943=CARTESIAN_POINT('Origin',(37.6485644315106,86.5270338480232,5.));
#1944=CARTESIAN_POINT('',(37.3985644315107,86.5270338480227,0.));
#1945=CARTESIAN_POINT('Origin',(37.3985644315107,85.6269908503293,0.));
#1946=CARTESIAN_POINT('',(37.3985644315107,85.6269908503293,0.));
#1947=CARTESIAN_POINT('',(37.3985644315107,86.5270338480227,0.));
#1948=CARTESIAN_POINT('',(37.3985644315107,85.6269908503293,5.));
#1949=CARTESIAN_POINT('',(37.3985644315107,86.5270338480227,5.));
#1950=CARTESIAN_POINT('',(37.3985644315107,85.6269908503293,0.));
#1951=CARTESIAN_POINT('Origin',(37.1485644315107,85.6269908503293,0.));
#1952=CARTESIAN_POINT('',(37.1485644315107,85.3769908503293,0.));
#1953=CARTESIAN_POINT('Origin',(37.1485644315107,85.6269908503293,0.));
#1954=CARTESIAN_POINT('',(37.1485644315107,85.3769908503293,5.));
#1955=CARTESIAN_POINT('Origin',(37.1485644315107,85.6269908503293,5.));
#1956=CARTESIAN_POINT('',(37.1485644315107,85.3769908503293,0.));
#1957=CARTESIAN_POINT('Origin',(33.5485557873839,85.3769908503293,0.));
#1958=CARTESIAN_POINT('',(33.5485557873839,85.3769908503293,0.));
#1959=CARTESIAN_POINT('',(33.5485557873839,85.3769908503293,0.));
#1960=CARTESIAN_POINT('',(33.5485557873839,85.3769908503293,5.));
#1961=CARTESIAN_POINT('',(33.5485557873839,85.3769908503293,5.));
#1962=CARTESIAN_POINT('',(33.5485557873839,85.3769908503293,0.));
#1963=CARTESIAN_POINT('Origin',(33.5485557873839,85.5269908503294,0.));
#1964=CARTESIAN_POINT('Origin',(33.5485557873839,85.5269908503294,0.));
#1965=CARTESIAN_POINT('Origin',(33.5485557873839,85.5269908503294,5.));
#1966=CARTESIAN_POINT('Origin',(-27.,33.,0.));
#1967=CARTESIAN_POINT('',(-28.,33.,5.));
#1968=CARTESIAN_POINT('Origin',(-27.,33.,5.));
#1969=CARTESIAN_POINT('',(-28.,33.,0.));
#1970=CARTESIAN_POINT('',(-28.,33.,0.));
#1971=CARTESIAN_POINT('Origin',(-27.,33.,0.));
#1972=CARTESIAN_POINT('Origin',(85.2317333912552,86.5270338480232,0.));
#1973=CARTESIAN_POINT('',(85.2317333912552,86.7770338480231,0.));
#1974=CARTESIAN_POINT('',(85.4817333912551,86.5270338480227,0.));
#1975=CARTESIAN_POINT('Origin',(85.2317333912552,86.5270338480232,0.));
#1976=CARTESIAN_POINT('',(85.4817333912551,86.5270338480227,5.));
#1977=CARTESIAN_POINT('',(85.4817333912551,86.5270338480227,0.));
#1978=CARTESIAN_POINT('',(85.2317333912552,86.7770338480231,5.));
#1979=CARTESIAN_POINT('Origin',(85.2317333912552,86.5270338480232,5.));
#1980=CARTESIAN_POINT('',(85.2317333912552,86.7770338480231,0.));
#1981=CARTESIAN_POINT('Origin',(84.7317333912552,86.7770338480232,0.));
#1982=CARTESIAN_POINT('',(84.7317333912552,86.7770338480231,0.));
#1983=CARTESIAN_POINT('',(84.7317333912552,86.7770338480232,0.));
#1984=CARTESIAN_POINT('',(84.7317333912552,86.7770338480231,5.));
#1985=CARTESIAN_POINT('',(84.7317333912552,86.7770338480232,5.));
#1986=CARTESIAN_POINT('',(84.7317333912552,86.7770338480231,0.));
#1987=CARTESIAN_POINT('Origin',(84.7317333912551,87.0270338480232,0.));
#1988=CARTESIAN_POINT('',(84.4817333912551,87.0270338480231,0.));
#1989=CARTESIAN_POINT('Origin',(84.7317333912551,87.0270338480232,0.));
#1990=CARTESIAN_POINT('',(84.4817333912551,87.0270338480231,5.));
#1991=CARTESIAN_POINT('Origin',(84.7317333912551,87.0270338480232,5.));
#1992=CARTESIAN_POINT('',(84.4817333912551,87.0270338480231,0.));
#1993=CARTESIAN_POINT('Origin',(84.481733391255,87.8770908448021,0.));
#1994=CARTESIAN_POINT('',(84.481733391255,87.8770908448021,0.));
#1995=CARTESIAN_POINT('',(84.481733391255,87.8770908448021,0.));
#1996=CARTESIAN_POINT('',(84.481733391255,87.8770908448021,5.));
#1997=CARTESIAN_POINT('',(84.481733391255,87.8770908448021,5.));
#1998=CARTESIAN_POINT('',(84.481733391255,87.8770908448021,0.));
#1999=CARTESIAN_POINT('Origin',(83.9817333912549,87.8770908448021,0.));
#2000=CARTESIAN_POINT('',(83.481734897318,87.8758636281428,0.));
#2001=CARTESIAN_POINT('Origin',(83.9817333912549,87.8770908448021,0.));
#2002=CARTESIAN_POINT('',(83.481734897318,87.8758636281428,5.));
#2003=CARTESIAN_POINT('Origin',(83.9817333912549,87.8770908448021,5.));
#2004=CARTESIAN_POINT('',(83.481734897318,87.8758636281428,0.));
#2005=CARTESIAN_POINT('Origin',(83.4817405381335,84.1258636281332,0.));
#2006=CARTESIAN_POINT('',(83.4817405381335,84.1258636281332,0.));
#2007=CARTESIAN_POINT('',(83.4817405381335,84.1258636281332,0.));
#2008=CARTESIAN_POINT('',(83.4817405381335,84.1258636281332,5.));
#2009=CARTESIAN_POINT('',(83.4817405381335,84.1258636281332,5.));
#2010=CARTESIAN_POINT('',(83.4817405381335,84.1258636281332,0.));
#2011=CARTESIAN_POINT('Origin',(83.731740538133,84.1258640041876,0.));
#2012=CARTESIAN_POINT('',(83.7317409141874,83.875864004188,0.));
#2013=CARTESIAN_POINT('Origin',(83.731740538133,84.1258640041876,0.));
#2014=CARTESIAN_POINT('',(83.7317409141874,83.875864004188,5.));
#2015=CARTESIAN_POINT('Origin',(83.731740538133,84.1258640041876,5.));
#2016=CARTESIAN_POINT('',(83.7317409141874,83.875864004188,0.));
#2017=CARTESIAN_POINT('Origin',(84.231740914186,83.8758647563105,0.));
#2018=CARTESIAN_POINT('',(84.231740914186,83.875864756311,0.));
#2019=CARTESIAN_POINT('',(84.231740914186,83.8758647563105,0.));
#2020=CARTESIAN_POINT('',(84.231740914186,83.875864756311,5.));
#2021=CARTESIAN_POINT('',(84.231740914186,83.8758647563105,5.));
#2022=CARTESIAN_POINT('',(84.231740914186,83.875864756311,0.));
#2023=CARTESIAN_POINT('Origin',(84.2317412902404,83.6258647563108,0.));
#2024=CARTESIAN_POINT('',(84.4817412902406,83.6258651323651,0.));
#2025=CARTESIAN_POINT('Origin',(84.2317412902404,83.6258647563108,0.));
#2026=CARTESIAN_POINT('',(84.4817412902406,83.6258651323651,5.));
#2027=CARTESIAN_POINT('Origin',(84.2317412902404,83.6258647563108,5.));
#2028=CARTESIAN_POINT('',(84.4817412902406,83.6258651323651,0.));
#2029=CARTESIAN_POINT('Origin',(84.4817447936888,81.296782595659,0.));
#2030=CARTESIAN_POINT('',(84.4817447936888,81.296782595659,0.));
#2031=CARTESIAN_POINT('',(84.4817447936888,81.296782595659,0.));
#2032=CARTESIAN_POINT('',(84.4817447936888,81.296782595659,5.));
#2033=CARTESIAN_POINT('',(84.4817447936888,81.296782595659,5.));
#2034=CARTESIAN_POINT('',(84.4817447936888,81.296782595659,0.));
#2035=CARTESIAN_POINT('Origin',(87.4817447936854,81.2967871083127,0.));
#2036=CARTESIAN_POINT('',(90.4713901099768,81.0477471862586,0.));
#2037=CARTESIAN_POINT('Origin',(87.4817447936854,81.2967871083127,0.));
#2038=CARTESIAN_POINT('',(90.4713901099768,81.0477471862586,5.));
#2039=CARTESIAN_POINT('Origin',(87.4817447936854,81.2967871083127,5.));
#2040=CARTESIAN_POINT('',(90.4713901099768,81.0477471862586,0.));
#2041=CARTESIAN_POINT('Origin',(90.7205272196678,81.0269938594209,0.));
#2042=CARTESIAN_POINT('',(90.7205272196639,81.2769938594209,0.));
#2043=CARTESIAN_POINT('Origin',(90.7205272196678,81.0269938594209,0.));
#2044=CARTESIAN_POINT('',(90.7205272196639,81.2769938594209,5.));
#2045=CARTESIAN_POINT('Origin',(90.7205272196678,81.0269938594209,5.));
#2046=CARTESIAN_POINT('',(90.7205272196639,81.2769938594209,0.));
#2047=CARTESIAN_POINT('Origin',(93.4816794975243,81.2769938594209,0.));
#2048=CARTESIAN_POINT('',(93.4816794975243,81.2769938594209,0.));
#2049=CARTESIAN_POINT('',(90.7205272196639,81.2769938594209,0.));
#2050=CARTESIAN_POINT('',(93.4816794975243,81.2769938594209,5.));
#2051=CARTESIAN_POINT('',(90.7205272196639,81.2769938594209,5.));
#2052=CARTESIAN_POINT('',(93.4816794975243,81.2769938594209,0.));
#2053=CARTESIAN_POINT('Origin',(93.4817422825858,84.2769938587639,0.));
#2054=CARTESIAN_POINT('',(93.2238728322406,87.2658905358972,0.));
#2055=CARTESIAN_POINT('Origin',(93.4817422825858,84.2769938587639,0.));
#2056=CARTESIAN_POINT('',(93.2238728322406,87.2658905358972,5.));
#2057=CARTESIAN_POINT('Origin',(93.4817422825858,84.2769938587639,5.));
#2058=CARTESIAN_POINT('',(93.2238728322406,87.2658905358972,0.));
#2059=CARTESIAN_POINT('Origin',(91.7317339352838,87.2658905358355,0.));
#2060=CARTESIAN_POINT('',(91.7317339352838,87.2658905358355,0.));
#2061=CARTESIAN_POINT('',(93.2238728322459,87.2658905358355,0.));
#2062=CARTESIAN_POINT('',(91.7317339352838,87.2658905358355,5.));
#2063=CARTESIAN_POINT('',(93.2238728322459,87.2658905358355,5.));
#2064=CARTESIAN_POINT('',(91.7317339352838,87.2658905358355,0.));
#2065=CARTESIAN_POINT('Origin',(91.7317339352728,87.0158905359155,0.));
#2066=CARTESIAN_POINT('',(91.4817380242141,87.0144607058783,0.));
#2067=CARTESIAN_POINT('Origin',(91.7317339352728,87.0158905359155,0.));
#2068=CARTESIAN_POINT('',(91.4817380242141,87.0144607058783,5.));
#2069=CARTESIAN_POINT('Origin',(91.7317339352728,87.0158905359155,5.));
#2070=CARTESIAN_POINT('',(91.4817380242141,87.0144607058783,0.));
#2071=CARTESIAN_POINT('Origin',(91.4817407113922,86.0269944306394,0.));
#2072=CARTESIAN_POINT('',(91.4817407113922,86.0269944306394,0.));
#2073=CARTESIAN_POINT('',(91.4817380241341,87.0144607058778,0.));
#2074=CARTESIAN_POINT('',(91.4817407113922,86.0269944306394,5.));
#2075=CARTESIAN_POINT('',(91.4817380241341,87.0144607058778,5.));
#2076=CARTESIAN_POINT('',(91.4817407113922,86.0269944306394,0.));
#2077=CARTESIAN_POINT('Origin',(91.231740711394,86.0269934827083,0.));
#2078=CARTESIAN_POINT('',(91.2317410874484,85.7769934827086,0.));
#2079=CARTESIAN_POINT('Origin',(91.231740711394,86.0269934827083,0.));
#2080=CARTESIAN_POINT('',(91.2317410874484,85.7769934827086,5.));
#2081=CARTESIAN_POINT('Origin',(91.231740711394,86.0269934827083,5.));
#2082=CARTESIAN_POINT('',(91.2317410874484,85.7769934827086,0.));
#2083=CARTESIAN_POINT('Origin',(89.7317416593274,85.7769912263832,0.));
#2084=CARTESIAN_POINT('',(89.7317416593274,85.7769912263832,0.));
#2085=CARTESIAN_POINT('',(91.2317410874484,85.7769934827086,0.));
#2086=CARTESIAN_POINT('',(89.7317416593274,85.7769912263832,5.));
#2087=CARTESIAN_POINT('',(91.2317410874484,85.7769934827086,5.));
#2088=CARTESIAN_POINT('',(89.7317416593274,85.7769912263832,0.));
#2089=CARTESIAN_POINT('Origin',(89.7317420353818,85.5269912263836,0.));
#2090=CARTESIAN_POINT('',(89.4817420353817,85.526991075962,0.));
#2091=CARTESIAN_POINT('Origin',(89.7317420353818,85.5269912263836,0.));
#2092=CARTESIAN_POINT('',(89.4817420353817,85.526991075962,5.));
#2093=CARTESIAN_POINT('Origin',(89.7317420353818,85.5269912263836,5.));
#2094=CARTESIAN_POINT('',(89.4817420353817,85.526991075962,0.));
#2095=CARTESIAN_POINT('Origin',(89.3317420353819,85.5269908503294,0.));
#2096=CARTESIAN_POINT('',(89.3317420353819,85.3769908503293,0.));
#2097=CARTESIAN_POINT('Origin',(89.3317420353819,85.5269908503294,0.));
#2098=CARTESIAN_POINT('',(89.3317420353819,85.3769908503293,5.));
#2099=CARTESIAN_POINT('Origin',(89.3317420353819,85.5269908503294,5.));
#2100=CARTESIAN_POINT('',(89.3317420353819,85.3769908503293,0.));
#2101=CARTESIAN_POINT('Origin',(85.7317333912551,85.3769908503293,0.));
#2102=CARTESIAN_POINT('',(85.7317333912551,85.3769908503293,0.));
#2103=CARTESIAN_POINT('',(89.3317420353819,85.3769908503293,0.));
#2104=CARTESIAN_POINT('',(85.7317333912551,85.3769908503293,5.));
#2105=CARTESIAN_POINT('',(89.3317420353819,85.3769908503293,5.));
#2106=CARTESIAN_POINT('',(85.7317333912551,85.3769908503293,0.));
#2107=CARTESIAN_POINT('Origin',(85.7317333912551,85.6269908503293,0.));
#2108=CARTESIAN_POINT('',(85.4817333912551,85.6269908503293,0.));
#2109=CARTESIAN_POINT('Origin',(85.7317333912551,85.6269908503293,0.));
#2110=CARTESIAN_POINT('',(85.4817333912551,85.6269908503293,5.));
#2111=CARTESIAN_POINT('Origin',(85.7317333912551,85.6269908503293,5.));
#2112=CARTESIAN_POINT('',(85.4817333912551,85.6269908503293,0.));
#2113=CARTESIAN_POINT('Origin',(85.4817333912551,86.5270338480227,0.));
#2114=CARTESIAN_POINT('',(85.4817333912551,85.6269908503293,0.));
#2115=CARTESIAN_POINT('',(85.4817333912551,85.6269908503293,5.));
#2116=CARTESIAN_POINT('Origin',(147.,97.,0.));
#2117=CARTESIAN_POINT('',(146.,97.,5.));
#2118=CARTESIAN_POINT('Origin',(147.,97.,5.));
#2119=CARTESIAN_POINT('',(146.,97.,0.));
#2120=CARTESIAN_POINT('',(146.,97.,0.));
#2121=CARTESIAN_POINT('Origin',(147.,97.,0.));
#2122=CARTESIAN_POINT('Origin',(61.4401489113829,0.,0.));
#2123=CARTESIAN_POINT('',(86.3714674629315,1.85185185185185,0.));
#2124=CARTESIAN_POINT('',(36.5088303598343,1.85185185185185,0.));
#2125=CARTESIAN_POINT('Origin',(61.4401489113829,0.,0.));
#2126=CARTESIAN_POINT('',(86.3714674629315,1.85185185185185,5.));
#2127=CARTESIAN_POINT('',(86.3714674629315,1.85185185185185,0.));
#2128=CARTESIAN_POINT('',(36.5088303598342,1.85185185185185,5.));
#2129=CARTESIAN_POINT('Origin',(61.4401489113829,0.,5.));
#2130=CARTESIAN_POINT('',(36.5088303598343,1.85185185185185,0.));
#2131=CARTESIAN_POINT('Origin',(34.5143248757104,2.,0.));
#2132=CARTESIAN_POINT('',(34.5143248757104,0.,0.));
#2133=CARTESIAN_POINT('Origin',(34.5143248757104,2.,0.));
#2134=CARTESIAN_POINT('',(34.5143248757104,0.,5.));
#2135=CARTESIAN_POINT('Origin',(34.5143248757104,2.,5.));
#2136=CARTESIAN_POINT('',(34.5143248757104,0.,0.));
#2137=CARTESIAN_POINT('Origin',(0.,0.,0.));
#2138=CARTESIAN_POINT('',(0.,0.,0.));
#2139=CARTESIAN_POINT('',(0.,0.,0.));
#2140=CARTESIAN_POINT('',(0.,0.,5.));
#2141=CARTESIAN_POINT('',(0.,0.,5.));
#2142=CARTESIAN_POINT('',(0.,0.,0.));
#2143=CARTESIAN_POINT('Origin',(0.,30.,0.));
#2144=CARTESIAN_POINT('',(0.,30.,0.));
#2145=CARTESIAN_POINT('',(0.,30.,0.));
#2146=CARTESIAN_POINT('',(0.,30.,5.));
#2147=CARTESIAN_POINT('',(0.,30.,5.));
#2148=CARTESIAN_POINT('',(0.,30.,0.));
#2149=CARTESIAN_POINT('Origin',(-30.,30.,0.));
#2150=CARTESIAN_POINT('',(-30.,30.,0.));
#2151=CARTESIAN_POINT('',(-30.,30.,0.));
#2152=CARTESIAN_POINT('',(-30.,30.,5.));
#2153=CARTESIAN_POINT('',(-30.,30.,5.));
#2154=CARTESIAN_POINT('',(-30.,30.,0.));
#2155=CARTESIAN_POINT('Origin',(-30.,100.,0.));
#2156=CARTESIAN_POINT('',(-30.,100.,0.));
#2157=CARTESIAN_POINT('',(-30.,100.,0.));
#2158=CARTESIAN_POINT('',(-30.,100.,5.));
#2159=CARTESIAN_POINT('',(-30.,100.,5.));
#2160=CARTESIAN_POINT('',(-30.,100.,0.));
#2161=CARTESIAN_POINT('Origin',(150.,100.,0.));
#2162=CARTESIAN_POINT('',(150.,100.,0.));
#2163=CARTESIAN_POINT('',(150.,100.,0.));
#2164=CARTESIAN_POINT('',(150.,100.,5.));
#2165=CARTESIAN_POINT('',(150.,100.,5.));
#2166=CARTESIAN_POINT('',(150.,100.,0.));
#2167=CARTESIAN_POINT('Origin',(150.,0.,0.));
#2168=CARTESIAN_POINT('',(150.,0.,0.));
#2169=CARTESIAN_POINT('',(150.,0.,0.));
#2170=CARTESIAN_POINT('',(150.,0.,5.));
#2171=CARTESIAN_POINT('',(150.,0.,5.));
#2172=CARTESIAN_POINT('',(150.,0.,0.));
#2173=CARTESIAN_POINT('Origin',(88.3659729470554,0.,0.));
#2174=CARTESIAN_POINT('',(88.3659729470554,5.55111512312578E-16,0.));
#2175=CARTESIAN_POINT('',(88.3659729470554,0.,0.));
#2176=CARTESIAN_POINT('',(88.3659729470554,0.,5.));
#2177=CARTESIAN_POINT('',(88.3659729470554,0.,5.));
#2178=CARTESIAN_POINT('',(88.3659729470554,5.55111512312578E-16,0.));
#2179=CARTESIAN_POINT('Origin',(88.3659729470554,2.00000000000001,0.));
#2180=CARTESIAN_POINT('Origin',(88.3659729470554,2.00000000000001,0.));
#2181=CARTESIAN_POINT('Origin',(88.3659729470554,2.00000000000001,5.));
#2182=CARTESIAN_POINT('Origin',(60.,50.,5.));
#2183=CARTESIAN_POINT('Origin',(60.,50.,0.));
#2184=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#2188,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#2185=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#2188,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#2186=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#2184))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#2188,#2190,#2191))
REPRESENTATION_CONTEXT('','3D')
);
#2187=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#2185))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#2188,#2190,#2191))
REPRESENTATION_CONTEXT('','3D')
);
#2188=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#2189=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT($,.METRE.)
);
#2190=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#2191=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#2192=SHAPE_DEFINITION_REPRESENTATION(#2193,#2194);
#2193=PRODUCT_DEFINITION_SHAPE('',$,#2196);
#2194=SHAPE_REPRESENTATION('',(#1296),#2186);
#2195=PRODUCT_DEFINITION_CONTEXT('part definition',#2200,'design');
#2196=PRODUCT_DEFINITION('S_983','S_983 v1',#2197,#2195);
#2197=PRODUCT_DEFINITION_FORMATION('',$,#2202);
#2198=PRODUCT_RELATED_PRODUCT_CATEGORY('S_983 v1','S_983 v1',(#2202));
#2199=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2009,#2200);
#2200=APPLICATION_CONTEXT(
'Core Data for Automotive Mechanical Design Process');
#2201=PRODUCT_CONTEXT('part definition',#2200,'mechanical');
#2202=PRODUCT('S_983','S_983 v1',$,(#2201));
#2203=PRESENTATION_STYLE_ASSIGNMENT((#2204));
#2204=SURFACE_STYLE_USAGE(.BOTH.,#2205);
#2205=SURFACE_SIDE_STYLE('',(#2206));
#2206=SURFACE_STYLE_FILL_AREA(#2207);
#2207=FILL_AREA_STYLE('Steel - Satin',(#2208));
#2208=FILL_AREA_STYLE_COLOUR('Steel - Satin',#2209);
#2209=COLOUR_RGB('Steel - Satin',0.627450980392157,0.627450980392157,0.627450980392157);
ENDSEC;
END-ISO-10303-21;
