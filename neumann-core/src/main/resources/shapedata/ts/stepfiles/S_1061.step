ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ (''),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 
'G:/Shared drives/TIP/Validation/SMF KION/Tset SMF Shapes/S_1061.step',

/* time_stamp */ '2021-06-09T11:09:25+02:00',
/* author */ (''),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v18.1',
/* originating_system */ 'Autodesk Translation Framework v10.9.0.1377',

/* authorisation */ '');

FILE_SCHEMA (('AUTOMOTIVE_DESIGN { 1 0 10303 214 3 1 1 }'));
ENDSEC;

DATA;
#10=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#13),#5907);
#11=SHAPE_REPRESENTATION_RELATIONSHIP('SRR','None',#5914,#12);
#12=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#14),#5906);
#13=STYLED_ITEM('',(#5923),#14);
#14=MANIFOLD_SOLID_BREP('Body1',#3475);
#15=FACE_BOUND('',#501,.T.);
#16=FACE_BOUND('',#502,.T.);
#17=FACE_BOUND('',#503,.T.);
#18=FACE_BOUND('',#504,.T.);
#19=FACE_BOUND('',#505,.T.);
#20=FACE_BOUND('',#506,.T.);
#21=FACE_BOUND('',#507,.T.);
#22=FACE_BOUND('',#508,.T.);
#23=FACE_BOUND('',#509,.T.);
#24=FACE_BOUND('',#510,.T.);
#25=FACE_BOUND('',#511,.T.);
#26=FACE_BOUND('',#512,.T.);
#27=FACE_BOUND('',#513,.T.);
#28=FACE_BOUND('',#514,.T.);
#29=FACE_BOUND('',#515,.T.);
#30=FACE_BOUND('',#516,.T.);
#31=FACE_BOUND('',#517,.T.);
#32=FACE_BOUND('',#518,.T.);
#33=FACE_BOUND('',#519,.T.);
#34=FACE_BOUND('',#520,.T.);
#35=FACE_BOUND('',#521,.T.);
#36=FACE_BOUND('',#522,.T.);
#37=FACE_BOUND('',#523,.T.);
#38=FACE_BOUND('',#524,.T.);
#39=FACE_BOUND('',#525,.T.);
#40=FACE_BOUND('',#526,.T.);
#41=FACE_BOUND('',#527,.T.);
#42=FACE_BOUND('',#528,.T.);
#43=FACE_BOUND('',#529,.T.);
#44=FACE_BOUND('',#530,.T.);
#45=FACE_BOUND('',#531,.T.);
#46=FACE_BOUND('',#532,.T.);
#47=FACE_BOUND('',#533,.T.);
#48=FACE_BOUND('',#534,.T.);
#49=FACE_BOUND('',#535,.T.);
#50=FACE_BOUND('',#536,.T.);
#51=FACE_BOUND('',#537,.T.);
#52=FACE_BOUND('',#538,.T.);
#53=FACE_BOUND('',#539,.T.);
#54=FACE_BOUND('',#540,.T.);
#55=FACE_BOUND('',#541,.T.);
#56=FACE_BOUND('',#542,.T.);
#57=FACE_BOUND('',#543,.T.);
#58=FACE_BOUND('',#545,.T.);
#59=FACE_BOUND('',#546,.T.);
#60=FACE_BOUND('',#547,.T.);
#61=FACE_BOUND('',#548,.T.);
#62=FACE_BOUND('',#549,.T.);
#63=FACE_BOUND('',#550,.T.);
#64=FACE_BOUND('',#551,.T.);
#65=FACE_BOUND('',#552,.T.);
#66=FACE_BOUND('',#553,.T.);
#67=FACE_BOUND('',#554,.T.);
#68=FACE_BOUND('',#555,.T.);
#69=FACE_BOUND('',#556,.T.);
#70=FACE_BOUND('',#557,.T.);
#71=FACE_BOUND('',#558,.T.);
#72=FACE_BOUND('',#559,.T.);
#73=FACE_BOUND('',#560,.T.);
#74=FACE_BOUND('',#561,.T.);
#75=FACE_BOUND('',#562,.T.);
#76=FACE_BOUND('',#563,.T.);
#77=FACE_BOUND('',#564,.T.);
#78=FACE_BOUND('',#565,.T.);
#79=FACE_BOUND('',#566,.T.);
#80=FACE_BOUND('',#567,.T.);
#81=FACE_BOUND('',#568,.T.);
#82=FACE_BOUND('',#569,.T.);
#83=FACE_BOUND('',#570,.T.);
#84=FACE_BOUND('',#571,.T.);
#85=FACE_BOUND('',#572,.T.);
#86=FACE_BOUND('',#573,.T.);
#87=FACE_BOUND('',#574,.T.);
#88=FACE_BOUND('',#575,.T.);
#89=FACE_BOUND('',#576,.T.);
#90=FACE_BOUND('',#577,.T.);
#91=FACE_BOUND('',#578,.T.);
#92=FACE_BOUND('',#579,.T.);
#93=FACE_BOUND('',#580,.T.);
#94=FACE_BOUND('',#581,.T.);
#95=FACE_BOUND('',#582,.T.);
#96=FACE_BOUND('',#583,.T.);
#97=FACE_BOUND('',#584,.T.);
#98=FACE_BOUND('',#585,.T.);
#99=FACE_BOUND('',#586,.T.);
#100=FACE_BOUND('',#587,.T.);
#101=PLANE('',#3498);
#102=PLANE('',#3502);
#103=PLANE('',#3506);
#104=PLANE('',#3510);
#105=PLANE('',#3610);
#106=PLANE('',#3614);
#107=PLANE('',#3618);
#108=PLANE('',#3622);
#109=PLANE('',#3626);
#110=PLANE('',#3630);
#111=PLANE('',#3634);
#112=PLANE('',#3638);
#113=PLANE('',#3642);
#114=PLANE('',#3646);
#115=PLANE('',#3650);
#116=PLANE('',#3654);
#117=PLANE('',#3655);
#118=PLANE('',#3659);
#119=PLANE('',#3663);
#120=PLANE('',#3667);
#121=PLANE('',#3674);
#122=PLANE('',#3678);
#123=PLANE('',#3682);
#124=PLANE('',#3686);
#125=PLANE('',#3690);
#126=PLANE('',#3694);
#127=PLANE('',#3698);
#128=PLANE('',#3702);
#129=PLANE('',#3706);
#130=PLANE('',#3710);
#131=PLANE('',#3714);
#132=PLANE('',#3718);
#133=PLANE('',#3722);
#134=PLANE('',#3726);
#135=PLANE('',#3730);
#136=PLANE('',#3734);
#137=PLANE('',#3735);
#138=PLANE('',#3742);
#139=PLANE('',#3752);
#140=PLANE('',#3756);
#141=PLANE('',#3763);
#142=PLANE('',#3767);
#143=PLANE('',#3771);
#144=PLANE('',#3775);
#145=PLANE('',#3779);
#146=PLANE('',#3783);
#147=PLANE('',#3787);
#148=PLANE('',#3791);
#149=PLANE('',#3795);
#150=PLANE('',#3799);
#151=PLANE('',#3803);
#152=PLANE('',#3807);
#153=PLANE('',#3811);
#154=PLANE('',#3815);
#155=PLANE('',#3819);
#156=PLANE('',#3823);
#157=PLANE('',#3827);
#158=PLANE('',#3831);
#159=PLANE('',#3838);
#160=PLANE('',#3842);
#161=PLANE('',#3846);
#162=PLANE('',#3853);
#163=PLANE('',#3857);
#164=PLANE('',#3861);
#165=PLANE('',#3862);
#166=FACE_OUTER_BOUND('',#334,.T.);
#167=FACE_OUTER_BOUND('',#335,.T.);
#168=FACE_OUTER_BOUND('',#336,.T.);
#169=FACE_OUTER_BOUND('',#337,.T.);
#170=FACE_OUTER_BOUND('',#338,.T.);
#171=FACE_OUTER_BOUND('',#339,.T.);
#172=FACE_OUTER_BOUND('',#340,.T.);
#173=FACE_OUTER_BOUND('',#341,.T.);
#174=FACE_OUTER_BOUND('',#342,.T.);
#175=FACE_OUTER_BOUND('',#343,.T.);
#176=FACE_OUTER_BOUND('',#344,.T.);
#177=FACE_OUTER_BOUND('',#345,.T.);
#178=FACE_OUTER_BOUND('',#346,.T.);
#179=FACE_OUTER_BOUND('',#347,.T.);
#180=FACE_OUTER_BOUND('',#348,.T.);
#181=FACE_OUTER_BOUND('',#349,.T.);
#182=FACE_OUTER_BOUND('',#350,.T.);
#183=FACE_OUTER_BOUND('',#351,.T.);
#184=FACE_OUTER_BOUND('',#352,.T.);
#185=FACE_OUTER_BOUND('',#353,.T.);
#186=FACE_OUTER_BOUND('',#354,.T.);
#187=FACE_OUTER_BOUND('',#355,.T.);
#188=FACE_OUTER_BOUND('',#356,.T.);
#189=FACE_OUTER_BOUND('',#357,.T.);
#190=FACE_OUTER_BOUND('',#358,.T.);
#191=FACE_OUTER_BOUND('',#359,.T.);
#192=FACE_OUTER_BOUND('',#360,.T.);
#193=FACE_OUTER_BOUND('',#361,.T.);
#194=FACE_OUTER_BOUND('',#362,.T.);
#195=FACE_OUTER_BOUND('',#363,.T.);
#196=FACE_OUTER_BOUND('',#364,.T.);
#197=FACE_OUTER_BOUND('',#365,.T.);
#198=FACE_OUTER_BOUND('',#366,.T.);
#199=FACE_OUTER_BOUND('',#367,.T.);
#200=FACE_OUTER_BOUND('',#368,.T.);
#201=FACE_OUTER_BOUND('',#369,.T.);
#202=FACE_OUTER_BOUND('',#370,.T.);
#203=FACE_OUTER_BOUND('',#371,.T.);
#204=FACE_OUTER_BOUND('',#372,.T.);
#205=FACE_OUTER_BOUND('',#373,.T.);
#206=FACE_OUTER_BOUND('',#374,.T.);
#207=FACE_OUTER_BOUND('',#375,.T.);
#208=FACE_OUTER_BOUND('',#376,.T.);
#209=FACE_OUTER_BOUND('',#377,.T.);
#210=FACE_OUTER_BOUND('',#378,.T.);
#211=FACE_OUTER_BOUND('',#379,.T.);
#212=FACE_OUTER_BOUND('',#380,.T.);
#213=FACE_OUTER_BOUND('',#381,.T.);
#214=FACE_OUTER_BOUND('',#382,.T.);
#215=FACE_OUTER_BOUND('',#383,.T.);
#216=FACE_OUTER_BOUND('',#384,.T.);
#217=FACE_OUTER_BOUND('',#385,.T.);
#218=FACE_OUTER_BOUND('',#386,.T.);
#219=FACE_OUTER_BOUND('',#387,.T.);
#220=FACE_OUTER_BOUND('',#388,.T.);
#221=FACE_OUTER_BOUND('',#389,.T.);
#222=FACE_OUTER_BOUND('',#390,.T.);
#223=FACE_OUTER_BOUND('',#391,.T.);
#224=FACE_OUTER_BOUND('',#392,.T.);
#225=FACE_OUTER_BOUND('',#393,.T.);
#226=FACE_OUTER_BOUND('',#394,.T.);
#227=FACE_OUTER_BOUND('',#395,.T.);
#228=FACE_OUTER_BOUND('',#396,.T.);
#229=FACE_OUTER_BOUND('',#397,.T.);
#230=FACE_OUTER_BOUND('',#398,.T.);
#231=FACE_OUTER_BOUND('',#399,.T.);
#232=FACE_OUTER_BOUND('',#400,.T.);
#233=FACE_OUTER_BOUND('',#401,.T.);
#234=FACE_OUTER_BOUND('',#402,.T.);
#235=FACE_OUTER_BOUND('',#403,.T.);
#236=FACE_OUTER_BOUND('',#404,.T.);
#237=FACE_OUTER_BOUND('',#405,.T.);
#238=FACE_OUTER_BOUND('',#406,.T.);
#239=FACE_OUTER_BOUND('',#407,.T.);
#240=FACE_OUTER_BOUND('',#408,.T.);
#241=FACE_OUTER_BOUND('',#409,.T.);
#242=FACE_OUTER_BOUND('',#410,.T.);
#243=FACE_OUTER_BOUND('',#411,.T.);
#244=FACE_OUTER_BOUND('',#412,.T.);
#245=FACE_OUTER_BOUND('',#413,.T.);
#246=FACE_OUTER_BOUND('',#414,.T.);
#247=FACE_OUTER_BOUND('',#415,.T.);
#248=FACE_OUTER_BOUND('',#416,.T.);
#249=FACE_OUTER_BOUND('',#417,.T.);
#250=FACE_OUTER_BOUND('',#418,.T.);
#251=FACE_OUTER_BOUND('',#419,.T.);
#252=FACE_OUTER_BOUND('',#420,.T.);
#253=FACE_OUTER_BOUND('',#421,.T.);
#254=FACE_OUTER_BOUND('',#422,.T.);
#255=FACE_OUTER_BOUND('',#423,.T.);
#256=FACE_OUTER_BOUND('',#424,.T.);
#257=FACE_OUTER_BOUND('',#425,.T.);
#258=FACE_OUTER_BOUND('',#426,.T.);
#259=FACE_OUTER_BOUND('',#427,.T.);
#260=FACE_OUTER_BOUND('',#428,.T.);
#261=FACE_OUTER_BOUND('',#429,.T.);
#262=FACE_OUTER_BOUND('',#430,.T.);
#263=FACE_OUTER_BOUND('',#431,.T.);
#264=FACE_OUTER_BOUND('',#432,.T.);
#265=FACE_OUTER_BOUND('',#433,.T.);
#266=FACE_OUTER_BOUND('',#434,.T.);
#267=FACE_OUTER_BOUND('',#435,.T.);
#268=FACE_OUTER_BOUND('',#436,.T.);
#269=FACE_OUTER_BOUND('',#437,.T.);
#270=FACE_OUTER_BOUND('',#438,.T.);
#271=FACE_OUTER_BOUND('',#439,.T.);
#272=FACE_OUTER_BOUND('',#440,.T.);
#273=FACE_OUTER_BOUND('',#441,.T.);
#274=FACE_OUTER_BOUND('',#442,.T.);
#275=FACE_OUTER_BOUND('',#443,.T.);
#276=FACE_OUTER_BOUND('',#444,.T.);
#277=FACE_OUTER_BOUND('',#445,.T.);
#278=FACE_OUTER_BOUND('',#446,.T.);
#279=FACE_OUTER_BOUND('',#447,.T.);
#280=FACE_OUTER_BOUND('',#448,.T.);
#281=FACE_OUTER_BOUND('',#449,.T.);
#282=FACE_OUTER_BOUND('',#450,.T.);
#283=FACE_OUTER_BOUND('',#451,.T.);
#284=FACE_OUTER_BOUND('',#452,.T.);
#285=FACE_OUTER_BOUND('',#453,.T.);
#286=FACE_OUTER_BOUND('',#454,.T.);
#287=FACE_OUTER_BOUND('',#455,.T.);
#288=FACE_OUTER_BOUND('',#456,.T.);
#289=FACE_OUTER_BOUND('',#457,.T.);
#290=FACE_OUTER_BOUND('',#458,.T.);
#291=FACE_OUTER_BOUND('',#459,.T.);
#292=FACE_OUTER_BOUND('',#460,.T.);
#293=FACE_OUTER_BOUND('',#461,.T.);
#294=FACE_OUTER_BOUND('',#462,.T.);
#295=FACE_OUTER_BOUND('',#463,.T.);
#296=FACE_OUTER_BOUND('',#464,.T.);
#297=FACE_OUTER_BOUND('',#465,.T.);
#298=FACE_OUTER_BOUND('',#466,.T.);
#299=FACE_OUTER_BOUND('',#467,.T.);
#300=FACE_OUTER_BOUND('',#468,.T.);
#301=FACE_OUTER_BOUND('',#469,.T.);
#302=FACE_OUTER_BOUND('',#470,.T.);
#303=FACE_OUTER_BOUND('',#471,.T.);
#304=FACE_OUTER_BOUND('',#472,.T.);
#305=FACE_OUTER_BOUND('',#473,.T.);
#306=FACE_OUTER_BOUND('',#474,.T.);
#307=FACE_OUTER_BOUND('',#475,.T.);
#308=FACE_OUTER_BOUND('',#476,.T.);
#309=FACE_OUTER_BOUND('',#477,.T.);
#310=FACE_OUTER_BOUND('',#478,.T.);
#311=FACE_OUTER_BOUND('',#479,.T.);
#312=FACE_OUTER_BOUND('',#480,.T.);
#313=FACE_OUTER_BOUND('',#481,.T.);
#314=FACE_OUTER_BOUND('',#482,.T.);
#315=FACE_OUTER_BOUND('',#483,.T.);
#316=FACE_OUTER_BOUND('',#484,.T.);
#317=FACE_OUTER_BOUND('',#485,.T.);
#318=FACE_OUTER_BOUND('',#486,.T.);
#319=FACE_OUTER_BOUND('',#487,.T.);
#320=FACE_OUTER_BOUND('',#488,.T.);
#321=FACE_OUTER_BOUND('',#489,.T.);
#322=FACE_OUTER_BOUND('',#490,.T.);
#323=FACE_OUTER_BOUND('',#491,.T.);
#324=FACE_OUTER_BOUND('',#492,.T.);
#325=FACE_OUTER_BOUND('',#493,.T.);
#326=FACE_OUTER_BOUND('',#494,.T.);
#327=FACE_OUTER_BOUND('',#495,.T.);
#328=FACE_OUTER_BOUND('',#496,.T.);
#329=FACE_OUTER_BOUND('',#497,.T.);
#330=FACE_OUTER_BOUND('',#498,.T.);
#331=FACE_OUTER_BOUND('',#499,.T.);
#332=FACE_OUTER_BOUND('',#500,.T.);
#333=FACE_OUTER_BOUND('',#544,.T.);
#334=EDGE_LOOP('',(#2208,#2209,#2210,#2211));
#335=EDGE_LOOP('',(#2212,#2213,#2214,#2215));
#336=EDGE_LOOP('',(#2216,#2217,#2218,#2219));
#337=EDGE_LOOP('',(#2220,#2221,#2222,#2223));
#338=EDGE_LOOP('',(#2224,#2225,#2226,#2227));
#339=EDGE_LOOP('',(#2228,#2229,#2230,#2231));
#340=EDGE_LOOP('',(#2232,#2233,#2234,#2235));
#341=EDGE_LOOP('',(#2236,#2237,#2238,#2239));
#342=EDGE_LOOP('',(#2240,#2241,#2242,#2243));
#343=EDGE_LOOP('',(#2244,#2245,#2246,#2247));
#344=EDGE_LOOP('',(#2248,#2249,#2250,#2251));
#345=EDGE_LOOP('',(#2252,#2253,#2254,#2255));
#346=EDGE_LOOP('',(#2256,#2257,#2258,#2259));
#347=EDGE_LOOP('',(#2260,#2261,#2262,#2263));
#348=EDGE_LOOP('',(#2264,#2265,#2266,#2267));
#349=EDGE_LOOP('',(#2268,#2269,#2270,#2271));
#350=EDGE_LOOP('',(#2272,#2273,#2274,#2275));
#351=EDGE_LOOP('',(#2276,#2277,#2278,#2279));
#352=EDGE_LOOP('',(#2280,#2281,#2282,#2283));
#353=EDGE_LOOP('',(#2284,#2285,#2286,#2287));
#354=EDGE_LOOP('',(#2288,#2289,#2290,#2291));
#355=EDGE_LOOP('',(#2292,#2293,#2294,#2295));
#356=EDGE_LOOP('',(#2296,#2297,#2298,#2299));
#357=EDGE_LOOP('',(#2300,#2301,#2302,#2303));
#358=EDGE_LOOP('',(#2304,#2305,#2306,#2307));
#359=EDGE_LOOP('',(#2308,#2309,#2310,#2311));
#360=EDGE_LOOP('',(#2312,#2313,#2314,#2315));
#361=EDGE_LOOP('',(#2316,#2317,#2318,#2319));
#362=EDGE_LOOP('',(#2320,#2321,#2322,#2323));
#363=EDGE_LOOP('',(#2324,#2325,#2326,#2327));
#364=EDGE_LOOP('',(#2328,#2329,#2330,#2331));
#365=EDGE_LOOP('',(#2332,#2333,#2334,#2335));
#366=EDGE_LOOP('',(#2336,#2337,#2338,#2339));
#367=EDGE_LOOP('',(#2340,#2341,#2342,#2343));
#368=EDGE_LOOP('',(#2344,#2345,#2346,#2347));
#369=EDGE_LOOP('',(#2348,#2349,#2350,#2351));
#370=EDGE_LOOP('',(#2352,#2353,#2354,#2355));
#371=EDGE_LOOP('',(#2356,#2357,#2358,#2359));
#372=EDGE_LOOP('',(#2360,#2361,#2362,#2363));
#373=EDGE_LOOP('',(#2364,#2365,#2366,#2367));
#374=EDGE_LOOP('',(#2368,#2369,#2370,#2371));
#375=EDGE_LOOP('',(#2372,#2373,#2374,#2375));
#376=EDGE_LOOP('',(#2376,#2377,#2378,#2379));
#377=EDGE_LOOP('',(#2380,#2381,#2382,#2383));
#378=EDGE_LOOP('',(#2384,#2385,#2386,#2387));
#379=EDGE_LOOP('',(#2388,#2389,#2390,#2391));
#380=EDGE_LOOP('',(#2392,#2393,#2394,#2395));
#381=EDGE_LOOP('',(#2396,#2397,#2398,#2399));
#382=EDGE_LOOP('',(#2400,#2401,#2402,#2403));
#383=EDGE_LOOP('',(#2404,#2405,#2406,#2407));
#384=EDGE_LOOP('',(#2408,#2409,#2410,#2411));
#385=EDGE_LOOP('',(#2412,#2413,#2414,#2415));
#386=EDGE_LOOP('',(#2416,#2417,#2418,#2419));
#387=EDGE_LOOP('',(#2420,#2421,#2422,#2423));
#388=EDGE_LOOP('',(#2424,#2425,#2426,#2427));
#389=EDGE_LOOP('',(#2428,#2429,#2430,#2431));
#390=EDGE_LOOP('',(#2432,#2433,#2434,#2435));
#391=EDGE_LOOP('',(#2436,#2437,#2438,#2439));
#392=EDGE_LOOP('',(#2440,#2441,#2442,#2443));
#393=EDGE_LOOP('',(#2444,#2445,#2446,#2447));
#394=EDGE_LOOP('',(#2448,#2449,#2450,#2451));
#395=EDGE_LOOP('',(#2452,#2453,#2454,#2455));
#396=EDGE_LOOP('',(#2456,#2457,#2458,#2459));
#397=EDGE_LOOP('',(#2460,#2461,#2462,#2463));
#398=EDGE_LOOP('',(#2464,#2465,#2466,#2467));
#399=EDGE_LOOP('',(#2468,#2469,#2470,#2471));
#400=EDGE_LOOP('',(#2472,#2473,#2474,#2475));
#401=EDGE_LOOP('',(#2476,#2477,#2478,#2479));
#402=EDGE_LOOP('',(#2480,#2481,#2482,#2483));
#403=EDGE_LOOP('',(#2484,#2485,#2486,#2487));
#404=EDGE_LOOP('',(#2488,#2489,#2490,#2491));
#405=EDGE_LOOP('',(#2492,#2493,#2494,#2495));
#406=EDGE_LOOP('',(#2496,#2497,#2498,#2499));
#407=EDGE_LOOP('',(#2500,#2501,#2502,#2503));
#408=EDGE_LOOP('',(#2504,#2505,#2506,#2507));
#409=EDGE_LOOP('',(#2508,#2509,#2510,#2511));
#410=EDGE_LOOP('',(#2512,#2513,#2514,#2515));
#411=EDGE_LOOP('',(#2516,#2517,#2518,#2519));
#412=EDGE_LOOP('',(#2520,#2521,#2522,#2523));
#413=EDGE_LOOP('',(#2524,#2525,#2526,#2527));
#414=EDGE_LOOP('',(#2528,#2529,#2530,#2531));
#415=EDGE_LOOP('',(#2532,#2533,#2534,#2535));
#416=EDGE_LOOP('',(#2536,#2537,#2538,#2539));
#417=EDGE_LOOP('',(#2540,#2541,#2542,#2543));
#418=EDGE_LOOP('',(#2544,#2545,#2546,#2547));
#419=EDGE_LOOP('',(#2548,#2549,#2550,#2551));
#420=EDGE_LOOP('',(#2552,#2553,#2554,#2555));
#421=EDGE_LOOP('',(#2556,#2557,#2558,#2559));
#422=EDGE_LOOP('',(#2560,#2561,#2562,#2563));
#423=EDGE_LOOP('',(#2564,#2565,#2566,#2567));
#424=EDGE_LOOP('',(#2568,#2569,#2570,#2571));
#425=EDGE_LOOP('',(#2572,#2573,#2574,#2575));
#426=EDGE_LOOP('',(#2576,#2577,#2578,#2579));
#427=EDGE_LOOP('',(#2580,#2581,#2582,#2583));
#428=EDGE_LOOP('',(#2584,#2585,#2586,#2587));
#429=EDGE_LOOP('',(#2588,#2589,#2590,#2591));
#430=EDGE_LOOP('',(#2592,#2593,#2594,#2595));
#431=EDGE_LOOP('',(#2596,#2597,#2598,#2599));
#432=EDGE_LOOP('',(#2600,#2601,#2602,#2603));
#433=EDGE_LOOP('',(#2604,#2605,#2606,#2607));
#434=EDGE_LOOP('',(#2608,#2609,#2610,#2611));
#435=EDGE_LOOP('',(#2612,#2613,#2614,#2615));
#436=EDGE_LOOP('',(#2616,#2617,#2618,#2619));
#437=EDGE_LOOP('',(#2620,#2621,#2622,#2623));
#438=EDGE_LOOP('',(#2624,#2625,#2626,#2627));
#439=EDGE_LOOP('',(#2628,#2629,#2630,#2631));
#440=EDGE_LOOP('',(#2632,#2633,#2634,#2635));
#441=EDGE_LOOP('',(#2636,#2637,#2638,#2639));
#442=EDGE_LOOP('',(#2640,#2641,#2642,#2643));
#443=EDGE_LOOP('',(#2644,#2645,#2646,#2647));
#444=EDGE_LOOP('',(#2648,#2649,#2650,#2651));
#445=EDGE_LOOP('',(#2652,#2653,#2654,#2655));
#446=EDGE_LOOP('',(#2656,#2657,#2658,#2659));
#447=EDGE_LOOP('',(#2660,#2661,#2662,#2663));
#448=EDGE_LOOP('',(#2664,#2665,#2666,#2667));
#449=EDGE_LOOP('',(#2668,#2669,#2670,#2671));
#450=EDGE_LOOP('',(#2672,#2673,#2674,#2675));
#451=EDGE_LOOP('',(#2676,#2677,#2678,#2679));
#452=EDGE_LOOP('',(#2680,#2681,#2682,#2683));
#453=EDGE_LOOP('',(#2684,#2685,#2686,#2687));
#454=EDGE_LOOP('',(#2688,#2689,#2690,#2691));
#455=EDGE_LOOP('',(#2692,#2693,#2694,#2695));
#456=EDGE_LOOP('',(#2696,#2697,#2698,#2699));
#457=EDGE_LOOP('',(#2700,#2701,#2702,#2703));
#458=EDGE_LOOP('',(#2704,#2705,#2706,#2707));
#459=EDGE_LOOP('',(#2708,#2709,#2710,#2711));
#460=EDGE_LOOP('',(#2712,#2713,#2714,#2715));
#461=EDGE_LOOP('',(#2716,#2717,#2718,#2719));
#462=EDGE_LOOP('',(#2720,#2721,#2722,#2723));
#463=EDGE_LOOP('',(#2724,#2725,#2726,#2727));
#464=EDGE_LOOP('',(#2728,#2729,#2730,#2731));
#465=EDGE_LOOP('',(#2732,#2733,#2734,#2735));
#466=EDGE_LOOP('',(#2736,#2737,#2738,#2739));
#467=EDGE_LOOP('',(#2740,#2741,#2742,#2743));
#468=EDGE_LOOP('',(#2744,#2745,#2746,#2747));
#469=EDGE_LOOP('',(#2748,#2749,#2750,#2751));
#470=EDGE_LOOP('',(#2752,#2753,#2754,#2755));
#471=EDGE_LOOP('',(#2756,#2757,#2758,#2759));
#472=EDGE_LOOP('',(#2760,#2761,#2762,#2763));
#473=EDGE_LOOP('',(#2764,#2765,#2766,#2767));
#474=EDGE_LOOP('',(#2768,#2769,#2770,#2771));
#475=EDGE_LOOP('',(#2772,#2773,#2774,#2775));
#476=EDGE_LOOP('',(#2776,#2777,#2778,#2779));
#477=EDGE_LOOP('',(#2780,#2781,#2782,#2783));
#478=EDGE_LOOP('',(#2784,#2785,#2786,#2787));
#479=EDGE_LOOP('',(#2788,#2789,#2790,#2791));
#480=EDGE_LOOP('',(#2792,#2793,#2794,#2795));
#481=EDGE_LOOP('',(#2796,#2797,#2798,#2799));
#482=EDGE_LOOP('',(#2800,#2801,#2802,#2803));
#483=EDGE_LOOP('',(#2804,#2805,#2806,#2807));
#484=EDGE_LOOP('',(#2808,#2809,#2810,#2811));
#485=EDGE_LOOP('',(#2812,#2813,#2814,#2815));
#486=EDGE_LOOP('',(#2816,#2817,#2818,#2819));
#487=EDGE_LOOP('',(#2820,#2821,#2822,#2823));
#488=EDGE_LOOP('',(#2824,#2825,#2826,#2827));
#489=EDGE_LOOP('',(#2828,#2829,#2830,#2831));
#490=EDGE_LOOP('',(#2832,#2833,#2834,#2835));
#491=EDGE_LOOP('',(#2836,#2837,#2838,#2839));
#492=EDGE_LOOP('',(#2840,#2841,#2842,#2843));
#493=EDGE_LOOP('',(#2844,#2845,#2846,#2847));
#494=EDGE_LOOP('',(#2848,#2849,#2850,#2851));
#495=EDGE_LOOP('',(#2852,#2853,#2854,#2855));
#496=EDGE_LOOP('',(#2856,#2857,#2858,#2859));
#497=EDGE_LOOP('',(#2860,#2861,#2862,#2863));
#498=EDGE_LOOP('',(#2864,#2865,#2866,#2867));
#499=EDGE_LOOP('',(#2868,#2869,#2870,#2871));
#500=EDGE_LOOP('',(#2872,#2873,#2874,#2875,#2876,#2877,#2878,#2879,#2880,
#2881,#2882,#2883,#2884,#2885,#2886,#2887,#2888,#2889,#2890,#2891,#2892,
#2893,#2894,#2895,#2896,#2897,#2898,#2899,#2900,#2901,#2902,#2903,#2904,
#2905,#2906,#2907,#2908,#2909,#2910,#2911,#2912,#2913,#2914,#2915,#2916,
#2917,#2918,#2919,#2920,#2921,#2922,#2923,#2924,#2925,#2926,#2927,#2928,
#2929,#2930,#2931));
#501=EDGE_LOOP('',(#2932,#2933,#2934,#2935,#2936,#2937,#2938,#2939));
#502=EDGE_LOOP('',(#2940,#2941,#2942,#2943,#2944,#2945,#2946,#2947));
#503=EDGE_LOOP('',(#2948,#2949,#2950,#2951,#2952,#2953,#2954,#2955));
#504=EDGE_LOOP('',(#2956,#2957,#2958,#2959,#2960,#2961,#2962,#2963));
#505=EDGE_LOOP('',(#2964,#2965,#2966,#2967,#2968,#2969,#2970,#2971));
#506=EDGE_LOOP('',(#2972,#2973,#2974,#2975,#2976,#2977,#2978,#2979));
#507=EDGE_LOOP('',(#2980,#2981,#2982,#2983,#2984,#2985,#2986,#2987));
#508=EDGE_LOOP('',(#2988,#2989,#2990,#2991,#2992,#2993,#2994,#2995));
#509=EDGE_LOOP('',(#2996));
#510=EDGE_LOOP('',(#2997));
#511=EDGE_LOOP('',(#2998));
#512=EDGE_LOOP('',(#2999));
#513=EDGE_LOOP('',(#3000));
#514=EDGE_LOOP('',(#3001));
#515=EDGE_LOOP('',(#3002));
#516=EDGE_LOOP('',(#3003));
#517=EDGE_LOOP('',(#3004));
#518=EDGE_LOOP('',(#3005));
#519=EDGE_LOOP('',(#3006));
#520=EDGE_LOOP('',(#3007));
#521=EDGE_LOOP('',(#3008));
#522=EDGE_LOOP('',(#3009));
#523=EDGE_LOOP('',(#3010));
#524=EDGE_LOOP('',(#3011));
#525=EDGE_LOOP('',(#3012));
#526=EDGE_LOOP('',(#3013));
#527=EDGE_LOOP('',(#3014));
#528=EDGE_LOOP('',(#3015));
#529=EDGE_LOOP('',(#3016));
#530=EDGE_LOOP('',(#3017));
#531=EDGE_LOOP('',(#3018));
#532=EDGE_LOOP('',(#3019));
#533=EDGE_LOOP('',(#3020));
#534=EDGE_LOOP('',(#3021));
#535=EDGE_LOOP('',(#3022));
#536=EDGE_LOOP('',(#3023));
#537=EDGE_LOOP('',(#3024));
#538=EDGE_LOOP('',(#3025));
#539=EDGE_LOOP('',(#3026));
#540=EDGE_LOOP('',(#3027));
#541=EDGE_LOOP('',(#3028,#3029,#3030,#3031,#3032,#3033,#3034,#3035));
#542=EDGE_LOOP('',(#3036));
#543=EDGE_LOOP('',(#3037));
#544=EDGE_LOOP('',(#3038,#3039,#3040,#3041,#3042,#3043,#3044,#3045,#3046,
#3047,#3048,#3049,#3050,#3051,#3052,#3053,#3054,#3055,#3056,#3057,#3058,
#3059,#3060,#3061,#3062,#3063,#3064,#3065,#3066,#3067,#3068,#3069,#3070,
#3071,#3072,#3073,#3074,#3075,#3076,#3077,#3078,#3079,#3080,#3081,#3082,
#3083,#3084,#3085,#3086,#3087,#3088,#3089,#3090,#3091,#3092,#3093,#3094,
#3095,#3096,#3097));
#545=EDGE_LOOP('',(#3098,#3099,#3100,#3101,#3102,#3103,#3104,#3105));
#546=EDGE_LOOP('',(#3106,#3107,#3108,#3109,#3110,#3111,#3112,#3113));
#547=EDGE_LOOP('',(#3114,#3115,#3116,#3117,#3118,#3119,#3120,#3121));
#548=EDGE_LOOP('',(#3122,#3123,#3124,#3125,#3126,#3127,#3128,#3129));
#549=EDGE_LOOP('',(#3130,#3131,#3132,#3133,#3134,#3135,#3136,#3137));
#550=EDGE_LOOP('',(#3138,#3139,#3140,#3141,#3142,#3143,#3144,#3145));
#551=EDGE_LOOP('',(#3146,#3147,#3148,#3149,#3150,#3151,#3152,#3153));
#552=EDGE_LOOP('',(#3154,#3155,#3156,#3157,#3158,#3159,#3160,#3161));
#553=EDGE_LOOP('',(#3162));
#554=EDGE_LOOP('',(#3163));
#555=EDGE_LOOP('',(#3164));
#556=EDGE_LOOP('',(#3165));
#557=EDGE_LOOP('',(#3166));
#558=EDGE_LOOP('',(#3167));
#559=EDGE_LOOP('',(#3168));
#560=EDGE_LOOP('',(#3169));
#561=EDGE_LOOP('',(#3170));
#562=EDGE_LOOP('',(#3171));
#563=EDGE_LOOP('',(#3172));
#564=EDGE_LOOP('',(#3173));
#565=EDGE_LOOP('',(#3174));
#566=EDGE_LOOP('',(#3175));
#567=EDGE_LOOP('',(#3176));
#568=EDGE_LOOP('',(#3177));
#569=EDGE_LOOP('',(#3178));
#570=EDGE_LOOP('',(#3179));
#571=EDGE_LOOP('',(#3180));
#572=EDGE_LOOP('',(#3181));
#573=EDGE_LOOP('',(#3182));
#574=EDGE_LOOP('',(#3183));
#575=EDGE_LOOP('',(#3184));
#576=EDGE_LOOP('',(#3185));
#577=EDGE_LOOP('',(#3186));
#578=EDGE_LOOP('',(#3187));
#579=EDGE_LOOP('',(#3188));
#580=EDGE_LOOP('',(#3189));
#581=EDGE_LOOP('',(#3190));
#582=EDGE_LOOP('',(#3191));
#583=EDGE_LOOP('',(#3192));
#584=EDGE_LOOP('',(#3193));
#585=EDGE_LOOP('',(#3194,#3195,#3196,#3197,#3198,#3199,#3200,#3201));
#586=EDGE_LOOP('',(#3202));
#587=EDGE_LOOP('',(#3203));
#588=LINE('',#4910,#880);
#589=LINE('',#4916,#881);
#590=LINE('',#4923,#882);
#591=LINE('',#4926,#883);
#592=LINE('',#4929,#884);
#593=LINE('',#4931,#885);
#594=LINE('',#4932,#886);
#595=LINE('',#4938,#887);
#596=LINE('',#4941,#888);
#597=LINE('',#4943,#889);
#598=LINE('',#4944,#890);
#599=LINE('',#4950,#891);
#600=LINE('',#4953,#892);
#601=LINE('',#4955,#893);
#602=LINE('',#4956,#894);
#603=LINE('',#4962,#895);
#604=LINE('',#4964,#896);
#605=LINE('',#4965,#897);
#606=LINE('',#4970,#898);
#607=LINE('',#4976,#899);
#608=LINE('',#4982,#900);
#609=LINE('',#4988,#901);
#610=LINE('',#4994,#902);
#611=LINE('',#5000,#903);
#612=LINE('',#5006,#904);
#613=LINE('',#5012,#905);
#614=LINE('',#5018,#906);
#615=LINE('',#5024,#907);
#616=LINE('',#5030,#908);
#617=LINE('',#5036,#909);
#618=LINE('',#5042,#910);
#619=LINE('',#5048,#911);
#620=LINE('',#5054,#912);
#621=LINE('',#5060,#913);
#622=LINE('',#5066,#914);
#623=LINE('',#5072,#915);
#624=LINE('',#5078,#916);
#625=LINE('',#5084,#917);
#626=LINE('',#5090,#918);
#627=LINE('',#5096,#919);
#628=LINE('',#5102,#920);
#629=LINE('',#5108,#921);
#630=LINE('',#5114,#922);
#631=LINE('',#5120,#923);
#632=LINE('',#5126,#924);
#633=LINE('',#5132,#925);
#634=LINE('',#5138,#926);
#635=LINE('',#5144,#927);
#636=LINE('',#5150,#928);
#637=LINE('',#5156,#929);
#638=LINE('',#5163,#930);
#639=LINE('',#5166,#931);
#640=LINE('',#5169,#932);
#641=LINE('',#5171,#933);
#642=LINE('',#5172,#934);
#643=LINE('',#5178,#935);
#644=LINE('',#5181,#936);
#645=LINE('',#5183,#937);
#646=LINE('',#5184,#938);
#647=LINE('',#5190,#939);
#648=LINE('',#5193,#940);
#649=LINE('',#5195,#941);
#650=LINE('',#5196,#942);
#651=LINE('',#5202,#943);
#652=LINE('',#5204,#944);
#653=LINE('',#5205,#945);
#654=LINE('',#5211,#946);
#655=LINE('',#5214,#947);
#656=LINE('',#5217,#948);
#657=LINE('',#5219,#949);
#658=LINE('',#5220,#950);
#659=LINE('',#5226,#951);
#660=LINE('',#5229,#952);
#661=LINE('',#5231,#953);
#662=LINE('',#5232,#954);
#663=LINE('',#5238,#955);
#664=LINE('',#5241,#956);
#665=LINE('',#5243,#957);
#666=LINE('',#5244,#958);
#667=LINE('',#5250,#959);
#668=LINE('',#5252,#960);
#669=LINE('',#5253,#961);
#670=LINE('',#5259,#962);
#671=LINE('',#5262,#963);
#672=LINE('',#5265,#964);
#673=LINE('',#5267,#965);
#674=LINE('',#5268,#966);
#675=LINE('',#5274,#967);
#676=LINE('',#5277,#968);
#677=LINE('',#5279,#969);
#678=LINE('',#5280,#970);
#679=LINE('',#5286,#971);
#680=LINE('',#5289,#972);
#681=LINE('',#5291,#973);
#682=LINE('',#5292,#974);
#683=LINE('',#5298,#975);
#684=LINE('',#5300,#976);
#685=LINE('',#5301,#977);
#686=LINE('',#5305,#978);
#687=LINE('',#5307,#979);
#688=LINE('',#5309,#980);
#689=LINE('',#5310,#981);
#690=LINE('',#5316,#982);
#691=LINE('',#5319,#983);
#692=LINE('',#5321,#984);
#693=LINE('',#5322,#985);
#694=LINE('',#5328,#986);
#695=LINE('',#5331,#987);
#696=LINE('',#5333,#988);
#697=LINE('',#5334,#989);
#698=LINE('',#5340,#990);
#699=LINE('',#5343,#991);
#700=LINE('',#5345,#992);
#701=LINE('',#5346,#993);
#702=LINE('',#5355,#994);
#703=LINE('',#5358,#995);
#704=LINE('',#5361,#996);
#705=LINE('',#5363,#997);
#706=LINE('',#5364,#998);
#707=LINE('',#5370,#999);
#708=LINE('',#5373,#1000);
#709=LINE('',#5375,#1001);
#710=LINE('',#5376,#1002);
#711=LINE('',#5382,#1003);
#712=LINE('',#5385,#1004);
#713=LINE('',#5387,#1005);
#714=LINE('',#5388,#1006);
#715=LINE('',#5394,#1007);
#716=LINE('',#5396,#1008);
#717=LINE('',#5397,#1009);
#718=LINE('',#5403,#1010);
#719=LINE('',#5406,#1011);
#720=LINE('',#5409,#1012);
#721=LINE('',#5411,#1013);
#722=LINE('',#5412,#1014);
#723=LINE('',#5418,#1015);
#724=LINE('',#5421,#1016);
#725=LINE('',#5423,#1017);
#726=LINE('',#5424,#1018);
#727=LINE('',#5430,#1019);
#728=LINE('',#5433,#1020);
#729=LINE('',#5435,#1021);
#730=LINE('',#5436,#1022);
#731=LINE('',#5442,#1023);
#732=LINE('',#5444,#1024);
#733=LINE('',#5445,#1025);
#734=LINE('',#5451,#1026);
#735=LINE('',#5454,#1027);
#736=LINE('',#5457,#1028);
#737=LINE('',#5459,#1029);
#738=LINE('',#5460,#1030);
#739=LINE('',#5466,#1031);
#740=LINE('',#5469,#1032);
#741=LINE('',#5471,#1033);
#742=LINE('',#5472,#1034);
#743=LINE('',#5478,#1035);
#744=LINE('',#5481,#1036);
#745=LINE('',#5483,#1037);
#746=LINE('',#5484,#1038);
#747=LINE('',#5490,#1039);
#748=LINE('',#5492,#1040);
#749=LINE('',#5493,#1041);
#750=LINE('',#5499,#1042);
#751=LINE('',#5502,#1043);
#752=LINE('',#5505,#1044);
#753=LINE('',#5507,#1045);
#754=LINE('',#5508,#1046);
#755=LINE('',#5514,#1047);
#756=LINE('',#5517,#1048);
#757=LINE('',#5519,#1049);
#758=LINE('',#5520,#1050);
#759=LINE('',#5526,#1051);
#760=LINE('',#5529,#1052);
#761=LINE('',#5531,#1053);
#762=LINE('',#5532,#1054);
#763=LINE('',#5538,#1055);
#764=LINE('',#5540,#1056);
#765=LINE('',#5541,#1057);
#766=LINE('',#5545,#1058);
#767=LINE('',#5547,#1059);
#768=LINE('',#5549,#1060);
#769=LINE('',#5550,#1061);
#770=LINE('',#5556,#1062);
#771=LINE('',#5562,#1063);
#772=LINE('',#5565,#1064);
#773=LINE('',#5567,#1065);
#774=LINE('',#5568,#1066);
#775=LINE('',#5574,#1067);
#776=LINE('',#5580,#1068);
#777=LINE('',#5586,#1069);
#778=LINE('',#5589,#1070);
#779=LINE('',#5591,#1071);
#780=LINE('',#5592,#1072);
#781=LINE('',#5598,#1073);
#782=LINE('',#5601,#1074);
#783=LINE('',#5603,#1075);
#784=LINE('',#5604,#1076);
#785=LINE('',#5610,#1077);
#786=LINE('',#5616,#1078);
#787=LINE('',#5619,#1079);
#788=LINE('',#5621,#1080);
#789=LINE('',#5622,#1081);
#790=LINE('',#5628,#1082);
#791=LINE('',#5631,#1083);
#792=LINE('',#5633,#1084);
#793=LINE('',#5634,#1085);
#794=LINE('',#5640,#1086);
#795=LINE('',#5643,#1087);
#796=LINE('',#5645,#1088);
#797=LINE('',#5646,#1089);
#798=LINE('',#5652,#1090);
#799=LINE('',#5655,#1091);
#800=LINE('',#5657,#1092);
#801=LINE('',#5658,#1093);
#802=LINE('',#5664,#1094);
#803=LINE('',#5667,#1095);
#804=LINE('',#5669,#1096);
#805=LINE('',#5670,#1097);
#806=LINE('',#5676,#1098);
#807=LINE('',#5679,#1099);
#808=LINE('',#5681,#1100);
#809=LINE('',#5682,#1101);
#810=LINE('',#5688,#1102);
#811=LINE('',#5691,#1103);
#812=LINE('',#5693,#1104);
#813=LINE('',#5694,#1105);
#814=LINE('',#5700,#1106);
#815=LINE('',#5703,#1107);
#816=LINE('',#5705,#1108);
#817=LINE('',#5706,#1109);
#818=LINE('',#5712,#1110);
#819=LINE('',#5715,#1111);
#820=LINE('',#5717,#1112);
#821=LINE('',#5718,#1113);
#822=LINE('',#5724,#1114);
#823=LINE('',#5727,#1115);
#824=LINE('',#5729,#1116);
#825=LINE('',#5730,#1117);
#826=LINE('',#5736,#1118);
#827=LINE('',#5739,#1119);
#828=LINE('',#5741,#1120);
#829=LINE('',#5742,#1121);
#830=LINE('',#5748,#1122);
#831=LINE('',#5751,#1123);
#832=LINE('',#5753,#1124);
#833=LINE('',#5754,#1125);
#834=LINE('',#5760,#1126);
#835=LINE('',#5763,#1127);
#836=LINE('',#5765,#1128);
#837=LINE('',#5766,#1129);
#838=LINE('',#5772,#1130);
#839=LINE('',#5775,#1131);
#840=LINE('',#5777,#1132);
#841=LINE('',#5778,#1133);
#842=LINE('',#5784,#1134);
#843=LINE('',#5787,#1135);
#844=LINE('',#5789,#1136);
#845=LINE('',#5790,#1137);
#846=LINE('',#5796,#1138);
#847=LINE('',#5799,#1139);
#848=LINE('',#5801,#1140);
#849=LINE('',#5802,#1141);
#850=LINE('',#5808,#1142);
#851=LINE('',#5811,#1143);
#852=LINE('',#5813,#1144);
#853=LINE('',#5814,#1145);
#854=LINE('',#5820,#1146);
#855=LINE('',#5823,#1147);
#856=LINE('',#5825,#1148);
#857=LINE('',#5826,#1149);
#858=LINE('',#5832,#1150);
#859=LINE('',#5838,#1151);
#860=LINE('',#5841,#1152);
#861=LINE('',#5843,#1153);
#862=LINE('',#5844,#1154);
#863=LINE('',#5850,#1155);
#864=LINE('',#5853,#1156);
#865=LINE('',#5855,#1157);
#866=LINE('',#5856,#1158);
#867=LINE('',#5862,#1159);
#868=LINE('',#5865,#1160);
#869=LINE('',#5867,#1161);
#870=LINE('',#5868,#1162);
#871=LINE('',#5874,#1163);
#872=LINE('',#5880,#1164);
#873=LINE('',#5883,#1165);
#874=LINE('',#5885,#1166);
#875=LINE('',#5886,#1167);
#876=LINE('',#5892,#1168);
#877=LINE('',#5895,#1169);
#878=LINE('',#5897,#1170);
#879=LINE('',#5898,#1171);
#880=VECTOR('',#3869,3.);
#881=VECTOR('',#3876,3.99999999999999);
#882=VECTOR('',#3883,10.);
#883=VECTOR('',#3886,10.);
#884=VECTOR('',#3889,10.);
#885=VECTOR('',#3890,10.);
#886=VECTOR('',#3891,10.);
#887=VECTOR('',#3898,10.);
#888=VECTOR('',#3901,10.);
#889=VECTOR('',#3902,10.);
#890=VECTOR('',#3903,10.);
#891=VECTOR('',#3910,10.);
#892=VECTOR('',#3913,10.);
#893=VECTOR('',#3914,10.);
#894=VECTOR('',#3915,10.);
#895=VECTOR('',#3922,10.);
#896=VECTOR('',#3925,10.);
#897=VECTOR('',#3926,10.);
#898=VECTOR('',#3931,3.99999999999999);
#899=VECTOR('',#3938,3.99999999999999);
#900=VECTOR('',#3945,2.99999999999997);
#901=VECTOR('',#3952,3.00000000000001);
#902=VECTOR('',#3959,2.99999999999997);
#903=VECTOR('',#3966,1.99999999999999);
#904=VECTOR('',#3973,1.99999999999999);
#905=VECTOR('',#3980,1.99999999999999);
#906=VECTOR('',#3987,3.00000000000008);
#907=VECTOR('',#3994,3.00000000000004);
#908=VECTOR('',#4001,3.00000000000004);
#909=VECTOR('',#4008,3.00000000000004);
#910=VECTOR('',#4015,3.00000000000004);
#911=VECTOR('',#4022,3.00000000000004);
#912=VECTOR('',#4029,3.00000000000011);
#913=VECTOR('',#4036,2.00000000000003);
#914=VECTOR('',#4043,2.00000000000003);
#915=VECTOR('',#4050,2.00000000000003);
#916=VECTOR('',#4057,2.00000000000003);
#917=VECTOR('',#4064,1.99999999999996);
#918=VECTOR('',#4071,1.99999999999999);
#919=VECTOR('',#4078,2.99999999999997);
#920=VECTOR('',#4085,2.99999999999997);
#921=VECTOR('',#4092,2.99999999999997);
#922=VECTOR('',#4099,2.99999999999997);
#923=VECTOR('',#4106,2.99999999999997);
#924=VECTOR('',#4113,2.99999999999997);
#925=VECTOR('',#4120,2.99999999999997);
#926=VECTOR('',#4127,2.99999999999997);
#927=VECTOR('',#4134,2.99999999999997);
#928=VECTOR('',#4141,7.00000000000003);
#929=VECTOR('',#4148,6.99999999999999);
#930=VECTOR('',#4155,10.);
#931=VECTOR('',#4158,10.);
#932=VECTOR('',#4161,10.);
#933=VECTOR('',#4162,10.);
#934=VECTOR('',#4163,10.);
#935=VECTOR('',#4170,10.);
#936=VECTOR('',#4173,10.);
#937=VECTOR('',#4174,10.);
#938=VECTOR('',#4175,10.);
#939=VECTOR('',#4182,10.);
#940=VECTOR('',#4185,10.);
#941=VECTOR('',#4186,10.);
#942=VECTOR('',#4187,10.);
#943=VECTOR('',#4194,10.);
#944=VECTOR('',#4197,10.);
#945=VECTOR('',#4198,10.);
#946=VECTOR('',#4203,10.);
#947=VECTOR('',#4206,10.);
#948=VECTOR('',#4209,10.);
#949=VECTOR('',#4210,10.);
#950=VECTOR('',#4211,10.);
#951=VECTOR('',#4218,10.);
#952=VECTOR('',#4221,10.);
#953=VECTOR('',#4222,10.);
#954=VECTOR('',#4223,10.);
#955=VECTOR('',#4230,10.);
#956=VECTOR('',#4233,10.);
#957=VECTOR('',#4234,10.);
#958=VECTOR('',#4235,10.);
#959=VECTOR('',#4242,10.);
#960=VECTOR('',#4245,10.);
#961=VECTOR('',#4246,10.);
#962=VECTOR('',#4251,10.);
#963=VECTOR('',#4254,10.);
#964=VECTOR('',#4257,10.);
#965=VECTOR('',#4258,10.);
#966=VECTOR('',#4259,10.);
#967=VECTOR('',#4266,10.);
#968=VECTOR('',#4269,10.);
#969=VECTOR('',#4270,10.);
#970=VECTOR('',#4271,10.);
#971=VECTOR('',#4278,10.);
#972=VECTOR('',#4281,10.);
#973=VECTOR('',#4282,10.);
#974=VECTOR('',#4283,10.);
#975=VECTOR('',#4290,10.);
#976=VECTOR('',#4293,10.);
#977=VECTOR('',#4294,10.);
#978=VECTOR('',#4297,10.);
#979=VECTOR('',#4298,10.);
#980=VECTOR('',#4299,10.);
#981=VECTOR('',#4300,10.);
#982=VECTOR('',#4307,10.);
#983=VECTOR('',#4310,10.);
#984=VECTOR('',#4311,10.);
#985=VECTOR('',#4312,10.);
#986=VECTOR('',#4319,10.);
#987=VECTOR('',#4322,10.);
#988=VECTOR('',#4323,10.);
#989=VECTOR('',#4324,10.);
#990=VECTOR('',#4331,10.);
#991=VECTOR('',#4334,10.);
#992=VECTOR('',#4335,10.);
#993=VECTOR('',#4336,10.);
#994=VECTOR('',#4347,10.);
#995=VECTOR('',#4350,10.);
#996=VECTOR('',#4353,10.);
#997=VECTOR('',#4354,10.);
#998=VECTOR('',#4355,10.);
#999=VECTOR('',#4362,10.);
#1000=VECTOR('',#4365,10.);
#1001=VECTOR('',#4366,10.);
#1002=VECTOR('',#4367,10.);
#1003=VECTOR('',#4374,10.);
#1004=VECTOR('',#4377,10.);
#1005=VECTOR('',#4378,10.);
#1006=VECTOR('',#4379,10.);
#1007=VECTOR('',#4386,10.);
#1008=VECTOR('',#4389,10.);
#1009=VECTOR('',#4390,10.);
#1010=VECTOR('',#4395,10.);
#1011=VECTOR('',#4398,10.);
#1012=VECTOR('',#4401,10.);
#1013=VECTOR('',#4402,10.);
#1014=VECTOR('',#4403,10.);
#1015=VECTOR('',#4410,10.);
#1016=VECTOR('',#4413,10.);
#1017=VECTOR('',#4414,10.);
#1018=VECTOR('',#4415,10.);
#1019=VECTOR('',#4422,10.);
#1020=VECTOR('',#4425,10.);
#1021=VECTOR('',#4426,10.);
#1022=VECTOR('',#4427,10.);
#1023=VECTOR('',#4434,10.);
#1024=VECTOR('',#4437,10.);
#1025=VECTOR('',#4438,10.);
#1026=VECTOR('',#4443,10.);
#1027=VECTOR('',#4446,10.);
#1028=VECTOR('',#4449,10.);
#1029=VECTOR('',#4450,10.);
#1030=VECTOR('',#4451,10.);
#1031=VECTOR('',#4458,10.);
#1032=VECTOR('',#4461,10.);
#1033=VECTOR('',#4462,10.);
#1034=VECTOR('',#4463,10.);
#1035=VECTOR('',#4470,10.);
#1036=VECTOR('',#4473,10.);
#1037=VECTOR('',#4474,10.);
#1038=VECTOR('',#4475,10.);
#1039=VECTOR('',#4482,10.);
#1040=VECTOR('',#4485,10.);
#1041=VECTOR('',#4486,10.);
#1042=VECTOR('',#4491,10.);
#1043=VECTOR('',#4494,10.);
#1044=VECTOR('',#4497,10.);
#1045=VECTOR('',#4498,10.);
#1046=VECTOR('',#4499,10.);
#1047=VECTOR('',#4506,10.);
#1048=VECTOR('',#4509,10.);
#1049=VECTOR('',#4510,10.);
#1050=VECTOR('',#4511,10.);
#1051=VECTOR('',#4518,10.);
#1052=VECTOR('',#4521,10.);
#1053=VECTOR('',#4522,10.);
#1054=VECTOR('',#4523,10.);
#1055=VECTOR('',#4530,10.);
#1056=VECTOR('',#4533,10.);
#1057=VECTOR('',#4534,10.);
#1058=VECTOR('',#4537,10.);
#1059=VECTOR('',#4538,10.);
#1060=VECTOR('',#4539,10.);
#1061=VECTOR('',#4540,10.);
#1062=VECTOR('',#4547,10.);
#1063=VECTOR('',#4554,10.);
#1064=VECTOR('',#4557,10.);
#1065=VECTOR('',#4558,10.);
#1066=VECTOR('',#4559,10.);
#1067=VECTOR('',#4566,10.);
#1068=VECTOR('',#4573,10.);
#1069=VECTOR('',#4580,10.);
#1070=VECTOR('',#4583,10.);
#1071=VECTOR('',#4584,10.);
#1072=VECTOR('',#4585,10.);
#1073=VECTOR('',#4592,10.);
#1074=VECTOR('',#4595,10.);
#1075=VECTOR('',#4596,10.);
#1076=VECTOR('',#4597,10.);
#1077=VECTOR('',#4604,10.);
#1078=VECTOR('',#4611,10.);
#1079=VECTOR('',#4614,10.);
#1080=VECTOR('',#4615,10.);
#1081=VECTOR('',#4616,10.);
#1082=VECTOR('',#4623,10.);
#1083=VECTOR('',#4626,10.);
#1084=VECTOR('',#4627,10.);
#1085=VECTOR('',#4628,10.);
#1086=VECTOR('',#4635,10.);
#1087=VECTOR('',#4638,10.);
#1088=VECTOR('',#4639,10.);
#1089=VECTOR('',#4640,10.);
#1090=VECTOR('',#4647,10.);
#1091=VECTOR('',#4650,10.);
#1092=VECTOR('',#4651,10.);
#1093=VECTOR('',#4652,10.);
#1094=VECTOR('',#4659,10.);
#1095=VECTOR('',#4662,10.);
#1096=VECTOR('',#4663,10.);
#1097=VECTOR('',#4664,10.);
#1098=VECTOR('',#4671,10.);
#1099=VECTOR('',#4674,10.);
#1100=VECTOR('',#4675,10.);
#1101=VECTOR('',#4676,10.);
#1102=VECTOR('',#4683,10.);
#1103=VECTOR('',#4686,10.);
#1104=VECTOR('',#4687,10.);
#1105=VECTOR('',#4688,10.);
#1106=VECTOR('',#4695,10.);
#1107=VECTOR('',#4698,10.);
#1108=VECTOR('',#4699,10.);
#1109=VECTOR('',#4700,10.);
#1110=VECTOR('',#4707,10.);
#1111=VECTOR('',#4710,10.);
#1112=VECTOR('',#4711,10.);
#1113=VECTOR('',#4712,10.);
#1114=VECTOR('',#4719,10.);
#1115=VECTOR('',#4722,10.);
#1116=VECTOR('',#4723,10.);
#1117=VECTOR('',#4724,10.);
#1118=VECTOR('',#4731,10.);
#1119=VECTOR('',#4734,10.);
#1120=VECTOR('',#4735,10.);
#1121=VECTOR('',#4736,10.);
#1122=VECTOR('',#4743,10.);
#1123=VECTOR('',#4746,10.);
#1124=VECTOR('',#4747,10.);
#1125=VECTOR('',#4748,10.);
#1126=VECTOR('',#4755,10.);
#1127=VECTOR('',#4758,10.);
#1128=VECTOR('',#4759,10.);
#1129=VECTOR('',#4760,10.);
#1130=VECTOR('',#4767,10.);
#1131=VECTOR('',#4770,10.);
#1132=VECTOR('',#4771,10.);
#1133=VECTOR('',#4772,10.);
#1134=VECTOR('',#4779,10.);
#1135=VECTOR('',#4782,10.);
#1136=VECTOR('',#4783,10.);
#1137=VECTOR('',#4784,10.);
#1138=VECTOR('',#4791,10.);
#1139=VECTOR('',#4794,10.);
#1140=VECTOR('',#4795,10.);
#1141=VECTOR('',#4796,10.);
#1142=VECTOR('',#4803,10.);
#1143=VECTOR('',#4806,10.);
#1144=VECTOR('',#4807,10.);
#1145=VECTOR('',#4808,10.);
#1146=VECTOR('',#4815,10.);
#1147=VECTOR('',#4818,10.);
#1148=VECTOR('',#4819,10.);
#1149=VECTOR('',#4820,10.);
#1150=VECTOR('',#4827,10.);
#1151=VECTOR('',#4834,10.);
#1152=VECTOR('',#4837,10.);
#1153=VECTOR('',#4838,10.);
#1154=VECTOR('',#4839,10.);
#1155=VECTOR('',#4846,10.);
#1156=VECTOR('',#4849,10.);
#1157=VECTOR('',#4850,10.);
#1158=VECTOR('',#4851,10.);
#1159=VECTOR('',#4858,10.);
#1160=VECTOR('',#4861,10.);
#1161=VECTOR('',#4862,10.);
#1162=VECTOR('',#4863,10.);
#1163=VECTOR('',#4870,10.);
#1164=VECTOR('',#4877,10.);
#1165=VECTOR('',#4880,10.);
#1166=VECTOR('',#4881,10.);
#1167=VECTOR('',#4882,10.);
#1168=VECTOR('',#4889,10.);
#1169=VECTOR('',#4892,10.);
#1170=VECTOR('',#4893,10.);
#1171=VECTOR('',#4894,10.);
#1172=CIRCLE('',#3490,2.5175);
#1173=CIRCLE('',#3491,2.5175);
#1174=CIRCLE('',#3493,3.38975);
#1175=CIRCLE('',#3494,3.38975);
#1176=CIRCLE('',#3496,2.);
#1177=CIRCLE('',#3497,2.);
#1178=CIRCLE('',#3500,1.99999999999999);
#1179=CIRCLE('',#3501,1.99999999999999);
#1180=CIRCLE('',#3504,1.99999999999999);
#1181=CIRCLE('',#3505,1.99999999999999);
#1182=CIRCLE('',#3508,1.99999999999999);
#1183=CIRCLE('',#3509,1.99999999999999);
#1184=CIRCLE('',#3512,3.38975);
#1185=CIRCLE('',#3513,3.38975);
#1186=CIRCLE('',#3515,3.38975);
#1187=CIRCLE('',#3516,3.38975);
#1188=CIRCLE('',#3518,2.5175);
#1189=CIRCLE('',#3519,2.5175);
#1190=CIRCLE('',#3521,2.5175);
#1191=CIRCLE('',#3522,2.5175);
#1192=CIRCLE('',#3524,2.5175);
#1193=CIRCLE('',#3525,2.5175);
#1194=CIRCLE('',#3527,1.666);
#1195=CIRCLE('',#3528,1.666);
#1196=CIRCLE('',#3530,1.666);
#1197=CIRCLE('',#3531,1.666);
#1198=CIRCLE('',#3533,1.666);
#1199=CIRCLE('',#3534,1.666);
#1200=CIRCLE('',#3536,2.5175);
#1201=CIRCLE('',#3537,2.5175);
#1202=CIRCLE('',#3539,2.5175);
#1203=CIRCLE('',#3540,2.5175);
#1204=CIRCLE('',#3542,2.5175);
#1205=CIRCLE('',#3543,2.5175);
#1206=CIRCLE('',#3545,2.5175);
#1207=CIRCLE('',#3546,2.5175);
#1208=CIRCLE('',#3548,2.5175);
#1209=CIRCLE('',#3549,2.5175);
#1210=CIRCLE('',#3551,2.5175);
#1211=CIRCLE('',#3552,2.5175);
#1212=CIRCLE('',#3554,2.5175);
#1213=CIRCLE('',#3555,2.5175);
#1214=CIRCLE('',#3557,1.666);
#1215=CIRCLE('',#3558,1.666);
#1216=CIRCLE('',#3560,1.666);
#1217=CIRCLE('',#3561,1.666);
#1218=CIRCLE('',#3563,1.666);
#1219=CIRCLE('',#3564,1.666);
#1220=CIRCLE('',#3566,1.666);
#1221=CIRCLE('',#3567,1.666);
#1222=CIRCLE('',#3569,1.666);
#1223=CIRCLE('',#3570,1.666);
#1224=CIRCLE('',#3572,1.666);
#1225=CIRCLE('',#3573,1.666);
#1226=CIRCLE('',#3575,2.5175);
#1227=CIRCLE('',#3576,2.5175);
#1228=CIRCLE('',#3578,2.5175);
#1229=CIRCLE('',#3579,2.5175);
#1230=CIRCLE('',#3581,2.5175);
#1231=CIRCLE('',#3582,2.5175);
#1232=CIRCLE('',#3584,2.5175);
#1233=CIRCLE('',#3585,2.5175);
#1234=CIRCLE('',#3587,2.5175);
#1235=CIRCLE('',#3588,2.5175);
#1236=CIRCLE('',#3590,2.5175);
#1237=CIRCLE('',#3591,2.5175);
#1238=CIRCLE('',#3593,2.5175);
#1239=CIRCLE('',#3594,2.5175);
#1240=CIRCLE('',#3596,2.5175);
#1241=CIRCLE('',#3597,2.5175);
#1242=CIRCLE('',#3599,2.5175);
#1243=CIRCLE('',#3600,2.5175);
#1244=CIRCLE('',#3602,7.00000000000003);
#1245=CIRCLE('',#3603,7.00000000000003);
#1246=CIRCLE('',#3605,6.99999999999999);
#1247=CIRCLE('',#3606,6.99999999999999);
#1248=CIRCLE('',#3608,1.99999999999999);
#1249=CIRCLE('',#3609,1.99999999999999);
#1250=CIRCLE('',#3612,1.99999999999999);
#1251=CIRCLE('',#3613,1.99999999999999);
#1252=CIRCLE('',#3616,1.99999999999998);
#1253=CIRCLE('',#3617,1.99999999999998);
#1254=CIRCLE('',#3620,2.0000000000001);
#1255=CIRCLE('',#3621,2.0000000000001);
#1256=CIRCLE('',#3624,1.99999999999999);
#1257=CIRCLE('',#3625,1.99999999999999);
#1258=CIRCLE('',#3628,2.00000000000003);
#1259=CIRCLE('',#3629,2.00000000000003);
#1260=CIRCLE('',#3632,1.99999999999998);
#1261=CIRCLE('',#3633,1.99999999999998);
#1262=CIRCLE('',#3636,2.00000000000003);
#1263=CIRCLE('',#3637,2.00000000000003);
#1264=CIRCLE('',#3640,1.99999999999999);
#1265=CIRCLE('',#3641,1.99999999999999);
#1266=CIRCLE('',#3644,2.00000000000003);
#1267=CIRCLE('',#3645,2.00000000000003);
#1268=CIRCLE('',#3648,1.99999999999998);
#1269=CIRCLE('',#3649,1.99999999999998);
#1270=CIRCLE('',#3652,2.00000000000003);
#1271=CIRCLE('',#3653,2.00000000000003);
#1272=CIRCLE('',#3657,1.99999999999996);
#1273=CIRCLE('',#3658,1.99999999999996);
#1274=CIRCLE('',#3661,1.99999999999999);
#1275=CIRCLE('',#3662,1.99999999999999);
#1276=CIRCLE('',#3665,1.99999999999989);
#1277=CIRCLE('',#3666,1.99999999999989);
#1278=CIRCLE('',#3669,2.0000000000001);
#1279=CIRCLE('',#3670,2.0000000000001);
#1280=CIRCLE('',#3672,1.99999999999996);
#1281=CIRCLE('',#3673,1.99999999999996);
#1282=CIRCLE('',#3676,1.99999999999999);
#1283=CIRCLE('',#3677,1.99999999999999);
#1284=CIRCLE('',#3680,1.99999999999989);
#1285=CIRCLE('',#3681,1.99999999999989);
#1286=CIRCLE('',#3684,2.0000000000001);
#1287=CIRCLE('',#3685,2.0000000000001);
#1288=CIRCLE('',#3688,1.99999999999989);
#1289=CIRCLE('',#3689,1.99999999999989);
#1290=CIRCLE('',#3692,2.0000000000001);
#1291=CIRCLE('',#3693,2.0000000000001);
#1292=CIRCLE('',#3696,1.99999999999996);
#1293=CIRCLE('',#3697,1.99999999999996);
#1294=CIRCLE('',#3700,1.99999999999999);
#1295=CIRCLE('',#3701,1.99999999999999);
#1296=CIRCLE('',#3704,2.00000000000017);
#1297=CIRCLE('',#3705,2.00000000000017);
#1298=CIRCLE('',#3708,1.99999999999999);
#1299=CIRCLE('',#3709,1.99999999999999);
#1300=CIRCLE('',#3712,2.00000000000017);
#1301=CIRCLE('',#3713,2.00000000000017);
#1302=CIRCLE('',#3716,2.00000000000008);
#1303=CIRCLE('',#3717,2.00000000000008);
#1304=CIRCLE('',#3720,1.99999999999989);
#1305=CIRCLE('',#3721,1.99999999999989);
#1306=CIRCLE('',#3724,2.0000000000001);
#1307=CIRCLE('',#3725,2.0000000000001);
#1308=CIRCLE('',#3728,1.99999999999996);
#1309=CIRCLE('',#3729,1.99999999999996);
#1310=CIRCLE('',#3732,1.99999999999999);
#1311=CIRCLE('',#3733,1.99999999999999);
#1312=CIRCLE('',#3737,2.0000000000001);
#1313=CIRCLE('',#3738,2.0000000000001);
#1314=CIRCLE('',#3740,12.5000000000001);
#1315=CIRCLE('',#3741,12.5000000000001);
#1316=CIRCLE('',#3744,56.3341738408199);
#1317=CIRCLE('',#3745,56.3341738408199);
#1318=CIRCLE('',#3747,100.000000000001);
#1319=CIRCLE('',#3748,100.000000000001);
#1320=CIRCLE('',#3750,18.0000000000004);
#1321=CIRCLE('',#3751,18.0000000000004);
#1322=CIRCLE('',#3754,15.);
#1323=CIRCLE('',#3755,15.);
#1324=CIRCLE('',#3758,18.);
#1325=CIRCLE('',#3759,18.);
#1326=CIRCLE('',#3761,1.99999999999999);
#1327=CIRCLE('',#3762,1.99999999999999);
#1328=CIRCLE('',#3765,2.00000000000003);
#1329=CIRCLE('',#3766,2.00000000000003);
#1330=CIRCLE('',#3769,2.0000000000001);
#1331=CIRCLE('',#3770,2.0000000000001);
#1332=CIRCLE('',#3773,2.00000000000003);
#1333=CIRCLE('',#3774,2.00000000000003);
#1334=CIRCLE('',#3777,2.00000000000003);
#1335=CIRCLE('',#3778,2.00000000000003);
#1336=CIRCLE('',#3781,2.00000000000003);
#1337=CIRCLE('',#3782,2.00000000000003);
#1338=CIRCLE('',#3785,2.00000000000003);
#1339=CIRCLE('',#3786,2.00000000000003);
#1340=CIRCLE('',#3789,2.00000000000003);
#1341=CIRCLE('',#3790,2.00000000000003);
#1342=CIRCLE('',#3793,15.0000000000001);
#1343=CIRCLE('',#3794,15.0000000000001);
#1344=CIRCLE('',#3797,1.99999999999996);
#1345=CIRCLE('',#3798,1.99999999999996);
#1346=CIRCLE('',#3801,2.00000000000025);
#1347=CIRCLE('',#3802,2.00000000000025);
#1348=CIRCLE('',#3805,2.00000000000011);
#1349=CIRCLE('',#3806,2.00000000000011);
#1350=CIRCLE('',#3809,2.00000000000003);
#1351=CIRCLE('',#3810,2.00000000000003);
#1352=CIRCLE('',#3813,1.99999999999974);
#1353=CIRCLE('',#3814,1.99999999999974);
#1354=CIRCLE('',#3817,2.00000000000003);
#1355=CIRCLE('',#3818,2.00000000000003);
#1356=CIRCLE('',#3821,1.99999999999999);
#1357=CIRCLE('',#3822,1.99999999999999);
#1358=CIRCLE('',#3825,1.99999999999999);
#1359=CIRCLE('',#3826,1.99999999999999);
#1360=CIRCLE('',#3829,50.0000000000001);
#1361=CIRCLE('',#3830,50.0000000000001);
#1362=CIRCLE('',#3833,100.);
#1363=CIRCLE('',#3834,100.);
#1364=CIRCLE('',#3836,100.000000000001);
#1365=CIRCLE('',#3837,100.000000000001);
#1366=CIRCLE('',#3840,1.99999999999999);
#1367=CIRCLE('',#3841,1.99999999999999);
#1368=CIRCLE('',#3844,30.0000000000006);
#1369=CIRCLE('',#3845,30.0000000000006);
#1370=CIRCLE('',#3848,70.0000000000005);
#1371=CIRCLE('',#3849,70.0000000000005);
#1372=CIRCLE('',#3851,71.0920157219637);
#1373=CIRCLE('',#3852,71.0920157219637);
#1374=CIRCLE('',#3855,15.);
#1375=CIRCLE('',#3856,15.);
#1376=CIRCLE('',#3859,12.);
#1377=CIRCLE('',#3860,12.);
#1378=VERTEX_POINT('',#4907);
#1379=VERTEX_POINT('',#4909);
#1380=VERTEX_POINT('',#4913);
#1381=VERTEX_POINT('',#4915);
#1382=VERTEX_POINT('',#4919);
#1383=VERTEX_POINT('',#4920);
#1384=VERTEX_POINT('',#4922);
#1385=VERTEX_POINT('',#4924);
#1386=VERTEX_POINT('',#4928);
#1387=VERTEX_POINT('',#4930);
#1388=VERTEX_POINT('',#4934);
#1389=VERTEX_POINT('',#4936);
#1390=VERTEX_POINT('',#4940);
#1391=VERTEX_POINT('',#4942);
#1392=VERTEX_POINT('',#4946);
#1393=VERTEX_POINT('',#4948);
#1394=VERTEX_POINT('',#4952);
#1395=VERTEX_POINT('',#4954);
#1396=VERTEX_POINT('',#4958);
#1397=VERTEX_POINT('',#4960);
#1398=VERTEX_POINT('',#4967);
#1399=VERTEX_POINT('',#4969);
#1400=VERTEX_POINT('',#4973);
#1401=VERTEX_POINT('',#4975);
#1402=VERTEX_POINT('',#4979);
#1403=VERTEX_POINT('',#4981);
#1404=VERTEX_POINT('',#4985);
#1405=VERTEX_POINT('',#4987);
#1406=VERTEX_POINT('',#4991);
#1407=VERTEX_POINT('',#4993);
#1408=VERTEX_POINT('',#4997);
#1409=VERTEX_POINT('',#4999);
#1410=VERTEX_POINT('',#5003);
#1411=VERTEX_POINT('',#5005);
#1412=VERTEX_POINT('',#5009);
#1413=VERTEX_POINT('',#5011);
#1414=VERTEX_POINT('',#5015);
#1415=VERTEX_POINT('',#5017);
#1416=VERTEX_POINT('',#5021);
#1417=VERTEX_POINT('',#5023);
#1418=VERTEX_POINT('',#5027);
#1419=VERTEX_POINT('',#5029);
#1420=VERTEX_POINT('',#5033);
#1421=VERTEX_POINT('',#5035);
#1422=VERTEX_POINT('',#5039);
#1423=VERTEX_POINT('',#5041);
#1424=VERTEX_POINT('',#5045);
#1425=VERTEX_POINT('',#5047);
#1426=VERTEX_POINT('',#5051);
#1427=VERTEX_POINT('',#5053);
#1428=VERTEX_POINT('',#5057);
#1429=VERTEX_POINT('',#5059);
#1430=VERTEX_POINT('',#5063);
#1431=VERTEX_POINT('',#5065);
#1432=VERTEX_POINT('',#5069);
#1433=VERTEX_POINT('',#5071);
#1434=VERTEX_POINT('',#5075);
#1435=VERTEX_POINT('',#5077);
#1436=VERTEX_POINT('',#5081);
#1437=VERTEX_POINT('',#5083);
#1438=VERTEX_POINT('',#5087);
#1439=VERTEX_POINT('',#5089);
#1440=VERTEX_POINT('',#5093);
#1441=VERTEX_POINT('',#5095);
#1442=VERTEX_POINT('',#5099);
#1443=VERTEX_POINT('',#5101);
#1444=VERTEX_POINT('',#5105);
#1445=VERTEX_POINT('',#5107);
#1446=VERTEX_POINT('',#5111);
#1447=VERTEX_POINT('',#5113);
#1448=VERTEX_POINT('',#5117);
#1449=VERTEX_POINT('',#5119);
#1450=VERTEX_POINT('',#5123);
#1451=VERTEX_POINT('',#5125);
#1452=VERTEX_POINT('',#5129);
#1453=VERTEX_POINT('',#5131);
#1454=VERTEX_POINT('',#5135);
#1455=VERTEX_POINT('',#5137);
#1456=VERTEX_POINT('',#5141);
#1457=VERTEX_POINT('',#5143);
#1458=VERTEX_POINT('',#5147);
#1459=VERTEX_POINT('',#5149);
#1460=VERTEX_POINT('',#5153);
#1461=VERTEX_POINT('',#5155);
#1462=VERTEX_POINT('',#5159);
#1463=VERTEX_POINT('',#5160);
#1464=VERTEX_POINT('',#5162);
#1465=VERTEX_POINT('',#5164);
#1466=VERTEX_POINT('',#5168);
#1467=VERTEX_POINT('',#5170);
#1468=VERTEX_POINT('',#5174);
#1469=VERTEX_POINT('',#5176);
#1470=VERTEX_POINT('',#5180);
#1471=VERTEX_POINT('',#5182);
#1472=VERTEX_POINT('',#5186);
#1473=VERTEX_POINT('',#5188);
#1474=VERTEX_POINT('',#5192);
#1475=VERTEX_POINT('',#5194);
#1476=VERTEX_POINT('',#5198);
#1477=VERTEX_POINT('',#5200);
#1478=VERTEX_POINT('',#5207);
#1479=VERTEX_POINT('',#5208);
#1480=VERTEX_POINT('',#5210);
#1481=VERTEX_POINT('',#5212);
#1482=VERTEX_POINT('',#5216);
#1483=VERTEX_POINT('',#5218);
#1484=VERTEX_POINT('',#5222);
#1485=VERTEX_POINT('',#5224);
#1486=VERTEX_POINT('',#5228);
#1487=VERTEX_POINT('',#5230);
#1488=VERTEX_POINT('',#5234);
#1489=VERTEX_POINT('',#5236);
#1490=VERTEX_POINT('',#5240);
#1491=VERTEX_POINT('',#5242);
#1492=VERTEX_POINT('',#5246);
#1493=VERTEX_POINT('',#5248);
#1494=VERTEX_POINT('',#5255);
#1495=VERTEX_POINT('',#5256);
#1496=VERTEX_POINT('',#5258);
#1497=VERTEX_POINT('',#5260);
#1498=VERTEX_POINT('',#5264);
#1499=VERTEX_POINT('',#5266);
#1500=VERTEX_POINT('',#5270);
#1501=VERTEX_POINT('',#5272);
#1502=VERTEX_POINT('',#5276);
#1503=VERTEX_POINT('',#5278);
#1504=VERTEX_POINT('',#5282);
#1505=VERTEX_POINT('',#5284);
#1506=VERTEX_POINT('',#5288);
#1507=VERTEX_POINT('',#5290);
#1508=VERTEX_POINT('',#5294);
#1509=VERTEX_POINT('',#5296);
#1510=VERTEX_POINT('',#5303);
#1511=VERTEX_POINT('',#5304);
#1512=VERTEX_POINT('',#5306);
#1513=VERTEX_POINT('',#5308);
#1514=VERTEX_POINT('',#5312);
#1515=VERTEX_POINT('',#5314);
#1516=VERTEX_POINT('',#5318);
#1517=VERTEX_POINT('',#5320);
#1518=VERTEX_POINT('',#5324);
#1519=VERTEX_POINT('',#5326);
#1520=VERTEX_POINT('',#5330);
#1521=VERTEX_POINT('',#5332);
#1522=VERTEX_POINT('',#5336);
#1523=VERTEX_POINT('',#5338);
#1524=VERTEX_POINT('',#5342);
#1525=VERTEX_POINT('',#5344);
#1526=VERTEX_POINT('',#5351);
#1527=VERTEX_POINT('',#5352);
#1528=VERTEX_POINT('',#5354);
#1529=VERTEX_POINT('',#5356);
#1530=VERTEX_POINT('',#5360);
#1531=VERTEX_POINT('',#5362);
#1532=VERTEX_POINT('',#5366);
#1533=VERTEX_POINT('',#5368);
#1534=VERTEX_POINT('',#5372);
#1535=VERTEX_POINT('',#5374);
#1536=VERTEX_POINT('',#5378);
#1537=VERTEX_POINT('',#5380);
#1538=VERTEX_POINT('',#5384);
#1539=VERTEX_POINT('',#5386);
#1540=VERTEX_POINT('',#5390);
#1541=VERTEX_POINT('',#5392);
#1542=VERTEX_POINT('',#5399);
#1543=VERTEX_POINT('',#5400);
#1544=VERTEX_POINT('',#5402);
#1545=VERTEX_POINT('',#5404);
#1546=VERTEX_POINT('',#5408);
#1547=VERTEX_POINT('',#5410);
#1548=VERTEX_POINT('',#5414);
#1549=VERTEX_POINT('',#5416);
#1550=VERTEX_POINT('',#5420);
#1551=VERTEX_POINT('',#5422);
#1552=VERTEX_POINT('',#5426);
#1553=VERTEX_POINT('',#5428);
#1554=VERTEX_POINT('',#5432);
#1555=VERTEX_POINT('',#5434);
#1556=VERTEX_POINT('',#5438);
#1557=VERTEX_POINT('',#5440);
#1558=VERTEX_POINT('',#5447);
#1559=VERTEX_POINT('',#5448);
#1560=VERTEX_POINT('',#5450);
#1561=VERTEX_POINT('',#5452);
#1562=VERTEX_POINT('',#5456);
#1563=VERTEX_POINT('',#5458);
#1564=VERTEX_POINT('',#5462);
#1565=VERTEX_POINT('',#5464);
#1566=VERTEX_POINT('',#5468);
#1567=VERTEX_POINT('',#5470);
#1568=VERTEX_POINT('',#5474);
#1569=VERTEX_POINT('',#5476);
#1570=VERTEX_POINT('',#5480);
#1571=VERTEX_POINT('',#5482);
#1572=VERTEX_POINT('',#5486);
#1573=VERTEX_POINT('',#5488);
#1574=VERTEX_POINT('',#5495);
#1575=VERTEX_POINT('',#5496);
#1576=VERTEX_POINT('',#5498);
#1577=VERTEX_POINT('',#5500);
#1578=VERTEX_POINT('',#5504);
#1579=VERTEX_POINT('',#5506);
#1580=VERTEX_POINT('',#5510);
#1581=VERTEX_POINT('',#5512);
#1582=VERTEX_POINT('',#5516);
#1583=VERTEX_POINT('',#5518);
#1584=VERTEX_POINT('',#5522);
#1585=VERTEX_POINT('',#5524);
#1586=VERTEX_POINT('',#5528);
#1587=VERTEX_POINT('',#5530);
#1588=VERTEX_POINT('',#5534);
#1589=VERTEX_POINT('',#5536);
#1590=VERTEX_POINT('',#5543);
#1591=VERTEX_POINT('',#5544);
#1592=VERTEX_POINT('',#5546);
#1593=VERTEX_POINT('',#5548);
#1594=VERTEX_POINT('',#5552);
#1595=VERTEX_POINT('',#5554);
#1596=VERTEX_POINT('',#5558);
#1597=VERTEX_POINT('',#5560);
#1598=VERTEX_POINT('',#5564);
#1599=VERTEX_POINT('',#5566);
#1600=VERTEX_POINT('',#5570);
#1601=VERTEX_POINT('',#5572);
#1602=VERTEX_POINT('',#5576);
#1603=VERTEX_POINT('',#5578);
#1604=VERTEX_POINT('',#5582);
#1605=VERTEX_POINT('',#5584);
#1606=VERTEX_POINT('',#5588);
#1607=VERTEX_POINT('',#5590);
#1608=VERTEX_POINT('',#5594);
#1609=VERTEX_POINT('',#5596);
#1610=VERTEX_POINT('',#5600);
#1611=VERTEX_POINT('',#5602);
#1612=VERTEX_POINT('',#5606);
#1613=VERTEX_POINT('',#5608);
#1614=VERTEX_POINT('',#5612);
#1615=VERTEX_POINT('',#5614);
#1616=VERTEX_POINT('',#5618);
#1617=VERTEX_POINT('',#5620);
#1618=VERTEX_POINT('',#5624);
#1619=VERTEX_POINT('',#5626);
#1620=VERTEX_POINT('',#5630);
#1621=VERTEX_POINT('',#5632);
#1622=VERTEX_POINT('',#5636);
#1623=VERTEX_POINT('',#5638);
#1624=VERTEX_POINT('',#5642);
#1625=VERTEX_POINT('',#5644);
#1626=VERTEX_POINT('',#5648);
#1627=VERTEX_POINT('',#5650);
#1628=VERTEX_POINT('',#5654);
#1629=VERTEX_POINT('',#5656);
#1630=VERTEX_POINT('',#5660);
#1631=VERTEX_POINT('',#5662);
#1632=VERTEX_POINT('',#5666);
#1633=VERTEX_POINT('',#5668);
#1634=VERTEX_POINT('',#5672);
#1635=VERTEX_POINT('',#5674);
#1636=VERTEX_POINT('',#5678);
#1637=VERTEX_POINT('',#5680);
#1638=VERTEX_POINT('',#5684);
#1639=VERTEX_POINT('',#5686);
#1640=VERTEX_POINT('',#5690);
#1641=VERTEX_POINT('',#5692);
#1642=VERTEX_POINT('',#5696);
#1643=VERTEX_POINT('',#5698);
#1644=VERTEX_POINT('',#5702);
#1645=VERTEX_POINT('',#5704);
#1646=VERTEX_POINT('',#5708);
#1647=VERTEX_POINT('',#5710);
#1648=VERTEX_POINT('',#5714);
#1649=VERTEX_POINT('',#5716);
#1650=VERTEX_POINT('',#5720);
#1651=VERTEX_POINT('',#5722);
#1652=VERTEX_POINT('',#5726);
#1653=VERTEX_POINT('',#5728);
#1654=VERTEX_POINT('',#5732);
#1655=VERTEX_POINT('',#5734);
#1656=VERTEX_POINT('',#5738);
#1657=VERTEX_POINT('',#5740);
#1658=VERTEX_POINT('',#5744);
#1659=VERTEX_POINT('',#5746);
#1660=VERTEX_POINT('',#5750);
#1661=VERTEX_POINT('',#5752);
#1662=VERTEX_POINT('',#5756);
#1663=VERTEX_POINT('',#5758);
#1664=VERTEX_POINT('',#5762);
#1665=VERTEX_POINT('',#5764);
#1666=VERTEX_POINT('',#5768);
#1667=VERTEX_POINT('',#5770);
#1668=VERTEX_POINT('',#5774);
#1669=VERTEX_POINT('',#5776);
#1670=VERTEX_POINT('',#5780);
#1671=VERTEX_POINT('',#5782);
#1672=VERTEX_POINT('',#5786);
#1673=VERTEX_POINT('',#5788);
#1674=VERTEX_POINT('',#5792);
#1675=VERTEX_POINT('',#5794);
#1676=VERTEX_POINT('',#5798);
#1677=VERTEX_POINT('',#5800);
#1678=VERTEX_POINT('',#5804);
#1679=VERTEX_POINT('',#5806);
#1680=VERTEX_POINT('',#5810);
#1681=VERTEX_POINT('',#5812);
#1682=VERTEX_POINT('',#5816);
#1683=VERTEX_POINT('',#5818);
#1684=VERTEX_POINT('',#5822);
#1685=VERTEX_POINT('',#5824);
#1686=VERTEX_POINT('',#5828);
#1687=VERTEX_POINT('',#5830);
#1688=VERTEX_POINT('',#5834);
#1689=VERTEX_POINT('',#5836);
#1690=VERTEX_POINT('',#5840);
#1691=VERTEX_POINT('',#5842);
#1692=VERTEX_POINT('',#5846);
#1693=VERTEX_POINT('',#5848);
#1694=VERTEX_POINT('',#5852);
#1695=VERTEX_POINT('',#5854);
#1696=VERTEX_POINT('',#5858);
#1697=VERTEX_POINT('',#5860);
#1698=VERTEX_POINT('',#5864);
#1699=VERTEX_POINT('',#5866);
#1700=VERTEX_POINT('',#5870);
#1701=VERTEX_POINT('',#5872);
#1702=VERTEX_POINT('',#5876);
#1703=VERTEX_POINT('',#5878);
#1704=VERTEX_POINT('',#5882);
#1705=VERTEX_POINT('',#5884);
#1706=VERTEX_POINT('',#5888);
#1707=VERTEX_POINT('',#5890);
#1708=VERTEX_POINT('',#5894);
#1709=VERTEX_POINT('',#5896);
#1710=EDGE_CURVE('',#1378,#1378,#1172,.F.);
#1711=EDGE_CURVE('',#1378,#1379,#588,.T.);
#1712=EDGE_CURVE('',#1379,#1379,#1173,.T.);
#1713=EDGE_CURVE('',#1380,#1380,#1174,.F.);
#1714=EDGE_CURVE('',#1380,#1381,#589,.T.);
#1715=EDGE_CURVE('',#1381,#1381,#1175,.T.);
#1716=EDGE_CURVE('',#1382,#1383,#1176,.T.);
#1717=EDGE_CURVE('',#1382,#1384,#590,.T.);
#1718=EDGE_CURVE('',#1385,#1384,#1177,.T.);
#1719=EDGE_CURVE('',#1383,#1385,#591,.T.);
#1720=EDGE_CURVE('',#1386,#1383,#592,.T.);
#1721=EDGE_CURVE('',#1387,#1385,#593,.T.);
#1722=EDGE_CURVE('',#1386,#1387,#594,.T.);
#1723=EDGE_CURVE('',#1386,#1388,#1178,.T.);
#1724=EDGE_CURVE('',#1389,#1387,#1179,.T.);
#1725=EDGE_CURVE('',#1388,#1389,#595,.T.);
#1726=EDGE_CURVE('',#1390,#1388,#596,.T.);
#1727=EDGE_CURVE('',#1391,#1389,#597,.T.);
#1728=EDGE_CURVE('',#1390,#1391,#598,.T.);
#1729=EDGE_CURVE('',#1390,#1392,#1180,.T.);
#1730=EDGE_CURVE('',#1393,#1391,#1181,.T.);
#1731=EDGE_CURVE('',#1392,#1393,#599,.T.);
#1732=EDGE_CURVE('',#1394,#1392,#600,.T.);
#1733=EDGE_CURVE('',#1395,#1393,#601,.T.);
#1734=EDGE_CURVE('',#1394,#1395,#602,.T.);
#1735=EDGE_CURVE('',#1394,#1396,#1182,.T.);
#1736=EDGE_CURVE('',#1397,#1395,#1183,.T.);
#1737=EDGE_CURVE('',#1396,#1397,#603,.T.);
#1738=EDGE_CURVE('',#1382,#1396,#604,.T.);
#1739=EDGE_CURVE('',#1384,#1397,#605,.T.);
#1740=EDGE_CURVE('',#1398,#1398,#1184,.F.);
#1741=EDGE_CURVE('',#1398,#1399,#606,.T.);
#1742=EDGE_CURVE('',#1399,#1399,#1185,.T.);
#1743=EDGE_CURVE('',#1400,#1400,#1186,.F.);
#1744=EDGE_CURVE('',#1400,#1401,#607,.T.);
#1745=EDGE_CURVE('',#1401,#1401,#1187,.T.);
#1746=EDGE_CURVE('',#1402,#1402,#1188,.F.);
#1747=EDGE_CURVE('',#1402,#1403,#608,.T.);
#1748=EDGE_CURVE('',#1403,#1403,#1189,.T.);
#1749=EDGE_CURVE('',#1404,#1404,#1190,.F.);
#1750=EDGE_CURVE('',#1404,#1405,#609,.T.);
#1751=EDGE_CURVE('',#1405,#1405,#1191,.T.);
#1752=EDGE_CURVE('',#1406,#1406,#1192,.F.);
#1753=EDGE_CURVE('',#1406,#1407,#610,.T.);
#1754=EDGE_CURVE('',#1407,#1407,#1193,.T.);
#1755=EDGE_CURVE('',#1408,#1408,#1194,.F.);
#1756=EDGE_CURVE('',#1408,#1409,#611,.T.);
#1757=EDGE_CURVE('',#1409,#1409,#1195,.T.);
#1758=EDGE_CURVE('',#1410,#1410,#1196,.F.);
#1759=EDGE_CURVE('',#1410,#1411,#612,.T.);
#1760=EDGE_CURVE('',#1411,#1411,#1197,.T.);
#1761=EDGE_CURVE('',#1412,#1412,#1198,.F.);
#1762=EDGE_CURVE('',#1412,#1413,#613,.T.);
#1763=EDGE_CURVE('',#1413,#1413,#1199,.T.);
#1764=EDGE_CURVE('',#1414,#1414,#1200,.F.);
#1765=EDGE_CURVE('',#1414,#1415,#614,.T.);
#1766=EDGE_CURVE('',#1415,#1415,#1201,.T.);
#1767=EDGE_CURVE('',#1416,#1416,#1202,.F.);
#1768=EDGE_CURVE('',#1416,#1417,#615,.T.);
#1769=EDGE_CURVE('',#1417,#1417,#1203,.T.);
#1770=EDGE_CURVE('',#1418,#1418,#1204,.F.);
#1771=EDGE_CURVE('',#1418,#1419,#616,.T.);
#1772=EDGE_CURVE('',#1419,#1419,#1205,.T.);
#1773=EDGE_CURVE('',#1420,#1420,#1206,.F.);
#1774=EDGE_CURVE('',#1420,#1421,#617,.T.);
#1775=EDGE_CURVE('',#1421,#1421,#1207,.T.);
#1776=EDGE_CURVE('',#1422,#1422,#1208,.F.);
#1777=EDGE_CURVE('',#1422,#1423,#618,.T.);
#1778=EDGE_CURVE('',#1423,#1423,#1209,.T.);
#1779=EDGE_CURVE('',#1424,#1424,#1210,.F.);
#1780=EDGE_CURVE('',#1424,#1425,#619,.T.);
#1781=EDGE_CURVE('',#1425,#1425,#1211,.T.);
#1782=EDGE_CURVE('',#1426,#1426,#1212,.F.);
#1783=EDGE_CURVE('',#1426,#1427,#620,.T.);
#1784=EDGE_CURVE('',#1427,#1427,#1213,.T.);
#1785=EDGE_CURVE('',#1428,#1428,#1214,.F.);
#1786=EDGE_CURVE('',#1428,#1429,#621,.T.);
#1787=EDGE_CURVE('',#1429,#1429,#1215,.T.);
#1788=EDGE_CURVE('',#1430,#1430,#1216,.F.);
#1789=EDGE_CURVE('',#1430,#1431,#622,.T.);
#1790=EDGE_CURVE('',#1431,#1431,#1217,.T.);
#1791=EDGE_CURVE('',#1432,#1432,#1218,.F.);
#1792=EDGE_CURVE('',#1432,#1433,#623,.T.);
#1793=EDGE_CURVE('',#1433,#1433,#1219,.T.);
#1794=EDGE_CURVE('',#1434,#1434,#1220,.F.);
#1795=EDGE_CURVE('',#1434,#1435,#624,.T.);
#1796=EDGE_CURVE('',#1435,#1435,#1221,.T.);
#1797=EDGE_CURVE('',#1436,#1436,#1222,.F.);
#1798=EDGE_CURVE('',#1436,#1437,#625,.T.);
#1799=EDGE_CURVE('',#1437,#1437,#1223,.T.);
#1800=EDGE_CURVE('',#1438,#1438,#1224,.F.);
#1801=EDGE_CURVE('',#1438,#1439,#626,.T.);
#1802=EDGE_CURVE('',#1439,#1439,#1225,.T.);
#1803=EDGE_CURVE('',#1440,#1440,#1226,.F.);
#1804=EDGE_CURVE('',#1440,#1441,#627,.T.);
#1805=EDGE_CURVE('',#1441,#1441,#1227,.T.);
#1806=EDGE_CURVE('',#1442,#1442,#1228,.F.);
#1807=EDGE_CURVE('',#1442,#1443,#628,.T.);
#1808=EDGE_CURVE('',#1443,#1443,#1229,.T.);
#1809=EDGE_CURVE('',#1444,#1444,#1230,.F.);
#1810=EDGE_CURVE('',#1444,#1445,#629,.T.);
#1811=EDGE_CURVE('',#1445,#1445,#1231,.T.);
#1812=EDGE_CURVE('',#1446,#1446,#1232,.F.);
#1813=EDGE_CURVE('',#1446,#1447,#630,.T.);
#1814=EDGE_CURVE('',#1447,#1447,#1233,.T.);
#1815=EDGE_CURVE('',#1448,#1448,#1234,.F.);
#1816=EDGE_CURVE('',#1448,#1449,#631,.T.);
#1817=EDGE_CURVE('',#1449,#1449,#1235,.T.);
#1818=EDGE_CURVE('',#1450,#1450,#1236,.F.);
#1819=EDGE_CURVE('',#1450,#1451,#632,.T.);
#1820=EDGE_CURVE('',#1451,#1451,#1237,.T.);
#1821=EDGE_CURVE('',#1452,#1452,#1238,.F.);
#1822=EDGE_CURVE('',#1452,#1453,#633,.T.);
#1823=EDGE_CURVE('',#1453,#1453,#1239,.T.);
#1824=EDGE_CURVE('',#1454,#1454,#1240,.F.);
#1825=EDGE_CURVE('',#1454,#1455,#634,.T.);
#1826=EDGE_CURVE('',#1455,#1455,#1241,.T.);
#1827=EDGE_CURVE('',#1456,#1456,#1242,.F.);
#1828=EDGE_CURVE('',#1456,#1457,#635,.T.);
#1829=EDGE_CURVE('',#1457,#1457,#1243,.T.);
#1830=EDGE_CURVE('',#1458,#1458,#1244,.T.);
#1831=EDGE_CURVE('',#1458,#1459,#636,.T.);
#1832=EDGE_CURVE('',#1459,#1459,#1245,.T.);
#1833=EDGE_CURVE('',#1460,#1460,#1246,.T.);
#1834=EDGE_CURVE('',#1460,#1461,#637,.T.);
#1835=EDGE_CURVE('',#1461,#1461,#1247,.T.);
#1836=EDGE_CURVE('',#1462,#1463,#1248,.T.);
#1837=EDGE_CURVE('',#1462,#1464,#638,.T.);
#1838=EDGE_CURVE('',#1465,#1464,#1249,.T.);
#1839=EDGE_CURVE('',#1463,#1465,#639,.T.);
#1840=EDGE_CURVE('',#1466,#1463,#640,.T.);
#1841=EDGE_CURVE('',#1467,#1465,#641,.T.);
#1842=EDGE_CURVE('',#1466,#1467,#642,.T.);
#1843=EDGE_CURVE('',#1466,#1468,#1250,.T.);
#1844=EDGE_CURVE('',#1469,#1467,#1251,.T.);
#1845=EDGE_CURVE('',#1468,#1469,#643,.T.);
#1846=EDGE_CURVE('',#1470,#1468,#644,.T.);
#1847=EDGE_CURVE('',#1471,#1469,#645,.T.);
#1848=EDGE_CURVE('',#1470,#1471,#646,.T.);
#1849=EDGE_CURVE('',#1470,#1472,#1252,.T.);
#1850=EDGE_CURVE('',#1473,#1471,#1253,.T.);
#1851=EDGE_CURVE('',#1472,#1473,#647,.T.);
#1852=EDGE_CURVE('',#1474,#1472,#648,.T.);
#1853=EDGE_CURVE('',#1475,#1473,#649,.T.);
#1854=EDGE_CURVE('',#1474,#1475,#650,.T.);
#1855=EDGE_CURVE('',#1474,#1476,#1254,.T.);
#1856=EDGE_CURVE('',#1477,#1475,#1255,.T.);
#1857=EDGE_CURVE('',#1476,#1477,#651,.T.);
#1858=EDGE_CURVE('',#1462,#1476,#652,.T.);
#1859=EDGE_CURVE('',#1464,#1477,#653,.T.);
#1860=EDGE_CURVE('',#1478,#1479,#1256,.T.);
#1861=EDGE_CURVE('',#1478,#1480,#654,.T.);
#1862=EDGE_CURVE('',#1481,#1480,#1257,.T.);
#1863=EDGE_CURVE('',#1479,#1481,#655,.T.);
#1864=EDGE_CURVE('',#1482,#1479,#656,.T.);
#1865=EDGE_CURVE('',#1483,#1481,#657,.T.);
#1866=EDGE_CURVE('',#1482,#1483,#658,.T.);
#1867=EDGE_CURVE('',#1482,#1484,#1258,.T.);
#1868=EDGE_CURVE('',#1485,#1483,#1259,.T.);
#1869=EDGE_CURVE('',#1484,#1485,#659,.T.);
#1870=EDGE_CURVE('',#1486,#1484,#660,.T.);
#1871=EDGE_CURVE('',#1487,#1485,#661,.T.);
#1872=EDGE_CURVE('',#1486,#1487,#662,.T.);
#1873=EDGE_CURVE('',#1486,#1488,#1260,.T.);
#1874=EDGE_CURVE('',#1489,#1487,#1261,.T.);
#1875=EDGE_CURVE('',#1488,#1489,#663,.T.);
#1876=EDGE_CURVE('',#1490,#1488,#664,.T.);
#1877=EDGE_CURVE('',#1491,#1489,#665,.T.);
#1878=EDGE_CURVE('',#1490,#1491,#666,.T.);
#1879=EDGE_CURVE('',#1490,#1492,#1262,.T.);
#1880=EDGE_CURVE('',#1493,#1491,#1263,.T.);
#1881=EDGE_CURVE('',#1492,#1493,#667,.T.);
#1882=EDGE_CURVE('',#1478,#1492,#668,.T.);
#1883=EDGE_CURVE('',#1480,#1493,#669,.T.);
#1884=EDGE_CURVE('',#1494,#1495,#1264,.T.);
#1885=EDGE_CURVE('',#1494,#1496,#670,.T.);
#1886=EDGE_CURVE('',#1497,#1496,#1265,.T.);
#1887=EDGE_CURVE('',#1495,#1497,#671,.T.);
#1888=EDGE_CURVE('',#1498,#1495,#672,.T.);
#1889=EDGE_CURVE('',#1499,#1497,#673,.T.);
#1890=EDGE_CURVE('',#1498,#1499,#674,.T.);
#1891=EDGE_CURVE('',#1498,#1500,#1266,.T.);
#1892=EDGE_CURVE('',#1501,#1499,#1267,.T.);
#1893=EDGE_CURVE('',#1500,#1501,#675,.T.);
#1894=EDGE_CURVE('',#1502,#1500,#676,.T.);
#1895=EDGE_CURVE('',#1503,#1501,#677,.T.);
#1896=EDGE_CURVE('',#1502,#1503,#678,.T.);
#1897=EDGE_CURVE('',#1502,#1504,#1268,.T.);
#1898=EDGE_CURVE('',#1505,#1503,#1269,.T.);
#1899=EDGE_CURVE('',#1504,#1505,#679,.T.);
#1900=EDGE_CURVE('',#1506,#1504,#680,.T.);
#1901=EDGE_CURVE('',#1507,#1505,#681,.T.);
#1902=EDGE_CURVE('',#1506,#1507,#682,.T.);
#1903=EDGE_CURVE('',#1506,#1508,#1270,.T.);
#1904=EDGE_CURVE('',#1509,#1507,#1271,.T.);
#1905=EDGE_CURVE('',#1508,#1509,#683,.T.);
#1906=EDGE_CURVE('',#1494,#1508,#684,.T.);
#1907=EDGE_CURVE('',#1496,#1509,#685,.T.);
#1908=EDGE_CURVE('',#1510,#1511,#686,.T.);
#1909=EDGE_CURVE('',#1510,#1512,#687,.T.);
#1910=EDGE_CURVE('',#1513,#1512,#688,.T.);
#1911=EDGE_CURVE('',#1511,#1513,#689,.T.);
#1912=EDGE_CURVE('',#1511,#1514,#1272,.T.);
#1913=EDGE_CURVE('',#1515,#1513,#1273,.T.);
#1914=EDGE_CURVE('',#1514,#1515,#690,.T.);
#1915=EDGE_CURVE('',#1514,#1516,#691,.T.);
#1916=EDGE_CURVE('',#1517,#1515,#692,.T.);
#1917=EDGE_CURVE('',#1516,#1517,#693,.T.);
#1918=EDGE_CURVE('',#1516,#1518,#1274,.T.);
#1919=EDGE_CURVE('',#1519,#1517,#1275,.T.);
#1920=EDGE_CURVE('',#1518,#1519,#694,.T.);
#1921=EDGE_CURVE('',#1518,#1520,#695,.T.);
#1922=EDGE_CURVE('',#1521,#1519,#696,.T.);
#1923=EDGE_CURVE('',#1520,#1521,#697,.T.);
#1924=EDGE_CURVE('',#1520,#1522,#1276,.T.);
#1925=EDGE_CURVE('',#1523,#1521,#1277,.T.);
#1926=EDGE_CURVE('',#1522,#1523,#698,.T.);
#1927=EDGE_CURVE('',#1522,#1524,#699,.T.);
#1928=EDGE_CURVE('',#1525,#1523,#700,.T.);
#1929=EDGE_CURVE('',#1524,#1525,#701,.T.);
#1930=EDGE_CURVE('',#1524,#1510,#1278,.T.);
#1931=EDGE_CURVE('',#1512,#1525,#1279,.T.);
#1932=EDGE_CURVE('',#1526,#1527,#1280,.T.);
#1933=EDGE_CURVE('',#1526,#1528,#702,.T.);
#1934=EDGE_CURVE('',#1529,#1528,#1281,.T.);
#1935=EDGE_CURVE('',#1527,#1529,#703,.T.);
#1936=EDGE_CURVE('',#1527,#1530,#704,.T.);
#1937=EDGE_CURVE('',#1531,#1529,#705,.T.);
#1938=EDGE_CURVE('',#1530,#1531,#706,.T.);
#1939=EDGE_CURVE('',#1530,#1532,#1282,.T.);
#1940=EDGE_CURVE('',#1533,#1531,#1283,.T.);
#1941=EDGE_CURVE('',#1532,#1533,#707,.T.);
#1942=EDGE_CURVE('',#1532,#1534,#708,.T.);
#1943=EDGE_CURVE('',#1535,#1533,#709,.T.);
#1944=EDGE_CURVE('',#1534,#1535,#710,.T.);
#1945=EDGE_CURVE('',#1534,#1536,#1284,.T.);
#1946=EDGE_CURVE('',#1537,#1535,#1285,.T.);
#1947=EDGE_CURVE('',#1536,#1537,#711,.T.);
#1948=EDGE_CURVE('',#1536,#1538,#712,.T.);
#1949=EDGE_CURVE('',#1539,#1537,#713,.T.);
#1950=EDGE_CURVE('',#1538,#1539,#714,.T.);
#1951=EDGE_CURVE('',#1538,#1540,#1286,.T.);
#1952=EDGE_CURVE('',#1541,#1539,#1287,.T.);
#1953=EDGE_CURVE('',#1540,#1541,#715,.T.);
#1954=EDGE_CURVE('',#1540,#1526,#716,.T.);
#1955=EDGE_CURVE('',#1528,#1541,#717,.T.);
#1956=EDGE_CURVE('',#1542,#1543,#1288,.T.);
#1957=EDGE_CURVE('',#1542,#1544,#718,.T.);
#1958=EDGE_CURVE('',#1545,#1544,#1289,.T.);
#1959=EDGE_CURVE('',#1543,#1545,#719,.T.);
#1960=EDGE_CURVE('',#1543,#1546,#720,.T.);
#1961=EDGE_CURVE('',#1547,#1545,#721,.T.);
#1962=EDGE_CURVE('',#1546,#1547,#722,.T.);
#1963=EDGE_CURVE('',#1546,#1548,#1290,.T.);
#1964=EDGE_CURVE('',#1549,#1547,#1291,.T.);
#1965=EDGE_CURVE('',#1548,#1549,#723,.T.);
#1966=EDGE_CURVE('',#1548,#1550,#724,.T.);
#1967=EDGE_CURVE('',#1551,#1549,#725,.T.);
#1968=EDGE_CURVE('',#1550,#1551,#726,.T.);
#1969=EDGE_CURVE('',#1550,#1552,#1292,.T.);
#1970=EDGE_CURVE('',#1553,#1551,#1293,.T.);
#1971=EDGE_CURVE('',#1552,#1553,#727,.T.);
#1972=EDGE_CURVE('',#1552,#1554,#728,.T.);
#1973=EDGE_CURVE('',#1555,#1553,#729,.T.);
#1974=EDGE_CURVE('',#1554,#1555,#730,.T.);
#1975=EDGE_CURVE('',#1554,#1556,#1294,.T.);
#1976=EDGE_CURVE('',#1557,#1555,#1295,.T.);
#1977=EDGE_CURVE('',#1556,#1557,#731,.T.);
#1978=EDGE_CURVE('',#1556,#1542,#732,.T.);
#1979=EDGE_CURVE('',#1544,#1557,#733,.T.);
#1980=EDGE_CURVE('',#1558,#1559,#1296,.T.);
#1981=EDGE_CURVE('',#1558,#1560,#734,.T.);
#1982=EDGE_CURVE('',#1561,#1560,#1297,.T.);
#1983=EDGE_CURVE('',#1559,#1561,#735,.T.);
#1984=EDGE_CURVE('',#1559,#1562,#736,.T.);
#1985=EDGE_CURVE('',#1563,#1561,#737,.T.);
#1986=EDGE_CURVE('',#1562,#1563,#738,.T.);
#1987=EDGE_CURVE('',#1562,#1564,#1298,.T.);
#1988=EDGE_CURVE('',#1565,#1563,#1299,.T.);
#1989=EDGE_CURVE('',#1564,#1565,#739,.T.);
#1990=EDGE_CURVE('',#1564,#1566,#740,.T.);
#1991=EDGE_CURVE('',#1567,#1565,#741,.T.);
#1992=EDGE_CURVE('',#1566,#1567,#742,.T.);
#1993=EDGE_CURVE('',#1566,#1568,#1300,.T.);
#1994=EDGE_CURVE('',#1569,#1567,#1301,.T.);
#1995=EDGE_CURVE('',#1568,#1569,#743,.T.);
#1996=EDGE_CURVE('',#1568,#1570,#744,.T.);
#1997=EDGE_CURVE('',#1571,#1569,#745,.T.);
#1998=EDGE_CURVE('',#1570,#1571,#746,.T.);
#1999=EDGE_CURVE('',#1570,#1572,#1302,.T.);
#2000=EDGE_CURVE('',#1573,#1571,#1303,.T.);
#2001=EDGE_CURVE('',#1572,#1573,#747,.T.);
#2002=EDGE_CURVE('',#1572,#1558,#748,.T.);
#2003=EDGE_CURVE('',#1560,#1573,#749,.T.);
#2004=EDGE_CURVE('',#1574,#1575,#1304,.T.);
#2005=EDGE_CURVE('',#1574,#1576,#750,.T.);
#2006=EDGE_CURVE('',#1577,#1576,#1305,.T.);
#2007=EDGE_CURVE('',#1575,#1577,#751,.T.);
#2008=EDGE_CURVE('',#1575,#1578,#752,.T.);
#2009=EDGE_CURVE('',#1579,#1577,#753,.T.);
#2010=EDGE_CURVE('',#1578,#1579,#754,.T.);
#2011=EDGE_CURVE('',#1578,#1580,#1306,.T.);
#2012=EDGE_CURVE('',#1581,#1579,#1307,.T.);
#2013=EDGE_CURVE('',#1580,#1581,#755,.T.);
#2014=EDGE_CURVE('',#1580,#1582,#756,.T.);
#2015=EDGE_CURVE('',#1583,#1581,#757,.T.);
#2016=EDGE_CURVE('',#1582,#1583,#758,.T.);
#2017=EDGE_CURVE('',#1582,#1584,#1308,.T.);
#2018=EDGE_CURVE('',#1585,#1583,#1309,.T.);
#2019=EDGE_CURVE('',#1584,#1585,#759,.T.);
#2020=EDGE_CURVE('',#1584,#1586,#760,.T.);
#2021=EDGE_CURVE('',#1587,#1585,#761,.T.);
#2022=EDGE_CURVE('',#1586,#1587,#762,.T.);
#2023=EDGE_CURVE('',#1586,#1588,#1310,.T.);
#2024=EDGE_CURVE('',#1589,#1587,#1311,.T.);
#2025=EDGE_CURVE('',#1588,#1589,#763,.T.);
#2026=EDGE_CURVE('',#1588,#1574,#764,.T.);
#2027=EDGE_CURVE('',#1576,#1589,#765,.T.);
#2028=EDGE_CURVE('',#1590,#1591,#766,.T.);
#2029=EDGE_CURVE('',#1590,#1592,#767,.T.);
#2030=EDGE_CURVE('',#1593,#1592,#768,.T.);
#2031=EDGE_CURVE('',#1591,#1593,#769,.T.);
#2032=EDGE_CURVE('',#1591,#1594,#1312,.T.);
#2033=EDGE_CURVE('',#1595,#1593,#1313,.T.);
#2034=EDGE_CURVE('',#1594,#1595,#770,.T.);
#2035=EDGE_CURVE('',#1594,#1596,#1314,.T.);
#2036=EDGE_CURVE('',#1597,#1595,#1315,.T.);
#2037=EDGE_CURVE('',#1596,#1597,#771,.T.);
#2038=EDGE_CURVE('',#1596,#1598,#772,.T.);
#2039=EDGE_CURVE('',#1599,#1597,#773,.T.);
#2040=EDGE_CURVE('',#1598,#1599,#774,.T.);
#2041=EDGE_CURVE('',#1598,#1600,#1316,.T.);
#2042=EDGE_CURVE('',#1601,#1599,#1317,.T.);
#2043=EDGE_CURVE('',#1600,#1601,#775,.T.);
#2044=EDGE_CURVE('',#1602,#1600,#1318,.T.);
#2045=EDGE_CURVE('',#1603,#1601,#1319,.T.);
#2046=EDGE_CURVE('',#1602,#1603,#776,.T.);
#2047=EDGE_CURVE('',#1602,#1604,#1320,.T.);
#2048=EDGE_CURVE('',#1605,#1603,#1321,.T.);
#2049=EDGE_CURVE('',#1604,#1605,#777,.T.);
#2050=EDGE_CURVE('',#1604,#1606,#778,.T.);
#2051=EDGE_CURVE('',#1607,#1605,#779,.T.);
#2052=EDGE_CURVE('',#1606,#1607,#780,.T.);
#2053=EDGE_CURVE('',#1608,#1606,#1322,.T.);
#2054=EDGE_CURVE('',#1609,#1607,#1323,.T.);
#2055=EDGE_CURVE('',#1608,#1609,#781,.T.);
#2056=EDGE_CURVE('',#1610,#1608,#782,.T.);
#2057=EDGE_CURVE('',#1611,#1609,#783,.T.);
#2058=EDGE_CURVE('',#1610,#1611,#784,.T.);
#2059=EDGE_CURVE('',#1610,#1612,#1324,.T.);
#2060=EDGE_CURVE('',#1613,#1611,#1325,.T.);
#2061=EDGE_CURVE('',#1612,#1613,#785,.T.);
#2062=EDGE_CURVE('',#1614,#1612,#1326,.T.);
#2063=EDGE_CURVE('',#1615,#1613,#1327,.T.);
#2064=EDGE_CURVE('',#1614,#1615,#786,.T.);
#2065=EDGE_CURVE('',#1614,#1616,#787,.T.);
#2066=EDGE_CURVE('',#1617,#1615,#788,.T.);
#2067=EDGE_CURVE('',#1616,#1617,#789,.T.);
#2068=EDGE_CURVE('',#1616,#1618,#1328,.T.);
#2069=EDGE_CURVE('',#1619,#1617,#1329,.T.);
#2070=EDGE_CURVE('',#1618,#1619,#790,.T.);
#2071=EDGE_CURVE('',#1620,#1618,#791,.T.);
#2072=EDGE_CURVE('',#1621,#1619,#792,.T.);
#2073=EDGE_CURVE('',#1620,#1621,#793,.T.);
#2074=EDGE_CURVE('',#1620,#1622,#1330,.T.);
#2075=EDGE_CURVE('',#1623,#1621,#1331,.T.);
#2076=EDGE_CURVE('',#1622,#1623,#794,.T.);
#2077=EDGE_CURVE('',#1624,#1622,#795,.T.);
#2078=EDGE_CURVE('',#1625,#1623,#796,.T.);
#2079=EDGE_CURVE('',#1624,#1625,#797,.T.);
#2080=EDGE_CURVE('',#1626,#1624,#1332,.T.);
#2081=EDGE_CURVE('',#1627,#1625,#1333,.T.);
#2082=EDGE_CURVE('',#1626,#1627,#798,.T.);
#2083=EDGE_CURVE('',#1626,#1628,#799,.T.);
#2084=EDGE_CURVE('',#1629,#1627,#800,.T.);
#2085=EDGE_CURVE('',#1628,#1629,#801,.T.);
#2086=EDGE_CURVE('',#1630,#1628,#1334,.T.);
#2087=EDGE_CURVE('',#1631,#1629,#1335,.T.);
#2088=EDGE_CURVE('',#1630,#1631,#802,.T.);
#2089=EDGE_CURVE('',#1632,#1630,#803,.T.);
#2090=EDGE_CURVE('',#1633,#1631,#804,.T.);
#2091=EDGE_CURVE('',#1632,#1633,#805,.T.);
#2092=EDGE_CURVE('',#1634,#1632,#1336,.T.);
#2093=EDGE_CURVE('',#1635,#1633,#1337,.T.);
#2094=EDGE_CURVE('',#1634,#1635,#806,.T.);
#2095=EDGE_CURVE('',#1636,#1634,#807,.T.);
#2096=EDGE_CURVE('',#1637,#1635,#808,.T.);
#2097=EDGE_CURVE('',#1636,#1637,#809,.T.);
#2098=EDGE_CURVE('',#1636,#1638,#1338,.T.);
#2099=EDGE_CURVE('',#1639,#1637,#1339,.T.);
#2100=EDGE_CURVE('',#1638,#1639,#810,.T.);
#2101=EDGE_CURVE('',#1638,#1640,#811,.T.);
#2102=EDGE_CURVE('',#1641,#1639,#812,.T.);
#2103=EDGE_CURVE('',#1640,#1641,#813,.T.);
#2104=EDGE_CURVE('',#1640,#1642,#1340,.T.);
#2105=EDGE_CURVE('',#1643,#1641,#1341,.T.);
#2106=EDGE_CURVE('',#1642,#1643,#814,.T.);
#2107=EDGE_CURVE('',#1644,#1642,#815,.T.);
#2108=EDGE_CURVE('',#1645,#1643,#816,.T.);
#2109=EDGE_CURVE('',#1644,#1645,#817,.T.);
#2110=EDGE_CURVE('',#1646,#1644,#1342,.T.);
#2111=EDGE_CURVE('',#1647,#1645,#1343,.T.);
#2112=EDGE_CURVE('',#1646,#1647,#818,.T.);
#2113=EDGE_CURVE('',#1648,#1646,#819,.T.);
#2114=EDGE_CURVE('',#1649,#1647,#820,.T.);
#2115=EDGE_CURVE('',#1648,#1649,#821,.T.);
#2116=EDGE_CURVE('',#1648,#1650,#1344,.T.);
#2117=EDGE_CURVE('',#1651,#1649,#1345,.T.);
#2118=EDGE_CURVE('',#1650,#1651,#822,.T.);
#2119=EDGE_CURVE('',#1650,#1652,#823,.T.);
#2120=EDGE_CURVE('',#1653,#1651,#824,.T.);
#2121=EDGE_CURVE('',#1652,#1653,#825,.T.);
#2122=EDGE_CURVE('',#1654,#1652,#1346,.T.);
#2123=EDGE_CURVE('',#1655,#1653,#1347,.T.);
#2124=EDGE_CURVE('',#1654,#1655,#826,.T.);
#2125=EDGE_CURVE('',#1654,#1656,#827,.T.);
#2126=EDGE_CURVE('',#1657,#1655,#828,.T.);
#2127=EDGE_CURVE('',#1656,#1657,#829,.T.);
#2128=EDGE_CURVE('',#1656,#1658,#1348,.T.);
#2129=EDGE_CURVE('',#1659,#1657,#1349,.T.);
#2130=EDGE_CURVE('',#1658,#1659,#830,.T.);
#2131=EDGE_CURVE('',#1660,#1658,#831,.T.);
#2132=EDGE_CURVE('',#1661,#1659,#832,.T.);
#2133=EDGE_CURVE('',#1660,#1661,#833,.T.);
#2134=EDGE_CURVE('',#1662,#1660,#1350,.T.);
#2135=EDGE_CURVE('',#1663,#1661,#1351,.T.);
#2136=EDGE_CURVE('',#1662,#1663,#834,.T.);
#2137=EDGE_CURVE('',#1664,#1662,#835,.T.);
#2138=EDGE_CURVE('',#1665,#1663,#836,.T.);
#2139=EDGE_CURVE('',#1664,#1665,#837,.T.);
#2140=EDGE_CURVE('',#1664,#1666,#1352,.T.);
#2141=EDGE_CURVE('',#1667,#1665,#1353,.T.);
#2142=EDGE_CURVE('',#1666,#1667,#838,.T.);
#2143=EDGE_CURVE('',#1668,#1666,#839,.T.);
#2144=EDGE_CURVE('',#1669,#1667,#840,.T.);
#2145=EDGE_CURVE('',#1668,#1669,#841,.T.);
#2146=EDGE_CURVE('',#1670,#1668,#1354,.T.);
#2147=EDGE_CURVE('',#1671,#1669,#1355,.T.);
#2148=EDGE_CURVE('',#1670,#1671,#842,.T.);
#2149=EDGE_CURVE('',#1672,#1670,#843,.T.);
#2150=EDGE_CURVE('',#1673,#1671,#844,.T.);
#2151=EDGE_CURVE('',#1672,#1673,#845,.T.);
#2152=EDGE_CURVE('',#1674,#1672,#1356,.T.);
#2153=EDGE_CURVE('',#1675,#1673,#1357,.T.);
#2154=EDGE_CURVE('',#1674,#1675,#846,.T.);
#2155=EDGE_CURVE('',#1676,#1674,#847,.T.);
#2156=EDGE_CURVE('',#1677,#1675,#848,.T.);
#2157=EDGE_CURVE('',#1676,#1677,#849,.T.);
#2158=EDGE_CURVE('',#1676,#1678,#1358,.T.);
#2159=EDGE_CURVE('',#1679,#1677,#1359,.T.);
#2160=EDGE_CURVE('',#1678,#1679,#850,.T.);
#2161=EDGE_CURVE('',#1680,#1678,#851,.T.);
#2162=EDGE_CURVE('',#1681,#1679,#852,.T.);
#2163=EDGE_CURVE('',#1680,#1681,#853,.T.);
#2164=EDGE_CURVE('',#1682,#1680,#1360,.T.);
#2165=EDGE_CURVE('',#1683,#1681,#1361,.T.);
#2166=EDGE_CURVE('',#1682,#1683,#854,.T.);
#2167=EDGE_CURVE('',#1684,#1682,#855,.T.);
#2168=EDGE_CURVE('',#1685,#1683,#856,.T.);
#2169=EDGE_CURVE('',#1684,#1685,#857,.T.);
#2170=EDGE_CURVE('',#1684,#1686,#1362,.T.);
#2171=EDGE_CURVE('',#1687,#1685,#1363,.T.);
#2172=EDGE_CURVE('',#1686,#1687,#858,.T.);
#2173=EDGE_CURVE('',#1688,#1686,#1364,.T.);
#2174=EDGE_CURVE('',#1689,#1687,#1365,.T.);
#2175=EDGE_CURVE('',#1688,#1689,#859,.T.);
#2176=EDGE_CURVE('',#1690,#1688,#860,.T.);
#2177=EDGE_CURVE('',#1691,#1689,#861,.T.);
#2178=EDGE_CURVE('',#1690,#1691,#862,.T.);
#2179=EDGE_CURVE('',#1692,#1690,#1366,.T.);
#2180=EDGE_CURVE('',#1693,#1691,#1367,.T.);
#2181=EDGE_CURVE('',#1692,#1693,#863,.T.);
#2182=EDGE_CURVE('',#1694,#1692,#864,.T.);
#2183=EDGE_CURVE('',#1695,#1693,#865,.T.);
#2184=EDGE_CURVE('',#1694,#1695,#866,.T.);
#2185=EDGE_CURVE('',#1694,#1696,#1368,.T.);
#2186=EDGE_CURVE('',#1697,#1695,#1369,.T.);
#2187=EDGE_CURVE('',#1696,#1697,#867,.T.);
#2188=EDGE_CURVE('',#1698,#1696,#868,.T.);
#2189=EDGE_CURVE('',#1699,#1697,#869,.T.);
#2190=EDGE_CURVE('',#1698,#1699,#870,.T.);
#2191=EDGE_CURVE('',#1698,#1700,#1370,.T.);
#2192=EDGE_CURVE('',#1701,#1699,#1371,.T.);
#2193=EDGE_CURVE('',#1700,#1701,#871,.T.);
#2194=EDGE_CURVE('',#1702,#1700,#1372,.T.);
#2195=EDGE_CURVE('',#1703,#1701,#1373,.T.);
#2196=EDGE_CURVE('',#1702,#1703,#872,.T.);
#2197=EDGE_CURVE('',#1704,#1702,#873,.T.);
#2198=EDGE_CURVE('',#1705,#1703,#874,.T.);
#2199=EDGE_CURVE('',#1704,#1705,#875,.T.);
#2200=EDGE_CURVE('',#1706,#1704,#1374,.T.);
#2201=EDGE_CURVE('',#1707,#1705,#1375,.T.);
#2202=EDGE_CURVE('',#1706,#1707,#876,.T.);
#2203=EDGE_CURVE('',#1706,#1708,#877,.T.);
#2204=EDGE_CURVE('',#1709,#1707,#878,.T.);
#2205=EDGE_CURVE('',#1708,#1709,#879,.T.);
#2206=EDGE_CURVE('',#1590,#1708,#1376,.T.);
#2207=EDGE_CURVE('',#1592,#1709,#1377,.T.);
#2208=ORIENTED_EDGE('',*,*,#1710,.F.);
#2209=ORIENTED_EDGE('',*,*,#1711,.T.);
#2210=ORIENTED_EDGE('',*,*,#1712,.F.);
#2211=ORIENTED_EDGE('',*,*,#1711,.F.);
#2212=ORIENTED_EDGE('',*,*,#1713,.F.);
#2213=ORIENTED_EDGE('',*,*,#1714,.T.);
#2214=ORIENTED_EDGE('',*,*,#1715,.F.);
#2215=ORIENTED_EDGE('',*,*,#1714,.F.);
#2216=ORIENTED_EDGE('',*,*,#1716,.F.);
#2217=ORIENTED_EDGE('',*,*,#1717,.T.);
#2218=ORIENTED_EDGE('',*,*,#1718,.F.);
#2219=ORIENTED_EDGE('',*,*,#1719,.F.);
#2220=ORIENTED_EDGE('',*,*,#1720,.T.);
#2221=ORIENTED_EDGE('',*,*,#1719,.T.);
#2222=ORIENTED_EDGE('',*,*,#1721,.F.);
#2223=ORIENTED_EDGE('',*,*,#1722,.F.);
#2224=ORIENTED_EDGE('',*,*,#1723,.F.);
#2225=ORIENTED_EDGE('',*,*,#1722,.T.);
#2226=ORIENTED_EDGE('',*,*,#1724,.F.);
#2227=ORIENTED_EDGE('',*,*,#1725,.F.);
#2228=ORIENTED_EDGE('',*,*,#1726,.T.);
#2229=ORIENTED_EDGE('',*,*,#1725,.T.);
#2230=ORIENTED_EDGE('',*,*,#1727,.F.);
#2231=ORIENTED_EDGE('',*,*,#1728,.F.);
#2232=ORIENTED_EDGE('',*,*,#1729,.F.);
#2233=ORIENTED_EDGE('',*,*,#1728,.T.);
#2234=ORIENTED_EDGE('',*,*,#1730,.F.);
#2235=ORIENTED_EDGE('',*,*,#1731,.F.);
#2236=ORIENTED_EDGE('',*,*,#1732,.T.);
#2237=ORIENTED_EDGE('',*,*,#1731,.T.);
#2238=ORIENTED_EDGE('',*,*,#1733,.F.);
#2239=ORIENTED_EDGE('',*,*,#1734,.F.);
#2240=ORIENTED_EDGE('',*,*,#1735,.F.);
#2241=ORIENTED_EDGE('',*,*,#1734,.T.);
#2242=ORIENTED_EDGE('',*,*,#1736,.F.);
#2243=ORIENTED_EDGE('',*,*,#1737,.F.);
#2244=ORIENTED_EDGE('',*,*,#1738,.T.);
#2245=ORIENTED_EDGE('',*,*,#1737,.T.);
#2246=ORIENTED_EDGE('',*,*,#1739,.F.);
#2247=ORIENTED_EDGE('',*,*,#1717,.F.);
#2248=ORIENTED_EDGE('',*,*,#1740,.F.);
#2249=ORIENTED_EDGE('',*,*,#1741,.T.);
#2250=ORIENTED_EDGE('',*,*,#1742,.F.);
#2251=ORIENTED_EDGE('',*,*,#1741,.F.);
#2252=ORIENTED_EDGE('',*,*,#1743,.F.);
#2253=ORIENTED_EDGE('',*,*,#1744,.T.);
#2254=ORIENTED_EDGE('',*,*,#1745,.F.);
#2255=ORIENTED_EDGE('',*,*,#1744,.F.);
#2256=ORIENTED_EDGE('',*,*,#1746,.F.);
#2257=ORIENTED_EDGE('',*,*,#1747,.T.);
#2258=ORIENTED_EDGE('',*,*,#1748,.F.);
#2259=ORIENTED_EDGE('',*,*,#1747,.F.);
#2260=ORIENTED_EDGE('',*,*,#1749,.F.);
#2261=ORIENTED_EDGE('',*,*,#1750,.T.);
#2262=ORIENTED_EDGE('',*,*,#1751,.F.);
#2263=ORIENTED_EDGE('',*,*,#1750,.F.);
#2264=ORIENTED_EDGE('',*,*,#1752,.F.);
#2265=ORIENTED_EDGE('',*,*,#1753,.T.);
#2266=ORIENTED_EDGE('',*,*,#1754,.F.);
#2267=ORIENTED_EDGE('',*,*,#1753,.F.);
#2268=ORIENTED_EDGE('',*,*,#1755,.F.);
#2269=ORIENTED_EDGE('',*,*,#1756,.T.);
#2270=ORIENTED_EDGE('',*,*,#1757,.F.);
#2271=ORIENTED_EDGE('',*,*,#1756,.F.);
#2272=ORIENTED_EDGE('',*,*,#1758,.F.);
#2273=ORIENTED_EDGE('',*,*,#1759,.T.);
#2274=ORIENTED_EDGE('',*,*,#1760,.F.);
#2275=ORIENTED_EDGE('',*,*,#1759,.F.);
#2276=ORIENTED_EDGE('',*,*,#1761,.F.);
#2277=ORIENTED_EDGE('',*,*,#1762,.T.);
#2278=ORIENTED_EDGE('',*,*,#1763,.F.);
#2279=ORIENTED_EDGE('',*,*,#1762,.F.);
#2280=ORIENTED_EDGE('',*,*,#1764,.F.);
#2281=ORIENTED_EDGE('',*,*,#1765,.T.);
#2282=ORIENTED_EDGE('',*,*,#1766,.F.);
#2283=ORIENTED_EDGE('',*,*,#1765,.F.);
#2284=ORIENTED_EDGE('',*,*,#1767,.F.);
#2285=ORIENTED_EDGE('',*,*,#1768,.T.);
#2286=ORIENTED_EDGE('',*,*,#1769,.F.);
#2287=ORIENTED_EDGE('',*,*,#1768,.F.);
#2288=ORIENTED_EDGE('',*,*,#1770,.F.);
#2289=ORIENTED_EDGE('',*,*,#1771,.T.);
#2290=ORIENTED_EDGE('',*,*,#1772,.F.);
#2291=ORIENTED_EDGE('',*,*,#1771,.F.);
#2292=ORIENTED_EDGE('',*,*,#1773,.F.);
#2293=ORIENTED_EDGE('',*,*,#1774,.T.);
#2294=ORIENTED_EDGE('',*,*,#1775,.F.);
#2295=ORIENTED_EDGE('',*,*,#1774,.F.);
#2296=ORIENTED_EDGE('',*,*,#1776,.F.);
#2297=ORIENTED_EDGE('',*,*,#1777,.T.);
#2298=ORIENTED_EDGE('',*,*,#1778,.F.);
#2299=ORIENTED_EDGE('',*,*,#1777,.F.);
#2300=ORIENTED_EDGE('',*,*,#1779,.F.);
#2301=ORIENTED_EDGE('',*,*,#1780,.T.);
#2302=ORIENTED_EDGE('',*,*,#1781,.F.);
#2303=ORIENTED_EDGE('',*,*,#1780,.F.);
#2304=ORIENTED_EDGE('',*,*,#1782,.F.);
#2305=ORIENTED_EDGE('',*,*,#1783,.T.);
#2306=ORIENTED_EDGE('',*,*,#1784,.F.);
#2307=ORIENTED_EDGE('',*,*,#1783,.F.);
#2308=ORIENTED_EDGE('',*,*,#1785,.F.);
#2309=ORIENTED_EDGE('',*,*,#1786,.T.);
#2310=ORIENTED_EDGE('',*,*,#1787,.F.);
#2311=ORIENTED_EDGE('',*,*,#1786,.F.);
#2312=ORIENTED_EDGE('',*,*,#1788,.F.);
#2313=ORIENTED_EDGE('',*,*,#1789,.T.);
#2314=ORIENTED_EDGE('',*,*,#1790,.F.);
#2315=ORIENTED_EDGE('',*,*,#1789,.F.);
#2316=ORIENTED_EDGE('',*,*,#1791,.F.);
#2317=ORIENTED_EDGE('',*,*,#1792,.T.);
#2318=ORIENTED_EDGE('',*,*,#1793,.F.);
#2319=ORIENTED_EDGE('',*,*,#1792,.F.);
#2320=ORIENTED_EDGE('',*,*,#1794,.F.);
#2321=ORIENTED_EDGE('',*,*,#1795,.T.);
#2322=ORIENTED_EDGE('',*,*,#1796,.F.);
#2323=ORIENTED_EDGE('',*,*,#1795,.F.);
#2324=ORIENTED_EDGE('',*,*,#1797,.F.);
#2325=ORIENTED_EDGE('',*,*,#1798,.T.);
#2326=ORIENTED_EDGE('',*,*,#1799,.F.);
#2327=ORIENTED_EDGE('',*,*,#1798,.F.);
#2328=ORIENTED_EDGE('',*,*,#1800,.F.);
#2329=ORIENTED_EDGE('',*,*,#1801,.T.);
#2330=ORIENTED_EDGE('',*,*,#1802,.F.);
#2331=ORIENTED_EDGE('',*,*,#1801,.F.);
#2332=ORIENTED_EDGE('',*,*,#1803,.F.);
#2333=ORIENTED_EDGE('',*,*,#1804,.T.);
#2334=ORIENTED_EDGE('',*,*,#1805,.F.);
#2335=ORIENTED_EDGE('',*,*,#1804,.F.);
#2336=ORIENTED_EDGE('',*,*,#1806,.F.);
#2337=ORIENTED_EDGE('',*,*,#1807,.T.);
#2338=ORIENTED_EDGE('',*,*,#1808,.F.);
#2339=ORIENTED_EDGE('',*,*,#1807,.F.);
#2340=ORIENTED_EDGE('',*,*,#1809,.F.);
#2341=ORIENTED_EDGE('',*,*,#1810,.T.);
#2342=ORIENTED_EDGE('',*,*,#1811,.F.);
#2343=ORIENTED_EDGE('',*,*,#1810,.F.);
#2344=ORIENTED_EDGE('',*,*,#1812,.F.);
#2345=ORIENTED_EDGE('',*,*,#1813,.T.);
#2346=ORIENTED_EDGE('',*,*,#1814,.F.);
#2347=ORIENTED_EDGE('',*,*,#1813,.F.);
#2348=ORIENTED_EDGE('',*,*,#1815,.F.);
#2349=ORIENTED_EDGE('',*,*,#1816,.T.);
#2350=ORIENTED_EDGE('',*,*,#1817,.F.);
#2351=ORIENTED_EDGE('',*,*,#1816,.F.);
#2352=ORIENTED_EDGE('',*,*,#1818,.F.);
#2353=ORIENTED_EDGE('',*,*,#1819,.T.);
#2354=ORIENTED_EDGE('',*,*,#1820,.F.);
#2355=ORIENTED_EDGE('',*,*,#1819,.F.);
#2356=ORIENTED_EDGE('',*,*,#1821,.F.);
#2357=ORIENTED_EDGE('',*,*,#1822,.T.);
#2358=ORIENTED_EDGE('',*,*,#1823,.F.);
#2359=ORIENTED_EDGE('',*,*,#1822,.F.);
#2360=ORIENTED_EDGE('',*,*,#1824,.F.);
#2361=ORIENTED_EDGE('',*,*,#1825,.T.);
#2362=ORIENTED_EDGE('',*,*,#1826,.F.);
#2363=ORIENTED_EDGE('',*,*,#1825,.F.);
#2364=ORIENTED_EDGE('',*,*,#1827,.F.);
#2365=ORIENTED_EDGE('',*,*,#1828,.T.);
#2366=ORIENTED_EDGE('',*,*,#1829,.F.);
#2367=ORIENTED_EDGE('',*,*,#1828,.F.);
#2368=ORIENTED_EDGE('',*,*,#1830,.F.);
#2369=ORIENTED_EDGE('',*,*,#1831,.T.);
#2370=ORIENTED_EDGE('',*,*,#1832,.F.);
#2371=ORIENTED_EDGE('',*,*,#1831,.F.);
#2372=ORIENTED_EDGE('',*,*,#1833,.F.);
#2373=ORIENTED_EDGE('',*,*,#1834,.T.);
#2374=ORIENTED_EDGE('',*,*,#1835,.F.);
#2375=ORIENTED_EDGE('',*,*,#1834,.F.);
#2376=ORIENTED_EDGE('',*,*,#1836,.F.);
#2377=ORIENTED_EDGE('',*,*,#1837,.T.);
#2378=ORIENTED_EDGE('',*,*,#1838,.F.);
#2379=ORIENTED_EDGE('',*,*,#1839,.F.);
#2380=ORIENTED_EDGE('',*,*,#1840,.T.);
#2381=ORIENTED_EDGE('',*,*,#1839,.T.);
#2382=ORIENTED_EDGE('',*,*,#1841,.F.);
#2383=ORIENTED_EDGE('',*,*,#1842,.F.);
#2384=ORIENTED_EDGE('',*,*,#1843,.F.);
#2385=ORIENTED_EDGE('',*,*,#1842,.T.);
#2386=ORIENTED_EDGE('',*,*,#1844,.F.);
#2387=ORIENTED_EDGE('',*,*,#1845,.F.);
#2388=ORIENTED_EDGE('',*,*,#1846,.T.);
#2389=ORIENTED_EDGE('',*,*,#1845,.T.);
#2390=ORIENTED_EDGE('',*,*,#1847,.F.);
#2391=ORIENTED_EDGE('',*,*,#1848,.F.);
#2392=ORIENTED_EDGE('',*,*,#1849,.F.);
#2393=ORIENTED_EDGE('',*,*,#1848,.T.);
#2394=ORIENTED_EDGE('',*,*,#1850,.F.);
#2395=ORIENTED_EDGE('',*,*,#1851,.F.);
#2396=ORIENTED_EDGE('',*,*,#1852,.T.);
#2397=ORIENTED_EDGE('',*,*,#1851,.T.);
#2398=ORIENTED_EDGE('',*,*,#1853,.F.);
#2399=ORIENTED_EDGE('',*,*,#1854,.F.);
#2400=ORIENTED_EDGE('',*,*,#1855,.F.);
#2401=ORIENTED_EDGE('',*,*,#1854,.T.);
#2402=ORIENTED_EDGE('',*,*,#1856,.F.);
#2403=ORIENTED_EDGE('',*,*,#1857,.F.);
#2404=ORIENTED_EDGE('',*,*,#1858,.T.);
#2405=ORIENTED_EDGE('',*,*,#1857,.T.);
#2406=ORIENTED_EDGE('',*,*,#1859,.F.);
#2407=ORIENTED_EDGE('',*,*,#1837,.F.);
#2408=ORIENTED_EDGE('',*,*,#1860,.F.);
#2409=ORIENTED_EDGE('',*,*,#1861,.T.);
#2410=ORIENTED_EDGE('',*,*,#1862,.F.);
#2411=ORIENTED_EDGE('',*,*,#1863,.F.);
#2412=ORIENTED_EDGE('',*,*,#1864,.T.);
#2413=ORIENTED_EDGE('',*,*,#1863,.T.);
#2414=ORIENTED_EDGE('',*,*,#1865,.F.);
#2415=ORIENTED_EDGE('',*,*,#1866,.F.);
#2416=ORIENTED_EDGE('',*,*,#1867,.F.);
#2417=ORIENTED_EDGE('',*,*,#1866,.T.);
#2418=ORIENTED_EDGE('',*,*,#1868,.F.);
#2419=ORIENTED_EDGE('',*,*,#1869,.F.);
#2420=ORIENTED_EDGE('',*,*,#1870,.T.);
#2421=ORIENTED_EDGE('',*,*,#1869,.T.);
#2422=ORIENTED_EDGE('',*,*,#1871,.F.);
#2423=ORIENTED_EDGE('',*,*,#1872,.F.);
#2424=ORIENTED_EDGE('',*,*,#1873,.F.);
#2425=ORIENTED_EDGE('',*,*,#1872,.T.);
#2426=ORIENTED_EDGE('',*,*,#1874,.F.);
#2427=ORIENTED_EDGE('',*,*,#1875,.F.);
#2428=ORIENTED_EDGE('',*,*,#1876,.T.);
#2429=ORIENTED_EDGE('',*,*,#1875,.T.);
#2430=ORIENTED_EDGE('',*,*,#1877,.F.);
#2431=ORIENTED_EDGE('',*,*,#1878,.F.);
#2432=ORIENTED_EDGE('',*,*,#1879,.F.);
#2433=ORIENTED_EDGE('',*,*,#1878,.T.);
#2434=ORIENTED_EDGE('',*,*,#1880,.F.);
#2435=ORIENTED_EDGE('',*,*,#1881,.F.);
#2436=ORIENTED_EDGE('',*,*,#1882,.T.);
#2437=ORIENTED_EDGE('',*,*,#1881,.T.);
#2438=ORIENTED_EDGE('',*,*,#1883,.F.);
#2439=ORIENTED_EDGE('',*,*,#1861,.F.);
#2440=ORIENTED_EDGE('',*,*,#1884,.F.);
#2441=ORIENTED_EDGE('',*,*,#1885,.T.);
#2442=ORIENTED_EDGE('',*,*,#1886,.F.);
#2443=ORIENTED_EDGE('',*,*,#1887,.F.);
#2444=ORIENTED_EDGE('',*,*,#1888,.T.);
#2445=ORIENTED_EDGE('',*,*,#1887,.T.);
#2446=ORIENTED_EDGE('',*,*,#1889,.F.);
#2447=ORIENTED_EDGE('',*,*,#1890,.F.);
#2448=ORIENTED_EDGE('',*,*,#1891,.F.);
#2449=ORIENTED_EDGE('',*,*,#1890,.T.);
#2450=ORIENTED_EDGE('',*,*,#1892,.F.);
#2451=ORIENTED_EDGE('',*,*,#1893,.F.);
#2452=ORIENTED_EDGE('',*,*,#1894,.T.);
#2453=ORIENTED_EDGE('',*,*,#1893,.T.);
#2454=ORIENTED_EDGE('',*,*,#1895,.F.);
#2455=ORIENTED_EDGE('',*,*,#1896,.F.);
#2456=ORIENTED_EDGE('',*,*,#1897,.F.);
#2457=ORIENTED_EDGE('',*,*,#1896,.T.);
#2458=ORIENTED_EDGE('',*,*,#1898,.F.);
#2459=ORIENTED_EDGE('',*,*,#1899,.F.);
#2460=ORIENTED_EDGE('',*,*,#1900,.T.);
#2461=ORIENTED_EDGE('',*,*,#1899,.T.);
#2462=ORIENTED_EDGE('',*,*,#1901,.F.);
#2463=ORIENTED_EDGE('',*,*,#1902,.F.);
#2464=ORIENTED_EDGE('',*,*,#1903,.F.);
#2465=ORIENTED_EDGE('',*,*,#1902,.T.);
#2466=ORIENTED_EDGE('',*,*,#1904,.F.);
#2467=ORIENTED_EDGE('',*,*,#1905,.F.);
#2468=ORIENTED_EDGE('',*,*,#1906,.T.);
#2469=ORIENTED_EDGE('',*,*,#1905,.T.);
#2470=ORIENTED_EDGE('',*,*,#1907,.F.);
#2471=ORIENTED_EDGE('',*,*,#1885,.F.);
#2472=ORIENTED_EDGE('',*,*,#1908,.F.);
#2473=ORIENTED_EDGE('',*,*,#1909,.T.);
#2474=ORIENTED_EDGE('',*,*,#1910,.F.);
#2475=ORIENTED_EDGE('',*,*,#1911,.F.);
#2476=ORIENTED_EDGE('',*,*,#1912,.F.);
#2477=ORIENTED_EDGE('',*,*,#1911,.T.);
#2478=ORIENTED_EDGE('',*,*,#1913,.F.);
#2479=ORIENTED_EDGE('',*,*,#1914,.F.);
#2480=ORIENTED_EDGE('',*,*,#1915,.F.);
#2481=ORIENTED_EDGE('',*,*,#1914,.T.);
#2482=ORIENTED_EDGE('',*,*,#1916,.F.);
#2483=ORIENTED_EDGE('',*,*,#1917,.F.);
#2484=ORIENTED_EDGE('',*,*,#1918,.F.);
#2485=ORIENTED_EDGE('',*,*,#1917,.T.);
#2486=ORIENTED_EDGE('',*,*,#1919,.F.);
#2487=ORIENTED_EDGE('',*,*,#1920,.F.);
#2488=ORIENTED_EDGE('',*,*,#1921,.F.);
#2489=ORIENTED_EDGE('',*,*,#1920,.T.);
#2490=ORIENTED_EDGE('',*,*,#1922,.F.);
#2491=ORIENTED_EDGE('',*,*,#1923,.F.);
#2492=ORIENTED_EDGE('',*,*,#1924,.F.);
#2493=ORIENTED_EDGE('',*,*,#1923,.T.);
#2494=ORIENTED_EDGE('',*,*,#1925,.F.);
#2495=ORIENTED_EDGE('',*,*,#1926,.F.);
#2496=ORIENTED_EDGE('',*,*,#1927,.F.);
#2497=ORIENTED_EDGE('',*,*,#1926,.T.);
#2498=ORIENTED_EDGE('',*,*,#1928,.F.);
#2499=ORIENTED_EDGE('',*,*,#1929,.F.);
#2500=ORIENTED_EDGE('',*,*,#1930,.F.);
#2501=ORIENTED_EDGE('',*,*,#1929,.T.);
#2502=ORIENTED_EDGE('',*,*,#1931,.F.);
#2503=ORIENTED_EDGE('',*,*,#1909,.F.);
#2504=ORIENTED_EDGE('',*,*,#1932,.F.);
#2505=ORIENTED_EDGE('',*,*,#1933,.T.);
#2506=ORIENTED_EDGE('',*,*,#1934,.F.);
#2507=ORIENTED_EDGE('',*,*,#1935,.F.);
#2508=ORIENTED_EDGE('',*,*,#1936,.F.);
#2509=ORIENTED_EDGE('',*,*,#1935,.T.);
#2510=ORIENTED_EDGE('',*,*,#1937,.F.);
#2511=ORIENTED_EDGE('',*,*,#1938,.F.);
#2512=ORIENTED_EDGE('',*,*,#1939,.F.);
#2513=ORIENTED_EDGE('',*,*,#1938,.T.);
#2514=ORIENTED_EDGE('',*,*,#1940,.F.);
#2515=ORIENTED_EDGE('',*,*,#1941,.F.);
#2516=ORIENTED_EDGE('',*,*,#1942,.F.);
#2517=ORIENTED_EDGE('',*,*,#1941,.T.);
#2518=ORIENTED_EDGE('',*,*,#1943,.F.);
#2519=ORIENTED_EDGE('',*,*,#1944,.F.);
#2520=ORIENTED_EDGE('',*,*,#1945,.F.);
#2521=ORIENTED_EDGE('',*,*,#1944,.T.);
#2522=ORIENTED_EDGE('',*,*,#1946,.F.);
#2523=ORIENTED_EDGE('',*,*,#1947,.F.);
#2524=ORIENTED_EDGE('',*,*,#1948,.F.);
#2525=ORIENTED_EDGE('',*,*,#1947,.T.);
#2526=ORIENTED_EDGE('',*,*,#1949,.F.);
#2527=ORIENTED_EDGE('',*,*,#1950,.F.);
#2528=ORIENTED_EDGE('',*,*,#1951,.F.);
#2529=ORIENTED_EDGE('',*,*,#1950,.T.);
#2530=ORIENTED_EDGE('',*,*,#1952,.F.);
#2531=ORIENTED_EDGE('',*,*,#1953,.F.);
#2532=ORIENTED_EDGE('',*,*,#1954,.F.);
#2533=ORIENTED_EDGE('',*,*,#1953,.T.);
#2534=ORIENTED_EDGE('',*,*,#1955,.F.);
#2535=ORIENTED_EDGE('',*,*,#1933,.F.);
#2536=ORIENTED_EDGE('',*,*,#1956,.F.);
#2537=ORIENTED_EDGE('',*,*,#1957,.T.);
#2538=ORIENTED_EDGE('',*,*,#1958,.F.);
#2539=ORIENTED_EDGE('',*,*,#1959,.F.);
#2540=ORIENTED_EDGE('',*,*,#1960,.F.);
#2541=ORIENTED_EDGE('',*,*,#1959,.T.);
#2542=ORIENTED_EDGE('',*,*,#1961,.F.);
#2543=ORIENTED_EDGE('',*,*,#1962,.F.);
#2544=ORIENTED_EDGE('',*,*,#1963,.F.);
#2545=ORIENTED_EDGE('',*,*,#1962,.T.);
#2546=ORIENTED_EDGE('',*,*,#1964,.F.);
#2547=ORIENTED_EDGE('',*,*,#1965,.F.);
#2548=ORIENTED_EDGE('',*,*,#1966,.F.);
#2549=ORIENTED_EDGE('',*,*,#1965,.T.);
#2550=ORIENTED_EDGE('',*,*,#1967,.F.);
#2551=ORIENTED_EDGE('',*,*,#1968,.F.);
#2552=ORIENTED_EDGE('',*,*,#1969,.F.);
#2553=ORIENTED_EDGE('',*,*,#1968,.T.);
#2554=ORIENTED_EDGE('',*,*,#1970,.F.);
#2555=ORIENTED_EDGE('',*,*,#1971,.F.);
#2556=ORIENTED_EDGE('',*,*,#1972,.F.);
#2557=ORIENTED_EDGE('',*,*,#1971,.T.);
#2558=ORIENTED_EDGE('',*,*,#1973,.F.);
#2559=ORIENTED_EDGE('',*,*,#1974,.F.);
#2560=ORIENTED_EDGE('',*,*,#1975,.F.);
#2561=ORIENTED_EDGE('',*,*,#1974,.T.);
#2562=ORIENTED_EDGE('',*,*,#1976,.F.);
#2563=ORIENTED_EDGE('',*,*,#1977,.F.);
#2564=ORIENTED_EDGE('',*,*,#1978,.F.);
#2565=ORIENTED_EDGE('',*,*,#1977,.T.);
#2566=ORIENTED_EDGE('',*,*,#1979,.F.);
#2567=ORIENTED_EDGE('',*,*,#1957,.F.);
#2568=ORIENTED_EDGE('',*,*,#1980,.F.);
#2569=ORIENTED_EDGE('',*,*,#1981,.T.);
#2570=ORIENTED_EDGE('',*,*,#1982,.F.);
#2571=ORIENTED_EDGE('',*,*,#1983,.F.);
#2572=ORIENTED_EDGE('',*,*,#1984,.F.);
#2573=ORIENTED_EDGE('',*,*,#1983,.T.);
#2574=ORIENTED_EDGE('',*,*,#1985,.F.);
#2575=ORIENTED_EDGE('',*,*,#1986,.F.);
#2576=ORIENTED_EDGE('',*,*,#1987,.F.);
#2577=ORIENTED_EDGE('',*,*,#1986,.T.);
#2578=ORIENTED_EDGE('',*,*,#1988,.F.);
#2579=ORIENTED_EDGE('',*,*,#1989,.F.);
#2580=ORIENTED_EDGE('',*,*,#1990,.F.);
#2581=ORIENTED_EDGE('',*,*,#1989,.T.);
#2582=ORIENTED_EDGE('',*,*,#1991,.F.);
#2583=ORIENTED_EDGE('',*,*,#1992,.F.);
#2584=ORIENTED_EDGE('',*,*,#1993,.F.);
#2585=ORIENTED_EDGE('',*,*,#1992,.T.);
#2586=ORIENTED_EDGE('',*,*,#1994,.F.);
#2587=ORIENTED_EDGE('',*,*,#1995,.F.);
#2588=ORIENTED_EDGE('',*,*,#1996,.F.);
#2589=ORIENTED_EDGE('',*,*,#1995,.T.);
#2590=ORIENTED_EDGE('',*,*,#1997,.F.);
#2591=ORIENTED_EDGE('',*,*,#1998,.F.);
#2592=ORIENTED_EDGE('',*,*,#1999,.F.);
#2593=ORIENTED_EDGE('',*,*,#1998,.T.);
#2594=ORIENTED_EDGE('',*,*,#2000,.F.);
#2595=ORIENTED_EDGE('',*,*,#2001,.F.);
#2596=ORIENTED_EDGE('',*,*,#2002,.F.);
#2597=ORIENTED_EDGE('',*,*,#2001,.T.);
#2598=ORIENTED_EDGE('',*,*,#2003,.F.);
#2599=ORIENTED_EDGE('',*,*,#1981,.F.);
#2600=ORIENTED_EDGE('',*,*,#2004,.F.);
#2601=ORIENTED_EDGE('',*,*,#2005,.T.);
#2602=ORIENTED_EDGE('',*,*,#2006,.F.);
#2603=ORIENTED_EDGE('',*,*,#2007,.F.);
#2604=ORIENTED_EDGE('',*,*,#2008,.F.);
#2605=ORIENTED_EDGE('',*,*,#2007,.T.);
#2606=ORIENTED_EDGE('',*,*,#2009,.F.);
#2607=ORIENTED_EDGE('',*,*,#2010,.F.);
#2608=ORIENTED_EDGE('',*,*,#2011,.F.);
#2609=ORIENTED_EDGE('',*,*,#2010,.T.);
#2610=ORIENTED_EDGE('',*,*,#2012,.F.);
#2611=ORIENTED_EDGE('',*,*,#2013,.F.);
#2612=ORIENTED_EDGE('',*,*,#2014,.F.);
#2613=ORIENTED_EDGE('',*,*,#2013,.T.);
#2614=ORIENTED_EDGE('',*,*,#2015,.F.);
#2615=ORIENTED_EDGE('',*,*,#2016,.F.);
#2616=ORIENTED_EDGE('',*,*,#2017,.F.);
#2617=ORIENTED_EDGE('',*,*,#2016,.T.);
#2618=ORIENTED_EDGE('',*,*,#2018,.F.);
#2619=ORIENTED_EDGE('',*,*,#2019,.F.);
#2620=ORIENTED_EDGE('',*,*,#2020,.F.);
#2621=ORIENTED_EDGE('',*,*,#2019,.T.);
#2622=ORIENTED_EDGE('',*,*,#2021,.F.);
#2623=ORIENTED_EDGE('',*,*,#2022,.F.);
#2624=ORIENTED_EDGE('',*,*,#2023,.F.);
#2625=ORIENTED_EDGE('',*,*,#2022,.T.);
#2626=ORIENTED_EDGE('',*,*,#2024,.F.);
#2627=ORIENTED_EDGE('',*,*,#2025,.F.);
#2628=ORIENTED_EDGE('',*,*,#2026,.F.);
#2629=ORIENTED_EDGE('',*,*,#2025,.T.);
#2630=ORIENTED_EDGE('',*,*,#2027,.F.);
#2631=ORIENTED_EDGE('',*,*,#2005,.F.);
#2632=ORIENTED_EDGE('',*,*,#2028,.F.);
#2633=ORIENTED_EDGE('',*,*,#2029,.T.);
#2634=ORIENTED_EDGE('',*,*,#2030,.F.);
#2635=ORIENTED_EDGE('',*,*,#2031,.F.);
#2636=ORIENTED_EDGE('',*,*,#2032,.F.);
#2637=ORIENTED_EDGE('',*,*,#2031,.T.);
#2638=ORIENTED_EDGE('',*,*,#2033,.F.);
#2639=ORIENTED_EDGE('',*,*,#2034,.F.);
#2640=ORIENTED_EDGE('',*,*,#2035,.F.);
#2641=ORIENTED_EDGE('',*,*,#2034,.T.);
#2642=ORIENTED_EDGE('',*,*,#2036,.F.);
#2643=ORIENTED_EDGE('',*,*,#2037,.F.);
#2644=ORIENTED_EDGE('',*,*,#2038,.F.);
#2645=ORIENTED_EDGE('',*,*,#2037,.T.);
#2646=ORIENTED_EDGE('',*,*,#2039,.F.);
#2647=ORIENTED_EDGE('',*,*,#2040,.F.);
#2648=ORIENTED_EDGE('',*,*,#2041,.F.);
#2649=ORIENTED_EDGE('',*,*,#2040,.T.);
#2650=ORIENTED_EDGE('',*,*,#2042,.F.);
#2651=ORIENTED_EDGE('',*,*,#2043,.F.);
#2652=ORIENTED_EDGE('',*,*,#2044,.T.);
#2653=ORIENTED_EDGE('',*,*,#2043,.T.);
#2654=ORIENTED_EDGE('',*,*,#2045,.F.);
#2655=ORIENTED_EDGE('',*,*,#2046,.F.);
#2656=ORIENTED_EDGE('',*,*,#2047,.F.);
#2657=ORIENTED_EDGE('',*,*,#2046,.T.);
#2658=ORIENTED_EDGE('',*,*,#2048,.F.);
#2659=ORIENTED_EDGE('',*,*,#2049,.F.);
#2660=ORIENTED_EDGE('',*,*,#2050,.F.);
#2661=ORIENTED_EDGE('',*,*,#2049,.T.);
#2662=ORIENTED_EDGE('',*,*,#2051,.F.);
#2663=ORIENTED_EDGE('',*,*,#2052,.F.);
#2664=ORIENTED_EDGE('',*,*,#2053,.T.);
#2665=ORIENTED_EDGE('',*,*,#2052,.T.);
#2666=ORIENTED_EDGE('',*,*,#2054,.F.);
#2667=ORIENTED_EDGE('',*,*,#2055,.F.);
#2668=ORIENTED_EDGE('',*,*,#2056,.T.);
#2669=ORIENTED_EDGE('',*,*,#2055,.T.);
#2670=ORIENTED_EDGE('',*,*,#2057,.F.);
#2671=ORIENTED_EDGE('',*,*,#2058,.F.);
#2672=ORIENTED_EDGE('',*,*,#2059,.F.);
#2673=ORIENTED_EDGE('',*,*,#2058,.T.);
#2674=ORIENTED_EDGE('',*,*,#2060,.F.);
#2675=ORIENTED_EDGE('',*,*,#2061,.F.);
#2676=ORIENTED_EDGE('',*,*,#2062,.T.);
#2677=ORIENTED_EDGE('',*,*,#2061,.T.);
#2678=ORIENTED_EDGE('',*,*,#2063,.F.);
#2679=ORIENTED_EDGE('',*,*,#2064,.F.);
#2680=ORIENTED_EDGE('',*,*,#2065,.F.);
#2681=ORIENTED_EDGE('',*,*,#2064,.T.);
#2682=ORIENTED_EDGE('',*,*,#2066,.F.);
#2683=ORIENTED_EDGE('',*,*,#2067,.F.);
#2684=ORIENTED_EDGE('',*,*,#2068,.F.);
#2685=ORIENTED_EDGE('',*,*,#2067,.T.);
#2686=ORIENTED_EDGE('',*,*,#2069,.F.);
#2687=ORIENTED_EDGE('',*,*,#2070,.F.);
#2688=ORIENTED_EDGE('',*,*,#2071,.T.);
#2689=ORIENTED_EDGE('',*,*,#2070,.T.);
#2690=ORIENTED_EDGE('',*,*,#2072,.F.);
#2691=ORIENTED_EDGE('',*,*,#2073,.F.);
#2692=ORIENTED_EDGE('',*,*,#2074,.F.);
#2693=ORIENTED_EDGE('',*,*,#2073,.T.);
#2694=ORIENTED_EDGE('',*,*,#2075,.F.);
#2695=ORIENTED_EDGE('',*,*,#2076,.F.);
#2696=ORIENTED_EDGE('',*,*,#2077,.T.);
#2697=ORIENTED_EDGE('',*,*,#2076,.T.);
#2698=ORIENTED_EDGE('',*,*,#2078,.F.);
#2699=ORIENTED_EDGE('',*,*,#2079,.F.);
#2700=ORIENTED_EDGE('',*,*,#2080,.T.);
#2701=ORIENTED_EDGE('',*,*,#2079,.T.);
#2702=ORIENTED_EDGE('',*,*,#2081,.F.);
#2703=ORIENTED_EDGE('',*,*,#2082,.F.);
#2704=ORIENTED_EDGE('',*,*,#2083,.F.);
#2705=ORIENTED_EDGE('',*,*,#2082,.T.);
#2706=ORIENTED_EDGE('',*,*,#2084,.F.);
#2707=ORIENTED_EDGE('',*,*,#2085,.F.);
#2708=ORIENTED_EDGE('',*,*,#2086,.T.);
#2709=ORIENTED_EDGE('',*,*,#2085,.T.);
#2710=ORIENTED_EDGE('',*,*,#2087,.F.);
#2711=ORIENTED_EDGE('',*,*,#2088,.F.);
#2712=ORIENTED_EDGE('',*,*,#2089,.T.);
#2713=ORIENTED_EDGE('',*,*,#2088,.T.);
#2714=ORIENTED_EDGE('',*,*,#2090,.F.);
#2715=ORIENTED_EDGE('',*,*,#2091,.F.);
#2716=ORIENTED_EDGE('',*,*,#2092,.T.);
#2717=ORIENTED_EDGE('',*,*,#2091,.T.);
#2718=ORIENTED_EDGE('',*,*,#2093,.F.);
#2719=ORIENTED_EDGE('',*,*,#2094,.F.);
#2720=ORIENTED_EDGE('',*,*,#2095,.T.);
#2721=ORIENTED_EDGE('',*,*,#2094,.T.);
#2722=ORIENTED_EDGE('',*,*,#2096,.F.);
#2723=ORIENTED_EDGE('',*,*,#2097,.F.);
#2724=ORIENTED_EDGE('',*,*,#2098,.F.);
#2725=ORIENTED_EDGE('',*,*,#2097,.T.);
#2726=ORIENTED_EDGE('',*,*,#2099,.F.);
#2727=ORIENTED_EDGE('',*,*,#2100,.F.);
#2728=ORIENTED_EDGE('',*,*,#2101,.F.);
#2729=ORIENTED_EDGE('',*,*,#2100,.T.);
#2730=ORIENTED_EDGE('',*,*,#2102,.F.);
#2731=ORIENTED_EDGE('',*,*,#2103,.F.);
#2732=ORIENTED_EDGE('',*,*,#2104,.F.);
#2733=ORIENTED_EDGE('',*,*,#2103,.T.);
#2734=ORIENTED_EDGE('',*,*,#2105,.F.);
#2735=ORIENTED_EDGE('',*,*,#2106,.F.);
#2736=ORIENTED_EDGE('',*,*,#2107,.T.);
#2737=ORIENTED_EDGE('',*,*,#2106,.T.);
#2738=ORIENTED_EDGE('',*,*,#2108,.F.);
#2739=ORIENTED_EDGE('',*,*,#2109,.F.);
#2740=ORIENTED_EDGE('',*,*,#2110,.T.);
#2741=ORIENTED_EDGE('',*,*,#2109,.T.);
#2742=ORIENTED_EDGE('',*,*,#2111,.F.);
#2743=ORIENTED_EDGE('',*,*,#2112,.F.);
#2744=ORIENTED_EDGE('',*,*,#2113,.T.);
#2745=ORIENTED_EDGE('',*,*,#2112,.T.);
#2746=ORIENTED_EDGE('',*,*,#2114,.F.);
#2747=ORIENTED_EDGE('',*,*,#2115,.F.);
#2748=ORIENTED_EDGE('',*,*,#2116,.F.);
#2749=ORIENTED_EDGE('',*,*,#2115,.T.);
#2750=ORIENTED_EDGE('',*,*,#2117,.F.);
#2751=ORIENTED_EDGE('',*,*,#2118,.F.);
#2752=ORIENTED_EDGE('',*,*,#2119,.F.);
#2753=ORIENTED_EDGE('',*,*,#2118,.T.);
#2754=ORIENTED_EDGE('',*,*,#2120,.F.);
#2755=ORIENTED_EDGE('',*,*,#2121,.F.);
#2756=ORIENTED_EDGE('',*,*,#2122,.T.);
#2757=ORIENTED_EDGE('',*,*,#2121,.T.);
#2758=ORIENTED_EDGE('',*,*,#2123,.F.);
#2759=ORIENTED_EDGE('',*,*,#2124,.F.);
#2760=ORIENTED_EDGE('',*,*,#2125,.F.);
#2761=ORIENTED_EDGE('',*,*,#2124,.T.);
#2762=ORIENTED_EDGE('',*,*,#2126,.F.);
#2763=ORIENTED_EDGE('',*,*,#2127,.F.);
#2764=ORIENTED_EDGE('',*,*,#2128,.F.);
#2765=ORIENTED_EDGE('',*,*,#2127,.T.);
#2766=ORIENTED_EDGE('',*,*,#2129,.F.);
#2767=ORIENTED_EDGE('',*,*,#2130,.F.);
#2768=ORIENTED_EDGE('',*,*,#2131,.T.);
#2769=ORIENTED_EDGE('',*,*,#2130,.T.);
#2770=ORIENTED_EDGE('',*,*,#2132,.F.);
#2771=ORIENTED_EDGE('',*,*,#2133,.F.);
#2772=ORIENTED_EDGE('',*,*,#2134,.T.);
#2773=ORIENTED_EDGE('',*,*,#2133,.T.);
#2774=ORIENTED_EDGE('',*,*,#2135,.F.);
#2775=ORIENTED_EDGE('',*,*,#2136,.F.);
#2776=ORIENTED_EDGE('',*,*,#2137,.T.);
#2777=ORIENTED_EDGE('',*,*,#2136,.T.);
#2778=ORIENTED_EDGE('',*,*,#2138,.F.);
#2779=ORIENTED_EDGE('',*,*,#2139,.F.);
#2780=ORIENTED_EDGE('',*,*,#2140,.F.);
#2781=ORIENTED_EDGE('',*,*,#2139,.T.);
#2782=ORIENTED_EDGE('',*,*,#2141,.F.);
#2783=ORIENTED_EDGE('',*,*,#2142,.F.);
#2784=ORIENTED_EDGE('',*,*,#2143,.T.);
#2785=ORIENTED_EDGE('',*,*,#2142,.T.);
#2786=ORIENTED_EDGE('',*,*,#2144,.F.);
#2787=ORIENTED_EDGE('',*,*,#2145,.F.);
#2788=ORIENTED_EDGE('',*,*,#2146,.T.);
#2789=ORIENTED_EDGE('',*,*,#2145,.T.);
#2790=ORIENTED_EDGE('',*,*,#2147,.F.);
#2791=ORIENTED_EDGE('',*,*,#2148,.F.);
#2792=ORIENTED_EDGE('',*,*,#2149,.T.);
#2793=ORIENTED_EDGE('',*,*,#2148,.T.);
#2794=ORIENTED_EDGE('',*,*,#2150,.F.);
#2795=ORIENTED_EDGE('',*,*,#2151,.F.);
#2796=ORIENTED_EDGE('',*,*,#2152,.T.);
#2797=ORIENTED_EDGE('',*,*,#2151,.T.);
#2798=ORIENTED_EDGE('',*,*,#2153,.F.);
#2799=ORIENTED_EDGE('',*,*,#2154,.F.);
#2800=ORIENTED_EDGE('',*,*,#2155,.T.);
#2801=ORIENTED_EDGE('',*,*,#2154,.T.);
#2802=ORIENTED_EDGE('',*,*,#2156,.F.);
#2803=ORIENTED_EDGE('',*,*,#2157,.F.);
#2804=ORIENTED_EDGE('',*,*,#2158,.F.);
#2805=ORIENTED_EDGE('',*,*,#2157,.T.);
#2806=ORIENTED_EDGE('',*,*,#2159,.F.);
#2807=ORIENTED_EDGE('',*,*,#2160,.F.);
#2808=ORIENTED_EDGE('',*,*,#2161,.T.);
#2809=ORIENTED_EDGE('',*,*,#2160,.T.);
#2810=ORIENTED_EDGE('',*,*,#2162,.F.);
#2811=ORIENTED_EDGE('',*,*,#2163,.F.);
#2812=ORIENTED_EDGE('',*,*,#2164,.T.);
#2813=ORIENTED_EDGE('',*,*,#2163,.T.);
#2814=ORIENTED_EDGE('',*,*,#2165,.F.);
#2815=ORIENTED_EDGE('',*,*,#2166,.F.);
#2816=ORIENTED_EDGE('',*,*,#2167,.T.);
#2817=ORIENTED_EDGE('',*,*,#2166,.T.);
#2818=ORIENTED_EDGE('',*,*,#2168,.F.);
#2819=ORIENTED_EDGE('',*,*,#2169,.F.);
#2820=ORIENTED_EDGE('',*,*,#2170,.F.);
#2821=ORIENTED_EDGE('',*,*,#2169,.T.);
#2822=ORIENTED_EDGE('',*,*,#2171,.F.);
#2823=ORIENTED_EDGE('',*,*,#2172,.F.);
#2824=ORIENTED_EDGE('',*,*,#2173,.T.);
#2825=ORIENTED_EDGE('',*,*,#2172,.T.);
#2826=ORIENTED_EDGE('',*,*,#2174,.F.);
#2827=ORIENTED_EDGE('',*,*,#2175,.F.);
#2828=ORIENTED_EDGE('',*,*,#2176,.T.);
#2829=ORIENTED_EDGE('',*,*,#2175,.T.);
#2830=ORIENTED_EDGE('',*,*,#2177,.F.);
#2831=ORIENTED_EDGE('',*,*,#2178,.F.);
#2832=ORIENTED_EDGE('',*,*,#2179,.T.);
#2833=ORIENTED_EDGE('',*,*,#2178,.T.);
#2834=ORIENTED_EDGE('',*,*,#2180,.F.);
#2835=ORIENTED_EDGE('',*,*,#2181,.F.);
#2836=ORIENTED_EDGE('',*,*,#2182,.T.);
#2837=ORIENTED_EDGE('',*,*,#2181,.T.);
#2838=ORIENTED_EDGE('',*,*,#2183,.F.);
#2839=ORIENTED_EDGE('',*,*,#2184,.F.);
#2840=ORIENTED_EDGE('',*,*,#2185,.F.);
#2841=ORIENTED_EDGE('',*,*,#2184,.T.);
#2842=ORIENTED_EDGE('',*,*,#2186,.F.);
#2843=ORIENTED_EDGE('',*,*,#2187,.F.);
#2844=ORIENTED_EDGE('',*,*,#2188,.T.);
#2845=ORIENTED_EDGE('',*,*,#2187,.T.);
#2846=ORIENTED_EDGE('',*,*,#2189,.F.);
#2847=ORIENTED_EDGE('',*,*,#2190,.F.);
#2848=ORIENTED_EDGE('',*,*,#2191,.F.);
#2849=ORIENTED_EDGE('',*,*,#2190,.T.);
#2850=ORIENTED_EDGE('',*,*,#2192,.F.);
#2851=ORIENTED_EDGE('',*,*,#2193,.F.);
#2852=ORIENTED_EDGE('',*,*,#2194,.T.);
#2853=ORIENTED_EDGE('',*,*,#2193,.T.);
#2854=ORIENTED_EDGE('',*,*,#2195,.F.);
#2855=ORIENTED_EDGE('',*,*,#2196,.F.);
#2856=ORIENTED_EDGE('',*,*,#2197,.T.);
#2857=ORIENTED_EDGE('',*,*,#2196,.T.);
#2858=ORIENTED_EDGE('',*,*,#2198,.F.);
#2859=ORIENTED_EDGE('',*,*,#2199,.F.);
#2860=ORIENTED_EDGE('',*,*,#2200,.T.);
#2861=ORIENTED_EDGE('',*,*,#2199,.T.);
#2862=ORIENTED_EDGE('',*,*,#2201,.F.);
#2863=ORIENTED_EDGE('',*,*,#2202,.F.);
#2864=ORIENTED_EDGE('',*,*,#2203,.F.);
#2865=ORIENTED_EDGE('',*,*,#2202,.T.);
#2866=ORIENTED_EDGE('',*,*,#2204,.F.);
#2867=ORIENTED_EDGE('',*,*,#2205,.F.);
#2868=ORIENTED_EDGE('',*,*,#2206,.T.);
#2869=ORIENTED_EDGE('',*,*,#2205,.T.);
#2870=ORIENTED_EDGE('',*,*,#2207,.F.);
#2871=ORIENTED_EDGE('',*,*,#2029,.F.);
#2872=ORIENTED_EDGE('',*,*,#2207,.T.);
#2873=ORIENTED_EDGE('',*,*,#2204,.T.);
#2874=ORIENTED_EDGE('',*,*,#2201,.T.);
#2875=ORIENTED_EDGE('',*,*,#2198,.T.);
#2876=ORIENTED_EDGE('',*,*,#2195,.T.);
#2877=ORIENTED_EDGE('',*,*,#2192,.T.);
#2878=ORIENTED_EDGE('',*,*,#2189,.T.);
#2879=ORIENTED_EDGE('',*,*,#2186,.T.);
#2880=ORIENTED_EDGE('',*,*,#2183,.T.);
#2881=ORIENTED_EDGE('',*,*,#2180,.T.);
#2882=ORIENTED_EDGE('',*,*,#2177,.T.);
#2883=ORIENTED_EDGE('',*,*,#2174,.T.);
#2884=ORIENTED_EDGE('',*,*,#2171,.T.);
#2885=ORIENTED_EDGE('',*,*,#2168,.T.);
#2886=ORIENTED_EDGE('',*,*,#2165,.T.);
#2887=ORIENTED_EDGE('',*,*,#2162,.T.);
#2888=ORIENTED_EDGE('',*,*,#2159,.T.);
#2889=ORIENTED_EDGE('',*,*,#2156,.T.);
#2890=ORIENTED_EDGE('',*,*,#2153,.T.);
#2891=ORIENTED_EDGE('',*,*,#2150,.T.);
#2892=ORIENTED_EDGE('',*,*,#2147,.T.);
#2893=ORIENTED_EDGE('',*,*,#2144,.T.);
#2894=ORIENTED_EDGE('',*,*,#2141,.T.);
#2895=ORIENTED_EDGE('',*,*,#2138,.T.);
#2896=ORIENTED_EDGE('',*,*,#2135,.T.);
#2897=ORIENTED_EDGE('',*,*,#2132,.T.);
#2898=ORIENTED_EDGE('',*,*,#2129,.T.);
#2899=ORIENTED_EDGE('',*,*,#2126,.T.);
#2900=ORIENTED_EDGE('',*,*,#2123,.T.);
#2901=ORIENTED_EDGE('',*,*,#2120,.T.);
#2902=ORIENTED_EDGE('',*,*,#2117,.T.);
#2903=ORIENTED_EDGE('',*,*,#2114,.T.);
#2904=ORIENTED_EDGE('',*,*,#2111,.T.);
#2905=ORIENTED_EDGE('',*,*,#2108,.T.);
#2906=ORIENTED_EDGE('',*,*,#2105,.T.);
#2907=ORIENTED_EDGE('',*,*,#2102,.T.);
#2908=ORIENTED_EDGE('',*,*,#2099,.T.);
#2909=ORIENTED_EDGE('',*,*,#2096,.T.);
#2910=ORIENTED_EDGE('',*,*,#2093,.T.);
#2911=ORIENTED_EDGE('',*,*,#2090,.T.);
#2912=ORIENTED_EDGE('',*,*,#2087,.T.);
#2913=ORIENTED_EDGE('',*,*,#2084,.T.);
#2914=ORIENTED_EDGE('',*,*,#2081,.T.);
#2915=ORIENTED_EDGE('',*,*,#2078,.T.);
#2916=ORIENTED_EDGE('',*,*,#2075,.T.);
#2917=ORIENTED_EDGE('',*,*,#2072,.T.);
#2918=ORIENTED_EDGE('',*,*,#2069,.T.);
#2919=ORIENTED_EDGE('',*,*,#2066,.T.);
#2920=ORIENTED_EDGE('',*,*,#2063,.T.);
#2921=ORIENTED_EDGE('',*,*,#2060,.T.);
#2922=ORIENTED_EDGE('',*,*,#2057,.T.);
#2923=ORIENTED_EDGE('',*,*,#2054,.T.);
#2924=ORIENTED_EDGE('',*,*,#2051,.T.);
#2925=ORIENTED_EDGE('',*,*,#2048,.T.);
#2926=ORIENTED_EDGE('',*,*,#2045,.T.);
#2927=ORIENTED_EDGE('',*,*,#2042,.T.);
#2928=ORIENTED_EDGE('',*,*,#2039,.T.);
#2929=ORIENTED_EDGE('',*,*,#2036,.T.);
#2930=ORIENTED_EDGE('',*,*,#2033,.T.);
#2931=ORIENTED_EDGE('',*,*,#2030,.T.);
#2932=ORIENTED_EDGE('',*,*,#2027,.T.);
#2933=ORIENTED_EDGE('',*,*,#2024,.T.);
#2934=ORIENTED_EDGE('',*,*,#2021,.T.);
#2935=ORIENTED_EDGE('',*,*,#2018,.T.);
#2936=ORIENTED_EDGE('',*,*,#2015,.T.);
#2937=ORIENTED_EDGE('',*,*,#2012,.T.);
#2938=ORIENTED_EDGE('',*,*,#2009,.T.);
#2939=ORIENTED_EDGE('',*,*,#2006,.T.);
#2940=ORIENTED_EDGE('',*,*,#2003,.T.);
#2941=ORIENTED_EDGE('',*,*,#2000,.T.);
#2942=ORIENTED_EDGE('',*,*,#1997,.T.);
#2943=ORIENTED_EDGE('',*,*,#1994,.T.);
#2944=ORIENTED_EDGE('',*,*,#1991,.T.);
#2945=ORIENTED_EDGE('',*,*,#1988,.T.);
#2946=ORIENTED_EDGE('',*,*,#1985,.T.);
#2947=ORIENTED_EDGE('',*,*,#1982,.T.);
#2948=ORIENTED_EDGE('',*,*,#1979,.T.);
#2949=ORIENTED_EDGE('',*,*,#1976,.T.);
#2950=ORIENTED_EDGE('',*,*,#1973,.T.);
#2951=ORIENTED_EDGE('',*,*,#1970,.T.);
#2952=ORIENTED_EDGE('',*,*,#1967,.T.);
#2953=ORIENTED_EDGE('',*,*,#1964,.T.);
#2954=ORIENTED_EDGE('',*,*,#1961,.T.);
#2955=ORIENTED_EDGE('',*,*,#1958,.T.);
#2956=ORIENTED_EDGE('',*,*,#1955,.T.);
#2957=ORIENTED_EDGE('',*,*,#1952,.T.);
#2958=ORIENTED_EDGE('',*,*,#1949,.T.);
#2959=ORIENTED_EDGE('',*,*,#1946,.T.);
#2960=ORIENTED_EDGE('',*,*,#1943,.T.);
#2961=ORIENTED_EDGE('',*,*,#1940,.T.);
#2962=ORIENTED_EDGE('',*,*,#1937,.T.);
#2963=ORIENTED_EDGE('',*,*,#1934,.T.);
#2964=ORIENTED_EDGE('',*,*,#1931,.T.);
#2965=ORIENTED_EDGE('',*,*,#1928,.T.);
#2966=ORIENTED_EDGE('',*,*,#1925,.T.);
#2967=ORIENTED_EDGE('',*,*,#1922,.T.);
#2968=ORIENTED_EDGE('',*,*,#1919,.T.);
#2969=ORIENTED_EDGE('',*,*,#1916,.T.);
#2970=ORIENTED_EDGE('',*,*,#1913,.T.);
#2971=ORIENTED_EDGE('',*,*,#1910,.T.);
#2972=ORIENTED_EDGE('',*,*,#1907,.T.);
#2973=ORIENTED_EDGE('',*,*,#1904,.T.);
#2974=ORIENTED_EDGE('',*,*,#1901,.T.);
#2975=ORIENTED_EDGE('',*,*,#1898,.T.);
#2976=ORIENTED_EDGE('',*,*,#1895,.T.);
#2977=ORIENTED_EDGE('',*,*,#1892,.T.);
#2978=ORIENTED_EDGE('',*,*,#1889,.T.);
#2979=ORIENTED_EDGE('',*,*,#1886,.T.);
#2980=ORIENTED_EDGE('',*,*,#1883,.T.);
#2981=ORIENTED_EDGE('',*,*,#1880,.T.);
#2982=ORIENTED_EDGE('',*,*,#1877,.T.);
#2983=ORIENTED_EDGE('',*,*,#1874,.T.);
#2984=ORIENTED_EDGE('',*,*,#1871,.T.);
#2985=ORIENTED_EDGE('',*,*,#1868,.T.);
#2986=ORIENTED_EDGE('',*,*,#1865,.T.);
#2987=ORIENTED_EDGE('',*,*,#1862,.T.);
#2988=ORIENTED_EDGE('',*,*,#1859,.T.);
#2989=ORIENTED_EDGE('',*,*,#1856,.T.);
#2990=ORIENTED_EDGE('',*,*,#1853,.T.);
#2991=ORIENTED_EDGE('',*,*,#1850,.T.);
#2992=ORIENTED_EDGE('',*,*,#1847,.T.);
#2993=ORIENTED_EDGE('',*,*,#1844,.T.);
#2994=ORIENTED_EDGE('',*,*,#1841,.T.);
#2995=ORIENTED_EDGE('',*,*,#1838,.T.);
#2996=ORIENTED_EDGE('',*,*,#1833,.T.);
#2997=ORIENTED_EDGE('',*,*,#1830,.T.);
#2998=ORIENTED_EDGE('',*,*,#1827,.T.);
#2999=ORIENTED_EDGE('',*,*,#1824,.T.);
#3000=ORIENTED_EDGE('',*,*,#1821,.T.);
#3001=ORIENTED_EDGE('',*,*,#1818,.T.);
#3002=ORIENTED_EDGE('',*,*,#1815,.T.);
#3003=ORIENTED_EDGE('',*,*,#1812,.T.);
#3004=ORIENTED_EDGE('',*,*,#1809,.T.);
#3005=ORIENTED_EDGE('',*,*,#1806,.T.);
#3006=ORIENTED_EDGE('',*,*,#1803,.T.);
#3007=ORIENTED_EDGE('',*,*,#1800,.T.);
#3008=ORIENTED_EDGE('',*,*,#1797,.T.);
#3009=ORIENTED_EDGE('',*,*,#1794,.T.);
#3010=ORIENTED_EDGE('',*,*,#1791,.T.);
#3011=ORIENTED_EDGE('',*,*,#1788,.T.);
#3012=ORIENTED_EDGE('',*,*,#1785,.T.);
#3013=ORIENTED_EDGE('',*,*,#1782,.T.);
#3014=ORIENTED_EDGE('',*,*,#1779,.T.);
#3015=ORIENTED_EDGE('',*,*,#1776,.T.);
#3016=ORIENTED_EDGE('',*,*,#1773,.T.);
#3017=ORIENTED_EDGE('',*,*,#1770,.T.);
#3018=ORIENTED_EDGE('',*,*,#1767,.T.);
#3019=ORIENTED_EDGE('',*,*,#1764,.T.);
#3020=ORIENTED_EDGE('',*,*,#1761,.T.);
#3021=ORIENTED_EDGE('',*,*,#1758,.T.);
#3022=ORIENTED_EDGE('',*,*,#1755,.T.);
#3023=ORIENTED_EDGE('',*,*,#1752,.T.);
#3024=ORIENTED_EDGE('',*,*,#1749,.T.);
#3025=ORIENTED_EDGE('',*,*,#1746,.T.);
#3026=ORIENTED_EDGE('',*,*,#1743,.T.);
#3027=ORIENTED_EDGE('',*,*,#1740,.T.);
#3028=ORIENTED_EDGE('',*,*,#1739,.T.);
#3029=ORIENTED_EDGE('',*,*,#1736,.T.);
#3030=ORIENTED_EDGE('',*,*,#1733,.T.);
#3031=ORIENTED_EDGE('',*,*,#1730,.T.);
#3032=ORIENTED_EDGE('',*,*,#1727,.T.);
#3033=ORIENTED_EDGE('',*,*,#1724,.T.);
#3034=ORIENTED_EDGE('',*,*,#1721,.T.);
#3035=ORIENTED_EDGE('',*,*,#1718,.T.);
#3036=ORIENTED_EDGE('',*,*,#1713,.T.);
#3037=ORIENTED_EDGE('',*,*,#1710,.T.);
#3038=ORIENTED_EDGE('',*,*,#2206,.F.);
#3039=ORIENTED_EDGE('',*,*,#2028,.T.);
#3040=ORIENTED_EDGE('',*,*,#2032,.T.);
#3041=ORIENTED_EDGE('',*,*,#2035,.T.);
#3042=ORIENTED_EDGE('',*,*,#2038,.T.);
#3043=ORIENTED_EDGE('',*,*,#2041,.T.);
#3044=ORIENTED_EDGE('',*,*,#2044,.F.);
#3045=ORIENTED_EDGE('',*,*,#2047,.T.);
#3046=ORIENTED_EDGE('',*,*,#2050,.T.);
#3047=ORIENTED_EDGE('',*,*,#2053,.F.);
#3048=ORIENTED_EDGE('',*,*,#2056,.F.);
#3049=ORIENTED_EDGE('',*,*,#2059,.T.);
#3050=ORIENTED_EDGE('',*,*,#2062,.F.);
#3051=ORIENTED_EDGE('',*,*,#2065,.T.);
#3052=ORIENTED_EDGE('',*,*,#2068,.T.);
#3053=ORIENTED_EDGE('',*,*,#2071,.F.);
#3054=ORIENTED_EDGE('',*,*,#2074,.T.);
#3055=ORIENTED_EDGE('',*,*,#2077,.F.);
#3056=ORIENTED_EDGE('',*,*,#2080,.F.);
#3057=ORIENTED_EDGE('',*,*,#2083,.T.);
#3058=ORIENTED_EDGE('',*,*,#2086,.F.);
#3059=ORIENTED_EDGE('',*,*,#2089,.F.);
#3060=ORIENTED_EDGE('',*,*,#2092,.F.);
#3061=ORIENTED_EDGE('',*,*,#2095,.F.);
#3062=ORIENTED_EDGE('',*,*,#2098,.T.);
#3063=ORIENTED_EDGE('',*,*,#2101,.T.);
#3064=ORIENTED_EDGE('',*,*,#2104,.T.);
#3065=ORIENTED_EDGE('',*,*,#2107,.F.);
#3066=ORIENTED_EDGE('',*,*,#2110,.F.);
#3067=ORIENTED_EDGE('',*,*,#2113,.F.);
#3068=ORIENTED_EDGE('',*,*,#2116,.T.);
#3069=ORIENTED_EDGE('',*,*,#2119,.T.);
#3070=ORIENTED_EDGE('',*,*,#2122,.F.);
#3071=ORIENTED_EDGE('',*,*,#2125,.T.);
#3072=ORIENTED_EDGE('',*,*,#2128,.T.);
#3073=ORIENTED_EDGE('',*,*,#2131,.F.);
#3074=ORIENTED_EDGE('',*,*,#2134,.F.);
#3075=ORIENTED_EDGE('',*,*,#2137,.F.);
#3076=ORIENTED_EDGE('',*,*,#2140,.T.);
#3077=ORIENTED_EDGE('',*,*,#2143,.F.);
#3078=ORIENTED_EDGE('',*,*,#2146,.F.);
#3079=ORIENTED_EDGE('',*,*,#2149,.F.);
#3080=ORIENTED_EDGE('',*,*,#2152,.F.);
#3081=ORIENTED_EDGE('',*,*,#2155,.F.);
#3082=ORIENTED_EDGE('',*,*,#2158,.T.);
#3083=ORIENTED_EDGE('',*,*,#2161,.F.);
#3084=ORIENTED_EDGE('',*,*,#2164,.F.);
#3085=ORIENTED_EDGE('',*,*,#2167,.F.);
#3086=ORIENTED_EDGE('',*,*,#2170,.T.);
#3087=ORIENTED_EDGE('',*,*,#2173,.F.);
#3088=ORIENTED_EDGE('',*,*,#2176,.F.);
#3089=ORIENTED_EDGE('',*,*,#2179,.F.);
#3090=ORIENTED_EDGE('',*,*,#2182,.F.);
#3091=ORIENTED_EDGE('',*,*,#2185,.T.);
#3092=ORIENTED_EDGE('',*,*,#2188,.F.);
#3093=ORIENTED_EDGE('',*,*,#2191,.T.);
#3094=ORIENTED_EDGE('',*,*,#2194,.F.);
#3095=ORIENTED_EDGE('',*,*,#2197,.F.);
#3096=ORIENTED_EDGE('',*,*,#2200,.F.);
#3097=ORIENTED_EDGE('',*,*,#2203,.T.);
#3098=ORIENTED_EDGE('',*,*,#2026,.T.);
#3099=ORIENTED_EDGE('',*,*,#2004,.T.);
#3100=ORIENTED_EDGE('',*,*,#2008,.T.);
#3101=ORIENTED_EDGE('',*,*,#2011,.T.);
#3102=ORIENTED_EDGE('',*,*,#2014,.T.);
#3103=ORIENTED_EDGE('',*,*,#2017,.T.);
#3104=ORIENTED_EDGE('',*,*,#2020,.T.);
#3105=ORIENTED_EDGE('',*,*,#2023,.T.);
#3106=ORIENTED_EDGE('',*,*,#2002,.T.);
#3107=ORIENTED_EDGE('',*,*,#1980,.T.);
#3108=ORIENTED_EDGE('',*,*,#1984,.T.);
#3109=ORIENTED_EDGE('',*,*,#1987,.T.);
#3110=ORIENTED_EDGE('',*,*,#1990,.T.);
#3111=ORIENTED_EDGE('',*,*,#1993,.T.);
#3112=ORIENTED_EDGE('',*,*,#1996,.T.);
#3113=ORIENTED_EDGE('',*,*,#1999,.T.);
#3114=ORIENTED_EDGE('',*,*,#1978,.T.);
#3115=ORIENTED_EDGE('',*,*,#1956,.T.);
#3116=ORIENTED_EDGE('',*,*,#1960,.T.);
#3117=ORIENTED_EDGE('',*,*,#1963,.T.);
#3118=ORIENTED_EDGE('',*,*,#1966,.T.);
#3119=ORIENTED_EDGE('',*,*,#1969,.T.);
#3120=ORIENTED_EDGE('',*,*,#1972,.T.);
#3121=ORIENTED_EDGE('',*,*,#1975,.T.);
#3122=ORIENTED_EDGE('',*,*,#1954,.T.);
#3123=ORIENTED_EDGE('',*,*,#1932,.T.);
#3124=ORIENTED_EDGE('',*,*,#1936,.T.);
#3125=ORIENTED_EDGE('',*,*,#1939,.T.);
#3126=ORIENTED_EDGE('',*,*,#1942,.T.);
#3127=ORIENTED_EDGE('',*,*,#1945,.T.);
#3128=ORIENTED_EDGE('',*,*,#1948,.T.);
#3129=ORIENTED_EDGE('',*,*,#1951,.T.);
#3130=ORIENTED_EDGE('',*,*,#1930,.T.);
#3131=ORIENTED_EDGE('',*,*,#1908,.T.);
#3132=ORIENTED_EDGE('',*,*,#1912,.T.);
#3133=ORIENTED_EDGE('',*,*,#1915,.T.);
#3134=ORIENTED_EDGE('',*,*,#1918,.T.);
#3135=ORIENTED_EDGE('',*,*,#1921,.T.);
#3136=ORIENTED_EDGE('',*,*,#1924,.T.);
#3137=ORIENTED_EDGE('',*,*,#1927,.T.);
#3138=ORIENTED_EDGE('',*,*,#1906,.F.);
#3139=ORIENTED_EDGE('',*,*,#1884,.T.);
#3140=ORIENTED_EDGE('',*,*,#1888,.F.);
#3141=ORIENTED_EDGE('',*,*,#1891,.T.);
#3142=ORIENTED_EDGE('',*,*,#1894,.F.);
#3143=ORIENTED_EDGE('',*,*,#1897,.T.);
#3144=ORIENTED_EDGE('',*,*,#1900,.F.);
#3145=ORIENTED_EDGE('',*,*,#1903,.T.);
#3146=ORIENTED_EDGE('',*,*,#1882,.F.);
#3147=ORIENTED_EDGE('',*,*,#1860,.T.);
#3148=ORIENTED_EDGE('',*,*,#1864,.F.);
#3149=ORIENTED_EDGE('',*,*,#1867,.T.);
#3150=ORIENTED_EDGE('',*,*,#1870,.F.);
#3151=ORIENTED_EDGE('',*,*,#1873,.T.);
#3152=ORIENTED_EDGE('',*,*,#1876,.F.);
#3153=ORIENTED_EDGE('',*,*,#1879,.T.);
#3154=ORIENTED_EDGE('',*,*,#1858,.F.);
#3155=ORIENTED_EDGE('',*,*,#1836,.T.);
#3156=ORIENTED_EDGE('',*,*,#1840,.F.);
#3157=ORIENTED_EDGE('',*,*,#1843,.T.);
#3158=ORIENTED_EDGE('',*,*,#1846,.F.);
#3159=ORIENTED_EDGE('',*,*,#1849,.T.);
#3160=ORIENTED_EDGE('',*,*,#1852,.F.);
#3161=ORIENTED_EDGE('',*,*,#1855,.T.);
#3162=ORIENTED_EDGE('',*,*,#1835,.T.);
#3163=ORIENTED_EDGE('',*,*,#1832,.T.);
#3164=ORIENTED_EDGE('',*,*,#1829,.T.);
#3165=ORIENTED_EDGE('',*,*,#1826,.T.);
#3166=ORIENTED_EDGE('',*,*,#1823,.T.);
#3167=ORIENTED_EDGE('',*,*,#1820,.T.);
#3168=ORIENTED_EDGE('',*,*,#1817,.T.);
#3169=ORIENTED_EDGE('',*,*,#1814,.T.);
#3170=ORIENTED_EDGE('',*,*,#1811,.T.);
#3171=ORIENTED_EDGE('',*,*,#1808,.T.);
#3172=ORIENTED_EDGE('',*,*,#1805,.T.);
#3173=ORIENTED_EDGE('',*,*,#1802,.T.);
#3174=ORIENTED_EDGE('',*,*,#1799,.T.);
#3175=ORIENTED_EDGE('',*,*,#1796,.T.);
#3176=ORIENTED_EDGE('',*,*,#1793,.T.);
#3177=ORIENTED_EDGE('',*,*,#1790,.T.);
#3178=ORIENTED_EDGE('',*,*,#1787,.T.);
#3179=ORIENTED_EDGE('',*,*,#1784,.T.);
#3180=ORIENTED_EDGE('',*,*,#1781,.T.);
#3181=ORIENTED_EDGE('',*,*,#1778,.T.);
#3182=ORIENTED_EDGE('',*,*,#1775,.T.);
#3183=ORIENTED_EDGE('',*,*,#1772,.T.);
#3184=ORIENTED_EDGE('',*,*,#1769,.T.);
#3185=ORIENTED_EDGE('',*,*,#1766,.T.);
#3186=ORIENTED_EDGE('',*,*,#1763,.T.);
#3187=ORIENTED_EDGE('',*,*,#1760,.T.);
#3188=ORIENTED_EDGE('',*,*,#1757,.T.);
#3189=ORIENTED_EDGE('',*,*,#1754,.T.);
#3190=ORIENTED_EDGE('',*,*,#1751,.T.);
#3191=ORIENTED_EDGE('',*,*,#1748,.T.);
#3192=ORIENTED_EDGE('',*,*,#1745,.T.);
#3193=ORIENTED_EDGE('',*,*,#1742,.T.);
#3194=ORIENTED_EDGE('',*,*,#1738,.F.);
#3195=ORIENTED_EDGE('',*,*,#1716,.T.);
#3196=ORIENTED_EDGE('',*,*,#1720,.F.);
#3197=ORIENTED_EDGE('',*,*,#1723,.T.);
#3198=ORIENTED_EDGE('',*,*,#1726,.F.);
#3199=ORIENTED_EDGE('',*,*,#1729,.T.);
#3200=ORIENTED_EDGE('',*,*,#1732,.F.);
#3201=ORIENTED_EDGE('',*,*,#1735,.T.);
#3202=ORIENTED_EDGE('',*,*,#1715,.T.);
#3203=ORIENTED_EDGE('',*,*,#1712,.T.);
#3204=CYLINDRICAL_SURFACE('',#3489,2.5175);
#3205=CYLINDRICAL_SURFACE('',#3492,3.38975);
#3206=CYLINDRICAL_SURFACE('',#3495,2.);
#3207=CYLINDRICAL_SURFACE('',#3499,1.99999999999999);
#3208=CYLINDRICAL_SURFACE('',#3503,1.99999999999999);
#3209=CYLINDRICAL_SURFACE('',#3507,1.99999999999999);
#3210=CYLINDRICAL_SURFACE('',#3511,3.38975);
#3211=CYLINDRICAL_SURFACE('',#3514,3.38975);
#3212=CYLINDRICAL_SURFACE('',#3517,2.5175);
#3213=CYLINDRICAL_SURFACE('',#3520,2.5175);
#3214=CYLINDRICAL_SURFACE('',#3523,2.5175);
#3215=CYLINDRICAL_SURFACE('',#3526,1.666);
#3216=CYLINDRICAL_SURFACE('',#3529,1.666);
#3217=CYLINDRICAL_SURFACE('',#3532,1.666);
#3218=CYLINDRICAL_SURFACE('',#3535,2.5175);
#3219=CYLINDRICAL_SURFACE('',#3538,2.5175);
#3220=CYLINDRICAL_SURFACE('',#3541,2.5175);
#3221=CYLINDRICAL_SURFACE('',#3544,2.5175);
#3222=CYLINDRICAL_SURFACE('',#3547,2.5175);
#3223=CYLINDRICAL_SURFACE('',#3550,2.5175);
#3224=CYLINDRICAL_SURFACE('',#3553,2.5175);
#3225=CYLINDRICAL_SURFACE('',#3556,1.666);
#3226=CYLINDRICAL_SURFACE('',#3559,1.666);
#3227=CYLINDRICAL_SURFACE('',#3562,1.666);
#3228=CYLINDRICAL_SURFACE('',#3565,1.666);
#3229=CYLINDRICAL_SURFACE('',#3568,1.666);
#3230=CYLINDRICAL_SURFACE('',#3571,1.666);
#3231=CYLINDRICAL_SURFACE('',#3574,2.5175);
#3232=CYLINDRICAL_SURFACE('',#3577,2.5175);
#3233=CYLINDRICAL_SURFACE('',#3580,2.5175);
#3234=CYLINDRICAL_SURFACE('',#3583,2.5175);
#3235=CYLINDRICAL_SURFACE('',#3586,2.5175);
#3236=CYLINDRICAL_SURFACE('',#3589,2.5175);
#3237=CYLINDRICAL_SURFACE('',#3592,2.5175);
#3238=CYLINDRICAL_SURFACE('',#3595,2.5175);
#3239=CYLINDRICAL_SURFACE('',#3598,2.5175);
#3240=CYLINDRICAL_SURFACE('',#3601,7.00000000000003);
#3241=CYLINDRICAL_SURFACE('',#3604,6.99999999999999);
#3242=CYLINDRICAL_SURFACE('',#3607,1.99999999999999);
#3243=CYLINDRICAL_SURFACE('',#3611,1.99999999999999);
#3244=CYLINDRICAL_SURFACE('',#3615,1.99999999999998);
#3245=CYLINDRICAL_SURFACE('',#3619,2.0000000000001);
#3246=CYLINDRICAL_SURFACE('',#3623,1.99999999999999);
#3247=CYLINDRICAL_SURFACE('',#3627,2.00000000000003);
#3248=CYLINDRICAL_SURFACE('',#3631,1.99999999999998);
#3249=CYLINDRICAL_SURFACE('',#3635,2.00000000000003);
#3250=CYLINDRICAL_SURFACE('',#3639,1.99999999999999);
#3251=CYLINDRICAL_SURFACE('',#3643,2.00000000000003);
#3252=CYLINDRICAL_SURFACE('',#3647,1.99999999999998);
#3253=CYLINDRICAL_SURFACE('',#3651,2.00000000000003);
#3254=CYLINDRICAL_SURFACE('',#3656,1.99999999999996);
#3255=CYLINDRICAL_SURFACE('',#3660,1.99999999999999);
#3256=CYLINDRICAL_SURFACE('',#3664,1.99999999999989);
#3257=CYLINDRICAL_SURFACE('',#3668,2.0000000000001);
#3258=CYLINDRICAL_SURFACE('',#3671,1.99999999999996);
#3259=CYLINDRICAL_SURFACE('',#3675,1.99999999999999);
#3260=CYLINDRICAL_SURFACE('',#3679,1.99999999999989);
#3261=CYLINDRICAL_SURFACE('',#3683,2.0000000000001);
#3262=CYLINDRICAL_SURFACE('',#3687,1.99999999999989);
#3263=CYLINDRICAL_SURFACE('',#3691,2.0000000000001);
#3264=CYLINDRICAL_SURFACE('',#3695,1.99999999999996);
#3265=CYLINDRICAL_SURFACE('',#3699,1.99999999999999);
#3266=CYLINDRICAL_SURFACE('',#3703,2.00000000000017);
#3267=CYLINDRICAL_SURFACE('',#3707,1.99999999999999);
#3268=CYLINDRICAL_SURFACE('',#3711,2.00000000000017);
#3269=CYLINDRICAL_SURFACE('',#3715,2.00000000000008);
#3270=CYLINDRICAL_SURFACE('',#3719,1.99999999999989);
#3271=CYLINDRICAL_SURFACE('',#3723,2.0000000000001);
#3272=CYLINDRICAL_SURFACE('',#3727,1.99999999999996);
#3273=CYLINDRICAL_SURFACE('',#3731,1.99999999999999);
#3274=CYLINDRICAL_SURFACE('',#3736,2.0000000000001);
#3275=CYLINDRICAL_SURFACE('',#3739,12.5000000000001);
#3276=CYLINDRICAL_SURFACE('',#3743,56.3341738408199);
#3277=CYLINDRICAL_SURFACE('',#3746,100.000000000001);
#3278=CYLINDRICAL_SURFACE('',#3749,18.0000000000004);
#3279=CYLINDRICAL_SURFACE('',#3753,15.);
#3280=CYLINDRICAL_SURFACE('',#3757,18.);
#3281=CYLINDRICAL_SURFACE('',#3760,1.99999999999999);
#3282=CYLINDRICAL_SURFACE('',#3764,2.00000000000003);
#3283=CYLINDRICAL_SURFACE('',#3768,2.0000000000001);
#3284=CYLINDRICAL_SURFACE('',#3772,2.00000000000003);
#3285=CYLINDRICAL_SURFACE('',#3776,2.00000000000003);
#3286=CYLINDRICAL_SURFACE('',#3780,2.00000000000003);
#3287=CYLINDRICAL_SURFACE('',#3784,2.00000000000003);
#3288=CYLINDRICAL_SURFACE('',#3788,2.00000000000003);
#3289=CYLINDRICAL_SURFACE('',#3792,15.0000000000001);
#3290=CYLINDRICAL_SURFACE('',#3796,1.99999999999996);
#3291=CYLINDRICAL_SURFACE('',#3800,2.00000000000025);
#3292=CYLINDRICAL_SURFACE('',#3804,2.00000000000011);
#3293=CYLINDRICAL_SURFACE('',#3808,2.00000000000003);
#3294=CYLINDRICAL_SURFACE('',#3812,1.99999999999974);
#3295=CYLINDRICAL_SURFACE('',#3816,2.00000000000003);
#3296=CYLINDRICAL_SURFACE('',#3820,1.99999999999999);
#3297=CYLINDRICAL_SURFACE('',#3824,1.99999999999999);
#3298=CYLINDRICAL_SURFACE('',#3828,50.0000000000001);
#3299=CYLINDRICAL_SURFACE('',#3832,100.);
#3300=CYLINDRICAL_SURFACE('',#3835,100.000000000001);
#3301=CYLINDRICAL_SURFACE('',#3839,1.99999999999999);
#3302=CYLINDRICAL_SURFACE('',#3843,30.0000000000006);
#3303=CYLINDRICAL_SURFACE('',#3847,70.0000000000005);
#3304=CYLINDRICAL_SURFACE('',#3850,71.0920157219637);
#3305=CYLINDRICAL_SURFACE('',#3854,15.);
#3306=CYLINDRICAL_SURFACE('',#3858,12.);
#3307=ADVANCED_FACE('',(#166),#3204,.F.);
#3308=ADVANCED_FACE('',(#167),#3205,.F.);
#3309=ADVANCED_FACE('',(#168),#3206,.F.);
#3310=ADVANCED_FACE('',(#169),#101,.T.);
#3311=ADVANCED_FACE('',(#170),#3207,.F.);
#3312=ADVANCED_FACE('',(#171),#102,.T.);
#3313=ADVANCED_FACE('',(#172),#3208,.F.);
#3314=ADVANCED_FACE('',(#173),#103,.T.);
#3315=ADVANCED_FACE('',(#174),#3209,.F.);
#3316=ADVANCED_FACE('',(#175),#104,.T.);
#3317=ADVANCED_FACE('',(#176),#3210,.F.);
#3318=ADVANCED_FACE('',(#177),#3211,.F.);
#3319=ADVANCED_FACE('',(#178),#3212,.F.);
#3320=ADVANCED_FACE('',(#179),#3213,.F.);
#3321=ADVANCED_FACE('',(#180),#3214,.F.);
#3322=ADVANCED_FACE('',(#181),#3215,.F.);
#3323=ADVANCED_FACE('',(#182),#3216,.F.);
#3324=ADVANCED_FACE('',(#183),#3217,.F.);
#3325=ADVANCED_FACE('',(#184),#3218,.F.);
#3326=ADVANCED_FACE('',(#185),#3219,.F.);
#3327=ADVANCED_FACE('',(#186),#3220,.F.);
#3328=ADVANCED_FACE('',(#187),#3221,.F.);
#3329=ADVANCED_FACE('',(#188),#3222,.F.);
#3330=ADVANCED_FACE('',(#189),#3223,.F.);
#3331=ADVANCED_FACE('',(#190),#3224,.F.);
#3332=ADVANCED_FACE('',(#191),#3225,.F.);
#3333=ADVANCED_FACE('',(#192),#3226,.F.);
#3334=ADVANCED_FACE('',(#193),#3227,.F.);
#3335=ADVANCED_FACE('',(#194),#3228,.F.);
#3336=ADVANCED_FACE('',(#195),#3229,.F.);
#3337=ADVANCED_FACE('',(#196),#3230,.F.);
#3338=ADVANCED_FACE('',(#197),#3231,.F.);
#3339=ADVANCED_FACE('',(#198),#3232,.F.);
#3340=ADVANCED_FACE('',(#199),#3233,.F.);
#3341=ADVANCED_FACE('',(#200),#3234,.F.);
#3342=ADVANCED_FACE('',(#201),#3235,.F.);
#3343=ADVANCED_FACE('',(#202),#3236,.F.);
#3344=ADVANCED_FACE('',(#203),#3237,.F.);
#3345=ADVANCED_FACE('',(#204),#3238,.F.);
#3346=ADVANCED_FACE('',(#205),#3239,.F.);
#3347=ADVANCED_FACE('',(#206),#3240,.F.);
#3348=ADVANCED_FACE('',(#207),#3241,.F.);
#3349=ADVANCED_FACE('',(#208),#3242,.F.);
#3350=ADVANCED_FACE('',(#209),#105,.T.);
#3351=ADVANCED_FACE('',(#210),#3243,.F.);
#3352=ADVANCED_FACE('',(#211),#106,.T.);
#3353=ADVANCED_FACE('',(#212),#3244,.F.);
#3354=ADVANCED_FACE('',(#213),#107,.T.);
#3355=ADVANCED_FACE('',(#214),#3245,.F.);
#3356=ADVANCED_FACE('',(#215),#108,.T.);
#3357=ADVANCED_FACE('',(#216),#3246,.F.);
#3358=ADVANCED_FACE('',(#217),#109,.T.);
#3359=ADVANCED_FACE('',(#218),#3247,.F.);
#3360=ADVANCED_FACE('',(#219),#110,.T.);
#3361=ADVANCED_FACE('',(#220),#3248,.F.);
#3362=ADVANCED_FACE('',(#221),#111,.T.);
#3363=ADVANCED_FACE('',(#222),#3249,.F.);
#3364=ADVANCED_FACE('',(#223),#112,.T.);
#3365=ADVANCED_FACE('',(#224),#3250,.F.);
#3366=ADVANCED_FACE('',(#225),#113,.T.);
#3367=ADVANCED_FACE('',(#226),#3251,.F.);
#3368=ADVANCED_FACE('',(#227),#114,.T.);
#3369=ADVANCED_FACE('',(#228),#3252,.F.);
#3370=ADVANCED_FACE('',(#229),#115,.T.);
#3371=ADVANCED_FACE('',(#230),#3253,.F.);
#3372=ADVANCED_FACE('',(#231),#116,.T.);
#3373=ADVANCED_FACE('',(#232),#117,.T.);
#3374=ADVANCED_FACE('',(#233),#3254,.F.);
#3375=ADVANCED_FACE('',(#234),#118,.T.);
#3376=ADVANCED_FACE('',(#235),#3255,.F.);
#3377=ADVANCED_FACE('',(#236),#119,.T.);
#3378=ADVANCED_FACE('',(#237),#3256,.F.);
#3379=ADVANCED_FACE('',(#238),#120,.T.);
#3380=ADVANCED_FACE('',(#239),#3257,.F.);
#3381=ADVANCED_FACE('',(#240),#3258,.F.);
#3382=ADVANCED_FACE('',(#241),#121,.T.);
#3383=ADVANCED_FACE('',(#242),#3259,.F.);
#3384=ADVANCED_FACE('',(#243),#122,.T.);
#3385=ADVANCED_FACE('',(#244),#3260,.F.);
#3386=ADVANCED_FACE('',(#245),#123,.T.);
#3387=ADVANCED_FACE('',(#246),#3261,.F.);
#3388=ADVANCED_FACE('',(#247),#124,.T.);
#3389=ADVANCED_FACE('',(#248),#3262,.F.);
#3390=ADVANCED_FACE('',(#249),#125,.T.);
#3391=ADVANCED_FACE('',(#250),#3263,.F.);
#3392=ADVANCED_FACE('',(#251),#126,.T.);
#3393=ADVANCED_FACE('',(#252),#3264,.F.);
#3394=ADVANCED_FACE('',(#253),#127,.T.);
#3395=ADVANCED_FACE('',(#254),#3265,.F.);
#3396=ADVANCED_FACE('',(#255),#128,.T.);
#3397=ADVANCED_FACE('',(#256),#3266,.F.);
#3398=ADVANCED_FACE('',(#257),#129,.T.);
#3399=ADVANCED_FACE('',(#258),#3267,.F.);
#3400=ADVANCED_FACE('',(#259),#130,.T.);
#3401=ADVANCED_FACE('',(#260),#3268,.F.);
#3402=ADVANCED_FACE('',(#261),#131,.T.);
#3403=ADVANCED_FACE('',(#262),#3269,.F.);
#3404=ADVANCED_FACE('',(#263),#132,.T.);
#3405=ADVANCED_FACE('',(#264),#3270,.F.);
#3406=ADVANCED_FACE('',(#265),#133,.T.);
#3407=ADVANCED_FACE('',(#266),#3271,.F.);
#3408=ADVANCED_FACE('',(#267),#134,.T.);
#3409=ADVANCED_FACE('',(#268),#3272,.F.);
#3410=ADVANCED_FACE('',(#269),#135,.T.);
#3411=ADVANCED_FACE('',(#270),#3273,.F.);
#3412=ADVANCED_FACE('',(#271),#136,.T.);
#3413=ADVANCED_FACE('',(#272),#137,.T.);
#3414=ADVANCED_FACE('',(#273),#3274,.F.);
#3415=ADVANCED_FACE('',(#274),#3275,.F.);
#3416=ADVANCED_FACE('',(#275),#138,.T.);
#3417=ADVANCED_FACE('',(#276),#3276,.F.);
#3418=ADVANCED_FACE('',(#277),#3277,.T.);
#3419=ADVANCED_FACE('',(#278),#3278,.F.);
#3420=ADVANCED_FACE('',(#279),#139,.T.);
#3421=ADVANCED_FACE('',(#280),#3279,.T.);
#3422=ADVANCED_FACE('',(#281),#140,.T.);
#3423=ADVANCED_FACE('',(#282),#3280,.F.);
#3424=ADVANCED_FACE('',(#283),#3281,.T.);
#3425=ADVANCED_FACE('',(#284),#141,.T.);
#3426=ADVANCED_FACE('',(#285),#3282,.F.);
#3427=ADVANCED_FACE('',(#286),#142,.T.);
#3428=ADVANCED_FACE('',(#287),#3283,.F.);
#3429=ADVANCED_FACE('',(#288),#143,.T.);
#3430=ADVANCED_FACE('',(#289),#3284,.T.);
#3431=ADVANCED_FACE('',(#290),#144,.T.);
#3432=ADVANCED_FACE('',(#291),#3285,.T.);
#3433=ADVANCED_FACE('',(#292),#145,.T.);
#3434=ADVANCED_FACE('',(#293),#3286,.T.);
#3435=ADVANCED_FACE('',(#294),#146,.T.);
#3436=ADVANCED_FACE('',(#295),#3287,.F.);
#3437=ADVANCED_FACE('',(#296),#147,.T.);
#3438=ADVANCED_FACE('',(#297),#3288,.F.);
#3439=ADVANCED_FACE('',(#298),#148,.T.);
#3440=ADVANCED_FACE('',(#299),#3289,.T.);
#3441=ADVANCED_FACE('',(#300),#149,.T.);
#3442=ADVANCED_FACE('',(#301),#3290,.F.);
#3443=ADVANCED_FACE('',(#302),#150,.T.);
#3444=ADVANCED_FACE('',(#303),#3291,.T.);
#3445=ADVANCED_FACE('',(#304),#151,.T.);
#3446=ADVANCED_FACE('',(#305),#3292,.F.);
#3447=ADVANCED_FACE('',(#306),#152,.T.);
#3448=ADVANCED_FACE('',(#307),#3293,.T.);
#3449=ADVANCED_FACE('',(#308),#153,.T.);
#3450=ADVANCED_FACE('',(#309),#3294,.F.);
#3451=ADVANCED_FACE('',(#310),#154,.T.);
#3452=ADVANCED_FACE('',(#311),#3295,.T.);
#3453=ADVANCED_FACE('',(#312),#155,.T.);
#3454=ADVANCED_FACE('',(#313),#3296,.T.);
#3455=ADVANCED_FACE('',(#314),#156,.T.);
#3456=ADVANCED_FACE('',(#315),#3297,.F.);
#3457=ADVANCED_FACE('',(#316),#157,.T.);
#3458=ADVANCED_FACE('',(#317),#3298,.T.);
#3459=ADVANCED_FACE('',(#318),#158,.T.);
#3460=ADVANCED_FACE('',(#319),#3299,.F.);
#3461=ADVANCED_FACE('',(#320),#3300,.T.);
#3462=ADVANCED_FACE('',(#321),#159,.T.);
#3463=ADVANCED_FACE('',(#322),#3301,.T.);
#3464=ADVANCED_FACE('',(#323),#160,.T.);
#3465=ADVANCED_FACE('',(#324),#3302,.F.);
#3466=ADVANCED_FACE('',(#325),#161,.T.);
#3467=ADVANCED_FACE('',(#326),#3303,.F.);
#3468=ADVANCED_FACE('',(#327),#3304,.T.);
#3469=ADVANCED_FACE('',(#328),#162,.T.);
#3470=ADVANCED_FACE('',(#329),#3305,.T.);
#3471=ADVANCED_FACE('',(#330),#163,.T.);
#3472=ADVANCED_FACE('',(#331),#3306,.T.);
#3473=ADVANCED_FACE('',(#332,#15,#16,#17,#18,#19,#20,#21,#22,#23,#24,#25,
#26,#27,#28,#29,#30,#31,#32,#33,#34,#35,#36,#37,#38,#39,#40,#41,#42,#43,
#44,#45,#46,#47,#48,#49,#50,#51,#52,#53,#54,#55,#56,#57),#164,.T.);
#3474=ADVANCED_FACE('',(#333,#58,#59,#60,#61,#62,#63,#64,#65,#66,#67,#68,
#69,#70,#71,#72,#73,#74,#75,#76,#77,#78,#79,#80,#81,#82,#83,#84,#85,#86,
#87,#88,#89,#90,#91,#92,#93,#94,#95,#96,#97,#98,#99,#100),#165,.F.);
#3475=CLOSED_SHELL('',(#3307,#3308,#3309,#3310,#3311,#3312,#3313,#3314,
#3315,#3316,#3317,#3318,#3319,#3320,#3321,#3322,#3323,#3324,#3325,#3326,
#3327,#3328,#3329,#3330,#3331,#3332,#3333,#3334,#3335,#3336,#3337,#3338,
#3339,#3340,#3341,#3342,#3343,#3344,#3345,#3346,#3347,#3348,#3349,#3350,
#3351,#3352,#3353,#3354,#3355,#3356,#3357,#3358,#3359,#3360,#3361,#3362,
#3363,#3364,#3365,#3366,#3367,#3368,#3369,#3370,#3371,#3372,#3373,#3374,
#3375,#3376,#3377,#3378,#3379,#3380,#3381,#3382,#3383,#3384,#3385,#3386,
#3387,#3388,#3389,#3390,#3391,#3392,#3393,#3394,#3395,#3396,#3397,#3398,
#3399,#3400,#3401,#3402,#3403,#3404,#3405,#3406,#3407,#3408,#3409,#3410,
#3411,#3412,#3413,#3414,#3415,#3416,#3417,#3418,#3419,#3420,#3421,#3422,
#3423,#3424,#3425,#3426,#3427,#3428,#3429,#3430,#3431,#3432,#3433,#3434,
#3435,#3436,#3437,#3438,#3439,#3440,#3441,#3442,#3443,#3444,#3445,#3446,
#3447,#3448,#3449,#3450,#3451,#3452,#3453,#3454,#3455,#3456,#3457,#3458,
#3459,#3460,#3461,#3462,#3463,#3464,#3465,#3466,#3467,#3468,#3469,#3470,
#3471,#3472,#3473,#3474));
#3476=DERIVED_UNIT_ELEMENT(#3478,1.);
#3477=DERIVED_UNIT_ELEMENT(#5909,-3.);
#3478=(
MASS_UNIT()
NAMED_UNIT(*)
SI_UNIT(.KILO.,.GRAM.)
);
#3479=DERIVED_UNIT((#3476,#3477));
#3480=MEASURE_REPRESENTATION_ITEM('density measure',
POSITIVE_RATIO_MEASURE(7850.),#3479);
#3481=PROPERTY_DEFINITION_REPRESENTATION(#3486,#3483);
#3482=PROPERTY_DEFINITION_REPRESENTATION(#3487,#3484);
#3483=REPRESENTATION('material name',(#3485),#5906);
#3484=REPRESENTATION('density',(#3480),#5906);
#3485=DESCRIPTIVE_REPRESENTATION_ITEM('Steel','Steel');
#3486=PROPERTY_DEFINITION('material property','material name',#5916);
#3487=PROPERTY_DEFINITION('material property','density of part',#5916);
#3488=AXIS2_PLACEMENT_3D('placement',#4905,#3863,#3864);
#3489=AXIS2_PLACEMENT_3D('',#4906,#3865,#3866);
#3490=AXIS2_PLACEMENT_3D('',#4908,#3867,#3868);
#3491=AXIS2_PLACEMENT_3D('',#4911,#3870,#3871);
#3492=AXIS2_PLACEMENT_3D('',#4912,#3872,#3873);
#3493=AXIS2_PLACEMENT_3D('',#4914,#3874,#3875);
#3494=AXIS2_PLACEMENT_3D('',#4917,#3877,#3878);
#3495=AXIS2_PLACEMENT_3D('',#4918,#3879,#3880);
#3496=AXIS2_PLACEMENT_3D('',#4921,#3881,#3882);
#3497=AXIS2_PLACEMENT_3D('',#4925,#3884,#3885);
#3498=AXIS2_PLACEMENT_3D('',#4927,#3887,#3888);
#3499=AXIS2_PLACEMENT_3D('',#4933,#3892,#3893);
#3500=AXIS2_PLACEMENT_3D('',#4935,#3894,#3895);
#3501=AXIS2_PLACEMENT_3D('',#4937,#3896,#3897);
#3502=AXIS2_PLACEMENT_3D('',#4939,#3899,#3900);
#3503=AXIS2_PLACEMENT_3D('',#4945,#3904,#3905);
#3504=AXIS2_PLACEMENT_3D('',#4947,#3906,#3907);
#3505=AXIS2_PLACEMENT_3D('',#4949,#3908,#3909);
#3506=AXIS2_PLACEMENT_3D('',#4951,#3911,#3912);
#3507=AXIS2_PLACEMENT_3D('',#4957,#3916,#3917);
#3508=AXIS2_PLACEMENT_3D('',#4959,#3918,#3919);
#3509=AXIS2_PLACEMENT_3D('',#4961,#3920,#3921);
#3510=AXIS2_PLACEMENT_3D('',#4963,#3923,#3924);
#3511=AXIS2_PLACEMENT_3D('',#4966,#3927,#3928);
#3512=AXIS2_PLACEMENT_3D('',#4968,#3929,#3930);
#3513=AXIS2_PLACEMENT_3D('',#4971,#3932,#3933);
#3514=AXIS2_PLACEMENT_3D('',#4972,#3934,#3935);
#3515=AXIS2_PLACEMENT_3D('',#4974,#3936,#3937);
#3516=AXIS2_PLACEMENT_3D('',#4977,#3939,#3940);
#3517=AXIS2_PLACEMENT_3D('',#4978,#3941,#3942);
#3518=AXIS2_PLACEMENT_3D('',#4980,#3943,#3944);
#3519=AXIS2_PLACEMENT_3D('',#4983,#3946,#3947);
#3520=AXIS2_PLACEMENT_3D('',#4984,#3948,#3949);
#3521=AXIS2_PLACEMENT_3D('',#4986,#3950,#3951);
#3522=AXIS2_PLACEMENT_3D('',#4989,#3953,#3954);
#3523=AXIS2_PLACEMENT_3D('',#4990,#3955,#3956);
#3524=AXIS2_PLACEMENT_3D('',#4992,#3957,#3958);
#3525=AXIS2_PLACEMENT_3D('',#4995,#3960,#3961);
#3526=AXIS2_PLACEMENT_3D('',#4996,#3962,#3963);
#3527=AXIS2_PLACEMENT_3D('',#4998,#3964,#3965);
#3528=AXIS2_PLACEMENT_3D('',#5001,#3967,#3968);
#3529=AXIS2_PLACEMENT_3D('',#5002,#3969,#3970);
#3530=AXIS2_PLACEMENT_3D('',#5004,#3971,#3972);
#3531=AXIS2_PLACEMENT_3D('',#5007,#3974,#3975);
#3532=AXIS2_PLACEMENT_3D('',#5008,#3976,#3977);
#3533=AXIS2_PLACEMENT_3D('',#5010,#3978,#3979);
#3534=AXIS2_PLACEMENT_3D('',#5013,#3981,#3982);
#3535=AXIS2_PLACEMENT_3D('',#5014,#3983,#3984);
#3536=AXIS2_PLACEMENT_3D('',#5016,#3985,#3986);
#3537=AXIS2_PLACEMENT_3D('',#5019,#3988,#3989);
#3538=AXIS2_PLACEMENT_3D('',#5020,#3990,#3991);
#3539=AXIS2_PLACEMENT_3D('',#5022,#3992,#3993);
#3540=AXIS2_PLACEMENT_3D('',#5025,#3995,#3996);
#3541=AXIS2_PLACEMENT_3D('',#5026,#3997,#3998);
#3542=AXIS2_PLACEMENT_3D('',#5028,#3999,#4000);
#3543=AXIS2_PLACEMENT_3D('',#5031,#4002,#4003);
#3544=AXIS2_PLACEMENT_3D('',#5032,#4004,#4005);
#3545=AXIS2_PLACEMENT_3D('',#5034,#4006,#4007);
#3546=AXIS2_PLACEMENT_3D('',#5037,#4009,#4010);
#3547=AXIS2_PLACEMENT_3D('',#5038,#4011,#4012);
#3548=AXIS2_PLACEMENT_3D('',#5040,#4013,#4014);
#3549=AXIS2_PLACEMENT_3D('',#5043,#4016,#4017);
#3550=AXIS2_PLACEMENT_3D('',#5044,#4018,#4019);
#3551=AXIS2_PLACEMENT_3D('',#5046,#4020,#4021);
#3552=AXIS2_PLACEMENT_3D('',#5049,#4023,#4024);
#3553=AXIS2_PLACEMENT_3D('',#5050,#4025,#4026);
#3554=AXIS2_PLACEMENT_3D('',#5052,#4027,#4028);
#3555=AXIS2_PLACEMENT_3D('',#5055,#4030,#4031);
#3556=AXIS2_PLACEMENT_3D('',#5056,#4032,#4033);
#3557=AXIS2_PLACEMENT_3D('',#5058,#4034,#4035);
#3558=AXIS2_PLACEMENT_3D('',#5061,#4037,#4038);
#3559=AXIS2_PLACEMENT_3D('',#5062,#4039,#4040);
#3560=AXIS2_PLACEMENT_3D('',#5064,#4041,#4042);
#3561=AXIS2_PLACEMENT_3D('',#5067,#4044,#4045);
#3562=AXIS2_PLACEMENT_3D('',#5068,#4046,#4047);
#3563=AXIS2_PLACEMENT_3D('',#5070,#4048,#4049);
#3564=AXIS2_PLACEMENT_3D('',#5073,#4051,#4052);
#3565=AXIS2_PLACEMENT_3D('',#5074,#4053,#4054);
#3566=AXIS2_PLACEMENT_3D('',#5076,#4055,#4056);
#3567=AXIS2_PLACEMENT_3D('',#5079,#4058,#4059);
#3568=AXIS2_PLACEMENT_3D('',#5080,#4060,#4061);
#3569=AXIS2_PLACEMENT_3D('',#5082,#4062,#4063);
#3570=AXIS2_PLACEMENT_3D('',#5085,#4065,#4066);
#3571=AXIS2_PLACEMENT_3D('',#5086,#4067,#4068);
#3572=AXIS2_PLACEMENT_3D('',#5088,#4069,#4070);
#3573=AXIS2_PLACEMENT_3D('',#5091,#4072,#4073);
#3574=AXIS2_PLACEMENT_3D('',#5092,#4074,#4075);
#3575=AXIS2_PLACEMENT_3D('',#5094,#4076,#4077);
#3576=AXIS2_PLACEMENT_3D('',#5097,#4079,#4080);
#3577=AXIS2_PLACEMENT_3D('',#5098,#4081,#4082);
#3578=AXIS2_PLACEMENT_3D('',#5100,#4083,#4084);
#3579=AXIS2_PLACEMENT_3D('',#5103,#4086,#4087);
#3580=AXIS2_PLACEMENT_3D('',#5104,#4088,#4089);
#3581=AXIS2_PLACEMENT_3D('',#5106,#4090,#4091);
#3582=AXIS2_PLACEMENT_3D('',#5109,#4093,#4094);
#3583=AXIS2_PLACEMENT_3D('',#5110,#4095,#4096);
#3584=AXIS2_PLACEMENT_3D('',#5112,#4097,#4098);
#3585=AXIS2_PLACEMENT_3D('',#5115,#4100,#4101);
#3586=AXIS2_PLACEMENT_3D('',#5116,#4102,#4103);
#3587=AXIS2_PLACEMENT_3D('',#5118,#4104,#4105);
#3588=AXIS2_PLACEMENT_3D('',#5121,#4107,#4108);
#3589=AXIS2_PLACEMENT_3D('',#5122,#4109,#4110);
#3590=AXIS2_PLACEMENT_3D('',#5124,#4111,#4112);
#3591=AXIS2_PLACEMENT_3D('',#5127,#4114,#4115);
#3592=AXIS2_PLACEMENT_3D('',#5128,#4116,#4117);
#3593=AXIS2_PLACEMENT_3D('',#5130,#4118,#4119);
#3594=AXIS2_PLACEMENT_3D('',#5133,#4121,#4122);
#3595=AXIS2_PLACEMENT_3D('',#5134,#4123,#4124);
#3596=AXIS2_PLACEMENT_3D('',#5136,#4125,#4126);
#3597=AXIS2_PLACEMENT_3D('',#5139,#4128,#4129);
#3598=AXIS2_PLACEMENT_3D('',#5140,#4130,#4131);
#3599=AXIS2_PLACEMENT_3D('',#5142,#4132,#4133);
#3600=AXIS2_PLACEMENT_3D('',#5145,#4135,#4136);
#3601=AXIS2_PLACEMENT_3D('',#5146,#4137,#4138);
#3602=AXIS2_PLACEMENT_3D('',#5148,#4139,#4140);
#3603=AXIS2_PLACEMENT_3D('',#5151,#4142,#4143);
#3604=AXIS2_PLACEMENT_3D('',#5152,#4144,#4145);
#3605=AXIS2_PLACEMENT_3D('',#5154,#4146,#4147);
#3606=AXIS2_PLACEMENT_3D('',#5157,#4149,#4150);
#3607=AXIS2_PLACEMENT_3D('',#5158,#4151,#4152);
#3608=AXIS2_PLACEMENT_3D('',#5161,#4153,#4154);
#3609=AXIS2_PLACEMENT_3D('',#5165,#4156,#4157);
#3610=AXIS2_PLACEMENT_3D('',#5167,#4159,#4160);
#3611=AXIS2_PLACEMENT_3D('',#5173,#4164,#4165);
#3612=AXIS2_PLACEMENT_3D('',#5175,#4166,#4167);
#3613=AXIS2_PLACEMENT_3D('',#5177,#4168,#4169);
#3614=AXIS2_PLACEMENT_3D('',#5179,#4171,#4172);
#3615=AXIS2_PLACEMENT_3D('',#5185,#4176,#4177);
#3616=AXIS2_PLACEMENT_3D('',#5187,#4178,#4179);
#3617=AXIS2_PLACEMENT_3D('',#5189,#4180,#4181);
#3618=AXIS2_PLACEMENT_3D('',#5191,#4183,#4184);
#3619=AXIS2_PLACEMENT_3D('',#5197,#4188,#4189);
#3620=AXIS2_PLACEMENT_3D('',#5199,#4190,#4191);
#3621=AXIS2_PLACEMENT_3D('',#5201,#4192,#4193);
#3622=AXIS2_PLACEMENT_3D('',#5203,#4195,#4196);
#3623=AXIS2_PLACEMENT_3D('',#5206,#4199,#4200);
#3624=AXIS2_PLACEMENT_3D('',#5209,#4201,#4202);
#3625=AXIS2_PLACEMENT_3D('',#5213,#4204,#4205);
#3626=AXIS2_PLACEMENT_3D('',#5215,#4207,#4208);
#3627=AXIS2_PLACEMENT_3D('',#5221,#4212,#4213);
#3628=AXIS2_PLACEMENT_3D('',#5223,#4214,#4215);
#3629=AXIS2_PLACEMENT_3D('',#5225,#4216,#4217);
#3630=AXIS2_PLACEMENT_3D('',#5227,#4219,#4220);
#3631=AXIS2_PLACEMENT_3D('',#5233,#4224,#4225);
#3632=AXIS2_PLACEMENT_3D('',#5235,#4226,#4227);
#3633=AXIS2_PLACEMENT_3D('',#5237,#4228,#4229);
#3634=AXIS2_PLACEMENT_3D('',#5239,#4231,#4232);
#3635=AXIS2_PLACEMENT_3D('',#5245,#4236,#4237);
#3636=AXIS2_PLACEMENT_3D('',#5247,#4238,#4239);
#3637=AXIS2_PLACEMENT_3D('',#5249,#4240,#4241);
#3638=AXIS2_PLACEMENT_3D('',#5251,#4243,#4244);
#3639=AXIS2_PLACEMENT_3D('',#5254,#4247,#4248);
#3640=AXIS2_PLACEMENT_3D('',#5257,#4249,#4250);
#3641=AXIS2_PLACEMENT_3D('',#5261,#4252,#4253);
#3642=AXIS2_PLACEMENT_3D('',#5263,#4255,#4256);
#3643=AXIS2_PLACEMENT_3D('',#5269,#4260,#4261);
#3644=AXIS2_PLACEMENT_3D('',#5271,#4262,#4263);
#3645=AXIS2_PLACEMENT_3D('',#5273,#4264,#4265);
#3646=AXIS2_PLACEMENT_3D('',#5275,#4267,#4268);
#3647=AXIS2_PLACEMENT_3D('',#5281,#4272,#4273);
#3648=AXIS2_PLACEMENT_3D('',#5283,#4274,#4275);
#3649=AXIS2_PLACEMENT_3D('',#5285,#4276,#4277);
#3650=AXIS2_PLACEMENT_3D('',#5287,#4279,#4280);
#3651=AXIS2_PLACEMENT_3D('',#5293,#4284,#4285);
#3652=AXIS2_PLACEMENT_3D('',#5295,#4286,#4287);
#3653=AXIS2_PLACEMENT_3D('',#5297,#4288,#4289);
#3654=AXIS2_PLACEMENT_3D('',#5299,#4291,#4292);
#3655=AXIS2_PLACEMENT_3D('',#5302,#4295,#4296);
#3656=AXIS2_PLACEMENT_3D('',#5311,#4301,#4302);
#3657=AXIS2_PLACEMENT_3D('',#5313,#4303,#4304);
#3658=AXIS2_PLACEMENT_3D('',#5315,#4305,#4306);
#3659=AXIS2_PLACEMENT_3D('',#5317,#4308,#4309);
#3660=AXIS2_PLACEMENT_3D('',#5323,#4313,#4314);
#3661=AXIS2_PLACEMENT_3D('',#5325,#4315,#4316);
#3662=AXIS2_PLACEMENT_3D('',#5327,#4317,#4318);
#3663=AXIS2_PLACEMENT_3D('',#5329,#4320,#4321);
#3664=AXIS2_PLACEMENT_3D('',#5335,#4325,#4326);
#3665=AXIS2_PLACEMENT_3D('',#5337,#4327,#4328);
#3666=AXIS2_PLACEMENT_3D('',#5339,#4329,#4330);
#3667=AXIS2_PLACEMENT_3D('',#5341,#4332,#4333);
#3668=AXIS2_PLACEMENT_3D('',#5347,#4337,#4338);
#3669=AXIS2_PLACEMENT_3D('',#5348,#4339,#4340);
#3670=AXIS2_PLACEMENT_3D('',#5349,#4341,#4342);
#3671=AXIS2_PLACEMENT_3D('',#5350,#4343,#4344);
#3672=AXIS2_PLACEMENT_3D('',#5353,#4345,#4346);
#3673=AXIS2_PLACEMENT_3D('',#5357,#4348,#4349);
#3674=AXIS2_PLACEMENT_3D('',#5359,#4351,#4352);
#3675=AXIS2_PLACEMENT_3D('',#5365,#4356,#4357);
#3676=AXIS2_PLACEMENT_3D('',#5367,#4358,#4359);
#3677=AXIS2_PLACEMENT_3D('',#5369,#4360,#4361);
#3678=AXIS2_PLACEMENT_3D('',#5371,#4363,#4364);
#3679=AXIS2_PLACEMENT_3D('',#5377,#4368,#4369);
#3680=AXIS2_PLACEMENT_3D('',#5379,#4370,#4371);
#3681=AXIS2_PLACEMENT_3D('',#5381,#4372,#4373);
#3682=AXIS2_PLACEMENT_3D('',#5383,#4375,#4376);
#3683=AXIS2_PLACEMENT_3D('',#5389,#4380,#4381);
#3684=AXIS2_PLACEMENT_3D('',#5391,#4382,#4383);
#3685=AXIS2_PLACEMENT_3D('',#5393,#4384,#4385);
#3686=AXIS2_PLACEMENT_3D('',#5395,#4387,#4388);
#3687=AXIS2_PLACEMENT_3D('',#5398,#4391,#4392);
#3688=AXIS2_PLACEMENT_3D('',#5401,#4393,#4394);
#3689=AXIS2_PLACEMENT_3D('',#5405,#4396,#4397);
#3690=AXIS2_PLACEMENT_3D('',#5407,#4399,#4400);
#3691=AXIS2_PLACEMENT_3D('',#5413,#4404,#4405);
#3692=AXIS2_PLACEMENT_3D('',#5415,#4406,#4407);
#3693=AXIS2_PLACEMENT_3D('',#5417,#4408,#4409);
#3694=AXIS2_PLACEMENT_3D('',#5419,#4411,#4412);
#3695=AXIS2_PLACEMENT_3D('',#5425,#4416,#4417);
#3696=AXIS2_PLACEMENT_3D('',#5427,#4418,#4419);
#3697=AXIS2_PLACEMENT_3D('',#5429,#4420,#4421);
#3698=AXIS2_PLACEMENT_3D('',#5431,#4423,#4424);
#3699=AXIS2_PLACEMENT_3D('',#5437,#4428,#4429);
#3700=AXIS2_PLACEMENT_3D('',#5439,#4430,#4431);
#3701=AXIS2_PLACEMENT_3D('',#5441,#4432,#4433);
#3702=AXIS2_PLACEMENT_3D('',#5443,#4435,#4436);
#3703=AXIS2_PLACEMENT_3D('',#5446,#4439,#4440);
#3704=AXIS2_PLACEMENT_3D('',#5449,#4441,#4442);
#3705=AXIS2_PLACEMENT_3D('',#5453,#4444,#4445);
#3706=AXIS2_PLACEMENT_3D('',#5455,#4447,#4448);
#3707=AXIS2_PLACEMENT_3D('',#5461,#4452,#4453);
#3708=AXIS2_PLACEMENT_3D('',#5463,#4454,#4455);
#3709=AXIS2_PLACEMENT_3D('',#5465,#4456,#4457);
#3710=AXIS2_PLACEMENT_3D('',#5467,#4459,#4460);
#3711=AXIS2_PLACEMENT_3D('',#5473,#4464,#4465);
#3712=AXIS2_PLACEMENT_3D('',#5475,#4466,#4467);
#3713=AXIS2_PLACEMENT_3D('',#5477,#4468,#4469);
#3714=AXIS2_PLACEMENT_3D('',#5479,#4471,#4472);
#3715=AXIS2_PLACEMENT_3D('',#5485,#4476,#4477);
#3716=AXIS2_PLACEMENT_3D('',#5487,#4478,#4479);
#3717=AXIS2_PLACEMENT_3D('',#5489,#4480,#4481);
#3718=AXIS2_PLACEMENT_3D('',#5491,#4483,#4484);
#3719=AXIS2_PLACEMENT_3D('',#5494,#4487,#4488);
#3720=AXIS2_PLACEMENT_3D('',#5497,#4489,#4490);
#3721=AXIS2_PLACEMENT_3D('',#5501,#4492,#4493);
#3722=AXIS2_PLACEMENT_3D('',#5503,#4495,#4496);
#3723=AXIS2_PLACEMENT_3D('',#5509,#4500,#4501);
#3724=AXIS2_PLACEMENT_3D('',#5511,#4502,#4503);
#3725=AXIS2_PLACEMENT_3D('',#5513,#4504,#4505);
#3726=AXIS2_PLACEMENT_3D('',#5515,#4507,#4508);
#3727=AXIS2_PLACEMENT_3D('',#5521,#4512,#4513);
#3728=AXIS2_PLACEMENT_3D('',#5523,#4514,#4515);
#3729=AXIS2_PLACEMENT_3D('',#5525,#4516,#4517);
#3730=AXIS2_PLACEMENT_3D('',#5527,#4519,#4520);
#3731=AXIS2_PLACEMENT_3D('',#5533,#4524,#4525);
#3732=AXIS2_PLACEMENT_3D('',#5535,#4526,#4527);
#3733=AXIS2_PLACEMENT_3D('',#5537,#4528,#4529);
#3734=AXIS2_PLACEMENT_3D('',#5539,#4531,#4532);
#3735=AXIS2_PLACEMENT_3D('',#5542,#4535,#4536);
#3736=AXIS2_PLACEMENT_3D('',#5551,#4541,#4542);
#3737=AXIS2_PLACEMENT_3D('',#5553,#4543,#4544);
#3738=AXIS2_PLACEMENT_3D('',#5555,#4545,#4546);
#3739=AXIS2_PLACEMENT_3D('',#5557,#4548,#4549);
#3740=AXIS2_PLACEMENT_3D('',#5559,#4550,#4551);
#3741=AXIS2_PLACEMENT_3D('',#5561,#4552,#4553);
#3742=AXIS2_PLACEMENT_3D('',#5563,#4555,#4556);
#3743=AXIS2_PLACEMENT_3D('',#5569,#4560,#4561);
#3744=AXIS2_PLACEMENT_3D('',#5571,#4562,#4563);
#3745=AXIS2_PLACEMENT_3D('',#5573,#4564,#4565);
#3746=AXIS2_PLACEMENT_3D('',#5575,#4567,#4568);
#3747=AXIS2_PLACEMENT_3D('',#5577,#4569,#4570);
#3748=AXIS2_PLACEMENT_3D('',#5579,#4571,#4572);
#3749=AXIS2_PLACEMENT_3D('',#5581,#4574,#4575);
#3750=AXIS2_PLACEMENT_3D('',#5583,#4576,#4577);
#3751=AXIS2_PLACEMENT_3D('',#5585,#4578,#4579);
#3752=AXIS2_PLACEMENT_3D('',#5587,#4581,#4582);
#3753=AXIS2_PLACEMENT_3D('',#5593,#4586,#4587);
#3754=AXIS2_PLACEMENT_3D('',#5595,#4588,#4589);
#3755=AXIS2_PLACEMENT_3D('',#5597,#4590,#4591);
#3756=AXIS2_PLACEMENT_3D('',#5599,#4593,#4594);
#3757=AXIS2_PLACEMENT_3D('',#5605,#4598,#4599);
#3758=AXIS2_PLACEMENT_3D('',#5607,#4600,#4601);
#3759=AXIS2_PLACEMENT_3D('',#5609,#4602,#4603);
#3760=AXIS2_PLACEMENT_3D('',#5611,#4605,#4606);
#3761=AXIS2_PLACEMENT_3D('',#5613,#4607,#4608);
#3762=AXIS2_PLACEMENT_3D('',#5615,#4609,#4610);
#3763=AXIS2_PLACEMENT_3D('',#5617,#4612,#4613);
#3764=AXIS2_PLACEMENT_3D('',#5623,#4617,#4618);
#3765=AXIS2_PLACEMENT_3D('',#5625,#4619,#4620);
#3766=AXIS2_PLACEMENT_3D('',#5627,#4621,#4622);
#3767=AXIS2_PLACEMENT_3D('',#5629,#4624,#4625);
#3768=AXIS2_PLACEMENT_3D('',#5635,#4629,#4630);
#3769=AXIS2_PLACEMENT_3D('',#5637,#4631,#4632);
#3770=AXIS2_PLACEMENT_3D('',#5639,#4633,#4634);
#3771=AXIS2_PLACEMENT_3D('',#5641,#4636,#4637);
#3772=AXIS2_PLACEMENT_3D('',#5647,#4641,#4642);
#3773=AXIS2_PLACEMENT_3D('',#5649,#4643,#4644);
#3774=AXIS2_PLACEMENT_3D('',#5651,#4645,#4646);
#3775=AXIS2_PLACEMENT_3D('',#5653,#4648,#4649);
#3776=AXIS2_PLACEMENT_3D('',#5659,#4653,#4654);
#3777=AXIS2_PLACEMENT_3D('',#5661,#4655,#4656);
#3778=AXIS2_PLACEMENT_3D('',#5663,#4657,#4658);
#3779=AXIS2_PLACEMENT_3D('',#5665,#4660,#4661);
#3780=AXIS2_PLACEMENT_3D('',#5671,#4665,#4666);
#3781=AXIS2_PLACEMENT_3D('',#5673,#4667,#4668);
#3782=AXIS2_PLACEMENT_3D('',#5675,#4669,#4670);
#3783=AXIS2_PLACEMENT_3D('',#5677,#4672,#4673);
#3784=AXIS2_PLACEMENT_3D('',#5683,#4677,#4678);
#3785=AXIS2_PLACEMENT_3D('',#5685,#4679,#4680);
#3786=AXIS2_PLACEMENT_3D('',#5687,#4681,#4682);
#3787=AXIS2_PLACEMENT_3D('',#5689,#4684,#4685);
#3788=AXIS2_PLACEMENT_3D('',#5695,#4689,#4690);
#3789=AXIS2_PLACEMENT_3D('',#5697,#4691,#4692);
#3790=AXIS2_PLACEMENT_3D('',#5699,#4693,#4694);
#3791=AXIS2_PLACEMENT_3D('',#5701,#4696,#4697);
#3792=AXIS2_PLACEMENT_3D('',#5707,#4701,#4702);
#3793=AXIS2_PLACEMENT_3D('',#5709,#4703,#4704);
#3794=AXIS2_PLACEMENT_3D('',#5711,#4705,#4706);
#3795=AXIS2_PLACEMENT_3D('',#5713,#4708,#4709);
#3796=AXIS2_PLACEMENT_3D('',#5719,#4713,#4714);
#3797=AXIS2_PLACEMENT_3D('',#5721,#4715,#4716);
#3798=AXIS2_PLACEMENT_3D('',#5723,#4717,#4718);
#3799=AXIS2_PLACEMENT_3D('',#5725,#4720,#4721);
#3800=AXIS2_PLACEMENT_3D('',#5731,#4725,#4726);
#3801=AXIS2_PLACEMENT_3D('',#5733,#4727,#4728);
#3802=AXIS2_PLACEMENT_3D('',#5735,#4729,#4730);
#3803=AXIS2_PLACEMENT_3D('',#5737,#4732,#4733);
#3804=AXIS2_PLACEMENT_3D('',#5743,#4737,#4738);
#3805=AXIS2_PLACEMENT_3D('',#5745,#4739,#4740);
#3806=AXIS2_PLACEMENT_3D('',#5747,#4741,#4742);
#3807=AXIS2_PLACEMENT_3D('',#5749,#4744,#4745);
#3808=AXIS2_PLACEMENT_3D('',#5755,#4749,#4750);
#3809=AXIS2_PLACEMENT_3D('',#5757,#4751,#4752);
#3810=AXIS2_PLACEMENT_3D('',#5759,#4753,#4754);
#3811=AXIS2_PLACEMENT_3D('',#5761,#4756,#4757);
#3812=AXIS2_PLACEMENT_3D('',#5767,#4761,#4762);
#3813=AXIS2_PLACEMENT_3D('',#5769,#4763,#4764);
#3814=AXIS2_PLACEMENT_3D('',#5771,#4765,#4766);
#3815=AXIS2_PLACEMENT_3D('',#5773,#4768,#4769);
#3816=AXIS2_PLACEMENT_3D('',#5779,#4773,#4774);
#3817=AXIS2_PLACEMENT_3D('',#5781,#4775,#4776);
#3818=AXIS2_PLACEMENT_3D('',#5783,#4777,#4778);
#3819=AXIS2_PLACEMENT_3D('',#5785,#4780,#4781);
#3820=AXIS2_PLACEMENT_3D('',#5791,#4785,#4786);
#3821=AXIS2_PLACEMENT_3D('',#5793,#4787,#4788);
#3822=AXIS2_PLACEMENT_3D('',#5795,#4789,#4790);
#3823=AXIS2_PLACEMENT_3D('',#5797,#4792,#4793);
#3824=AXIS2_PLACEMENT_3D('',#5803,#4797,#4798);
#3825=AXIS2_PLACEMENT_3D('',#5805,#4799,#4800);
#3826=AXIS2_PLACEMENT_3D('',#5807,#4801,#4802);
#3827=AXIS2_PLACEMENT_3D('',#5809,#4804,#4805);
#3828=AXIS2_PLACEMENT_3D('',#5815,#4809,#4810);
#3829=AXIS2_PLACEMENT_3D('',#5817,#4811,#4812);
#3830=AXIS2_PLACEMENT_3D('',#5819,#4813,#4814);
#3831=AXIS2_PLACEMENT_3D('',#5821,#4816,#4817);
#3832=AXIS2_PLACEMENT_3D('',#5827,#4821,#4822);
#3833=AXIS2_PLACEMENT_3D('',#5829,#4823,#4824);
#3834=AXIS2_PLACEMENT_3D('',#5831,#4825,#4826);
#3835=AXIS2_PLACEMENT_3D('',#5833,#4828,#4829);
#3836=AXIS2_PLACEMENT_3D('',#5835,#4830,#4831);
#3837=AXIS2_PLACEMENT_3D('',#5837,#4832,#4833);
#3838=AXIS2_PLACEMENT_3D('',#5839,#4835,#4836);
#3839=AXIS2_PLACEMENT_3D('',#5845,#4840,#4841);
#3840=AXIS2_PLACEMENT_3D('',#5847,#4842,#4843);
#3841=AXIS2_PLACEMENT_3D('',#5849,#4844,#4845);
#3842=AXIS2_PLACEMENT_3D('',#5851,#4847,#4848);
#3843=AXIS2_PLACEMENT_3D('',#5857,#4852,#4853);
#3844=AXIS2_PLACEMENT_3D('',#5859,#4854,#4855);
#3845=AXIS2_PLACEMENT_3D('',#5861,#4856,#4857);
#3846=AXIS2_PLACEMENT_3D('',#5863,#4859,#4860);
#3847=AXIS2_PLACEMENT_3D('',#5869,#4864,#4865);
#3848=AXIS2_PLACEMENT_3D('',#5871,#4866,#4867);
#3849=AXIS2_PLACEMENT_3D('',#5873,#4868,#4869);
#3850=AXIS2_PLACEMENT_3D('',#5875,#4871,#4872);
#3851=AXIS2_PLACEMENT_3D('',#5877,#4873,#4874);
#3852=AXIS2_PLACEMENT_3D('',#5879,#4875,#4876);
#3853=AXIS2_PLACEMENT_3D('',#5881,#4878,#4879);
#3854=AXIS2_PLACEMENT_3D('',#5887,#4883,#4884);
#3855=AXIS2_PLACEMENT_3D('',#5889,#4885,#4886);
#3856=AXIS2_PLACEMENT_3D('',#5891,#4887,#4888);
#3857=AXIS2_PLACEMENT_3D('',#5893,#4890,#4891);
#3858=AXIS2_PLACEMENT_3D('',#5899,#4895,#4896);
#3859=AXIS2_PLACEMENT_3D('',#5900,#4897,#4898);
#3860=AXIS2_PLACEMENT_3D('',#5901,#4899,#4900);
#3861=AXIS2_PLACEMENT_3D('',#5902,#4901,#4902);
#3862=AXIS2_PLACEMENT_3D('',#5903,#4903,#4904);
#3863=DIRECTION('axis',(0.,0.,1.));
#3864=DIRECTION('refdir',(1.,0.,0.));
#3865=DIRECTION('center_axis',(0.,0.,1.));
#3866=DIRECTION('ref_axis',(1.,0.,0.));
#3867=DIRECTION('center_axis',(0.,0.,1.));
#3868=DIRECTION('ref_axis',(1.,0.,0.));
#3869=DIRECTION('',(0.,0.,-1.));
#3870=DIRECTION('center_axis',(0.,0.,1.));
#3871=DIRECTION('ref_axis',(1.,0.,0.));
#3872=DIRECTION('center_axis',(0.,0.,1.));
#3873=DIRECTION('ref_axis',(1.,0.,0.));
#3874=DIRECTION('center_axis',(0.,0.,1.));
#3875=DIRECTION('ref_axis',(1.,0.,0.));
#3876=DIRECTION('',(0.,0.,-1.));
#3877=DIRECTION('center_axis',(0.,0.,1.));
#3878=DIRECTION('ref_axis',(1.,0.,0.));
#3879=DIRECTION('center_axis',(0.,0.,1.));
#3880=DIRECTION('ref_axis',(0.,-1.,0.));
#3881=DIRECTION('center_axis',(0.,0.,1.));
#3882=DIRECTION('ref_axis',(0.,-1.,0.));
#3883=DIRECTION('',(0.,0.,1.));
#3884=DIRECTION('center_axis',(0.,0.,-1.));
#3885=DIRECTION('ref_axis',(0.,-1.,0.));
#3886=DIRECTION('',(0.,0.,1.));
#3887=DIRECTION('center_axis',(-1.,0.,0.));
#3888=DIRECTION('ref_axis',(0.,-1.,0.));
#3889=DIRECTION('',(0.,-1.,0.));
#3890=DIRECTION('',(0.,-1.,0.));
#3891=DIRECTION('',(0.,0.,1.));
#3892=DIRECTION('center_axis',(0.,0.,1.));
#3893=DIRECTION('ref_axis',(1.,0.,0.));
#3894=DIRECTION('center_axis',(0.,0.,1.));
#3895=DIRECTION('ref_axis',(1.,0.,0.));
#3896=DIRECTION('center_axis',(0.,0.,-1.));
#3897=DIRECTION('ref_axis',(1.,0.,0.));
#3898=DIRECTION('',(0.,0.,1.));
#3899=DIRECTION('center_axis',(0.,-1.,0.));
#3900=DIRECTION('ref_axis',(1.,0.,0.));
#3901=DIRECTION('',(1.,0.,0.));
#3902=DIRECTION('',(1.,0.,0.));
#3903=DIRECTION('',(0.,0.,1.));
#3904=DIRECTION('center_axis',(0.,0.,1.));
#3905=DIRECTION('ref_axis',(0.,1.,0.));
#3906=DIRECTION('center_axis',(0.,0.,1.));
#3907=DIRECTION('ref_axis',(0.,1.,0.));
#3908=DIRECTION('center_axis',(0.,0.,-1.));
#3909=DIRECTION('ref_axis',(0.,1.,0.));
#3910=DIRECTION('',(0.,0.,1.));
#3911=DIRECTION('center_axis',(1.,9.25185853854297E-17,0.));
#3912=DIRECTION('ref_axis',(-9.25185853854297E-17,1.,0.));
#3913=DIRECTION('',(-9.25185853854297E-17,1.,0.));
#3914=DIRECTION('',(-9.25185853854297E-17,1.,0.));
#3915=DIRECTION('',(0.,0.,1.));
#3916=DIRECTION('center_axis',(0.,0.,1.));
#3917=DIRECTION('ref_axis',(-1.,4.44089209850064E-15,0.));
#3918=DIRECTION('center_axis',(0.,0.,1.));
#3919=DIRECTION('ref_axis',(-1.,4.44089209850064E-15,0.));
#3920=DIRECTION('center_axis',(0.,0.,-1.));
#3921=DIRECTION('ref_axis',(-1.,4.44089209850064E-15,0.));
#3922=DIRECTION('',(0.,0.,1.));
#3923=DIRECTION('center_axis',(0.,1.,0.));
#3924=DIRECTION('ref_axis',(-1.,0.,0.));
#3925=DIRECTION('',(-1.,0.,0.));
#3926=DIRECTION('',(-1.,0.,0.));
#3927=DIRECTION('center_axis',(0.,0.,1.));
#3928=DIRECTION('ref_axis',(1.,0.,0.));
#3929=DIRECTION('center_axis',(0.,0.,1.));
#3930=DIRECTION('ref_axis',(1.,0.,0.));
#3931=DIRECTION('',(0.,0.,-1.));
#3932=DIRECTION('center_axis',(0.,0.,1.));
#3933=DIRECTION('ref_axis',(1.,0.,0.));
#3934=DIRECTION('center_axis',(0.,0.,1.));
#3935=DIRECTION('ref_axis',(1.,0.,0.));
#3936=DIRECTION('center_axis',(0.,0.,1.));
#3937=DIRECTION('ref_axis',(1.,0.,0.));
#3938=DIRECTION('',(0.,0.,-1.));
#3939=DIRECTION('center_axis',(0.,0.,1.));
#3940=DIRECTION('ref_axis',(1.,0.,0.));
#3941=DIRECTION('center_axis',(0.,0.,1.));
#3942=DIRECTION('ref_axis',(1.,0.,0.));
#3943=DIRECTION('center_axis',(0.,0.,1.));
#3944=DIRECTION('ref_axis',(1.,0.,0.));
#3945=DIRECTION('',(0.,0.,-1.));
#3946=DIRECTION('center_axis',(0.,0.,1.));
#3947=DIRECTION('ref_axis',(1.,0.,0.));
#3948=DIRECTION('center_axis',(0.,0.,1.));
#3949=DIRECTION('ref_axis',(1.,0.,0.));
#3950=DIRECTION('center_axis',(0.,0.,1.));
#3951=DIRECTION('ref_axis',(1.,0.,0.));
#3952=DIRECTION('',(0.,0.,-1.));
#3953=DIRECTION('center_axis',(0.,0.,1.));
#3954=DIRECTION('ref_axis',(1.,0.,0.));
#3955=DIRECTION('center_axis',(0.,0.,1.));
#3956=DIRECTION('ref_axis',(1.,0.,0.));
#3957=DIRECTION('center_axis',(0.,0.,1.));
#3958=DIRECTION('ref_axis',(1.,0.,0.));
#3959=DIRECTION('',(0.,0.,-1.));
#3960=DIRECTION('center_axis',(0.,0.,1.));
#3961=DIRECTION('ref_axis',(1.,0.,0.));
#3962=DIRECTION('center_axis',(0.,0.,1.));
#3963=DIRECTION('ref_axis',(1.,0.,0.));
#3964=DIRECTION('center_axis',(0.,0.,1.));
#3965=DIRECTION('ref_axis',(1.,0.,0.));
#3966=DIRECTION('',(0.,0.,-1.));
#3967=DIRECTION('center_axis',(0.,0.,1.));
#3968=DIRECTION('ref_axis',(1.,0.,0.));
#3969=DIRECTION('center_axis',(0.,0.,1.));
#3970=DIRECTION('ref_axis',(1.,0.,0.));
#3971=DIRECTION('center_axis',(0.,0.,1.));
#3972=DIRECTION('ref_axis',(1.,0.,0.));
#3973=DIRECTION('',(0.,0.,-1.));
#3974=DIRECTION('center_axis',(0.,0.,1.));
#3975=DIRECTION('ref_axis',(1.,0.,0.));
#3976=DIRECTION('center_axis',(0.,0.,1.));
#3977=DIRECTION('ref_axis',(1.,0.,0.));
#3978=DIRECTION('center_axis',(0.,0.,1.));
#3979=DIRECTION('ref_axis',(1.,0.,0.));
#3980=DIRECTION('',(0.,0.,-1.));
#3981=DIRECTION('center_axis',(0.,0.,1.));
#3982=DIRECTION('ref_axis',(1.,0.,0.));
#3983=DIRECTION('center_axis',(0.,0.,1.));
#3984=DIRECTION('ref_axis',(1.,0.,0.));
#3985=DIRECTION('center_axis',(0.,0.,1.));
#3986=DIRECTION('ref_axis',(1.,0.,0.));
#3987=DIRECTION('',(0.,0.,-1.));
#3988=DIRECTION('center_axis',(0.,0.,1.));
#3989=DIRECTION('ref_axis',(1.,0.,0.));
#3990=DIRECTION('center_axis',(0.,0.,1.));
#3991=DIRECTION('ref_axis',(1.,0.,0.));
#3992=DIRECTION('center_axis',(0.,0.,1.));
#3993=DIRECTION('ref_axis',(1.,0.,0.));
#3994=DIRECTION('',(0.,0.,-1.));
#3995=DIRECTION('center_axis',(0.,0.,1.));
#3996=DIRECTION('ref_axis',(1.,0.,0.));
#3997=DIRECTION('center_axis',(0.,0.,1.));
#3998=DIRECTION('ref_axis',(1.,0.,0.));
#3999=DIRECTION('center_axis',(0.,0.,1.));
#4000=DIRECTION('ref_axis',(1.,0.,0.));
#4001=DIRECTION('',(0.,0.,-1.));
#4002=DIRECTION('center_axis',(0.,0.,1.));
#4003=DIRECTION('ref_axis',(1.,0.,0.));
#4004=DIRECTION('center_axis',(0.,0.,1.));
#4005=DIRECTION('ref_axis',(1.,0.,0.));
#4006=DIRECTION('center_axis',(0.,0.,1.));
#4007=DIRECTION('ref_axis',(1.,0.,0.));
#4008=DIRECTION('',(0.,0.,-1.));
#4009=DIRECTION('center_axis',(0.,0.,1.));
#4010=DIRECTION('ref_axis',(1.,0.,0.));
#4011=DIRECTION('center_axis',(0.,0.,1.));
#4012=DIRECTION('ref_axis',(1.,0.,0.));
#4013=DIRECTION('center_axis',(0.,0.,1.));
#4014=DIRECTION('ref_axis',(1.,0.,0.));
#4015=DIRECTION('',(0.,0.,-1.));
#4016=DIRECTION('center_axis',(0.,0.,1.));
#4017=DIRECTION('ref_axis',(1.,0.,0.));
#4018=DIRECTION('center_axis',(0.,0.,1.));
#4019=DIRECTION('ref_axis',(1.,0.,0.));
#4020=DIRECTION('center_axis',(0.,0.,1.));
#4021=DIRECTION('ref_axis',(1.,0.,0.));
#4022=DIRECTION('',(0.,0.,-1.));
#4023=DIRECTION('center_axis',(0.,0.,1.));
#4024=DIRECTION('ref_axis',(1.,0.,0.));
#4025=DIRECTION('center_axis',(0.,0.,1.));
#4026=DIRECTION('ref_axis',(1.,0.,0.));
#4027=DIRECTION('center_axis',(0.,0.,1.));
#4028=DIRECTION('ref_axis',(1.,0.,0.));
#4029=DIRECTION('',(0.,0.,-1.));
#4030=DIRECTION('center_axis',(0.,0.,1.));
#4031=DIRECTION('ref_axis',(1.,0.,0.));
#4032=DIRECTION('center_axis',(0.,0.,1.));
#4033=DIRECTION('ref_axis',(1.,0.,0.));
#4034=DIRECTION('center_axis',(0.,0.,1.));
#4035=DIRECTION('ref_axis',(1.,0.,0.));
#4036=DIRECTION('',(0.,0.,-1.));
#4037=DIRECTION('center_axis',(0.,0.,1.));
#4038=DIRECTION('ref_axis',(1.,0.,0.));
#4039=DIRECTION('center_axis',(0.,0.,1.));
#4040=DIRECTION('ref_axis',(1.,0.,0.));
#4041=DIRECTION('center_axis',(0.,0.,1.));
#4042=DIRECTION('ref_axis',(1.,0.,0.));
#4043=DIRECTION('',(0.,0.,-1.));
#4044=DIRECTION('center_axis',(0.,0.,1.));
#4045=DIRECTION('ref_axis',(1.,0.,0.));
#4046=DIRECTION('center_axis',(0.,0.,1.));
#4047=DIRECTION('ref_axis',(1.,0.,0.));
#4048=DIRECTION('center_axis',(0.,0.,1.));
#4049=DIRECTION('ref_axis',(1.,0.,0.));
#4050=DIRECTION('',(0.,0.,-1.));
#4051=DIRECTION('center_axis',(0.,0.,1.));
#4052=DIRECTION('ref_axis',(1.,0.,0.));
#4053=DIRECTION('center_axis',(0.,0.,1.));
#4054=DIRECTION('ref_axis',(1.,0.,0.));
#4055=DIRECTION('center_axis',(0.,0.,1.));
#4056=DIRECTION('ref_axis',(1.,0.,0.));
#4057=DIRECTION('',(0.,0.,-1.));
#4058=DIRECTION('center_axis',(0.,0.,1.));
#4059=DIRECTION('ref_axis',(1.,0.,0.));
#4060=DIRECTION('center_axis',(0.,0.,1.));
#4061=DIRECTION('ref_axis',(1.,0.,0.));
#4062=DIRECTION('center_axis',(0.,0.,1.));
#4063=DIRECTION('ref_axis',(1.,0.,0.));
#4064=DIRECTION('',(0.,0.,-1.));
#4065=DIRECTION('center_axis',(0.,0.,1.));
#4066=DIRECTION('ref_axis',(1.,0.,0.));
#4067=DIRECTION('center_axis',(0.,0.,1.));
#4068=DIRECTION('ref_axis',(1.,0.,0.));
#4069=DIRECTION('center_axis',(0.,0.,1.));
#4070=DIRECTION('ref_axis',(1.,0.,0.));
#4071=DIRECTION('',(0.,0.,-1.));
#4072=DIRECTION('center_axis',(0.,0.,1.));
#4073=DIRECTION('ref_axis',(1.,0.,0.));
#4074=DIRECTION('center_axis',(0.,0.,1.));
#4075=DIRECTION('ref_axis',(1.,0.,0.));
#4076=DIRECTION('center_axis',(0.,0.,1.));
#4077=DIRECTION('ref_axis',(1.,0.,0.));
#4078=DIRECTION('',(0.,0.,-1.));
#4079=DIRECTION('center_axis',(0.,0.,1.));
#4080=DIRECTION('ref_axis',(1.,0.,0.));
#4081=DIRECTION('center_axis',(0.,0.,1.));
#4082=DIRECTION('ref_axis',(1.,0.,0.));
#4083=DIRECTION('center_axis',(0.,0.,1.));
#4084=DIRECTION('ref_axis',(1.,0.,0.));
#4085=DIRECTION('',(0.,0.,-1.));
#4086=DIRECTION('center_axis',(0.,0.,1.));
#4087=DIRECTION('ref_axis',(1.,0.,0.));
#4088=DIRECTION('center_axis',(0.,0.,1.));
#4089=DIRECTION('ref_axis',(1.,0.,0.));
#4090=DIRECTION('center_axis',(0.,0.,1.));
#4091=DIRECTION('ref_axis',(1.,0.,0.));
#4092=DIRECTION('',(0.,0.,-1.));
#4093=DIRECTION('center_axis',(0.,0.,1.));
#4094=DIRECTION('ref_axis',(1.,0.,0.));
#4095=DIRECTION('center_axis',(0.,0.,1.));
#4096=DIRECTION('ref_axis',(1.,0.,0.));
#4097=DIRECTION('center_axis',(0.,0.,1.));
#4098=DIRECTION('ref_axis',(1.,0.,0.));
#4099=DIRECTION('',(0.,0.,-1.));
#4100=DIRECTION('center_axis',(0.,0.,1.));
#4101=DIRECTION('ref_axis',(1.,0.,0.));
#4102=DIRECTION('center_axis',(0.,0.,1.));
#4103=DIRECTION('ref_axis',(1.,0.,0.));
#4104=DIRECTION('center_axis',(0.,0.,1.));
#4105=DIRECTION('ref_axis',(1.,0.,0.));
#4106=DIRECTION('',(0.,0.,-1.));
#4107=DIRECTION('center_axis',(0.,0.,1.));
#4108=DIRECTION('ref_axis',(1.,0.,0.));
#4109=DIRECTION('center_axis',(0.,0.,1.));
#4110=DIRECTION('ref_axis',(1.,0.,0.));
#4111=DIRECTION('center_axis',(0.,0.,1.));
#4112=DIRECTION('ref_axis',(1.,0.,0.));
#4113=DIRECTION('',(0.,0.,-1.));
#4114=DIRECTION('center_axis',(0.,0.,1.));
#4115=DIRECTION('ref_axis',(1.,0.,0.));
#4116=DIRECTION('center_axis',(0.,0.,1.));
#4117=DIRECTION('ref_axis',(1.,0.,0.));
#4118=DIRECTION('center_axis',(0.,0.,1.));
#4119=DIRECTION('ref_axis',(1.,0.,0.));
#4120=DIRECTION('',(0.,0.,-1.));
#4121=DIRECTION('center_axis',(0.,0.,1.));
#4122=DIRECTION('ref_axis',(1.,0.,0.));
#4123=DIRECTION('center_axis',(0.,0.,1.));
#4124=DIRECTION('ref_axis',(1.,0.,0.));
#4125=DIRECTION('center_axis',(0.,0.,1.));
#4126=DIRECTION('ref_axis',(1.,0.,0.));
#4127=DIRECTION('',(0.,0.,-1.));
#4128=DIRECTION('center_axis',(0.,0.,1.));
#4129=DIRECTION('ref_axis',(1.,0.,0.));
#4130=DIRECTION('center_axis',(0.,0.,1.));
#4131=DIRECTION('ref_axis',(1.,0.,0.));
#4132=DIRECTION('center_axis',(0.,0.,1.));
#4133=DIRECTION('ref_axis',(1.,0.,0.));
#4134=DIRECTION('',(0.,0.,-1.));
#4135=DIRECTION('center_axis',(0.,0.,1.));
#4136=DIRECTION('ref_axis',(1.,0.,0.));
#4137=DIRECTION('center_axis',(0.,0.,1.));
#4138=DIRECTION('ref_axis',(1.,0.,0.));
#4139=DIRECTION('center_axis',(0.,0.,-1.));
#4140=DIRECTION('ref_axis',(1.,0.,0.));
#4141=DIRECTION('',(0.,0.,-1.));
#4142=DIRECTION('center_axis',(0.,0.,1.));
#4143=DIRECTION('ref_axis',(1.,0.,0.));
#4144=DIRECTION('center_axis',(0.,0.,1.));
#4145=DIRECTION('ref_axis',(1.,0.,0.));
#4146=DIRECTION('center_axis',(0.,0.,-1.));
#4147=DIRECTION('ref_axis',(1.,0.,0.));
#4148=DIRECTION('',(0.,0.,-1.));
#4149=DIRECTION('center_axis',(0.,0.,1.));
#4150=DIRECTION('ref_axis',(1.,0.,0.));
#4151=DIRECTION('center_axis',(0.,0.,1.));
#4152=DIRECTION('ref_axis',(0.,1.,0.));
#4153=DIRECTION('center_axis',(0.,0.,1.));
#4154=DIRECTION('ref_axis',(0.,1.,0.));
#4155=DIRECTION('',(0.,0.,1.));
#4156=DIRECTION('center_axis',(0.,0.,-1.));
#4157=DIRECTION('ref_axis',(0.,1.,0.));
#4158=DIRECTION('',(0.,0.,1.));
#4159=DIRECTION('center_axis',(1.,0.,0.));
#4160=DIRECTION('ref_axis',(0.,1.,0.));
#4161=DIRECTION('',(0.,1.,0.));
#4162=DIRECTION('',(0.,1.,0.));
#4163=DIRECTION('',(0.,0.,1.));
#4164=DIRECTION('center_axis',(0.,0.,1.));
#4165=DIRECTION('ref_axis',(-1.,0.,0.));
#4166=DIRECTION('center_axis',(0.,0.,1.));
#4167=DIRECTION('ref_axis',(-1.,0.,0.));
#4168=DIRECTION('center_axis',(0.,0.,-1.));
#4169=DIRECTION('ref_axis',(-1.,0.,0.));
#4170=DIRECTION('',(0.,0.,1.));
#4171=DIRECTION('center_axis',(0.,1.,0.));
#4172=DIRECTION('ref_axis',(-1.,0.,0.));
#4173=DIRECTION('',(-1.,0.,0.));
#4174=DIRECTION('',(-1.,0.,0.));
#4175=DIRECTION('',(0.,0.,1.));
#4176=DIRECTION('center_axis',(0.,0.,1.));
#4177=DIRECTION('ref_axis',(0.,-1.,0.));
#4178=DIRECTION('center_axis',(0.,0.,1.));
#4179=DIRECTION('ref_axis',(0.,-1.,0.));
#4180=DIRECTION('center_axis',(0.,0.,-1.));
#4181=DIRECTION('ref_axis',(0.,-1.,0.));
#4182=DIRECTION('',(0.,0.,1.));
#4183=DIRECTION('center_axis',(-1.,0.,0.));
#4184=DIRECTION('ref_axis',(0.,-1.,0.));
#4185=DIRECTION('',(0.,-1.,0.));
#4186=DIRECTION('',(0.,-1.,0.));
#4187=DIRECTION('',(0.,0.,1.));
#4188=DIRECTION('center_axis',(0.,0.,1.));
#4189=DIRECTION('ref_axis',(1.,0.,0.));
#4190=DIRECTION('center_axis',(0.,0.,1.));
#4191=DIRECTION('ref_axis',(1.,0.,0.));
#4192=DIRECTION('center_axis',(0.,0.,-1.));
#4193=DIRECTION('ref_axis',(1.,0.,0.));
#4194=DIRECTION('',(0.,0.,1.));
#4195=DIRECTION('center_axis',(0.,-1.,0.));
#4196=DIRECTION('ref_axis',(1.,0.,0.));
#4197=DIRECTION('',(1.,0.,0.));
#4198=DIRECTION('',(1.,0.,0.));
#4199=DIRECTION('center_axis',(0.,0.,1.));
#4200=DIRECTION('ref_axis',(0.,1.,0.));
#4201=DIRECTION('center_axis',(0.,0.,1.));
#4202=DIRECTION('ref_axis',(0.,1.,0.));
#4203=DIRECTION('',(0.,0.,1.));
#4204=DIRECTION('center_axis',(0.,0.,-1.));
#4205=DIRECTION('ref_axis',(0.,1.,0.));
#4206=DIRECTION('',(0.,0.,1.));
#4207=DIRECTION('center_axis',(1.,0.,0.));
#4208=DIRECTION('ref_axis',(0.,1.,0.));
#4209=DIRECTION('',(0.,1.,0.));
#4210=DIRECTION('',(0.,1.,0.));
#4211=DIRECTION('',(0.,0.,1.));
#4212=DIRECTION('center_axis',(0.,0.,1.));
#4213=DIRECTION('ref_axis',(-1.,0.,0.));
#4214=DIRECTION('center_axis',(0.,0.,1.));
#4215=DIRECTION('ref_axis',(-1.,0.,0.));
#4216=DIRECTION('center_axis',(0.,0.,-1.));
#4217=DIRECTION('ref_axis',(-1.,0.,0.));
#4218=DIRECTION('',(0.,0.,1.));
#4219=DIRECTION('center_axis',(1.0208947352875E-16,1.,0.));
#4220=DIRECTION('ref_axis',(-1.,1.0208947352875E-16,0.));
#4221=DIRECTION('',(-1.,1.0208947352875E-16,0.));
#4222=DIRECTION('',(-1.,1.0208947352875E-16,0.));
#4223=DIRECTION('',(0.,0.,1.));
#4224=DIRECTION('center_axis',(0.,0.,1.));
#4225=DIRECTION('ref_axis',(0.,-1.,0.));
#4226=DIRECTION('center_axis',(0.,0.,1.));
#4227=DIRECTION('ref_axis',(0.,-1.,0.));
#4228=DIRECTION('center_axis',(0.,0.,-1.));
#4229=DIRECTION('ref_axis',(0.,-1.,0.));
#4230=DIRECTION('',(0.,0.,1.));
#4231=DIRECTION('center_axis',(-1.,0.,0.));
#4232=DIRECTION('ref_axis',(0.,-1.,0.));
#4233=DIRECTION('',(0.,-1.,0.));
#4234=DIRECTION('',(0.,-1.,0.));
#4235=DIRECTION('',(0.,0.,1.));
#4236=DIRECTION('center_axis',(0.,0.,1.));
#4237=DIRECTION('ref_axis',(1.,0.,0.));
#4238=DIRECTION('center_axis',(0.,0.,1.));
#4239=DIRECTION('ref_axis',(1.,0.,0.));
#4240=DIRECTION('center_axis',(0.,0.,-1.));
#4241=DIRECTION('ref_axis',(1.,0.,0.));
#4242=DIRECTION('',(0.,0.,1.));
#4243=DIRECTION('center_axis',(0.,-1.,0.));
#4244=DIRECTION('ref_axis',(1.,0.,0.));
#4245=DIRECTION('',(1.,0.,0.));
#4246=DIRECTION('',(1.,0.,0.));
#4247=DIRECTION('center_axis',(0.,0.,1.));
#4248=DIRECTION('ref_axis',(0.,1.,0.));
#4249=DIRECTION('center_axis',(0.,0.,1.));
#4250=DIRECTION('ref_axis',(0.,1.,0.));
#4251=DIRECTION('',(0.,0.,1.));
#4252=DIRECTION('center_axis',(0.,0.,-1.));
#4253=DIRECTION('ref_axis',(0.,1.,0.));
#4254=DIRECTION('',(0.,0.,1.));
#4255=DIRECTION('center_axis',(1.,0.,0.));
#4256=DIRECTION('ref_axis',(0.,1.,0.));
#4257=DIRECTION('',(0.,1.,0.));
#4258=DIRECTION('',(0.,1.,0.));
#4259=DIRECTION('',(0.,0.,1.));
#4260=DIRECTION('center_axis',(0.,0.,1.));
#4261=DIRECTION('ref_axis',(-1.,0.,0.));
#4262=DIRECTION('center_axis',(0.,0.,1.));
#4263=DIRECTION('ref_axis',(-1.,0.,0.));
#4264=DIRECTION('center_axis',(0.,0.,-1.));
#4265=DIRECTION('ref_axis',(-1.,0.,0.));
#4266=DIRECTION('',(0.,0.,1.));
#4267=DIRECTION('center_axis',(7.65671051465625E-17,1.,0.));
#4268=DIRECTION('ref_axis',(-1.,7.65671051465625E-17,0.));
#4269=DIRECTION('',(-1.,7.65671051465625E-17,0.));
#4270=DIRECTION('',(-1.,7.65671051465625E-17,0.));
#4271=DIRECTION('',(0.,0.,1.));
#4272=DIRECTION('center_axis',(0.,0.,1.));
#4273=DIRECTION('ref_axis',(0.,-1.,0.));
#4274=DIRECTION('center_axis',(0.,0.,1.));
#4275=DIRECTION('ref_axis',(0.,-1.,0.));
#4276=DIRECTION('center_axis',(0.,0.,-1.));
#4277=DIRECTION('ref_axis',(0.,-1.,0.));
#4278=DIRECTION('',(0.,0.,1.));
#4279=DIRECTION('center_axis',(-1.,0.,0.));
#4280=DIRECTION('ref_axis',(0.,-1.,0.));
#4281=DIRECTION('',(0.,-1.,0.));
#4282=DIRECTION('',(0.,-1.,0.));
#4283=DIRECTION('',(0.,0.,1.));
#4284=DIRECTION('center_axis',(0.,0.,1.));
#4285=DIRECTION('ref_axis',(1.,0.,0.));
#4286=DIRECTION('center_axis',(0.,0.,1.));
#4287=DIRECTION('ref_axis',(1.,0.,0.));
#4288=DIRECTION('center_axis',(0.,0.,-1.));
#4289=DIRECTION('ref_axis',(1.,0.,0.));
#4290=DIRECTION('',(0.,0.,1.));
#4291=DIRECTION('center_axis',(0.,-1.,0.));
#4292=DIRECTION('ref_axis',(1.,0.,0.));
#4293=DIRECTION('',(1.,0.,0.));
#4294=DIRECTION('',(1.,0.,0.));
#4295=DIRECTION('center_axis',(1.,2.3373116307898E-15,0.));
#4296=DIRECTION('ref_axis',(-2.3373116307898E-15,1.,0.));
#4297=DIRECTION('',(2.3373116307898E-15,-1.,0.));
#4298=DIRECTION('',(0.,0.,1.));
#4299=DIRECTION('',(-2.3373116307898E-15,1.,0.));
#4300=DIRECTION('',(0.,0.,1.));
#4301=DIRECTION('center_axis',(0.,0.,1.));
#4302=DIRECTION('ref_axis',(-1.,0.,0.));
#4303=DIRECTION('center_axis',(0.,0.,1.));
#4304=DIRECTION('ref_axis',(-1.,0.,0.));
#4305=DIRECTION('center_axis',(0.,0.,-1.));
#4306=DIRECTION('ref_axis',(-1.,0.,0.));
#4307=DIRECTION('',(0.,0.,1.));
#4308=DIRECTION('center_axis',(0.,1.,0.));
#4309=DIRECTION('ref_axis',(-1.,0.,0.));
#4310=DIRECTION('',(1.,0.,0.));
#4311=DIRECTION('',(-1.,0.,0.));
#4312=DIRECTION('',(0.,0.,1.));
#4313=DIRECTION('center_axis',(0.,0.,1.));
#4314=DIRECTION('ref_axis',(0.,-1.,0.));
#4315=DIRECTION('center_axis',(0.,0.,1.));
#4316=DIRECTION('ref_axis',(0.,-1.,0.));
#4317=DIRECTION('center_axis',(0.,0.,-1.));
#4318=DIRECTION('ref_axis',(0.,-1.,0.));
#4319=DIRECTION('',(0.,0.,1.));
#4320=DIRECTION('center_axis',(-1.,0.,0.));
#4321=DIRECTION('ref_axis',(0.,-1.,0.));
#4322=DIRECTION('',(0.,1.,0.));
#4323=DIRECTION('',(0.,-1.,0.));
#4324=DIRECTION('',(0.,0.,1.));
#4325=DIRECTION('center_axis',(0.,0.,1.));
#4326=DIRECTION('ref_axis',(1.,-3.5527136788007E-14,0.));
#4327=DIRECTION('center_axis',(0.,0.,1.));
#4328=DIRECTION('ref_axis',(1.,-3.5527136788007E-14,0.));
#4329=DIRECTION('center_axis',(0.,0.,-1.));
#4330=DIRECTION('ref_axis',(1.,-3.5527136788007E-14,0.));
#4331=DIRECTION('',(0.,0.,1.));
#4332=DIRECTION('center_axis',(-6.96610525255001E-16,-1.,0.));
#4333=DIRECTION('ref_axis',(1.,-6.96610525255001E-16,0.));
#4334=DIRECTION('',(-1.,6.96610525255001E-16,0.));
#4335=DIRECTION('',(1.,-6.96610525255001E-16,0.));
#4336=DIRECTION('',(0.,0.,1.));
#4337=DIRECTION('center_axis',(0.,0.,1.));
#4338=DIRECTION('ref_axis',(-3.55271367880032E-14,1.,0.));
#4339=DIRECTION('center_axis',(0.,0.,1.));
#4340=DIRECTION('ref_axis',(-3.55271367880032E-14,1.,0.));
#4341=DIRECTION('center_axis',(0.,0.,-1.));
#4342=DIRECTION('ref_axis',(-3.55271367880032E-14,1.,0.));
#4343=DIRECTION('center_axis',(0.,0.,1.));
#4344=DIRECTION('ref_axis',(-1.,0.,0.));
#4345=DIRECTION('center_axis',(0.,0.,1.));
#4346=DIRECTION('ref_axis',(-1.,0.,0.));
#4347=DIRECTION('',(0.,0.,1.));
#4348=DIRECTION('center_axis',(0.,0.,-1.));
#4349=DIRECTION('ref_axis',(-1.,0.,0.));
#4350=DIRECTION('',(0.,0.,1.));
#4351=DIRECTION('center_axis',(0.,1.,0.));
#4352=DIRECTION('ref_axis',(-1.,0.,0.));
#4353=DIRECTION('',(1.,0.,0.));
#4354=DIRECTION('',(-1.,0.,0.));
#4355=DIRECTION('',(0.,0.,1.));
#4356=DIRECTION('center_axis',(0.,0.,1.));
#4357=DIRECTION('ref_axis',(0.,-1.,0.));
#4358=DIRECTION('center_axis',(0.,0.,1.));
#4359=DIRECTION('ref_axis',(0.,-1.,0.));
#4360=DIRECTION('center_axis',(0.,0.,-1.));
#4361=DIRECTION('ref_axis',(0.,-1.,0.));
#4362=DIRECTION('',(0.,0.,1.));
#4363=DIRECTION('center_axis',(-1.,0.,0.));
#4364=DIRECTION('ref_axis',(0.,-1.,0.));
#4365=DIRECTION('',(0.,1.,0.));
#4366=DIRECTION('',(0.,-1.,0.));
#4367=DIRECTION('',(0.,0.,1.));
#4368=DIRECTION('center_axis',(0.,0.,1.));
#4369=DIRECTION('ref_axis',(1.,-3.5527136788007E-14,0.));
#4370=DIRECTION('center_axis',(0.,0.,1.));
#4371=DIRECTION('ref_axis',(1.,-3.5527136788007E-14,0.));
#4372=DIRECTION('center_axis',(0.,0.,-1.));
#4373=DIRECTION('ref_axis',(1.,-3.5527136788007E-14,0.));
#4374=DIRECTION('',(0.,0.,1.));
#4375=DIRECTION('center_axis',(-6.96610525255001E-16,-1.,0.));
#4376=DIRECTION('ref_axis',(1.,-6.96610525255001E-16,0.));
#4377=DIRECTION('',(-1.,6.96610525255001E-16,0.));
#4378=DIRECTION('',(1.,-6.96610525255001E-16,0.));
#4379=DIRECTION('',(0.,0.,1.));
#4380=DIRECTION('center_axis',(0.,0.,1.));
#4381=DIRECTION('ref_axis',(-3.55271367880032E-14,1.,0.));
#4382=DIRECTION('center_axis',(0.,0.,1.));
#4383=DIRECTION('ref_axis',(-3.55271367880032E-14,1.,0.));
#4384=DIRECTION('center_axis',(0.,0.,-1.));
#4385=DIRECTION('ref_axis',(-3.55271367880032E-14,1.,0.));
#4386=DIRECTION('',(0.,0.,1.));
#4387=DIRECTION('center_axis',(1.,2.3373116307898E-15,0.));
#4388=DIRECTION('ref_axis',(-2.3373116307898E-15,1.,0.));
#4389=DIRECTION('',(2.3373116307898E-15,-1.,0.));
#4390=DIRECTION('',(-2.3373116307898E-15,1.,0.));
#4391=DIRECTION('center_axis',(0.,0.,1.));
#4392=DIRECTION('ref_axis',(1.,-3.5527136788007E-14,0.));
#4393=DIRECTION('center_axis',(0.,0.,1.));
#4394=DIRECTION('ref_axis',(1.,-3.5527136788007E-14,0.));
#4395=DIRECTION('',(0.,0.,1.));
#4396=DIRECTION('center_axis',(0.,0.,-1.));
#4397=DIRECTION('ref_axis',(1.,-3.5527136788007E-14,0.));
#4398=DIRECTION('',(0.,0.,1.));
#4399=DIRECTION('center_axis',(-6.96610525255001E-16,-1.,0.));
#4400=DIRECTION('ref_axis',(1.,-6.96610525255001E-16,0.));
#4401=DIRECTION('',(-1.,6.96610525255001E-16,0.));
#4402=DIRECTION('',(1.,-6.96610525255001E-16,0.));
#4403=DIRECTION('',(0.,0.,1.));
#4404=DIRECTION('center_axis',(0.,0.,1.));
#4405=DIRECTION('ref_axis',(-3.55271367880032E-14,1.,0.));
#4406=DIRECTION('center_axis',(0.,0.,1.));
#4407=DIRECTION('ref_axis',(-3.55271367880032E-14,1.,0.));
#4408=DIRECTION('center_axis',(0.,0.,-1.));
#4409=DIRECTION('ref_axis',(-3.55271367880032E-14,1.,0.));
#4410=DIRECTION('',(0.,0.,1.));
#4411=DIRECTION('center_axis',(1.,2.3373116307898E-15,0.));
#4412=DIRECTION('ref_axis',(-2.3373116307898E-15,1.,0.));
#4413=DIRECTION('',(2.3373116307898E-15,-1.,0.));
#4414=DIRECTION('',(-2.3373116307898E-15,1.,0.));
#4415=DIRECTION('',(0.,0.,1.));
#4416=DIRECTION('center_axis',(0.,0.,1.));
#4417=DIRECTION('ref_axis',(-1.,0.,0.));
#4418=DIRECTION('center_axis',(0.,0.,1.));
#4419=DIRECTION('ref_axis',(-1.,0.,0.));
#4420=DIRECTION('center_axis',(0.,0.,-1.));
#4421=DIRECTION('ref_axis',(-1.,0.,0.));
#4422=DIRECTION('',(0.,0.,1.));
#4423=DIRECTION('center_axis',(0.,1.,0.));
#4424=DIRECTION('ref_axis',(-1.,0.,0.));
#4425=DIRECTION('',(1.,0.,0.));
#4426=DIRECTION('',(-1.,0.,0.));
#4427=DIRECTION('',(0.,0.,1.));
#4428=DIRECTION('center_axis',(0.,0.,1.));
#4429=DIRECTION('ref_axis',(0.,-1.,0.));
#4430=DIRECTION('center_axis',(0.,0.,1.));
#4431=DIRECTION('ref_axis',(0.,-1.,0.));
#4432=DIRECTION('center_axis',(0.,0.,-1.));
#4433=DIRECTION('ref_axis',(0.,-1.,0.));
#4434=DIRECTION('',(0.,0.,1.));
#4435=DIRECTION('center_axis',(-1.,0.,0.));
#4436=DIRECTION('ref_axis',(0.,-1.,0.));
#4437=DIRECTION('',(0.,1.,0.));
#4438=DIRECTION('',(0.,-1.,0.));
#4439=DIRECTION('center_axis',(0.,0.,1.));
#4440=DIRECTION('ref_axis',(-1.,8.8817841970005E-15,0.));
#4441=DIRECTION('center_axis',(0.,0.,1.));
#4442=DIRECTION('ref_axis',(-1.,8.8817841970005E-15,0.));
#4443=DIRECTION('',(0.,0.,1.));
#4444=DIRECTION('center_axis',(0.,0.,-1.));
#4445=DIRECTION('ref_axis',(-1.,8.8817841970005E-15,0.));
#4446=DIRECTION('',(0.,0.,1.));
#4447=DIRECTION('center_axis',(0.,1.,0.));
#4448=DIRECTION('ref_axis',(-1.,0.,0.));
#4449=DIRECTION('',(1.,0.,0.));
#4450=DIRECTION('',(-1.,0.,0.));
#4451=DIRECTION('',(0.,0.,1.));
#4452=DIRECTION('center_axis',(0.,0.,1.));
#4453=DIRECTION('ref_axis',(0.,-1.,0.));
#4454=DIRECTION('center_axis',(0.,0.,1.));
#4455=DIRECTION('ref_axis',(0.,-1.,0.));
#4456=DIRECTION('center_axis',(0.,0.,-1.));
#4457=DIRECTION('ref_axis',(0.,-1.,0.));
#4458=DIRECTION('',(0.,0.,1.));
#4459=DIRECTION('center_axis',(-1.,-1.86984930463183E-14,0.));
#4460=DIRECTION('ref_axis',(1.86984930463183E-14,-1.,0.));
#4461=DIRECTION('',(-1.86984930463183E-14,1.,0.));
#4462=DIRECTION('',(1.86984930463183E-14,-1.,0.));
#4463=DIRECTION('',(0.,0.,1.));
#4464=DIRECTION('center_axis',(0.,0.,1.));
#4465=DIRECTION('ref_axis',(1.,2.66453525910015E-14,0.));
#4466=DIRECTION('center_axis',(0.,0.,1.));
#4467=DIRECTION('ref_axis',(1.,2.66453525910015E-14,0.));
#4468=DIRECTION('center_axis',(0.,0.,-1.));
#4469=DIRECTION('ref_axis',(1.,2.66453525910015E-14,0.));
#4470=DIRECTION('',(0.,0.,1.));
#4471=DIRECTION('center_axis',(-6.96610525255012E-16,-1.,0.));
#4472=DIRECTION('ref_axis',(1.,-6.96610525255012E-16,0.));
#4473=DIRECTION('',(-1.,6.96610525255012E-16,0.));
#4474=DIRECTION('',(1.,-6.96610525255012E-16,0.));
#4475=DIRECTION('',(0.,0.,1.));
#4476=DIRECTION('center_axis',(0.,0.,1.));
#4477=DIRECTION('ref_axis',(1.06581410364011E-13,1.,0.));
#4478=DIRECTION('center_axis',(0.,0.,1.));
#4479=DIRECTION('ref_axis',(1.06581410364011E-13,1.,0.));
#4480=DIRECTION('center_axis',(0.,0.,-1.));
#4481=DIRECTION('ref_axis',(1.06581410364011E-13,1.,0.));
#4482=DIRECTION('',(0.,0.,1.));
#4483=DIRECTION('center_axis',(1.,2.3373116307898E-15,0.));
#4484=DIRECTION('ref_axis',(-2.3373116307898E-15,1.,0.));
#4485=DIRECTION('',(2.3373116307898E-15,-1.,0.));
#4486=DIRECTION('',(-2.3373116307898E-15,1.,0.));
#4487=DIRECTION('center_axis',(0.,0.,1.));
#4488=DIRECTION('ref_axis',(1.,0.,0.));
#4489=DIRECTION('center_axis',(0.,0.,1.));
#4490=DIRECTION('ref_axis',(1.,0.,0.));
#4491=DIRECTION('',(0.,0.,1.));
#4492=DIRECTION('center_axis',(0.,0.,-1.));
#4493=DIRECTION('ref_axis',(1.,0.,0.));
#4494=DIRECTION('',(0.,0.,1.));
#4495=DIRECTION('center_axis',(0.,-1.,0.));
#4496=DIRECTION('ref_axis',(1.,0.,0.));
#4497=DIRECTION('',(-1.,0.,0.));
#4498=DIRECTION('',(1.,0.,0.));
#4499=DIRECTION('',(0.,0.,1.));
#4500=DIRECTION('center_axis',(0.,0.,1.));
#4501=DIRECTION('ref_axis',(0.,1.,0.));
#4502=DIRECTION('center_axis',(0.,0.,1.));
#4503=DIRECTION('ref_axis',(0.,1.,0.));
#4504=DIRECTION('center_axis',(0.,0.,-1.));
#4505=DIRECTION('ref_axis',(0.,1.,0.));
#4506=DIRECTION('',(0.,0.,1.));
#4507=DIRECTION('center_axis',(1.,0.,0.));
#4508=DIRECTION('ref_axis',(0.,1.,0.));
#4509=DIRECTION('',(0.,-1.,0.));
#4510=DIRECTION('',(0.,1.,0.));
#4511=DIRECTION('',(0.,0.,1.));
#4512=DIRECTION('center_axis',(0.,0.,1.));
#4513=DIRECTION('ref_axis',(-1.,0.,0.));
#4514=DIRECTION('center_axis',(0.,0.,1.));
#4515=DIRECTION('ref_axis',(-1.,0.,0.));
#4516=DIRECTION('center_axis',(0.,0.,-1.));
#4517=DIRECTION('ref_axis',(-1.,0.,0.));
#4518=DIRECTION('',(0.,0.,1.));
#4519=DIRECTION('center_axis',(0.,1.,0.));
#4520=DIRECTION('ref_axis',(-1.,0.,0.));
#4521=DIRECTION('',(1.,0.,0.));
#4522=DIRECTION('',(-1.,0.,0.));
#4523=DIRECTION('',(0.,0.,1.));
#4524=DIRECTION('center_axis',(0.,0.,1.));
#4525=DIRECTION('ref_axis',(0.,-1.,0.));
#4526=DIRECTION('center_axis',(0.,0.,1.));
#4527=DIRECTION('ref_axis',(0.,-1.,0.));
#4528=DIRECTION('center_axis',(0.,0.,-1.));
#4529=DIRECTION('ref_axis',(0.,-1.,0.));
#4530=DIRECTION('',(0.,0.,1.));
#4531=DIRECTION('center_axis',(-1.,0.,0.));
#4532=DIRECTION('ref_axis',(0.,-1.,0.));
#4533=DIRECTION('',(0.,1.,0.));
#4534=DIRECTION('',(0.,-1.,0.));
#4535=DIRECTION('center_axis',(0.,-1.,0.));
#4536=DIRECTION('ref_axis',(1.,0.,0.));
#4537=DIRECTION('',(-1.,0.,0.));
#4538=DIRECTION('',(0.,0.,1.));
#4539=DIRECTION('',(1.,0.,0.));
#4540=DIRECTION('',(0.,0.,1.));
#4541=DIRECTION('center_axis',(0.,0.,1.));
#4542=DIRECTION('ref_axis',(-7.77156117237571E-15,1.,0.));
#4543=DIRECTION('center_axis',(0.,0.,1.));
#4544=DIRECTION('ref_axis',(-7.77156117237571E-15,1.,0.));
#4545=DIRECTION('center_axis',(0.,0.,-1.));
#4546=DIRECTION('ref_axis',(-7.77156117237571E-15,1.,0.));
#4547=DIRECTION('',(0.,0.,1.));
#4548=DIRECTION('center_axis',(0.,0.,1.));
#4549=DIRECTION('ref_axis',(-0.935403383377095,0.353582395442256,0.));
#4550=DIRECTION('center_axis',(0.,0.,1.));
#4551=DIRECTION('ref_axis',(-0.935403383377095,0.353582395442256,0.));
#4552=DIRECTION('center_axis',(0.,0.,-1.));
#4553=DIRECTION('ref_axis',(-0.935403383377095,0.353582395442256,0.));
#4554=DIRECTION('',(0.,0.,1.));
#4555=DIRECTION('center_axis',(1.,-2.50430672142002E-16,0.));
#4556=DIRECTION('ref_axis',(2.50430672142002E-16,1.,0.));
#4557=DIRECTION('',(-2.50430672142002E-16,-1.,0.));
#4558=DIRECTION('',(2.50430672142002E-16,1.,0.));
#4559=DIRECTION('',(0.,0.,1.));
#4560=DIRECTION('center_axis',(0.,0.,1.));
#4561=DIRECTION('ref_axis',(-0.992261924581792,-0.12416228503551,0.));
#4562=DIRECTION('center_axis',(0.,0.,1.));
#4563=DIRECTION('ref_axis',(-0.992261924581792,-0.12416228503551,0.));
#4564=DIRECTION('center_axis',(0.,0.,-1.));
#4565=DIRECTION('ref_axis',(-0.992261924581792,-0.12416228503551,0.));
#4566=DIRECTION('',(0.,0.,1.));
#4567=DIRECTION('center_axis',(0.,0.,1.));
#4568=DIRECTION('ref_axis',(-0.572529192342877,0.819884335693281,0.));
#4569=DIRECTION('center_axis',(0.,0.,1.));
#4570=DIRECTION('ref_axis',(-0.572529192342877,0.819884335693281,0.));
#4571=DIRECTION('center_axis',(0.,0.,1.));
#4572=DIRECTION('ref_axis',(-0.572529192342877,0.819884335693281,0.));
#4573=DIRECTION('',(0.,0.,1.));
#4574=DIRECTION('center_axis',(0.,0.,1.));
#4575=DIRECTION('ref_axis',(0.572529192342877,-0.819884335693281,0.));
#4576=DIRECTION('center_axis',(0.,0.,1.));
#4577=DIRECTION('ref_axis',(0.572529192342877,-0.819884335693281,0.));
#4578=DIRECTION('center_axis',(0.,0.,-1.));
#4579=DIRECTION('ref_axis',(0.572529192342877,-0.819884335693281,0.));
#4580=DIRECTION('',(0.,0.,1.));
#4581=DIRECTION('center_axis',(-1.,0.,0.));
#4582=DIRECTION('ref_axis',(0.,-1.,0.));
#4583=DIRECTION('',(0.,1.,0.));
#4584=DIRECTION('',(0.,-1.,0.));
#4585=DIRECTION('',(0.,0.,1.));
#4586=DIRECTION('center_axis',(0.,0.,1.));
#4587=DIRECTION('ref_axis',(1.,4.73695157173399E-15,0.));
#4588=DIRECTION('center_axis',(0.,0.,1.));
#4589=DIRECTION('ref_axis',(1.,4.73695157173399E-15,0.));
#4590=DIRECTION('center_axis',(0.,0.,1.));
#4591=DIRECTION('ref_axis',(1.,4.73695157173399E-15,0.));
#4592=DIRECTION('',(0.,0.,1.));
#4593=DIRECTION('center_axis',(1.,-2.68129334249094E-15,0.));
#4594=DIRECTION('ref_axis',(2.68129334249094E-15,1.,0.));
#4595=DIRECTION('',(2.68129334249094E-15,1.,0.));
#4596=DIRECTION('',(2.68129334249094E-15,1.,0.));
#4597=DIRECTION('',(0.,0.,1.));
#4598=DIRECTION('center_axis',(0.,0.,1.));
#4599=DIRECTION('ref_axis',(-1.,0.,0.));
#4600=DIRECTION('center_axis',(0.,0.,1.));
#4601=DIRECTION('ref_axis',(-1.,0.,0.));
#4602=DIRECTION('center_axis',(0.,0.,-1.));
#4603=DIRECTION('ref_axis',(-1.,0.,0.));
#4604=DIRECTION('',(0.,0.,1.));
#4605=DIRECTION('center_axis',(0.,0.,1.));
#4606=DIRECTION('ref_axis',(0.,1.,0.));
#4607=DIRECTION('center_axis',(0.,0.,1.));
#4608=DIRECTION('ref_axis',(0.,1.,0.));
#4609=DIRECTION('center_axis',(0.,0.,1.));
#4610=DIRECTION('ref_axis',(0.,1.,0.));
#4611=DIRECTION('',(0.,0.,1.));
#4612=DIRECTION('center_axis',(0.,1.,0.));
#4613=DIRECTION('ref_axis',(-1.,0.,0.));
#4614=DIRECTION('',(1.,0.,0.));
#4615=DIRECTION('',(-1.,0.,0.));
#4616=DIRECTION('',(0.,0.,1.));
#4617=DIRECTION('center_axis',(0.,0.,1.));
#4618=DIRECTION('ref_axis',(0.,-1.,0.));
#4619=DIRECTION('center_axis',(0.,0.,1.));
#4620=DIRECTION('ref_axis',(0.,-1.,0.));
#4621=DIRECTION('center_axis',(0.,0.,-1.));
#4622=DIRECTION('ref_axis',(0.,-1.,0.));
#4623=DIRECTION('',(0.,0.,1.));
#4624=DIRECTION('center_axis',(-1.,1.29189588320017E-14,0.));
#4625=DIRECTION('ref_axis',(-1.29189588320017E-14,-1.,0.));
#4626=DIRECTION('',(-1.29189588320017E-14,-1.,0.));
#4627=DIRECTION('',(-1.29189588320017E-14,-1.,0.));
#4628=DIRECTION('',(0.,0.,1.));
#4629=DIRECTION('center_axis',(0.,0.,1.));
#4630=DIRECTION('ref_axis',(1.,7.10542735760065E-14,0.));
#4631=DIRECTION('center_axis',(0.,0.,1.));
#4632=DIRECTION('ref_axis',(1.,7.10542735760065E-14,0.));
#4633=DIRECTION('center_axis',(0.,0.,-1.));
#4634=DIRECTION('ref_axis',(1.,7.10542735760065E-14,0.));
#4635=DIRECTION('',(0.,0.,1.));
#4636=DIRECTION('center_axis',(0.,-1.,0.));
#4637=DIRECTION('ref_axis',(1.,0.,0.));
#4638=DIRECTION('',(1.,0.,0.));
#4639=DIRECTION('',(1.,0.,0.));
#4640=DIRECTION('',(0.,0.,1.));
#4641=DIRECTION('center_axis',(0.,0.,1.));
#4642=DIRECTION('ref_axis',(-1.,3.55271367880045E-14,0.));
#4643=DIRECTION('center_axis',(0.,0.,1.));
#4644=DIRECTION('ref_axis',(-1.,3.55271367880045E-14,0.));
#4645=DIRECTION('center_axis',(0.,0.,1.));
#4646=DIRECTION('ref_axis',(-1.,3.55271367880045E-14,0.));
#4647=DIRECTION('',(0.,0.,1.));
#4648=DIRECTION('center_axis',(-1.,-3.08931624243523E-15,0.));
#4649=DIRECTION('ref_axis',(3.08931624243523E-15,-1.,0.));
#4650=DIRECTION('',(-3.08931624243523E-15,1.,0.));
#4651=DIRECTION('',(3.08931624243523E-15,-1.,0.));
#4652=DIRECTION('',(0.,0.,1.));
#4653=DIRECTION('center_axis',(0.,0.,1.));
#4654=DIRECTION('ref_axis',(-3.55271367880045E-14,1.,0.));
#4655=DIRECTION('center_axis',(0.,0.,1.));
#4656=DIRECTION('ref_axis',(-3.55271367880045E-14,1.,0.));
#4657=DIRECTION('center_axis',(0.,0.,1.));
#4658=DIRECTION('ref_axis',(-3.55271367880045E-14,1.,0.));
#4659=DIRECTION('',(0.,0.,1.));
#4660=DIRECTION('center_axis',(-1.5398614377214E-15,1.,0.));
#4661=DIRECTION('ref_axis',(-1.,-1.5398614377214E-15,0.));
#4662=DIRECTION('',(-1.,-1.5398614377214E-15,0.));
#4663=DIRECTION('',(-1.,-1.5398614377214E-15,0.));
#4664=DIRECTION('',(0.,0.,1.));
#4665=DIRECTION('center_axis',(0.,0.,1.));
#4666=DIRECTION('ref_axis',(1.,0.,0.));
#4667=DIRECTION('center_axis',(0.,0.,1.));
#4668=DIRECTION('ref_axis',(1.,0.,0.));
#4669=DIRECTION('center_axis',(0.,0.,1.));
#4670=DIRECTION('ref_axis',(1.,0.,0.));
#4671=DIRECTION('',(0.,0.,1.));
#4672=DIRECTION('center_axis',(1.,0.,0.));
#4673=DIRECTION('ref_axis',(0.,1.,0.));
#4674=DIRECTION('',(0.,1.,0.));
#4675=DIRECTION('',(0.,1.,0.));
#4676=DIRECTION('',(0.,0.,1.));
#4677=DIRECTION('center_axis',(0.,0.,1.));
#4678=DIRECTION('ref_axis',(-1.,0.,0.));
#4679=DIRECTION('center_axis',(0.,0.,1.));
#4680=DIRECTION('ref_axis',(-1.,0.,0.));
#4681=DIRECTION('center_axis',(0.,0.,-1.));
#4682=DIRECTION('ref_axis',(-1.,0.,0.));
#4683=DIRECTION('',(0.,0.,1.));
#4684=DIRECTION('center_axis',(0.,1.,0.));
#4685=DIRECTION('ref_axis',(-1.,0.,0.));
#4686=DIRECTION('',(1.,0.,0.));
#4687=DIRECTION('',(-1.,0.,0.));
#4688=DIRECTION('',(0.,0.,1.));
#4689=DIRECTION('center_axis',(0.,0.,1.));
#4690=DIRECTION('ref_axis',(0.,-1.,0.));
#4691=DIRECTION('center_axis',(0.,0.,1.));
#4692=DIRECTION('ref_axis',(0.,-1.,0.));
#4693=DIRECTION('center_axis',(0.,0.,-1.));
#4694=DIRECTION('ref_axis',(0.,-1.,0.));
#4695=DIRECTION('',(0.,0.,1.));
#4696=DIRECTION('center_axis',(-1.,0.,0.));
#4697=DIRECTION('ref_axis',(0.,-1.,0.));
#4698=DIRECTION('',(0.,-1.,0.));
#4699=DIRECTION('',(0.,-1.,0.));
#4700=DIRECTION('',(0.,0.,1.));
#4701=DIRECTION('center_axis',(0.,0.,1.));
#4702=DIRECTION('ref_axis',(1.,0.,0.));
#4703=DIRECTION('center_axis',(0.,0.,1.));
#4704=DIRECTION('ref_axis',(1.,0.,0.));
#4705=DIRECTION('center_axis',(0.,0.,1.));
#4706=DIRECTION('ref_axis',(1.,0.,0.));
#4707=DIRECTION('',(0.,0.,1.));
#4708=DIRECTION('center_axis',(1.,3.64380890133388E-15,0.));
#4709=DIRECTION('ref_axis',(-3.64380890133388E-15,1.,0.));
#4710=DIRECTION('',(-3.64380890133388E-15,1.,0.));
#4711=DIRECTION('',(-3.64380890133388E-15,1.,0.));
#4712=DIRECTION('',(0.,0.,1.));
#4713=DIRECTION('center_axis',(0.,0.,1.));
#4714=DIRECTION('ref_axis',(-1.,3.55271367880058E-14,0.));
#4715=DIRECTION('center_axis',(0.,0.,1.));
#4716=DIRECTION('ref_axis',(-1.,3.55271367880058E-14,0.));
#4717=DIRECTION('center_axis',(0.,0.,-1.));
#4718=DIRECTION('ref_axis',(-1.,3.55271367880058E-14,0.));
#4719=DIRECTION('',(0.,0.,1.));
#4720=DIRECTION('center_axis',(0.,1.,0.));
#4721=DIRECTION('ref_axis',(-1.,0.,0.));
#4722=DIRECTION('',(1.,0.,0.));
#4723=DIRECTION('',(-1.,0.,0.));
#4724=DIRECTION('',(0.,0.,1.));
#4725=DIRECTION('center_axis',(0.,0.,1.));
#4726=DIRECTION('ref_axis',(0.999997048491126,0.0024296108819041,0.));
#4727=DIRECTION('center_axis',(0.,0.,1.));
#4728=DIRECTION('ref_axis',(0.999997048491126,0.0024296108819041,0.));
#4729=DIRECTION('center_axis',(0.,0.,1.));
#4730=DIRECTION('ref_axis',(0.999997048491126,0.0024296108819041,0.));
#4731=DIRECTION('',(0.,0.,1.));
#4732=DIRECTION('center_axis',(0.999997048491126,0.00242961088176846,0.));
#4733=DIRECTION('ref_axis',(-0.00242961088176846,0.999997048491126,0.));
#4734=DIRECTION('',(0.00242961088176846,-0.999997048491126,0.));
#4735=DIRECTION('',(-0.00242961088176846,0.999997048491126,0.));
#4736=DIRECTION('',(0.,0.,1.));
#4737=DIRECTION('center_axis',(0.,0.,1.));
#4738=DIRECTION('ref_axis',(-0.999997048491126,-0.00242961088185098,0.));
#4739=DIRECTION('center_axis',(0.,0.,1.));
#4740=DIRECTION('ref_axis',(-0.999997048491126,-0.00242961088185098,0.));
#4741=DIRECTION('center_axis',(0.,0.,-1.));
#4742=DIRECTION('ref_axis',(-0.999997048491126,-0.00242961088185098,0.));
#4743=DIRECTION('',(0.,0.,1.));
#4744=DIRECTION('center_axis',(5.38448572143287E-16,1.,0.));
#4745=DIRECTION('ref_axis',(-1.,5.38448572143287E-16,0.));
#4746=DIRECTION('',(-1.,5.38448572143287E-16,0.));
#4747=DIRECTION('',(-1.,5.38448572143287E-16,0.));
#4748=DIRECTION('',(0.,0.,1.));
#4749=DIRECTION('center_axis',(0.,0.,1.));
#4750=DIRECTION('ref_axis',(1.,0.,0.));
#4751=DIRECTION('center_axis',(0.,0.,1.));
#4752=DIRECTION('ref_axis',(1.,0.,0.));
#4753=DIRECTION('center_axis',(0.,0.,1.));
#4754=DIRECTION('ref_axis',(1.,0.,0.));
#4755=DIRECTION('',(0.,0.,1.));
#4756=DIRECTION('center_axis',(1.,7.38226218971534E-15,0.));
#4757=DIRECTION('ref_axis',(-7.38226218971534E-15,1.,0.));
#4758=DIRECTION('',(-7.38226218971534E-15,1.,0.));
#4759=DIRECTION('',(-7.38226218971534E-15,1.,0.));
#4760=DIRECTION('',(0.,0.,1.));
#4761=DIRECTION('center_axis',(0.,0.,1.));
#4762=DIRECTION('ref_axis',(-1.,3.55271367880096E-14,0.));
#4763=DIRECTION('center_axis',(0.,0.,1.));
#4764=DIRECTION('ref_axis',(-1.,3.55271367880096E-14,0.));
#4765=DIRECTION('center_axis',(0.,0.,-1.));
#4766=DIRECTION('ref_axis',(-1.,3.55271367880096E-14,0.));
#4767=DIRECTION('',(0.,0.,1.));
#4768=DIRECTION('center_axis',(0.,1.,0.));
#4769=DIRECTION('ref_axis',(-1.,0.,0.));
#4770=DIRECTION('',(-1.,0.,0.));
#4771=DIRECTION('',(-1.,0.,0.));
#4772=DIRECTION('',(0.,0.,1.));
#4773=DIRECTION('center_axis',(0.,0.,1.));
#4774=DIRECTION('ref_axis',(1.,0.,0.));
#4775=DIRECTION('center_axis',(0.,0.,1.));
#4776=DIRECTION('ref_axis',(1.,0.,0.));
#4777=DIRECTION('center_axis',(0.,0.,1.));
#4778=DIRECTION('ref_axis',(1.,0.,0.));
#4779=DIRECTION('',(0.,0.,1.));
#4780=DIRECTION('center_axis',(1.,0.,0.));
#4781=DIRECTION('ref_axis',(0.,1.,0.));
#4782=DIRECTION('',(0.,1.,0.));
#4783=DIRECTION('',(0.,1.,0.));
#4784=DIRECTION('',(0.,0.,1.));
#4785=DIRECTION('center_axis',(0.,0.,1.));
#4786=DIRECTION('ref_axis',(0.,-1.,0.));
#4787=DIRECTION('center_axis',(0.,0.,1.));
#4788=DIRECTION('ref_axis',(0.,-1.,0.));
#4789=DIRECTION('center_axis',(0.,0.,1.));
#4790=DIRECTION('ref_axis',(0.,-1.,0.));
#4791=DIRECTION('',(0.,0.,1.));
#4792=DIRECTION('center_axis',(0.,-1.,0.));
#4793=DIRECTION('ref_axis',(1.,0.,0.));
#4794=DIRECTION('',(1.,0.,0.));
#4795=DIRECTION('',(1.,0.,0.));
#4796=DIRECTION('',(0.,0.,1.));
#4797=DIRECTION('center_axis',(0.,0.,1.));
#4798=DIRECTION('ref_axis',(0.,1.,0.));
#4799=DIRECTION('center_axis',(0.,0.,1.));
#4800=DIRECTION('ref_axis',(0.,1.,0.));
#4801=DIRECTION('center_axis',(0.,0.,-1.));
#4802=DIRECTION('ref_axis',(0.,1.,0.));
#4803=DIRECTION('',(0.,0.,1.));
#4804=DIRECTION('center_axis',(1.,0.,0.));
#4805=DIRECTION('ref_axis',(0.,1.,0.));
#4806=DIRECTION('',(0.,1.,0.));
#4807=DIRECTION('',(0.,1.,0.));
#4808=DIRECTION('',(0.,0.,1.));
#4809=DIRECTION('center_axis',(0.,0.,1.));
#4810=DIRECTION('ref_axis',(0.,-1.,0.));
#4811=DIRECTION('center_axis',(0.,0.,1.));
#4812=DIRECTION('ref_axis',(0.,-1.,0.));
#4813=DIRECTION('center_axis',(0.,0.,1.));
#4814=DIRECTION('ref_axis',(0.,-1.,0.));
#4815=DIRECTION('',(0.,0.,1.));
#4816=DIRECTION('center_axis',(1.38646972338412E-15,-1.,0.));
#4817=DIRECTION('ref_axis',(1.,1.38646972338412E-15,0.));
#4818=DIRECTION('',(1.,1.38646972338412E-15,0.));
#4819=DIRECTION('',(1.,1.38646972338412E-15,0.));
#4820=DIRECTION('',(0.,0.,1.));
#4821=DIRECTION('center_axis',(0.,0.,1.));
#4822=DIRECTION('ref_axis',(1.4210854715202E-15,1.,0.));
#4823=DIRECTION('center_axis',(0.,0.,1.));
#4824=DIRECTION('ref_axis',(1.4210854715202E-15,1.,0.));
#4825=DIRECTION('center_axis',(0.,0.,-1.));
#4826=DIRECTION('ref_axis',(1.4210854715202E-15,1.,0.));
#4827=DIRECTION('',(0.,0.,1.));
#4828=DIRECTION('center_axis',(0.,0.,1.));
#4829=DIRECTION('ref_axis',(0.00341715401696231,-0.999994161512168,0.));
#4830=DIRECTION('center_axis',(0.,0.,1.));
#4831=DIRECTION('ref_axis',(0.00341715401696231,-0.999994161512168,0.));
#4832=DIRECTION('center_axis',(0.,0.,1.));
#4833=DIRECTION('ref_axis',(0.00341715401696231,-0.999994161512168,0.));
#4834=DIRECTION('',(0.,0.,1.));
#4835=DIRECTION('center_axis',(0.,-1.,0.));
#4836=DIRECTION('ref_axis',(1.,0.,0.));
#4837=DIRECTION('',(1.,0.,0.));
#4838=DIRECTION('',(1.,0.,0.));
#4839=DIRECTION('',(0.,0.,1.));
#4840=DIRECTION('center_axis',(0.,0.,1.));
#4841=DIRECTION('ref_axis',(-1.,3.33066907387548E-15,0.));
#4842=DIRECTION('center_axis',(0.,0.,1.));
#4843=DIRECTION('ref_axis',(-1.,3.33066907387548E-15,0.));
#4844=DIRECTION('center_axis',(0.,0.,1.));
#4845=DIRECTION('ref_axis',(-1.,3.33066907387548E-15,0.));
#4846=DIRECTION('',(0.,0.,1.));
#4847=DIRECTION('center_axis',(-1.,-8.79543806605119E-17,0.));
#4848=DIRECTION('ref_axis',(8.79543806605119E-17,-1.,0.));
#4849=DIRECTION('',(8.79543806605119E-17,-1.,0.));
#4850=DIRECTION('',(8.79543806605119E-17,-1.,0.));
#4851=DIRECTION('',(0.,0.,1.));
#4852=DIRECTION('center_axis',(0.,0.,1.));
#4853=DIRECTION('ref_axis',(1.,0.,0.));
#4854=DIRECTION('center_axis',(0.,0.,1.));
#4855=DIRECTION('ref_axis',(1.,0.,0.));
#4856=DIRECTION('center_axis',(0.,0.,-1.));
#4857=DIRECTION('ref_axis',(1.,0.,0.));
#4858=DIRECTION('',(0.,0.,1.));
#4859=DIRECTION('center_axis',(-0.864293856861593,-0.502987205594052,0.));
#4860=DIRECTION('ref_axis',(0.502987205594051,-0.864293856861593,0.));
#4861=DIRECTION('',(0.502987205594051,-0.864293856861593,0.));
#4862=DIRECTION('',(0.502987205594051,-0.864293856861593,0.));
#4863=DIRECTION('',(0.,0.,1.));
#4864=DIRECTION('center_axis',(0.,0.,1.));
#4865=DIRECTION('ref_axis',(0.864293856861596,0.502987205594046,0.));
#4866=DIRECTION('center_axis',(0.,0.,1.));
#4867=DIRECTION('ref_axis',(0.864293856861596,0.502987205594046,0.));
#4868=DIRECTION('center_axis',(0.,0.,-1.));
#4869=DIRECTION('ref_axis',(0.864293856861596,0.502987205594046,0.));
#4870=DIRECTION('',(0.,0.,1.));
#4871=DIRECTION('center_axis',(0.,0.,1.));
#4872=DIRECTION('ref_axis',(-1.,0.,0.));
#4873=DIRECTION('center_axis',(0.,0.,1.));
#4874=DIRECTION('ref_axis',(-1.,0.,0.));
#4875=DIRECTION('center_axis',(0.,0.,1.));
#4876=DIRECTION('ref_axis',(-1.,0.,0.));
#4877=DIRECTION('',(0.,0.,1.));
#4878=DIRECTION('center_axis',(-1.,-4.7310640253913E-16,0.));
#4879=DIRECTION('ref_axis',(4.7310640253913E-16,-1.,0.));
#4880=DIRECTION('',(4.7310640253913E-16,-1.,0.));
#4881=DIRECTION('',(4.7310640253913E-16,-1.,0.));
#4882=DIRECTION('',(0.,0.,1.));
#4883=DIRECTION('center_axis',(0.,0.,1.));
#4884=DIRECTION('ref_axis',(0.0346253852311105,0.999400361565673,0.));
#4885=DIRECTION('center_axis',(0.,0.,1.));
#4886=DIRECTION('ref_axis',(0.997602165395195,-0.0692092450386402,0.));
#4887=DIRECTION('center_axis',(0.,0.,1.));
#4888=DIRECTION('ref_axis',(0.997602165395195,-0.0692092450386402,0.));
#4889=DIRECTION('',(0.,0.,1.));
#4890=DIRECTION('center_axis',(1.,0.,0.));
#4891=DIRECTION('ref_axis',(0.,1.,0.));
#4892=DIRECTION('',(0.,-1.,0.));
#4893=DIRECTION('',(0.,1.,0.));
#4894=DIRECTION('',(0.,0.,1.));
#4895=DIRECTION('center_axis',(0.,0.,1.));
#4896=DIRECTION('ref_axis',(-2.96059473233374E-15,-1.,0.));
#4897=DIRECTION('center_axis',(0.,0.,1.));
#4898=DIRECTION('ref_axis',(-2.96059473233374E-15,-1.,0.));
#4899=DIRECTION('center_axis',(0.,0.,1.));
#4900=DIRECTION('ref_axis',(-2.96059473233374E-15,-1.,0.));
#4901=DIRECTION('center_axis',(0.,0.,1.));
#4902=DIRECTION('ref_axis',(1.,0.,0.));
#4903=DIRECTION('center_axis',(0.,0.,1.));
#4904=DIRECTION('ref_axis',(1.,0.,0.));
#4905=CARTESIAN_POINT('',(0.,0.,0.));
#4906=CARTESIAN_POINT('Origin',(13.0393814936691,359.414780338643,0.));
#4907=CARTESIAN_POINT('',(10.5218814936691,359.414780338643,6.));
#4908=CARTESIAN_POINT('Origin',(13.0393814936691,359.414780338643,6.));
#4909=CARTESIAN_POINT('',(10.5218814936691,359.414780338643,0.));
#4910=CARTESIAN_POINT('',(10.5218814936691,359.414780338643,0.));
#4911=CARTESIAN_POINT('Origin',(13.0393814936691,359.414780338643,0.));
#4912=CARTESIAN_POINT('Origin',(13.1432904516085,345.,0.));
#4913=CARTESIAN_POINT('',(9.75354045160851,345.,6.));
#4914=CARTESIAN_POINT('Origin',(13.1432904516085,345.,6.));
#4915=CARTESIAN_POINT('',(9.75354045160851,345.,0.));
#4916=CARTESIAN_POINT('',(9.75354045160851,345.,0.));
#4917=CARTESIAN_POINT('Origin',(13.1432904516085,345.,0.));
#4918=CARTESIAN_POINT('Origin',(199.143290451609,74.,0.));
#4919=CARTESIAN_POINT('',(199.143290451609,72.,0.));
#4920=CARTESIAN_POINT('',(201.143290451608,74.,0.));
#4921=CARTESIAN_POINT('Origin',(199.143290451609,74.,0.));
#4922=CARTESIAN_POINT('',(199.143290451609,72.,6.));
#4923=CARTESIAN_POINT('',(199.143290451609,72.,0.));
#4924=CARTESIAN_POINT('',(201.143290451608,74.,6.));
#4925=CARTESIAN_POINT('Origin',(199.143290451609,74.,6.));
#4926=CARTESIAN_POINT('',(201.143290451608,74.,0.));
#4927=CARTESIAN_POINT('Origin',(201.143290451608,266.,0.));
#4928=CARTESIAN_POINT('',(201.143290451608,266.,0.));
#4929=CARTESIAN_POINT('',(201.143290451608,266.,0.));
#4930=CARTESIAN_POINT('',(201.143290451608,266.,6.));
#4931=CARTESIAN_POINT('',(201.143290451608,266.,6.));
#4932=CARTESIAN_POINT('',(201.143290451608,266.,0.));
#4933=CARTESIAN_POINT('Origin',(199.143290451609,266.,0.));
#4934=CARTESIAN_POINT('',(199.143290451609,268.,0.));
#4935=CARTESIAN_POINT('Origin',(199.143290451609,266.,0.));
#4936=CARTESIAN_POINT('',(199.143290451609,268.,6.));
#4937=CARTESIAN_POINT('Origin',(199.143290451609,266.,6.));
#4938=CARTESIAN_POINT('',(199.143290451609,268.,0.));
#4939=CARTESIAN_POINT('Origin',(148.143290451608,268.,0.));
#4940=CARTESIAN_POINT('',(148.143290451608,268.,0.));
#4941=CARTESIAN_POINT('',(148.143290451608,268.,0.));
#4942=CARTESIAN_POINT('',(148.143290451608,268.,6.));
#4943=CARTESIAN_POINT('',(148.143290451608,268.,6.));
#4944=CARTESIAN_POINT('',(148.143290451608,268.,0.));
#4945=CARTESIAN_POINT('Origin',(148.143290451608,266.,0.));
#4946=CARTESIAN_POINT('',(146.143290451608,266.,0.));
#4947=CARTESIAN_POINT('Origin',(148.143290451608,266.,0.));
#4948=CARTESIAN_POINT('',(146.143290451608,266.,6.));
#4949=CARTESIAN_POINT('Origin',(148.143290451608,266.,6.));
#4950=CARTESIAN_POINT('',(146.143290451608,266.,0.));
#4951=CARTESIAN_POINT('Origin',(146.143290451608,74.,0.));
#4952=CARTESIAN_POINT('',(146.143290451608,74.,0.));
#4953=CARTESIAN_POINT('',(146.143290451608,74.,0.));
#4954=CARTESIAN_POINT('',(146.143290451608,74.,6.));
#4955=CARTESIAN_POINT('',(146.143290451608,74.,6.));
#4956=CARTESIAN_POINT('',(146.143290451608,74.,0.));
#4957=CARTESIAN_POINT('Origin',(148.143290451608,74.,0.));
#4958=CARTESIAN_POINT('',(148.143290451608,72.,0.));
#4959=CARTESIAN_POINT('Origin',(148.143290451608,74.,0.));
#4960=CARTESIAN_POINT('',(148.143290451608,72.,6.));
#4961=CARTESIAN_POINT('Origin',(148.143290451608,74.,6.));
#4962=CARTESIAN_POINT('',(148.143290451608,72.,0.));
#4963=CARTESIAN_POINT('Origin',(199.143290451609,72.,0.));
#4964=CARTESIAN_POINT('',(199.143290451609,72.,0.));
#4965=CARTESIAN_POINT('',(199.143290451609,72.,6.));
#4966=CARTESIAN_POINT('Origin',(171.143290451608,336.,0.));
#4967=CARTESIAN_POINT('',(167.753540451609,336.,6.));
#4968=CARTESIAN_POINT('Origin',(171.143290451608,336.,6.));
#4969=CARTESIAN_POINT('',(167.753540451609,336.,0.));
#4970=CARTESIAN_POINT('',(167.753540451609,336.,0.));
#4971=CARTESIAN_POINT('Origin',(171.143290451608,336.,0.));
#4972=CARTESIAN_POINT('Origin',(622.143290451609,341.,0.));
#4973=CARTESIAN_POINT('',(618.753540451609,341.,6.));
#4974=CARTESIAN_POINT('Origin',(622.143290451609,341.,6.));
#4975=CARTESIAN_POINT('',(618.753540451609,341.,0.));
#4976=CARTESIAN_POINT('',(618.753540451609,341.,0.));
#4977=CARTESIAN_POINT('Origin',(622.143290451609,341.,0.));
#4978=CARTESIAN_POINT('Origin',(401.143290451608,342.,0.));
#4979=CARTESIAN_POINT('',(398.625790451609,342.,6.));
#4980=CARTESIAN_POINT('Origin',(401.143290451608,342.,6.));
#4981=CARTESIAN_POINT('',(398.625790451609,342.,0.));
#4982=CARTESIAN_POINT('',(398.625790451609,342.,0.));
#4983=CARTESIAN_POINT('Origin',(401.143290451608,342.,0.));
#4984=CARTESIAN_POINT('Origin',(171.056663602785,347.,0.));
#4985=CARTESIAN_POINT('',(168.539163602785,347.,6.));
#4986=CARTESIAN_POINT('Origin',(171.056663602785,347.,6.));
#4987=CARTESIAN_POINT('',(168.539163602785,347.,0.));
#4988=CARTESIAN_POINT('',(168.539163602785,347.,0.));
#4989=CARTESIAN_POINT('Origin',(171.056663602785,347.,0.));
#4990=CARTESIAN_POINT('Origin',(622.056663602785,349.,0.));
#4991=CARTESIAN_POINT('',(619.539163602785,349.,6.));
#4992=CARTESIAN_POINT('Origin',(622.056663602785,349.,6.));
#4993=CARTESIAN_POINT('',(619.539163602785,349.,0.));
#4994=CARTESIAN_POINT('',(619.539163602785,349.,0.));
#4995=CARTESIAN_POINT('Origin',(622.056663602785,349.,0.));
#4996=CARTESIAN_POINT('Origin',(308.143290451608,278.,0.));
#4997=CARTESIAN_POINT('',(306.477290451608,278.,6.));
#4998=CARTESIAN_POINT('Origin',(308.143290451608,278.,6.));
#4999=CARTESIAN_POINT('',(306.477290451608,278.,0.));
#5000=CARTESIAN_POINT('',(306.477290451608,278.,0.));
#5001=CARTESIAN_POINT('Origin',(308.143290451608,278.,0.));
#5002=CARTESIAN_POINT('Origin',(129.143290451609,283.,0.));
#5003=CARTESIAN_POINT('',(127.477290451609,283.,6.));
#5004=CARTESIAN_POINT('Origin',(129.143290451609,283.,6.));
#5005=CARTESIAN_POINT('',(127.477290451609,283.,0.));
#5006=CARTESIAN_POINT('',(127.477290451609,283.,0.));
#5007=CARTESIAN_POINT('Origin',(129.143290451609,283.,0.));
#5008=CARTESIAN_POINT('Origin',(184.143290451608,279.5,0.));
#5009=CARTESIAN_POINT('',(182.477290451608,279.5,6.));
#5010=CARTESIAN_POINT('Origin',(184.143290451608,279.5,6.));
#5011=CARTESIAN_POINT('',(182.477290451608,279.5,0.));
#5012=CARTESIAN_POINT('',(182.477290451608,279.5,0.));
#5013=CARTESIAN_POINT('Origin',(184.143290451608,279.5,0.));
#5014=CARTESIAN_POINT('Origin',(209.143290451609,259.,0.));
#5015=CARTESIAN_POINT('',(206.625790451609,259.,6.));
#5016=CARTESIAN_POINT('Origin',(209.143290451609,259.,6.));
#5017=CARTESIAN_POINT('',(206.625790451609,259.,0.));
#5018=CARTESIAN_POINT('',(206.625790451609,259.,0.));
#5019=CARTESIAN_POINT('Origin',(209.143290451609,259.,0.));
#5020=CARTESIAN_POINT('Origin',(397.,259.,0.));
#5021=CARTESIAN_POINT('',(394.4825,259.,6.));
#5022=CARTESIAN_POINT('Origin',(397.,259.,6.));
#5023=CARTESIAN_POINT('',(394.4825,259.,0.));
#5024=CARTESIAN_POINT('',(394.4825,259.,0.));
#5025=CARTESIAN_POINT('Origin',(397.,259.,0.));
#5026=CARTESIAN_POINT('Origin',(427.,259.,0.));
#5027=CARTESIAN_POINT('',(424.4825,259.,6.));
#5028=CARTESIAN_POINT('Origin',(427.,259.,6.));
#5029=CARTESIAN_POINT('',(424.4825,259.,0.));
#5030=CARTESIAN_POINT('',(424.4825,259.,0.));
#5031=CARTESIAN_POINT('Origin',(427.,259.,0.));
#5032=CARTESIAN_POINT('Origin',(441.143290451608,259.,0.));
#5033=CARTESIAN_POINT('',(438.625790451609,259.,6.));
#5034=CARTESIAN_POINT('Origin',(441.143290451608,259.,6.));
#5035=CARTESIAN_POINT('',(438.625790451609,259.,0.));
#5036=CARTESIAN_POINT('',(438.625790451609,259.,0.));
#5037=CARTESIAN_POINT('Origin',(441.143290451608,259.,0.));
#5038=CARTESIAN_POINT('Origin',(491.143290451608,259.,0.));
#5039=CARTESIAN_POINT('',(488.625790451609,259.,6.));
#5040=CARTESIAN_POINT('Origin',(491.143290451608,259.,6.));
#5041=CARTESIAN_POINT('',(488.625790451609,259.,0.));
#5042=CARTESIAN_POINT('',(488.625790451609,259.,0.));
#5043=CARTESIAN_POINT('Origin',(491.143290451608,259.,0.));
#5044=CARTESIAN_POINT('Origin',(629.143290451609,264.,0.));
#5045=CARTESIAN_POINT('',(626.625790451609,264.,6.));
#5046=CARTESIAN_POINT('Origin',(629.143290451609,264.,6.));
#5047=CARTESIAN_POINT('',(626.625790451609,264.,0.));
#5048=CARTESIAN_POINT('',(626.625790451609,264.,0.));
#5049=CARTESIAN_POINT('Origin',(629.143290451609,264.,0.));
#5050=CARTESIAN_POINT('Origin',(647.143290451609,310.247066304291,0.));
#5051=CARTESIAN_POINT('',(644.625790451609,310.247066304291,6.));
#5052=CARTESIAN_POINT('Origin',(647.143290451609,310.247066304291,6.));
#5053=CARTESIAN_POINT('',(644.625790451609,310.247066304291,0.));
#5054=CARTESIAN_POINT('',(644.625790451609,310.247066304291,0.));
#5055=CARTESIAN_POINT('Origin',(647.143290451609,310.247066304291,0.));
#5056=CARTESIAN_POINT('Origin',(717.,258.,0.));
#5057=CARTESIAN_POINT('',(715.334,258.,6.));
#5058=CARTESIAN_POINT('Origin',(717.,258.,6.));
#5059=CARTESIAN_POINT('',(715.334,258.,0.));
#5060=CARTESIAN_POINT('',(715.334,258.,0.));
#5061=CARTESIAN_POINT('Origin',(717.,258.,0.));
#5062=CARTESIAN_POINT('Origin',(767.143290451609,263.,0.));
#5063=CARTESIAN_POINT('',(765.477290451608,263.,6.));
#5064=CARTESIAN_POINT('Origin',(767.143290451609,263.,6.));
#5065=CARTESIAN_POINT('',(765.477290451608,263.,0.));
#5066=CARTESIAN_POINT('',(765.477290451608,263.,0.));
#5067=CARTESIAN_POINT('Origin',(767.143290451609,263.,0.));
#5068=CARTESIAN_POINT('Origin',(760.143290451608,91.2,0.));
#5069=CARTESIAN_POINT('',(758.477290451608,91.2,6.));
#5070=CARTESIAN_POINT('Origin',(760.143290451608,91.2,6.));
#5071=CARTESIAN_POINT('',(758.477290451608,91.2,0.));
#5072=CARTESIAN_POINT('',(758.477290451608,91.2,0.));
#5073=CARTESIAN_POINT('Origin',(760.143290451608,91.2,0.));
#5074=CARTESIAN_POINT('Origin',(744.143290451609,69.,0.));
#5075=CARTESIAN_POINT('',(742.477290451608,69.,6.));
#5076=CARTESIAN_POINT('Origin',(744.143290451609,69.,6.));
#5077=CARTESIAN_POINT('',(742.477290451608,69.,0.));
#5078=CARTESIAN_POINT('',(742.477290451608,69.,0.));
#5079=CARTESIAN_POINT('Origin',(744.143290451609,69.,0.));
#5080=CARTESIAN_POINT('Origin',(332.143290451609,10.,0.));
#5081=CARTESIAN_POINT('',(330.477290451608,10.,6.));
#5082=CARTESIAN_POINT('Origin',(332.143290451609,10.,6.));
#5083=CARTESIAN_POINT('',(330.477290451608,10.,0.));
#5084=CARTESIAN_POINT('',(330.477290451608,10.,0.));
#5085=CARTESIAN_POINT('Origin',(332.143290451609,10.,0.));
#5086=CARTESIAN_POINT('Origin',(232.143290451609,15.,0.));
#5087=CARTESIAN_POINT('',(230.477290451609,15.,6.));
#5088=CARTESIAN_POINT('Origin',(232.143290451609,15.,6.));
#5089=CARTESIAN_POINT('',(230.477290451609,15.,0.));
#5090=CARTESIAN_POINT('',(230.477290451609,15.,0.));
#5091=CARTESIAN_POINT('Origin',(232.143290451609,15.,0.));
#5092=CARTESIAN_POINT('Origin',(89.0000000000001,16.,0.));
#5093=CARTESIAN_POINT('',(86.4825000000001,16.,6.));
#5094=CARTESIAN_POINT('Origin',(89.0000000000001,16.,6.));
#5095=CARTESIAN_POINT('',(86.4825000000001,16.,0.));
#5096=CARTESIAN_POINT('',(86.4825000000001,16.,0.));
#5097=CARTESIAN_POINT('Origin',(89.0000000000001,16.,0.));
#5098=CARTESIAN_POINT('Origin',(127.,16.,0.));
#5099=CARTESIAN_POINT('',(124.4825,16.,6.));
#5100=CARTESIAN_POINT('Origin',(127.,16.,6.));
#5101=CARTESIAN_POINT('',(124.4825,16.,0.));
#5102=CARTESIAN_POINT('',(124.4825,16.,0.));
#5103=CARTESIAN_POINT('Origin',(127.,16.,0.));
#5104=CARTESIAN_POINT('Origin',(427.,16.,0.));
#5105=CARTESIAN_POINT('',(424.4825,16.,6.));
#5106=CARTESIAN_POINT('Origin',(427.,16.,6.));
#5107=CARTESIAN_POINT('',(424.4825,16.,0.));
#5108=CARTESIAN_POINT('',(424.4825,16.,0.));
#5109=CARTESIAN_POINT('Origin',(427.,16.,0.));
#5110=CARTESIAN_POINT('Origin',(209.,71.,0.));
#5111=CARTESIAN_POINT('',(206.4825,71.,6.));
#5112=CARTESIAN_POINT('Origin',(209.,71.,6.));
#5113=CARTESIAN_POINT('',(206.4825,71.,0.));
#5114=CARTESIAN_POINT('',(206.4825,71.,0.));
#5115=CARTESIAN_POINT('Origin',(209.,71.,0.));
#5116=CARTESIAN_POINT('Origin',(397.,71.,0.));
#5117=CARTESIAN_POINT('',(394.4825,71.,6.));
#5118=CARTESIAN_POINT('Origin',(397.,71.,6.));
#5119=CARTESIAN_POINT('',(394.4825,71.,0.));
#5120=CARTESIAN_POINT('',(394.4825,71.,0.));
#5121=CARTESIAN_POINT('Origin',(397.,71.,0.));
#5122=CARTESIAN_POINT('Origin',(427.,71.,0.));
#5123=CARTESIAN_POINT('',(424.4825,71.,6.));
#5124=CARTESIAN_POINT('Origin',(427.,71.,6.));
#5125=CARTESIAN_POINT('',(424.4825,71.,0.));
#5126=CARTESIAN_POINT('',(424.4825,71.,0.));
#5127=CARTESIAN_POINT('Origin',(427.,71.,0.));
#5128=CARTESIAN_POINT('Origin',(441.,71.,0.));
#5129=CARTESIAN_POINT('',(438.4825,71.,6.));
#5130=CARTESIAN_POINT('Origin',(441.,71.,6.));
#5131=CARTESIAN_POINT('',(438.4825,71.,0.));
#5132=CARTESIAN_POINT('',(438.4825,71.,0.));
#5133=CARTESIAN_POINT('Origin',(441.,71.,0.));
#5134=CARTESIAN_POINT('Origin',(491.,71.,0.));
#5135=CARTESIAN_POINT('',(488.4825,71.,6.));
#5136=CARTESIAN_POINT('Origin',(491.,71.,6.));
#5137=CARTESIAN_POINT('',(488.4825,71.,0.));
#5138=CARTESIAN_POINT('',(488.4825,71.,0.));
#5139=CARTESIAN_POINT('Origin',(491.,71.,0.));
#5140=CARTESIAN_POINT('Origin',(629.143290451609,76.,0.));
#5141=CARTESIAN_POINT('',(626.625790451609,76.,6.));
#5142=CARTESIAN_POINT('Origin',(629.143290451609,76.,6.));
#5143=CARTESIAN_POINT('',(626.625790451609,76.,0.));
#5144=CARTESIAN_POINT('',(626.625790451609,76.,0.));
#5145=CARTESIAN_POINT('Origin',(629.143290451609,76.,0.));
#5146=CARTESIAN_POINT('Origin',(744.143290451609,130.,0.));
#5147=CARTESIAN_POINT('',(737.143290451609,130.,6.));
#5148=CARTESIAN_POINT('Origin',(744.143290451609,130.,6.));
#5149=CARTESIAN_POINT('',(737.143290451609,130.,0.));
#5150=CARTESIAN_POINT('',(737.143290451609,130.,0.));
#5151=CARTESIAN_POINT('Origin',(744.143290451609,130.,0.));
#5152=CARTESIAN_POINT('Origin',(89.1432904516086,130.,0.));
#5153=CARTESIAN_POINT('',(82.1432904516086,130.,6.));
#5154=CARTESIAN_POINT('Origin',(89.1432904516086,130.,6.));
#5155=CARTESIAN_POINT('',(82.1432904516086,130.,0.));
#5156=CARTESIAN_POINT('',(82.1432904516086,130.,0.));
#5157=CARTESIAN_POINT('Origin',(89.1432904516086,130.,0.));
#5158=CARTESIAN_POINT('Origin',(228.143290451608,254.,0.));
#5159=CARTESIAN_POINT('',(228.143290451608,256.,0.));
#5160=CARTESIAN_POINT('',(226.143290451608,254.,0.));
#5161=CARTESIAN_POINT('Origin',(228.143290451608,254.,0.));
#5162=CARTESIAN_POINT('',(228.143290451608,256.,6.));
#5163=CARTESIAN_POINT('',(228.143290451608,256.,0.));
#5164=CARTESIAN_POINT('',(226.143290451608,254.,6.));
#5165=CARTESIAN_POINT('Origin',(228.143290451608,254.,6.));
#5166=CARTESIAN_POINT('',(226.143290451608,254.,0.));
#5167=CARTESIAN_POINT('Origin',(226.143290451608,86.,0.));
#5168=CARTESIAN_POINT('',(226.143290451608,86.,0.));
#5169=CARTESIAN_POINT('',(226.143290451608,86.,0.));
#5170=CARTESIAN_POINT('',(226.143290451608,86.,6.));
#5171=CARTESIAN_POINT('',(226.143290451608,86.,6.));
#5172=CARTESIAN_POINT('',(226.143290451608,86.,0.));
#5173=CARTESIAN_POINT('Origin',(228.143290451608,86.,0.));
#5174=CARTESIAN_POINT('',(228.143290451608,84.,0.));
#5175=CARTESIAN_POINT('Origin',(228.143290451608,86.,0.));
#5176=CARTESIAN_POINT('',(228.143290451608,84.,6.));
#5177=CARTESIAN_POINT('Origin',(228.143290451608,86.,6.));
#5178=CARTESIAN_POINT('',(228.143290451608,84.,0.));
#5179=CARTESIAN_POINT('Origin',(344.143290451609,84.,0.));
#5180=CARTESIAN_POINT('',(344.143290451609,84.,0.));
#5181=CARTESIAN_POINT('',(344.143290451609,84.,0.));
#5182=CARTESIAN_POINT('',(344.143290451609,84.,6.));
#5183=CARTESIAN_POINT('',(344.143290451609,84.,6.));
#5184=CARTESIAN_POINT('',(344.143290451609,84.,0.));
#5185=CARTESIAN_POINT('Origin',(344.143290451609,86.,0.));
#5186=CARTESIAN_POINT('',(346.143290451608,86.,0.));
#5187=CARTESIAN_POINT('Origin',(344.143290451609,86.,0.));
#5188=CARTESIAN_POINT('',(346.143290451608,86.,6.));
#5189=CARTESIAN_POINT('Origin',(344.143290451609,86.,6.));
#5190=CARTESIAN_POINT('',(346.143290451608,86.,0.));
#5191=CARTESIAN_POINT('Origin',(346.143290451608,254.,0.));
#5192=CARTESIAN_POINT('',(346.143290451608,254.,0.));
#5193=CARTESIAN_POINT('',(346.143290451608,254.,0.));
#5194=CARTESIAN_POINT('',(346.143290451608,254.,6.));
#5195=CARTESIAN_POINT('',(346.143290451608,254.,6.));
#5196=CARTESIAN_POINT('',(346.143290451608,254.,0.));
#5197=CARTESIAN_POINT('Origin',(344.143290451608,254.,0.));
#5198=CARTESIAN_POINT('',(344.143290451608,256.,0.));
#5199=CARTESIAN_POINT('Origin',(344.143290451608,254.,0.));
#5200=CARTESIAN_POINT('',(344.143290451608,256.,6.));
#5201=CARTESIAN_POINT('Origin',(344.143290451608,254.,6.));
#5202=CARTESIAN_POINT('',(344.143290451608,256.,0.));
#5203=CARTESIAN_POINT('Origin',(228.143290451608,256.,0.));
#5204=CARTESIAN_POINT('',(228.143290451608,256.,0.));
#5205=CARTESIAN_POINT('',(228.143290451608,256.,6.));
#5206=CARTESIAN_POINT('Origin',(373.143290451608,249.,0.));
#5207=CARTESIAN_POINT('',(373.143290451608,251.,0.));
#5208=CARTESIAN_POINT('',(371.143290451608,249.,0.));
#5209=CARTESIAN_POINT('Origin',(373.143290451608,249.,0.));
#5210=CARTESIAN_POINT('',(373.143290451608,251.,6.));
#5211=CARTESIAN_POINT('',(373.143290451608,251.,0.));
#5212=CARTESIAN_POINT('',(371.143290451608,249.,6.));
#5213=CARTESIAN_POINT('Origin',(373.143290451608,249.,6.));
#5214=CARTESIAN_POINT('',(371.143290451608,249.,0.));
#5215=CARTESIAN_POINT('Origin',(371.143290451608,81.,0.));
#5216=CARTESIAN_POINT('',(371.143290451608,81.,0.));
#5217=CARTESIAN_POINT('',(371.143290451608,81.,0.));
#5218=CARTESIAN_POINT('',(371.143290451608,81.,6.));
#5219=CARTESIAN_POINT('',(371.143290451608,81.,6.));
#5220=CARTESIAN_POINT('',(371.143290451608,81.,0.));
#5221=CARTESIAN_POINT('Origin',(373.143290451609,81.,0.));
#5222=CARTESIAN_POINT('',(373.143290451609,78.9999999999999,0.));
#5223=CARTESIAN_POINT('Origin',(373.143290451609,81.,0.));
#5224=CARTESIAN_POINT('',(373.143290451609,78.9999999999999,6.));
#5225=CARTESIAN_POINT('Origin',(373.143290451609,81.,6.));
#5226=CARTESIAN_POINT('',(373.143290451609,78.9999999999999,0.));
#5227=CARTESIAN_POINT('Origin',(460.143290451609,78.9999999999999,0.));
#5228=CARTESIAN_POINT('',(460.143290451609,78.9999999999999,0.));
#5229=CARTESIAN_POINT('',(460.143290451609,78.9999999999999,0.));
#5230=CARTESIAN_POINT('',(460.143290451609,78.9999999999999,6.));
#5231=CARTESIAN_POINT('',(460.143290451609,78.9999999999999,6.));
#5232=CARTESIAN_POINT('',(460.143290451609,78.9999999999999,0.));
#5233=CARTESIAN_POINT('Origin',(460.143290451609,80.9999999999999,0.));
#5234=CARTESIAN_POINT('',(462.143290451609,80.9999999999999,0.));
#5235=CARTESIAN_POINT('Origin',(460.143290451609,80.9999999999999,0.));
#5236=CARTESIAN_POINT('',(462.143290451609,80.9999999999999,6.));
#5237=CARTESIAN_POINT('Origin',(460.143290451609,80.9999999999999,6.));
#5238=CARTESIAN_POINT('',(462.143290451609,80.9999999999999,0.));
#5239=CARTESIAN_POINT('Origin',(462.143290451609,249.,0.));
#5240=CARTESIAN_POINT('',(462.143290451609,249.,0.));
#5241=CARTESIAN_POINT('',(462.143290451609,249.,0.));
#5242=CARTESIAN_POINT('',(462.143290451609,249.,6.));
#5243=CARTESIAN_POINT('',(462.143290451609,249.,6.));
#5244=CARTESIAN_POINT('',(462.143290451609,249.,0.));
#5245=CARTESIAN_POINT('Origin',(460.143290451608,249.,0.));
#5246=CARTESIAN_POINT('',(460.143290451608,251.,0.));
#5247=CARTESIAN_POINT('Origin',(460.143290451608,249.,0.));
#5248=CARTESIAN_POINT('',(460.143290451608,251.,6.));
#5249=CARTESIAN_POINT('Origin',(460.143290451608,249.,6.));
#5250=CARTESIAN_POINT('',(460.143290451608,251.,0.));
#5251=CARTESIAN_POINT('Origin',(373.143290451608,251.,0.));
#5252=CARTESIAN_POINT('',(373.143290451608,251.,0.));
#5253=CARTESIAN_POINT('',(373.143290451608,251.,6.));
#5254=CARTESIAN_POINT('Origin',(484.143290451609,249.,0.));
#5255=CARTESIAN_POINT('',(484.143290451609,251.,0.));
#5256=CARTESIAN_POINT('',(482.143290451609,249.,0.));
#5257=CARTESIAN_POINT('Origin',(484.143290451609,249.,0.));
#5258=CARTESIAN_POINT('',(484.143290451609,251.,6.));
#5259=CARTESIAN_POINT('',(484.143290451609,251.,0.));
#5260=CARTESIAN_POINT('',(482.143290451609,249.,6.));
#5261=CARTESIAN_POINT('Origin',(484.143290451609,249.,6.));
#5262=CARTESIAN_POINT('',(482.143290451609,249.,0.));
#5263=CARTESIAN_POINT('Origin',(482.143290451609,81.,0.));
#5264=CARTESIAN_POINT('',(482.143290451609,81.,0.));
#5265=CARTESIAN_POINT('',(482.143290451609,81.,0.));
#5266=CARTESIAN_POINT('',(482.143290451609,81.,6.));
#5267=CARTESIAN_POINT('',(482.143290451609,81.,6.));
#5268=CARTESIAN_POINT('',(482.143290451609,81.,0.));
#5269=CARTESIAN_POINT('Origin',(484.143290451609,81.,0.));
#5270=CARTESIAN_POINT('',(484.143290451609,78.9999999999999,0.));
#5271=CARTESIAN_POINT('Origin',(484.143290451609,81.,0.));
#5272=CARTESIAN_POINT('',(484.143290451609,78.9999999999999,6.));
#5273=CARTESIAN_POINT('Origin',(484.143290451609,81.,6.));
#5274=CARTESIAN_POINT('',(484.143290451609,78.9999999999999,0.));
#5275=CARTESIAN_POINT('Origin',(600.143290451609,78.9999999999999,0.));
#5276=CARTESIAN_POINT('',(600.143290451609,78.9999999999999,0.));
#5277=CARTESIAN_POINT('',(600.143290451609,78.9999999999999,0.));
#5278=CARTESIAN_POINT('',(600.143290451609,78.9999999999999,6.));
#5279=CARTESIAN_POINT('',(600.143290451609,78.9999999999999,6.));
#5280=CARTESIAN_POINT('',(600.143290451609,78.9999999999999,0.));
#5281=CARTESIAN_POINT('Origin',(600.143290451609,80.9999999999999,0.));
#5282=CARTESIAN_POINT('',(602.143290451609,80.9999999999999,0.));
#5283=CARTESIAN_POINT('Origin',(600.143290451609,80.9999999999999,0.));
#5284=CARTESIAN_POINT('',(602.143290451609,80.9999999999999,6.));
#5285=CARTESIAN_POINT('Origin',(600.143290451609,80.9999999999999,6.));
#5286=CARTESIAN_POINT('',(602.143290451609,80.9999999999999,0.));
#5287=CARTESIAN_POINT('Origin',(602.143290451609,249.,0.));
#5288=CARTESIAN_POINT('',(602.143290451609,249.,0.));
#5289=CARTESIAN_POINT('',(602.143290451609,249.,0.));
#5290=CARTESIAN_POINT('',(602.143290451609,249.,6.));
#5291=CARTESIAN_POINT('',(602.143290451609,249.,6.));
#5292=CARTESIAN_POINT('',(602.143290451609,249.,0.));
#5293=CARTESIAN_POINT('Origin',(600.143290451608,249.,0.));
#5294=CARTESIAN_POINT('',(600.143290451608,251.,0.));
#5295=CARTESIAN_POINT('Origin',(600.143290451608,249.,0.));
#5296=CARTESIAN_POINT('',(600.143290451608,251.,6.));
#5297=CARTESIAN_POINT('Origin',(600.143290451608,249.,6.));
#5298=CARTESIAN_POINT('',(600.143290451608,251.,0.));
#5299=CARTESIAN_POINT('Origin',(484.143290451609,251.,0.));
#5300=CARTESIAN_POINT('',(484.143290451609,251.,0.));
#5301=CARTESIAN_POINT('',(484.143290451609,251.,6.));
#5302=CARTESIAN_POINT('Origin',(637.,230.6,0.));
#5303=CARTESIAN_POINT('',(637.,261.,0.));
#5304=CARTESIAN_POINT('',(637.,230.6,0.));
#5305=CARTESIAN_POINT('',(637.,261.,0.));
#5306=CARTESIAN_POINT('',(637.,261.,6.));
#5307=CARTESIAN_POINT('',(637.,261.,0.));
#5308=CARTESIAN_POINT('',(637.,230.6,6.));
#5309=CARTESIAN_POINT('',(637.,261.,6.));
#5310=CARTESIAN_POINT('',(637.,230.6,0.));
#5311=CARTESIAN_POINT('Origin',(639.,230.6,0.));
#5312=CARTESIAN_POINT('',(639.,228.6,0.));
#5313=CARTESIAN_POINT('Origin',(639.,230.6,0.));
#5314=CARTESIAN_POINT('',(639.,228.6,6.));
#5315=CARTESIAN_POINT('Origin',(639.,230.6,6.));
#5316=CARTESIAN_POINT('',(639.,228.6,0.));
#5317=CARTESIAN_POINT('Origin',(690.,228.6,0.));
#5318=CARTESIAN_POINT('',(690.,228.6,0.));
#5319=CARTESIAN_POINT('',(639.,228.6,0.));
#5320=CARTESIAN_POINT('',(690.,228.6,6.));
#5321=CARTESIAN_POINT('',(639.,228.6,6.));
#5322=CARTESIAN_POINT('',(690.,228.6,0.));
#5323=CARTESIAN_POINT('Origin',(690.,230.6,0.));
#5324=CARTESIAN_POINT('',(692.,230.6,0.));
#5325=CARTESIAN_POINT('Origin',(690.,230.6,0.));
#5326=CARTESIAN_POINT('',(692.,230.6,6.));
#5327=CARTESIAN_POINT('Origin',(690.,230.6,6.));
#5328=CARTESIAN_POINT('',(692.,230.6,0.));
#5329=CARTESIAN_POINT('Origin',(692.,261.,0.));
#5330=CARTESIAN_POINT('',(692.,261.,0.));
#5331=CARTESIAN_POINT('',(692.,230.6,0.));
#5332=CARTESIAN_POINT('',(692.,261.,6.));
#5333=CARTESIAN_POINT('',(692.,230.6,6.));
#5334=CARTESIAN_POINT('',(692.,261.,0.));
#5335=CARTESIAN_POINT('Origin',(690.,261.,0.));
#5336=CARTESIAN_POINT('',(690.,263.,0.));
#5337=CARTESIAN_POINT('Origin',(690.,261.,0.));
#5338=CARTESIAN_POINT('',(690.,263.,6.));
#5339=CARTESIAN_POINT('Origin',(690.,261.,6.));
#5340=CARTESIAN_POINT('',(690.,263.,0.));
#5341=CARTESIAN_POINT('Origin',(639.,263.,0.));
#5342=CARTESIAN_POINT('',(639.,263.,0.));
#5343=CARTESIAN_POINT('',(690.,263.,0.));
#5344=CARTESIAN_POINT('',(639.,263.,6.));
#5345=CARTESIAN_POINT('',(690.,263.,6.));
#5346=CARTESIAN_POINT('',(639.,263.,0.));
#5347=CARTESIAN_POINT('Origin',(639.,261.,0.));
#5348=CARTESIAN_POINT('Origin',(639.,261.,0.));
#5349=CARTESIAN_POINT('Origin',(639.,261.,6.));
#5350=CARTESIAN_POINT('Origin',(639.,190.2,0.));
#5351=CARTESIAN_POINT('',(637.,190.2,0.));
#5352=CARTESIAN_POINT('',(639.,188.2,0.));
#5353=CARTESIAN_POINT('Origin',(639.,190.2,0.));
#5354=CARTESIAN_POINT('',(637.,190.2,6.));
#5355=CARTESIAN_POINT('',(637.,190.2,0.));
#5356=CARTESIAN_POINT('',(639.,188.2,6.));
#5357=CARTESIAN_POINT('Origin',(639.,190.2,6.));
#5358=CARTESIAN_POINT('',(639.,188.2,0.));
#5359=CARTESIAN_POINT('Origin',(690.,188.2,0.));
#5360=CARTESIAN_POINT('',(690.,188.2,0.));
#5361=CARTESIAN_POINT('',(639.,188.2,0.));
#5362=CARTESIAN_POINT('',(690.,188.2,6.));
#5363=CARTESIAN_POINT('',(639.,188.2,6.));
#5364=CARTESIAN_POINT('',(690.,188.2,0.));
#5365=CARTESIAN_POINT('Origin',(690.,190.2,0.));
#5366=CARTESIAN_POINT('',(692.,190.2,0.));
#5367=CARTESIAN_POINT('Origin',(690.,190.2,0.));
#5368=CARTESIAN_POINT('',(692.,190.2,6.));
#5369=CARTESIAN_POINT('Origin',(690.,190.2,6.));
#5370=CARTESIAN_POINT('',(692.,190.2,0.));
#5371=CARTESIAN_POINT('Origin',(692.,220.6,0.));
#5372=CARTESIAN_POINT('',(692.,220.6,0.));
#5373=CARTESIAN_POINT('',(692.,190.2,0.));
#5374=CARTESIAN_POINT('',(692.,220.6,6.));
#5375=CARTESIAN_POINT('',(692.,190.2,6.));
#5376=CARTESIAN_POINT('',(692.,220.6,0.));
#5377=CARTESIAN_POINT('Origin',(690.,220.6,0.));
#5378=CARTESIAN_POINT('',(690.,222.6,0.));
#5379=CARTESIAN_POINT('Origin',(690.,220.6,0.));
#5380=CARTESIAN_POINT('',(690.,222.6,6.));
#5381=CARTESIAN_POINT('Origin',(690.,220.6,6.));
#5382=CARTESIAN_POINT('',(690.,222.6,0.));
#5383=CARTESIAN_POINT('Origin',(639.,222.6,0.));
#5384=CARTESIAN_POINT('',(639.,222.6,0.));
#5385=CARTESIAN_POINT('',(690.,222.6,0.));
#5386=CARTESIAN_POINT('',(639.,222.6,6.));
#5387=CARTESIAN_POINT('',(690.,222.6,6.));
#5388=CARTESIAN_POINT('',(639.,222.6,0.));
#5389=CARTESIAN_POINT('Origin',(639.,220.6,0.));
#5390=CARTESIAN_POINT('',(637.,220.6,0.));
#5391=CARTESIAN_POINT('Origin',(639.,220.6,0.));
#5392=CARTESIAN_POINT('',(637.,220.6,6.));
#5393=CARTESIAN_POINT('Origin',(639.,220.6,6.));
#5394=CARTESIAN_POINT('',(637.,220.6,0.));
#5395=CARTESIAN_POINT('Origin',(637.,190.2,0.));
#5396=CARTESIAN_POINT('',(637.,220.6,0.));
#5397=CARTESIAN_POINT('',(637.,220.6,6.));
#5398=CARTESIAN_POINT('Origin',(690.,180.2,0.));
#5399=CARTESIAN_POINT('',(692.,180.2,0.));
#5400=CARTESIAN_POINT('',(690.,182.2,0.));
#5401=CARTESIAN_POINT('Origin',(690.,180.2,0.));
#5402=CARTESIAN_POINT('',(692.,180.2,6.));
#5403=CARTESIAN_POINT('',(692.,180.2,0.));
#5404=CARTESIAN_POINT('',(690.,182.2,6.));
#5405=CARTESIAN_POINT('Origin',(690.,180.2,6.));
#5406=CARTESIAN_POINT('',(690.,182.2,0.));
#5407=CARTESIAN_POINT('Origin',(639.,182.2,0.));
#5408=CARTESIAN_POINT('',(639.,182.2,0.));
#5409=CARTESIAN_POINT('',(690.,182.2,0.));
#5410=CARTESIAN_POINT('',(639.,182.2,6.));
#5411=CARTESIAN_POINT('',(690.,182.2,6.));
#5412=CARTESIAN_POINT('',(639.,182.2,0.));
#5413=CARTESIAN_POINT('Origin',(639.,180.2,0.));
#5414=CARTESIAN_POINT('',(637.,180.2,0.));
#5415=CARTESIAN_POINT('Origin',(639.,180.2,0.));
#5416=CARTESIAN_POINT('',(637.,180.2,6.));
#5417=CARTESIAN_POINT('Origin',(639.,180.2,6.));
#5418=CARTESIAN_POINT('',(637.,180.2,0.));
#5419=CARTESIAN_POINT('Origin',(637.,149.8,0.));
#5420=CARTESIAN_POINT('',(637.,149.8,0.));
#5421=CARTESIAN_POINT('',(637.,180.2,0.));
#5422=CARTESIAN_POINT('',(637.,149.8,6.));
#5423=CARTESIAN_POINT('',(637.,180.2,6.));
#5424=CARTESIAN_POINT('',(637.,149.8,0.));
#5425=CARTESIAN_POINT('Origin',(639.,149.8,0.));
#5426=CARTESIAN_POINT('',(639.,147.8,0.));
#5427=CARTESIAN_POINT('Origin',(639.,149.8,0.));
#5428=CARTESIAN_POINT('',(639.,147.8,6.));
#5429=CARTESIAN_POINT('Origin',(639.,149.8,6.));
#5430=CARTESIAN_POINT('',(639.,147.8,0.));
#5431=CARTESIAN_POINT('Origin',(690.,147.8,0.));
#5432=CARTESIAN_POINT('',(690.,147.8,0.));
#5433=CARTESIAN_POINT('',(639.,147.8,0.));
#5434=CARTESIAN_POINT('',(690.,147.8,6.));
#5435=CARTESIAN_POINT('',(639.,147.8,6.));
#5436=CARTESIAN_POINT('',(690.,147.8,0.));
#5437=CARTESIAN_POINT('Origin',(690.,149.8,0.));
#5438=CARTESIAN_POINT('',(692.,149.8,0.));
#5439=CARTESIAN_POINT('Origin',(690.,149.8,0.));
#5440=CARTESIAN_POINT('',(692.,149.8,6.));
#5441=CARTESIAN_POINT('Origin',(690.,149.8,6.));
#5442=CARTESIAN_POINT('',(692.,149.8,0.));
#5443=CARTESIAN_POINT('Origin',(692.,180.2,0.));
#5444=CARTESIAN_POINT('',(692.,149.8,0.));
#5445=CARTESIAN_POINT('',(692.,149.8,6.));
#5446=CARTESIAN_POINT('Origin',(638.875208004294,114.4,0.));
#5447=CARTESIAN_POINT('',(636.875208004294,114.4,0.));
#5448=CARTESIAN_POINT('',(638.875208004294,112.4,0.));
#5449=CARTESIAN_POINT('Origin',(638.875208004294,114.4,0.));
#5450=CARTESIAN_POINT('',(636.875208004294,114.4,6.));
#5451=CARTESIAN_POINT('',(636.875208004294,114.4,0.));
#5452=CARTESIAN_POINT('',(638.875208004294,112.4,6.));
#5453=CARTESIAN_POINT('Origin',(638.875208004294,114.4,6.));
#5454=CARTESIAN_POINT('',(638.875208004294,112.4,0.));
#5455=CARTESIAN_POINT('Origin',(689.875208004294,112.4,0.));
#5456=CARTESIAN_POINT('',(689.875208004294,112.4,0.));
#5457=CARTESIAN_POINT('',(638.875208004294,112.4,0.));
#5458=CARTESIAN_POINT('',(689.875208004294,112.4,6.));
#5459=CARTESIAN_POINT('',(638.875208004294,112.4,6.));
#5460=CARTESIAN_POINT('',(689.875208004294,112.4,0.));
#5461=CARTESIAN_POINT('Origin',(689.875208004294,114.4,0.));
#5462=CARTESIAN_POINT('',(691.875208004294,114.4,0.));
#5463=CARTESIAN_POINT('Origin',(689.875208004294,114.4,0.));
#5464=CARTESIAN_POINT('',(691.875208004294,114.4,6.));
#5465=CARTESIAN_POINT('Origin',(689.875208004294,114.4,6.));
#5466=CARTESIAN_POINT('',(691.875208004294,114.4,0.));
#5467=CARTESIAN_POINT('Origin',(691.875208004294,144.8,0.));
#5468=CARTESIAN_POINT('',(691.875208004294,144.8,0.));
#5469=CARTESIAN_POINT('',(691.875208004294,114.4,0.));
#5470=CARTESIAN_POINT('',(691.875208004294,144.8,6.));
#5471=CARTESIAN_POINT('',(691.875208004294,114.4,6.));
#5472=CARTESIAN_POINT('',(691.875208004294,144.8,0.));
#5473=CARTESIAN_POINT('Origin',(689.875208004293,144.8,0.));
#5474=CARTESIAN_POINT('',(689.875208004294,146.8,0.));
#5475=CARTESIAN_POINT('Origin',(689.875208004293,144.8,0.));
#5476=CARTESIAN_POINT('',(689.875208004294,146.8,6.));
#5477=CARTESIAN_POINT('Origin',(689.875208004293,144.8,6.));
#5478=CARTESIAN_POINT('',(689.875208004294,146.8,0.));
#5479=CARTESIAN_POINT('Origin',(638.875208004294,146.8,0.));
#5480=CARTESIAN_POINT('',(638.875208004294,146.8,0.));
#5481=CARTESIAN_POINT('',(689.875208004294,146.8,0.));
#5482=CARTESIAN_POINT('',(638.875208004294,146.8,6.));
#5483=CARTESIAN_POINT('',(689.875208004294,146.8,6.));
#5484=CARTESIAN_POINT('',(638.875208004294,146.8,0.));
#5485=CARTESIAN_POINT('Origin',(638.875208004294,144.8,0.));
#5486=CARTESIAN_POINT('',(636.875208004294,144.8,0.));
#5487=CARTESIAN_POINT('Origin',(638.875208004294,144.8,0.));
#5488=CARTESIAN_POINT('',(636.875208004294,144.8,6.));
#5489=CARTESIAN_POINT('Origin',(638.875208004294,144.8,6.));
#5490=CARTESIAN_POINT('',(636.875208004294,144.8,0.));
#5491=CARTESIAN_POINT('Origin',(636.875208004294,114.4,0.));
#5492=CARTESIAN_POINT('',(636.875208004294,144.8,0.));
#5493=CARTESIAN_POINT('',(636.875208004294,144.8,6.));
#5494=CARTESIAN_POINT('Origin',(690.143290451609,104.4,0.));
#5495=CARTESIAN_POINT('',(692.143290451609,104.4,0.));
#5496=CARTESIAN_POINT('',(690.143290451609,106.4,0.));
#5497=CARTESIAN_POINT('Origin',(690.143290451609,104.4,0.));
#5498=CARTESIAN_POINT('',(692.143290451609,104.4,6.));
#5499=CARTESIAN_POINT('',(692.143290451609,104.4,0.));
#5500=CARTESIAN_POINT('',(690.143290451609,106.4,6.));
#5501=CARTESIAN_POINT('Origin',(690.143290451609,104.4,6.));
#5502=CARTESIAN_POINT('',(690.143290451609,106.4,0.));
#5503=CARTESIAN_POINT('Origin',(639.143290451609,106.4,0.));
#5504=CARTESIAN_POINT('',(639.143290451609,106.4,0.));
#5505=CARTESIAN_POINT('',(690.143290451609,106.4,0.));
#5506=CARTESIAN_POINT('',(639.143290451609,106.4,6.));
#5507=CARTESIAN_POINT('',(690.143290451609,106.4,6.));
#5508=CARTESIAN_POINT('',(639.143290451609,106.4,0.));
#5509=CARTESIAN_POINT('Origin',(639.143290451609,104.4,0.));
#5510=CARTESIAN_POINT('',(637.143290451609,104.4,0.));
#5511=CARTESIAN_POINT('Origin',(639.143290451609,104.4,0.));
#5512=CARTESIAN_POINT('',(637.143290451609,104.4,6.));
#5513=CARTESIAN_POINT('Origin',(639.143290451609,104.4,6.));
#5514=CARTESIAN_POINT('',(637.143290451609,104.4,0.));
#5515=CARTESIAN_POINT('Origin',(637.143290451609,74.,0.));
#5516=CARTESIAN_POINT('',(637.143290451609,74.,0.));
#5517=CARTESIAN_POINT('',(637.143290451609,104.4,0.));
#5518=CARTESIAN_POINT('',(637.143290451609,73.9999999999999,6.));
#5519=CARTESIAN_POINT('',(637.143290451609,104.4,6.));
#5520=CARTESIAN_POINT('',(637.143290451609,74.,0.));
#5521=CARTESIAN_POINT('Origin',(639.143290451608,74.,0.));
#5522=CARTESIAN_POINT('',(639.143290451608,72.,0.));
#5523=CARTESIAN_POINT('Origin',(639.143290451608,74.,0.));
#5524=CARTESIAN_POINT('',(639.143290451608,72.,6.));
#5525=CARTESIAN_POINT('Origin',(639.143290451608,74.,6.));
#5526=CARTESIAN_POINT('',(639.143290451608,72.,0.));
#5527=CARTESIAN_POINT('Origin',(690.143290451608,72.,0.));
#5528=CARTESIAN_POINT('',(690.143290451608,72.,0.));
#5529=CARTESIAN_POINT('',(639.143290451608,72.,0.));
#5530=CARTESIAN_POINT('',(690.143290451608,72.,6.));
#5531=CARTESIAN_POINT('',(639.143290451608,72.,6.));
#5532=CARTESIAN_POINT('',(690.143290451608,72.,0.));
#5533=CARTESIAN_POINT('Origin',(690.143290451608,74.,0.));
#5534=CARTESIAN_POINT('',(692.143290451609,74.,0.));
#5535=CARTESIAN_POINT('Origin',(690.143290451608,74.,0.));
#5536=CARTESIAN_POINT('',(692.143290451609,74.,6.));
#5537=CARTESIAN_POINT('Origin',(690.143290451608,74.,6.));
#5538=CARTESIAN_POINT('',(692.143290451609,74.,0.));
#5539=CARTESIAN_POINT('Origin',(692.143290451609,104.4,0.));
#5540=CARTESIAN_POINT('',(692.143290451609,74.,0.));
#5541=CARTESIAN_POINT('',(692.143290451609,74.,6.));
#5542=CARTESIAN_POINT('Origin',(15.9114059351563,335.317565287437,0.));
#5543=CARTESIAN_POINT('',(16.003413974597,335.317565287437,0.));
#5544=CARTESIAN_POINT('',(15.9114059351563,335.317565287437,0.));
#5545=CARTESIAN_POINT('',(16.003413974597,335.317565287437,0.));
#5546=CARTESIAN_POINT('',(16.003413974597,335.317565287437,6.));
#5547=CARTESIAN_POINT('',(16.003413974597,335.317565287437,0.));
#5548=CARTESIAN_POINT('',(15.9114059351563,335.317565287437,6.));
#5549=CARTESIAN_POINT('',(16.003413974597,335.317565287437,6.));
#5550=CARTESIAN_POINT('',(15.9114059351563,335.317565287437,0.));
#5551=CARTESIAN_POINT('Origin',(15.9114059351563,333.317565287437,0.));
#5552=CARTESIAN_POINT('',(14.040599168402,334.024730078321,0.));
#5553=CARTESIAN_POINT('Origin',(15.9114059351563,333.317565287437,0.));
#5554=CARTESIAN_POINT('',(14.040599168402,334.024730078321,6.));
#5555=CARTESIAN_POINT('Origin',(15.9114059351563,333.317565287437,6.));
#5556=CARTESIAN_POINT('',(14.040599168402,334.024730078321,0.));
#5557=CARTESIAN_POINT('Origin',(25.7331414606158,329.604950135293,0.));
#5558=CARTESIAN_POINT('',(13.2331414606157,329.604950135293,0.));
#5559=CARTESIAN_POINT('Origin',(25.7331414606158,329.604950135293,0.));
#5560=CARTESIAN_POINT('',(13.2331414606157,329.604950135293,6.));
#5561=CARTESIAN_POINT('Origin',(25.7331414606158,329.604950135293,6.));
#5562=CARTESIAN_POINT('',(13.2331414606157,329.604950135293,0.));
#5563=CARTESIAN_POINT('Origin',(13.2331414606156,303.005420250336,0.));
#5564=CARTESIAN_POINT('',(13.2331414606156,303.005420250336,0.));
#5565=CARTESIAN_POINT('',(13.2331414606156,329.604950135293,0.));
#5566=CARTESIAN_POINT('',(13.2331414606156,303.005420250336,6.));
#5567=CARTESIAN_POINT('',(13.2331414606156,329.604950135293,6.));
#5568=CARTESIAN_POINT('',(13.2331414606156,303.005420250336,0.));
#5569=CARTESIAN_POINT('Origin',(69.1313972156328,310.,0.));
#5570=CARTESIAN_POINT('',(118.343821747385,282.58328221624,0.));
#5571=CARTESIAN_POINT('Origin',(69.1313972156328,310.,0.));
#5572=CARTESIAN_POINT('',(118.343821747385,282.58328221624,6.));
#5573=CARTESIAN_POINT('Origin',(69.1313972156328,310.,6.));
#5574=CARTESIAN_POINT('',(118.343821747385,282.58328221624,0.));
#5575=CARTESIAN_POINT('Origin',(205.701851831946,233.915278561425,0.));
#5576=CARTESIAN_POINT('',(148.448932597658,315.903712130754,0.));
#5577=CARTESIAN_POINT('Origin',(205.701851831946,233.915278561425,0.));
#5578=CARTESIAN_POINT('',(148.448932597658,315.903712130754,6.));
#5579=CARTESIAN_POINT('Origin',(205.701851831946,233.915278561425,6.));
#5580=CARTESIAN_POINT('',(148.448932597658,315.903712130754,0.));
#5581=CARTESIAN_POINT('Origin',(138.143407135486,330.661630173234,0.));
#5582=CARTESIAN_POINT('',(156.143290451608,330.726442255367,0.));
#5583=CARTESIAN_POINT('Origin',(138.143407135486,330.661630173234,0.));
#5584=CARTESIAN_POINT('',(156.143290451608,330.726442255367,6.));
#5585=CARTESIAN_POINT('Origin',(138.143407135486,330.661630173234,6.));
#5586=CARTESIAN_POINT('',(156.143290451608,330.726442255367,0.));
#5587=CARTESIAN_POINT('Origin',(156.143290451608,347.,0.));
#5588=CARTESIAN_POINT('',(156.143290451608,347.,0.));
#5589=CARTESIAN_POINT('',(156.143290451608,330.726442255367,0.));
#5590=CARTESIAN_POINT('',(156.143290451608,347.,6.));
#5591=CARTESIAN_POINT('',(156.143290451608,330.726442255367,6.));
#5592=CARTESIAN_POINT('',(156.143290451608,347.,0.));
#5593=CARTESIAN_POINT('Origin',(171.143290451608,347.,0.));
#5594=CARTESIAN_POINT('',(186.143290451609,347.,0.));
#5595=CARTESIAN_POINT('Origin',(171.143290451608,347.,0.));
#5596=CARTESIAN_POINT('',(186.143290451609,347.,6.));
#5597=CARTESIAN_POINT('Origin',(171.143290451608,347.,6.));
#5598=CARTESIAN_POINT('',(186.143290451609,347.,0.));
#5599=CARTESIAN_POINT('Origin',(186.143290451608,320.5,0.));
#5600=CARTESIAN_POINT('',(186.143290451608,320.5,0.));
#5601=CARTESIAN_POINT('',(186.143290451608,320.5,0.));
#5602=CARTESIAN_POINT('',(186.143290451608,320.5,6.));
#5603=CARTESIAN_POINT('',(186.143290451608,320.5,6.));
#5604=CARTESIAN_POINT('',(186.143290451608,320.5,0.));
#5605=CARTESIAN_POINT('Origin',(204.143290451608,320.5,0.));
#5606=CARTESIAN_POINT('',(222.053064319528,318.7,0.));
#5607=CARTESIAN_POINT('Origin',(204.143290451608,320.5,0.));
#5608=CARTESIAN_POINT('',(222.053064319528,318.7,6.));
#5609=CARTESIAN_POINT('Origin',(204.143290451608,320.5,6.));
#5610=CARTESIAN_POINT('',(222.053064319528,318.7,0.));
#5611=CARTESIAN_POINT('Origin',(224.043039193741,318.5,0.));
#5612=CARTESIAN_POINT('',(224.043039193741,320.5,0.));
#5613=CARTESIAN_POINT('Origin',(224.043039193741,318.5,0.));
#5614=CARTESIAN_POINT('',(224.043039193741,320.5,6.));
#5615=CARTESIAN_POINT('Origin',(224.043039193741,318.5,6.));
#5616=CARTESIAN_POINT('',(224.043039193741,320.5,0.));
#5617=CARTESIAN_POINT('Origin',(384.,320.5,0.));
#5618=CARTESIAN_POINT('',(384.,320.5,0.));
#5619=CARTESIAN_POINT('',(224.043039193741,320.5,0.));
#5620=CARTESIAN_POINT('',(384.,320.5,6.));
#5621=CARTESIAN_POINT('',(224.043039193741,320.5,6.));
#5622=CARTESIAN_POINT('',(384.,320.5,0.));
#5623=CARTESIAN_POINT('Origin',(384.,322.5,0.));
#5624=CARTESIAN_POINT('',(386.,322.5,0.));
#5625=CARTESIAN_POINT('Origin',(384.,322.5,0.));
#5626=CARTESIAN_POINT('',(386.,322.5,6.));
#5627=CARTESIAN_POINT('Origin',(384.,322.5,6.));
#5628=CARTESIAN_POINT('',(386.,322.5,0.));
#5629=CARTESIAN_POINT('Origin',(386.,328.,0.));
#5630=CARTESIAN_POINT('',(386.,328.,0.));
#5631=CARTESIAN_POINT('',(386.,328.,0.));
#5632=CARTESIAN_POINT('',(386.,328.,6.));
#5633=CARTESIAN_POINT('',(386.,328.,6.));
#5634=CARTESIAN_POINT('',(386.,328.,0.));
#5635=CARTESIAN_POINT('Origin',(384.,328.,0.));
#5636=CARTESIAN_POINT('',(384.,330.,0.));
#5637=CARTESIAN_POINT('Origin',(384.,328.,0.));
#5638=CARTESIAN_POINT('',(384.,330.,6.));
#5639=CARTESIAN_POINT('Origin',(384.,328.,6.));
#5640=CARTESIAN_POINT('',(384.,330.,0.));
#5641=CARTESIAN_POINT('Origin',(368.,330.,0.));
#5642=CARTESIAN_POINT('',(368.,330.,0.));
#5643=CARTESIAN_POINT('',(368.,330.,0.));
#5644=CARTESIAN_POINT('',(368.,330.,6.));
#5645=CARTESIAN_POINT('',(368.,330.,6.));
#5646=CARTESIAN_POINT('',(368.,330.,0.));
#5647=CARTESIAN_POINT('Origin',(368.,332.,0.));
#5648=CARTESIAN_POINT('',(366.,332.,0.));
#5649=CARTESIAN_POINT('Origin',(368.,332.,0.));
#5650=CARTESIAN_POINT('',(366.,332.,6.));
#5651=CARTESIAN_POINT('Origin',(368.,332.,6.));
#5652=CARTESIAN_POINT('',(366.,332.,0.));
#5653=CARTESIAN_POINT('Origin',(366.,355.,0.));
#5654=CARTESIAN_POINT('',(366.,355.,0.));
#5655=CARTESIAN_POINT('',(366.,332.,0.));
#5656=CARTESIAN_POINT('',(366.,355.,6.));
#5657=CARTESIAN_POINT('',(366.,332.,6.));
#5658=CARTESIAN_POINT('',(366.,355.,0.));
#5659=CARTESIAN_POINT('Origin',(368.,355.,0.));
#5660=CARTESIAN_POINT('',(368.,357.,0.));
#5661=CARTESIAN_POINT('Origin',(368.,355.,0.));
#5662=CARTESIAN_POINT('',(368.,357.,6.));
#5663=CARTESIAN_POINT('Origin',(368.,355.,6.));
#5664=CARTESIAN_POINT('',(368.,357.,0.));
#5665=CARTESIAN_POINT('Origin',(414.143290451609,357.,0.));
#5666=CARTESIAN_POINT('',(414.143290451609,357.,0.));
#5667=CARTESIAN_POINT('',(414.143290451609,357.,0.));
#5668=CARTESIAN_POINT('',(414.143290451609,357.,6.));
#5669=CARTESIAN_POINT('',(414.143290451609,357.,6.));
#5670=CARTESIAN_POINT('',(414.143290451609,357.,0.));
#5671=CARTESIAN_POINT('Origin',(414.143290451608,355.,0.));
#5672=CARTESIAN_POINT('',(416.143290451608,355.,0.));
#5673=CARTESIAN_POINT('Origin',(414.143290451608,355.,0.));
#5674=CARTESIAN_POINT('',(416.143290451608,355.,6.));
#5675=CARTESIAN_POINT('Origin',(414.143290451608,355.,6.));
#5676=CARTESIAN_POINT('',(416.143290451608,355.,0.));
#5677=CARTESIAN_POINT('Origin',(416.143290451608,322.5,0.));
#5678=CARTESIAN_POINT('',(416.143290451608,322.5,0.));
#5679=CARTESIAN_POINT('',(416.143290451608,322.5,0.));
#5680=CARTESIAN_POINT('',(416.143290451608,322.5,6.));
#5681=CARTESIAN_POINT('',(416.143290451608,322.5,6.));
#5682=CARTESIAN_POINT('',(416.143290451608,322.5,0.));
#5683=CARTESIAN_POINT('Origin',(418.143290451609,322.5,0.));
#5684=CARTESIAN_POINT('',(418.143290451609,320.5,0.));
#5685=CARTESIAN_POINT('Origin',(418.143290451609,322.5,0.));
#5686=CARTESIAN_POINT('',(418.143290451609,320.5,6.));
#5687=CARTESIAN_POINT('Origin',(418.143290451609,322.5,6.));
#5688=CARTESIAN_POINT('',(418.143290451609,320.5,0.));
#5689=CARTESIAN_POINT('Origin',(605.143290451608,320.5,0.));
#5690=CARTESIAN_POINT('',(605.143290451608,320.5,0.));
#5691=CARTESIAN_POINT('',(418.143290451609,320.5,0.));
#5692=CARTESIAN_POINT('',(605.143290451608,320.5,6.));
#5693=CARTESIAN_POINT('',(418.143290451609,320.5,6.));
#5694=CARTESIAN_POINT('',(605.143290451608,320.5,0.));
#5695=CARTESIAN_POINT('Origin',(605.143290451608,322.5,0.));
#5696=CARTESIAN_POINT('',(607.143290451608,322.5,0.));
#5697=CARTESIAN_POINT('Origin',(605.143290451608,322.5,0.));
#5698=CARTESIAN_POINT('',(607.143290451608,322.5,6.));
#5699=CARTESIAN_POINT('Origin',(605.143290451608,322.5,6.));
#5700=CARTESIAN_POINT('',(607.143290451608,322.5,0.));
#5701=CARTESIAN_POINT('Origin',(607.143290451608,342.,0.));
#5702=CARTESIAN_POINT('',(607.143290451608,342.,0.));
#5703=CARTESIAN_POINT('',(607.143290451608,342.,0.));
#5704=CARTESIAN_POINT('',(607.143290451608,342.,6.));
#5705=CARTESIAN_POINT('',(607.143290451608,342.,6.));
#5706=CARTESIAN_POINT('',(607.143290451608,342.,0.));
#5707=CARTESIAN_POINT('Origin',(622.143290451609,342.,0.));
#5708=CARTESIAN_POINT('',(637.143290451609,342.,0.));
#5709=CARTESIAN_POINT('Origin',(622.143290451609,342.,0.));
#5710=CARTESIAN_POINT('',(637.143290451609,342.,6.));
#5711=CARTESIAN_POINT('Origin',(622.143290451609,342.,6.));
#5712=CARTESIAN_POINT('',(637.143290451609,342.,0.));
#5713=CARTESIAN_POINT('Origin',(637.143290451609,322.5,0.));
#5714=CARTESIAN_POINT('',(637.143290451609,322.5,0.));
#5715=CARTESIAN_POINT('',(637.143290451609,322.5,0.));
#5716=CARTESIAN_POINT('',(637.143290451609,322.5,6.));
#5717=CARTESIAN_POINT('',(637.143290451609,322.5,6.));
#5718=CARTESIAN_POINT('',(637.143290451609,322.5,0.));
#5719=CARTESIAN_POINT('Origin',(639.143290451609,322.5,0.));
#5720=CARTESIAN_POINT('',(639.143290451609,320.5,0.));
#5721=CARTESIAN_POINT('Origin',(639.143290451609,322.5,0.));
#5722=CARTESIAN_POINT('',(639.143290451609,320.5,6.));
#5723=CARTESIAN_POINT('Origin',(639.143290451609,322.5,6.));
#5724=CARTESIAN_POINT('',(639.143290451609,320.5,0.));
#5725=CARTESIAN_POINT('Origin',(655.148143784679,320.5,0.));
#5726=CARTESIAN_POINT('',(655.148143784679,320.5,0.));
#5727=CARTESIAN_POINT('',(647.211005860868,320.5,0.));
#5728=CARTESIAN_POINT('',(655.148143784679,320.5,6.));
#5729=CARTESIAN_POINT('',(647.211005860868,320.5,6.));
#5730=CARTESIAN_POINT('',(655.148143784679,320.5,0.));
#5731=CARTESIAN_POINT('Origin',(655.148143784679,318.5,0.));
#5732=CARTESIAN_POINT('',(657.148137881661,318.504859221764,0.));
#5733=CARTESIAN_POINT('Origin',(655.148143784679,318.5,0.));
#5734=CARTESIAN_POINT('',(657.148137881661,318.504859221764,6.));
#5735=CARTESIAN_POINT('Origin',(655.148143784679,318.5,6.));
#5736=CARTESIAN_POINT('',(657.148137881661,318.504859221764,0.));
#5737=CARTESIAN_POINT('Origin',(657.162739202084,312.495140778237,0.));
#5738=CARTESIAN_POINT('',(657.162739202084,312.495140778237,0.));
#5739=CARTESIAN_POINT('',(657.148137881661,318.504859221764,0.));
#5740=CARTESIAN_POINT('',(657.162739202084,312.495140778237,6.));
#5741=CARTESIAN_POINT('',(657.148137881661,318.504859221764,6.));
#5742=CARTESIAN_POINT('',(657.162739202084,312.495140778237,0.));
#5743=CARTESIAN_POINT('Origin',(659.162733299066,312.5,0.));
#5744=CARTESIAN_POINT('',(659.162733299066,310.5,0.));
#5745=CARTESIAN_POINT('Origin',(659.162733299066,312.5,0.));
#5746=CARTESIAN_POINT('',(659.162733299066,310.5,6.));
#5747=CARTESIAN_POINT('Origin',(659.162733299066,312.5,6.));
#5748=CARTESIAN_POINT('',(659.162733299066,310.5,0.));
#5749=CARTESIAN_POINT('Origin',(725.143290451609,310.5,0.));
#5750=CARTESIAN_POINT('',(725.143290451609,310.5,0.));
#5751=CARTESIAN_POINT('',(725.143290451609,310.5,0.));
#5752=CARTESIAN_POINT('',(725.143290451609,310.5,6.));
#5753=CARTESIAN_POINT('',(725.143290451609,310.5,6.));
#5754=CARTESIAN_POINT('',(725.143290451609,310.5,0.));
#5755=CARTESIAN_POINT('Origin',(725.143290451609,308.5,0.));
#5756=CARTESIAN_POINT('',(727.143290451609,308.5,0.));
#5757=CARTESIAN_POINT('Origin',(725.143290451609,308.5,0.));
#5758=CARTESIAN_POINT('',(727.143290451609,308.5,6.));
#5759=CARTESIAN_POINT('Origin',(725.143290451609,308.5,6.));
#5760=CARTESIAN_POINT('',(727.143290451609,308.5,0.));
#5761=CARTESIAN_POINT('Origin',(727.143290451609,270.,0.));
#5762=CARTESIAN_POINT('',(727.143290451609,270.,0.));
#5763=CARTESIAN_POINT('',(727.143290451609,270.,0.));
#5764=CARTESIAN_POINT('',(727.143290451609,270.,6.));
#5765=CARTESIAN_POINT('',(727.143290451609,270.,6.));
#5766=CARTESIAN_POINT('',(727.143290451609,270.,0.));
#5767=CARTESIAN_POINT('Origin',(729.143290451609,270.,0.));
#5768=CARTESIAN_POINT('',(729.143290451609,268.,0.));
#5769=CARTESIAN_POINT('Origin',(729.143290451609,270.,0.));
#5770=CARTESIAN_POINT('',(729.143290451609,268.,6.));
#5771=CARTESIAN_POINT('Origin',(729.143290451609,270.,6.));
#5772=CARTESIAN_POINT('',(729.143290451609,268.,0.));
#5773=CARTESIAN_POINT('Origin',(775.143290451608,268.,0.));
#5774=CARTESIAN_POINT('',(775.143290451608,268.,0.));
#5775=CARTESIAN_POINT('',(775.143290451608,268.,0.));
#5776=CARTESIAN_POINT('',(775.143290451608,268.,6.));
#5777=CARTESIAN_POINT('',(775.143290451608,268.,6.));
#5778=CARTESIAN_POINT('',(775.143290451608,268.,0.));
#5779=CARTESIAN_POINT('Origin',(775.143290451608,266.,0.));
#5780=CARTESIAN_POINT('',(777.143290451608,266.,0.));
#5781=CARTESIAN_POINT('Origin',(775.143290451608,266.,0.));
#5782=CARTESIAN_POINT('',(777.143290451608,266.,6.));
#5783=CARTESIAN_POINT('Origin',(775.143290451608,266.,6.));
#5784=CARTESIAN_POINT('',(777.143290451608,266.,0.));
#5785=CARTESIAN_POINT('Origin',(777.143290451608,250.,0.));
#5786=CARTESIAN_POINT('',(777.143290451608,250.,0.));
#5787=CARTESIAN_POINT('',(777.143290451608,250.,0.));
#5788=CARTESIAN_POINT('',(777.143290451608,250.,6.));
#5789=CARTESIAN_POINT('',(777.143290451608,250.,6.));
#5790=CARTESIAN_POINT('',(777.143290451608,250.,0.));
#5791=CARTESIAN_POINT('Origin',(775.143290451608,250.,0.));
#5792=CARTESIAN_POINT('',(775.143290451608,248.,0.));
#5793=CARTESIAN_POINT('Origin',(775.143290451608,250.,0.));
#5794=CARTESIAN_POINT('',(775.143290451608,248.,6.));
#5795=CARTESIAN_POINT('Origin',(775.143290451608,250.,6.));
#5796=CARTESIAN_POINT('',(775.143290451608,248.,0.));
#5797=CARTESIAN_POINT('Origin',(774.,248.,0.));
#5798=CARTESIAN_POINT('',(774.,248.,0.));
#5799=CARTESIAN_POINT('',(774.,248.,0.));
#5800=CARTESIAN_POINT('',(774.,248.,6.));
#5801=CARTESIAN_POINT('',(774.,248.,6.));
#5802=CARTESIAN_POINT('',(774.,248.,0.));
#5803=CARTESIAN_POINT('Origin',(774.,246.,0.));
#5804=CARTESIAN_POINT('',(772.,246.,0.));
#5805=CARTESIAN_POINT('Origin',(774.,246.,0.));
#5806=CARTESIAN_POINT('',(772.,246.,6.));
#5807=CARTESIAN_POINT('Origin',(774.,246.,6.));
#5808=CARTESIAN_POINT('',(772.,246.,0.));
#5809=CARTESIAN_POINT('Origin',(772.,88.8236367437249,0.));
#5810=CARTESIAN_POINT('',(772.,88.8236367437249,0.));
#5811=CARTESIAN_POINT('',(772.,88.8236367437249,0.));
#5812=CARTESIAN_POINT('',(772.,88.8236367437249,6.));
#5813=CARTESIAN_POINT('',(772.,88.8236367437249,6.));
#5814=CARTESIAN_POINT('',(772.,88.8236367437249,0.));
#5815=CARTESIAN_POINT('Origin',(722.,88.8236367437249,0.));
#5816=CARTESIAN_POINT('',(722.,38.8236367437249,0.));
#5817=CARTESIAN_POINT('Origin',(722.,88.8236367437249,0.));
#5818=CARTESIAN_POINT('',(722.,38.8236367437249,6.));
#5819=CARTESIAN_POINT('Origin',(722.,88.8236367437249,6.));
#5820=CARTESIAN_POINT('',(722.,38.8236367437249,0.));
#5821=CARTESIAN_POINT('Origin',(645.127488060923,38.8236367437248,0.));
#5822=CARTESIAN_POINT('',(645.127488060923,38.8236367437248,0.));
#5823=CARTESIAN_POINT('',(645.127488060923,38.8236367437248,0.));
#5824=CARTESIAN_POINT('',(645.127488060923,38.8236367437248,6.));
#5825=CARTESIAN_POINT('',(645.127488060923,38.8236367437248,6.));
#5826=CARTESIAN_POINT('',(645.127488060923,38.8236367437248,0.));
#5827=CARTESIAN_POINT('Origin',(645.127488060923,-61.1763632562752,0.));
#5828=CARTESIAN_POINT('',(589.482159554586,21.9115264474709,0.));
#5829=CARTESIAN_POINT('Origin',(645.127488060923,-61.1763632562752,0.));
#5830=CARTESIAN_POINT('',(589.482159554586,21.9115264474709,6.));
#5831=CARTESIAN_POINT('Origin',(645.127488060923,-61.1763632562752,6.));
#5832=CARTESIAN_POINT('',(589.482159554586,21.9115264474709,0.));
#5833=CARTESIAN_POINT('Origin',(533.836831048249,104.999416151218,0.));
#5834=CARTESIAN_POINT('',(534.178546449945,5.,0.));
#5835=CARTESIAN_POINT('Origin',(533.836831048249,104.999416151218,0.));
#5836=CARTESIAN_POINT('',(534.178546449945,5.,6.));
#5837=CARTESIAN_POINT('Origin',(533.836831048249,104.999416151218,6.));
#5838=CARTESIAN_POINT('',(534.178546449945,5.,0.));
#5839=CARTESIAN_POINT('Origin',(61.9238901805544,5.,0.));
#5840=CARTESIAN_POINT('',(61.9238901805544,5.,0.));
#5841=CARTESIAN_POINT('',(59.9238901805545,5.,0.));
#5842=CARTESIAN_POINT('',(61.9238901805544,5.,6.));
#5843=CARTESIAN_POINT('',(59.9238901805545,5.,6.));
#5844=CARTESIAN_POINT('',(61.9238901805544,5.,0.));
#5845=CARTESIAN_POINT('Origin',(61.9238901805545,6.99999999999999,0.));
#5846=CARTESIAN_POINT('',(59.9238901805545,7.,0.));
#5847=CARTESIAN_POINT('Origin',(61.9238901805545,6.99999999999999,0.));
#5848=CARTESIAN_POINT('',(59.9238901805545,7.00000000000003,6.));
#5849=CARTESIAN_POINT('Origin',(61.9238901805545,6.99999999999999,6.));
#5850=CARTESIAN_POINT('',(59.9238901805545,7.,0.));
#5851=CARTESIAN_POINT('Origin',(59.9238901805545,198.869418277588,0.));
#5852=CARTESIAN_POINT('',(59.9238901805545,198.869418277588,0.));
#5853=CARTESIAN_POINT('',(59.9238901805545,206.963429912226,0.));
#5854=CARTESIAN_POINT('',(59.9238901805545,198.869418277588,6.));
#5855=CARTESIAN_POINT('',(59.9238901805545,206.963429912226,6.));
#5856=CARTESIAN_POINT('',(59.9238901805545,198.869418277588,0.));
#5857=CARTESIAN_POINT('Origin',(29.9238901805539,198.869418277588,0.));
#5858=CARTESIAN_POINT('',(55.8527058864023,213.959034445409,0.));
#5859=CARTESIAN_POINT('Origin',(29.9238901805539,198.869418277588,0.));
#5860=CARTESIAN_POINT('',(55.8527058864023,213.959034445409,6.));
#5861=CARTESIAN_POINT('Origin',(29.9238901805539,198.869418277588,6.));
#5862=CARTESIAN_POINT('',(55.8527058864023,213.959034445409,0.));
#5863=CARTESIAN_POINT('Origin',(45.2981462638577,232.095163838345,0.));
#5864=CARTESIAN_POINT('',(45.2981462638577,232.095163838345,0.));
#5865=CARTESIAN_POINT('',(45.2981462638577,232.095163838345,0.));
#5866=CARTESIAN_POINT('',(45.2981462638577,232.095163838345,6.));
#5867=CARTESIAN_POINT('',(45.2981462638577,232.095163838345,6.));
#5868=CARTESIAN_POINT('',(45.2981462638577,232.095163838345,0.));
#5869=CARTESIAN_POINT('Origin',(-15.2024237164544,196.886059446761,0.));
#5870=CARTESIAN_POINT('',(26.6381257649515,253.005293396398,0.));
#5871=CARTESIAN_POINT('Origin',(-15.2024237164544,196.886059446761,0.));
#5872=CARTESIAN_POINT('',(26.6381257649515,253.005293396398,6.));
#5873=CARTESIAN_POINT('Origin',(-15.2024237164544,196.886059446761,6.));
#5874=CARTESIAN_POINT('',(26.6381257649515,253.005293396398,0.));
#5875=CARTESIAN_POINT('Origin',(69.1313972156328,310.,0.));
#5876=CARTESIAN_POINT('',(-1.96061850633094,310.,0.));
#5877=CARTESIAN_POINT('Origin',(69.1313972156328,310.,0.));
#5878=CARTESIAN_POINT('',(-1.96061850633094,310.,6.));
#5879=CARTESIAN_POINT('Origin',(69.1313972156328,310.,6.));
#5880=CARTESIAN_POINT('',(-1.96061850633094,310.,0.));
#5881=CARTESIAN_POINT('Origin',(-1.96061850633096,354.,0.));
#5882=CARTESIAN_POINT('',(-1.96061850633092,354.,0.));
#5883=CARTESIAN_POINT('',(-1.96061850633096,354.,0.));
#5884=CARTESIAN_POINT('',(-1.96061850633092,354.,6.));
#5885=CARTESIAN_POINT('',(-1.96061850633096,354.,6.));
#5886=CARTESIAN_POINT('',(-1.96061850633092,354.,0.));
#5887=CARTESIAN_POINT('Origin',(13.0393814936691,354.,0.));
#5888=CARTESIAN_POINT('',(28.003413974597,352.96186132442,0.));
#5889=CARTESIAN_POINT('Origin',(13.0393814936691,354.,0.));
#5890=CARTESIAN_POINT('',(28.003413974597,352.96186132442,6.));
#5891=CARTESIAN_POINT('Origin',(13.0393814936691,354.,6.));
#5892=CARTESIAN_POINT('',(28.003413974597,352.96186132442,0.));
#5893=CARTESIAN_POINT('Origin',(28.003413974597,347.317565287437,0.));
#5894=CARTESIAN_POINT('',(28.003413974597,347.317565287437,0.));
#5895=CARTESIAN_POINT('',(28.003413974597,352.96186132442,0.));
#5896=CARTESIAN_POINT('',(28.003413974597,347.317565287437,6.));
#5897=CARTESIAN_POINT('',(28.003413974597,352.96186132442,6.));
#5898=CARTESIAN_POINT('',(28.003413974597,347.317565287437,0.));
#5899=CARTESIAN_POINT('Origin',(16.003413974597,347.317565287437,0.));
#5900=CARTESIAN_POINT('Origin',(16.003413974597,347.317565287437,0.));
#5901=CARTESIAN_POINT('Origin',(16.003413974597,347.317565287437,6.));
#5902=CARTESIAN_POINT('Origin',(387.591335972639,187.,6.));
#5903=CARTESIAN_POINT('Origin',(387.591335972639,187.,0.));
#5904=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#5908,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#5905=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#5908,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#5906=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#5904))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#5908,#5910,#5911))
REPRESENTATION_CONTEXT('','3D')
);
#5907=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#5905))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#5908,#5910,#5911))
REPRESENTATION_CONTEXT('','3D')
);
#5908=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#5909=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT($,.METRE.)
);
#5910=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#5911=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#5912=SHAPE_DEFINITION_REPRESENTATION(#5913,#5914);
#5913=PRODUCT_DEFINITION_SHAPE('',$,#5916);
#5914=SHAPE_REPRESENTATION('',(#3488),#5906);
#5915=PRODUCT_DEFINITION_CONTEXT('part definition',#5920,'design');
#5916=PRODUCT_DEFINITION(
'MU-M Platte Schalttafel Umrichter 52313632513V03DEd001.tif',
'MU-M Platte Schalttafel Umrichter 52313632513V03DEd001.tif v4',#5917,#5915);
#5917=PRODUCT_DEFINITION_FORMATION('',$,#5922);
#5918=PRODUCT_RELATED_PRODUCT_CATEGORY(
'MU-M Platte Schalttafel Umrichter 52313632513V03DEd001.tif v4',
'MU-M Platte Schalttafel Umrichter 52313632513V03DEd001.tif v4',(#5922));
#5919=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2009,#5920);
#5920=APPLICATION_CONTEXT(
'Core Data for Automotive Mechanical Design Process');
#5921=PRODUCT_CONTEXT('part definition',#5920,'mechanical');
#5922=PRODUCT(
'MU-M Platte Schalttafel Umrichter 52313632513V03DEd001.tif',
'MU-M Platte Schalttafel Umrichter 52313632513V03DEd001.tif v4',$,(#5921));
#5923=PRESENTATION_STYLE_ASSIGNMENT((#5924));
#5924=SURFACE_STYLE_USAGE(.BOTH.,#5925);
#5925=SURFACE_SIDE_STYLE('',(#5926));
#5926=SURFACE_STYLE_FILL_AREA(#5927);
#5927=FILL_AREA_STYLE('Steel - Satin',(#5928));
#5928=FILL_AREA_STYLE_COLOUR('Steel - Satin',#5929);
#5929=COLOUR_RGB('Steel - Satin',0.627450980392157,0.627450980392157,0.627450980392157);
ENDSEC;
END-ISO-10303-21;
