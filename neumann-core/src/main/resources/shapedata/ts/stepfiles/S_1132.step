ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ (''),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 
'G:/Shared drives/TIP/Validation/SMF KION/Tset SMF Shapes/S_1132.step',

/* time_stamp */ '2021-07-09T12:30:21+02:00',
/* author */ (''),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v18.1',
/* originating_system */ 'Autodesk Translation Framework v10.9.0.1377',

/* authorisation */ '');

FILE_SCHEMA (('AUTOMOTIVE_DESIGN { 1 0 10303 214 3 1 1 }'));
ENDSEC;

DATA;
#10=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#13),#3083);
#11=SHAPE_REPRESENTATION_RELATIONSHIP('SRR','None',#3090,#12);
#12=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#14),#3082);
#13=STYLED_ITEM('',(#3099),#14);
#14=MANIFOLD_SOLID_BREP('Body1',#1811);
#15=FACE_BOUND('',#184,.T.);
#16=FACE_BOUND('',#185,.T.);
#17=FACE_BOUND('',#189,.T.);
#18=FACE_BOUND('',#194,.T.);
#19=FACE_BOUND('',#199,.T.);
#20=FACE_BOUND('',#209,.T.);
#21=FACE_BOUND('',#214,.T.);
#22=FACE_BOUND('',#227,.T.);
#23=FACE_BOUND('',#235,.T.);
#24=FACE_BOUND('',#243,.T.);
#25=FACE_BOUND('',#249,.T.);
#26=FACE_BOUND('',#252,.T.);
#27=FACE_BOUND('',#297,.T.);
#28=FACE_BOUND('',#298,.T.);
#29=CYLINDRICAL_SURFACE('',#1833,1.6);
#30=CYLINDRICAL_SURFACE('',#1848,4.6);
#31=CYLINDRICAL_SURFACE('',#1856,4.6);
#32=CYLINDRICAL_SURFACE('',#1865,4.6);
#33=CYLINDRICAL_SURFACE('',#1874,4.6);
#34=CYLINDRICAL_SURFACE('',#1884,1.6);
#35=CYLINDRICAL_SURFACE('',#1891,1.6);
#36=CYLINDRICAL_SURFACE('',#1898,1.6);
#37=CYLINDRICAL_SURFACE('',#1904,1.6);
#38=CYLINDRICAL_SURFACE('',#1913,1.6);
#39=CYLINDRICAL_SURFACE('',#1921,4.6);
#40=CYLINDRICAL_SURFACE('',#1926,4.6);
#41=CYLINDRICAL_SURFACE('',#1934,4.6);
#42=CYLINDRICAL_SURFACE('',#1938,1.6);
#43=CYLINDRICAL_SURFACE('',#1951,1.6);
#44=CYLINDRICAL_SURFACE('',#1962,5.25);
#45=CYLINDRICAL_SURFACE('',#1964,4.5);
#46=CYLINDRICAL_SURFACE('',#1965,4.49999999999999);
#47=CYLINDRICAL_SURFACE('',#1966,4.50000000000001);
#48=CYLINDRICAL_SURFACE('',#1967,5.25);
#49=CYLINDRICAL_SURFACE('',#1969,4.5);
#50=CYLINDRICAL_SURFACE('',#1970,4.49999999999999);
#51=CYLINDRICAL_SURFACE('',#1972,2.99999999999999);
#52=CYLINDRICAL_SURFACE('',#1974,3.);
#53=CYLINDRICAL_SURFACE('',#1976,7.5);
#54=CYLINDRICAL_SURFACE('',#1978,6.39999999999999);
#55=CYLINDRICAL_SURFACE('',#1981,6.39999999999999);
#56=CYLINDRICAL_SURFACE('',#1984,7.5);
#57=CYLINDRICAL_SURFACE('',#1986,3.);
#58=CYLINDRICAL_SURFACE('',#1987,3.00000000000001);
#59=CYLINDRICAL_SURFACE('',#1989,3.00000000000001);
#60=CYLINDRICAL_SURFACE('',#1992,8.);
#61=CYLINDRICAL_SURFACE('',#1995,11.8);
#62=CYLINDRICAL_SURFACE('',#1998,8.);
#63=CYLINDRICAL_SURFACE('',#2001,2.99999999999997);
#64=CYLINDRICAL_SURFACE('',#2004,7.5);
#65=CYLINDRICAL_SURFACE('',#2006,7.5);
#66=CYLINDRICAL_SURFACE('',#2008,3.00000000000002);
#67=CYLINDRICAL_SURFACE('',#2010,3.);
#68=CYLINDRICAL_SURFACE('',#2012,7.50000000000001);
#69=CYLINDRICAL_SURFACE('',#2014,4.6);
#70=FACE_OUTER_BOUND('',#178,.T.);
#71=FACE_OUTER_BOUND('',#179,.T.);
#72=FACE_OUTER_BOUND('',#180,.T.);
#73=FACE_OUTER_BOUND('',#181,.T.);
#74=FACE_OUTER_BOUND('',#182,.T.);
#75=FACE_OUTER_BOUND('',#183,.T.);
#76=FACE_OUTER_BOUND('',#186,.T.);
#77=FACE_OUTER_BOUND('',#187,.T.);
#78=FACE_OUTER_BOUND('',#188,.T.);
#79=FACE_OUTER_BOUND('',#190,.T.);
#80=FACE_OUTER_BOUND('',#191,.T.);
#81=FACE_OUTER_BOUND('',#192,.T.);
#82=FACE_OUTER_BOUND('',#193,.T.);
#83=FACE_OUTER_BOUND('',#195,.T.);
#84=FACE_OUTER_BOUND('',#196,.T.);
#85=FACE_OUTER_BOUND('',#197,.T.);
#86=FACE_OUTER_BOUND('',#198,.T.);
#87=FACE_OUTER_BOUND('',#200,.T.);
#88=FACE_OUTER_BOUND('',#201,.T.);
#89=FACE_OUTER_BOUND('',#202,.T.);
#90=FACE_OUTER_BOUND('',#203,.T.);
#91=FACE_OUTER_BOUND('',#204,.T.);
#92=FACE_OUTER_BOUND('',#205,.T.);
#93=FACE_OUTER_BOUND('',#206,.T.);
#94=FACE_OUTER_BOUND('',#207,.T.);
#95=FACE_OUTER_BOUND('',#208,.T.);
#96=FACE_OUTER_BOUND('',#210,.T.);
#97=FACE_OUTER_BOUND('',#211,.T.);
#98=FACE_OUTER_BOUND('',#212,.T.);
#99=FACE_OUTER_BOUND('',#213,.T.);
#100=FACE_OUTER_BOUND('',#215,.T.);
#101=FACE_OUTER_BOUND('',#216,.T.);
#102=FACE_OUTER_BOUND('',#217,.T.);
#103=FACE_OUTER_BOUND('',#218,.T.);
#104=FACE_OUTER_BOUND('',#219,.T.);
#105=FACE_OUTER_BOUND('',#220,.T.);
#106=FACE_OUTER_BOUND('',#221,.T.);
#107=FACE_OUTER_BOUND('',#222,.T.);
#108=FACE_OUTER_BOUND('',#223,.T.);
#109=FACE_OUTER_BOUND('',#224,.T.);
#110=FACE_OUTER_BOUND('',#225,.T.);
#111=FACE_OUTER_BOUND('',#226,.T.);
#112=FACE_OUTER_BOUND('',#228,.T.);
#113=FACE_OUTER_BOUND('',#229,.T.);
#114=FACE_OUTER_BOUND('',#230,.T.);
#115=FACE_OUTER_BOUND('',#231,.T.);
#116=FACE_OUTER_BOUND('',#232,.T.);
#117=FACE_OUTER_BOUND('',#233,.T.);
#118=FACE_OUTER_BOUND('',#234,.T.);
#119=FACE_OUTER_BOUND('',#236,.T.);
#120=FACE_OUTER_BOUND('',#237,.T.);
#121=FACE_OUTER_BOUND('',#238,.T.);
#122=FACE_OUTER_BOUND('',#239,.T.);
#123=FACE_OUTER_BOUND('',#240,.T.);
#124=FACE_OUTER_BOUND('',#241,.T.);
#125=FACE_OUTER_BOUND('',#242,.T.);
#126=FACE_OUTER_BOUND('',#244,.T.);
#127=FACE_OUTER_BOUND('',#245,.T.);
#128=FACE_OUTER_BOUND('',#246,.T.);
#129=FACE_OUTER_BOUND('',#247,.T.);
#130=FACE_OUTER_BOUND('',#248,.T.);
#131=FACE_OUTER_BOUND('',#250,.T.);
#132=FACE_OUTER_BOUND('',#251,.T.);
#133=FACE_OUTER_BOUND('',#253,.T.);
#134=FACE_OUTER_BOUND('',#254,.T.);
#135=FACE_OUTER_BOUND('',#255,.T.);
#136=FACE_OUTER_BOUND('',#256,.T.);
#137=FACE_OUTER_BOUND('',#257,.T.);
#138=FACE_OUTER_BOUND('',#258,.T.);
#139=FACE_OUTER_BOUND('',#259,.T.);
#140=FACE_OUTER_BOUND('',#260,.T.);
#141=FACE_OUTER_BOUND('',#261,.T.);
#142=FACE_OUTER_BOUND('',#262,.T.);
#143=FACE_OUTER_BOUND('',#263,.T.);
#144=FACE_OUTER_BOUND('',#264,.T.);
#145=FACE_OUTER_BOUND('',#265,.T.);
#146=FACE_OUTER_BOUND('',#266,.T.);
#147=FACE_OUTER_BOUND('',#267,.T.);
#148=FACE_OUTER_BOUND('',#268,.T.);
#149=FACE_OUTER_BOUND('',#269,.T.);
#150=FACE_OUTER_BOUND('',#270,.T.);
#151=FACE_OUTER_BOUND('',#271,.T.);
#152=FACE_OUTER_BOUND('',#272,.T.);
#153=FACE_OUTER_BOUND('',#273,.T.);
#154=FACE_OUTER_BOUND('',#274,.T.);
#155=FACE_OUTER_BOUND('',#275,.T.);
#156=FACE_OUTER_BOUND('',#276,.T.);
#157=FACE_OUTER_BOUND('',#277,.T.);
#158=FACE_OUTER_BOUND('',#278,.T.);
#159=FACE_OUTER_BOUND('',#279,.T.);
#160=FACE_OUTER_BOUND('',#280,.T.);
#161=FACE_OUTER_BOUND('',#281,.T.);
#162=FACE_OUTER_BOUND('',#282,.T.);
#163=FACE_OUTER_BOUND('',#283,.T.);
#164=FACE_OUTER_BOUND('',#284,.T.);
#165=FACE_OUTER_BOUND('',#285,.T.);
#166=FACE_OUTER_BOUND('',#286,.T.);
#167=FACE_OUTER_BOUND('',#287,.T.);
#168=FACE_OUTER_BOUND('',#288,.T.);
#169=FACE_OUTER_BOUND('',#289,.T.);
#170=FACE_OUTER_BOUND('',#290,.T.);
#171=FACE_OUTER_BOUND('',#291,.T.);
#172=FACE_OUTER_BOUND('',#292,.T.);
#173=FACE_OUTER_BOUND('',#293,.T.);
#174=FACE_OUTER_BOUND('',#294,.T.);
#175=FACE_OUTER_BOUND('',#295,.T.);
#176=FACE_OUTER_BOUND('',#296,.T.);
#177=FACE_OUTER_BOUND('',#299,.T.);
#178=EDGE_LOOP('',(#1128,#1129,#1130,#1131,#1132,#1133,#1134,#1135,#1136,
#1137,#1138,#1139,#1140,#1141,#1142,#1143));
#179=EDGE_LOOP('',(#1144,#1145,#1146,#1147));
#180=EDGE_LOOP('',(#1148,#1149,#1150,#1151));
#181=EDGE_LOOP('',(#1152,#1153,#1154,#1155));
#182=EDGE_LOOP('',(#1156,#1157,#1158,#1159));
#183=EDGE_LOOP('',(#1160,#1161,#1162,#1163,#1164,#1165,#1166,#1167,#1168,
#1169,#1170,#1171,#1172,#1173,#1174,#1175,#1176,#1177,#1178,#1179));
#184=EDGE_LOOP('',(#1180));
#185=EDGE_LOOP('',(#1181));
#186=EDGE_LOOP('',(#1182,#1183,#1184,#1185));
#187=EDGE_LOOP('',(#1186,#1187,#1188,#1189));
#188=EDGE_LOOP('',(#1190,#1191,#1192,#1193));
#189=EDGE_LOOP('',(#1194));
#190=EDGE_LOOP('',(#1195,#1196,#1197,#1198));
#191=EDGE_LOOP('',(#1199,#1200,#1201,#1202));
#192=EDGE_LOOP('',(#1203,#1204,#1205,#1206));
#193=EDGE_LOOP('',(#1207,#1208,#1209,#1210));
#194=EDGE_LOOP('',(#1211));
#195=EDGE_LOOP('',(#1212,#1213,#1214,#1215));
#196=EDGE_LOOP('',(#1216,#1217,#1218,#1219));
#197=EDGE_LOOP('',(#1220,#1221,#1222,#1223));
#198=EDGE_LOOP('',(#1224,#1225,#1226,#1227));
#199=EDGE_LOOP('',(#1228));
#200=EDGE_LOOP('',(#1229,#1230,#1231,#1232));
#201=EDGE_LOOP('',(#1233,#1234,#1235,#1236));
#202=EDGE_LOOP('',(#1237,#1238,#1239,#1240));
#203=EDGE_LOOP('',(#1241,#1242,#1243,#1244));
#204=EDGE_LOOP('',(#1245,#1246,#1247,#1248,#1249,#1250,#1251,#1252,#1253,
#1254,#1255,#1256,#1257,#1258,#1259,#1260));
#205=EDGE_LOOP('',(#1261,#1262,#1263,#1264));
#206=EDGE_LOOP('',(#1265,#1266,#1267,#1268));
#207=EDGE_LOOP('',(#1269,#1270,#1271,#1272));
#208=EDGE_LOOP('',(#1273,#1274,#1275,#1276));
#209=EDGE_LOOP('',(#1277));
#210=EDGE_LOOP('',(#1278,#1279,#1280,#1281));
#211=EDGE_LOOP('',(#1282,#1283,#1284,#1285));
#212=EDGE_LOOP('',(#1286,#1287,#1288,#1289));
#213=EDGE_LOOP('',(#1290,#1291,#1292,#1293));
#214=EDGE_LOOP('',(#1294));
#215=EDGE_LOOP('',(#1295,#1296,#1297,#1298));
#216=EDGE_LOOP('',(#1299,#1300,#1301,#1302));
#217=EDGE_LOOP('',(#1303,#1304,#1305,#1306));
#218=EDGE_LOOP('',(#1307,#1308,#1309,#1310));
#219=EDGE_LOOP('',(#1311,#1312,#1313,#1314));
#220=EDGE_LOOP('',(#1315,#1316,#1317,#1318));
#221=EDGE_LOOP('',(#1319,#1320,#1321,#1322,#1323,#1324,#1325));
#222=EDGE_LOOP('',(#1326,#1327,#1328,#1329));
#223=EDGE_LOOP('',(#1330,#1331,#1332,#1333));
#224=EDGE_LOOP('',(#1334,#1335,#1336,#1337));
#225=EDGE_LOOP('',(#1338,#1339,#1340,#1341));
#226=EDGE_LOOP('',(#1342,#1343,#1344,#1345));
#227=EDGE_LOOP('',(#1346));
#228=EDGE_LOOP('',(#1347,#1348,#1349,#1350));
#229=EDGE_LOOP('',(#1351,#1352,#1353,#1354));
#230=EDGE_LOOP('',(#1355,#1356,#1357,#1358));
#231=EDGE_LOOP('',(#1359,#1360,#1361,#1362));
#232=EDGE_LOOP('',(#1363,#1364,#1365,#1366));
#233=EDGE_LOOP('',(#1367,#1368,#1369,#1370));
#234=EDGE_LOOP('',(#1371,#1372,#1373,#1374));
#235=EDGE_LOOP('',(#1375));
#236=EDGE_LOOP('',(#1376,#1377,#1378,#1379));
#237=EDGE_LOOP('',(#1380,#1381,#1382,#1383));
#238=EDGE_LOOP('',(#1384,#1385,#1386,#1387));
#239=EDGE_LOOP('',(#1388,#1389,#1390,#1391));
#240=EDGE_LOOP('',(#1392,#1393,#1394,#1395));
#241=EDGE_LOOP('',(#1396,#1397,#1398,#1399));
#242=EDGE_LOOP('',(#1400,#1401,#1402,#1403));
#243=EDGE_LOOP('',(#1404));
#244=EDGE_LOOP('',(#1405,#1406,#1407,#1408));
#245=EDGE_LOOP('',(#1409,#1410,#1411,#1412,#1413,#1414,#1415));
#246=EDGE_LOOP('',(#1416,#1417,#1418,#1419));
#247=EDGE_LOOP('',(#1420,#1421,#1422,#1423));
#248=EDGE_LOOP('',(#1424,#1425,#1426,#1427));
#249=EDGE_LOOP('',(#1428));
#250=EDGE_LOOP('',(#1429,#1430,#1431,#1432));
#251=EDGE_LOOP('',(#1433,#1434,#1435,#1436));
#252=EDGE_LOOP('',(#1437));
#253=EDGE_LOOP('',(#1438,#1439,#1440,#1441));
#254=EDGE_LOOP('',(#1442,#1443,#1444,#1445));
#255=EDGE_LOOP('',(#1446,#1447,#1448,#1449));
#256=EDGE_LOOP('',(#1450,#1451,#1452,#1453));
#257=EDGE_LOOP('',(#1454,#1455,#1456,#1457));
#258=EDGE_LOOP('',(#1458,#1459,#1460,#1461));
#259=EDGE_LOOP('',(#1462,#1463,#1464,#1465));
#260=EDGE_LOOP('',(#1466,#1467,#1468,#1469));
#261=EDGE_LOOP('',(#1470,#1471,#1472,#1473));
#262=EDGE_LOOP('',(#1474,#1475,#1476,#1477));
#263=EDGE_LOOP('',(#1478,#1479,#1480,#1481));
#264=EDGE_LOOP('',(#1482,#1483,#1484,#1485));
#265=EDGE_LOOP('',(#1486,#1487,#1488,#1489));
#266=EDGE_LOOP('',(#1490,#1491,#1492,#1493));
#267=EDGE_LOOP('',(#1494,#1495,#1496,#1497));
#268=EDGE_LOOP('',(#1498,#1499,#1500,#1501));
#269=EDGE_LOOP('',(#1502,#1503,#1504,#1505));
#270=EDGE_LOOP('',(#1506,#1507,#1508,#1509));
#271=EDGE_LOOP('',(#1510,#1511,#1512,#1513));
#272=EDGE_LOOP('',(#1514,#1515,#1516,#1517));
#273=EDGE_LOOP('',(#1518,#1519,#1520,#1521));
#274=EDGE_LOOP('',(#1522,#1523,#1524,#1525));
#275=EDGE_LOOP('',(#1526,#1527,#1528,#1529));
#276=EDGE_LOOP('',(#1530,#1531,#1532,#1533));
#277=EDGE_LOOP('',(#1534,#1535,#1536,#1537));
#278=EDGE_LOOP('',(#1538,#1539,#1540,#1541));
#279=EDGE_LOOP('',(#1542,#1543,#1544,#1545));
#280=EDGE_LOOP('',(#1546,#1547,#1548,#1549));
#281=EDGE_LOOP('',(#1550,#1551,#1552,#1553));
#282=EDGE_LOOP('',(#1554,#1555,#1556,#1557));
#283=EDGE_LOOP('',(#1558,#1559,#1560,#1561));
#284=EDGE_LOOP('',(#1562,#1563,#1564,#1565));
#285=EDGE_LOOP('',(#1566,#1567,#1568,#1569));
#286=EDGE_LOOP('',(#1570,#1571,#1572,#1573));
#287=EDGE_LOOP('',(#1574,#1575,#1576,#1577));
#288=EDGE_LOOP('',(#1578,#1579,#1580,#1581));
#289=EDGE_LOOP('',(#1582,#1583,#1584,#1585));
#290=EDGE_LOOP('',(#1586,#1587,#1588,#1589));
#291=EDGE_LOOP('',(#1590,#1591,#1592,#1593));
#292=EDGE_LOOP('',(#1594,#1595,#1596,#1597));
#293=EDGE_LOOP('',(#1598,#1599,#1600,#1601));
#294=EDGE_LOOP('',(#1602,#1603,#1604,#1605));
#295=EDGE_LOOP('',(#1606,#1607,#1608,#1609));
#296=EDGE_LOOP('',(#1610,#1611,#1612,#1613,#1614,#1615,#1616,#1617,#1618,
#1619,#1620,#1621,#1622,#1623,#1624,#1625,#1626,#1627,#1628,#1629));
#297=EDGE_LOOP('',(#1630));
#298=EDGE_LOOP('',(#1631));
#299=EDGE_LOOP('',(#1632,#1633,#1634,#1635));
#300=CIRCLE('',#1826,3.00000000000002);
#301=CIRCLE('',#1827,3.);
#302=CIRCLE('',#1828,2.99999999999999);
#303=CIRCLE('',#1829,3.);
#304=CIRCLE('',#1831,1.6);
#305=CIRCLE('',#1832,4.6);
#306=CIRCLE('',#1834,1.59999999999999);
#307=CIRCLE('',#1838,2.99999999999997);
#308=CIRCLE('',#1839,8.);
#309=CIRCLE('',#1840,11.8);
#310=CIRCLE('',#1841,8.);
#311=CIRCLE('',#1842,3.00000000000001);
#312=CIRCLE('',#1843,6.39999999999999);
#313=CIRCLE('',#1844,6.39999999999999);
#314=CIRCLE('',#1845,5.25);
#315=CIRCLE('',#1846,5.25);
#316=CIRCLE('',#1849,4.6);
#317=CIRCLE('',#1851,7.5);
#318=CIRCLE('',#1852,4.49999999999999);
#319=CIRCLE('',#1854,4.6);
#320=CIRCLE('',#1855,1.6);
#321=CIRCLE('',#1857,4.6);
#322=CIRCLE('',#1860,7.50000000000001);
#323=CIRCLE('',#1861,4.5);
#324=CIRCLE('',#1863,4.6);
#325=CIRCLE('',#1864,1.6);
#326=CIRCLE('',#1866,4.6);
#327=CIRCLE('',#1869,7.5);
#328=CIRCLE('',#1870,4.49999999999999);
#329=CIRCLE('',#1872,4.6);
#330=CIRCLE('',#1873,1.6);
#331=CIRCLE('',#1875,4.6);
#332=CIRCLE('',#1879,3.);
#333=CIRCLE('',#1880,2.99999999999999);
#334=CIRCLE('',#1881,3.);
#335=CIRCLE('',#1882,3.00000000000002);
#336=CIRCLE('',#1885,1.6);
#337=CIRCLE('',#1888,7.5);
#338=CIRCLE('',#1889,4.49999999999999);
#339=CIRCLE('',#1892,1.6);
#340=CIRCLE('',#1895,7.50000000000001);
#341=CIRCLE('',#1896,4.5);
#342=CIRCLE('',#1899,1.6);
#343=CIRCLE('',#1902,1.6);
#344=CIRCLE('',#1903,4.6);
#345=CIRCLE('',#1905,1.6);
#346=CIRCLE('',#1908,3.);
#347=CIRCLE('',#1909,3.00000000000001);
#348=CIRCLE('',#1911,1.6);
#349=CIRCLE('',#1912,4.6);
#350=CIRCLE('',#1914,1.6);
#351=CIRCLE('',#1918,7.5);
#352=CIRCLE('',#1919,4.49999999999999);
#353=CIRCLE('',#1922,4.6);
#354=CIRCLE('',#1927,4.6);
#355=CIRCLE('',#1929,7.5);
#356=CIRCLE('',#1930,4.50000000000001);
#357=CIRCLE('',#1932,4.6);
#358=CIRCLE('',#1933,1.6);
#359=CIRCLE('',#1935,4.6);
#360=CIRCLE('',#1939,1.6);
#361=CIRCLE('',#1940,1.6);
#362=CIRCLE('',#1942,4.6);
#363=CIRCLE('',#1944,7.5);
#364=CIRCLE('',#1945,4.5);
#365=CIRCLE('',#1948,3.00000000000001);
#366=CIRCLE('',#1949,3.);
#367=CIRCLE('',#1952,1.6);
#368=CIRCLE('',#1954,7.5);
#369=CIRCLE('',#1955,4.5);
#370=CIRCLE('',#1958,7.5);
#371=CIRCLE('',#1959,4.50000000000001);
#372=CIRCLE('',#1961,4.6);
#373=CIRCLE('',#1963,5.25);
#374=CIRCLE('',#1968,5.25);
#375=CIRCLE('',#1979,6.39999999999999);
#376=CIRCLE('',#1982,6.39999999999999);
#377=CIRCLE('',#1990,3.00000000000001);
#378=CIRCLE('',#1993,8.);
#379=CIRCLE('',#1996,11.8);
#380=CIRCLE('',#1999,8.);
#381=CIRCLE('',#2002,2.99999999999997);
#382=LINE('',#2573,#554);
#383=LINE('',#2575,#555);
#384=LINE('',#2577,#556);
#385=LINE('',#2579,#557);
#386=LINE('',#2583,#558);
#387=LINE('',#2587,#559);
#388=LINE('',#2589,#560);
#389=LINE('',#2591,#561);
#390=LINE('',#2595,#562);
#391=LINE('',#2599,#563);
#392=LINE('',#2601,#564);
#393=LINE('',#2602,#565);
#394=LINE('',#2605,#566);
#395=LINE('',#2609,#567);
#396=LINE('',#2613,#568);
#397=LINE('',#2616,#569);
#398=LINE('',#2618,#570);
#399=LINE('',#2620,#571);
#400=LINE('',#2621,#572);
#401=LINE('',#2624,#573);
#402=LINE('',#2625,#574);
#403=LINE('',#2628,#575);
#404=LINE('',#2630,#576);
#405=LINE('',#2632,#577);
#406=LINE('',#2636,#578);
#407=LINE('',#2640,#579);
#408=LINE('',#2644,#580);
#409=LINE('',#2648,#581);
#410=LINE('',#2652,#582);
#411=LINE('',#2654,#583);
#412=LINE('',#2656,#584);
#413=LINE('',#2660,#585);
#414=LINE('',#2663,#586);
#415=LINE('',#2670,#587);
#416=LINE('',#2672,#588);
#417=LINE('',#2673,#589);
#418=LINE('',#2675,#590);
#419=LINE('',#2680,#591);
#420=LINE('',#2682,#592);
#421=LINE('',#2685,#593);
#422=LINE('',#2690,#594);
#423=LINE('',#2695,#595);
#424=LINE('',#2698,#596);
#425=LINE('',#2700,#597);
#426=LINE('',#2702,#598);
#427=LINE('',#2703,#599);
#428=LINE('',#2707,#600);
#429=LINE('',#2709,#601);
#430=LINE('',#2712,#602);
#431=LINE('',#2717,#603);
#432=LINE('',#2720,#604);
#433=LINE('',#2724,#605);
#434=LINE('',#2727,#606);
#435=LINE('',#2729,#607);
#436=LINE('',#2731,#608);
#437=LINE('',#2732,#609);
#438=LINE('',#2736,#610);
#439=LINE('',#2738,#611);
#440=LINE('',#2741,#612);
#441=LINE('',#2746,#613);
#442=LINE('',#2749,#614);
#443=LINE('',#2753,#615);
#444=LINE('',#2755,#616);
#445=LINE('',#2757,#617);
#446=LINE('',#2759,#618);
#447=LINE('',#2760,#619);
#448=LINE('',#2763,#620);
#449=LINE('',#2764,#621);
#450=LINE('',#2767,#622);
#451=LINE('',#2771,#623);
#452=LINE('',#2774,#624);
#453=LINE('',#2776,#625);
#454=LINE('',#2780,#626);
#455=LINE('',#2783,#627);
#456=LINE('',#2785,#628);
#457=LINE('',#2786,#629);
#458=LINE('',#2791,#630);
#459=LINE('',#2792,#631);
#460=LINE('',#2798,#632);
#461=LINE('',#2799,#633);
#462=LINE('',#2804,#634);
#463=LINE('',#2805,#635);
#464=LINE('',#2816,#636);
#465=LINE('',#2818,#637);
#466=LINE('',#2820,#638);
#467=LINE('',#2821,#639);
#468=LINE('',#2824,#640);
#469=LINE('',#2829,#641);
#470=LINE('',#2832,#642);
#471=LINE('',#2834,#643);
#472=LINE('',#2835,#644);
#473=LINE('',#2839,#645);
#474=LINE('',#2841,#646);
#475=LINE('',#2843,#647);
#476=LINE('',#2845,#648);
#477=LINE('',#2850,#649);
#478=LINE('',#2853,#650);
#479=LINE('',#2856,#651);
#480=LINE('',#2860,#652);
#481=LINE('',#2863,#653);
#482=LINE('',#2865,#654);
#483=LINE('',#2866,#655);
#484=LINE('',#2869,#656);
#485=LINE('',#2870,#657);
#486=LINE('',#2877,#658);
#487=LINE('',#2879,#659);
#488=LINE('',#2880,#660);
#489=LINE('',#2882,#661);
#490=LINE('',#2886,#662);
#491=LINE('',#2887,#663);
#492=LINE('',#2889,#664);
#493=LINE('',#2892,#665);
#494=LINE('',#2894,#666);
#495=LINE('',#2895,#667);
#496=LINE('',#2897,#668);
#497=LINE('',#2902,#669);
#498=LINE('',#2904,#670);
#499=LINE('',#2907,#671);
#500=LINE('',#2912,#672);
#501=LINE('',#2917,#673);
#502=LINE('',#2920,#674);
#503=LINE('',#2922,#675);
#504=LINE('',#2923,#676);
#505=LINE('',#2927,#677);
#506=LINE('',#2929,#678);
#507=LINE('',#2931,#679);
#508=LINE('',#2932,#680);
#509=LINE('',#2935,#681);
#510=LINE('',#2940,#682);
#511=LINE('',#2943,#683);
#512=LINE('',#2945,#684);
#513=LINE('',#2947,#685);
#514=LINE('',#2953,#686);
#515=LINE('',#2954,#687);
#516=LINE('',#2960,#688);
#517=LINE('',#2961,#689);
#518=LINE('',#2968,#690);
#519=LINE('',#2972,#691);
#520=LINE('',#2981,#692);
#521=LINE('',#2984,#693);
#522=LINE('',#2986,#694);
#523=LINE('',#2988,#695);
#524=LINE('',#2991,#696);
#525=LINE('',#2994,#697);
#526=LINE('',#2996,#698);
#527=LINE('',#2998,#699);
#528=LINE('',#3000,#700);
#529=LINE('',#3002,#701);
#530=LINE('',#3008,#702);
#531=LINE('',#3009,#703);
#532=LINE('',#3013,#704);
#533=LINE('',#3016,#705);
#534=LINE('',#3017,#706);
#535=LINE('',#3022,#707);
#536=LINE('',#3025,#708);
#537=LINE('',#3029,#709);
#538=LINE('',#3030,#710);
#539=LINE('',#3034,#711);
#540=LINE('',#3037,#712);
#541=LINE('',#3038,#713);
#542=LINE('',#3042,#714);
#543=LINE('',#3045,#715);
#544=LINE('',#3046,#716);
#545=LINE('',#3050,#717);
#546=LINE('',#3053,#718);
#547=LINE('',#3054,#719);
#548=LINE('',#3058,#720);
#549=LINE('',#3061,#721);
#550=LINE('',#3062,#722);
#551=LINE('',#3070,#723);
#552=LINE('',#3072,#724);
#553=LINE('',#3074,#725);
#554=VECTOR('',#2019,10.);
#555=VECTOR('',#2020,10.);
#556=VECTOR('',#2021,10.);
#557=VECTOR('',#2022,10.);
#558=VECTOR('',#2025,10.);
#559=VECTOR('',#2028,10.);
#560=VECTOR('',#2029,10.);
#561=VECTOR('',#2030,10.);
#562=VECTOR('',#2033,10.);
#563=VECTOR('',#2036,10.);
#564=VECTOR('',#2037,10.);
#565=VECTOR('',#2038,10.);
#566=VECTOR('',#2041,10.);
#567=VECTOR('',#2044,10.);
#568=VECTOR('',#2049,10.);
#569=VECTOR('',#2052,10.);
#570=VECTOR('',#2055,10.);
#571=VECTOR('',#2056,10.);
#572=VECTOR('',#2057,10.);
#573=VECTOR('',#2060,10.);
#574=VECTOR('',#2061,10.);
#575=VECTOR('',#2064,10.);
#576=VECTOR('',#2065,10.);
#577=VECTOR('',#2066,10.);
#578=VECTOR('',#2069,10.);
#579=VECTOR('',#2072,10.);
#580=VECTOR('',#2075,10.);
#581=VECTOR('',#2078,10.);
#582=VECTOR('',#2081,10.);
#583=VECTOR('',#2082,10.);
#584=VECTOR('',#2083,10.);
#585=VECTOR('',#2086,10.);
#586=VECTOR('',#2089,10.);
#587=VECTOR('',#2096,10.);
#588=VECTOR('',#2097,10.);
#589=VECTOR('',#2098,10.);
#590=VECTOR('',#2101,10.);
#591=VECTOR('',#2106,10.);
#592=VECTOR('',#2107,10.);
#593=VECTOR('',#2110,10.);
#594=VECTOR('',#2115,10.);
#595=VECTOR('',#2122,10.);
#596=VECTOR('',#2125,10.);
#597=VECTOR('',#2128,10.);
#598=VECTOR('',#2129,10.);
#599=VECTOR('',#2130,10.);
#600=VECTOR('',#2133,10.);
#601=VECTOR('',#2134,10.);
#602=VECTOR('',#2137,10.);
#603=VECTOR('',#2142,10.);
#604=VECTOR('',#2145,10.);
#605=VECTOR('',#2150,10.);
#606=VECTOR('',#2153,10.);
#607=VECTOR('',#2156,10.);
#608=VECTOR('',#2157,10.);
#609=VECTOR('',#2158,10.);
#610=VECTOR('',#2161,10.);
#611=VECTOR('',#2162,10.);
#612=VECTOR('',#2165,10.);
#613=VECTOR('',#2170,10.);
#614=VECTOR('',#2173,10.);
#615=VECTOR('',#2178,10.);
#616=VECTOR('',#2181,10.);
#617=VECTOR('',#2184,10.);
#618=VECTOR('',#2185,10.);
#619=VECTOR('',#2186,10.);
#620=VECTOR('',#2189,10.);
#621=VECTOR('',#2190,10.);
#622=VECTOR('',#2193,10.);
#623=VECTOR('',#2196,10.);
#624=VECTOR('',#2199,10.);
#625=VECTOR('',#2200,10.);
#626=VECTOR('',#2203,10.);
#627=VECTOR('',#2206,10.);
#628=VECTOR('',#2209,10.);
#629=VECTOR('',#2210,10.);
#630=VECTOR('',#2217,10.);
#631=VECTOR('',#2218,10.);
#632=VECTOR('',#2227,10.);
#633=VECTOR('',#2228,10.);
#634=VECTOR('',#2235,10.);
#635=VECTOR('',#2236,10.);
#636=VECTOR('',#2251,10.);
#637=VECTOR('',#2252,10.);
#638=VECTOR('',#2253,10.);
#639=VECTOR('',#2254,10.);
#640=VECTOR('',#2257,10.);
#641=VECTOR('',#2264,10.);
#642=VECTOR('',#2269,10.);
#643=VECTOR('',#2270,10.);
#644=VECTOR('',#2271,10.);
#645=VECTOR('',#2274,10.);
#646=VECTOR('',#2275,10.);
#647=VECTOR('',#2276,10.);
#648=VECTOR('',#2277,10.);
#649=VECTOR('',#2282,10.);
#650=VECTOR('',#2285,10.);
#651=VECTOR('',#2288,10.);
#652=VECTOR('',#2293,10.);
#653=VECTOR('',#2298,10.);
#654=VECTOR('',#2299,10.);
#655=VECTOR('',#2300,10.);
#656=VECTOR('',#2303,10.);
#657=VECTOR('',#2304,10.);
#658=VECTOR('',#2313,10.);
#659=VECTOR('',#2314,10.);
#660=VECTOR('',#2315,10.);
#661=VECTOR('',#2318,10.);
#662=VECTOR('',#2323,10.);
#663=VECTOR('',#2324,10.);
#664=VECTOR('',#2327,10.);
#665=VECTOR('',#2330,10.);
#666=VECTOR('',#2331,10.);
#667=VECTOR('',#2332,10.);
#668=VECTOR('',#2335,10.);
#669=VECTOR('',#2340,10.);
#670=VECTOR('',#2341,10.);
#671=VECTOR('',#2344,10.);
#672=VECTOR('',#2349,10.);
#673=VECTOR('',#2356,10.);
#674=VECTOR('',#2361,10.);
#675=VECTOR('',#2362,10.);
#676=VECTOR('',#2363,10.);
#677=VECTOR('',#2366,10.);
#678=VECTOR('',#2367,10.);
#679=VECTOR('',#2368,10.);
#680=VECTOR('',#2369,10.);
#681=VECTOR('',#2372,10.);
#682=VECTOR('',#2379,10.);
#683=VECTOR('',#2382,10.);
#684=VECTOR('',#2385,10.);
#685=VECTOR('',#2386,10.);
#686=VECTOR('',#2393,10.);
#687=VECTOR('',#2394,10.);
#688=VECTOR('',#2401,10.);
#689=VECTOR('',#2402,10.);
#690=VECTOR('',#2413,10.);
#691=VECTOR('',#2418,10.);
#692=VECTOR('',#2431,5.25);
#693=VECTOR('',#2436,4.5);
#694=VECTOR('',#2439,4.49999999999999);
#695=VECTOR('',#2442,4.50000000000001);
#696=VECTOR('',#2445,5.25);
#697=VECTOR('',#2450,4.5);
#698=VECTOR('',#2453,4.49999999999999);
#699=VECTOR('',#2456,10.);
#700=VECTOR('',#2459,10.);
#701=VECTOR('',#2462,10.);
#702=VECTOR('',#2471,10.);
#703=VECTOR('',#2472,10.);
#704=VECTOR('',#2477,10.);
#705=VECTOR('',#2480,10.);
#706=VECTOR('',#2481,10.);
#707=VECTOR('',#2490,10.);
#708=VECTOR('',#2495,10.);
#709=VECTOR('',#2500,10.);
#710=VECTOR('',#2501,10.);
#711=VECTOR('',#2506,10.);
#712=VECTOR('',#2509,10.);
#713=VECTOR('',#2510,10.);
#714=VECTOR('',#2515,10.);
#715=VECTOR('',#2518,10.);
#716=VECTOR('',#2519,10.);
#717=VECTOR('',#2524,10.);
#718=VECTOR('',#2527,10.);
#719=VECTOR('',#2528,10.);
#720=VECTOR('',#2533,10.);
#721=VECTOR('',#2536,10.);
#722=VECTOR('',#2537,10.);
#723=VECTOR('',#2552,10.);
#724=VECTOR('',#2555,10.);
#725=VECTOR('',#2558,10.);
#726=VERTEX_POINT('',#2571);
#727=VERTEX_POINT('',#2572);
#728=VERTEX_POINT('',#2574);
#729=VERTEX_POINT('',#2576);
#730=VERTEX_POINT('',#2578);
#731=VERTEX_POINT('',#2580);
#732=VERTEX_POINT('',#2582);
#733=VERTEX_POINT('',#2584);
#734=VERTEX_POINT('',#2586);
#735=VERTEX_POINT('',#2588);
#736=VERTEX_POINT('',#2590);
#737=VERTEX_POINT('',#2592);
#738=VERTEX_POINT('',#2594);
#739=VERTEX_POINT('',#2596);
#740=VERTEX_POINT('',#2598);
#741=VERTEX_POINT('',#2600);
#742=VERTEX_POINT('',#2604);
#743=VERTEX_POINT('',#2606);
#744=VERTEX_POINT('',#2608);
#745=VERTEX_POINT('',#2612);
#746=VERTEX_POINT('',#2614);
#747=VERTEX_POINT('',#2619);
#748=VERTEX_POINT('',#2623);
#749=VERTEX_POINT('',#2627);
#750=VERTEX_POINT('',#2629);
#751=VERTEX_POINT('',#2631);
#752=VERTEX_POINT('',#2633);
#753=VERTEX_POINT('',#2635);
#754=VERTEX_POINT('',#2637);
#755=VERTEX_POINT('',#2639);
#756=VERTEX_POINT('',#2641);
#757=VERTEX_POINT('',#2643);
#758=VERTEX_POINT('',#2645);
#759=VERTEX_POINT('',#2647);
#760=VERTEX_POINT('',#2649);
#761=VERTEX_POINT('',#2651);
#762=VERTEX_POINT('',#2653);
#763=VERTEX_POINT('',#2655);
#764=VERTEX_POINT('',#2657);
#765=VERTEX_POINT('',#2659);
#766=VERTEX_POINT('',#2661);
#767=VERTEX_POINT('',#2664);
#768=VERTEX_POINT('',#2666);
#769=VERTEX_POINT('',#2669);
#770=VERTEX_POINT('',#2671);
#771=VERTEX_POINT('',#2678);
#772=VERTEX_POINT('',#2679);
#773=VERTEX_POINT('',#2681);
#774=VERTEX_POINT('',#2683);
#775=VERTEX_POINT('',#2686);
#776=VERTEX_POINT('',#2689);
#777=VERTEX_POINT('',#2694);
#778=VERTEX_POINT('',#2696);
#779=VERTEX_POINT('',#2701);
#780=VERTEX_POINT('',#2705);
#781=VERTEX_POINT('',#2706);
#782=VERTEX_POINT('',#2708);
#783=VERTEX_POINT('',#2710);
#784=VERTEX_POINT('',#2713);
#785=VERTEX_POINT('',#2716);
#786=VERTEX_POINT('',#2718);
#787=VERTEX_POINT('',#2723);
#788=VERTEX_POINT('',#2725);
#789=VERTEX_POINT('',#2730);
#790=VERTEX_POINT('',#2734);
#791=VERTEX_POINT('',#2735);
#792=VERTEX_POINT('',#2737);
#793=VERTEX_POINT('',#2739);
#794=VERTEX_POINT('',#2742);
#795=VERTEX_POINT('',#2745);
#796=VERTEX_POINT('',#2747);
#797=VERTEX_POINT('',#2752);
#798=VERTEX_POINT('',#2758);
#799=VERTEX_POINT('',#2762);
#800=VERTEX_POINT('',#2766);
#801=VERTEX_POINT('',#2768);
#802=VERTEX_POINT('',#2770);
#803=VERTEX_POINT('',#2772);
#804=VERTEX_POINT('',#2775);
#805=VERTEX_POINT('',#2777);
#806=VERTEX_POINT('',#2779);
#807=VERTEX_POINT('',#2781);
#808=VERTEX_POINT('',#2790);
#809=VERTEX_POINT('',#2795);
#810=VERTEX_POINT('',#2803);
#811=VERTEX_POINT('',#2808);
#812=VERTEX_POINT('',#2814);
#813=VERTEX_POINT('',#2815);
#814=VERTEX_POINT('',#2817);
#815=VERTEX_POINT('',#2819);
#816=VERTEX_POINT('',#2823);
#817=VERTEX_POINT('',#2828);
#818=VERTEX_POINT('',#2833);
#819=VERTEX_POINT('',#2837);
#820=VERTEX_POINT('',#2838);
#821=VERTEX_POINT('',#2840);
#822=VERTEX_POINT('',#2842);
#823=VERTEX_POINT('',#2844);
#824=VERTEX_POINT('',#2846);
#825=VERTEX_POINT('',#2848);
#826=VERTEX_POINT('',#2852);
#827=VERTEX_POINT('',#2855);
#828=VERTEX_POINT('',#2859);
#829=VERTEX_POINT('',#2864);
#830=VERTEX_POINT('',#2868);
#831=VERTEX_POINT('',#2873);
#832=VERTEX_POINT('',#2876);
#833=VERTEX_POINT('',#2878);
#834=VERTEX_POINT('',#2885);
#835=VERTEX_POINT('',#2891);
#836=VERTEX_POINT('',#2893);
#837=VERTEX_POINT('',#2900);
#838=VERTEX_POINT('',#2901);
#839=VERTEX_POINT('',#2903);
#840=VERTEX_POINT('',#2905);
#841=VERTEX_POINT('',#2908);
#842=VERTEX_POINT('',#2911);
#843=VERTEX_POINT('',#2916);
#844=VERTEX_POINT('',#2921);
#845=VERTEX_POINT('',#2925);
#846=VERTEX_POINT('',#2926);
#847=VERTEX_POINT('',#2928);
#848=VERTEX_POINT('',#2930);
#849=VERTEX_POINT('',#2934);
#850=VERTEX_POINT('',#2939);
#851=VERTEX_POINT('',#2941);
#852=VERTEX_POINT('',#2946);
#853=VERTEX_POINT('',#2949);
#854=VERTEX_POINT('',#2952);
#855=VERTEX_POINT('',#2956);
#856=VERTEX_POINT('',#2958);
#857=VERTEX_POINT('',#2966);
#858=VERTEX_POINT('',#2969);
#859=VERTEX_POINT('',#2975);
#860=VERTEX_POINT('',#2980);
#861=VERTEX_POINT('',#2990);
#862=VERTEX_POINT('',#3007);
#863=VERTEX_POINT('',#3011);
#864=VERTEX_POINT('',#3015);
#865=VERTEX_POINT('',#3028);
#866=VERTEX_POINT('',#3032);
#867=VERTEX_POINT('',#3036);
#868=VERTEX_POINT('',#3040);
#869=VERTEX_POINT('',#3044);
#870=VERTEX_POINT('',#3048);
#871=VERTEX_POINT('',#3052);
#872=VERTEX_POINT('',#3056);
#873=VERTEX_POINT('',#3060);
#874=EDGE_CURVE('',#726,#727,#382,.T.);
#875=EDGE_CURVE('',#727,#728,#383,.T.);
#876=EDGE_CURVE('',#729,#728,#384,.T.);
#877=EDGE_CURVE('',#729,#730,#385,.T.);
#878=EDGE_CURVE('',#731,#730,#300,.T.);
#879=EDGE_CURVE('',#732,#731,#386,.T.);
#880=EDGE_CURVE('',#733,#732,#301,.T.);
#881=EDGE_CURVE('',#734,#733,#387,.T.);
#882=EDGE_CURVE('',#735,#734,#388,.T.);
#883=EDGE_CURVE('',#735,#736,#389,.T.);
#884=EDGE_CURVE('',#737,#736,#302,.T.);
#885=EDGE_CURVE('',#738,#737,#390,.T.);
#886=EDGE_CURVE('',#739,#738,#303,.T.);
#887=EDGE_CURVE('',#740,#739,#391,.T.);
#888=EDGE_CURVE('',#741,#740,#392,.T.);
#889=EDGE_CURVE('',#726,#741,#393,.T.);
#890=EDGE_CURVE('',#742,#726,#394,.T.);
#891=EDGE_CURVE('',#743,#742,#304,.T.);
#892=EDGE_CURVE('',#743,#744,#395,.T.);
#893=EDGE_CURVE('',#744,#726,#305,.T.);
#894=EDGE_CURVE('',#745,#742,#396,.T.);
#895=EDGE_CURVE('',#745,#746,#306,.T.);
#896=EDGE_CURVE('',#746,#743,#397,.T.);
#897=EDGE_CURVE('',#727,#745,#398,.T.);
#898=EDGE_CURVE('',#747,#745,#399,.T.);
#899=EDGE_CURVE('',#728,#747,#400,.T.);
#900=EDGE_CURVE('',#748,#741,#401,.T.);
#901=EDGE_CURVE('',#742,#748,#402,.T.);
#902=EDGE_CURVE('',#746,#749,#403,.T.);
#903=EDGE_CURVE('',#750,#749,#404,.T.);
#904=EDGE_CURVE('',#750,#751,#405,.T.);
#905=EDGE_CURVE('',#751,#752,#307,.T.);
#906=EDGE_CURVE('',#752,#753,#406,.T.);
#907=EDGE_CURVE('',#753,#754,#308,.T.);
#908=EDGE_CURVE('',#754,#755,#407,.T.);
#909=EDGE_CURVE('',#755,#756,#309,.T.);
#910=EDGE_CURVE('',#756,#757,#408,.T.);
#911=EDGE_CURVE('',#757,#758,#310,.T.);
#912=EDGE_CURVE('',#758,#759,#409,.T.);
#913=EDGE_CURVE('',#759,#760,#311,.T.);
#914=EDGE_CURVE('',#760,#761,#410,.T.);
#915=EDGE_CURVE('',#762,#761,#411,.T.);
#916=EDGE_CURVE('',#762,#763,#412,.T.);
#917=EDGE_CURVE('',#763,#764,#312,.T.);
#918=EDGE_CURVE('',#764,#765,#413,.T.);
#919=EDGE_CURVE('',#765,#766,#313,.T.);
#920=EDGE_CURVE('',#766,#743,#414,.T.);
#921=EDGE_CURVE('',#767,#767,#314,.T.);
#922=EDGE_CURVE('',#768,#768,#315,.T.);
#923=EDGE_CURVE('',#769,#746,#415,.T.);
#924=EDGE_CURVE('',#770,#769,#416,.T.);
#925=EDGE_CURVE('',#749,#770,#417,.T.);
#926=EDGE_CURVE('',#744,#769,#418,.T.);
#927=EDGE_CURVE('',#769,#727,#316,.T.);
#928=EDGE_CURVE('',#771,#772,#419,.T.);
#929=EDGE_CURVE('',#773,#772,#420,.T.);
#930=EDGE_CURVE('',#773,#774,#317,.T.);
#931=EDGE_CURVE('',#771,#774,#421,.T.);
#932=EDGE_CURVE('',#775,#775,#318,.T.);
#933=EDGE_CURVE('',#776,#771,#422,.T.);
#934=EDGE_CURVE('',#748,#776,#319,.T.);
#935=EDGE_CURVE('',#741,#771,#320,.T.);
#936=EDGE_CURVE('',#777,#776,#423,.T.);
#937=EDGE_CURVE('',#777,#778,#321,.T.);
#938=EDGE_CURVE('',#778,#748,#424,.T.);
#939=EDGE_CURVE('',#772,#777,#425,.T.);
#940=EDGE_CURVE('',#779,#777,#426,.T.);
#941=EDGE_CURVE('',#773,#779,#427,.T.);
#942=EDGE_CURVE('',#780,#781,#428,.T.);
#943=EDGE_CURVE('',#782,#781,#429,.T.);
#944=EDGE_CURVE('',#782,#783,#322,.T.);
#945=EDGE_CURVE('',#783,#780,#430,.T.);
#946=EDGE_CURVE('',#784,#784,#323,.T.);
#947=EDGE_CURVE('',#785,#780,#431,.T.);
#948=EDGE_CURVE('',#786,#785,#324,.T.);
#949=EDGE_CURVE('',#786,#735,#432,.T.);
#950=EDGE_CURVE('',#780,#735,#325,.T.);
#951=EDGE_CURVE('',#787,#785,#433,.T.);
#952=EDGE_CURVE('',#787,#788,#326,.T.);
#953=EDGE_CURVE('',#788,#786,#434,.T.);
#954=EDGE_CURVE('',#781,#787,#435,.T.);
#955=EDGE_CURVE('',#789,#787,#436,.T.);
#956=EDGE_CURVE('',#782,#789,#437,.T.);
#957=EDGE_CURVE('',#790,#791,#438,.T.);
#958=EDGE_CURVE('',#791,#792,#439,.T.);
#959=EDGE_CURVE('',#792,#793,#327,.T.);
#960=EDGE_CURVE('',#793,#790,#440,.T.);
#961=EDGE_CURVE('',#794,#794,#328,.T.);
#962=EDGE_CURVE('',#795,#790,#441,.T.);
#963=EDGE_CURVE('',#796,#795,#329,.T.);
#964=EDGE_CURVE('',#796,#729,#442,.T.);
#965=EDGE_CURVE('',#790,#729,#330,.T.);
#966=EDGE_CURVE('',#797,#795,#443,.T.);
#967=EDGE_CURVE('',#797,#747,#331,.T.);
#968=EDGE_CURVE('',#747,#796,#444,.T.);
#969=EDGE_CURVE('',#791,#797,#445,.T.);
#970=EDGE_CURVE('',#798,#797,#446,.T.);
#971=EDGE_CURVE('',#792,#798,#447,.T.);
#972=EDGE_CURVE('',#774,#799,#448,.T.);
#973=EDGE_CURVE('',#776,#799,#449,.T.);
#974=EDGE_CURVE('',#778,#800,#450,.T.);
#975=EDGE_CURVE('',#800,#801,#332,.T.);
#976=EDGE_CURVE('',#801,#802,#451,.T.);
#977=EDGE_CURVE('',#802,#803,#333,.T.);
#978=EDGE_CURVE('',#803,#786,#452,.T.);
#979=EDGE_CURVE('',#788,#804,#453,.T.);
#980=EDGE_CURVE('',#804,#805,#334,.T.);
#981=EDGE_CURVE('',#805,#806,#454,.T.);
#982=EDGE_CURVE('',#806,#807,#335,.T.);
#983=EDGE_CURVE('',#807,#796,#455,.T.);
#984=EDGE_CURVE('',#740,#778,#456,.T.);
#985=EDGE_CURVE('',#739,#800,#457,.T.);
#986=EDGE_CURVE('',#772,#740,#336,.T.);
#987=EDGE_CURVE('',#783,#808,#458,.T.);
#988=EDGE_CURVE('',#785,#808,#459,.T.);
#989=EDGE_CURVE('',#799,#779,#337,.T.);
#990=EDGE_CURVE('',#809,#809,#338,.T.);
#991=EDGE_CURVE('',#734,#788,#460,.T.);
#992=EDGE_CURVE('',#733,#804,#461,.T.);
#993=EDGE_CURVE('',#781,#734,#339,.T.);
#994=EDGE_CURVE('',#793,#810,#462,.T.);
#995=EDGE_CURVE('',#795,#810,#463,.T.);
#996=EDGE_CURVE('',#808,#789,#340,.T.);
#997=EDGE_CURVE('',#811,#811,#341,.T.);
#998=EDGE_CURVE('',#728,#791,#342,.T.);
#999=EDGE_CURVE('',#812,#813,#464,.T.);
#1000=EDGE_CURVE('',#814,#813,#465,.T.);
#1001=EDGE_CURVE('',#815,#814,#466,.T.);
#1002=EDGE_CURVE('',#815,#812,#467,.T.);
#1003=EDGE_CURVE('',#816,#812,#468,.T.);
#1004=EDGE_CURVE('',#749,#816,#343,.T.);
#1005=EDGE_CURVE('',#812,#770,#344,.T.);
#1006=EDGE_CURVE('',#817,#816,#469,.T.);
#1007=EDGE_CURVE('',#817,#750,#345,.T.);
#1008=EDGE_CURVE('',#813,#817,#470,.T.);
#1009=EDGE_CURVE('',#818,#817,#471,.T.);
#1010=EDGE_CURVE('',#814,#818,#472,.T.);
#1011=EDGE_CURVE('',#819,#820,#473,.T.);
#1012=EDGE_CURVE('',#821,#820,#474,.T.);
#1013=EDGE_CURVE('',#822,#821,#475,.T.);
#1014=EDGE_CURVE('',#822,#823,#476,.T.);
#1015=EDGE_CURVE('',#823,#824,#346,.T.);
#1016=EDGE_CURVE('',#825,#824,#347,.T.);
#1017=EDGE_CURVE('',#825,#819,#477,.T.);
#1018=EDGE_CURVE('',#826,#819,#478,.T.);
#1019=EDGE_CURVE('',#761,#826,#348,.T.);
#1020=EDGE_CURVE('',#761,#827,#479,.T.);
#1021=EDGE_CURVE('',#819,#827,#349,.T.);
#1022=EDGE_CURVE('',#828,#826,#480,.T.);
#1023=EDGE_CURVE('',#828,#762,#350,.T.);
#1024=EDGE_CURVE('',#820,#828,#481,.T.);
#1025=EDGE_CURVE('',#829,#828,#482,.T.);
#1026=EDGE_CURVE('',#821,#829,#483,.T.);
#1027=EDGE_CURVE('',#830,#815,#484,.T.);
#1028=EDGE_CURVE('',#816,#830,#485,.T.);
#1029=EDGE_CURVE('',#810,#798,#351,.T.);
#1030=EDGE_CURVE('',#831,#831,#352,.T.);
#1031=EDGE_CURVE('',#832,#750,#486,.T.);
#1032=EDGE_CURVE('',#832,#833,#487,.T.);
#1033=EDGE_CURVE('',#833,#751,#488,.T.);
#1034=EDGE_CURVE('',#770,#832,#489,.T.);
#1035=EDGE_CURVE('',#813,#832,#353,.T.);
#1036=EDGE_CURVE('',#825,#834,#490,.T.);
#1037=EDGE_CURVE('',#826,#834,#491,.T.);
#1038=EDGE_CURVE('',#818,#830,#492,.T.);
#1039=EDGE_CURVE('',#835,#762,#493,.T.);
#1040=EDGE_CURVE('',#835,#836,#494,.T.);
#1041=EDGE_CURVE('',#836,#763,#495,.T.);
#1042=EDGE_CURVE('',#827,#835,#496,.T.);
#1043=EDGE_CURVE('',#820,#835,#354,.T.);
#1044=EDGE_CURVE('',#837,#838,#497,.T.);
#1045=EDGE_CURVE('',#839,#838,#498,.T.);
#1046=EDGE_CURVE('',#839,#840,#355,.T.);
#1047=EDGE_CURVE('',#840,#837,#499,.T.);
#1048=EDGE_CURVE('',#841,#841,#356,.T.);
#1049=EDGE_CURVE('',#842,#837,#500,.T.);
#1050=EDGE_CURVE('',#830,#842,#357,.T.);
#1051=EDGE_CURVE('',#837,#815,#358,.T.);
#1052=EDGE_CURVE('',#843,#842,#501,.T.);
#1053=EDGE_CURVE('',#843,#818,#359,.T.);
#1054=EDGE_CURVE('',#838,#843,#502,.T.);
#1055=EDGE_CURVE('',#844,#843,#503,.T.);
#1056=EDGE_CURVE('',#839,#844,#504,.T.);
#1057=EDGE_CURVE('',#845,#846,#505,.T.);
#1058=EDGE_CURVE('',#847,#846,#506,.T.);
#1059=EDGE_CURVE('',#848,#847,#507,.T.);
#1060=EDGE_CURVE('',#848,#845,#508,.T.);
#1061=EDGE_CURVE('',#849,#845,#509,.T.);
#1062=EDGE_CURVE('',#849,#822,#360,.T.);
#1063=EDGE_CURVE('',#845,#821,#361,.T.);
#1064=EDGE_CURVE('',#850,#849,#510,.T.);
#1065=EDGE_CURVE('',#851,#850,#362,.T.);
#1066=EDGE_CURVE('',#851,#822,#511,.T.);
#1067=EDGE_CURVE('',#846,#850,#512,.T.);
#1068=EDGE_CURVE('',#850,#852,#513,.T.);
#1069=EDGE_CURVE('',#852,#847,#363,.T.);
#1070=EDGE_CURVE('',#853,#853,#364,.T.);
#1071=EDGE_CURVE('',#840,#854,#514,.T.);
#1072=EDGE_CURVE('',#842,#854,#515,.T.);
#1073=EDGE_CURVE('',#834,#855,#365,.T.);
#1074=EDGE_CURVE('',#855,#856,#366,.T.);
#1075=EDGE_CURVE('',#856,#851,#516,.T.);
#1076=EDGE_CURVE('',#829,#851,#517,.T.);
#1077=EDGE_CURVE('',#838,#814,#367,.T.);
#1078=EDGE_CURVE('',#848,#857,#368,.T.);
#1079=EDGE_CURVE('',#857,#849,#518,.T.);
#1080=EDGE_CURVE('',#858,#858,#369,.T.);
#1081=EDGE_CURVE('',#823,#856,#519,.T.);
#1082=EDGE_CURVE('',#854,#844,#370,.T.);
#1083=EDGE_CURVE('',#859,#859,#371,.T.);
#1084=EDGE_CURVE('',#846,#829,#372,.T.);
#1085=EDGE_CURVE('',#768,#860,#520,.T.);
#1086=EDGE_CURVE('',#860,#860,#373,.T.);
#1087=EDGE_CURVE('',#853,#858,#521,.T.);
#1088=EDGE_CURVE('',#809,#775,#522,.T.);
#1089=EDGE_CURVE('',#859,#841,#523,.T.);
#1090=EDGE_CURVE('',#767,#861,#524,.T.);
#1091=EDGE_CURVE('',#861,#861,#374,.T.);
#1092=EDGE_CURVE('',#811,#784,#525,.T.);
#1093=EDGE_CURVE('',#831,#794,#526,.T.);
#1094=EDGE_CURVE('',#736,#803,#527,.T.);
#1095=EDGE_CURVE('',#737,#802,#528,.T.);
#1096=EDGE_CURVE('',#738,#801,#529,.T.);
#1097=EDGE_CURVE('',#862,#766,#530,.T.);
#1098=EDGE_CURVE('',#862,#744,#531,.T.);
#1099=EDGE_CURVE('',#862,#863,#375,.T.);
#1100=EDGE_CURVE('',#863,#765,#532,.T.);
#1101=EDGE_CURVE('',#864,#863,#533,.T.);
#1102=EDGE_CURVE('',#864,#764,#534,.T.);
#1103=EDGE_CURVE('',#836,#864,#376,.T.);
#1104=EDGE_CURVE('',#857,#852,#535,.T.);
#1105=EDGE_CURVE('',#824,#855,#536,.T.);
#1106=EDGE_CURVE('',#865,#760,#537,.T.);
#1107=EDGE_CURVE('',#827,#865,#538,.T.);
#1108=EDGE_CURVE('',#866,#865,#377,.T.);
#1109=EDGE_CURVE('',#866,#759,#539,.T.);
#1110=EDGE_CURVE('',#866,#867,#540,.T.);
#1111=EDGE_CURVE('',#867,#758,#541,.T.);
#1112=EDGE_CURVE('',#867,#868,#378,.T.);
#1113=EDGE_CURVE('',#868,#757,#542,.T.);
#1114=EDGE_CURVE('',#869,#868,#543,.T.);
#1115=EDGE_CURVE('',#869,#756,#544,.T.);
#1116=EDGE_CURVE('',#870,#869,#379,.T.);
#1117=EDGE_CURVE('',#870,#755,#545,.T.);
#1118=EDGE_CURVE('',#870,#871,#546,.T.);
#1119=EDGE_CURVE('',#871,#754,#547,.T.);
#1120=EDGE_CURVE('',#871,#872,#380,.T.);
#1121=EDGE_CURVE('',#872,#753,#548,.T.);
#1122=EDGE_CURVE('',#872,#873,#549,.T.);
#1123=EDGE_CURVE('',#873,#752,#550,.T.);
#1124=EDGE_CURVE('',#833,#873,#381,.T.);
#1125=EDGE_CURVE('',#730,#807,#551,.T.);
#1126=EDGE_CURVE('',#731,#806,#552,.T.);
#1127=EDGE_CURVE('',#732,#805,#553,.T.);
#1128=ORIENTED_EDGE('',*,*,#874,.T.);
#1129=ORIENTED_EDGE('',*,*,#875,.T.);
#1130=ORIENTED_EDGE('',*,*,#876,.F.);
#1131=ORIENTED_EDGE('',*,*,#877,.T.);
#1132=ORIENTED_EDGE('',*,*,#878,.F.);
#1133=ORIENTED_EDGE('',*,*,#879,.F.);
#1134=ORIENTED_EDGE('',*,*,#880,.F.);
#1135=ORIENTED_EDGE('',*,*,#881,.F.);
#1136=ORIENTED_EDGE('',*,*,#882,.F.);
#1137=ORIENTED_EDGE('',*,*,#883,.T.);
#1138=ORIENTED_EDGE('',*,*,#884,.F.);
#1139=ORIENTED_EDGE('',*,*,#885,.F.);
#1140=ORIENTED_EDGE('',*,*,#886,.F.);
#1141=ORIENTED_EDGE('',*,*,#887,.F.);
#1142=ORIENTED_EDGE('',*,*,#888,.F.);
#1143=ORIENTED_EDGE('',*,*,#889,.F.);
#1144=ORIENTED_EDGE('',*,*,#890,.F.);
#1145=ORIENTED_EDGE('',*,*,#891,.F.);
#1146=ORIENTED_EDGE('',*,*,#892,.T.);
#1147=ORIENTED_EDGE('',*,*,#893,.T.);
#1148=ORIENTED_EDGE('',*,*,#894,.F.);
#1149=ORIENTED_EDGE('',*,*,#895,.T.);
#1150=ORIENTED_EDGE('',*,*,#896,.T.);
#1151=ORIENTED_EDGE('',*,*,#891,.T.);
#1152=ORIENTED_EDGE('',*,*,#897,.T.);
#1153=ORIENTED_EDGE('',*,*,#898,.F.);
#1154=ORIENTED_EDGE('',*,*,#899,.F.);
#1155=ORIENTED_EDGE('',*,*,#875,.F.);
#1156=ORIENTED_EDGE('',*,*,#890,.T.);
#1157=ORIENTED_EDGE('',*,*,#889,.T.);
#1158=ORIENTED_EDGE('',*,*,#900,.F.);
#1159=ORIENTED_EDGE('',*,*,#901,.F.);
#1160=ORIENTED_EDGE('',*,*,#896,.F.);
#1161=ORIENTED_EDGE('',*,*,#902,.T.);
#1162=ORIENTED_EDGE('',*,*,#903,.F.);
#1163=ORIENTED_EDGE('',*,*,#904,.T.);
#1164=ORIENTED_EDGE('',*,*,#905,.T.);
#1165=ORIENTED_EDGE('',*,*,#906,.T.);
#1166=ORIENTED_EDGE('',*,*,#907,.T.);
#1167=ORIENTED_EDGE('',*,*,#908,.T.);
#1168=ORIENTED_EDGE('',*,*,#909,.T.);
#1169=ORIENTED_EDGE('',*,*,#910,.T.);
#1170=ORIENTED_EDGE('',*,*,#911,.T.);
#1171=ORIENTED_EDGE('',*,*,#912,.T.);
#1172=ORIENTED_EDGE('',*,*,#913,.T.);
#1173=ORIENTED_EDGE('',*,*,#914,.T.);
#1174=ORIENTED_EDGE('',*,*,#915,.F.);
#1175=ORIENTED_EDGE('',*,*,#916,.T.);
#1176=ORIENTED_EDGE('',*,*,#917,.T.);
#1177=ORIENTED_EDGE('',*,*,#918,.T.);
#1178=ORIENTED_EDGE('',*,*,#919,.T.);
#1179=ORIENTED_EDGE('',*,*,#920,.T.);
#1180=ORIENTED_EDGE('',*,*,#921,.T.);
#1181=ORIENTED_EDGE('',*,*,#922,.T.);
#1182=ORIENTED_EDGE('',*,*,#923,.F.);
#1183=ORIENTED_EDGE('',*,*,#924,.F.);
#1184=ORIENTED_EDGE('',*,*,#925,.F.);
#1185=ORIENTED_EDGE('',*,*,#902,.F.);
#1186=ORIENTED_EDGE('',*,*,#874,.F.);
#1187=ORIENTED_EDGE('',*,*,#893,.F.);
#1188=ORIENTED_EDGE('',*,*,#926,.T.);
#1189=ORIENTED_EDGE('',*,*,#927,.T.);
#1190=ORIENTED_EDGE('',*,*,#928,.T.);
#1191=ORIENTED_EDGE('',*,*,#929,.F.);
#1192=ORIENTED_EDGE('',*,*,#930,.T.);
#1193=ORIENTED_EDGE('',*,*,#931,.F.);
#1194=ORIENTED_EDGE('',*,*,#932,.T.);
#1195=ORIENTED_EDGE('',*,*,#933,.F.);
#1196=ORIENTED_EDGE('',*,*,#934,.F.);
#1197=ORIENTED_EDGE('',*,*,#900,.T.);
#1198=ORIENTED_EDGE('',*,*,#935,.T.);
#1199=ORIENTED_EDGE('',*,*,#936,.F.);
#1200=ORIENTED_EDGE('',*,*,#937,.T.);
#1201=ORIENTED_EDGE('',*,*,#938,.T.);
#1202=ORIENTED_EDGE('',*,*,#934,.T.);
#1203=ORIENTED_EDGE('',*,*,#939,.T.);
#1204=ORIENTED_EDGE('',*,*,#940,.F.);
#1205=ORIENTED_EDGE('',*,*,#941,.F.);
#1206=ORIENTED_EDGE('',*,*,#929,.T.);
#1207=ORIENTED_EDGE('',*,*,#942,.T.);
#1208=ORIENTED_EDGE('',*,*,#943,.F.);
#1209=ORIENTED_EDGE('',*,*,#944,.T.);
#1210=ORIENTED_EDGE('',*,*,#945,.T.);
#1211=ORIENTED_EDGE('',*,*,#946,.T.);
#1212=ORIENTED_EDGE('',*,*,#947,.F.);
#1213=ORIENTED_EDGE('',*,*,#948,.F.);
#1214=ORIENTED_EDGE('',*,*,#949,.T.);
#1215=ORIENTED_EDGE('',*,*,#950,.F.);
#1216=ORIENTED_EDGE('',*,*,#951,.F.);
#1217=ORIENTED_EDGE('',*,*,#952,.T.);
#1218=ORIENTED_EDGE('',*,*,#953,.T.);
#1219=ORIENTED_EDGE('',*,*,#948,.T.);
#1220=ORIENTED_EDGE('',*,*,#954,.T.);
#1221=ORIENTED_EDGE('',*,*,#955,.F.);
#1222=ORIENTED_EDGE('',*,*,#956,.F.);
#1223=ORIENTED_EDGE('',*,*,#943,.T.);
#1224=ORIENTED_EDGE('',*,*,#957,.T.);
#1225=ORIENTED_EDGE('',*,*,#958,.T.);
#1226=ORIENTED_EDGE('',*,*,#959,.T.);
#1227=ORIENTED_EDGE('',*,*,#960,.T.);
#1228=ORIENTED_EDGE('',*,*,#961,.T.);
#1229=ORIENTED_EDGE('',*,*,#962,.F.);
#1230=ORIENTED_EDGE('',*,*,#963,.F.);
#1231=ORIENTED_EDGE('',*,*,#964,.T.);
#1232=ORIENTED_EDGE('',*,*,#965,.F.);
#1233=ORIENTED_EDGE('',*,*,#966,.F.);
#1234=ORIENTED_EDGE('',*,*,#967,.T.);
#1235=ORIENTED_EDGE('',*,*,#968,.T.);
#1236=ORIENTED_EDGE('',*,*,#963,.T.);
#1237=ORIENTED_EDGE('',*,*,#969,.T.);
#1238=ORIENTED_EDGE('',*,*,#970,.F.);
#1239=ORIENTED_EDGE('',*,*,#971,.F.);
#1240=ORIENTED_EDGE('',*,*,#958,.F.);
#1241=ORIENTED_EDGE('',*,*,#933,.T.);
#1242=ORIENTED_EDGE('',*,*,#931,.T.);
#1243=ORIENTED_EDGE('',*,*,#972,.T.);
#1244=ORIENTED_EDGE('',*,*,#973,.F.);
#1245=ORIENTED_EDGE('',*,*,#894,.T.);
#1246=ORIENTED_EDGE('',*,*,#901,.T.);
#1247=ORIENTED_EDGE('',*,*,#938,.F.);
#1248=ORIENTED_EDGE('',*,*,#974,.T.);
#1249=ORIENTED_EDGE('',*,*,#975,.T.);
#1250=ORIENTED_EDGE('',*,*,#976,.T.);
#1251=ORIENTED_EDGE('',*,*,#977,.T.);
#1252=ORIENTED_EDGE('',*,*,#978,.T.);
#1253=ORIENTED_EDGE('',*,*,#953,.F.);
#1254=ORIENTED_EDGE('',*,*,#979,.T.);
#1255=ORIENTED_EDGE('',*,*,#980,.T.);
#1256=ORIENTED_EDGE('',*,*,#981,.T.);
#1257=ORIENTED_EDGE('',*,*,#982,.T.);
#1258=ORIENTED_EDGE('',*,*,#983,.T.);
#1259=ORIENTED_EDGE('',*,*,#968,.F.);
#1260=ORIENTED_EDGE('',*,*,#898,.T.);
#1261=ORIENTED_EDGE('',*,*,#984,.F.);
#1262=ORIENTED_EDGE('',*,*,#887,.T.);
#1263=ORIENTED_EDGE('',*,*,#985,.T.);
#1264=ORIENTED_EDGE('',*,*,#974,.F.);
#1265=ORIENTED_EDGE('',*,*,#928,.F.);
#1266=ORIENTED_EDGE('',*,*,#935,.F.);
#1267=ORIENTED_EDGE('',*,*,#888,.T.);
#1268=ORIENTED_EDGE('',*,*,#986,.F.);
#1269=ORIENTED_EDGE('',*,*,#947,.T.);
#1270=ORIENTED_EDGE('',*,*,#945,.F.);
#1271=ORIENTED_EDGE('',*,*,#987,.T.);
#1272=ORIENTED_EDGE('',*,*,#988,.F.);
#1273=ORIENTED_EDGE('',*,*,#936,.T.);
#1274=ORIENTED_EDGE('',*,*,#973,.T.);
#1275=ORIENTED_EDGE('',*,*,#989,.T.);
#1276=ORIENTED_EDGE('',*,*,#940,.T.);
#1277=ORIENTED_EDGE('',*,*,#990,.T.);
#1278=ORIENTED_EDGE('',*,*,#991,.F.);
#1279=ORIENTED_EDGE('',*,*,#881,.T.);
#1280=ORIENTED_EDGE('',*,*,#992,.T.);
#1281=ORIENTED_EDGE('',*,*,#979,.F.);
#1282=ORIENTED_EDGE('',*,*,#942,.F.);
#1283=ORIENTED_EDGE('',*,*,#950,.T.);
#1284=ORIENTED_EDGE('',*,*,#882,.T.);
#1285=ORIENTED_EDGE('',*,*,#993,.F.);
#1286=ORIENTED_EDGE('',*,*,#962,.T.);
#1287=ORIENTED_EDGE('',*,*,#960,.F.);
#1288=ORIENTED_EDGE('',*,*,#994,.T.);
#1289=ORIENTED_EDGE('',*,*,#995,.F.);
#1290=ORIENTED_EDGE('',*,*,#951,.T.);
#1291=ORIENTED_EDGE('',*,*,#988,.T.);
#1292=ORIENTED_EDGE('',*,*,#996,.T.);
#1293=ORIENTED_EDGE('',*,*,#955,.T.);
#1294=ORIENTED_EDGE('',*,*,#997,.T.);
#1295=ORIENTED_EDGE('',*,*,#897,.F.);
#1296=ORIENTED_EDGE('',*,*,#927,.F.);
#1297=ORIENTED_EDGE('',*,*,#923,.T.);
#1298=ORIENTED_EDGE('',*,*,#895,.F.);
#1299=ORIENTED_EDGE('',*,*,#957,.F.);
#1300=ORIENTED_EDGE('',*,*,#965,.T.);
#1301=ORIENTED_EDGE('',*,*,#876,.T.);
#1302=ORIENTED_EDGE('',*,*,#998,.T.);
#1303=ORIENTED_EDGE('',*,*,#999,.T.);
#1304=ORIENTED_EDGE('',*,*,#1000,.F.);
#1305=ORIENTED_EDGE('',*,*,#1001,.F.);
#1306=ORIENTED_EDGE('',*,*,#1002,.T.);
#1307=ORIENTED_EDGE('',*,*,#1003,.F.);
#1308=ORIENTED_EDGE('',*,*,#1004,.F.);
#1309=ORIENTED_EDGE('',*,*,#925,.T.);
#1310=ORIENTED_EDGE('',*,*,#1005,.F.);
#1311=ORIENTED_EDGE('',*,*,#1006,.F.);
#1312=ORIENTED_EDGE('',*,*,#1007,.T.);
#1313=ORIENTED_EDGE('',*,*,#903,.T.);
#1314=ORIENTED_EDGE('',*,*,#1004,.T.);
#1315=ORIENTED_EDGE('',*,*,#1008,.T.);
#1316=ORIENTED_EDGE('',*,*,#1009,.F.);
#1317=ORIENTED_EDGE('',*,*,#1010,.F.);
#1318=ORIENTED_EDGE('',*,*,#1000,.T.);
#1319=ORIENTED_EDGE('',*,*,#1011,.T.);
#1320=ORIENTED_EDGE('',*,*,#1012,.F.);
#1321=ORIENTED_EDGE('',*,*,#1013,.F.);
#1322=ORIENTED_EDGE('',*,*,#1014,.T.);
#1323=ORIENTED_EDGE('',*,*,#1015,.T.);
#1324=ORIENTED_EDGE('',*,*,#1016,.F.);
#1325=ORIENTED_EDGE('',*,*,#1017,.T.);
#1326=ORIENTED_EDGE('',*,*,#1018,.F.);
#1327=ORIENTED_EDGE('',*,*,#1019,.F.);
#1328=ORIENTED_EDGE('',*,*,#1020,.T.);
#1329=ORIENTED_EDGE('',*,*,#1021,.F.);
#1330=ORIENTED_EDGE('',*,*,#1022,.F.);
#1331=ORIENTED_EDGE('',*,*,#1023,.T.);
#1332=ORIENTED_EDGE('',*,*,#915,.T.);
#1333=ORIENTED_EDGE('',*,*,#1019,.T.);
#1334=ORIENTED_EDGE('',*,*,#1024,.T.);
#1335=ORIENTED_EDGE('',*,*,#1025,.F.);
#1336=ORIENTED_EDGE('',*,*,#1026,.F.);
#1337=ORIENTED_EDGE('',*,*,#1012,.T.);
#1338=ORIENTED_EDGE('',*,*,#1003,.T.);
#1339=ORIENTED_EDGE('',*,*,#1002,.F.);
#1340=ORIENTED_EDGE('',*,*,#1027,.F.);
#1341=ORIENTED_EDGE('',*,*,#1028,.F.);
#1342=ORIENTED_EDGE('',*,*,#966,.T.);
#1343=ORIENTED_EDGE('',*,*,#995,.T.);
#1344=ORIENTED_EDGE('',*,*,#1029,.T.);
#1345=ORIENTED_EDGE('',*,*,#970,.T.);
#1346=ORIENTED_EDGE('',*,*,#1030,.T.);
#1347=ORIENTED_EDGE('',*,*,#1031,.F.);
#1348=ORIENTED_EDGE('',*,*,#1032,.T.);
#1349=ORIENTED_EDGE('',*,*,#1033,.T.);
#1350=ORIENTED_EDGE('',*,*,#904,.F.);
#1351=ORIENTED_EDGE('',*,*,#999,.F.);
#1352=ORIENTED_EDGE('',*,*,#1005,.T.);
#1353=ORIENTED_EDGE('',*,*,#1034,.T.);
#1354=ORIENTED_EDGE('',*,*,#1035,.F.);
#1355=ORIENTED_EDGE('',*,*,#1018,.T.);
#1356=ORIENTED_EDGE('',*,*,#1017,.F.);
#1357=ORIENTED_EDGE('',*,*,#1036,.T.);
#1358=ORIENTED_EDGE('',*,*,#1037,.F.);
#1359=ORIENTED_EDGE('',*,*,#1006,.T.);
#1360=ORIENTED_EDGE('',*,*,#1028,.T.);
#1361=ORIENTED_EDGE('',*,*,#1038,.F.);
#1362=ORIENTED_EDGE('',*,*,#1009,.T.);
#1363=ORIENTED_EDGE('',*,*,#1039,.F.);
#1364=ORIENTED_EDGE('',*,*,#1040,.T.);
#1365=ORIENTED_EDGE('',*,*,#1041,.T.);
#1366=ORIENTED_EDGE('',*,*,#916,.F.);
#1367=ORIENTED_EDGE('',*,*,#1011,.F.);
#1368=ORIENTED_EDGE('',*,*,#1021,.T.);
#1369=ORIENTED_EDGE('',*,*,#1042,.T.);
#1370=ORIENTED_EDGE('',*,*,#1043,.F.);
#1371=ORIENTED_EDGE('',*,*,#1044,.T.);
#1372=ORIENTED_EDGE('',*,*,#1045,.F.);
#1373=ORIENTED_EDGE('',*,*,#1046,.T.);
#1374=ORIENTED_EDGE('',*,*,#1047,.T.);
#1375=ORIENTED_EDGE('',*,*,#1048,.T.);
#1376=ORIENTED_EDGE('',*,*,#1049,.F.);
#1377=ORIENTED_EDGE('',*,*,#1050,.F.);
#1378=ORIENTED_EDGE('',*,*,#1027,.T.);
#1379=ORIENTED_EDGE('',*,*,#1051,.F.);
#1380=ORIENTED_EDGE('',*,*,#1052,.F.);
#1381=ORIENTED_EDGE('',*,*,#1053,.T.);
#1382=ORIENTED_EDGE('',*,*,#1038,.T.);
#1383=ORIENTED_EDGE('',*,*,#1050,.T.);
#1384=ORIENTED_EDGE('',*,*,#1054,.T.);
#1385=ORIENTED_EDGE('',*,*,#1055,.F.);
#1386=ORIENTED_EDGE('',*,*,#1056,.F.);
#1387=ORIENTED_EDGE('',*,*,#1045,.T.);
#1388=ORIENTED_EDGE('',*,*,#1057,.T.);
#1389=ORIENTED_EDGE('',*,*,#1058,.F.);
#1390=ORIENTED_EDGE('',*,*,#1059,.F.);
#1391=ORIENTED_EDGE('',*,*,#1060,.T.);
#1392=ORIENTED_EDGE('',*,*,#1061,.F.);
#1393=ORIENTED_EDGE('',*,*,#1062,.T.);
#1394=ORIENTED_EDGE('',*,*,#1013,.T.);
#1395=ORIENTED_EDGE('',*,*,#1063,.F.);
#1396=ORIENTED_EDGE('',*,*,#1064,.F.);
#1397=ORIENTED_EDGE('',*,*,#1065,.F.);
#1398=ORIENTED_EDGE('',*,*,#1066,.T.);
#1399=ORIENTED_EDGE('',*,*,#1062,.F.);
#1400=ORIENTED_EDGE('',*,*,#1067,.T.);
#1401=ORIENTED_EDGE('',*,*,#1068,.T.);
#1402=ORIENTED_EDGE('',*,*,#1069,.T.);
#1403=ORIENTED_EDGE('',*,*,#1058,.T.);
#1404=ORIENTED_EDGE('',*,*,#1070,.T.);
#1405=ORIENTED_EDGE('',*,*,#1049,.T.);
#1406=ORIENTED_EDGE('',*,*,#1047,.F.);
#1407=ORIENTED_EDGE('',*,*,#1071,.T.);
#1408=ORIENTED_EDGE('',*,*,#1072,.F.);
#1409=ORIENTED_EDGE('',*,*,#1022,.T.);
#1410=ORIENTED_EDGE('',*,*,#1037,.T.);
#1411=ORIENTED_EDGE('',*,*,#1073,.T.);
#1412=ORIENTED_EDGE('',*,*,#1074,.T.);
#1413=ORIENTED_EDGE('',*,*,#1075,.T.);
#1414=ORIENTED_EDGE('',*,*,#1076,.F.);
#1415=ORIENTED_EDGE('',*,*,#1025,.T.);
#1416=ORIENTED_EDGE('',*,*,#1008,.F.);
#1417=ORIENTED_EDGE('',*,*,#1035,.T.);
#1418=ORIENTED_EDGE('',*,*,#1031,.T.);
#1419=ORIENTED_EDGE('',*,*,#1007,.F.);
#1420=ORIENTED_EDGE('',*,*,#1044,.F.);
#1421=ORIENTED_EDGE('',*,*,#1051,.T.);
#1422=ORIENTED_EDGE('',*,*,#1001,.T.);
#1423=ORIENTED_EDGE('',*,*,#1077,.F.);
#1424=ORIENTED_EDGE('',*,*,#1061,.T.);
#1425=ORIENTED_EDGE('',*,*,#1060,.F.);
#1426=ORIENTED_EDGE('',*,*,#1078,.T.);
#1427=ORIENTED_EDGE('',*,*,#1079,.T.);
#1428=ORIENTED_EDGE('',*,*,#1080,.T.);
#1429=ORIENTED_EDGE('',*,*,#1066,.F.);
#1430=ORIENTED_EDGE('',*,*,#1075,.F.);
#1431=ORIENTED_EDGE('',*,*,#1081,.F.);
#1432=ORIENTED_EDGE('',*,*,#1014,.F.);
#1433=ORIENTED_EDGE('',*,*,#1052,.T.);
#1434=ORIENTED_EDGE('',*,*,#1072,.T.);
#1435=ORIENTED_EDGE('',*,*,#1082,.T.);
#1436=ORIENTED_EDGE('',*,*,#1055,.T.);
#1437=ORIENTED_EDGE('',*,*,#1083,.T.);
#1438=ORIENTED_EDGE('',*,*,#1057,.F.);
#1439=ORIENTED_EDGE('',*,*,#1063,.T.);
#1440=ORIENTED_EDGE('',*,*,#1026,.T.);
#1441=ORIENTED_EDGE('',*,*,#1084,.F.);
#1442=ORIENTED_EDGE('',*,*,#922,.F.);
#1443=ORIENTED_EDGE('',*,*,#1085,.T.);
#1444=ORIENTED_EDGE('',*,*,#1086,.F.);
#1445=ORIENTED_EDGE('',*,*,#1085,.F.);
#1446=ORIENTED_EDGE('',*,*,#1070,.F.);
#1447=ORIENTED_EDGE('',*,*,#1087,.T.);
#1448=ORIENTED_EDGE('',*,*,#1080,.F.);
#1449=ORIENTED_EDGE('',*,*,#1087,.F.);
#1450=ORIENTED_EDGE('',*,*,#990,.F.);
#1451=ORIENTED_EDGE('',*,*,#1088,.T.);
#1452=ORIENTED_EDGE('',*,*,#932,.F.);
#1453=ORIENTED_EDGE('',*,*,#1088,.F.);
#1454=ORIENTED_EDGE('',*,*,#1083,.F.);
#1455=ORIENTED_EDGE('',*,*,#1089,.T.);
#1456=ORIENTED_EDGE('',*,*,#1048,.F.);
#1457=ORIENTED_EDGE('',*,*,#1089,.F.);
#1458=ORIENTED_EDGE('',*,*,#921,.F.);
#1459=ORIENTED_EDGE('',*,*,#1090,.T.);
#1460=ORIENTED_EDGE('',*,*,#1091,.F.);
#1461=ORIENTED_EDGE('',*,*,#1090,.F.);
#1462=ORIENTED_EDGE('',*,*,#997,.F.);
#1463=ORIENTED_EDGE('',*,*,#1092,.T.);
#1464=ORIENTED_EDGE('',*,*,#946,.F.);
#1465=ORIENTED_EDGE('',*,*,#1092,.F.);
#1466=ORIENTED_EDGE('',*,*,#1030,.F.);
#1467=ORIENTED_EDGE('',*,*,#1093,.T.);
#1468=ORIENTED_EDGE('',*,*,#961,.F.);
#1469=ORIENTED_EDGE('',*,*,#1093,.F.);
#1470=ORIENTED_EDGE('',*,*,#949,.F.);
#1471=ORIENTED_EDGE('',*,*,#978,.F.);
#1472=ORIENTED_EDGE('',*,*,#1094,.F.);
#1473=ORIENTED_EDGE('',*,*,#883,.F.);
#1474=ORIENTED_EDGE('',*,*,#884,.T.);
#1475=ORIENTED_EDGE('',*,*,#1094,.T.);
#1476=ORIENTED_EDGE('',*,*,#977,.F.);
#1477=ORIENTED_EDGE('',*,*,#1095,.F.);
#1478=ORIENTED_EDGE('',*,*,#885,.T.);
#1479=ORIENTED_EDGE('',*,*,#1095,.T.);
#1480=ORIENTED_EDGE('',*,*,#976,.F.);
#1481=ORIENTED_EDGE('',*,*,#1096,.F.);
#1482=ORIENTED_EDGE('',*,*,#886,.T.);
#1483=ORIENTED_EDGE('',*,*,#1096,.T.);
#1484=ORIENTED_EDGE('',*,*,#975,.F.);
#1485=ORIENTED_EDGE('',*,*,#985,.F.);
#1486=ORIENTED_EDGE('',*,*,#939,.F.);
#1487=ORIENTED_EDGE('',*,*,#986,.T.);
#1488=ORIENTED_EDGE('',*,*,#984,.T.);
#1489=ORIENTED_EDGE('',*,*,#937,.F.);
#1490=ORIENTED_EDGE('',*,*,#930,.F.);
#1491=ORIENTED_EDGE('',*,*,#941,.T.);
#1492=ORIENTED_EDGE('',*,*,#989,.F.);
#1493=ORIENTED_EDGE('',*,*,#972,.F.);
#1494=ORIENTED_EDGE('',*,*,#892,.F.);
#1495=ORIENTED_EDGE('',*,*,#920,.F.);
#1496=ORIENTED_EDGE('',*,*,#1097,.F.);
#1497=ORIENTED_EDGE('',*,*,#1098,.T.);
#1498=ORIENTED_EDGE('',*,*,#1099,.F.);
#1499=ORIENTED_EDGE('',*,*,#1097,.T.);
#1500=ORIENTED_EDGE('',*,*,#919,.F.);
#1501=ORIENTED_EDGE('',*,*,#1100,.F.);
#1502=ORIENTED_EDGE('',*,*,#1101,.T.);
#1503=ORIENTED_EDGE('',*,*,#1100,.T.);
#1504=ORIENTED_EDGE('',*,*,#918,.F.);
#1505=ORIENTED_EDGE('',*,*,#1102,.F.);
#1506=ORIENTED_EDGE('',*,*,#1103,.T.);
#1507=ORIENTED_EDGE('',*,*,#1102,.T.);
#1508=ORIENTED_EDGE('',*,*,#917,.F.);
#1509=ORIENTED_EDGE('',*,*,#1041,.F.);
#1510=ORIENTED_EDGE('',*,*,#1024,.F.);
#1511=ORIENTED_EDGE('',*,*,#1043,.T.);
#1512=ORIENTED_EDGE('',*,*,#1039,.T.);
#1513=ORIENTED_EDGE('',*,*,#1023,.F.);
#1514=ORIENTED_EDGE('',*,*,#1078,.F.);
#1515=ORIENTED_EDGE('',*,*,#1059,.T.);
#1516=ORIENTED_EDGE('',*,*,#1069,.F.);
#1517=ORIENTED_EDGE('',*,*,#1104,.F.);
#1518=ORIENTED_EDGE('',*,*,#1064,.T.);
#1519=ORIENTED_EDGE('',*,*,#1079,.F.);
#1520=ORIENTED_EDGE('',*,*,#1104,.T.);
#1521=ORIENTED_EDGE('',*,*,#1068,.F.);
#1522=ORIENTED_EDGE('',*,*,#1015,.F.);
#1523=ORIENTED_EDGE('',*,*,#1081,.T.);
#1524=ORIENTED_EDGE('',*,*,#1074,.F.);
#1525=ORIENTED_EDGE('',*,*,#1105,.F.);
#1526=ORIENTED_EDGE('',*,*,#1016,.T.);
#1527=ORIENTED_EDGE('',*,*,#1105,.T.);
#1528=ORIENTED_EDGE('',*,*,#1073,.F.);
#1529=ORIENTED_EDGE('',*,*,#1036,.F.);
#1530=ORIENTED_EDGE('',*,*,#1020,.F.);
#1531=ORIENTED_EDGE('',*,*,#914,.F.);
#1532=ORIENTED_EDGE('',*,*,#1106,.F.);
#1533=ORIENTED_EDGE('',*,*,#1107,.F.);
#1534=ORIENTED_EDGE('',*,*,#1108,.T.);
#1535=ORIENTED_EDGE('',*,*,#1106,.T.);
#1536=ORIENTED_EDGE('',*,*,#913,.F.);
#1537=ORIENTED_EDGE('',*,*,#1109,.F.);
#1538=ORIENTED_EDGE('',*,*,#1110,.F.);
#1539=ORIENTED_EDGE('',*,*,#1109,.T.);
#1540=ORIENTED_EDGE('',*,*,#912,.F.);
#1541=ORIENTED_EDGE('',*,*,#1111,.F.);
#1542=ORIENTED_EDGE('',*,*,#1112,.F.);
#1543=ORIENTED_EDGE('',*,*,#1111,.T.);
#1544=ORIENTED_EDGE('',*,*,#911,.F.);
#1545=ORIENTED_EDGE('',*,*,#1113,.F.);
#1546=ORIENTED_EDGE('',*,*,#1114,.T.);
#1547=ORIENTED_EDGE('',*,*,#1113,.T.);
#1548=ORIENTED_EDGE('',*,*,#910,.F.);
#1549=ORIENTED_EDGE('',*,*,#1115,.F.);
#1550=ORIENTED_EDGE('',*,*,#1116,.T.);
#1551=ORIENTED_EDGE('',*,*,#1115,.T.);
#1552=ORIENTED_EDGE('',*,*,#909,.F.);
#1553=ORIENTED_EDGE('',*,*,#1117,.F.);
#1554=ORIENTED_EDGE('',*,*,#1118,.F.);
#1555=ORIENTED_EDGE('',*,*,#1117,.T.);
#1556=ORIENTED_EDGE('',*,*,#908,.F.);
#1557=ORIENTED_EDGE('',*,*,#1119,.F.);
#1558=ORIENTED_EDGE('',*,*,#1120,.F.);
#1559=ORIENTED_EDGE('',*,*,#1119,.T.);
#1560=ORIENTED_EDGE('',*,*,#907,.F.);
#1561=ORIENTED_EDGE('',*,*,#1121,.F.);
#1562=ORIENTED_EDGE('',*,*,#1122,.F.);
#1563=ORIENTED_EDGE('',*,*,#1121,.T.);
#1564=ORIENTED_EDGE('',*,*,#906,.F.);
#1565=ORIENTED_EDGE('',*,*,#1123,.F.);
#1566=ORIENTED_EDGE('',*,*,#1124,.T.);
#1567=ORIENTED_EDGE('',*,*,#1123,.T.);
#1568=ORIENTED_EDGE('',*,*,#905,.F.);
#1569=ORIENTED_EDGE('',*,*,#1033,.F.);
#1570=ORIENTED_EDGE('',*,*,#1054,.F.);
#1571=ORIENTED_EDGE('',*,*,#1077,.T.);
#1572=ORIENTED_EDGE('',*,*,#1010,.T.);
#1573=ORIENTED_EDGE('',*,*,#1053,.F.);
#1574=ORIENTED_EDGE('',*,*,#1046,.F.);
#1575=ORIENTED_EDGE('',*,*,#1056,.T.);
#1576=ORIENTED_EDGE('',*,*,#1082,.F.);
#1577=ORIENTED_EDGE('',*,*,#1071,.F.);
#1578=ORIENTED_EDGE('',*,*,#969,.F.);
#1579=ORIENTED_EDGE('',*,*,#998,.F.);
#1580=ORIENTED_EDGE('',*,*,#899,.T.);
#1581=ORIENTED_EDGE('',*,*,#967,.F.);
#1582=ORIENTED_EDGE('',*,*,#959,.F.);
#1583=ORIENTED_EDGE('',*,*,#971,.T.);
#1584=ORIENTED_EDGE('',*,*,#1029,.F.);
#1585=ORIENTED_EDGE('',*,*,#994,.F.);
#1586=ORIENTED_EDGE('',*,*,#964,.F.);
#1587=ORIENTED_EDGE('',*,*,#983,.F.);
#1588=ORIENTED_EDGE('',*,*,#1125,.F.);
#1589=ORIENTED_EDGE('',*,*,#877,.F.);
#1590=ORIENTED_EDGE('',*,*,#878,.T.);
#1591=ORIENTED_EDGE('',*,*,#1125,.T.);
#1592=ORIENTED_EDGE('',*,*,#982,.F.);
#1593=ORIENTED_EDGE('',*,*,#1126,.F.);
#1594=ORIENTED_EDGE('',*,*,#879,.T.);
#1595=ORIENTED_EDGE('',*,*,#1126,.T.);
#1596=ORIENTED_EDGE('',*,*,#981,.F.);
#1597=ORIENTED_EDGE('',*,*,#1127,.F.);
#1598=ORIENTED_EDGE('',*,*,#880,.T.);
#1599=ORIENTED_EDGE('',*,*,#1127,.T.);
#1600=ORIENTED_EDGE('',*,*,#980,.F.);
#1601=ORIENTED_EDGE('',*,*,#992,.F.);
#1602=ORIENTED_EDGE('',*,*,#954,.F.);
#1603=ORIENTED_EDGE('',*,*,#993,.T.);
#1604=ORIENTED_EDGE('',*,*,#991,.T.);
#1605=ORIENTED_EDGE('',*,*,#952,.F.);
#1606=ORIENTED_EDGE('',*,*,#944,.F.);
#1607=ORIENTED_EDGE('',*,*,#956,.T.);
#1608=ORIENTED_EDGE('',*,*,#996,.F.);
#1609=ORIENTED_EDGE('',*,*,#987,.F.);
#1610=ORIENTED_EDGE('',*,*,#926,.F.);
#1611=ORIENTED_EDGE('',*,*,#1098,.F.);
#1612=ORIENTED_EDGE('',*,*,#1099,.T.);
#1613=ORIENTED_EDGE('',*,*,#1101,.F.);
#1614=ORIENTED_EDGE('',*,*,#1103,.F.);
#1615=ORIENTED_EDGE('',*,*,#1040,.F.);
#1616=ORIENTED_EDGE('',*,*,#1042,.F.);
#1617=ORIENTED_EDGE('',*,*,#1107,.T.);
#1618=ORIENTED_EDGE('',*,*,#1108,.F.);
#1619=ORIENTED_EDGE('',*,*,#1110,.T.);
#1620=ORIENTED_EDGE('',*,*,#1112,.T.);
#1621=ORIENTED_EDGE('',*,*,#1114,.F.);
#1622=ORIENTED_EDGE('',*,*,#1116,.F.);
#1623=ORIENTED_EDGE('',*,*,#1118,.T.);
#1624=ORIENTED_EDGE('',*,*,#1120,.T.);
#1625=ORIENTED_EDGE('',*,*,#1122,.T.);
#1626=ORIENTED_EDGE('',*,*,#1124,.F.);
#1627=ORIENTED_EDGE('',*,*,#1032,.F.);
#1628=ORIENTED_EDGE('',*,*,#1034,.F.);
#1629=ORIENTED_EDGE('',*,*,#924,.T.);
#1630=ORIENTED_EDGE('',*,*,#1091,.T.);
#1631=ORIENTED_EDGE('',*,*,#1086,.T.);
#1632=ORIENTED_EDGE('',*,*,#1067,.F.);
#1633=ORIENTED_EDGE('',*,*,#1084,.T.);
#1634=ORIENTED_EDGE('',*,*,#1076,.T.);
#1635=ORIENTED_EDGE('',*,*,#1065,.T.);
#1636=PLANE('',#1825);
#1637=PLANE('',#1830);
#1638=PLANE('',#1835);
#1639=PLANE('',#1836);
#1640=PLANE('',#1837);
#1641=PLANE('',#1847);
#1642=PLANE('',#1850);
#1643=PLANE('',#1853);
#1644=PLANE('',#1858);
#1645=PLANE('',#1859);
#1646=PLANE('',#1862);
#1647=PLANE('',#1867);
#1648=PLANE('',#1868);
#1649=PLANE('',#1871);
#1650=PLANE('',#1876);
#1651=PLANE('',#1877);
#1652=PLANE('',#1878);
#1653=PLANE('',#1883);
#1654=PLANE('',#1886);
#1655=PLANE('',#1887);
#1656=PLANE('',#1890);
#1657=PLANE('',#1893);
#1658=PLANE('',#1894);
#1659=PLANE('',#1897);
#1660=PLANE('',#1900);
#1661=PLANE('',#1901);
#1662=PLANE('',#1906);
#1663=PLANE('',#1907);
#1664=PLANE('',#1910);
#1665=PLANE('',#1915);
#1666=PLANE('',#1916);
#1667=PLANE('',#1917);
#1668=PLANE('',#1920);
#1669=PLANE('',#1923);
#1670=PLANE('',#1924);
#1671=PLANE('',#1925);
#1672=PLANE('',#1928);
#1673=PLANE('',#1931);
#1674=PLANE('',#1936);
#1675=PLANE('',#1937);
#1676=PLANE('',#1941);
#1677=PLANE('',#1943);
#1678=PLANE('',#1946);
#1679=PLANE('',#1947);
#1680=PLANE('',#1950);
#1681=PLANE('',#1953);
#1682=PLANE('',#1956);
#1683=PLANE('',#1957);
#1684=PLANE('',#1960);
#1685=PLANE('',#1971);
#1686=PLANE('',#1973);
#1687=PLANE('',#1975);
#1688=PLANE('',#1977);
#1689=PLANE('',#1980);
#1690=PLANE('',#1983);
#1691=PLANE('',#1985);
#1692=PLANE('',#1988);
#1693=PLANE('',#1991);
#1694=PLANE('',#1994);
#1695=PLANE('',#1997);
#1696=PLANE('',#2000);
#1697=PLANE('',#2003);
#1698=PLANE('',#2005);
#1699=PLANE('',#2007);
#1700=PLANE('',#2009);
#1701=PLANE('',#2011);
#1702=PLANE('',#2013);
#1703=ADVANCED_FACE('',(#70),#1636,.T.);
#1704=ADVANCED_FACE('',(#71),#1637,.T.);
#1705=ADVANCED_FACE('',(#72),#29,.F.);
#1706=ADVANCED_FACE('',(#73),#1638,.T.);
#1707=ADVANCED_FACE('',(#74),#1639,.T.);
#1708=ADVANCED_FACE('',(#75,#15,#16),#1640,.F.);
#1709=ADVANCED_FACE('',(#76),#1641,.T.);
#1710=ADVANCED_FACE('',(#77),#30,.T.);
#1711=ADVANCED_FACE('',(#78,#17),#1642,.T.);
#1712=ADVANCED_FACE('',(#79),#1643,.T.);
#1713=ADVANCED_FACE('',(#80),#31,.T.);
#1714=ADVANCED_FACE('',(#81),#1644,.T.);
#1715=ADVANCED_FACE('',(#82,#18),#1645,.T.);
#1716=ADVANCED_FACE('',(#83),#1646,.T.);
#1717=ADVANCED_FACE('',(#84),#32,.T.);
#1718=ADVANCED_FACE('',(#85),#1647,.T.);
#1719=ADVANCED_FACE('',(#86,#19),#1648,.T.);
#1720=ADVANCED_FACE('',(#87),#1649,.T.);
#1721=ADVANCED_FACE('',(#88),#33,.T.);
#1722=ADVANCED_FACE('',(#89),#1650,.T.);
#1723=ADVANCED_FACE('',(#90),#1651,.T.);
#1724=ADVANCED_FACE('',(#91),#1652,.F.);
#1725=ADVANCED_FACE('',(#92),#1653,.T.);
#1726=ADVANCED_FACE('',(#93),#34,.F.);
#1727=ADVANCED_FACE('',(#94),#1654,.T.);
#1728=ADVANCED_FACE('',(#95,#20),#1655,.F.);
#1729=ADVANCED_FACE('',(#96),#1656,.T.);
#1730=ADVANCED_FACE('',(#97),#35,.F.);
#1731=ADVANCED_FACE('',(#98),#1657,.T.);
#1732=ADVANCED_FACE('',(#99,#21),#1658,.F.);
#1733=ADVANCED_FACE('',(#100),#1659,.T.);
#1734=ADVANCED_FACE('',(#101),#36,.F.);
#1735=ADVANCED_FACE('',(#102),#1660,.T.);
#1736=ADVANCED_FACE('',(#103),#1661,.T.);
#1737=ADVANCED_FACE('',(#104),#37,.F.);
#1738=ADVANCED_FACE('',(#105),#1662,.T.);
#1739=ADVANCED_FACE('',(#106),#1663,.T.);
#1740=ADVANCED_FACE('',(#107),#1664,.T.);
#1741=ADVANCED_FACE('',(#108),#38,.F.);
#1742=ADVANCED_FACE('',(#109),#1665,.T.);
#1743=ADVANCED_FACE('',(#110),#1666,.T.);
#1744=ADVANCED_FACE('',(#111,#22),#1667,.F.);
#1745=ADVANCED_FACE('',(#112),#1668,.T.);
#1746=ADVANCED_FACE('',(#113),#39,.T.);
#1747=ADVANCED_FACE('',(#114),#1669,.T.);
#1748=ADVANCED_FACE('',(#115),#1670,.F.);
#1749=ADVANCED_FACE('',(#116),#1671,.T.);
#1750=ADVANCED_FACE('',(#117),#40,.T.);
#1751=ADVANCED_FACE('',(#118,#23),#1672,.T.);
#1752=ADVANCED_FACE('',(#119),#1673,.T.);
#1753=ADVANCED_FACE('',(#120),#41,.T.);
#1754=ADVANCED_FACE('',(#121),#1674,.T.);
#1755=ADVANCED_FACE('',(#122),#1675,.T.);
#1756=ADVANCED_FACE('',(#123),#42,.F.);
#1757=ADVANCED_FACE('',(#124),#1676,.T.);
#1758=ADVANCED_FACE('',(#125,#24),#1677,.F.);
#1759=ADVANCED_FACE('',(#126),#1678,.T.);
#1760=ADVANCED_FACE('',(#127),#1679,.F.);
#1761=ADVANCED_FACE('',(#128),#1680,.T.);
#1762=ADVANCED_FACE('',(#129),#43,.F.);
#1763=ADVANCED_FACE('',(#130,#25),#1681,.T.);
#1764=ADVANCED_FACE('',(#131),#1682,.T.);
#1765=ADVANCED_FACE('',(#132,#26),#1683,.F.);
#1766=ADVANCED_FACE('',(#133),#1684,.T.);
#1767=ADVANCED_FACE('',(#134),#44,.F.);
#1768=ADVANCED_FACE('',(#135),#45,.F.);
#1769=ADVANCED_FACE('',(#136),#46,.F.);
#1770=ADVANCED_FACE('',(#137),#47,.F.);
#1771=ADVANCED_FACE('',(#138),#48,.F.);
#1772=ADVANCED_FACE('',(#139),#49,.F.);
#1773=ADVANCED_FACE('',(#140),#50,.F.);
#1774=ADVANCED_FACE('',(#141),#1685,.T.);
#1775=ADVANCED_FACE('',(#142),#51,.F.);
#1776=ADVANCED_FACE('',(#143),#1686,.T.);
#1777=ADVANCED_FACE('',(#144),#52,.F.);
#1778=ADVANCED_FACE('',(#145),#1687,.T.);
#1779=ADVANCED_FACE('',(#146),#53,.T.);
#1780=ADVANCED_FACE('',(#147),#1688,.T.);
#1781=ADVANCED_FACE('',(#148),#54,.T.);
#1782=ADVANCED_FACE('',(#149),#1689,.T.);
#1783=ADVANCED_FACE('',(#150),#55,.F.);
#1784=ADVANCED_FACE('',(#151),#1690,.T.);
#1785=ADVANCED_FACE('',(#152),#56,.T.);
#1786=ADVANCED_FACE('',(#153),#1691,.T.);
#1787=ADVANCED_FACE('',(#154),#57,.T.);
#1788=ADVANCED_FACE('',(#155),#58,.F.);
#1789=ADVANCED_FACE('',(#156),#1692,.T.);
#1790=ADVANCED_FACE('',(#157),#59,.F.);
#1791=ADVANCED_FACE('',(#158),#1693,.T.);
#1792=ADVANCED_FACE('',(#159),#60,.T.);
#1793=ADVANCED_FACE('',(#160),#1694,.T.);
#1794=ADVANCED_FACE('',(#161),#61,.F.);
#1795=ADVANCED_FACE('',(#162),#1695,.T.);
#1796=ADVANCED_FACE('',(#163),#62,.T.);
#1797=ADVANCED_FACE('',(#164),#1696,.T.);
#1798=ADVANCED_FACE('',(#165),#63,.F.);
#1799=ADVANCED_FACE('',(#166),#1697,.T.);
#1800=ADVANCED_FACE('',(#167),#64,.T.);
#1801=ADVANCED_FACE('',(#168),#1698,.T.);
#1802=ADVANCED_FACE('',(#169),#65,.T.);
#1803=ADVANCED_FACE('',(#170),#1699,.T.);
#1804=ADVANCED_FACE('',(#171),#66,.F.);
#1805=ADVANCED_FACE('',(#172),#1700,.T.);
#1806=ADVANCED_FACE('',(#173),#67,.F.);
#1807=ADVANCED_FACE('',(#174),#1701,.T.);
#1808=ADVANCED_FACE('',(#175),#68,.T.);
#1809=ADVANCED_FACE('',(#176,#27,#28),#1702,.T.);
#1810=ADVANCED_FACE('',(#177),#69,.T.);
#1811=CLOSED_SHELL('',(#1703,#1704,#1705,#1706,#1707,#1708,#1709,#1710,
#1711,#1712,#1713,#1714,#1715,#1716,#1717,#1718,#1719,#1720,#1721,#1722,
#1723,#1724,#1725,#1726,#1727,#1728,#1729,#1730,#1731,#1732,#1733,#1734,
#1735,#1736,#1737,#1738,#1739,#1740,#1741,#1742,#1743,#1744,#1745,#1746,
#1747,#1748,#1749,#1750,#1751,#1752,#1753,#1754,#1755,#1756,#1757,#1758,
#1759,#1760,#1761,#1762,#1763,#1764,#1765,#1766,#1767,#1768,#1769,#1770,
#1771,#1772,#1773,#1774,#1775,#1776,#1777,#1778,#1779,#1780,#1781,#1782,
#1783,#1784,#1785,#1786,#1787,#1788,#1789,#1790,#1791,#1792,#1793,#1794,
#1795,#1796,#1797,#1798,#1799,#1800,#1801,#1802,#1803,#1804,#1805,#1806,
#1807,#1808,#1809,#1810));
#1812=DERIVED_UNIT_ELEMENT(#1814,1.);
#1813=DERIVED_UNIT_ELEMENT(#3085,-3.);
#1814=(
MASS_UNIT()
NAMED_UNIT(*)
SI_UNIT(.KILO.,.GRAM.)
);
#1815=DERIVED_UNIT((#1812,#1813));
#1816=MEASURE_REPRESENTATION_ITEM('density measure',
POSITIVE_RATIO_MEASURE(7850.),#1815);
#1817=PROPERTY_DEFINITION_REPRESENTATION(#1822,#1819);
#1818=PROPERTY_DEFINITION_REPRESENTATION(#1823,#1820);
#1819=REPRESENTATION('material name',(#1821),#3082);
#1820=REPRESENTATION('density',(#1816),#3082);
#1821=DESCRIPTIVE_REPRESENTATION_ITEM('Steel','Steel');
#1822=PROPERTY_DEFINITION('material property','material name',#3092);
#1823=PROPERTY_DEFINITION('material property','density of part',#3092);
#1824=AXIS2_PLACEMENT_3D('placement',#2569,#2015,#2016);
#1825=AXIS2_PLACEMENT_3D('',#2570,#2017,#2018);
#1826=AXIS2_PLACEMENT_3D('',#2581,#2023,#2024);
#1827=AXIS2_PLACEMENT_3D('',#2585,#2026,#2027);
#1828=AXIS2_PLACEMENT_3D('',#2593,#2031,#2032);
#1829=AXIS2_PLACEMENT_3D('',#2597,#2034,#2035);
#1830=AXIS2_PLACEMENT_3D('',#2603,#2039,#2040);
#1831=AXIS2_PLACEMENT_3D('',#2607,#2042,#2043);
#1832=AXIS2_PLACEMENT_3D('',#2610,#2045,#2046);
#1833=AXIS2_PLACEMENT_3D('',#2611,#2047,#2048);
#1834=AXIS2_PLACEMENT_3D('',#2615,#2050,#2051);
#1835=AXIS2_PLACEMENT_3D('',#2617,#2053,#2054);
#1836=AXIS2_PLACEMENT_3D('',#2622,#2058,#2059);
#1837=AXIS2_PLACEMENT_3D('',#2626,#2062,#2063);
#1838=AXIS2_PLACEMENT_3D('',#2634,#2067,#2068);
#1839=AXIS2_PLACEMENT_3D('',#2638,#2070,#2071);
#1840=AXIS2_PLACEMENT_3D('',#2642,#2073,#2074);
#1841=AXIS2_PLACEMENT_3D('',#2646,#2076,#2077);
#1842=AXIS2_PLACEMENT_3D('',#2650,#2079,#2080);
#1843=AXIS2_PLACEMENT_3D('',#2658,#2084,#2085);
#1844=AXIS2_PLACEMENT_3D('',#2662,#2087,#2088);
#1845=AXIS2_PLACEMENT_3D('',#2665,#2090,#2091);
#1846=AXIS2_PLACEMENT_3D('',#2667,#2092,#2093);
#1847=AXIS2_PLACEMENT_3D('',#2668,#2094,#2095);
#1848=AXIS2_PLACEMENT_3D('',#2674,#2099,#2100);
#1849=AXIS2_PLACEMENT_3D('',#2676,#2102,#2103);
#1850=AXIS2_PLACEMENT_3D('',#2677,#2104,#2105);
#1851=AXIS2_PLACEMENT_3D('',#2684,#2108,#2109);
#1852=AXIS2_PLACEMENT_3D('',#2687,#2111,#2112);
#1853=AXIS2_PLACEMENT_3D('',#2688,#2113,#2114);
#1854=AXIS2_PLACEMENT_3D('',#2691,#2116,#2117);
#1855=AXIS2_PLACEMENT_3D('',#2692,#2118,#2119);
#1856=AXIS2_PLACEMENT_3D('',#2693,#2120,#2121);
#1857=AXIS2_PLACEMENT_3D('',#2697,#2123,#2124);
#1858=AXIS2_PLACEMENT_3D('',#2699,#2126,#2127);
#1859=AXIS2_PLACEMENT_3D('',#2704,#2131,#2132);
#1860=AXIS2_PLACEMENT_3D('',#2711,#2135,#2136);
#1861=AXIS2_PLACEMENT_3D('',#2714,#2138,#2139);
#1862=AXIS2_PLACEMENT_3D('',#2715,#2140,#2141);
#1863=AXIS2_PLACEMENT_3D('',#2719,#2143,#2144);
#1864=AXIS2_PLACEMENT_3D('',#2721,#2146,#2147);
#1865=AXIS2_PLACEMENT_3D('',#2722,#2148,#2149);
#1866=AXIS2_PLACEMENT_3D('',#2726,#2151,#2152);
#1867=AXIS2_PLACEMENT_3D('',#2728,#2154,#2155);
#1868=AXIS2_PLACEMENT_3D('',#2733,#2159,#2160);
#1869=AXIS2_PLACEMENT_3D('',#2740,#2163,#2164);
#1870=AXIS2_PLACEMENT_3D('',#2743,#2166,#2167);
#1871=AXIS2_PLACEMENT_3D('',#2744,#2168,#2169);
#1872=AXIS2_PLACEMENT_3D('',#2748,#2171,#2172);
#1873=AXIS2_PLACEMENT_3D('',#2750,#2174,#2175);
#1874=AXIS2_PLACEMENT_3D('',#2751,#2176,#2177);
#1875=AXIS2_PLACEMENT_3D('',#2754,#2179,#2180);
#1876=AXIS2_PLACEMENT_3D('',#2756,#2182,#2183);
#1877=AXIS2_PLACEMENT_3D('',#2761,#2187,#2188);
#1878=AXIS2_PLACEMENT_3D('',#2765,#2191,#2192);
#1879=AXIS2_PLACEMENT_3D('',#2769,#2194,#2195);
#1880=AXIS2_PLACEMENT_3D('',#2773,#2197,#2198);
#1881=AXIS2_PLACEMENT_3D('',#2778,#2201,#2202);
#1882=AXIS2_PLACEMENT_3D('',#2782,#2204,#2205);
#1883=AXIS2_PLACEMENT_3D('',#2784,#2207,#2208);
#1884=AXIS2_PLACEMENT_3D('',#2787,#2211,#2212);
#1885=AXIS2_PLACEMENT_3D('',#2788,#2213,#2214);
#1886=AXIS2_PLACEMENT_3D('',#2789,#2215,#2216);
#1887=AXIS2_PLACEMENT_3D('',#2793,#2219,#2220);
#1888=AXIS2_PLACEMENT_3D('',#2794,#2221,#2222);
#1889=AXIS2_PLACEMENT_3D('',#2796,#2223,#2224);
#1890=AXIS2_PLACEMENT_3D('',#2797,#2225,#2226);
#1891=AXIS2_PLACEMENT_3D('',#2800,#2229,#2230);
#1892=AXIS2_PLACEMENT_3D('',#2801,#2231,#2232);
#1893=AXIS2_PLACEMENT_3D('',#2802,#2233,#2234);
#1894=AXIS2_PLACEMENT_3D('',#2806,#2237,#2238);
#1895=AXIS2_PLACEMENT_3D('',#2807,#2239,#2240);
#1896=AXIS2_PLACEMENT_3D('',#2809,#2241,#2242);
#1897=AXIS2_PLACEMENT_3D('',#2810,#2243,#2244);
#1898=AXIS2_PLACEMENT_3D('',#2811,#2245,#2246);
#1899=AXIS2_PLACEMENT_3D('',#2812,#2247,#2248);
#1900=AXIS2_PLACEMENT_3D('',#2813,#2249,#2250);
#1901=AXIS2_PLACEMENT_3D('',#2822,#2255,#2256);
#1902=AXIS2_PLACEMENT_3D('',#2825,#2258,#2259);
#1903=AXIS2_PLACEMENT_3D('',#2826,#2260,#2261);
#1904=AXIS2_PLACEMENT_3D('',#2827,#2262,#2263);
#1905=AXIS2_PLACEMENT_3D('',#2830,#2265,#2266);
#1906=AXIS2_PLACEMENT_3D('',#2831,#2267,#2268);
#1907=AXIS2_PLACEMENT_3D('',#2836,#2272,#2273);
#1908=AXIS2_PLACEMENT_3D('',#2847,#2278,#2279);
#1909=AXIS2_PLACEMENT_3D('',#2849,#2280,#2281);
#1910=AXIS2_PLACEMENT_3D('',#2851,#2283,#2284);
#1911=AXIS2_PLACEMENT_3D('',#2854,#2286,#2287);
#1912=AXIS2_PLACEMENT_3D('',#2857,#2289,#2290);
#1913=AXIS2_PLACEMENT_3D('',#2858,#2291,#2292);
#1914=AXIS2_PLACEMENT_3D('',#2861,#2294,#2295);
#1915=AXIS2_PLACEMENT_3D('',#2862,#2296,#2297);
#1916=AXIS2_PLACEMENT_3D('',#2867,#2301,#2302);
#1917=AXIS2_PLACEMENT_3D('',#2871,#2305,#2306);
#1918=AXIS2_PLACEMENT_3D('',#2872,#2307,#2308);
#1919=AXIS2_PLACEMENT_3D('',#2874,#2309,#2310);
#1920=AXIS2_PLACEMENT_3D('',#2875,#2311,#2312);
#1921=AXIS2_PLACEMENT_3D('',#2881,#2316,#2317);
#1922=AXIS2_PLACEMENT_3D('',#2883,#2319,#2320);
#1923=AXIS2_PLACEMENT_3D('',#2884,#2321,#2322);
#1924=AXIS2_PLACEMENT_3D('',#2888,#2325,#2326);
#1925=AXIS2_PLACEMENT_3D('',#2890,#2328,#2329);
#1926=AXIS2_PLACEMENT_3D('',#2896,#2333,#2334);
#1927=AXIS2_PLACEMENT_3D('',#2898,#2336,#2337);
#1928=AXIS2_PLACEMENT_3D('',#2899,#2338,#2339);
#1929=AXIS2_PLACEMENT_3D('',#2906,#2342,#2343);
#1930=AXIS2_PLACEMENT_3D('',#2909,#2345,#2346);
#1931=AXIS2_PLACEMENT_3D('',#2910,#2347,#2348);
#1932=AXIS2_PLACEMENT_3D('',#2913,#2350,#2351);
#1933=AXIS2_PLACEMENT_3D('',#2914,#2352,#2353);
#1934=AXIS2_PLACEMENT_3D('',#2915,#2354,#2355);
#1935=AXIS2_PLACEMENT_3D('',#2918,#2357,#2358);
#1936=AXIS2_PLACEMENT_3D('',#2919,#2359,#2360);
#1937=AXIS2_PLACEMENT_3D('',#2924,#2364,#2365);
#1938=AXIS2_PLACEMENT_3D('',#2933,#2370,#2371);
#1939=AXIS2_PLACEMENT_3D('',#2936,#2373,#2374);
#1940=AXIS2_PLACEMENT_3D('',#2937,#2375,#2376);
#1941=AXIS2_PLACEMENT_3D('',#2938,#2377,#2378);
#1942=AXIS2_PLACEMENT_3D('',#2942,#2380,#2381);
#1943=AXIS2_PLACEMENT_3D('',#2944,#2383,#2384);
#1944=AXIS2_PLACEMENT_3D('',#2948,#2387,#2388);
#1945=AXIS2_PLACEMENT_3D('',#2950,#2389,#2390);
#1946=AXIS2_PLACEMENT_3D('',#2951,#2391,#2392);
#1947=AXIS2_PLACEMENT_3D('',#2955,#2395,#2396);
#1948=AXIS2_PLACEMENT_3D('',#2957,#2397,#2398);
#1949=AXIS2_PLACEMENT_3D('',#2959,#2399,#2400);
#1950=AXIS2_PLACEMENT_3D('',#2962,#2403,#2404);
#1951=AXIS2_PLACEMENT_3D('',#2963,#2405,#2406);
#1952=AXIS2_PLACEMENT_3D('',#2964,#2407,#2408);
#1953=AXIS2_PLACEMENT_3D('',#2965,#2409,#2410);
#1954=AXIS2_PLACEMENT_3D('',#2967,#2411,#2412);
#1955=AXIS2_PLACEMENT_3D('',#2970,#2414,#2415);
#1956=AXIS2_PLACEMENT_3D('',#2971,#2416,#2417);
#1957=AXIS2_PLACEMENT_3D('',#2973,#2419,#2420);
#1958=AXIS2_PLACEMENT_3D('',#2974,#2421,#2422);
#1959=AXIS2_PLACEMENT_3D('',#2976,#2423,#2424);
#1960=AXIS2_PLACEMENT_3D('',#2977,#2425,#2426);
#1961=AXIS2_PLACEMENT_3D('',#2978,#2427,#2428);
#1962=AXIS2_PLACEMENT_3D('',#2979,#2429,#2430);
#1963=AXIS2_PLACEMENT_3D('',#2982,#2432,#2433);
#1964=AXIS2_PLACEMENT_3D('',#2983,#2434,#2435);
#1965=AXIS2_PLACEMENT_3D('',#2985,#2437,#2438);
#1966=AXIS2_PLACEMENT_3D('',#2987,#2440,#2441);
#1967=AXIS2_PLACEMENT_3D('',#2989,#2443,#2444);
#1968=AXIS2_PLACEMENT_3D('',#2992,#2446,#2447);
#1969=AXIS2_PLACEMENT_3D('',#2993,#2448,#2449);
#1970=AXIS2_PLACEMENT_3D('',#2995,#2451,#2452);
#1971=AXIS2_PLACEMENT_3D('',#2997,#2454,#2455);
#1972=AXIS2_PLACEMENT_3D('',#2999,#2457,#2458);
#1973=AXIS2_PLACEMENT_3D('',#3001,#2460,#2461);
#1974=AXIS2_PLACEMENT_3D('',#3003,#2463,#2464);
#1975=AXIS2_PLACEMENT_3D('',#3004,#2465,#2466);
#1976=AXIS2_PLACEMENT_3D('',#3005,#2467,#2468);
#1977=AXIS2_PLACEMENT_3D('',#3006,#2469,#2470);
#1978=AXIS2_PLACEMENT_3D('',#3010,#2473,#2474);
#1979=AXIS2_PLACEMENT_3D('',#3012,#2475,#2476);
#1980=AXIS2_PLACEMENT_3D('',#3014,#2478,#2479);
#1981=AXIS2_PLACEMENT_3D('',#3018,#2482,#2483);
#1982=AXIS2_PLACEMENT_3D('',#3019,#2484,#2485);
#1983=AXIS2_PLACEMENT_3D('',#3020,#2486,#2487);
#1984=AXIS2_PLACEMENT_3D('',#3021,#2488,#2489);
#1985=AXIS2_PLACEMENT_3D('',#3023,#2491,#2492);
#1986=AXIS2_PLACEMENT_3D('',#3024,#2493,#2494);
#1987=AXIS2_PLACEMENT_3D('',#3026,#2496,#2497);
#1988=AXIS2_PLACEMENT_3D('',#3027,#2498,#2499);
#1989=AXIS2_PLACEMENT_3D('',#3031,#2502,#2503);
#1990=AXIS2_PLACEMENT_3D('',#3033,#2504,#2505);
#1991=AXIS2_PLACEMENT_3D('',#3035,#2507,#2508);
#1992=AXIS2_PLACEMENT_3D('',#3039,#2511,#2512);
#1993=AXIS2_PLACEMENT_3D('',#3041,#2513,#2514);
#1994=AXIS2_PLACEMENT_3D('',#3043,#2516,#2517);
#1995=AXIS2_PLACEMENT_3D('',#3047,#2520,#2521);
#1996=AXIS2_PLACEMENT_3D('',#3049,#2522,#2523);
#1997=AXIS2_PLACEMENT_3D('',#3051,#2525,#2526);
#1998=AXIS2_PLACEMENT_3D('',#3055,#2529,#2530);
#1999=AXIS2_PLACEMENT_3D('',#3057,#2531,#2532);
#2000=AXIS2_PLACEMENT_3D('',#3059,#2534,#2535);
#2001=AXIS2_PLACEMENT_3D('',#3063,#2538,#2539);
#2002=AXIS2_PLACEMENT_3D('',#3064,#2540,#2541);
#2003=AXIS2_PLACEMENT_3D('',#3065,#2542,#2543);
#2004=AXIS2_PLACEMENT_3D('',#3066,#2544,#2545);
#2005=AXIS2_PLACEMENT_3D('',#3067,#2546,#2547);
#2006=AXIS2_PLACEMENT_3D('',#3068,#2548,#2549);
#2007=AXIS2_PLACEMENT_3D('',#3069,#2550,#2551);
#2008=AXIS2_PLACEMENT_3D('',#3071,#2553,#2554);
#2009=AXIS2_PLACEMENT_3D('',#3073,#2556,#2557);
#2010=AXIS2_PLACEMENT_3D('',#3075,#2559,#2560);
#2011=AXIS2_PLACEMENT_3D('',#3076,#2561,#2562);
#2012=AXIS2_PLACEMENT_3D('',#3077,#2563,#2564);
#2013=AXIS2_PLACEMENT_3D('',#3078,#2565,#2566);
#2014=AXIS2_PLACEMENT_3D('',#3079,#2567,#2568);
#2015=DIRECTION('axis',(0.,0.,1.));
#2016=DIRECTION('refdir',(1.,0.,0.));
#2017=DIRECTION('center_axis',(0.,0.999847695156391,0.0174524064372843));
#2018=DIRECTION('ref_axis',(1.,0.,0.));
#2019=DIRECTION('',(1.,0.,0.));
#2020=DIRECTION('',(0.,0.0174524064372843,-0.999847695156391));
#2021=DIRECTION('',(1.,0.,0.));
#2022=DIRECTION('',(0.,-0.0174524064372843,0.999847695156391));
#2023=DIRECTION('center_axis',(0.,0.999847695156391,0.0174524064372843));
#2024=DIRECTION('ref_axis',(8.88178419700118E-15,-0.0174524064372843,0.999847695156391));
#2025=DIRECTION('',(1.,1.65342408207269E-17,-9.47246022213137E-16));
#2026=DIRECTION('center_axis',(0.,0.999847695156391,0.0174524064372843));
#2027=DIRECTION('ref_axis',(-1.,0.,0.));
#2028=DIRECTION('',(0.,-0.0174524064372843,0.999847695156391));
#2029=DIRECTION('',(1.,0.,0.));
#2030=DIRECTION('',(0.,-0.0174524064372843,0.999847695156391));
#2031=DIRECTION('center_axis',(0.,0.999847695156391,0.0174524064372843));
#2032=DIRECTION('ref_axis',(2.96059473233376E-15,-0.0174524064372843,0.999847695156391));
#2033=DIRECTION('',(1.,1.65342408207269E-17,-9.47246022213137E-16));
#2034=DIRECTION('center_axis',(0.,0.999847695156391,0.0174524064372843));
#2035=DIRECTION('ref_axis',(-1.,0.,0.));
#2036=DIRECTION('',(0.,-0.0174524064372843,0.999847695156391));
#2037=DIRECTION('',(1.,0.,0.));
#2038=DIRECTION('',(4.39402252490167E-17,0.0174524064372843,-0.999847695156391));
#2039=DIRECTION('center_axis',(-1.,0.,0.));
#2040=DIRECTION('ref_axis',(0.,0.,1.));
#2041=DIRECTION('',(0.,0.999847695156391,0.0174524064372843));
#2042=DIRECTION('center_axis',(-1.,0.,0.));
#2043=DIRECTION('ref_axis',(0.,0.,1.));
#2044=DIRECTION('',(0.,0.,1.));
#2045=DIRECTION('center_axis',(-1.,0.,0.));
#2046=DIRECTION('ref_axis',(0.,0.,1.));
#2047=DIRECTION('center_axis',(-1.,0.,0.));
#2048=DIRECTION('ref_axis',(0.,0.,1.));
#2049=DIRECTION('',(-1.,0.,0.));
#2050=DIRECTION('center_axis',(1.,0.,0.));
#2051=DIRECTION('ref_axis',(0.,0.999847695156391,0.0174524064372844));
#2052=DIRECTION('',(-1.,0.,0.));
#2053=DIRECTION('center_axis',(1.,0.,0.));
#2054=DIRECTION('ref_axis',(0.,-0.0174524064372843,0.999847695156391));
#2055=DIRECTION('',(0.,-0.999847695156391,-0.0174524064372843));
#2056=DIRECTION('',(0.,-0.0174524064372843,0.999847695156391));
#2057=DIRECTION('',(0.,-0.999847695156391,-0.0174524064372843));
#2058=DIRECTION('center_axis',(-1.,7.6686266999166E-19,-4.39335329398821E-17));
#2059=DIRECTION('ref_axis',(4.39402252490167E-17,0.0174524064372843,-0.999847695156391));
#2060=DIRECTION('',(0.,0.999847695156391,0.0174524064372843));
#2061=DIRECTION('',(4.39402252490167E-17,0.0174524064372843,-0.999847695156391));
#2062=DIRECTION('center_axis',(0.,0.,1.));
#2063=DIRECTION('ref_axis',(1.,0.,0.));
#2064=DIRECTION('',(0.,-1.,0.));
#2065=DIRECTION('',(1.,0.,0.));
#2066=DIRECTION('',(0.,1.,0.));
#2067=DIRECTION('center_axis',(0.,0.,1.));
#2068=DIRECTION('ref_axis',(1.,-8.88178419700134E-15,0.));
#2069=DIRECTION('',(-1.,0.,0.));
#2070=DIRECTION('center_axis',(0.,0.,-1.));
#2071=DIRECTION('ref_axis',(-1.,0.,0.));
#2072=DIRECTION('',(0.,1.,0.));
#2073=DIRECTION('center_axis',(0.,0.,1.));
#2074=DIRECTION('ref_axis',(1.,0.,0.));
#2075=DIRECTION('',(0.,-1.,0.));
#2076=DIRECTION('center_axis',(0.,0.,-1.));
#2077=DIRECTION('ref_axis',(0.,-1.,0.));
#2078=DIRECTION('',(1.,1.77635683940019E-14,0.));
#2079=DIRECTION('center_axis',(0.,0.,1.));
#2080=DIRECTION('ref_axis',(2.96059473233374E-15,1.,0.));
#2081=DIRECTION('',(0.,-1.,0.));
#2082=DIRECTION('',(1.,0.,0.));
#2083=DIRECTION('',(0.,1.,0.));
#2084=DIRECTION('center_axis',(0.,0.,1.));
#2085=DIRECTION('ref_axis',(1.,-1.38777878078145E-15,0.));
#2086=DIRECTION('',(-1.,0.,0.));
#2087=DIRECTION('center_axis',(0.,0.,-1.));
#2088=DIRECTION('ref_axis',(-1.,1.38777878078145E-15,0.));
#2089=DIRECTION('',(4.39402252490167E-17,1.,0.));
#2090=DIRECTION('center_axis',(0.,0.,1.));
#2091=DIRECTION('ref_axis',(1.,0.,0.));
#2092=DIRECTION('center_axis',(0.,0.,1.));
#2093=DIRECTION('ref_axis',(1.,0.,0.));
#2094=DIRECTION('center_axis',(1.,0.,0.));
#2095=DIRECTION('ref_axis',(0.,-1.,0.));
#2096=DIRECTION('',(0.,0.,-1.));
#2097=DIRECTION('',(0.,1.,0.));
#2098=DIRECTION('',(0.,0.,1.));
#2099=DIRECTION('center_axis',(-1.,0.,0.));
#2100=DIRECTION('ref_axis',(0.,0.,1.));
#2101=DIRECTION('',(1.,0.,0.));
#2102=DIRECTION('center_axis',(-1.,0.,0.));
#2103=DIRECTION('ref_axis',(0.,0.,1.));
#2104=DIRECTION('center_axis',(0.,0.369746757273829,0.929132571534056));
#2105=DIRECTION('ref_axis',(1.,0.,0.));
#2106=DIRECTION('',(1.,0.,0.));
#2107=DIRECTION('',(0.,-0.929132571534056,0.369746757273829));
#2108=DIRECTION('center_axis',(0.,0.369746757273829,0.929132571534056));
#2109=DIRECTION('ref_axis',(1.,0.,0.));
#2110=DIRECTION('',(4.39402252490167E-17,0.929132571534056,-0.369746757273829));
#2111=DIRECTION('center_axis',(0.,-0.369746757273829,-0.929132571534056));
#2112=DIRECTION('ref_axis',(1.,0.,0.));
#2113=DIRECTION('center_axis',(-1.,0.,0.));
#2114=DIRECTION('ref_axis',(0.,0.999847695156391,0.0174524064372843));
#2115=DIRECTION('',(0.,0.369746757273829,0.929132571534056));
#2116=DIRECTION('center_axis',(1.,0.,0.));
#2117=DIRECTION('ref_axis',(0.,-0.999847695156391,-0.0174524064372843));
#2118=DIRECTION('center_axis',(1.,0.,0.));
#2119=DIRECTION('ref_axis',(0.,-0.999847695156391,-0.0174524064372843));
#2120=DIRECTION('center_axis',(1.,0.,0.));
#2121=DIRECTION('ref_axis',(0.,-0.999847695156391,-0.0174524064372843));
#2122=DIRECTION('',(-1.,0.,0.));
#2123=DIRECTION('center_axis',(-1.,0.,0.));
#2124=DIRECTION('ref_axis',(0.,-0.369746757273829,-0.929132571534056));
#2125=DIRECTION('',(-1.,0.,0.));
#2126=DIRECTION('center_axis',(1.,0.,0.));
#2127=DIRECTION('ref_axis',(0.,-0.929132571534056,0.369746757273829));
#2128=DIRECTION('',(0.,-0.369746757273829,-0.929132571534056));
#2129=DIRECTION('',(0.,-0.929132571534056,0.369746757273829));
#2130=DIRECTION('',(0.,-0.369746757273829,-0.929132571534056));
#2131=DIRECTION('center_axis',(0.,0.369746757273829,0.929132571534056));
#2132=DIRECTION('ref_axis',(1.,0.,0.));
#2133=DIRECTION('',(1.,0.,0.));
#2134=DIRECTION('',(0.,-0.929132571534056,0.369746757273829));
#2135=DIRECTION('center_axis',(0.,0.369746757273829,0.929132571534056));
#2136=DIRECTION('ref_axis',(1.,0.,0.));
#2137=DIRECTION('',(0.,-0.929132571534056,0.369746757273829));
#2138=DIRECTION('center_axis',(0.,-0.369746757273829,-0.929132571534056));
#2139=DIRECTION('ref_axis',(1.,0.,0.));
#2140=DIRECTION('center_axis',(-1.,0.,0.));
#2141=DIRECTION('ref_axis',(0.,0.999847695156391,0.0174524064372843));
#2142=DIRECTION('',(0.,0.369746757273829,0.929132571534056));
#2143=DIRECTION('center_axis',(1.,0.,0.));
#2144=DIRECTION('ref_axis',(0.,-0.999847695156391,-0.0174524064372843));
#2145=DIRECTION('',(0.,0.999847695156391,0.0174524064372843));
#2146=DIRECTION('center_axis',(-1.,0.,0.));
#2147=DIRECTION('ref_axis',(0.,-0.369746757273828,-0.929132571534057));
#2148=DIRECTION('center_axis',(1.,0.,0.));
#2149=DIRECTION('ref_axis',(0.,-0.999847695156391,-0.0174524064372843));
#2150=DIRECTION('',(-1.,0.,0.));
#2151=DIRECTION('center_axis',(-1.,0.,0.));
#2152=DIRECTION('ref_axis',(0.,-0.369746757273829,-0.929132571534056));
#2153=DIRECTION('',(-1.,0.,0.));
#2154=DIRECTION('center_axis',(1.,0.,0.));
#2155=DIRECTION('ref_axis',(0.,-0.929132571534056,0.369746757273829));
#2156=DIRECTION('',(0.,-0.369746757273829,-0.929132571534056));
#2157=DIRECTION('',(0.,-0.929132571534056,0.369746757273829));
#2158=DIRECTION('',(0.,-0.369746757273829,-0.929132571534056));
#2159=DIRECTION('center_axis',(0.,0.369746757273829,0.929132571534056));
#2160=DIRECTION('ref_axis',(1.,0.,0.));
#2161=DIRECTION('',(1.,0.,0.));
#2162=DIRECTION('',(0.,0.929132571534056,-0.369746757273829));
#2163=DIRECTION('center_axis',(0.,0.369746757273829,0.929132571534056));
#2164=DIRECTION('ref_axis',(1.,0.,0.));
#2165=DIRECTION('',(0.,-0.929132571534056,0.369746757273829));
#2166=DIRECTION('center_axis',(0.,-0.369746757273829,-0.929132571534056));
#2167=DIRECTION('ref_axis',(1.,0.,0.));
#2168=DIRECTION('center_axis',(-1.,0.,0.));
#2169=DIRECTION('ref_axis',(0.,0.999847695156391,0.0174524064372843));
#2170=DIRECTION('',(0.,0.369746757273829,0.929132571534056));
#2171=DIRECTION('center_axis',(1.,0.,0.));
#2172=DIRECTION('ref_axis',(0.,-0.999847695156391,-0.0174524064372843));
#2173=DIRECTION('',(0.,0.999847695156391,0.0174524064372843));
#2174=DIRECTION('center_axis',(-1.,0.,0.));
#2175=DIRECTION('ref_axis',(0.,-0.369746757273828,-0.929132571534057));
#2176=DIRECTION('center_axis',(1.,0.,0.));
#2177=DIRECTION('ref_axis',(0.,-0.999847695156391,-0.0174524064372843));
#2178=DIRECTION('',(-1.,0.,0.));
#2179=DIRECTION('center_axis',(-1.,0.,0.));
#2180=DIRECTION('ref_axis',(0.,-0.369746757273829,-0.929132571534056));
#2181=DIRECTION('',(-1.,0.,0.));
#2182=DIRECTION('center_axis',(1.,0.,0.));
#2183=DIRECTION('ref_axis',(0.,-0.929132571534056,0.369746757273829));
#2184=DIRECTION('',(0.,-0.369746757273829,-0.929132571534056));
#2185=DIRECTION('',(0.,-0.929132571534056,0.369746757273829));
#2186=DIRECTION('',(0.,-0.369746757273829,-0.929132571534056));
#2187=DIRECTION('center_axis',(-1.,4.08262944794046E-17,-1.62467557997056E-17));
#2188=DIRECTION('ref_axis',(4.39402252490167E-17,0.929132571534056,-0.369746757273829));
#2189=DIRECTION('',(0.,-0.369746757273829,-0.929132571534056));
#2190=DIRECTION('',(4.39402252490167E-17,0.929132571534056,-0.369746757273829));
#2191=DIRECTION('center_axis',(0.,0.999847695156391,0.0174524064372843));
#2192=DIRECTION('ref_axis',(1.,0.,0.));
#2193=DIRECTION('',(0.,-0.0174524064372843,0.999847695156391));
#2194=DIRECTION('center_axis',(0.,0.999847695156391,0.0174524064372843));
#2195=DIRECTION('ref_axis',(-1.,0.,0.));
#2196=DIRECTION('',(1.,1.65342408207269E-17,-9.47246022213137E-16));
#2197=DIRECTION('center_axis',(0.,0.999847695156391,0.0174524064372843));
#2198=DIRECTION('ref_axis',(2.96059473233376E-15,-0.0174524064372843,0.999847695156391));
#2199=DIRECTION('',(0.,0.0174524064372843,-0.999847695156391));
#2200=DIRECTION('',(0.,-0.0174524064372843,0.999847695156391));
#2201=DIRECTION('center_axis',(0.,0.999847695156391,0.0174524064372843));
#2202=DIRECTION('ref_axis',(-1.,0.,0.));
#2203=DIRECTION('',(1.,1.65342408207269E-17,-9.47246022213137E-16));
#2204=DIRECTION('center_axis',(0.,0.999847695156391,0.0174524064372843));
#2205=DIRECTION('ref_axis',(8.88178419700118E-15,-0.0174524064372843,0.999847695156391));
#2206=DIRECTION('',(0.,0.0174524064372843,-0.999847695156391));
#2207=DIRECTION('center_axis',(1.,0.,0.));
#2208=DIRECTION('ref_axis',(0.,-0.0174524064372843,0.999847695156391));
#2209=DIRECTION('',(0.,-0.999847695156391,-0.0174524064372843));
#2210=DIRECTION('',(0.,-0.999847695156391,-0.0174524064372843));
#2211=DIRECTION('center_axis',(1.,0.,0.));
#2212=DIRECTION('ref_axis',(0.,-0.999847695156391,-0.0174524064372843));
#2213=DIRECTION('center_axis',(-1.,0.,0.));
#2214=DIRECTION('ref_axis',(0.,-0.369746757273828,-0.929132571534057));
#2215=DIRECTION('center_axis',(-1.,0.,0.));
#2216=DIRECTION('ref_axis',(0.,0.929132571534056,-0.369746757273829));
#2217=DIRECTION('',(0.,-0.369746757273829,-0.929132571534056));
#2218=DIRECTION('',(0.,0.929132571534056,-0.369746757273829));
#2219=DIRECTION('center_axis',(0.,0.369746757273829,0.929132571534056));
#2220=DIRECTION('ref_axis',(1.,0.,0.));
#2221=DIRECTION('center_axis',(0.,-0.369746757273829,-0.929132571534056));
#2222=DIRECTION('ref_axis',(1.,0.,0.));
#2223=DIRECTION('center_axis',(0.,0.369746757273829,0.929132571534056));
#2224=DIRECTION('ref_axis',(1.,0.,0.));
#2225=DIRECTION('center_axis',(1.,0.,0.));
#2226=DIRECTION('ref_axis',(0.,-0.0174524064372843,0.999847695156391));
#2227=DIRECTION('',(0.,-0.999847695156391,-0.0174524064372843));
#2228=DIRECTION('',(0.,-0.999847695156391,-0.0174524064372843));
#2229=DIRECTION('center_axis',(1.,0.,0.));
#2230=DIRECTION('ref_axis',(0.,-0.999847695156391,-0.0174524064372843));
#2231=DIRECTION('center_axis',(-1.,0.,0.));
#2232=DIRECTION('ref_axis',(0.,-0.369746757273828,-0.929132571534057));
#2233=DIRECTION('center_axis',(-1.,0.,0.));
#2234=DIRECTION('ref_axis',(0.,0.929132571534056,-0.369746757273829));
#2235=DIRECTION('',(0.,-0.369746757273829,-0.929132571534056));
#2236=DIRECTION('',(0.,0.929132571534056,-0.369746757273829));
#2237=DIRECTION('center_axis',(0.,0.369746757273829,0.929132571534056));
#2238=DIRECTION('ref_axis',(1.,0.,0.));
#2239=DIRECTION('center_axis',(0.,-0.369746757273829,-0.929132571534056));
#2240=DIRECTION('ref_axis',(1.,0.,0.));
#2241=DIRECTION('center_axis',(0.,0.369746757273829,0.929132571534056));
#2242=DIRECTION('ref_axis',(1.,0.,0.));
#2243=DIRECTION('center_axis',(1.,0.,0.));
#2244=DIRECTION('ref_axis',(0.,0.,-1.));
#2245=DIRECTION('center_axis',(1.,0.,0.));
#2246=DIRECTION('ref_axis',(0.,-0.999847695156391,-0.0174524064372843));
#2247=DIRECTION('center_axis',(1.,0.,0.));
#2248=DIRECTION('ref_axis',(0.,-0.999847695156391,-0.0174524064372843));
#2249=DIRECTION('center_axis',(0.,-0.994521895368273,0.104528463267654));
#2250=DIRECTION('ref_axis',(1.,0.,0.));
#2251=DIRECTION('',(-1.,0.,0.));
#2252=DIRECTION('',(0.,0.104528463267654,0.994521895368273));
#2253=DIRECTION('',(-1.,-6.14020670097537E-18,-5.8420164377339E-17));
#2254=DIRECTION('',(0.,0.104528463267654,0.994521895368273));
#2255=DIRECTION('center_axis',(1.,0.,0.));
#2256=DIRECTION('ref_axis',(0.,0.,-1.));
#2257=DIRECTION('',(0.,-0.994521895368273,0.104528463267654));
#2258=DIRECTION('center_axis',(1.,0.,0.));
#2259=DIRECTION('ref_axis',(0.,0.,1.));
#2260=DIRECTION('center_axis',(-1.,0.,0.));
#2261=DIRECTION('ref_axis',(0.,-0.994521895368273,0.104528463267654));
#2262=DIRECTION('center_axis',(1.,0.,0.));
#2263=DIRECTION('ref_axis',(0.,0.,1.));
#2264=DIRECTION('',(1.,0.,0.));
#2265=DIRECTION('center_axis',(-1.,0.,0.));
#2266=DIRECTION('ref_axis',(0.,-0.994521895368273,0.104528463267654));
#2267=DIRECTION('center_axis',(-1.,0.,0.));
#2268=DIRECTION('ref_axis',(0.,0.104528463267654,0.994521895368273));
#2269=DIRECTION('',(0.,0.994521895368273,-0.104528463267654));
#2270=DIRECTION('',(0.,0.104528463267654,0.994521895368273));
#2271=DIRECTION('',(0.,0.994521895368273,-0.104528463267654));
#2272=DIRECTION('center_axis',(0.,-0.994521895368273,0.104528463267654));
#2273=DIRECTION('ref_axis',(1.,0.,0.));
#2274=DIRECTION('',(-1.,0.,0.));
#2275=DIRECTION('',(0.,0.104528463267654,0.994521895368273));
#2276=DIRECTION('',(-1.,-6.14020670097537E-18,-5.8420164377339E-17));
#2277=DIRECTION('',(0.,0.104528463267654,0.994521895368273));
#2278=DIRECTION('center_axis',(0.,-0.994521895368273,0.104528463267654));
#2279=DIRECTION('ref_axis',(1.,-7.73666044322897E-17,-7.36094071154472E-16));
#2280=DIRECTION('center_axis',(0.,-0.994521895368273,0.104528463267654));
#2281=DIRECTION('ref_axis',(-1.,-1.54733208864579E-16,-1.47218814230894E-15));
#2282=DIRECTION('',(0.,0.104528463267654,0.994521895368273));
#2283=DIRECTION('center_axis',(1.,0.,0.));
#2284=DIRECTION('ref_axis',(0.,0.,-1.));
#2285=DIRECTION('',(0.,-0.994521895368273,0.104528463267654));
#2286=DIRECTION('center_axis',(1.,0.,0.));
#2287=DIRECTION('ref_axis',(0.,0.,1.));
#2288=DIRECTION('',(0.,0.,1.));
#2289=DIRECTION('center_axis',(-1.,0.,0.));
#2290=DIRECTION('ref_axis',(0.,-0.994521895368273,0.104528463267654));
#2291=DIRECTION('center_axis',(1.,0.,0.));
#2292=DIRECTION('ref_axis',(0.,0.,1.));
#2293=DIRECTION('',(1.,0.,0.));
#2294=DIRECTION('center_axis',(-1.,0.,0.));
#2295=DIRECTION('ref_axis',(0.,-0.994521895368273,0.104528463267654));
#2296=DIRECTION('center_axis',(-1.,0.,0.));
#2297=DIRECTION('ref_axis',(0.,0.104528463267654,0.994521895368273));
#2298=DIRECTION('',(0.,0.994521895368273,-0.104528463267654));
#2299=DIRECTION('',(0.,0.104528463267654,0.994521895368273));
#2300=DIRECTION('',(0.,0.994521895368273,-0.104528463267654));
#2301=DIRECTION('center_axis',(1.,0.,0.));
#2302=DIRECTION('ref_axis',(0.,-0.104528463267654,-0.994521895368273));
#2303=DIRECTION('',(0.,-0.994521895368273,0.104528463267654));
#2304=DIRECTION('',(0.,-0.104528463267654,-0.994521895368273));
#2305=DIRECTION('center_axis',(0.,0.369746757273829,0.929132571534056));
#2306=DIRECTION('ref_axis',(1.,0.,0.));
#2307=DIRECTION('center_axis',(0.,-0.369746757273829,-0.929132571534056));
#2308=DIRECTION('ref_axis',(1.,0.,0.));
#2309=DIRECTION('center_axis',(0.,0.369746757273829,0.929132571534056));
#2310=DIRECTION('ref_axis',(1.,0.,0.));
#2311=DIRECTION('center_axis',(-1.,0.,0.));
#2312=DIRECTION('ref_axis',(0.,1.,0.));
#2313=DIRECTION('',(0.,0.,-1.));
#2314=DIRECTION('',(0.,1.,0.));
#2315=DIRECTION('',(0.,0.,-1.));
#2316=DIRECTION('center_axis',(1.,0.,0.));
#2317=DIRECTION('ref_axis',(0.,0.,1.));
#2318=DIRECTION('',(-1.,0.,0.));
#2319=DIRECTION('center_axis',(-1.,0.,0.));
#2320=DIRECTION('ref_axis',(0.,-0.994521895368273,0.104528463267654));
#2321=DIRECTION('center_axis',(1.,0.,0.));
#2322=DIRECTION('ref_axis',(0.,-0.104528463267654,-0.994521895368273));
#2323=DIRECTION('',(0.,0.994521895368273,-0.104528463267654));
#2324=DIRECTION('',(0.,-0.104528463267654,-0.994521895368273));
#2325=DIRECTION('center_axis',(0.,-0.994521895368273,0.104528463267654));
#2326=DIRECTION('ref_axis',(1.,0.,0.));
#2327=DIRECTION('',(1.,6.14020670097537E-18,5.8420164377339E-17));
#2328=DIRECTION('center_axis',(-1.,0.,0.));
#2329=DIRECTION('ref_axis',(0.,1.,0.));
#2330=DIRECTION('',(0.,0.,-1.));
#2331=DIRECTION('',(0.,1.,0.));
#2332=DIRECTION('',(0.,0.,-1.));
#2333=DIRECTION('center_axis',(1.,0.,0.));
#2334=DIRECTION('ref_axis',(0.,0.,1.));
#2335=DIRECTION('',(-1.,0.,0.));
#2336=DIRECTION('center_axis',(-1.,0.,0.));
#2337=DIRECTION('ref_axis',(0.,-0.994521895368273,0.104528463267654));
#2338=DIRECTION('center_axis',(-6.01788044531115E-17,-0.241921895599668,
0.970295726275996));
#2339=DIRECTION('ref_axis',(1.,-5.54445546022382E-17,4.81972159998533E-17));
#2340=DIRECTION('',(-1.,-1.55251714416393E-18,-6.24081820662932E-17));
#2341=DIRECTION('',(4.21376525185167E-17,0.970295726275996,0.241921895599668));
#2342=DIRECTION('center_axis',(-6.01788044531115E-17,-0.241921895599668,
0.970295726275996));
#2343=DIRECTION('ref_axis',(-1.,5.54445546022382E-17,-4.81972159998533E-17));
#2344=DIRECTION('',(4.21376525185167E-17,0.970295726275996,0.241921895599668));
#2345=DIRECTION('center_axis',(6.01788044531115E-17,0.241921895599668,-0.970295726275996));
#2346=DIRECTION('ref_axis',(1.,-5.54445546022382E-17,4.81972159998533E-17));
#2347=DIRECTION('center_axis',(1.,6.69410167923576E-18,6.36901230698721E-17));
#2348=DIRECTION('ref_axis',(0.,0.994521895368273,-0.104528463267654));
#2349=DIRECTION('',(-6.01788044531115E-17,-0.241921895599668,0.970295726275996));
#2350=DIRECTION('center_axis',(-1.,-6.69410167923576E-18,-6.36901230698721E-17));
#2351=DIRECTION('ref_axis',(0.,0.994521895368273,-0.104528463267656));
#2352=DIRECTION('center_axis',(1.,6.69410167923576E-18,6.36901230698721E-17));
#2353=DIRECTION('ref_axis',(0.,0.241921895599668,-0.970295726275996));
#2354=DIRECTION('center_axis',(-1.,-6.69410167923576E-18,-6.36901230698721E-17));
#2355=DIRECTION('ref_axis',(0.,0.994521895368273,-0.104528463267654));
#2356=DIRECTION('',(1.,1.55251714416393E-18,6.24081820662932E-17));
#2357=DIRECTION('center_axis',(1.,6.69410167923576E-18,6.36901230698721E-17));
#2358=DIRECTION('ref_axis',(0.,0.241921895599668,-0.970295726275996));
#2359=DIRECTION('center_axis',(-1.,5.54445546022382E-17,-4.81972159998533E-17));
#2360=DIRECTION('ref_axis',(4.21376525185167E-17,0.970295726275996,0.241921895599668));
#2361=DIRECTION('',(6.01788044531115E-17,0.241921895599668,-0.970295726275996));
#2362=DIRECTION('',(4.21376525185167E-17,0.970295726275996,0.241921895599668));
#2363=DIRECTION('',(6.01788044531115E-17,0.241921895599668,-0.970295726275996));
#2364=DIRECTION('center_axis',(-1.,5.54445546022382E-17,-4.81972159998533E-17));
#2365=DIRECTION('ref_axis',(4.21376525185167E-17,0.970295726275996,0.241921895599668));
#2366=DIRECTION('',(6.01788044531115E-17,0.241921895599668,-0.970295726275996));
#2367=DIRECTION('',(4.21376525185167E-17,0.970295726275996,0.241921895599668));
#2368=DIRECTION('',(6.01788044531115E-17,0.241921895599668,-0.970295726275996));
#2369=DIRECTION('',(4.21376525185167E-17,0.970295726275996,0.241921895599668));
#2370=DIRECTION('center_axis',(-1.,-6.69410167923576E-18,-6.36901230698721E-17));
#2371=DIRECTION('ref_axis',(0.,0.994521895368273,-0.104528463267654));
#2372=DIRECTION('',(-1.,-1.55251714416393E-18,-6.24081820662932E-17));
#2373=DIRECTION('center_axis',(1.,6.69410167923576E-18,6.36901230698721E-17));
#2374=DIRECTION('ref_axis',(0.,0.241921895599668,-0.970295726275996));
#2375=DIRECTION('center_axis',(1.,6.69410167923576E-18,6.36901230698721E-17));
#2376=DIRECTION('ref_axis',(0.,0.241921895599668,-0.970295726275996));
#2377=DIRECTION('center_axis',(1.,6.69410167923576E-18,6.36901230698721E-17));
#2378=DIRECTION('ref_axis',(0.,0.994521895368273,-0.104528463267654));
#2379=DIRECTION('',(-6.01788044531115E-17,-0.241921895599668,0.970295726275996));
#2380=DIRECTION('center_axis',(-1.,-6.69410167923576E-18,-6.36901230698721E-17));
#2381=DIRECTION('ref_axis',(0.,0.994521895368273,-0.104528463267656));
#2382=DIRECTION('',(0.,-0.994521895368273,0.104528463267654));
#2383=DIRECTION('center_axis',(-6.01788044531115E-17,-0.241921895599668,
0.970295726275996));
#2384=DIRECTION('ref_axis',(1.,-5.54445546022382E-17,4.81972159998533E-17));
#2385=DIRECTION('',(1.,1.55251714416393E-18,6.24081820662932E-17));
#2386=DIRECTION('',(-4.21376525185167E-17,-0.970295726275996,-0.241921895599668));
#2387=DIRECTION('center_axis',(6.01788044531115E-17,0.241921895599668,-0.970295726275996));
#2388=DIRECTION('ref_axis',(-0.999934791257286,0.0110806426623112,0.00276271450521294));
#2389=DIRECTION('center_axis',(-6.01788044531115E-17,-0.241921895599668,
0.970295726275996));
#2390=DIRECTION('ref_axis',(1.,-5.54445546022382E-17,4.81972159998533E-17));
#2391=DIRECTION('center_axis',(1.,-5.54445546022382E-17,4.81972159998533E-17));
#2392=DIRECTION('ref_axis',(-4.21376525185167E-17,-0.970295726275996,-0.241921895599668));
#2393=DIRECTION('',(6.01788044531115E-17,0.241921895599668,-0.970295726275996));
#2394=DIRECTION('',(-4.21376525185167E-17,-0.970295726275996,-0.241921895599668));
#2395=DIRECTION('center_axis',(0.,-0.994521895368273,0.104528463267654));
#2396=DIRECTION('ref_axis',(1.,0.,0.));
#2397=DIRECTION('center_axis',(0.,-0.994521895368273,0.104528463267654));
#2398=DIRECTION('ref_axis',(-1.,-1.54733208864579E-16,-1.47218814230894E-15));
#2399=DIRECTION('center_axis',(0.,0.994521895368273,-0.104528463267654));
#2400=DIRECTION('ref_axis',(1.,-7.73666044322897E-17,-7.36094071154472E-16));
#2401=DIRECTION('',(0.,-0.104528463267654,-0.994521895368273));
#2402=DIRECTION('',(1.,6.14020670097537E-18,5.8420164377339E-17));
#2403=DIRECTION('center_axis',(-1.,0.,0.));
#2404=DIRECTION('ref_axis',(0.,0.,1.));
#2405=DIRECTION('center_axis',(-1.,-6.69410167923576E-18,-6.36901230698721E-17));
#2406=DIRECTION('ref_axis',(0.,0.994521895368273,-0.104528463267654));
#2407=DIRECTION('center_axis',(1.,6.69410167923576E-18,6.36901230698721E-17));
#2408=DIRECTION('ref_axis',(0.,0.241921895599668,-0.970295726275996));
#2409=DIRECTION('center_axis',(-6.01788044531115E-17,-0.241921895599668,
0.970295726275996));
#2410=DIRECTION('ref_axis',(1.,-5.54445546022382E-17,4.81972159998533E-17));
#2411=DIRECTION('center_axis',(-6.01788044531115E-17,-0.241921895599668,
0.970295726275996));
#2412=DIRECTION('ref_axis',(-0.999934791257286,0.0110806426623112,0.00276271450521294));
#2413=DIRECTION('',(4.21376525185167E-17,0.970295726275996,0.241921895599668));
#2414=DIRECTION('center_axis',(6.01788044531115E-17,0.241921895599668,-0.970295726275996));
#2415=DIRECTION('ref_axis',(1.,-5.54445546022382E-17,4.81972159998533E-17));
#2416=DIRECTION('center_axis',(1.,0.,0.));
#2417=DIRECTION('ref_axis',(0.,-0.104528463267654,-0.994521895368273));
#2418=DIRECTION('',(0.,0.994521895368273,-0.104528463267654));
#2419=DIRECTION('center_axis',(-6.01788044531115E-17,-0.241921895599668,
0.970295726275996));
#2420=DIRECTION('ref_axis',(1.,-5.54445546022382E-17,4.81972159998533E-17));
#2421=DIRECTION('center_axis',(6.01788044531115E-17,0.241921895599668,-0.970295726275996));
#2422=DIRECTION('ref_axis',(-1.,5.54445546022382E-17,-4.81972159998533E-17));
#2423=DIRECTION('center_axis',(-6.01788044531115E-17,-0.241921895599668,
0.970295726275996));
#2424=DIRECTION('ref_axis',(1.,-5.54445546022382E-17,4.81972159998533E-17));
#2425=DIRECTION('center_axis',(-1.,-6.69410167923576E-18,-6.36901230698721E-17));
#2426=DIRECTION('ref_axis',(0.,-0.994521895368273,0.104528463267654));
#2427=DIRECTION('center_axis',(1.,6.69410167923576E-18,6.36901230698721E-17));
#2428=DIRECTION('ref_axis',(0.,0.241921895599669,-0.970295726275996));
#2429=DIRECTION('center_axis',(0.,0.,-1.));
#2430=DIRECTION('ref_axis',(1.,0.,0.));
#2431=DIRECTION('',(0.,0.,1.));
#2432=DIRECTION('center_axis',(0.,0.,-1.));
#2433=DIRECTION('ref_axis',(1.,0.,0.));
#2434=DIRECTION('center_axis',(6.01788044531115E-17,0.241921895599668,-0.970295726275996));
#2435=DIRECTION('ref_axis',(1.,-5.54445546022382E-17,4.81972159998533E-17));
#2436=DIRECTION('',(-6.01788044531115E-17,-0.241921895599668,0.970295726275996));
#2437=DIRECTION('center_axis',(0.,-0.369746757273829,-0.929132571534056));
#2438=DIRECTION('ref_axis',(1.,0.,0.));
#2439=DIRECTION('',(0.,0.369746757273829,0.929132571534056));
#2440=DIRECTION('center_axis',(6.01788044531115E-17,0.241921895599668,-0.970295726275996));
#2441=DIRECTION('ref_axis',(1.,-5.54445546022382E-17,4.81972159998533E-17));
#2442=DIRECTION('',(-6.01788044531115E-17,-0.241921895599668,0.970295726275996));
#2443=DIRECTION('center_axis',(0.,0.,-1.));
#2444=DIRECTION('ref_axis',(1.,0.,0.));
#2445=DIRECTION('',(0.,0.,1.));
#2446=DIRECTION('center_axis',(0.,0.,-1.));
#2447=DIRECTION('ref_axis',(1.,0.,0.));
#2448=DIRECTION('center_axis',(0.,-0.369746757273829,-0.929132571534056));
#2449=DIRECTION('ref_axis',(1.,0.,0.));
#2450=DIRECTION('',(0.,0.369746757273829,0.929132571534056));
#2451=DIRECTION('center_axis',(0.,-0.369746757273829,-0.929132571534056));
#2452=DIRECTION('ref_axis',(1.,0.,0.));
#2453=DIRECTION('',(0.,0.369746757273829,0.929132571534056));
#2454=DIRECTION('center_axis',(-1.,0.,0.));
#2455=DIRECTION('ref_axis',(0.,0.0174524064372843,-0.999847695156391));
#2456=DIRECTION('',(0.,-0.999847695156391,-0.0174524064372843));
#2457=DIRECTION('center_axis',(0.,-0.999847695156391,-0.0174524064372843));
#2458=DIRECTION('ref_axis',(2.96059473233376E-15,-0.0174524064372843,0.999847695156391));
#2459=DIRECTION('',(0.,-0.999847695156391,-0.0174524064372843));
#2460=DIRECTION('center_axis',(-9.473903143468E-16,0.0174524064372843,-0.999847695156391));
#2461=DIRECTION('ref_axis',(1.,1.65342408207269E-17,-9.47246022213137E-16));
#2462=DIRECTION('',(0.,-0.999847695156391,-0.0174524064372843));
#2463=DIRECTION('center_axis',(0.,-0.999847695156391,-0.0174524064372843));
#2464=DIRECTION('ref_axis',(-1.,0.,0.));
#2465=DIRECTION('center_axis',(1.,0.,0.));
#2466=DIRECTION('ref_axis',(0.,-0.999847695156391,-0.0174524064372843));
#2467=DIRECTION('center_axis',(0.,-0.369746757273829,-0.929132571534056));
#2468=DIRECTION('ref_axis',(1.,0.,0.));
#2469=DIRECTION('center_axis',(-1.,4.39402252490167E-17,0.));
#2470=DIRECTION('ref_axis',(4.39402252490167E-17,1.,0.));
#2471=DIRECTION('',(0.,0.,-1.));
#2472=DIRECTION('',(4.39402252490167E-17,1.,0.));
#2473=DIRECTION('center_axis',(0.,0.,-1.));
#2474=DIRECTION('ref_axis',(-1.,1.38777878078145E-15,0.));
#2475=DIRECTION('center_axis',(0.,0.,1.));
#2476=DIRECTION('ref_axis',(-1.,1.38777878078145E-15,0.));
#2477=DIRECTION('',(0.,0.,-1.));
#2478=DIRECTION('center_axis',(0.,-1.,0.));
#2479=DIRECTION('ref_axis',(-1.,0.,0.));
#2480=DIRECTION('',(-1.,0.,0.));
#2481=DIRECTION('',(0.,0.,-1.));
#2482=DIRECTION('center_axis',(0.,0.,-1.));
#2483=DIRECTION('ref_axis',(1.,-1.38777878078145E-15,0.));
#2484=DIRECTION('center_axis',(0.,0.,1.));
#2485=DIRECTION('ref_axis',(1.,-1.38777878078145E-15,0.));
#2486=DIRECTION('center_axis',(-1.,0.,0.));
#2487=DIRECTION('ref_axis',(0.,0.,1.));
#2488=DIRECTION('center_axis',(6.01788044531115E-17,0.241921895599668,-0.970295726275996));
#2489=DIRECTION('ref_axis',(-2.69749500514394E-16,-0.970295726275996,-0.241921895599668));
#2490=DIRECTION('',(6.01788044531115E-17,0.241921895599668,-0.970295726275996));
#2491=DIRECTION('center_axis',(1.,-5.54445546022382E-17,4.81972159998533E-17));
#2492=DIRECTION('ref_axis',(-4.21376525185167E-17,-0.970295726275996,-0.241921895599668));
#2493=DIRECTION('center_axis',(0.,0.994521895368273,-0.104528463267654));
#2494=DIRECTION('ref_axis',(1.,-7.73666044322897E-17,-7.36094071154472E-16));
#2495=DIRECTION('',(0.,0.994521895368273,-0.104528463267654));
#2496=DIRECTION('center_axis',(0.,0.994521895368273,-0.104528463267654));
#2497=DIRECTION('ref_axis',(-1.,-1.54733208864579E-16,-1.47218814230894E-15));
#2498=DIRECTION('center_axis',(1.,0.,0.));
#2499=DIRECTION('ref_axis',(0.,-1.,0.));
#2500=DIRECTION('',(0.,0.,-1.));
#2501=DIRECTION('',(0.,1.,0.));
#2502=DIRECTION('center_axis',(0.,0.,-1.));
#2503=DIRECTION('ref_axis',(2.96059473233374E-15,1.,0.));
#2504=DIRECTION('center_axis',(0.,0.,1.));
#2505=DIRECTION('ref_axis',(2.96059473233374E-15,1.,0.));
#2506=DIRECTION('',(0.,0.,-1.));
#2507=DIRECTION('center_axis',(-1.77635683940019E-14,1.,0.));
#2508=DIRECTION('ref_axis',(1.,1.77635683940019E-14,0.));
#2509=DIRECTION('',(-1.,-1.77635683940019E-14,0.));
#2510=DIRECTION('',(0.,0.,-1.));
#2511=DIRECTION('center_axis',(0.,0.,-1.));
#2512=DIRECTION('ref_axis',(0.,-1.,0.));
#2513=DIRECTION('center_axis',(0.,0.,1.));
#2514=DIRECTION('ref_axis',(0.,-1.,0.));
#2515=DIRECTION('',(0.,0.,-1.));
#2516=DIRECTION('center_axis',(1.,0.,0.));
#2517=DIRECTION('ref_axis',(0.,-1.,0.));
#2518=DIRECTION('',(0.,-1.,0.));
#2519=DIRECTION('',(0.,0.,-1.));
#2520=DIRECTION('center_axis',(0.,0.,-1.));
#2521=DIRECTION('ref_axis',(1.,0.,0.));
#2522=DIRECTION('center_axis',(0.,0.,1.));
#2523=DIRECTION('ref_axis',(1.,0.,0.));
#2524=DIRECTION('',(0.,0.,-1.));
#2525=DIRECTION('center_axis',(-1.,0.,0.));
#2526=DIRECTION('ref_axis',(0.,1.,0.));
#2527=DIRECTION('',(0.,-1.,0.));
#2528=DIRECTION('',(0.,0.,-1.));
#2529=DIRECTION('center_axis',(0.,0.,-1.));
#2530=DIRECTION('ref_axis',(-1.,0.,0.));
#2531=DIRECTION('center_axis',(0.,0.,1.));
#2532=DIRECTION('ref_axis',(-1.,0.,0.));
#2533=DIRECTION('',(0.,0.,-1.));
#2534=DIRECTION('center_axis',(0.,-1.,0.));
#2535=DIRECTION('ref_axis',(-1.,0.,0.));
#2536=DIRECTION('',(1.,0.,0.));
#2537=DIRECTION('',(0.,0.,-1.));
#2538=DIRECTION('center_axis',(0.,0.,-1.));
#2539=DIRECTION('ref_axis',(1.,-8.88178419700134E-15,0.));
#2540=DIRECTION('center_axis',(0.,0.,1.));
#2541=DIRECTION('ref_axis',(1.,-8.88178419700134E-15,0.));
#2542=DIRECTION('center_axis',(-1.,-6.69410167923576E-18,-6.36901230698721E-17));
#2543=DIRECTION('ref_axis',(0.,-0.994521895368273,0.104528463267654));
#2544=DIRECTION('center_axis',(6.01788044531115E-17,0.241921895599668,-0.970295726275996));
#2545=DIRECTION('ref_axis',(-1.,5.54445546022382E-17,-4.81972159998533E-17));
#2546=DIRECTION('center_axis',(1.,0.,0.));
#2547=DIRECTION('ref_axis',(0.,-0.999847695156391,-0.0174524064372843));
#2548=DIRECTION('center_axis',(0.,-0.369746757273829,-0.929132571534056));
#2549=DIRECTION('ref_axis',(1.,0.,0.));
#2550=DIRECTION('center_axis',(-1.,0.,0.));
#2551=DIRECTION('ref_axis',(0.,0.0174524064372843,-0.999847695156391));
#2552=DIRECTION('',(0.,-0.999847695156391,-0.0174524064372843));
#2553=DIRECTION('center_axis',(0.,-0.999847695156391,-0.0174524064372843));
#2554=DIRECTION('ref_axis',(8.88178419700118E-15,-0.0174524064372843,0.999847695156391));
#2555=DIRECTION('',(0.,-0.999847695156391,-0.0174524064372843));
#2556=DIRECTION('center_axis',(-9.473903143468E-16,0.0174524064372843,-0.999847695156391));
#2557=DIRECTION('ref_axis',(1.,1.65342408207269E-17,-9.47246022213137E-16));
#2558=DIRECTION('',(0.,-0.999847695156391,-0.0174524064372843));
#2559=DIRECTION('center_axis',(0.,-0.999847695156391,-0.0174524064372843));
#2560=DIRECTION('ref_axis',(-1.,0.,0.));
#2561=DIRECTION('center_axis',(1.,0.,0.));
#2562=DIRECTION('ref_axis',(0.,-0.999847695156391,-0.0174524064372843));
#2563=DIRECTION('center_axis',(0.,-0.369746757273829,-0.929132571534056));
#2564=DIRECTION('ref_axis',(1.,0.,0.));
#2565=DIRECTION('center_axis',(0.,0.,1.));
#2566=DIRECTION('ref_axis',(1.,0.,0.));
#2567=DIRECTION('center_axis',(-1.,-6.69410167923576E-18,-6.36901230698721E-17));
#2568=DIRECTION('ref_axis',(0.,0.994521895368273,-0.104528463267654));
#2569=CARTESIAN_POINT('',(0.,0.,0.));
#2570=CARTESIAN_POINT('Origin',(47.25,89.8642099157646,10.7886544851366));
#2571=CARTESIAN_POINT('',(-2.31266717166107E-15,90.131418567678,-4.5197189303885));
#2572=CARTESIAN_POINT('',(94.5,90.131418567678,-4.5197189303885));
#2573=CARTESIAN_POINT('',(23.625,90.131418567678,-4.5197189303885));
#2574=CARTESIAN_POINT('',(94.5,90.8734459101469,-47.0304369094076));
#2575=CARTESIAN_POINT('',(94.5,88.6799389089045,78.6354950285698));
#2576=CARTESIAN_POINT('',(79.5,90.8734459101469,-47.0304369094076));
#2577=CARTESIAN_POINT('',(23.625,90.8734459101469,-47.0304369094076));
#2578=CARTESIAN_POINT('',(79.5,90.3501342049526,-17.0499293978968));
#2579=CARTESIAN_POINT('',(79.5,91.0499757030877,-57.1438219736681));
#2580=CARTESIAN_POINT('',(76.5,90.2977769856407,-14.0503863124276));
#2581=CARTESIAN_POINT('Origin',(76.5,90.3501342049526,-17.0499293978968));
#2582=CARTESIAN_POINT('',(57.75,90.2977769856407,-14.0503863124276));
#2583=CARTESIAN_POINT('',(57.75,90.2977769856407,-14.0503863124276));
#2584=CARTESIAN_POINT('',(54.75,90.3501342049526,-17.0499293978968));
#2585=CARTESIAN_POINT('Origin',(57.75,90.3501342049526,-17.0499293978968));
#2586=CARTESIAN_POINT('',(54.75,90.8734459101469,-47.0304369094076));
#2587=CARTESIAN_POINT('',(54.75,91.0499757030877,-57.1438219736681));
#2588=CARTESIAN_POINT('',(39.75,90.8734459101469,-47.0304369094076));
#2589=CARTESIAN_POINT('',(23.625,90.8734459101469,-47.0304369094076));
#2590=CARTESIAN_POINT('',(39.75,90.3501342049526,-17.0499293978968));
#2591=CARTESIAN_POINT('',(39.75,91.0499757030877,-57.1438219736681));
#2592=CARTESIAN_POINT('',(36.75,90.2977769856407,-14.0503863124277));
#2593=CARTESIAN_POINT('Origin',(36.75,90.3501342049526,-17.0499293978968));
#2594=CARTESIAN_POINT('',(18.,90.2977769856407,-14.0503863124276));
#2595=CARTESIAN_POINT('',(18.,90.2977769856407,-14.0503863124276));
#2596=CARTESIAN_POINT('',(15.,90.3501342049526,-17.0499293978968));
#2597=CARTESIAN_POINT('Origin',(18.,90.3501342049526,-17.0499293978968));
#2598=CARTESIAN_POINT('',(15.,90.8734459101469,-47.0304369094076));
#2599=CARTESIAN_POINT('',(15.,91.0499757030877,-57.1438219736681));
#2600=CARTESIAN_POINT('',(-4.44452109962746E-16,90.8734459101469,-47.0304369094076));
#2601=CARTESIAN_POINT('',(23.625,90.8734459101469,-47.0304369094076));
#2602=CARTESIAN_POINT('',(-3.33066907387547E-15,89.7270832951415,18.6446333191864));
#2603=CARTESIAN_POINT('Origin',(0.,87.1318754822088,-4.57207614970035));
#2604=CARTESIAN_POINT('',(-2.31266717166107E-15,87.1318754822088,-4.57207614970036));
#2605=CARTESIAN_POINT('',(-2.31266717166107E-15,90.131418567678,-4.5197189303885));
#2606=CARTESIAN_POINT('',(-2.51196956068097E-15,85.5321191699586,-3.));
#2607=CARTESIAN_POINT('Origin',(0.,85.5321191699586,-4.6));
#2608=CARTESIAN_POINT('',(-2.51196956068097E-15,85.5321191699586,0.));
#2609=CARTESIAN_POINT('',(-2.51196956068097E-15,85.5321191699586,0.));
#2610=CARTESIAN_POINT('Origin',(0.,85.5321191699586,-4.6));
#2611=CARTESIAN_POINT('Origin',(47.25,85.5321191699586,-4.6));
#2612=CARTESIAN_POINT('',(94.5,87.1318754822088,-4.57207614970036));
#2613=CARTESIAN_POINT('',(23.625,87.1318754822088,-4.57207614970036));
#2614=CARTESIAN_POINT('',(94.5,85.5321191699586,-3.));
#2615=CARTESIAN_POINT('Origin',(94.5,85.5321191699586,-4.6));
#2616=CARTESIAN_POINT('',(23.625,85.5321191699586,-3.));
#2617=CARTESIAN_POINT('Origin',(94.5,91.0499757030877,-57.1438219736681));
#2618=CARTESIAN_POINT('',(94.5,90.131418567678,-4.5197189303885));
#2619=CARTESIAN_POINT('',(94.5,87.8739028246778,-47.0827941287195));
#2620=CARTESIAN_POINT('',(94.5,85.6803958234353,78.583137809258));
#2621=CARTESIAN_POINT('',(94.5,90.8734459101469,-47.0304369094076));
#2622=CARTESIAN_POINT('Origin',(-3.33066907387547E-15,89.7270832951415,
18.6446333191864));
#2623=CARTESIAN_POINT('',(-4.44452109962746E-16,87.8739028246778,-47.0827941287195));
#2624=CARTESIAN_POINT('',(-4.44452109962746E-16,90.8734459101469,-47.0304369094076));
#2625=CARTESIAN_POINT('',(-3.33066907387547E-15,86.7275402096724,18.5922760998745));
#2626=CARTESIAN_POINT('Origin',(47.25,74.7571755199385,-3.));
#2627=CARTESIAN_POINT('',(94.5,43.3404717946459,-3.));
#2628=CARTESIAN_POINT('',(94.5,6.90000000000001,-3.));
#2629=CARTESIAN_POINT('',(79.5,43.3404717946459,-3.));
#2630=CARTESIAN_POINT('',(23.625,43.3404717946459,-3.));
#2631=CARTESIAN_POINT('',(79.5,43.8,-3.));
#2632=CARTESIAN_POINT('',(79.5,6.90000000000001,-3.));
#2633=CARTESIAN_POINT('',(76.5,46.8,-3.));
#2634=CARTESIAN_POINT('Origin',(76.5,43.8,-3.));
#2635=CARTESIAN_POINT('',(72.3,46.8,-3.));
#2636=CARTESIAN_POINT('',(72.3,46.8,-3.));
#2637=CARTESIAN_POINT('',(64.3,54.8,-3.));
#2638=CARTESIAN_POINT('Origin',(72.3,54.8,-3.));
#2639=CARTESIAN_POINT('',(64.3,56.7,-3.));
#2640=CARTESIAN_POINT('',(64.3,56.7,-3.));
#2641=CARTESIAN_POINT('',(40.7,56.7,-3.));
#2642=CARTESIAN_POINT('Origin',(52.5,56.7,-3.));
#2643=CARTESIAN_POINT('',(40.7,54.8,-3.));
#2644=CARTESIAN_POINT('',(40.7,56.7,-3.));
#2645=CARTESIAN_POINT('',(32.7,46.8,-3.));
#2646=CARTESIAN_POINT('Origin',(32.7,54.8,-3.));
#2647=CARTESIAN_POINT('',(33.2,46.8,-3.));
#2648=CARTESIAN_POINT('',(33.2,46.8,-3.));
#2649=CARTESIAN_POINT('',(30.2,43.8,-3.));
#2650=CARTESIAN_POINT('Origin',(33.2,43.8,-3.));
#2651=CARTESIAN_POINT('',(30.2,43.3404717946459,-3.));
#2652=CARTESIAN_POINT('',(30.2,21.6716984733155,-3.));
#2653=CARTESIAN_POINT('',(17.2004890655704,43.3404717946459,-3.));
#2654=CARTESIAN_POINT('',(23.625,43.3404717946459,-3.));
#2655=CARTESIAN_POINT('',(17.2004890655704,54.1,-3.));
#2656=CARTESIAN_POINT('',(17.2004890655704,6.90000000000001,-3.));
#2657=CARTESIAN_POINT('',(10.8004890655704,60.5,-3.));
#2658=CARTESIAN_POINT('Origin',(10.8004890655704,54.1,-3.));
#2659=CARTESIAN_POINT('',(6.4,60.5,-3.));
#2660=CARTESIAN_POINT('',(10.8004890655704,60.5,-3.));
#2661=CARTESIAN_POINT('',(0.,66.9,-3.));
#2662=CARTESIAN_POINT('Origin',(6.39999999999998,66.9,-3.));
#2663=CARTESIAN_POINT('',(-3.33066907387547E-15,66.9,-3.));
#2664=CARTESIAN_POINT('',(67.45,54.9,-3.));
#2665=CARTESIAN_POINT('Origin',(72.7,54.9,-3.));
#2666=CARTESIAN_POINT('',(5.95000000000001,73.5,-3.));
#2667=CARTESIAN_POINT('Origin',(11.2,73.5,-3.));
#2668=CARTESIAN_POINT('Origin',(94.5,142.7,0.));
#2669=CARTESIAN_POINT('',(94.5,85.5321191699586,0.));
#2670=CARTESIAN_POINT('',(94.5,85.5321191699586,0.));
#2671=CARTESIAN_POINT('',(94.5,43.3404717946459,0.));
#2672=CARTESIAN_POINT('',(94.5,6.90000000000001,0.));
#2673=CARTESIAN_POINT('',(94.5,43.3404717946459,0.));
#2674=CARTESIAN_POINT('Origin',(47.25,85.5321191699586,-4.6));
#2675=CARTESIAN_POINT('',(23.625,85.5321191699586,0.));
#2676=CARTESIAN_POINT('Origin',(94.5,85.5321191699586,-4.6));
#2677=CARTESIAN_POINT('Origin',(47.25,34.9650365372203,-25.8392707174962));
#2678=CARTESIAN_POINT('',(-2.93743674220736E-16,91.881607410759,-48.4891251735625));
#2679=CARTESIAN_POINT('',(15.,91.881607410759,-48.4891251735625));
#2680=CARTESIAN_POINT('',(23.625,91.881607410759,-48.4891251735625));
#2681=CARTESIAN_POINT('',(15.,98.0929277636669,-50.9609097490238));
#2682=CARTESIAN_POINT('',(15.,98.0929277636669,-50.9609097490238));
#2683=CARTESIAN_POINT('',(8.88178419700125E-15,98.0929277636669,-50.9609097490238));
#2684=CARTESIAN_POINT('Origin',(7.50000000000001,98.0929277636669,-50.9609097490238));
#2685=CARTESIAN_POINT('',(-3.33066907387547E-15,27.6646788413854,-22.9341055476676));
#2686=CARTESIAN_POINT('',(3.00000000000002,98.0929277636669,-50.9609097490238));
#2687=CARTESIAN_POINT('Origin',(7.50000000000001,98.0929277636669,-50.9609097490238));
#2688=CARTESIAN_POINT('Origin',(0.,90.7723671389376,-51.2765228881646));
#2689=CARTESIAN_POINT('',(-2.93743674220736E-16,90.7723671389376,-51.2765228881646));
#2690=CARTESIAN_POINT('',(-2.93743674220736E-16,91.881607410759,-48.4891251735625));
#2691=CARTESIAN_POINT('Origin',(0.,92.4732022223972,-47.002513059108));
#2692=CARTESIAN_POINT('Origin',(0.,92.4732022223972,-47.002513059108));
#2693=CARTESIAN_POINT('Origin',(46.5460223653339,92.4732022223972,-47.002513059108));
#2694=CARTESIAN_POINT('',(15.,90.7723671389376,-51.2765228881646));
#2695=CARTESIAN_POINT('',(23.625,90.7723671389376,-51.2765228881646));
#2696=CARTESIAN_POINT('',(15.,87.8739028246778,-47.0827941287195));
#2697=CARTESIAN_POINT('Origin',(15.,92.4732022223972,-47.002513059108));
#2698=CARTESIAN_POINT('',(23.625,87.8739028246778,-47.0827941287195));
#2699=CARTESIAN_POINT('Origin',(15.,98.0929277636669,-50.9609097490238));
#2700=CARTESIAN_POINT('',(15.,91.881607410759,-48.4891251735625));
#2701=CARTESIAN_POINT('',(15.,96.9836874918454,-53.748307463626));
#2702=CARTESIAN_POINT('',(15.,96.9836874918454,-53.748307463626));
#2703=CARTESIAN_POINT('',(15.,98.0929277636669,-50.9609097490238));
#2704=CARTESIAN_POINT('Origin',(47.25,34.9650365372203,-25.8392707174962));
#2705=CARTESIAN_POINT('',(39.75,91.881607410759,-48.4891251735625));
#2706=CARTESIAN_POINT('',(54.75,91.881607410759,-48.4891251735625));
#2707=CARTESIAN_POINT('',(23.625,91.881607410759,-48.4891251735625));
#2708=CARTESIAN_POINT('',(54.75,98.0929277636669,-50.9609097490238));
#2709=CARTESIAN_POINT('',(54.75,98.0929277636669,-50.9609097490238));
#2710=CARTESIAN_POINT('',(39.75,98.0929277636669,-50.9609097490238));
#2711=CARTESIAN_POINT('Origin',(47.25,98.0929277636669,-50.9609097490238));
#2712=CARTESIAN_POINT('',(39.75,98.0929277636669,-50.9609097490238));
#2713=CARTESIAN_POINT('',(42.75,98.0929277636669,-50.9609097490238));
#2714=CARTESIAN_POINT('Origin',(47.25,98.0929277636669,-50.9609097490238));
#2715=CARTESIAN_POINT('Origin',(39.75,90.7723671389376,-51.2765228881646));
#2716=CARTESIAN_POINT('',(39.75,90.7723671389376,-51.2765228881646));
#2717=CARTESIAN_POINT('',(39.75,91.881607410759,-48.4891251735625));
#2718=CARTESIAN_POINT('',(39.75,87.8739028246778,-47.0827941287195));
#2719=CARTESIAN_POINT('Origin',(39.75,92.4732022223972,-47.002513059108));
#2720=CARTESIAN_POINT('',(39.75,90.8734459101469,-47.0304369094076));
#2721=CARTESIAN_POINT('Origin',(39.75,92.4732022223972,-47.002513059108));
#2722=CARTESIAN_POINT('Origin',(46.5460223653339,92.4732022223972,-47.002513059108));
#2723=CARTESIAN_POINT('',(54.75,90.7723671389376,-51.2765228881646));
#2724=CARTESIAN_POINT('',(23.625,90.7723671389376,-51.2765228881646));
#2725=CARTESIAN_POINT('',(54.75,87.8739028246778,-47.0827941287195));
#2726=CARTESIAN_POINT('Origin',(54.75,92.4732022223972,-47.002513059108));
#2727=CARTESIAN_POINT('',(23.625,87.8739028246778,-47.0827941287195));
#2728=CARTESIAN_POINT('Origin',(54.75,98.0929277636669,-50.9609097490238));
#2729=CARTESIAN_POINT('',(54.75,91.881607410759,-48.4891251735625));
#2730=CARTESIAN_POINT('',(54.75,96.9836874918454,-53.748307463626));
#2731=CARTESIAN_POINT('',(54.75,96.9836874918454,-53.748307463626));
#2732=CARTESIAN_POINT('',(54.75,98.0929277636669,-50.9609097490238));
#2733=CARTESIAN_POINT('Origin',(47.25,34.9650365372203,-25.8392707174962));
#2734=CARTESIAN_POINT('',(79.5,91.881607410759,-48.4891251735625));
#2735=CARTESIAN_POINT('',(94.5,91.881607410759,-48.4891251735625));
#2736=CARTESIAN_POINT('',(23.625,91.881607410759,-48.4891251735625));
#2737=CARTESIAN_POINT('',(94.5,98.0929277636669,-50.9609097490238));
#2738=CARTESIAN_POINT('',(94.5,-28.0832754506579,-0.749300111237847));
#2739=CARTESIAN_POINT('',(79.5,98.0929277636669,-50.9609097490238));
#2740=CARTESIAN_POINT('Origin',(87.,98.0929277636669,-50.9609097490238));
#2741=CARTESIAN_POINT('',(79.5,98.0929277636669,-50.9609097490238));
#2742=CARTESIAN_POINT('',(82.5,98.0929277636669,-50.9609097490238));
#2743=CARTESIAN_POINT('Origin',(87.,98.0929277636669,-50.9609097490238));
#2744=CARTESIAN_POINT('Origin',(79.5,90.7723671389376,-51.2765228881646));
#2745=CARTESIAN_POINT('',(79.5,90.7723671389376,-51.2765228881646));
#2746=CARTESIAN_POINT('',(79.5,91.881607410759,-48.4891251735625));
#2747=CARTESIAN_POINT('',(79.5,87.8739028246778,-47.0827941287195));
#2748=CARTESIAN_POINT('Origin',(79.5,92.4732022223972,-47.002513059108));
#2749=CARTESIAN_POINT('',(79.5,90.8734459101469,-47.0304369094076));
#2750=CARTESIAN_POINT('Origin',(79.5,92.4732022223972,-47.002513059108));
#2751=CARTESIAN_POINT('Origin',(46.5460223653339,92.4732022223972,-47.002513059108));
#2752=CARTESIAN_POINT('',(94.5,90.7723671389376,-51.2765228881646));
#2753=CARTESIAN_POINT('',(23.625,90.7723671389376,-51.2765228881646));
#2754=CARTESIAN_POINT('Origin',(94.5,92.4732022223972,-47.002513059108));
#2755=CARTESIAN_POINT('',(23.625,87.8739028246778,-47.0827941287195));
#2756=CARTESIAN_POINT('Origin',(94.5,98.0929277636669,-50.9609097490238));
#2757=CARTESIAN_POINT('',(94.5,91.881607410759,-48.4891251735625));
#2758=CARTESIAN_POINT('',(94.5,96.9836874918454,-53.748307463626));
#2759=CARTESIAN_POINT('',(94.5,-29.1925157224794,-3.53669782584003));
#2760=CARTESIAN_POINT('',(94.5,98.0929277636669,-50.9609097490238));
#2761=CARTESIAN_POINT('Origin',(-3.33066907387547E-15,27.6646788413854,
-22.9341055476676));
#2762=CARTESIAN_POINT('',(1.77635683940025E-14,96.9836874918454,-53.748307463626));
#2763=CARTESIAN_POINT('',(8.88178419700125E-15,98.0929277636669,-50.9609097490238));
#2764=CARTESIAN_POINT('',(-3.33066907387547E-15,26.555438569564,-25.7215032622698));
#2765=CARTESIAN_POINT('Origin',(47.25,86.8646668302954,10.7362972658248));
#2766=CARTESIAN_POINT('',(15.,87.3505911194834,-17.1022866172087));
#2767=CARTESIAN_POINT('',(15.,88.0504326176185,-57.19617919298));
#2768=CARTESIAN_POINT('',(18.,87.2982339001715,-14.1027435317395));
#2769=CARTESIAN_POINT('Origin',(18.,87.3505911194834,-17.1022866172087));
#2770=CARTESIAN_POINT('',(36.75,87.2982339001716,-14.1027435317395));
#2771=CARTESIAN_POINT('',(18.,87.2982339001715,-14.1027435317395));
#2772=CARTESIAN_POINT('',(39.75,87.3505911194834,-17.1022866172087));
#2773=CARTESIAN_POINT('Origin',(36.75,87.3505911194834,-17.1022866172087));
#2774=CARTESIAN_POINT('',(39.75,88.0504326176185,-57.19617919298));
#2775=CARTESIAN_POINT('',(54.75,87.3505911194834,-17.1022866172087));
#2776=CARTESIAN_POINT('',(54.75,88.0504326176185,-57.19617919298));
#2777=CARTESIAN_POINT('',(57.75,87.2982339001715,-14.1027435317395));
#2778=CARTESIAN_POINT('Origin',(57.75,87.3505911194834,-17.1022866172087));
#2779=CARTESIAN_POINT('',(76.5,87.2982339001715,-14.1027435317395));
#2780=CARTESIAN_POINT('',(57.75,87.2982339001715,-14.1027435317395));
#2781=CARTESIAN_POINT('',(79.5,87.3505911194834,-17.1022866172087));
#2782=CARTESIAN_POINT('Origin',(76.5,87.3505911194834,-17.1022866172087));
#2783=CARTESIAN_POINT('',(79.5,88.0504326176185,-57.19617919298));
#2784=CARTESIAN_POINT('Origin',(15.,91.0499757030877,-57.1438219736681));
#2785=CARTESIAN_POINT('',(15.,90.8734459101469,-47.0304369094076));
#2786=CARTESIAN_POINT('',(15.,90.3501342049526,-17.0499293978968));
#2787=CARTESIAN_POINT('Origin',(46.5460223653339,92.4732022223972,-47.002513059108));
#2788=CARTESIAN_POINT('Origin',(15.,92.4732022223972,-47.002513059108));
#2789=CARTESIAN_POINT('Origin',(39.75,60.8347116451513,-36.1340647823433));
#2790=CARTESIAN_POINT('',(39.75,96.9836874918454,-53.748307463626));
#2791=CARTESIAN_POINT('',(39.75,98.0929277636669,-50.9609097490238));
#2792=CARTESIAN_POINT('',(39.75,96.9836874918454,-53.748307463626));
#2793=CARTESIAN_POINT('Origin',(47.25,33.8557962653989,-28.6266684320983));
#2794=CARTESIAN_POINT('Origin',(7.50000000000001,96.9836874918454,-53.748307463626));
#2795=CARTESIAN_POINT('',(3.00000000000002,96.9836874918454,-53.748307463626));
#2796=CARTESIAN_POINT('Origin',(7.50000000000001,96.9836874918454,-53.748307463626));
#2797=CARTESIAN_POINT('Origin',(54.75,91.0499757030877,-57.1438219736681));
#2798=CARTESIAN_POINT('',(54.75,90.8734459101469,-47.0304369094076));
#2799=CARTESIAN_POINT('',(54.75,90.3501342049526,-17.0499293978968));
#2800=CARTESIAN_POINT('Origin',(46.5460223653339,92.4732022223972,-47.002513059108));
#2801=CARTESIAN_POINT('Origin',(54.75,92.4732022223972,-47.002513059108));
#2802=CARTESIAN_POINT('Origin',(79.5,60.8347116451513,-36.1340647823433));
#2803=CARTESIAN_POINT('',(79.5,96.9836874918454,-53.748307463626));
#2804=CARTESIAN_POINT('',(79.5,98.0929277636669,-50.9609097490238));
#2805=CARTESIAN_POINT('',(79.5,96.9836874918454,-53.748307463626));
#2806=CARTESIAN_POINT('Origin',(47.25,33.8557962653989,-28.6266684320983));
#2807=CARTESIAN_POINT('Origin',(47.25,96.9836874918454,-53.748307463626));
#2808=CARTESIAN_POINT('',(42.75,96.9836874918454,-53.748307463626));
#2809=CARTESIAN_POINT('Origin',(47.25,96.9836874918454,-53.748307463626));
#2810=CARTESIAN_POINT('Origin',(94.5,90.131418567678,-4.51971893038849));
#2811=CARTESIAN_POINT('Origin',(46.5460223653339,92.4732022223972,-47.002513059108));
#2812=CARTESIAN_POINT('Origin',(94.5,92.4732022223972,-47.002513059108));
#2813=CARTESIAN_POINT('Origin',(47.25,42.497091292016,31.3829227985198));
#2814=CARTESIAN_POINT('',(94.5,38.7656710759518,-4.11916906896879));
#2815=CARTESIAN_POINT('',(79.5,38.7656710759518,-4.11916906896879));
#2816=CARTESIAN_POINT('',(23.625,38.7656710759518,-4.11916906896878));
#2817=CARTESIAN_POINT('',(79.5,36.4685742941501,-25.9745850376523));
#2818=CARTESIAN_POINT('',(79.5,35.4040850132334,-36.1025240139071));
#2819=CARTESIAN_POINT('',(94.5,36.4685742941501,-25.9745850376523));
#2820=CARTESIAN_POINT('',(23.625,36.4685742941501,-25.9745850376523));
#2821=CARTESIAN_POINT('',(94.5,35.4040850132334,-36.1025240139071));
#2822=CARTESIAN_POINT('Origin',(94.5,41.7492367620566,-4.43275445877175));
#2823=CARTESIAN_POINT('',(94.5,41.7492367620566,-4.43275445877175));
#2824=CARTESIAN_POINT('',(94.5,38.7656710759518,-4.11916906896879));
#2825=CARTESIAN_POINT('Origin',(94.5,43.3404717946459,-4.6));
#2826=CARTESIAN_POINT('Origin',(94.5,43.3404717946459,-4.6));
#2827=CARTESIAN_POINT('Origin',(54.3407821347177,43.3404717946459,-4.6));
#2828=CARTESIAN_POINT('',(79.5,41.7492367620566,-4.43275445877175));
#2829=CARTESIAN_POINT('',(23.625,41.7492367620566,-4.43275445877175));
#2830=CARTESIAN_POINT('Origin',(79.5,43.3404717946459,-4.6));
#2831=CARTESIAN_POINT('Origin',(79.5,35.4040850132334,-36.1025240139071));
#2832=CARTESIAN_POINT('',(79.5,38.7656710759518,-4.11916906896879));
#2833=CARTESIAN_POINT('',(79.5,39.4521399802549,-26.2881704274552));
#2834=CARTESIAN_POINT('',(79.5,38.3876506993382,-36.41610940371));
#2835=CARTESIAN_POINT('',(79.5,36.4685742941501,-25.9745850376523));
#2836=CARTESIAN_POINT('Origin',(47.25,42.497091292016,31.3829227985198));
#2837=CARTESIAN_POINT('',(30.2,38.7656710759518,-4.11916906896879));
#2838=CARTESIAN_POINT('',(17.2004890655704,38.7656710759518,-4.11916906896879));
#2839=CARTESIAN_POINT('',(23.625,38.7656710759518,-4.11916906896878));
#2840=CARTESIAN_POINT('',(17.2004890655704,36.4685742941501,-25.9745850376523));
#2841=CARTESIAN_POINT('',(17.2004890655704,35.4040850132334,-36.1025240139071));
#2842=CARTESIAN_POINT('',(32.1995109344297,36.4685742941501,-25.9745850376523));
#2843=CARTESIAN_POINT('',(23.625,36.4685742941501,-25.9745850376523));
#2844=CARTESIAN_POINT('',(32.1995109344297,36.4807281848902,-25.8589484916138));
#2845=CARTESIAN_POINT('',(32.1995109344297,35.4040850132334,-36.1025240139071));
#2846=CARTESIAN_POINT('',(31.1997554672148,36.7144380696962,-23.6353474710153));
#2847=CARTESIAN_POINT('Origin',(29.1995109344297,36.4807281848902,-25.8589484916138));
#2848=CARTESIAN_POINT('',(30.2,36.9481479545022,-21.4117464504167));
#2849=CARTESIAN_POINT('Origin',(33.2,36.9481479545022,-21.4117464504167));
#2850=CARTESIAN_POINT('',(30.2,36.9481479545022,-21.4117464504167));
#2851=CARTESIAN_POINT('Origin',(30.2,41.7492367620566,-4.43275445877175));
#2852=CARTESIAN_POINT('',(30.2,41.7492367620566,-4.43275445877175));
#2853=CARTESIAN_POINT('',(30.2,38.7656710759518,-4.11916906896878));
#2854=CARTESIAN_POINT('Origin',(30.2,43.3404717946459,-4.6));
#2855=CARTESIAN_POINT('',(30.2,43.3404717946459,0.));
#2856=CARTESIAN_POINT('',(30.2,43.3404717946459,0.));
#2857=CARTESIAN_POINT('Origin',(30.2,43.3404717946459,-4.6));
#2858=CARTESIAN_POINT('Origin',(54.3407821347177,43.3404717946459,-4.6));
#2859=CARTESIAN_POINT('',(17.2004890655704,41.7492367620566,-4.43275445877175));
#2860=CARTESIAN_POINT('',(23.625,41.7492367620566,-4.43275445877175));
#2861=CARTESIAN_POINT('Origin',(17.2004890655704,43.3404717946459,-4.6));
#2862=CARTESIAN_POINT('Origin',(17.2004890655704,35.4040850132334,-36.1025240139071));
#2863=CARTESIAN_POINT('',(17.2004890655704,38.7656710759518,-4.11916906896879));
#2864=CARTESIAN_POINT('',(17.2004890655704,39.4521399802549,-26.2881704274552));
#2865=CARTESIAN_POINT('',(17.2004890655704,38.3876506993382,-36.41610940371));
#2866=CARTESIAN_POINT('',(17.2004890655704,36.4685742941501,-25.9745850376523));
#2867=CARTESIAN_POINT('Origin',(94.5,49.5990503249808,98.9535493771045));
#2868=CARTESIAN_POINT('',(94.5,39.4521399802549,-26.2881704274552));
#2869=CARTESIAN_POINT('',(94.5,36.4685742941501,-25.9745850376523));
#2870=CARTESIAN_POINT('',(94.5,38.3876506993382,-36.41610940371));
#2871=CARTESIAN_POINT('Origin',(47.25,33.8557962653989,-28.6266684320983));
#2872=CARTESIAN_POINT('Origin',(87.,96.9836874918454,-53.748307463626));
#2873=CARTESIAN_POINT('',(82.5,96.9836874918454,-53.748307463626));
#2874=CARTESIAN_POINT('Origin',(87.,96.9836874918454,-53.748307463626));
#2875=CARTESIAN_POINT('Origin',(79.5,6.90000000000001,0.));
#2876=CARTESIAN_POINT('',(79.5,43.3404717946459,0.));
#2877=CARTESIAN_POINT('',(79.5,43.3404717946459,0.));
#2878=CARTESIAN_POINT('',(79.5,43.8,0.));
#2879=CARTESIAN_POINT('',(79.5,6.90000000000001,0.));
#2880=CARTESIAN_POINT('',(79.5,43.8,0.));
#2881=CARTESIAN_POINT('Origin',(54.3407821347177,43.3404717946459,-4.6));
#2882=CARTESIAN_POINT('',(23.625,43.3404717946459,0.));
#2883=CARTESIAN_POINT('Origin',(79.5,43.3404717946459,-4.6));
#2884=CARTESIAN_POINT('Origin',(30.2,39.2611853078098,0.595333925182242));
#2885=CARTESIAN_POINT('',(30.2,39.931713640607,-21.7253318402197));
#2886=CARTESIAN_POINT('',(30.2,36.9481479545022,-21.4117464504167));
#2887=CARTESIAN_POINT('',(30.2,39.931713640607,-21.7253318402197));
#2888=CARTESIAN_POINT('Origin',(47.25,45.4806569781208,31.0693374087168));
#2889=CARTESIAN_POINT('',(23.625,39.4521399802549,-26.2881704274552));
#2890=CARTESIAN_POINT('Origin',(17.2004890655704,6.90000000000001,0.));
#2891=CARTESIAN_POINT('',(17.2004890655704,43.3404717946459,0.));
#2892=CARTESIAN_POINT('',(17.2004890655704,43.3404717946459,0.));
#2893=CARTESIAN_POINT('',(17.2004890655704,54.1,0.));
#2894=CARTESIAN_POINT('',(17.2004890655704,6.90000000000001,0.));
#2895=CARTESIAN_POINT('',(17.2004890655704,54.1,0.));
#2896=CARTESIAN_POINT('Origin',(54.3407821347177,43.3404717946459,-4.6));
#2897=CARTESIAN_POINT('',(23.625,43.3404717946459,0.));
#2898=CARTESIAN_POINT('Origin',(17.2004890655704,43.3404717946459,-4.6));
#2899=CARTESIAN_POINT('Origin',(47.25,94.6861997934244,-12.544297554648));
#2900=CARTESIAN_POINT('',(94.5,35.2644142945203,-27.3598126584656));
#2901=CARTESIAN_POINT('',(79.5,35.2644142945203,-27.3598126584656));
#2902=CARTESIAN_POINT('',(23.625,35.2644142945203,-27.3598126584656));
#2903=CARTESIAN_POINT('',(79.5,28.8446723892679,-28.9604340864709));
#2904=CARTESIAN_POINT('',(79.5,28.8446723892679,-28.9604340864709));
#2905=CARTESIAN_POINT('',(94.5,28.8446723892679,-28.9604340864709));
#2906=CARTESIAN_POINT('Origin',(87.,28.8446723892679,-28.9604340864709));
#2907=CARTESIAN_POINT('',(94.5,28.8446723892679,-28.9604340864709));
#2908=CARTESIAN_POINT('',(82.5,28.8446723892679,-28.9604340864709));
#2909=CARTESIAN_POINT('Origin',(87.,28.8446723892679,-28.9604340864709));
#2910=CARTESIAN_POINT('Origin',(94.5,35.9901799813193,-30.2706998372936));
#2911=CARTESIAN_POINT('',(94.5,35.9901799813193,-30.2706998372936));
#2912=CARTESIAN_POINT('',(94.5,35.2644142945203,-27.3598126584656));
#2913=CARTESIAN_POINT('Origin',(94.5,34.8773392615608,-25.807339496424));
#2914=CARTESIAN_POINT('Origin',(94.5,34.8773392615608,-25.807339496424));
#2915=CARTESIAN_POINT('Origin',(58.3334532468855,34.8773392615608,-25.807339496424));
#2916=CARTESIAN_POINT('',(79.5,35.9901799813193,-30.2706998372936));
#2917=CARTESIAN_POINT('',(23.625,35.9901799813193,-30.2706998372936));
#2918=CARTESIAN_POINT('Origin',(79.5,34.8773392615608,-25.807339496424));
#2919=CARTESIAN_POINT('Origin',(79.5,28.8446723892679,-28.9604340864709));
#2920=CARTESIAN_POINT('',(79.5,35.2644142945203,-27.3598126584656));
#2921=CARTESIAN_POINT('',(79.5,29.5704380760669,-31.8713212652989));
#2922=CARTESIAN_POINT('',(79.5,29.5704380760669,-31.8713212652989));
#2923=CARTESIAN_POINT('',(79.5,28.8446723892679,-28.9604340864709));
#2924=CARTESIAN_POINT('Origin',(17.2004890655704,28.8446723892679,-28.9604340864709));
#2925=CARTESIAN_POINT('',(17.2004890655704,35.2644142945203,-27.3598126584656));
#2926=CARTESIAN_POINT('',(17.2004890655704,35.9901799813193,-30.2706998372936));
#2927=CARTESIAN_POINT('',(17.2004890655704,35.2644142945203,-27.3598126584656));
#2928=CARTESIAN_POINT('',(17.2004890655704,29.5704380760669,-31.8713212652989));
#2929=CARTESIAN_POINT('',(17.2004890655704,29.5704380760669,-31.8713212652989));
#2930=CARTESIAN_POINT('',(17.2004890655704,28.8446723892679,-28.9604340864709));
#2931=CARTESIAN_POINT('',(17.2004890655704,28.8446723892679,-28.9604340864709));
#2932=CARTESIAN_POINT('',(17.2004890655704,28.8446723892679,-28.9604340864709));
#2933=CARTESIAN_POINT('Origin',(58.3334532468855,34.8773392615608,-25.807339496424));
#2934=CARTESIAN_POINT('',(32.1995109344297,35.2644142945203,-27.3598126584656));
#2935=CARTESIAN_POINT('',(23.625,35.2644142945203,-27.3598126584656));
#2936=CARTESIAN_POINT('Origin',(32.1995109344297,34.8773392615608,-25.807339496424));
#2937=CARTESIAN_POINT('Origin',(17.2004890655704,34.8773392615608,-25.807339496424));
#2938=CARTESIAN_POINT('Origin',(32.1995109344297,35.9901799813193,-30.2706998372936));
#2939=CARTESIAN_POINT('',(32.1995109344297,35.9901799813193,-30.2706998372936));
#2940=CARTESIAN_POINT('',(32.1995109344297,35.2644142945203,-27.3598126584656));
#2941=CARTESIAN_POINT('',(32.1995109344297,39.4521399802549,-26.2881704274552));
#2942=CARTESIAN_POINT('Origin',(32.1995109344297,34.8773392615608,-25.807339496424));
#2943=CARTESIAN_POINT('',(32.1995109344297,36.4685742941501,-25.9745850376523));
#2944=CARTESIAN_POINT('Origin',(47.25,95.4119654802234,-15.455184733476));
#2945=CARTESIAN_POINT('',(23.625,35.9901799813193,-30.2706998372936));
#2946=CARTESIAN_POINT('',(32.1995109344297,29.5704380760669,-31.8713212652989));
#2947=CARTESIAN_POINT('',(32.1995109344297,29.5704380760669,-31.8713212652989));
#2948=CARTESIAN_POINT('Origin',(24.7,29.4873332560996,-31.892041624088));
#2949=CARTESIAN_POINT('',(20.2,29.4873332560996,-31.892041624088));
#2950=CARTESIAN_POINT('Origin',(24.7,29.4873332560996,-31.892041624088));
#2951=CARTESIAN_POINT('Origin',(94.5,160.610832017548,3.89255933596398));
#2952=CARTESIAN_POINT('',(94.5,29.5704380760669,-31.8713212652989));
#2953=CARTESIAN_POINT('',(94.5,28.8446723892679,-28.9604340864709));
#2954=CARTESIAN_POINT('',(94.5,29.5704380760669,-31.8713212652989));
#2955=CARTESIAN_POINT('Origin',(47.25,45.4806569781208,31.0693374087168));
#2956=CARTESIAN_POINT('',(31.1997554672148,39.698003755801,-23.9489328608182));
#2957=CARTESIAN_POINT('Origin',(33.2,39.931713640607,-21.7253318402197));
#2958=CARTESIAN_POINT('',(32.1995109344297,39.464293870995,-26.1725338814168));
#2959=CARTESIAN_POINT('Origin',(29.1995109344297,39.464293870995,-26.1725338814168));
#2960=CARTESIAN_POINT('',(32.1995109344297,38.3876506993382,-36.41610940371));
#2961=CARTESIAN_POINT('',(23.625,39.4521399802549,-26.2881704274552));
#2962=CARTESIAN_POINT('Origin',(79.5,38.7656710759518,-4.11916906896879));
#2963=CARTESIAN_POINT('Origin',(58.3334532468855,34.8773392615608,-25.807339496424));
#2964=CARTESIAN_POINT('Origin',(79.5,34.8773392615608,-25.807339496424));
#2965=CARTESIAN_POINT('Origin',(47.25,94.6861997934244,-12.544297554648));
#2966=CARTESIAN_POINT('',(32.1995109344297,28.8446723892679,-28.9604340864709));
#2967=CARTESIAN_POINT('Origin',(24.7,28.7615675693006,-28.98115444526));
#2968=CARTESIAN_POINT('',(32.1995109344297,28.8446723892679,-28.9604340864709));
#2969=CARTESIAN_POINT('',(20.2,28.7615675693006,-28.98115444526));
#2970=CARTESIAN_POINT('Origin',(24.7,28.7615675693006,-28.98115444526));
#2971=CARTESIAN_POINT('Origin',(32.1995109344297,36.4807281848902,-25.8589484916138));
#2972=CARTESIAN_POINT('',(32.1995109344297,36.4807281848902,-25.8589484916138));
#2973=CARTESIAN_POINT('Origin',(47.25,95.4119654802234,-15.455184733476));
#2974=CARTESIAN_POINT('Origin',(87.,29.5704380760669,-31.8713212652989));
#2975=CARTESIAN_POINT('',(82.5,29.5704380760669,-31.8713212652989));
#2976=CARTESIAN_POINT('Origin',(87.,29.5704380760669,-31.8713212652989));
#2977=CARTESIAN_POINT('Origin',(17.2004890655704,35.2644142945203,-27.3598126584656));
#2978=CARTESIAN_POINT('Origin',(17.2004890655704,34.8773392615608,-25.807339496424));
#2979=CARTESIAN_POINT('Origin',(11.2,73.5,0.));
#2980=CARTESIAN_POINT('',(5.95000000000001,73.5,0.));
#2981=CARTESIAN_POINT('',(5.95000000000001,73.5,0.));
#2982=CARTESIAN_POINT('Origin',(11.2,73.5,0.));
#2983=CARTESIAN_POINT('Origin',(24.7,28.7615675693006,-28.98115444526));
#2984=CARTESIAN_POINT('',(20.2,28.7615675693006,-28.98115444526));
#2985=CARTESIAN_POINT('Origin',(7.50000000000001,98.0929277636669,-50.9609097490238));
#2986=CARTESIAN_POINT('',(3.00000000000002,98.0929277636669,-50.9609097490238));
#2987=CARTESIAN_POINT('Origin',(87.,28.8446723892679,-28.9604340864709));
#2988=CARTESIAN_POINT('',(82.5,28.8446723892679,-28.9604340864709));
#2989=CARTESIAN_POINT('Origin',(72.7,54.9,0.));
#2990=CARTESIAN_POINT('',(67.45,54.9,0.));
#2991=CARTESIAN_POINT('',(67.45,54.9,0.));
#2992=CARTESIAN_POINT('Origin',(72.7,54.9,0.));
#2993=CARTESIAN_POINT('Origin',(47.25,98.0929277636669,-50.9609097490238));
#2994=CARTESIAN_POINT('',(42.75,98.0929277636669,-50.9609097490238));
#2995=CARTESIAN_POINT('Origin',(87.,98.0929277636669,-50.9609097490238));
#2996=CARTESIAN_POINT('',(82.5,98.0929277636669,-50.9609097490238));
#2997=CARTESIAN_POINT('Origin',(39.75,90.3501342049526,-17.0499293978968));
#2998=CARTESIAN_POINT('',(39.75,90.3501342049526,-17.0499293978968));
#2999=CARTESIAN_POINT('Origin',(36.75,90.3501342049526,-17.0499293978968));
#3000=CARTESIAN_POINT('',(36.75,90.2977769856407,-14.0503863124277));
#3001=CARTESIAN_POINT('Origin',(18.,90.2977769856407,-14.0503863124276));
#3002=CARTESIAN_POINT('',(18.,90.2977769856407,-14.0503863124276));
#3003=CARTESIAN_POINT('Origin',(18.,90.3501342049526,-17.0499293978968));
#3004=CARTESIAN_POINT('Origin',(15.,91.881607410759,-48.4891251735625));
#3005=CARTESIAN_POINT('Origin',(7.50000000000001,98.0929277636669,-50.9609097490238));
#3006=CARTESIAN_POINT('Origin',(-3.33066907387547E-15,66.9,0.));
#3007=CARTESIAN_POINT('',(-3.33066907387547E-15,66.9,0.));
#3008=CARTESIAN_POINT('',(-3.33066907387547E-15,66.9,0.));
#3009=CARTESIAN_POINT('',(-3.33066907387547E-15,66.9,0.));
#3010=CARTESIAN_POINT('Origin',(6.39999999999998,66.9,0.));
#3011=CARTESIAN_POINT('',(6.4,60.5,0.));
#3012=CARTESIAN_POINT('Origin',(6.39999999999998,66.9,0.));
#3013=CARTESIAN_POINT('',(6.4,60.5,0.));
#3014=CARTESIAN_POINT('Origin',(10.8004890655704,60.5,0.));
#3015=CARTESIAN_POINT('',(10.8004890655704,60.5,0.));
#3016=CARTESIAN_POINT('',(10.8004890655704,60.5,0.));
#3017=CARTESIAN_POINT('',(10.8004890655704,60.5,0.));
#3018=CARTESIAN_POINT('Origin',(10.8004890655704,54.1,0.));
#3019=CARTESIAN_POINT('Origin',(10.8004890655704,54.1,0.));
#3020=CARTESIAN_POINT('Origin',(17.2004890655704,38.7656710759518,-4.11916906896879));
#3021=CARTESIAN_POINT('Origin',(24.7,28.7615675693006,-28.98115444526));
#3022=CARTESIAN_POINT('',(32.1995109344297,28.8446723892679,-28.9604340864709));
#3023=CARTESIAN_POINT('Origin',(32.1995109344297,38.8387183699107,-26.4686385617943));
#3024=CARTESIAN_POINT('Origin',(29.1995109344297,36.4807281848902,-25.8589484916138));
#3025=CARTESIAN_POINT('',(31.1997554672148,36.7144380696962,-23.6353474710153));
#3026=CARTESIAN_POINT('Origin',(33.2,36.9481479545022,-21.4117464504167));
#3027=CARTESIAN_POINT('Origin',(30.2,43.8,0.));
#3028=CARTESIAN_POINT('',(30.2,43.8,0.));
#3029=CARTESIAN_POINT('',(30.2,43.8,0.));
#3030=CARTESIAN_POINT('',(30.2,21.6716984733155,0.));
#3031=CARTESIAN_POINT('Origin',(33.2,43.8,0.));
#3032=CARTESIAN_POINT('',(33.2,46.8,0.));
#3033=CARTESIAN_POINT('Origin',(33.2,43.8,0.));
#3034=CARTESIAN_POINT('',(33.2,46.8,0.));
#3035=CARTESIAN_POINT('Origin',(32.7,46.8,0.));
#3036=CARTESIAN_POINT('',(32.7,46.8,0.));
#3037=CARTESIAN_POINT('',(33.2,46.8,0.));
#3038=CARTESIAN_POINT('',(32.7,46.8,0.));
#3039=CARTESIAN_POINT('Origin',(32.7,54.8,0.));
#3040=CARTESIAN_POINT('',(40.7,54.8,0.));
#3041=CARTESIAN_POINT('Origin',(32.7,54.8,0.));
#3042=CARTESIAN_POINT('',(40.7,54.8,0.));
#3043=CARTESIAN_POINT('Origin',(40.7,56.7,0.));
#3044=CARTESIAN_POINT('',(40.7,56.7,0.));
#3045=CARTESIAN_POINT('',(40.7,56.7,0.));
#3046=CARTESIAN_POINT('',(40.7,56.7,0.));
#3047=CARTESIAN_POINT('Origin',(52.5,56.7,0.));
#3048=CARTESIAN_POINT('',(64.3,56.7,0.));
#3049=CARTESIAN_POINT('Origin',(52.5,56.7,0.));
#3050=CARTESIAN_POINT('',(64.3,56.7,0.));
#3051=CARTESIAN_POINT('Origin',(64.3,54.8,0.));
#3052=CARTESIAN_POINT('',(64.3,54.8,0.));
#3053=CARTESIAN_POINT('',(64.3,56.7,0.));
#3054=CARTESIAN_POINT('',(64.3,54.8,0.));
#3055=CARTESIAN_POINT('Origin',(72.3,54.8,0.));
#3056=CARTESIAN_POINT('',(72.3,46.8,0.));
#3057=CARTESIAN_POINT('Origin',(72.3,54.8,0.));
#3058=CARTESIAN_POINT('',(72.3,46.8,0.));
#3059=CARTESIAN_POINT('Origin',(76.5,46.8,0.));
#3060=CARTESIAN_POINT('',(76.5,46.8,0.));
#3061=CARTESIAN_POINT('',(72.3,46.8,0.));
#3062=CARTESIAN_POINT('',(76.5,46.8,0.));
#3063=CARTESIAN_POINT('Origin',(76.5,43.8,0.));
#3064=CARTESIAN_POINT('Origin',(76.5,43.8,0.));
#3065=CARTESIAN_POINT('Origin',(79.5,35.2644142945203,-27.3598126584656));
#3066=CARTESIAN_POINT('Origin',(87.,28.8446723892679,-28.9604340864709));
#3067=CARTESIAN_POINT('Origin',(94.5,91.881607410759,-48.4891251735625));
#3068=CARTESIAN_POINT('Origin',(87.,98.0929277636669,-50.9609097490238));
#3069=CARTESIAN_POINT('Origin',(79.5,90.3501342049526,-17.0499293978968));
#3070=CARTESIAN_POINT('',(79.5,90.3501342049526,-17.0499293978968));
#3071=CARTESIAN_POINT('Origin',(76.5,90.3501342049526,-17.0499293978968));
#3072=CARTESIAN_POINT('',(76.5,90.2977769856407,-14.0503863124276));
#3073=CARTESIAN_POINT('Origin',(57.75,90.2977769856407,-14.0503863124276));
#3074=CARTESIAN_POINT('',(57.75,90.2977769856407,-14.0503863124276));
#3075=CARTESIAN_POINT('Origin',(57.75,90.3501342049526,-17.0499293978968));
#3076=CARTESIAN_POINT('Origin',(54.75,91.881607410759,-48.4891251735625));
#3077=CARTESIAN_POINT('Origin',(47.25,98.0929277636669,-50.9609097490238));
#3078=CARTESIAN_POINT('Origin',(47.25,74.7571755199385,0.));
#3079=CARTESIAN_POINT('Origin',(58.3334532468855,34.8773392615608,-25.807339496424));
#3080=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#3084,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#3081=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#3084,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#3082=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#3080))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#3084,#3086,#3087))
REPRESENTATION_CONTEXT('','3D')
);
#3083=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#3081))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#3084,#3086,#3087))
REPRESENTATION_CONTEXT('','3D')
);
#3084=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#3085=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT($,.METRE.)
);
#3086=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#3087=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#3088=SHAPE_DEFINITION_REPRESENTATION(#3089,#3090);
#3089=PRODUCT_DEFINITION_SHAPE('',$,#3092);
#3090=SHAPE_REPRESENTATION('',(#1824),#3082);
#3091=PRODUCT_DEFINITION_CONTEXT('part definition',#3096,'design');
#3092=PRODUCT_DEFINITION('S_1132','S_1132 v1',#3093,#3091);
#3093=PRODUCT_DEFINITION_FORMATION('',$,#3098);
#3094=PRODUCT_RELATED_PRODUCT_CATEGORY('S_1132 v1','S_1132 v1',(#3098));
#3095=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2009,#3096);
#3096=APPLICATION_CONTEXT(
'Core Data for Automotive Mechanical Design Process');
#3097=PRODUCT_CONTEXT('part definition',#3096,'mechanical');
#3098=PRODUCT('S_1132','S_1132 v1',$,(#3097));
#3099=PRESENTATION_STYLE_ASSIGNMENT((#3100));
#3100=SURFACE_STYLE_USAGE(.BOTH.,#3101);
#3101=SURFACE_SIDE_STYLE('',(#3102));
#3102=SURFACE_STYLE_FILL_AREA(#3103);
#3103=FILL_AREA_STYLE('Steel - Satin',(#3104));
#3104=FILL_AREA_STYLE_COLOUR('Steel - Satin',#3105);
#3105=COLOUR_RGB('Steel - Satin',0.627450980392157,0.627450980392157,0.627450980392157);
ENDSEC;
END-ISO-10303-21;
