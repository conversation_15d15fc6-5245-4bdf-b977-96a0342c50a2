ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ (''),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 
'G:/Shared drives/TIP/Validation/SMF KION/Tset SMF Shapes/S_1087.step',

/* time_stamp */ '2021-06-29T15:58:39+02:00',
/* author */ (''),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v18.1',
/* originating_system */ 'Autodesk Translation Framework v10.9.0.1377',

/* authorisation */ '');

FILE_SCHEMA (('AUTOMOTIVE_DESIGN { 1 0 10303 214 3 1 1 }'));
ENDSEC;

DATA;
#10=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#13),#2653);
#11=SHAPE_REPRESENTATION_RELATIONSHIP('SRR','None',#2660,#12);
#12=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#14),#2652);
#13=STYLED_ITEM('',(#2669),#14);
#14=MANIFOLD_SOLID_BREP('Body1',#1633);
#15=FACE_BOUND('',#270,.T.);
#16=FACE_BOUND('',#271,.T.);
#17=FACE_BOUND('',#272,.T.);
#18=FACE_BOUND('',#273,.T.);
#19=FACE_BOUND('',#274,.T.);
#20=FACE_BOUND('',#275,.T.);
#21=FACE_BOUND('',#276,.T.);
#22=FACE_BOUND('',#277,.T.);
#23=FACE_BOUND('',#278,.T.);
#24=FACE_BOUND('',#279,.T.);
#25=FACE_BOUND('',#280,.T.);
#26=FACE_BOUND('',#281,.T.);
#27=FACE_BOUND('',#282,.T.);
#28=FACE_BOUND('',#283,.T.);
#29=FACE_BOUND('',#284,.T.);
#30=FACE_BOUND('',#285,.T.);
#31=FACE_BOUND('',#286,.T.);
#32=FACE_BOUND('',#287,.T.);
#33=FACE_BOUND('',#288,.T.);
#34=FACE_BOUND('',#289,.T.);
#35=FACE_BOUND('',#290,.T.);
#36=FACE_BOUND('',#291,.T.);
#37=FACE_BOUND('',#292,.T.);
#38=FACE_BOUND('',#293,.T.);
#39=FACE_BOUND('',#294,.T.);
#40=FACE_BOUND('',#295,.T.);
#41=FACE_BOUND('',#296,.T.);
#42=FACE_BOUND('',#298,.T.);
#43=FACE_BOUND('',#299,.T.);
#44=FACE_BOUND('',#300,.T.);
#45=FACE_BOUND('',#301,.T.);
#46=FACE_BOUND('',#302,.T.);
#47=FACE_BOUND('',#303,.T.);
#48=FACE_BOUND('',#304,.T.);
#49=FACE_BOUND('',#305,.T.);
#50=FACE_BOUND('',#306,.T.);
#51=FACE_BOUND('',#307,.T.);
#52=FACE_BOUND('',#308,.T.);
#53=FACE_BOUND('',#309,.T.);
#54=FACE_BOUND('',#310,.T.);
#55=FACE_BOUND('',#311,.T.);
#56=FACE_BOUND('',#312,.T.);
#57=FACE_BOUND('',#313,.T.);
#58=FACE_BOUND('',#314,.T.);
#59=FACE_BOUND('',#315,.T.);
#60=FACE_BOUND('',#316,.T.);
#61=FACE_BOUND('',#317,.T.);
#62=FACE_BOUND('',#318,.T.);
#63=FACE_BOUND('',#319,.T.);
#64=FACE_BOUND('',#320,.T.);
#65=FACE_BOUND('',#321,.T.);
#66=FACE_BOUND('',#322,.T.);
#67=FACE_BOUND('',#323,.T.);
#68=FACE_BOUND('',#324,.T.);
#69=PLANE('',#1686);
#70=PLANE('',#1687);
#71=PLANE('',#1688);
#72=PLANE('',#1689);
#73=PLANE('',#1708);
#74=PLANE('',#1709);
#75=PLANE('',#1710);
#76=PLANE('',#1711);
#77=PLANE('',#1712);
#78=PLANE('',#1713);
#79=PLANE('',#1714);
#80=PLANE('',#1715);
#81=PLANE('',#1731);
#82=PLANE('',#1732);
#83=PLANE('',#1733);
#84=PLANE('',#1734);
#85=PLANE('',#1735);
#86=PLANE('',#1736);
#87=PLANE('',#1737);
#88=PLANE('',#1738);
#89=PLANE('',#1739);
#90=PLANE('',#1740);
#91=PLANE('',#1741);
#92=PLANE('',#1742);
#93=PLANE('',#1743);
#94=PLANE('',#1744);
#95=PLANE('',#1745);
#96=PLANE('',#1746);
#97=PLANE('',#1747);
#98=PLANE('',#1748);
#99=PLANE('',#1749);
#100=PLANE('',#1750);
#101=PLANE('',#1751);
#102=PLANE('',#1752);
#103=PLANE('',#1753);
#104=PLANE('',#1754);
#105=PLANE('',#1755);
#106=PLANE('',#1756);
#107=PLANE('',#1760);
#108=PLANE('',#1761);
#109=PLANE('',#1762);
#110=PLANE('',#1763);
#111=PLANE('',#1764);
#112=PLANE('',#1765);
#113=PLANE('',#1766);
#114=PLANE('',#1767);
#115=PLANE('',#1768);
#116=PLANE('',#1772);
#117=PLANE('',#1773);
#118=PLANE('',#1774);
#119=FACE_OUTER_BOUND('',#195,.T.);
#120=FACE_OUTER_BOUND('',#196,.T.);
#121=FACE_OUTER_BOUND('',#197,.T.);
#122=FACE_OUTER_BOUND('',#198,.T.);
#123=FACE_OUTER_BOUND('',#199,.T.);
#124=FACE_OUTER_BOUND('',#200,.T.);
#125=FACE_OUTER_BOUND('',#201,.T.);
#126=FACE_OUTER_BOUND('',#202,.T.);
#127=FACE_OUTER_BOUND('',#203,.T.);
#128=FACE_OUTER_BOUND('',#204,.T.);
#129=FACE_OUTER_BOUND('',#205,.T.);
#130=FACE_OUTER_BOUND('',#206,.T.);
#131=FACE_OUTER_BOUND('',#207,.T.);
#132=FACE_OUTER_BOUND('',#208,.T.);
#133=FACE_OUTER_BOUND('',#209,.T.);
#134=FACE_OUTER_BOUND('',#210,.T.);
#135=FACE_OUTER_BOUND('',#211,.T.);
#136=FACE_OUTER_BOUND('',#212,.T.);
#137=FACE_OUTER_BOUND('',#213,.T.);
#138=FACE_OUTER_BOUND('',#214,.T.);
#139=FACE_OUTER_BOUND('',#215,.T.);
#140=FACE_OUTER_BOUND('',#216,.T.);
#141=FACE_OUTER_BOUND('',#217,.T.);
#142=FACE_OUTER_BOUND('',#218,.T.);
#143=FACE_OUTER_BOUND('',#219,.T.);
#144=FACE_OUTER_BOUND('',#220,.T.);
#145=FACE_OUTER_BOUND('',#221,.T.);
#146=FACE_OUTER_BOUND('',#222,.T.);
#147=FACE_OUTER_BOUND('',#223,.T.);
#148=FACE_OUTER_BOUND('',#224,.T.);
#149=FACE_OUTER_BOUND('',#225,.T.);
#150=FACE_OUTER_BOUND('',#226,.T.);
#151=FACE_OUTER_BOUND('',#227,.T.);
#152=FACE_OUTER_BOUND('',#228,.T.);
#153=FACE_OUTER_BOUND('',#229,.T.);
#154=FACE_OUTER_BOUND('',#230,.T.);
#155=FACE_OUTER_BOUND('',#231,.T.);
#156=FACE_OUTER_BOUND('',#232,.T.);
#157=FACE_OUTER_BOUND('',#233,.T.);
#158=FACE_OUTER_BOUND('',#234,.T.);
#159=FACE_OUTER_BOUND('',#235,.T.);
#160=FACE_OUTER_BOUND('',#236,.T.);
#161=FACE_OUTER_BOUND('',#237,.T.);
#162=FACE_OUTER_BOUND('',#238,.T.);
#163=FACE_OUTER_BOUND('',#239,.T.);
#164=FACE_OUTER_BOUND('',#240,.T.);
#165=FACE_OUTER_BOUND('',#241,.T.);
#166=FACE_OUTER_BOUND('',#242,.T.);
#167=FACE_OUTER_BOUND('',#243,.T.);
#168=FACE_OUTER_BOUND('',#244,.T.);
#169=FACE_OUTER_BOUND('',#245,.T.);
#170=FACE_OUTER_BOUND('',#246,.T.);
#171=FACE_OUTER_BOUND('',#247,.T.);
#172=FACE_OUTER_BOUND('',#248,.T.);
#173=FACE_OUTER_BOUND('',#249,.T.);
#174=FACE_OUTER_BOUND('',#250,.T.);
#175=FACE_OUTER_BOUND('',#251,.T.);
#176=FACE_OUTER_BOUND('',#252,.T.);
#177=FACE_OUTER_BOUND('',#253,.T.);
#178=FACE_OUTER_BOUND('',#254,.T.);
#179=FACE_OUTER_BOUND('',#255,.T.);
#180=FACE_OUTER_BOUND('',#256,.T.);
#181=FACE_OUTER_BOUND('',#257,.T.);
#182=FACE_OUTER_BOUND('',#258,.T.);
#183=FACE_OUTER_BOUND('',#259,.T.);
#184=FACE_OUTER_BOUND('',#260,.T.);
#185=FACE_OUTER_BOUND('',#261,.T.);
#186=FACE_OUTER_BOUND('',#262,.T.);
#187=FACE_OUTER_BOUND('',#263,.T.);
#188=FACE_OUTER_BOUND('',#264,.T.);
#189=FACE_OUTER_BOUND('',#265,.T.);
#190=FACE_OUTER_BOUND('',#266,.T.);
#191=FACE_OUTER_BOUND('',#267,.T.);
#192=FACE_OUTER_BOUND('',#268,.T.);
#193=FACE_OUTER_BOUND('',#269,.T.);
#194=FACE_OUTER_BOUND('',#297,.T.);
#195=EDGE_LOOP('',(#1087,#1088,#1089,#1090));
#196=EDGE_LOOP('',(#1091,#1092,#1093,#1094));
#197=EDGE_LOOP('',(#1095,#1096,#1097,#1098));
#198=EDGE_LOOP('',(#1099,#1100,#1101,#1102));
#199=EDGE_LOOP('',(#1103,#1104,#1105,#1106));
#200=EDGE_LOOP('',(#1107,#1108,#1109,#1110));
#201=EDGE_LOOP('',(#1111,#1112,#1113,#1114));
#202=EDGE_LOOP('',(#1115,#1116,#1117,#1118));
#203=EDGE_LOOP('',(#1119,#1120,#1121,#1122));
#204=EDGE_LOOP('',(#1123,#1124,#1125,#1126));
#205=EDGE_LOOP('',(#1127,#1128,#1129,#1130));
#206=EDGE_LOOP('',(#1131,#1132,#1133,#1134));
#207=EDGE_LOOP('',(#1135,#1136,#1137,#1138));
#208=EDGE_LOOP('',(#1139,#1140,#1141,#1142));
#209=EDGE_LOOP('',(#1143,#1144,#1145,#1146));
#210=EDGE_LOOP('',(#1147,#1148,#1149,#1150));
#211=EDGE_LOOP('',(#1151,#1152,#1153,#1154));
#212=EDGE_LOOP('',(#1155,#1156,#1157,#1158));
#213=EDGE_LOOP('',(#1159,#1160,#1161,#1162));
#214=EDGE_LOOP('',(#1163,#1164,#1165,#1166));
#215=EDGE_LOOP('',(#1167,#1168,#1169,#1170));
#216=EDGE_LOOP('',(#1171,#1172,#1173,#1174));
#217=EDGE_LOOP('',(#1175,#1176,#1177,#1178));
#218=EDGE_LOOP('',(#1179,#1180,#1181,#1182));
#219=EDGE_LOOP('',(#1183,#1184,#1185,#1186));
#220=EDGE_LOOP('',(#1187,#1188,#1189,#1190));
#221=EDGE_LOOP('',(#1191,#1192,#1193,#1194));
#222=EDGE_LOOP('',(#1195,#1196,#1197,#1198));
#223=EDGE_LOOP('',(#1199,#1200,#1201,#1202));
#224=EDGE_LOOP('',(#1203,#1204,#1205,#1206));
#225=EDGE_LOOP('',(#1207,#1208,#1209,#1210));
#226=EDGE_LOOP('',(#1211,#1212,#1213,#1214));
#227=EDGE_LOOP('',(#1215,#1216,#1217,#1218));
#228=EDGE_LOOP('',(#1219,#1220,#1221,#1222));
#229=EDGE_LOOP('',(#1223,#1224,#1225,#1226));
#230=EDGE_LOOP('',(#1227,#1228,#1229,#1230));
#231=EDGE_LOOP('',(#1231,#1232,#1233,#1234));
#232=EDGE_LOOP('',(#1235,#1236,#1237,#1238));
#233=EDGE_LOOP('',(#1239,#1240,#1241,#1242));
#234=EDGE_LOOP('',(#1243,#1244,#1245,#1246));
#235=EDGE_LOOP('',(#1247,#1248,#1249,#1250));
#236=EDGE_LOOP('',(#1251,#1252,#1253,#1254));
#237=EDGE_LOOP('',(#1255,#1256,#1257,#1258));
#238=EDGE_LOOP('',(#1259,#1260,#1261,#1262));
#239=EDGE_LOOP('',(#1263,#1264,#1265,#1266));
#240=EDGE_LOOP('',(#1267,#1268,#1269,#1270));
#241=EDGE_LOOP('',(#1271,#1272,#1273,#1274));
#242=EDGE_LOOP('',(#1275,#1276,#1277,#1278));
#243=EDGE_LOOP('',(#1279,#1280,#1281,#1282));
#244=EDGE_LOOP('',(#1283,#1284,#1285,#1286));
#245=EDGE_LOOP('',(#1287,#1288,#1289,#1290));
#246=EDGE_LOOP('',(#1291,#1292,#1293,#1294));
#247=EDGE_LOOP('',(#1295,#1296,#1297,#1298));
#248=EDGE_LOOP('',(#1299,#1300,#1301,#1302));
#249=EDGE_LOOP('',(#1303,#1304,#1305,#1306));
#250=EDGE_LOOP('',(#1307,#1308,#1309,#1310));
#251=EDGE_LOOP('',(#1311,#1312,#1313,#1314));
#252=EDGE_LOOP('',(#1315,#1316,#1317,#1318));
#253=EDGE_LOOP('',(#1319,#1320,#1321,#1322));
#254=EDGE_LOOP('',(#1323,#1324,#1325,#1326));
#255=EDGE_LOOP('',(#1327,#1328,#1329,#1330));
#256=EDGE_LOOP('',(#1331,#1332,#1333,#1334));
#257=EDGE_LOOP('',(#1335,#1336,#1337,#1338));
#258=EDGE_LOOP('',(#1339,#1340,#1341,#1342));
#259=EDGE_LOOP('',(#1343,#1344,#1345,#1346));
#260=EDGE_LOOP('',(#1347,#1348,#1349,#1350));
#261=EDGE_LOOP('',(#1351,#1352,#1353,#1354));
#262=EDGE_LOOP('',(#1355,#1356,#1357,#1358));
#263=EDGE_LOOP('',(#1359,#1360,#1361,#1362));
#264=EDGE_LOOP('',(#1363,#1364,#1365,#1366));
#265=EDGE_LOOP('',(#1367,#1368,#1369,#1370));
#266=EDGE_LOOP('',(#1371,#1372,#1373,#1374));
#267=EDGE_LOOP('',(#1375,#1376,#1377,#1378));
#268=EDGE_LOOP('',(#1379,#1380,#1381,#1382));
#269=EDGE_LOOP('',(#1383,#1384,#1385,#1386,#1387,#1388,#1389,#1390,#1391,
#1392,#1393,#1394,#1395,#1396,#1397,#1398,#1399,#1400,#1401,#1402,#1403,
#1404,#1405,#1406,#1407,#1408,#1409,#1410,#1411,#1412,#1413,#1414,#1415,
#1416,#1417,#1418,#1419,#1420));
#270=EDGE_LOOP('',(#1421));
#271=EDGE_LOOP('',(#1422));
#272=EDGE_LOOP('',(#1423));
#273=EDGE_LOOP('',(#1424));
#274=EDGE_LOOP('',(#1425));
#275=EDGE_LOOP('',(#1426,#1427,#1428,#1429));
#276=EDGE_LOOP('',(#1430,#1431,#1432,#1433));
#277=EDGE_LOOP('',(#1434));
#278=EDGE_LOOP('',(#1435));
#279=EDGE_LOOP('',(#1436));
#280=EDGE_LOOP('',(#1437));
#281=EDGE_LOOP('',(#1438));
#282=EDGE_LOOP('',(#1439));
#283=EDGE_LOOP('',(#1440,#1441,#1442,#1443));
#284=EDGE_LOOP('',(#1444));
#285=EDGE_LOOP('',(#1445));
#286=EDGE_LOOP('',(#1446));
#287=EDGE_LOOP('',(#1447));
#288=EDGE_LOOP('',(#1448));
#289=EDGE_LOOP('',(#1449));
#290=EDGE_LOOP('',(#1450));
#291=EDGE_LOOP('',(#1451));
#292=EDGE_LOOP('',(#1452));
#293=EDGE_LOOP('',(#1453));
#294=EDGE_LOOP('',(#1454));
#295=EDGE_LOOP('',(#1455));
#296=EDGE_LOOP('',(#1456));
#297=EDGE_LOOP('',(#1457,#1458,#1459,#1460,#1461,#1462,#1463,#1464,#1465,
#1466,#1467,#1468,#1469,#1470,#1471,#1472,#1473,#1474,#1475,#1476,#1477,
#1478,#1479,#1480,#1481,#1482,#1483,#1484,#1485,#1486,#1487,#1488,#1489,
#1490,#1491,#1492,#1493,#1494));
#298=EDGE_LOOP('',(#1495));
#299=EDGE_LOOP('',(#1496));
#300=EDGE_LOOP('',(#1497));
#301=EDGE_LOOP('',(#1498));
#302=EDGE_LOOP('',(#1499));
#303=EDGE_LOOP('',(#1500,#1501,#1502,#1503));
#304=EDGE_LOOP('',(#1504,#1505,#1506,#1507));
#305=EDGE_LOOP('',(#1508));
#306=EDGE_LOOP('',(#1509));
#307=EDGE_LOOP('',(#1510));
#308=EDGE_LOOP('',(#1511));
#309=EDGE_LOOP('',(#1512));
#310=EDGE_LOOP('',(#1513));
#311=EDGE_LOOP('',(#1514,#1515,#1516,#1517));
#312=EDGE_LOOP('',(#1518));
#313=EDGE_LOOP('',(#1519));
#314=EDGE_LOOP('',(#1520));
#315=EDGE_LOOP('',(#1521));
#316=EDGE_LOOP('',(#1522));
#317=EDGE_LOOP('',(#1523));
#318=EDGE_LOOP('',(#1524));
#319=EDGE_LOOP('',(#1525));
#320=EDGE_LOOP('',(#1526));
#321=EDGE_LOOP('',(#1527));
#322=EDGE_LOOP('',(#1528));
#323=EDGE_LOOP('',(#1529));
#324=EDGE_LOOP('',(#1530));
#325=LINE('',#2208,#495);
#326=LINE('',#2214,#496);
#327=LINE('',#2220,#497);
#328=LINE('',#2226,#498);
#329=LINE('',#2232,#499);
#330=LINE('',#2238,#500);
#331=LINE('',#2244,#501);
#332=LINE('',#2250,#502);
#333=LINE('',#2256,#503);
#334=LINE('',#2262,#504);
#335=LINE('',#2268,#505);
#336=LINE('',#2274,#506);
#337=LINE('',#2280,#507);
#338=LINE('',#2285,#508);
#339=LINE('',#2287,#509);
#340=LINE('',#2289,#510);
#341=LINE('',#2290,#511);
#342=LINE('',#2293,#512);
#343=LINE('',#2295,#513);
#344=LINE('',#2296,#514);
#345=LINE('',#2299,#515);
#346=LINE('',#2301,#516);
#347=LINE('',#2302,#517);
#348=LINE('',#2304,#518);
#349=LINE('',#2305,#519);
#350=LINE('',#2310,#520);
#351=LINE('',#2316,#521);
#352=LINE('',#2322,#522);
#353=LINE('',#2328,#523);
#354=LINE('',#2334,#524);
#355=LINE('',#2340,#525);
#356=LINE('',#2345,#526);
#357=LINE('',#2347,#527);
#358=LINE('',#2349,#528);
#359=LINE('',#2350,#529);
#360=LINE('',#2353,#530);
#361=LINE('',#2355,#531);
#362=LINE('',#2356,#532);
#363=LINE('',#2359,#533);
#364=LINE('',#2361,#534);
#365=LINE('',#2362,#535);
#366=LINE('',#2364,#536);
#367=LINE('',#2365,#537);
#368=LINE('',#2369,#538);
#369=LINE('',#2371,#539);
#370=LINE('',#2373,#540);
#371=LINE('',#2374,#541);
#372=LINE('',#2377,#542);
#373=LINE('',#2379,#543);
#374=LINE('',#2380,#544);
#375=LINE('',#2383,#545);
#376=LINE('',#2385,#546);
#377=LINE('',#2386,#547);
#378=LINE('',#2388,#548);
#379=LINE('',#2389,#549);
#380=LINE('',#2394,#550);
#381=LINE('',#2400,#551);
#382=LINE('',#2406,#552);
#383=LINE('',#2412,#553);
#384=LINE('',#2418,#554);
#385=LINE('',#2423,#555);
#386=LINE('',#2425,#556);
#387=LINE('',#2427,#557);
#388=LINE('',#2428,#558);
#389=LINE('',#2431,#559);
#390=LINE('',#2433,#560);
#391=LINE('',#2434,#561);
#392=LINE('',#2437,#562);
#393=LINE('',#2439,#563);
#394=LINE('',#2440,#564);
#395=LINE('',#2443,#565);
#396=LINE('',#2445,#566);
#397=LINE('',#2446,#567);
#398=LINE('',#2449,#568);
#399=LINE('',#2451,#569);
#400=LINE('',#2452,#570);
#401=LINE('',#2455,#571);
#402=LINE('',#2457,#572);
#403=LINE('',#2458,#573);
#404=LINE('',#2461,#574);
#405=LINE('',#2463,#575);
#406=LINE('',#2464,#576);
#407=LINE('',#2467,#577);
#408=LINE('',#2469,#578);
#409=LINE('',#2470,#579);
#410=LINE('',#2473,#580);
#411=LINE('',#2475,#581);
#412=LINE('',#2476,#582);
#413=LINE('',#2479,#583);
#414=LINE('',#2481,#584);
#415=LINE('',#2482,#585);
#416=LINE('',#2485,#586);
#417=LINE('',#2487,#587);
#418=LINE('',#2488,#588);
#419=LINE('',#2491,#589);
#420=LINE('',#2493,#590);
#421=LINE('',#2494,#591);
#422=LINE('',#2497,#592);
#423=LINE('',#2499,#593);
#424=LINE('',#2500,#594);
#425=LINE('',#2503,#595);
#426=LINE('',#2505,#596);
#427=LINE('',#2506,#597);
#428=LINE('',#2509,#598);
#429=LINE('',#2511,#599);
#430=LINE('',#2512,#600);
#431=LINE('',#2515,#601);
#432=LINE('',#2517,#602);
#433=LINE('',#2518,#603);
#434=LINE('',#2521,#604);
#435=LINE('',#2523,#605);
#436=LINE('',#2524,#606);
#437=LINE('',#2527,#607);
#438=LINE('',#2529,#608);
#439=LINE('',#2530,#609);
#440=LINE('',#2533,#610);
#441=LINE('',#2535,#611);
#442=LINE('',#2536,#612);
#443=LINE('',#2539,#613);
#444=LINE('',#2541,#614);
#445=LINE('',#2542,#615);
#446=LINE('',#2545,#616);
#447=LINE('',#2547,#617);
#448=LINE('',#2548,#618);
#449=LINE('',#2551,#619);
#450=LINE('',#2553,#620);
#451=LINE('',#2554,#621);
#452=LINE('',#2557,#622);
#453=LINE('',#2559,#623);
#454=LINE('',#2560,#624);
#455=LINE('',#2563,#625);
#456=LINE('',#2565,#626);
#457=LINE('',#2566,#627);
#458=LINE('',#2569,#628);
#459=LINE('',#2571,#629);
#460=LINE('',#2572,#630);
#461=LINE('',#2575,#631);
#462=LINE('',#2577,#632);
#463=LINE('',#2578,#633);
#464=LINE('',#2584,#634);
#465=LINE('',#2587,#635);
#466=LINE('',#2589,#636);
#467=LINE('',#2590,#637);
#468=LINE('',#2593,#638);
#469=LINE('',#2595,#639);
#470=LINE('',#2596,#640);
#471=LINE('',#2599,#641);
#472=LINE('',#2601,#642);
#473=LINE('',#2602,#643);
#474=LINE('',#2605,#644);
#475=LINE('',#2607,#645);
#476=LINE('',#2608,#646);
#477=LINE('',#2611,#647);
#478=LINE('',#2613,#648);
#479=LINE('',#2614,#649);
#480=LINE('',#2617,#650);
#481=LINE('',#2619,#651);
#482=LINE('',#2620,#652);
#483=LINE('',#2623,#653);
#484=LINE('',#2625,#654);
#485=LINE('',#2626,#655);
#486=LINE('',#2629,#656);
#487=LINE('',#2631,#657);
#488=LINE('',#2632,#658);
#489=LINE('',#2635,#659);
#490=LINE('',#2637,#660);
#491=LINE('',#2638,#661);
#492=LINE('',#2644,#662);
#493=LINE('',#2646,#663);
#494=LINE('',#2647,#664);
#495=VECTOR('',#1781,2.53999999999998);
#496=VECTOR('',#1788,6.98500000000003);
#497=VECTOR('',#1795,2.53999999999998);
#498=VECTOR('',#1802,6.98500000000003);
#499=VECTOR('',#1809,6.98499999999996);
#500=VECTOR('',#1816,6.98499999999996);
#501=VECTOR('',#1823,2.53999999999998);
#502=VECTOR('',#1830,2.53999999999998);
#503=VECTOR('',#1837,2.53999999999998);
#504=VECTOR('',#1844,2.53999999999998);
#505=VECTOR('',#1851,2.53999999999991);
#506=VECTOR('',#1858,2.53999999999991);
#507=VECTOR('',#1865,2.53999999999991);
#508=VECTOR('',#1870,10.);
#509=VECTOR('',#1871,10.);
#510=VECTOR('',#1872,10.);
#511=VECTOR('',#1873,10.);
#512=VECTOR('',#1876,10.);
#513=VECTOR('',#1877,10.);
#514=VECTOR('',#1878,10.);
#515=VECTOR('',#1881,10.);
#516=VECTOR('',#1882,10.);
#517=VECTOR('',#1883,10.);
#518=VECTOR('',#1886,10.);
#519=VECTOR('',#1887,10.);
#520=VECTOR('',#1892,6.98499999999996);
#521=VECTOR('',#1899,5.588);
#522=VECTOR('',#1906,5.58799999999998);
#523=VECTOR('',#1913,5.58799999999998);
#524=VECTOR('',#1920,5.588);
#525=VECTOR('',#1927,25.4);
#526=VECTOR('',#1932,10.);
#527=VECTOR('',#1933,10.);
#528=VECTOR('',#1934,10.);
#529=VECTOR('',#1935,10.);
#530=VECTOR('',#1938,10.);
#531=VECTOR('',#1939,10.);
#532=VECTOR('',#1940,10.);
#533=VECTOR('',#1943,10.);
#534=VECTOR('',#1944,10.);
#535=VECTOR('',#1945,10.);
#536=VECTOR('',#1948,10.);
#537=VECTOR('',#1949,10.);
#538=VECTOR('',#1952,10.);
#539=VECTOR('',#1953,10.);
#540=VECTOR('',#1954,10.);
#541=VECTOR('',#1955,10.);
#542=VECTOR('',#1958,10.);
#543=VECTOR('',#1959,10.);
#544=VECTOR('',#1960,10.);
#545=VECTOR('',#1963,10.);
#546=VECTOR('',#1964,10.);
#547=VECTOR('',#1965,10.);
#548=VECTOR('',#1968,10.);
#549=VECTOR('',#1969,10.);
#550=VECTOR('',#1974,6.98499999999996);
#551=VECTOR('',#1981,6.98499999999996);
#552=VECTOR('',#1988,2.53999999999991);
#553=VECTOR('',#1995,2.53999999999991);
#554=VECTOR('',#2002,6.9850000000001);
#555=VECTOR('',#2007,10.);
#556=VECTOR('',#2008,10.);
#557=VECTOR('',#2009,10.);
#558=VECTOR('',#2010,10.);
#559=VECTOR('',#2013,10.);
#560=VECTOR('',#2014,10.);
#561=VECTOR('',#2015,10.);
#562=VECTOR('',#2018,10.);
#563=VECTOR('',#2019,10.);
#564=VECTOR('',#2020,10.);
#565=VECTOR('',#2023,10.);
#566=VECTOR('',#2024,10.);
#567=VECTOR('',#2025,10.);
#568=VECTOR('',#2028,10.);
#569=VECTOR('',#2029,10.);
#570=VECTOR('',#2030,10.);
#571=VECTOR('',#2033,10.);
#572=VECTOR('',#2034,10.);
#573=VECTOR('',#2035,10.);
#574=VECTOR('',#2038,10.);
#575=VECTOR('',#2039,10.);
#576=VECTOR('',#2040,10.);
#577=VECTOR('',#2043,10.);
#578=VECTOR('',#2044,10.);
#579=VECTOR('',#2045,10.);
#580=VECTOR('',#2048,10.);
#581=VECTOR('',#2049,10.);
#582=VECTOR('',#2050,10.);
#583=VECTOR('',#2053,10.);
#584=VECTOR('',#2054,10.);
#585=VECTOR('',#2055,10.);
#586=VECTOR('',#2058,10.);
#587=VECTOR('',#2059,10.);
#588=VECTOR('',#2060,10.);
#589=VECTOR('',#2063,10.);
#590=VECTOR('',#2064,10.);
#591=VECTOR('',#2065,10.);
#592=VECTOR('',#2068,10.);
#593=VECTOR('',#2069,10.);
#594=VECTOR('',#2070,10.);
#595=VECTOR('',#2073,10.);
#596=VECTOR('',#2074,10.);
#597=VECTOR('',#2075,10.);
#598=VECTOR('',#2078,10.);
#599=VECTOR('',#2079,10.);
#600=VECTOR('',#2080,10.);
#601=VECTOR('',#2083,10.);
#602=VECTOR('',#2084,10.);
#603=VECTOR('',#2085,10.);
#604=VECTOR('',#2088,10.);
#605=VECTOR('',#2089,10.);
#606=VECTOR('',#2090,10.);
#607=VECTOR('',#2093,10.);
#608=VECTOR('',#2094,10.);
#609=VECTOR('',#2095,10.);
#610=VECTOR('',#2098,10.);
#611=VECTOR('',#2099,10.);
#612=VECTOR('',#2100,10.);
#613=VECTOR('',#2103,10.);
#614=VECTOR('',#2104,10.);
#615=VECTOR('',#2105,10.);
#616=VECTOR('',#2108,10.);
#617=VECTOR('',#2109,10.);
#618=VECTOR('',#2110,10.);
#619=VECTOR('',#2113,10.);
#620=VECTOR('',#2114,10.);
#621=VECTOR('',#2115,10.);
#622=VECTOR('',#2118,10.);
#623=VECTOR('',#2119,10.);
#624=VECTOR('',#2120,10.);
#625=VECTOR('',#2123,10.);
#626=VECTOR('',#2124,10.);
#627=VECTOR('',#2125,10.);
#628=VECTOR('',#2128,10.);
#629=VECTOR('',#2129,10.);
#630=VECTOR('',#2130,10.);
#631=VECTOR('',#2133,10.);
#632=VECTOR('',#2134,10.);
#633=VECTOR('',#2135,10.);
#634=VECTOR('',#2142,10.);
#635=VECTOR('',#2145,10.);
#636=VECTOR('',#2146,10.);
#637=VECTOR('',#2147,10.);
#638=VECTOR('',#2150,10.);
#639=VECTOR('',#2151,10.);
#640=VECTOR('',#2152,10.);
#641=VECTOR('',#2155,10.);
#642=VECTOR('',#2156,10.);
#643=VECTOR('',#2157,10.);
#644=VECTOR('',#2160,10.);
#645=VECTOR('',#2161,10.);
#646=VECTOR('',#2162,10.);
#647=VECTOR('',#2165,10.);
#648=VECTOR('',#2166,10.);
#649=VECTOR('',#2167,10.);
#650=VECTOR('',#2170,10.);
#651=VECTOR('',#2171,10.);
#652=VECTOR('',#2172,10.);
#653=VECTOR('',#2175,10.);
#654=VECTOR('',#2176,10.);
#655=VECTOR('',#2177,10.);
#656=VECTOR('',#2180,10.);
#657=VECTOR('',#2181,10.);
#658=VECTOR('',#2182,10.);
#659=VECTOR('',#2185,10.);
#660=VECTOR('',#2186,10.);
#661=VECTOR('',#2187,10.);
#662=VECTOR('',#2194,10.);
#663=VECTOR('',#2197,10.);
#664=VECTOR('',#2198,10.);
#665=CIRCLE('',#1648,2.53999999999998);
#666=CIRCLE('',#1649,2.53999999999998);
#667=CIRCLE('',#1651,6.98500000000003);
#668=CIRCLE('',#1652,6.98500000000003);
#669=CIRCLE('',#1654,2.53999999999998);
#670=CIRCLE('',#1655,2.53999999999998);
#671=CIRCLE('',#1657,6.98500000000003);
#672=CIRCLE('',#1658,6.98500000000003);
#673=CIRCLE('',#1660,6.98499999999996);
#674=CIRCLE('',#1661,6.98499999999996);
#675=CIRCLE('',#1663,6.98499999999996);
#676=CIRCLE('',#1664,6.98499999999996);
#677=CIRCLE('',#1666,2.53999999999998);
#678=CIRCLE('',#1667,2.53999999999998);
#679=CIRCLE('',#1669,2.53999999999998);
#680=CIRCLE('',#1670,2.53999999999998);
#681=CIRCLE('',#1672,2.53999999999998);
#682=CIRCLE('',#1673,2.53999999999998);
#683=CIRCLE('',#1675,2.53999999999998);
#684=CIRCLE('',#1676,2.53999999999998);
#685=CIRCLE('',#1678,2.53999999999991);
#686=CIRCLE('',#1679,2.53999999999991);
#687=CIRCLE('',#1681,2.53999999999991);
#688=CIRCLE('',#1682,2.53999999999991);
#689=CIRCLE('',#1684,2.53999999999991);
#690=CIRCLE('',#1685,2.53999999999991);
#691=CIRCLE('',#1691,6.98499999999996);
#692=CIRCLE('',#1692,6.98499999999996);
#693=CIRCLE('',#1694,5.588);
#694=CIRCLE('',#1695,5.588);
#695=CIRCLE('',#1697,5.58799999999998);
#696=CIRCLE('',#1698,5.58799999999998);
#697=CIRCLE('',#1700,5.58799999999998);
#698=CIRCLE('',#1701,5.58799999999998);
#699=CIRCLE('',#1703,5.588);
#700=CIRCLE('',#1704,5.588);
#701=CIRCLE('',#1706,25.4);
#702=CIRCLE('',#1707,25.4);
#703=CIRCLE('',#1717,6.98499999999996);
#704=CIRCLE('',#1718,6.98499999999996);
#705=CIRCLE('',#1720,6.98499999999996);
#706=CIRCLE('',#1721,6.98499999999996);
#707=CIRCLE('',#1723,2.53999999999991);
#708=CIRCLE('',#1724,2.53999999999991);
#709=CIRCLE('',#1726,2.53999999999991);
#710=CIRCLE('',#1727,2.53999999999991);
#711=CIRCLE('',#1729,6.9850000000001);
#712=CIRCLE('',#1730,6.9850000000001);
#713=CIRCLE('',#1758,50.);
#714=CIRCLE('',#1759,50.);
#715=CIRCLE('',#1770,49.9999999999997);
#716=CIRCLE('',#1771,49.9999999999997);
#717=VERTEX_POINT('',#2205);
#718=VERTEX_POINT('',#2207);
#719=VERTEX_POINT('',#2211);
#720=VERTEX_POINT('',#2213);
#721=VERTEX_POINT('',#2217);
#722=VERTEX_POINT('',#2219);
#723=VERTEX_POINT('',#2223);
#724=VERTEX_POINT('',#2225);
#725=VERTEX_POINT('',#2229);
#726=VERTEX_POINT('',#2231);
#727=VERTEX_POINT('',#2235);
#728=VERTEX_POINT('',#2237);
#729=VERTEX_POINT('',#2241);
#730=VERTEX_POINT('',#2243);
#731=VERTEX_POINT('',#2247);
#732=VERTEX_POINT('',#2249);
#733=VERTEX_POINT('',#2253);
#734=VERTEX_POINT('',#2255);
#735=VERTEX_POINT('',#2259);
#736=VERTEX_POINT('',#2261);
#737=VERTEX_POINT('',#2265);
#738=VERTEX_POINT('',#2267);
#739=VERTEX_POINT('',#2271);
#740=VERTEX_POINT('',#2273);
#741=VERTEX_POINT('',#2277);
#742=VERTEX_POINT('',#2279);
#743=VERTEX_POINT('',#2283);
#744=VERTEX_POINT('',#2284);
#745=VERTEX_POINT('',#2286);
#746=VERTEX_POINT('',#2288);
#747=VERTEX_POINT('',#2292);
#748=VERTEX_POINT('',#2294);
#749=VERTEX_POINT('',#2298);
#750=VERTEX_POINT('',#2300);
#751=VERTEX_POINT('',#2307);
#752=VERTEX_POINT('',#2309);
#753=VERTEX_POINT('',#2313);
#754=VERTEX_POINT('',#2315);
#755=VERTEX_POINT('',#2319);
#756=VERTEX_POINT('',#2321);
#757=VERTEX_POINT('',#2325);
#758=VERTEX_POINT('',#2327);
#759=VERTEX_POINT('',#2331);
#760=VERTEX_POINT('',#2333);
#761=VERTEX_POINT('',#2337);
#762=VERTEX_POINT('',#2339);
#763=VERTEX_POINT('',#2343);
#764=VERTEX_POINT('',#2344);
#765=VERTEX_POINT('',#2346);
#766=VERTEX_POINT('',#2348);
#767=VERTEX_POINT('',#2352);
#768=VERTEX_POINT('',#2354);
#769=VERTEX_POINT('',#2358);
#770=VERTEX_POINT('',#2360);
#771=VERTEX_POINT('',#2367);
#772=VERTEX_POINT('',#2368);
#773=VERTEX_POINT('',#2370);
#774=VERTEX_POINT('',#2372);
#775=VERTEX_POINT('',#2376);
#776=VERTEX_POINT('',#2378);
#777=VERTEX_POINT('',#2382);
#778=VERTEX_POINT('',#2384);
#779=VERTEX_POINT('',#2391);
#780=VERTEX_POINT('',#2393);
#781=VERTEX_POINT('',#2397);
#782=VERTEX_POINT('',#2399);
#783=VERTEX_POINT('',#2403);
#784=VERTEX_POINT('',#2405);
#785=VERTEX_POINT('',#2409);
#786=VERTEX_POINT('',#2411);
#787=VERTEX_POINT('',#2415);
#788=VERTEX_POINT('',#2417);
#789=VERTEX_POINT('',#2421);
#790=VERTEX_POINT('',#2422);
#791=VERTEX_POINT('',#2424);
#792=VERTEX_POINT('',#2426);
#793=VERTEX_POINT('',#2430);
#794=VERTEX_POINT('',#2432);
#795=VERTEX_POINT('',#2436);
#796=VERTEX_POINT('',#2438);
#797=VERTEX_POINT('',#2442);
#798=VERTEX_POINT('',#2444);
#799=VERTEX_POINT('',#2448);
#800=VERTEX_POINT('',#2450);
#801=VERTEX_POINT('',#2454);
#802=VERTEX_POINT('',#2456);
#803=VERTEX_POINT('',#2460);
#804=VERTEX_POINT('',#2462);
#805=VERTEX_POINT('',#2466);
#806=VERTEX_POINT('',#2468);
#807=VERTEX_POINT('',#2472);
#808=VERTEX_POINT('',#2474);
#809=VERTEX_POINT('',#2478);
#810=VERTEX_POINT('',#2480);
#811=VERTEX_POINT('',#2484);
#812=VERTEX_POINT('',#2486);
#813=VERTEX_POINT('',#2490);
#814=VERTEX_POINT('',#2492);
#815=VERTEX_POINT('',#2496);
#816=VERTEX_POINT('',#2498);
#817=VERTEX_POINT('',#2502);
#818=VERTEX_POINT('',#2504);
#819=VERTEX_POINT('',#2508);
#820=VERTEX_POINT('',#2510);
#821=VERTEX_POINT('',#2514);
#822=VERTEX_POINT('',#2516);
#823=VERTEX_POINT('',#2520);
#824=VERTEX_POINT('',#2522);
#825=VERTEX_POINT('',#2526);
#826=VERTEX_POINT('',#2528);
#827=VERTEX_POINT('',#2532);
#828=VERTEX_POINT('',#2534);
#829=VERTEX_POINT('',#2538);
#830=VERTEX_POINT('',#2540);
#831=VERTEX_POINT('',#2544);
#832=VERTEX_POINT('',#2546);
#833=VERTEX_POINT('',#2550);
#834=VERTEX_POINT('',#2552);
#835=VERTEX_POINT('',#2556);
#836=VERTEX_POINT('',#2558);
#837=VERTEX_POINT('',#2562);
#838=VERTEX_POINT('',#2564);
#839=VERTEX_POINT('',#2568);
#840=VERTEX_POINT('',#2570);
#841=VERTEX_POINT('',#2574);
#842=VERTEX_POINT('',#2576);
#843=VERTEX_POINT('',#2580);
#844=VERTEX_POINT('',#2582);
#845=VERTEX_POINT('',#2586);
#846=VERTEX_POINT('',#2588);
#847=VERTEX_POINT('',#2592);
#848=VERTEX_POINT('',#2594);
#849=VERTEX_POINT('',#2598);
#850=VERTEX_POINT('',#2600);
#851=VERTEX_POINT('',#2604);
#852=VERTEX_POINT('',#2606);
#853=VERTEX_POINT('',#2610);
#854=VERTEX_POINT('',#2612);
#855=VERTEX_POINT('',#2616);
#856=VERTEX_POINT('',#2618);
#857=VERTEX_POINT('',#2622);
#858=VERTEX_POINT('',#2624);
#859=VERTEX_POINT('',#2628);
#860=VERTEX_POINT('',#2630);
#861=VERTEX_POINT('',#2634);
#862=VERTEX_POINT('',#2636);
#863=VERTEX_POINT('',#2640);
#864=VERTEX_POINT('',#2642);
#865=EDGE_CURVE('',#717,#717,#665,.T.);
#866=EDGE_CURVE('',#717,#718,#325,.T.);
#867=EDGE_CURVE('',#718,#718,#666,.T.);
#868=EDGE_CURVE('',#719,#719,#667,.T.);
#869=EDGE_CURVE('',#719,#720,#326,.T.);
#870=EDGE_CURVE('',#720,#720,#668,.T.);
#871=EDGE_CURVE('',#721,#721,#669,.T.);
#872=EDGE_CURVE('',#721,#722,#327,.T.);
#873=EDGE_CURVE('',#722,#722,#670,.T.);
#874=EDGE_CURVE('',#723,#723,#671,.T.);
#875=EDGE_CURVE('',#723,#724,#328,.T.);
#876=EDGE_CURVE('',#724,#724,#672,.T.);
#877=EDGE_CURVE('',#725,#725,#673,.T.);
#878=EDGE_CURVE('',#725,#726,#329,.T.);
#879=EDGE_CURVE('',#726,#726,#674,.T.);
#880=EDGE_CURVE('',#727,#727,#675,.T.);
#881=EDGE_CURVE('',#727,#728,#330,.T.);
#882=EDGE_CURVE('',#728,#728,#676,.T.);
#883=EDGE_CURVE('',#729,#729,#677,.T.);
#884=EDGE_CURVE('',#729,#730,#331,.T.);
#885=EDGE_CURVE('',#730,#730,#678,.T.);
#886=EDGE_CURVE('',#731,#731,#679,.T.);
#887=EDGE_CURVE('',#731,#732,#332,.T.);
#888=EDGE_CURVE('',#732,#732,#680,.T.);
#889=EDGE_CURVE('',#733,#733,#681,.T.);
#890=EDGE_CURVE('',#733,#734,#333,.T.);
#891=EDGE_CURVE('',#734,#734,#682,.T.);
#892=EDGE_CURVE('',#735,#735,#683,.T.);
#893=EDGE_CURVE('',#735,#736,#334,.T.);
#894=EDGE_CURVE('',#736,#736,#684,.T.);
#895=EDGE_CURVE('',#737,#737,#685,.T.);
#896=EDGE_CURVE('',#737,#738,#335,.T.);
#897=EDGE_CURVE('',#738,#738,#686,.T.);
#898=EDGE_CURVE('',#739,#739,#687,.T.);
#899=EDGE_CURVE('',#739,#740,#336,.T.);
#900=EDGE_CURVE('',#740,#740,#688,.T.);
#901=EDGE_CURVE('',#741,#741,#689,.T.);
#902=EDGE_CURVE('',#741,#742,#337,.T.);
#903=EDGE_CURVE('',#742,#742,#690,.T.);
#904=EDGE_CURVE('',#743,#744,#338,.T.);
#905=EDGE_CURVE('',#744,#745,#339,.T.);
#906=EDGE_CURVE('',#746,#745,#340,.T.);
#907=EDGE_CURVE('',#743,#746,#341,.T.);
#908=EDGE_CURVE('',#747,#743,#342,.T.);
#909=EDGE_CURVE('',#748,#746,#343,.T.);
#910=EDGE_CURVE('',#747,#748,#344,.T.);
#911=EDGE_CURVE('',#749,#747,#345,.T.);
#912=EDGE_CURVE('',#750,#748,#346,.T.);
#913=EDGE_CURVE('',#749,#750,#347,.T.);
#914=EDGE_CURVE('',#744,#749,#348,.T.);
#915=EDGE_CURVE('',#745,#750,#349,.T.);
#916=EDGE_CURVE('',#751,#751,#691,.T.);
#917=EDGE_CURVE('',#751,#752,#350,.T.);
#918=EDGE_CURVE('',#752,#752,#692,.T.);
#919=EDGE_CURVE('',#753,#753,#693,.T.);
#920=EDGE_CURVE('',#753,#754,#351,.T.);
#921=EDGE_CURVE('',#754,#754,#694,.T.);
#922=EDGE_CURVE('',#755,#755,#695,.T.);
#923=EDGE_CURVE('',#755,#756,#352,.T.);
#924=EDGE_CURVE('',#756,#756,#696,.T.);
#925=EDGE_CURVE('',#757,#757,#697,.T.);
#926=EDGE_CURVE('',#757,#758,#353,.T.);
#927=EDGE_CURVE('',#758,#758,#698,.T.);
#928=EDGE_CURVE('',#759,#759,#699,.T.);
#929=EDGE_CURVE('',#759,#760,#354,.T.);
#930=EDGE_CURVE('',#760,#760,#700,.T.);
#931=EDGE_CURVE('',#761,#761,#701,.T.);
#932=EDGE_CURVE('',#761,#762,#355,.T.);
#933=EDGE_CURVE('',#762,#762,#702,.T.);
#934=EDGE_CURVE('',#763,#764,#356,.T.);
#935=EDGE_CURVE('',#764,#765,#357,.T.);
#936=EDGE_CURVE('',#766,#765,#358,.T.);
#937=EDGE_CURVE('',#763,#766,#359,.T.);
#938=EDGE_CURVE('',#767,#763,#360,.T.);
#939=EDGE_CURVE('',#768,#766,#361,.T.);
#940=EDGE_CURVE('',#767,#768,#362,.T.);
#941=EDGE_CURVE('',#769,#767,#363,.T.);
#942=EDGE_CURVE('',#770,#768,#364,.T.);
#943=EDGE_CURVE('',#769,#770,#365,.T.);
#944=EDGE_CURVE('',#764,#769,#366,.T.);
#945=EDGE_CURVE('',#765,#770,#367,.T.);
#946=EDGE_CURVE('',#771,#772,#368,.T.);
#947=EDGE_CURVE('',#772,#773,#369,.T.);
#948=EDGE_CURVE('',#774,#773,#370,.T.);
#949=EDGE_CURVE('',#771,#774,#371,.T.);
#950=EDGE_CURVE('',#775,#771,#372,.T.);
#951=EDGE_CURVE('',#776,#774,#373,.T.);
#952=EDGE_CURVE('',#775,#776,#374,.T.);
#953=EDGE_CURVE('',#777,#775,#375,.T.);
#954=EDGE_CURVE('',#778,#776,#376,.T.);
#955=EDGE_CURVE('',#777,#778,#377,.T.);
#956=EDGE_CURVE('',#772,#777,#378,.T.);
#957=EDGE_CURVE('',#773,#778,#379,.T.);
#958=EDGE_CURVE('',#779,#779,#703,.T.);
#959=EDGE_CURVE('',#779,#780,#380,.T.);
#960=EDGE_CURVE('',#780,#780,#704,.T.);
#961=EDGE_CURVE('',#781,#781,#705,.T.);
#962=EDGE_CURVE('',#781,#782,#381,.T.);
#963=EDGE_CURVE('',#782,#782,#706,.T.);
#964=EDGE_CURVE('',#783,#783,#707,.T.);
#965=EDGE_CURVE('',#783,#784,#382,.T.);
#966=EDGE_CURVE('',#784,#784,#708,.T.);
#967=EDGE_CURVE('',#785,#785,#709,.T.);
#968=EDGE_CURVE('',#785,#786,#383,.T.);
#969=EDGE_CURVE('',#786,#786,#710,.T.);
#970=EDGE_CURVE('',#787,#787,#711,.T.);
#971=EDGE_CURVE('',#787,#788,#384,.T.);
#972=EDGE_CURVE('',#788,#788,#712,.T.);
#973=EDGE_CURVE('',#789,#790,#385,.T.);
#974=EDGE_CURVE('',#789,#791,#386,.T.);
#975=EDGE_CURVE('',#792,#791,#387,.T.);
#976=EDGE_CURVE('',#790,#792,#388,.T.);
#977=EDGE_CURVE('',#790,#793,#389,.T.);
#978=EDGE_CURVE('',#794,#792,#390,.T.);
#979=EDGE_CURVE('',#793,#794,#391,.T.);
#980=EDGE_CURVE('',#793,#795,#392,.T.);
#981=EDGE_CURVE('',#796,#794,#393,.T.);
#982=EDGE_CURVE('',#795,#796,#394,.T.);
#983=EDGE_CURVE('',#795,#797,#395,.T.);
#984=EDGE_CURVE('',#798,#796,#396,.T.);
#985=EDGE_CURVE('',#797,#798,#397,.T.);
#986=EDGE_CURVE('',#797,#799,#398,.T.);
#987=EDGE_CURVE('',#800,#798,#399,.T.);
#988=EDGE_CURVE('',#799,#800,#400,.T.);
#989=EDGE_CURVE('',#799,#801,#401,.T.);
#990=EDGE_CURVE('',#802,#800,#402,.T.);
#991=EDGE_CURVE('',#801,#802,#403,.T.);
#992=EDGE_CURVE('',#801,#803,#404,.T.);
#993=EDGE_CURVE('',#804,#802,#405,.T.);
#994=EDGE_CURVE('',#803,#804,#406,.T.);
#995=EDGE_CURVE('',#803,#805,#407,.T.);
#996=EDGE_CURVE('',#806,#804,#408,.T.);
#997=EDGE_CURVE('',#805,#806,#409,.T.);
#998=EDGE_CURVE('',#807,#805,#410,.T.);
#999=EDGE_CURVE('',#808,#806,#411,.T.);
#1000=EDGE_CURVE('',#807,#808,#412,.T.);
#1001=EDGE_CURVE('',#809,#807,#413,.T.);
#1002=EDGE_CURVE('',#810,#808,#414,.T.);
#1003=EDGE_CURVE('',#809,#810,#415,.T.);
#1004=EDGE_CURVE('',#811,#809,#416,.T.);
#1005=EDGE_CURVE('',#812,#810,#417,.T.);
#1006=EDGE_CURVE('',#811,#812,#418,.T.);
#1007=EDGE_CURVE('',#813,#811,#419,.T.);
#1008=EDGE_CURVE('',#814,#812,#420,.T.);
#1009=EDGE_CURVE('',#813,#814,#421,.T.);
#1010=EDGE_CURVE('',#815,#813,#422,.T.);
#1011=EDGE_CURVE('',#816,#814,#423,.T.);
#1012=EDGE_CURVE('',#815,#816,#424,.T.);
#1013=EDGE_CURVE('',#817,#815,#425,.T.);
#1014=EDGE_CURVE('',#818,#816,#426,.T.);
#1015=EDGE_CURVE('',#817,#818,#427,.T.);
#1016=EDGE_CURVE('',#819,#817,#428,.T.);
#1017=EDGE_CURVE('',#820,#818,#429,.T.);
#1018=EDGE_CURVE('',#819,#820,#430,.T.);
#1019=EDGE_CURVE('',#821,#819,#431,.T.);
#1020=EDGE_CURVE('',#822,#820,#432,.T.);
#1021=EDGE_CURVE('',#821,#822,#433,.T.);
#1022=EDGE_CURVE('',#823,#821,#434,.T.);
#1023=EDGE_CURVE('',#824,#822,#435,.T.);
#1024=EDGE_CURVE('',#823,#824,#436,.T.);
#1025=EDGE_CURVE('',#823,#825,#437,.T.);
#1026=EDGE_CURVE('',#826,#824,#438,.T.);
#1027=EDGE_CURVE('',#825,#826,#439,.T.);
#1028=EDGE_CURVE('',#827,#825,#440,.T.);
#1029=EDGE_CURVE('',#828,#826,#441,.T.);
#1030=EDGE_CURVE('',#827,#828,#442,.T.);
#1031=EDGE_CURVE('',#829,#827,#443,.T.);
#1032=EDGE_CURVE('',#830,#828,#444,.T.);
#1033=EDGE_CURVE('',#829,#830,#445,.T.);
#1034=EDGE_CURVE('',#831,#829,#446,.T.);
#1035=EDGE_CURVE('',#832,#830,#447,.T.);
#1036=EDGE_CURVE('',#831,#832,#448,.T.);
#1037=EDGE_CURVE('',#831,#833,#449,.T.);
#1038=EDGE_CURVE('',#834,#832,#450,.T.);
#1039=EDGE_CURVE('',#833,#834,#451,.T.);
#1040=EDGE_CURVE('',#833,#835,#452,.T.);
#1041=EDGE_CURVE('',#836,#834,#453,.T.);
#1042=EDGE_CURVE('',#835,#836,#454,.T.);
#1043=EDGE_CURVE('',#837,#835,#455,.T.);
#1044=EDGE_CURVE('',#838,#836,#456,.T.);
#1045=EDGE_CURVE('',#837,#838,#457,.T.);
#1046=EDGE_CURVE('',#839,#837,#458,.T.);
#1047=EDGE_CURVE('',#840,#838,#459,.T.);
#1048=EDGE_CURVE('',#839,#840,#460,.T.);
#1049=EDGE_CURVE('',#839,#841,#461,.T.);
#1050=EDGE_CURVE('',#842,#840,#462,.T.);
#1051=EDGE_CURVE('',#841,#842,#463,.T.);
#1052=EDGE_CURVE('',#843,#841,#713,.T.);
#1053=EDGE_CURVE('',#844,#842,#714,.T.);
#1054=EDGE_CURVE('',#843,#844,#464,.T.);
#1055=EDGE_CURVE('',#843,#845,#465,.T.);
#1056=EDGE_CURVE('',#846,#844,#466,.T.);
#1057=EDGE_CURVE('',#845,#846,#467,.T.);
#1058=EDGE_CURVE('',#845,#847,#468,.T.);
#1059=EDGE_CURVE('',#848,#846,#469,.T.);
#1060=EDGE_CURVE('',#847,#848,#470,.T.);
#1061=EDGE_CURVE('',#847,#849,#471,.T.);
#1062=EDGE_CURVE('',#850,#848,#472,.T.);
#1063=EDGE_CURVE('',#849,#850,#473,.T.);
#1064=EDGE_CURVE('',#849,#851,#474,.T.);
#1065=EDGE_CURVE('',#852,#850,#475,.T.);
#1066=EDGE_CURVE('',#851,#852,#476,.T.);
#1067=EDGE_CURVE('',#851,#853,#477,.T.);
#1068=EDGE_CURVE('',#854,#852,#478,.T.);
#1069=EDGE_CURVE('',#853,#854,#479,.T.);
#1070=EDGE_CURVE('',#853,#855,#480,.T.);
#1071=EDGE_CURVE('',#856,#854,#481,.T.);
#1072=EDGE_CURVE('',#855,#856,#482,.T.);
#1073=EDGE_CURVE('',#855,#857,#483,.T.);
#1074=EDGE_CURVE('',#858,#856,#484,.T.);
#1075=EDGE_CURVE('',#857,#858,#485,.T.);
#1076=EDGE_CURVE('',#857,#859,#486,.T.);
#1077=EDGE_CURVE('',#860,#858,#487,.T.);
#1078=EDGE_CURVE('',#859,#860,#488,.T.);
#1079=EDGE_CURVE('',#859,#861,#489,.T.);
#1080=EDGE_CURVE('',#862,#860,#490,.T.);
#1081=EDGE_CURVE('',#861,#862,#491,.T.);
#1082=EDGE_CURVE('',#863,#861,#715,.T.);
#1083=EDGE_CURVE('',#864,#862,#716,.T.);
#1084=EDGE_CURVE('',#863,#864,#492,.T.);
#1085=EDGE_CURVE('',#863,#789,#493,.T.);
#1086=EDGE_CURVE('',#791,#864,#494,.T.);
#1087=ORIENTED_EDGE('',*,*,#865,.F.);
#1088=ORIENTED_EDGE('',*,*,#866,.T.);
#1089=ORIENTED_EDGE('',*,*,#867,.T.);
#1090=ORIENTED_EDGE('',*,*,#866,.F.);
#1091=ORIENTED_EDGE('',*,*,#868,.F.);
#1092=ORIENTED_EDGE('',*,*,#869,.T.);
#1093=ORIENTED_EDGE('',*,*,#870,.T.);
#1094=ORIENTED_EDGE('',*,*,#869,.F.);
#1095=ORIENTED_EDGE('',*,*,#871,.F.);
#1096=ORIENTED_EDGE('',*,*,#872,.T.);
#1097=ORIENTED_EDGE('',*,*,#873,.T.);
#1098=ORIENTED_EDGE('',*,*,#872,.F.);
#1099=ORIENTED_EDGE('',*,*,#874,.F.);
#1100=ORIENTED_EDGE('',*,*,#875,.T.);
#1101=ORIENTED_EDGE('',*,*,#876,.T.);
#1102=ORIENTED_EDGE('',*,*,#875,.F.);
#1103=ORIENTED_EDGE('',*,*,#877,.F.);
#1104=ORIENTED_EDGE('',*,*,#878,.T.);
#1105=ORIENTED_EDGE('',*,*,#879,.T.);
#1106=ORIENTED_EDGE('',*,*,#878,.F.);
#1107=ORIENTED_EDGE('',*,*,#880,.F.);
#1108=ORIENTED_EDGE('',*,*,#881,.T.);
#1109=ORIENTED_EDGE('',*,*,#882,.T.);
#1110=ORIENTED_EDGE('',*,*,#881,.F.);
#1111=ORIENTED_EDGE('',*,*,#883,.F.);
#1112=ORIENTED_EDGE('',*,*,#884,.T.);
#1113=ORIENTED_EDGE('',*,*,#885,.T.);
#1114=ORIENTED_EDGE('',*,*,#884,.F.);
#1115=ORIENTED_EDGE('',*,*,#886,.F.);
#1116=ORIENTED_EDGE('',*,*,#887,.T.);
#1117=ORIENTED_EDGE('',*,*,#888,.T.);
#1118=ORIENTED_EDGE('',*,*,#887,.F.);
#1119=ORIENTED_EDGE('',*,*,#889,.F.);
#1120=ORIENTED_EDGE('',*,*,#890,.T.);
#1121=ORIENTED_EDGE('',*,*,#891,.T.);
#1122=ORIENTED_EDGE('',*,*,#890,.F.);
#1123=ORIENTED_EDGE('',*,*,#892,.F.);
#1124=ORIENTED_EDGE('',*,*,#893,.T.);
#1125=ORIENTED_EDGE('',*,*,#894,.T.);
#1126=ORIENTED_EDGE('',*,*,#893,.F.);
#1127=ORIENTED_EDGE('',*,*,#895,.F.);
#1128=ORIENTED_EDGE('',*,*,#896,.T.);
#1129=ORIENTED_EDGE('',*,*,#897,.T.);
#1130=ORIENTED_EDGE('',*,*,#896,.F.);
#1131=ORIENTED_EDGE('',*,*,#898,.F.);
#1132=ORIENTED_EDGE('',*,*,#899,.T.);
#1133=ORIENTED_EDGE('',*,*,#900,.T.);
#1134=ORIENTED_EDGE('',*,*,#899,.F.);
#1135=ORIENTED_EDGE('',*,*,#901,.F.);
#1136=ORIENTED_EDGE('',*,*,#902,.T.);
#1137=ORIENTED_EDGE('',*,*,#903,.T.);
#1138=ORIENTED_EDGE('',*,*,#902,.F.);
#1139=ORIENTED_EDGE('',*,*,#904,.T.);
#1140=ORIENTED_EDGE('',*,*,#905,.T.);
#1141=ORIENTED_EDGE('',*,*,#906,.F.);
#1142=ORIENTED_EDGE('',*,*,#907,.F.);
#1143=ORIENTED_EDGE('',*,*,#908,.T.);
#1144=ORIENTED_EDGE('',*,*,#907,.T.);
#1145=ORIENTED_EDGE('',*,*,#909,.F.);
#1146=ORIENTED_EDGE('',*,*,#910,.F.);
#1147=ORIENTED_EDGE('',*,*,#911,.T.);
#1148=ORIENTED_EDGE('',*,*,#910,.T.);
#1149=ORIENTED_EDGE('',*,*,#912,.F.);
#1150=ORIENTED_EDGE('',*,*,#913,.F.);
#1151=ORIENTED_EDGE('',*,*,#914,.T.);
#1152=ORIENTED_EDGE('',*,*,#913,.T.);
#1153=ORIENTED_EDGE('',*,*,#915,.F.);
#1154=ORIENTED_EDGE('',*,*,#905,.F.);
#1155=ORIENTED_EDGE('',*,*,#916,.F.);
#1156=ORIENTED_EDGE('',*,*,#917,.T.);
#1157=ORIENTED_EDGE('',*,*,#918,.T.);
#1158=ORIENTED_EDGE('',*,*,#917,.F.);
#1159=ORIENTED_EDGE('',*,*,#919,.F.);
#1160=ORIENTED_EDGE('',*,*,#920,.T.);
#1161=ORIENTED_EDGE('',*,*,#921,.T.);
#1162=ORIENTED_EDGE('',*,*,#920,.F.);
#1163=ORIENTED_EDGE('',*,*,#922,.F.);
#1164=ORIENTED_EDGE('',*,*,#923,.T.);
#1165=ORIENTED_EDGE('',*,*,#924,.T.);
#1166=ORIENTED_EDGE('',*,*,#923,.F.);
#1167=ORIENTED_EDGE('',*,*,#925,.F.);
#1168=ORIENTED_EDGE('',*,*,#926,.T.);
#1169=ORIENTED_EDGE('',*,*,#927,.T.);
#1170=ORIENTED_EDGE('',*,*,#926,.F.);
#1171=ORIENTED_EDGE('',*,*,#928,.F.);
#1172=ORIENTED_EDGE('',*,*,#929,.T.);
#1173=ORIENTED_EDGE('',*,*,#930,.T.);
#1174=ORIENTED_EDGE('',*,*,#929,.F.);
#1175=ORIENTED_EDGE('',*,*,#931,.F.);
#1176=ORIENTED_EDGE('',*,*,#932,.T.);
#1177=ORIENTED_EDGE('',*,*,#933,.T.);
#1178=ORIENTED_EDGE('',*,*,#932,.F.);
#1179=ORIENTED_EDGE('',*,*,#934,.T.);
#1180=ORIENTED_EDGE('',*,*,#935,.T.);
#1181=ORIENTED_EDGE('',*,*,#936,.F.);
#1182=ORIENTED_EDGE('',*,*,#937,.F.);
#1183=ORIENTED_EDGE('',*,*,#938,.T.);
#1184=ORIENTED_EDGE('',*,*,#937,.T.);
#1185=ORIENTED_EDGE('',*,*,#939,.F.);
#1186=ORIENTED_EDGE('',*,*,#940,.F.);
#1187=ORIENTED_EDGE('',*,*,#941,.T.);
#1188=ORIENTED_EDGE('',*,*,#940,.T.);
#1189=ORIENTED_EDGE('',*,*,#942,.F.);
#1190=ORIENTED_EDGE('',*,*,#943,.F.);
#1191=ORIENTED_EDGE('',*,*,#944,.T.);
#1192=ORIENTED_EDGE('',*,*,#943,.T.);
#1193=ORIENTED_EDGE('',*,*,#945,.F.);
#1194=ORIENTED_EDGE('',*,*,#935,.F.);
#1195=ORIENTED_EDGE('',*,*,#946,.T.);
#1196=ORIENTED_EDGE('',*,*,#947,.T.);
#1197=ORIENTED_EDGE('',*,*,#948,.F.);
#1198=ORIENTED_EDGE('',*,*,#949,.F.);
#1199=ORIENTED_EDGE('',*,*,#950,.T.);
#1200=ORIENTED_EDGE('',*,*,#949,.T.);
#1201=ORIENTED_EDGE('',*,*,#951,.F.);
#1202=ORIENTED_EDGE('',*,*,#952,.F.);
#1203=ORIENTED_EDGE('',*,*,#953,.T.);
#1204=ORIENTED_EDGE('',*,*,#952,.T.);
#1205=ORIENTED_EDGE('',*,*,#954,.F.);
#1206=ORIENTED_EDGE('',*,*,#955,.F.);
#1207=ORIENTED_EDGE('',*,*,#956,.T.);
#1208=ORIENTED_EDGE('',*,*,#955,.T.);
#1209=ORIENTED_EDGE('',*,*,#957,.F.);
#1210=ORIENTED_EDGE('',*,*,#947,.F.);
#1211=ORIENTED_EDGE('',*,*,#958,.F.);
#1212=ORIENTED_EDGE('',*,*,#959,.T.);
#1213=ORIENTED_EDGE('',*,*,#960,.T.);
#1214=ORIENTED_EDGE('',*,*,#959,.F.);
#1215=ORIENTED_EDGE('',*,*,#961,.F.);
#1216=ORIENTED_EDGE('',*,*,#962,.T.);
#1217=ORIENTED_EDGE('',*,*,#963,.T.);
#1218=ORIENTED_EDGE('',*,*,#962,.F.);
#1219=ORIENTED_EDGE('',*,*,#964,.F.);
#1220=ORIENTED_EDGE('',*,*,#965,.T.);
#1221=ORIENTED_EDGE('',*,*,#966,.T.);
#1222=ORIENTED_EDGE('',*,*,#965,.F.);
#1223=ORIENTED_EDGE('',*,*,#967,.F.);
#1224=ORIENTED_EDGE('',*,*,#968,.T.);
#1225=ORIENTED_EDGE('',*,*,#969,.T.);
#1226=ORIENTED_EDGE('',*,*,#968,.F.);
#1227=ORIENTED_EDGE('',*,*,#970,.F.);
#1228=ORIENTED_EDGE('',*,*,#971,.T.);
#1229=ORIENTED_EDGE('',*,*,#972,.T.);
#1230=ORIENTED_EDGE('',*,*,#971,.F.);
#1231=ORIENTED_EDGE('',*,*,#973,.F.);
#1232=ORIENTED_EDGE('',*,*,#974,.T.);
#1233=ORIENTED_EDGE('',*,*,#975,.F.);
#1234=ORIENTED_EDGE('',*,*,#976,.F.);
#1235=ORIENTED_EDGE('',*,*,#977,.F.);
#1236=ORIENTED_EDGE('',*,*,#976,.T.);
#1237=ORIENTED_EDGE('',*,*,#978,.F.);
#1238=ORIENTED_EDGE('',*,*,#979,.F.);
#1239=ORIENTED_EDGE('',*,*,#980,.F.);
#1240=ORIENTED_EDGE('',*,*,#979,.T.);
#1241=ORIENTED_EDGE('',*,*,#981,.F.);
#1242=ORIENTED_EDGE('',*,*,#982,.F.);
#1243=ORIENTED_EDGE('',*,*,#983,.F.);
#1244=ORIENTED_EDGE('',*,*,#982,.T.);
#1245=ORIENTED_EDGE('',*,*,#984,.F.);
#1246=ORIENTED_EDGE('',*,*,#985,.F.);
#1247=ORIENTED_EDGE('',*,*,#986,.F.);
#1248=ORIENTED_EDGE('',*,*,#985,.T.);
#1249=ORIENTED_EDGE('',*,*,#987,.F.);
#1250=ORIENTED_EDGE('',*,*,#988,.F.);
#1251=ORIENTED_EDGE('',*,*,#989,.F.);
#1252=ORIENTED_EDGE('',*,*,#988,.T.);
#1253=ORIENTED_EDGE('',*,*,#990,.F.);
#1254=ORIENTED_EDGE('',*,*,#991,.F.);
#1255=ORIENTED_EDGE('',*,*,#992,.F.);
#1256=ORIENTED_EDGE('',*,*,#991,.T.);
#1257=ORIENTED_EDGE('',*,*,#993,.F.);
#1258=ORIENTED_EDGE('',*,*,#994,.F.);
#1259=ORIENTED_EDGE('',*,*,#995,.F.);
#1260=ORIENTED_EDGE('',*,*,#994,.T.);
#1261=ORIENTED_EDGE('',*,*,#996,.F.);
#1262=ORIENTED_EDGE('',*,*,#997,.F.);
#1263=ORIENTED_EDGE('',*,*,#998,.T.);
#1264=ORIENTED_EDGE('',*,*,#997,.T.);
#1265=ORIENTED_EDGE('',*,*,#999,.F.);
#1266=ORIENTED_EDGE('',*,*,#1000,.F.);
#1267=ORIENTED_EDGE('',*,*,#1001,.T.);
#1268=ORIENTED_EDGE('',*,*,#1000,.T.);
#1269=ORIENTED_EDGE('',*,*,#1002,.F.);
#1270=ORIENTED_EDGE('',*,*,#1003,.F.);
#1271=ORIENTED_EDGE('',*,*,#1004,.T.);
#1272=ORIENTED_EDGE('',*,*,#1003,.T.);
#1273=ORIENTED_EDGE('',*,*,#1005,.F.);
#1274=ORIENTED_EDGE('',*,*,#1006,.F.);
#1275=ORIENTED_EDGE('',*,*,#1007,.T.);
#1276=ORIENTED_EDGE('',*,*,#1006,.T.);
#1277=ORIENTED_EDGE('',*,*,#1008,.F.);
#1278=ORIENTED_EDGE('',*,*,#1009,.F.);
#1279=ORIENTED_EDGE('',*,*,#1010,.T.);
#1280=ORIENTED_EDGE('',*,*,#1009,.T.);
#1281=ORIENTED_EDGE('',*,*,#1011,.F.);
#1282=ORIENTED_EDGE('',*,*,#1012,.F.);
#1283=ORIENTED_EDGE('',*,*,#1013,.T.);
#1284=ORIENTED_EDGE('',*,*,#1012,.T.);
#1285=ORIENTED_EDGE('',*,*,#1014,.F.);
#1286=ORIENTED_EDGE('',*,*,#1015,.F.);
#1287=ORIENTED_EDGE('',*,*,#1016,.T.);
#1288=ORIENTED_EDGE('',*,*,#1015,.T.);
#1289=ORIENTED_EDGE('',*,*,#1017,.F.);
#1290=ORIENTED_EDGE('',*,*,#1018,.F.);
#1291=ORIENTED_EDGE('',*,*,#1019,.T.);
#1292=ORIENTED_EDGE('',*,*,#1018,.T.);
#1293=ORIENTED_EDGE('',*,*,#1020,.F.);
#1294=ORIENTED_EDGE('',*,*,#1021,.F.);
#1295=ORIENTED_EDGE('',*,*,#1022,.T.);
#1296=ORIENTED_EDGE('',*,*,#1021,.T.);
#1297=ORIENTED_EDGE('',*,*,#1023,.F.);
#1298=ORIENTED_EDGE('',*,*,#1024,.F.);
#1299=ORIENTED_EDGE('',*,*,#1025,.F.);
#1300=ORIENTED_EDGE('',*,*,#1024,.T.);
#1301=ORIENTED_EDGE('',*,*,#1026,.F.);
#1302=ORIENTED_EDGE('',*,*,#1027,.F.);
#1303=ORIENTED_EDGE('',*,*,#1028,.T.);
#1304=ORIENTED_EDGE('',*,*,#1027,.T.);
#1305=ORIENTED_EDGE('',*,*,#1029,.F.);
#1306=ORIENTED_EDGE('',*,*,#1030,.F.);
#1307=ORIENTED_EDGE('',*,*,#1031,.T.);
#1308=ORIENTED_EDGE('',*,*,#1030,.T.);
#1309=ORIENTED_EDGE('',*,*,#1032,.F.);
#1310=ORIENTED_EDGE('',*,*,#1033,.F.);
#1311=ORIENTED_EDGE('',*,*,#1034,.T.);
#1312=ORIENTED_EDGE('',*,*,#1033,.T.);
#1313=ORIENTED_EDGE('',*,*,#1035,.F.);
#1314=ORIENTED_EDGE('',*,*,#1036,.F.);
#1315=ORIENTED_EDGE('',*,*,#1037,.F.);
#1316=ORIENTED_EDGE('',*,*,#1036,.T.);
#1317=ORIENTED_EDGE('',*,*,#1038,.F.);
#1318=ORIENTED_EDGE('',*,*,#1039,.F.);
#1319=ORIENTED_EDGE('',*,*,#1040,.F.);
#1320=ORIENTED_EDGE('',*,*,#1039,.T.);
#1321=ORIENTED_EDGE('',*,*,#1041,.F.);
#1322=ORIENTED_EDGE('',*,*,#1042,.F.);
#1323=ORIENTED_EDGE('',*,*,#1043,.T.);
#1324=ORIENTED_EDGE('',*,*,#1042,.T.);
#1325=ORIENTED_EDGE('',*,*,#1044,.F.);
#1326=ORIENTED_EDGE('',*,*,#1045,.F.);
#1327=ORIENTED_EDGE('',*,*,#1046,.T.);
#1328=ORIENTED_EDGE('',*,*,#1045,.T.);
#1329=ORIENTED_EDGE('',*,*,#1047,.F.);
#1330=ORIENTED_EDGE('',*,*,#1048,.F.);
#1331=ORIENTED_EDGE('',*,*,#1049,.F.);
#1332=ORIENTED_EDGE('',*,*,#1048,.T.);
#1333=ORIENTED_EDGE('',*,*,#1050,.F.);
#1334=ORIENTED_EDGE('',*,*,#1051,.F.);
#1335=ORIENTED_EDGE('',*,*,#1052,.T.);
#1336=ORIENTED_EDGE('',*,*,#1051,.T.);
#1337=ORIENTED_EDGE('',*,*,#1053,.F.);
#1338=ORIENTED_EDGE('',*,*,#1054,.F.);
#1339=ORIENTED_EDGE('',*,*,#1055,.F.);
#1340=ORIENTED_EDGE('',*,*,#1054,.T.);
#1341=ORIENTED_EDGE('',*,*,#1056,.F.);
#1342=ORIENTED_EDGE('',*,*,#1057,.F.);
#1343=ORIENTED_EDGE('',*,*,#1058,.F.);
#1344=ORIENTED_EDGE('',*,*,#1057,.T.);
#1345=ORIENTED_EDGE('',*,*,#1059,.F.);
#1346=ORIENTED_EDGE('',*,*,#1060,.F.);
#1347=ORIENTED_EDGE('',*,*,#1061,.F.);
#1348=ORIENTED_EDGE('',*,*,#1060,.T.);
#1349=ORIENTED_EDGE('',*,*,#1062,.F.);
#1350=ORIENTED_EDGE('',*,*,#1063,.F.);
#1351=ORIENTED_EDGE('',*,*,#1064,.F.);
#1352=ORIENTED_EDGE('',*,*,#1063,.T.);
#1353=ORIENTED_EDGE('',*,*,#1065,.F.);
#1354=ORIENTED_EDGE('',*,*,#1066,.F.);
#1355=ORIENTED_EDGE('',*,*,#1067,.F.);
#1356=ORIENTED_EDGE('',*,*,#1066,.T.);
#1357=ORIENTED_EDGE('',*,*,#1068,.F.);
#1358=ORIENTED_EDGE('',*,*,#1069,.F.);
#1359=ORIENTED_EDGE('',*,*,#1070,.F.);
#1360=ORIENTED_EDGE('',*,*,#1069,.T.);
#1361=ORIENTED_EDGE('',*,*,#1071,.F.);
#1362=ORIENTED_EDGE('',*,*,#1072,.F.);
#1363=ORIENTED_EDGE('',*,*,#1073,.F.);
#1364=ORIENTED_EDGE('',*,*,#1072,.T.);
#1365=ORIENTED_EDGE('',*,*,#1074,.F.);
#1366=ORIENTED_EDGE('',*,*,#1075,.F.);
#1367=ORIENTED_EDGE('',*,*,#1076,.F.);
#1368=ORIENTED_EDGE('',*,*,#1075,.T.);
#1369=ORIENTED_EDGE('',*,*,#1077,.F.);
#1370=ORIENTED_EDGE('',*,*,#1078,.F.);
#1371=ORIENTED_EDGE('',*,*,#1079,.F.);
#1372=ORIENTED_EDGE('',*,*,#1078,.T.);
#1373=ORIENTED_EDGE('',*,*,#1080,.F.);
#1374=ORIENTED_EDGE('',*,*,#1081,.F.);
#1375=ORIENTED_EDGE('',*,*,#1082,.T.);
#1376=ORIENTED_EDGE('',*,*,#1081,.T.);
#1377=ORIENTED_EDGE('',*,*,#1083,.F.);
#1378=ORIENTED_EDGE('',*,*,#1084,.F.);
#1379=ORIENTED_EDGE('',*,*,#1085,.F.);
#1380=ORIENTED_EDGE('',*,*,#1084,.T.);
#1381=ORIENTED_EDGE('',*,*,#1086,.F.);
#1382=ORIENTED_EDGE('',*,*,#974,.F.);
#1383=ORIENTED_EDGE('',*,*,#1086,.T.);
#1384=ORIENTED_EDGE('',*,*,#1083,.T.);
#1385=ORIENTED_EDGE('',*,*,#1080,.T.);
#1386=ORIENTED_EDGE('',*,*,#1077,.T.);
#1387=ORIENTED_EDGE('',*,*,#1074,.T.);
#1388=ORIENTED_EDGE('',*,*,#1071,.T.);
#1389=ORIENTED_EDGE('',*,*,#1068,.T.);
#1390=ORIENTED_EDGE('',*,*,#1065,.T.);
#1391=ORIENTED_EDGE('',*,*,#1062,.T.);
#1392=ORIENTED_EDGE('',*,*,#1059,.T.);
#1393=ORIENTED_EDGE('',*,*,#1056,.T.);
#1394=ORIENTED_EDGE('',*,*,#1053,.T.);
#1395=ORIENTED_EDGE('',*,*,#1050,.T.);
#1396=ORIENTED_EDGE('',*,*,#1047,.T.);
#1397=ORIENTED_EDGE('',*,*,#1044,.T.);
#1398=ORIENTED_EDGE('',*,*,#1041,.T.);
#1399=ORIENTED_EDGE('',*,*,#1038,.T.);
#1400=ORIENTED_EDGE('',*,*,#1035,.T.);
#1401=ORIENTED_EDGE('',*,*,#1032,.T.);
#1402=ORIENTED_EDGE('',*,*,#1029,.T.);
#1403=ORIENTED_EDGE('',*,*,#1026,.T.);
#1404=ORIENTED_EDGE('',*,*,#1023,.T.);
#1405=ORIENTED_EDGE('',*,*,#1020,.T.);
#1406=ORIENTED_EDGE('',*,*,#1017,.T.);
#1407=ORIENTED_EDGE('',*,*,#1014,.T.);
#1408=ORIENTED_EDGE('',*,*,#1011,.T.);
#1409=ORIENTED_EDGE('',*,*,#1008,.T.);
#1410=ORIENTED_EDGE('',*,*,#1005,.T.);
#1411=ORIENTED_EDGE('',*,*,#1002,.T.);
#1412=ORIENTED_EDGE('',*,*,#999,.T.);
#1413=ORIENTED_EDGE('',*,*,#996,.T.);
#1414=ORIENTED_EDGE('',*,*,#993,.T.);
#1415=ORIENTED_EDGE('',*,*,#990,.T.);
#1416=ORIENTED_EDGE('',*,*,#987,.T.);
#1417=ORIENTED_EDGE('',*,*,#984,.T.);
#1418=ORIENTED_EDGE('',*,*,#981,.T.);
#1419=ORIENTED_EDGE('',*,*,#978,.T.);
#1420=ORIENTED_EDGE('',*,*,#975,.T.);
#1421=ORIENTED_EDGE('',*,*,#970,.T.);
#1422=ORIENTED_EDGE('',*,*,#967,.T.);
#1423=ORIENTED_EDGE('',*,*,#964,.T.);
#1424=ORIENTED_EDGE('',*,*,#961,.T.);
#1425=ORIENTED_EDGE('',*,*,#958,.T.);
#1426=ORIENTED_EDGE('',*,*,#957,.T.);
#1427=ORIENTED_EDGE('',*,*,#954,.T.);
#1428=ORIENTED_EDGE('',*,*,#951,.T.);
#1429=ORIENTED_EDGE('',*,*,#948,.T.);
#1430=ORIENTED_EDGE('',*,*,#945,.T.);
#1431=ORIENTED_EDGE('',*,*,#942,.T.);
#1432=ORIENTED_EDGE('',*,*,#939,.T.);
#1433=ORIENTED_EDGE('',*,*,#936,.T.);
#1434=ORIENTED_EDGE('',*,*,#931,.T.);
#1435=ORIENTED_EDGE('',*,*,#928,.T.);
#1436=ORIENTED_EDGE('',*,*,#925,.T.);
#1437=ORIENTED_EDGE('',*,*,#922,.T.);
#1438=ORIENTED_EDGE('',*,*,#919,.T.);
#1439=ORIENTED_EDGE('',*,*,#916,.T.);
#1440=ORIENTED_EDGE('',*,*,#915,.T.);
#1441=ORIENTED_EDGE('',*,*,#912,.T.);
#1442=ORIENTED_EDGE('',*,*,#909,.T.);
#1443=ORIENTED_EDGE('',*,*,#906,.T.);
#1444=ORIENTED_EDGE('',*,*,#901,.T.);
#1445=ORIENTED_EDGE('',*,*,#898,.T.);
#1446=ORIENTED_EDGE('',*,*,#895,.T.);
#1447=ORIENTED_EDGE('',*,*,#892,.T.);
#1448=ORIENTED_EDGE('',*,*,#889,.T.);
#1449=ORIENTED_EDGE('',*,*,#886,.T.);
#1450=ORIENTED_EDGE('',*,*,#883,.T.);
#1451=ORIENTED_EDGE('',*,*,#880,.T.);
#1452=ORIENTED_EDGE('',*,*,#877,.T.);
#1453=ORIENTED_EDGE('',*,*,#874,.T.);
#1454=ORIENTED_EDGE('',*,*,#871,.T.);
#1455=ORIENTED_EDGE('',*,*,#868,.T.);
#1456=ORIENTED_EDGE('',*,*,#865,.T.);
#1457=ORIENTED_EDGE('',*,*,#1085,.T.);
#1458=ORIENTED_EDGE('',*,*,#973,.T.);
#1459=ORIENTED_EDGE('',*,*,#977,.T.);
#1460=ORIENTED_EDGE('',*,*,#980,.T.);
#1461=ORIENTED_EDGE('',*,*,#983,.T.);
#1462=ORIENTED_EDGE('',*,*,#986,.T.);
#1463=ORIENTED_EDGE('',*,*,#989,.T.);
#1464=ORIENTED_EDGE('',*,*,#992,.T.);
#1465=ORIENTED_EDGE('',*,*,#995,.T.);
#1466=ORIENTED_EDGE('',*,*,#998,.F.);
#1467=ORIENTED_EDGE('',*,*,#1001,.F.);
#1468=ORIENTED_EDGE('',*,*,#1004,.F.);
#1469=ORIENTED_EDGE('',*,*,#1007,.F.);
#1470=ORIENTED_EDGE('',*,*,#1010,.F.);
#1471=ORIENTED_EDGE('',*,*,#1013,.F.);
#1472=ORIENTED_EDGE('',*,*,#1016,.F.);
#1473=ORIENTED_EDGE('',*,*,#1019,.F.);
#1474=ORIENTED_EDGE('',*,*,#1022,.F.);
#1475=ORIENTED_EDGE('',*,*,#1025,.T.);
#1476=ORIENTED_EDGE('',*,*,#1028,.F.);
#1477=ORIENTED_EDGE('',*,*,#1031,.F.);
#1478=ORIENTED_EDGE('',*,*,#1034,.F.);
#1479=ORIENTED_EDGE('',*,*,#1037,.T.);
#1480=ORIENTED_EDGE('',*,*,#1040,.T.);
#1481=ORIENTED_EDGE('',*,*,#1043,.F.);
#1482=ORIENTED_EDGE('',*,*,#1046,.F.);
#1483=ORIENTED_EDGE('',*,*,#1049,.T.);
#1484=ORIENTED_EDGE('',*,*,#1052,.F.);
#1485=ORIENTED_EDGE('',*,*,#1055,.T.);
#1486=ORIENTED_EDGE('',*,*,#1058,.T.);
#1487=ORIENTED_EDGE('',*,*,#1061,.T.);
#1488=ORIENTED_EDGE('',*,*,#1064,.T.);
#1489=ORIENTED_EDGE('',*,*,#1067,.T.);
#1490=ORIENTED_EDGE('',*,*,#1070,.T.);
#1491=ORIENTED_EDGE('',*,*,#1073,.T.);
#1492=ORIENTED_EDGE('',*,*,#1076,.T.);
#1493=ORIENTED_EDGE('',*,*,#1079,.T.);
#1494=ORIENTED_EDGE('',*,*,#1082,.F.);
#1495=ORIENTED_EDGE('',*,*,#972,.F.);
#1496=ORIENTED_EDGE('',*,*,#969,.F.);
#1497=ORIENTED_EDGE('',*,*,#966,.F.);
#1498=ORIENTED_EDGE('',*,*,#963,.F.);
#1499=ORIENTED_EDGE('',*,*,#960,.F.);
#1500=ORIENTED_EDGE('',*,*,#956,.F.);
#1501=ORIENTED_EDGE('',*,*,#946,.F.);
#1502=ORIENTED_EDGE('',*,*,#950,.F.);
#1503=ORIENTED_EDGE('',*,*,#953,.F.);
#1504=ORIENTED_EDGE('',*,*,#944,.F.);
#1505=ORIENTED_EDGE('',*,*,#934,.F.);
#1506=ORIENTED_EDGE('',*,*,#938,.F.);
#1507=ORIENTED_EDGE('',*,*,#941,.F.);
#1508=ORIENTED_EDGE('',*,*,#933,.F.);
#1509=ORIENTED_EDGE('',*,*,#930,.F.);
#1510=ORIENTED_EDGE('',*,*,#927,.F.);
#1511=ORIENTED_EDGE('',*,*,#924,.F.);
#1512=ORIENTED_EDGE('',*,*,#921,.F.);
#1513=ORIENTED_EDGE('',*,*,#918,.F.);
#1514=ORIENTED_EDGE('',*,*,#914,.F.);
#1515=ORIENTED_EDGE('',*,*,#904,.F.);
#1516=ORIENTED_EDGE('',*,*,#908,.F.);
#1517=ORIENTED_EDGE('',*,*,#911,.F.);
#1518=ORIENTED_EDGE('',*,*,#903,.F.);
#1519=ORIENTED_EDGE('',*,*,#900,.F.);
#1520=ORIENTED_EDGE('',*,*,#897,.F.);
#1521=ORIENTED_EDGE('',*,*,#894,.F.);
#1522=ORIENTED_EDGE('',*,*,#891,.F.);
#1523=ORIENTED_EDGE('',*,*,#888,.F.);
#1524=ORIENTED_EDGE('',*,*,#885,.F.);
#1525=ORIENTED_EDGE('',*,*,#882,.F.);
#1526=ORIENTED_EDGE('',*,*,#879,.F.);
#1527=ORIENTED_EDGE('',*,*,#876,.F.);
#1528=ORIENTED_EDGE('',*,*,#873,.F.);
#1529=ORIENTED_EDGE('',*,*,#870,.F.);
#1530=ORIENTED_EDGE('',*,*,#867,.F.);
#1531=CYLINDRICAL_SURFACE('',#1647,2.53999999999998);
#1532=CYLINDRICAL_SURFACE('',#1650,6.98500000000003);
#1533=CYLINDRICAL_SURFACE('',#1653,2.53999999999998);
#1534=CYLINDRICAL_SURFACE('',#1656,6.98500000000003);
#1535=CYLINDRICAL_SURFACE('',#1659,6.98499999999996);
#1536=CYLINDRICAL_SURFACE('',#1662,6.98499999999996);
#1537=CYLINDRICAL_SURFACE('',#1665,2.53999999999998);
#1538=CYLINDRICAL_SURFACE('',#1668,2.53999999999998);
#1539=CYLINDRICAL_SURFACE('',#1671,2.53999999999998);
#1540=CYLINDRICAL_SURFACE('',#1674,2.53999999999998);
#1541=CYLINDRICAL_SURFACE('',#1677,2.53999999999991);
#1542=CYLINDRICAL_SURFACE('',#1680,2.53999999999991);
#1543=CYLINDRICAL_SURFACE('',#1683,2.53999999999991);
#1544=CYLINDRICAL_SURFACE('',#1690,6.98499999999996);
#1545=CYLINDRICAL_SURFACE('',#1693,5.588);
#1546=CYLINDRICAL_SURFACE('',#1696,5.58799999999998);
#1547=CYLINDRICAL_SURFACE('',#1699,5.58799999999998);
#1548=CYLINDRICAL_SURFACE('',#1702,5.588);
#1549=CYLINDRICAL_SURFACE('',#1705,25.4);
#1550=CYLINDRICAL_SURFACE('',#1716,6.98499999999996);
#1551=CYLINDRICAL_SURFACE('',#1719,6.98499999999996);
#1552=CYLINDRICAL_SURFACE('',#1722,2.53999999999991);
#1553=CYLINDRICAL_SURFACE('',#1725,2.53999999999991);
#1554=CYLINDRICAL_SURFACE('',#1728,6.9850000000001);
#1555=CYLINDRICAL_SURFACE('',#1757,50.);
#1556=CYLINDRICAL_SURFACE('',#1769,49.9999999999997);
#1557=ADVANCED_FACE('',(#119),#1531,.F.);
#1558=ADVANCED_FACE('',(#120),#1532,.F.);
#1559=ADVANCED_FACE('',(#121),#1533,.F.);
#1560=ADVANCED_FACE('',(#122),#1534,.F.);
#1561=ADVANCED_FACE('',(#123),#1535,.F.);
#1562=ADVANCED_FACE('',(#124),#1536,.F.);
#1563=ADVANCED_FACE('',(#125),#1537,.F.);
#1564=ADVANCED_FACE('',(#126),#1538,.F.);
#1565=ADVANCED_FACE('',(#127),#1539,.F.);
#1566=ADVANCED_FACE('',(#128),#1540,.F.);
#1567=ADVANCED_FACE('',(#129),#1541,.F.);
#1568=ADVANCED_FACE('',(#130),#1542,.F.);
#1569=ADVANCED_FACE('',(#131),#1543,.F.);
#1570=ADVANCED_FACE('',(#132),#69,.T.);
#1571=ADVANCED_FACE('',(#133),#70,.T.);
#1572=ADVANCED_FACE('',(#134),#71,.T.);
#1573=ADVANCED_FACE('',(#135),#72,.T.);
#1574=ADVANCED_FACE('',(#136),#1544,.F.);
#1575=ADVANCED_FACE('',(#137),#1545,.F.);
#1576=ADVANCED_FACE('',(#138),#1546,.F.);
#1577=ADVANCED_FACE('',(#139),#1547,.F.);
#1578=ADVANCED_FACE('',(#140),#1548,.F.);
#1579=ADVANCED_FACE('',(#141),#1549,.F.);
#1580=ADVANCED_FACE('',(#142),#73,.T.);
#1581=ADVANCED_FACE('',(#143),#74,.T.);
#1582=ADVANCED_FACE('',(#144),#75,.T.);
#1583=ADVANCED_FACE('',(#145),#76,.T.);
#1584=ADVANCED_FACE('',(#146),#77,.T.);
#1585=ADVANCED_FACE('',(#147),#78,.T.);
#1586=ADVANCED_FACE('',(#148),#79,.T.);
#1587=ADVANCED_FACE('',(#149),#80,.T.);
#1588=ADVANCED_FACE('',(#150),#1550,.F.);
#1589=ADVANCED_FACE('',(#151),#1551,.F.);
#1590=ADVANCED_FACE('',(#152),#1552,.F.);
#1591=ADVANCED_FACE('',(#153),#1553,.F.);
#1592=ADVANCED_FACE('',(#154),#1554,.F.);
#1593=ADVANCED_FACE('',(#155),#81,.T.);
#1594=ADVANCED_FACE('',(#156),#82,.T.);
#1595=ADVANCED_FACE('',(#157),#83,.T.);
#1596=ADVANCED_FACE('',(#158),#84,.T.);
#1597=ADVANCED_FACE('',(#159),#85,.T.);
#1598=ADVANCED_FACE('',(#160),#86,.T.);
#1599=ADVANCED_FACE('',(#161),#87,.T.);
#1600=ADVANCED_FACE('',(#162),#88,.T.);
#1601=ADVANCED_FACE('',(#163),#89,.T.);
#1602=ADVANCED_FACE('',(#164),#90,.T.);
#1603=ADVANCED_FACE('',(#165),#91,.T.);
#1604=ADVANCED_FACE('',(#166),#92,.T.);
#1605=ADVANCED_FACE('',(#167),#93,.T.);
#1606=ADVANCED_FACE('',(#168),#94,.T.);
#1607=ADVANCED_FACE('',(#169),#95,.T.);
#1608=ADVANCED_FACE('',(#170),#96,.T.);
#1609=ADVANCED_FACE('',(#171),#97,.T.);
#1610=ADVANCED_FACE('',(#172),#98,.T.);
#1611=ADVANCED_FACE('',(#173),#99,.T.);
#1612=ADVANCED_FACE('',(#174),#100,.T.);
#1613=ADVANCED_FACE('',(#175),#101,.T.);
#1614=ADVANCED_FACE('',(#176),#102,.T.);
#1615=ADVANCED_FACE('',(#177),#103,.T.);
#1616=ADVANCED_FACE('',(#178),#104,.T.);
#1617=ADVANCED_FACE('',(#179),#105,.T.);
#1618=ADVANCED_FACE('',(#180),#106,.T.);
#1619=ADVANCED_FACE('',(#181),#1555,.T.);
#1620=ADVANCED_FACE('',(#182),#107,.T.);
#1621=ADVANCED_FACE('',(#183),#108,.T.);
#1622=ADVANCED_FACE('',(#184),#109,.T.);
#1623=ADVANCED_FACE('',(#185),#110,.T.);
#1624=ADVANCED_FACE('',(#186),#111,.T.);
#1625=ADVANCED_FACE('',(#187),#112,.T.);
#1626=ADVANCED_FACE('',(#188),#113,.T.);
#1627=ADVANCED_FACE('',(#189),#114,.T.);
#1628=ADVANCED_FACE('',(#190),#115,.T.);
#1629=ADVANCED_FACE('',(#191),#1556,.T.);
#1630=ADVANCED_FACE('',(#192),#116,.T.);
#1631=ADVANCED_FACE('',(#193,#15,#16,#17,#18,#19,#20,#21,#22,#23,#24,#25,
#26,#27,#28,#29,#30,#31,#32,#33,#34,#35,#36,#37,#38,#39,#40,#41),#117,.T.);
#1632=ADVANCED_FACE('',(#194,#42,#43,#44,#45,#46,#47,#48,#49,#50,#51,#52,
#53,#54,#55,#56,#57,#58,#59,#60,#61,#62,#63,#64,#65,#66,#67,#68),#118,.F.);
#1633=CLOSED_SHELL('',(#1557,#1558,#1559,#1560,#1561,#1562,#1563,#1564,
#1565,#1566,#1567,#1568,#1569,#1570,#1571,#1572,#1573,#1574,#1575,#1576,
#1577,#1578,#1579,#1580,#1581,#1582,#1583,#1584,#1585,#1586,#1587,#1588,
#1589,#1590,#1591,#1592,#1593,#1594,#1595,#1596,#1597,#1598,#1599,#1600,
#1601,#1602,#1603,#1604,#1605,#1606,#1607,#1608,#1609,#1610,#1611,#1612,
#1613,#1614,#1615,#1616,#1617,#1618,#1619,#1620,#1621,#1622,#1623,#1624,
#1625,#1626,#1627,#1628,#1629,#1630,#1631,#1632));
#1634=DERIVED_UNIT_ELEMENT(#1636,1.);
#1635=DERIVED_UNIT_ELEMENT(#2655,-3.);
#1636=(
MASS_UNIT()
NAMED_UNIT(*)
SI_UNIT(.KILO.,.GRAM.)
);
#1637=DERIVED_UNIT((#1634,#1635));
#1638=MEASURE_REPRESENTATION_ITEM('density measure',
POSITIVE_RATIO_MEASURE(7850.),#1637);
#1639=PROPERTY_DEFINITION_REPRESENTATION(#1644,#1641);
#1640=PROPERTY_DEFINITION_REPRESENTATION(#1645,#1642);
#1641=REPRESENTATION('material name',(#1643),#2652);
#1642=REPRESENTATION('density',(#1638),#2652);
#1643=DESCRIPTIVE_REPRESENTATION_ITEM('Steel','Steel');
#1644=PROPERTY_DEFINITION('material property','material name',#2662);
#1645=PROPERTY_DEFINITION('material property','density of part',#2662);
#1646=AXIS2_PLACEMENT_3D('placement',#2203,#1775,#1776);
#1647=AXIS2_PLACEMENT_3D('',#2204,#1777,#1778);
#1648=AXIS2_PLACEMENT_3D('',#2206,#1779,#1780);
#1649=AXIS2_PLACEMENT_3D('',#2209,#1782,#1783);
#1650=AXIS2_PLACEMENT_3D('',#2210,#1784,#1785);
#1651=AXIS2_PLACEMENT_3D('',#2212,#1786,#1787);
#1652=AXIS2_PLACEMENT_3D('',#2215,#1789,#1790);
#1653=AXIS2_PLACEMENT_3D('',#2216,#1791,#1792);
#1654=AXIS2_PLACEMENT_3D('',#2218,#1793,#1794);
#1655=AXIS2_PLACEMENT_3D('',#2221,#1796,#1797);
#1656=AXIS2_PLACEMENT_3D('',#2222,#1798,#1799);
#1657=AXIS2_PLACEMENT_3D('',#2224,#1800,#1801);
#1658=AXIS2_PLACEMENT_3D('',#2227,#1803,#1804);
#1659=AXIS2_PLACEMENT_3D('',#2228,#1805,#1806);
#1660=AXIS2_PLACEMENT_3D('',#2230,#1807,#1808);
#1661=AXIS2_PLACEMENT_3D('',#2233,#1810,#1811);
#1662=AXIS2_PLACEMENT_3D('',#2234,#1812,#1813);
#1663=AXIS2_PLACEMENT_3D('',#2236,#1814,#1815);
#1664=AXIS2_PLACEMENT_3D('',#2239,#1817,#1818);
#1665=AXIS2_PLACEMENT_3D('',#2240,#1819,#1820);
#1666=AXIS2_PLACEMENT_3D('',#2242,#1821,#1822);
#1667=AXIS2_PLACEMENT_3D('',#2245,#1824,#1825);
#1668=AXIS2_PLACEMENT_3D('',#2246,#1826,#1827);
#1669=AXIS2_PLACEMENT_3D('',#2248,#1828,#1829);
#1670=AXIS2_PLACEMENT_3D('',#2251,#1831,#1832);
#1671=AXIS2_PLACEMENT_3D('',#2252,#1833,#1834);
#1672=AXIS2_PLACEMENT_3D('',#2254,#1835,#1836);
#1673=AXIS2_PLACEMENT_3D('',#2257,#1838,#1839);
#1674=AXIS2_PLACEMENT_3D('',#2258,#1840,#1841);
#1675=AXIS2_PLACEMENT_3D('',#2260,#1842,#1843);
#1676=AXIS2_PLACEMENT_3D('',#2263,#1845,#1846);
#1677=AXIS2_PLACEMENT_3D('',#2264,#1847,#1848);
#1678=AXIS2_PLACEMENT_3D('',#2266,#1849,#1850);
#1679=AXIS2_PLACEMENT_3D('',#2269,#1852,#1853);
#1680=AXIS2_PLACEMENT_3D('',#2270,#1854,#1855);
#1681=AXIS2_PLACEMENT_3D('',#2272,#1856,#1857);
#1682=AXIS2_PLACEMENT_3D('',#2275,#1859,#1860);
#1683=AXIS2_PLACEMENT_3D('',#2276,#1861,#1862);
#1684=AXIS2_PLACEMENT_3D('',#2278,#1863,#1864);
#1685=AXIS2_PLACEMENT_3D('',#2281,#1866,#1867);
#1686=AXIS2_PLACEMENT_3D('',#2282,#1868,#1869);
#1687=AXIS2_PLACEMENT_3D('',#2291,#1874,#1875);
#1688=AXIS2_PLACEMENT_3D('',#2297,#1879,#1880);
#1689=AXIS2_PLACEMENT_3D('',#2303,#1884,#1885);
#1690=AXIS2_PLACEMENT_3D('',#2306,#1888,#1889);
#1691=AXIS2_PLACEMENT_3D('',#2308,#1890,#1891);
#1692=AXIS2_PLACEMENT_3D('',#2311,#1893,#1894);
#1693=AXIS2_PLACEMENT_3D('',#2312,#1895,#1896);
#1694=AXIS2_PLACEMENT_3D('',#2314,#1897,#1898);
#1695=AXIS2_PLACEMENT_3D('',#2317,#1900,#1901);
#1696=AXIS2_PLACEMENT_3D('',#2318,#1902,#1903);
#1697=AXIS2_PLACEMENT_3D('',#2320,#1904,#1905);
#1698=AXIS2_PLACEMENT_3D('',#2323,#1907,#1908);
#1699=AXIS2_PLACEMENT_3D('',#2324,#1909,#1910);
#1700=AXIS2_PLACEMENT_3D('',#2326,#1911,#1912);
#1701=AXIS2_PLACEMENT_3D('',#2329,#1914,#1915);
#1702=AXIS2_PLACEMENT_3D('',#2330,#1916,#1917);
#1703=AXIS2_PLACEMENT_3D('',#2332,#1918,#1919);
#1704=AXIS2_PLACEMENT_3D('',#2335,#1921,#1922);
#1705=AXIS2_PLACEMENT_3D('',#2336,#1923,#1924);
#1706=AXIS2_PLACEMENT_3D('',#2338,#1925,#1926);
#1707=AXIS2_PLACEMENT_3D('',#2341,#1928,#1929);
#1708=AXIS2_PLACEMENT_3D('',#2342,#1930,#1931);
#1709=AXIS2_PLACEMENT_3D('',#2351,#1936,#1937);
#1710=AXIS2_PLACEMENT_3D('',#2357,#1941,#1942);
#1711=AXIS2_PLACEMENT_3D('',#2363,#1946,#1947);
#1712=AXIS2_PLACEMENT_3D('',#2366,#1950,#1951);
#1713=AXIS2_PLACEMENT_3D('',#2375,#1956,#1957);
#1714=AXIS2_PLACEMENT_3D('',#2381,#1961,#1962);
#1715=AXIS2_PLACEMENT_3D('',#2387,#1966,#1967);
#1716=AXIS2_PLACEMENT_3D('',#2390,#1970,#1971);
#1717=AXIS2_PLACEMENT_3D('',#2392,#1972,#1973);
#1718=AXIS2_PLACEMENT_3D('',#2395,#1975,#1976);
#1719=AXIS2_PLACEMENT_3D('',#2396,#1977,#1978);
#1720=AXIS2_PLACEMENT_3D('',#2398,#1979,#1980);
#1721=AXIS2_PLACEMENT_3D('',#2401,#1982,#1983);
#1722=AXIS2_PLACEMENT_3D('',#2402,#1984,#1985);
#1723=AXIS2_PLACEMENT_3D('',#2404,#1986,#1987);
#1724=AXIS2_PLACEMENT_3D('',#2407,#1989,#1990);
#1725=AXIS2_PLACEMENT_3D('',#2408,#1991,#1992);
#1726=AXIS2_PLACEMENT_3D('',#2410,#1993,#1994);
#1727=AXIS2_PLACEMENT_3D('',#2413,#1996,#1997);
#1728=AXIS2_PLACEMENT_3D('',#2414,#1998,#1999);
#1729=AXIS2_PLACEMENT_3D('',#2416,#2000,#2001);
#1730=AXIS2_PLACEMENT_3D('',#2419,#2003,#2004);
#1731=AXIS2_PLACEMENT_3D('',#2420,#2005,#2006);
#1732=AXIS2_PLACEMENT_3D('',#2429,#2011,#2012);
#1733=AXIS2_PLACEMENT_3D('',#2435,#2016,#2017);
#1734=AXIS2_PLACEMENT_3D('',#2441,#2021,#2022);
#1735=AXIS2_PLACEMENT_3D('',#2447,#2026,#2027);
#1736=AXIS2_PLACEMENT_3D('',#2453,#2031,#2032);
#1737=AXIS2_PLACEMENT_3D('',#2459,#2036,#2037);
#1738=AXIS2_PLACEMENT_3D('',#2465,#2041,#2042);
#1739=AXIS2_PLACEMENT_3D('',#2471,#2046,#2047);
#1740=AXIS2_PLACEMENT_3D('',#2477,#2051,#2052);
#1741=AXIS2_PLACEMENT_3D('',#2483,#2056,#2057);
#1742=AXIS2_PLACEMENT_3D('',#2489,#2061,#2062);
#1743=AXIS2_PLACEMENT_3D('',#2495,#2066,#2067);
#1744=AXIS2_PLACEMENT_3D('',#2501,#2071,#2072);
#1745=AXIS2_PLACEMENT_3D('',#2507,#2076,#2077);
#1746=AXIS2_PLACEMENT_3D('',#2513,#2081,#2082);
#1747=AXIS2_PLACEMENT_3D('',#2519,#2086,#2087);
#1748=AXIS2_PLACEMENT_3D('',#2525,#2091,#2092);
#1749=AXIS2_PLACEMENT_3D('',#2531,#2096,#2097);
#1750=AXIS2_PLACEMENT_3D('',#2537,#2101,#2102);
#1751=AXIS2_PLACEMENT_3D('',#2543,#2106,#2107);
#1752=AXIS2_PLACEMENT_3D('',#2549,#2111,#2112);
#1753=AXIS2_PLACEMENT_3D('',#2555,#2116,#2117);
#1754=AXIS2_PLACEMENT_3D('',#2561,#2121,#2122);
#1755=AXIS2_PLACEMENT_3D('',#2567,#2126,#2127);
#1756=AXIS2_PLACEMENT_3D('',#2573,#2131,#2132);
#1757=AXIS2_PLACEMENT_3D('',#2579,#2136,#2137);
#1758=AXIS2_PLACEMENT_3D('',#2581,#2138,#2139);
#1759=AXIS2_PLACEMENT_3D('',#2583,#2140,#2141);
#1760=AXIS2_PLACEMENT_3D('',#2585,#2143,#2144);
#1761=AXIS2_PLACEMENT_3D('',#2591,#2148,#2149);
#1762=AXIS2_PLACEMENT_3D('',#2597,#2153,#2154);
#1763=AXIS2_PLACEMENT_3D('',#2603,#2158,#2159);
#1764=AXIS2_PLACEMENT_3D('',#2609,#2163,#2164);
#1765=AXIS2_PLACEMENT_3D('',#2615,#2168,#2169);
#1766=AXIS2_PLACEMENT_3D('',#2621,#2173,#2174);
#1767=AXIS2_PLACEMENT_3D('',#2627,#2178,#2179);
#1768=AXIS2_PLACEMENT_3D('',#2633,#2183,#2184);
#1769=AXIS2_PLACEMENT_3D('',#2639,#2188,#2189);
#1770=AXIS2_PLACEMENT_3D('',#2641,#2190,#2191);
#1771=AXIS2_PLACEMENT_3D('',#2643,#2192,#2193);
#1772=AXIS2_PLACEMENT_3D('',#2645,#2195,#2196);
#1773=AXIS2_PLACEMENT_3D('',#2648,#2199,#2200);
#1774=AXIS2_PLACEMENT_3D('',#2649,#2201,#2202);
#1775=DIRECTION('axis',(0.,0.,1.));
#1776=DIRECTION('refdir',(1.,0.,0.));
#1777=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#1778=DIRECTION('ref_axis',(1.,0.,0.));
#1779=DIRECTION('center_axis',(0.,0.,-1.));
#1780=DIRECTION('ref_axis',(1.,0.,0.));
#1781=DIRECTION('',(0.,-2.79741234551221E-15,-1.));
#1782=DIRECTION('center_axis',(0.,0.,-1.));
#1783=DIRECTION('ref_axis',(1.,0.,0.));
#1784=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#1785=DIRECTION('ref_axis',(1.,0.,0.));
#1786=DIRECTION('center_axis',(0.,0.,-1.));
#1787=DIRECTION('ref_axis',(1.,0.,0.));
#1788=DIRECTION('',(0.,-2.79741234551221E-15,-1.));
#1789=DIRECTION('center_axis',(0.,0.,-1.));
#1790=DIRECTION('ref_axis',(1.,0.,0.));
#1791=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#1792=DIRECTION('ref_axis',(1.,0.,0.));
#1793=DIRECTION('center_axis',(0.,0.,-1.));
#1794=DIRECTION('ref_axis',(1.,0.,0.));
#1795=DIRECTION('',(0.,-2.79741234551221E-15,-1.));
#1796=DIRECTION('center_axis',(0.,0.,-1.));
#1797=DIRECTION('ref_axis',(1.,0.,0.));
#1798=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#1799=DIRECTION('ref_axis',(1.,0.,0.));
#1800=DIRECTION('center_axis',(0.,0.,-1.));
#1801=DIRECTION('ref_axis',(1.,0.,0.));
#1802=DIRECTION('',(0.,-2.79741234551221E-15,-1.));
#1803=DIRECTION('center_axis',(0.,0.,-1.));
#1804=DIRECTION('ref_axis',(1.,0.,0.));
#1805=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#1806=DIRECTION('ref_axis',(1.,0.,0.));
#1807=DIRECTION('center_axis',(0.,0.,-1.));
#1808=DIRECTION('ref_axis',(1.,0.,0.));
#1809=DIRECTION('',(0.,-2.79741234551221E-15,-1.));
#1810=DIRECTION('center_axis',(0.,0.,-1.));
#1811=DIRECTION('ref_axis',(1.,0.,0.));
#1812=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#1813=DIRECTION('ref_axis',(1.,0.,0.));
#1814=DIRECTION('center_axis',(0.,0.,-1.));
#1815=DIRECTION('ref_axis',(1.,0.,0.));
#1816=DIRECTION('',(0.,-2.79741234551221E-15,-1.));
#1817=DIRECTION('center_axis',(0.,0.,-1.));
#1818=DIRECTION('ref_axis',(1.,0.,0.));
#1819=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#1820=DIRECTION('ref_axis',(1.,0.,0.));
#1821=DIRECTION('center_axis',(0.,0.,-1.));
#1822=DIRECTION('ref_axis',(1.,0.,0.));
#1823=DIRECTION('',(0.,-2.79741234551221E-15,-1.));
#1824=DIRECTION('center_axis',(0.,0.,-1.));
#1825=DIRECTION('ref_axis',(1.,0.,0.));
#1826=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#1827=DIRECTION('ref_axis',(1.,0.,0.));
#1828=DIRECTION('center_axis',(0.,0.,-1.));
#1829=DIRECTION('ref_axis',(1.,0.,0.));
#1830=DIRECTION('',(0.,-2.79741234551221E-15,-1.));
#1831=DIRECTION('center_axis',(0.,0.,-1.));
#1832=DIRECTION('ref_axis',(1.,0.,0.));
#1833=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#1834=DIRECTION('ref_axis',(1.,0.,0.));
#1835=DIRECTION('center_axis',(0.,0.,-1.));
#1836=DIRECTION('ref_axis',(1.,0.,0.));
#1837=DIRECTION('',(0.,-2.79741234551221E-15,-1.));
#1838=DIRECTION('center_axis',(0.,0.,-1.));
#1839=DIRECTION('ref_axis',(1.,0.,0.));
#1840=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#1841=DIRECTION('ref_axis',(1.,0.,0.));
#1842=DIRECTION('center_axis',(0.,0.,-1.));
#1843=DIRECTION('ref_axis',(1.,0.,0.));
#1844=DIRECTION('',(0.,-2.79741234551221E-15,-1.));
#1845=DIRECTION('center_axis',(0.,0.,-1.));
#1846=DIRECTION('ref_axis',(1.,0.,0.));
#1847=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#1848=DIRECTION('ref_axis',(1.,0.,0.));
#1849=DIRECTION('center_axis',(0.,0.,-1.));
#1850=DIRECTION('ref_axis',(1.,0.,0.));
#1851=DIRECTION('',(0.,-2.79741234551221E-15,-1.));
#1852=DIRECTION('center_axis',(0.,0.,-1.));
#1853=DIRECTION('ref_axis',(1.,0.,0.));
#1854=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#1855=DIRECTION('ref_axis',(1.,0.,0.));
#1856=DIRECTION('center_axis',(0.,0.,-1.));
#1857=DIRECTION('ref_axis',(1.,0.,0.));
#1858=DIRECTION('',(0.,-2.79741234551221E-15,-1.));
#1859=DIRECTION('center_axis',(0.,0.,-1.));
#1860=DIRECTION('ref_axis',(1.,0.,0.));
#1861=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#1862=DIRECTION('ref_axis',(1.,0.,0.));
#1863=DIRECTION('center_axis',(0.,0.,-1.));
#1864=DIRECTION('ref_axis',(1.,0.,0.));
#1865=DIRECTION('',(0.,-2.79741234551221E-15,-1.));
#1866=DIRECTION('center_axis',(0.,0.,-1.));
#1867=DIRECTION('ref_axis',(1.,0.,0.));
#1868=DIRECTION('center_axis',(-1.,0.,0.));
#1869=DIRECTION('ref_axis',(0.,-1.,0.));
#1870=DIRECTION('',(0.,-1.,0.));
#1871=DIRECTION('',(0.,2.79741234551221E-15,1.));
#1872=DIRECTION('',(0.,-1.,0.));
#1873=DIRECTION('',(0.,2.79741234551221E-15,1.));
#1874=DIRECTION('center_axis',(-3.04639228612205E-31,-1.,2.79741234551221E-15));
#1875=DIRECTION('ref_axis',(1.,0.,1.08900366119041E-16));
#1876=DIRECTION('',(1.,0.,1.08900366119041E-16));
#1877=DIRECTION('',(1.,0.,1.08900366119041E-16));
#1878=DIRECTION('',(0.,2.79741234551221E-15,1.));
#1879=DIRECTION('center_axis',(1.,0.,0.));
#1880=DIRECTION('ref_axis',(0.,1.,-6.12323399573677E-17));
#1881=DIRECTION('',(0.,1.,-6.12323399573677E-17));
#1882=DIRECTION('',(0.,1.,-6.12323399573677E-17));
#1883=DIRECTION('',(0.,2.79741234551221E-15,1.));
#1884=DIRECTION('center_axis',(5.08497154419331E-17,1.,-2.79741234551221E-15));
#1885=DIRECTION('ref_axis',(-1.,5.0849715441933E-17,-6.12323399573677E-17));
#1886=DIRECTION('',(-1.,5.0849715441933E-17,-6.12323399573677E-17));
#1887=DIRECTION('',(-1.,5.0849715441933E-17,-6.12323399573677E-17));
#1888=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#1889=DIRECTION('ref_axis',(1.,0.,0.));
#1890=DIRECTION('center_axis',(0.,0.,-1.));
#1891=DIRECTION('ref_axis',(1.,0.,0.));
#1892=DIRECTION('',(0.,-2.79741234551221E-15,-1.));
#1893=DIRECTION('center_axis',(0.,0.,-1.));
#1894=DIRECTION('ref_axis',(1.,0.,0.));
#1895=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#1896=DIRECTION('ref_axis',(1.,0.,0.));
#1897=DIRECTION('center_axis',(0.,0.,-1.));
#1898=DIRECTION('ref_axis',(1.,0.,0.));
#1899=DIRECTION('',(0.,-2.79741234551221E-15,-1.));
#1900=DIRECTION('center_axis',(0.,0.,-1.));
#1901=DIRECTION('ref_axis',(1.,0.,0.));
#1902=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#1903=DIRECTION('ref_axis',(1.,0.,0.));
#1904=DIRECTION('center_axis',(0.,0.,-1.));
#1905=DIRECTION('ref_axis',(1.,0.,0.));
#1906=DIRECTION('',(0.,-2.79741234551221E-15,-1.));
#1907=DIRECTION('center_axis',(0.,0.,-1.));
#1908=DIRECTION('ref_axis',(1.,0.,0.));
#1909=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#1910=DIRECTION('ref_axis',(1.,0.,0.));
#1911=DIRECTION('center_axis',(0.,0.,-1.));
#1912=DIRECTION('ref_axis',(1.,0.,0.));
#1913=DIRECTION('',(0.,-2.79741234551221E-15,-1.));
#1914=DIRECTION('center_axis',(0.,0.,-1.));
#1915=DIRECTION('ref_axis',(1.,0.,0.));
#1916=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#1917=DIRECTION('ref_axis',(1.,0.,0.));
#1918=DIRECTION('center_axis',(0.,0.,-1.));
#1919=DIRECTION('ref_axis',(1.,0.,0.));
#1920=DIRECTION('',(0.,-2.79741234551221E-15,-1.));
#1921=DIRECTION('center_axis',(0.,0.,-1.));
#1922=DIRECTION('ref_axis',(1.,0.,0.));
#1923=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#1924=DIRECTION('ref_axis',(1.,0.,0.));
#1925=DIRECTION('center_axis',(0.,0.,-1.));
#1926=DIRECTION('ref_axis',(1.,0.,0.));
#1927=DIRECTION('',(0.,-2.79741234551221E-15,-1.));
#1928=DIRECTION('center_axis',(0.,0.,-1.));
#1929=DIRECTION('ref_axis',(1.,0.,0.));
#1930=DIRECTION('center_axis',(3.42584207482726E-31,1.,-2.79741234551221E-15));
#1931=DIRECTION('ref_axis',(-1.,0.,-1.22464679914751E-16));
#1932=DIRECTION('',(-1.,0.,-1.22464679914751E-16));
#1933=DIRECTION('',(0.,2.79741234551221E-15,1.));
#1934=DIRECTION('',(-1.,0.,-1.22464679914751E-16));
#1935=DIRECTION('',(0.,2.79741234551221E-15,1.));
#1936=DIRECTION('center_axis',(-1.,0.,0.));
#1937=DIRECTION('ref_axis',(0.,-1.,1.22464679914738E-16));
#1938=DIRECTION('',(0.,-1.,1.22464679914738E-16));
#1939=DIRECTION('',(0.,-1.,1.22464679914738E-16));
#1940=DIRECTION('',(0.,2.79741234551221E-15,1.));
#1941=DIRECTION('center_axis',(-7.404541253356E-30,-1.,2.79741234551221E-15));
#1942=DIRECTION('ref_axis',(1.,0.,2.64692520758867E-15));
#1943=DIRECTION('',(1.,0.,2.64692520758867E-15));
#1944=DIRECTION('',(1.,0.,2.64692520758867E-15));
#1945=DIRECTION('',(0.,2.79741234551221E-15,1.));
#1946=DIRECTION('center_axis',(1.,0.,0.));
#1947=DIRECTION('ref_axis',(0.,1.,-7.35197817699671E-16));
#1948=DIRECTION('',(0.,1.,-7.35197817699671E-16));
#1949=DIRECTION('',(0.,1.,-7.35197817699671E-16));
#1950=DIRECTION('center_axis',(1.,0.,0.));
#1951=DIRECTION('ref_axis',(0.,1.,0.));
#1952=DIRECTION('',(0.,1.,0.));
#1953=DIRECTION('',(0.,2.79741234551221E-15,1.));
#1954=DIRECTION('',(0.,1.,0.));
#1955=DIRECTION('',(0.,2.79741234551221E-15,1.));
#1956=DIRECTION('center_axis',(-7.06195704587334E-30,1.,-2.79741234551221E-15));
#1957=DIRECTION('ref_axis',(-1.,0.,2.52446052767394E-15));
#1958=DIRECTION('',(-1.,0.,2.52446052767394E-15));
#1959=DIRECTION('',(-1.,0.,2.52446052767394E-15));
#1960=DIRECTION('',(0.,2.79741234551221E-15,1.));
#1961=DIRECTION('center_axis',(-1.,0.,0.));
#1962=DIRECTION('ref_axis',(0.,-1.,-6.12733137784939E-16));
#1963=DIRECTION('',(0.,-1.,-6.12733137784939E-16));
#1964=DIRECTION('',(0.,-1.,-6.12733137784939E-16));
#1965=DIRECTION('',(0.,2.79741234551221E-15,1.));
#1966=DIRECTION('center_axis',(0.,-1.,2.79741234551221E-15));
#1967=DIRECTION('ref_axis',(1.,0.,0.));
#1968=DIRECTION('',(1.,0.,0.));
#1969=DIRECTION('',(1.,0.,0.));
#1970=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#1971=DIRECTION('ref_axis',(1.,0.,0.));
#1972=DIRECTION('center_axis',(0.,0.,-1.));
#1973=DIRECTION('ref_axis',(1.,0.,0.));
#1974=DIRECTION('',(0.,-2.79741234551221E-15,-1.));
#1975=DIRECTION('center_axis',(0.,0.,-1.));
#1976=DIRECTION('ref_axis',(1.,0.,0.));
#1977=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#1978=DIRECTION('ref_axis',(1.,0.,0.));
#1979=DIRECTION('center_axis',(0.,0.,-1.));
#1980=DIRECTION('ref_axis',(1.,0.,0.));
#1981=DIRECTION('',(0.,-2.79741234551221E-15,-1.));
#1982=DIRECTION('center_axis',(0.,0.,-1.));
#1983=DIRECTION('ref_axis',(1.,0.,0.));
#1984=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#1985=DIRECTION('ref_axis',(1.,0.,0.));
#1986=DIRECTION('center_axis',(0.,0.,-1.));
#1987=DIRECTION('ref_axis',(1.,0.,0.));
#1988=DIRECTION('',(0.,-2.79741234551221E-15,-1.));
#1989=DIRECTION('center_axis',(0.,0.,-1.));
#1990=DIRECTION('ref_axis',(1.,0.,0.));
#1991=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#1992=DIRECTION('ref_axis',(1.,0.,0.));
#1993=DIRECTION('center_axis',(0.,0.,-1.));
#1994=DIRECTION('ref_axis',(1.,0.,0.));
#1995=DIRECTION('',(0.,-2.79741234551221E-15,-1.));
#1996=DIRECTION('center_axis',(0.,0.,-1.));
#1997=DIRECTION('ref_axis',(1.,0.,0.));
#1998=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#1999=DIRECTION('ref_axis',(1.,0.,0.));
#2000=DIRECTION('center_axis',(0.,0.,-1.));
#2001=DIRECTION('ref_axis',(1.,0.,0.));
#2002=DIRECTION('',(0.,-2.79741234551221E-15,-1.));
#2003=DIRECTION('center_axis',(0.,0.,-1.));
#2004=DIRECTION('ref_axis',(1.,0.,0.));
#2005=DIRECTION('center_axis',(-3.62822521414441E-28,-1.,2.79741234551221E-15));
#2006=DIRECTION('ref_axis',(1.,0.,1.29699335171844E-13));
#2007=DIRECTION('',(-1.,0.,-1.29699335171844E-13));
#2008=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2009=DIRECTION('',(1.,0.,1.29699335171844E-13));
#2010=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2011=DIRECTION('center_axis',(1.,0.,0.));
#2012=DIRECTION('ref_axis',(0.,1.,-1.54923764979533E-15));
#2013=DIRECTION('',(0.,-1.,1.54923764979533E-15));
#2014=DIRECTION('',(0.,1.,-1.54923764979533E-15));
#2015=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2016=DIRECTION('center_axis',(3.42584207482724E-31,1.,-2.79741234551221E-15));
#2017=DIRECTION('ref_axis',(-1.,0.,-1.22464679914751E-16));
#2018=DIRECTION('',(1.,0.,1.22464679914751E-16));
#2019=DIRECTION('',(-1.,0.,-1.22464679914751E-16));
#2020=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2021=DIRECTION('center_axis',(1.,0.,0.));
#2022=DIRECTION('ref_axis',(0.,1.,7.05998909643647E-15));
#2023=DIRECTION('',(0.,-1.,-7.05998909643647E-15));
#2024=DIRECTION('',(0.,1.,7.05998909643647E-15));
#2025=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2026=DIRECTION('center_axis',(0.,-1.,2.79741234551221E-15));
#2027=DIRECTION('ref_axis',(1.,0.,0.));
#2028=DIRECTION('',(-1.,0.,0.));
#2029=DIRECTION('',(1.,0.,0.));
#2030=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2031=DIRECTION('center_axis',(1.,0.,0.));
#2032=DIRECTION('ref_axis',(0.,1.,-3.05433681033789E-16));
#2033=DIRECTION('',(0.,-1.,3.05433681033789E-16));
#2034=DIRECTION('',(0.,1.,-3.05433681033789E-16));
#2035=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2036=DIRECTION('center_axis',(-1.28855607355985E-31,1.,-2.79741234551221E-15));
#2037=DIRECTION('ref_axis',(-1.,0.,4.60624289310453E-17));
#2038=DIRECTION('',(1.,0.,-4.60624289310453E-17));
#2039=DIRECTION('',(-1.,0.,4.60624289310453E-17));
#2040=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2041=DIRECTION('center_axis',(1.,6.35491222395227E-15,-1.77773099099305E-29));
#2042=DIRECTION('ref_axis',(-6.35491222395227E-15,1.,-9.54063733081312E-15));
#2043=DIRECTION('',(6.35491222395227E-15,-1.,9.54063733081312E-15));
#2044=DIRECTION('',(-6.35491222395227E-15,1.,-9.54063733081312E-15));
#2045=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2046=DIRECTION('center_axis',(-7.92557894807454E-18,-1.,2.79741234551221E-15));
#2047=DIRECTION('ref_axis',(1.,-7.92557894807401E-18,1.89209022394927E-16));
#2048=DIRECTION('',(1.,-7.92557894807401E-18,1.89209022394927E-16));
#2049=DIRECTION('',(1.,-7.92557894807401E-18,1.89209022394927E-16));
#2050=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2051=DIRECTION('center_axis',(1.,0.,0.));
#2052=DIRECTION('ref_axis',(0.,1.,9.95181718137747E-15));
#2053=DIRECTION('',(0.,1.,9.95181718137747E-15));
#2054=DIRECTION('',(0.,1.,9.95181718137747E-15));
#2055=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2056=DIRECTION('center_axis',(1.27309421063966E-16,-1.,2.79741234551221E-15));
#2057=DIRECTION('ref_axis',(1.,1.27309421063966E-16,-6.12323399573721E-17));
#2058=DIRECTION('',(1.,1.27309421063966E-16,-6.12323399573721E-17));
#2059=DIRECTION('',(1.,1.27309421063966E-16,-6.12323399573721E-17));
#2060=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2061=DIRECTION('center_axis',(-1.,0.,0.));
#2062=DIRECTION('ref_axis',(0.,-1.,-8.91999876848615E-15));
#2063=DIRECTION('',(0.,-1.,-8.91999876848615E-15));
#2064=DIRECTION('',(0.,-1.,-8.91999876848615E-15));
#2065=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2066=DIRECTION('center_axis',(4.50403041578367E-17,-1.,2.79741234551221E-15));
#2067=DIRECTION('ref_axis',(1.,4.5040304157837E-17,1.27976682437523E-16));
#2068=DIRECTION('',(1.,4.5040304157837E-17,1.27976682437523E-16));
#2069=DIRECTION('',(1.,4.5040304157837E-17,1.27976682437523E-16));
#2070=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2071=DIRECTION('center_axis',(1.,0.,0.));
#2072=DIRECTION('ref_axis',(0.,1.,8.40773512224315E-16));
#2073=DIRECTION('',(0.,1.,8.40773512224315E-16));
#2074=DIRECTION('',(0.,1.,8.40773512224315E-16));
#2075=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2076=DIRECTION('center_axis',(1.27309421063966E-16,-1.,2.79741234551221E-15));
#2077=DIRECTION('ref_axis',(1.,1.27309421063966E-16,-6.12323399573703E-17));
#2078=DIRECTION('',(1.,1.27309421063966E-16,-6.12323399573703E-17));
#2079=DIRECTION('',(1.,1.27309421063966E-16,-6.12323399573703E-17));
#2080=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2081=DIRECTION('center_axis',(-1.,0.,0.));
#2082=DIRECTION('ref_axis',(0.,-1.,-6.12323399573758E-17));
#2083=DIRECTION('',(0.,-1.,-6.12323399573758E-17));
#2084=DIRECTION('',(0.,-1.,-6.12323399573758E-17));
#2085=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2086=DIRECTION('center_axis',(1.08233215748984E-16,-1.,2.79741234551221E-15));
#2087=DIRECTION('ref_axis',(1.,1.08233215748984E-16,6.67443424801572E-17));
#2088=DIRECTION('',(1.,1.08233215748984E-16,6.67443424801572E-17));
#2089=DIRECTION('',(1.,1.08233215748984E-16,6.67443424801572E-17));
#2090=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2091=DIRECTION('center_axis',(-1.,1.60611582804465E-15,-4.49296824569465E-30));
#2092=DIRECTION('ref_axis',(-1.60611582804465E-15,-1.,6.37879413406475E-15));
#2093=DIRECTION('',(1.60611582804465E-15,1.,-6.37879413406475E-15));
#2094=DIRECTION('',(-1.60611582804465E-15,-1.,6.37879413406475E-15));
#2095=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2096=DIRECTION('center_axis',(-1.39870617275613E-15,1.,-2.79741234551221E-15));
#2097=DIRECTION('ref_axis',(-1.,-1.39870617275611E-15,4.37498851021865E-15));
#2098=DIRECTION('',(-1.,-1.39870617275611E-15,4.37498851021865E-15));
#2099=DIRECTION('',(-1.,-1.39870617275611E-15,4.37498851021865E-15));
#2100=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2101=DIRECTION('center_axis',(-1.,1.35796715801563E-15,-3.79879409263306E-30));
#2102=DIRECTION('ref_axis',(-1.35796715801563E-15,-1.,-1.06189041510161E-15));
#2103=DIRECTION('',(-1.35796715801563E-15,-1.,-1.06189041510161E-15));
#2104=DIRECTION('',(-1.35796715801563E-15,-1.,-1.06189041510161E-15));
#2105=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2106=DIRECTION('center_axis',(-2.57214602749781E-29,-1.,2.79741234551221E-15));
#2107=DIRECTION('ref_axis',(1.,0.,9.19473323846669E-15));
#2108=DIRECTION('',(1.,0.,9.19473323846669E-15));
#2109=DIRECTION('',(1.,0.,9.19473323846669E-15));
#2110=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2111=DIRECTION('center_axis',(-1.,0.,0.));
#2112=DIRECTION('ref_axis',(0.,-1.,-7.25340844421539E-15));
#2113=DIRECTION('',(0.,1.,7.25340844421539E-15));
#2114=DIRECTION('',(0.,-1.,-7.25340844421539E-15));
#2115=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2116=DIRECTION('center_axis',(-1.47311209217572E-29,1.,-2.79741234551221E-15));
#2117=DIRECTION('ref_axis',(-1.,0.,5.26598123633429E-15));
#2118=DIRECTION('',(1.,0.,-5.26598123633429E-15));
#2119=DIRECTION('',(-1.,0.,5.26598123633429E-15));
#2120=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2121=DIRECTION('center_axis',(-1.,0.,0.));
#2122=DIRECTION('ref_axis',(0.,-1.,2.70827227424517E-14));
#2123=DIRECTION('',(0.,-1.,2.70827227424517E-14));
#2124=DIRECTION('',(0.,-1.,2.70827227424517E-14));
#2125=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2126=DIRECTION('center_axis',(-7.40454125335595E-30,-1.,2.79741234551221E-15));
#2127=DIRECTION('ref_axis',(1.,0.,2.64692520758865E-15));
#2128=DIRECTION('',(1.,0.,2.64692520758865E-15));
#2129=DIRECTION('',(1.,0.,2.64692520758865E-15));
#2130=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2131=DIRECTION('center_axis',(-1.,0.,0.));
#2132=DIRECTION('ref_axis',(0.,-1.,-9.6193942945346E-16));
#2133=DIRECTION('',(0.,1.,9.6193942945346E-16));
#2134=DIRECTION('',(0.,-1.,-9.6193942945346E-16));
#2135=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2136=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#2137=DIRECTION('ref_axis',(0.,1.,-2.79741234551221E-15));
#2138=DIRECTION('center_axis',(6.12323399573741E-17,-9.6193942945346E-16,
1.));
#2139=DIRECTION('ref_axis',(0.,1.,9.6193942945346E-16));
#2140=DIRECTION('center_axis',(6.12323399573741E-17,-9.6193942945346E-16,
1.));
#2141=DIRECTION('ref_axis',(0.,1.,9.6193942945346E-16));
#2142=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2143=DIRECTION('center_axis',(-1.71292103741358E-31,1.,-2.79741234551221E-15));
#2144=DIRECTION('ref_axis',(-1.,0.,6.12323399573738E-17));
#2145=DIRECTION('',(1.,0.,-6.12323399573738E-17));
#2146=DIRECTION('',(-1.,0.,6.12323399573738E-17));
#2147=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2148=DIRECTION('center_axis',(-1.,0.,0.));
#2149=DIRECTION('ref_axis',(0.,-1.,-1.92817453591187E-14));
#2150=DIRECTION('',(0.,1.,1.92817453591187E-14));
#2151=DIRECTION('',(0.,-1.,-1.92817453591187E-14));
#2152=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2153=DIRECTION('center_axis',(-1.10563908044839E-29,1.,-2.79741234551221E-15));
#2154=DIRECTION('ref_axis',(-1.,0.,3.95236362712896E-15));
#2155=DIRECTION('',(1.,0.,-3.95236362712896E-15));
#2156=DIRECTION('',(-1.,0.,3.95236362712896E-15));
#2157=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2158=DIRECTION('center_axis',(1.,0.,0.));
#2159=DIRECTION('ref_axis',(0.,1.,3.50256169659624E-15));
#2160=DIRECTION('',(0.,-1.,-3.50256169659624E-15));
#2161=DIRECTION('',(0.,1.,3.50256169659624E-15));
#2162=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2163=DIRECTION('center_axis',(-1.31026339368253E-16,1.,-2.79741234551221E-15));
#2164=DIRECTION('ref_axis',(-1.,-1.31026339368253E-16,1.22464679914734E-16));
#2165=DIRECTION('',(1.,1.31026339368253E-16,-1.22464679914734E-16));
#2166=DIRECTION('',(-1.,-1.31026339368253E-16,1.22464679914734E-16));
#2167=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2168=DIRECTION('center_axis',(-1.,0.,0.));
#2169=DIRECTION('ref_axis',(0.,-1.,1.57167225192646E-13));
#2170=DIRECTION('',(0.,1.,-1.57167225192646E-13));
#2171=DIRECTION('',(0.,-1.,1.57167225192646E-13));
#2172=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2173=DIRECTION('center_axis',(-1.08850987007426E-29,1.,-2.79741234551221E-15));
#2174=DIRECTION('ref_axis',(-1.,0.,3.89113128717159E-15));
#2175=DIRECTION('',(1.,0.,-3.89113128717159E-15));
#2176=DIRECTION('',(-1.,0.,3.89113128717159E-15));
#2177=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2178=DIRECTION('center_axis',(1.,0.,0.));
#2179=DIRECTION('ref_axis',(0.,1.,-1.7140825247544E-13));
#2180=DIRECTION('',(0.,-1.,1.7140825247544E-13));
#2181=DIRECTION('',(0.,1.,-1.7140825247544E-13));
#2182=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2183=DIRECTION('center_axis',(-3.08714182080492E-16,1.,-2.79741234551221E-15));
#2184=DIRECTION('ref_axis',(-1.,-3.08714182080491E-16,1.22464679914739E-16));
#2185=DIRECTION('',(1.,3.08714182080491E-16,-1.22464679914739E-16));
#2186=DIRECTION('',(-1.,-3.08714182080491E-16,1.22464679914739E-16));
#2187=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2188=DIRECTION('center_axis',(0.,2.79741234551221E-15,1.));
#2189=DIRECTION('ref_axis',(1.,-4.26325641456062E-15,1.19315211914679E-29));
#2190=DIRECTION('center_axis',(1.22464679914785E-16,2.09906086622158E-14,
1.));
#2191=DIRECTION('ref_axis',(1.,-4.26325641456063E-15,-1.22464679914695E-16));
#2192=DIRECTION('center_axis',(1.22464679914785E-16,2.09906086622158E-14,
1.));
#2193=DIRECTION('ref_axis',(1.,-4.26325641456063E-15,-1.22464679914695E-16));
#2194=DIRECTION('',(0.,2.79741234551221E-15,1.));
#2195=DIRECTION('center_axis',(1.,1.41258172950856E-15,-3.95157356917222E-30));
#2196=DIRECTION('ref_axis',(-1.41258172950856E-15,1.,-2.09906086622157E-14));
#2197=DIRECTION('',(1.41258172950856E-15,-1.,2.09906086622157E-14));
#2198=DIRECTION('',(-1.41258172950856E-15,1.,-2.09906086622157E-14));
#2199=DIRECTION('center_axis',(6.12323399573701E-17,1.54923764979533E-15,
1.));
#2200=DIRECTION('ref_axis',(1.,0.,-6.12323399573701E-17));
#2201=DIRECTION('center_axis',(6.12323399573701E-17,1.54923764979533E-15,
1.));
#2202=DIRECTION('ref_axis',(1.,0.,-6.12323399573701E-17));
#2203=CARTESIAN_POINT('',(0.,0.,0.));
#2204=CARTESIAN_POINT('Origin',(590.804,228.873,-1.47342090683919E-13));
#2205=CARTESIAN_POINT('',(588.264,228.873,12.6999999999999));
#2206=CARTESIAN_POINT('Origin',(590.804,228.873,12.6999999999999));
#2207=CARTESIAN_POINT('',(588.264,228.873,-1.47342090683919E-13));
#2208=CARTESIAN_POINT('',(588.264,228.873,-1.47342090683919E-13));
#2209=CARTESIAN_POINT('Origin',(590.804,228.873,-1.47342090683919E-13));
#2210=CARTESIAN_POINT('Origin',(590.1944,291.5602,-1.89329411822066E-13));
#2211=CARTESIAN_POINT('',(583.2094,291.5602,12.6999999999998));
#2212=CARTESIAN_POINT('Origin',(590.1944,291.5602,12.6999999999998));
#2213=CARTESIAN_POINT('',(583.2094,291.5602,-1.89329411822066E-13));
#2214=CARTESIAN_POINT('',(583.2094,291.5602,-1.89329411822066E-13));
#2215=CARTESIAN_POINT('Origin',(590.1944,291.5602,-1.89329411822066E-13));
#2216=CARTESIAN_POINT('Origin',(590.804,76.4729999999999,-1.47342854212762E-13));
#2217=CARTESIAN_POINT('',(588.264,76.473,12.6999999999999));
#2218=CARTESIAN_POINT('Origin',(590.804,76.473,12.6999999999999));
#2219=CARTESIAN_POINT('',(588.264,76.4729999999999,-1.47342854212762E-13));
#2220=CARTESIAN_POINT('',(588.264,76.4729999999999,-1.47342854212762E-13));
#2221=CARTESIAN_POINT('Origin',(590.804,76.4729999999999,-1.47342854212762E-13));
#2222=CARTESIAN_POINT('Origin',(590.1944,131.5656,-1.89329411822066E-13));
#2223=CARTESIAN_POINT('',(583.2094,131.5656,12.6999999999998));
#2224=CARTESIAN_POINT('Origin',(590.1944,131.5656,12.6999999999998));
#2225=CARTESIAN_POINT('',(583.2094,131.5656,-1.89329411822066E-13));
#2226=CARTESIAN_POINT('',(583.2094,131.5656,-1.89329411822066E-13));
#2227=CARTESIAN_POINT('Origin',(590.1944,131.5656,-1.89329411822066E-13));
#2228=CARTESIAN_POINT('Origin',(590.1944,211.5756,-1.89329411822066E-13));
#2229=CARTESIAN_POINT('',(583.2094,211.5756,12.6999999999998));
#2230=CARTESIAN_POINT('Origin',(590.1944,211.5756,12.6999999999998));
#2231=CARTESIAN_POINT('',(583.2094,211.5756,-1.89329411822066E-13));
#2232=CARTESIAN_POINT('',(583.2094,211.5756,-1.89329411822066E-13));
#2233=CARTESIAN_POINT('Origin',(590.1944,211.5756,-1.89329411822066E-13));
#2234=CARTESIAN_POINT('Origin',(590.1944,411.5752,-1.89329411822066E-13));
#2235=CARTESIAN_POINT('',(583.2094,411.5752,12.6999999999998));
#2236=CARTESIAN_POINT('Origin',(590.1944,411.5752,12.6999999999998));
#2237=CARTESIAN_POINT('',(583.2094,411.5752,-1.89329411822066E-13));
#2238=CARTESIAN_POINT('',(583.2094,411.5752,-1.89329411822066E-13));
#2239=CARTESIAN_POINT('Origin',(590.1944,411.5752,-1.89329411822066E-13));
#2240=CARTESIAN_POINT('Origin',(590.804,381.273,-1.47338956257864E-13));
#2241=CARTESIAN_POINT('',(588.264,381.273,12.6999999999999));
#2242=CARTESIAN_POINT('Origin',(590.804,381.273,12.6999999999999));
#2243=CARTESIAN_POINT('',(588.264,381.273,-1.47338956257864E-13));
#2244=CARTESIAN_POINT('',(588.264,381.273,-1.47338956257864E-13));
#2245=CARTESIAN_POINT('Origin',(590.804,381.273,-1.47338956257864E-13));
#2246=CARTESIAN_POINT('Origin',(530.804,120.923,-1.88594045648586E-13));
#2247=CARTESIAN_POINT('',(528.264,120.923,12.6999999999998));
#2248=CARTESIAN_POINT('Origin',(530.804,120.923,12.6999999999998));
#2249=CARTESIAN_POINT('',(528.264,120.923,-1.88594045648586E-13));
#2250=CARTESIAN_POINT('',(528.264,120.923,-1.88594045648586E-13));
#2251=CARTESIAN_POINT('Origin',(530.804,120.923,-1.88594045648586E-13));
#2252=CARTESIAN_POINT('Origin',(530.804,324.123,-1.96987075579941E-13));
#2253=CARTESIAN_POINT('',(528.264,324.123,12.6999999999998));
#2254=CARTESIAN_POINT('Origin',(530.804,324.123,12.6999999999998));
#2255=CARTESIAN_POINT('',(528.264,324.123,-1.96987075579941E-13));
#2256=CARTESIAN_POINT('',(528.264,324.123,-1.96987075579941E-13));
#2257=CARTESIAN_POINT('Origin',(530.804,324.123,-1.96987075579941E-13));
#2258=CARTESIAN_POINT('Origin',(590.804,470.173,-1.51070901237899E-13));
#2259=CARTESIAN_POINT('',(588.264,470.173,12.6999999999999));
#2260=CARTESIAN_POINT('Origin',(590.804,470.173,12.6999999999999));
#2261=CARTESIAN_POINT('',(588.264,470.173,-1.51070901237899E-13));
#2262=CARTESIAN_POINT('',(588.264,470.173,-1.51070901237899E-13));
#2263=CARTESIAN_POINT('Origin',(590.804,470.173,-1.51070901237899E-13));
#2264=CARTESIAN_POINT('Origin',(800.1,470.173,-1.49856399714948E-13));
#2265=CARTESIAN_POINT('',(797.56,470.173,12.6999999999999));
#2266=CARTESIAN_POINT('Origin',(800.1,470.173,12.6999999999999));
#2267=CARTESIAN_POINT('',(797.56,470.173,-1.49856399714948E-13));
#2268=CARTESIAN_POINT('',(797.56,470.173,-1.49856399714948E-13));
#2269=CARTESIAN_POINT('Origin',(800.1,470.173,-1.49856399714948E-13));
#2270=CARTESIAN_POINT('Origin',(1003.3,470.173,-1.48411577176958E-13));
#2271=CARTESIAN_POINT('',(1000.76,470.173,12.6999999999999));
#2272=CARTESIAN_POINT('Origin',(1003.3,470.173,12.6999999999999));
#2273=CARTESIAN_POINT('',(1000.76,470.173,-1.48411577176958E-13));
#2274=CARTESIAN_POINT('',(1000.76,470.173,-1.48411577176958E-13));
#2275=CARTESIAN_POINT('Origin',(1003.3,470.173,-1.48411577176958E-13));
#2276=CARTESIAN_POINT('Origin',(1149.35,381.273,-1.47338956257864E-13));
#2277=CARTESIAN_POINT('',(1146.81,381.273,12.6999999999999));
#2278=CARTESIAN_POINT('Origin',(1149.35,381.273,12.6999999999999));
#2279=CARTESIAN_POINT('',(1146.81,381.273,-1.47338956257864E-13));
#2280=CARTESIAN_POINT('',(1146.81,381.273,-1.47338956257864E-13));
#2281=CARTESIAN_POINT('Origin',(1149.35,381.273,-1.47338956257864E-13));
#2282=CARTESIAN_POINT('Origin',(1132.078,452.921,2.0605606264512E-14));
#2283=CARTESIAN_POINT('',(1132.078,452.921,2.0605606264512E-14));
#2284=CARTESIAN_POINT('',(1132.078,44.9969999999998,2.0605606264512E-14));
#2285=CARTESIAN_POINT('',(1132.078,452.921,2.0605606264512E-14));
#2286=CARTESIAN_POINT('',(1132.078,44.9969999999999,12.7));
#2287=CARTESIAN_POINT('',(1132.078,44.9969999999998,2.0605606264512E-14));
#2288=CARTESIAN_POINT('',(1132.078,452.921,12.7));
#2289=CARTESIAN_POINT('',(1132.078,452.921,12.7));
#2290=CARTESIAN_POINT('',(1132.078,452.921,2.0605606264512E-14));
#2291=CARTESIAN_POINT('Origin',(608.076,452.921,-3.64584033825978E-14));
#2292=CARTESIAN_POINT('',(608.076,452.921,-3.64584033825978E-14));
#2293=CARTESIAN_POINT('',(608.076,452.921,-3.64584033825978E-14));
#2294=CARTESIAN_POINT('',(608.076,452.921,12.7));
#2295=CARTESIAN_POINT('',(608.076,452.921,12.7));
#2296=CARTESIAN_POINT('',(608.076,452.921,-3.64584033825978E-14));
#2297=CARTESIAN_POINT('Origin',(608.076,44.9969999999999,-1.14802623378286E-14));
#2298=CARTESIAN_POINT('',(608.076,44.9969999999999,-1.14802623378286E-14));
#2299=CARTESIAN_POINT('',(608.076,44.9969999999999,-1.14802623378286E-14));
#2300=CARTESIAN_POINT('',(608.076,44.9969999999999,12.7));
#2301=CARTESIAN_POINT('',(608.076,44.9969999999999,12.7));
#2302=CARTESIAN_POINT('',(608.076,44.9969999999999,-1.14802623378286E-14));
#2303=CARTESIAN_POINT('Origin',(1132.078,44.9969999999998,2.0605606264512E-14));
#2304=CARTESIAN_POINT('',(1132.078,44.9969999999998,2.0605606264512E-14));
#2305=CARTESIAN_POINT('',(1132.078,44.9969999999999,12.7));
#2306=CARTESIAN_POINT('Origin',(1149.9596,411.5752,-1.89329411822066E-13));
#2307=CARTESIAN_POINT('',(1142.9746,411.5752,12.6999999999998));
#2308=CARTESIAN_POINT('Origin',(1149.9596,411.5752,12.6999999999998));
#2309=CARTESIAN_POINT('',(1142.9746,411.5752,-1.89329411822066E-13));
#2310=CARTESIAN_POINT('',(1142.9746,411.5752,-1.89329411822066E-13));
#2311=CARTESIAN_POINT('Origin',(1149.9596,411.5752,-1.89329411822066E-13));
#2312=CARTESIAN_POINT('Origin',(95.25,21.3549999999998,-3.48096393879125E-15));
#2313=CARTESIAN_POINT('',(89.662,21.3549999999998,12.7));
#2314=CARTESIAN_POINT('Origin',(95.25,21.3549999999998,12.7));
#2315=CARTESIAN_POINT('',(89.662,21.3549999999998,-3.48096393879125E-15));
#2316=CARTESIAN_POINT('',(89.662,21.3549999999998,-3.48096393879125E-15));
#2317=CARTESIAN_POINT('Origin',(95.25,21.3549999999998,-3.48096393879125E-15));
#2318=CARTESIAN_POINT('Origin',(476.25,21.3549999999998,-3.48096393879125E-15));
#2319=CARTESIAN_POINT('',(470.662,21.3549999999998,12.7));
#2320=CARTESIAN_POINT('Origin',(476.25,21.3549999999998,12.7));
#2321=CARTESIAN_POINT('',(470.662,21.3549999999998,-3.48096393879125E-15));
#2322=CARTESIAN_POINT('',(470.662,21.3549999999998,-3.48096393879125E-15));
#2323=CARTESIAN_POINT('Origin',(476.25,21.3549999999998,-3.48096393879125E-15));
#2324=CARTESIAN_POINT('Origin',(476.25,435.121,5.69966403558715E-14));
#2325=CARTESIAN_POINT('',(470.662,435.121,12.7000000000001));
#2326=CARTESIAN_POINT('Origin',(476.25,435.121,12.7000000000001));
#2327=CARTESIAN_POINT('',(470.662,435.121,5.69966403558715E-14));
#2328=CARTESIAN_POINT('',(470.662,435.121,5.69966403558715E-14));
#2329=CARTESIAN_POINT('Origin',(476.25,435.121,5.69966403558715E-14));
#2330=CARTESIAN_POINT('Origin',(95.25,435.121,-3.75870374695316E-15));
#2331=CARTESIAN_POINT('',(89.662,435.121,12.7));
#2332=CARTESIAN_POINT('Origin',(95.25,435.121,12.7));
#2333=CARTESIAN_POINT('',(89.662,435.121,-3.75870374695316E-15));
#2334=CARTESIAN_POINT('',(89.662,435.121,-3.75870374695316E-15));
#2335=CARTESIAN_POINT('Origin',(95.25,435.121,-3.75870374695316E-15));
#2336=CARTESIAN_POINT('Origin',(533.4,457.473,-1.92437095792677E-14));
#2337=CARTESIAN_POINT('',(508.,457.473,12.7));
#2338=CARTESIAN_POINT('Origin',(533.4,457.473,12.7));
#2339=CARTESIAN_POINT('',(508.,457.473,-1.92437095792677E-14));
#2340=CARTESIAN_POINT('',(508.,457.473,-1.92437095792677E-14));
#2341=CARTESIAN_POINT('Origin',(533.4,457.473,-1.92437095792677E-14));
#2342=CARTESIAN_POINT('Origin',(571.5,298.997,1.35524641680946E-12));
#2343=CARTESIAN_POINT('',(571.5,298.997,1.35524641680946E-12));
#2344=CARTESIAN_POINT('',(558.8,298.997,1.35369111537455E-12));
#2345=CARTESIAN_POINT('',(571.5,298.997,1.35524641680946E-12));
#2346=CARTESIAN_POINT('',(558.8,298.997,12.7000000000014));
#2347=CARTESIAN_POINT('',(558.8,298.997,1.35369111537455E-12));
#2348=CARTESIAN_POINT('',(571.5,298.997,12.7000000000014));
#2349=CARTESIAN_POINT('',(571.5,298.997,12.7000000000014));
#2350=CARTESIAN_POINT('',(571.5,298.997,1.35524641680946E-12));
#2351=CARTESIAN_POINT('Origin',(571.5,351.321,1.3488385748976E-12));
#2352=CARTESIAN_POINT('',(571.5,351.321,1.3488385748976E-12));
#2353=CARTESIAN_POINT('',(571.5,351.321,1.3488385748976E-12));
#2354=CARTESIAN_POINT('',(571.5,351.321,12.7000000000013));
#2355=CARTESIAN_POINT('',(571.5,351.321,12.7000000000013));
#2356=CARTESIAN_POINT('',(571.5,351.321,1.3488385748976E-12));
#2357=CARTESIAN_POINT('Origin',(558.8,351.321,1.31522262476123E-12));
#2358=CARTESIAN_POINT('',(558.8,351.321,1.31522262476123E-12));
#2359=CARTESIAN_POINT('',(558.8,351.321,1.31522262476123E-12));
#2360=CARTESIAN_POINT('',(558.8,351.321,12.7000000000013));
#2361=CARTESIAN_POINT('',(558.8,351.321,12.7000000000013));
#2362=CARTESIAN_POINT('',(558.8,351.321,1.31522262476123E-12));
#2363=CARTESIAN_POINT('Origin',(558.8,298.997,1.35369111537455E-12));
#2364=CARTESIAN_POINT('',(558.8,298.997,1.35369111537455E-12));
#2365=CARTESIAN_POINT('',(558.8,298.997,12.7000000000014));
#2366=CARTESIAN_POINT('Origin',(558.8,44.9969999999999,7.62582610749009E-15));
#2367=CARTESIAN_POINT('',(558.8,44.9969999999999,7.62582610749009E-15));
#2368=CARTESIAN_POINT('',(558.8,97.3209999999999,7.62582610749009E-15));
#2369=CARTESIAN_POINT('',(558.8,44.9969999999999,7.62582610749009E-15));
#2370=CARTESIAN_POINT('',(558.8,97.3209999999999,12.7));
#2371=CARTESIAN_POINT('',(558.8,97.3209999999999,7.62582610749009E-15));
#2372=CARTESIAN_POINT('',(558.8,44.9969999999999,12.7));
#2373=CARTESIAN_POINT('',(558.8,44.9969999999999,12.7));
#2374=CARTESIAN_POINT('',(558.8,44.9969999999999,7.62582610749009E-15));
#2375=CARTESIAN_POINT('Origin',(571.5,44.9969999999999,-2.4434822593969E-14));
#2376=CARTESIAN_POINT('',(571.5,44.9969999999999,-2.4434822593969E-14));
#2377=CARTESIAN_POINT('',(571.5,44.9969999999999,-2.4434822593969E-14));
#2378=CARTESIAN_POINT('',(571.5,44.9969999999999,12.7));
#2379=CARTESIAN_POINT('',(571.5,44.9969999999999,12.7));
#2380=CARTESIAN_POINT('',(571.5,44.9969999999999,-2.4434822593969E-14));
#2381=CARTESIAN_POINT('Origin',(571.5,97.3209999999999,7.62582610749009E-15));
#2382=CARTESIAN_POINT('',(571.5,97.3209999999999,7.62582610749009E-15));
#2383=CARTESIAN_POINT('',(571.5,97.3209999999999,7.62582610749009E-15));
#2384=CARTESIAN_POINT('',(571.5,97.3209999999999,12.7));
#2385=CARTESIAN_POINT('',(571.5,97.3209999999999,12.7));
#2386=CARTESIAN_POINT('',(571.5,97.3209999999999,7.62582610749009E-15));
#2387=CARTESIAN_POINT('Origin',(558.8,97.3209999999999,7.62582610749009E-15));
#2388=CARTESIAN_POINT('',(558.8,97.3209999999999,7.62582610749009E-15));
#2389=CARTESIAN_POINT('',(558.8,97.3209999999999,12.7));
#2390=CARTESIAN_POINT('Origin',(1149.9596,211.5756,-1.89329411822066E-13));
#2391=CARTESIAN_POINT('',(1142.9746,211.5756,12.6999999999998));
#2392=CARTESIAN_POINT('Origin',(1149.9596,211.5756,12.6999999999998));
#2393=CARTESIAN_POINT('',(1142.9746,211.5756,-1.89329411822066E-13));
#2394=CARTESIAN_POINT('',(1142.9746,211.5756,-1.89329411822066E-13));
#2395=CARTESIAN_POINT('Origin',(1149.9596,211.5756,-1.89329411822066E-13));
#2396=CARTESIAN_POINT('Origin',(1149.9596,131.5656,-1.89329411822066E-13));
#2397=CARTESIAN_POINT('',(1142.9746,131.5656,12.6999999999998));
#2398=CARTESIAN_POINT('Origin',(1149.9596,131.5656,12.6999999999998));
#2399=CARTESIAN_POINT('',(1142.9746,131.5656,-1.89329411822066E-13));
#2400=CARTESIAN_POINT('',(1142.9746,131.5656,-1.89329411822066E-13));
#2401=CARTESIAN_POINT('Origin',(1149.9596,131.5656,-1.89329411822066E-13));
#2402=CARTESIAN_POINT('Origin',(1149.35,228.873,-1.47342090683919E-13));
#2403=CARTESIAN_POINT('',(1146.81,228.873,12.6999999999999));
#2404=CARTESIAN_POINT('Origin',(1149.35,228.873,12.6999999999999));
#2405=CARTESIAN_POINT('',(1146.81,228.873,-1.47342090683919E-13));
#2406=CARTESIAN_POINT('',(1146.81,228.873,-1.47342090683919E-13));
#2407=CARTESIAN_POINT('Origin',(1149.35,228.873,-1.47342090683919E-13));
#2408=CARTESIAN_POINT('Origin',(1149.35,76.4729999999999,-1.47342854212762E-13));
#2409=CARTESIAN_POINT('',(1146.81,76.473,12.6999999999999));
#2410=CARTESIAN_POINT('Origin',(1149.35,76.473,12.6999999999999));
#2411=CARTESIAN_POINT('',(1146.81,76.4729999999999,-1.47342854212762E-13));
#2412=CARTESIAN_POINT('',(1146.81,76.4729999999999,-1.47342854212762E-13));
#2413=CARTESIAN_POINT('Origin',(1149.35,76.4729999999999,-1.47342854212762E-13));
#2414=CARTESIAN_POINT('Origin',(1149.9596,291.5602,-1.89329411822066E-13));
#2415=CARTESIAN_POINT('',(1142.9746,291.5602,12.6999999999998));
#2416=CARTESIAN_POINT('Origin',(1149.9596,291.5602,12.6999999999998));
#2417=CARTESIAN_POINT('',(1142.9746,291.5602,-1.89329411822066E-13));
#2418=CARTESIAN_POINT('',(1142.9746,291.5602,-1.89329411822066E-13));
#2419=CARTESIAN_POINT('Origin',(1149.9596,291.5602,-1.89329411822066E-13));
#2420=CARTESIAN_POINT('Origin',(1181.1,351.321,1.3488385748976E-12));
#2421=CARTESIAN_POINT('',(1193.8,351.321,2.99602013158003E-12));
#2422=CARTESIAN_POINT('',(1181.1,351.321,1.3488385748976E-12));
#2423=CARTESIAN_POINT('',(1193.8,351.321,2.99602013158003E-12));
#2424=CARTESIAN_POINT('',(1193.8,351.321,12.700000000003));
#2425=CARTESIAN_POINT('',(1193.8,351.321,2.99602013158003E-12));
#2426=CARTESIAN_POINT('',(1181.1,351.321,12.7000000000013));
#2427=CARTESIAN_POINT('',(1193.8,351.321,12.700000000003));
#2428=CARTESIAN_POINT('',(1181.1,351.321,1.3488385748976E-12));
#2429=CARTESIAN_POINT('Origin',(1181.1,298.997,1.4299008856855E-12));
#2430=CARTESIAN_POINT('',(1181.1,298.997,1.4299008856855E-12));
#2431=CARTESIAN_POINT('',(1181.1,351.321,1.3488385748976E-12));
#2432=CARTESIAN_POINT('',(1181.1,298.997,12.7000000000014));
#2433=CARTESIAN_POINT('',(1181.1,351.321,12.7000000000013));
#2434=CARTESIAN_POINT('',(1181.1,298.997,1.4299008856855E-12));
#2435=CARTESIAN_POINT('Origin',(1193.8,298.997,1.43145618712041E-12));
#2436=CARTESIAN_POINT('',(1193.8,298.997,1.43145618712041E-12));
#2437=CARTESIAN_POINT('',(1181.1,298.997,1.4299008856855E-12));
#2438=CARTESIAN_POINT('',(1193.8,298.997,12.7000000000014));
#2439=CARTESIAN_POINT('',(1181.1,298.997,12.7000000000014));
#2440=CARTESIAN_POINT('',(1193.8,298.997,1.43145618712041E-12));
#2441=CARTESIAN_POINT('Origin',(1193.8,97.3209999999999,7.62582610749003E-15));
#2442=CARTESIAN_POINT('',(1193.8,97.3209999999999,7.62582610749009E-15));
#2443=CARTESIAN_POINT('',(1193.8,298.997,1.43145618712041E-12));
#2444=CARTESIAN_POINT('',(1193.8,97.3209999999999,12.7));
#2445=CARTESIAN_POINT('',(1193.8,298.997,12.7000000000014));
#2446=CARTESIAN_POINT('',(1193.8,97.3209999999999,7.62582610749009E-15));
#2447=CARTESIAN_POINT('Origin',(1181.1,97.3209999999999,7.62582610749009E-15));
#2448=CARTESIAN_POINT('',(1181.1,97.3209999999999,7.62582610749009E-15));
#2449=CARTESIAN_POINT('',(1193.8,97.3209999999999,7.62582610749009E-15));
#2450=CARTESIAN_POINT('',(1181.1,97.3209999999999,12.7));
#2451=CARTESIAN_POINT('',(1193.8,97.3209999999999,12.7));
#2452=CARTESIAN_POINT('',(1181.1,97.3209999999999,7.62582610749009E-15));
#2453=CARTESIAN_POINT('Origin',(1181.1,44.9969999999998,2.36073380339021E-14));
#2454=CARTESIAN_POINT('',(1181.1,44.9969999999998,2.36073380339021E-14));
#2455=CARTESIAN_POINT('',(1181.1,97.3209999999999,7.62582610749009E-15));
#2456=CARTESIAN_POINT('',(1181.1,44.9969999999999,12.7));
#2457=CARTESIAN_POINT('',(1181.1,97.3209999999999,12.7));
#2458=CARTESIAN_POINT('',(1181.1,44.9969999999998,2.36073380339021E-14));
#2459=CARTESIAN_POINT('Origin',(1193.8,44.9969999999998,2.30223451864778E-14));
#2460=CARTESIAN_POINT('',(1193.8,44.9969999999998,2.30223451864778E-14));
#2461=CARTESIAN_POINT('',(1181.1,44.9969999999998,2.36073380339021E-14));
#2462=CARTESIAN_POINT('',(1193.8,44.9969999999999,12.7));
#2463=CARTESIAN_POINT('',(1181.1,44.9969999999999,12.7));
#2464=CARTESIAN_POINT('',(1193.8,44.9969999999998,2.30223451864778E-14));
#2465=CARTESIAN_POINT('Origin',(1193.8,0.273000000000065,4.49717809169762E-13));
#2466=CARTESIAN_POINT('',(1193.8,0.273000000000065,4.49717809169762E-13));
#2467=CARTESIAN_POINT('',(1193.8,44.9969999999998,2.30223451864778E-14));
#2468=CARTESIAN_POINT('',(1193.8,0.27300000000011,12.7000000000005));
#2469=CARTESIAN_POINT('',(1193.8,44.9969999999999,12.7));
#2470=CARTESIAN_POINT('',(1193.8,0.273000000000065,4.49717809169762E-13));
#2471=CARTESIAN_POINT('Origin',(913.638,0.273000000000065,3.96708631037554E-13));
#2472=CARTESIAN_POINT('',(913.638,0.273000000000065,3.96708631037554E-13));
#2473=CARTESIAN_POINT('',(913.638,0.273000000000065,3.96708631037554E-13));
#2474=CARTESIAN_POINT('',(913.638,0.27300000000011,12.7000000000004));
#2475=CARTESIAN_POINT('',(913.638,0.273000000000101,12.7000000000004));
#2476=CARTESIAN_POINT('',(913.638,0.273000000000065,3.96708631037554E-13));
#2477=CARTESIAN_POINT('Origin',(913.638,-12.4269999999999,2.7032055283406E-13));
#2478=CARTESIAN_POINT('',(913.638,-12.4269999999999,2.7032055283406E-13));
#2479=CARTESIAN_POINT('',(913.638,-12.4269999999999,2.7032055283406E-13));
#2480=CARTESIAN_POINT('',(913.638,-12.4269999999999,12.7000000000003));
#2481=CARTESIAN_POINT('',(913.638,-12.4269999999999,12.7000000000003));
#2482=CARTESIAN_POINT('',(913.638,-12.4269999999999,2.7032055283406E-13));
#2483=CARTESIAN_POINT('Origin',(861.314,-12.4269999999999,2.7352447378999E-13));
#2484=CARTESIAN_POINT('',(861.314,-12.4269999999999,2.7352447378999E-13));
#2485=CARTESIAN_POINT('',(861.314,-12.4269999999999,2.7352447378999E-13));
#2486=CARTESIAN_POINT('',(861.314,-12.4269999999999,12.7000000000003));
#2487=CARTESIAN_POINT('',(861.314,-12.4269999999999,12.7000000000003));
#2488=CARTESIAN_POINT('',(861.314,-12.4269999999999,2.7352447378999E-13));
#2489=CARTESIAN_POINT('Origin',(861.314,0.273000000000068,3.86808458149764E-13));
#2490=CARTESIAN_POINT('',(861.314,0.273000000000068,3.86808458149764E-13));
#2491=CARTESIAN_POINT('',(861.314,0.273000000000068,3.86808458149764E-13));
#2492=CARTESIAN_POINT('',(861.314,0.27300000000011,12.7000000000004));
#2493=CARTESIAN_POINT('',(861.314,0.273000000000103,12.7000000000004));
#2494=CARTESIAN_POINT('',(861.314,0.273000000000068,3.86808458149764E-13));
#2495=CARTESIAN_POINT('Origin',(319.024,0.273000000000043,3.17407983030719E-13));
#2496=CARTESIAN_POINT('',(319.024,0.273000000000043,3.17407983030719E-13));
#2497=CARTESIAN_POINT('',(319.024,0.273000000000043,3.17407983030719E-13));
#2498=CARTESIAN_POINT('',(319.024,0.273000000000074,12.7000000000003));
#2499=CARTESIAN_POINT('',(319.024,0.273000000000079,12.7000000000003));
#2500=CARTESIAN_POINT('',(319.024,0.273000000000043,3.17407983030719E-13));
#2501=CARTESIAN_POINT('Origin',(319.024,-12.427,3.06730159425471E-13));
#2502=CARTESIAN_POINT('',(319.024,-12.427,3.06730159425471E-13));
#2503=CARTESIAN_POINT('',(319.024,-12.427,3.06730159425471E-13));
#2504=CARTESIAN_POINT('',(319.024,-12.4269999999999,12.7000000000003));
#2505=CARTESIAN_POINT('',(319.024,-12.4269999999999,12.7000000000003));
#2506=CARTESIAN_POINT('',(319.024,-12.427,3.06730159425471E-13));
#2507=CARTESIAN_POINT('Origin',(266.7,-12.427,3.099340803814E-13));
#2508=CARTESIAN_POINT('',(266.7,-12.427,3.099340803814E-13));
#2509=CARTESIAN_POINT('',(266.7,-12.427,3.099340803814E-13));
#2510=CARTESIAN_POINT('',(266.7,-12.4269999999999,12.7000000000003));
#2511=CARTESIAN_POINT('',(266.7,-12.4269999999999,12.7000000000003));
#2512=CARTESIAN_POINT('',(266.7,-12.427,3.099340803814E-13));
#2513=CARTESIAN_POINT('Origin',(266.7,0.273000000000039,3.10711731098859E-13));
#2514=CARTESIAN_POINT('',(266.7,0.273000000000039,3.10711731098859E-13));
#2515=CARTESIAN_POINT('',(266.7,0.273000000000039,3.10711731098859E-13));
#2516=CARTESIAN_POINT('',(266.7,0.273000000000074,12.7000000000003));
#2517=CARTESIAN_POINT('',(266.7,0.273000000000074,12.7000000000003));
#2518=CARTESIAN_POINT('',(266.7,0.273000000000039,3.10711731098859E-13));
#2519=CARTESIAN_POINT('Origin',(-7.77650717458568E-16,0.27300000000001,
2.92911014959401E-13));
#2520=CARTESIAN_POINT('',(-7.77650717458568E-16,0.27300000000001,2.92911014959401E-13));
#2521=CARTESIAN_POINT('',(-7.77650717458568E-16,0.27300000000001,2.92911014959401E-13));
#2522=CARTESIAN_POINT('',(0.,0.273000000000039,12.7000000000003));
#2523=CARTESIAN_POINT('',(-7.77650717458568E-16,0.273000000000045,12.7000000000003));
#2524=CARTESIAN_POINT('',(-7.77650717458568E-16,0.27300000000001,2.92911014959401E-13));
#2525=CARTESIAN_POINT('Origin',(7.105427357601E-14,44.9969999999999,7.62582610749009E-15));
#2526=CARTESIAN_POINT('',(7.105427357601E-14,44.9969999999999,7.62582610749009E-15));
#2527=CARTESIAN_POINT('',(-7.77650717458568E-16,0.27300000000001,2.92911014959401E-13));
#2528=CARTESIAN_POINT('',(0.,44.9969999999999,12.7));
#2529=CARTESIAN_POINT('',(-7.77650717458568E-16,0.273000000000045,12.7000000000003));
#2530=CARTESIAN_POINT('',(7.105427357601E-14,44.9969999999999,7.62582610749009E-15));
#2531=CARTESIAN_POINT('Origin',(12.7,44.9969999999999,-4.79365279722863E-14));
#2532=CARTESIAN_POINT('',(12.7,44.9969999999999,-4.79365279722863E-14));
#2533=CARTESIAN_POINT('',(12.7,44.9969999999999,-4.79365279722863E-14));
#2534=CARTESIAN_POINT('',(12.7,44.9969999999999,12.7));
#2535=CARTESIAN_POINT('',(12.7,44.9969999999999,12.7));
#2536=CARTESIAN_POINT('',(12.7,44.9969999999999,-4.79365279722863E-14));
#2537=CARTESIAN_POINT('Origin',(12.7,97.3209999999999,7.62582610749009E-15));
#2538=CARTESIAN_POINT('',(12.7,97.3209999999999,7.62582610749009E-15));
#2539=CARTESIAN_POINT('',(12.7,97.3209999999999,7.62582610749009E-15));
#2540=CARTESIAN_POINT('',(12.7000000000001,97.3209999999999,12.7));
#2541=CARTESIAN_POINT('',(12.7,97.3209999999999,12.7));
#2542=CARTESIAN_POINT('',(12.7,97.3209999999999,7.62582610749009E-15));
#2543=CARTESIAN_POINT('Origin',(0.,97.3209999999999,-1.09147286021037E-13));
#2544=CARTESIAN_POINT('',(0.,97.3209999999999,-1.09147286021037E-13));
#2545=CARTESIAN_POINT('',(0.,97.3209999999999,-1.09147286021037E-13));
#2546=CARTESIAN_POINT('',(0.,97.3209999999999,12.6999999999999));
#2547=CARTESIAN_POINT('',(0.,97.3209999999999,12.6999999999999));
#2548=CARTESIAN_POINT('',(0.,97.3209999999999,-1.09147286021037E-13));
#2549=CARTESIAN_POINT('Origin',(0.,298.997,1.35369111537455E-12));
#2550=CARTESIAN_POINT('',(0.,298.997,1.35369111537455E-12));
#2551=CARTESIAN_POINT('',(0.,97.3209999999999,-1.09147286021037E-13));
#2552=CARTESIAN_POINT('',(0.,298.997,12.7000000000014));
#2553=CARTESIAN_POINT('',(0.,97.3209999999999,12.6999999999999));
#2554=CARTESIAN_POINT('',(0.,298.997,1.35369111537455E-12));
#2555=CARTESIAN_POINT('Origin',(12.7,298.997,1.2868131536731E-12));
#2556=CARTESIAN_POINT('',(12.7,298.997,1.2868131536731E-12));
#2557=CARTESIAN_POINT('',(0.,298.997,1.35369111537455E-12));
#2558=CARTESIAN_POINT('',(12.7000000000001,298.997,12.7000000000013));
#2559=CARTESIAN_POINT('',(0.,298.997,12.7000000000014));
#2560=CARTESIAN_POINT('',(12.7,298.997,1.2868131536731E-12));
#2561=CARTESIAN_POINT('Origin',(12.7,351.321,-1.30263231102945E-13));
#2562=CARTESIAN_POINT('',(12.7,351.321,-1.30263231102945E-13));
#2563=CARTESIAN_POINT('',(12.7,351.321,-1.30263231102945E-13));
#2564=CARTESIAN_POINT('',(12.7000000000001,351.321,12.6999999999999));
#2565=CARTESIAN_POINT('',(12.7,351.321,12.6999999999999));
#2566=CARTESIAN_POINT('',(12.7,351.321,-1.30263231102945E-13));
#2567=CARTESIAN_POINT('Origin',(0.,351.321,-1.63879181239321E-13));
#2568=CARTESIAN_POINT('',(0.,351.321,-1.63879181239321E-13));
#2569=CARTESIAN_POINT('',(0.,351.321,-1.63879181239321E-13));
#2570=CARTESIAN_POINT('',(0.,351.321,12.6999999999998));
#2571=CARTESIAN_POINT('',(0.,351.321,12.6999999999998));
#2572=CARTESIAN_POINT('',(0.,351.321,-1.63879181239321E-13));
#2573=CARTESIAN_POINT('Origin',(0.,451.923,-6.71061507574444E-14));
#2574=CARTESIAN_POINT('',(-1.77635683940025E-14,451.923,-6.71061507574444E-14));
#2575=CARTESIAN_POINT('',(0.,351.321,-1.63879181239321E-13));
#2576=CARTESIAN_POINT('',(0.,451.923,12.6999999999999));
#2577=CARTESIAN_POINT('',(0.,351.321,12.6999999999998));
#2578=CARTESIAN_POINT('',(-1.77635683940025E-14,451.923,-6.71061507574444E-14));
#2579=CARTESIAN_POINT('Origin',(50.,451.923,-7.01677677553131E-14));
#2580=CARTESIAN_POINT('',(50.,501.923,-2.20707962826401E-14));
#2581=CARTESIAN_POINT('Origin',(50.,451.923,-7.01677677553131E-14));
#2582=CARTESIAN_POINT('',(50.,501.923,12.7));
#2583=CARTESIAN_POINT('Origin',(50.,451.923,12.6999999999999));
#2584=CARTESIAN_POINT('',(50.,501.923,-2.20707962826401E-14));
#2585=CARTESIAN_POINT('Origin',(266.7,501.923,-3.5339844351403E-14));
#2586=CARTESIAN_POINT('',(266.7,501.923,-3.5339844351403E-14));
#2587=CARTESIAN_POINT('',(50.,501.923,-2.20707962826401E-14));
#2588=CARTESIAN_POINT('',(266.7,501.923,12.7));
#2589=CARTESIAN_POINT('',(50.,501.923,12.7));
#2590=CARTESIAN_POINT('',(266.7,501.923,-3.5339844351403E-14));
#2591=CARTESIAN_POINT('Origin',(266.7,514.623,2.09538321709408E-13));
#2592=CARTESIAN_POINT('',(266.7,514.623,2.09538321709408E-13));
#2593=CARTESIAN_POINT('',(266.7,501.923,-3.5339844351403E-14));
#2594=CARTESIAN_POINT('',(266.7,514.623,12.7000000000002));
#2595=CARTESIAN_POINT('',(266.7,501.923,12.7));
#2596=CARTESIAN_POINT('',(266.7,514.623,2.09538321709408E-13));
#2597=CARTESIAN_POINT('Origin',(319.024,514.623,2.7348472835124E-15));
#2598=CARTESIAN_POINT('',(319.024,514.623,2.7348472835124E-15));
#2599=CARTESIAN_POINT('',(266.7,514.623,2.09538321709408E-13));
#2600=CARTESIAN_POINT('',(319.024,514.623,12.7));
#2601=CARTESIAN_POINT('',(266.7,514.623,12.7000000000002));
#2602=CARTESIAN_POINT('',(319.024,514.623,2.7348472835124E-15));
#2603=CARTESIAN_POINT('Origin',(319.024,501.923,-4.17476862632605E-14));
#2604=CARTESIAN_POINT('',(319.024,501.923,-4.17476862632605E-14));
#2605=CARTESIAN_POINT('',(319.024,514.623,2.7348472835124E-15));
#2606=CARTESIAN_POINT('',(319.024,501.923,12.7));
#2607=CARTESIAN_POINT('',(319.024,514.623,12.7));
#2608=CARTESIAN_POINT('',(319.024,501.923,-4.17476862632605E-14));
#2609=CARTESIAN_POINT('Origin',(861.314,501.923,-1.08159057534222E-13));
#2610=CARTESIAN_POINT('',(861.314,501.923,-1.08159057534222E-13));
#2611=CARTESIAN_POINT('',(319.024,501.923,-4.17476862632605E-14));
#2612=CARTESIAN_POINT('',(861.314,501.923,12.6999999999999));
#2613=CARTESIAN_POINT('',(319.024,501.923,12.7));
#2614=CARTESIAN_POINT('',(861.314,501.923,-1.08159057534222E-13));
#2615=CARTESIAN_POINT('Origin',(861.314,514.623,-2.10418281748084E-12));
#2616=CARTESIAN_POINT('',(861.314,514.623,-2.10418281748084E-12));
#2617=CARTESIAN_POINT('',(861.314,501.923,-1.08159057534222E-13));
#2618=CARTESIAN_POINT('',(861.314,514.623,12.6999999999979));
#2619=CARTESIAN_POINT('',(861.314,501.923,12.6999999999999));
#2620=CARTESIAN_POINT('',(861.314,514.623,-2.10418281748084E-12));
#2621=CARTESIAN_POINT('Origin',(913.638,514.623,-2.30778237095081E-12));
#2622=CARTESIAN_POINT('',(913.638,514.623,-2.30778237095081E-12));
#2623=CARTESIAN_POINT('',(861.314,514.623,-2.10418281748084E-12));
#2624=CARTESIAN_POINT('',(913.638,514.623,12.6999999999977));
#2625=CARTESIAN_POINT('',(861.314,514.623,12.6999999999979));
#2626=CARTESIAN_POINT('',(913.638,514.623,-2.30778237095081E-12));
#2627=CARTESIAN_POINT('Origin',(913.638,501.923,-1.3089756451271E-13));
#2628=CARTESIAN_POINT('',(913.638,501.923,-1.3089756451271E-13));
#2629=CARTESIAN_POINT('',(913.638,514.623,-2.30778237095081E-12));
#2630=CARTESIAN_POINT('',(913.638,501.923,12.6999999999999));
#2631=CARTESIAN_POINT('',(913.638,514.623,12.6999999999977));
#2632=CARTESIAN_POINT('',(913.638,501.923,-1.3089756451271E-13));
#2633=CARTESIAN_POINT('Origin',(1143.8,501.923,-1.59084280171247E-13));
#2634=CARTESIAN_POINT('',(1143.8,501.923,-1.59084280171247E-13));
#2635=CARTESIAN_POINT('',(913.638,501.923,-1.3089756451271E-13));
#2636=CARTESIAN_POINT('',(1143.8,501.923,12.6999999999998));
#2637=CARTESIAN_POINT('',(913.638,501.923,12.6999999999999));
#2638=CARTESIAN_POINT('',(1143.8,501.923,-1.59084280171247E-13));
#2639=CARTESIAN_POINT('Origin',(1143.8,451.923,8.90446152939537E-13));
#2640=CARTESIAN_POINT('',(1193.8,451.923,8.84322918943802E-13));
#2641=CARTESIAN_POINT('Origin',(1143.8,451.923,8.90446152939537E-13));
#2642=CARTESIAN_POINT('',(1193.8,451.923,12.7000000000009));
#2643=CARTESIAN_POINT('Origin',(1143.8,451.923,12.7000000000009));
#2644=CARTESIAN_POINT('',(1193.8,451.923,8.84322918943802E-13));
#2645=CARTESIAN_POINT('Origin',(1193.8,351.321,2.99602013158003E-12));
#2646=CARTESIAN_POINT('',(1193.8,451.923,8.84322918943802E-13));
#2647=CARTESIAN_POINT('',(1193.8,451.923,12.7000000000009));
#2648=CARTESIAN_POINT('Origin',(596.9,251.098,12.7000000000015));
#2649=CARTESIAN_POINT('Origin',(596.9,251.098,1.53987975287614E-12));
#2650=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#2654,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#2651=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#2654,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#2652=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#2650))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#2654,#2656,#2657))
REPRESENTATION_CONTEXT('','3D')
);
#2653=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#2651))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#2654,#2656,#2657))
REPRESENTATION_CONTEXT('','3D')
);
#2654=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#2655=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT($,.METRE.)
);
#2656=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#2657=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#2658=SHAPE_DEFINITION_REPRESENTATION(#2659,#2660);
#2659=PRODUCT_DEFINITION_SHAPE('',$,#2662);
#2660=SHAPE_REPRESENTATION('',(#1646),#2652);
#2661=PRODUCT_DEFINITION_CONTEXT('part definition',#2666,'design');
#2662=PRODUCT_DEFINITION('S_1087','S_1087 v2',#2663,#2661);
#2663=PRODUCT_DEFINITION_FORMATION('',$,#2668);
#2664=PRODUCT_RELATED_PRODUCT_CATEGORY('S_1087 v2','S_1087 v2',(#2668));
#2665=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2009,#2666);
#2666=APPLICATION_CONTEXT(
'Core Data for Automotive Mechanical Design Process');
#2667=PRODUCT_CONTEXT('part definition',#2666,'mechanical');
#2668=PRODUCT('S_1087','S_1087 v2',$,(#2667));
#2669=PRESENTATION_STYLE_ASSIGNMENT((#2670));
#2670=SURFACE_STYLE_USAGE(.BOTH.,#2671);
#2671=SURFACE_SIDE_STYLE('',(#2672));
#2672=SURFACE_STYLE_FILL_AREA(#2673);
#2673=FILL_AREA_STYLE('Steel - Satin',(#2674));
#2674=FILL_AREA_STYLE_COLOUR('Steel - Satin',#2675);
#2675=COLOUR_RGB('Steel - Satin',0.627450980392157,0.627450980392157,0.627450980392157);
ENDSEC;
END-ISO-10303-21;
