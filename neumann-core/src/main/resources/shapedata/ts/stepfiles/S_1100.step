ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ (''),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 
'G:/Shared drives/TIP/Validation/SMF KION/Tset SMF Shapes/S_1100.step',

/* time_stamp */ '2021-06-30T16:39:23+02:00',
/* author */ (''),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v18.1',
/* originating_system */ 'Autodesk Translation Framework v10.9.0.1377',

/* authorisation */ '');

FILE_SCHEMA (('AUTOMOTIVE_DESIGN { 1 0 10303 214 3 1 1 }'));
ENDSEC;

DATA;
#10=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#13),#830);
#11=SHAPE_REPRESENTATION_RELATIONSHIP('SRR','None',#837,#12);
#12=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#14),#829);
#13=STYLED_ITEM('',(#846),#14);
#14=MANIFOLD_SOLID_BREP('Body1',#486);
#15=CIRCLE('',#502,1.99999999999999);
#16=CIRCLE('',#503,1.99999999999999);
#17=CIRCLE('',#506,1.99999999999999);
#18=CIRCLE('',#507,1.99999999999999);
#19=CIRCLE('',#510,1.99999999999999);
#20=CIRCLE('',#511,1.99999999999999);
#21=CIRCLE('',#514,62.5);
#22=CIRCLE('',#515,62.5);
#23=CIRCLE('',#517,45.);
#24=CIRCLE('',#518,45.);
#25=CIRCLE('',#521,2.);
#26=CIRCLE('',#522,2.);
#27=CIRCLE('',#525,2.);
#28=CIRCLE('',#526,2.);
#29=CIRCLE('',#535,0.999999999999908);
#30=CIRCLE('',#536,0.999999999999908);
#31=CIRCLE('',#538,0.500000000000009);
#32=CIRCLE('',#539,0.500000000000009);
#33=CIRCLE('',#541,0.999999999999996);
#34=CIRCLE('',#542,0.999999999999996);
#35=CYLINDRICAL_SURFACE('',#501,1.99999999999999);
#36=CYLINDRICAL_SURFACE('',#505,1.99999999999999);
#37=CYLINDRICAL_SURFACE('',#509,1.99999999999999);
#38=CYLINDRICAL_SURFACE('',#513,62.5);
#39=CYLINDRICAL_SURFACE('',#516,45.);
#40=CYLINDRICAL_SURFACE('',#520,2.);
#41=CYLINDRICAL_SURFACE('',#524,2.);
#42=CYLINDRICAL_SURFACE('',#534,0.999999999999908);
#43=CYLINDRICAL_SURFACE('',#537,0.500000000000009);
#44=CYLINDRICAL_SURFACE('',#540,0.999999999999996);
#45=FACE_OUTER_BOUND('',#70,.T.);
#46=FACE_OUTER_BOUND('',#71,.T.);
#47=FACE_OUTER_BOUND('',#72,.T.);
#48=FACE_OUTER_BOUND('',#73,.T.);
#49=FACE_OUTER_BOUND('',#74,.T.);
#50=FACE_OUTER_BOUND('',#75,.T.);
#51=FACE_OUTER_BOUND('',#76,.T.);
#52=FACE_OUTER_BOUND('',#77,.T.);
#53=FACE_OUTER_BOUND('',#78,.T.);
#54=FACE_OUTER_BOUND('',#79,.T.);
#55=FACE_OUTER_BOUND('',#80,.T.);
#56=FACE_OUTER_BOUND('',#81,.T.);
#57=FACE_OUTER_BOUND('',#82,.T.);
#58=FACE_OUTER_BOUND('',#83,.T.);
#59=FACE_OUTER_BOUND('',#84,.T.);
#60=FACE_OUTER_BOUND('',#85,.T.);
#61=FACE_OUTER_BOUND('',#86,.T.);
#62=FACE_OUTER_BOUND('',#87,.T.);
#63=FACE_OUTER_BOUND('',#88,.T.);
#64=FACE_OUTER_BOUND('',#89,.T.);
#65=FACE_OUTER_BOUND('',#90,.T.);
#66=FACE_OUTER_BOUND('',#91,.T.);
#67=FACE_OUTER_BOUND('',#92,.T.);
#68=FACE_OUTER_BOUND('',#93,.T.);
#69=FACE_OUTER_BOUND('',#94,.T.);
#70=EDGE_LOOP('',(#308,#309,#310,#311));
#71=EDGE_LOOP('',(#312,#313,#314,#315));
#72=EDGE_LOOP('',(#316,#317,#318,#319));
#73=EDGE_LOOP('',(#320,#321,#322,#323));
#74=EDGE_LOOP('',(#324,#325,#326,#327));
#75=EDGE_LOOP('',(#328,#329,#330,#331));
#76=EDGE_LOOP('',(#332,#333,#334,#335));
#77=EDGE_LOOP('',(#336,#337,#338,#339));
#78=EDGE_LOOP('',(#340,#341,#342,#343));
#79=EDGE_LOOP('',(#344,#345,#346,#347));
#80=EDGE_LOOP('',(#348,#349,#350,#351));
#81=EDGE_LOOP('',(#352,#353,#354,#355));
#82=EDGE_LOOP('',(#356,#357,#358,#359));
#83=EDGE_LOOP('',(#360,#361,#362,#363));
#84=EDGE_LOOP('',(#364,#365,#366,#367));
#85=EDGE_LOOP('',(#368,#369,#370,#371));
#86=EDGE_LOOP('',(#372,#373,#374,#375));
#87=EDGE_LOOP('',(#376,#377,#378,#379));
#88=EDGE_LOOP('',(#380,#381,#382,#383));
#89=EDGE_LOOP('',(#384,#385,#386,#387));
#90=EDGE_LOOP('',(#388,#389,#390,#391));
#91=EDGE_LOOP('',(#392,#393,#394,#395));
#92=EDGE_LOOP('',(#396,#397,#398,#399));
#93=EDGE_LOOP('',(#400,#401,#402,#403,#404,#405,#406,#407,#408,#409,#410,
#411,#412,#413,#414,#415,#416,#417,#418,#419,#420,#421,#422));
#94=EDGE_LOOP('',(#423,#424,#425,#426,#427,#428,#429,#430,#431,#432,#433,
#434,#435,#436,#437,#438,#439,#440,#441,#442,#443,#444,#445));
#95=LINE('',#690,#144);
#96=LINE('',#692,#145);
#97=LINE('',#694,#146);
#98=LINE('',#695,#147);
#99=LINE('',#701,#148);
#100=LINE('',#704,#149);
#101=LINE('',#706,#150);
#102=LINE('',#707,#151);
#103=LINE('',#713,#152);
#104=LINE('',#716,#153);
#105=LINE('',#718,#154);
#106=LINE('',#719,#155);
#107=LINE('',#725,#156);
#108=LINE('',#728,#157);
#109=LINE('',#730,#158);
#110=LINE('',#731,#159);
#111=LINE('',#737,#160);
#112=LINE('',#743,#161);
#113=LINE('',#746,#162);
#114=LINE('',#748,#163);
#115=LINE('',#749,#164);
#116=LINE('',#755,#165);
#117=LINE('',#758,#166);
#118=LINE('',#760,#167);
#119=LINE('',#761,#168);
#120=LINE('',#767,#169);
#121=LINE('',#770,#170);
#122=LINE('',#772,#171);
#123=LINE('',#773,#172);
#124=LINE('',#776,#173);
#125=LINE('',#778,#174);
#126=LINE('',#779,#175);
#127=LINE('',#782,#176);
#128=LINE('',#784,#177);
#129=LINE('',#785,#178);
#130=LINE('',#788,#179);
#131=LINE('',#790,#180);
#132=LINE('',#791,#181);
#133=LINE('',#794,#182);
#134=LINE('',#796,#183);
#135=LINE('',#797,#184);
#136=LINE('',#800,#185);
#137=LINE('',#802,#186);
#138=LINE('',#803,#187);
#139=LINE('',#806,#188);
#140=LINE('',#808,#189);
#141=LINE('',#809,#190);
#142=LINE('',#815,#191);
#143=LINE('',#821,#192);
#144=VECTOR('',#549,10.);
#145=VECTOR('',#550,10.);
#146=VECTOR('',#551,10.);
#147=VECTOR('',#552,10.);
#148=VECTOR('',#559,10.);
#149=VECTOR('',#562,10.);
#150=VECTOR('',#563,10.);
#151=VECTOR('',#564,10.);
#152=VECTOR('',#571,10.);
#153=VECTOR('',#574,10.);
#154=VECTOR('',#575,10.);
#155=VECTOR('',#576,10.);
#156=VECTOR('',#583,10.);
#157=VECTOR('',#586,10.);
#158=VECTOR('',#587,10.);
#159=VECTOR('',#588,10.);
#160=VECTOR('',#595,10.);
#161=VECTOR('',#602,10.);
#162=VECTOR('',#605,10.);
#163=VECTOR('',#606,10.);
#164=VECTOR('',#607,10.);
#165=VECTOR('',#614,10.);
#166=VECTOR('',#617,10.);
#167=VECTOR('',#618,10.);
#168=VECTOR('',#619,10.);
#169=VECTOR('',#626,10.);
#170=VECTOR('',#629,10.);
#171=VECTOR('',#630,10.);
#172=VECTOR('',#631,10.);
#173=VECTOR('',#634,10.);
#174=VECTOR('',#635,10.);
#175=VECTOR('',#636,10.);
#176=VECTOR('',#639,10.);
#177=VECTOR('',#640,10.);
#178=VECTOR('',#641,10.);
#179=VECTOR('',#644,10.);
#180=VECTOR('',#645,10.);
#181=VECTOR('',#646,10.);
#182=VECTOR('',#649,10.);
#183=VECTOR('',#650,10.);
#184=VECTOR('',#651,10.);
#185=VECTOR('',#654,10.);
#186=VECTOR('',#655,10.);
#187=VECTOR('',#656,10.);
#188=VECTOR('',#659,10.);
#189=VECTOR('',#660,10.);
#190=VECTOR('',#661,10.);
#191=VECTOR('',#668,10.);
#192=VECTOR('',#675,10.);
#193=VERTEX_POINT('',#688);
#194=VERTEX_POINT('',#689);
#195=VERTEX_POINT('',#691);
#196=VERTEX_POINT('',#693);
#197=VERTEX_POINT('',#697);
#198=VERTEX_POINT('',#699);
#199=VERTEX_POINT('',#703);
#200=VERTEX_POINT('',#705);
#201=VERTEX_POINT('',#709);
#202=VERTEX_POINT('',#711);
#203=VERTEX_POINT('',#715);
#204=VERTEX_POINT('',#717);
#205=VERTEX_POINT('',#721);
#206=VERTEX_POINT('',#723);
#207=VERTEX_POINT('',#727);
#208=VERTEX_POINT('',#729);
#209=VERTEX_POINT('',#733);
#210=VERTEX_POINT('',#735);
#211=VERTEX_POINT('',#739);
#212=VERTEX_POINT('',#741);
#213=VERTEX_POINT('',#745);
#214=VERTEX_POINT('',#747);
#215=VERTEX_POINT('',#751);
#216=VERTEX_POINT('',#753);
#217=VERTEX_POINT('',#757);
#218=VERTEX_POINT('',#759);
#219=VERTEX_POINT('',#763);
#220=VERTEX_POINT('',#765);
#221=VERTEX_POINT('',#769);
#222=VERTEX_POINT('',#771);
#223=VERTEX_POINT('',#775);
#224=VERTEX_POINT('',#777);
#225=VERTEX_POINT('',#781);
#226=VERTEX_POINT('',#783);
#227=VERTEX_POINT('',#787);
#228=VERTEX_POINT('',#789);
#229=VERTEX_POINT('',#793);
#230=VERTEX_POINT('',#795);
#231=VERTEX_POINT('',#799);
#232=VERTEX_POINT('',#801);
#233=VERTEX_POINT('',#805);
#234=VERTEX_POINT('',#807);
#235=VERTEX_POINT('',#811);
#236=VERTEX_POINT('',#813);
#237=VERTEX_POINT('',#817);
#238=VERTEX_POINT('',#819);
#239=EDGE_CURVE('',#193,#194,#95,.T.);
#240=EDGE_CURVE('',#193,#195,#96,.T.);
#241=EDGE_CURVE('',#196,#195,#97,.T.);
#242=EDGE_CURVE('',#194,#196,#98,.T.);
#243=EDGE_CURVE('',#197,#194,#15,.T.);
#244=EDGE_CURVE('',#198,#196,#16,.T.);
#245=EDGE_CURVE('',#197,#198,#99,.T.);
#246=EDGE_CURVE('',#199,#197,#100,.T.);
#247=EDGE_CURVE('',#200,#198,#101,.T.);
#248=EDGE_CURVE('',#199,#200,#102,.T.);
#249=EDGE_CURVE('',#201,#199,#17,.T.);
#250=EDGE_CURVE('',#202,#200,#18,.T.);
#251=EDGE_CURVE('',#201,#202,#103,.T.);
#252=EDGE_CURVE('',#203,#201,#104,.T.);
#253=EDGE_CURVE('',#204,#202,#105,.T.);
#254=EDGE_CURVE('',#203,#204,#106,.T.);
#255=EDGE_CURVE('',#205,#203,#19,.T.);
#256=EDGE_CURVE('',#206,#204,#20,.T.);
#257=EDGE_CURVE('',#205,#206,#107,.T.);
#258=EDGE_CURVE('',#205,#207,#108,.T.);
#259=EDGE_CURVE('',#208,#206,#109,.T.);
#260=EDGE_CURVE('',#207,#208,#110,.T.);
#261=EDGE_CURVE('',#207,#209,#21,.T.);
#262=EDGE_CURVE('',#210,#208,#22,.T.);
#263=EDGE_CURVE('',#209,#210,#111,.T.);
#264=EDGE_CURVE('',#209,#211,#23,.T.);
#265=EDGE_CURVE('',#212,#210,#24,.T.);
#266=EDGE_CURVE('',#211,#212,#112,.T.);
#267=EDGE_CURVE('',#213,#211,#113,.T.);
#268=EDGE_CURVE('',#214,#212,#114,.T.);
#269=EDGE_CURVE('',#213,#214,#115,.T.);
#270=EDGE_CURVE('',#215,#213,#25,.T.);
#271=EDGE_CURVE('',#216,#214,#26,.T.);
#272=EDGE_CURVE('',#215,#216,#116,.T.);
#273=EDGE_CURVE('',#217,#215,#117,.T.);
#274=EDGE_CURVE('',#218,#216,#118,.T.);
#275=EDGE_CURVE('',#217,#218,#119,.T.);
#276=EDGE_CURVE('',#219,#217,#27,.T.);
#277=EDGE_CURVE('',#220,#218,#28,.T.);
#278=EDGE_CURVE('',#219,#220,#120,.T.);
#279=EDGE_CURVE('',#221,#219,#121,.T.);
#280=EDGE_CURVE('',#222,#220,#122,.T.);
#281=EDGE_CURVE('',#221,#222,#123,.T.);
#282=EDGE_CURVE('',#221,#223,#124,.T.);
#283=EDGE_CURVE('',#224,#222,#125,.T.);
#284=EDGE_CURVE('',#223,#224,#126,.T.);
#285=EDGE_CURVE('',#225,#223,#127,.T.);
#286=EDGE_CURVE('',#226,#224,#128,.T.);
#287=EDGE_CURVE('',#225,#226,#129,.T.);
#288=EDGE_CURVE('',#227,#225,#130,.T.);
#289=EDGE_CURVE('',#228,#226,#131,.T.);
#290=EDGE_CURVE('',#227,#228,#132,.T.);
#291=EDGE_CURVE('',#229,#227,#133,.T.);
#292=EDGE_CURVE('',#230,#228,#134,.T.);
#293=EDGE_CURVE('',#229,#230,#135,.T.);
#294=EDGE_CURVE('',#231,#229,#136,.T.);
#295=EDGE_CURVE('',#232,#230,#137,.T.);
#296=EDGE_CURVE('',#231,#232,#138,.T.);
#297=EDGE_CURVE('',#233,#231,#139,.T.);
#298=EDGE_CURVE('',#234,#232,#140,.T.);
#299=EDGE_CURVE('',#233,#234,#141,.T.);
#300=EDGE_CURVE('',#235,#233,#29,.T.);
#301=EDGE_CURVE('',#236,#234,#30,.T.);
#302=EDGE_CURVE('',#235,#236,#142,.T.);
#303=EDGE_CURVE('',#235,#237,#31,.T.);
#304=EDGE_CURVE('',#238,#236,#32,.T.);
#305=EDGE_CURVE('',#237,#238,#143,.T.);
#306=EDGE_CURVE('',#193,#237,#33,.T.);
#307=EDGE_CURVE('',#195,#238,#34,.T.);
#308=ORIENTED_EDGE('',*,*,#239,.F.);
#309=ORIENTED_EDGE('',*,*,#240,.T.);
#310=ORIENTED_EDGE('',*,*,#241,.F.);
#311=ORIENTED_EDGE('',*,*,#242,.F.);
#312=ORIENTED_EDGE('',*,*,#243,.T.);
#313=ORIENTED_EDGE('',*,*,#242,.T.);
#314=ORIENTED_EDGE('',*,*,#244,.F.);
#315=ORIENTED_EDGE('',*,*,#245,.F.);
#316=ORIENTED_EDGE('',*,*,#246,.T.);
#317=ORIENTED_EDGE('',*,*,#245,.T.);
#318=ORIENTED_EDGE('',*,*,#247,.F.);
#319=ORIENTED_EDGE('',*,*,#248,.F.);
#320=ORIENTED_EDGE('',*,*,#249,.T.);
#321=ORIENTED_EDGE('',*,*,#248,.T.);
#322=ORIENTED_EDGE('',*,*,#250,.F.);
#323=ORIENTED_EDGE('',*,*,#251,.F.);
#324=ORIENTED_EDGE('',*,*,#252,.T.);
#325=ORIENTED_EDGE('',*,*,#251,.T.);
#326=ORIENTED_EDGE('',*,*,#253,.F.);
#327=ORIENTED_EDGE('',*,*,#254,.F.);
#328=ORIENTED_EDGE('',*,*,#255,.T.);
#329=ORIENTED_EDGE('',*,*,#254,.T.);
#330=ORIENTED_EDGE('',*,*,#256,.F.);
#331=ORIENTED_EDGE('',*,*,#257,.F.);
#332=ORIENTED_EDGE('',*,*,#258,.F.);
#333=ORIENTED_EDGE('',*,*,#257,.T.);
#334=ORIENTED_EDGE('',*,*,#259,.F.);
#335=ORIENTED_EDGE('',*,*,#260,.F.);
#336=ORIENTED_EDGE('',*,*,#261,.F.);
#337=ORIENTED_EDGE('',*,*,#260,.T.);
#338=ORIENTED_EDGE('',*,*,#262,.F.);
#339=ORIENTED_EDGE('',*,*,#263,.F.);
#340=ORIENTED_EDGE('',*,*,#264,.F.);
#341=ORIENTED_EDGE('',*,*,#263,.T.);
#342=ORIENTED_EDGE('',*,*,#265,.F.);
#343=ORIENTED_EDGE('',*,*,#266,.F.);
#344=ORIENTED_EDGE('',*,*,#267,.T.);
#345=ORIENTED_EDGE('',*,*,#266,.T.);
#346=ORIENTED_EDGE('',*,*,#268,.F.);
#347=ORIENTED_EDGE('',*,*,#269,.F.);
#348=ORIENTED_EDGE('',*,*,#270,.T.);
#349=ORIENTED_EDGE('',*,*,#269,.T.);
#350=ORIENTED_EDGE('',*,*,#271,.F.);
#351=ORIENTED_EDGE('',*,*,#272,.F.);
#352=ORIENTED_EDGE('',*,*,#273,.T.);
#353=ORIENTED_EDGE('',*,*,#272,.T.);
#354=ORIENTED_EDGE('',*,*,#274,.F.);
#355=ORIENTED_EDGE('',*,*,#275,.F.);
#356=ORIENTED_EDGE('',*,*,#276,.T.);
#357=ORIENTED_EDGE('',*,*,#275,.T.);
#358=ORIENTED_EDGE('',*,*,#277,.F.);
#359=ORIENTED_EDGE('',*,*,#278,.F.);
#360=ORIENTED_EDGE('',*,*,#279,.T.);
#361=ORIENTED_EDGE('',*,*,#278,.T.);
#362=ORIENTED_EDGE('',*,*,#280,.F.);
#363=ORIENTED_EDGE('',*,*,#281,.F.);
#364=ORIENTED_EDGE('',*,*,#282,.F.);
#365=ORIENTED_EDGE('',*,*,#281,.T.);
#366=ORIENTED_EDGE('',*,*,#283,.F.);
#367=ORIENTED_EDGE('',*,*,#284,.F.);
#368=ORIENTED_EDGE('',*,*,#285,.T.);
#369=ORIENTED_EDGE('',*,*,#284,.T.);
#370=ORIENTED_EDGE('',*,*,#286,.F.);
#371=ORIENTED_EDGE('',*,*,#287,.F.);
#372=ORIENTED_EDGE('',*,*,#288,.T.);
#373=ORIENTED_EDGE('',*,*,#287,.T.);
#374=ORIENTED_EDGE('',*,*,#289,.F.);
#375=ORIENTED_EDGE('',*,*,#290,.F.);
#376=ORIENTED_EDGE('',*,*,#291,.T.);
#377=ORIENTED_EDGE('',*,*,#290,.T.);
#378=ORIENTED_EDGE('',*,*,#292,.F.);
#379=ORIENTED_EDGE('',*,*,#293,.F.);
#380=ORIENTED_EDGE('',*,*,#294,.T.);
#381=ORIENTED_EDGE('',*,*,#293,.T.);
#382=ORIENTED_EDGE('',*,*,#295,.F.);
#383=ORIENTED_EDGE('',*,*,#296,.F.);
#384=ORIENTED_EDGE('',*,*,#297,.T.);
#385=ORIENTED_EDGE('',*,*,#296,.T.);
#386=ORIENTED_EDGE('',*,*,#298,.F.);
#387=ORIENTED_EDGE('',*,*,#299,.F.);
#388=ORIENTED_EDGE('',*,*,#300,.T.);
#389=ORIENTED_EDGE('',*,*,#299,.T.);
#390=ORIENTED_EDGE('',*,*,#301,.F.);
#391=ORIENTED_EDGE('',*,*,#302,.F.);
#392=ORIENTED_EDGE('',*,*,#303,.F.);
#393=ORIENTED_EDGE('',*,*,#302,.T.);
#394=ORIENTED_EDGE('',*,*,#304,.F.);
#395=ORIENTED_EDGE('',*,*,#305,.F.);
#396=ORIENTED_EDGE('',*,*,#306,.T.);
#397=ORIENTED_EDGE('',*,*,#305,.T.);
#398=ORIENTED_EDGE('',*,*,#307,.F.);
#399=ORIENTED_EDGE('',*,*,#240,.F.);
#400=ORIENTED_EDGE('',*,*,#307,.T.);
#401=ORIENTED_EDGE('',*,*,#304,.T.);
#402=ORIENTED_EDGE('',*,*,#301,.T.);
#403=ORIENTED_EDGE('',*,*,#298,.T.);
#404=ORIENTED_EDGE('',*,*,#295,.T.);
#405=ORIENTED_EDGE('',*,*,#292,.T.);
#406=ORIENTED_EDGE('',*,*,#289,.T.);
#407=ORIENTED_EDGE('',*,*,#286,.T.);
#408=ORIENTED_EDGE('',*,*,#283,.T.);
#409=ORIENTED_EDGE('',*,*,#280,.T.);
#410=ORIENTED_EDGE('',*,*,#277,.T.);
#411=ORIENTED_EDGE('',*,*,#274,.T.);
#412=ORIENTED_EDGE('',*,*,#271,.T.);
#413=ORIENTED_EDGE('',*,*,#268,.T.);
#414=ORIENTED_EDGE('',*,*,#265,.T.);
#415=ORIENTED_EDGE('',*,*,#262,.T.);
#416=ORIENTED_EDGE('',*,*,#259,.T.);
#417=ORIENTED_EDGE('',*,*,#256,.T.);
#418=ORIENTED_EDGE('',*,*,#253,.T.);
#419=ORIENTED_EDGE('',*,*,#250,.T.);
#420=ORIENTED_EDGE('',*,*,#247,.T.);
#421=ORIENTED_EDGE('',*,*,#244,.T.);
#422=ORIENTED_EDGE('',*,*,#241,.T.);
#423=ORIENTED_EDGE('',*,*,#306,.F.);
#424=ORIENTED_EDGE('',*,*,#239,.T.);
#425=ORIENTED_EDGE('',*,*,#243,.F.);
#426=ORIENTED_EDGE('',*,*,#246,.F.);
#427=ORIENTED_EDGE('',*,*,#249,.F.);
#428=ORIENTED_EDGE('',*,*,#252,.F.);
#429=ORIENTED_EDGE('',*,*,#255,.F.);
#430=ORIENTED_EDGE('',*,*,#258,.T.);
#431=ORIENTED_EDGE('',*,*,#261,.T.);
#432=ORIENTED_EDGE('',*,*,#264,.T.);
#433=ORIENTED_EDGE('',*,*,#267,.F.);
#434=ORIENTED_EDGE('',*,*,#270,.F.);
#435=ORIENTED_EDGE('',*,*,#273,.F.);
#436=ORIENTED_EDGE('',*,*,#276,.F.);
#437=ORIENTED_EDGE('',*,*,#279,.F.);
#438=ORIENTED_EDGE('',*,*,#282,.T.);
#439=ORIENTED_EDGE('',*,*,#285,.F.);
#440=ORIENTED_EDGE('',*,*,#288,.F.);
#441=ORIENTED_EDGE('',*,*,#291,.F.);
#442=ORIENTED_EDGE('',*,*,#294,.F.);
#443=ORIENTED_EDGE('',*,*,#297,.F.);
#444=ORIENTED_EDGE('',*,*,#300,.F.);
#445=ORIENTED_EDGE('',*,*,#303,.T.);
#446=PLANE('',#500);
#447=PLANE('',#504);
#448=PLANE('',#508);
#449=PLANE('',#512);
#450=PLANE('',#519);
#451=PLANE('',#523);
#452=PLANE('',#527);
#453=PLANE('',#528);
#454=PLANE('',#529);
#455=PLANE('',#530);
#456=PLANE('',#531);
#457=PLANE('',#532);
#458=PLANE('',#533);
#459=PLANE('',#543);
#460=PLANE('',#544);
#461=ADVANCED_FACE('',(#45),#446,.T.);
#462=ADVANCED_FACE('',(#46),#35,.T.);
#463=ADVANCED_FACE('',(#47),#447,.T.);
#464=ADVANCED_FACE('',(#48),#36,.T.);
#465=ADVANCED_FACE('',(#49),#448,.T.);
#466=ADVANCED_FACE('',(#50),#37,.T.);
#467=ADVANCED_FACE('',(#51),#449,.T.);
#468=ADVANCED_FACE('',(#52),#38,.F.);
#469=ADVANCED_FACE('',(#53),#39,.F.);
#470=ADVANCED_FACE('',(#54),#450,.T.);
#471=ADVANCED_FACE('',(#55),#40,.T.);
#472=ADVANCED_FACE('',(#56),#451,.T.);
#473=ADVANCED_FACE('',(#57),#41,.T.);
#474=ADVANCED_FACE('',(#58),#452,.T.);
#475=ADVANCED_FACE('',(#59),#453,.T.);
#476=ADVANCED_FACE('',(#60),#454,.T.);
#477=ADVANCED_FACE('',(#61),#455,.T.);
#478=ADVANCED_FACE('',(#62),#456,.T.);
#479=ADVANCED_FACE('',(#63),#457,.T.);
#480=ADVANCED_FACE('',(#64),#458,.T.);
#481=ADVANCED_FACE('',(#65),#42,.T.);
#482=ADVANCED_FACE('',(#66),#43,.F.);
#483=ADVANCED_FACE('',(#67),#44,.T.);
#484=ADVANCED_FACE('',(#68),#459,.T.);
#485=ADVANCED_FACE('',(#69),#460,.F.);
#486=CLOSED_SHELL('',(#461,#462,#463,#464,#465,#466,#467,#468,#469,#470,
#471,#472,#473,#474,#475,#476,#477,#478,#479,#480,#481,#482,#483,#484,#485));
#487=DERIVED_UNIT_ELEMENT(#489,1.);
#488=DERIVED_UNIT_ELEMENT(#832,-3.);
#489=(
MASS_UNIT()
NAMED_UNIT(*)
SI_UNIT(.KILO.,.GRAM.)
);
#490=DERIVED_UNIT((#487,#488));
#491=MEASURE_REPRESENTATION_ITEM('density measure',
POSITIVE_RATIO_MEASURE(7850.),#490);
#492=PROPERTY_DEFINITION_REPRESENTATION(#497,#494);
#493=PROPERTY_DEFINITION_REPRESENTATION(#498,#495);
#494=REPRESENTATION('material name',(#496),#829);
#495=REPRESENTATION('density',(#491),#829);
#496=DESCRIPTIVE_REPRESENTATION_ITEM('Steel','Steel');
#497=PROPERTY_DEFINITION('material property','material name',#839);
#498=PROPERTY_DEFINITION('material property','density of part',#839);
#499=AXIS2_PLACEMENT_3D('placement',#686,#545,#546);
#500=AXIS2_PLACEMENT_3D('',#687,#547,#548);
#501=AXIS2_PLACEMENT_3D('',#696,#553,#554);
#502=AXIS2_PLACEMENT_3D('',#698,#555,#556);
#503=AXIS2_PLACEMENT_3D('',#700,#557,#558);
#504=AXIS2_PLACEMENT_3D('',#702,#560,#561);
#505=AXIS2_PLACEMENT_3D('',#708,#565,#566);
#506=AXIS2_PLACEMENT_3D('',#710,#567,#568);
#507=AXIS2_PLACEMENT_3D('',#712,#569,#570);
#508=AXIS2_PLACEMENT_3D('',#714,#572,#573);
#509=AXIS2_PLACEMENT_3D('',#720,#577,#578);
#510=AXIS2_PLACEMENT_3D('',#722,#579,#580);
#511=AXIS2_PLACEMENT_3D('',#724,#581,#582);
#512=AXIS2_PLACEMENT_3D('',#726,#584,#585);
#513=AXIS2_PLACEMENT_3D('',#732,#589,#590);
#514=AXIS2_PLACEMENT_3D('',#734,#591,#592);
#515=AXIS2_PLACEMENT_3D('',#736,#593,#594);
#516=AXIS2_PLACEMENT_3D('',#738,#596,#597);
#517=AXIS2_PLACEMENT_3D('',#740,#598,#599);
#518=AXIS2_PLACEMENT_3D('',#742,#600,#601);
#519=AXIS2_PLACEMENT_3D('',#744,#603,#604);
#520=AXIS2_PLACEMENT_3D('',#750,#608,#609);
#521=AXIS2_PLACEMENT_3D('',#752,#610,#611);
#522=AXIS2_PLACEMENT_3D('',#754,#612,#613);
#523=AXIS2_PLACEMENT_3D('',#756,#615,#616);
#524=AXIS2_PLACEMENT_3D('',#762,#620,#621);
#525=AXIS2_PLACEMENT_3D('',#764,#622,#623);
#526=AXIS2_PLACEMENT_3D('',#766,#624,#625);
#527=AXIS2_PLACEMENT_3D('',#768,#627,#628);
#528=AXIS2_PLACEMENT_3D('',#774,#632,#633);
#529=AXIS2_PLACEMENT_3D('',#780,#637,#638);
#530=AXIS2_PLACEMENT_3D('',#786,#642,#643);
#531=AXIS2_PLACEMENT_3D('',#792,#647,#648);
#532=AXIS2_PLACEMENT_3D('',#798,#652,#653);
#533=AXIS2_PLACEMENT_3D('',#804,#657,#658);
#534=AXIS2_PLACEMENT_3D('',#810,#662,#663);
#535=AXIS2_PLACEMENT_3D('',#812,#664,#665);
#536=AXIS2_PLACEMENT_3D('',#814,#666,#667);
#537=AXIS2_PLACEMENT_3D('',#816,#669,#670);
#538=AXIS2_PLACEMENT_3D('',#818,#671,#672);
#539=AXIS2_PLACEMENT_3D('',#820,#673,#674);
#540=AXIS2_PLACEMENT_3D('',#822,#676,#677);
#541=AXIS2_PLACEMENT_3D('',#823,#678,#679);
#542=AXIS2_PLACEMENT_3D('',#824,#680,#681);
#543=AXIS2_PLACEMENT_3D('',#825,#682,#683);
#544=AXIS2_PLACEMENT_3D('',#826,#684,#685);
#545=DIRECTION('axis',(0.,0.,1.));
#546=DIRECTION('refdir',(1.,0.,0.));
#547=DIRECTION('center_axis',(-1.,0.,0.));
#548=DIRECTION('ref_axis',(0.,-1.,0.));
#549=DIRECTION('',(0.,1.,0.));
#550=DIRECTION('',(0.,0.,1.));
#551=DIRECTION('',(0.,-1.,0.));
#552=DIRECTION('',(0.,0.,1.));
#553=DIRECTION('center_axis',(0.,0.,1.));
#554=DIRECTION('ref_axis',(8.88178419700128E-15,1.,0.));
#555=DIRECTION('center_axis',(0.,0.,1.));
#556=DIRECTION('ref_axis',(8.88178419700128E-15,1.,0.));
#557=DIRECTION('center_axis',(0.,0.,1.));
#558=DIRECTION('ref_axis',(8.88178419700128E-15,1.,0.));
#559=DIRECTION('',(0.,0.,1.));
#560=DIRECTION('center_axis',(1.61486985400023E-15,1.,0.));
#561=DIRECTION('ref_axis',(-1.,1.61486985400023E-15,0.));
#562=DIRECTION('',(-1.,1.61486985400023E-15,0.));
#563=DIRECTION('',(-1.,1.61486985400023E-15,0.));
#564=DIRECTION('',(0.,0.,1.));
#565=DIRECTION('center_axis',(0.,0.,1.));
#566=DIRECTION('ref_axis',(1.,0.,0.));
#567=DIRECTION('center_axis',(0.,0.,1.));
#568=DIRECTION('ref_axis',(1.,0.,0.));
#569=DIRECTION('center_axis',(0.,0.,1.));
#570=DIRECTION('ref_axis',(1.,0.,0.));
#571=DIRECTION('',(0.,0.,1.));
#572=DIRECTION('center_axis',(1.,0.,0.));
#573=DIRECTION('ref_axis',(0.,1.,0.));
#574=DIRECTION('',(0.,1.,0.));
#575=DIRECTION('',(0.,1.,0.));
#576=DIRECTION('',(0.,0.,1.));
#577=DIRECTION('center_axis',(0.,0.,1.));
#578=DIRECTION('ref_axis',(0.,-1.,0.));
#579=DIRECTION('center_axis',(0.,0.,1.));
#580=DIRECTION('ref_axis',(0.,-1.,0.));
#581=DIRECTION('center_axis',(0.,0.,1.));
#582=DIRECTION('ref_axis',(0.,-1.,0.));
#583=DIRECTION('',(0.,0.,1.));
#584=DIRECTION('center_axis',(-1.31581988103722E-15,-1.,0.));
#585=DIRECTION('ref_axis',(1.,-1.31581988103722E-15,0.));
#586=DIRECTION('',(-1.,1.31581988103722E-15,0.));
#587=DIRECTION('',(1.,-1.31581988103722E-15,0.));
#588=DIRECTION('',(0.,0.,1.));
#589=DIRECTION('center_axis',(0.,0.,1.));
#590=DIRECTION('ref_axis',(1.4210854715202E-16,1.,0.));
#591=DIRECTION('center_axis',(0.,0.,1.));
#592=DIRECTION('ref_axis',(1.4210854715202E-16,1.,0.));
#593=DIRECTION('center_axis',(0.,0.,-1.));
#594=DIRECTION('ref_axis',(1.4210854715202E-16,1.,0.));
#595=DIRECTION('',(0.,0.,1.));
#596=DIRECTION('center_axis',(0.,0.,1.));
#597=DIRECTION('ref_axis',(-0.422277078441888,0.906466805251347,0.));
#598=DIRECTION('center_axis',(0.,0.,1.));
#599=DIRECTION('ref_axis',(-0.422277078441888,0.906466805251347,0.));
#600=DIRECTION('center_axis',(0.,0.,-1.));
#601=DIRECTION('ref_axis',(-0.422277078441888,0.906466805251347,0.));
#602=DIRECTION('',(0.,0.,1.));
#603=DIRECTION('center_axis',(0.994298127062704,-0.10663599073296,0.));
#604=DIRECTION('ref_axis',(0.10663599073296,0.994298127062703,0.));
#605=DIRECTION('',(0.10663599073296,0.994298127062703,0.));
#606=DIRECTION('',(0.10663599073296,0.994298127062703,0.));
#607=DIRECTION('',(0.,0.,1.));
#608=DIRECTION('center_axis',(0.,0.,1.));
#609=DIRECTION('ref_axis',(1.11022302462516E-15,-1.,0.));
#610=DIRECTION('center_axis',(0.,0.,1.));
#611=DIRECTION('ref_axis',(1.11022302462516E-15,-1.,0.));
#612=DIRECTION('center_axis',(0.,0.,1.));
#613=DIRECTION('ref_axis',(1.11022302462516E-15,-1.,0.));
#614=DIRECTION('',(0.,0.,1.));
#615=DIRECTION('center_axis',(0.,-1.,0.));
#616=DIRECTION('ref_axis',(1.,0.,0.));
#617=DIRECTION('',(1.,0.,0.));
#618=DIRECTION('',(1.,0.,0.));
#619=DIRECTION('',(0.,0.,1.));
#620=DIRECTION('center_axis',(0.,0.,1.));
#621=DIRECTION('ref_axis',(-1.,0.,0.));
#622=DIRECTION('center_axis',(0.,0.,1.));
#623=DIRECTION('ref_axis',(-1.,0.,0.));
#624=DIRECTION('center_axis',(0.,0.,1.));
#625=DIRECTION('ref_axis',(-1.,0.,0.));
#626=DIRECTION('',(0.,0.,1.));
#627=DIRECTION('center_axis',(-1.,0.,0.));
#628=DIRECTION('ref_axis',(0.,-1.,0.));
#629=DIRECTION('',(0.,-1.,0.));
#630=DIRECTION('',(0.,-1.,0.));
#631=DIRECTION('',(0.,0.,1.));
#632=DIRECTION('center_axis',(0.,1.,0.));
#633=DIRECTION('ref_axis',(-1.,0.,0.));
#634=DIRECTION('',(1.,0.,0.));
#635=DIRECTION('',(-1.,0.,0.));
#636=DIRECTION('',(0.,0.,1.));
#637=DIRECTION('center_axis',(-1.,-5.73462306108035E-19,0.));
#638=DIRECTION('ref_axis',(5.73462306108035E-19,-1.,0.));
#639=DIRECTION('',(5.73462306108035E-19,-1.,0.));
#640=DIRECTION('',(5.73462306108035E-19,-1.,0.));
#641=DIRECTION('',(0.,0.,1.));
#642=DIRECTION('center_axis',(0.,-1.,0.));
#643=DIRECTION('ref_axis',(1.,0.,0.));
#644=DIRECTION('',(1.,0.,0.));
#645=DIRECTION('',(1.,0.,0.));
#646=DIRECTION('',(0.,0.,1.));
#647=DIRECTION('center_axis',(-1.,0.,0.));
#648=DIRECTION('ref_axis',(0.,-1.,0.));
#649=DIRECTION('',(0.,-1.,0.));
#650=DIRECTION('',(0.,-1.,0.));
#651=DIRECTION('',(0.,0.,1.));
#652=DIRECTION('center_axis',(-0.707106781186548,0.707106781186548,0.));
#653=DIRECTION('ref_axis',(-0.707106781186548,-0.707106781186548,0.));
#654=DIRECTION('',(-0.707106781186547,-0.707106781186547,0.));
#655=DIRECTION('',(-0.707106781186547,-0.707106781186547,0.));
#656=DIRECTION('',(0.,0.,1.));
#657=DIRECTION('center_axis',(0.,1.,0.));
#658=DIRECTION('ref_axis',(-1.,0.,0.));
#659=DIRECTION('',(-1.,0.,0.));
#660=DIRECTION('',(-1.,0.,0.));
#661=DIRECTION('',(0.,0.,1.));
#662=DIRECTION('center_axis',(0.,0.,1.));
#663=DIRECTION('ref_axis',(0.600000000000043,0.799999999999968,0.));
#664=DIRECTION('center_axis',(0.,0.,1.));
#665=DIRECTION('ref_axis',(0.600000000000043,0.799999999999968,0.));
#666=DIRECTION('center_axis',(0.,0.,1.));
#667=DIRECTION('ref_axis',(0.600000000000043,0.799999999999968,0.));
#668=DIRECTION('',(0.,0.,1.));
#669=DIRECTION('center_axis',(0.,0.,1.));
#670=DIRECTION('ref_axis',(-0.599999999999994,-0.800000000000004,0.));
#671=DIRECTION('center_axis',(0.,0.,1.));
#672=DIRECTION('ref_axis',(-0.599999999999994,-0.800000000000004,0.));
#673=DIRECTION('center_axis',(0.,0.,-1.));
#674=DIRECTION('ref_axis',(-0.599999999999994,-0.800000000000004,0.));
#675=DIRECTION('',(0.,0.,1.));
#676=DIRECTION('center_axis',(0.,0.,1.));
#677=DIRECTION('ref_axis',(-1.,0.,0.));
#678=DIRECTION('center_axis',(0.,0.,1.));
#679=DIRECTION('ref_axis',(-1.,0.,0.));
#680=DIRECTION('center_axis',(0.,0.,1.));
#681=DIRECTION('ref_axis',(-1.,0.,0.));
#682=DIRECTION('center_axis',(0.,0.,1.));
#683=DIRECTION('ref_axis',(1.,0.,0.));
#684=DIRECTION('center_axis',(0.,0.,1.));
#685=DIRECTION('ref_axis',(1.,0.,0.));
#686=CARTESIAN_POINT('',(0.,0.,0.));
#687=CARTESIAN_POINT('Origin',(75.,156.5,0.));
#688=CARTESIAN_POINT('',(75.,151.6,0.));
#689=CARTESIAN_POINT('',(75.,156.5,0.));
#690=CARTESIAN_POINT('',(75.,151.6,0.));
#691=CARTESIAN_POINT('',(75.,151.6,8.));
#692=CARTESIAN_POINT('',(75.,151.6,0.));
#693=CARTESIAN_POINT('',(75.,156.5,8.));
#694=CARTESIAN_POINT('',(75.,151.6,8.));
#695=CARTESIAN_POINT('',(75.,156.5,0.));
#696=CARTESIAN_POINT('Origin',(77.,156.5,0.));
#697=CARTESIAN_POINT('',(77.,158.5,0.));
#698=CARTESIAN_POINT('Origin',(77.,156.5,0.));
#699=CARTESIAN_POINT('',(77.,158.5,8.));
#700=CARTESIAN_POINT('Origin',(77.,156.5,8.));
#701=CARTESIAN_POINT('',(77.,158.5,0.));
#702=CARTESIAN_POINT('Origin',(88.,158.5,0.));
#703=CARTESIAN_POINT('',(88.,158.5,0.));
#704=CARTESIAN_POINT('',(88.,158.5,0.));
#705=CARTESIAN_POINT('',(88.,158.5,8.));
#706=CARTESIAN_POINT('',(88.,158.5,8.));
#707=CARTESIAN_POINT('',(88.,158.5,0.));
#708=CARTESIAN_POINT('Origin',(88.,156.5,0.));
#709=CARTESIAN_POINT('',(90.,156.5,0.));
#710=CARTESIAN_POINT('Origin',(88.,156.5,0.));
#711=CARTESIAN_POINT('',(90.,156.5,8.));
#712=CARTESIAN_POINT('Origin',(88.,156.5,8.));
#713=CARTESIAN_POINT('',(90.,156.5,0.));
#714=CARTESIAN_POINT('Origin',(90.,140.5,0.));
#715=CARTESIAN_POINT('',(90.,140.5,0.));
#716=CARTESIAN_POINT('',(90.,140.5,0.));
#717=CARTESIAN_POINT('',(90.,140.5,8.));
#718=CARTESIAN_POINT('',(90.,140.5,8.));
#719=CARTESIAN_POINT('',(90.,140.5,0.));
#720=CARTESIAN_POINT('Origin',(88.,140.5,0.));
#721=CARTESIAN_POINT('',(88.,138.5,0.));
#722=CARTESIAN_POINT('Origin',(88.,140.5,0.));
#723=CARTESIAN_POINT('',(88.,138.5,8.));
#724=CARTESIAN_POINT('Origin',(88.,140.5,8.));
#725=CARTESIAN_POINT('',(88.,138.5,0.));
#726=CARTESIAN_POINT('Origin',(74.5,138.5,0.));
#727=CARTESIAN_POINT('',(74.5,138.5,0.));
#728=CARTESIAN_POINT('',(88.,138.5,0.));
#729=CARTESIAN_POINT('',(74.5,138.5,8.));
#730=CARTESIAN_POINT('',(88.,138.5,8.));
#731=CARTESIAN_POINT('',(74.5,138.5,0.));
#732=CARTESIAN_POINT('Origin',(74.5,76.,0.));
#733=CARTESIAN_POINT('',(48.107682597382,132.654175328209,0.));
#734=CARTESIAN_POINT('Origin',(74.5,76.,0.));
#735=CARTESIAN_POINT('',(48.107682597382,132.654175328209,8.));
#736=CARTESIAN_POINT('Origin',(74.5,76.,8.));
#737=CARTESIAN_POINT('',(48.107682597382,132.654175328209,0.));
#738=CARTESIAN_POINT('Origin',(67.1101511272669,91.8631690918986,0.));
#739=CARTESIAN_POINT('',(22.3667354094453,96.6617886748818,0.));
#740=CARTESIAN_POINT('Origin',(67.1101511272669,91.8631690918986,0.));
#741=CARTESIAN_POINT('',(22.3667354094453,96.6617886748818,8.));
#742=CARTESIAN_POINT('Origin',(67.1101511272669,91.8631690918986,8.));
#743=CARTESIAN_POINT('',(22.3667354094453,96.6617886748818,0.));
#744=CARTESIAN_POINT('Origin',(12.1916221173921,1.78672801853408,0.));
#745=CARTESIAN_POINT('',(12.1916221173921,1.78672801853408,0.));
#746=CARTESIAN_POINT('',(12.1916221173921,1.78672801853408,0.));
#747=CARTESIAN_POINT('',(12.1916221173921,1.78672801853407,8.));
#748=CARTESIAN_POINT('',(12.1916221173921,1.78672801853408,8.));
#749=CARTESIAN_POINT('',(12.1916221173921,1.78672801853408,0.));
#750=CARTESIAN_POINT('Origin',(10.2030258632667,2.,0.));
#751=CARTESIAN_POINT('',(10.2030258632667,0.,0.));
#752=CARTESIAN_POINT('Origin',(10.2030258632667,2.,0.));
#753=CARTESIAN_POINT('',(10.2030258632667,0.,8.));
#754=CARTESIAN_POINT('Origin',(10.2030258632667,2.,8.));
#755=CARTESIAN_POINT('',(10.2030258632667,0.,0.));
#756=CARTESIAN_POINT('Origin',(2.,0.,0.));
#757=CARTESIAN_POINT('',(2.,0.,0.));
#758=CARTESIAN_POINT('',(2.,0.,0.));
#759=CARTESIAN_POINT('',(2.,0.,8.));
#760=CARTESIAN_POINT('',(2.,0.,8.));
#761=CARTESIAN_POINT('',(2.,0.,0.));
#762=CARTESIAN_POINT('Origin',(2.,2.,0.));
#763=CARTESIAN_POINT('',(0.,2.,0.));
#764=CARTESIAN_POINT('Origin',(2.,2.,0.));
#765=CARTESIAN_POINT('',(0.,1.99999999999999,8.));
#766=CARTESIAN_POINT('Origin',(2.,2.,8.));
#767=CARTESIAN_POINT('',(0.,2.,0.));
#768=CARTESIAN_POINT('Origin',(0.,45.,0.));
#769=CARTESIAN_POINT('',(0.,45.,0.));
#770=CARTESIAN_POINT('',(0.,45.,0.));
#771=CARTESIAN_POINT('',(0.,45.,8.));
#772=CARTESIAN_POINT('',(0.,45.,8.));
#773=CARTESIAN_POINT('',(0.,45.,0.));
#774=CARTESIAN_POINT('Origin',(0.2,45.,0.));
#775=CARTESIAN_POINT('',(0.2,45.,0.));
#776=CARTESIAN_POINT('',(0.,45.,0.));
#777=CARTESIAN_POINT('',(0.199999999999996,45.,8.));
#778=CARTESIAN_POINT('',(0.,45.,8.));
#779=CARTESIAN_POINT('',(0.2,45.,0.));
#780=CARTESIAN_POINT('Origin',(0.2,105.5,0.));
#781=CARTESIAN_POINT('',(0.2,105.5,0.));
#782=CARTESIAN_POINT('',(0.2,105.5,0.));
#783=CARTESIAN_POINT('',(0.199999999999996,105.5,8.));
#784=CARTESIAN_POINT('',(0.2,105.5,8.));
#785=CARTESIAN_POINT('',(0.2,105.5,0.));
#786=CARTESIAN_POINT('Origin',(0.,105.5,0.));
#787=CARTESIAN_POINT('',(0.,105.5,0.));
#788=CARTESIAN_POINT('',(0.,105.5,0.));
#789=CARTESIAN_POINT('',(0.,105.5,8.));
#790=CARTESIAN_POINT('',(0.,105.5,8.));
#791=CARTESIAN_POINT('',(0.,105.5,0.));
#792=CARTESIAN_POINT('Origin',(0.,140.5,0.));
#793=CARTESIAN_POINT('',(0.,140.5,0.));
#794=CARTESIAN_POINT('',(0.,140.5,0.));
#795=CARTESIAN_POINT('',(0.,140.5,8.));
#796=CARTESIAN_POINT('',(0.,140.5,8.));
#797=CARTESIAN_POINT('',(0.,140.5,0.));
#798=CARTESIAN_POINT('Origin',(10.,150.5,0.));
#799=CARTESIAN_POINT('',(10.,150.5,0.));
#800=CARTESIAN_POINT('',(10.,150.5,0.));
#801=CARTESIAN_POINT('',(10.,150.5,8.));
#802=CARTESIAN_POINT('',(10.,150.5,8.));
#803=CARTESIAN_POINT('',(10.,150.5,0.));
#804=CARTESIAN_POINT('Origin',(73.9,150.5,0.));
#805=CARTESIAN_POINT('',(73.9,150.5,0.));
#806=CARTESIAN_POINT('',(73.9,150.5,0.));
#807=CARTESIAN_POINT('',(73.9,150.5,8.));
#808=CARTESIAN_POINT('',(73.9,150.5,8.));
#809=CARTESIAN_POINT('',(73.9,150.5,0.));
#810=CARTESIAN_POINT('Origin',(73.9,149.5,0.));
#811=CARTESIAN_POINT('',(74.5,150.3,0.));
#812=CARTESIAN_POINT('Origin',(73.9,149.5,0.));
#813=CARTESIAN_POINT('',(74.5,150.3,8.));
#814=CARTESIAN_POINT('Origin',(73.9,149.5,8.));
#815=CARTESIAN_POINT('',(74.5,150.3,0.));
#816=CARTESIAN_POINT('Origin',(74.8,150.7,0.));
#817=CARTESIAN_POINT('',(75.2,151.,0.));
#818=CARTESIAN_POINT('Origin',(74.8,150.7,0.));
#819=CARTESIAN_POINT('',(75.2,151.,8.));
#820=CARTESIAN_POINT('Origin',(74.8,150.7,8.));
#821=CARTESIAN_POINT('',(75.2,151.,0.));
#822=CARTESIAN_POINT('Origin',(76.,151.6,0.));
#823=CARTESIAN_POINT('Origin',(76.,151.6,0.));
#824=CARTESIAN_POINT('Origin',(76.,151.6,8.));
#825=CARTESIAN_POINT('Origin',(45.,79.25,8.));
#826=CARTESIAN_POINT('Origin',(45.,79.25,0.));
#827=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#831,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#828=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#831,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#829=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#827))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#831,#833,#834))
REPRESENTATION_CONTEXT('','3D')
);
#830=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#828))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#831,#833,#834))
REPRESENTATION_CONTEXT('','3D')
);
#831=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#832=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT($,.METRE.)
);
#833=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#834=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#835=SHAPE_DEFINITION_REPRESENTATION(#836,#837);
#836=PRODUCT_DEFINITION_SHAPE('',$,#839);
#837=SHAPE_REPRESENTATION('',(#499),#829);
#838=PRODUCT_DEFINITION_CONTEXT('part definition',#843,'design');
#839=PRODUCT_DEFINITION('S_1100','S_1100 v0',#840,#838);
#840=PRODUCT_DEFINITION_FORMATION('',$,#845);
#841=PRODUCT_RELATED_PRODUCT_CATEGORY('S_1100 v0','S_1100 v0',(#845));
#842=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2009,#843);
#843=APPLICATION_CONTEXT(
'Core Data for Automotive Mechanical Design Process');
#844=PRODUCT_CONTEXT('part definition',#843,'mechanical');
#845=PRODUCT('S_1100','S_1100 v0',$,(#844));
#846=PRESENTATION_STYLE_ASSIGNMENT((#847));
#847=SURFACE_STYLE_USAGE(.BOTH.,#848);
#848=SURFACE_SIDE_STYLE('',(#849));
#849=SURFACE_STYLE_FILL_AREA(#850);
#850=FILL_AREA_STYLE('Steel - Satin',(#851));
#851=FILL_AREA_STYLE_COLOUR('Steel - Satin',#852);
#852=COLOUR_RGB('Steel - Satin',0.627450980392157,0.627450980392157,0.627450980392157);
ENDSEC;
END-ISO-10303-21;
