ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ (''),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 
'G:/Shared drives/TIP/Validation/SMF KION/Tset SMF Shapes/S_1140.step',

/* time_stamp */ '2021-07-12T09:57:42+02:00',
/* author */ (''),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v18.1',
/* originating_system */ 'Autodesk Translation Framework v10.9.0.1377',

/* authorisation */ '');

FILE_SCHEMA (('AUTOMOTIVE_DESIGN { 1 0 10303 214 3 1 1 }'));
ENDSEC;

DATA;
#10=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#13),#1007);
#11=SHAPE_REPRESENTATION_RELATIONSHIP('SRR','None',#1014,#12);
#12=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#14),#1006);
#13=STYLED_ITEM('',(#1023),#14);
#14=MANIFOLD_SOLID_BREP('Body1',#587);
#15=FACE_BOUND('',#118,.T.);
#16=FACE_BOUND('',#120,.T.);
#17=CIRCLE('',#603,0.999999999999999);
#18=CIRCLE('',#604,0.999999999999999);
#19=CIRCLE('',#607,1.);
#20=CIRCLE('',#608,1.);
#21=CIRCLE('',#611,1.);
#22=CIRCLE('',#612,1.);
#23=CIRCLE('',#615,1.);
#24=CIRCLE('',#616,1.);
#25=CIRCLE('',#619,1.);
#26=CIRCLE('',#620,1.);
#27=CIRCLE('',#623,1.);
#28=CIRCLE('',#624,1.);
#29=CIRCLE('',#627,1.);
#30=CIRCLE('',#628,1.);
#31=CIRCLE('',#631,1.);
#32=CIRCLE('',#632,1.);
#33=CIRCLE('',#635,0.500000000000001);
#34=CIRCLE('',#636,0.500000000000001);
#35=CIRCLE('',#639,0.500000000000002);
#36=CIRCLE('',#640,0.500000000000002);
#37=CIRCLE('',#643,0.500000000000007);
#38=CIRCLE('',#644,0.500000000000007);
#39=CIRCLE('',#647,3.00000000000001);
#40=CIRCLE('',#648,3.00000000000001);
#41=CIRCLE('',#651,3.);
#42=CIRCLE('',#652,3.);
#43=CIRCLE('',#655,0.500000000000002);
#44=CIRCLE('',#656,0.500000000000002);
#45=CYLINDRICAL_SURFACE('',#602,0.999999999999999);
#46=CYLINDRICAL_SURFACE('',#606,1.);
#47=CYLINDRICAL_SURFACE('',#610,1.);
#48=CYLINDRICAL_SURFACE('',#614,1.);
#49=CYLINDRICAL_SURFACE('',#618,1.);
#50=CYLINDRICAL_SURFACE('',#622,1.);
#51=CYLINDRICAL_SURFACE('',#626,1.);
#52=CYLINDRICAL_SURFACE('',#630,1.);
#53=CYLINDRICAL_SURFACE('',#634,0.500000000000001);
#54=CYLINDRICAL_SURFACE('',#638,0.500000000000002);
#55=CYLINDRICAL_SURFACE('',#642,0.500000000000007);
#56=CYLINDRICAL_SURFACE('',#646,3.00000000000001);
#57=CYLINDRICAL_SURFACE('',#650,3.);
#58=CYLINDRICAL_SURFACE('',#654,0.500000000000002);
#59=FACE_OUTER_BOUND('',#89,.T.);
#60=FACE_OUTER_BOUND('',#90,.T.);
#61=FACE_OUTER_BOUND('',#91,.T.);
#62=FACE_OUTER_BOUND('',#92,.T.);
#63=FACE_OUTER_BOUND('',#93,.T.);
#64=FACE_OUTER_BOUND('',#94,.T.);
#65=FACE_OUTER_BOUND('',#95,.T.);
#66=FACE_OUTER_BOUND('',#96,.T.);
#67=FACE_OUTER_BOUND('',#97,.T.);
#68=FACE_OUTER_BOUND('',#98,.T.);
#69=FACE_OUTER_BOUND('',#99,.T.);
#70=FACE_OUTER_BOUND('',#100,.T.);
#71=FACE_OUTER_BOUND('',#101,.T.);
#72=FACE_OUTER_BOUND('',#102,.T.);
#73=FACE_OUTER_BOUND('',#103,.T.);
#74=FACE_OUTER_BOUND('',#104,.T.);
#75=FACE_OUTER_BOUND('',#105,.T.);
#76=FACE_OUTER_BOUND('',#106,.T.);
#77=FACE_OUTER_BOUND('',#107,.T.);
#78=FACE_OUTER_BOUND('',#108,.T.);
#79=FACE_OUTER_BOUND('',#109,.T.);
#80=FACE_OUTER_BOUND('',#110,.T.);
#81=FACE_OUTER_BOUND('',#111,.T.);
#82=FACE_OUTER_BOUND('',#112,.T.);
#83=FACE_OUTER_BOUND('',#113,.T.);
#84=FACE_OUTER_BOUND('',#114,.T.);
#85=FACE_OUTER_BOUND('',#115,.T.);
#86=FACE_OUTER_BOUND('',#116,.T.);
#87=FACE_OUTER_BOUND('',#117,.T.);
#88=FACE_OUTER_BOUND('',#119,.T.);
#89=EDGE_LOOP('',(#373,#374,#375,#376));
#90=EDGE_LOOP('',(#377,#378,#379,#380));
#91=EDGE_LOOP('',(#381,#382,#383,#384));
#92=EDGE_LOOP('',(#385,#386,#387,#388));
#93=EDGE_LOOP('',(#389,#390,#391,#392));
#94=EDGE_LOOP('',(#393,#394,#395,#396));
#95=EDGE_LOOP('',(#397,#398,#399,#400));
#96=EDGE_LOOP('',(#401,#402,#403,#404));
#97=EDGE_LOOP('',(#405,#406,#407,#408));
#98=EDGE_LOOP('',(#409,#410,#411,#412));
#99=EDGE_LOOP('',(#413,#414,#415,#416));
#100=EDGE_LOOP('',(#417,#418,#419,#420));
#101=EDGE_LOOP('',(#421,#422,#423,#424));
#102=EDGE_LOOP('',(#425,#426,#427,#428));
#103=EDGE_LOOP('',(#429,#430,#431,#432));
#104=EDGE_LOOP('',(#433,#434,#435,#436));
#105=EDGE_LOOP('',(#437,#438,#439,#440));
#106=EDGE_LOOP('',(#441,#442,#443,#444));
#107=EDGE_LOOP('',(#445,#446,#447,#448));
#108=EDGE_LOOP('',(#449,#450,#451,#452));
#109=EDGE_LOOP('',(#453,#454,#455,#456));
#110=EDGE_LOOP('',(#457,#458,#459,#460));
#111=EDGE_LOOP('',(#461,#462,#463,#464));
#112=EDGE_LOOP('',(#465,#466,#467,#468));
#113=EDGE_LOOP('',(#469,#470,#471,#472));
#114=EDGE_LOOP('',(#473,#474,#475,#476));
#115=EDGE_LOOP('',(#477,#478,#479,#480));
#116=EDGE_LOOP('',(#481,#482,#483,#484));
#117=EDGE_LOOP('',(#485,#486,#487,#488,#489,#490,#491,#492,#493,#494,#495,
#496));
#118=EDGE_LOOP('',(#497,#498,#499,#500,#501,#502,#503,#504,#505,#506,#507,
#508,#509,#510,#511,#512));
#119=EDGE_LOOP('',(#513,#514,#515,#516,#517,#518,#519,#520,#521,#522,#523,
#524));
#120=EDGE_LOOP('',(#525,#526,#527,#528,#529,#530,#531,#532,#533,#534,#535,
#536,#537,#538,#539,#540));
#121=LINE('',#837,#177);
#122=LINE('',#839,#178);
#123=LINE('',#841,#179);
#124=LINE('',#842,#180);
#125=LINE('',#848,#181);
#126=LINE('',#851,#182);
#127=LINE('',#853,#183);
#128=LINE('',#854,#184);
#129=LINE('',#860,#185);
#130=LINE('',#863,#186);
#131=LINE('',#865,#187);
#132=LINE('',#866,#188);
#133=LINE('',#872,#189);
#134=LINE('',#875,#190);
#135=LINE('',#877,#191);
#136=LINE('',#878,#192);
#137=LINE('',#884,#193);
#138=LINE('',#887,#194);
#139=LINE('',#889,#195);
#140=LINE('',#890,#196);
#141=LINE('',#896,#197);
#142=LINE('',#899,#198);
#143=LINE('',#901,#199);
#144=LINE('',#902,#200);
#145=LINE('',#908,#201);
#146=LINE('',#911,#202);
#147=LINE('',#913,#203);
#148=LINE('',#914,#204);
#149=LINE('',#920,#205);
#150=LINE('',#923,#206);
#151=LINE('',#925,#207);
#152=LINE('',#926,#208);
#153=LINE('',#933,#209);
#154=LINE('',#935,#210);
#155=LINE('',#937,#211);
#156=LINE('',#938,#212);
#157=LINE('',#944,#213);
#158=LINE('',#947,#214);
#159=LINE('',#949,#215);
#160=LINE('',#950,#216);
#161=LINE('',#956,#217);
#162=LINE('',#959,#218);
#163=LINE('',#961,#219);
#164=LINE('',#962,#220);
#165=LINE('',#968,#221);
#166=LINE('',#971,#222);
#167=LINE('',#973,#223);
#168=LINE('',#974,#224);
#169=LINE('',#980,#225);
#170=LINE('',#983,#226);
#171=LINE('',#985,#227);
#172=LINE('',#986,#228);
#173=LINE('',#992,#229);
#174=LINE('',#995,#230);
#175=LINE('',#997,#231);
#176=LINE('',#998,#232);
#177=VECTOR('',#663,10.);
#178=VECTOR('',#664,10.);
#179=VECTOR('',#665,10.);
#180=VECTOR('',#666,10.);
#181=VECTOR('',#673,10.);
#182=VECTOR('',#676,10.);
#183=VECTOR('',#677,10.);
#184=VECTOR('',#678,10.);
#185=VECTOR('',#685,10.);
#186=VECTOR('',#688,10.);
#187=VECTOR('',#689,10.);
#188=VECTOR('',#690,10.);
#189=VECTOR('',#697,10.);
#190=VECTOR('',#700,10.);
#191=VECTOR('',#701,10.);
#192=VECTOR('',#702,10.);
#193=VECTOR('',#709,10.);
#194=VECTOR('',#712,10.);
#195=VECTOR('',#713,10.);
#196=VECTOR('',#714,10.);
#197=VECTOR('',#721,10.);
#198=VECTOR('',#724,10.);
#199=VECTOR('',#725,10.);
#200=VECTOR('',#726,10.);
#201=VECTOR('',#733,10.);
#202=VECTOR('',#736,10.);
#203=VECTOR('',#737,10.);
#204=VECTOR('',#738,10.);
#205=VECTOR('',#745,10.);
#206=VECTOR('',#748,10.);
#207=VECTOR('',#749,10.);
#208=VECTOR('',#750,10.);
#209=VECTOR('',#759,10.);
#210=VECTOR('',#760,10.);
#211=VECTOR('',#761,10.);
#212=VECTOR('',#762,10.);
#213=VECTOR('',#769,10.);
#214=VECTOR('',#772,10.);
#215=VECTOR('',#773,10.);
#216=VECTOR('',#774,10.);
#217=VECTOR('',#781,10.);
#218=VECTOR('',#784,10.);
#219=VECTOR('',#785,10.);
#220=VECTOR('',#786,10.);
#221=VECTOR('',#793,10.);
#222=VECTOR('',#796,10.);
#223=VECTOR('',#797,10.);
#224=VECTOR('',#798,10.);
#225=VECTOR('',#805,10.);
#226=VECTOR('',#808,10.);
#227=VECTOR('',#809,10.);
#228=VECTOR('',#810,10.);
#229=VECTOR('',#817,10.);
#230=VECTOR('',#820,10.);
#231=VECTOR('',#821,10.);
#232=VECTOR('',#822,10.);
#233=VERTEX_POINT('',#835);
#234=VERTEX_POINT('',#836);
#235=VERTEX_POINT('',#838);
#236=VERTEX_POINT('',#840);
#237=VERTEX_POINT('',#844);
#238=VERTEX_POINT('',#846);
#239=VERTEX_POINT('',#850);
#240=VERTEX_POINT('',#852);
#241=VERTEX_POINT('',#856);
#242=VERTEX_POINT('',#858);
#243=VERTEX_POINT('',#862);
#244=VERTEX_POINT('',#864);
#245=VERTEX_POINT('',#868);
#246=VERTEX_POINT('',#870);
#247=VERTEX_POINT('',#874);
#248=VERTEX_POINT('',#876);
#249=VERTEX_POINT('',#880);
#250=VERTEX_POINT('',#882);
#251=VERTEX_POINT('',#886);
#252=VERTEX_POINT('',#888);
#253=VERTEX_POINT('',#892);
#254=VERTEX_POINT('',#894);
#255=VERTEX_POINT('',#898);
#256=VERTEX_POINT('',#900);
#257=VERTEX_POINT('',#904);
#258=VERTEX_POINT('',#906);
#259=VERTEX_POINT('',#910);
#260=VERTEX_POINT('',#912);
#261=VERTEX_POINT('',#916);
#262=VERTEX_POINT('',#918);
#263=VERTEX_POINT('',#922);
#264=VERTEX_POINT('',#924);
#265=VERTEX_POINT('',#931);
#266=VERTEX_POINT('',#932);
#267=VERTEX_POINT('',#934);
#268=VERTEX_POINT('',#936);
#269=VERTEX_POINT('',#940);
#270=VERTEX_POINT('',#942);
#271=VERTEX_POINT('',#946);
#272=VERTEX_POINT('',#948);
#273=VERTEX_POINT('',#952);
#274=VERTEX_POINT('',#954);
#275=VERTEX_POINT('',#958);
#276=VERTEX_POINT('',#960);
#277=VERTEX_POINT('',#964);
#278=VERTEX_POINT('',#966);
#279=VERTEX_POINT('',#970);
#280=VERTEX_POINT('',#972);
#281=VERTEX_POINT('',#976);
#282=VERTEX_POINT('',#978);
#283=VERTEX_POINT('',#982);
#284=VERTEX_POINT('',#984);
#285=VERTEX_POINT('',#988);
#286=VERTEX_POINT('',#990);
#287=VERTEX_POINT('',#994);
#288=VERTEX_POINT('',#996);
#289=EDGE_CURVE('',#233,#234,#121,.T.);
#290=EDGE_CURVE('',#234,#235,#122,.T.);
#291=EDGE_CURVE('',#236,#235,#123,.T.);
#292=EDGE_CURVE('',#233,#236,#124,.T.);
#293=EDGE_CURVE('',#237,#233,#17,.T.);
#294=EDGE_CURVE('',#238,#236,#18,.T.);
#295=EDGE_CURVE('',#237,#238,#125,.T.);
#296=EDGE_CURVE('',#239,#237,#126,.T.);
#297=EDGE_CURVE('',#240,#238,#127,.T.);
#298=EDGE_CURVE('',#239,#240,#128,.T.);
#299=EDGE_CURVE('',#241,#239,#19,.T.);
#300=EDGE_CURVE('',#242,#240,#20,.T.);
#301=EDGE_CURVE('',#241,#242,#129,.T.);
#302=EDGE_CURVE('',#243,#241,#130,.T.);
#303=EDGE_CURVE('',#244,#242,#131,.T.);
#304=EDGE_CURVE('',#243,#244,#132,.T.);
#305=EDGE_CURVE('',#245,#243,#21,.T.);
#306=EDGE_CURVE('',#246,#244,#22,.T.);
#307=EDGE_CURVE('',#245,#246,#133,.T.);
#308=EDGE_CURVE('',#247,#245,#134,.T.);
#309=EDGE_CURVE('',#248,#246,#135,.T.);
#310=EDGE_CURVE('',#247,#248,#136,.T.);
#311=EDGE_CURVE('',#249,#247,#23,.T.);
#312=EDGE_CURVE('',#250,#248,#24,.T.);
#313=EDGE_CURVE('',#249,#250,#137,.T.);
#314=EDGE_CURVE('',#251,#249,#138,.T.);
#315=EDGE_CURVE('',#252,#250,#139,.T.);
#316=EDGE_CURVE('',#251,#252,#140,.T.);
#317=EDGE_CURVE('',#253,#251,#25,.T.);
#318=EDGE_CURVE('',#254,#252,#26,.T.);
#319=EDGE_CURVE('',#253,#254,#141,.T.);
#320=EDGE_CURVE('',#255,#253,#142,.T.);
#321=EDGE_CURVE('',#256,#254,#143,.T.);
#322=EDGE_CURVE('',#255,#256,#144,.T.);
#323=EDGE_CURVE('',#257,#255,#27,.T.);
#324=EDGE_CURVE('',#258,#256,#28,.T.);
#325=EDGE_CURVE('',#257,#258,#145,.T.);
#326=EDGE_CURVE('',#259,#257,#146,.T.);
#327=EDGE_CURVE('',#260,#258,#147,.T.);
#328=EDGE_CURVE('',#259,#260,#148,.T.);
#329=EDGE_CURVE('',#261,#259,#29,.T.);
#330=EDGE_CURVE('',#262,#260,#30,.T.);
#331=EDGE_CURVE('',#261,#262,#149,.T.);
#332=EDGE_CURVE('',#263,#261,#150,.T.);
#333=EDGE_CURVE('',#264,#262,#151,.T.);
#334=EDGE_CURVE('',#263,#264,#152,.T.);
#335=EDGE_CURVE('',#234,#263,#31,.T.);
#336=EDGE_CURVE('',#235,#264,#32,.T.);
#337=EDGE_CURVE('',#265,#266,#153,.T.);
#338=EDGE_CURVE('',#265,#267,#154,.T.);
#339=EDGE_CURVE('',#268,#267,#155,.T.);
#340=EDGE_CURVE('',#266,#268,#156,.T.);
#341=EDGE_CURVE('',#269,#266,#33,.T.);
#342=EDGE_CURVE('',#270,#268,#34,.T.);
#343=EDGE_CURVE('',#269,#270,#157,.T.);
#344=EDGE_CURVE('',#271,#269,#158,.T.);
#345=EDGE_CURVE('',#272,#270,#159,.T.);
#346=EDGE_CURVE('',#271,#272,#160,.T.);
#347=EDGE_CURVE('',#273,#271,#35,.T.);
#348=EDGE_CURVE('',#274,#272,#36,.T.);
#349=EDGE_CURVE('',#273,#274,#161,.T.);
#350=EDGE_CURVE('',#273,#275,#162,.T.);
#351=EDGE_CURVE('',#276,#274,#163,.T.);
#352=EDGE_CURVE('',#275,#276,#164,.T.);
#353=EDGE_CURVE('',#277,#275,#37,.T.);
#354=EDGE_CURVE('',#278,#276,#38,.T.);
#355=EDGE_CURVE('',#277,#278,#165,.T.);
#356=EDGE_CURVE('',#279,#277,#166,.T.);
#357=EDGE_CURVE('',#280,#278,#167,.T.);
#358=EDGE_CURVE('',#279,#280,#168,.T.);
#359=EDGE_CURVE('',#281,#279,#39,.T.);
#360=EDGE_CURVE('',#282,#280,#40,.T.);
#361=EDGE_CURVE('',#281,#282,#169,.T.);
#362=EDGE_CURVE('',#283,#281,#170,.T.);
#363=EDGE_CURVE('',#284,#282,#171,.T.);
#364=EDGE_CURVE('',#283,#284,#172,.T.);
#365=EDGE_CURVE('',#285,#283,#41,.T.);
#366=EDGE_CURVE('',#286,#284,#42,.T.);
#367=EDGE_CURVE('',#285,#286,#173,.T.);
#368=EDGE_CURVE('',#287,#285,#174,.T.);
#369=EDGE_CURVE('',#288,#286,#175,.T.);
#370=EDGE_CURVE('',#287,#288,#176,.T.);
#371=EDGE_CURVE('',#265,#287,#43,.T.);
#372=EDGE_CURVE('',#267,#288,#44,.T.);
#373=ORIENTED_EDGE('',*,*,#289,.T.);
#374=ORIENTED_EDGE('',*,*,#290,.T.);
#375=ORIENTED_EDGE('',*,*,#291,.F.);
#376=ORIENTED_EDGE('',*,*,#292,.F.);
#377=ORIENTED_EDGE('',*,*,#293,.T.);
#378=ORIENTED_EDGE('',*,*,#292,.T.);
#379=ORIENTED_EDGE('',*,*,#294,.F.);
#380=ORIENTED_EDGE('',*,*,#295,.F.);
#381=ORIENTED_EDGE('',*,*,#296,.T.);
#382=ORIENTED_EDGE('',*,*,#295,.T.);
#383=ORIENTED_EDGE('',*,*,#297,.F.);
#384=ORIENTED_EDGE('',*,*,#298,.F.);
#385=ORIENTED_EDGE('',*,*,#299,.T.);
#386=ORIENTED_EDGE('',*,*,#298,.T.);
#387=ORIENTED_EDGE('',*,*,#300,.F.);
#388=ORIENTED_EDGE('',*,*,#301,.F.);
#389=ORIENTED_EDGE('',*,*,#302,.T.);
#390=ORIENTED_EDGE('',*,*,#301,.T.);
#391=ORIENTED_EDGE('',*,*,#303,.F.);
#392=ORIENTED_EDGE('',*,*,#304,.F.);
#393=ORIENTED_EDGE('',*,*,#305,.T.);
#394=ORIENTED_EDGE('',*,*,#304,.T.);
#395=ORIENTED_EDGE('',*,*,#306,.F.);
#396=ORIENTED_EDGE('',*,*,#307,.F.);
#397=ORIENTED_EDGE('',*,*,#308,.T.);
#398=ORIENTED_EDGE('',*,*,#307,.T.);
#399=ORIENTED_EDGE('',*,*,#309,.F.);
#400=ORIENTED_EDGE('',*,*,#310,.F.);
#401=ORIENTED_EDGE('',*,*,#311,.T.);
#402=ORIENTED_EDGE('',*,*,#310,.T.);
#403=ORIENTED_EDGE('',*,*,#312,.F.);
#404=ORIENTED_EDGE('',*,*,#313,.F.);
#405=ORIENTED_EDGE('',*,*,#314,.T.);
#406=ORIENTED_EDGE('',*,*,#313,.T.);
#407=ORIENTED_EDGE('',*,*,#315,.F.);
#408=ORIENTED_EDGE('',*,*,#316,.F.);
#409=ORIENTED_EDGE('',*,*,#317,.T.);
#410=ORIENTED_EDGE('',*,*,#316,.T.);
#411=ORIENTED_EDGE('',*,*,#318,.F.);
#412=ORIENTED_EDGE('',*,*,#319,.F.);
#413=ORIENTED_EDGE('',*,*,#320,.T.);
#414=ORIENTED_EDGE('',*,*,#319,.T.);
#415=ORIENTED_EDGE('',*,*,#321,.F.);
#416=ORIENTED_EDGE('',*,*,#322,.F.);
#417=ORIENTED_EDGE('',*,*,#323,.T.);
#418=ORIENTED_EDGE('',*,*,#322,.T.);
#419=ORIENTED_EDGE('',*,*,#324,.F.);
#420=ORIENTED_EDGE('',*,*,#325,.F.);
#421=ORIENTED_EDGE('',*,*,#326,.T.);
#422=ORIENTED_EDGE('',*,*,#325,.T.);
#423=ORIENTED_EDGE('',*,*,#327,.F.);
#424=ORIENTED_EDGE('',*,*,#328,.F.);
#425=ORIENTED_EDGE('',*,*,#329,.T.);
#426=ORIENTED_EDGE('',*,*,#328,.T.);
#427=ORIENTED_EDGE('',*,*,#330,.F.);
#428=ORIENTED_EDGE('',*,*,#331,.F.);
#429=ORIENTED_EDGE('',*,*,#332,.T.);
#430=ORIENTED_EDGE('',*,*,#331,.T.);
#431=ORIENTED_EDGE('',*,*,#333,.F.);
#432=ORIENTED_EDGE('',*,*,#334,.F.);
#433=ORIENTED_EDGE('',*,*,#335,.T.);
#434=ORIENTED_EDGE('',*,*,#334,.T.);
#435=ORIENTED_EDGE('',*,*,#336,.F.);
#436=ORIENTED_EDGE('',*,*,#290,.F.);
#437=ORIENTED_EDGE('',*,*,#337,.F.);
#438=ORIENTED_EDGE('',*,*,#338,.T.);
#439=ORIENTED_EDGE('',*,*,#339,.F.);
#440=ORIENTED_EDGE('',*,*,#340,.F.);
#441=ORIENTED_EDGE('',*,*,#341,.T.);
#442=ORIENTED_EDGE('',*,*,#340,.T.);
#443=ORIENTED_EDGE('',*,*,#342,.F.);
#444=ORIENTED_EDGE('',*,*,#343,.F.);
#445=ORIENTED_EDGE('',*,*,#344,.T.);
#446=ORIENTED_EDGE('',*,*,#343,.T.);
#447=ORIENTED_EDGE('',*,*,#345,.F.);
#448=ORIENTED_EDGE('',*,*,#346,.F.);
#449=ORIENTED_EDGE('',*,*,#347,.T.);
#450=ORIENTED_EDGE('',*,*,#346,.T.);
#451=ORIENTED_EDGE('',*,*,#348,.F.);
#452=ORIENTED_EDGE('',*,*,#349,.F.);
#453=ORIENTED_EDGE('',*,*,#350,.F.);
#454=ORIENTED_EDGE('',*,*,#349,.T.);
#455=ORIENTED_EDGE('',*,*,#351,.F.);
#456=ORIENTED_EDGE('',*,*,#352,.F.);
#457=ORIENTED_EDGE('',*,*,#353,.T.);
#458=ORIENTED_EDGE('',*,*,#352,.T.);
#459=ORIENTED_EDGE('',*,*,#354,.F.);
#460=ORIENTED_EDGE('',*,*,#355,.F.);
#461=ORIENTED_EDGE('',*,*,#356,.T.);
#462=ORIENTED_EDGE('',*,*,#355,.T.);
#463=ORIENTED_EDGE('',*,*,#357,.F.);
#464=ORIENTED_EDGE('',*,*,#358,.F.);
#465=ORIENTED_EDGE('',*,*,#359,.T.);
#466=ORIENTED_EDGE('',*,*,#358,.T.);
#467=ORIENTED_EDGE('',*,*,#360,.F.);
#468=ORIENTED_EDGE('',*,*,#361,.F.);
#469=ORIENTED_EDGE('',*,*,#362,.T.);
#470=ORIENTED_EDGE('',*,*,#361,.T.);
#471=ORIENTED_EDGE('',*,*,#363,.F.);
#472=ORIENTED_EDGE('',*,*,#364,.F.);
#473=ORIENTED_EDGE('',*,*,#365,.T.);
#474=ORIENTED_EDGE('',*,*,#364,.T.);
#475=ORIENTED_EDGE('',*,*,#366,.F.);
#476=ORIENTED_EDGE('',*,*,#367,.F.);
#477=ORIENTED_EDGE('',*,*,#368,.T.);
#478=ORIENTED_EDGE('',*,*,#367,.T.);
#479=ORIENTED_EDGE('',*,*,#369,.F.);
#480=ORIENTED_EDGE('',*,*,#370,.F.);
#481=ORIENTED_EDGE('',*,*,#371,.T.);
#482=ORIENTED_EDGE('',*,*,#370,.T.);
#483=ORIENTED_EDGE('',*,*,#372,.F.);
#484=ORIENTED_EDGE('',*,*,#338,.F.);
#485=ORIENTED_EDGE('',*,*,#372,.T.);
#486=ORIENTED_EDGE('',*,*,#369,.T.);
#487=ORIENTED_EDGE('',*,*,#366,.T.);
#488=ORIENTED_EDGE('',*,*,#363,.T.);
#489=ORIENTED_EDGE('',*,*,#360,.T.);
#490=ORIENTED_EDGE('',*,*,#357,.T.);
#491=ORIENTED_EDGE('',*,*,#354,.T.);
#492=ORIENTED_EDGE('',*,*,#351,.T.);
#493=ORIENTED_EDGE('',*,*,#348,.T.);
#494=ORIENTED_EDGE('',*,*,#345,.T.);
#495=ORIENTED_EDGE('',*,*,#342,.T.);
#496=ORIENTED_EDGE('',*,*,#339,.T.);
#497=ORIENTED_EDGE('',*,*,#336,.T.);
#498=ORIENTED_EDGE('',*,*,#333,.T.);
#499=ORIENTED_EDGE('',*,*,#330,.T.);
#500=ORIENTED_EDGE('',*,*,#327,.T.);
#501=ORIENTED_EDGE('',*,*,#324,.T.);
#502=ORIENTED_EDGE('',*,*,#321,.T.);
#503=ORIENTED_EDGE('',*,*,#318,.T.);
#504=ORIENTED_EDGE('',*,*,#315,.T.);
#505=ORIENTED_EDGE('',*,*,#312,.T.);
#506=ORIENTED_EDGE('',*,*,#309,.T.);
#507=ORIENTED_EDGE('',*,*,#306,.T.);
#508=ORIENTED_EDGE('',*,*,#303,.T.);
#509=ORIENTED_EDGE('',*,*,#300,.T.);
#510=ORIENTED_EDGE('',*,*,#297,.T.);
#511=ORIENTED_EDGE('',*,*,#294,.T.);
#512=ORIENTED_EDGE('',*,*,#291,.T.);
#513=ORIENTED_EDGE('',*,*,#371,.F.);
#514=ORIENTED_EDGE('',*,*,#337,.T.);
#515=ORIENTED_EDGE('',*,*,#341,.F.);
#516=ORIENTED_EDGE('',*,*,#344,.F.);
#517=ORIENTED_EDGE('',*,*,#347,.F.);
#518=ORIENTED_EDGE('',*,*,#350,.T.);
#519=ORIENTED_EDGE('',*,*,#353,.F.);
#520=ORIENTED_EDGE('',*,*,#356,.F.);
#521=ORIENTED_EDGE('',*,*,#359,.F.);
#522=ORIENTED_EDGE('',*,*,#362,.F.);
#523=ORIENTED_EDGE('',*,*,#365,.F.);
#524=ORIENTED_EDGE('',*,*,#368,.F.);
#525=ORIENTED_EDGE('',*,*,#335,.F.);
#526=ORIENTED_EDGE('',*,*,#289,.F.);
#527=ORIENTED_EDGE('',*,*,#293,.F.);
#528=ORIENTED_EDGE('',*,*,#296,.F.);
#529=ORIENTED_EDGE('',*,*,#299,.F.);
#530=ORIENTED_EDGE('',*,*,#302,.F.);
#531=ORIENTED_EDGE('',*,*,#305,.F.);
#532=ORIENTED_EDGE('',*,*,#308,.F.);
#533=ORIENTED_EDGE('',*,*,#311,.F.);
#534=ORIENTED_EDGE('',*,*,#314,.F.);
#535=ORIENTED_EDGE('',*,*,#317,.F.);
#536=ORIENTED_EDGE('',*,*,#320,.F.);
#537=ORIENTED_EDGE('',*,*,#323,.F.);
#538=ORIENTED_EDGE('',*,*,#326,.F.);
#539=ORIENTED_EDGE('',*,*,#329,.F.);
#540=ORIENTED_EDGE('',*,*,#332,.F.);
#541=PLANE('',#601);
#542=PLANE('',#605);
#543=PLANE('',#609);
#544=PLANE('',#613);
#545=PLANE('',#617);
#546=PLANE('',#621);
#547=PLANE('',#625);
#548=PLANE('',#629);
#549=PLANE('',#633);
#550=PLANE('',#637);
#551=PLANE('',#641);
#552=PLANE('',#645);
#553=PLANE('',#649);
#554=PLANE('',#653);
#555=PLANE('',#657);
#556=PLANE('',#658);
#557=ADVANCED_FACE('',(#59),#541,.T.);
#558=ADVANCED_FACE('',(#60),#45,.F.);
#559=ADVANCED_FACE('',(#61),#542,.T.);
#560=ADVANCED_FACE('',(#62),#46,.F.);
#561=ADVANCED_FACE('',(#63),#543,.T.);
#562=ADVANCED_FACE('',(#64),#47,.F.);
#563=ADVANCED_FACE('',(#65),#544,.T.);
#564=ADVANCED_FACE('',(#66),#48,.F.);
#565=ADVANCED_FACE('',(#67),#545,.T.);
#566=ADVANCED_FACE('',(#68),#49,.F.);
#567=ADVANCED_FACE('',(#69),#546,.T.);
#568=ADVANCED_FACE('',(#70),#50,.T.);
#569=ADVANCED_FACE('',(#71),#547,.T.);
#570=ADVANCED_FACE('',(#72),#51,.T.);
#571=ADVANCED_FACE('',(#73),#548,.T.);
#572=ADVANCED_FACE('',(#74),#52,.F.);
#573=ADVANCED_FACE('',(#75),#549,.T.);
#574=ADVANCED_FACE('',(#76),#53,.T.);
#575=ADVANCED_FACE('',(#77),#550,.T.);
#576=ADVANCED_FACE('',(#78),#54,.T.);
#577=ADVANCED_FACE('',(#79),#551,.T.);
#578=ADVANCED_FACE('',(#80),#55,.T.);
#579=ADVANCED_FACE('',(#81),#552,.T.);
#580=ADVANCED_FACE('',(#82),#56,.T.);
#581=ADVANCED_FACE('',(#83),#553,.T.);
#582=ADVANCED_FACE('',(#84),#57,.T.);
#583=ADVANCED_FACE('',(#85),#554,.T.);
#584=ADVANCED_FACE('',(#86),#58,.T.);
#585=ADVANCED_FACE('',(#87,#15),#555,.T.);
#586=ADVANCED_FACE('',(#88,#16),#556,.F.);
#587=CLOSED_SHELL('',(#557,#558,#559,#560,#561,#562,#563,#564,#565,#566,
#567,#568,#569,#570,#571,#572,#573,#574,#575,#576,#577,#578,#579,#580,#581,
#582,#583,#584,#585,#586));
#588=DERIVED_UNIT_ELEMENT(#590,1.);
#589=DERIVED_UNIT_ELEMENT(#1009,-3.);
#590=(
MASS_UNIT()
NAMED_UNIT(*)
SI_UNIT(.KILO.,.GRAM.)
);
#591=DERIVED_UNIT((#588,#589));
#592=MEASURE_REPRESENTATION_ITEM('density measure',
POSITIVE_RATIO_MEASURE(7850.),#591);
#593=PROPERTY_DEFINITION_REPRESENTATION(#598,#595);
#594=PROPERTY_DEFINITION_REPRESENTATION(#599,#596);
#595=REPRESENTATION('material name',(#597),#1006);
#596=REPRESENTATION('density',(#592),#1006);
#597=DESCRIPTIVE_REPRESENTATION_ITEM('Steel','Steel');
#598=PROPERTY_DEFINITION('material property','material name',#1016);
#599=PROPERTY_DEFINITION('material property','density of part',#1016);
#600=AXIS2_PLACEMENT_3D('placement',#833,#659,#660);
#601=AXIS2_PLACEMENT_3D('',#834,#661,#662);
#602=AXIS2_PLACEMENT_3D('',#843,#667,#668);
#603=AXIS2_PLACEMENT_3D('',#845,#669,#670);
#604=AXIS2_PLACEMENT_3D('',#847,#671,#672);
#605=AXIS2_PLACEMENT_3D('',#849,#674,#675);
#606=AXIS2_PLACEMENT_3D('',#855,#679,#680);
#607=AXIS2_PLACEMENT_3D('',#857,#681,#682);
#608=AXIS2_PLACEMENT_3D('',#859,#683,#684);
#609=AXIS2_PLACEMENT_3D('',#861,#686,#687);
#610=AXIS2_PLACEMENT_3D('',#867,#691,#692);
#611=AXIS2_PLACEMENT_3D('',#869,#693,#694);
#612=AXIS2_PLACEMENT_3D('',#871,#695,#696);
#613=AXIS2_PLACEMENT_3D('',#873,#698,#699);
#614=AXIS2_PLACEMENT_3D('',#879,#703,#704);
#615=AXIS2_PLACEMENT_3D('',#881,#705,#706);
#616=AXIS2_PLACEMENT_3D('',#883,#707,#708);
#617=AXIS2_PLACEMENT_3D('',#885,#710,#711);
#618=AXIS2_PLACEMENT_3D('',#891,#715,#716);
#619=AXIS2_PLACEMENT_3D('',#893,#717,#718);
#620=AXIS2_PLACEMENT_3D('',#895,#719,#720);
#621=AXIS2_PLACEMENT_3D('',#897,#722,#723);
#622=AXIS2_PLACEMENT_3D('',#903,#727,#728);
#623=AXIS2_PLACEMENT_3D('',#905,#729,#730);
#624=AXIS2_PLACEMENT_3D('',#907,#731,#732);
#625=AXIS2_PLACEMENT_3D('',#909,#734,#735);
#626=AXIS2_PLACEMENT_3D('',#915,#739,#740);
#627=AXIS2_PLACEMENT_3D('',#917,#741,#742);
#628=AXIS2_PLACEMENT_3D('',#919,#743,#744);
#629=AXIS2_PLACEMENT_3D('',#921,#746,#747);
#630=AXIS2_PLACEMENT_3D('',#927,#751,#752);
#631=AXIS2_PLACEMENT_3D('',#928,#753,#754);
#632=AXIS2_PLACEMENT_3D('',#929,#755,#756);
#633=AXIS2_PLACEMENT_3D('',#930,#757,#758);
#634=AXIS2_PLACEMENT_3D('',#939,#763,#764);
#635=AXIS2_PLACEMENT_3D('',#941,#765,#766);
#636=AXIS2_PLACEMENT_3D('',#943,#767,#768);
#637=AXIS2_PLACEMENT_3D('',#945,#770,#771);
#638=AXIS2_PLACEMENT_3D('',#951,#775,#776);
#639=AXIS2_PLACEMENT_3D('',#953,#777,#778);
#640=AXIS2_PLACEMENT_3D('',#955,#779,#780);
#641=AXIS2_PLACEMENT_3D('',#957,#782,#783);
#642=AXIS2_PLACEMENT_3D('',#963,#787,#788);
#643=AXIS2_PLACEMENT_3D('',#965,#789,#790);
#644=AXIS2_PLACEMENT_3D('',#967,#791,#792);
#645=AXIS2_PLACEMENT_3D('',#969,#794,#795);
#646=AXIS2_PLACEMENT_3D('',#975,#799,#800);
#647=AXIS2_PLACEMENT_3D('',#977,#801,#802);
#648=AXIS2_PLACEMENT_3D('',#979,#803,#804);
#649=AXIS2_PLACEMENT_3D('',#981,#806,#807);
#650=AXIS2_PLACEMENT_3D('',#987,#811,#812);
#651=AXIS2_PLACEMENT_3D('',#989,#813,#814);
#652=AXIS2_PLACEMENT_3D('',#991,#815,#816);
#653=AXIS2_PLACEMENT_3D('',#993,#818,#819);
#654=AXIS2_PLACEMENT_3D('',#999,#823,#824);
#655=AXIS2_PLACEMENT_3D('',#1000,#825,#826);
#656=AXIS2_PLACEMENT_3D('',#1001,#827,#828);
#657=AXIS2_PLACEMENT_3D('',#1002,#829,#830);
#658=AXIS2_PLACEMENT_3D('',#1003,#831,#832);
#659=DIRECTION('axis',(0.,0.,1.));
#660=DIRECTION('refdir',(1.,0.,0.));
#661=DIRECTION('center_axis',(0.,-1.,0.));
#662=DIRECTION('ref_axis',(1.,0.,0.));
#663=DIRECTION('',(1.,0.,0.));
#664=DIRECTION('',(0.,0.,1.));
#665=DIRECTION('',(1.,0.,0.));
#666=DIRECTION('',(0.,0.,1.));
#667=DIRECTION('center_axis',(0.,0.,1.));
#668=DIRECTION('ref_axis',(1.11022302462516E-15,1.,0.));
#669=DIRECTION('center_axis',(0.,0.,-1.));
#670=DIRECTION('ref_axis',(1.11022302462516E-15,1.,0.));
#671=DIRECTION('center_axis',(0.,0.,-1.));
#672=DIRECTION('ref_axis',(1.11022302462516E-15,1.,0.));
#673=DIRECTION('',(0.,0.,1.));
#674=DIRECTION('center_axis',(1.,1.42336285208353E-16,0.));
#675=DIRECTION('ref_axis',(-1.42336285208353E-16,1.,0.));
#676=DIRECTION('',(-1.42336285208353E-16,1.,0.));
#677=DIRECTION('',(-1.42336285208353E-16,1.,0.));
#678=DIRECTION('',(0.,0.,1.));
#679=DIRECTION('center_axis',(0.,0.,1.));
#680=DIRECTION('ref_axis',(-1.,0.,0.));
#681=DIRECTION('center_axis',(0.,0.,-1.));
#682=DIRECTION('ref_axis',(-1.,0.,0.));
#683=DIRECTION('center_axis',(0.,0.,-1.));
#684=DIRECTION('ref_axis',(-1.,0.,0.));
#685=DIRECTION('',(0.,0.,1.));
#686=DIRECTION('center_axis',(0.,1.,0.));
#687=DIRECTION('ref_axis',(-1.,0.,0.));
#688=DIRECTION('',(-1.,0.,0.));
#689=DIRECTION('',(-1.,0.,0.));
#690=DIRECTION('',(0.,0.,1.));
#691=DIRECTION('center_axis',(0.,0.,1.));
#692=DIRECTION('ref_axis',(0.,-1.,0.));
#693=DIRECTION('center_axis',(0.,0.,-1.));
#694=DIRECTION('ref_axis',(0.,-1.,0.));
#695=DIRECTION('center_axis',(0.,0.,-1.));
#696=DIRECTION('ref_axis',(0.,-1.,0.));
#697=DIRECTION('',(0.,0.,1.));
#698=DIRECTION('center_axis',(-1.,0.,0.));
#699=DIRECTION('ref_axis',(0.,-1.,0.));
#700=DIRECTION('',(0.,-1.,0.));
#701=DIRECTION('',(0.,-1.,0.));
#702=DIRECTION('',(0.,0.,1.));
#703=DIRECTION('center_axis',(0.,0.,1.));
#704=DIRECTION('ref_axis',(1.,0.,0.));
#705=DIRECTION('center_axis',(0.,0.,-1.));
#706=DIRECTION('ref_axis',(1.,0.,0.));
#707=DIRECTION('center_axis',(0.,0.,-1.));
#708=DIRECTION('ref_axis',(1.,0.,0.));
#709=DIRECTION('',(0.,0.,1.));
#710=DIRECTION('center_axis',(7.65671051465626E-16,-1.,0.));
#711=DIRECTION('ref_axis',(1.,7.65671051465626E-16,0.));
#712=DIRECTION('',(1.,7.65671051465626E-16,0.));
#713=DIRECTION('',(1.,7.65671051465626E-16,0.));
#714=DIRECTION('',(0.,0.,1.));
#715=DIRECTION('center_axis',(0.,0.,1.));
#716=DIRECTION('ref_axis',(4.44089209850062E-15,1.,0.));
#717=DIRECTION('center_axis',(0.,0.,-1.));
#718=DIRECTION('ref_axis',(4.44089209850062E-15,1.,0.));
#719=DIRECTION('center_axis',(0.,0.,-1.));
#720=DIRECTION('ref_axis',(4.44089209850062E-15,1.,0.));
#721=DIRECTION('',(0.,0.,1.));
#722=DIRECTION('center_axis',(1.,0.,0.));
#723=DIRECTION('ref_axis',(0.,1.,0.));
#724=DIRECTION('',(0.,1.,0.));
#725=DIRECTION('',(0.,1.,0.));
#726=DIRECTION('',(0.,0.,1.));
#727=DIRECTION('center_axis',(0.,0.,1.));
#728=DIRECTION('ref_axis',(0.,-1.,0.));
#729=DIRECTION('center_axis',(0.,0.,1.));
#730=DIRECTION('ref_axis',(0.,-1.,0.));
#731=DIRECTION('center_axis',(0.,0.,1.));
#732=DIRECTION('ref_axis',(0.,-1.,0.));
#733=DIRECTION('',(0.,0.,1.));
#734=DIRECTION('center_axis',(0.,-1.,0.));
#735=DIRECTION('ref_axis',(1.,0.,0.));
#736=DIRECTION('',(1.,0.,0.));
#737=DIRECTION('',(1.,0.,0.));
#738=DIRECTION('',(0.,0.,1.));
#739=DIRECTION('center_axis',(0.,0.,1.));
#740=DIRECTION('ref_axis',(-1.,0.,0.));
#741=DIRECTION('center_axis',(0.,0.,1.));
#742=DIRECTION('ref_axis',(-1.,0.,0.));
#743=DIRECTION('center_axis',(0.,0.,1.));
#744=DIRECTION('ref_axis',(-1.,0.,0.));
#745=DIRECTION('',(0.,0.,1.));
#746=DIRECTION('center_axis',(-1.,0.,0.));
#747=DIRECTION('ref_axis',(0.,-1.,0.));
#748=DIRECTION('',(0.,-1.,0.));
#749=DIRECTION('',(0.,-1.,0.));
#750=DIRECTION('',(0.,0.,1.));
#751=DIRECTION('center_axis',(0.,0.,1.));
#752=DIRECTION('ref_axis',(1.,0.,0.));
#753=DIRECTION('center_axis',(0.,0.,-1.));
#754=DIRECTION('ref_axis',(1.,0.,0.));
#755=DIRECTION('center_axis',(0.,0.,-1.));
#756=DIRECTION('ref_axis',(1.,0.,0.));
#757=DIRECTION('center_axis',(-0.655068869123884,0.755569174003781,0.));
#758=DIRECTION('ref_axis',(-0.755569174003781,-0.655068869123884,0.));
#759=DIRECTION('',(0.755569174003781,0.655068869123884,0.));
#760=DIRECTION('',(0.,0.,1.));
#761=DIRECTION('',(-0.755569174003781,-0.655068869123884,0.));
#762=DIRECTION('',(0.,0.,1.));
#763=DIRECTION('center_axis',(0.,0.,1.));
#764=DIRECTION('ref_axis',(0.141769553409512,0.989899688719048,0.));
#765=DIRECTION('center_axis',(0.,0.,1.));
#766=DIRECTION('ref_axis',(0.141769553409512,0.989899688719048,0.));
#767=DIRECTION('center_axis',(0.,0.,1.));
#768=DIRECTION('ref_axis',(0.141769553409512,0.989899688719048,0.));
#769=DIRECTION('',(0.,0.,1.));
#770=DIRECTION('center_axis',(0.141769553409512,0.989899688719048,0.));
#771=DIRECTION('ref_axis',(-0.989899688719048,0.141769553409512,0.));
#772=DIRECTION('',(-0.989899688719048,0.141769553409512,0.));
#773=DIRECTION('',(-0.989899688719048,0.141769553409512,0.));
#774=DIRECTION('',(0.,0.,1.));
#775=DIRECTION('center_axis',(0.,0.,1.));
#776=DIRECTION('ref_axis',(0.755569174003782,0.655068869123882,0.));
#777=DIRECTION('center_axis',(0.,0.,1.));
#778=DIRECTION('ref_axis',(0.755569174003782,0.655068869123882,0.));
#779=DIRECTION('center_axis',(0.,0.,1.));
#780=DIRECTION('ref_axis',(0.755569174003782,0.655068869123882,0.));
#781=DIRECTION('',(0.,0.,1.));
#782=DIRECTION('center_axis',(0.75556917400378,0.655068869123885,0.));
#783=DIRECTION('ref_axis',(-0.655068869123885,0.75556917400378,0.));
#784=DIRECTION('',(0.655068869123885,-0.75556917400378,0.));
#785=DIRECTION('',(-0.655068869123885,0.75556917400378,0.));
#786=DIRECTION('',(0.,0.,1.));
#787=DIRECTION('center_axis',(0.,0.,1.));
#788=DIRECTION('ref_axis',(1.,-8.88178419700113E-15,0.));
#789=DIRECTION('center_axis',(0.,0.,1.));
#790=DIRECTION('ref_axis',(1.,-8.88178419700113E-15,0.));
#791=DIRECTION('center_axis',(0.,0.,1.));
#792=DIRECTION('ref_axis',(1.,-8.88178419700113E-15,0.));
#793=DIRECTION('',(0.,0.,1.));
#794=DIRECTION('center_axis',(1.,0.,0.));
#795=DIRECTION('ref_axis',(0.,1.,0.));
#796=DIRECTION('',(0.,1.,0.));
#797=DIRECTION('',(0.,1.,0.));
#798=DIRECTION('',(0.,0.,1.));
#799=DIRECTION('center_axis',(0.,0.,1.));
#800=DIRECTION('ref_axis',(1.48029736616687E-15,-1.,0.));
#801=DIRECTION('center_axis',(0.,0.,1.));
#802=DIRECTION('ref_axis',(1.48029736616687E-15,-1.,0.));
#803=DIRECTION('center_axis',(0.,0.,1.));
#804=DIRECTION('ref_axis',(1.48029736616687E-15,-1.,0.));
#805=DIRECTION('',(0.,0.,1.));
#806=DIRECTION('center_axis',(-1.23988862691912E-33,-1.,0.));
#807=DIRECTION('ref_axis',(1.,-1.23988862691912E-33,0.));
#808=DIRECTION('',(1.,-1.23988862691912E-33,0.));
#809=DIRECTION('',(1.,-1.23988862691912E-33,0.));
#810=DIRECTION('',(0.,0.,1.));
#811=DIRECTION('center_axis',(0.,0.,1.));
#812=DIRECTION('ref_axis',(-1.,0.,0.));
#813=DIRECTION('center_axis',(0.,0.,1.));
#814=DIRECTION('ref_axis',(-1.,0.,0.));
#815=DIRECTION('center_axis',(0.,0.,1.));
#816=DIRECTION('ref_axis',(-1.,0.,0.));
#817=DIRECTION('',(0.,0.,1.));
#818=DIRECTION('center_axis',(-1.,-3.71625439414334E-17,0.));
#819=DIRECTION('ref_axis',(3.71625439414334E-17,-1.,0.));
#820=DIRECTION('',(3.71625439414334E-17,-1.,0.));
#821=DIRECTION('',(3.71625439414334E-17,-1.,0.));
#822=DIRECTION('',(0.,0.,1.));
#823=DIRECTION('center_axis',(0.,0.,1.));
#824=DIRECTION('ref_axis',(-0.655068869123882,0.755569174003783,0.));
#825=DIRECTION('center_axis',(0.,0.,1.));
#826=DIRECTION('ref_axis',(-0.655068869123882,0.755569174003783,0.));
#827=DIRECTION('center_axis',(0.,0.,1.));
#828=DIRECTION('ref_axis',(-0.655068869123882,0.755569174003783,0.));
#829=DIRECTION('center_axis',(0.,0.,1.));
#830=DIRECTION('ref_axis',(1.,0.,0.));
#831=DIRECTION('center_axis',(0.,0.,1.));
#832=DIRECTION('ref_axis',(1.,0.,0.));
#833=CARTESIAN_POINT('',(0.,0.,0.));
#834=CARTESIAN_POINT('Origin',(4.,13.6,0.));
#835=CARTESIAN_POINT('',(4.,13.6,0.));
#836=CARTESIAN_POINT('',(9.8,13.6,0.));
#837=CARTESIAN_POINT('',(4.,13.6,0.));
#838=CARTESIAN_POINT('',(9.8,13.6,3.));
#839=CARTESIAN_POINT('',(9.8,13.6,0.));
#840=CARTESIAN_POINT('',(4.,13.6,3.));
#841=CARTESIAN_POINT('',(4.,13.6,3.));
#842=CARTESIAN_POINT('',(4.,13.6,0.));
#843=CARTESIAN_POINT('Origin',(4.,12.6,0.));
#844=CARTESIAN_POINT('',(3.,12.6,0.));
#845=CARTESIAN_POINT('Origin',(4.,12.6,0.));
#846=CARTESIAN_POINT('',(3.,12.6,3.));
#847=CARTESIAN_POINT('Origin',(4.,12.6,3.));
#848=CARTESIAN_POINT('',(3.,12.6,0.));
#849=CARTESIAN_POINT('Origin',(3.,4.8,0.));
#850=CARTESIAN_POINT('',(3.,4.8,0.));
#851=CARTESIAN_POINT('',(3.,12.6,0.));
#852=CARTESIAN_POINT('',(3.,4.8,3.));
#853=CARTESIAN_POINT('',(3.,12.6,3.));
#854=CARTESIAN_POINT('',(3.,4.8,0.));
#855=CARTESIAN_POINT('Origin',(4.,4.8,0.));
#856=CARTESIAN_POINT('',(4.,3.8,0.));
#857=CARTESIAN_POINT('Origin',(4.,4.8,0.));
#858=CARTESIAN_POINT('',(4.,3.8,3.));
#859=CARTESIAN_POINT('Origin',(4.,4.8,3.));
#860=CARTESIAN_POINT('',(4.,3.8,0.));
#861=CARTESIAN_POINT('Origin',(35.8,3.8,0.));
#862=CARTESIAN_POINT('',(35.8,3.8,0.));
#863=CARTESIAN_POINT('',(4.,3.8,0.));
#864=CARTESIAN_POINT('',(35.8,3.8,3.));
#865=CARTESIAN_POINT('',(4.,3.8,3.));
#866=CARTESIAN_POINT('',(35.8,3.8,0.));
#867=CARTESIAN_POINT('Origin',(35.8,4.8,0.));
#868=CARTESIAN_POINT('',(36.8,4.8,0.));
#869=CARTESIAN_POINT('Origin',(35.8,4.8,0.));
#870=CARTESIAN_POINT('',(36.8,4.8,3.));
#871=CARTESIAN_POINT('Origin',(35.8,4.8,3.));
#872=CARTESIAN_POINT('',(36.8,4.8,0.));
#873=CARTESIAN_POINT('Origin',(36.8,12.6,0.));
#874=CARTESIAN_POINT('',(36.8,12.6,0.));
#875=CARTESIAN_POINT('',(36.8,4.8,0.));
#876=CARTESIAN_POINT('',(36.8,12.6,3.));
#877=CARTESIAN_POINT('',(36.8,4.8,3.));
#878=CARTESIAN_POINT('',(36.8,12.6,0.));
#879=CARTESIAN_POINT('Origin',(35.8,12.6,0.));
#880=CARTESIAN_POINT('',(35.8,13.6,0.));
#881=CARTESIAN_POINT('Origin',(35.8,12.6,0.));
#882=CARTESIAN_POINT('',(35.8,13.6,3.));
#883=CARTESIAN_POINT('Origin',(35.8,12.6,3.));
#884=CARTESIAN_POINT('',(35.8,13.6,0.));
#885=CARTESIAN_POINT('Origin',(30.,13.6,0.));
#886=CARTESIAN_POINT('',(30.,13.6,0.));
#887=CARTESIAN_POINT('',(30.,13.6,0.));
#888=CARTESIAN_POINT('',(30.,13.6,3.));
#889=CARTESIAN_POINT('',(30.,13.6,3.));
#890=CARTESIAN_POINT('',(30.,13.6,0.));
#891=CARTESIAN_POINT('Origin',(30.,12.6,0.));
#892=CARTESIAN_POINT('',(29.,12.6,0.));
#893=CARTESIAN_POINT('Origin',(30.,12.6,0.));
#894=CARTESIAN_POINT('',(29.,12.6,3.));
#895=CARTESIAN_POINT('Origin',(30.,12.6,3.));
#896=CARTESIAN_POINT('',(29.,12.6,0.));
#897=CARTESIAN_POINT('Origin',(29.,7.8,0.));
#898=CARTESIAN_POINT('',(29.,7.8,0.));
#899=CARTESIAN_POINT('',(29.,7.8,0.));
#900=CARTESIAN_POINT('',(29.,7.8,3.));
#901=CARTESIAN_POINT('',(29.,7.8,3.));
#902=CARTESIAN_POINT('',(29.,7.8,0.));
#903=CARTESIAN_POINT('Origin',(28.,7.8,0.));
#904=CARTESIAN_POINT('',(28.,6.8,0.));
#905=CARTESIAN_POINT('Origin',(28.,7.8,0.));
#906=CARTESIAN_POINT('',(28.,6.8,3.));
#907=CARTESIAN_POINT('Origin',(28.,7.8,3.));
#908=CARTESIAN_POINT('',(28.,6.8,0.));
#909=CARTESIAN_POINT('Origin',(11.8,6.8,0.));
#910=CARTESIAN_POINT('',(11.8,6.8,0.));
#911=CARTESIAN_POINT('',(11.8,6.8,0.));
#912=CARTESIAN_POINT('',(11.8,6.8,3.));
#913=CARTESIAN_POINT('',(11.8,6.8,3.));
#914=CARTESIAN_POINT('',(11.8,6.8,0.));
#915=CARTESIAN_POINT('Origin',(11.8,7.8,0.));
#916=CARTESIAN_POINT('',(10.8,7.8,0.));
#917=CARTESIAN_POINT('Origin',(11.8,7.8,0.));
#918=CARTESIAN_POINT('',(10.8,7.8,3.));
#919=CARTESIAN_POINT('Origin',(11.8,7.8,3.));
#920=CARTESIAN_POINT('',(10.8,7.8,0.));
#921=CARTESIAN_POINT('Origin',(10.8,12.6,0.));
#922=CARTESIAN_POINT('',(10.8,12.6,0.));
#923=CARTESIAN_POINT('',(10.8,12.6,0.));
#924=CARTESIAN_POINT('',(10.8,12.6,3.));
#925=CARTESIAN_POINT('',(10.8,12.6,3.));
#926=CARTESIAN_POINT('',(10.8,12.6,0.));
#927=CARTESIAN_POINT('Origin',(9.8,12.6,0.));
#928=CARTESIAN_POINT('Origin',(9.8,12.6,0.));
#929=CARTESIAN_POINT('Origin',(9.8,12.6,3.));
#930=CARTESIAN_POINT('Origin',(1.80733381200003,23.4669354403152,0.));
#931=CARTESIAN_POINT('',(0.172465565438057,22.0495254528658,0.));
#932=CARTESIAN_POINT('',(1.80733381200003,23.4669354403152,0.));
#933=CARTESIAN_POINT('',(0.172465565438057,22.0495254528658,0.));
#934=CARTESIAN_POINT('',(0.172465565438057,22.0495254528658,3.));
#935=CARTESIAN_POINT('',(0.172465565438057,22.0495254528658,0.));
#936=CARTESIAN_POINT('',(1.80733381200003,23.4669354403152,3.));
#937=CARTESIAN_POINT('',(0.172465565438057,22.0495254528658,3.));
#938=CARTESIAN_POINT('',(1.80733381200003,23.4669354403152,0.));
#939=CARTESIAN_POINT('Origin',(2.13486824656198,23.0891508533133,0.));
#940=CARTESIAN_POINT('',(2.20575302326673,23.5841006976728,0.));
#941=CARTESIAN_POINT('Origin',(2.13486824656198,23.0891508533133,0.));
#942=CARTESIAN_POINT('',(2.20575302326673,23.5841006976728,3.));
#943=CARTESIAN_POINT('Origin',(2.13486824656198,23.0891508533133,3.));
#944=CARTESIAN_POINT('',(2.20575302326673,23.5841006976728,0.));
#945=CARTESIAN_POINT('Origin',(37.6355162252629,18.5099888823116,0.));
#946=CARTESIAN_POINT('',(37.6355162252629,18.5099888823116,0.));
#947=CARTESIAN_POINT('',(37.6355162252629,18.5099888823116,0.));
#948=CARTESIAN_POINT('',(37.6355162252629,18.5099888823116,3.));
#949=CARTESIAN_POINT('',(37.6355162252629,18.5099888823116,3.));
#950=CARTESIAN_POINT('',(37.6355162252629,18.5099888823116,0.));
#951=CARTESIAN_POINT('Origin',(37.5646314485581,18.0150390379521,0.));
#952=CARTESIAN_POINT('',(37.94241603556,18.342573472514,0.));
#953=CARTESIAN_POINT('Origin',(37.5646314485581,18.0150390379521,0.));
#954=CARTESIAN_POINT('',(37.94241603556,18.342573472514,3.));
#955=CARTESIAN_POINT('Origin',(37.5646314485581,18.0150390379521,3.));
#956=CARTESIAN_POINT('',(37.94241603556,18.342573472514,0.));
#957=CARTESIAN_POINT('Origin',(39.6777845870019,16.340965634305,0.));
#958=CARTESIAN_POINT('',(39.6777845870019,16.340965634305,0.));
#959=CARTESIAN_POINT('',(37.94241603556,18.342573472514,0.));
#960=CARTESIAN_POINT('',(39.6777845870019,16.340965634305,3.));
#961=CARTESIAN_POINT('',(37.94241603556,18.342573472514,3.));
#962=CARTESIAN_POINT('',(39.6777845870019,16.340965634305,0.));
#963=CARTESIAN_POINT('Origin',(39.3,16.0134311997431,0.));
#964=CARTESIAN_POINT('',(39.8,16.0134311997431,0.));
#965=CARTESIAN_POINT('Origin',(39.3,16.0134311997431,0.));
#966=CARTESIAN_POINT('',(39.8,16.0134311997431,3.));
#967=CARTESIAN_POINT('Origin',(39.3,16.0134311997431,3.));
#968=CARTESIAN_POINT('',(39.8,16.0134311997431,0.));
#969=CARTESIAN_POINT('Origin',(39.8,3.,0.));
#970=CARTESIAN_POINT('',(39.8,3.,0.));
#971=CARTESIAN_POINT('',(39.8,3.,0.));
#972=CARTESIAN_POINT('',(39.8,3.,3.));
#973=CARTESIAN_POINT('',(39.8,3.,3.));
#974=CARTESIAN_POINT('',(39.8,3.,0.));
#975=CARTESIAN_POINT('Origin',(36.8,3.00000000000001,0.));
#976=CARTESIAN_POINT('',(36.8,0.,0.));
#977=CARTESIAN_POINT('Origin',(36.8,3.00000000000001,0.));
#978=CARTESIAN_POINT('',(36.8,0.,3.));
#979=CARTESIAN_POINT('Origin',(36.8,3.00000000000001,3.));
#980=CARTESIAN_POINT('',(36.8,0.,0.));
#981=CARTESIAN_POINT('Origin',(3.,4.19082355898662E-32,0.));
#982=CARTESIAN_POINT('',(3.,0.,0.));
#983=CARTESIAN_POINT('',(3.,4.19082355898662E-32,0.));
#984=CARTESIAN_POINT('',(3.,0.,3.));
#985=CARTESIAN_POINT('',(3.,4.19082355898662E-32,3.));
#986=CARTESIAN_POINT('',(3.,0.,0.));
#987=CARTESIAN_POINT('Origin',(3.,3.,0.));
#988=CARTESIAN_POINT('',(0.,3.,0.));
#989=CARTESIAN_POINT('Origin',(3.,3.,0.));
#990=CARTESIAN_POINT('',(0.,3.,3.));
#991=CARTESIAN_POINT('Origin',(3.,3.,3.));
#992=CARTESIAN_POINT('',(0.,3.,0.));
#993=CARTESIAN_POINT('Origin',(-6.93889390390723E-16,21.6717408658639,0.));
#994=CARTESIAN_POINT('',(-2.22044604925031E-15,21.6717408658639,0.));
#995=CARTESIAN_POINT('',(-6.93889390390723E-16,21.6717408658639,0.));
#996=CARTESIAN_POINT('',(-2.22044604925031E-15,21.6717408658639,3.));
#997=CARTESIAN_POINT('',(-6.93889390390723E-16,21.6717408658639,3.));
#998=CARTESIAN_POINT('',(-2.22044604925031E-15,21.6717408658639,0.));
#999=CARTESIAN_POINT('Origin',(0.499999999999999,21.6717408658639,0.));
#1000=CARTESIAN_POINT('Origin',(0.499999999999999,21.6717408658639,0.));
#1001=CARTESIAN_POINT('Origin',(0.499999999999999,21.6717408658639,3.));
#1002=CARTESIAN_POINT('Origin',(19.9,11.7945754266567,3.));
#1003=CARTESIAN_POINT('Origin',(19.9,11.7945754266567,0.));
#1004=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#1008,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#1005=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#1008,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#1006=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1004))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1008,#1010,#1011))
REPRESENTATION_CONTEXT('','3D')
);
#1007=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1005))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1008,#1010,#1011))
REPRESENTATION_CONTEXT('','3D')
);
#1008=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#1009=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT($,.METRE.)
);
#1010=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#1011=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#1012=SHAPE_DEFINITION_REPRESENTATION(#1013,#1014);
#1013=PRODUCT_DEFINITION_SHAPE('',$,#1016);
#1014=SHAPE_REPRESENTATION('',(#600),#1006);
#1015=PRODUCT_DEFINITION_CONTEXT('part definition',#1020,'design');
#1016=PRODUCT_DEFINITION('S_1140','S_1140 v1',#1017,#1015);
#1017=PRODUCT_DEFINITION_FORMATION('',$,#1022);
#1018=PRODUCT_RELATED_PRODUCT_CATEGORY('S_1140 v1','S_1140 v1',(#1022));
#1019=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2009,#1020);
#1020=APPLICATION_CONTEXT(
'Core Data for Automotive Mechanical Design Process');
#1021=PRODUCT_CONTEXT('part definition',#1020,'mechanical');
#1022=PRODUCT('S_1140','S_1140 v1',$,(#1021));
#1023=PRESENTATION_STYLE_ASSIGNMENT((#1024));
#1024=SURFACE_STYLE_USAGE(.BOTH.,#1025);
#1025=SURFACE_SIDE_STYLE('',(#1026));
#1026=SURFACE_STYLE_FILL_AREA(#1027);
#1027=FILL_AREA_STYLE('Steel - Satin',(#1028));
#1028=FILL_AREA_STYLE_COLOUR('Steel - Satin',#1029);
#1029=COLOUR_RGB('Steel - Satin',0.627450980392157,0.627450980392157,0.627450980392157);
ENDSEC;
END-ISO-10303-21;
