ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ (''),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 
'G:/Shared drives/TIP/Validation/SMF KION/Tset SMF Shapes/S_1157.step',

/* time_stamp */ '2021-10-04T14:53:33+02:00',
/* author */ (''),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v18.1',
/* originating_system */ 'Autodesk Translation Framework v10.10.0.1391',

/* authorisation */ '');

FILE_SCHEMA (('AUTOMOTIVE_DESIGN { 1 0 10303 214 3 1 1 }'));
ENDSEC;

DATA;
#10=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#13),#1623);
#11=SHAPE_REPRESENTATION_RELATIONSHIP('SRR','None',#1630,#12);
#12=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#14),#1622);
#13=STYLED_ITEM('',(#1639),#14);
#14=MANIFOLD_SOLID_BREP('Body1',#935);
#15=B_SPLINE_SURFACE_WITH_KNOTS('',3,3,((#1328,#1329,#1330,#1331),(#1332,
#1333,#1334,#1335),(#1336,#1337,#1338,#1339),(#1340,#1341,#1342,#1343)),
 .UNSPECIFIED.,.F.,.F.,.F.,(4,4),(4,4),(3.33287653855218,5.10264040338901),
(0.,4.),.UNSPECIFIED.);
#16=B_SPLINE_SURFACE_WITH_KNOTS('',3,3,((#1421,#1422,#1423,#1424),(#1425,
#1426,#1427,#1428),(#1429,#1430,#1431,#1432),(#1433,#1434,#1435,#1436)),
 .UNSPECIFIED.,.F.,.F.,.F.,(4,4),(4,4),(23.2628765758789,25.0326404407157),
(0.,4.),.UNSPECIFIED.);
#17=CIRCLE('',#951,50.);
#18=CIRCLE('',#956,49.9999999999999);
#19=CIRCLE('',#957,49.9999999999996);
#20=CIRCLE('',#958,49.9999999999999);
#21=CIRCLE('',#959,2.);
#22=CIRCLE('',#960,1.99999999999999);
#23=CIRCLE('',#961,1.99999999999999);
#24=CIRCLE('',#962,1.99999999999999);
#25=CIRCLE('',#963,100.);
#26=CIRCLE('',#965,90.);
#27=CIRCLE('',#968,90.);
#28=CIRCLE('',#970,19.9999999999999);
#29=CIRCLE('',#971,20.);
#30=CIRCLE('',#972,4.99999999999986);
#31=CIRCLE('',#973,2.00000000000003);
#32=CIRCLE('',#974,24.0000000000001);
#33=CIRCLE('',#975,1.99999999999989);
#34=CIRCLE('',#976,9.99999999999986);
#35=CIRCLE('',#977,49.9999999999998);
#36=CIRCLE('',#978,50.0000000000006);
#37=CIRCLE('',#980,50.0000000000006);
#38=CIRCLE('',#981,49.9999999999998);
#39=CIRCLE('',#982,9.99999999999986);
#40=CIRCLE('',#983,1.99999999999989);
#41=CIRCLE('',#984,24.0000000000001);
#42=CIRCLE('',#985,2.00000000000003);
#43=CIRCLE('',#986,4.99999999999986);
#44=CIRCLE('',#987,20.);
#45=CIRCLE('',#988,19.9999999999999);
#46=CIRCLE('',#992,50.);
#47=CIRCLE('',#994,1.99999999999999);
#48=CIRCLE('',#997,2.);
#49=CIRCLE('',#1000,49.9999999999999);
#50=CIRCLE('',#1003,49.9999999999996);
#51=CIRCLE('',#1006,49.9999999999999);
#52=CIRCLE('',#1027,100.);
#53=CIRCLE('',#1030,1.99999999999999);
#54=CIRCLE('',#1033,1.99999999999999);
#55=B_SPLINE_CURVE_WITH_KNOTS('',3,(#1321,#1322,#1323,#1324),
 .UNSPECIFIED.,.F.,.F.,(4,4),(0.,0.26179938779915),.UNSPECIFIED.);
#56=B_SPLINE_CURVE_WITH_KNOTS('',3,(#1347,#1348,#1349,#1350),
 .UNSPECIFIED.,.F.,.F.,(4,4),(0.,0.26179938779915),.UNSPECIFIED.);
#57=B_SPLINE_CURVE_WITH_KNOTS('',3,(#1416,#1417,#1418,#1419),
 .UNSPECIFIED.,.F.,.F.,(4,4),(0.,0.261799387799149),.UNSPECIFIED.);
#58=B_SPLINE_CURVE_WITH_KNOTS('',3,(#1439,#1440,#1441,#1442),
 .UNSPECIFIED.,.F.,.F.,(4,4),(0.,0.261799387799149),.UNSPECIFIED.);
#59=CYLINDRICAL_SURFACE('',#950,50.);
#60=CYLINDRICAL_SURFACE('',#967,90.);
#61=CYLINDRICAL_SURFACE('',#990,90.);
#62=CYLINDRICAL_SURFACE('',#993,1.99999999999999);
#63=CYLINDRICAL_SURFACE('',#996,2.);
#64=CYLINDRICAL_SURFACE('',#999,49.9999999999999);
#65=CYLINDRICAL_SURFACE('',#1002,49.9999999999996);
#66=CYLINDRICAL_SURFACE('',#1005,49.9999999999999);
#67=CYLINDRICAL_SURFACE('',#1008,50.0000000000006);
#68=CYLINDRICAL_SURFACE('',#1010,49.9999999999998);
#69=CYLINDRICAL_SURFACE('',#1012,9.99999999999986);
#70=CYLINDRICAL_SURFACE('',#1014,1.99999999999989);
#71=CYLINDRICAL_SURFACE('',#1016,24.0000000000001);
#72=CYLINDRICAL_SURFACE('',#1018,2.00000000000003);
#73=CYLINDRICAL_SURFACE('',#1020,4.99999999999986);
#74=CYLINDRICAL_SURFACE('',#1022,20.);
#75=CYLINDRICAL_SURFACE('',#1024,19.9999999999999);
#76=CYLINDRICAL_SURFACE('',#1026,100.);
#77=CYLINDRICAL_SURFACE('',#1029,1.99999999999999);
#78=CYLINDRICAL_SURFACE('',#1032,1.99999999999999);
#79=CYLINDRICAL_SURFACE('',#1036,50.);
#80=FACE_OUTER_BOUND('',#132,.T.);
#81=FACE_OUTER_BOUND('',#133,.T.);
#82=FACE_OUTER_BOUND('',#134,.T.);
#83=FACE_OUTER_BOUND('',#135,.T.);
#84=FACE_OUTER_BOUND('',#136,.T.);
#85=FACE_OUTER_BOUND('',#137,.T.);
#86=FACE_OUTER_BOUND('',#138,.T.);
#87=FACE_OUTER_BOUND('',#139,.T.);
#88=FACE_OUTER_BOUND('',#140,.T.);
#89=FACE_OUTER_BOUND('',#141,.T.);
#90=FACE_OUTER_BOUND('',#142,.T.);
#91=FACE_OUTER_BOUND('',#143,.T.);
#92=FACE_OUTER_BOUND('',#144,.T.);
#93=FACE_OUTER_BOUND('',#145,.T.);
#94=FACE_OUTER_BOUND('',#146,.T.);
#95=FACE_OUTER_BOUND('',#147,.T.);
#96=FACE_OUTER_BOUND('',#148,.T.);
#97=FACE_OUTER_BOUND('',#149,.T.);
#98=FACE_OUTER_BOUND('',#150,.T.);
#99=FACE_OUTER_BOUND('',#151,.T.);
#100=FACE_OUTER_BOUND('',#152,.T.);
#101=FACE_OUTER_BOUND('',#153,.T.);
#102=FACE_OUTER_BOUND('',#154,.T.);
#103=FACE_OUTER_BOUND('',#155,.T.);
#104=FACE_OUTER_BOUND('',#156,.T.);
#105=FACE_OUTER_BOUND('',#157,.T.);
#106=FACE_OUTER_BOUND('',#158,.T.);
#107=FACE_OUTER_BOUND('',#159,.T.);
#108=FACE_OUTER_BOUND('',#160,.T.);
#109=FACE_OUTER_BOUND('',#161,.T.);
#110=FACE_OUTER_BOUND('',#162,.T.);
#111=FACE_OUTER_BOUND('',#163,.T.);
#112=FACE_OUTER_BOUND('',#164,.T.);
#113=FACE_OUTER_BOUND('',#165,.T.);
#114=FACE_OUTER_BOUND('',#166,.T.);
#115=FACE_OUTER_BOUND('',#167,.T.);
#116=FACE_OUTER_BOUND('',#168,.T.);
#117=FACE_OUTER_BOUND('',#169,.T.);
#118=FACE_OUTER_BOUND('',#170,.T.);
#119=FACE_OUTER_BOUND('',#171,.T.);
#120=FACE_OUTER_BOUND('',#172,.T.);
#121=FACE_OUTER_BOUND('',#173,.T.);
#122=FACE_OUTER_BOUND('',#174,.T.);
#123=FACE_OUTER_BOUND('',#175,.T.);
#124=FACE_OUTER_BOUND('',#176,.T.);
#125=FACE_OUTER_BOUND('',#177,.T.);
#126=FACE_OUTER_BOUND('',#178,.T.);
#127=FACE_OUTER_BOUND('',#179,.T.);
#128=FACE_OUTER_BOUND('',#180,.T.);
#129=FACE_OUTER_BOUND('',#181,.T.);
#130=FACE_OUTER_BOUND('',#182,.T.);
#131=FACE_OUTER_BOUND('',#183,.T.);
#132=EDGE_LOOP('',(#586,#587,#588,#589));
#133=EDGE_LOOP('',(#590,#591,#592,#593));
#134=EDGE_LOOP('',(#594,#595,#596,#597));
#135=EDGE_LOOP('',(#598,#599,#600,#601));
#136=EDGE_LOOP('',(#602,#603,#604,#605));
#137=EDGE_LOOP('',(#606,#607,#608,#609));
#138=EDGE_LOOP('',(#610,#611,#612,#613,#614,#615,#616,#617,#618,#619,#620,
#621,#622,#623,#624,#625,#626,#627));
#139=EDGE_LOOP('',(#628,#629,#630,#631));
#140=EDGE_LOOP('',(#632,#633,#634,#635));
#141=EDGE_LOOP('',(#636,#637,#638,#639));
#142=EDGE_LOOP('',(#640,#641,#642,#643));
#143=EDGE_LOOP('',(#644,#645,#646,#647,#648,#649,#650,#651,#652,#653,#654,
#655,#656,#657,#658,#659,#660,#661,#662,#663));
#144=EDGE_LOOP('',(#664,#665,#666,#667,#668,#669,#670,#671,#672,#673,#674,
#675,#676,#677,#678,#679,#680,#681,#682,#683));
#145=EDGE_LOOP('',(#684,#685,#686,#687));
#146=EDGE_LOOP('',(#688,#689,#690,#691));
#147=EDGE_LOOP('',(#692,#693,#694,#695));
#148=EDGE_LOOP('',(#696,#697,#698,#699));
#149=EDGE_LOOP('',(#700,#701,#702,#703));
#150=EDGE_LOOP('',(#704,#705,#706,#707));
#151=EDGE_LOOP('',(#708,#709,#710,#711));
#152=EDGE_LOOP('',(#712,#713,#714,#715));
#153=EDGE_LOOP('',(#716,#717,#718,#719));
#154=EDGE_LOOP('',(#720,#721,#722,#723));
#155=EDGE_LOOP('',(#724,#725,#726,#727));
#156=EDGE_LOOP('',(#728,#729,#730,#731));
#157=EDGE_LOOP('',(#732,#733,#734,#735));
#158=EDGE_LOOP('',(#736,#737,#738,#739));
#159=EDGE_LOOP('',(#740,#741,#742,#743));
#160=EDGE_LOOP('',(#744,#745,#746,#747));
#161=EDGE_LOOP('',(#748,#749,#750,#751));
#162=EDGE_LOOP('',(#752,#753,#754,#755));
#163=EDGE_LOOP('',(#756,#757,#758,#759));
#164=EDGE_LOOP('',(#760,#761,#762,#763));
#165=EDGE_LOOP('',(#764,#765,#766,#767));
#166=EDGE_LOOP('',(#768,#769,#770,#771));
#167=EDGE_LOOP('',(#772,#773,#774,#775));
#168=EDGE_LOOP('',(#776,#777,#778,#779));
#169=EDGE_LOOP('',(#780,#781,#782,#783));
#170=EDGE_LOOP('',(#784,#785,#786,#787));
#171=EDGE_LOOP('',(#788,#789,#790,#791));
#172=EDGE_LOOP('',(#792,#793,#794,#795));
#173=EDGE_LOOP('',(#796,#797,#798,#799));
#174=EDGE_LOOP('',(#800,#801,#802,#803));
#175=EDGE_LOOP('',(#804,#805,#806,#807));
#176=EDGE_LOOP('',(#808,#809,#810,#811));
#177=EDGE_LOOP('',(#812,#813,#814,#815));
#178=EDGE_LOOP('',(#816,#817,#818,#819));
#179=EDGE_LOOP('',(#820,#821,#822,#823));
#180=EDGE_LOOP('',(#824,#825,#826,#827));
#181=EDGE_LOOP('',(#828,#829,#830,#831));
#182=EDGE_LOOP('',(#832,#833,#834,#835,#836,#837,#838,#839,#840,#841,#842,
#843,#844,#845,#846,#847,#848,#849));
#183=EDGE_LOOP('',(#850,#851,#852,#853));
#184=LINE('',#1311,#276);
#185=LINE('',#1313,#277);
#186=LINE('',#1315,#278);
#187=LINE('',#1316,#279);
#188=LINE('',#1319,#280);
#189=LINE('',#1326,#281);
#190=LINE('',#1345,#282);
#191=LINE('',#1351,#283);
#192=LINE('',#1353,#284);
#193=LINE('',#1355,#285);
#194=LINE('',#1356,#286);
#195=LINE('',#1359,#287);
#196=LINE('',#1360,#288);
#197=LINE('',#1363,#289);
#198=LINE('',#1365,#290);
#199=LINE('',#1366,#291);
#200=LINE('',#1369,#292);
#201=LINE('',#1371,#293);
#202=LINE('',#1375,#294);
#203=LINE('',#1379,#295);
#204=LINE('',#1383,#296);
#205=LINE('',#1387,#297);
#206=LINE('',#1391,#298);
#207=LINE('',#1395,#299);
#208=LINE('',#1399,#300);
#209=LINE('',#1402,#301);
#210=LINE('',#1407,#302);
#211=LINE('',#1409,#303);
#212=LINE('',#1411,#304);
#213=LINE('',#1412,#305);
#214=LINE('',#1415,#306);
#215=LINE('',#1438,#307);
#216=LINE('',#1443,#308);
#217=LINE('',#1445,#309);
#218=LINE('',#1447,#310);
#219=LINE('',#1451,#311);
#220=LINE('',#1455,#312);
#221=LINE('',#1459,#313);
#222=LINE('',#1463,#314);
#223=LINE('',#1467,#315);
#224=LINE('',#1471,#316);
#225=LINE('',#1475,#317);
#226=LINE('',#1479,#318);
#227=LINE('',#1485,#319);
#228=LINE('',#1489,#320);
#229=LINE('',#1493,#321);
#230=LINE('',#1497,#322);
#231=LINE('',#1501,#323);
#232=LINE('',#1505,#324);
#233=LINE('',#1509,#325);
#234=LINE('',#1513,#326);
#235=LINE('',#1516,#327);
#236=LINE('',#1525,#328);
#237=LINE('',#1526,#329);
#238=LINE('',#1529,#330);
#239=LINE('',#1530,#331);
#240=LINE('',#1534,#332);
#241=LINE('',#1537,#333);
#242=LINE('',#1538,#334);
#243=LINE('',#1542,#335);
#244=LINE('',#1545,#336);
#245=LINE('',#1546,#337);
#246=LINE('',#1550,#338);
#247=LINE('',#1553,#339);
#248=LINE('',#1554,#340);
#249=LINE('',#1558,#341);
#250=LINE('',#1560,#342);
#251=LINE('',#1562,#343);
#252=LINE('',#1564,#344);
#253=LINE('',#1566,#345);
#254=LINE('',#1568,#346);
#255=LINE('',#1570,#347);
#256=LINE('',#1572,#348);
#257=LINE('',#1574,#349);
#258=LINE('',#1576,#350);
#259=LINE('',#1578,#351);
#260=LINE('',#1580,#352);
#261=LINE('',#1582,#353);
#262=LINE('',#1584,#354);
#263=LINE('',#1586,#355);
#264=LINE('',#1588,#356);
#265=LINE('',#1590,#357);
#266=LINE('',#1592,#358);
#267=LINE('',#1594,#359);
#268=LINE('',#1599,#360);
#269=LINE('',#1602,#361);
#270=LINE('',#1603,#362);
#271=LINE('',#1607,#363);
#272=LINE('',#1610,#364);
#273=LINE('',#1611,#365);
#274=LINE('',#1615,#366);
#275=LINE('',#1617,#367);
#276=VECTOR('',#1041,10.);
#277=VECTOR('',#1042,10.);
#278=VECTOR('',#1043,10.);
#279=VECTOR('',#1044,10.);
#280=VECTOR('',#1047,10.);
#281=VECTOR('',#1048,10.);
#282=VECTOR('',#1051,10.);
#283=VECTOR('',#1052,10.);
#284=VECTOR('',#1055,10.);
#285=VECTOR('',#1056,10.);
#286=VECTOR('',#1057,10.);
#287=VECTOR('',#1060,10.);
#288=VECTOR('',#1061,10.);
#289=VECTOR('',#1064,10.);
#290=VECTOR('',#1065,10.);
#291=VECTOR('',#1066,10.);
#292=VECTOR('',#1069,10.);
#293=VECTOR('',#1070,10.);
#294=VECTOR('',#1073,10.);
#295=VECTOR('',#1076,10.);
#296=VECTOR('',#1079,10.);
#297=VECTOR('',#1082,10.);
#298=VECTOR('',#1085,10.);
#299=VECTOR('',#1088,10.);
#300=VECTOR('',#1091,10.);
#301=VECTOR('',#1096,10.);
#302=VECTOR('',#1101,10.);
#303=VECTOR('',#1102,10.);
#304=VECTOR('',#1103,10.);
#305=VECTOR('',#1104,10.);
#306=VECTOR('',#1107,10.);
#307=VECTOR('',#1110,10.);
#308=VECTOR('',#1111,10.);
#309=VECTOR('',#1114,10.);
#310=VECTOR('',#1115,10.);
#311=VECTOR('',#1118,10.);
#312=VECTOR('',#1121,10.);
#313=VECTOR('',#1124,10.);
#314=VECTOR('',#1127,10.);
#315=VECTOR('',#1130,10.);
#316=VECTOR('',#1133,10.);
#317=VECTOR('',#1136,10.);
#318=VECTOR('',#1139,10.);
#319=VECTOR('',#1146,10.);
#320=VECTOR('',#1149,10.);
#321=VECTOR('',#1152,10.);
#322=VECTOR('',#1155,10.);
#323=VECTOR('',#1158,10.);
#324=VECTOR('',#1161,10.);
#325=VECTOR('',#1164,10.);
#326=VECTOR('',#1167,10.);
#327=VECTOR('',#1170,10.);
#328=VECTOR('',#1183,10.);
#329=VECTOR('',#1184,10.);
#330=VECTOR('',#1187,10.);
#331=VECTOR('',#1188,10.);
#332=VECTOR('',#1193,10.);
#333=VECTOR('',#1196,10.);
#334=VECTOR('',#1197,10.);
#335=VECTOR('',#1202,10.);
#336=VECTOR('',#1205,10.);
#337=VECTOR('',#1206,10.);
#338=VECTOR('',#1211,10.);
#339=VECTOR('',#1214,10.);
#340=VECTOR('',#1215,10.);
#341=VECTOR('',#1220,10.);
#342=VECTOR('',#1223,10.);
#343=VECTOR('',#1226,10.);
#344=VECTOR('',#1229,10.);
#345=VECTOR('',#1232,10.);
#346=VECTOR('',#1235,10.);
#347=VECTOR('',#1238,10.);
#348=VECTOR('',#1241,10.);
#349=VECTOR('',#1244,10.);
#350=VECTOR('',#1247,10.);
#351=VECTOR('',#1250,10.);
#352=VECTOR('',#1253,10.);
#353=VECTOR('',#1256,10.);
#354=VECTOR('',#1259,10.);
#355=VECTOR('',#1262,10.);
#356=VECTOR('',#1265,10.);
#357=VECTOR('',#1268,10.);
#358=VECTOR('',#1271,10.);
#359=VECTOR('',#1274,10.);
#360=VECTOR('',#1281,10.);
#361=VECTOR('',#1284,10.);
#362=VECTOR('',#1285,10.);
#363=VECTOR('',#1290,10.);
#364=VECTOR('',#1293,10.);
#365=VECTOR('',#1294,10.);
#366=VECTOR('',#1299,10.);
#367=VECTOR('',#1302,10.);
#368=VERTEX_POINT('',#1309);
#369=VERTEX_POINT('',#1310);
#370=VERTEX_POINT('',#1312);
#371=VERTEX_POINT('',#1314);
#372=VERTEX_POINT('',#1318);
#373=VERTEX_POINT('',#1320);
#374=VERTEX_POINT('',#1325);
#375=VERTEX_POINT('',#1344);
#376=VERTEX_POINT('',#1346);
#377=VERTEX_POINT('',#1354);
#378=VERTEX_POINT('',#1358);
#379=VERTEX_POINT('',#1362);
#380=VERTEX_POINT('',#1364);
#381=VERTEX_POINT('',#1368);
#382=VERTEX_POINT('',#1370);
#383=VERTEX_POINT('',#1372);
#384=VERTEX_POINT('',#1374);
#385=VERTEX_POINT('',#1376);
#386=VERTEX_POINT('',#1378);
#387=VERTEX_POINT('',#1380);
#388=VERTEX_POINT('',#1382);
#389=VERTEX_POINT('',#1384);
#390=VERTEX_POINT('',#1386);
#391=VERTEX_POINT('',#1388);
#392=VERTEX_POINT('',#1390);
#393=VERTEX_POINT('',#1392);
#394=VERTEX_POINT('',#1394);
#395=VERTEX_POINT('',#1396);
#396=VERTEX_POINT('',#1398);
#397=VERTEX_POINT('',#1405);
#398=VERTEX_POINT('',#1406);
#399=VERTEX_POINT('',#1408);
#400=VERTEX_POINT('',#1410);
#401=VERTEX_POINT('',#1414);
#402=VERTEX_POINT('',#1437);
#403=VERTEX_POINT('',#1446);
#404=VERTEX_POINT('',#1448);
#405=VERTEX_POINT('',#1450);
#406=VERTEX_POINT('',#1452);
#407=VERTEX_POINT('',#1454);
#408=VERTEX_POINT('',#1456);
#409=VERTEX_POINT('',#1458);
#410=VERTEX_POINT('',#1460);
#411=VERTEX_POINT('',#1462);
#412=VERTEX_POINT('',#1464);
#413=VERTEX_POINT('',#1466);
#414=VERTEX_POINT('',#1468);
#415=VERTEX_POINT('',#1470);
#416=VERTEX_POINT('',#1472);
#417=VERTEX_POINT('',#1474);
#418=VERTEX_POINT('',#1476);
#419=VERTEX_POINT('',#1478);
#420=VERTEX_POINT('',#1482);
#421=VERTEX_POINT('',#1484);
#422=VERTEX_POINT('',#1486);
#423=VERTEX_POINT('',#1488);
#424=VERTEX_POINT('',#1490);
#425=VERTEX_POINT('',#1492);
#426=VERTEX_POINT('',#1494);
#427=VERTEX_POINT('',#1496);
#428=VERTEX_POINT('',#1498);
#429=VERTEX_POINT('',#1500);
#430=VERTEX_POINT('',#1502);
#431=VERTEX_POINT('',#1504);
#432=VERTEX_POINT('',#1506);
#433=VERTEX_POINT('',#1508);
#434=VERTEX_POINT('',#1510);
#435=VERTEX_POINT('',#1512);
#436=VERTEX_POINT('',#1514);
#437=VERTEX_POINT('',#1522);
#438=VERTEX_POINT('',#1523);
#439=VERTEX_POINT('',#1528);
#440=VERTEX_POINT('',#1532);
#441=VERTEX_POINT('',#1536);
#442=VERTEX_POINT('',#1540);
#443=VERTEX_POINT('',#1544);
#444=VERTEX_POINT('',#1548);
#445=VERTEX_POINT('',#1552);
#446=VERTEX_POINT('',#1556);
#447=VERTEX_POINT('',#1597);
#448=VERTEX_POINT('',#1601);
#449=VERTEX_POINT('',#1605);
#450=VERTEX_POINT('',#1609);
#451=VERTEX_POINT('',#1613);
#452=EDGE_CURVE('',#368,#369,#184,.T.);
#453=EDGE_CURVE('',#370,#369,#185,.T.);
#454=EDGE_CURVE('',#371,#370,#186,.T.);
#455=EDGE_CURVE('',#368,#371,#187,.T.);
#456=EDGE_CURVE('',#372,#368,#188,.T.);
#457=EDGE_CURVE('',#373,#372,#55,.T.);
#458=EDGE_CURVE('',#373,#374,#189,.T.);
#459=EDGE_CURVE('',#374,#368,#17,.T.);
#460=EDGE_CURVE('',#375,#372,#190,.T.);
#461=EDGE_CURVE('',#376,#375,#56,.T.);
#462=EDGE_CURVE('',#376,#373,#191,.T.);
#463=EDGE_CURVE('',#369,#375,#192,.T.);
#464=EDGE_CURVE('',#375,#377,#193,.T.);
#465=EDGE_CURVE('',#370,#377,#194,.T.);
#466=EDGE_CURVE('',#378,#371,#195,.T.);
#467=EDGE_CURVE('',#372,#378,#196,.T.);
#468=EDGE_CURVE('',#379,#376,#197,.T.);
#469=EDGE_CURVE('',#380,#379,#198,.T.);
#470=EDGE_CURVE('',#380,#373,#199,.T.);
#471=EDGE_CURVE('',#381,#376,#200,.T.);
#472=EDGE_CURVE('',#381,#382,#201,.T.);
#473=EDGE_CURVE('',#382,#383,#18,.T.);
#474=EDGE_CURVE('',#383,#384,#202,.T.);
#475=EDGE_CURVE('',#384,#385,#19,.T.);
#476=EDGE_CURVE('',#385,#386,#203,.T.);
#477=EDGE_CURVE('',#386,#387,#20,.T.);
#478=EDGE_CURVE('',#387,#388,#204,.T.);
#479=EDGE_CURVE('',#388,#389,#21,.T.);
#480=EDGE_CURVE('',#389,#390,#205,.T.);
#481=EDGE_CURVE('',#390,#391,#22,.T.);
#482=EDGE_CURVE('',#391,#392,#206,.T.);
#483=EDGE_CURVE('',#392,#393,#23,.T.);
#484=EDGE_CURVE('',#393,#394,#207,.T.);
#485=EDGE_CURVE('',#394,#395,#24,.T.);
#486=EDGE_CURVE('',#395,#396,#208,.T.);
#487=EDGE_CURVE('',#396,#379,#25,.T.);
#488=EDGE_CURVE('',#374,#381,#209,.T.);
#489=EDGE_CURVE('',#369,#381,#26,.T.);
#490=EDGE_CURVE('',#397,#398,#210,.T.);
#491=EDGE_CURVE('',#399,#398,#211,.T.);
#492=EDGE_CURVE('',#400,#399,#212,.T.);
#493=EDGE_CURVE('',#397,#400,#213,.T.);
#494=EDGE_CURVE('',#401,#397,#214,.T.);
#495=EDGE_CURVE('',#378,#401,#57,.T.);
#496=EDGE_CURVE('',#371,#397,#27,.T.);
#497=EDGE_CURVE('',#402,#401,#215,.T.);
#498=EDGE_CURVE('',#377,#402,#58,.T.);
#499=EDGE_CURVE('',#377,#378,#216,.T.);
#500=EDGE_CURVE('',#398,#402,#217,.T.);
#501=EDGE_CURVE('',#402,#403,#218,.T.);
#502=EDGE_CURVE('',#403,#404,#28,.T.);
#503=EDGE_CURVE('',#404,#405,#219,.T.);
#504=EDGE_CURVE('',#405,#406,#29,.T.);
#505=EDGE_CURVE('',#406,#407,#220,.T.);
#506=EDGE_CURVE('',#407,#408,#30,.T.);
#507=EDGE_CURVE('',#408,#409,#221,.T.);
#508=EDGE_CURVE('',#409,#410,#31,.T.);
#509=EDGE_CURVE('',#410,#411,#222,.T.);
#510=EDGE_CURVE('',#411,#412,#32,.T.);
#511=EDGE_CURVE('',#412,#413,#223,.T.);
#512=EDGE_CURVE('',#413,#414,#33,.T.);
#513=EDGE_CURVE('',#414,#415,#224,.T.);
#514=EDGE_CURVE('',#415,#416,#34,.T.);
#515=EDGE_CURVE('',#416,#417,#225,.T.);
#516=EDGE_CURVE('',#417,#418,#35,.T.);
#517=EDGE_CURVE('',#418,#419,#226,.T.);
#518=EDGE_CURVE('',#419,#399,#36,.T.);
#519=EDGE_CURVE('',#420,#400,#37,.T.);
#520=EDGE_CURVE('',#421,#420,#227,.T.);
#521=EDGE_CURVE('',#421,#422,#38,.T.);
#522=EDGE_CURVE('',#422,#423,#228,.T.);
#523=EDGE_CURVE('',#423,#424,#39,.T.);
#524=EDGE_CURVE('',#424,#425,#229,.T.);
#525=EDGE_CURVE('',#425,#426,#40,.T.);
#526=EDGE_CURVE('',#427,#426,#230,.T.);
#527=EDGE_CURVE('',#428,#427,#41,.T.);
#528=EDGE_CURVE('',#428,#429,#231,.T.);
#529=EDGE_CURVE('',#429,#430,#42,.T.);
#530=EDGE_CURVE('',#430,#431,#232,.T.);
#531=EDGE_CURVE('',#431,#432,#43,.T.);
#532=EDGE_CURVE('',#433,#432,#233,.T.);
#533=EDGE_CURVE('',#434,#433,#44,.T.);
#534=EDGE_CURVE('',#434,#435,#234,.T.);
#535=EDGE_CURVE('',#435,#436,#45,.T.);
#536=EDGE_CURVE('',#401,#436,#235,.T.);
#537=EDGE_CURVE('',#398,#370,#46,.T.);
#538=EDGE_CURVE('',#437,#438,#47,.T.);
#539=EDGE_CURVE('',#437,#391,#236,.T.);
#540=EDGE_CURVE('',#438,#390,#237,.T.);
#541=EDGE_CURVE('',#438,#439,#238,.T.);
#542=EDGE_CURVE('',#439,#389,#239,.T.);
#543=EDGE_CURVE('',#439,#440,#48,.T.);
#544=EDGE_CURVE('',#440,#388,#240,.T.);
#545=EDGE_CURVE('',#440,#441,#241,.T.);
#546=EDGE_CURVE('',#441,#387,#242,.T.);
#547=EDGE_CURVE('',#441,#442,#49,.T.);
#548=EDGE_CURVE('',#442,#386,#243,.T.);
#549=EDGE_CURVE('',#442,#443,#244,.T.);
#550=EDGE_CURVE('',#443,#385,#245,.T.);
#551=EDGE_CURVE('',#443,#444,#50,.T.);
#552=EDGE_CURVE('',#444,#384,#246,.T.);
#553=EDGE_CURVE('',#444,#445,#247,.T.);
#554=EDGE_CURVE('',#445,#383,#248,.T.);
#555=EDGE_CURVE('',#446,#445,#51,.T.);
#556=EDGE_CURVE('',#446,#382,#249,.T.);
#557=EDGE_CURVE('',#446,#374,#250,.T.);
#558=EDGE_CURVE('',#420,#419,#251,.T.);
#559=EDGE_CURVE('',#421,#418,#252,.T.);
#560=EDGE_CURVE('',#422,#417,#253,.T.);
#561=EDGE_CURVE('',#423,#416,#254,.T.);
#562=EDGE_CURVE('',#424,#415,#255,.T.);
#563=EDGE_CURVE('',#425,#414,#256,.T.);
#564=EDGE_CURVE('',#426,#413,#257,.T.);
#565=EDGE_CURVE('',#427,#412,#258,.T.);
#566=EDGE_CURVE('',#428,#411,#259,.T.);
#567=EDGE_CURVE('',#429,#410,#260,.T.);
#568=EDGE_CURVE('',#430,#409,#261,.T.);
#569=EDGE_CURVE('',#431,#408,#262,.T.);
#570=EDGE_CURVE('',#432,#407,#263,.T.);
#571=EDGE_CURVE('',#433,#406,#264,.T.);
#572=EDGE_CURVE('',#434,#405,#265,.T.);
#573=EDGE_CURVE('',#435,#404,#266,.T.);
#574=EDGE_CURVE('',#436,#403,#267,.T.);
#575=EDGE_CURVE('',#447,#380,#52,.T.);
#576=EDGE_CURVE('',#447,#396,#268,.T.);
#577=EDGE_CURVE('',#447,#448,#269,.T.);
#578=EDGE_CURVE('',#448,#395,#270,.T.);
#579=EDGE_CURVE('',#448,#449,#53,.T.);
#580=EDGE_CURVE('',#449,#394,#271,.T.);
#581=EDGE_CURVE('',#450,#449,#272,.T.);
#582=EDGE_CURVE('',#450,#393,#273,.T.);
#583=EDGE_CURVE('',#451,#450,#54,.T.);
#584=EDGE_CURVE('',#451,#392,#274,.T.);
#585=EDGE_CURVE('',#451,#437,#275,.T.);
#586=ORIENTED_EDGE('',*,*,#452,.T.);
#587=ORIENTED_EDGE('',*,*,#453,.F.);
#588=ORIENTED_EDGE('',*,*,#454,.F.);
#589=ORIENTED_EDGE('',*,*,#455,.F.);
#590=ORIENTED_EDGE('',*,*,#456,.F.);
#591=ORIENTED_EDGE('',*,*,#457,.F.);
#592=ORIENTED_EDGE('',*,*,#458,.T.);
#593=ORIENTED_EDGE('',*,*,#459,.T.);
#594=ORIENTED_EDGE('',*,*,#460,.F.);
#595=ORIENTED_EDGE('',*,*,#461,.F.);
#596=ORIENTED_EDGE('',*,*,#462,.T.);
#597=ORIENTED_EDGE('',*,*,#457,.T.);
#598=ORIENTED_EDGE('',*,*,#463,.T.);
#599=ORIENTED_EDGE('',*,*,#464,.T.);
#600=ORIENTED_EDGE('',*,*,#465,.F.);
#601=ORIENTED_EDGE('',*,*,#453,.T.);
#602=ORIENTED_EDGE('',*,*,#456,.T.);
#603=ORIENTED_EDGE('',*,*,#455,.T.);
#604=ORIENTED_EDGE('',*,*,#466,.F.);
#605=ORIENTED_EDGE('',*,*,#467,.F.);
#606=ORIENTED_EDGE('',*,*,#462,.F.);
#607=ORIENTED_EDGE('',*,*,#468,.F.);
#608=ORIENTED_EDGE('',*,*,#469,.F.);
#609=ORIENTED_EDGE('',*,*,#470,.T.);
#610=ORIENTED_EDGE('',*,*,#471,.F.);
#611=ORIENTED_EDGE('',*,*,#472,.T.);
#612=ORIENTED_EDGE('',*,*,#473,.T.);
#613=ORIENTED_EDGE('',*,*,#474,.T.);
#614=ORIENTED_EDGE('',*,*,#475,.T.);
#615=ORIENTED_EDGE('',*,*,#476,.T.);
#616=ORIENTED_EDGE('',*,*,#477,.T.);
#617=ORIENTED_EDGE('',*,*,#478,.T.);
#618=ORIENTED_EDGE('',*,*,#479,.T.);
#619=ORIENTED_EDGE('',*,*,#480,.T.);
#620=ORIENTED_EDGE('',*,*,#481,.T.);
#621=ORIENTED_EDGE('',*,*,#482,.T.);
#622=ORIENTED_EDGE('',*,*,#483,.T.);
#623=ORIENTED_EDGE('',*,*,#484,.T.);
#624=ORIENTED_EDGE('',*,*,#485,.T.);
#625=ORIENTED_EDGE('',*,*,#486,.T.);
#626=ORIENTED_EDGE('',*,*,#487,.T.);
#627=ORIENTED_EDGE('',*,*,#468,.T.);
#628=ORIENTED_EDGE('',*,*,#452,.F.);
#629=ORIENTED_EDGE('',*,*,#459,.F.);
#630=ORIENTED_EDGE('',*,*,#488,.T.);
#631=ORIENTED_EDGE('',*,*,#489,.F.);
#632=ORIENTED_EDGE('',*,*,#490,.T.);
#633=ORIENTED_EDGE('',*,*,#491,.F.);
#634=ORIENTED_EDGE('',*,*,#492,.F.);
#635=ORIENTED_EDGE('',*,*,#493,.F.);
#636=ORIENTED_EDGE('',*,*,#494,.F.);
#637=ORIENTED_EDGE('',*,*,#495,.F.);
#638=ORIENTED_EDGE('',*,*,#466,.T.);
#639=ORIENTED_EDGE('',*,*,#496,.T.);
#640=ORIENTED_EDGE('',*,*,#497,.F.);
#641=ORIENTED_EDGE('',*,*,#498,.F.);
#642=ORIENTED_EDGE('',*,*,#499,.T.);
#643=ORIENTED_EDGE('',*,*,#495,.T.);
#644=ORIENTED_EDGE('',*,*,#500,.T.);
#645=ORIENTED_EDGE('',*,*,#501,.T.);
#646=ORIENTED_EDGE('',*,*,#502,.T.);
#647=ORIENTED_EDGE('',*,*,#503,.T.);
#648=ORIENTED_EDGE('',*,*,#504,.T.);
#649=ORIENTED_EDGE('',*,*,#505,.T.);
#650=ORIENTED_EDGE('',*,*,#506,.T.);
#651=ORIENTED_EDGE('',*,*,#507,.T.);
#652=ORIENTED_EDGE('',*,*,#508,.T.);
#653=ORIENTED_EDGE('',*,*,#509,.T.);
#654=ORIENTED_EDGE('',*,*,#510,.T.);
#655=ORIENTED_EDGE('',*,*,#511,.T.);
#656=ORIENTED_EDGE('',*,*,#512,.T.);
#657=ORIENTED_EDGE('',*,*,#513,.T.);
#658=ORIENTED_EDGE('',*,*,#514,.T.);
#659=ORIENTED_EDGE('',*,*,#515,.T.);
#660=ORIENTED_EDGE('',*,*,#516,.T.);
#661=ORIENTED_EDGE('',*,*,#517,.T.);
#662=ORIENTED_EDGE('',*,*,#518,.T.);
#663=ORIENTED_EDGE('',*,*,#491,.T.);
#664=ORIENTED_EDGE('',*,*,#494,.T.);
#665=ORIENTED_EDGE('',*,*,#493,.T.);
#666=ORIENTED_EDGE('',*,*,#519,.F.);
#667=ORIENTED_EDGE('',*,*,#520,.F.);
#668=ORIENTED_EDGE('',*,*,#521,.T.);
#669=ORIENTED_EDGE('',*,*,#522,.T.);
#670=ORIENTED_EDGE('',*,*,#523,.T.);
#671=ORIENTED_EDGE('',*,*,#524,.T.);
#672=ORIENTED_EDGE('',*,*,#525,.T.);
#673=ORIENTED_EDGE('',*,*,#526,.F.);
#674=ORIENTED_EDGE('',*,*,#527,.F.);
#675=ORIENTED_EDGE('',*,*,#528,.T.);
#676=ORIENTED_EDGE('',*,*,#529,.T.);
#677=ORIENTED_EDGE('',*,*,#530,.T.);
#678=ORIENTED_EDGE('',*,*,#531,.T.);
#679=ORIENTED_EDGE('',*,*,#532,.F.);
#680=ORIENTED_EDGE('',*,*,#533,.F.);
#681=ORIENTED_EDGE('',*,*,#534,.T.);
#682=ORIENTED_EDGE('',*,*,#535,.T.);
#683=ORIENTED_EDGE('',*,*,#536,.F.);
#684=ORIENTED_EDGE('',*,*,#460,.T.);
#685=ORIENTED_EDGE('',*,*,#467,.T.);
#686=ORIENTED_EDGE('',*,*,#499,.F.);
#687=ORIENTED_EDGE('',*,*,#464,.F.);
#688=ORIENTED_EDGE('',*,*,#463,.F.);
#689=ORIENTED_EDGE('',*,*,#489,.T.);
#690=ORIENTED_EDGE('',*,*,#471,.T.);
#691=ORIENTED_EDGE('',*,*,#461,.T.);
#692=ORIENTED_EDGE('',*,*,#490,.F.);
#693=ORIENTED_EDGE('',*,*,#496,.F.);
#694=ORIENTED_EDGE('',*,*,#454,.T.);
#695=ORIENTED_EDGE('',*,*,#537,.F.);
#696=ORIENTED_EDGE('',*,*,#538,.F.);
#697=ORIENTED_EDGE('',*,*,#539,.T.);
#698=ORIENTED_EDGE('',*,*,#481,.F.);
#699=ORIENTED_EDGE('',*,*,#540,.F.);
#700=ORIENTED_EDGE('',*,*,#541,.F.);
#701=ORIENTED_EDGE('',*,*,#540,.T.);
#702=ORIENTED_EDGE('',*,*,#480,.F.);
#703=ORIENTED_EDGE('',*,*,#542,.F.);
#704=ORIENTED_EDGE('',*,*,#543,.F.);
#705=ORIENTED_EDGE('',*,*,#542,.T.);
#706=ORIENTED_EDGE('',*,*,#479,.F.);
#707=ORIENTED_EDGE('',*,*,#544,.F.);
#708=ORIENTED_EDGE('',*,*,#545,.F.);
#709=ORIENTED_EDGE('',*,*,#544,.T.);
#710=ORIENTED_EDGE('',*,*,#478,.F.);
#711=ORIENTED_EDGE('',*,*,#546,.F.);
#712=ORIENTED_EDGE('',*,*,#547,.F.);
#713=ORIENTED_EDGE('',*,*,#546,.T.);
#714=ORIENTED_EDGE('',*,*,#477,.F.);
#715=ORIENTED_EDGE('',*,*,#548,.F.);
#716=ORIENTED_EDGE('',*,*,#549,.F.);
#717=ORIENTED_EDGE('',*,*,#548,.T.);
#718=ORIENTED_EDGE('',*,*,#476,.F.);
#719=ORIENTED_EDGE('',*,*,#550,.F.);
#720=ORIENTED_EDGE('',*,*,#551,.F.);
#721=ORIENTED_EDGE('',*,*,#550,.T.);
#722=ORIENTED_EDGE('',*,*,#475,.F.);
#723=ORIENTED_EDGE('',*,*,#552,.F.);
#724=ORIENTED_EDGE('',*,*,#553,.F.);
#725=ORIENTED_EDGE('',*,*,#552,.T.);
#726=ORIENTED_EDGE('',*,*,#474,.F.);
#727=ORIENTED_EDGE('',*,*,#554,.F.);
#728=ORIENTED_EDGE('',*,*,#555,.T.);
#729=ORIENTED_EDGE('',*,*,#554,.T.);
#730=ORIENTED_EDGE('',*,*,#473,.F.);
#731=ORIENTED_EDGE('',*,*,#556,.F.);
#732=ORIENTED_EDGE('',*,*,#488,.F.);
#733=ORIENTED_EDGE('',*,*,#557,.F.);
#734=ORIENTED_EDGE('',*,*,#556,.T.);
#735=ORIENTED_EDGE('',*,*,#472,.F.);
#736=ORIENTED_EDGE('',*,*,#519,.T.);
#737=ORIENTED_EDGE('',*,*,#492,.T.);
#738=ORIENTED_EDGE('',*,*,#518,.F.);
#739=ORIENTED_EDGE('',*,*,#558,.F.);
#740=ORIENTED_EDGE('',*,*,#520,.T.);
#741=ORIENTED_EDGE('',*,*,#558,.T.);
#742=ORIENTED_EDGE('',*,*,#517,.F.);
#743=ORIENTED_EDGE('',*,*,#559,.F.);
#744=ORIENTED_EDGE('',*,*,#521,.F.);
#745=ORIENTED_EDGE('',*,*,#559,.T.);
#746=ORIENTED_EDGE('',*,*,#516,.F.);
#747=ORIENTED_EDGE('',*,*,#560,.F.);
#748=ORIENTED_EDGE('',*,*,#522,.F.);
#749=ORIENTED_EDGE('',*,*,#560,.T.);
#750=ORIENTED_EDGE('',*,*,#515,.F.);
#751=ORIENTED_EDGE('',*,*,#561,.F.);
#752=ORIENTED_EDGE('',*,*,#523,.F.);
#753=ORIENTED_EDGE('',*,*,#561,.T.);
#754=ORIENTED_EDGE('',*,*,#514,.F.);
#755=ORIENTED_EDGE('',*,*,#562,.F.);
#756=ORIENTED_EDGE('',*,*,#524,.F.);
#757=ORIENTED_EDGE('',*,*,#562,.T.);
#758=ORIENTED_EDGE('',*,*,#513,.F.);
#759=ORIENTED_EDGE('',*,*,#563,.F.);
#760=ORIENTED_EDGE('',*,*,#525,.F.);
#761=ORIENTED_EDGE('',*,*,#563,.T.);
#762=ORIENTED_EDGE('',*,*,#512,.F.);
#763=ORIENTED_EDGE('',*,*,#564,.F.);
#764=ORIENTED_EDGE('',*,*,#526,.T.);
#765=ORIENTED_EDGE('',*,*,#564,.T.);
#766=ORIENTED_EDGE('',*,*,#511,.F.);
#767=ORIENTED_EDGE('',*,*,#565,.F.);
#768=ORIENTED_EDGE('',*,*,#527,.T.);
#769=ORIENTED_EDGE('',*,*,#565,.T.);
#770=ORIENTED_EDGE('',*,*,#510,.F.);
#771=ORIENTED_EDGE('',*,*,#566,.F.);
#772=ORIENTED_EDGE('',*,*,#528,.F.);
#773=ORIENTED_EDGE('',*,*,#566,.T.);
#774=ORIENTED_EDGE('',*,*,#509,.F.);
#775=ORIENTED_EDGE('',*,*,#567,.F.);
#776=ORIENTED_EDGE('',*,*,#529,.F.);
#777=ORIENTED_EDGE('',*,*,#567,.T.);
#778=ORIENTED_EDGE('',*,*,#508,.F.);
#779=ORIENTED_EDGE('',*,*,#568,.F.);
#780=ORIENTED_EDGE('',*,*,#530,.F.);
#781=ORIENTED_EDGE('',*,*,#568,.T.);
#782=ORIENTED_EDGE('',*,*,#507,.F.);
#783=ORIENTED_EDGE('',*,*,#569,.F.);
#784=ORIENTED_EDGE('',*,*,#531,.F.);
#785=ORIENTED_EDGE('',*,*,#569,.T.);
#786=ORIENTED_EDGE('',*,*,#506,.F.);
#787=ORIENTED_EDGE('',*,*,#570,.F.);
#788=ORIENTED_EDGE('',*,*,#532,.T.);
#789=ORIENTED_EDGE('',*,*,#570,.T.);
#790=ORIENTED_EDGE('',*,*,#505,.F.);
#791=ORIENTED_EDGE('',*,*,#571,.F.);
#792=ORIENTED_EDGE('',*,*,#533,.T.);
#793=ORIENTED_EDGE('',*,*,#571,.T.);
#794=ORIENTED_EDGE('',*,*,#504,.F.);
#795=ORIENTED_EDGE('',*,*,#572,.F.);
#796=ORIENTED_EDGE('',*,*,#534,.F.);
#797=ORIENTED_EDGE('',*,*,#572,.T.);
#798=ORIENTED_EDGE('',*,*,#503,.F.);
#799=ORIENTED_EDGE('',*,*,#573,.F.);
#800=ORIENTED_EDGE('',*,*,#535,.F.);
#801=ORIENTED_EDGE('',*,*,#573,.T.);
#802=ORIENTED_EDGE('',*,*,#502,.F.);
#803=ORIENTED_EDGE('',*,*,#574,.F.);
#804=ORIENTED_EDGE('',*,*,#497,.T.);
#805=ORIENTED_EDGE('',*,*,#536,.T.);
#806=ORIENTED_EDGE('',*,*,#574,.T.);
#807=ORIENTED_EDGE('',*,*,#501,.F.);
#808=ORIENTED_EDGE('',*,*,#575,.T.);
#809=ORIENTED_EDGE('',*,*,#469,.T.);
#810=ORIENTED_EDGE('',*,*,#487,.F.);
#811=ORIENTED_EDGE('',*,*,#576,.F.);
#812=ORIENTED_EDGE('',*,*,#577,.F.);
#813=ORIENTED_EDGE('',*,*,#576,.T.);
#814=ORIENTED_EDGE('',*,*,#486,.F.);
#815=ORIENTED_EDGE('',*,*,#578,.F.);
#816=ORIENTED_EDGE('',*,*,#579,.F.);
#817=ORIENTED_EDGE('',*,*,#578,.T.);
#818=ORIENTED_EDGE('',*,*,#485,.F.);
#819=ORIENTED_EDGE('',*,*,#580,.F.);
#820=ORIENTED_EDGE('',*,*,#581,.T.);
#821=ORIENTED_EDGE('',*,*,#580,.T.);
#822=ORIENTED_EDGE('',*,*,#484,.F.);
#823=ORIENTED_EDGE('',*,*,#582,.F.);
#824=ORIENTED_EDGE('',*,*,#583,.T.);
#825=ORIENTED_EDGE('',*,*,#582,.T.);
#826=ORIENTED_EDGE('',*,*,#483,.F.);
#827=ORIENTED_EDGE('',*,*,#584,.F.);
#828=ORIENTED_EDGE('',*,*,#585,.F.);
#829=ORIENTED_EDGE('',*,*,#584,.T.);
#830=ORIENTED_EDGE('',*,*,#482,.F.);
#831=ORIENTED_EDGE('',*,*,#539,.F.);
#832=ORIENTED_EDGE('',*,*,#458,.F.);
#833=ORIENTED_EDGE('',*,*,#470,.F.);
#834=ORIENTED_EDGE('',*,*,#575,.F.);
#835=ORIENTED_EDGE('',*,*,#577,.T.);
#836=ORIENTED_EDGE('',*,*,#579,.T.);
#837=ORIENTED_EDGE('',*,*,#581,.F.);
#838=ORIENTED_EDGE('',*,*,#583,.F.);
#839=ORIENTED_EDGE('',*,*,#585,.T.);
#840=ORIENTED_EDGE('',*,*,#538,.T.);
#841=ORIENTED_EDGE('',*,*,#541,.T.);
#842=ORIENTED_EDGE('',*,*,#543,.T.);
#843=ORIENTED_EDGE('',*,*,#545,.T.);
#844=ORIENTED_EDGE('',*,*,#547,.T.);
#845=ORIENTED_EDGE('',*,*,#549,.T.);
#846=ORIENTED_EDGE('',*,*,#551,.T.);
#847=ORIENTED_EDGE('',*,*,#553,.T.);
#848=ORIENTED_EDGE('',*,*,#555,.F.);
#849=ORIENTED_EDGE('',*,*,#557,.T.);
#850=ORIENTED_EDGE('',*,*,#500,.F.);
#851=ORIENTED_EDGE('',*,*,#537,.T.);
#852=ORIENTED_EDGE('',*,*,#465,.T.);
#853=ORIENTED_EDGE('',*,*,#498,.T.);
#854=PLANE('',#949);
#855=PLANE('',#952);
#856=PLANE('',#953);
#857=PLANE('',#954);
#858=PLANE('',#955);
#859=PLANE('',#964);
#860=PLANE('',#966);
#861=PLANE('',#969);
#862=PLANE('',#979);
#863=PLANE('',#989);
#864=PLANE('',#991);
#865=PLANE('',#995);
#866=PLANE('',#998);
#867=PLANE('',#1001);
#868=PLANE('',#1004);
#869=PLANE('',#1007);
#870=PLANE('',#1009);
#871=PLANE('',#1011);
#872=PLANE('',#1013);
#873=PLANE('',#1015);
#874=PLANE('',#1017);
#875=PLANE('',#1019);
#876=PLANE('',#1021);
#877=PLANE('',#1023);
#878=PLANE('',#1025);
#879=PLANE('',#1028);
#880=PLANE('',#1031);
#881=PLANE('',#1034);
#882=PLANE('',#1035);
#883=ADVANCED_FACE('',(#80),#854,.T.);
#884=ADVANCED_FACE('',(#81),#59,.F.);
#885=ADVANCED_FACE('',(#82),#15,.T.);
#886=ADVANCED_FACE('',(#83),#855,.F.);
#887=ADVANCED_FACE('',(#84),#856,.T.);
#888=ADVANCED_FACE('',(#85),#857,.T.);
#889=ADVANCED_FACE('',(#86),#858,.F.);
#890=ADVANCED_FACE('',(#87),#859,.T.);
#891=ADVANCED_FACE('',(#88),#860,.T.);
#892=ADVANCED_FACE('',(#89),#60,.T.);
#893=ADVANCED_FACE('',(#90),#16,.T.);
#894=ADVANCED_FACE('',(#91),#861,.F.);
#895=ADVANCED_FACE('',(#92),#862,.T.);
#896=ADVANCED_FACE('',(#93),#863,.T.);
#897=ADVANCED_FACE('',(#94),#61,.T.);
#898=ADVANCED_FACE('',(#95),#864,.T.);
#899=ADVANCED_FACE('',(#96),#62,.T.);
#900=ADVANCED_FACE('',(#97),#865,.T.);
#901=ADVANCED_FACE('',(#98),#63,.T.);
#902=ADVANCED_FACE('',(#99),#866,.T.);
#903=ADVANCED_FACE('',(#100),#64,.T.);
#904=ADVANCED_FACE('',(#101),#867,.T.);
#905=ADVANCED_FACE('',(#102),#65,.T.);
#906=ADVANCED_FACE('',(#103),#868,.T.);
#907=ADVANCED_FACE('',(#104),#66,.F.);
#908=ADVANCED_FACE('',(#105),#869,.T.);
#909=ADVANCED_FACE('',(#106),#67,.F.);
#910=ADVANCED_FACE('',(#107),#870,.T.);
#911=ADVANCED_FACE('',(#108),#68,.T.);
#912=ADVANCED_FACE('',(#109),#871,.T.);
#913=ADVANCED_FACE('',(#110),#69,.T.);
#914=ADVANCED_FACE('',(#111),#872,.T.);
#915=ADVANCED_FACE('',(#112),#70,.T.);
#916=ADVANCED_FACE('',(#113),#873,.T.);
#917=ADVANCED_FACE('',(#114),#71,.F.);
#918=ADVANCED_FACE('',(#115),#874,.T.);
#919=ADVANCED_FACE('',(#116),#72,.T.);
#920=ADVANCED_FACE('',(#117),#875,.T.);
#921=ADVANCED_FACE('',(#118),#73,.T.);
#922=ADVANCED_FACE('',(#119),#876,.T.);
#923=ADVANCED_FACE('',(#120),#74,.F.);
#924=ADVANCED_FACE('',(#121),#877,.T.);
#925=ADVANCED_FACE('',(#122),#75,.T.);
#926=ADVANCED_FACE('',(#123),#878,.T.);
#927=ADVANCED_FACE('',(#124),#76,.F.);
#928=ADVANCED_FACE('',(#125),#879,.T.);
#929=ADVANCED_FACE('',(#126),#77,.T.);
#930=ADVANCED_FACE('',(#127),#880,.T.);
#931=ADVANCED_FACE('',(#128),#78,.F.);
#932=ADVANCED_FACE('',(#129),#881,.T.);
#933=ADVANCED_FACE('',(#130),#882,.T.);
#934=ADVANCED_FACE('',(#131),#79,.F.);
#935=CLOSED_SHELL('',(#883,#884,#885,#886,#887,#888,#889,#890,#891,#892,
#893,#894,#895,#896,#897,#898,#899,#900,#901,#902,#903,#904,#905,#906,#907,
#908,#909,#910,#911,#912,#913,#914,#915,#916,#917,#918,#919,#920,#921,#922,
#923,#924,#925,#926,#927,#928,#929,#930,#931,#932,#933,#934));
#936=DERIVED_UNIT_ELEMENT(#938,1.);
#937=DERIVED_UNIT_ELEMENT(#1625,-3.);
#938=(
MASS_UNIT()
NAMED_UNIT(*)
SI_UNIT(.KILO.,.GRAM.)
);
#939=DERIVED_UNIT((#936,#937));
#940=MEASURE_REPRESENTATION_ITEM('density measure',
POSITIVE_RATIO_MEASURE(7850.),#939);
#941=PROPERTY_DEFINITION_REPRESENTATION(#946,#943);
#942=PROPERTY_DEFINITION_REPRESENTATION(#947,#944);
#943=REPRESENTATION('material name',(#945),#1622);
#944=REPRESENTATION('density',(#940),#1622);
#945=DESCRIPTIVE_REPRESENTATION_ITEM('Steel','Steel');
#946=PROPERTY_DEFINITION('material property','material name',#1632);
#947=PROPERTY_DEFINITION('material property','density of part',#1632);
#948=AXIS2_PLACEMENT_3D('placement',#1307,#1037,#1038);
#949=AXIS2_PLACEMENT_3D('',#1308,#1039,#1040);
#950=AXIS2_PLACEMENT_3D('',#1317,#1045,#1046);
#951=AXIS2_PLACEMENT_3D('',#1327,#1049,#1050);
#952=AXIS2_PLACEMENT_3D('',#1352,#1053,#1054);
#953=AXIS2_PLACEMENT_3D('',#1357,#1058,#1059);
#954=AXIS2_PLACEMENT_3D('',#1361,#1062,#1063);
#955=AXIS2_PLACEMENT_3D('',#1367,#1067,#1068);
#956=AXIS2_PLACEMENT_3D('',#1373,#1071,#1072);
#957=AXIS2_PLACEMENT_3D('',#1377,#1074,#1075);
#958=AXIS2_PLACEMENT_3D('',#1381,#1077,#1078);
#959=AXIS2_PLACEMENT_3D('',#1385,#1080,#1081);
#960=AXIS2_PLACEMENT_3D('',#1389,#1083,#1084);
#961=AXIS2_PLACEMENT_3D('',#1393,#1086,#1087);
#962=AXIS2_PLACEMENT_3D('',#1397,#1089,#1090);
#963=AXIS2_PLACEMENT_3D('',#1400,#1092,#1093);
#964=AXIS2_PLACEMENT_3D('',#1401,#1094,#1095);
#965=AXIS2_PLACEMENT_3D('',#1403,#1097,#1098);
#966=AXIS2_PLACEMENT_3D('',#1404,#1099,#1100);
#967=AXIS2_PLACEMENT_3D('',#1413,#1105,#1106);
#968=AXIS2_PLACEMENT_3D('',#1420,#1108,#1109);
#969=AXIS2_PLACEMENT_3D('',#1444,#1112,#1113);
#970=AXIS2_PLACEMENT_3D('',#1449,#1116,#1117);
#971=AXIS2_PLACEMENT_3D('',#1453,#1119,#1120);
#972=AXIS2_PLACEMENT_3D('',#1457,#1122,#1123);
#973=AXIS2_PLACEMENT_3D('',#1461,#1125,#1126);
#974=AXIS2_PLACEMENT_3D('',#1465,#1128,#1129);
#975=AXIS2_PLACEMENT_3D('',#1469,#1131,#1132);
#976=AXIS2_PLACEMENT_3D('',#1473,#1134,#1135);
#977=AXIS2_PLACEMENT_3D('',#1477,#1137,#1138);
#978=AXIS2_PLACEMENT_3D('',#1480,#1140,#1141);
#979=AXIS2_PLACEMENT_3D('',#1481,#1142,#1143);
#980=AXIS2_PLACEMENT_3D('',#1483,#1144,#1145);
#981=AXIS2_PLACEMENT_3D('',#1487,#1147,#1148);
#982=AXIS2_PLACEMENT_3D('',#1491,#1150,#1151);
#983=AXIS2_PLACEMENT_3D('',#1495,#1153,#1154);
#984=AXIS2_PLACEMENT_3D('',#1499,#1156,#1157);
#985=AXIS2_PLACEMENT_3D('',#1503,#1159,#1160);
#986=AXIS2_PLACEMENT_3D('',#1507,#1162,#1163);
#987=AXIS2_PLACEMENT_3D('',#1511,#1165,#1166);
#988=AXIS2_PLACEMENT_3D('',#1515,#1168,#1169);
#989=AXIS2_PLACEMENT_3D('',#1517,#1171,#1172);
#990=AXIS2_PLACEMENT_3D('',#1518,#1173,#1174);
#991=AXIS2_PLACEMENT_3D('',#1519,#1175,#1176);
#992=AXIS2_PLACEMENT_3D('',#1520,#1177,#1178);
#993=AXIS2_PLACEMENT_3D('',#1521,#1179,#1180);
#994=AXIS2_PLACEMENT_3D('',#1524,#1181,#1182);
#995=AXIS2_PLACEMENT_3D('',#1527,#1185,#1186);
#996=AXIS2_PLACEMENT_3D('',#1531,#1189,#1190);
#997=AXIS2_PLACEMENT_3D('',#1533,#1191,#1192);
#998=AXIS2_PLACEMENT_3D('',#1535,#1194,#1195);
#999=AXIS2_PLACEMENT_3D('',#1539,#1198,#1199);
#1000=AXIS2_PLACEMENT_3D('',#1541,#1200,#1201);
#1001=AXIS2_PLACEMENT_3D('',#1543,#1203,#1204);
#1002=AXIS2_PLACEMENT_3D('',#1547,#1207,#1208);
#1003=AXIS2_PLACEMENT_3D('',#1549,#1209,#1210);
#1004=AXIS2_PLACEMENT_3D('',#1551,#1212,#1213);
#1005=AXIS2_PLACEMENT_3D('',#1555,#1216,#1217);
#1006=AXIS2_PLACEMENT_3D('',#1557,#1218,#1219);
#1007=AXIS2_PLACEMENT_3D('',#1559,#1221,#1222);
#1008=AXIS2_PLACEMENT_3D('',#1561,#1224,#1225);
#1009=AXIS2_PLACEMENT_3D('',#1563,#1227,#1228);
#1010=AXIS2_PLACEMENT_3D('',#1565,#1230,#1231);
#1011=AXIS2_PLACEMENT_3D('',#1567,#1233,#1234);
#1012=AXIS2_PLACEMENT_3D('',#1569,#1236,#1237);
#1013=AXIS2_PLACEMENT_3D('',#1571,#1239,#1240);
#1014=AXIS2_PLACEMENT_3D('',#1573,#1242,#1243);
#1015=AXIS2_PLACEMENT_3D('',#1575,#1245,#1246);
#1016=AXIS2_PLACEMENT_3D('',#1577,#1248,#1249);
#1017=AXIS2_PLACEMENT_3D('',#1579,#1251,#1252);
#1018=AXIS2_PLACEMENT_3D('',#1581,#1254,#1255);
#1019=AXIS2_PLACEMENT_3D('',#1583,#1257,#1258);
#1020=AXIS2_PLACEMENT_3D('',#1585,#1260,#1261);
#1021=AXIS2_PLACEMENT_3D('',#1587,#1263,#1264);
#1022=AXIS2_PLACEMENT_3D('',#1589,#1266,#1267);
#1023=AXIS2_PLACEMENT_3D('',#1591,#1269,#1270);
#1024=AXIS2_PLACEMENT_3D('',#1593,#1272,#1273);
#1025=AXIS2_PLACEMENT_3D('',#1595,#1275,#1276);
#1026=AXIS2_PLACEMENT_3D('',#1596,#1277,#1278);
#1027=AXIS2_PLACEMENT_3D('',#1598,#1279,#1280);
#1028=AXIS2_PLACEMENT_3D('',#1600,#1282,#1283);
#1029=AXIS2_PLACEMENT_3D('',#1604,#1286,#1287);
#1030=AXIS2_PLACEMENT_3D('',#1606,#1288,#1289);
#1031=AXIS2_PLACEMENT_3D('',#1608,#1291,#1292);
#1032=AXIS2_PLACEMENT_3D('',#1612,#1295,#1296);
#1033=AXIS2_PLACEMENT_3D('',#1614,#1297,#1298);
#1034=AXIS2_PLACEMENT_3D('',#1616,#1300,#1301);
#1035=AXIS2_PLACEMENT_3D('',#1618,#1303,#1304);
#1036=AXIS2_PLACEMENT_3D('',#1619,#1305,#1306);
#1037=DIRECTION('axis',(0.,0.,1.));
#1038=DIRECTION('refdir',(1.,0.,0.));
#1039=DIRECTION('center_axis',(1.,4.10184757271524E-16,-2.04968453393103E-17));
#1040=DIRECTION('ref_axis',(3.90903076660344E-16,-0.965925826289068,-0.25881904510252));
#1041=DIRECTION('',(-1.25962059463318E-16,0.25881904510252,-0.965925826289068));
#1042=DIRECTION('',(3.90903076660344E-16,-0.965925826289068,-0.25881904510252));
#1043=DIRECTION('',(-1.25962059463318E-16,0.25881904510252,-0.965925826289068));
#1044=DIRECTION('',(-3.90903076660344E-16,0.965925826289068,0.25881904510252));
#1045=DIRECTION('center_axis',(1.,4.86680025472712E-16,0.));
#1046=DIRECTION('ref_axis',(0.,0.,-1.));
#1047=DIRECTION('',(1.,4.946251156569E-16,2.12888049864573E-18));
#1048=DIRECTION('',(1.,4.94905388091612E-16,0.));
#1049=DIRECTION('center_axis',(1.,4.86680025472712E-16,0.));
#1050=DIRECTION('ref_axis',(0.,0.,-1.));
#1051=DIRECTION('',(1.25962059463318E-16,-0.25881904510252,0.965925826289068));
#1052=DIRECTION('',(0.,0.,1.));
#1053=DIRECTION('center_axis',(1.25962059463318E-16,-0.258819045102521,
0.965925826289068));
#1054=DIRECTION('ref_axis',(1.,1.65832197295978E-17,-1.25962059463318E-16));
#1055=DIRECTION('',(-1.,-4.946251156569E-16,-2.12888049864573E-18));
#1056=DIRECTION('',(-6.12027889790049E-5,0.965925824479995,0.258819044617781));
#1057=DIRECTION('',(-1.,-3.23895867111435E-16,4.3617883773484E-17));
#1058=DIRECTION('center_axis',(1.25962059463318E-16,-0.258819045102521,
0.965925826289068));
#1059=DIRECTION('ref_axis',(1.,1.65832197295978E-17,-1.25962059463318E-16));
#1060=DIRECTION('',(1.,3.23895867111435E-16,-4.3617883773484E-17));
#1061=DIRECTION('',(-6.12027889790049E-5,0.965925824479995,0.258819044617781));
#1062=DIRECTION('center_axis',(-0.999999998127109,-6.12027889790215E-5,
0.));
#1063=DIRECTION('ref_axis',(-6.12027889790215E-5,0.999999998127109,0.));
#1064=DIRECTION('',(-6.12027889790215E-5,0.999999998127109,0.));
#1065=DIRECTION('',(0.,0.,-1.));
#1066=DIRECTION('',(-6.12027889790215E-5,0.999999998127109,0.));
#1067=DIRECTION('center_axis',(0.,0.,1.));
#1068=DIRECTION('ref_axis',(1.,0.,0.));
#1069=DIRECTION('',(-1.,-4.94905388091612E-16,0.));
#1070=DIRECTION('',(4.07486296389941E-16,-1.,0.));
#1071=DIRECTION('center_axis',(0.,0.,1.));
#1072=DIRECTION('ref_axis',(-1.,7.10542735760102E-16,0.));
#1073=DIRECTION('',(0.70710678118655,-0.707106781186545,0.));
#1074=DIRECTION('center_axis',(0.,0.,-1.));
#1075=DIRECTION('ref_axis',(1.,-3.19744231092048E-15,0.));
#1076=DIRECTION('',(0.,-1.,0.));
#1077=DIRECTION('center_axis',(0.,0.,-1.));
#1078=DIRECTION('ref_axis',(2.84217094304041E-15,-1.,0.));
#1079=DIRECTION('',(-1.,-9.31981552675892E-18,0.));
#1080=DIRECTION('center_axis',(0.,0.,-1.));
#1081=DIRECTION('ref_axis',(-1.,-3.33066907387547E-15,0.));
#1082=DIRECTION('',(8.59101150007562E-17,1.,0.));
#1083=DIRECTION('center_axis',(0.,0.,-1.));
#1084=DIRECTION('ref_axis',(0.,1.,0.));
#1085=DIRECTION('',(1.,0.,0.));
#1086=DIRECTION('center_axis',(0.,0.,1.));
#1087=DIRECTION('ref_axis',(0.,-1.,0.));
#1088=DIRECTION('',(0.707106781186547,0.707106781186548,0.));
#1089=DIRECTION('center_axis',(0.,0.,-1.));
#1090=DIRECTION('ref_axis',(0.,1.,0.));
#1091=DIRECTION('',(1.,-4.9171014128413E-17,0.));
#1092=DIRECTION('center_axis',(0.,0.,1.));
#1093=DIRECTION('ref_axis',(0.,-1.,0.));
#1094=DIRECTION('center_axis',(1.,4.86680025472712E-16,0.));
#1095=DIRECTION('ref_axis',(0.,0.,-1.));
#1096=DIRECTION('',(0.,0.,-1.));
#1097=DIRECTION('center_axis',(-1.,-4.86680025472712E-16,0.));
#1098=DIRECTION('ref_axis',(0.,0.25881904510252,-0.965925826289068));
#1099=DIRECTION('center_axis',(1.,4.13014104451238E-16,-4.19878708159476E-17));
#1100=DIRECTION('ref_axis',(4.13014104451238E-16,-1.,0.));
#1101=DIRECTION('',(-4.19878708159476E-17,0.,-1.));
#1102=DIRECTION('',(4.13014104451238E-16,-1.,0.));
#1103=DIRECTION('',(-4.19878708159476E-17,0.,-1.));
#1104=DIRECTION('',(-4.13014104451238E-16,1.,0.));
#1105=DIRECTION('center_axis',(-1.,-3.29979158285957E-16,4.19878708159476E-17));
#1106=DIRECTION('ref_axis',(1.25962059463318E-16,-0.258819045102521,0.965925826289068));
#1107=DIRECTION('',(1.,3.23681271834476E-16,-4.19878708159476E-17));
#1108=DIRECTION('center_axis',(-1.,-3.29979158285957E-16,4.19878708159476E-17));
#1109=DIRECTION('ref_axis',(1.25962059463318E-16,-0.258819045102521,0.965925826289068));
#1110=DIRECTION('',(4.19878708159476E-17,0.,1.));
#1111=DIRECTION('',(1.25962059463318E-16,-0.25881904510252,0.965925826289068));
#1112=DIRECTION('center_axis',(4.19878708159476E-17,0.,1.));
#1113=DIRECTION('ref_axis',(1.,5.52780806129631E-18,-4.19878708159476E-17));
#1114=DIRECTION('',(-1.,-3.23681271834476E-16,4.19878708159476E-17));
#1115=DIRECTION('',(-6.1202788979027E-5,0.999999998127109,0.));
#1116=DIRECTION('center_axis',(-4.19878708159476E-17,0.,-1.));
#1117=DIRECTION('ref_axis',(-0.173657688017635,0.984806076033434,5.55111512312581E-17));
#1118=DIRECTION('',(0.984806076033434,0.173657688017634,-4.16333634234434E-17));
#1119=DIRECTION('center_axis',(4.19878708159476E-17,0.,1.));
#1120=DIRECTION('ref_axis',(0.173657688017627,-0.984806076033435,-5.55111512312578E-17));
#1121=DIRECTION('',(-2.34916915229017E-6,0.999999999997241,2.77555756156289E-17));
#1122=DIRECTION('center_axis',(-4.19878708159476E-17,0.,-1.));
#1123=DIRECTION('ref_axis',(2.84161816223435E-14,1.,2.77555756156297E-17));
#1124=DIRECTION('',(1.,5.52780806129631E-18,-4.19878708159476E-17));
#1125=DIRECTION('center_axis',(-4.19878708159476E-17,0.,-1.));
#1126=DIRECTION('ref_axis',(1.,5.52780806129631E-18,-4.19878708159476E-17));
#1127=DIRECTION('',(5.52780806129645E-18,-1.,0.));
#1128=DIRECTION('center_axis',(4.19878708159476E-17,0.,1.));
#1129=DIRECTION('ref_axis',(-1.,-5.5278080612963E-18,4.19878708159476E-17));
#1130=DIRECTION('',(-5.52780806129645E-18,1.,0.));
#1131=DIRECTION('center_axis',(-4.19878708159476E-17,0.,-1.));
#1132=DIRECTION('ref_axis',(7.10487457679527E-14,1.,0.));
#1133=DIRECTION('',(1.,5.52780806129631E-18,-4.19878708159476E-17));
#1134=DIRECTION('center_axis',(-4.19878708159476E-17,0.,-1.));
#1135=DIRECTION('ref_axis',(1.,-1.42053269071409E-14,-4.19878708159471E-17));
#1136=DIRECTION('',(5.52780806129645E-18,-1.,0.));
#1137=DIRECTION('center_axis',(-4.19878708159476E-17,0.,-1.));
#1138=DIRECTION('ref_axis',(0.707106781186548,-0.707106781186548,-2.22044604925032E-17));
#1139=DIRECTION('',(-0.707106781186547,-0.707106781186548,2.77555756156289E-17));
#1140=DIRECTION('center_axis',(4.19878708159476E-17,0.,1.));
#1141=DIRECTION('ref_axis',(-0.707106781186548,0.707106781186547,2.22044604925029E-17));
#1142=DIRECTION('center_axis',(4.19878708159476E-17,0.,1.));
#1143=DIRECTION('ref_axis',(1.,5.52780806129631E-18,-4.19878708159476E-17));
#1144=DIRECTION('center_axis',(4.19878708159476E-17,0.,1.));
#1145=DIRECTION('ref_axis',(-0.707106781186548,0.707106781186547,2.22044604925029E-17));
#1146=DIRECTION('',(-0.707106781186547,-0.707106781186548,2.77555756156289E-17));
#1147=DIRECTION('center_axis',(4.19878708159476E-17,0.,1.));
#1148=DIRECTION('ref_axis',(0.707106781186548,-0.707106781186548,-2.22044604925032E-17));
#1149=DIRECTION('',(-5.52780806129645E-18,1.,0.));
#1150=DIRECTION('center_axis',(4.19878708159476E-17,0.,1.));
#1151=DIRECTION('ref_axis',(1.,-1.42053269071409E-14,-4.19878708159471E-17));
#1152=DIRECTION('',(-1.,-5.52780806129631E-18,4.19878708159476E-17));
#1153=DIRECTION('center_axis',(4.19878708159476E-17,0.,1.));
#1154=DIRECTION('ref_axis',(7.10487457679527E-14,1.,0.));
#1155=DIRECTION('',(-5.52780806129645E-18,1.,0.));
#1156=DIRECTION('center_axis',(4.19878708159476E-17,0.,1.));
#1157=DIRECTION('ref_axis',(-1.,-5.5278080612963E-18,4.19878708159476E-17));
#1158=DIRECTION('',(-5.52780806129645E-18,1.,0.));
#1159=DIRECTION('center_axis',(4.19878708159476E-17,0.,1.));
#1160=DIRECTION('ref_axis',(1.,5.52780806129631E-18,-4.19878708159476E-17));
#1161=DIRECTION('',(-1.,-5.52780806129631E-18,4.19878708159476E-17));
#1162=DIRECTION('center_axis',(4.19878708159476E-17,0.,1.));
#1163=DIRECTION('ref_axis',(2.84161816223435E-14,1.,2.77555756156297E-17));
#1164=DIRECTION('',(-2.34916915229017E-6,0.999999999997241,2.77555756156289E-17));
#1165=DIRECTION('center_axis',(4.19878708159476E-17,0.,1.));
#1166=DIRECTION('ref_axis',(0.173657688017627,-0.984806076033435,-5.55111512312578E-17));
#1167=DIRECTION('',(-0.984806076033434,-0.173657688017634,4.16333634234434E-17));
#1168=DIRECTION('center_axis',(4.19878708159476E-17,0.,1.));
#1169=DIRECTION('ref_axis',(-0.173657688017635,0.984806076033434,5.55111512312581E-17));
#1170=DIRECTION('',(-6.1202788979027E-5,0.999999998127109,0.));
#1171=DIRECTION('center_axis',(-0.999999998127109,-5.91173545157734E-5,
-1.58404474010354E-5));
#1172=DIRECTION('ref_axis',(-6.12027889790049E-5,0.965925824479995,0.258819044617781));
#1173=DIRECTION('center_axis',(1.,4.86680025472712E-16,0.));
#1174=DIRECTION('ref_axis',(0.,0.,-1.));
#1175=DIRECTION('center_axis',(1.,3.29979158285957E-16,-4.19878708159476E-17));
#1176=DIRECTION('ref_axis',(-1.25962059463318E-16,0.25881904510252,-0.965925826289068));
#1177=DIRECTION('center_axis',(1.,3.29979158285957E-16,-4.19878708159476E-17));
#1178=DIRECTION('ref_axis',(1.25962059463318E-16,-7.105427357601E-16,1.));
#1179=DIRECTION('center_axis',(0.,0.,-1.));
#1180=DIRECTION('ref_axis',(0.,1.,0.));
#1181=DIRECTION('center_axis',(0.,0.,1.));
#1182=DIRECTION('ref_axis',(0.,1.,0.));
#1183=DIRECTION('',(0.,0.,-1.));
#1184=DIRECTION('',(0.,0.,-1.));
#1185=DIRECTION('center_axis',(-1.,8.59101150007562E-17,0.));
#1186=DIRECTION('ref_axis',(8.59101150007562E-17,1.,0.));
#1187=DIRECTION('',(-8.59101150007562E-17,-1.,0.));
#1188=DIRECTION('',(0.,0.,-1.));
#1189=DIRECTION('center_axis',(0.,0.,-1.));
#1190=DIRECTION('ref_axis',(-1.,-3.33066907387547E-15,0.));
#1191=DIRECTION('center_axis',(0.,0.,1.));
#1192=DIRECTION('ref_axis',(-1.,-3.33066907387547E-15,0.));
#1193=DIRECTION('',(0.,0.,-1.));
#1194=DIRECTION('center_axis',(9.31981552675892E-18,-1.,0.));
#1195=DIRECTION('ref_axis',(-1.,-9.31981552675892E-18,0.));
#1196=DIRECTION('',(1.,9.31981552675892E-18,0.));
#1197=DIRECTION('',(0.,0.,-1.));
#1198=DIRECTION('center_axis',(0.,0.,-1.));
#1199=DIRECTION('ref_axis',(2.84217094304041E-15,-1.,0.));
#1200=DIRECTION('center_axis',(0.,0.,1.));
#1201=DIRECTION('ref_axis',(2.84217094304041E-15,-1.,0.));
#1202=DIRECTION('',(0.,0.,-1.));
#1203=DIRECTION('center_axis',(1.,0.,0.));
#1204=DIRECTION('ref_axis',(0.,-1.,0.));
#1205=DIRECTION('',(0.,1.,0.));
#1206=DIRECTION('',(0.,0.,-1.));
#1207=DIRECTION('center_axis',(0.,0.,-1.));
#1208=DIRECTION('ref_axis',(1.,-3.19744231092048E-15,0.));
#1209=DIRECTION('center_axis',(0.,0.,1.));
#1210=DIRECTION('ref_axis',(1.,-3.19744231092048E-15,0.));
#1211=DIRECTION('',(0.,0.,-1.));
#1212=DIRECTION('center_axis',(0.707106781186545,0.70710678118655,0.));
#1213=DIRECTION('ref_axis',(0.70710678118655,-0.707106781186545,0.));
#1214=DIRECTION('',(-0.70710678118655,0.707106781186545,0.));
#1215=DIRECTION('',(0.,0.,-1.));
#1216=DIRECTION('center_axis',(0.,0.,-1.));
#1217=DIRECTION('ref_axis',(-1.,7.10542735760102E-16,0.));
#1218=DIRECTION('center_axis',(0.,0.,1.));
#1219=DIRECTION('ref_axis',(-1.,7.10542735760102E-16,0.));
#1220=DIRECTION('',(0.,0.,-1.));
#1221=DIRECTION('center_axis',(1.,4.07486296389941E-16,0.));
#1222=DIRECTION('ref_axis',(4.07486296389941E-16,-1.,0.));
#1223=DIRECTION('',(-4.07486296389941E-16,1.,0.));
#1224=DIRECTION('center_axis',(-4.19878708159476E-17,0.,-1.));
#1225=DIRECTION('ref_axis',(-0.707106781186548,0.707106781186547,2.22044604925029E-17));
#1226=DIRECTION('',(-4.19878708159476E-17,0.,-1.));
#1227=DIRECTION('center_axis',(0.707106781186547,-0.707106781186548,-2.77555756156289E-17));
#1228=DIRECTION('ref_axis',(-0.707106781186547,-0.707106781186548,2.77555756156289E-17));
#1229=DIRECTION('',(-4.19878708159476E-17,0.,-1.));
#1230=DIRECTION('center_axis',(-4.19878708159476E-17,0.,-1.));
#1231=DIRECTION('ref_axis',(0.707106781186548,-0.707106781186548,-2.22044604925032E-17));
#1232=DIRECTION('',(-4.19878708159476E-17,0.,-1.));
#1233=DIRECTION('center_axis',(1.,5.52780806129631E-18,-4.19878708159476E-17));
#1234=DIRECTION('ref_axis',(5.52780806129645E-18,-1.,0.));
#1235=DIRECTION('',(-4.19878708159476E-17,0.,-1.));
#1236=DIRECTION('center_axis',(-4.19878708159476E-17,0.,-1.));
#1237=DIRECTION('ref_axis',(1.,-1.42053269071409E-14,-4.19878708159471E-17));
#1238=DIRECTION('',(-4.19878708159476E-17,0.,-1.));
#1239=DIRECTION('center_axis',(-5.52780806129645E-18,1.,0.));
#1240=DIRECTION('ref_axis',(1.,5.52780806129631E-18,-4.19878708159476E-17));
#1241=DIRECTION('',(-4.19878708159476E-17,0.,-1.));
#1242=DIRECTION('center_axis',(-4.19878708159476E-17,0.,-1.));
#1243=DIRECTION('ref_axis',(7.10487457679527E-14,1.,0.));
#1244=DIRECTION('',(-4.19878708159476E-17,0.,-1.));
#1245=DIRECTION('center_axis',(-1.,-5.52780806129631E-18,4.19878708159476E-17));
#1246=DIRECTION('ref_axis',(-5.52780806129645E-18,1.,0.));
#1247=DIRECTION('',(-4.19878708159476E-17,0.,-1.));
#1248=DIRECTION('center_axis',(-4.19878708159476E-17,0.,-1.));
#1249=DIRECTION('ref_axis',(-1.,-5.5278080612963E-18,4.19878708159476E-17));
#1250=DIRECTION('',(-4.19878708159476E-17,0.,-1.));
#1251=DIRECTION('center_axis',(1.,5.52780806129631E-18,-4.19878708159476E-17));
#1252=DIRECTION('ref_axis',(5.52780806129645E-18,-1.,0.));
#1253=DIRECTION('',(-4.19878708159476E-17,0.,-1.));
#1254=DIRECTION('center_axis',(-4.19878708159476E-17,0.,-1.));
#1255=DIRECTION('ref_axis',(1.,5.52780806129631E-18,-4.19878708159476E-17));
#1256=DIRECTION('',(-4.19878708159476E-17,0.,-1.));
#1257=DIRECTION('center_axis',(-5.52780806129645E-18,1.,0.));
#1258=DIRECTION('ref_axis',(1.,5.52780806129631E-18,-4.19878708159476E-17));
#1259=DIRECTION('',(-4.19878708159476E-17,0.,-1.));
#1260=DIRECTION('center_axis',(-4.19878708159476E-17,0.,-1.));
#1261=DIRECTION('ref_axis',(2.84161816223435E-14,1.,2.77555756156297E-17));
#1262=DIRECTION('',(-4.19878708159476E-17,0.,-1.));
#1263=DIRECTION('center_axis',(-0.999999999997241,-2.34916915229017E-6,
4.19877408327509E-17));
#1264=DIRECTION('ref_axis',(-2.34916915229017E-6,0.999999999997241,2.77555756156289E-17));
#1265=DIRECTION('',(-4.19878708159476E-17,0.,-1.));
#1266=DIRECTION('center_axis',(-4.19878708159476E-17,0.,-1.));
#1267=DIRECTION('ref_axis',(0.173657688017627,-0.984806076033435,-5.55111512312578E-17));
#1268=DIRECTION('',(-4.19878708159476E-17,0.,-1.));
#1269=DIRECTION('center_axis',(-0.173657688017634,0.984806076033434,2.77555756156289E-17));
#1270=DIRECTION('ref_axis',(0.984806076033434,0.173657688017634,-4.16333634234434E-17));
#1271=DIRECTION('',(-4.19878708159476E-17,0.,-1.));
#1272=DIRECTION('center_axis',(-4.19878708159476E-17,0.,-1.));
#1273=DIRECTION('ref_axis',(-0.173657688017635,0.984806076033434,5.55111512312581E-17));
#1274=DIRECTION('',(-4.19878708159476E-17,0.,-1.));
#1275=DIRECTION('center_axis',(-0.999999998127109,-6.1202788979027E-5,4.19857291295012E-17));
#1276=DIRECTION('ref_axis',(-6.1202788979027E-5,0.999999998127109,0.));
#1277=DIRECTION('center_axis',(0.,0.,-1.));
#1278=DIRECTION('ref_axis',(0.,-1.,0.));
#1279=DIRECTION('center_axis',(0.,0.,1.));
#1280=DIRECTION('ref_axis',(0.,-1.,0.));
#1281=DIRECTION('',(0.,0.,-1.));
#1282=DIRECTION('center_axis',(4.9171014128413E-17,1.,0.));
#1283=DIRECTION('ref_axis',(1.,-4.9171014128413E-17,0.));
#1284=DIRECTION('',(-1.,4.9171014128413E-17,0.));
#1285=DIRECTION('',(0.,0.,-1.));
#1286=DIRECTION('center_axis',(0.,0.,-1.));
#1287=DIRECTION('ref_axis',(0.,1.,0.));
#1288=DIRECTION('center_axis',(0.,0.,1.));
#1289=DIRECTION('ref_axis',(0.,1.,0.));
#1290=DIRECTION('',(0.,0.,-1.));
#1291=DIRECTION('center_axis',(-0.707106781186548,0.707106781186547,0.));
#1292=DIRECTION('ref_axis',(0.707106781186547,0.707106781186548,0.));
#1293=DIRECTION('',(0.707106781186547,0.707106781186548,0.));
#1294=DIRECTION('',(0.,0.,-1.));
#1295=DIRECTION('center_axis',(0.,0.,-1.));
#1296=DIRECTION('ref_axis',(0.,-1.,0.));
#1297=DIRECTION('center_axis',(0.,0.,1.));
#1298=DIRECTION('ref_axis',(0.,-1.,0.));
#1299=DIRECTION('',(0.,0.,-1.));
#1300=DIRECTION('center_axis',(0.,1.,0.));
#1301=DIRECTION('ref_axis',(1.,0.,0.));
#1302=DIRECTION('',(-1.,0.,0.));
#1303=DIRECTION('center_axis',(0.,0.,1.));
#1304=DIRECTION('ref_axis',(1.,0.,0.));
#1305=DIRECTION('center_axis',(-1.,-3.29979158285957E-16,4.19878708159476E-17));
#1306=DIRECTION('ref_axis',(1.25962059463318E-16,-0.25881904510252,0.965925826289068));
#1307=CARTESIAN_POINT('',(0.,0.,0.));
#1308=CARTESIAN_POINT('Origin',(946.,560.208481314708,73.7864219813055));
#1309=CARTESIAN_POINT('',(946.,291.192132947515,1.70370868554659));
#1310=CARTESIAN_POINT('',(946.,301.544894751616,-36.9333243660162));
#1311=CARTESIAN_POINT('',(946.,291.192132947515,1.70370868554659));
#1312=CARTESIAN_POINT('',(946.,476.959305728253,10.0688253959547));
#1313=CARTESIAN_POINT('',(946.,233.700060186665,-55.1122929983179));
#1314=CARTESIAN_POINT('',(946.,466.606543924152,48.7058584475175));
#1315=CARTESIAN_POINT('',(946.,466.606543924152,48.7058584475175));
#1316=CARTESIAN_POINT('',(946.,223.347298382564,-16.4752599467552));
#1317=CARTESIAN_POINT('Origin',(836.501556684505,278.251180692389,50.));
#1318=CARTESIAN_POINT('',(727.002571796588,291.192132947515,1.70370868554659));
#1319=CARTESIAN_POINT('',(251.25,291.192132947515,1.70370868554659));
#1320=CARTESIAN_POINT('',(727.003654941431,278.251180692389,0.));
#1321=CARTESIAN_POINT('Ctrl Pts',(727.003654941431,278.251180692389,-4.95321121856872E-29));
#1322=CARTESIAN_POINT('Ctrl Pts',(727.00329389315,282.614503822375,1.64947992237237E-15));
#1323=CARTESIAN_POINT('Ctrl Pts',(727.002932844869,286.977486447817,0.574397559569918));
#1324=CARTESIAN_POINT('Ctrl Pts',(727.002571796588,291.192132947515,1.70370868554659));
#1325=CARTESIAN_POINT('',(946.,278.251180692389,0.));
#1326=CARTESIAN_POINT('',(251.25,278.251180692389,0.));
#1327=CARTESIAN_POINT('Origin',(946.,278.251180692389,50.));
#1328=CARTESIAN_POINT('Ctrl Pts',(727.003654941431,278.251180692389,0.));
#1329=CARTESIAN_POINT('Ctrl Pts',(727.003654941431,278.251180692389,-13.3333333333333));
#1330=CARTESIAN_POINT('Ctrl Pts',(727.003654941431,278.251180692389,-26.6666666666667));
#1331=CARTESIAN_POINT('Ctrl Pts',(727.003654941431,278.251180692389,-40.));
#1332=CARTESIAN_POINT('Ctrl Pts',(727.00329389315,282.614503822375,-3.87540944223183E-15));
#1333=CARTESIAN_POINT('Ctrl Pts',(727.00329389315,283.778077172953,-13.3335186817705));
#1334=CARTESIAN_POINT('Ctrl Pts',(727.00329389315,284.941588975784,-26.6664813182295));
#1335=CARTESIAN_POINT('Ctrl Pts',(727.00329389315,286.105162326363,-40.));
#1336=CARTESIAN_POINT('Ctrl Pts',(727.002932844869,286.977486447817,0.574397559569913));
#1337=CARTESIAN_POINT('Ctrl Pts',(727.002932844869,289.304529470784,-12.6059474339636));
#1338=CARTESIAN_POINT('Ctrl Pts',(727.002932844869,291.631488029193,-25.7857393992407));
#1339=CARTESIAN_POINT('Ctrl Pts',(727.002932844869,293.95853105216,-38.9660843927742));
#1340=CARTESIAN_POINT('Ctrl Pts',(727.002571796588,291.192132947515,1.70370868554659));
#1341=CARTESIAN_POINT('Ctrl Pts',(727.002571796588,294.643053548882,-11.175302331641));
#1342=CARTESIAN_POINT('Ctrl Pts',(727.002571796588,298.093974150249,-24.0543133488286));
#1343=CARTESIAN_POINT('Ctrl Pts',(727.002571796588,301.544894751616,-36.9333243660161));
#1344=CARTESIAN_POINT('',(727.002571796588,301.544894751616,-36.9333243660162));
#1345=CARTESIAN_POINT('',(727.002571796588,291.192132947515,1.70370868554659));
#1346=CARTESIAN_POINT('',(727.003654941431,278.251180692389,-40.));
#1347=CARTESIAN_POINT('Ctrl Pts',(727.003654941431,278.251180692389,-40.));
#1348=CARTESIAN_POINT('Ctrl Pts',(727.00329389315,286.105162326363,-40.));
#1349=CARTESIAN_POINT('Ctrl Pts',(727.002932844869,293.95853105216,-38.9660843927741));
#1350=CARTESIAN_POINT('Ctrl Pts',(727.002571796588,301.544894751616,-36.9333243660161));
#1351=CARTESIAN_POINT('',(727.003654941431,278.251180692389,0.));
#1352=CARTESIAN_POINT('Origin',(502.5,368.001732061575,-19.1262684772861));
#1353=CARTESIAN_POINT('',(251.25,301.544894751615,-36.9333243660162));
#1354=CARTESIAN_POINT('',(726.991457225565,476.959305728253,10.0688253959548));
#1355=CARTESIAN_POINT('',(727.005694754826,252.257173364931,-50.1399295083485));
#1356=CARTESIAN_POINT('',(251.25,476.959305728253,10.0688253959548));
#1357=CARTESIAN_POINT('Origin',(502.5,357.648970257474,19.5107645742766));
#1358=CARTESIAN_POINT('',(726.991457225565,466.606543924152,48.7058584475175));
#1359=CARTESIAN_POINT('',(251.25,466.606543924152,48.7058584475175));
#1360=CARTESIAN_POINT('',(727.005694754826,241.90441156083,-11.5028964567857));
#1361=CARTESIAN_POINT('Origin',(727.005694754826,244.922415369288,0.));
#1362=CARTESIAN_POINT('',(727.005694754826,244.922415369288,-40.));
#1363=CARTESIAN_POINT('',(727.005694754826,244.922415369288,-40.));
#1364=CARTESIAN_POINT('',(727.005694754826,244.922415369288,0.));
#1365=CARTESIAN_POINT('',(727.005694754826,244.922415369288,0.));
#1366=CARTESIAN_POINT('',(727.005694754826,244.922415369288,0.));
#1367=CARTESIAN_POINT('Origin',(502.5,364.75,-40.));
#1368=CARTESIAN_POINT('',(946.,278.251180692389,-40.));
#1369=CARTESIAN_POINT('',(251.25,278.251180692389,-40.));
#1370=CARTESIAN_POINT('',(946.,225.710678118655,-40.));
#1371=CARTESIAN_POINT('',(946.,225.710678118655,-40.));
#1372=CARTESIAN_POINT('',(960.644660940673,190.355339059328,-40.));
#1373=CARTESIAN_POINT('Origin',(996.,225.710678118655,-40.));
#1374=CARTESIAN_POINT('',(990.355339059327,160.644660940673,-40.));
#1375=CARTESIAN_POINT('',(990.355339059327,160.644660940673,-40.));
#1376=CARTESIAN_POINT('',(1005.,125.289321881345,-40.));
#1377=CARTESIAN_POINT('Origin',(955.,125.289321881345,-40.));
#1378=CARTESIAN_POINT('',(1005.,49.9999999999999,-40.));
#1379=CARTESIAN_POINT('',(1005.,49.9999999999999,-40.));
#1380=CARTESIAN_POINT('',(955.,0.,-40.));
#1381=CARTESIAN_POINT('Origin',(955.,49.9999999999999,-40.));
#1382=CARTESIAN_POINT('',(1.99999999999999,0.,-40.));
#1383=CARTESIAN_POINT('',(2.,0.,-40.));
#1384=CARTESIAN_POINT('',(0.,1.99999999999999,-40.));
#1385=CARTESIAN_POINT('Origin',(2.,2.,-40.));
#1386=CARTESIAN_POINT('',(3.5527136788005E-14,128.,-40.));
#1387=CARTESIAN_POINT('',(1.11022302462516E-14,128.,-40.));
#1388=CARTESIAN_POINT('',(2.00000000000003,130.,-40.));
#1389=CARTESIAN_POINT('Origin',(2.00000000000001,128.,-40.));
#1390=CARTESIAN_POINT('',(249.171572875254,130.,-40.));
#1391=CARTESIAN_POINT('',(249.171572875254,130.,-40.));
#1392=CARTESIAN_POINT('',(250.585786437627,130.585786437627,-40.));
#1393=CARTESIAN_POINT('Origin',(249.171572875254,132.,-40.));
#1394=CARTESIAN_POINT('',(264.330508652763,144.330508652763,-40.));
#1395=CARTESIAN_POINT('',(250.585786437627,130.585786437627,-40.));
#1396=CARTESIAN_POINT('',(265.744722215136,144.91629509039,-40.));
#1397=CARTESIAN_POINT('Origin',(265.744722215136,142.91629509039,-40.));
#1398=CARTESIAN_POINT('',(627.005694942115,144.91629509039,-40.));
#1399=CARTESIAN_POINT('',(627.005694942115,144.91629509039,-40.));
#1400=CARTESIAN_POINT('Origin',(627.005694942115,244.91629509039,-40.));
#1401=CARTESIAN_POINT('Origin',(946.,291.192132947515,1.70370868554659));
#1402=CARTESIAN_POINT('',(946.,278.251180692389,0.));
#1403=CARTESIAN_POINT('Origin',(946.,278.251180692389,50.));
#1404=CARTESIAN_POINT('Origin',(946.,569.106475588835,51.7725340815013));
#1405=CARTESIAN_POINT('',(946.,489.900257983379,51.7725340815013));
#1406=CARTESIAN_POINT('',(946.,489.900257983379,11.7725340815014));
#1407=CARTESIAN_POINT('',(946.,489.900257983379,51.7725340815014));
#1408=CARTESIAN_POINT('',(946.,569.106475588835,11.7725340815013));
#1409=CARTESIAN_POINT('',(946.,220.362116794422,11.7725340815014));
#1410=CARTESIAN_POINT('',(946.,569.106475588835,51.7725340815013));
#1411=CARTESIAN_POINT('',(946.,569.106475588835,51.7725340815013));
#1412=CARTESIAN_POINT('',(946.,220.362116794422,51.7725340815014));
#1413=CARTESIAN_POINT('Origin',(836.500881677931,489.900257983379,-38.2274659184986));
#1414=CARTESIAN_POINT('',(726.990374080721,489.900257983379,51.7725340815014));
#1415=CARTESIAN_POINT('',(251.25,489.900257983379,51.7725340815014));
#1416=CARTESIAN_POINT('Ctrl Pts',(726.991457225565,466.606543924152,48.7058584475175));
#1417=CARTESIAN_POINT('Ctrl Pts',(726.991096177284,474.192907623608,50.7386184742755));
#1418=CARTESIAN_POINT('Ctrl Pts',(726.990735129002,482.046276349404,51.7725340815013));
#1419=CARTESIAN_POINT('Ctrl Pts',(726.990374080721,489.900257983379,51.7725340815013));
#1420=CARTESIAN_POINT('Origin',(946.,489.900257983379,-38.2274659184986));
#1421=CARTESIAN_POINT('Ctrl Pts',(726.991457225565,466.606543924152,48.7058584475175));
#1422=CARTESIAN_POINT('Ctrl Pts',(726.991457225565,470.057464525519,35.8268474303299));
#1423=CARTESIAN_POINT('Ctrl Pts',(726.991457225565,473.508385126886,22.9478364131423));
#1424=CARTESIAN_POINT('Ctrl Pts',(726.991457225565,476.959305728253,10.0688253959548));
#1425=CARTESIAN_POINT('Ctrl Pts',(726.991096177284,474.192907623608,50.7386184742755));
#1426=CARTESIAN_POINT('Ctrl Pts',(726.991096177284,476.519950646575,37.558273480742));
#1427=CARTESIAN_POINT('Ctrl Pts',(726.991096177284,478.846909204984,24.3784815154649));
#1428=CARTESIAN_POINT('Ctrl Pts',(726.991096177284,481.17395222795,11.1981365219314));
#1429=CARTESIAN_POINT('Ctrl Pts',(726.990735129002,482.046276349404,51.7725340815014));
#1430=CARTESIAN_POINT('Ctrl Pts',(726.990735129002,483.209849699983,38.4390153997308));
#1431=CARTESIAN_POINT('Ctrl Pts',(726.990735129002,484.373361502814,25.1060527632719));
#1432=CARTESIAN_POINT('Ctrl Pts',(726.990735129002,485.536934853393,11.7725340815014));
#1433=CARTESIAN_POINT('Ctrl Pts',(726.990374080721,489.900257983379,51.7725340815014));
#1434=CARTESIAN_POINT('Ctrl Pts',(726.990374080721,489.900257983379,38.439200748168));
#1435=CARTESIAN_POINT('Ctrl Pts',(726.990374080721,489.900257983379,25.1058674148347));
#1436=CARTESIAN_POINT('Ctrl Pts',(726.990374080721,489.900257983379,11.7725340815014));
#1437=CARTESIAN_POINT('',(726.990374080721,489.900257983379,11.7725340815014));
#1438=CARTESIAN_POINT('',(726.990374080721,489.900257983379,51.7725340815014));
#1439=CARTESIAN_POINT('Ctrl Pts',(726.991457225565,476.959305728253,10.0688253959548));
#1440=CARTESIAN_POINT('Ctrl Pts',(726.991096177284,481.17395222795,11.1981365219314));
#1441=CARTESIAN_POINT('Ctrl Pts',(726.990735129002,485.536934853393,11.7725340815014));
#1442=CARTESIAN_POINT('Ctrl Pts',(726.990374080721,489.900257983379,11.7725340815014));
#1443=CARTESIAN_POINT('',(726.991457225565,466.606543924152,48.7058584475175));
#1444=CARTESIAN_POINT('Origin',(502.5,359.401438675768,11.7725340815014));
#1445=CARTESIAN_POINT('',(251.25,489.900257983379,11.7725340815014));
#1446=CARTESIAN_POINT('',(726.979900223203,661.033921553603,11.7725340815014));
#1447=CARTESIAN_POINT('',(727.005694754826,239.573854045056,11.7725340815014));
#1448=CARTESIAN_POINT('',(743.506746425393,680.731267130051,11.7725340815013));
#1449=CARTESIAN_POINT('Origin',(746.979900185745,661.035145609382,11.7725340815014));
#1450=CARTESIAN_POINT('',(808.807455664011,692.246194249474,11.7725340815014));
#1451=CARTESIAN_POINT('',(808.807455664011,692.246194249474,11.7725340815014));
#1452=CARTESIAN_POINT('',(825.334301903603,711.942362753526,11.7725340815014));
#1453=CARTESIAN_POINT('Origin',(805.334301903658,711.942315770143,11.7725340815014));
#1454=CARTESIAN_POINT('',(825.334284968292,719.151426929922,11.7725340815013));
#1455=CARTESIAN_POINT('',(825.334301903603,711.942362753526,11.7725340815014));
#1456=CARTESIAN_POINT('',(830.334284968278,724.151438675768,11.7725340815013));
#1457=CARTESIAN_POINT('Origin',(830.334284968278,719.151438675768,11.7725340815014));
#1458=CARTESIAN_POINT('',(875.034284968278,724.151438675768,11.7725340815013));
#1459=CARTESIAN_POINT('',(875.034284968278,724.151438675768,11.7725340815013));
#1460=CARTESIAN_POINT('',(877.034284968278,722.151438675768,11.7725340815014));
#1461=CARTESIAN_POINT('Origin',(875.034284968278,722.151438675768,11.7725340815014));
#1462=CARTESIAN_POINT('',(877.034284968278,719.151438675768,11.7725340815014));
#1463=CARTESIAN_POINT('',(877.034284968278,719.151438675768,11.7725340815014));
#1464=CARTESIAN_POINT('',(925.034284968278,719.151438675768,11.7725340815014));
#1465=CARTESIAN_POINT('Origin',(901.034284968278,719.151438675768,11.7725340815014));
#1466=CARTESIAN_POINT('',(925.034284968278,722.151438675768,11.7725340815014));
#1467=CARTESIAN_POINT('',(925.034284968278,719.151438675768,11.7725340815014));
#1468=CARTESIAN_POINT('',(927.034284968278,724.151438675768,11.7725340815013));
#1469=CARTESIAN_POINT('Origin',(927.034284968278,722.151438675768,11.7725340815014));
#1470=CARTESIAN_POINT('',(965.334284968278,724.151438675768,11.7725340815013));
#1471=CARTESIAN_POINT('',(965.334284968278,724.151438675768,11.7725340815013));
#1472=CARTESIAN_POINT('',(975.334284968278,714.151438675768,11.7725340815014));
#1473=CARTESIAN_POINT('Origin',(965.334284968278,714.151438675768,11.7725340815014));
#1474=CARTESIAN_POINT('',(975.334284968278,639.862116794423,11.7725340815014));
#1475=CARTESIAN_POINT('',(975.334284968278,639.862116794423,11.7725340815014));
#1476=CARTESIAN_POINT('',(960.689624027605,604.506777735095,11.7725340815014));
#1477=CARTESIAN_POINT('Origin',(925.334284968278,639.862116794422,11.7725340815014));
#1478=CARTESIAN_POINT('',(960.644660940673,604.461814648163,11.7725340815013));
#1479=CARTESIAN_POINT('',(960.689624027605,604.506777735095,11.7725340815014));
#1480=CARTESIAN_POINT('Origin',(996.,569.106475588835,11.7725340815014));
#1481=CARTESIAN_POINT('Origin',(502.5,359.401438675768,51.7725340815014));
#1482=CARTESIAN_POINT('',(960.644660940673,604.461814648163,51.7725340815014));
#1483=CARTESIAN_POINT('Origin',(996.,569.106475588835,51.7725340815014));
#1484=CARTESIAN_POINT('',(960.689624027605,604.506777735095,51.7725340815014));
#1485=CARTESIAN_POINT('',(960.689624027605,604.506777735095,51.7725340815014));
#1486=CARTESIAN_POINT('',(975.334284968278,639.862116794423,51.7725340815014));
#1487=CARTESIAN_POINT('Origin',(925.334284968278,639.862116794422,51.7725340815014));
#1488=CARTESIAN_POINT('',(975.334284968278,714.151438675768,51.7725340815014));
#1489=CARTESIAN_POINT('',(975.334284968278,639.862116794423,51.7725340815014));
#1490=CARTESIAN_POINT('',(965.334284968278,724.151438675768,51.7725340815013));
#1491=CARTESIAN_POINT('Origin',(965.334284968278,714.151438675768,51.7725340815014));
#1492=CARTESIAN_POINT('',(927.034284968278,724.151438675768,51.7725340815013));
#1493=CARTESIAN_POINT('',(965.334284968278,724.151438675768,51.7725340815013));
#1494=CARTESIAN_POINT('',(925.034284968278,722.151438675768,51.7725340815014));
#1495=CARTESIAN_POINT('Origin',(927.034284968278,722.151438675768,51.7725340815014));
#1496=CARTESIAN_POINT('',(925.034284968278,719.151438675768,51.7725340815014));
#1497=CARTESIAN_POINT('',(925.034284968278,719.151438675768,51.7725340815014));
#1498=CARTESIAN_POINT('',(877.034284968278,719.151438675768,51.7725340815014));
#1499=CARTESIAN_POINT('Origin',(901.034284968278,719.151438675768,51.7725340815014));
#1500=CARTESIAN_POINT('',(877.034284968278,722.151438675768,51.7725340815014));
#1501=CARTESIAN_POINT('',(877.034284968278,719.151438675768,51.7725340815014));
#1502=CARTESIAN_POINT('',(875.034284968278,724.151438675768,51.7725340815013));
#1503=CARTESIAN_POINT('Origin',(875.034284968278,722.151438675768,51.7725340815014));
#1504=CARTESIAN_POINT('',(830.334284968278,724.151438675768,51.7725340815013));
#1505=CARTESIAN_POINT('',(875.034284968278,724.151438675768,51.7725340815013));
#1506=CARTESIAN_POINT('',(825.334284968292,719.151426929922,51.7725340815013));
#1507=CARTESIAN_POINT('Origin',(830.334284968278,719.151438675768,51.7725340815014));
#1508=CARTESIAN_POINT('',(825.334301903603,711.942362753526,51.7725340815014));
#1509=CARTESIAN_POINT('',(825.334301903603,711.942362753526,51.7725340815014));
#1510=CARTESIAN_POINT('',(808.807455664011,692.246194249474,51.7725340815014));
#1511=CARTESIAN_POINT('Origin',(805.334301903658,711.942315770143,51.7725340815014));
#1512=CARTESIAN_POINT('',(743.506746425393,680.731267130051,51.7725340815013));
#1513=CARTESIAN_POINT('',(808.807455664011,692.246194249474,51.7725340815014));
#1514=CARTESIAN_POINT('',(726.979900223203,661.033921553603,51.7725340815014));
#1515=CARTESIAN_POINT('Origin',(746.979900185745,661.035145609382,51.7725340815014));
#1516=CARTESIAN_POINT('',(727.005694754826,239.573854045056,51.7725340815014));
#1517=CARTESIAN_POINT('Origin',(727.005694754826,241.90441156083,-11.5028964567857));
#1518=CARTESIAN_POINT('Origin',(836.501556684505,278.251180692389,50.));
#1519=CARTESIAN_POINT('Origin',(946.,489.900257983379,51.7725340815014));
#1520=CARTESIAN_POINT('Origin',(946.,489.900257983379,-38.2274659184986));
#1521=CARTESIAN_POINT('Origin',(2.00000000000001,128.,0.));
#1522=CARTESIAN_POINT('',(2.00000000000001,130.,0.));
#1523=CARTESIAN_POINT('',(1.83186799063151E-14,128.,0.));
#1524=CARTESIAN_POINT('Origin',(2.00000000000001,128.,0.));
#1525=CARTESIAN_POINT('',(2.00000000000001,130.,0.));
#1526=CARTESIAN_POINT('',(1.83186799063151E-14,128.,0.));
#1527=CARTESIAN_POINT('Origin',(2.77555756156289E-16,1.99999999999999,0.));
#1528=CARTESIAN_POINT('',(2.77555756156289E-16,1.99999999999999,0.));
#1529=CARTESIAN_POINT('',(1.11022302462516E-14,128.,0.));
#1530=CARTESIAN_POINT('',(2.77555756156289E-16,1.99999999999999,0.));
#1531=CARTESIAN_POINT('Origin',(2.,2.,0.));
#1532=CARTESIAN_POINT('',(2.,0.,0.));
#1533=CARTESIAN_POINT('Origin',(2.,2.,0.));
#1534=CARTESIAN_POINT('',(2.,0.,0.));
#1535=CARTESIAN_POINT('Origin',(955.,8.88178419700125E-15,0.));
#1536=CARTESIAN_POINT('',(955.,8.88178419700125E-15,0.));
#1537=CARTESIAN_POINT('',(2.,0.,0.));
#1538=CARTESIAN_POINT('',(955.,8.88178419700125E-15,0.));
#1539=CARTESIAN_POINT('Origin',(955.,49.9999999999999,0.));
#1540=CARTESIAN_POINT('',(1005.,49.9999999999999,0.));
#1541=CARTESIAN_POINT('Origin',(955.,49.9999999999999,0.));
#1542=CARTESIAN_POINT('',(1005.,49.9999999999999,0.));
#1543=CARTESIAN_POINT('Origin',(1005.,125.289321881345,0.));
#1544=CARTESIAN_POINT('',(1005.,125.289321881345,0.));
#1545=CARTESIAN_POINT('',(1005.,49.9999999999999,0.));
#1546=CARTESIAN_POINT('',(1005.,125.289321881345,0.));
#1547=CARTESIAN_POINT('Origin',(955.,125.289321881345,0.));
#1548=CARTESIAN_POINT('',(990.355339059327,160.644660940673,0.));
#1549=CARTESIAN_POINT('Origin',(955.,125.289321881345,0.));
#1550=CARTESIAN_POINT('',(990.355339059327,160.644660940673,0.));
#1551=CARTESIAN_POINT('Origin',(960.644660940672,190.355339059327,0.));
#1552=CARTESIAN_POINT('',(960.644660940673,190.355339059328,0.));
#1553=CARTESIAN_POINT('',(990.355339059327,160.644660940673,0.));
#1554=CARTESIAN_POINT('',(960.644660940673,190.355339059328,0.));
#1555=CARTESIAN_POINT('Origin',(996.,225.710678118655,0.));
#1556=CARTESIAN_POINT('',(946.,225.710678118655,0.));
#1557=CARTESIAN_POINT('Origin',(996.,225.710678118655,0.));
#1558=CARTESIAN_POINT('',(946.,225.710678118655,0.));
#1559=CARTESIAN_POINT('Origin',(946.,574.455036913067,0.));
#1560=CARTESIAN_POINT('',(946.,225.710678118655,0.));
#1561=CARTESIAN_POINT('Origin',(996.,569.106475588835,51.7725340815014));
#1562=CARTESIAN_POINT('',(960.644660940673,604.461814648163,51.7725340815014));
#1563=CARTESIAN_POINT('Origin',(960.689624027605,604.506777735095,51.7725340815014));
#1564=CARTESIAN_POINT('',(960.689624027605,604.506777735095,51.7725340815014));
#1565=CARTESIAN_POINT('Origin',(925.334284968278,639.862116794422,51.7725340815014));
#1566=CARTESIAN_POINT('',(975.334284968278,639.862116794423,51.7725340815014));
#1567=CARTESIAN_POINT('Origin',(975.334284968278,714.151438675768,51.7725340815014));
#1568=CARTESIAN_POINT('',(975.334284968278,714.151438675768,51.7725340815014));
#1569=CARTESIAN_POINT('Origin',(965.334284968278,714.151438675768,51.7725340815014));
#1570=CARTESIAN_POINT('',(965.334284968278,724.151438675768,51.7725340815013));
#1571=CARTESIAN_POINT('Origin',(927.034284968278,724.151438675768,51.7725340815013));
#1572=CARTESIAN_POINT('',(927.034284968278,724.151438675768,51.7725340815013));
#1573=CARTESIAN_POINT('Origin',(927.034284968278,722.151438675768,51.7725340815014));
#1574=CARTESIAN_POINT('',(925.034284968278,722.151438675768,51.7725340815014));
#1575=CARTESIAN_POINT('Origin',(925.034284968278,719.151438675768,51.7725340815014));
#1576=CARTESIAN_POINT('',(925.034284968278,719.151438675768,51.7725340815014));
#1577=CARTESIAN_POINT('Origin',(901.034284968278,719.151438675768,51.7725340815014));
#1578=CARTESIAN_POINT('',(877.034284968278,719.151438675768,51.7725340815014));
#1579=CARTESIAN_POINT('Origin',(877.034284968278,722.151438675768,51.7725340815014));
#1580=CARTESIAN_POINT('',(877.034284968278,722.151438675768,51.7725340815014));
#1581=CARTESIAN_POINT('Origin',(875.034284968278,722.151438675768,51.7725340815014));
#1582=CARTESIAN_POINT('',(875.034284968278,724.151438675768,51.7725340815013));
#1583=CARTESIAN_POINT('Origin',(830.334284968278,724.151438675768,51.7725340815013));
#1584=CARTESIAN_POINT('',(830.334284968278,724.151438675768,51.7725340815013));
#1585=CARTESIAN_POINT('Origin',(830.334284968278,719.151438675768,51.7725340815014));
#1586=CARTESIAN_POINT('',(825.334284968292,719.151426929922,51.7725340815013));
#1587=CARTESIAN_POINT('Origin',(825.334301903603,711.942362753526,51.7725340815014));
#1588=CARTESIAN_POINT('',(825.334301903603,711.942362753526,51.7725340815014));
#1589=CARTESIAN_POINT('Origin',(805.334301903658,711.942315770143,51.7725340815014));
#1590=CARTESIAN_POINT('',(808.807455664011,692.246194249474,51.7725340815014));
#1591=CARTESIAN_POINT('Origin',(743.506746425393,680.731267130051,51.7725340815013));
#1592=CARTESIAN_POINT('',(743.506746425393,680.731267130051,51.7725340815013));
#1593=CARTESIAN_POINT('Origin',(746.979900185745,661.035145609382,51.7725340815014));
#1594=CARTESIAN_POINT('',(726.979900223203,661.033921553603,51.7725340815014));
#1595=CARTESIAN_POINT('Origin',(727.005694754826,239.573854045056,51.7725340815014));
#1596=CARTESIAN_POINT('Origin',(627.005694942115,244.91629509039,0.));
#1597=CARTESIAN_POINT('',(627.005694942115,144.91629509039,0.));
#1598=CARTESIAN_POINT('Origin',(627.005694942115,244.91629509039,0.));
#1599=CARTESIAN_POINT('',(627.005694942115,144.91629509039,0.));
#1600=CARTESIAN_POINT('Origin',(265.744722215136,144.91629509039,0.));
#1601=CARTESIAN_POINT('',(265.744722215136,144.91629509039,0.));
#1602=CARTESIAN_POINT('',(627.005694942115,144.91629509039,0.));
#1603=CARTESIAN_POINT('',(265.744722215136,144.91629509039,0.));
#1604=CARTESIAN_POINT('Origin',(265.744722215136,142.91629509039,0.));
#1605=CARTESIAN_POINT('',(264.330508652763,144.330508652763,0.));
#1606=CARTESIAN_POINT('Origin',(265.744722215136,142.91629509039,0.));
#1607=CARTESIAN_POINT('',(264.330508652763,144.330508652763,0.));
#1608=CARTESIAN_POINT('Origin',(250.585786437627,130.585786437627,0.));
#1609=CARTESIAN_POINT('',(250.585786437627,130.585786437627,0.));
#1610=CARTESIAN_POINT('',(250.585786437627,130.585786437627,0.));
#1611=CARTESIAN_POINT('',(250.585786437627,130.585786437627,0.));
#1612=CARTESIAN_POINT('Origin',(249.171572875254,132.,0.));
#1613=CARTESIAN_POINT('',(249.171572875254,130.,0.));
#1614=CARTESIAN_POINT('Origin',(249.171572875254,132.,0.));
#1615=CARTESIAN_POINT('',(249.171572875254,130.,0.));
#1616=CARTESIAN_POINT('Origin',(2.00000000000003,130.,0.));
#1617=CARTESIAN_POINT('',(249.171572875254,130.,0.));
#1618=CARTESIAN_POINT('Origin',(502.5,364.75,0.));
#1619=CARTESIAN_POINT('Origin',(836.500881677931,489.900257983379,-38.2274659184986));
#1620=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#1624,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#1621=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#1624,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#1622=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1620))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1624,#1626,#1627))
REPRESENTATION_CONTEXT('','3D')
);
#1623=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1621))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1624,#1626,#1627))
REPRESENTATION_CONTEXT('','3D')
);
#1624=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#1625=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT($,.METRE.)
);
#1626=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#1627=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#1628=SHAPE_DEFINITION_REPRESENTATION(#1629,#1630);
#1629=PRODUCT_DEFINITION_SHAPE('',$,#1632);
#1630=SHAPE_REPRESENTATION('',(#948),#1622);
#1631=PRODUCT_DEFINITION_CONTEXT('part definition',#1636,'design');
#1632=PRODUCT_DEFINITION('S_1157','S_1157 v4',#1633,#1631);
#1633=PRODUCT_DEFINITION_FORMATION('',$,#1638);
#1634=PRODUCT_RELATED_PRODUCT_CATEGORY('S_1157 v4','S_1157 v4',(#1638));
#1635=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2009,#1636);
#1636=APPLICATION_CONTEXT(
'Core Data for Automotive Mechanical Design Process');
#1637=PRODUCT_CONTEXT('part definition',#1636,'mechanical');
#1638=PRODUCT('S_1157','S_1157 v4',$,(#1637));
#1639=PRESENTATION_STYLE_ASSIGNMENT((#1640));
#1640=SURFACE_STYLE_USAGE(.BOTH.,#1641);
#1641=SURFACE_SIDE_STYLE('',(#1642));
#1642=SURFACE_STYLE_FILL_AREA(#1643);
#1643=FILL_AREA_STYLE('Steel - Satin',(#1644));
#1644=FILL_AREA_STYLE_COLOUR('Steel - Satin',#1645);
#1645=COLOUR_RGB('Steel - Satin',0.627450980392157,0.627450980392157,0.627450980392157);
ENDSEC;
END-ISO-10303-21;
