ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ (''),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 
'G:/Shared drives/TIP/Validation/SMF KION/Tset SMF Shapes/S_1125.step',

/* time_stamp */ '2021-07-21T17:12:26+02:00',
/* author */ (''),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v18.1',
/* originating_system */ 'Autodesk Translation Framework v10.9.0.1377',

/* authorisation */ '');

FILE_SCHEMA (('AUTOMOTIVE_DESIGN { 1 0 10303 214 3 1 1 }'));
ENDSEC;

DATA;
#10=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#13),#6720);
#11=SHAPE_REPRESENTATION_RELATIONSHIP('SRR','None',#6727,#12);
#12=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#14),#6719);
#13=STYLED_ITEM('',(#6736),#14);
#14=MANIFOLD_SOLID_BREP('Body1',#3969);
#15=B_SPLINE_SURFACE_WITH_KNOTS('',3,3,((#6609,#6610,#6611,#6612),(#6613,
#6614,#6615,#6616),(#6617,#6618,#6619,#6620),(#6621,#6622,#6623,#6624)),
 .UNSPECIFIED.,.F.,.F.,.F.,(4,4),(4,4),(0.,1.00000000000001),(-1.5707963267949,
-1.12522515557483),.UNSPECIFIED.);
#16=FACE_BOUND('',#381,.T.);
#17=FACE_BOUND('',#382,.T.);
#18=FACE_BOUND('',#396,.T.);
#19=FACE_BOUND('',#397,.T.);
#20=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5799,#5800,#5801,#5802),
 .UNSPECIFIED.,.F.,.F.,(4,4),(1.12522515557483,1.5707963267949),
 .UNSPECIFIED.);
#21=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5821,#5822,#5823,#5824),
 .UNSPECIFIED.,.F.,.F.,(4,4),(1.12522515557483,1.5707963267949),
 .UNSPECIFIED.);
#22=CYLINDRICAL_SURFACE('',#3992,6.);
#23=CYLINDRICAL_SURFACE('',#4012,3.);
#24=CYLINDRICAL_SURFACE('',#4018,3.);
#25=CYLINDRICAL_SURFACE('',#4029,6.);
#26=CYLINDRICAL_SURFACE('',#4035,3.);
#27=CYLINDRICAL_SURFACE('',#4041,6.);
#28=CYLINDRICAL_SURFACE('',#4051,6.);
#29=CYLINDRICAL_SURFACE('',#4053,2.99999999999997);
#30=CYLINDRICAL_SURFACE('',#4058,3.);
#31=CYLINDRICAL_SURFACE('',#4066,6.);
#32=CYLINDRICAL_SURFACE('',#4075,3.);
#33=CYLINDRICAL_SURFACE('',#4092,6.);
#34=CYLINDRICAL_SURFACE('',#4100,3.);
#35=CYLINDRICAL_SURFACE('',#4106,6.);
#36=CYLINDRICAL_SURFACE('',#4123,3.);
#37=CYLINDRICAL_SURFACE('',#4141,6.);
#38=CYLINDRICAL_SURFACE('',#4147,3.);
#39=CYLINDRICAL_SURFACE('',#4165,6.);
#40=CYLINDRICAL_SURFACE('',#4183,3.);
#41=CYLINDRICAL_SURFACE('',#4186,3.);
#42=CYLINDRICAL_SURFACE('',#4225,1.00000000000005);
#43=CYLINDRICAL_SURFACE('',#4227,1.00000000000001);
#44=CYLINDRICAL_SURFACE('',#4229,0.999999999999943);
#45=CYLINDRICAL_SURFACE('',#4231,0.999999999999996);
#46=CYLINDRICAL_SURFACE('',#4233,1.99999999999999);
#47=CYLINDRICAL_SURFACE('',#4235,1.99999999999999);
#48=CYLINDRICAL_SURFACE('',#4237,1.00000000000002);
#49=CYLINDRICAL_SURFACE('',#4240,1.);
#50=CYLINDRICAL_SURFACE('',#4243,1.);
#51=CYLINDRICAL_SURFACE('',#4246,15.);
#52=CYLINDRICAL_SURFACE('',#4249,15.0000000000001);
#53=CYLINDRICAL_SURFACE('',#4252,15.);
#54=CYLINDRICAL_SURFACE('',#4255,1.);
#55=CYLINDRICAL_SURFACE('',#4257,1.);
#56=CYLINDRICAL_SURFACE('',#4259,1.);
#57=CYLINDRICAL_SURFACE('',#4261,15.);
#58=CYLINDRICAL_SURFACE('',#4264,15.);
#59=CYLINDRICAL_SURFACE('',#4267,3.00000000000002);
#60=CYLINDRICAL_SURFACE('',#4270,0.499999999999998);
#61=CYLINDRICAL_SURFACE('',#4272,0.499999999999998);
#62=CYLINDRICAL_SURFACE('',#4274,0.499999999999998);
#63=CYLINDRICAL_SURFACE('',#4276,0.499999999999998);
#64=CYLINDRICAL_SURFACE('',#4278,0.499999999999998);
#65=CYLINDRICAL_SURFACE('',#4280,0.999999999999997);
#66=CYLINDRICAL_SURFACE('',#4282,0.999999999999996);
#67=CYLINDRICAL_SURFACE('',#4284,0.499999999999994);
#68=CYLINDRICAL_SURFACE('',#4286,0.499999999999998);
#69=CYLINDRICAL_SURFACE('',#4288,0.499999999999998);
#70=CYLINDRICAL_SURFACE('',#4290,0.499999999999998);
#71=CYLINDRICAL_SURFACE('',#4292,0.999999999999997);
#72=CYLINDRICAL_SURFACE('',#4294,3.00000000000002);
#73=CYLINDRICAL_SURFACE('',#4297,3.00000000000004);
#74=CYLINDRICAL_SURFACE('',#4302,3.00000000000001);
#75=CYLINDRICAL_SURFACE('',#4305,2.99999999999997);
#76=CYLINDRICAL_SURFACE('',#4307,2.99999999999997);
#77=CYLINDRICAL_SURFACE('',#4309,0.5);
#78=CYLINDRICAL_SURFACE('',#4311,0.499999999999829);
#79=CYLINDRICAL_SURFACE('',#4313,1.);
#80=CYLINDRICAL_SURFACE('',#4316,2.99999999999997);
#81=CYLINDRICAL_SURFACE('',#4318,0.499999999999829);
#82=CYLINDRICAL_SURFACE('',#4320,0.50000000000006);
#83=CYLINDRICAL_SURFACE('',#4322,0.500000000000114);
#84=CYLINDRICAL_SURFACE('',#4324,0.499999999999972);
#85=CYLINDRICAL_SURFACE('',#4326,0.999999999999885);
#86=CYLINDRICAL_SURFACE('',#4328,0.999999999999943);
#87=CYLINDRICAL_SURFACE('',#4330,0.5);
#88=CYLINDRICAL_SURFACE('',#4332,0.5);
#89=CYLINDRICAL_SURFACE('',#4334,0.499999999999972);
#90=CYLINDRICAL_SURFACE('',#4336,0.499999999999972);
#91=CYLINDRICAL_SURFACE('',#4339,0.999999999999659);
#92=CYLINDRICAL_SURFACE('',#4341,0.999999999999996);
#93=CYLINDRICAL_SURFACE('',#4343,1.00000000000014);
#94=CYLINDRICAL_SURFACE('',#4345,0.999999999999983);
#95=CYLINDRICAL_SURFACE('',#4347,0.999999999999839);
#96=CYLINDRICAL_SURFACE('',#4349,1.00000000000001);
#97=CYLINDRICAL_SURFACE('',#4352,2.99999999999999);
#98=CYLINDRICAL_SURFACE('',#4354,2.99999999999997);
#99=CYLINDRICAL_SURFACE('',#4356,1.00000000000001);
#100=CYLINDRICAL_SURFACE('',#4358,1.00000000000015);
#101=CYLINDRICAL_SURFACE('',#4360,1.00000000000009);
#102=CYLINDRICAL_SURFACE('',#4362,2.00000000000006);
#103=CYLINDRICAL_SURFACE('',#4365,6.);
#104=FACE_OUTER_BOUND('',#324,.T.);
#105=FACE_OUTER_BOUND('',#325,.T.);
#106=FACE_OUTER_BOUND('',#326,.T.);
#107=FACE_OUTER_BOUND('',#327,.T.);
#108=FACE_OUTER_BOUND('',#328,.T.);
#109=FACE_OUTER_BOUND('',#329,.T.);
#110=FACE_OUTER_BOUND('',#330,.T.);
#111=FACE_OUTER_BOUND('',#331,.T.);
#112=FACE_OUTER_BOUND('',#332,.T.);
#113=FACE_OUTER_BOUND('',#333,.T.);
#114=FACE_OUTER_BOUND('',#334,.T.);
#115=FACE_OUTER_BOUND('',#335,.T.);
#116=FACE_OUTER_BOUND('',#336,.T.);
#117=FACE_OUTER_BOUND('',#337,.T.);
#118=FACE_OUTER_BOUND('',#338,.T.);
#119=FACE_OUTER_BOUND('',#339,.T.);
#120=FACE_OUTER_BOUND('',#340,.T.);
#121=FACE_OUTER_BOUND('',#341,.T.);
#122=FACE_OUTER_BOUND('',#342,.T.);
#123=FACE_OUTER_BOUND('',#343,.T.);
#124=FACE_OUTER_BOUND('',#344,.T.);
#125=FACE_OUTER_BOUND('',#345,.T.);
#126=FACE_OUTER_BOUND('',#346,.T.);
#127=FACE_OUTER_BOUND('',#347,.T.);
#128=FACE_OUTER_BOUND('',#348,.T.);
#129=FACE_OUTER_BOUND('',#349,.T.);
#130=FACE_OUTER_BOUND('',#350,.T.);
#131=FACE_OUTER_BOUND('',#351,.T.);
#132=FACE_OUTER_BOUND('',#352,.T.);
#133=FACE_OUTER_BOUND('',#353,.T.);
#134=FACE_OUTER_BOUND('',#354,.T.);
#135=FACE_OUTER_BOUND('',#355,.T.);
#136=FACE_OUTER_BOUND('',#356,.T.);
#137=FACE_OUTER_BOUND('',#357,.T.);
#138=FACE_OUTER_BOUND('',#358,.T.);
#139=FACE_OUTER_BOUND('',#359,.T.);
#140=FACE_OUTER_BOUND('',#360,.T.);
#141=FACE_OUTER_BOUND('',#361,.T.);
#142=FACE_OUTER_BOUND('',#362,.T.);
#143=FACE_OUTER_BOUND('',#363,.T.);
#144=FACE_OUTER_BOUND('',#364,.T.);
#145=FACE_OUTER_BOUND('',#365,.T.);
#146=FACE_OUTER_BOUND('',#366,.T.);
#147=FACE_OUTER_BOUND('',#367,.T.);
#148=FACE_OUTER_BOUND('',#368,.T.);
#149=FACE_OUTER_BOUND('',#369,.T.);
#150=FACE_OUTER_BOUND('',#370,.T.);
#151=FACE_OUTER_BOUND('',#371,.T.);
#152=FACE_OUTER_BOUND('',#372,.T.);
#153=FACE_OUTER_BOUND('',#373,.T.);
#154=FACE_OUTER_BOUND('',#374,.T.);
#155=FACE_OUTER_BOUND('',#375,.T.);
#156=FACE_OUTER_BOUND('',#376,.T.);
#157=FACE_OUTER_BOUND('',#377,.T.);
#158=FACE_OUTER_BOUND('',#378,.T.);
#159=FACE_OUTER_BOUND('',#379,.T.);
#160=FACE_OUTER_BOUND('',#380,.T.);
#161=FACE_OUTER_BOUND('',#383,.T.);
#162=FACE_OUTER_BOUND('',#384,.T.);
#163=FACE_OUTER_BOUND('',#385,.T.);
#164=FACE_OUTER_BOUND('',#386,.T.);
#165=FACE_OUTER_BOUND('',#387,.T.);
#166=FACE_OUTER_BOUND('',#388,.T.);
#167=FACE_OUTER_BOUND('',#389,.T.);
#168=FACE_OUTER_BOUND('',#390,.T.);
#169=FACE_OUTER_BOUND('',#391,.T.);
#170=FACE_OUTER_BOUND('',#392,.T.);
#171=FACE_OUTER_BOUND('',#393,.T.);
#172=FACE_OUTER_BOUND('',#394,.T.);
#173=FACE_OUTER_BOUND('',#395,.T.);
#174=FACE_OUTER_BOUND('',#398,.T.);
#175=FACE_OUTER_BOUND('',#399,.T.);
#176=FACE_OUTER_BOUND('',#400,.T.);
#177=FACE_OUTER_BOUND('',#401,.T.);
#178=FACE_OUTER_BOUND('',#402,.T.);
#179=FACE_OUTER_BOUND('',#403,.T.);
#180=FACE_OUTER_BOUND('',#404,.T.);
#181=FACE_OUTER_BOUND('',#405,.T.);
#182=FACE_OUTER_BOUND('',#406,.T.);
#183=FACE_OUTER_BOUND('',#407,.T.);
#184=FACE_OUTER_BOUND('',#408,.T.);
#185=FACE_OUTER_BOUND('',#409,.T.);
#186=FACE_OUTER_BOUND('',#410,.T.);
#187=FACE_OUTER_BOUND('',#411,.T.);
#188=FACE_OUTER_BOUND('',#412,.T.);
#189=FACE_OUTER_BOUND('',#413,.T.);
#190=FACE_OUTER_BOUND('',#414,.T.);
#191=FACE_OUTER_BOUND('',#415,.T.);
#192=FACE_OUTER_BOUND('',#416,.T.);
#193=FACE_OUTER_BOUND('',#417,.T.);
#194=FACE_OUTER_BOUND('',#418,.T.);
#195=FACE_OUTER_BOUND('',#419,.T.);
#196=FACE_OUTER_BOUND('',#420,.T.);
#197=FACE_OUTER_BOUND('',#421,.T.);
#198=FACE_OUTER_BOUND('',#422,.T.);
#199=FACE_OUTER_BOUND('',#423,.T.);
#200=FACE_OUTER_BOUND('',#424,.T.);
#201=FACE_OUTER_BOUND('',#425,.T.);
#202=FACE_OUTER_BOUND('',#426,.T.);
#203=FACE_OUTER_BOUND('',#427,.T.);
#204=FACE_OUTER_BOUND('',#428,.T.);
#205=FACE_OUTER_BOUND('',#429,.T.);
#206=FACE_OUTER_BOUND('',#430,.T.);
#207=FACE_OUTER_BOUND('',#431,.T.);
#208=FACE_OUTER_BOUND('',#432,.T.);
#209=FACE_OUTER_BOUND('',#433,.T.);
#210=FACE_OUTER_BOUND('',#434,.T.);
#211=FACE_OUTER_BOUND('',#435,.T.);
#212=FACE_OUTER_BOUND('',#436,.T.);
#213=FACE_OUTER_BOUND('',#437,.T.);
#214=FACE_OUTER_BOUND('',#438,.T.);
#215=FACE_OUTER_BOUND('',#439,.T.);
#216=FACE_OUTER_BOUND('',#440,.T.);
#217=FACE_OUTER_BOUND('',#441,.T.);
#218=FACE_OUTER_BOUND('',#442,.T.);
#219=FACE_OUTER_BOUND('',#443,.T.);
#220=FACE_OUTER_BOUND('',#444,.T.);
#221=FACE_OUTER_BOUND('',#445,.T.);
#222=FACE_OUTER_BOUND('',#446,.T.);
#223=FACE_OUTER_BOUND('',#447,.T.);
#224=FACE_OUTER_BOUND('',#448,.T.);
#225=FACE_OUTER_BOUND('',#449,.T.);
#226=FACE_OUTER_BOUND('',#450,.T.);
#227=FACE_OUTER_BOUND('',#451,.T.);
#228=FACE_OUTER_BOUND('',#452,.T.);
#229=FACE_OUTER_BOUND('',#453,.T.);
#230=FACE_OUTER_BOUND('',#454,.T.);
#231=FACE_OUTER_BOUND('',#455,.T.);
#232=FACE_OUTER_BOUND('',#456,.T.);
#233=FACE_OUTER_BOUND('',#457,.T.);
#234=FACE_OUTER_BOUND('',#458,.T.);
#235=FACE_OUTER_BOUND('',#459,.T.);
#236=FACE_OUTER_BOUND('',#460,.T.);
#237=FACE_OUTER_BOUND('',#461,.T.);
#238=FACE_OUTER_BOUND('',#462,.T.);
#239=FACE_OUTER_BOUND('',#463,.T.);
#240=FACE_OUTER_BOUND('',#464,.T.);
#241=FACE_OUTER_BOUND('',#465,.T.);
#242=FACE_OUTER_BOUND('',#466,.T.);
#243=FACE_OUTER_BOUND('',#467,.T.);
#244=FACE_OUTER_BOUND('',#468,.T.);
#245=FACE_OUTER_BOUND('',#469,.T.);
#246=FACE_OUTER_BOUND('',#470,.T.);
#247=FACE_OUTER_BOUND('',#471,.T.);
#248=FACE_OUTER_BOUND('',#472,.T.);
#249=FACE_OUTER_BOUND('',#473,.T.);
#250=FACE_OUTER_BOUND('',#474,.T.);
#251=FACE_OUTER_BOUND('',#475,.T.);
#252=FACE_OUTER_BOUND('',#476,.T.);
#253=FACE_OUTER_BOUND('',#477,.T.);
#254=FACE_OUTER_BOUND('',#478,.T.);
#255=FACE_OUTER_BOUND('',#479,.T.);
#256=FACE_OUTER_BOUND('',#480,.T.);
#257=FACE_OUTER_BOUND('',#481,.T.);
#258=FACE_OUTER_BOUND('',#482,.T.);
#259=FACE_OUTER_BOUND('',#483,.T.);
#260=FACE_OUTER_BOUND('',#484,.T.);
#261=FACE_OUTER_BOUND('',#485,.T.);
#262=FACE_OUTER_BOUND('',#486,.T.);
#263=FACE_OUTER_BOUND('',#487,.T.);
#264=FACE_OUTER_BOUND('',#488,.T.);
#265=FACE_OUTER_BOUND('',#489,.T.);
#266=FACE_OUTER_BOUND('',#490,.T.);
#267=FACE_OUTER_BOUND('',#491,.T.);
#268=FACE_OUTER_BOUND('',#492,.T.);
#269=FACE_OUTER_BOUND('',#493,.T.);
#270=FACE_OUTER_BOUND('',#494,.T.);
#271=FACE_OUTER_BOUND('',#495,.T.);
#272=FACE_OUTER_BOUND('',#496,.T.);
#273=FACE_OUTER_BOUND('',#497,.T.);
#274=FACE_OUTER_BOUND('',#498,.T.);
#275=FACE_OUTER_BOUND('',#499,.T.);
#276=FACE_OUTER_BOUND('',#500,.T.);
#277=FACE_OUTER_BOUND('',#501,.T.);
#278=FACE_OUTER_BOUND('',#502,.T.);
#279=FACE_OUTER_BOUND('',#503,.T.);
#280=FACE_OUTER_BOUND('',#504,.T.);
#281=FACE_OUTER_BOUND('',#505,.T.);
#282=FACE_OUTER_BOUND('',#506,.T.);
#283=FACE_OUTER_BOUND('',#507,.T.);
#284=FACE_OUTER_BOUND('',#508,.T.);
#285=FACE_OUTER_BOUND('',#509,.T.);
#286=FACE_OUTER_BOUND('',#510,.T.);
#287=FACE_OUTER_BOUND('',#511,.T.);
#288=FACE_OUTER_BOUND('',#512,.T.);
#289=FACE_OUTER_BOUND('',#513,.T.);
#290=FACE_OUTER_BOUND('',#514,.T.);
#291=FACE_OUTER_BOUND('',#515,.T.);
#292=FACE_OUTER_BOUND('',#516,.T.);
#293=FACE_OUTER_BOUND('',#517,.T.);
#294=FACE_OUTER_BOUND('',#518,.T.);
#295=FACE_OUTER_BOUND('',#519,.T.);
#296=FACE_OUTER_BOUND('',#520,.T.);
#297=FACE_OUTER_BOUND('',#521,.T.);
#298=FACE_OUTER_BOUND('',#522,.T.);
#299=FACE_OUTER_BOUND('',#523,.T.);
#300=FACE_OUTER_BOUND('',#524,.T.);
#301=FACE_OUTER_BOUND('',#525,.T.);
#302=FACE_OUTER_BOUND('',#526,.T.);
#303=FACE_OUTER_BOUND('',#527,.T.);
#304=FACE_OUTER_BOUND('',#528,.T.);
#305=FACE_OUTER_BOUND('',#529,.T.);
#306=FACE_OUTER_BOUND('',#530,.T.);
#307=FACE_OUTER_BOUND('',#531,.T.);
#308=FACE_OUTER_BOUND('',#532,.T.);
#309=FACE_OUTER_BOUND('',#533,.T.);
#310=FACE_OUTER_BOUND('',#534,.T.);
#311=FACE_OUTER_BOUND('',#535,.T.);
#312=FACE_OUTER_BOUND('',#536,.T.);
#313=FACE_OUTER_BOUND('',#537,.T.);
#314=FACE_OUTER_BOUND('',#538,.T.);
#315=FACE_OUTER_BOUND('',#539,.T.);
#316=FACE_OUTER_BOUND('',#540,.T.);
#317=FACE_OUTER_BOUND('',#541,.T.);
#318=FACE_OUTER_BOUND('',#542,.T.);
#319=FACE_OUTER_BOUND('',#543,.T.);
#320=FACE_OUTER_BOUND('',#544,.T.);
#321=FACE_OUTER_BOUND('',#545,.T.);
#322=FACE_OUTER_BOUND('',#546,.T.);
#323=FACE_OUTER_BOUND('',#547,.T.);
#324=EDGE_LOOP('',(#2462,#2463,#2464,#2465,#2466,#2467,#2468,#2469,#2470,
#2471,#2472,#2473,#2474,#2475));
#325=EDGE_LOOP('',(#2476,#2477,#2478,#2479));
#326=EDGE_LOOP('',(#2480,#2481,#2482,#2483));
#327=EDGE_LOOP('',(#2484,#2485,#2486,#2487));
#328=EDGE_LOOP('',(#2488,#2489,#2490,#2491));
#329=EDGE_LOOP('',(#2492,#2493,#2494,#2495,#2496,#2497,#2498,#2499,#2500,
#2501,#2502,#2503,#2504,#2505,#2506,#2507,#2508,#2509,#2510,#2511,#2512,
#2513,#2514,#2515,#2516,#2517,#2518,#2519,#2520,#2521,#2522,#2523,#2524,
#2525,#2526,#2527,#2528));
#330=EDGE_LOOP('',(#2529,#2530,#2531,#2532));
#331=EDGE_LOOP('',(#2533,#2534,#2535,#2536));
#332=EDGE_LOOP('',(#2537,#2538,#2539,#2540));
#333=EDGE_LOOP('',(#2541,#2542,#2543,#2544));
#334=EDGE_LOOP('',(#2545,#2546,#2547,#2548));
#335=EDGE_LOOP('',(#2549,#2550,#2551,#2552));
#336=EDGE_LOOP('',(#2553,#2554,#2555,#2556));
#337=EDGE_LOOP('',(#2557,#2558,#2559,#2560,#2561,#2562,#2563,#2564,#2565,
#2566,#2567,#2568,#2569,#2570));
#338=EDGE_LOOP('',(#2571,#2572,#2573,#2574));
#339=EDGE_LOOP('',(#2575,#2576,#2577,#2578));
#340=EDGE_LOOP('',(#2579,#2580,#2581,#2582));
#341=EDGE_LOOP('',(#2583,#2584,#2585,#2586));
#342=EDGE_LOOP('',(#2587,#2588,#2589,#2590));
#343=EDGE_LOOP('',(#2591,#2592,#2593,#2594));
#344=EDGE_LOOP('',(#2595,#2596,#2597,#2598));
#345=EDGE_LOOP('',(#2599,#2600,#2601,#2602));
#346=EDGE_LOOP('',(#2603,#2604,#2605,#2606));
#347=EDGE_LOOP('',(#2607,#2608,#2609,#2610));
#348=EDGE_LOOP('',(#2611,#2612,#2613,#2614,#2615,#2616,#2617,#2618,#2619,
#2620,#2621,#2622,#2623));
#349=EDGE_LOOP('',(#2624,#2625,#2626,#2627));
#350=EDGE_LOOP('',(#2628,#2629,#2630,#2631,#2632));
#351=EDGE_LOOP('',(#2633,#2634,#2635,#2636,#2637));
#352=EDGE_LOOP('',(#2638,#2639,#2640,#2641));
#353=EDGE_LOOP('',(#2642,#2643,#2644,#2645));
#354=EDGE_LOOP('',(#2646,#2647,#2648,#2649));
#355=EDGE_LOOP('',(#2650,#2651,#2652,#2653,#2654));
#356=EDGE_LOOP('',(#2655,#2656,#2657,#2658,#2659,#2660));
#357=EDGE_LOOP('',(#2661,#2662,#2663,#2664));
#358=EDGE_LOOP('',(#2665,#2666,#2667,#2668));
#359=EDGE_LOOP('',(#2669,#2670,#2671,#2672));
#360=EDGE_LOOP('',(#2673,#2674,#2675,#2676));
#361=EDGE_LOOP('',(#2677,#2678,#2679,#2680,#2681,#2682,#2683,#2684,#2685,
#2686,#2687,#2688,#2689));
#362=EDGE_LOOP('',(#2690,#2691,#2692,#2693));
#363=EDGE_LOOP('',(#2694,#2695,#2696,#2697));
#364=EDGE_LOOP('',(#2698,#2699,#2700,#2701,#2702,#2703,#2704,#2705,#2706,
#2707,#2708,#2709,#2710,#2711,#2712,#2713,#2714,#2715,#2716,#2717,#2718,
#2719,#2720,#2721,#2722));
#365=EDGE_LOOP('',(#2723,#2724,#2725,#2726));
#366=EDGE_LOOP('',(#2727,#2728,#2729,#2730));
#367=EDGE_LOOP('',(#2731,#2732,#2733,#2734));
#368=EDGE_LOOP('',(#2735,#2736,#2737,#2738));
#369=EDGE_LOOP('',(#2739,#2740,#2741,#2742,#2743,#2744));
#370=EDGE_LOOP('',(#2745,#2746,#2747,#2748));
#371=EDGE_LOOP('',(#2749,#2750,#2751,#2752));
#372=EDGE_LOOP('',(#2753,#2754,#2755,#2756));
#373=EDGE_LOOP('',(#2757,#2758,#2759,#2760));
#374=EDGE_LOOP('',(#2761,#2762,#2763,#2764));
#375=EDGE_LOOP('',(#2765,#2766,#2767,#2768));
#376=EDGE_LOOP('',(#2769,#2770,#2771,#2772));
#377=EDGE_LOOP('',(#2773,#2774,#2775,#2776,#2777,#2778,#2779,#2780,#2781,
#2782,#2783,#2784,#2785,#2786,#2787,#2788,#2789,#2790,#2791,#2792,#2793,
#2794,#2795,#2796,#2797));
#378=EDGE_LOOP('',(#2798,#2799,#2800,#2801));
#379=EDGE_LOOP('',(#2802,#2803,#2804,#2805));
#380=EDGE_LOOP('',(#2806,#2807,#2808,#2809,#2810,#2811,#2812,#2813,#2814,
#2815,#2816,#2817,#2818,#2819,#2820,#2821,#2822,#2823,#2824,#2825,#2826,
#2827,#2828,#2829,#2830,#2831));
#381=EDGE_LOOP('',(#2832,#2833,#2834,#2835,#2836,#2837));
#382=EDGE_LOOP('',(#2838,#2839,#2840,#2841,#2842,#2843));
#383=EDGE_LOOP('',(#2844,#2845,#2846,#2847));
#384=EDGE_LOOP('',(#2848,#2849,#2850,#2851));
#385=EDGE_LOOP('',(#2852,#2853,#2854,#2855));
#386=EDGE_LOOP('',(#2856,#2857,#2858,#2859));
#387=EDGE_LOOP('',(#2860,#2861,#2862,#2863));
#388=EDGE_LOOP('',(#2864,#2865,#2866,#2867));
#389=EDGE_LOOP('',(#2868,#2869,#2870,#2871));
#390=EDGE_LOOP('',(#2872,#2873,#2874,#2875,#2876,#2877,#2878,#2879,#2880,
#2881,#2882,#2883,#2884,#2885,#2886,#2887,#2888,#2889,#2890,#2891,#2892,
#2893,#2894,#2895,#2896,#2897));
#391=EDGE_LOOP('',(#2898,#2899,#2900,#2901));
#392=EDGE_LOOP('',(#2902,#2903,#2904,#2905));
#393=EDGE_LOOP('',(#2906,#2907,#2908,#2909));
#394=EDGE_LOOP('',(#2910,#2911,#2912,#2913));
#395=EDGE_LOOP('',(#2914,#2915,#2916,#2917,#2918,#2919,#2920,#2921,#2922,
#2923,#2924,#2925,#2926,#2927,#2928,#2929,#2930,#2931,#2932,#2933,#2934,
#2935,#2936,#2937,#2938,#2939));
#396=EDGE_LOOP('',(#2940,#2941,#2942,#2943,#2944,#2945));
#397=EDGE_LOOP('',(#2946,#2947,#2948,#2949,#2950,#2951));
#398=EDGE_LOOP('',(#2952,#2953,#2954,#2955));
#399=EDGE_LOOP('',(#2956,#2957,#2958,#2959));
#400=EDGE_LOOP('',(#2960,#2961,#2962,#2963));
#401=EDGE_LOOP('',(#2964,#2965,#2966,#2967));
#402=EDGE_LOOP('',(#2968,#2969,#2970,#2971));
#403=EDGE_LOOP('',(#2972,#2973,#2974,#2975,#2976,#2977));
#404=EDGE_LOOP('',(#2978,#2979,#2980,#2981,#2982,#2983));
#405=EDGE_LOOP('',(#2984,#2985,#2986,#2987));
#406=EDGE_LOOP('',(#2988,#2989,#2990,#2991,#2992,#2993,#2994,#2995,#2996,
#2997,#2998,#2999,#3000,#3001,#3002,#3003,#3004,#3005,#3006,#3007,#3008,
#3009,#3010,#3011,#3012,#3013));
#407=EDGE_LOOP('',(#3014,#3015,#3016,#3017));
#408=EDGE_LOOP('',(#3018,#3019,#3020,#3021));
#409=EDGE_LOOP('',(#3022,#3023,#3024,#3025));
#410=EDGE_LOOP('',(#3026,#3027,#3028,#3029));
#411=EDGE_LOOP('',(#3030,#3031,#3032,#3033));
#412=EDGE_LOOP('',(#3034,#3035,#3036,#3037));
#413=EDGE_LOOP('',(#3038,#3039,#3040,#3041));
#414=EDGE_LOOP('',(#3042,#3043,#3044,#3045));
#415=EDGE_LOOP('',(#3046,#3047,#3048,#3049));
#416=EDGE_LOOP('',(#3050,#3051,#3052,#3053));
#417=EDGE_LOOP('',(#3054,#3055,#3056,#3057));
#418=EDGE_LOOP('',(#3058,#3059,#3060,#3061));
#419=EDGE_LOOP('',(#3062,#3063,#3064,#3065));
#420=EDGE_LOOP('',(#3066,#3067,#3068,#3069));
#421=EDGE_LOOP('',(#3070,#3071,#3072,#3073));
#422=EDGE_LOOP('',(#3074,#3075,#3076,#3077));
#423=EDGE_LOOP('',(#3078,#3079,#3080,#3081));
#424=EDGE_LOOP('',(#3082,#3083,#3084,#3085));
#425=EDGE_LOOP('',(#3086,#3087,#3088,#3089));
#426=EDGE_LOOP('',(#3090,#3091,#3092,#3093));
#427=EDGE_LOOP('',(#3094,#3095,#3096,#3097));
#428=EDGE_LOOP('',(#3098,#3099,#3100,#3101));
#429=EDGE_LOOP('',(#3102,#3103,#3104,#3105));
#430=EDGE_LOOP('',(#3106,#3107,#3108,#3109));
#431=EDGE_LOOP('',(#3110,#3111,#3112,#3113));
#432=EDGE_LOOP('',(#3114,#3115,#3116,#3117));
#433=EDGE_LOOP('',(#3118,#3119,#3120,#3121));
#434=EDGE_LOOP('',(#3122,#3123,#3124,#3125));
#435=EDGE_LOOP('',(#3126,#3127,#3128,#3129));
#436=EDGE_LOOP('',(#3130,#3131,#3132,#3133));
#437=EDGE_LOOP('',(#3134,#3135,#3136,#3137));
#438=EDGE_LOOP('',(#3138,#3139,#3140,#3141));
#439=EDGE_LOOP('',(#3142,#3143,#3144,#3145));
#440=EDGE_LOOP('',(#3146,#3147,#3148,#3149));
#441=EDGE_LOOP('',(#3150,#3151,#3152,#3153));
#442=EDGE_LOOP('',(#3154,#3155,#3156,#3157));
#443=EDGE_LOOP('',(#3158,#3159,#3160,#3161));
#444=EDGE_LOOP('',(#3162,#3163,#3164,#3165));
#445=EDGE_LOOP('',(#3166,#3167,#3168,#3169));
#446=EDGE_LOOP('',(#3170,#3171,#3172,#3173));
#447=EDGE_LOOP('',(#3174,#3175,#3176,#3177));
#448=EDGE_LOOP('',(#3178,#3179,#3180,#3181));
#449=EDGE_LOOP('',(#3182,#3183,#3184,#3185));
#450=EDGE_LOOP('',(#3186,#3187,#3188,#3189));
#451=EDGE_LOOP('',(#3190,#3191,#3192,#3193));
#452=EDGE_LOOP('',(#3194,#3195,#3196,#3197));
#453=EDGE_LOOP('',(#3198,#3199,#3200,#3201));
#454=EDGE_LOOP('',(#3202,#3203,#3204,#3205));
#455=EDGE_LOOP('',(#3206,#3207,#3208,#3209));
#456=EDGE_LOOP('',(#3210,#3211,#3212,#3213));
#457=EDGE_LOOP('',(#3214,#3215,#3216,#3217));
#458=EDGE_LOOP('',(#3218,#3219,#3220,#3221));
#459=EDGE_LOOP('',(#3222,#3223,#3224,#3225));
#460=EDGE_LOOP('',(#3226,#3227,#3228,#3229));
#461=EDGE_LOOP('',(#3230,#3231,#3232,#3233));
#462=EDGE_LOOP('',(#3234,#3235,#3236,#3237));
#463=EDGE_LOOP('',(#3238,#3239,#3240,#3241));
#464=EDGE_LOOP('',(#3242,#3243,#3244,#3245));
#465=EDGE_LOOP('',(#3246,#3247,#3248,#3249));
#466=EDGE_LOOP('',(#3250,#3251,#3252,#3253));
#467=EDGE_LOOP('',(#3254,#3255,#3256,#3257));
#468=EDGE_LOOP('',(#3258,#3259,#3260,#3261));
#469=EDGE_LOOP('',(#3262,#3263,#3264,#3265));
#470=EDGE_LOOP('',(#3266,#3267,#3268,#3269));
#471=EDGE_LOOP('',(#3270,#3271,#3272,#3273));
#472=EDGE_LOOP('',(#3274,#3275,#3276,#3277));
#473=EDGE_LOOP('',(#3278,#3279,#3280,#3281));
#474=EDGE_LOOP('',(#3282,#3283,#3284,#3285));
#475=EDGE_LOOP('',(#3286,#3287,#3288,#3289));
#476=EDGE_LOOP('',(#3290,#3291,#3292,#3293));
#477=EDGE_LOOP('',(#3294,#3295,#3296,#3297));
#478=EDGE_LOOP('',(#3298,#3299,#3300,#3301));
#479=EDGE_LOOP('',(#3302,#3303,#3304,#3305));
#480=EDGE_LOOP('',(#3306,#3307,#3308,#3309));
#481=EDGE_LOOP('',(#3310,#3311,#3312,#3313));
#482=EDGE_LOOP('',(#3314,#3315,#3316,#3317));
#483=EDGE_LOOP('',(#3318,#3319,#3320,#3321));
#484=EDGE_LOOP('',(#3322,#3323,#3324,#3325));
#485=EDGE_LOOP('',(#3326,#3327,#3328,#3329));
#486=EDGE_LOOP('',(#3330,#3331,#3332,#3333));
#487=EDGE_LOOP('',(#3334,#3335,#3336,#3337));
#488=EDGE_LOOP('',(#3338,#3339,#3340,#3341));
#489=EDGE_LOOP('',(#3342,#3343,#3344,#3345));
#490=EDGE_LOOP('',(#3346,#3347,#3348,#3349));
#491=EDGE_LOOP('',(#3350,#3351,#3352,#3353));
#492=EDGE_LOOP('',(#3354,#3355,#3356,#3357));
#493=EDGE_LOOP('',(#3358,#3359,#3360,#3361));
#494=EDGE_LOOP('',(#3362,#3363,#3364,#3365));
#495=EDGE_LOOP('',(#3366,#3367,#3368,#3369));
#496=EDGE_LOOP('',(#3370,#3371,#3372,#3373));
#497=EDGE_LOOP('',(#3374,#3375,#3376,#3377,#3378));
#498=EDGE_LOOP('',(#3379,#3380,#3381,#3382));
#499=EDGE_LOOP('',(#3383,#3384,#3385,#3386));
#500=EDGE_LOOP('',(#3387,#3388,#3389,#3390));
#501=EDGE_LOOP('',(#3391,#3392,#3393,#3394));
#502=EDGE_LOOP('',(#3395,#3396,#3397,#3398));
#503=EDGE_LOOP('',(#3399,#3400,#3401,#3402));
#504=EDGE_LOOP('',(#3403,#3404,#3405,#3406));
#505=EDGE_LOOP('',(#3407,#3408,#3409,#3410));
#506=EDGE_LOOP('',(#3411,#3412,#3413,#3414));
#507=EDGE_LOOP('',(#3415,#3416,#3417,#3418));
#508=EDGE_LOOP('',(#3419,#3420,#3421,#3422));
#509=EDGE_LOOP('',(#3423,#3424,#3425,#3426));
#510=EDGE_LOOP('',(#3427,#3428,#3429,#3430));
#511=EDGE_LOOP('',(#3431,#3432,#3433,#3434));
#512=EDGE_LOOP('',(#3435,#3436,#3437,#3438));
#513=EDGE_LOOP('',(#3439,#3440,#3441,#3442));
#514=EDGE_LOOP('',(#3443,#3444,#3445,#3446));
#515=EDGE_LOOP('',(#3447,#3448,#3449,#3450));
#516=EDGE_LOOP('',(#3451,#3452,#3453,#3454));
#517=EDGE_LOOP('',(#3455,#3456,#3457,#3458));
#518=EDGE_LOOP('',(#3459,#3460,#3461,#3462));
#519=EDGE_LOOP('',(#3463,#3464,#3465,#3466));
#520=EDGE_LOOP('',(#3467,#3468,#3469,#3470));
#521=EDGE_LOOP('',(#3471,#3472,#3473,#3474));
#522=EDGE_LOOP('',(#3475,#3476,#3477,#3478));
#523=EDGE_LOOP('',(#3479,#3480,#3481,#3482));
#524=EDGE_LOOP('',(#3483,#3484,#3485,#3486));
#525=EDGE_LOOP('',(#3487,#3488,#3489,#3490));
#526=EDGE_LOOP('',(#3491,#3492,#3493,#3494));
#527=EDGE_LOOP('',(#3495,#3496,#3497,#3498));
#528=EDGE_LOOP('',(#3499,#3500,#3501,#3502));
#529=EDGE_LOOP('',(#3503,#3504,#3505,#3506));
#530=EDGE_LOOP('',(#3507,#3508,#3509,#3510));
#531=EDGE_LOOP('',(#3511,#3512,#3513,#3514));
#532=EDGE_LOOP('',(#3515,#3516,#3517,#3518));
#533=EDGE_LOOP('',(#3519,#3520,#3521,#3522));
#534=EDGE_LOOP('',(#3523,#3524,#3525,#3526));
#535=EDGE_LOOP('',(#3527,#3528,#3529,#3530));
#536=EDGE_LOOP('',(#3531,#3532,#3533,#3534));
#537=EDGE_LOOP('',(#3535,#3536,#3537,#3538));
#538=EDGE_LOOP('',(#3539,#3540,#3541,#3542));
#539=EDGE_LOOP('',(#3543,#3544,#3545,#3546));
#540=EDGE_LOOP('',(#3547,#3548,#3549,#3550));
#541=EDGE_LOOP('',(#3551,#3552,#3553,#3554));
#542=EDGE_LOOP('',(#3555,#3556,#3557,#3558));
#543=EDGE_LOOP('',(#3559,#3560,#3561,#3562));
#544=EDGE_LOOP('',(#3563,#3564,#3565,#3566));
#545=EDGE_LOOP('',(#3567,#3568,#3569,#3570));
#546=EDGE_LOOP('',(#3571,#3572,#3573,#3574,#3575,#3576,#3577,#3578,#3579,
#3580,#3581,#3582,#3583,#3584,#3585,#3586,#3587,#3588,#3589,#3590,#3591,
#3592,#3593,#3594,#3595,#3596,#3597,#3598,#3599,#3600,#3601,#3602,#3603,
#3604,#3605,#3606,#3607));
#547=EDGE_LOOP('',(#3608,#3609,#3610,#3611));
#548=CIRCLE('',#3984,2.99999999999997);
#549=CIRCLE('',#3985,2.99999999999997);
#550=CIRCLE('',#3986,0.5);
#551=CIRCLE('',#3987,0.499999999999829);
#552=CIRCLE('',#3988,1.);
#553=CIRCLE('',#3990,6.);
#554=CIRCLE('',#3991,2.99999999999997);
#555=CIRCLE('',#3993,6.00000000000001);
#556=CIRCLE('',#3997,3.00000000000001);
#557=CIRCLE('',#3998,3.00000000000004);
#558=CIRCLE('',#3999,3.00000000000002);
#559=CIRCLE('',#4000,3.00000000000002);
#560=CIRCLE('',#4001,15.);
#561=CIRCLE('',#4002,15.);
#562=CIRCLE('',#4003,1.);
#563=CIRCLE('',#4004,15.);
#564=CIRCLE('',#4005,15.0000000000001);
#565=CIRCLE('',#4006,15.);
#566=CIRCLE('',#4007,1.);
#567=CIRCLE('',#4008,1.);
#568=CIRCLE('',#4009,1.00000000000002);
#569=CIRCLE('',#4010,1.00000000000001);
#570=CIRCLE('',#4013,3.);
#571=CIRCLE('',#4016,3.);
#572=CIRCLE('',#4017,6.00000000000001);
#573=CIRCLE('',#4019,2.99999999999997);
#574=CIRCLE('',#4023,1.);
#575=CIRCLE('',#4024,0.499999999999829);
#576=CIRCLE('',#4025,0.5);
#577=CIRCLE('',#4026,2.99999999999997);
#578=CIRCLE('',#4027,2.99999999999997);
#579=CIRCLE('',#4030,6.);
#580=CIRCLE('',#4033,3.);
#581=CIRCLE('',#4034,6.00000000000001);
#582=CIRCLE('',#4036,2.99999999999997);
#583=CIRCLE('',#4042,6.);
#584=CIRCLE('',#4044,2.99999999999997);
#585=CIRCLE('',#4045,0.999999999999659);
#586=CIRCLE('',#4046,0.999999999999983);
#587=CIRCLE('',#4047,0.999999999999839);
#588=CIRCLE('',#4049,6.);
#589=CIRCLE('',#4050,2.99999999999997);
#590=CIRCLE('',#4052,5.99999999999999);
#591=CIRCLE('',#4054,2.99999999999997);
#592=CIRCLE('',#4059,3.);
#593=CIRCLE('',#4061,0.999999999999996);
#594=CIRCLE('',#4062,1.00000000000014);
#595=CIRCLE('',#4064,6.);
#596=CIRCLE('',#4065,2.99999999999997);
#597=CIRCLE('',#4067,5.99999999999994);
#598=CIRCLE('',#4071,0.999999999999839);
#599=CIRCLE('',#4072,0.999999999999983);
#600=CIRCLE('',#4073,0.999999999999659);
#601=CIRCLE('',#4076,2.99999999999997);
#602=CIRCLE('',#4078,2.99999999999997);
#603=CIRCLE('',#4079,0.499999999999829);
#604=CIRCLE('',#4080,0.50000000000006);
#605=CIRCLE('',#4081,0.500000000000114);
#606=CIRCLE('',#4082,0.499999999999972);
#607=CIRCLE('',#4083,0.999999999999885);
#608=CIRCLE('',#4084,0.999999999999943);
#609=CIRCLE('',#4085,0.5);
#610=CIRCLE('',#4086,0.5);
#611=CIRCLE('',#4087,0.499999999999972);
#612=CIRCLE('',#4088,0.499999999999972);
#613=CIRCLE('',#4090,6.);
#614=CIRCLE('',#4091,3.);
#615=CIRCLE('',#4093,6.);
#616=CIRCLE('',#4097,1.00000000000014);
#617=CIRCLE('',#4098,0.999999999999996);
#618=CIRCLE('',#4101,3.);
#619=CIRCLE('',#4104,6.);
#620=CIRCLE('',#4105,3.);
#621=CIRCLE('',#4107,6.);
#622=CIRCLE('',#4111,0.499999999999972);
#623=CIRCLE('',#4112,0.499999999999972);
#624=CIRCLE('',#4113,0.5);
#625=CIRCLE('',#4114,0.5);
#626=CIRCLE('',#4115,0.999999999999943);
#627=CIRCLE('',#4116,0.999999999999885);
#628=CIRCLE('',#4117,0.499999999999972);
#629=CIRCLE('',#4118,0.500000000000114);
#630=CIRCLE('',#4119,0.50000000000006);
#631=CIRCLE('',#4120,0.499999999999829);
#632=CIRCLE('',#4121,2.99999999999997);
#633=CIRCLE('',#4124,3.);
#634=CIRCLE('',#4126,2.99999999999999);
#635=CIRCLE('',#4127,2.99999999999997);
#636=CIRCLE('',#4128,1.00000000000001);
#637=CIRCLE('',#4129,1.00000000000015);
#638=CIRCLE('',#4130,1.00000000000009);
#639=CIRCLE('',#4131,2.00000000000006);
#640=CIRCLE('',#4132,1.00000000000005);
#641=CIRCLE('',#4133,1.00000000000001);
#642=CIRCLE('',#4134,0.999999999999943);
#643=CIRCLE('',#4135,0.999999999999996);
#644=CIRCLE('',#4136,1.99999999999999);
#645=CIRCLE('',#4137,1.99999999999999);
#646=CIRCLE('',#4139,6.);
#647=CIRCLE('',#4140,3.);
#648=CIRCLE('',#4142,6.);
#649=CIRCLE('',#4148,3.);
#650=CIRCLE('',#4150,0.499999999999998);
#651=CIRCLE('',#4151,0.499999999999998);
#652=CIRCLE('',#4152,0.499999999999998);
#653=CIRCLE('',#4153,0.499999999999998);
#654=CIRCLE('',#4154,0.499999999999998);
#655=CIRCLE('',#4155,0.999999999999997);
#656=CIRCLE('',#4156,0.999999999999996);
#657=CIRCLE('',#4157,0.499999999999994);
#658=CIRCLE('',#4158,0.499999999999998);
#659=CIRCLE('',#4159,0.499999999999998);
#660=CIRCLE('',#4160,0.499999999999998);
#661=CIRCLE('',#4161,0.999999999999997);
#662=CIRCLE('',#4163,6.);
#663=CIRCLE('',#4164,3.);
#664=CIRCLE('',#4166,6.);
#665=CIRCLE('',#4170,1.99999999999999);
#666=CIRCLE('',#4171,1.99999999999999);
#667=CIRCLE('',#4172,0.999999999999996);
#668=CIRCLE('',#4173,0.999999999999943);
#669=CIRCLE('',#4174,1.00000000000001);
#670=CIRCLE('',#4175,1.00000000000005);
#671=CIRCLE('',#4176,2.00000000000006);
#672=CIRCLE('',#4177,1.00000000000009);
#673=CIRCLE('',#4178,1.00000000000015);
#674=CIRCLE('',#4179,1.00000000000001);
#675=CIRCLE('',#4180,2.99999999999997);
#676=CIRCLE('',#4181,2.99999999999999);
#677=CIRCLE('',#4184,3.);
#678=CIRCLE('',#4187,3.);
#679=CIRCLE('',#4188,3.);
#680=CIRCLE('',#4190,6.);
#681=CIRCLE('',#4192,1.);
#682=CIRCLE('',#4193,1.);
#683=CIRCLE('',#4195,1.);
#684=CIRCLE('',#4196,1.);
#685=CIRCLE('',#4199,0.999999999999997);
#686=CIRCLE('',#4200,0.499999999999998);
#687=CIRCLE('',#4201,0.499999999999998);
#688=CIRCLE('',#4202,0.499999999999998);
#689=CIRCLE('',#4203,0.499999999999994);
#690=CIRCLE('',#4204,0.999999999999996);
#691=CIRCLE('',#4205,0.999999999999997);
#692=CIRCLE('',#4206,0.499999999999998);
#693=CIRCLE('',#4207,0.499999999999998);
#694=CIRCLE('',#4208,0.499999999999998);
#695=CIRCLE('',#4209,0.499999999999998);
#696=CIRCLE('',#4210,0.499999999999998);
#697=CIRCLE('',#4212,6.);
#698=CIRCLE('',#4238,1.00000000000002);
#699=CIRCLE('',#4241,1.);
#700=CIRCLE('',#4244,1.);
#701=CIRCLE('',#4247,15.);
#702=CIRCLE('',#4250,15.0000000000001);
#703=CIRCLE('',#4253,15.);
#704=CIRCLE('',#4260,1.);
#705=CIRCLE('',#4262,15.);
#706=CIRCLE('',#4265,15.);
#707=CIRCLE('',#4268,3.00000000000002);
#708=CIRCLE('',#4295,3.00000000000002);
#709=CIRCLE('',#4298,3.00000000000004);
#710=CIRCLE('',#4303,3.00000000000001);
#711=CIRCLE('',#4350,1.00000000000001);
#712=LINE('',#5547,#1121);
#713=LINE('',#5549,#1122);
#714=LINE('',#5553,#1123);
#715=LINE('',#5557,#1124);
#716=LINE('',#5561,#1125);
#717=LINE('',#5565,#1126);
#718=LINE('',#5569,#1127);
#719=LINE('',#5571,#1128);
#720=LINE('',#5572,#1129);
#721=LINE('',#5575,#1130);
#722=LINE('',#5579,#1131);
#723=LINE('',#5583,#1132);
#724=LINE('',#5586,#1133);
#725=LINE('',#5588,#1134);
#726=LINE('',#5590,#1135);
#727=LINE('',#5591,#1136);
#728=LINE('',#5594,#1137);
#729=LINE('',#5595,#1138);
#730=LINE('',#5598,#1139);
#731=LINE('',#5602,#1140);
#732=LINE('',#5604,#1141);
#733=LINE('',#5606,#1142);
#734=LINE('',#5610,#1143);
#735=LINE('',#5614,#1144);
#736=LINE('',#5616,#1145);
#737=LINE('',#5618,#1146);
#738=LINE('',#5622,#1147);
#739=LINE('',#5626,#1148);
#740=LINE('',#5632,#1149);
#741=LINE('',#5634,#1150);
#742=LINE('',#5636,#1151);
#743=LINE('',#5640,#1152);
#744=LINE('',#5644,#1153);
#745=LINE('',#5648,#1154);
#746=LINE('',#5652,#1155);
#747=LINE('',#5656,#1156);
#748=LINE('',#5660,#1157);
#749=LINE('',#5662,#1158);
#750=LINE('',#5664,#1159);
#751=LINE('',#5667,#1160);
#752=LINE('',#5670,#1161);
#753=LINE('',#5672,#1162);
#754=LINE('',#5673,#1163);
#755=LINE('',#5675,#1164);
#756=LINE('',#5680,#1165);
#757=LINE('',#5682,#1166);
#758=LINE('',#5684,#1167);
#759=LINE('',#5685,#1168);
#760=LINE('',#5688,#1169);
#761=LINE('',#5693,#1170);
#762=LINE('',#5696,#1171);
#763=LINE('',#5698,#1172);
#764=LINE('',#5700,#1173);
#765=LINE('',#5701,#1174);
#766=LINE('',#5704,#1175);
#767=LINE('',#5705,#1176);
#768=LINE('',#5708,#1177);
#769=LINE('',#5712,#1178);
#770=LINE('',#5716,#1179);
#771=LINE('',#5720,#1180);
#772=LINE('',#5724,#1181);
#773=LINE('',#5727,#1182);
#774=LINE('',#5728,#1183);
#775=LINE('',#5734,#1184);
#776=LINE('',#5736,#1185);
#777=LINE('',#5738,#1186);
#778=LINE('',#5739,#1187);
#779=LINE('',#5742,#1188);
#780=LINE('',#5747,#1189);
#781=LINE('',#5749,#1190);
#782=LINE('',#5751,#1191);
#783=LINE('',#5753,#1192);
#784=LINE('',#5754,#1193);
#785=LINE('',#5757,#1194);
#786=LINE('',#5758,#1195);
#787=LINE('',#5766,#1196);
#788=LINE('',#5770,#1197);
#789=LINE('',#5772,#1198);
#790=LINE('',#5774,#1199);
#791=LINE('',#5778,#1200);
#792=LINE('',#5780,#1201);
#793=LINE('',#5782,#1202);
#794=LINE('',#5786,#1203);
#795=LINE('',#5789,#1204);
#796=LINE('',#5792,#1205);
#797=LINE('',#5797,#1206);
#798=LINE('',#5804,#1207);
#799=LINE('',#5807,#1208);
#800=LINE('',#5808,#1209);
#801=LINE('',#5811,#1210);
#802=LINE('',#5814,#1211);
#803=LINE('',#5815,#1212);
#804=LINE('',#5828,#1213);
#805=LINE('',#5830,#1214);
#806=LINE('',#5834,#1215);
#807=LINE('',#5837,#1216);
#808=LINE('',#5840,#1217);
#809=LINE('',#5843,#1218);
#810=LINE('',#5847,#1219);
#811=LINE('',#5850,#1220);
#812=LINE('',#5852,#1221);
#813=LINE('',#5854,#1222);
#814=LINE('',#5855,#1223);
#815=LINE('',#5858,#1224);
#816=LINE('',#5859,#1225);
#817=LINE('',#5864,#1226);
#818=LINE('',#5867,#1227);
#819=LINE('',#5869,#1228);
#820=LINE('',#5873,#1229);
#821=LINE('',#5875,#1230);
#822=LINE('',#5876,#1231);
#823=LINE('',#5878,#1232);
#824=LINE('',#5879,#1233);
#825=LINE('',#5885,#1234);
#826=LINE('',#5887,#1235);
#827=LINE('',#5891,#1236);
#828=LINE('',#5895,#1237);
#829=LINE('',#5899,#1238);
#830=LINE('',#5903,#1239);
#831=LINE('',#5907,#1240);
#832=LINE('',#5911,#1241);
#833=LINE('',#5915,#1242);
#834=LINE('',#5919,#1243);
#835=LINE('',#5923,#1244);
#836=LINE('',#5927,#1245);
#837=LINE('',#5931,#1246);
#838=LINE('',#5932,#1247);
#839=LINE('',#5935,#1248);
#840=LINE('',#5937,#1249);
#841=LINE('',#5941,#1250);
#842=LINE('',#5944,#1251);
#843=LINE('',#5946,#1252);
#844=LINE('',#5947,#1253);
#845=LINE('',#5950,#1254);
#846=LINE('',#5951,#1255);
#847=LINE('',#5956,#1256);
#848=LINE('',#5959,#1257);
#849=LINE('',#5965,#1258);
#850=LINE('',#5967,#1259);
#851=LINE('',#5969,#1260);
#852=LINE('',#5970,#1261);
#853=LINE('',#5973,#1262);
#854=LINE('',#5976,#1263);
#855=LINE('',#5980,#1264);
#856=LINE('',#5983,#1265);
#857=LINE('',#5985,#1266);
#858=LINE('',#5986,#1267);
#859=LINE('',#5989,#1268);
#860=LINE('',#5990,#1269);
#861=LINE('',#5993,#1270);
#862=LINE('',#5997,#1271);
#863=LINE('',#6001,#1272);
#864=LINE('',#6005,#1273);
#865=LINE('',#6009,#1274);
#866=LINE('',#6013,#1275);
#867=LINE('',#6017,#1276);
#868=LINE('',#6021,#1277);
#869=LINE('',#6025,#1278);
#870=LINE('',#6029,#1279);
#871=LINE('',#6033,#1280);
#872=LINE('',#6037,#1281);
#873=LINE('',#6039,#1282);
#874=LINE('',#6040,#1283);
#875=LINE('',#6042,#1284);
#876=LINE('',#6047,#1285);
#877=LINE('',#6049,#1286);
#878=LINE('',#6053,#1287);
#879=LINE('',#6057,#1288);
#880=LINE('',#6061,#1289);
#881=LINE('',#6065,#1290);
#882=LINE('',#6069,#1291);
#883=LINE('',#6073,#1292);
#884=LINE('',#6077,#1293);
#885=LINE('',#6081,#1294);
#886=LINE('',#6085,#1295);
#887=LINE('',#6089,#1296);
#888=LINE('',#6093,#1297);
#889=LINE('',#6096,#1298);
#890=LINE('',#6099,#1299);
#891=LINE('',#6101,#1300);
#892=LINE('',#6103,#1301);
#893=LINE('',#6105,#1302);
#894=LINE('',#6107,#1303);
#895=LINE('',#6108,#1304);
#896=LINE('',#6111,#1305);
#897=LINE('',#6113,#1306);
#898=LINE('',#6115,#1307);
#899=LINE('',#6117,#1308);
#900=LINE('',#6119,#1309);
#901=LINE('',#6120,#1310);
#902=LINE('',#6123,#1311);
#903=LINE('',#6126,#1312);
#904=LINE('',#6130,#1313);
#905=LINE('',#6133,#1314);
#906=LINE('',#6135,#1315);
#907=LINE('',#6136,#1316);
#908=LINE('',#6139,#1317);
#909=LINE('',#6140,#1318);
#910=LINE('',#6142,#1319);
#911=LINE('',#6145,#1320);
#912=LINE('',#6147,#1321);
#913=LINE('',#6148,#1322);
#914=LINE('',#6150,#1323);
#915=LINE('',#6155,#1324);
#916=LINE('',#6157,#1325);
#917=LINE('',#6161,#1326);
#918=LINE('',#6165,#1327);
#919=LINE('',#6169,#1328);
#920=LINE('',#6173,#1329);
#921=LINE('',#6177,#1330);
#922=LINE('',#6181,#1331);
#923=LINE('',#6185,#1332);
#924=LINE('',#6189,#1333);
#925=LINE('',#6193,#1334);
#926=LINE('',#6197,#1335);
#927=LINE('',#6201,#1336);
#928=LINE('',#6204,#1337);
#929=LINE('',#6207,#1338);
#930=LINE('',#6210,#1339);
#931=LINE('',#6214,#1340);
#932=LINE('',#6217,#1341);
#933=LINE('',#6219,#1342);
#934=LINE('',#6220,#1343);
#935=LINE('',#6223,#1344);
#936=LINE('',#6224,#1345);
#937=LINE('',#6229,#1346);
#938=LINE('',#6233,#1347);
#939=LINE('',#6237,#1348);
#940=LINE('',#6241,#1349);
#941=LINE('',#6245,#1350);
#942=LINE('',#6249,#1351);
#943=LINE('',#6253,#1352);
#944=LINE('',#6257,#1353);
#945=LINE('',#6261,#1354);
#946=LINE('',#6265,#1355);
#947=LINE('',#6269,#1356);
#948=LINE('',#6273,#1357);
#949=LINE('',#6275,#1358);
#950=LINE('',#6277,#1359);
#951=LINE('',#6279,#1360);
#952=LINE('',#6281,#1361);
#953=LINE('',#6282,#1362);
#954=LINE('',#6285,#1363);
#955=LINE('',#6287,#1364);
#956=LINE('',#6289,#1365);
#957=LINE('',#6291,#1366);
#958=LINE('',#6293,#1367);
#959=LINE('',#6294,#1368);
#960=LINE('',#6297,#1369);
#961=LINE('',#6299,#1370);
#962=LINE('',#6300,#1371);
#963=LINE('',#6302,#1372);
#964=LINE('',#6307,#1373);
#965=LINE('',#6309,#1374);
#966=LINE('',#6311,#1375);
#967=LINE('',#6312,#1376);
#968=LINE('',#6315,#1377);
#969=LINE('',#6319,#1378);
#970=LINE('',#6323,#1379);
#971=LINE('',#6325,#1380);
#972=LINE('',#6327,#1381);
#973=LINE('',#6329,#1382);
#974=LINE('',#6333,#1383);
#975=LINE('',#6339,#1384);
#976=LINE('',#6342,#1385);
#977=LINE('',#6345,#1386);
#978=LINE('',#6346,#1387);
#979=LINE('',#6351,#1388);
#980=LINE('',#6355,#1389);
#981=LINE('',#6359,#1390);
#982=LINE('',#6363,#1391);
#983=LINE('',#6367,#1392);
#984=LINE('',#6371,#1393);
#985=LINE('',#6375,#1394);
#986=LINE('',#6379,#1395);
#987=LINE('',#6383,#1396);
#988=LINE('',#6387,#1397);
#989=LINE('',#6391,#1398);
#990=LINE('',#6394,#1399);
#991=LINE('',#6397,#1400);
#992=LINE('',#6398,#1401);
#993=LINE('',#6400,#1402);
#994=LINE('',#6402,#1403);
#995=LINE('',#6404,#1404);
#996=LINE('',#6406,#1405);
#997=LINE('',#6409,#1406);
#998=LINE('',#6410,#1407);
#999=LINE('',#6412,#1408);
#1000=LINE('',#6414,#1409);
#1001=LINE('',#6416,#1410);
#1002=LINE('',#6418,#1411);
#1003=LINE('',#6421,#1412);
#1004=LINE('',#6422,#1413);
#1005=LINE('',#6424,#1414);
#1006=LINE('',#6426,#1415);
#1007=LINE('',#6428,#1416);
#1008=LINE('',#6430,#1417);
#1009=LINE('',#6432,#1418);
#1010=LINE('',#6434,#1419);
#1011=LINE('',#6436,#1420);
#1012=LINE('',#6438,#1421);
#1013=LINE('',#6440,#1422);
#1014=LINE('',#6444,#1423);
#1015=LINE('',#6445,#1424);
#1016=LINE('',#6449,#1425);
#1017=LINE('',#6452,#1426);
#1018=LINE('',#6453,#1427);
#1019=LINE('',#6457,#1428);
#1020=LINE('',#6460,#1429);
#1021=LINE('',#6461,#1430);
#1022=LINE('',#6465,#1431);
#1023=LINE('',#6468,#1432);
#1024=LINE('',#6469,#1433);
#1025=LINE('',#6473,#1434);
#1026=LINE('',#6476,#1435);
#1027=LINE('',#6477,#1436);
#1028=LINE('',#6481,#1437);
#1029=LINE('',#6484,#1438);
#1030=LINE('',#6485,#1439);
#1031=LINE('',#6489,#1440);
#1032=LINE('',#6491,#1441);
#1033=LINE('',#6493,#1442);
#1034=LINE('',#6495,#1443);
#1035=LINE('',#6497,#1444);
#1036=LINE('',#6502,#1445);
#1037=LINE('',#6506,#1446);
#1038=LINE('',#6509,#1447);
#1039=LINE('',#6510,#1448);
#1040=LINE('',#6514,#1449);
#1041=LINE('',#6517,#1450);
#1042=LINE('',#6518,#1451);
#1043=LINE('',#6523,#1452);
#1044=LINE('',#6525,#1453);
#1045=LINE('',#6527,#1454);
#1046=LINE('',#6529,#1455);
#1047=LINE('',#6531,#1456);
#1048=LINE('',#6533,#1457);
#1049=LINE('',#6535,#1458);
#1050=LINE('',#6537,#1459);
#1051=LINE('',#6539,#1460);
#1052=LINE('',#6541,#1461);
#1053=LINE('',#6543,#1462);
#1054=LINE('',#6545,#1463);
#1055=LINE('',#6547,#1464);
#1056=LINE('',#6549,#1465);
#1057=LINE('',#6551,#1466);
#1058=LINE('',#6553,#1467);
#1059=LINE('',#6555,#1468);
#1060=LINE('',#6557,#1469);
#1061=LINE('',#6559,#1470);
#1062=LINE('',#6561,#1471);
#1063=LINE('',#6563,#1472);
#1064=LINE('',#6565,#1473);
#1065=LINE('',#6569,#1474);
#1066=LINE('',#6570,#1475);
#1067=LINE('',#6574,#1476);
#1068=LINE('',#6577,#1477);
#1069=LINE('',#6578,#1478);
#1070=LINE('',#6585,#1479);
#1071=LINE('',#6586,#1480);
#1072=LINE('',#6591,#1481);
#1073=LINE('',#6593,#1482);
#1074=LINE('',#6595,#1483);
#1075=LINE('',#6597,#1484);
#1076=LINE('',#6599,#1485);
#1077=LINE('',#6601,#1486);
#1078=LINE('',#6603,#1487);
#1079=LINE('',#6605,#1488);
#1080=LINE('',#6608,#1489);
#1081=LINE('',#6627,#1490);
#1082=LINE('',#6629,#1491);
#1083=LINE('',#6631,#1492);
#1084=LINE('',#6633,#1493);
#1085=LINE('',#6635,#1494);
#1086=LINE('',#6637,#1495);
#1087=LINE('',#6639,#1496);
#1088=LINE('',#6641,#1497);
#1089=LINE('',#6643,#1498);
#1090=LINE('',#6645,#1499);
#1091=LINE('',#6647,#1500);
#1092=LINE('',#6649,#1501);
#1093=LINE('',#6651,#1502);
#1094=LINE('',#6653,#1503);
#1095=LINE('',#6655,#1504);
#1096=LINE('',#6657,#1505);
#1097=LINE('',#6659,#1506);
#1098=LINE('',#6661,#1507);
#1099=LINE('',#6663,#1508);
#1100=LINE('',#6665,#1509);
#1101=LINE('',#6667,#1510);
#1102=LINE('',#6670,#1511);
#1103=LINE('',#6674,#1512);
#1104=LINE('',#6676,#1513);
#1105=LINE('',#6679,#1514);
#1106=LINE('',#6681,#1515);
#1107=LINE('',#6683,#1516);
#1108=LINE('',#6687,#1517);
#1109=LINE('',#6688,#1518);
#1110=LINE('',#6693,#1519);
#1111=LINE('',#6695,#1520);
#1112=LINE('',#6697,#1521);
#1113=LINE('',#6699,#1522);
#1114=LINE('',#6701,#1523);
#1115=LINE('',#6703,#1524);
#1116=LINE('',#6705,#1525);
#1117=LINE('',#6707,#1526);
#1118=LINE('',#6709,#1527);
#1119=LINE('',#6711,#1528);
#1120=LINE('',#6713,#1529);
#1121=VECTOR('',#4370,10.);
#1122=VECTOR('',#4371,10.);
#1123=VECTOR('',#4374,10.);
#1124=VECTOR('',#4377,10.);
#1125=VECTOR('',#4380,10.);
#1126=VECTOR('',#4383,10.);
#1127=VECTOR('',#4386,10.);
#1128=VECTOR('',#4387,10.);
#1129=VECTOR('',#4388,10.);
#1130=VECTOR('',#4391,10.);
#1131=VECTOR('',#4394,10.);
#1132=VECTOR('',#4399,10.);
#1133=VECTOR('',#4402,10.);
#1134=VECTOR('',#4405,10.);
#1135=VECTOR('',#4406,10.);
#1136=VECTOR('',#4407,10.);
#1137=VECTOR('',#4410,10.);
#1138=VECTOR('',#4411,10.);
#1139=VECTOR('',#4414,10.);
#1140=VECTOR('',#4417,10.);
#1141=VECTOR('',#4418,10.);
#1142=VECTOR('',#4419,10.);
#1143=VECTOR('',#4422,10.);
#1144=VECTOR('',#4425,10.);
#1145=VECTOR('',#4426,10.);
#1146=VECTOR('',#4427,10.);
#1147=VECTOR('',#4430,10.);
#1148=VECTOR('',#4433,10.);
#1149=VECTOR('',#4438,10.);
#1150=VECTOR('',#4439,10.);
#1151=VECTOR('',#4440,10.);
#1152=VECTOR('',#4443,10.);
#1153=VECTOR('',#4446,10.);
#1154=VECTOR('',#4449,10.);
#1155=VECTOR('',#4452,10.);
#1156=VECTOR('',#4455,10.);
#1157=VECTOR('',#4458,10.);
#1158=VECTOR('',#4459,10.);
#1159=VECTOR('',#4460,10.);
#1160=VECTOR('',#4463,10.);
#1161=VECTOR('',#4466,10.);
#1162=VECTOR('',#4467,10.);
#1163=VECTOR('',#4468,10.);
#1164=VECTOR('',#4471,10.);
#1165=VECTOR('',#4476,10.);
#1166=VECTOR('',#4477,10.);
#1167=VECTOR('',#4478,10.);
#1168=VECTOR('',#4479,10.);
#1169=VECTOR('',#4482,10.);
#1170=VECTOR('',#4489,10.);
#1171=VECTOR('',#4492,10.);
#1172=VECTOR('',#4495,10.);
#1173=VECTOR('',#4496,10.);
#1174=VECTOR('',#4497,10.);
#1175=VECTOR('',#4500,10.);
#1176=VECTOR('',#4501,10.);
#1177=VECTOR('',#4504,10.);
#1178=VECTOR('',#4507,10.);
#1179=VECTOR('',#4510,10.);
#1180=VECTOR('',#4513,10.);
#1181=VECTOR('',#4516,10.);
#1182=VECTOR('',#4521,10.);
#1183=VECTOR('',#4522,10.);
#1184=VECTOR('',#4529,10.);
#1185=VECTOR('',#4530,10.);
#1186=VECTOR('',#4531,10.);
#1187=VECTOR('',#4532,10.);
#1188=VECTOR('',#4535,10.);
#1189=VECTOR('',#4542,10.);
#1190=VECTOR('',#4545,10.);
#1191=VECTOR('',#4548,10.);
#1192=VECTOR('',#4549,10.);
#1193=VECTOR('',#4550,10.);
#1194=VECTOR('',#4553,10.);
#1195=VECTOR('',#4554,10.);
#1196=VECTOR('',#4565,10.);
#1197=VECTOR('',#4568,10.);
#1198=VECTOR('',#4569,10.);
#1199=VECTOR('',#4570,10.);
#1200=VECTOR('',#4573,10.);
#1201=VECTOR('',#4574,10.);
#1202=VECTOR('',#4575,10.);
#1203=VECTOR('',#4578,10.);
#1204=VECTOR('',#4581,10.);
#1205=VECTOR('',#4584,10.);
#1206=VECTOR('',#4591,10.);
#1207=VECTOR('',#4594,10.);
#1208=VECTOR('',#4597,10.);
#1209=VECTOR('',#4598,10.);
#1210=VECTOR('',#4601,10.);
#1211=VECTOR('',#4604,10.);
#1212=VECTOR('',#4605,10.);
#1213=VECTOR('',#4616,10.);
#1214=VECTOR('',#4617,10.);
#1215=VECTOR('',#4620,10.);
#1216=VECTOR('',#4623,10.);
#1217=VECTOR('',#4626,10.);
#1218=VECTOR('',#4629,10.);
#1219=VECTOR('',#4634,10.);
#1220=VECTOR('',#4637,10.);
#1221=VECTOR('',#4640,10.);
#1222=VECTOR('',#4641,10.);
#1223=VECTOR('',#4642,10.);
#1224=VECTOR('',#4645,10.);
#1225=VECTOR('',#4646,10.);
#1226=VECTOR('',#4651,10.);
#1227=VECTOR('',#4654,10.);
#1228=VECTOR('',#4655,10.);
#1229=VECTOR('',#4658,10.);
#1230=VECTOR('',#4659,10.);
#1231=VECTOR('',#4660,10.);
#1232=VECTOR('',#4663,10.);
#1233=VECTOR('',#4664,10.);
#1234=VECTOR('',#4671,10.);
#1235=VECTOR('',#4672,10.);
#1236=VECTOR('',#4675,10.);
#1237=VECTOR('',#4678,10.);
#1238=VECTOR('',#4681,10.);
#1239=VECTOR('',#4684,10.);
#1240=VECTOR('',#4687,10.);
#1241=VECTOR('',#4690,10.);
#1242=VECTOR('',#4693,10.);
#1243=VECTOR('',#4696,10.);
#1244=VECTOR('',#4699,10.);
#1245=VECTOR('',#4702,10.);
#1246=VECTOR('',#4705,10.);
#1247=VECTOR('',#4706,10.);
#1248=VECTOR('',#4709,10.);
#1249=VECTOR('',#4712,10.);
#1250=VECTOR('',#4717,10.);
#1251=VECTOR('',#4722,10.);
#1252=VECTOR('',#4723,10.);
#1253=VECTOR('',#4724,10.);
#1254=VECTOR('',#4727,10.);
#1255=VECTOR('',#4728,10.);
#1256=VECTOR('',#4733,10.);
#1257=VECTOR('',#4738,10.);
#1258=VECTOR('',#4745,10.);
#1259=VECTOR('',#4746,10.);
#1260=VECTOR('',#4747,10.);
#1261=VECTOR('',#4748,10.);
#1262=VECTOR('',#4751,10.);
#1263=VECTOR('',#4754,10.);
#1264=VECTOR('',#4759,10.);
#1265=VECTOR('',#4764,10.);
#1266=VECTOR('',#4765,10.);
#1267=VECTOR('',#4766,10.);
#1268=VECTOR('',#4769,10.);
#1269=VECTOR('',#4770,10.);
#1270=VECTOR('',#4773,10.);
#1271=VECTOR('',#4776,10.);
#1272=VECTOR('',#4779,10.);
#1273=VECTOR('',#4782,10.);
#1274=VECTOR('',#4785,10.);
#1275=VECTOR('',#4788,10.);
#1276=VECTOR('',#4791,10.);
#1277=VECTOR('',#4794,10.);
#1278=VECTOR('',#4797,10.);
#1279=VECTOR('',#4800,10.);
#1280=VECTOR('',#4803,10.);
#1281=VECTOR('',#4808,10.);
#1282=VECTOR('',#4809,10.);
#1283=VECTOR('',#4810,10.);
#1284=VECTOR('',#4813,10.);
#1285=VECTOR('',#4818,10.);
#1286=VECTOR('',#4819,10.);
#1287=VECTOR('',#4822,10.);
#1288=VECTOR('',#4825,10.);
#1289=VECTOR('',#4828,10.);
#1290=VECTOR('',#4831,10.);
#1291=VECTOR('',#4834,10.);
#1292=VECTOR('',#4837,10.);
#1293=VECTOR('',#4840,10.);
#1294=VECTOR('',#4843,10.);
#1295=VECTOR('',#4846,10.);
#1296=VECTOR('',#4849,10.);
#1297=VECTOR('',#4852,10.);
#1298=VECTOR('',#4855,10.);
#1299=VECTOR('',#4856,10.);
#1300=VECTOR('',#4857,10.);
#1301=VECTOR('',#4858,10.);
#1302=VECTOR('',#4859,10.);
#1303=VECTOR('',#4860,10.);
#1304=VECTOR('',#4861,10.);
#1305=VECTOR('',#4862,10.);
#1306=VECTOR('',#4863,10.);
#1307=VECTOR('',#4864,10.);
#1308=VECTOR('',#4865,10.);
#1309=VECTOR('',#4866,10.);
#1310=VECTOR('',#4867,10.);
#1311=VECTOR('',#4870,10.);
#1312=VECTOR('',#4873,10.);
#1313=VECTOR('',#4878,10.);
#1314=VECTOR('',#4883,10.);
#1315=VECTOR('',#4884,10.);
#1316=VECTOR('',#4885,10.);
#1317=VECTOR('',#4888,10.);
#1318=VECTOR('',#4889,10.);
#1319=VECTOR('',#4892,10.);
#1320=VECTOR('',#4895,10.);
#1321=VECTOR('',#4896,10.);
#1322=VECTOR('',#4897,10.);
#1323=VECTOR('',#4900,10.);
#1324=VECTOR('',#4905,10.);
#1325=VECTOR('',#4906,10.);
#1326=VECTOR('',#4909,10.);
#1327=VECTOR('',#4912,10.);
#1328=VECTOR('',#4915,10.);
#1329=VECTOR('',#4918,10.);
#1330=VECTOR('',#4921,10.);
#1331=VECTOR('',#4924,10.);
#1332=VECTOR('',#4927,10.);
#1333=VECTOR('',#4930,10.);
#1334=VECTOR('',#4933,10.);
#1335=VECTOR('',#4936,10.);
#1336=VECTOR('',#4939,10.);
#1337=VECTOR('',#4942,10.);
#1338=VECTOR('',#4945,10.);
#1339=VECTOR('',#4948,10.);
#1340=VECTOR('',#4953,10.);
#1341=VECTOR('',#4958,10.);
#1342=VECTOR('',#4959,10.);
#1343=VECTOR('',#4960,10.);
#1344=VECTOR('',#4963,10.);
#1345=VECTOR('',#4964,10.);
#1346=VECTOR('',#4969,10.);
#1347=VECTOR('',#4972,10.);
#1348=VECTOR('',#4975,10.);
#1349=VECTOR('',#4978,10.);
#1350=VECTOR('',#4981,10.);
#1351=VECTOR('',#4984,10.);
#1352=VECTOR('',#4987,10.);
#1353=VECTOR('',#4990,10.);
#1354=VECTOR('',#4993,10.);
#1355=VECTOR('',#4996,10.);
#1356=VECTOR('',#4999,10.);
#1357=VECTOR('',#5002,10.);
#1358=VECTOR('',#5003,10.);
#1359=VECTOR('',#5004,10.);
#1360=VECTOR('',#5005,10.);
#1361=VECTOR('',#5006,10.);
#1362=VECTOR('',#5007,10.);
#1363=VECTOR('',#5008,10.);
#1364=VECTOR('',#5009,10.);
#1365=VECTOR('',#5010,10.);
#1366=VECTOR('',#5011,10.);
#1367=VECTOR('',#5012,10.);
#1368=VECTOR('',#5013,10.);
#1369=VECTOR('',#5016,10.);
#1370=VECTOR('',#5017,10.);
#1371=VECTOR('',#5018,10.);
#1372=VECTOR('',#5021,10.);
#1373=VECTOR('',#5026,10.);
#1374=VECTOR('',#5027,10.);
#1375=VECTOR('',#5028,10.);
#1376=VECTOR('',#5029,10.);
#1377=VECTOR('',#5032,10.);
#1378=VECTOR('',#5035,10.);
#1379=VECTOR('',#5040,10.);
#1380=VECTOR('',#5043,10.);
#1381=VECTOR('',#5046,10.);
#1382=VECTOR('',#5047,10.);
#1383=VECTOR('',#5050,10.);
#1384=VECTOR('',#5057,10.);
#1385=VECTOR('',#5060,10.);
#1386=VECTOR('',#5063,10.);
#1387=VECTOR('',#5064,10.);
#1388=VECTOR('',#5069,10.);
#1389=VECTOR('',#5072,10.);
#1390=VECTOR('',#5075,10.);
#1391=VECTOR('',#5078,10.);
#1392=VECTOR('',#5081,10.);
#1393=VECTOR('',#5084,10.);
#1394=VECTOR('',#5087,10.);
#1395=VECTOR('',#5090,10.);
#1396=VECTOR('',#5093,10.);
#1397=VECTOR('',#5096,10.);
#1398=VECTOR('',#5099,10.);
#1399=VECTOR('',#5104,10.);
#1400=VECTOR('',#5109,10.);
#1401=VECTOR('',#5110,10.);
#1402=VECTOR('',#5113,10.);
#1403=VECTOR('',#5116,10.);
#1404=VECTOR('',#5119,10.);
#1405=VECTOR('',#5122,10.);
#1406=VECTOR('',#5127,10.);
#1407=VECTOR('',#5128,10.);
#1408=VECTOR('',#5131,10.);
#1409=VECTOR('',#5134,10.);
#1410=VECTOR('',#5137,10.);
#1411=VECTOR('',#5140,10.);
#1412=VECTOR('',#5145,10.);
#1413=VECTOR('',#5146,10.);
#1414=VECTOR('',#5149,10.);
#1415=VECTOR('',#5152,10.);
#1416=VECTOR('',#5155,10.);
#1417=VECTOR('',#5158,10.);
#1418=VECTOR('',#5161,10.);
#1419=VECTOR('',#5164,10.);
#1420=VECTOR('',#5167,10.);
#1421=VECTOR('',#5170,10.);
#1422=VECTOR('',#5173,10.);
#1423=VECTOR('',#5178,10.);
#1424=VECTOR('',#5179,10.);
#1425=VECTOR('',#5184,10.);
#1426=VECTOR('',#5187,10.);
#1427=VECTOR('',#5188,10.);
#1428=VECTOR('',#5193,10.);
#1429=VECTOR('',#5196,10.);
#1430=VECTOR('',#5197,10.);
#1431=VECTOR('',#5202,10.);
#1432=VECTOR('',#5205,10.);
#1433=VECTOR('',#5206,10.);
#1434=VECTOR('',#5211,10.);
#1435=VECTOR('',#5214,10.);
#1436=VECTOR('',#5215,10.);
#1437=VECTOR('',#5220,10.);
#1438=VECTOR('',#5223,10.);
#1439=VECTOR('',#5224,10.);
#1440=VECTOR('',#5229,10.);
#1441=VECTOR('',#5232,10.);
#1442=VECTOR('',#5235,10.);
#1443=VECTOR('',#5238,10.);
#1444=VECTOR('',#5241,10.);
#1445=VECTOR('',#5248,10.);
#1446=VECTOR('',#5253,10.);
#1447=VECTOR('',#5256,10.);
#1448=VECTOR('',#5257,10.);
#1449=VECTOR('',#5262,10.);
#1450=VECTOR('',#5265,10.);
#1451=VECTOR('',#5266,10.);
#1452=VECTOR('',#5275,10.);
#1453=VECTOR('',#5278,10.);
#1454=VECTOR('',#5281,10.);
#1455=VECTOR('',#5284,10.);
#1456=VECTOR('',#5287,10.);
#1457=VECTOR('',#5290,10.);
#1458=VECTOR('',#5293,10.);
#1459=VECTOR('',#5296,10.);
#1460=VECTOR('',#5299,10.);
#1461=VECTOR('',#5302,10.);
#1462=VECTOR('',#5305,10.);
#1463=VECTOR('',#5308,10.);
#1464=VECTOR('',#5311,10.);
#1465=VECTOR('',#5314,10.);
#1466=VECTOR('',#5317,10.);
#1467=VECTOR('',#5320,10.);
#1468=VECTOR('',#5323,10.);
#1469=VECTOR('',#5326,10.);
#1470=VECTOR('',#5329,10.);
#1471=VECTOR('',#5332,10.);
#1472=VECTOR('',#5335,10.);
#1473=VECTOR('',#5338,10.);
#1474=VECTOR('',#5343,10.);
#1475=VECTOR('',#5344,10.);
#1476=VECTOR('',#5349,10.);
#1477=VECTOR('',#5352,10.);
#1478=VECTOR('',#5353,10.);
#1479=VECTOR('',#5364,10.);
#1480=VECTOR('',#5365,10.);
#1481=VECTOR('',#5374,10.);
#1482=VECTOR('',#5377,10.);
#1483=VECTOR('',#5380,10.);
#1484=VECTOR('',#5383,10.);
#1485=VECTOR('',#5386,10.);
#1486=VECTOR('',#5389,10.);
#1487=VECTOR('',#5392,10.);
#1488=VECTOR('',#5395,10.);
#1489=VECTOR('',#5400,10.);
#1490=VECTOR('',#5405,10.);
#1491=VECTOR('',#5408,10.);
#1492=VECTOR('',#5411,10.);
#1493=VECTOR('',#5414,10.);
#1494=VECTOR('',#5417,10.);
#1495=VECTOR('',#5420,10.);
#1496=VECTOR('',#5423,10.);
#1497=VECTOR('',#5426,10.);
#1498=VECTOR('',#5429,10.);
#1499=VECTOR('',#5432,10.);
#1500=VECTOR('',#5435,10.);
#1501=VECTOR('',#5438,10.);
#1502=VECTOR('',#5441,10.);
#1503=VECTOR('',#5444,10.);
#1504=VECTOR('',#5447,10.);
#1505=VECTOR('',#5450,10.);
#1506=VECTOR('',#5453,10.);
#1507=VECTOR('',#5456,10.);
#1508=VECTOR('',#5459,10.);
#1509=VECTOR('',#5462,10.);
#1510=VECTOR('',#5465,10.);
#1511=VECTOR('',#5470,10.);
#1512=VECTOR('',#5477,10.);
#1513=VECTOR('',#5480,10.);
#1514=VECTOR('',#5485,10.);
#1515=VECTOR('',#5488,10.);
#1516=VECTOR('',#5491,10.);
#1517=VECTOR('',#5496,10.);
#1518=VECTOR('',#5497,10.);
#1519=VECTOR('',#5506,10.);
#1520=VECTOR('',#5509,10.);
#1521=VECTOR('',#5512,10.);
#1522=VECTOR('',#5515,10.);
#1523=VECTOR('',#5518,10.);
#1524=VECTOR('',#5521,10.);
#1525=VECTOR('',#5524,10.);
#1526=VECTOR('',#5527,10.);
#1527=VECTOR('',#5530,10.);
#1528=VECTOR('',#5533,10.);
#1529=VECTOR('',#5536,10.);
#1530=VERTEX_POINT('',#5545);
#1531=VERTEX_POINT('',#5546);
#1532=VERTEX_POINT('',#5548);
#1533=VERTEX_POINT('',#5550);
#1534=VERTEX_POINT('',#5552);
#1535=VERTEX_POINT('',#5554);
#1536=VERTEX_POINT('',#5556);
#1537=VERTEX_POINT('',#5558);
#1538=VERTEX_POINT('',#5560);
#1539=VERTEX_POINT('',#5562);
#1540=VERTEX_POINT('',#5564);
#1541=VERTEX_POINT('',#5566);
#1542=VERTEX_POINT('',#5568);
#1543=VERTEX_POINT('',#5570);
#1544=VERTEX_POINT('',#5574);
#1545=VERTEX_POINT('',#5576);
#1546=VERTEX_POINT('',#5578);
#1547=VERTEX_POINT('',#5582);
#1548=VERTEX_POINT('',#5584);
#1549=VERTEX_POINT('',#5589);
#1550=VERTEX_POINT('',#5593);
#1551=VERTEX_POINT('',#5597);
#1552=VERTEX_POINT('',#5599);
#1553=VERTEX_POINT('',#5601);
#1554=VERTEX_POINT('',#5603);
#1555=VERTEX_POINT('',#5605);
#1556=VERTEX_POINT('',#5607);
#1557=VERTEX_POINT('',#5609);
#1558=VERTEX_POINT('',#5611);
#1559=VERTEX_POINT('',#5613);
#1560=VERTEX_POINT('',#5615);
#1561=VERTEX_POINT('',#5617);
#1562=VERTEX_POINT('',#5619);
#1563=VERTEX_POINT('',#5621);
#1564=VERTEX_POINT('',#5623);
#1565=VERTEX_POINT('',#5625);
#1566=VERTEX_POINT('',#5627);
#1567=VERTEX_POINT('',#5629);
#1568=VERTEX_POINT('',#5631);
#1569=VERTEX_POINT('',#5633);
#1570=VERTEX_POINT('',#5635);
#1571=VERTEX_POINT('',#5637);
#1572=VERTEX_POINT('',#5639);
#1573=VERTEX_POINT('',#5641);
#1574=VERTEX_POINT('',#5643);
#1575=VERTEX_POINT('',#5645);
#1576=VERTEX_POINT('',#5647);
#1577=VERTEX_POINT('',#5649);
#1578=VERTEX_POINT('',#5651);
#1579=VERTEX_POINT('',#5653);
#1580=VERTEX_POINT('',#5655);
#1581=VERTEX_POINT('',#5657);
#1582=VERTEX_POINT('',#5659);
#1583=VERTEX_POINT('',#5661);
#1584=VERTEX_POINT('',#5663);
#1585=VERTEX_POINT('',#5665);
#1586=VERTEX_POINT('',#5669);
#1587=VERTEX_POINT('',#5671);
#1588=VERTEX_POINT('',#5678);
#1589=VERTEX_POINT('',#5679);
#1590=VERTEX_POINT('',#5681);
#1591=VERTEX_POINT('',#5683);
#1592=VERTEX_POINT('',#5687);
#1593=VERTEX_POINT('',#5692);
#1594=VERTEX_POINT('',#5694);
#1595=VERTEX_POINT('',#5699);
#1596=VERTEX_POINT('',#5703);
#1597=VERTEX_POINT('',#5707);
#1598=VERTEX_POINT('',#5709);
#1599=VERTEX_POINT('',#5711);
#1600=VERTEX_POINT('',#5713);
#1601=VERTEX_POINT('',#5715);
#1602=VERTEX_POINT('',#5717);
#1603=VERTEX_POINT('',#5719);
#1604=VERTEX_POINT('',#5721);
#1605=VERTEX_POINT('',#5723);
#1606=VERTEX_POINT('',#5732);
#1607=VERTEX_POINT('',#5733);
#1608=VERTEX_POINT('',#5735);
#1609=VERTEX_POINT('',#5737);
#1610=VERTEX_POINT('',#5741);
#1611=VERTEX_POINT('',#5746);
#1612=VERTEX_POINT('',#5752);
#1613=VERTEX_POINT('',#5756);
#1614=VERTEX_POINT('',#5764);
#1615=VERTEX_POINT('',#5765);
#1616=VERTEX_POINT('',#5767);
#1617=VERTEX_POINT('',#5769);
#1618=VERTEX_POINT('',#5771);
#1619=VERTEX_POINT('',#5773);
#1620=VERTEX_POINT('',#5775);
#1621=VERTEX_POINT('',#5777);
#1622=VERTEX_POINT('',#5779);
#1623=VERTEX_POINT('',#5781);
#1624=VERTEX_POINT('',#5783);
#1625=VERTEX_POINT('',#5785);
#1626=VERTEX_POINT('',#5787);
#1627=VERTEX_POINT('',#5791);
#1628=VERTEX_POINT('',#5796);
#1629=VERTEX_POINT('',#5798);
#1630=VERTEX_POINT('',#5806);
#1631=VERTEX_POINT('',#5809);
#1632=VERTEX_POINT('',#5813);
#1633=VERTEX_POINT('',#5819);
#1634=VERTEX_POINT('',#5826);
#1635=VERTEX_POINT('',#5827);
#1636=VERTEX_POINT('',#5829);
#1637=VERTEX_POINT('',#5831);
#1638=VERTEX_POINT('',#5833);
#1639=VERTEX_POINT('',#5835);
#1640=VERTEX_POINT('',#5839);
#1641=VERTEX_POINT('',#5841);
#1642=VERTEX_POINT('',#5846);
#1643=VERTEX_POINT('',#5848);
#1644=VERTEX_POINT('',#5853);
#1645=VERTEX_POINT('',#5857);
#1646=VERTEX_POINT('',#5861);
#1647=VERTEX_POINT('',#5863);
#1648=VERTEX_POINT('',#5865);
#1649=VERTEX_POINT('',#5868);
#1650=VERTEX_POINT('',#5870);
#1651=VERTEX_POINT('',#5872);
#1652=VERTEX_POINT('',#5874);
#1653=VERTEX_POINT('',#5883);
#1654=VERTEX_POINT('',#5884);
#1655=VERTEX_POINT('',#5886);
#1656=VERTEX_POINT('',#5888);
#1657=VERTEX_POINT('',#5890);
#1658=VERTEX_POINT('',#5892);
#1659=VERTEX_POINT('',#5894);
#1660=VERTEX_POINT('',#5896);
#1661=VERTEX_POINT('',#5898);
#1662=VERTEX_POINT('',#5900);
#1663=VERTEX_POINT('',#5902);
#1664=VERTEX_POINT('',#5904);
#1665=VERTEX_POINT('',#5906);
#1666=VERTEX_POINT('',#5908);
#1667=VERTEX_POINT('',#5910);
#1668=VERTEX_POINT('',#5912);
#1669=VERTEX_POINT('',#5914);
#1670=VERTEX_POINT('',#5916);
#1671=VERTEX_POINT('',#5918);
#1672=VERTEX_POINT('',#5920);
#1673=VERTEX_POINT('',#5922);
#1674=VERTEX_POINT('',#5924);
#1675=VERTEX_POINT('',#5926);
#1676=VERTEX_POINT('',#5928);
#1677=VERTEX_POINT('',#5930);
#1678=VERTEX_POINT('',#5934);
#1679=VERTEX_POINT('',#5940);
#1680=VERTEX_POINT('',#5945);
#1681=VERTEX_POINT('',#5949);
#1682=VERTEX_POINT('',#5953);
#1683=VERTEX_POINT('',#5955);
#1684=VERTEX_POINT('',#5963);
#1685=VERTEX_POINT('',#5964);
#1686=VERTEX_POINT('',#5966);
#1687=VERTEX_POINT('',#5968);
#1688=VERTEX_POINT('',#5972);
#1689=VERTEX_POINT('',#5975);
#1690=VERTEX_POINT('',#5979);
#1691=VERTEX_POINT('',#5984);
#1692=VERTEX_POINT('',#5988);
#1693=VERTEX_POINT('',#5992);
#1694=VERTEX_POINT('',#5994);
#1695=VERTEX_POINT('',#5996);
#1696=VERTEX_POINT('',#5998);
#1697=VERTEX_POINT('',#6000);
#1698=VERTEX_POINT('',#6002);
#1699=VERTEX_POINT('',#6004);
#1700=VERTEX_POINT('',#6006);
#1701=VERTEX_POINT('',#6008);
#1702=VERTEX_POINT('',#6010);
#1703=VERTEX_POINT('',#6012);
#1704=VERTEX_POINT('',#6014);
#1705=VERTEX_POINT('',#6016);
#1706=VERTEX_POINT('',#6018);
#1707=VERTEX_POINT('',#6020);
#1708=VERTEX_POINT('',#6022);
#1709=VERTEX_POINT('',#6024);
#1710=VERTEX_POINT('',#6026);
#1711=VERTEX_POINT('',#6028);
#1712=VERTEX_POINT('',#6030);
#1713=VERTEX_POINT('',#6032);
#1714=VERTEX_POINT('',#6036);
#1715=VERTEX_POINT('',#6038);
#1716=VERTEX_POINT('',#6045);
#1717=VERTEX_POINT('',#6046);
#1718=VERTEX_POINT('',#6048);
#1719=VERTEX_POINT('',#6050);
#1720=VERTEX_POINT('',#6052);
#1721=VERTEX_POINT('',#6054);
#1722=VERTEX_POINT('',#6056);
#1723=VERTEX_POINT('',#6058);
#1724=VERTEX_POINT('',#6060);
#1725=VERTEX_POINT('',#6062);
#1726=VERTEX_POINT('',#6064);
#1727=VERTEX_POINT('',#6066);
#1728=VERTEX_POINT('',#6068);
#1729=VERTEX_POINT('',#6070);
#1730=VERTEX_POINT('',#6072);
#1731=VERTEX_POINT('',#6074);
#1732=VERTEX_POINT('',#6076);
#1733=VERTEX_POINT('',#6078);
#1734=VERTEX_POINT('',#6080);
#1735=VERTEX_POINT('',#6082);
#1736=VERTEX_POINT('',#6084);
#1737=VERTEX_POINT('',#6086);
#1738=VERTEX_POINT('',#6088);
#1739=VERTEX_POINT('',#6090);
#1740=VERTEX_POINT('',#6092);
#1741=VERTEX_POINT('',#6094);
#1742=VERTEX_POINT('',#6097);
#1743=VERTEX_POINT('',#6098);
#1744=VERTEX_POINT('',#6100);
#1745=VERTEX_POINT('',#6102);
#1746=VERTEX_POINT('',#6104);
#1747=VERTEX_POINT('',#6106);
#1748=VERTEX_POINT('',#6109);
#1749=VERTEX_POINT('',#6110);
#1750=VERTEX_POINT('',#6112);
#1751=VERTEX_POINT('',#6114);
#1752=VERTEX_POINT('',#6116);
#1753=VERTEX_POINT('',#6118);
#1754=VERTEX_POINT('',#6122);
#1755=VERTEX_POINT('',#6125);
#1756=VERTEX_POINT('',#6129);
#1757=VERTEX_POINT('',#6134);
#1758=VERTEX_POINT('',#6138);
#1759=VERTEX_POINT('',#6144);
#1760=VERTEX_POINT('',#6146);
#1761=VERTEX_POINT('',#6153);
#1762=VERTEX_POINT('',#6154);
#1763=VERTEX_POINT('',#6156);
#1764=VERTEX_POINT('',#6158);
#1765=VERTEX_POINT('',#6160);
#1766=VERTEX_POINT('',#6162);
#1767=VERTEX_POINT('',#6164);
#1768=VERTEX_POINT('',#6166);
#1769=VERTEX_POINT('',#6168);
#1770=VERTEX_POINT('',#6170);
#1771=VERTEX_POINT('',#6172);
#1772=VERTEX_POINT('',#6174);
#1773=VERTEX_POINT('',#6176);
#1774=VERTEX_POINT('',#6178);
#1775=VERTEX_POINT('',#6180);
#1776=VERTEX_POINT('',#6182);
#1777=VERTEX_POINT('',#6184);
#1778=VERTEX_POINT('',#6186);
#1779=VERTEX_POINT('',#6188);
#1780=VERTEX_POINT('',#6190);
#1781=VERTEX_POINT('',#6192);
#1782=VERTEX_POINT('',#6194);
#1783=VERTEX_POINT('',#6196);
#1784=VERTEX_POINT('',#6198);
#1785=VERTEX_POINT('',#6200);
#1786=VERTEX_POINT('',#6202);
#1787=VERTEX_POINT('',#6206);
#1788=VERTEX_POINT('',#6209);
#1789=VERTEX_POINT('',#6213);
#1790=VERTEX_POINT('',#6218);
#1791=VERTEX_POINT('',#6222);
#1792=VERTEX_POINT('',#6226);
#1793=VERTEX_POINT('',#6228);
#1794=VERTEX_POINT('',#6230);
#1795=VERTEX_POINT('',#6232);
#1796=VERTEX_POINT('',#6234);
#1797=VERTEX_POINT('',#6236);
#1798=VERTEX_POINT('',#6238);
#1799=VERTEX_POINT('',#6240);
#1800=VERTEX_POINT('',#6242);
#1801=VERTEX_POINT('',#6244);
#1802=VERTEX_POINT('',#6246);
#1803=VERTEX_POINT('',#6248);
#1804=VERTEX_POINT('',#6250);
#1805=VERTEX_POINT('',#6252);
#1806=VERTEX_POINT('',#6254);
#1807=VERTEX_POINT('',#6256);
#1808=VERTEX_POINT('',#6258);
#1809=VERTEX_POINT('',#6260);
#1810=VERTEX_POINT('',#6262);
#1811=VERTEX_POINT('',#6264);
#1812=VERTEX_POINT('',#6266);
#1813=VERTEX_POINT('',#6268);
#1814=VERTEX_POINT('',#6271);
#1815=VERTEX_POINT('',#6272);
#1816=VERTEX_POINT('',#6274);
#1817=VERTEX_POINT('',#6276);
#1818=VERTEX_POINT('',#6278);
#1819=VERTEX_POINT('',#6280);
#1820=VERTEX_POINT('',#6283);
#1821=VERTEX_POINT('',#6284);
#1822=VERTEX_POINT('',#6286);
#1823=VERTEX_POINT('',#6288);
#1824=VERTEX_POINT('',#6290);
#1825=VERTEX_POINT('',#6292);
#1826=VERTEX_POINT('',#6296);
#1827=VERTEX_POINT('',#6298);
#1828=VERTEX_POINT('',#6305);
#1829=VERTEX_POINT('',#6306);
#1830=VERTEX_POINT('',#6308);
#1831=VERTEX_POINT('',#6310);
#1832=VERTEX_POINT('',#6314);
#1833=VERTEX_POINT('',#6316);
#1834=VERTEX_POINT('',#6318);
#1835=VERTEX_POINT('',#6322);
#1836=VERTEX_POINT('',#6328);
#1837=VERTEX_POINT('',#6330);
#1838=VERTEX_POINT('',#6332);
#1839=VERTEX_POINT('',#6336);
#1840=VERTEX_POINT('',#6338);
#1841=VERTEX_POINT('',#6340);
#1842=VERTEX_POINT('',#6344);
#1843=VERTEX_POINT('',#6348);
#1844=VERTEX_POINT('',#6350);
#1845=VERTEX_POINT('',#6352);
#1846=VERTEX_POINT('',#6354);
#1847=VERTEX_POINT('',#6356);
#1848=VERTEX_POINT('',#6358);
#1849=VERTEX_POINT('',#6360);
#1850=VERTEX_POINT('',#6362);
#1851=VERTEX_POINT('',#6364);
#1852=VERTEX_POINT('',#6366);
#1853=VERTEX_POINT('',#6368);
#1854=VERTEX_POINT('',#6370);
#1855=VERTEX_POINT('',#6372);
#1856=VERTEX_POINT('',#6374);
#1857=VERTEX_POINT('',#6376);
#1858=VERTEX_POINT('',#6378);
#1859=VERTEX_POINT('',#6380);
#1860=VERTEX_POINT('',#6382);
#1861=VERTEX_POINT('',#6384);
#1862=VERTEX_POINT('',#6386);
#1863=VERTEX_POINT('',#6388);
#1864=VERTEX_POINT('',#6390);
#1865=VERTEX_POINT('',#6443);
#1866=VERTEX_POINT('',#6447);
#1867=VERTEX_POINT('',#6451);
#1868=VERTEX_POINT('',#6455);
#1869=VERTEX_POINT('',#6459);
#1870=VERTEX_POINT('',#6463);
#1871=VERTEX_POINT('',#6467);
#1872=VERTEX_POINT('',#6471);
#1873=VERTEX_POINT('',#6475);
#1874=VERTEX_POINT('',#6479);
#1875=VERTEX_POINT('',#6483);
#1876=VERTEX_POINT('',#6487);
#1877=VERTEX_POINT('',#6500);
#1878=VERTEX_POINT('',#6504);
#1879=VERTEX_POINT('',#6508);
#1880=VERTEX_POINT('',#6512);
#1881=VERTEX_POINT('',#6516);
#1882=VERTEX_POINT('',#6568);
#1883=VERTEX_POINT('',#6572);
#1884=VERTEX_POINT('',#6576);
#1885=VERTEX_POINT('',#6584);
#1886=VERTEX_POINT('',#6686);
#1887=EDGE_CURVE('',#1530,#1531,#712,.T.);
#1888=EDGE_CURVE('',#1531,#1532,#713,.T.);
#1889=EDGE_CURVE('',#1533,#1532,#548,.T.);
#1890=EDGE_CURVE('',#1533,#1534,#714,.T.);
#1891=EDGE_CURVE('',#1534,#1535,#549,.T.);
#1892=EDGE_CURVE('',#1536,#1535,#715,.T.);
#1893=EDGE_CURVE('',#1536,#1537,#550,.T.);
#1894=EDGE_CURVE('',#1537,#1538,#716,.T.);
#1895=EDGE_CURVE('',#1538,#1539,#551,.T.);
#1896=EDGE_CURVE('',#1539,#1540,#717,.T.);
#1897=EDGE_CURVE('',#1541,#1540,#552,.T.);
#1898=EDGE_CURVE('',#1541,#1542,#718,.T.);
#1899=EDGE_CURVE('',#1543,#1542,#719,.T.);
#1900=EDGE_CURVE('',#1543,#1530,#720,.T.);
#1901=EDGE_CURVE('',#1544,#1530,#721,.T.);
#1902=EDGE_CURVE('',#1545,#1544,#553,.T.);
#1903=EDGE_CURVE('',#1545,#1546,#722,.T.);
#1904=EDGE_CURVE('',#1530,#1546,#554,.T.);
#1905=EDGE_CURVE('',#1547,#1544,#723,.T.);
#1906=EDGE_CURVE('',#1547,#1548,#555,.T.);
#1907=EDGE_CURVE('',#1548,#1545,#724,.T.);
#1908=EDGE_CURVE('',#1531,#1547,#725,.T.);
#1909=EDGE_CURVE('',#1549,#1547,#726,.T.);
#1910=EDGE_CURVE('',#1532,#1549,#727,.T.);
#1911=EDGE_CURVE('',#1550,#1543,#728,.T.);
#1912=EDGE_CURVE('',#1544,#1550,#729,.T.);
#1913=EDGE_CURVE('',#1548,#1551,#730,.T.);
#1914=EDGE_CURVE('',#1551,#1552,#556,.T.);
#1915=EDGE_CURVE('',#1552,#1553,#731,.T.);
#1916=EDGE_CURVE('',#1554,#1553,#732,.T.);
#1917=EDGE_CURVE('',#1554,#1555,#733,.T.);
#1918=EDGE_CURVE('',#1555,#1556,#557,.T.);
#1919=EDGE_CURVE('',#1556,#1557,#734,.T.);
#1920=EDGE_CURVE('',#1557,#1558,#558,.T.);
#1921=EDGE_CURVE('',#1558,#1559,#735,.T.);
#1922=EDGE_CURVE('',#1560,#1559,#736,.T.);
#1923=EDGE_CURVE('',#1560,#1561,#737,.T.);
#1924=EDGE_CURVE('',#1561,#1562,#559,.T.);
#1925=EDGE_CURVE('',#1562,#1563,#738,.T.);
#1926=EDGE_CURVE('',#1563,#1564,#560,.T.);
#1927=EDGE_CURVE('',#1564,#1565,#739,.T.);
#1928=EDGE_CURVE('',#1565,#1566,#561,.T.);
#1929=EDGE_CURVE('',#1566,#1567,#562,.T.);
#1930=EDGE_CURVE('',#1567,#1568,#740,.T.);
#1931=EDGE_CURVE('',#1569,#1568,#741,.T.);
#1932=EDGE_CURVE('',#1569,#1570,#742,.T.);
#1933=EDGE_CURVE('',#1570,#1571,#563,.T.);
#1934=EDGE_CURVE('',#1571,#1572,#743,.T.);
#1935=EDGE_CURVE('',#1572,#1573,#564,.T.);
#1936=EDGE_CURVE('',#1573,#1574,#744,.T.);
#1937=EDGE_CURVE('',#1574,#1575,#565,.T.);
#1938=EDGE_CURVE('',#1575,#1576,#745,.T.);
#1939=EDGE_CURVE('',#1576,#1577,#566,.T.);
#1940=EDGE_CURVE('',#1577,#1578,#746,.T.);
#1941=EDGE_CURVE('',#1578,#1579,#567,.T.);
#1942=EDGE_CURVE('',#1579,#1580,#747,.T.);
#1943=EDGE_CURVE('',#1580,#1581,#568,.T.);
#1944=EDGE_CURVE('',#1581,#1582,#748,.T.);
#1945=EDGE_CURVE('',#1583,#1582,#749,.T.);
#1946=EDGE_CURVE('',#1583,#1584,#750,.T.);
#1947=EDGE_CURVE('',#1584,#1585,#569,.T.);
#1948=EDGE_CURVE('',#1585,#1545,#751,.T.);
#1949=EDGE_CURVE('',#1586,#1548,#752,.T.);
#1950=EDGE_CURVE('',#1587,#1586,#753,.T.);
#1951=EDGE_CURVE('',#1587,#1551,#754,.T.);
#1952=EDGE_CURVE('',#1546,#1586,#755,.T.);
#1953=EDGE_CURVE('',#1586,#1531,#570,.T.);
#1954=EDGE_CURVE('',#1588,#1589,#756,.T.);
#1955=EDGE_CURVE('',#1589,#1590,#757,.T.);
#1956=EDGE_CURVE('',#1591,#1590,#758,.T.);
#1957=EDGE_CURVE('',#1591,#1588,#759,.T.);
#1958=EDGE_CURVE('',#1592,#1588,#760,.T.);
#1959=EDGE_CURVE('',#1550,#1592,#571,.T.);
#1960=EDGE_CURVE('',#1588,#1543,#572,.T.);
#1961=EDGE_CURVE('',#1593,#1592,#761,.T.);
#1962=EDGE_CURVE('',#1593,#1594,#573,.T.);
#1963=EDGE_CURVE('',#1594,#1550,#762,.T.);
#1964=EDGE_CURVE('',#1589,#1593,#763,.T.);
#1965=EDGE_CURVE('',#1595,#1593,#764,.T.);
#1966=EDGE_CURVE('',#1590,#1595,#765,.T.);
#1967=EDGE_CURVE('',#1596,#1591,#766,.T.);
#1968=EDGE_CURVE('',#1592,#1596,#767,.T.);
#1969=EDGE_CURVE('',#1594,#1597,#768,.T.);
#1970=EDGE_CURVE('',#1597,#1598,#574,.T.);
#1971=EDGE_CURVE('',#1598,#1599,#769,.T.);
#1972=EDGE_CURVE('',#1599,#1600,#575,.T.);
#1973=EDGE_CURVE('',#1600,#1601,#770,.T.);
#1974=EDGE_CURVE('',#1601,#1602,#576,.T.);
#1975=EDGE_CURVE('',#1602,#1603,#771,.T.);
#1976=EDGE_CURVE('',#1603,#1604,#577,.T.);
#1977=EDGE_CURVE('',#1604,#1605,#772,.T.);
#1978=EDGE_CURVE('',#1605,#1549,#578,.T.);
#1979=EDGE_CURVE('',#1542,#1594,#773,.T.);
#1980=EDGE_CURVE('',#1541,#1597,#774,.T.);
#1981=EDGE_CURVE('',#1542,#1589,#579,.T.);
#1982=EDGE_CURVE('',#1606,#1607,#775,.T.);
#1983=EDGE_CURVE('',#1607,#1608,#776,.T.);
#1984=EDGE_CURVE('',#1609,#1608,#777,.T.);
#1985=EDGE_CURVE('',#1609,#1606,#778,.T.);
#1986=EDGE_CURVE('',#1610,#1606,#779,.T.);
#1987=EDGE_CURVE('',#1596,#1610,#580,.T.);
#1988=EDGE_CURVE('',#1606,#1591,#581,.T.);
#1989=EDGE_CURVE('',#1611,#1610,#780,.T.);
#1990=EDGE_CURVE('',#1611,#1595,#582,.T.);
#1991=EDGE_CURVE('',#1595,#1596,#781,.T.);
#1992=EDGE_CURVE('',#1607,#1611,#782,.T.);
#1993=EDGE_CURVE('',#1612,#1611,#783,.T.);
#1994=EDGE_CURVE('',#1608,#1612,#784,.T.);
#1995=EDGE_CURVE('',#1613,#1609,#785,.T.);
#1996=EDGE_CURVE('',#1610,#1613,#786,.T.);
#1997=EDGE_CURVE('',#1590,#1607,#583,.T.);
#1998=EDGE_CURVE('',#1614,#1615,#787,.T.);
#1999=EDGE_CURVE('',#1616,#1615,#584,.T.);
#2000=EDGE_CURVE('',#1617,#1616,#788,.T.);
#2001=EDGE_CURVE('',#1618,#1617,#789,.T.);
#2002=EDGE_CURVE('',#1619,#1618,#790,.T.);
#2003=EDGE_CURVE('',#1620,#1619,#585,.T.);
#2004=EDGE_CURVE('',#1621,#1620,#791,.T.);
#2005=EDGE_CURVE('',#1622,#1621,#792,.T.);
#2006=EDGE_CURVE('',#1622,#1623,#793,.T.);
#2007=EDGE_CURVE('',#1623,#1624,#586,.T.);
#2008=EDGE_CURVE('',#1624,#1625,#794,.T.);
#2009=EDGE_CURVE('',#1626,#1625,#587,.T.);
#2010=EDGE_CURVE('',#1626,#1614,#795,.T.);
#2011=EDGE_CURVE('',#1627,#1614,#796,.T.);
#2012=EDGE_CURVE('',#1613,#1627,#588,.T.);
#2013=EDGE_CURVE('',#1614,#1609,#589,.T.);
#2014=EDGE_CURVE('',#1628,#1627,#797,.T.);
#2015=EDGE_CURVE('',#1628,#1629,#20,.T.);
#2016=EDGE_CURVE('',#1629,#1612,#590,.T.);
#2017=EDGE_CURVE('',#1612,#1613,#798,.T.);
#2018=EDGE_CURVE('',#1615,#1630,#799,.T.);
#2019=EDGE_CURVE('',#1630,#1628,#800,.T.);
#2020=EDGE_CURVE('',#1631,#1628,#591,.T.);
#2021=EDGE_CURVE('',#1616,#1631,#801,.T.);
#2022=EDGE_CURVE('',#1626,#1632,#802,.T.);
#2023=EDGE_CURVE('',#1627,#1632,#803,.T.);
#2024=EDGE_CURVE('',#1608,#1633,#592,.T.);
#2025=EDGE_CURVE('',#1615,#1633,#21,.T.);
#2026=EDGE_CURVE('',#1634,#1635,#804,.T.);
#2027=EDGE_CURVE('',#1636,#1635,#805,.T.);
#2028=EDGE_CURVE('',#1636,#1637,#593,.T.);
#2029=EDGE_CURVE('',#1638,#1637,#806,.T.);
#2030=EDGE_CURVE('',#1638,#1639,#594,.T.);
#2031=EDGE_CURVE('',#1639,#1634,#807,.T.);
#2032=EDGE_CURVE('',#1640,#1634,#808,.T.);
#2033=EDGE_CURVE('',#1641,#1640,#595,.T.);
#2034=EDGE_CURVE('',#1641,#1622,#809,.T.);
#2035=EDGE_CURVE('',#1634,#1622,#596,.T.);
#2036=EDGE_CURVE('',#1642,#1640,#810,.T.);
#2037=EDGE_CURVE('',#1642,#1643,#597,.T.);
#2038=EDGE_CURVE('',#1643,#1641,#811,.T.);
#2039=EDGE_CURVE('',#1635,#1642,#812,.T.);
#2040=EDGE_CURVE('',#1644,#1642,#813,.T.);
#2041=EDGE_CURVE('',#1636,#1644,#814,.T.);
#2042=EDGE_CURVE('',#1639,#1645,#815,.T.);
#2043=EDGE_CURVE('',#1640,#1645,#816,.T.);
#2044=EDGE_CURVE('',#1632,#1646,#598,.T.);
#2045=EDGE_CURVE('',#1646,#1647,#817,.T.);
#2046=EDGE_CURVE('',#1647,#1648,#599,.T.);
#2047=EDGE_CURVE('',#1648,#1641,#818,.T.);
#2048=EDGE_CURVE('',#1643,#1649,#819,.T.);
#2049=EDGE_CURVE('',#1649,#1650,#600,.T.);
#2050=EDGE_CURVE('',#1650,#1651,#820,.T.);
#2051=EDGE_CURVE('',#1652,#1651,#821,.T.);
#2052=EDGE_CURVE('',#1652,#1631,#822,.T.);
#2053=EDGE_CURVE('',#1621,#1643,#823,.T.);
#2054=EDGE_CURVE('',#1620,#1649,#824,.T.);
#2055=EDGE_CURVE('',#1635,#1621,#601,.T.);
#2056=EDGE_CURVE('',#1653,#1654,#825,.T.);
#2057=EDGE_CURVE('',#1655,#1654,#826,.T.);
#2058=EDGE_CURVE('',#1655,#1656,#602,.T.);
#2059=EDGE_CURVE('',#1657,#1656,#827,.T.);
#2060=EDGE_CURVE('',#1657,#1658,#603,.T.);
#2061=EDGE_CURVE('',#1658,#1659,#828,.T.);
#2062=EDGE_CURVE('',#1660,#1659,#604,.T.);
#2063=EDGE_CURVE('',#1661,#1660,#829,.T.);
#2064=EDGE_CURVE('',#1662,#1661,#605,.T.);
#2065=EDGE_CURVE('',#1663,#1662,#830,.T.);
#2066=EDGE_CURVE('',#1663,#1664,#606,.T.);
#2067=EDGE_CURVE('',#1665,#1664,#831,.T.);
#2068=EDGE_CURVE('',#1665,#1666,#607,.T.);
#2069=EDGE_CURVE('',#1667,#1666,#832,.T.);
#2070=EDGE_CURVE('',#1667,#1668,#608,.T.);
#2071=EDGE_CURVE('',#1669,#1668,#833,.T.);
#2072=EDGE_CURVE('',#1669,#1670,#609,.T.);
#2073=EDGE_CURVE('',#1671,#1670,#834,.T.);
#2074=EDGE_CURVE('',#1672,#1671,#610,.T.);
#2075=EDGE_CURVE('',#1673,#1672,#835,.T.);
#2076=EDGE_CURVE('',#1674,#1673,#611,.T.);
#2077=EDGE_CURVE('',#1674,#1675,#836,.T.);
#2078=EDGE_CURVE('',#1675,#1676,#612,.T.);
#2079=EDGE_CURVE('',#1677,#1676,#837,.T.);
#2080=EDGE_CURVE('',#1653,#1677,#838,.T.);
#2081=EDGE_CURVE('',#1678,#1653,#839,.T.);
#2082=EDGE_CURVE('',#1651,#1678,#613,.T.);
#2083=EDGE_CURVE('',#1651,#1618,#840,.T.);
#2084=EDGE_CURVE('',#1618,#1653,#614,.T.);
#2085=EDGE_CURVE('',#1679,#1678,#841,.T.);
#2086=EDGE_CURVE('',#1679,#1652,#615,.T.);
#2087=EDGE_CURVE('',#1654,#1679,#842,.T.);
#2088=EDGE_CURVE('',#1680,#1679,#843,.T.);
#2089=EDGE_CURVE('',#1655,#1680,#844,.T.);
#2090=EDGE_CURVE('',#1677,#1681,#845,.T.);
#2091=EDGE_CURVE('',#1678,#1681,#846,.T.);
#2092=EDGE_CURVE('',#1645,#1682,#616,.T.);
#2093=EDGE_CURVE('',#1682,#1683,#847,.T.);
#2094=EDGE_CURVE('',#1683,#1644,#617,.T.);
#2095=EDGE_CURVE('',#1617,#1652,#848,.T.);
#2096=EDGE_CURVE('',#1654,#1617,#618,.T.);
#2097=EDGE_CURVE('',#1684,#1685,#849,.T.);
#2098=EDGE_CURVE('',#1686,#1685,#850,.T.);
#2099=EDGE_CURVE('',#1687,#1686,#851,.T.);
#2100=EDGE_CURVE('',#1684,#1687,#852,.T.);
#2101=EDGE_CURVE('',#1688,#1684,#853,.T.);
#2102=EDGE_CURVE('',#1553,#1688,#619,.T.);
#2103=EDGE_CURVE('',#1553,#1689,#854,.T.);
#2104=EDGE_CURVE('',#1689,#1684,#620,.T.);
#2105=EDGE_CURVE('',#1690,#1688,#855,.T.);
#2106=EDGE_CURVE('',#1690,#1554,#621,.T.);
#2107=EDGE_CURVE('',#1685,#1690,#856,.T.);
#2108=EDGE_CURVE('',#1691,#1690,#857,.T.);
#2109=EDGE_CURVE('',#1686,#1691,#858,.T.);
#2110=EDGE_CURVE('',#1687,#1692,#859,.T.);
#2111=EDGE_CURVE('',#1688,#1692,#860,.T.);
#2112=EDGE_CURVE('',#1681,#1693,#861,.T.);
#2113=EDGE_CURVE('',#1693,#1694,#622,.T.);
#2114=EDGE_CURVE('',#1694,#1695,#862,.T.);
#2115=EDGE_CURVE('',#1695,#1696,#623,.T.);
#2116=EDGE_CURVE('',#1696,#1697,#863,.T.);
#2117=EDGE_CURVE('',#1697,#1698,#624,.T.);
#2118=EDGE_CURVE('',#1698,#1699,#864,.T.);
#2119=EDGE_CURVE('',#1699,#1700,#625,.T.);
#2120=EDGE_CURVE('',#1700,#1701,#865,.T.);
#2121=EDGE_CURVE('',#1701,#1702,#626,.T.);
#2122=EDGE_CURVE('',#1702,#1703,#866,.T.);
#2123=EDGE_CURVE('',#1703,#1704,#627,.T.);
#2124=EDGE_CURVE('',#1704,#1705,#867,.T.);
#2125=EDGE_CURVE('',#1705,#1706,#628,.T.);
#2126=EDGE_CURVE('',#1706,#1707,#868,.T.);
#2127=EDGE_CURVE('',#1707,#1708,#629,.T.);
#2128=EDGE_CURVE('',#1708,#1709,#869,.T.);
#2129=EDGE_CURVE('',#1709,#1710,#630,.T.);
#2130=EDGE_CURVE('',#1710,#1711,#870,.T.);
#2131=EDGE_CURVE('',#1711,#1712,#631,.T.);
#2132=EDGE_CURVE('',#1712,#1713,#871,.T.);
#2133=EDGE_CURVE('',#1713,#1680,#632,.T.);
#2134=EDGE_CURVE('',#1714,#1554,#872,.T.);
#2135=EDGE_CURVE('',#1714,#1715,#873,.T.);
#2136=EDGE_CURVE('',#1715,#1555,#874,.T.);
#2137=EDGE_CURVE('',#1689,#1714,#875,.T.);
#2138=EDGE_CURVE('',#1685,#1714,#633,.T.);
#2139=EDGE_CURVE('',#1716,#1717,#876,.T.);
#2140=EDGE_CURVE('',#1717,#1718,#877,.T.);
#2141=EDGE_CURVE('',#1719,#1718,#634,.T.);
#2142=EDGE_CURVE('',#1719,#1720,#878,.T.);
#2143=EDGE_CURVE('',#1720,#1721,#635,.T.);
#2144=EDGE_CURVE('',#1721,#1722,#879,.T.);
#2145=EDGE_CURVE('',#1722,#1723,#636,.T.);
#2146=EDGE_CURVE('',#1723,#1724,#880,.T.);
#2147=EDGE_CURVE('',#1724,#1725,#637,.T.);
#2148=EDGE_CURVE('',#1726,#1725,#881,.T.);
#2149=EDGE_CURVE('',#1727,#1726,#638,.T.);
#2150=EDGE_CURVE('',#1728,#1727,#882,.T.);
#2151=EDGE_CURVE('',#1729,#1728,#639,.T.);
#2152=EDGE_CURVE('',#1730,#1729,#883,.T.);
#2153=EDGE_CURVE('',#1730,#1731,#640,.T.);
#2154=EDGE_CURVE('',#1731,#1732,#884,.T.);
#2155=EDGE_CURVE('',#1732,#1733,#641,.T.);
#2156=EDGE_CURVE('',#1734,#1733,#885,.T.);
#2157=EDGE_CURVE('',#1734,#1735,#642,.T.);
#2158=EDGE_CURVE('',#1736,#1735,#886,.T.);
#2159=EDGE_CURVE('',#1736,#1737,#643,.T.);
#2160=EDGE_CURVE('',#1738,#1737,#887,.T.);
#2161=EDGE_CURVE('',#1739,#1738,#644,.T.);
#2162=EDGE_CURVE('',#1739,#1740,#888,.T.);
#2163=EDGE_CURVE('',#1741,#1740,#645,.T.);
#2164=EDGE_CURVE('',#1716,#1741,#889,.T.);
#2165=EDGE_CURVE('',#1742,#1743,#890,.T.);
#2166=EDGE_CURVE('',#1743,#1744,#891,.T.);
#2167=EDGE_CURVE('',#1744,#1745,#892,.T.);
#2168=EDGE_CURVE('',#1745,#1746,#893,.T.);
#2169=EDGE_CURVE('',#1746,#1747,#894,.T.);
#2170=EDGE_CURVE('',#1747,#1742,#895,.T.);
#2171=EDGE_CURVE('',#1748,#1749,#896,.T.);
#2172=EDGE_CURVE('',#1749,#1750,#897,.T.);
#2173=EDGE_CURVE('',#1750,#1751,#898,.T.);
#2174=EDGE_CURVE('',#1751,#1752,#899,.T.);
#2175=EDGE_CURVE('',#1752,#1753,#900,.T.);
#2176=EDGE_CURVE('',#1753,#1748,#901,.T.);
#2177=EDGE_CURVE('',#1754,#1716,#902,.T.);
#2178=EDGE_CURVE('',#1582,#1754,#646,.T.);
#2179=EDGE_CURVE('',#1582,#1755,#903,.T.);
#2180=EDGE_CURVE('',#1755,#1716,#647,.T.);
#2181=EDGE_CURVE('',#1756,#1754,#904,.T.);
#2182=EDGE_CURVE('',#1756,#1583,#648,.T.);
#2183=EDGE_CURVE('',#1717,#1756,#905,.T.);
#2184=EDGE_CURVE('',#1757,#1756,#906,.T.);
#2185=EDGE_CURVE('',#1718,#1757,#907,.T.);
#2186=EDGE_CURVE('',#1741,#1758,#908,.T.);
#2187=EDGE_CURVE('',#1754,#1758,#909,.T.);
#2188=EDGE_CURVE('',#1692,#1691,#910,.T.);
#2189=EDGE_CURVE('',#1759,#1583,#911,.T.);
#2190=EDGE_CURVE('',#1760,#1759,#912,.T.);
#2191=EDGE_CURVE('',#1760,#1584,#913,.T.);
#2192=EDGE_CURVE('',#1755,#1759,#914,.T.);
#2193=EDGE_CURVE('',#1759,#1717,#649,.T.);
#2194=EDGE_CURVE('',#1761,#1762,#915,.T.);
#2195=EDGE_CURVE('',#1763,#1762,#916,.T.);
#2196=EDGE_CURVE('',#1764,#1763,#650,.T.);
#2197=EDGE_CURVE('',#1764,#1765,#917,.T.);
#2198=EDGE_CURVE('',#1765,#1766,#651,.T.);
#2199=EDGE_CURVE('',#1767,#1766,#918,.T.);
#2200=EDGE_CURVE('',#1768,#1767,#652,.T.);
#2201=EDGE_CURVE('',#1768,#1769,#919,.T.);
#2202=EDGE_CURVE('',#1770,#1769,#653,.T.);
#2203=EDGE_CURVE('',#1770,#1771,#920,.T.);
#2204=EDGE_CURVE('',#1771,#1772,#654,.T.);
#2205=EDGE_CURVE('',#1772,#1773,#921,.T.);
#2206=EDGE_CURVE('',#1773,#1774,#655,.T.);
#2207=EDGE_CURVE('',#1774,#1775,#922,.T.);
#2208=EDGE_CURVE('',#1775,#1776,#656,.T.);
#2209=EDGE_CURVE('',#1776,#1777,#923,.T.);
#2210=EDGE_CURVE('',#1777,#1778,#657,.T.);
#2211=EDGE_CURVE('',#1778,#1779,#924,.T.);
#2212=EDGE_CURVE('',#1780,#1779,#658,.T.);
#2213=EDGE_CURVE('',#1780,#1781,#925,.T.);
#2214=EDGE_CURVE('',#1782,#1781,#659,.T.);
#2215=EDGE_CURVE('',#1783,#1782,#926,.T.);
#2216=EDGE_CURVE('',#1783,#1784,#660,.T.);
#2217=EDGE_CURVE('',#1784,#1785,#927,.T.);
#2218=EDGE_CURVE('',#1785,#1786,#661,.T.);
#2219=EDGE_CURVE('',#1786,#1761,#928,.T.);
#2220=EDGE_CURVE('',#1787,#1761,#929,.T.);
#2221=EDGE_CURVE('',#1559,#1787,#662,.T.);
#2222=EDGE_CURVE('',#1559,#1788,#930,.T.);
#2223=EDGE_CURVE('',#1761,#1788,#663,.T.);
#2224=EDGE_CURVE('',#1789,#1787,#931,.T.);
#2225=EDGE_CURVE('',#1789,#1560,#664,.T.);
#2226=EDGE_CURVE('',#1762,#1789,#932,.T.);
#2227=EDGE_CURVE('',#1790,#1789,#933,.T.);
#2228=EDGE_CURVE('',#1763,#1790,#934,.T.);
#2229=EDGE_CURVE('',#1786,#1791,#935,.T.);
#2230=EDGE_CURVE('',#1787,#1791,#936,.T.);
#2231=EDGE_CURVE('',#1758,#1792,#665,.T.);
#2232=EDGE_CURVE('',#1792,#1793,#937,.T.);
#2233=EDGE_CURVE('',#1793,#1794,#666,.T.);
#2234=EDGE_CURVE('',#1794,#1795,#938,.T.);
#2235=EDGE_CURVE('',#1795,#1796,#667,.T.);
#2236=EDGE_CURVE('',#1796,#1797,#939,.T.);
#2237=EDGE_CURVE('',#1797,#1798,#668,.T.);
#2238=EDGE_CURVE('',#1798,#1799,#940,.T.);
#2239=EDGE_CURVE('',#1799,#1800,#669,.T.);
#2240=EDGE_CURVE('',#1800,#1801,#941,.T.);
#2241=EDGE_CURVE('',#1801,#1802,#670,.T.);
#2242=EDGE_CURVE('',#1802,#1803,#942,.T.);
#2243=EDGE_CURVE('',#1803,#1804,#671,.T.);
#2244=EDGE_CURVE('',#1804,#1805,#943,.T.);
#2245=EDGE_CURVE('',#1805,#1806,#672,.T.);
#2246=EDGE_CURVE('',#1806,#1807,#944,.T.);
#2247=EDGE_CURVE('',#1807,#1808,#673,.T.);
#2248=EDGE_CURVE('',#1808,#1809,#945,.T.);
#2249=EDGE_CURVE('',#1809,#1810,#674,.T.);
#2250=EDGE_CURVE('',#1810,#1811,#946,.T.);
#2251=EDGE_CURVE('',#1811,#1812,#675,.T.);
#2252=EDGE_CURVE('',#1812,#1813,#947,.T.);
#2253=EDGE_CURVE('',#1813,#1757,#676,.T.);
#2254=EDGE_CURVE('',#1814,#1815,#948,.T.);
#2255=EDGE_CURVE('',#1815,#1816,#949,.T.);
#2256=EDGE_CURVE('',#1816,#1817,#950,.T.);
#2257=EDGE_CURVE('',#1817,#1818,#951,.T.);
#2258=EDGE_CURVE('',#1818,#1819,#952,.T.);
#2259=EDGE_CURVE('',#1819,#1814,#953,.T.);
#2260=EDGE_CURVE('',#1820,#1821,#954,.T.);
#2261=EDGE_CURVE('',#1821,#1822,#955,.T.);
#2262=EDGE_CURVE('',#1822,#1823,#956,.T.);
#2263=EDGE_CURVE('',#1823,#1824,#957,.T.);
#2264=EDGE_CURVE('',#1824,#1825,#958,.T.);
#2265=EDGE_CURVE('',#1825,#1820,#959,.T.);
#2266=EDGE_CURVE('',#1826,#1560,#960,.T.);
#2267=EDGE_CURVE('',#1826,#1827,#961,.T.);
#2268=EDGE_CURVE('',#1827,#1561,#962,.T.);
#2269=EDGE_CURVE('',#1788,#1826,#963,.T.);
#2270=EDGE_CURVE('',#1762,#1826,#677,.T.);
#2271=EDGE_CURVE('',#1828,#1829,#964,.T.);
#2272=EDGE_CURVE('',#1830,#1829,#965,.T.);
#2273=EDGE_CURVE('',#1831,#1830,#966,.T.);
#2274=EDGE_CURVE('',#1831,#1828,#967,.T.);
#2275=EDGE_CURVE('',#1832,#1828,#968,.T.);
#2276=EDGE_CURVE('',#1832,#1833,#678,.T.);
#2277=EDGE_CURVE('',#1833,#1834,#969,.T.);
#2278=EDGE_CURVE('',#1828,#1834,#679,.T.);
#2279=EDGE_CURVE('',#1835,#1832,#970,.T.);
#2280=EDGE_CURVE('',#1568,#1835,#680,.T.);
#2281=EDGE_CURVE('',#1568,#1833,#971,.T.);
#2282=EDGE_CURVE('',#1829,#1835,#972,.T.);
#2283=EDGE_CURVE('',#1835,#1836,#973,.T.);
#2284=EDGE_CURVE('',#1836,#1837,#681,.T.);
#2285=EDGE_CURVE('',#1837,#1838,#974,.T.);
#2286=EDGE_CURVE('',#1838,#1830,#682,.T.);
#2287=EDGE_CURVE('',#1831,#1839,#683,.T.);
#2288=EDGE_CURVE('',#1840,#1839,#975,.T.);
#2289=EDGE_CURVE('',#1840,#1841,#684,.T.);
#2290=EDGE_CURVE('',#1841,#1832,#976,.T.);
#2291=EDGE_CURVE('',#1842,#1567,#977,.T.);
#2292=EDGE_CURVE('',#1833,#1842,#978,.T.);
#2293=EDGE_CURVE('',#1791,#1843,#685,.T.);
#2294=EDGE_CURVE('',#1843,#1844,#979,.T.);
#2295=EDGE_CURVE('',#1844,#1845,#686,.T.);
#2296=EDGE_CURVE('',#1845,#1846,#980,.T.);
#2297=EDGE_CURVE('',#1846,#1847,#687,.T.);
#2298=EDGE_CURVE('',#1847,#1848,#981,.T.);
#2299=EDGE_CURVE('',#1848,#1849,#688,.T.);
#2300=EDGE_CURVE('',#1849,#1850,#982,.T.);
#2301=EDGE_CURVE('',#1850,#1851,#689,.T.);
#2302=EDGE_CURVE('',#1851,#1852,#983,.T.);
#2303=EDGE_CURVE('',#1852,#1853,#690,.T.);
#2304=EDGE_CURVE('',#1853,#1854,#984,.T.);
#2305=EDGE_CURVE('',#1854,#1855,#691,.T.);
#2306=EDGE_CURVE('',#1855,#1856,#985,.T.);
#2307=EDGE_CURVE('',#1856,#1857,#692,.T.);
#2308=EDGE_CURVE('',#1857,#1858,#986,.T.);
#2309=EDGE_CURVE('',#1858,#1859,#693,.T.);
#2310=EDGE_CURVE('',#1859,#1860,#987,.T.);
#2311=EDGE_CURVE('',#1860,#1861,#694,.T.);
#2312=EDGE_CURVE('',#1861,#1862,#988,.T.);
#2313=EDGE_CURVE('',#1862,#1863,#695,.T.);
#2314=EDGE_CURVE('',#1863,#1864,#989,.T.);
#2315=EDGE_CURVE('',#1864,#1790,#696,.T.);
#2316=EDGE_CURVE('',#1834,#1569,#990,.T.);
#2317=EDGE_CURVE('',#1829,#1569,#697,.T.);
#2318=EDGE_CURVE('',#1749,#1820,#991,.T.);
#2319=EDGE_CURVE('',#1750,#1825,#992,.T.);
#2320=EDGE_CURVE('',#1751,#1824,#993,.T.);
#2321=EDGE_CURVE('',#1752,#1823,#994,.T.);
#2322=EDGE_CURVE('',#1753,#1822,#995,.T.);
#2323=EDGE_CURVE('',#1748,#1821,#996,.T.);
#2324=EDGE_CURVE('',#1743,#1814,#997,.T.);
#2325=EDGE_CURVE('',#1744,#1819,#998,.T.);
#2326=EDGE_CURVE('',#1745,#1818,#999,.T.);
#2327=EDGE_CURVE('',#1746,#1817,#1000,.T.);
#2328=EDGE_CURVE('',#1747,#1816,#1001,.T.);
#2329=EDGE_CURVE('',#1742,#1815,#1002,.T.);
#2330=EDGE_CURVE('',#1730,#1802,#1003,.T.);
#2331=EDGE_CURVE('',#1731,#1801,#1004,.T.);
#2332=EDGE_CURVE('',#1732,#1800,#1005,.T.);
#2333=EDGE_CURVE('',#1733,#1799,#1006,.T.);
#2334=EDGE_CURVE('',#1734,#1798,#1007,.T.);
#2335=EDGE_CURVE('',#1735,#1797,#1008,.T.);
#2336=EDGE_CURVE('',#1736,#1796,#1009,.T.);
#2337=EDGE_CURVE('',#1737,#1795,#1010,.T.);
#2338=EDGE_CURVE('',#1738,#1794,#1011,.T.);
#2339=EDGE_CURVE('',#1739,#1793,#1012,.T.);
#2340=EDGE_CURVE('',#1740,#1792,#1013,.T.);
#2341=EDGE_CURVE('',#1865,#1581,#1014,.T.);
#2342=EDGE_CURVE('',#1865,#1755,#1015,.T.);
#2343=EDGE_CURVE('',#1866,#1865,#698,.T.);
#2344=EDGE_CURVE('',#1866,#1580,#1016,.T.);
#2345=EDGE_CURVE('',#1867,#1866,#1017,.T.);
#2346=EDGE_CURVE('',#1867,#1579,#1018,.T.);
#2347=EDGE_CURVE('',#1867,#1868,#699,.T.);
#2348=EDGE_CURVE('',#1868,#1578,#1019,.T.);
#2349=EDGE_CURVE('',#1869,#1868,#1020,.T.);
#2350=EDGE_CURVE('',#1869,#1577,#1021,.T.);
#2351=EDGE_CURVE('',#1870,#1869,#700,.T.);
#2352=EDGE_CURVE('',#1870,#1576,#1022,.T.);
#2353=EDGE_CURVE('',#1870,#1871,#1023,.T.);
#2354=EDGE_CURVE('',#1871,#1575,#1024,.T.);
#2355=EDGE_CURVE('',#1872,#1871,#701,.T.);
#2356=EDGE_CURVE('',#1872,#1574,#1025,.T.);
#2357=EDGE_CURVE('',#1872,#1873,#1026,.T.);
#2358=EDGE_CURVE('',#1873,#1573,#1027,.T.);
#2359=EDGE_CURVE('',#1873,#1874,#702,.T.);
#2360=EDGE_CURVE('',#1874,#1572,#1028,.T.);
#2361=EDGE_CURVE('',#1875,#1874,#1029,.T.);
#2362=EDGE_CURVE('',#1875,#1571,#1030,.T.);
#2363=EDGE_CURVE('',#1875,#1876,#703,.T.);
#2364=EDGE_CURVE('',#1876,#1570,#1031,.T.);
#2365=EDGE_CURVE('',#1834,#1876,#1032,.T.);
#2366=EDGE_CURVE('',#1839,#1838,#1033,.T.);
#2367=EDGE_CURVE('',#1840,#1837,#1034,.T.);
#2368=EDGE_CURVE('',#1841,#1836,#1035,.T.);
#2369=EDGE_CURVE('',#1877,#1842,#704,.T.);
#2370=EDGE_CURVE('',#1877,#1566,#1036,.T.);
#2371=EDGE_CURVE('',#1878,#1877,#705,.T.);
#2372=EDGE_CURVE('',#1878,#1565,#1037,.T.);
#2373=EDGE_CURVE('',#1879,#1878,#1038,.T.);
#2374=EDGE_CURVE('',#1879,#1564,#1039,.T.);
#2375=EDGE_CURVE('',#1879,#1880,#706,.T.);
#2376=EDGE_CURVE('',#1880,#1563,#1040,.T.);
#2377=EDGE_CURVE('',#1881,#1880,#1041,.T.);
#2378=EDGE_CURVE('',#1881,#1562,#1042,.T.);
#2379=EDGE_CURVE('',#1827,#1881,#707,.T.);
#2380=EDGE_CURVE('',#1764,#1864,#1043,.T.);
#2381=EDGE_CURVE('',#1765,#1863,#1044,.T.);
#2382=EDGE_CURVE('',#1766,#1862,#1045,.T.);
#2383=EDGE_CURVE('',#1767,#1861,#1046,.T.);
#2384=EDGE_CURVE('',#1768,#1860,#1047,.T.);
#2385=EDGE_CURVE('',#1769,#1859,#1048,.T.);
#2386=EDGE_CURVE('',#1770,#1858,#1049,.T.);
#2387=EDGE_CURVE('',#1771,#1857,#1050,.T.);
#2388=EDGE_CURVE('',#1772,#1856,#1051,.T.);
#2389=EDGE_CURVE('',#1773,#1855,#1052,.T.);
#2390=EDGE_CURVE('',#1774,#1854,#1053,.T.);
#2391=EDGE_CURVE('',#1775,#1853,#1054,.T.);
#2392=EDGE_CURVE('',#1776,#1852,#1055,.T.);
#2393=EDGE_CURVE('',#1777,#1851,#1056,.T.);
#2394=EDGE_CURVE('',#1778,#1850,#1057,.T.);
#2395=EDGE_CURVE('',#1779,#1849,#1058,.T.);
#2396=EDGE_CURVE('',#1780,#1848,#1059,.T.);
#2397=EDGE_CURVE('',#1781,#1847,#1060,.T.);
#2398=EDGE_CURVE('',#1782,#1846,#1061,.T.);
#2399=EDGE_CURVE('',#1783,#1845,#1062,.T.);
#2400=EDGE_CURVE('',#1784,#1844,#1063,.T.);
#2401=EDGE_CURVE('',#1785,#1843,#1064,.T.);
#2402=EDGE_CURVE('',#1882,#1558,#1065,.T.);
#2403=EDGE_CURVE('',#1788,#1882,#1066,.T.);
#2404=EDGE_CURVE('',#1883,#1882,#708,.T.);
#2405=EDGE_CURVE('',#1883,#1557,#1067,.T.);
#2406=EDGE_CURVE('',#1883,#1884,#1068,.T.);
#2407=EDGE_CURVE('',#1884,#1556,#1069,.T.);
#2408=EDGE_CURVE('',#1715,#1884,#709,.T.);
#2409=EDGE_CURVE('',#1885,#1552,#1070,.T.);
#2410=EDGE_CURVE('',#1885,#1689,#1071,.T.);
#2411=EDGE_CURVE('',#1587,#1885,#710,.T.);
#2412=EDGE_CURVE('',#1533,#1605,#1072,.T.);
#2413=EDGE_CURVE('',#1534,#1604,#1073,.T.);
#2414=EDGE_CURVE('',#1535,#1603,#1074,.T.);
#2415=EDGE_CURVE('',#1536,#1602,#1075,.T.);
#2416=EDGE_CURVE('',#1537,#1601,#1076,.T.);
#2417=EDGE_CURVE('',#1538,#1600,#1077,.T.);
#2418=EDGE_CURVE('',#1539,#1599,#1078,.T.);
#2419=EDGE_CURVE('',#1540,#1598,#1079,.T.);
#2420=EDGE_CURVE('',#1633,#1629,#1080,.T.);
#2421=EDGE_CURVE('',#1656,#1713,#1081,.T.);
#2422=EDGE_CURVE('',#1657,#1712,#1082,.T.);
#2423=EDGE_CURVE('',#1658,#1711,#1083,.T.);
#2424=EDGE_CURVE('',#1659,#1710,#1084,.T.);
#2425=EDGE_CURVE('',#1660,#1709,#1085,.T.);
#2426=EDGE_CURVE('',#1661,#1708,#1086,.T.);
#2427=EDGE_CURVE('',#1662,#1707,#1087,.T.);
#2428=EDGE_CURVE('',#1663,#1706,#1088,.T.);
#2429=EDGE_CURVE('',#1664,#1705,#1089,.T.);
#2430=EDGE_CURVE('',#1665,#1704,#1090,.T.);
#2431=EDGE_CURVE('',#1666,#1703,#1091,.T.);
#2432=EDGE_CURVE('',#1667,#1702,#1092,.T.);
#2433=EDGE_CURVE('',#1668,#1701,#1093,.T.);
#2434=EDGE_CURVE('',#1669,#1700,#1094,.T.);
#2435=EDGE_CURVE('',#1670,#1699,#1095,.T.);
#2436=EDGE_CURVE('',#1671,#1698,#1096,.T.);
#2437=EDGE_CURVE('',#1672,#1697,#1097,.T.);
#2438=EDGE_CURVE('',#1673,#1696,#1098,.T.);
#2439=EDGE_CURVE('',#1674,#1695,#1099,.T.);
#2440=EDGE_CURVE('',#1675,#1694,#1100,.T.);
#2441=EDGE_CURVE('',#1676,#1693,#1101,.T.);
#2442=EDGE_CURVE('',#1619,#1650,#1102,.T.);
#2443=EDGE_CURVE('',#1637,#1683,#1103,.T.);
#2444=EDGE_CURVE('',#1638,#1682,#1104,.T.);
#2445=EDGE_CURVE('',#1623,#1648,#1105,.T.);
#2446=EDGE_CURVE('',#1624,#1647,#1106,.T.);
#2447=EDGE_CURVE('',#1625,#1646,#1107,.T.);
#2448=EDGE_CURVE('',#1886,#1585,#1108,.T.);
#2449=EDGE_CURVE('',#1546,#1886,#1109,.T.);
#2450=EDGE_CURVE('',#1760,#1886,#711,.T.);
#2451=EDGE_CURVE('',#1719,#1813,#1110,.T.);
#2452=EDGE_CURVE('',#1720,#1812,#1111,.T.);
#2453=EDGE_CURVE('',#1721,#1811,#1112,.T.);
#2454=EDGE_CURVE('',#1722,#1810,#1113,.T.);
#2455=EDGE_CURVE('',#1723,#1809,#1114,.T.);
#2456=EDGE_CURVE('',#1724,#1808,#1115,.T.);
#2457=EDGE_CURVE('',#1725,#1807,#1116,.T.);
#2458=EDGE_CURVE('',#1726,#1806,#1117,.T.);
#2459=EDGE_CURVE('',#1727,#1805,#1118,.T.);
#2460=EDGE_CURVE('',#1728,#1804,#1119,.T.);
#2461=EDGE_CURVE('',#1729,#1803,#1120,.T.);
#2462=ORIENTED_EDGE('',*,*,#1887,.T.);
#2463=ORIENTED_EDGE('',*,*,#1888,.T.);
#2464=ORIENTED_EDGE('',*,*,#1889,.F.);
#2465=ORIENTED_EDGE('',*,*,#1890,.T.);
#2466=ORIENTED_EDGE('',*,*,#1891,.T.);
#2467=ORIENTED_EDGE('',*,*,#1892,.F.);
#2468=ORIENTED_EDGE('',*,*,#1893,.T.);
#2469=ORIENTED_EDGE('',*,*,#1894,.T.);
#2470=ORIENTED_EDGE('',*,*,#1895,.T.);
#2471=ORIENTED_EDGE('',*,*,#1896,.T.);
#2472=ORIENTED_EDGE('',*,*,#1897,.F.);
#2473=ORIENTED_EDGE('',*,*,#1898,.T.);
#2474=ORIENTED_EDGE('',*,*,#1899,.F.);
#2475=ORIENTED_EDGE('',*,*,#1900,.T.);
#2476=ORIENTED_EDGE('',*,*,#1901,.F.);
#2477=ORIENTED_EDGE('',*,*,#1902,.F.);
#2478=ORIENTED_EDGE('',*,*,#1903,.T.);
#2479=ORIENTED_EDGE('',*,*,#1904,.F.);
#2480=ORIENTED_EDGE('',*,*,#1905,.F.);
#2481=ORIENTED_EDGE('',*,*,#1906,.T.);
#2482=ORIENTED_EDGE('',*,*,#1907,.T.);
#2483=ORIENTED_EDGE('',*,*,#1902,.T.);
#2484=ORIENTED_EDGE('',*,*,#1908,.T.);
#2485=ORIENTED_EDGE('',*,*,#1909,.F.);
#2486=ORIENTED_EDGE('',*,*,#1910,.F.);
#2487=ORIENTED_EDGE('',*,*,#1888,.F.);
#2488=ORIENTED_EDGE('',*,*,#1901,.T.);
#2489=ORIENTED_EDGE('',*,*,#1900,.F.);
#2490=ORIENTED_EDGE('',*,*,#1911,.F.);
#2491=ORIENTED_EDGE('',*,*,#1912,.F.);
#2492=ORIENTED_EDGE('',*,*,#1907,.F.);
#2493=ORIENTED_EDGE('',*,*,#1913,.T.);
#2494=ORIENTED_EDGE('',*,*,#1914,.T.);
#2495=ORIENTED_EDGE('',*,*,#1915,.T.);
#2496=ORIENTED_EDGE('',*,*,#1916,.F.);
#2497=ORIENTED_EDGE('',*,*,#1917,.T.);
#2498=ORIENTED_EDGE('',*,*,#1918,.T.);
#2499=ORIENTED_EDGE('',*,*,#1919,.T.);
#2500=ORIENTED_EDGE('',*,*,#1920,.T.);
#2501=ORIENTED_EDGE('',*,*,#1921,.T.);
#2502=ORIENTED_EDGE('',*,*,#1922,.F.);
#2503=ORIENTED_EDGE('',*,*,#1923,.T.);
#2504=ORIENTED_EDGE('',*,*,#1924,.T.);
#2505=ORIENTED_EDGE('',*,*,#1925,.T.);
#2506=ORIENTED_EDGE('',*,*,#1926,.T.);
#2507=ORIENTED_EDGE('',*,*,#1927,.T.);
#2508=ORIENTED_EDGE('',*,*,#1928,.T.);
#2509=ORIENTED_EDGE('',*,*,#1929,.T.);
#2510=ORIENTED_EDGE('',*,*,#1930,.T.);
#2511=ORIENTED_EDGE('',*,*,#1931,.F.);
#2512=ORIENTED_EDGE('',*,*,#1932,.T.);
#2513=ORIENTED_EDGE('',*,*,#1933,.T.);
#2514=ORIENTED_EDGE('',*,*,#1934,.T.);
#2515=ORIENTED_EDGE('',*,*,#1935,.T.);
#2516=ORIENTED_EDGE('',*,*,#1936,.T.);
#2517=ORIENTED_EDGE('',*,*,#1937,.T.);
#2518=ORIENTED_EDGE('',*,*,#1938,.T.);
#2519=ORIENTED_EDGE('',*,*,#1939,.T.);
#2520=ORIENTED_EDGE('',*,*,#1940,.T.);
#2521=ORIENTED_EDGE('',*,*,#1941,.T.);
#2522=ORIENTED_EDGE('',*,*,#1942,.T.);
#2523=ORIENTED_EDGE('',*,*,#1943,.T.);
#2524=ORIENTED_EDGE('',*,*,#1944,.T.);
#2525=ORIENTED_EDGE('',*,*,#1945,.F.);
#2526=ORIENTED_EDGE('',*,*,#1946,.T.);
#2527=ORIENTED_EDGE('',*,*,#1947,.T.);
#2528=ORIENTED_EDGE('',*,*,#1948,.T.);
#2529=ORIENTED_EDGE('',*,*,#1949,.F.);
#2530=ORIENTED_EDGE('',*,*,#1950,.F.);
#2531=ORIENTED_EDGE('',*,*,#1951,.T.);
#2532=ORIENTED_EDGE('',*,*,#1913,.F.);
#2533=ORIENTED_EDGE('',*,*,#1887,.F.);
#2534=ORIENTED_EDGE('',*,*,#1904,.T.);
#2535=ORIENTED_EDGE('',*,*,#1952,.T.);
#2536=ORIENTED_EDGE('',*,*,#1953,.T.);
#2537=ORIENTED_EDGE('',*,*,#1954,.T.);
#2538=ORIENTED_EDGE('',*,*,#1955,.T.);
#2539=ORIENTED_EDGE('',*,*,#1956,.F.);
#2540=ORIENTED_EDGE('',*,*,#1957,.T.);
#2541=ORIENTED_EDGE('',*,*,#1958,.F.);
#2542=ORIENTED_EDGE('',*,*,#1959,.F.);
#2543=ORIENTED_EDGE('',*,*,#1911,.T.);
#2544=ORIENTED_EDGE('',*,*,#1960,.F.);
#2545=ORIENTED_EDGE('',*,*,#1961,.F.);
#2546=ORIENTED_EDGE('',*,*,#1962,.T.);
#2547=ORIENTED_EDGE('',*,*,#1963,.T.);
#2548=ORIENTED_EDGE('',*,*,#1959,.T.);
#2549=ORIENTED_EDGE('',*,*,#1964,.T.);
#2550=ORIENTED_EDGE('',*,*,#1965,.F.);
#2551=ORIENTED_EDGE('',*,*,#1966,.F.);
#2552=ORIENTED_EDGE('',*,*,#1955,.F.);
#2553=ORIENTED_EDGE('',*,*,#1958,.T.);
#2554=ORIENTED_EDGE('',*,*,#1957,.F.);
#2555=ORIENTED_EDGE('',*,*,#1967,.F.);
#2556=ORIENTED_EDGE('',*,*,#1968,.F.);
#2557=ORIENTED_EDGE('',*,*,#1905,.T.);
#2558=ORIENTED_EDGE('',*,*,#1912,.T.);
#2559=ORIENTED_EDGE('',*,*,#1963,.F.);
#2560=ORIENTED_EDGE('',*,*,#1969,.T.);
#2561=ORIENTED_EDGE('',*,*,#1970,.T.);
#2562=ORIENTED_EDGE('',*,*,#1971,.T.);
#2563=ORIENTED_EDGE('',*,*,#1972,.T.);
#2564=ORIENTED_EDGE('',*,*,#1973,.T.);
#2565=ORIENTED_EDGE('',*,*,#1974,.T.);
#2566=ORIENTED_EDGE('',*,*,#1975,.T.);
#2567=ORIENTED_EDGE('',*,*,#1976,.T.);
#2568=ORIENTED_EDGE('',*,*,#1977,.T.);
#2569=ORIENTED_EDGE('',*,*,#1978,.T.);
#2570=ORIENTED_EDGE('',*,*,#1909,.T.);
#2571=ORIENTED_EDGE('',*,*,#1979,.F.);
#2572=ORIENTED_EDGE('',*,*,#1898,.F.);
#2573=ORIENTED_EDGE('',*,*,#1980,.T.);
#2574=ORIENTED_EDGE('',*,*,#1969,.F.);
#2575=ORIENTED_EDGE('',*,*,#1954,.F.);
#2576=ORIENTED_EDGE('',*,*,#1960,.T.);
#2577=ORIENTED_EDGE('',*,*,#1899,.T.);
#2578=ORIENTED_EDGE('',*,*,#1981,.T.);
#2579=ORIENTED_EDGE('',*,*,#1982,.T.);
#2580=ORIENTED_EDGE('',*,*,#1983,.T.);
#2581=ORIENTED_EDGE('',*,*,#1984,.F.);
#2582=ORIENTED_EDGE('',*,*,#1985,.T.);
#2583=ORIENTED_EDGE('',*,*,#1986,.F.);
#2584=ORIENTED_EDGE('',*,*,#1987,.F.);
#2585=ORIENTED_EDGE('',*,*,#1967,.T.);
#2586=ORIENTED_EDGE('',*,*,#1988,.F.);
#2587=ORIENTED_EDGE('',*,*,#1989,.F.);
#2588=ORIENTED_EDGE('',*,*,#1990,.T.);
#2589=ORIENTED_EDGE('',*,*,#1991,.T.);
#2590=ORIENTED_EDGE('',*,*,#1987,.T.);
#2591=ORIENTED_EDGE('',*,*,#1992,.T.);
#2592=ORIENTED_EDGE('',*,*,#1993,.F.);
#2593=ORIENTED_EDGE('',*,*,#1994,.F.);
#2594=ORIENTED_EDGE('',*,*,#1983,.F.);
#2595=ORIENTED_EDGE('',*,*,#1986,.T.);
#2596=ORIENTED_EDGE('',*,*,#1985,.F.);
#2597=ORIENTED_EDGE('',*,*,#1995,.F.);
#2598=ORIENTED_EDGE('',*,*,#1996,.F.);
#2599=ORIENTED_EDGE('',*,*,#1961,.T.);
#2600=ORIENTED_EDGE('',*,*,#1968,.T.);
#2601=ORIENTED_EDGE('',*,*,#1991,.F.);
#2602=ORIENTED_EDGE('',*,*,#1965,.T.);
#2603=ORIENTED_EDGE('',*,*,#1964,.F.);
#2604=ORIENTED_EDGE('',*,*,#1981,.F.);
#2605=ORIENTED_EDGE('',*,*,#1979,.T.);
#2606=ORIENTED_EDGE('',*,*,#1962,.F.);
#2607=ORIENTED_EDGE('',*,*,#1982,.F.);
#2608=ORIENTED_EDGE('',*,*,#1988,.T.);
#2609=ORIENTED_EDGE('',*,*,#1956,.T.);
#2610=ORIENTED_EDGE('',*,*,#1997,.T.);
#2611=ORIENTED_EDGE('',*,*,#1998,.T.);
#2612=ORIENTED_EDGE('',*,*,#1999,.F.);
#2613=ORIENTED_EDGE('',*,*,#2000,.F.);
#2614=ORIENTED_EDGE('',*,*,#2001,.F.);
#2615=ORIENTED_EDGE('',*,*,#2002,.F.);
#2616=ORIENTED_EDGE('',*,*,#2003,.F.);
#2617=ORIENTED_EDGE('',*,*,#2004,.F.);
#2618=ORIENTED_EDGE('',*,*,#2005,.F.);
#2619=ORIENTED_EDGE('',*,*,#2006,.T.);
#2620=ORIENTED_EDGE('',*,*,#2007,.T.);
#2621=ORIENTED_EDGE('',*,*,#2008,.T.);
#2622=ORIENTED_EDGE('',*,*,#2009,.F.);
#2623=ORIENTED_EDGE('',*,*,#2010,.T.);
#2624=ORIENTED_EDGE('',*,*,#2011,.F.);
#2625=ORIENTED_EDGE('',*,*,#2012,.F.);
#2626=ORIENTED_EDGE('',*,*,#1995,.T.);
#2627=ORIENTED_EDGE('',*,*,#2013,.F.);
#2628=ORIENTED_EDGE('',*,*,#2014,.F.);
#2629=ORIENTED_EDGE('',*,*,#2015,.T.);
#2630=ORIENTED_EDGE('',*,*,#2016,.T.);
#2631=ORIENTED_EDGE('',*,*,#2017,.T.);
#2632=ORIENTED_EDGE('',*,*,#2012,.T.);
#2633=ORIENTED_EDGE('',*,*,#2018,.T.);
#2634=ORIENTED_EDGE('',*,*,#2019,.T.);
#2635=ORIENTED_EDGE('',*,*,#2020,.F.);
#2636=ORIENTED_EDGE('',*,*,#2021,.F.);
#2637=ORIENTED_EDGE('',*,*,#1999,.T.);
#2638=ORIENTED_EDGE('',*,*,#2011,.T.);
#2639=ORIENTED_EDGE('',*,*,#2010,.F.);
#2640=ORIENTED_EDGE('',*,*,#2022,.T.);
#2641=ORIENTED_EDGE('',*,*,#2023,.F.);
#2642=ORIENTED_EDGE('',*,*,#1989,.T.);
#2643=ORIENTED_EDGE('',*,*,#1996,.T.);
#2644=ORIENTED_EDGE('',*,*,#2017,.F.);
#2645=ORIENTED_EDGE('',*,*,#1993,.T.);
#2646=ORIENTED_EDGE('',*,*,#1992,.F.);
#2647=ORIENTED_EDGE('',*,*,#1997,.F.);
#2648=ORIENTED_EDGE('',*,*,#1966,.T.);
#2649=ORIENTED_EDGE('',*,*,#1990,.F.);
#2650=ORIENTED_EDGE('',*,*,#1998,.F.);
#2651=ORIENTED_EDGE('',*,*,#2013,.T.);
#2652=ORIENTED_EDGE('',*,*,#1984,.T.);
#2653=ORIENTED_EDGE('',*,*,#2024,.T.);
#2654=ORIENTED_EDGE('',*,*,#2025,.F.);
#2655=ORIENTED_EDGE('',*,*,#2026,.T.);
#2656=ORIENTED_EDGE('',*,*,#2027,.F.);
#2657=ORIENTED_EDGE('',*,*,#2028,.T.);
#2658=ORIENTED_EDGE('',*,*,#2029,.F.);
#2659=ORIENTED_EDGE('',*,*,#2030,.T.);
#2660=ORIENTED_EDGE('',*,*,#2031,.T.);
#2661=ORIENTED_EDGE('',*,*,#2032,.F.);
#2662=ORIENTED_EDGE('',*,*,#2033,.F.);
#2663=ORIENTED_EDGE('',*,*,#2034,.T.);
#2664=ORIENTED_EDGE('',*,*,#2035,.F.);
#2665=ORIENTED_EDGE('',*,*,#2036,.F.);
#2666=ORIENTED_EDGE('',*,*,#2037,.T.);
#2667=ORIENTED_EDGE('',*,*,#2038,.T.);
#2668=ORIENTED_EDGE('',*,*,#2033,.T.);
#2669=ORIENTED_EDGE('',*,*,#2039,.T.);
#2670=ORIENTED_EDGE('',*,*,#2040,.F.);
#2671=ORIENTED_EDGE('',*,*,#2041,.F.);
#2672=ORIENTED_EDGE('',*,*,#2027,.T.);
#2673=ORIENTED_EDGE('',*,*,#2032,.T.);
#2674=ORIENTED_EDGE('',*,*,#2031,.F.);
#2675=ORIENTED_EDGE('',*,*,#2042,.T.);
#2676=ORIENTED_EDGE('',*,*,#2043,.F.);
#2677=ORIENTED_EDGE('',*,*,#2014,.T.);
#2678=ORIENTED_EDGE('',*,*,#2023,.T.);
#2679=ORIENTED_EDGE('',*,*,#2044,.T.);
#2680=ORIENTED_EDGE('',*,*,#2045,.T.);
#2681=ORIENTED_EDGE('',*,*,#2046,.T.);
#2682=ORIENTED_EDGE('',*,*,#2047,.T.);
#2683=ORIENTED_EDGE('',*,*,#2038,.F.);
#2684=ORIENTED_EDGE('',*,*,#2048,.T.);
#2685=ORIENTED_EDGE('',*,*,#2049,.T.);
#2686=ORIENTED_EDGE('',*,*,#2050,.T.);
#2687=ORIENTED_EDGE('',*,*,#2051,.F.);
#2688=ORIENTED_EDGE('',*,*,#2052,.T.);
#2689=ORIENTED_EDGE('',*,*,#2020,.T.);
#2690=ORIENTED_EDGE('',*,*,#2053,.F.);
#2691=ORIENTED_EDGE('',*,*,#2004,.T.);
#2692=ORIENTED_EDGE('',*,*,#2054,.T.);
#2693=ORIENTED_EDGE('',*,*,#2048,.F.);
#2694=ORIENTED_EDGE('',*,*,#2026,.F.);
#2695=ORIENTED_EDGE('',*,*,#2035,.T.);
#2696=ORIENTED_EDGE('',*,*,#2005,.T.);
#2697=ORIENTED_EDGE('',*,*,#2055,.F.);
#2698=ORIENTED_EDGE('',*,*,#2056,.T.);
#2699=ORIENTED_EDGE('',*,*,#2057,.F.);
#2700=ORIENTED_EDGE('',*,*,#2058,.T.);
#2701=ORIENTED_EDGE('',*,*,#2059,.F.);
#2702=ORIENTED_EDGE('',*,*,#2060,.T.);
#2703=ORIENTED_EDGE('',*,*,#2061,.T.);
#2704=ORIENTED_EDGE('',*,*,#2062,.F.);
#2705=ORIENTED_EDGE('',*,*,#2063,.F.);
#2706=ORIENTED_EDGE('',*,*,#2064,.F.);
#2707=ORIENTED_EDGE('',*,*,#2065,.F.);
#2708=ORIENTED_EDGE('',*,*,#2066,.T.);
#2709=ORIENTED_EDGE('',*,*,#2067,.F.);
#2710=ORIENTED_EDGE('',*,*,#2068,.T.);
#2711=ORIENTED_EDGE('',*,*,#2069,.F.);
#2712=ORIENTED_EDGE('',*,*,#2070,.T.);
#2713=ORIENTED_EDGE('',*,*,#2071,.F.);
#2714=ORIENTED_EDGE('',*,*,#2072,.T.);
#2715=ORIENTED_EDGE('',*,*,#2073,.F.);
#2716=ORIENTED_EDGE('',*,*,#2074,.F.);
#2717=ORIENTED_EDGE('',*,*,#2075,.F.);
#2718=ORIENTED_EDGE('',*,*,#2076,.F.);
#2719=ORIENTED_EDGE('',*,*,#2077,.T.);
#2720=ORIENTED_EDGE('',*,*,#2078,.T.);
#2721=ORIENTED_EDGE('',*,*,#2079,.F.);
#2722=ORIENTED_EDGE('',*,*,#2080,.F.);
#2723=ORIENTED_EDGE('',*,*,#2081,.F.);
#2724=ORIENTED_EDGE('',*,*,#2082,.F.);
#2725=ORIENTED_EDGE('',*,*,#2083,.T.);
#2726=ORIENTED_EDGE('',*,*,#2084,.T.);
#2727=ORIENTED_EDGE('',*,*,#2085,.F.);
#2728=ORIENTED_EDGE('',*,*,#2086,.T.);
#2729=ORIENTED_EDGE('',*,*,#2051,.T.);
#2730=ORIENTED_EDGE('',*,*,#2082,.T.);
#2731=ORIENTED_EDGE('',*,*,#2087,.T.);
#2732=ORIENTED_EDGE('',*,*,#2088,.F.);
#2733=ORIENTED_EDGE('',*,*,#2089,.F.);
#2734=ORIENTED_EDGE('',*,*,#2057,.T.);
#2735=ORIENTED_EDGE('',*,*,#2081,.T.);
#2736=ORIENTED_EDGE('',*,*,#2080,.T.);
#2737=ORIENTED_EDGE('',*,*,#2090,.T.);
#2738=ORIENTED_EDGE('',*,*,#2091,.F.);
#2739=ORIENTED_EDGE('',*,*,#2036,.T.);
#2740=ORIENTED_EDGE('',*,*,#2043,.T.);
#2741=ORIENTED_EDGE('',*,*,#2092,.T.);
#2742=ORIENTED_EDGE('',*,*,#2093,.T.);
#2743=ORIENTED_EDGE('',*,*,#2094,.T.);
#2744=ORIENTED_EDGE('',*,*,#2040,.T.);
#2745=ORIENTED_EDGE('',*,*,#2095,.F.);
#2746=ORIENTED_EDGE('',*,*,#2000,.T.);
#2747=ORIENTED_EDGE('',*,*,#2021,.T.);
#2748=ORIENTED_EDGE('',*,*,#2052,.F.);
#2749=ORIENTED_EDGE('',*,*,#2056,.F.);
#2750=ORIENTED_EDGE('',*,*,#2084,.F.);
#2751=ORIENTED_EDGE('',*,*,#2001,.T.);
#2752=ORIENTED_EDGE('',*,*,#2096,.F.);
#2753=ORIENTED_EDGE('',*,*,#2097,.T.);
#2754=ORIENTED_EDGE('',*,*,#2098,.F.);
#2755=ORIENTED_EDGE('',*,*,#2099,.F.);
#2756=ORIENTED_EDGE('',*,*,#2100,.F.);
#2757=ORIENTED_EDGE('',*,*,#2101,.F.);
#2758=ORIENTED_EDGE('',*,*,#2102,.F.);
#2759=ORIENTED_EDGE('',*,*,#2103,.T.);
#2760=ORIENTED_EDGE('',*,*,#2104,.T.);
#2761=ORIENTED_EDGE('',*,*,#2105,.F.);
#2762=ORIENTED_EDGE('',*,*,#2106,.T.);
#2763=ORIENTED_EDGE('',*,*,#1916,.T.);
#2764=ORIENTED_EDGE('',*,*,#2102,.T.);
#2765=ORIENTED_EDGE('',*,*,#2107,.T.);
#2766=ORIENTED_EDGE('',*,*,#2108,.F.);
#2767=ORIENTED_EDGE('',*,*,#2109,.F.);
#2768=ORIENTED_EDGE('',*,*,#2098,.T.);
#2769=ORIENTED_EDGE('',*,*,#2101,.T.);
#2770=ORIENTED_EDGE('',*,*,#2100,.T.);
#2771=ORIENTED_EDGE('',*,*,#2110,.T.);
#2772=ORIENTED_EDGE('',*,*,#2111,.F.);
#2773=ORIENTED_EDGE('',*,*,#2085,.T.);
#2774=ORIENTED_EDGE('',*,*,#2091,.T.);
#2775=ORIENTED_EDGE('',*,*,#2112,.T.);
#2776=ORIENTED_EDGE('',*,*,#2113,.T.);
#2777=ORIENTED_EDGE('',*,*,#2114,.T.);
#2778=ORIENTED_EDGE('',*,*,#2115,.T.);
#2779=ORIENTED_EDGE('',*,*,#2116,.T.);
#2780=ORIENTED_EDGE('',*,*,#2117,.T.);
#2781=ORIENTED_EDGE('',*,*,#2118,.T.);
#2782=ORIENTED_EDGE('',*,*,#2119,.T.);
#2783=ORIENTED_EDGE('',*,*,#2120,.T.);
#2784=ORIENTED_EDGE('',*,*,#2121,.T.);
#2785=ORIENTED_EDGE('',*,*,#2122,.T.);
#2786=ORIENTED_EDGE('',*,*,#2123,.T.);
#2787=ORIENTED_EDGE('',*,*,#2124,.T.);
#2788=ORIENTED_EDGE('',*,*,#2125,.T.);
#2789=ORIENTED_EDGE('',*,*,#2126,.T.);
#2790=ORIENTED_EDGE('',*,*,#2127,.T.);
#2791=ORIENTED_EDGE('',*,*,#2128,.T.);
#2792=ORIENTED_EDGE('',*,*,#2129,.T.);
#2793=ORIENTED_EDGE('',*,*,#2130,.T.);
#2794=ORIENTED_EDGE('',*,*,#2131,.T.);
#2795=ORIENTED_EDGE('',*,*,#2132,.T.);
#2796=ORIENTED_EDGE('',*,*,#2133,.T.);
#2797=ORIENTED_EDGE('',*,*,#2088,.T.);
#2798=ORIENTED_EDGE('',*,*,#2134,.F.);
#2799=ORIENTED_EDGE('',*,*,#2135,.T.);
#2800=ORIENTED_EDGE('',*,*,#2136,.T.);
#2801=ORIENTED_EDGE('',*,*,#1917,.F.);
#2802=ORIENTED_EDGE('',*,*,#2097,.F.);
#2803=ORIENTED_EDGE('',*,*,#2104,.F.);
#2804=ORIENTED_EDGE('',*,*,#2137,.T.);
#2805=ORIENTED_EDGE('',*,*,#2138,.F.);
#2806=ORIENTED_EDGE('',*,*,#2139,.T.);
#2807=ORIENTED_EDGE('',*,*,#2140,.T.);
#2808=ORIENTED_EDGE('',*,*,#2141,.F.);
#2809=ORIENTED_EDGE('',*,*,#2142,.T.);
#2810=ORIENTED_EDGE('',*,*,#2143,.T.);
#2811=ORIENTED_EDGE('',*,*,#2144,.T.);
#2812=ORIENTED_EDGE('',*,*,#2145,.T.);
#2813=ORIENTED_EDGE('',*,*,#2146,.T.);
#2814=ORIENTED_EDGE('',*,*,#2147,.T.);
#2815=ORIENTED_EDGE('',*,*,#2148,.F.);
#2816=ORIENTED_EDGE('',*,*,#2149,.F.);
#2817=ORIENTED_EDGE('',*,*,#2150,.F.);
#2818=ORIENTED_EDGE('',*,*,#2151,.F.);
#2819=ORIENTED_EDGE('',*,*,#2152,.F.);
#2820=ORIENTED_EDGE('',*,*,#2153,.T.);
#2821=ORIENTED_EDGE('',*,*,#2154,.T.);
#2822=ORIENTED_EDGE('',*,*,#2155,.T.);
#2823=ORIENTED_EDGE('',*,*,#2156,.F.);
#2824=ORIENTED_EDGE('',*,*,#2157,.T.);
#2825=ORIENTED_EDGE('',*,*,#2158,.F.);
#2826=ORIENTED_EDGE('',*,*,#2159,.T.);
#2827=ORIENTED_EDGE('',*,*,#2160,.F.);
#2828=ORIENTED_EDGE('',*,*,#2161,.F.);
#2829=ORIENTED_EDGE('',*,*,#2162,.T.);
#2830=ORIENTED_EDGE('',*,*,#2163,.F.);
#2831=ORIENTED_EDGE('',*,*,#2164,.F.);
#2832=ORIENTED_EDGE('',*,*,#2165,.T.);
#2833=ORIENTED_EDGE('',*,*,#2166,.T.);
#2834=ORIENTED_EDGE('',*,*,#2167,.T.);
#2835=ORIENTED_EDGE('',*,*,#2168,.T.);
#2836=ORIENTED_EDGE('',*,*,#2169,.T.);
#2837=ORIENTED_EDGE('',*,*,#2170,.T.);
#2838=ORIENTED_EDGE('',*,*,#2171,.T.);
#2839=ORIENTED_EDGE('',*,*,#2172,.T.);
#2840=ORIENTED_EDGE('',*,*,#2173,.T.);
#2841=ORIENTED_EDGE('',*,*,#2174,.T.);
#2842=ORIENTED_EDGE('',*,*,#2175,.T.);
#2843=ORIENTED_EDGE('',*,*,#2176,.T.);
#2844=ORIENTED_EDGE('',*,*,#2177,.F.);
#2845=ORIENTED_EDGE('',*,*,#2178,.F.);
#2846=ORIENTED_EDGE('',*,*,#2179,.T.);
#2847=ORIENTED_EDGE('',*,*,#2180,.T.);
#2848=ORIENTED_EDGE('',*,*,#2181,.F.);
#2849=ORIENTED_EDGE('',*,*,#2182,.T.);
#2850=ORIENTED_EDGE('',*,*,#1945,.T.);
#2851=ORIENTED_EDGE('',*,*,#2178,.T.);
#2852=ORIENTED_EDGE('',*,*,#2183,.T.);
#2853=ORIENTED_EDGE('',*,*,#2184,.F.);
#2854=ORIENTED_EDGE('',*,*,#2185,.F.);
#2855=ORIENTED_EDGE('',*,*,#2140,.F.);
#2856=ORIENTED_EDGE('',*,*,#2177,.T.);
#2857=ORIENTED_EDGE('',*,*,#2164,.T.);
#2858=ORIENTED_EDGE('',*,*,#2186,.T.);
#2859=ORIENTED_EDGE('',*,*,#2187,.F.);
#2860=ORIENTED_EDGE('',*,*,#2105,.T.);
#2861=ORIENTED_EDGE('',*,*,#2111,.T.);
#2862=ORIENTED_EDGE('',*,*,#2188,.T.);
#2863=ORIENTED_EDGE('',*,*,#2108,.T.);
#2864=ORIENTED_EDGE('',*,*,#2189,.F.);
#2865=ORIENTED_EDGE('',*,*,#2190,.F.);
#2866=ORIENTED_EDGE('',*,*,#2191,.T.);
#2867=ORIENTED_EDGE('',*,*,#1946,.F.);
#2868=ORIENTED_EDGE('',*,*,#2139,.F.);
#2869=ORIENTED_EDGE('',*,*,#2180,.F.);
#2870=ORIENTED_EDGE('',*,*,#2192,.T.);
#2871=ORIENTED_EDGE('',*,*,#2193,.T.);
#2872=ORIENTED_EDGE('',*,*,#2194,.T.);
#2873=ORIENTED_EDGE('',*,*,#2195,.F.);
#2874=ORIENTED_EDGE('',*,*,#2196,.F.);
#2875=ORIENTED_EDGE('',*,*,#2197,.T.);
#2876=ORIENTED_EDGE('',*,*,#2198,.T.);
#2877=ORIENTED_EDGE('',*,*,#2199,.F.);
#2878=ORIENTED_EDGE('',*,*,#2200,.F.);
#2879=ORIENTED_EDGE('',*,*,#2201,.T.);
#2880=ORIENTED_EDGE('',*,*,#2202,.F.);
#2881=ORIENTED_EDGE('',*,*,#2203,.T.);
#2882=ORIENTED_EDGE('',*,*,#2204,.T.);
#2883=ORIENTED_EDGE('',*,*,#2205,.T.);
#2884=ORIENTED_EDGE('',*,*,#2206,.T.);
#2885=ORIENTED_EDGE('',*,*,#2207,.T.);
#2886=ORIENTED_EDGE('',*,*,#2208,.T.);
#2887=ORIENTED_EDGE('',*,*,#2209,.T.);
#2888=ORIENTED_EDGE('',*,*,#2210,.T.);
#2889=ORIENTED_EDGE('',*,*,#2211,.T.);
#2890=ORIENTED_EDGE('',*,*,#2212,.F.);
#2891=ORIENTED_EDGE('',*,*,#2213,.T.);
#2892=ORIENTED_EDGE('',*,*,#2214,.F.);
#2893=ORIENTED_EDGE('',*,*,#2215,.F.);
#2894=ORIENTED_EDGE('',*,*,#2216,.T.);
#2895=ORIENTED_EDGE('',*,*,#2217,.T.);
#2896=ORIENTED_EDGE('',*,*,#2218,.T.);
#2897=ORIENTED_EDGE('',*,*,#2219,.T.);
#2898=ORIENTED_EDGE('',*,*,#2220,.F.);
#2899=ORIENTED_EDGE('',*,*,#2221,.F.);
#2900=ORIENTED_EDGE('',*,*,#2222,.T.);
#2901=ORIENTED_EDGE('',*,*,#2223,.F.);
#2902=ORIENTED_EDGE('',*,*,#2224,.F.);
#2903=ORIENTED_EDGE('',*,*,#2225,.T.);
#2904=ORIENTED_EDGE('',*,*,#1922,.T.);
#2905=ORIENTED_EDGE('',*,*,#2221,.T.);
#2906=ORIENTED_EDGE('',*,*,#2226,.T.);
#2907=ORIENTED_EDGE('',*,*,#2227,.F.);
#2908=ORIENTED_EDGE('',*,*,#2228,.F.);
#2909=ORIENTED_EDGE('',*,*,#2195,.T.);
#2910=ORIENTED_EDGE('',*,*,#2220,.T.);
#2911=ORIENTED_EDGE('',*,*,#2219,.F.);
#2912=ORIENTED_EDGE('',*,*,#2229,.T.);
#2913=ORIENTED_EDGE('',*,*,#2230,.F.);
#2914=ORIENTED_EDGE('',*,*,#2181,.T.);
#2915=ORIENTED_EDGE('',*,*,#2187,.T.);
#2916=ORIENTED_EDGE('',*,*,#2231,.T.);
#2917=ORIENTED_EDGE('',*,*,#2232,.T.);
#2918=ORIENTED_EDGE('',*,*,#2233,.T.);
#2919=ORIENTED_EDGE('',*,*,#2234,.T.);
#2920=ORIENTED_EDGE('',*,*,#2235,.T.);
#2921=ORIENTED_EDGE('',*,*,#2236,.T.);
#2922=ORIENTED_EDGE('',*,*,#2237,.T.);
#2923=ORIENTED_EDGE('',*,*,#2238,.T.);
#2924=ORIENTED_EDGE('',*,*,#2239,.T.);
#2925=ORIENTED_EDGE('',*,*,#2240,.T.);
#2926=ORIENTED_EDGE('',*,*,#2241,.T.);
#2927=ORIENTED_EDGE('',*,*,#2242,.T.);
#2928=ORIENTED_EDGE('',*,*,#2243,.T.);
#2929=ORIENTED_EDGE('',*,*,#2244,.T.);
#2930=ORIENTED_EDGE('',*,*,#2245,.T.);
#2931=ORIENTED_EDGE('',*,*,#2246,.T.);
#2932=ORIENTED_EDGE('',*,*,#2247,.T.);
#2933=ORIENTED_EDGE('',*,*,#2248,.T.);
#2934=ORIENTED_EDGE('',*,*,#2249,.T.);
#2935=ORIENTED_EDGE('',*,*,#2250,.T.);
#2936=ORIENTED_EDGE('',*,*,#2251,.T.);
#2937=ORIENTED_EDGE('',*,*,#2252,.T.);
#2938=ORIENTED_EDGE('',*,*,#2253,.T.);
#2939=ORIENTED_EDGE('',*,*,#2184,.T.);
#2940=ORIENTED_EDGE('',*,*,#2254,.T.);
#2941=ORIENTED_EDGE('',*,*,#2255,.T.);
#2942=ORIENTED_EDGE('',*,*,#2256,.T.);
#2943=ORIENTED_EDGE('',*,*,#2257,.T.);
#2944=ORIENTED_EDGE('',*,*,#2258,.T.);
#2945=ORIENTED_EDGE('',*,*,#2259,.T.);
#2946=ORIENTED_EDGE('',*,*,#2260,.T.);
#2947=ORIENTED_EDGE('',*,*,#2261,.T.);
#2948=ORIENTED_EDGE('',*,*,#2262,.T.);
#2949=ORIENTED_EDGE('',*,*,#2263,.T.);
#2950=ORIENTED_EDGE('',*,*,#2264,.T.);
#2951=ORIENTED_EDGE('',*,*,#2265,.T.);
#2952=ORIENTED_EDGE('',*,*,#2266,.F.);
#2953=ORIENTED_EDGE('',*,*,#2267,.T.);
#2954=ORIENTED_EDGE('',*,*,#2268,.T.);
#2955=ORIENTED_EDGE('',*,*,#1923,.F.);
#2956=ORIENTED_EDGE('',*,*,#2194,.F.);
#2957=ORIENTED_EDGE('',*,*,#2223,.T.);
#2958=ORIENTED_EDGE('',*,*,#2269,.T.);
#2959=ORIENTED_EDGE('',*,*,#2270,.F.);
#2960=ORIENTED_EDGE('',*,*,#2271,.T.);
#2961=ORIENTED_EDGE('',*,*,#2272,.F.);
#2962=ORIENTED_EDGE('',*,*,#2273,.F.);
#2963=ORIENTED_EDGE('',*,*,#2274,.T.);
#2964=ORIENTED_EDGE('',*,*,#2275,.F.);
#2965=ORIENTED_EDGE('',*,*,#2276,.T.);
#2966=ORIENTED_EDGE('',*,*,#2277,.T.);
#2967=ORIENTED_EDGE('',*,*,#2278,.F.);
#2968=ORIENTED_EDGE('',*,*,#2279,.F.);
#2969=ORIENTED_EDGE('',*,*,#2280,.F.);
#2970=ORIENTED_EDGE('',*,*,#2281,.T.);
#2971=ORIENTED_EDGE('',*,*,#2276,.F.);
#2972=ORIENTED_EDGE('',*,*,#2282,.T.);
#2973=ORIENTED_EDGE('',*,*,#2283,.T.);
#2974=ORIENTED_EDGE('',*,*,#2284,.T.);
#2975=ORIENTED_EDGE('',*,*,#2285,.T.);
#2976=ORIENTED_EDGE('',*,*,#2286,.T.);
#2977=ORIENTED_EDGE('',*,*,#2272,.T.);
#2978=ORIENTED_EDGE('',*,*,#2275,.T.);
#2979=ORIENTED_EDGE('',*,*,#2274,.F.);
#2980=ORIENTED_EDGE('',*,*,#2287,.T.);
#2981=ORIENTED_EDGE('',*,*,#2288,.F.);
#2982=ORIENTED_EDGE('',*,*,#2289,.T.);
#2983=ORIENTED_EDGE('',*,*,#2290,.T.);
#2984=ORIENTED_EDGE('',*,*,#2281,.F.);
#2985=ORIENTED_EDGE('',*,*,#1930,.F.);
#2986=ORIENTED_EDGE('',*,*,#2291,.F.);
#2987=ORIENTED_EDGE('',*,*,#2292,.F.);
#2988=ORIENTED_EDGE('',*,*,#2224,.T.);
#2989=ORIENTED_EDGE('',*,*,#2230,.T.);
#2990=ORIENTED_EDGE('',*,*,#2293,.T.);
#2991=ORIENTED_EDGE('',*,*,#2294,.T.);
#2992=ORIENTED_EDGE('',*,*,#2295,.T.);
#2993=ORIENTED_EDGE('',*,*,#2296,.T.);
#2994=ORIENTED_EDGE('',*,*,#2297,.T.);
#2995=ORIENTED_EDGE('',*,*,#2298,.T.);
#2996=ORIENTED_EDGE('',*,*,#2299,.T.);
#2997=ORIENTED_EDGE('',*,*,#2300,.T.);
#2998=ORIENTED_EDGE('',*,*,#2301,.T.);
#2999=ORIENTED_EDGE('',*,*,#2302,.T.);
#3000=ORIENTED_EDGE('',*,*,#2303,.T.);
#3001=ORIENTED_EDGE('',*,*,#2304,.T.);
#3002=ORIENTED_EDGE('',*,*,#2305,.T.);
#3003=ORIENTED_EDGE('',*,*,#2306,.T.);
#3004=ORIENTED_EDGE('',*,*,#2307,.T.);
#3005=ORIENTED_EDGE('',*,*,#2308,.T.);
#3006=ORIENTED_EDGE('',*,*,#2309,.T.);
#3007=ORIENTED_EDGE('',*,*,#2310,.T.);
#3008=ORIENTED_EDGE('',*,*,#2311,.T.);
#3009=ORIENTED_EDGE('',*,*,#2312,.T.);
#3010=ORIENTED_EDGE('',*,*,#2313,.T.);
#3011=ORIENTED_EDGE('',*,*,#2314,.T.);
#3012=ORIENTED_EDGE('',*,*,#2315,.T.);
#3013=ORIENTED_EDGE('',*,*,#2227,.T.);
#3014=ORIENTED_EDGE('',*,*,#2271,.F.);
#3015=ORIENTED_EDGE('',*,*,#2278,.T.);
#3016=ORIENTED_EDGE('',*,*,#2316,.T.);
#3017=ORIENTED_EDGE('',*,*,#2317,.F.);
#3018=ORIENTED_EDGE('',*,*,#2172,.F.);
#3019=ORIENTED_EDGE('',*,*,#2318,.T.);
#3020=ORIENTED_EDGE('',*,*,#2265,.F.);
#3021=ORIENTED_EDGE('',*,*,#2319,.F.);
#3022=ORIENTED_EDGE('',*,*,#2173,.F.);
#3023=ORIENTED_EDGE('',*,*,#2319,.T.);
#3024=ORIENTED_EDGE('',*,*,#2264,.F.);
#3025=ORIENTED_EDGE('',*,*,#2320,.F.);
#3026=ORIENTED_EDGE('',*,*,#2174,.F.);
#3027=ORIENTED_EDGE('',*,*,#2320,.T.);
#3028=ORIENTED_EDGE('',*,*,#2263,.F.);
#3029=ORIENTED_EDGE('',*,*,#2321,.F.);
#3030=ORIENTED_EDGE('',*,*,#2175,.F.);
#3031=ORIENTED_EDGE('',*,*,#2321,.T.);
#3032=ORIENTED_EDGE('',*,*,#2262,.F.);
#3033=ORIENTED_EDGE('',*,*,#2322,.F.);
#3034=ORIENTED_EDGE('',*,*,#2176,.F.);
#3035=ORIENTED_EDGE('',*,*,#2322,.T.);
#3036=ORIENTED_EDGE('',*,*,#2261,.F.);
#3037=ORIENTED_EDGE('',*,*,#2323,.F.);
#3038=ORIENTED_EDGE('',*,*,#2171,.F.);
#3039=ORIENTED_EDGE('',*,*,#2323,.T.);
#3040=ORIENTED_EDGE('',*,*,#2260,.F.);
#3041=ORIENTED_EDGE('',*,*,#2318,.F.);
#3042=ORIENTED_EDGE('',*,*,#2166,.F.);
#3043=ORIENTED_EDGE('',*,*,#2324,.T.);
#3044=ORIENTED_EDGE('',*,*,#2259,.F.);
#3045=ORIENTED_EDGE('',*,*,#2325,.F.);
#3046=ORIENTED_EDGE('',*,*,#2167,.F.);
#3047=ORIENTED_EDGE('',*,*,#2325,.T.);
#3048=ORIENTED_EDGE('',*,*,#2258,.F.);
#3049=ORIENTED_EDGE('',*,*,#2326,.F.);
#3050=ORIENTED_EDGE('',*,*,#2168,.F.);
#3051=ORIENTED_EDGE('',*,*,#2326,.T.);
#3052=ORIENTED_EDGE('',*,*,#2257,.F.);
#3053=ORIENTED_EDGE('',*,*,#2327,.F.);
#3054=ORIENTED_EDGE('',*,*,#2169,.F.);
#3055=ORIENTED_EDGE('',*,*,#2327,.T.);
#3056=ORIENTED_EDGE('',*,*,#2256,.F.);
#3057=ORIENTED_EDGE('',*,*,#2328,.F.);
#3058=ORIENTED_EDGE('',*,*,#2170,.F.);
#3059=ORIENTED_EDGE('',*,*,#2328,.T.);
#3060=ORIENTED_EDGE('',*,*,#2255,.F.);
#3061=ORIENTED_EDGE('',*,*,#2329,.F.);
#3062=ORIENTED_EDGE('',*,*,#2165,.F.);
#3063=ORIENTED_EDGE('',*,*,#2329,.T.);
#3064=ORIENTED_EDGE('',*,*,#2254,.F.);
#3065=ORIENTED_EDGE('',*,*,#2324,.F.);
#3066=ORIENTED_EDGE('',*,*,#2153,.F.);
#3067=ORIENTED_EDGE('',*,*,#2330,.T.);
#3068=ORIENTED_EDGE('',*,*,#2241,.F.);
#3069=ORIENTED_EDGE('',*,*,#2331,.F.);
#3070=ORIENTED_EDGE('',*,*,#2154,.F.);
#3071=ORIENTED_EDGE('',*,*,#2331,.T.);
#3072=ORIENTED_EDGE('',*,*,#2240,.F.);
#3073=ORIENTED_EDGE('',*,*,#2332,.F.);
#3074=ORIENTED_EDGE('',*,*,#2155,.F.);
#3075=ORIENTED_EDGE('',*,*,#2332,.T.);
#3076=ORIENTED_EDGE('',*,*,#2239,.F.);
#3077=ORIENTED_EDGE('',*,*,#2333,.F.);
#3078=ORIENTED_EDGE('',*,*,#2156,.T.);
#3079=ORIENTED_EDGE('',*,*,#2333,.T.);
#3080=ORIENTED_EDGE('',*,*,#2238,.F.);
#3081=ORIENTED_EDGE('',*,*,#2334,.F.);
#3082=ORIENTED_EDGE('',*,*,#2157,.F.);
#3083=ORIENTED_EDGE('',*,*,#2334,.T.);
#3084=ORIENTED_EDGE('',*,*,#2237,.F.);
#3085=ORIENTED_EDGE('',*,*,#2335,.F.);
#3086=ORIENTED_EDGE('',*,*,#2158,.T.);
#3087=ORIENTED_EDGE('',*,*,#2335,.T.);
#3088=ORIENTED_EDGE('',*,*,#2236,.F.);
#3089=ORIENTED_EDGE('',*,*,#2336,.F.);
#3090=ORIENTED_EDGE('',*,*,#2159,.F.);
#3091=ORIENTED_EDGE('',*,*,#2336,.T.);
#3092=ORIENTED_EDGE('',*,*,#2235,.F.);
#3093=ORIENTED_EDGE('',*,*,#2337,.F.);
#3094=ORIENTED_EDGE('',*,*,#2160,.T.);
#3095=ORIENTED_EDGE('',*,*,#2337,.T.);
#3096=ORIENTED_EDGE('',*,*,#2234,.F.);
#3097=ORIENTED_EDGE('',*,*,#2338,.F.);
#3098=ORIENTED_EDGE('',*,*,#2161,.T.);
#3099=ORIENTED_EDGE('',*,*,#2338,.T.);
#3100=ORIENTED_EDGE('',*,*,#2233,.F.);
#3101=ORIENTED_EDGE('',*,*,#2339,.F.);
#3102=ORIENTED_EDGE('',*,*,#2162,.F.);
#3103=ORIENTED_EDGE('',*,*,#2339,.T.);
#3104=ORIENTED_EDGE('',*,*,#2232,.F.);
#3105=ORIENTED_EDGE('',*,*,#2340,.F.);
#3106=ORIENTED_EDGE('',*,*,#2163,.T.);
#3107=ORIENTED_EDGE('',*,*,#2340,.T.);
#3108=ORIENTED_EDGE('',*,*,#2231,.F.);
#3109=ORIENTED_EDGE('',*,*,#2186,.F.);
#3110=ORIENTED_EDGE('',*,*,#2179,.F.);
#3111=ORIENTED_EDGE('',*,*,#1944,.F.);
#3112=ORIENTED_EDGE('',*,*,#2341,.F.);
#3113=ORIENTED_EDGE('',*,*,#2342,.T.);
#3114=ORIENTED_EDGE('',*,*,#2343,.T.);
#3115=ORIENTED_EDGE('',*,*,#2341,.T.);
#3116=ORIENTED_EDGE('',*,*,#1943,.F.);
#3117=ORIENTED_EDGE('',*,*,#2344,.F.);
#3118=ORIENTED_EDGE('',*,*,#2345,.T.);
#3119=ORIENTED_EDGE('',*,*,#2344,.T.);
#3120=ORIENTED_EDGE('',*,*,#1942,.F.);
#3121=ORIENTED_EDGE('',*,*,#2346,.F.);
#3122=ORIENTED_EDGE('',*,*,#2347,.F.);
#3123=ORIENTED_EDGE('',*,*,#2346,.T.);
#3124=ORIENTED_EDGE('',*,*,#1941,.F.);
#3125=ORIENTED_EDGE('',*,*,#2348,.F.);
#3126=ORIENTED_EDGE('',*,*,#2349,.T.);
#3127=ORIENTED_EDGE('',*,*,#2348,.T.);
#3128=ORIENTED_EDGE('',*,*,#1940,.F.);
#3129=ORIENTED_EDGE('',*,*,#2350,.F.);
#3130=ORIENTED_EDGE('',*,*,#2351,.T.);
#3131=ORIENTED_EDGE('',*,*,#2350,.T.);
#3132=ORIENTED_EDGE('',*,*,#1939,.F.);
#3133=ORIENTED_EDGE('',*,*,#2352,.F.);
#3134=ORIENTED_EDGE('',*,*,#2353,.F.);
#3135=ORIENTED_EDGE('',*,*,#2352,.T.);
#3136=ORIENTED_EDGE('',*,*,#1938,.F.);
#3137=ORIENTED_EDGE('',*,*,#2354,.F.);
#3138=ORIENTED_EDGE('',*,*,#2355,.T.);
#3139=ORIENTED_EDGE('',*,*,#2354,.T.);
#3140=ORIENTED_EDGE('',*,*,#1937,.F.);
#3141=ORIENTED_EDGE('',*,*,#2356,.F.);
#3142=ORIENTED_EDGE('',*,*,#2357,.F.);
#3143=ORIENTED_EDGE('',*,*,#2356,.T.);
#3144=ORIENTED_EDGE('',*,*,#1936,.F.);
#3145=ORIENTED_EDGE('',*,*,#2358,.F.);
#3146=ORIENTED_EDGE('',*,*,#2359,.F.);
#3147=ORIENTED_EDGE('',*,*,#2358,.T.);
#3148=ORIENTED_EDGE('',*,*,#1935,.F.);
#3149=ORIENTED_EDGE('',*,*,#2360,.F.);
#3150=ORIENTED_EDGE('',*,*,#2361,.T.);
#3151=ORIENTED_EDGE('',*,*,#2360,.T.);
#3152=ORIENTED_EDGE('',*,*,#1934,.F.);
#3153=ORIENTED_EDGE('',*,*,#2362,.F.);
#3154=ORIENTED_EDGE('',*,*,#2363,.F.);
#3155=ORIENTED_EDGE('',*,*,#2362,.T.);
#3156=ORIENTED_EDGE('',*,*,#1933,.F.);
#3157=ORIENTED_EDGE('',*,*,#2364,.F.);
#3158=ORIENTED_EDGE('',*,*,#2316,.F.);
#3159=ORIENTED_EDGE('',*,*,#2365,.T.);
#3160=ORIENTED_EDGE('',*,*,#2364,.T.);
#3161=ORIENTED_EDGE('',*,*,#1932,.F.);
#3162=ORIENTED_EDGE('',*,*,#2287,.F.);
#3163=ORIENTED_EDGE('',*,*,#2273,.T.);
#3164=ORIENTED_EDGE('',*,*,#2286,.F.);
#3165=ORIENTED_EDGE('',*,*,#2366,.F.);
#3166=ORIENTED_EDGE('',*,*,#2288,.T.);
#3167=ORIENTED_EDGE('',*,*,#2366,.T.);
#3168=ORIENTED_EDGE('',*,*,#2285,.F.);
#3169=ORIENTED_EDGE('',*,*,#2367,.F.);
#3170=ORIENTED_EDGE('',*,*,#2289,.F.);
#3171=ORIENTED_EDGE('',*,*,#2367,.T.);
#3172=ORIENTED_EDGE('',*,*,#2284,.F.);
#3173=ORIENTED_EDGE('',*,*,#2368,.F.);
#3174=ORIENTED_EDGE('',*,*,#2279,.T.);
#3175=ORIENTED_EDGE('',*,*,#2290,.F.);
#3176=ORIENTED_EDGE('',*,*,#2368,.T.);
#3177=ORIENTED_EDGE('',*,*,#2283,.F.);
#3178=ORIENTED_EDGE('',*,*,#2369,.T.);
#3179=ORIENTED_EDGE('',*,*,#2291,.T.);
#3180=ORIENTED_EDGE('',*,*,#1929,.F.);
#3181=ORIENTED_EDGE('',*,*,#2370,.F.);
#3182=ORIENTED_EDGE('',*,*,#2371,.T.);
#3183=ORIENTED_EDGE('',*,*,#2370,.T.);
#3184=ORIENTED_EDGE('',*,*,#1928,.F.);
#3185=ORIENTED_EDGE('',*,*,#2372,.F.);
#3186=ORIENTED_EDGE('',*,*,#2373,.T.);
#3187=ORIENTED_EDGE('',*,*,#2372,.T.);
#3188=ORIENTED_EDGE('',*,*,#1927,.F.);
#3189=ORIENTED_EDGE('',*,*,#2374,.F.);
#3190=ORIENTED_EDGE('',*,*,#2375,.F.);
#3191=ORIENTED_EDGE('',*,*,#2374,.T.);
#3192=ORIENTED_EDGE('',*,*,#1926,.F.);
#3193=ORIENTED_EDGE('',*,*,#2376,.F.);
#3194=ORIENTED_EDGE('',*,*,#2377,.T.);
#3195=ORIENTED_EDGE('',*,*,#2376,.T.);
#3196=ORIENTED_EDGE('',*,*,#1925,.F.);
#3197=ORIENTED_EDGE('',*,*,#2378,.F.);
#3198=ORIENTED_EDGE('',*,*,#2379,.T.);
#3199=ORIENTED_EDGE('',*,*,#2378,.T.);
#3200=ORIENTED_EDGE('',*,*,#1924,.F.);
#3201=ORIENTED_EDGE('',*,*,#2268,.F.);
#3202=ORIENTED_EDGE('',*,*,#2226,.F.);
#3203=ORIENTED_EDGE('',*,*,#2270,.T.);
#3204=ORIENTED_EDGE('',*,*,#2266,.T.);
#3205=ORIENTED_EDGE('',*,*,#2225,.F.);
#3206=ORIENTED_EDGE('',*,*,#2196,.T.);
#3207=ORIENTED_EDGE('',*,*,#2228,.T.);
#3208=ORIENTED_EDGE('',*,*,#2315,.F.);
#3209=ORIENTED_EDGE('',*,*,#2380,.F.);
#3210=ORIENTED_EDGE('',*,*,#2197,.F.);
#3211=ORIENTED_EDGE('',*,*,#2380,.T.);
#3212=ORIENTED_EDGE('',*,*,#2314,.F.);
#3213=ORIENTED_EDGE('',*,*,#2381,.F.);
#3214=ORIENTED_EDGE('',*,*,#2198,.F.);
#3215=ORIENTED_EDGE('',*,*,#2381,.T.);
#3216=ORIENTED_EDGE('',*,*,#2313,.F.);
#3217=ORIENTED_EDGE('',*,*,#2382,.F.);
#3218=ORIENTED_EDGE('',*,*,#2199,.T.);
#3219=ORIENTED_EDGE('',*,*,#2382,.T.);
#3220=ORIENTED_EDGE('',*,*,#2312,.F.);
#3221=ORIENTED_EDGE('',*,*,#2383,.F.);
#3222=ORIENTED_EDGE('',*,*,#2200,.T.);
#3223=ORIENTED_EDGE('',*,*,#2383,.T.);
#3224=ORIENTED_EDGE('',*,*,#2311,.F.);
#3225=ORIENTED_EDGE('',*,*,#2384,.F.);
#3226=ORIENTED_EDGE('',*,*,#2201,.F.);
#3227=ORIENTED_EDGE('',*,*,#2384,.T.);
#3228=ORIENTED_EDGE('',*,*,#2310,.F.);
#3229=ORIENTED_EDGE('',*,*,#2385,.F.);
#3230=ORIENTED_EDGE('',*,*,#2202,.T.);
#3231=ORIENTED_EDGE('',*,*,#2385,.T.);
#3232=ORIENTED_EDGE('',*,*,#2309,.F.);
#3233=ORIENTED_EDGE('',*,*,#2386,.F.);
#3234=ORIENTED_EDGE('',*,*,#2203,.F.);
#3235=ORIENTED_EDGE('',*,*,#2386,.T.);
#3236=ORIENTED_EDGE('',*,*,#2308,.F.);
#3237=ORIENTED_EDGE('',*,*,#2387,.F.);
#3238=ORIENTED_EDGE('',*,*,#2204,.F.);
#3239=ORIENTED_EDGE('',*,*,#2387,.T.);
#3240=ORIENTED_EDGE('',*,*,#2307,.F.);
#3241=ORIENTED_EDGE('',*,*,#2388,.F.);
#3242=ORIENTED_EDGE('',*,*,#2205,.F.);
#3243=ORIENTED_EDGE('',*,*,#2388,.T.);
#3244=ORIENTED_EDGE('',*,*,#2306,.F.);
#3245=ORIENTED_EDGE('',*,*,#2389,.F.);
#3246=ORIENTED_EDGE('',*,*,#2206,.F.);
#3247=ORIENTED_EDGE('',*,*,#2389,.T.);
#3248=ORIENTED_EDGE('',*,*,#2305,.F.);
#3249=ORIENTED_EDGE('',*,*,#2390,.F.);
#3250=ORIENTED_EDGE('',*,*,#2207,.F.);
#3251=ORIENTED_EDGE('',*,*,#2390,.T.);
#3252=ORIENTED_EDGE('',*,*,#2304,.F.);
#3253=ORIENTED_EDGE('',*,*,#2391,.F.);
#3254=ORIENTED_EDGE('',*,*,#2208,.F.);
#3255=ORIENTED_EDGE('',*,*,#2391,.T.);
#3256=ORIENTED_EDGE('',*,*,#2303,.F.);
#3257=ORIENTED_EDGE('',*,*,#2392,.F.);
#3258=ORIENTED_EDGE('',*,*,#2209,.F.);
#3259=ORIENTED_EDGE('',*,*,#2392,.T.);
#3260=ORIENTED_EDGE('',*,*,#2302,.F.);
#3261=ORIENTED_EDGE('',*,*,#2393,.F.);
#3262=ORIENTED_EDGE('',*,*,#2210,.F.);
#3263=ORIENTED_EDGE('',*,*,#2393,.T.);
#3264=ORIENTED_EDGE('',*,*,#2301,.F.);
#3265=ORIENTED_EDGE('',*,*,#2394,.F.);
#3266=ORIENTED_EDGE('',*,*,#2211,.F.);
#3267=ORIENTED_EDGE('',*,*,#2394,.T.);
#3268=ORIENTED_EDGE('',*,*,#2300,.F.);
#3269=ORIENTED_EDGE('',*,*,#2395,.F.);
#3270=ORIENTED_EDGE('',*,*,#2212,.T.);
#3271=ORIENTED_EDGE('',*,*,#2395,.T.);
#3272=ORIENTED_EDGE('',*,*,#2299,.F.);
#3273=ORIENTED_EDGE('',*,*,#2396,.F.);
#3274=ORIENTED_EDGE('',*,*,#2213,.F.);
#3275=ORIENTED_EDGE('',*,*,#2396,.T.);
#3276=ORIENTED_EDGE('',*,*,#2298,.F.);
#3277=ORIENTED_EDGE('',*,*,#2397,.F.);
#3278=ORIENTED_EDGE('',*,*,#2214,.T.);
#3279=ORIENTED_EDGE('',*,*,#2397,.T.);
#3280=ORIENTED_EDGE('',*,*,#2297,.F.);
#3281=ORIENTED_EDGE('',*,*,#2398,.F.);
#3282=ORIENTED_EDGE('',*,*,#2215,.T.);
#3283=ORIENTED_EDGE('',*,*,#2398,.T.);
#3284=ORIENTED_EDGE('',*,*,#2296,.F.);
#3285=ORIENTED_EDGE('',*,*,#2399,.F.);
#3286=ORIENTED_EDGE('',*,*,#2216,.F.);
#3287=ORIENTED_EDGE('',*,*,#2399,.T.);
#3288=ORIENTED_EDGE('',*,*,#2295,.F.);
#3289=ORIENTED_EDGE('',*,*,#2400,.F.);
#3290=ORIENTED_EDGE('',*,*,#2217,.F.);
#3291=ORIENTED_EDGE('',*,*,#2400,.T.);
#3292=ORIENTED_EDGE('',*,*,#2294,.F.);
#3293=ORIENTED_EDGE('',*,*,#2401,.F.);
#3294=ORIENTED_EDGE('',*,*,#2218,.F.);
#3295=ORIENTED_EDGE('',*,*,#2401,.T.);
#3296=ORIENTED_EDGE('',*,*,#2293,.F.);
#3297=ORIENTED_EDGE('',*,*,#2229,.F.);
#3298=ORIENTED_EDGE('',*,*,#2222,.F.);
#3299=ORIENTED_EDGE('',*,*,#1921,.F.);
#3300=ORIENTED_EDGE('',*,*,#2402,.F.);
#3301=ORIENTED_EDGE('',*,*,#2403,.F.);
#3302=ORIENTED_EDGE('',*,*,#2404,.T.);
#3303=ORIENTED_EDGE('',*,*,#2402,.T.);
#3304=ORIENTED_EDGE('',*,*,#1920,.F.);
#3305=ORIENTED_EDGE('',*,*,#2405,.F.);
#3306=ORIENTED_EDGE('',*,*,#2406,.F.);
#3307=ORIENTED_EDGE('',*,*,#2405,.T.);
#3308=ORIENTED_EDGE('',*,*,#1919,.F.);
#3309=ORIENTED_EDGE('',*,*,#2407,.F.);
#3310=ORIENTED_EDGE('',*,*,#2408,.T.);
#3311=ORIENTED_EDGE('',*,*,#2407,.T.);
#3312=ORIENTED_EDGE('',*,*,#1918,.F.);
#3313=ORIENTED_EDGE('',*,*,#2136,.F.);
#3314=ORIENTED_EDGE('',*,*,#2107,.F.);
#3315=ORIENTED_EDGE('',*,*,#2138,.T.);
#3316=ORIENTED_EDGE('',*,*,#2134,.T.);
#3317=ORIENTED_EDGE('',*,*,#2106,.F.);
#3318=ORIENTED_EDGE('',*,*,#2099,.T.);
#3319=ORIENTED_EDGE('',*,*,#2109,.T.);
#3320=ORIENTED_EDGE('',*,*,#2188,.F.);
#3321=ORIENTED_EDGE('',*,*,#2110,.F.);
#3322=ORIENTED_EDGE('',*,*,#2103,.F.);
#3323=ORIENTED_EDGE('',*,*,#1915,.F.);
#3324=ORIENTED_EDGE('',*,*,#2409,.F.);
#3325=ORIENTED_EDGE('',*,*,#2410,.T.);
#3326=ORIENTED_EDGE('',*,*,#2411,.T.);
#3327=ORIENTED_EDGE('',*,*,#2409,.T.);
#3328=ORIENTED_EDGE('',*,*,#1914,.F.);
#3329=ORIENTED_EDGE('',*,*,#1951,.F.);
#3330=ORIENTED_EDGE('',*,*,#1908,.F.);
#3331=ORIENTED_EDGE('',*,*,#1953,.F.);
#3332=ORIENTED_EDGE('',*,*,#1949,.T.);
#3333=ORIENTED_EDGE('',*,*,#1906,.F.);
#3334=ORIENTED_EDGE('',*,*,#1889,.T.);
#3335=ORIENTED_EDGE('',*,*,#1910,.T.);
#3336=ORIENTED_EDGE('',*,*,#1978,.F.);
#3337=ORIENTED_EDGE('',*,*,#2412,.F.);
#3338=ORIENTED_EDGE('',*,*,#1890,.F.);
#3339=ORIENTED_EDGE('',*,*,#2412,.T.);
#3340=ORIENTED_EDGE('',*,*,#1977,.F.);
#3341=ORIENTED_EDGE('',*,*,#2413,.F.);
#3342=ORIENTED_EDGE('',*,*,#1891,.F.);
#3343=ORIENTED_EDGE('',*,*,#2413,.T.);
#3344=ORIENTED_EDGE('',*,*,#1976,.F.);
#3345=ORIENTED_EDGE('',*,*,#2414,.F.);
#3346=ORIENTED_EDGE('',*,*,#1892,.T.);
#3347=ORIENTED_EDGE('',*,*,#2414,.T.);
#3348=ORIENTED_EDGE('',*,*,#1975,.F.);
#3349=ORIENTED_EDGE('',*,*,#2415,.F.);
#3350=ORIENTED_EDGE('',*,*,#1893,.F.);
#3351=ORIENTED_EDGE('',*,*,#2415,.T.);
#3352=ORIENTED_EDGE('',*,*,#1974,.F.);
#3353=ORIENTED_EDGE('',*,*,#2416,.F.);
#3354=ORIENTED_EDGE('',*,*,#1894,.F.);
#3355=ORIENTED_EDGE('',*,*,#2416,.T.);
#3356=ORIENTED_EDGE('',*,*,#1973,.F.);
#3357=ORIENTED_EDGE('',*,*,#2417,.F.);
#3358=ORIENTED_EDGE('',*,*,#1895,.F.);
#3359=ORIENTED_EDGE('',*,*,#2417,.T.);
#3360=ORIENTED_EDGE('',*,*,#1972,.F.);
#3361=ORIENTED_EDGE('',*,*,#2418,.F.);
#3362=ORIENTED_EDGE('',*,*,#1896,.F.);
#3363=ORIENTED_EDGE('',*,*,#2418,.T.);
#3364=ORIENTED_EDGE('',*,*,#1971,.F.);
#3365=ORIENTED_EDGE('',*,*,#2419,.F.);
#3366=ORIENTED_EDGE('',*,*,#1897,.T.);
#3367=ORIENTED_EDGE('',*,*,#2419,.T.);
#3368=ORIENTED_EDGE('',*,*,#1970,.F.);
#3369=ORIENTED_EDGE('',*,*,#1980,.F.);
#3370=ORIENTED_EDGE('',*,*,#1994,.T.);
#3371=ORIENTED_EDGE('',*,*,#2016,.F.);
#3372=ORIENTED_EDGE('',*,*,#2420,.F.);
#3373=ORIENTED_EDGE('',*,*,#2024,.F.);
#3374=ORIENTED_EDGE('',*,*,#2018,.F.);
#3375=ORIENTED_EDGE('',*,*,#2025,.T.);
#3376=ORIENTED_EDGE('',*,*,#2420,.T.);
#3377=ORIENTED_EDGE('',*,*,#2015,.F.);
#3378=ORIENTED_EDGE('',*,*,#2019,.F.);
#3379=ORIENTED_EDGE('',*,*,#2087,.F.);
#3380=ORIENTED_EDGE('',*,*,#2096,.T.);
#3381=ORIENTED_EDGE('',*,*,#2095,.T.);
#3382=ORIENTED_EDGE('',*,*,#2086,.F.);
#3383=ORIENTED_EDGE('',*,*,#2058,.F.);
#3384=ORIENTED_EDGE('',*,*,#2089,.T.);
#3385=ORIENTED_EDGE('',*,*,#2133,.F.);
#3386=ORIENTED_EDGE('',*,*,#2421,.F.);
#3387=ORIENTED_EDGE('',*,*,#2059,.T.);
#3388=ORIENTED_EDGE('',*,*,#2421,.T.);
#3389=ORIENTED_EDGE('',*,*,#2132,.F.);
#3390=ORIENTED_EDGE('',*,*,#2422,.F.);
#3391=ORIENTED_EDGE('',*,*,#2060,.F.);
#3392=ORIENTED_EDGE('',*,*,#2422,.T.);
#3393=ORIENTED_EDGE('',*,*,#2131,.F.);
#3394=ORIENTED_EDGE('',*,*,#2423,.F.);
#3395=ORIENTED_EDGE('',*,*,#2061,.F.);
#3396=ORIENTED_EDGE('',*,*,#2423,.T.);
#3397=ORIENTED_EDGE('',*,*,#2130,.F.);
#3398=ORIENTED_EDGE('',*,*,#2424,.F.);
#3399=ORIENTED_EDGE('',*,*,#2062,.T.);
#3400=ORIENTED_EDGE('',*,*,#2424,.T.);
#3401=ORIENTED_EDGE('',*,*,#2129,.F.);
#3402=ORIENTED_EDGE('',*,*,#2425,.F.);
#3403=ORIENTED_EDGE('',*,*,#2063,.T.);
#3404=ORIENTED_EDGE('',*,*,#2425,.T.);
#3405=ORIENTED_EDGE('',*,*,#2128,.F.);
#3406=ORIENTED_EDGE('',*,*,#2426,.F.);
#3407=ORIENTED_EDGE('',*,*,#2064,.T.);
#3408=ORIENTED_EDGE('',*,*,#2426,.T.);
#3409=ORIENTED_EDGE('',*,*,#2127,.F.);
#3410=ORIENTED_EDGE('',*,*,#2427,.F.);
#3411=ORIENTED_EDGE('',*,*,#2065,.T.);
#3412=ORIENTED_EDGE('',*,*,#2427,.T.);
#3413=ORIENTED_EDGE('',*,*,#2126,.F.);
#3414=ORIENTED_EDGE('',*,*,#2428,.F.);
#3415=ORIENTED_EDGE('',*,*,#2066,.F.);
#3416=ORIENTED_EDGE('',*,*,#2428,.T.);
#3417=ORIENTED_EDGE('',*,*,#2125,.F.);
#3418=ORIENTED_EDGE('',*,*,#2429,.F.);
#3419=ORIENTED_EDGE('',*,*,#2067,.T.);
#3420=ORIENTED_EDGE('',*,*,#2429,.T.);
#3421=ORIENTED_EDGE('',*,*,#2124,.F.);
#3422=ORIENTED_EDGE('',*,*,#2430,.F.);
#3423=ORIENTED_EDGE('',*,*,#2068,.F.);
#3424=ORIENTED_EDGE('',*,*,#2430,.T.);
#3425=ORIENTED_EDGE('',*,*,#2123,.F.);
#3426=ORIENTED_EDGE('',*,*,#2431,.F.);
#3427=ORIENTED_EDGE('',*,*,#2069,.T.);
#3428=ORIENTED_EDGE('',*,*,#2431,.T.);
#3429=ORIENTED_EDGE('',*,*,#2122,.F.);
#3430=ORIENTED_EDGE('',*,*,#2432,.F.);
#3431=ORIENTED_EDGE('',*,*,#2070,.F.);
#3432=ORIENTED_EDGE('',*,*,#2432,.T.);
#3433=ORIENTED_EDGE('',*,*,#2121,.F.);
#3434=ORIENTED_EDGE('',*,*,#2433,.F.);
#3435=ORIENTED_EDGE('',*,*,#2071,.T.);
#3436=ORIENTED_EDGE('',*,*,#2433,.T.);
#3437=ORIENTED_EDGE('',*,*,#2120,.F.);
#3438=ORIENTED_EDGE('',*,*,#2434,.F.);
#3439=ORIENTED_EDGE('',*,*,#2072,.F.);
#3440=ORIENTED_EDGE('',*,*,#2434,.T.);
#3441=ORIENTED_EDGE('',*,*,#2119,.F.);
#3442=ORIENTED_EDGE('',*,*,#2435,.F.);
#3443=ORIENTED_EDGE('',*,*,#2073,.T.);
#3444=ORIENTED_EDGE('',*,*,#2435,.T.);
#3445=ORIENTED_EDGE('',*,*,#2118,.F.);
#3446=ORIENTED_EDGE('',*,*,#2436,.F.);
#3447=ORIENTED_EDGE('',*,*,#2074,.T.);
#3448=ORIENTED_EDGE('',*,*,#2436,.T.);
#3449=ORIENTED_EDGE('',*,*,#2117,.F.);
#3450=ORIENTED_EDGE('',*,*,#2437,.F.);
#3451=ORIENTED_EDGE('',*,*,#2075,.T.);
#3452=ORIENTED_EDGE('',*,*,#2437,.T.);
#3453=ORIENTED_EDGE('',*,*,#2116,.F.);
#3454=ORIENTED_EDGE('',*,*,#2438,.F.);
#3455=ORIENTED_EDGE('',*,*,#2076,.T.);
#3456=ORIENTED_EDGE('',*,*,#2438,.T.);
#3457=ORIENTED_EDGE('',*,*,#2115,.F.);
#3458=ORIENTED_EDGE('',*,*,#2439,.F.);
#3459=ORIENTED_EDGE('',*,*,#2077,.F.);
#3460=ORIENTED_EDGE('',*,*,#2439,.T.);
#3461=ORIENTED_EDGE('',*,*,#2114,.F.);
#3462=ORIENTED_EDGE('',*,*,#2440,.F.);
#3463=ORIENTED_EDGE('',*,*,#2078,.F.);
#3464=ORIENTED_EDGE('',*,*,#2440,.T.);
#3465=ORIENTED_EDGE('',*,*,#2113,.F.);
#3466=ORIENTED_EDGE('',*,*,#2441,.F.);
#3467=ORIENTED_EDGE('',*,*,#2079,.T.);
#3468=ORIENTED_EDGE('',*,*,#2441,.T.);
#3469=ORIENTED_EDGE('',*,*,#2112,.F.);
#3470=ORIENTED_EDGE('',*,*,#2090,.F.);
#3471=ORIENTED_EDGE('',*,*,#2083,.F.);
#3472=ORIENTED_EDGE('',*,*,#2050,.F.);
#3473=ORIENTED_EDGE('',*,*,#2442,.F.);
#3474=ORIENTED_EDGE('',*,*,#2002,.T.);
#3475=ORIENTED_EDGE('',*,*,#2003,.T.);
#3476=ORIENTED_EDGE('',*,*,#2442,.T.);
#3477=ORIENTED_EDGE('',*,*,#2049,.F.);
#3478=ORIENTED_EDGE('',*,*,#2054,.F.);
#3479=ORIENTED_EDGE('',*,*,#2039,.F.);
#3480=ORIENTED_EDGE('',*,*,#2055,.T.);
#3481=ORIENTED_EDGE('',*,*,#2053,.T.);
#3482=ORIENTED_EDGE('',*,*,#2037,.F.);
#3483=ORIENTED_EDGE('',*,*,#2028,.F.);
#3484=ORIENTED_EDGE('',*,*,#2041,.T.);
#3485=ORIENTED_EDGE('',*,*,#2094,.F.);
#3486=ORIENTED_EDGE('',*,*,#2443,.F.);
#3487=ORIENTED_EDGE('',*,*,#2029,.T.);
#3488=ORIENTED_EDGE('',*,*,#2443,.T.);
#3489=ORIENTED_EDGE('',*,*,#2093,.F.);
#3490=ORIENTED_EDGE('',*,*,#2444,.F.);
#3491=ORIENTED_EDGE('',*,*,#2030,.F.);
#3492=ORIENTED_EDGE('',*,*,#2444,.T.);
#3493=ORIENTED_EDGE('',*,*,#2092,.F.);
#3494=ORIENTED_EDGE('',*,*,#2042,.F.);
#3495=ORIENTED_EDGE('',*,*,#2034,.F.);
#3496=ORIENTED_EDGE('',*,*,#2047,.F.);
#3497=ORIENTED_EDGE('',*,*,#2445,.F.);
#3498=ORIENTED_EDGE('',*,*,#2006,.F.);
#3499=ORIENTED_EDGE('',*,*,#2007,.F.);
#3500=ORIENTED_EDGE('',*,*,#2445,.T.);
#3501=ORIENTED_EDGE('',*,*,#2046,.F.);
#3502=ORIENTED_EDGE('',*,*,#2446,.F.);
#3503=ORIENTED_EDGE('',*,*,#2008,.F.);
#3504=ORIENTED_EDGE('',*,*,#2446,.T.);
#3505=ORIENTED_EDGE('',*,*,#2045,.F.);
#3506=ORIENTED_EDGE('',*,*,#2447,.F.);
#3507=ORIENTED_EDGE('',*,*,#2009,.T.);
#3508=ORIENTED_EDGE('',*,*,#2447,.T.);
#3509=ORIENTED_EDGE('',*,*,#2044,.F.);
#3510=ORIENTED_EDGE('',*,*,#2022,.F.);
#3511=ORIENTED_EDGE('',*,*,#1903,.F.);
#3512=ORIENTED_EDGE('',*,*,#1948,.F.);
#3513=ORIENTED_EDGE('',*,*,#2448,.F.);
#3514=ORIENTED_EDGE('',*,*,#2449,.F.);
#3515=ORIENTED_EDGE('',*,*,#2450,.T.);
#3516=ORIENTED_EDGE('',*,*,#2448,.T.);
#3517=ORIENTED_EDGE('',*,*,#1947,.F.);
#3518=ORIENTED_EDGE('',*,*,#2191,.F.);
#3519=ORIENTED_EDGE('',*,*,#2183,.F.);
#3520=ORIENTED_EDGE('',*,*,#2193,.F.);
#3521=ORIENTED_EDGE('',*,*,#2189,.T.);
#3522=ORIENTED_EDGE('',*,*,#2182,.F.);
#3523=ORIENTED_EDGE('',*,*,#2141,.T.);
#3524=ORIENTED_EDGE('',*,*,#2185,.T.);
#3525=ORIENTED_EDGE('',*,*,#2253,.F.);
#3526=ORIENTED_EDGE('',*,*,#2451,.F.);
#3527=ORIENTED_EDGE('',*,*,#2142,.F.);
#3528=ORIENTED_EDGE('',*,*,#2451,.T.);
#3529=ORIENTED_EDGE('',*,*,#2252,.F.);
#3530=ORIENTED_EDGE('',*,*,#2452,.F.);
#3531=ORIENTED_EDGE('',*,*,#2143,.F.);
#3532=ORIENTED_EDGE('',*,*,#2452,.T.);
#3533=ORIENTED_EDGE('',*,*,#2251,.F.);
#3534=ORIENTED_EDGE('',*,*,#2453,.F.);
#3535=ORIENTED_EDGE('',*,*,#2144,.F.);
#3536=ORIENTED_EDGE('',*,*,#2453,.T.);
#3537=ORIENTED_EDGE('',*,*,#2250,.F.);
#3538=ORIENTED_EDGE('',*,*,#2454,.F.);
#3539=ORIENTED_EDGE('',*,*,#2145,.F.);
#3540=ORIENTED_EDGE('',*,*,#2454,.T.);
#3541=ORIENTED_EDGE('',*,*,#2249,.F.);
#3542=ORIENTED_EDGE('',*,*,#2455,.F.);
#3543=ORIENTED_EDGE('',*,*,#2146,.F.);
#3544=ORIENTED_EDGE('',*,*,#2455,.T.);
#3545=ORIENTED_EDGE('',*,*,#2248,.F.);
#3546=ORIENTED_EDGE('',*,*,#2456,.F.);
#3547=ORIENTED_EDGE('',*,*,#2147,.F.);
#3548=ORIENTED_EDGE('',*,*,#2456,.T.);
#3549=ORIENTED_EDGE('',*,*,#2247,.F.);
#3550=ORIENTED_EDGE('',*,*,#2457,.F.);
#3551=ORIENTED_EDGE('',*,*,#2148,.T.);
#3552=ORIENTED_EDGE('',*,*,#2457,.T.);
#3553=ORIENTED_EDGE('',*,*,#2246,.F.);
#3554=ORIENTED_EDGE('',*,*,#2458,.F.);
#3555=ORIENTED_EDGE('',*,*,#2149,.T.);
#3556=ORIENTED_EDGE('',*,*,#2458,.T.);
#3557=ORIENTED_EDGE('',*,*,#2245,.F.);
#3558=ORIENTED_EDGE('',*,*,#2459,.F.);
#3559=ORIENTED_EDGE('',*,*,#2150,.T.);
#3560=ORIENTED_EDGE('',*,*,#2459,.T.);
#3561=ORIENTED_EDGE('',*,*,#2244,.F.);
#3562=ORIENTED_EDGE('',*,*,#2460,.F.);
#3563=ORIENTED_EDGE('',*,*,#2151,.T.);
#3564=ORIENTED_EDGE('',*,*,#2460,.T.);
#3565=ORIENTED_EDGE('',*,*,#2243,.F.);
#3566=ORIENTED_EDGE('',*,*,#2461,.F.);
#3567=ORIENTED_EDGE('',*,*,#2152,.T.);
#3568=ORIENTED_EDGE('',*,*,#2461,.T.);
#3569=ORIENTED_EDGE('',*,*,#2242,.F.);
#3570=ORIENTED_EDGE('',*,*,#2330,.F.);
#3571=ORIENTED_EDGE('',*,*,#1952,.F.);
#3572=ORIENTED_EDGE('',*,*,#2449,.T.);
#3573=ORIENTED_EDGE('',*,*,#2450,.F.);
#3574=ORIENTED_EDGE('',*,*,#2190,.T.);
#3575=ORIENTED_EDGE('',*,*,#2192,.F.);
#3576=ORIENTED_EDGE('',*,*,#2342,.F.);
#3577=ORIENTED_EDGE('',*,*,#2343,.F.);
#3578=ORIENTED_EDGE('',*,*,#2345,.F.);
#3579=ORIENTED_EDGE('',*,*,#2347,.T.);
#3580=ORIENTED_EDGE('',*,*,#2349,.F.);
#3581=ORIENTED_EDGE('',*,*,#2351,.F.);
#3582=ORIENTED_EDGE('',*,*,#2353,.T.);
#3583=ORIENTED_EDGE('',*,*,#2355,.F.);
#3584=ORIENTED_EDGE('',*,*,#2357,.T.);
#3585=ORIENTED_EDGE('',*,*,#2359,.T.);
#3586=ORIENTED_EDGE('',*,*,#2361,.F.);
#3587=ORIENTED_EDGE('',*,*,#2363,.T.);
#3588=ORIENTED_EDGE('',*,*,#2365,.F.);
#3589=ORIENTED_EDGE('',*,*,#2277,.F.);
#3590=ORIENTED_EDGE('',*,*,#2292,.T.);
#3591=ORIENTED_EDGE('',*,*,#2369,.F.);
#3592=ORIENTED_EDGE('',*,*,#2371,.F.);
#3593=ORIENTED_EDGE('',*,*,#2373,.F.);
#3594=ORIENTED_EDGE('',*,*,#2375,.T.);
#3595=ORIENTED_EDGE('',*,*,#2377,.F.);
#3596=ORIENTED_EDGE('',*,*,#2379,.F.);
#3597=ORIENTED_EDGE('',*,*,#2267,.F.);
#3598=ORIENTED_EDGE('',*,*,#2269,.F.);
#3599=ORIENTED_EDGE('',*,*,#2403,.T.);
#3600=ORIENTED_EDGE('',*,*,#2404,.F.);
#3601=ORIENTED_EDGE('',*,*,#2406,.T.);
#3602=ORIENTED_EDGE('',*,*,#2408,.F.);
#3603=ORIENTED_EDGE('',*,*,#2135,.F.);
#3604=ORIENTED_EDGE('',*,*,#2137,.F.);
#3605=ORIENTED_EDGE('',*,*,#2410,.F.);
#3606=ORIENTED_EDGE('',*,*,#2411,.F.);
#3607=ORIENTED_EDGE('',*,*,#1950,.T.);
#3608=ORIENTED_EDGE('',*,*,#2282,.F.);
#3609=ORIENTED_EDGE('',*,*,#2317,.T.);
#3610=ORIENTED_EDGE('',*,*,#1931,.T.);
#3611=ORIENTED_EDGE('',*,*,#2280,.T.);
#3612=PLANE('',#3983);
#3613=PLANE('',#3989);
#3614=PLANE('',#3994);
#3615=PLANE('',#3995);
#3616=PLANE('',#3996);
#3617=PLANE('',#4011);
#3618=PLANE('',#4014);
#3619=PLANE('',#4015);
#3620=PLANE('',#4020);
#3621=PLANE('',#4021);
#3622=PLANE('',#4022);
#3623=PLANE('',#4028);
#3624=PLANE('',#4031);
#3625=PLANE('',#4032);
#3626=PLANE('',#4037);
#3627=PLANE('',#4038);
#3628=PLANE('',#4039);
#3629=PLANE('',#4040);
#3630=PLANE('',#4043);
#3631=PLANE('',#4048);
#3632=PLANE('',#4055);
#3633=PLANE('',#4056);
#3634=PLANE('',#4057);
#3635=PLANE('',#4060);
#3636=PLANE('',#4063);
#3637=PLANE('',#4068);
#3638=PLANE('',#4069);
#3639=PLANE('',#4070);
#3640=PLANE('',#4074);
#3641=PLANE('',#4077);
#3642=PLANE('',#4089);
#3643=PLANE('',#4094);
#3644=PLANE('',#4095);
#3645=PLANE('',#4096);
#3646=PLANE('',#4099);
#3647=PLANE('',#4102);
#3648=PLANE('',#4103);
#3649=PLANE('',#4108);
#3650=PLANE('',#4109);
#3651=PLANE('',#4110);
#3652=PLANE('',#4122);
#3653=PLANE('',#4125);
#3654=PLANE('',#4138);
#3655=PLANE('',#4143);
#3656=PLANE('',#4144);
#3657=PLANE('',#4145);
#3658=PLANE('',#4146);
#3659=PLANE('',#4149);
#3660=PLANE('',#4162);
#3661=PLANE('',#4167);
#3662=PLANE('',#4168);
#3663=PLANE('',#4169);
#3664=PLANE('',#4182);
#3665=PLANE('',#4185);
#3666=PLANE('',#4189);
#3667=PLANE('',#4191);
#3668=PLANE('',#4194);
#3669=PLANE('',#4197);
#3670=PLANE('',#4198);
#3671=PLANE('',#4211);
#3672=PLANE('',#4213);
#3673=PLANE('',#4214);
#3674=PLANE('',#4215);
#3675=PLANE('',#4216);
#3676=PLANE('',#4217);
#3677=PLANE('',#4218);
#3678=PLANE('',#4219);
#3679=PLANE('',#4220);
#3680=PLANE('',#4221);
#3681=PLANE('',#4222);
#3682=PLANE('',#4223);
#3683=PLANE('',#4224);
#3684=PLANE('',#4226);
#3685=PLANE('',#4228);
#3686=PLANE('',#4230);
#3687=PLANE('',#4232);
#3688=PLANE('',#4234);
#3689=PLANE('',#4236);
#3690=PLANE('',#4239);
#3691=PLANE('',#4242);
#3692=PLANE('',#4245);
#3693=PLANE('',#4248);
#3694=PLANE('',#4251);
#3695=PLANE('',#4254);
#3696=PLANE('',#4256);
#3697=PLANE('',#4258);
#3698=PLANE('',#4263);
#3699=PLANE('',#4266);
#3700=PLANE('',#4269);
#3701=PLANE('',#4271);
#3702=PLANE('',#4273);
#3703=PLANE('',#4275);
#3704=PLANE('',#4277);
#3705=PLANE('',#4279);
#3706=PLANE('',#4281);
#3707=PLANE('',#4283);
#3708=PLANE('',#4285);
#3709=PLANE('',#4287);
#3710=PLANE('',#4289);
#3711=PLANE('',#4291);
#3712=PLANE('',#4293);
#3713=PLANE('',#4296);
#3714=PLANE('',#4299);
#3715=PLANE('',#4300);
#3716=PLANE('',#4301);
#3717=PLANE('',#4304);
#3718=PLANE('',#4306);
#3719=PLANE('',#4308);
#3720=PLANE('',#4310);
#3721=PLANE('',#4312);
#3722=PLANE('',#4314);
#3723=PLANE('',#4315);
#3724=PLANE('',#4317);
#3725=PLANE('',#4319);
#3726=PLANE('',#4321);
#3727=PLANE('',#4323);
#3728=PLANE('',#4325);
#3729=PLANE('',#4327);
#3730=PLANE('',#4329);
#3731=PLANE('',#4331);
#3732=PLANE('',#4333);
#3733=PLANE('',#4335);
#3734=PLANE('',#4337);
#3735=PLANE('',#4338);
#3736=PLANE('',#4340);
#3737=PLANE('',#4342);
#3738=PLANE('',#4344);
#3739=PLANE('',#4346);
#3740=PLANE('',#4348);
#3741=PLANE('',#4351);
#3742=PLANE('',#4353);
#3743=PLANE('',#4355);
#3744=PLANE('',#4357);
#3745=PLANE('',#4359);
#3746=PLANE('',#4361);
#3747=PLANE('',#4363);
#3748=PLANE('',#4364);
#3749=ADVANCED_FACE('',(#104),#3612,.T.);
#3750=ADVANCED_FACE('',(#105),#3613,.T.);
#3751=ADVANCED_FACE('',(#106),#22,.T.);
#3752=ADVANCED_FACE('',(#107),#3614,.T.);
#3753=ADVANCED_FACE('',(#108),#3615,.T.);
#3754=ADVANCED_FACE('',(#109),#3616,.F.);
#3755=ADVANCED_FACE('',(#110),#3617,.T.);
#3756=ADVANCED_FACE('',(#111),#23,.F.);
#3757=ADVANCED_FACE('',(#112),#3618,.T.);
#3758=ADVANCED_FACE('',(#113),#3619,.T.);
#3759=ADVANCED_FACE('',(#114),#24,.F.);
#3760=ADVANCED_FACE('',(#115),#3620,.T.);
#3761=ADVANCED_FACE('',(#116),#3621,.T.);
#3762=ADVANCED_FACE('',(#117),#3622,.F.);
#3763=ADVANCED_FACE('',(#118),#3623,.T.);
#3764=ADVANCED_FACE('',(#119),#25,.T.);
#3765=ADVANCED_FACE('',(#120),#3624,.T.);
#3766=ADVANCED_FACE('',(#121),#3625,.T.);
#3767=ADVANCED_FACE('',(#122),#26,.F.);
#3768=ADVANCED_FACE('',(#123),#3626,.T.);
#3769=ADVANCED_FACE('',(#124),#3627,.T.);
#3770=ADVANCED_FACE('',(#125),#3628,.F.);
#3771=ADVANCED_FACE('',(#126),#3629,.T.);
#3772=ADVANCED_FACE('',(#127),#27,.T.);
#3773=ADVANCED_FACE('',(#128),#3630,.T.);
#3774=ADVANCED_FACE('',(#129),#3631,.T.);
#3775=ADVANCED_FACE('',(#130),#28,.T.);
#3776=ADVANCED_FACE('',(#131),#29,.F.);
#3777=ADVANCED_FACE('',(#132),#3632,.T.);
#3778=ADVANCED_FACE('',(#133),#3633,.F.);
#3779=ADVANCED_FACE('',(#134),#3634,.T.);
#3780=ADVANCED_FACE('',(#135),#30,.F.);
#3781=ADVANCED_FACE('',(#136),#3635,.T.);
#3782=ADVANCED_FACE('',(#137),#3636,.T.);
#3783=ADVANCED_FACE('',(#138),#31,.T.);
#3784=ADVANCED_FACE('',(#139),#3637,.T.);
#3785=ADVANCED_FACE('',(#140),#3638,.T.);
#3786=ADVANCED_FACE('',(#141),#3639,.F.);
#3787=ADVANCED_FACE('',(#142),#3640,.T.);
#3788=ADVANCED_FACE('',(#143),#32,.F.);
#3789=ADVANCED_FACE('',(#144),#3641,.T.);
#3790=ADVANCED_FACE('',(#145),#3642,.T.);
#3791=ADVANCED_FACE('',(#146),#33,.T.);
#3792=ADVANCED_FACE('',(#147),#3643,.T.);
#3793=ADVANCED_FACE('',(#148),#3644,.T.);
#3794=ADVANCED_FACE('',(#149),#3645,.F.);
#3795=ADVANCED_FACE('',(#150),#3646,.T.);
#3796=ADVANCED_FACE('',(#151),#34,.F.);
#3797=ADVANCED_FACE('',(#152),#3647,.T.);
#3798=ADVANCED_FACE('',(#153),#3648,.T.);
#3799=ADVANCED_FACE('',(#154),#35,.T.);
#3800=ADVANCED_FACE('',(#155),#3649,.T.);
#3801=ADVANCED_FACE('',(#156),#3650,.T.);
#3802=ADVANCED_FACE('',(#157),#3651,.F.);
#3803=ADVANCED_FACE('',(#158),#3652,.T.);
#3804=ADVANCED_FACE('',(#159),#36,.F.);
#3805=ADVANCED_FACE('',(#160,#16,#17),#3653,.T.);
#3806=ADVANCED_FACE('',(#161),#3654,.T.);
#3807=ADVANCED_FACE('',(#162),#37,.T.);
#3808=ADVANCED_FACE('',(#163),#3655,.T.);
#3809=ADVANCED_FACE('',(#164),#3656,.T.);
#3810=ADVANCED_FACE('',(#165),#3657,.F.);
#3811=ADVANCED_FACE('',(#166),#3658,.T.);
#3812=ADVANCED_FACE('',(#167),#38,.F.);
#3813=ADVANCED_FACE('',(#168),#3659,.T.);
#3814=ADVANCED_FACE('',(#169),#3660,.T.);
#3815=ADVANCED_FACE('',(#170),#39,.T.);
#3816=ADVANCED_FACE('',(#171),#3661,.T.);
#3817=ADVANCED_FACE('',(#172),#3662,.T.);
#3818=ADVANCED_FACE('',(#173,#18,#19),#3663,.F.);
#3819=ADVANCED_FACE('',(#174),#3664,.T.);
#3820=ADVANCED_FACE('',(#175),#40,.F.);
#3821=ADVANCED_FACE('',(#176),#3665,.T.);
#3822=ADVANCED_FACE('',(#177),#41,.F.);
#3823=ADVANCED_FACE('',(#178),#3666,.T.);
#3824=ADVANCED_FACE('',(#179),#3667,.F.);
#3825=ADVANCED_FACE('',(#180),#3668,.T.);
#3826=ADVANCED_FACE('',(#181),#3669,.T.);
#3827=ADVANCED_FACE('',(#182),#3670,.F.);
#3828=ADVANCED_FACE('',(#183),#3671,.T.);
#3829=ADVANCED_FACE('',(#184),#3672,.T.);
#3830=ADVANCED_FACE('',(#185),#3673,.T.);
#3831=ADVANCED_FACE('',(#186),#3674,.T.);
#3832=ADVANCED_FACE('',(#187),#3675,.T.);
#3833=ADVANCED_FACE('',(#188),#3676,.T.);
#3834=ADVANCED_FACE('',(#189),#3677,.T.);
#3835=ADVANCED_FACE('',(#190),#3678,.T.);
#3836=ADVANCED_FACE('',(#191),#3679,.T.);
#3837=ADVANCED_FACE('',(#192),#3680,.T.);
#3838=ADVANCED_FACE('',(#193),#3681,.T.);
#3839=ADVANCED_FACE('',(#194),#3682,.T.);
#3840=ADVANCED_FACE('',(#195),#3683,.T.);
#3841=ADVANCED_FACE('',(#196),#42,.T.);
#3842=ADVANCED_FACE('',(#197),#3684,.T.);
#3843=ADVANCED_FACE('',(#198),#43,.T.);
#3844=ADVANCED_FACE('',(#199),#3685,.T.);
#3845=ADVANCED_FACE('',(#200),#44,.T.);
#3846=ADVANCED_FACE('',(#201),#3686,.T.);
#3847=ADVANCED_FACE('',(#202),#45,.T.);
#3848=ADVANCED_FACE('',(#203),#3687,.T.);
#3849=ADVANCED_FACE('',(#204),#46,.F.);
#3850=ADVANCED_FACE('',(#205),#3688,.T.);
#3851=ADVANCED_FACE('',(#206),#47,.F.);
#3852=ADVANCED_FACE('',(#207),#3689,.T.);
#3853=ADVANCED_FACE('',(#208),#48,.F.);
#3854=ADVANCED_FACE('',(#209),#3690,.T.);
#3855=ADVANCED_FACE('',(#210),#49,.T.);
#3856=ADVANCED_FACE('',(#211),#3691,.T.);
#3857=ADVANCED_FACE('',(#212),#50,.F.);
#3858=ADVANCED_FACE('',(#213),#3692,.T.);
#3859=ADVANCED_FACE('',(#214),#51,.F.);
#3860=ADVANCED_FACE('',(#215),#3693,.T.);
#3861=ADVANCED_FACE('',(#216),#52,.T.);
#3862=ADVANCED_FACE('',(#217),#3694,.T.);
#3863=ADVANCED_FACE('',(#218),#53,.T.);
#3864=ADVANCED_FACE('',(#219),#3695,.T.);
#3865=ADVANCED_FACE('',(#220),#54,.T.);
#3866=ADVANCED_FACE('',(#221),#3696,.T.);
#3867=ADVANCED_FACE('',(#222),#55,.T.);
#3868=ADVANCED_FACE('',(#223),#3697,.T.);
#3869=ADVANCED_FACE('',(#224),#56,.F.);
#3870=ADVANCED_FACE('',(#225),#57,.F.);
#3871=ADVANCED_FACE('',(#226),#3698,.T.);
#3872=ADVANCED_FACE('',(#227),#58,.T.);
#3873=ADVANCED_FACE('',(#228),#3699,.T.);
#3874=ADVANCED_FACE('',(#229),#59,.F.);
#3875=ADVANCED_FACE('',(#230),#3700,.T.);
#3876=ADVANCED_FACE('',(#231),#60,.F.);
#3877=ADVANCED_FACE('',(#232),#3701,.T.);
#3878=ADVANCED_FACE('',(#233),#61,.T.);
#3879=ADVANCED_FACE('',(#234),#3702,.T.);
#3880=ADVANCED_FACE('',(#235),#62,.F.);
#3881=ADVANCED_FACE('',(#236),#3703,.T.);
#3882=ADVANCED_FACE('',(#237),#63,.F.);
#3883=ADVANCED_FACE('',(#238),#3704,.T.);
#3884=ADVANCED_FACE('',(#239),#64,.T.);
#3885=ADVANCED_FACE('',(#240),#3705,.T.);
#3886=ADVANCED_FACE('',(#241),#65,.T.);
#3887=ADVANCED_FACE('',(#242),#3706,.T.);
#3888=ADVANCED_FACE('',(#243),#66,.T.);
#3889=ADVANCED_FACE('',(#244),#3707,.T.);
#3890=ADVANCED_FACE('',(#245),#67,.T.);
#3891=ADVANCED_FACE('',(#246),#3708,.T.);
#3892=ADVANCED_FACE('',(#247),#68,.F.);
#3893=ADVANCED_FACE('',(#248),#3709,.T.);
#3894=ADVANCED_FACE('',(#249),#69,.F.);
#3895=ADVANCED_FACE('',(#250),#3710,.T.);
#3896=ADVANCED_FACE('',(#251),#70,.T.);
#3897=ADVANCED_FACE('',(#252),#3711,.T.);
#3898=ADVANCED_FACE('',(#253),#71,.T.);
#3899=ADVANCED_FACE('',(#254),#3712,.T.);
#3900=ADVANCED_FACE('',(#255),#72,.F.);
#3901=ADVANCED_FACE('',(#256),#3713,.T.);
#3902=ADVANCED_FACE('',(#257),#73,.F.);
#3903=ADVANCED_FACE('',(#258),#3714,.T.);
#3904=ADVANCED_FACE('',(#259),#3715,.T.);
#3905=ADVANCED_FACE('',(#260),#3716,.T.);
#3906=ADVANCED_FACE('',(#261),#74,.F.);
#3907=ADVANCED_FACE('',(#262),#3717,.T.);
#3908=ADVANCED_FACE('',(#263),#75,.F.);
#3909=ADVANCED_FACE('',(#264),#3718,.T.);
#3910=ADVANCED_FACE('',(#265),#76,.T.);
#3911=ADVANCED_FACE('',(#266),#3719,.T.);
#3912=ADVANCED_FACE('',(#267),#77,.T.);
#3913=ADVANCED_FACE('',(#268),#3720,.T.);
#3914=ADVANCED_FACE('',(#269),#78,.T.);
#3915=ADVANCED_FACE('',(#270),#3721,.T.);
#3916=ADVANCED_FACE('',(#271),#79,.F.);
#3917=ADVANCED_FACE('',(#272),#3722,.T.);
#3918=ADVANCED_FACE('',(#273),#15,.T.);
#3919=ADVANCED_FACE('',(#274),#3723,.T.);
#3920=ADVANCED_FACE('',(#275),#80,.T.);
#3921=ADVANCED_FACE('',(#276),#3724,.T.);
#3922=ADVANCED_FACE('',(#277),#81,.T.);
#3923=ADVANCED_FACE('',(#278),#3725,.T.);
#3924=ADVANCED_FACE('',(#279),#82,.F.);
#3925=ADVANCED_FACE('',(#280),#3726,.T.);
#3926=ADVANCED_FACE('',(#281),#83,.F.);
#3927=ADVANCED_FACE('',(#282),#3727,.T.);
#3928=ADVANCED_FACE('',(#283),#84,.T.);
#3929=ADVANCED_FACE('',(#284),#3728,.T.);
#3930=ADVANCED_FACE('',(#285),#85,.T.);
#3931=ADVANCED_FACE('',(#286),#3729,.T.);
#3932=ADVANCED_FACE('',(#287),#86,.T.);
#3933=ADVANCED_FACE('',(#288),#3730,.T.);
#3934=ADVANCED_FACE('',(#289),#87,.T.);
#3935=ADVANCED_FACE('',(#290),#3731,.T.);
#3936=ADVANCED_FACE('',(#291),#88,.F.);
#3937=ADVANCED_FACE('',(#292),#3732,.T.);
#3938=ADVANCED_FACE('',(#293),#89,.F.);
#3939=ADVANCED_FACE('',(#294),#3733,.T.);
#3940=ADVANCED_FACE('',(#295),#90,.T.);
#3941=ADVANCED_FACE('',(#296),#3734,.T.);
#3942=ADVANCED_FACE('',(#297),#3735,.T.);
#3943=ADVANCED_FACE('',(#298),#91,.F.);
#3944=ADVANCED_FACE('',(#299),#3736,.T.);
#3945=ADVANCED_FACE('',(#300),#92,.T.);
#3946=ADVANCED_FACE('',(#301),#3737,.T.);
#3947=ADVANCED_FACE('',(#302),#93,.T.);
#3948=ADVANCED_FACE('',(#303),#3738,.T.);
#3949=ADVANCED_FACE('',(#304),#94,.T.);
#3950=ADVANCED_FACE('',(#305),#3739,.T.);
#3951=ADVANCED_FACE('',(#306),#95,.F.);
#3952=ADVANCED_FACE('',(#307),#3740,.T.);
#3953=ADVANCED_FACE('',(#308),#96,.F.);
#3954=ADVANCED_FACE('',(#309),#3741,.T.);
#3955=ADVANCED_FACE('',(#310),#97,.F.);
#3956=ADVANCED_FACE('',(#311),#3742,.T.);
#3957=ADVANCED_FACE('',(#312),#98,.T.);
#3958=ADVANCED_FACE('',(#313),#3743,.T.);
#3959=ADVANCED_FACE('',(#314),#99,.T.);
#3960=ADVANCED_FACE('',(#315),#3744,.T.);
#3961=ADVANCED_FACE('',(#316),#100,.T.);
#3962=ADVANCED_FACE('',(#317),#3745,.T.);
#3963=ADVANCED_FACE('',(#318),#101,.F.);
#3964=ADVANCED_FACE('',(#319),#3746,.T.);
#3965=ADVANCED_FACE('',(#320),#102,.F.);
#3966=ADVANCED_FACE('',(#321),#3747,.T.);
#3967=ADVANCED_FACE('',(#322),#3748,.T.);
#3968=ADVANCED_FACE('',(#323),#103,.T.);
#3969=CLOSED_SHELL('',(#3749,#3750,#3751,#3752,#3753,#3754,#3755,#3756,
#3757,#3758,#3759,#3760,#3761,#3762,#3763,#3764,#3765,#3766,#3767,#3768,
#3769,#3770,#3771,#3772,#3773,#3774,#3775,#3776,#3777,#3778,#3779,#3780,
#3781,#3782,#3783,#3784,#3785,#3786,#3787,#3788,#3789,#3790,#3791,#3792,
#3793,#3794,#3795,#3796,#3797,#3798,#3799,#3800,#3801,#3802,#3803,#3804,
#3805,#3806,#3807,#3808,#3809,#3810,#3811,#3812,#3813,#3814,#3815,#3816,
#3817,#3818,#3819,#3820,#3821,#3822,#3823,#3824,#3825,#3826,#3827,#3828,
#3829,#3830,#3831,#3832,#3833,#3834,#3835,#3836,#3837,#3838,#3839,#3840,
#3841,#3842,#3843,#3844,#3845,#3846,#3847,#3848,#3849,#3850,#3851,#3852,
#3853,#3854,#3855,#3856,#3857,#3858,#3859,#3860,#3861,#3862,#3863,#3864,
#3865,#3866,#3867,#3868,#3869,#3870,#3871,#3872,#3873,#3874,#3875,#3876,
#3877,#3878,#3879,#3880,#3881,#3882,#3883,#3884,#3885,#3886,#3887,#3888,
#3889,#3890,#3891,#3892,#3893,#3894,#3895,#3896,#3897,#3898,#3899,#3900,
#3901,#3902,#3903,#3904,#3905,#3906,#3907,#3908,#3909,#3910,#3911,#3912,
#3913,#3914,#3915,#3916,#3917,#3918,#3919,#3920,#3921,#3922,#3923,#3924,
#3925,#3926,#3927,#3928,#3929,#3930,#3931,#3932,#3933,#3934,#3935,#3936,
#3937,#3938,#3939,#3940,#3941,#3942,#3943,#3944,#3945,#3946,#3947,#3948,
#3949,#3950,#3951,#3952,#3953,#3954,#3955,#3956,#3957,#3958,#3959,#3960,
#3961,#3962,#3963,#3964,#3965,#3966,#3967,#3968));
#3970=DERIVED_UNIT_ELEMENT(#3972,1.);
#3971=DERIVED_UNIT_ELEMENT(#6722,-3.);
#3972=(
MASS_UNIT()
NAMED_UNIT(*)
SI_UNIT(.KILO.,.GRAM.)
);
#3973=DERIVED_UNIT((#3970,#3971));
#3974=MEASURE_REPRESENTATION_ITEM('density measure',
POSITIVE_RATIO_MEASURE(7850.),#3973);
#3975=PROPERTY_DEFINITION_REPRESENTATION(#3980,#3977);
#3976=PROPERTY_DEFINITION_REPRESENTATION(#3981,#3978);
#3977=REPRESENTATION('material name',(#3979),#6719);
#3978=REPRESENTATION('density',(#3974),#6719);
#3979=DESCRIPTIVE_REPRESENTATION_ITEM('Steel','Steel');
#3980=PROPERTY_DEFINITION('material property','material name',#6729);
#3981=PROPERTY_DEFINITION('material property','density of part',#6729);
#3982=AXIS2_PLACEMENT_3D('placement',#5543,#4366,#4367);
#3983=AXIS2_PLACEMENT_3D('',#5544,#4368,#4369);
#3984=AXIS2_PLACEMENT_3D('',#5551,#4372,#4373);
#3985=AXIS2_PLACEMENT_3D('',#5555,#4375,#4376);
#3986=AXIS2_PLACEMENT_3D('',#5559,#4378,#4379);
#3987=AXIS2_PLACEMENT_3D('',#5563,#4381,#4382);
#3988=AXIS2_PLACEMENT_3D('',#5567,#4384,#4385);
#3989=AXIS2_PLACEMENT_3D('',#5573,#4389,#4390);
#3990=AXIS2_PLACEMENT_3D('',#5577,#4392,#4393);
#3991=AXIS2_PLACEMENT_3D('',#5580,#4395,#4396);
#3992=AXIS2_PLACEMENT_3D('',#5581,#4397,#4398);
#3993=AXIS2_PLACEMENT_3D('',#5585,#4400,#4401);
#3994=AXIS2_PLACEMENT_3D('',#5587,#4403,#4404);
#3995=AXIS2_PLACEMENT_3D('',#5592,#4408,#4409);
#3996=AXIS2_PLACEMENT_3D('',#5596,#4412,#4413);
#3997=AXIS2_PLACEMENT_3D('',#5600,#4415,#4416);
#3998=AXIS2_PLACEMENT_3D('',#5608,#4420,#4421);
#3999=AXIS2_PLACEMENT_3D('',#5612,#4423,#4424);
#4000=AXIS2_PLACEMENT_3D('',#5620,#4428,#4429);
#4001=AXIS2_PLACEMENT_3D('',#5624,#4431,#4432);
#4002=AXIS2_PLACEMENT_3D('',#5628,#4434,#4435);
#4003=AXIS2_PLACEMENT_3D('',#5630,#4436,#4437);
#4004=AXIS2_PLACEMENT_3D('',#5638,#4441,#4442);
#4005=AXIS2_PLACEMENT_3D('',#5642,#4444,#4445);
#4006=AXIS2_PLACEMENT_3D('',#5646,#4447,#4448);
#4007=AXIS2_PLACEMENT_3D('',#5650,#4450,#4451);
#4008=AXIS2_PLACEMENT_3D('',#5654,#4453,#4454);
#4009=AXIS2_PLACEMENT_3D('',#5658,#4456,#4457);
#4010=AXIS2_PLACEMENT_3D('',#5666,#4461,#4462);
#4011=AXIS2_PLACEMENT_3D('',#5668,#4464,#4465);
#4012=AXIS2_PLACEMENT_3D('',#5674,#4469,#4470);
#4013=AXIS2_PLACEMENT_3D('',#5676,#4472,#4473);
#4014=AXIS2_PLACEMENT_3D('',#5677,#4474,#4475);
#4015=AXIS2_PLACEMENT_3D('',#5686,#4480,#4481);
#4016=AXIS2_PLACEMENT_3D('',#5689,#4483,#4484);
#4017=AXIS2_PLACEMENT_3D('',#5690,#4485,#4486);
#4018=AXIS2_PLACEMENT_3D('',#5691,#4487,#4488);
#4019=AXIS2_PLACEMENT_3D('',#5695,#4490,#4491);
#4020=AXIS2_PLACEMENT_3D('',#5697,#4493,#4494);
#4021=AXIS2_PLACEMENT_3D('',#5702,#4498,#4499);
#4022=AXIS2_PLACEMENT_3D('',#5706,#4502,#4503);
#4023=AXIS2_PLACEMENT_3D('',#5710,#4505,#4506);
#4024=AXIS2_PLACEMENT_3D('',#5714,#4508,#4509);
#4025=AXIS2_PLACEMENT_3D('',#5718,#4511,#4512);
#4026=AXIS2_PLACEMENT_3D('',#5722,#4514,#4515);
#4027=AXIS2_PLACEMENT_3D('',#5725,#4517,#4518);
#4028=AXIS2_PLACEMENT_3D('',#5726,#4519,#4520);
#4029=AXIS2_PLACEMENT_3D('',#5729,#4523,#4524);
#4030=AXIS2_PLACEMENT_3D('',#5730,#4525,#4526);
#4031=AXIS2_PLACEMENT_3D('',#5731,#4527,#4528);
#4032=AXIS2_PLACEMENT_3D('',#5740,#4533,#4534);
#4033=AXIS2_PLACEMENT_3D('',#5743,#4536,#4537);
#4034=AXIS2_PLACEMENT_3D('',#5744,#4538,#4539);
#4035=AXIS2_PLACEMENT_3D('',#5745,#4540,#4541);
#4036=AXIS2_PLACEMENT_3D('',#5748,#4543,#4544);
#4037=AXIS2_PLACEMENT_3D('',#5750,#4546,#4547);
#4038=AXIS2_PLACEMENT_3D('',#5755,#4551,#4552);
#4039=AXIS2_PLACEMENT_3D('',#5759,#4555,#4556);
#4040=AXIS2_PLACEMENT_3D('',#5760,#4557,#4558);
#4041=AXIS2_PLACEMENT_3D('',#5761,#4559,#4560);
#4042=AXIS2_PLACEMENT_3D('',#5762,#4561,#4562);
#4043=AXIS2_PLACEMENT_3D('',#5763,#4563,#4564);
#4044=AXIS2_PLACEMENT_3D('',#5768,#4566,#4567);
#4045=AXIS2_PLACEMENT_3D('',#5776,#4571,#4572);
#4046=AXIS2_PLACEMENT_3D('',#5784,#4576,#4577);
#4047=AXIS2_PLACEMENT_3D('',#5788,#4579,#4580);
#4048=AXIS2_PLACEMENT_3D('',#5790,#4582,#4583);
#4049=AXIS2_PLACEMENT_3D('',#5793,#4585,#4586);
#4050=AXIS2_PLACEMENT_3D('',#5794,#4587,#4588);
#4051=AXIS2_PLACEMENT_3D('',#5795,#4589,#4590);
#4052=AXIS2_PLACEMENT_3D('',#5803,#4592,#4593);
#4053=AXIS2_PLACEMENT_3D('',#5805,#4595,#4596);
#4054=AXIS2_PLACEMENT_3D('',#5810,#4599,#4600);
#4055=AXIS2_PLACEMENT_3D('',#5812,#4602,#4603);
#4056=AXIS2_PLACEMENT_3D('',#5816,#4606,#4607);
#4057=AXIS2_PLACEMENT_3D('',#5817,#4608,#4609);
#4058=AXIS2_PLACEMENT_3D('',#5818,#4610,#4611);
#4059=AXIS2_PLACEMENT_3D('',#5820,#4612,#4613);
#4060=AXIS2_PLACEMENT_3D('',#5825,#4614,#4615);
#4061=AXIS2_PLACEMENT_3D('',#5832,#4618,#4619);
#4062=AXIS2_PLACEMENT_3D('',#5836,#4621,#4622);
#4063=AXIS2_PLACEMENT_3D('',#5838,#4624,#4625);
#4064=AXIS2_PLACEMENT_3D('',#5842,#4627,#4628);
#4065=AXIS2_PLACEMENT_3D('',#5844,#4630,#4631);
#4066=AXIS2_PLACEMENT_3D('',#5845,#4632,#4633);
#4067=AXIS2_PLACEMENT_3D('',#5849,#4635,#4636);
#4068=AXIS2_PLACEMENT_3D('',#5851,#4638,#4639);
#4069=AXIS2_PLACEMENT_3D('',#5856,#4643,#4644);
#4070=AXIS2_PLACEMENT_3D('',#5860,#4647,#4648);
#4071=AXIS2_PLACEMENT_3D('',#5862,#4649,#4650);
#4072=AXIS2_PLACEMENT_3D('',#5866,#4652,#4653);
#4073=AXIS2_PLACEMENT_3D('',#5871,#4656,#4657);
#4074=AXIS2_PLACEMENT_3D('',#5877,#4661,#4662);
#4075=AXIS2_PLACEMENT_3D('',#5880,#4665,#4666);
#4076=AXIS2_PLACEMENT_3D('',#5881,#4667,#4668);
#4077=AXIS2_PLACEMENT_3D('',#5882,#4669,#4670);
#4078=AXIS2_PLACEMENT_3D('',#5889,#4673,#4674);
#4079=AXIS2_PLACEMENT_3D('',#5893,#4676,#4677);
#4080=AXIS2_PLACEMENT_3D('',#5897,#4679,#4680);
#4081=AXIS2_PLACEMENT_3D('',#5901,#4682,#4683);
#4082=AXIS2_PLACEMENT_3D('',#5905,#4685,#4686);
#4083=AXIS2_PLACEMENT_3D('',#5909,#4688,#4689);
#4084=AXIS2_PLACEMENT_3D('',#5913,#4691,#4692);
#4085=AXIS2_PLACEMENT_3D('',#5917,#4694,#4695);
#4086=AXIS2_PLACEMENT_3D('',#5921,#4697,#4698);
#4087=AXIS2_PLACEMENT_3D('',#5925,#4700,#4701);
#4088=AXIS2_PLACEMENT_3D('',#5929,#4703,#4704);
#4089=AXIS2_PLACEMENT_3D('',#5933,#4707,#4708);
#4090=AXIS2_PLACEMENT_3D('',#5936,#4710,#4711);
#4091=AXIS2_PLACEMENT_3D('',#5938,#4713,#4714);
#4092=AXIS2_PLACEMENT_3D('',#5939,#4715,#4716);
#4093=AXIS2_PLACEMENT_3D('',#5942,#4718,#4719);
#4094=AXIS2_PLACEMENT_3D('',#5943,#4720,#4721);
#4095=AXIS2_PLACEMENT_3D('',#5948,#4725,#4726);
#4096=AXIS2_PLACEMENT_3D('',#5952,#4729,#4730);
#4097=AXIS2_PLACEMENT_3D('',#5954,#4731,#4732);
#4098=AXIS2_PLACEMENT_3D('',#5957,#4734,#4735);
#4099=AXIS2_PLACEMENT_3D('',#5958,#4736,#4737);
#4100=AXIS2_PLACEMENT_3D('',#5960,#4739,#4740);
#4101=AXIS2_PLACEMENT_3D('',#5961,#4741,#4742);
#4102=AXIS2_PLACEMENT_3D('',#5962,#4743,#4744);
#4103=AXIS2_PLACEMENT_3D('',#5971,#4749,#4750);
#4104=AXIS2_PLACEMENT_3D('',#5974,#4752,#4753);
#4105=AXIS2_PLACEMENT_3D('',#5977,#4755,#4756);
#4106=AXIS2_PLACEMENT_3D('',#5978,#4757,#4758);
#4107=AXIS2_PLACEMENT_3D('',#5981,#4760,#4761);
#4108=AXIS2_PLACEMENT_3D('',#5982,#4762,#4763);
#4109=AXIS2_PLACEMENT_3D('',#5987,#4767,#4768);
#4110=AXIS2_PLACEMENT_3D('',#5991,#4771,#4772);
#4111=AXIS2_PLACEMENT_3D('',#5995,#4774,#4775);
#4112=AXIS2_PLACEMENT_3D('',#5999,#4777,#4778);
#4113=AXIS2_PLACEMENT_3D('',#6003,#4780,#4781);
#4114=AXIS2_PLACEMENT_3D('',#6007,#4783,#4784);
#4115=AXIS2_PLACEMENT_3D('',#6011,#4786,#4787);
#4116=AXIS2_PLACEMENT_3D('',#6015,#4789,#4790);
#4117=AXIS2_PLACEMENT_3D('',#6019,#4792,#4793);
#4118=AXIS2_PLACEMENT_3D('',#6023,#4795,#4796);
#4119=AXIS2_PLACEMENT_3D('',#6027,#4798,#4799);
#4120=AXIS2_PLACEMENT_3D('',#6031,#4801,#4802);
#4121=AXIS2_PLACEMENT_3D('',#6034,#4804,#4805);
#4122=AXIS2_PLACEMENT_3D('',#6035,#4806,#4807);
#4123=AXIS2_PLACEMENT_3D('',#6041,#4811,#4812);
#4124=AXIS2_PLACEMENT_3D('',#6043,#4814,#4815);
#4125=AXIS2_PLACEMENT_3D('',#6044,#4816,#4817);
#4126=AXIS2_PLACEMENT_3D('',#6051,#4820,#4821);
#4127=AXIS2_PLACEMENT_3D('',#6055,#4823,#4824);
#4128=AXIS2_PLACEMENT_3D('',#6059,#4826,#4827);
#4129=AXIS2_PLACEMENT_3D('',#6063,#4829,#4830);
#4130=AXIS2_PLACEMENT_3D('',#6067,#4832,#4833);
#4131=AXIS2_PLACEMENT_3D('',#6071,#4835,#4836);
#4132=AXIS2_PLACEMENT_3D('',#6075,#4838,#4839);
#4133=AXIS2_PLACEMENT_3D('',#6079,#4841,#4842);
#4134=AXIS2_PLACEMENT_3D('',#6083,#4844,#4845);
#4135=AXIS2_PLACEMENT_3D('',#6087,#4847,#4848);
#4136=AXIS2_PLACEMENT_3D('',#6091,#4850,#4851);
#4137=AXIS2_PLACEMENT_3D('',#6095,#4853,#4854);
#4138=AXIS2_PLACEMENT_3D('',#6121,#4868,#4869);
#4139=AXIS2_PLACEMENT_3D('',#6124,#4871,#4872);
#4140=AXIS2_PLACEMENT_3D('',#6127,#4874,#4875);
#4141=AXIS2_PLACEMENT_3D('',#6128,#4876,#4877);
#4142=AXIS2_PLACEMENT_3D('',#6131,#4879,#4880);
#4143=AXIS2_PLACEMENT_3D('',#6132,#4881,#4882);
#4144=AXIS2_PLACEMENT_3D('',#6137,#4886,#4887);
#4145=AXIS2_PLACEMENT_3D('',#6141,#4890,#4891);
#4146=AXIS2_PLACEMENT_3D('',#6143,#4893,#4894);
#4147=AXIS2_PLACEMENT_3D('',#6149,#4898,#4899);
#4148=AXIS2_PLACEMENT_3D('',#6151,#4901,#4902);
#4149=AXIS2_PLACEMENT_3D('',#6152,#4903,#4904);
#4150=AXIS2_PLACEMENT_3D('',#6159,#4907,#4908);
#4151=AXIS2_PLACEMENT_3D('',#6163,#4910,#4911);
#4152=AXIS2_PLACEMENT_3D('',#6167,#4913,#4914);
#4153=AXIS2_PLACEMENT_3D('',#6171,#4916,#4917);
#4154=AXIS2_PLACEMENT_3D('',#6175,#4919,#4920);
#4155=AXIS2_PLACEMENT_3D('',#6179,#4922,#4923);
#4156=AXIS2_PLACEMENT_3D('',#6183,#4925,#4926);
#4157=AXIS2_PLACEMENT_3D('',#6187,#4928,#4929);
#4158=AXIS2_PLACEMENT_3D('',#6191,#4931,#4932);
#4159=AXIS2_PLACEMENT_3D('',#6195,#4934,#4935);
#4160=AXIS2_PLACEMENT_3D('',#6199,#4937,#4938);
#4161=AXIS2_PLACEMENT_3D('',#6203,#4940,#4941);
#4162=AXIS2_PLACEMENT_3D('',#6205,#4943,#4944);
#4163=AXIS2_PLACEMENT_3D('',#6208,#4946,#4947);
#4164=AXIS2_PLACEMENT_3D('',#6211,#4949,#4950);
#4165=AXIS2_PLACEMENT_3D('',#6212,#4951,#4952);
#4166=AXIS2_PLACEMENT_3D('',#6215,#4954,#4955);
#4167=AXIS2_PLACEMENT_3D('',#6216,#4956,#4957);
#4168=AXIS2_PLACEMENT_3D('',#6221,#4961,#4962);
#4169=AXIS2_PLACEMENT_3D('',#6225,#4965,#4966);
#4170=AXIS2_PLACEMENT_3D('',#6227,#4967,#4968);
#4171=AXIS2_PLACEMENT_3D('',#6231,#4970,#4971);
#4172=AXIS2_PLACEMENT_3D('',#6235,#4973,#4974);
#4173=AXIS2_PLACEMENT_3D('',#6239,#4976,#4977);
#4174=AXIS2_PLACEMENT_3D('',#6243,#4979,#4980);
#4175=AXIS2_PLACEMENT_3D('',#6247,#4982,#4983);
#4176=AXIS2_PLACEMENT_3D('',#6251,#4985,#4986);
#4177=AXIS2_PLACEMENT_3D('',#6255,#4988,#4989);
#4178=AXIS2_PLACEMENT_3D('',#6259,#4991,#4992);
#4179=AXIS2_PLACEMENT_3D('',#6263,#4994,#4995);
#4180=AXIS2_PLACEMENT_3D('',#6267,#4997,#4998);
#4181=AXIS2_PLACEMENT_3D('',#6270,#5000,#5001);
#4182=AXIS2_PLACEMENT_3D('',#6295,#5014,#5015);
#4183=AXIS2_PLACEMENT_3D('',#6301,#5019,#5020);
#4184=AXIS2_PLACEMENT_3D('',#6303,#5022,#5023);
#4185=AXIS2_PLACEMENT_3D('',#6304,#5024,#5025);
#4186=AXIS2_PLACEMENT_3D('',#6313,#5030,#5031);
#4187=AXIS2_PLACEMENT_3D('',#6317,#5033,#5034);
#4188=AXIS2_PLACEMENT_3D('',#6320,#5036,#5037);
#4189=AXIS2_PLACEMENT_3D('',#6321,#5038,#5039);
#4190=AXIS2_PLACEMENT_3D('',#6324,#5041,#5042);
#4191=AXIS2_PLACEMENT_3D('',#6326,#5044,#5045);
#4192=AXIS2_PLACEMENT_3D('',#6331,#5048,#5049);
#4193=AXIS2_PLACEMENT_3D('',#6334,#5051,#5052);
#4194=AXIS2_PLACEMENT_3D('',#6335,#5053,#5054);
#4195=AXIS2_PLACEMENT_3D('',#6337,#5055,#5056);
#4196=AXIS2_PLACEMENT_3D('',#6341,#5058,#5059);
#4197=AXIS2_PLACEMENT_3D('',#6343,#5061,#5062);
#4198=AXIS2_PLACEMENT_3D('',#6347,#5065,#5066);
#4199=AXIS2_PLACEMENT_3D('',#6349,#5067,#5068);
#4200=AXIS2_PLACEMENT_3D('',#6353,#5070,#5071);
#4201=AXIS2_PLACEMENT_3D('',#6357,#5073,#5074);
#4202=AXIS2_PLACEMENT_3D('',#6361,#5076,#5077);
#4203=AXIS2_PLACEMENT_3D('',#6365,#5079,#5080);
#4204=AXIS2_PLACEMENT_3D('',#6369,#5082,#5083);
#4205=AXIS2_PLACEMENT_3D('',#6373,#5085,#5086);
#4206=AXIS2_PLACEMENT_3D('',#6377,#5088,#5089);
#4207=AXIS2_PLACEMENT_3D('',#6381,#5091,#5092);
#4208=AXIS2_PLACEMENT_3D('',#6385,#5094,#5095);
#4209=AXIS2_PLACEMENT_3D('',#6389,#5097,#5098);
#4210=AXIS2_PLACEMENT_3D('',#6392,#5100,#5101);
#4211=AXIS2_PLACEMENT_3D('',#6393,#5102,#5103);
#4212=AXIS2_PLACEMENT_3D('',#6395,#5105,#5106);
#4213=AXIS2_PLACEMENT_3D('',#6396,#5107,#5108);
#4214=AXIS2_PLACEMENT_3D('',#6399,#5111,#5112);
#4215=AXIS2_PLACEMENT_3D('',#6401,#5114,#5115);
#4216=AXIS2_PLACEMENT_3D('',#6403,#5117,#5118);
#4217=AXIS2_PLACEMENT_3D('',#6405,#5120,#5121);
#4218=AXIS2_PLACEMENT_3D('',#6407,#5123,#5124);
#4219=AXIS2_PLACEMENT_3D('',#6408,#5125,#5126);
#4220=AXIS2_PLACEMENT_3D('',#6411,#5129,#5130);
#4221=AXIS2_PLACEMENT_3D('',#6413,#5132,#5133);
#4222=AXIS2_PLACEMENT_3D('',#6415,#5135,#5136);
#4223=AXIS2_PLACEMENT_3D('',#6417,#5138,#5139);
#4224=AXIS2_PLACEMENT_3D('',#6419,#5141,#5142);
#4225=AXIS2_PLACEMENT_3D('',#6420,#5143,#5144);
#4226=AXIS2_PLACEMENT_3D('',#6423,#5147,#5148);
#4227=AXIS2_PLACEMENT_3D('',#6425,#5150,#5151);
#4228=AXIS2_PLACEMENT_3D('',#6427,#5153,#5154);
#4229=AXIS2_PLACEMENT_3D('',#6429,#5156,#5157);
#4230=AXIS2_PLACEMENT_3D('',#6431,#5159,#5160);
#4231=AXIS2_PLACEMENT_3D('',#6433,#5162,#5163);
#4232=AXIS2_PLACEMENT_3D('',#6435,#5165,#5166);
#4233=AXIS2_PLACEMENT_3D('',#6437,#5168,#5169);
#4234=AXIS2_PLACEMENT_3D('',#6439,#5171,#5172);
#4235=AXIS2_PLACEMENT_3D('',#6441,#5174,#5175);
#4236=AXIS2_PLACEMENT_3D('',#6442,#5176,#5177);
#4237=AXIS2_PLACEMENT_3D('',#6446,#5180,#5181);
#4238=AXIS2_PLACEMENT_3D('',#6448,#5182,#5183);
#4239=AXIS2_PLACEMENT_3D('',#6450,#5185,#5186);
#4240=AXIS2_PLACEMENT_3D('',#6454,#5189,#5190);
#4241=AXIS2_PLACEMENT_3D('',#6456,#5191,#5192);
#4242=AXIS2_PLACEMENT_3D('',#6458,#5194,#5195);
#4243=AXIS2_PLACEMENT_3D('',#6462,#5198,#5199);
#4244=AXIS2_PLACEMENT_3D('',#6464,#5200,#5201);
#4245=AXIS2_PLACEMENT_3D('',#6466,#5203,#5204);
#4246=AXIS2_PLACEMENT_3D('',#6470,#5207,#5208);
#4247=AXIS2_PLACEMENT_3D('',#6472,#5209,#5210);
#4248=AXIS2_PLACEMENT_3D('',#6474,#5212,#5213);
#4249=AXIS2_PLACEMENT_3D('',#6478,#5216,#5217);
#4250=AXIS2_PLACEMENT_3D('',#6480,#5218,#5219);
#4251=AXIS2_PLACEMENT_3D('',#6482,#5221,#5222);
#4252=AXIS2_PLACEMENT_3D('',#6486,#5225,#5226);
#4253=AXIS2_PLACEMENT_3D('',#6488,#5227,#5228);
#4254=AXIS2_PLACEMENT_3D('',#6490,#5230,#5231);
#4255=AXIS2_PLACEMENT_3D('',#6492,#5233,#5234);
#4256=AXIS2_PLACEMENT_3D('',#6494,#5236,#5237);
#4257=AXIS2_PLACEMENT_3D('',#6496,#5239,#5240);
#4258=AXIS2_PLACEMENT_3D('',#6498,#5242,#5243);
#4259=AXIS2_PLACEMENT_3D('',#6499,#5244,#5245);
#4260=AXIS2_PLACEMENT_3D('',#6501,#5246,#5247);
#4261=AXIS2_PLACEMENT_3D('',#6503,#5249,#5250);
#4262=AXIS2_PLACEMENT_3D('',#6505,#5251,#5252);
#4263=AXIS2_PLACEMENT_3D('',#6507,#5254,#5255);
#4264=AXIS2_PLACEMENT_3D('',#6511,#5258,#5259);
#4265=AXIS2_PLACEMENT_3D('',#6513,#5260,#5261);
#4266=AXIS2_PLACEMENT_3D('',#6515,#5263,#5264);
#4267=AXIS2_PLACEMENT_3D('',#6519,#5267,#5268);
#4268=AXIS2_PLACEMENT_3D('',#6520,#5269,#5270);
#4269=AXIS2_PLACEMENT_3D('',#6521,#5271,#5272);
#4270=AXIS2_PLACEMENT_3D('',#6522,#5273,#5274);
#4271=AXIS2_PLACEMENT_3D('',#6524,#5276,#5277);
#4272=AXIS2_PLACEMENT_3D('',#6526,#5279,#5280);
#4273=AXIS2_PLACEMENT_3D('',#6528,#5282,#5283);
#4274=AXIS2_PLACEMENT_3D('',#6530,#5285,#5286);
#4275=AXIS2_PLACEMENT_3D('',#6532,#5288,#5289);
#4276=AXIS2_PLACEMENT_3D('',#6534,#5291,#5292);
#4277=AXIS2_PLACEMENT_3D('',#6536,#5294,#5295);
#4278=AXIS2_PLACEMENT_3D('',#6538,#5297,#5298);
#4279=AXIS2_PLACEMENT_3D('',#6540,#5300,#5301);
#4280=AXIS2_PLACEMENT_3D('',#6542,#5303,#5304);
#4281=AXIS2_PLACEMENT_3D('',#6544,#5306,#5307);
#4282=AXIS2_PLACEMENT_3D('',#6546,#5309,#5310);
#4283=AXIS2_PLACEMENT_3D('',#6548,#5312,#5313);
#4284=AXIS2_PLACEMENT_3D('',#6550,#5315,#5316);
#4285=AXIS2_PLACEMENT_3D('',#6552,#5318,#5319);
#4286=AXIS2_PLACEMENT_3D('',#6554,#5321,#5322);
#4287=AXIS2_PLACEMENT_3D('',#6556,#5324,#5325);
#4288=AXIS2_PLACEMENT_3D('',#6558,#5327,#5328);
#4289=AXIS2_PLACEMENT_3D('',#6560,#5330,#5331);
#4290=AXIS2_PLACEMENT_3D('',#6562,#5333,#5334);
#4291=AXIS2_PLACEMENT_3D('',#6564,#5336,#5337);
#4292=AXIS2_PLACEMENT_3D('',#6566,#5339,#5340);
#4293=AXIS2_PLACEMENT_3D('',#6567,#5341,#5342);
#4294=AXIS2_PLACEMENT_3D('',#6571,#5345,#5346);
#4295=AXIS2_PLACEMENT_3D('',#6573,#5347,#5348);
#4296=AXIS2_PLACEMENT_3D('',#6575,#5350,#5351);
#4297=AXIS2_PLACEMENT_3D('',#6579,#5354,#5355);
#4298=AXIS2_PLACEMENT_3D('',#6580,#5356,#5357);
#4299=AXIS2_PLACEMENT_3D('',#6581,#5358,#5359);
#4300=AXIS2_PLACEMENT_3D('',#6582,#5360,#5361);
#4301=AXIS2_PLACEMENT_3D('',#6583,#5362,#5363);
#4302=AXIS2_PLACEMENT_3D('',#6587,#5366,#5367);
#4303=AXIS2_PLACEMENT_3D('',#6588,#5368,#5369);
#4304=AXIS2_PLACEMENT_3D('',#6589,#5370,#5371);
#4305=AXIS2_PLACEMENT_3D('',#6590,#5372,#5373);
#4306=AXIS2_PLACEMENT_3D('',#6592,#5375,#5376);
#4307=AXIS2_PLACEMENT_3D('',#6594,#5378,#5379);
#4308=AXIS2_PLACEMENT_3D('',#6596,#5381,#5382);
#4309=AXIS2_PLACEMENT_3D('',#6598,#5384,#5385);
#4310=AXIS2_PLACEMENT_3D('',#6600,#5387,#5388);
#4311=AXIS2_PLACEMENT_3D('',#6602,#5390,#5391);
#4312=AXIS2_PLACEMENT_3D('',#6604,#5393,#5394);
#4313=AXIS2_PLACEMENT_3D('',#6606,#5396,#5397);
#4314=AXIS2_PLACEMENT_3D('',#6607,#5398,#5399);
#4315=AXIS2_PLACEMENT_3D('',#6625,#5401,#5402);
#4316=AXIS2_PLACEMENT_3D('',#6626,#5403,#5404);
#4317=AXIS2_PLACEMENT_3D('',#6628,#5406,#5407);
#4318=AXIS2_PLACEMENT_3D('',#6630,#5409,#5410);
#4319=AXIS2_PLACEMENT_3D('',#6632,#5412,#5413);
#4320=AXIS2_PLACEMENT_3D('',#6634,#5415,#5416);
#4321=AXIS2_PLACEMENT_3D('',#6636,#5418,#5419);
#4322=AXIS2_PLACEMENT_3D('',#6638,#5421,#5422);
#4323=AXIS2_PLACEMENT_3D('',#6640,#5424,#5425);
#4324=AXIS2_PLACEMENT_3D('',#6642,#5427,#5428);
#4325=AXIS2_PLACEMENT_3D('',#6644,#5430,#5431);
#4326=AXIS2_PLACEMENT_3D('',#6646,#5433,#5434);
#4327=AXIS2_PLACEMENT_3D('',#6648,#5436,#5437);
#4328=AXIS2_PLACEMENT_3D('',#6650,#5439,#5440);
#4329=AXIS2_PLACEMENT_3D('',#6652,#5442,#5443);
#4330=AXIS2_PLACEMENT_3D('',#6654,#5445,#5446);
#4331=AXIS2_PLACEMENT_3D('',#6656,#5448,#5449);
#4332=AXIS2_PLACEMENT_3D('',#6658,#5451,#5452);
#4333=AXIS2_PLACEMENT_3D('',#6660,#5454,#5455);
#4334=AXIS2_PLACEMENT_3D('',#6662,#5457,#5458);
#4335=AXIS2_PLACEMENT_3D('',#6664,#5460,#5461);
#4336=AXIS2_PLACEMENT_3D('',#6666,#5463,#5464);
#4337=AXIS2_PLACEMENT_3D('',#6668,#5466,#5467);
#4338=AXIS2_PLACEMENT_3D('',#6669,#5468,#5469);
#4339=AXIS2_PLACEMENT_3D('',#6671,#5471,#5472);
#4340=AXIS2_PLACEMENT_3D('',#6672,#5473,#5474);
#4341=AXIS2_PLACEMENT_3D('',#6673,#5475,#5476);
#4342=AXIS2_PLACEMENT_3D('',#6675,#5478,#5479);
#4343=AXIS2_PLACEMENT_3D('',#6677,#5481,#5482);
#4344=AXIS2_PLACEMENT_3D('',#6678,#5483,#5484);
#4345=AXIS2_PLACEMENT_3D('',#6680,#5486,#5487);
#4346=AXIS2_PLACEMENT_3D('',#6682,#5489,#5490);
#4347=AXIS2_PLACEMENT_3D('',#6684,#5492,#5493);
#4348=AXIS2_PLACEMENT_3D('',#6685,#5494,#5495);
#4349=AXIS2_PLACEMENT_3D('',#6689,#5498,#5499);
#4350=AXIS2_PLACEMENT_3D('',#6690,#5500,#5501);
#4351=AXIS2_PLACEMENT_3D('',#6691,#5502,#5503);
#4352=AXIS2_PLACEMENT_3D('',#6692,#5504,#5505);
#4353=AXIS2_PLACEMENT_3D('',#6694,#5507,#5508);
#4354=AXIS2_PLACEMENT_3D('',#6696,#5510,#5511);
#4355=AXIS2_PLACEMENT_3D('',#6698,#5513,#5514);
#4356=AXIS2_PLACEMENT_3D('',#6700,#5516,#5517);
#4357=AXIS2_PLACEMENT_3D('',#6702,#5519,#5520);
#4358=AXIS2_PLACEMENT_3D('',#6704,#5522,#5523);
#4359=AXIS2_PLACEMENT_3D('',#6706,#5525,#5526);
#4360=AXIS2_PLACEMENT_3D('',#6708,#5528,#5529);
#4361=AXIS2_PLACEMENT_3D('',#6710,#5531,#5532);
#4362=AXIS2_PLACEMENT_3D('',#6712,#5534,#5535);
#4363=AXIS2_PLACEMENT_3D('',#6714,#5537,#5538);
#4364=AXIS2_PLACEMENT_3D('',#6715,#5539,#5540);
#4365=AXIS2_PLACEMENT_3D('',#6716,#5541,#5542);
#4366=DIRECTION('axis',(0.,0.,1.));
#4367=DIRECTION('refdir',(1.,0.,0.));
#4368=DIRECTION('center_axis',(1.,0.,-8.82055185704388E-15));
#4369=DIRECTION('ref_axis',(-8.82055185704388E-15,0.,-1.));
#4370=DIRECTION('',(0.,1.,0.));
#4371=DIRECTION('',(8.82055185704388E-15,1.0244547222936E-15,1.));
#4372=DIRECTION('center_axis',(1.,0.,-8.82055185704388E-15));
#4373=DIRECTION('ref_axis',(8.82055185704388E-15,-3.70074341541722E-16,
1.));
#4374=DIRECTION('',(0.,1.,0.));
#4375=DIRECTION('center_axis',(1.,0.,-8.82055185704388E-15));
#4376=DIRECTION('ref_axis',(-8.82055185704388E-15,-7.40148683083445E-16,
-1.));
#4377=DIRECTION('',(-8.82055185704388E-15,0.,-1.));
#4378=DIRECTION('center_axis',(1.,0.,-8.82055185704388E-15));
#4379=DIRECTION('ref_axis',(0.,1.,0.));
#4380=DIRECTION('',(-5.69761731583435E-29,-1.,-6.45947941600091E-15));
#4381=DIRECTION('center_axis',(1.,0.,-8.82055185704388E-15));
#4382=DIRECTION('ref_axis',(8.82055185704388E-15,2.44804176929931E-13,1.));
#4383=DIRECTION('',(-8.82055185704388E-15,-2.95398626194907E-15,-1.));
#4384=DIRECTION('center_axis',(1.,0.,-8.82055185704388E-15));
#4385=DIRECTION('ref_axis',(0.,-1.,0.));
#4386=DIRECTION('',(8.82055185704388E-15,0.,1.));
#4387=DIRECTION('',(0.,1.,0.));
#4388=DIRECTION('',(-8.82055185704388E-15,0.,-1.));
#4389=DIRECTION('center_axis',(0.,-1.,0.));
#4390=DIRECTION('ref_axis',(0.,0.,-1.));
#4391=DIRECTION('',(1.,0.,-8.82055185704388E-15));
#4392=DIRECTION('center_axis',(0.,1.,0.));
#4393=DIRECTION('ref_axis',(0.,0.,-1.));
#4394=DIRECTION('',(0.,0.,1.));
#4395=DIRECTION('center_axis',(0.,-1.,0.));
#4396=DIRECTION('ref_axis',(-1.,0.,8.88178419700134E-15));
#4397=DIRECTION('center_axis',(0.,1.,0.));
#4398=DIRECTION('ref_axis',(0.,0.,-1.));
#4399=DIRECTION('',(0.,-1.,0.));
#4400=DIRECTION('center_axis',(0.,-1.,0.));
#4401=DIRECTION('ref_axis',(-1.,0.,8.7892656116158E-15));
#4402=DIRECTION('',(0.,-1.,0.));
#4403=DIRECTION('center_axis',(-9.03625600318418E-30,1.,-1.0244547222936E-15));
#4404=DIRECTION('ref_axis',(-8.82055185704388E-15,-1.0244547222936E-15,
-1.));
#4405=DIRECTION('',(-1.,0.,8.82055185704388E-15));
#4406=DIRECTION('',(-8.82055185704388E-15,-1.0244547222936E-15,-1.));
#4407=DIRECTION('',(-1.,0.,8.82055185704388E-15));
#4408=DIRECTION('center_axis',(0.,-1.,0.));
#4409=DIRECTION('ref_axis',(8.82055185704388E-15,0.,1.));
#4410=DIRECTION('',(1.,0.,-8.82055185704388E-15));
#4411=DIRECTION('',(8.82055185704388E-15,0.,1.));
#4412=DIRECTION('center_axis',(0.,0.,1.));
#4413=DIRECTION('ref_axis',(1.,0.,0.));
#4414=DIRECTION('',(1.,-1.0244547222936E-15,0.));
#4415=DIRECTION('center_axis',(0.,0.,1.));
#4416=DIRECTION('ref_axis',(0.,-1.,0.));
#4417=DIRECTION('',(0.,1.,0.));
#4418=DIRECTION('',(-1.,-1.54105517787103E-16,0.));
#4419=DIRECTION('',(0.,-1.,0.));
#4420=DIRECTION('center_axis',(0.,0.,1.));
#4421=DIRECTION('ref_axis',(-1.,-1.11022302462514E-14,0.));
#4422=DIRECTION('',(1.,0.,0.));
#4423=DIRECTION('center_axis',(0.,0.,1.));
#4424=DIRECTION('ref_axis',(5.92118946466746E-15,-1.,0.));
#4425=DIRECTION('',(0.,1.,0.));
#4426=DIRECTION('',(-1.,-3.05401820406212E-16,0.));
#4427=DIRECTION('',(4.2700885562506E-15,-1.,0.));
#4428=DIRECTION('center_axis',(0.,0.,1.));
#4429=DIRECTION('ref_axis',(-1.,-8.14163551391777E-15,0.));
#4430=DIRECTION('',(1.,1.98038310139587E-16,0.));
#4431=DIRECTION('center_axis',(0.,0.,-1.));
#4432=DIRECTION('ref_axis',(0.742933354233167,0.669365394360849,0.));
#4433=DIRECTION('',(0.669365394360849,-0.742933354233167,0.));
#4434=DIRECTION('center_axis',(0.,0.,1.));
#4435=DIRECTION('ref_axis',(-0.742933354233167,-0.669365394360849,0.));
#4436=DIRECTION('center_axis',(0.,0.,1.));
#4437=DIRECTION('ref_axis',(-0.0714285714285709,-0.997445717412067,0.));
#4438=DIRECTION('',(0.,1.,0.));
#4439=DIRECTION('',(-1.,-7.83369715417773E-16,0.));
#4440=DIRECTION('',(1.52116272351834E-17,-1.,0.));
#4441=DIRECTION('center_axis',(0.,0.,-1.));
#4442=DIRECTION('ref_axis',(-1.48029736616688E-16,-1.,0.));
#4443=DIRECTION('',(-1.,0.,0.));
#4444=DIRECTION('center_axis',(0.,0.,-1.));
#4445=DIRECTION('ref_axis',(-0.742933354233165,-0.669365394360852,0.));
#4446=DIRECTION('',(-0.669365394360851,0.742933354233166,0.));
#4447=DIRECTION('center_axis',(0.,0.,1.));
#4448=DIRECTION('ref_axis',(0.742933354233167,0.66936539436085,0.));
#4449=DIRECTION('',(-1.,-8.05514838952211E-17,0.));
#4450=DIRECTION('center_axis',(0.,0.,1.));
#4451=DIRECTION('ref_axis',(0.,1.,0.));
#4452=DIRECTION('',(0.,-1.,0.));
#4453=DIRECTION('center_axis',(0.,0.,-1.));
#4454=DIRECTION('ref_axis',(0.,-1.,0.));
#4455=DIRECTION('',(-1.,7.90118834947295E-15,0.));
#4456=DIRECTION('center_axis',(0.,0.,1.));
#4457=DIRECTION('ref_axis',(0.,1.,0.));
#4458=DIRECTION('',(0.,-1.,0.));
#4459=DIRECTION('',(1.,0.,0.));
#4460=DIRECTION('',(0.,1.,0.));
#4461=DIRECTION('center_axis',(0.,0.,1.));
#4462=DIRECTION('ref_axis',(1.,0.,0.));
#4463=DIRECTION('',(-1.,0.,0.));
#4464=DIRECTION('center_axis',(1.0244547222936E-15,1.,0.));
#4465=DIRECTION('ref_axis',(1.,-1.0244547222936E-15,0.));
#4466=DIRECTION('',(0.,0.,-1.));
#4467=DIRECTION('',(-1.,1.0244547222936E-15,0.));
#4468=DIRECTION('',(0.,0.,-1.));
#4469=DIRECTION('center_axis',(0.,1.,0.));
#4470=DIRECTION('ref_axis',(0.,0.,-1.));
#4471=DIRECTION('',(0.,1.,0.));
#4472=DIRECTION('center_axis',(0.,1.,0.));
#4473=DIRECTION('ref_axis',(0.,0.,-1.));
#4474=DIRECTION('center_axis',(1.4210854715202E-14,0.,1.));
#4475=DIRECTION('ref_axis',(1.,0.,-1.4210854715202E-14));
#4476=DIRECTION('',(0.,1.,0.));
#4477=DIRECTION('',(-1.,0.,1.4210854715202E-14));
#4478=DIRECTION('',(0.,1.,0.));
#4479=DIRECTION('',(1.,0.,-1.4210854715202E-14));
#4480=DIRECTION('center_axis',(0.,-1.,0.));
#4481=DIRECTION('ref_axis',(-1.,0.,8.82055185704388E-15));
#4482=DIRECTION('',(1.4210854715202E-14,0.,1.));
#4483=DIRECTION('center_axis',(0.,-1.,0.));
#4484=DIRECTION('ref_axis',(1.,0.,-8.82055185704388E-15));
#4485=DIRECTION('center_axis',(0.,1.,0.));
#4486=DIRECTION('ref_axis',(1.41866298093988E-14,0.,1.));
#4487=DIRECTION('center_axis',(0.,-1.,0.));
#4488=DIRECTION('ref_axis',(1.,0.,-8.82055185704388E-15));
#4489=DIRECTION('',(0.,-1.,0.));
#4490=DIRECTION('center_axis',(0.,1.,0.));
#4491=DIRECTION('ref_axis',(1.43716669801697E-14,0.,1.));
#4492=DIRECTION('',(0.,-1.,0.));
#4493=DIRECTION('center_axis',(0.,1.,0.));
#4494=DIRECTION('ref_axis',(1.,0.,-1.4210854715202E-14));
#4495=DIRECTION('',(-1.4210854715202E-14,0.,-1.));
#4496=DIRECTION('',(1.,0.,-1.4210854715202E-14));
#4497=DIRECTION('',(-1.4210854715202E-14,0.,-1.));
#4498=DIRECTION('center_axis',(0.,-1.,0.));
#4499=DIRECTION('ref_axis',(-1.,0.,1.4210854715202E-14));
#4500=DIRECTION('',(1.4210854715202E-14,0.,1.));
#4501=DIRECTION('',(-1.,0.,1.4210854715202E-14));
#4502=DIRECTION('center_axis',(1.,0.,-8.82055185704388E-15));
#4503=DIRECTION('ref_axis',(-8.82055185704388E-15,0.,-1.));
#4504=DIRECTION('',(-8.82055185704388E-15,0.,-1.));
#4505=DIRECTION('center_axis',(1.,0.,-8.82055185704388E-15));
#4506=DIRECTION('ref_axis',(0.,-1.,0.));
#4507=DIRECTION('',(8.82055185704388E-15,2.95398626194907E-15,1.));
#4508=DIRECTION('center_axis',(-1.,0.,8.82055185704388E-15));
#4509=DIRECTION('ref_axis',(8.82055185704388E-15,2.44804176929931E-13,1.));
#4510=DIRECTION('',(5.69761731583435E-29,1.,6.45947941600091E-15));
#4511=DIRECTION('center_axis',(-1.,0.,8.82055185704388E-15));
#4512=DIRECTION('ref_axis',(0.,1.,0.));
#4513=DIRECTION('',(-8.82055185704388E-15,0.,-1.));
#4514=DIRECTION('center_axis',(-1.,0.,8.82055185704388E-15));
#4515=DIRECTION('ref_axis',(-8.82055185704388E-15,-7.40148683083445E-16,
-1.));
#4516=DIRECTION('',(0.,-1.,0.));
#4517=DIRECTION('center_axis',(1.,0.,-8.82055185704388E-15));
#4518=DIRECTION('ref_axis',(8.82055185704388E-15,-3.70074341541722E-16,
1.));
#4519=DIRECTION('center_axis',(0.,1.,0.));
#4520=DIRECTION('ref_axis',(-8.82055185704388E-15,0.,-1.));
#4521=DIRECTION('',(-1.,0.,8.82055185704388E-15));
#4522=DIRECTION('',(-1.,0.,8.82055185704388E-15));
#4523=DIRECTION('center_axis',(0.,-1.,0.));
#4524=DIRECTION('ref_axis',(1.,0.,-8.82055185704388E-15));
#4525=DIRECTION('center_axis',(0.,-1.,0.));
#4526=DIRECTION('ref_axis',(1.,0.,-8.82055185704388E-15));
#4527=DIRECTION('center_axis',(-1.,0.,1.96011575733601E-14));
#4528=DIRECTION('ref_axis',(1.96011575733601E-14,0.,1.));
#4529=DIRECTION('',(0.,1.,0.));
#4530=DIRECTION('',(-1.96011575733601E-14,0.,-1.));
#4531=DIRECTION('',(0.,1.,0.));
#4532=DIRECTION('',(1.96011575733601E-14,0.,1.));
#4533=DIRECTION('center_axis',(0.,-1.,0.));
#4534=DIRECTION('ref_axis',(-1.4210854715202E-14,0.,-1.));
#4535=DIRECTION('',(-1.,0.,1.96011575733601E-14));
#4536=DIRECTION('center_axis',(0.,-1.,0.));
#4537=DIRECTION('ref_axis',(1.4210854715202E-14,0.,1.));
#4538=DIRECTION('center_axis',(0.,1.,0.));
#4539=DIRECTION('ref_axis',(-1.,0.,1.95769326675569E-14));
#4540=DIRECTION('center_axis',(0.,-1.,0.));
#4541=DIRECTION('ref_axis',(1.4210854715202E-14,0.,1.));
#4542=DIRECTION('',(0.,-1.,0.));
#4543=DIRECTION('center_axis',(0.,1.,0.));
#4544=DIRECTION('ref_axis',(-1.,0.,1.97619698383278E-14));
#4545=DIRECTION('',(0.,-1.,0.));
#4546=DIRECTION('center_axis',(0.,1.,0.));
#4547=DIRECTION('ref_axis',(1.96011575733601E-14,0.,1.));
#4548=DIRECTION('',(1.,0.,-1.96011575733601E-14));
#4549=DIRECTION('',(1.96011575733601E-14,0.,1.));
#4550=DIRECTION('',(1.,0.,-1.96011575733601E-14));
#4551=DIRECTION('center_axis',(0.,-1.,0.));
#4552=DIRECTION('ref_axis',(-1.96011575733601E-14,0.,-1.));
#4553=DIRECTION('',(-1.,0.,1.96011575733601E-14));
#4554=DIRECTION('',(-1.96011575733601E-14,0.,-1.));
#4555=DIRECTION('center_axis',(1.4210854715202E-14,0.,1.));
#4556=DIRECTION('ref_axis',(1.,0.,-1.4210854715202E-14));
#4557=DIRECTION('center_axis',(0.,1.,0.));
#4558=DIRECTION('ref_axis',(1.,0.,-8.82055185704388E-15));
#4559=DIRECTION('center_axis',(0.,-1.,0.));
#4560=DIRECTION('ref_axis',(1.4210854715202E-14,0.,1.));
#4561=DIRECTION('center_axis',(0.,-1.,0.));
#4562=DIRECTION('ref_axis',(1.4210854715202E-14,0.,1.));
#4563=DIRECTION('center_axis',(1.4210854715202E-14,0.,1.));
#4564=DIRECTION('ref_axis',(1.,0.,-1.4210854715202E-14));
#4565=DIRECTION('',(0.,1.,0.));
#4566=DIRECTION('center_axis',(1.4210854715202E-14,0.,1.));
#4567=DIRECTION('ref_axis',(-1.,-7.40148683083445E-16,1.4210854715202E-14));
#4568=DIRECTION('',(0.,-1.,0.));
#4569=DIRECTION('',(1.,1.76579239131055E-16,-1.4210854715202E-14));
#4570=DIRECTION('',(0.,1.,0.));
#4571=DIRECTION('center_axis',(1.4210854715202E-14,0.,1.));
#4572=DIRECTION('ref_axis',(-2.84217094304137E-13,-1.,4.03896783473296E-27));
#4573=DIRECTION('',(1.,1.67651595115511E-15,-1.4210854715202E-14));
#4574=DIRECTION('',(0.,1.,0.));
#4575=DIRECTION('',(1.,3.80691479877441E-16,-1.4210854715202E-14));
#4576=DIRECTION('center_axis',(1.4210854715202E-14,0.,1.));
#4577=DIRECTION('ref_axis',(4.26325641456067E-13,-1.,-6.05845175209747E-27));
#4578=DIRECTION('',(0.572741390821248,0.81973611561291,-8.13914469434348E-15));
#4579=DIRECTION('center_axis',(1.4210854715202E-14,0.,1.));
#4580=DIRECTION('ref_axis',(7.10542735760215E-14,1.,-1.00974195868306E-27));
#4581=DIRECTION('',(1.,0.,-1.4210854715202E-14));
#4582=DIRECTION('center_axis',(0.,-1.,0.));
#4583=DIRECTION('ref_axis',(1.,0.,-1.96011575733601E-14));
#4584=DIRECTION('',(1.4210854715202E-14,0.,1.));
#4585=DIRECTION('center_axis',(0.,1.,0.));
#4586=DIRECTION('ref_axis',(1.,0.,-1.96011575733601E-14));
#4587=DIRECTION('center_axis',(0.,-1.,0.));
#4588=DIRECTION('ref_axis',(-1.42350796210051E-14,0.,-1.));
#4589=DIRECTION('center_axis',(0.,1.,0.));
#4590=DIRECTION('ref_axis',(1.,0.,-1.96011575733601E-14));
#4591=DIRECTION('',(0.,-1.,0.));
#4592=DIRECTION('center_axis',(0.,-1.,0.));
#4593=DIRECTION('ref_axis',(0.294839026647685,0.,-0.95554693676734));
#4594=DIRECTION('',(0.,-1.,0.));
#4595=DIRECTION('center_axis',(-1.4210854715202E-14,0.,-1.));
#4596=DIRECTION('ref_axis',(-1.,-7.40148683083445E-16,1.4210854715202E-14));
#4597=DIRECTION('',(-1.4210854715202E-14,0.,-1.));
#4598=DIRECTION('',(-1.4210854715202E-14,0.,-1.));
#4599=DIRECTION('center_axis',(1.4210854715202E-14,0.,1.));
#4600=DIRECTION('ref_axis',(-1.,-7.40148683083445E-16,1.4210854715202E-14));
#4601=DIRECTION('',(-1.4210854715202E-14,0.,-1.));
#4602=DIRECTION('center_axis',(0.,-1.,0.));
#4603=DIRECTION('ref_axis',(-1.,0.,1.4210854715202E-14));
#4604=DIRECTION('',(-1.4210854715202E-14,0.,-1.));
#4605=DIRECTION('',(-1.,0.,1.4210854715202E-14));
#4606=DIRECTION('center_axis',(-1.,0.,1.96011575733601E-14));
#4607=DIRECTION('ref_axis',(1.96011575733601E-14,0.,1.));
#4608=DIRECTION('center_axis',(0.,1.,0.));
#4609=DIRECTION('ref_axis',(1.4210854715202E-14,0.,1.));
#4610=DIRECTION('center_axis',(0.,1.,0.));
#4611=DIRECTION('ref_axis',(1.,0.,-1.96011575733601E-14));
#4612=DIRECTION('center_axis',(0.,1.,0.));
#4613=DIRECTION('ref_axis',(1.,0.,-1.96011575733601E-14));
#4614=DIRECTION('center_axis',(1.,0.,-8.82055185704388E-15));
#4615=DIRECTION('ref_axis',(-8.82055185704388E-15,0.,-1.));
#4616=DIRECTION('',(0.,1.,0.));
#4617=DIRECTION('',(-8.82055185704388E-15,1.67651595115511E-15,-1.));
#4618=DIRECTION('center_axis',(1.,0.,-8.82055185704388E-15));
#4619=DIRECTION('ref_axis',(1.25347580948356E-27,1.,1.42108547152021E-13));
#4620=DIRECTION('',(-7.32506961543088E-16,0.996545758244879,-0.0830454798537492));
#4621=DIRECTION('center_axis',(1.,0.,-8.82055185704388E-15));
#4622=DIRECTION('ref_axis',(8.79008353851611E-15,0.0830454798537047,0.996545758244883));
#4623=DIRECTION('',(-8.82055185704388E-15,3.80691479877441E-16,-1.));
#4624=DIRECTION('center_axis',(0.,-1.,0.));
#4625=DIRECTION('ref_axis',(-1.4210854715202E-14,0.,-1.));
#4626=DIRECTION('',(1.,0.,-8.82055185704388E-15));
#4627=DIRECTION('center_axis',(0.,1.,0.));
#4628=DIRECTION('ref_axis',(-1.4210854715202E-14,0.,-1.));
#4629=DIRECTION('',(1.4210854715202E-14,0.,1.));
#4630=DIRECTION('center_axis',(0.,-1.,0.));
#4631=DIRECTION('ref_axis',(-1.,0.,8.84477676284703E-15));
#4632=DIRECTION('center_axis',(0.,1.,0.));
#4633=DIRECTION('ref_axis',(-1.4210854715202E-14,0.,-1.));
#4634=DIRECTION('',(0.,-1.,0.));
#4635=DIRECTION('center_axis',(0.,-1.,0.));
#4636=DIRECTION('ref_axis',(-1.,0.,8.84477676284703E-15));
#4637=DIRECTION('',(0.,-1.,0.));
#4638=DIRECTION('center_axis',(1.47877958863249E-29,1.,1.67651595115511E-15));
#4639=DIRECTION('ref_axis',(-8.82055185704388E-15,1.67651595115511E-15,
-1.));
#4640=DIRECTION('',(-1.,0.,8.82055185704388E-15));
#4641=DIRECTION('',(-8.82055185704388E-15,1.67651595115511E-15,-1.));
#4642=DIRECTION('',(-1.,0.,8.82055185704388E-15));
#4643=DIRECTION('center_axis',(-3.35790893979375E-30,-1.,-3.80691479877441E-16));
#4644=DIRECTION('ref_axis',(8.82055185704388E-15,-3.80691479877441E-16,
1.));
#4645=DIRECTION('',(-1.,0.,8.82055185704388E-15));
#4646=DIRECTION('',(8.82055185704388E-15,-3.80691479877441E-16,1.));
#4647=DIRECTION('center_axis',(1.4210854715202E-14,0.,1.));
#4648=DIRECTION('ref_axis',(1.,0.,-1.4210854715202E-14));
#4649=DIRECTION('center_axis',(1.4210854715202E-14,0.,1.));
#4650=DIRECTION('ref_axis',(7.10542735760215E-14,1.,-1.00974195868306E-27));
#4651=DIRECTION('',(-0.572741390821248,-0.81973611561291,8.13914469434348E-15));
#4652=DIRECTION('center_axis',(-1.4210854715202E-14,0.,-1.));
#4653=DIRECTION('ref_axis',(4.26325641456067E-13,-1.,-6.05845175209747E-27));
#4654=DIRECTION('',(-1.,-3.80691479877441E-16,1.4210854715202E-14));
#4655=DIRECTION('',(1.,1.67651595115511E-15,-1.4210854715202E-14));
#4656=DIRECTION('center_axis',(1.4210854715202E-14,0.,1.));
#4657=DIRECTION('ref_axis',(-2.84217094304137E-13,-1.,4.03896783473296E-27));
#4658=DIRECTION('',(0.,1.,0.));
#4659=DIRECTION('',(-1.,-1.76579239131055E-16,1.4210854715202E-14));
#4660=DIRECTION('',(0.,-1.,0.));
#4661=DIRECTION('center_axis',(-1.67651595115511E-15,1.,2.3824724609584E-29));
#4662=DIRECTION('ref_axis',(1.,1.67651595115511E-15,-1.4210854715202E-14));
#4663=DIRECTION('',(-1.4210854715202E-14,0.,-1.));
#4664=DIRECTION('',(-1.4210854715202E-14,0.,-1.));
#4665=DIRECTION('center_axis',(0.,1.,0.));
#4666=DIRECTION('ref_axis',(-1.4210854715202E-14,0.,-1.));
#4667=DIRECTION('center_axis',(0.,-1.,0.));
#4668=DIRECTION('ref_axis',(-1.,0.,8.84477676284703E-15));
#4669=DIRECTION('center_axis',(9.26991626957946E-15,-0.766044443118978,
0.642787609686539));
#4670=DIRECTION('ref_axis',(1.,6.31170430356592E-17,-1.43462096507941E-14));
#4671=DIRECTION('',(1.,1.76619990076978E-16,-1.42109421058876E-14));
#4672=DIRECTION('',(-1.09492633295873E-14,-0.642787609686539,-0.766044443118978));
#4673=DIRECTION('center_axis',(9.26991626957946E-15,-0.766044443118978,
0.642787609686539));
#4674=DIRECTION('ref_axis',(1.,1.01463384865935E-15,-1.32122360792783E-14));
#4675=DIRECTION('',(1.,6.31170430356592E-17,-1.43462096507941E-14));
#4676=DIRECTION('center_axis',(9.26991626957946E-15,-0.766044443118978,
0.642787609686539));
#4677=DIRECTION('ref_axis',(2.95166357633724E-13,0.64278760968654,0.766044443118978));
#4678=DIRECTION('',(-1.09492633295873E-14,-0.642787609686539,-0.766044443118978));
#4679=DIRECTION('center_axis',(9.26991626957946E-15,-0.766044443118978,
0.642787609686539));
#4680=DIRECTION('ref_axis',(-1.09492633295873E-14,-0.642787609686539,-0.766044443118978));
#4681=DIRECTION('',(1.,6.31170430356592E-17,-1.43462096507941E-14));
#4682=DIRECTION('center_axis',(9.26991626957946E-15,-0.766044443118978,
0.642787609686539));
#4683=DIRECTION('ref_axis',(-1.,-7.99905287154071E-14,-8.09075703565109E-14));
#4684=DIRECTION('',(-2.95166357633626E-13,-0.642787609686539,-0.766044443118978));
#4685=DIRECTION('center_axis',(9.26991626957946E-15,-0.766044443118978,
0.642787609686539));
#4686=DIRECTION('ref_axis',(1.,7.99905287154298E-14,8.0907570356538E-14));
#4687=DIRECTION('',(1.,-2.84823871256725E-14,-4.8365416796265E-14));
#4688=DIRECTION('center_axis',(9.26991626957946E-15,-0.766044443118978,
0.642787609686539));
#4689=DIRECTION('ref_axis',(1.09492633295873E-14,0.64278760968654,0.766044443118978));
#4690=DIRECTION('',(1.09492633295873E-14,0.642787609686539,0.766044443118978));
#4691=DIRECTION('center_axis',(9.26991626957946E-15,-0.766044443118978,
0.642787609686539));
#4692=DIRECTION('ref_axis',(-1.,7.70097442124873E-14,1.06198068943579E-13));
#4693=DIRECTION('',(-1.,-6.31170430356592E-17,1.43462096507941E-14));
#4694=DIRECTION('center_axis',(9.26991626957946E-15,-0.766044443118978,
0.642787609686539));
#4695=DIRECTION('ref_axis',(-1.09492633295873E-14,-0.64278760968654,-0.766044443118978));
#4696=DIRECTION('',(2.73267830974502E-13,-0.642787609686539,-0.766044443118978));
#4697=DIRECTION('center_axis',(9.26991626957946E-15,-0.766044443118978,
0.642787609686539));
#4698=DIRECTION('ref_axis',(1.09492633295873E-14,0.64278760968654,0.766044443118978));
#4699=DIRECTION('',(-1.,-6.31170430356592E-17,1.43462096507941E-14));
#4700=DIRECTION('center_axis',(9.26991626957946E-15,-0.766044443118978,
0.642787609686539));
#4701=DIRECTION('ref_axis',(1.,6.31170430356592E-17,-1.43462096507941E-14));
#4702=DIRECTION('',(-1.09492633295873E-14,-0.642787609686539,-0.766044443118978));
#4703=DIRECTION('center_axis',(9.26991626957946E-15,-0.766044443118978,
0.642787609686539));
#4704=DIRECTION('ref_axis',(-1.,1.42096350413204E-14,3.13558132235319E-14));
#4705=DIRECTION('',(-1.,3.74295017945894E-15,1.88821039368571E-14));
#4706=DIRECTION('',(1.09492633295873E-14,0.642787609686539,0.766044443118978));
#4707=DIRECTION('center_axis',(-1.,-1.7669331956899E-16,1.4210854715202E-14));
#4708=DIRECTION('ref_axis',(1.4210854715202E-14,0.,1.));
#4709=DIRECTION('',(9.26991626957946E-15,-0.766044443118978,0.642787609686539));
#4710=DIRECTION('center_axis',(1.,1.7669331956899E-16,-1.4210854715202E-14));
#4711=DIRECTION('ref_axis',(-1.4210854715202E-14,4.62592926927148E-16,-1.));
#4712=DIRECTION('',(1.4210854715202E-14,0.,1.));
#4713=DIRECTION('center_axis',(1.,1.7669331956899E-16,-1.4210854715202E-14));
#4714=DIRECTION('ref_axis',(-1.4210854715202E-14,3.70074341541719E-16,-1.));
#4715=DIRECTION('center_axis',(1.,1.7669331956899E-16,-1.4210854715202E-14));
#4716=DIRECTION('ref_axis',(-1.4210854715202E-14,0.,-1.));
#4717=DIRECTION('',(-1.,-1.76619990076978E-16,1.42109421058876E-14));
#4718=DIRECTION('center_axis',(-1.,-1.7669331956899E-16,1.4210854715202E-14));
#4719=DIRECTION('ref_axis',(-9.13456133398738E-15,0.766044443118978,-0.642787609686539));
#4720=DIRECTION('center_axis',(1.,6.31170430356592E-17,-1.43462096507941E-14));
#4721=DIRECTION('ref_axis',(-1.09492633295873E-14,-0.642787609686539,-0.766044443118978));
#4722=DIRECTION('',(-9.26991626957946E-15,0.766044443118978,-0.642787609686539));
#4723=DIRECTION('',(-1.09492633295873E-14,-0.642787609686539,-0.766044443118978));
#4724=DIRECTION('',(-9.26991626957946E-15,0.766044443118978,-0.642787609686539));
#4725=DIRECTION('center_axis',(-1.,-6.31170430356592E-17,1.43462096507941E-14));
#4726=DIRECTION('ref_axis',(1.09492633295873E-14,0.642787609686539,0.766044443118978));
#4727=DIRECTION('',(-9.26991626957946E-15,0.766044443118978,-0.642787609686539));
#4728=DIRECTION('',(1.09492633295873E-14,0.642787609686539,0.766044443118978));
#4729=DIRECTION('center_axis',(1.,0.,-8.82055185704388E-15));
#4730=DIRECTION('ref_axis',(-8.82055185704388E-15,0.,-1.));
#4731=DIRECTION('center_axis',(-1.,0.,8.82055185704388E-15));
#4732=DIRECTION('ref_axis',(8.79008353851611E-15,0.0830454798537047,0.996545758244883));
#4733=DIRECTION('',(-7.32506961543088E-16,0.996545758244879,-0.0830454798537492));
#4734=DIRECTION('center_axis',(-1.,0.,8.82055185704388E-15));
#4735=DIRECTION('ref_axis',(1.25347580948356E-27,1.,1.42108547152021E-13));
#4736=DIRECTION('center_axis',(1.,0.,-1.4210854715202E-14));
#4737=DIRECTION('ref_axis',(0.,-1.,0.));
#4738=DIRECTION('',(-1.4210854715202E-14,0.,-1.));
#4739=DIRECTION('center_axis',(1.,1.7669331956899E-16,-1.4210854715202E-14));
#4740=DIRECTION('ref_axis',(-1.4210854715202E-14,0.,-1.));
#4741=DIRECTION('center_axis',(-1.,-1.7669331956899E-16,1.4210854715202E-14));
#4742=DIRECTION('ref_axis',(-9.13456133398738E-15,0.766044443118978,-0.642787609686539));
#4743=DIRECTION('center_axis',(1.18122247032895E-16,-0.766044443118978,
0.642787609686539));
#4744=DIRECTION('ref_axis',(1.,5.50813083899156E-17,-1.18122247032895E-16));
#4745=DIRECTION('',(1.,1.54138425807794E-16,-7.05714781121616E-20));
#4746=DIRECTION('',(-5.50813083899156E-17,-0.642787609686539,-0.766044443118978));
#4747=DIRECTION('',(1.,5.50813083899156E-17,-1.18122247032895E-16));
#4748=DIRECTION('',(5.50813083899156E-17,0.642787609686539,0.766044443118978));
#4749=DIRECTION('center_axis',(-1.,-1.5419764230905E-16,0.));
#4750=DIRECTION('ref_axis',(0.,0.,1.));
#4751=DIRECTION('',(1.18122247032895E-16,-0.766044443118978,0.642787609686539));
#4752=DIRECTION('center_axis',(1.,1.5419764230905E-16,0.));
#4753=DIRECTION('ref_axis',(0.,-4.62592926927148E-16,-1.));
#4754=DIRECTION('',(0.,0.,1.));
#4755=DIRECTION('center_axis',(1.,1.5419764230905E-16,0.));
#4756=DIRECTION('ref_axis',(0.,-3.70074341541719E-16,-1.));
#4757=DIRECTION('center_axis',(1.,1.5419764230905E-16,0.));
#4758=DIRECTION('ref_axis',(0.,0.,-1.));
#4759=DIRECTION('',(-1.,-1.54138425807794E-16,7.05714781121616E-20));
#4760=DIRECTION('center_axis',(-1.,-1.5419764230905E-16,0.));
#4761=DIRECTION('ref_axis',(0.,0.766044443118978,-0.64278760968654));
#4762=DIRECTION('center_axis',(1.,5.50813083899156E-17,-1.18122247032895E-16));
#4763=DIRECTION('ref_axis',(-5.50813083899156E-17,-0.642787609686539,-0.766044443118978));
#4764=DIRECTION('',(-1.18122247032895E-16,0.766044443118978,-0.642787609686539));
#4765=DIRECTION('',(-5.50813083899156E-17,-0.642787609686539,-0.766044443118978));
#4766=DIRECTION('',(-1.18122247032895E-16,0.766044443118978,-0.642787609686539));
#4767=DIRECTION('center_axis',(-1.,-5.50813083899156E-17,1.18122247032895E-16));
#4768=DIRECTION('ref_axis',(5.50813083899156E-17,0.642787609686539,0.766044443118978));
#4769=DIRECTION('',(-1.18122247032895E-16,0.766044443118978,-0.642787609686539));
#4770=DIRECTION('',(5.50813083899156E-17,0.642787609686539,0.766044443118978));
#4771=DIRECTION('center_axis',(9.26991626957946E-15,-0.766044443118978,
0.642787609686539));
#4772=DIRECTION('ref_axis',(1.,6.31170430356592E-17,-1.43462096507941E-14));
#4773=DIRECTION('',(-1.,3.74295017945894E-15,1.88821039368571E-14));
#4774=DIRECTION('center_axis',(-9.26991626957946E-15,0.766044443118978,
-0.642787609686539));
#4775=DIRECTION('ref_axis',(-1.,1.42096350413204E-14,3.13558132235319E-14));
#4776=DIRECTION('',(1.09492633295873E-14,0.642787609686539,0.766044443118978));
#4777=DIRECTION('center_axis',(9.26991626957946E-15,-0.766044443118978,
0.642787609686539));
#4778=DIRECTION('ref_axis',(1.,6.31170430356592E-17,-1.43462096507941E-14));
#4779=DIRECTION('',(-1.,-6.31170430356592E-17,1.43462096507941E-14));
#4780=DIRECTION('center_axis',(9.26991626957946E-15,-0.766044443118978,
0.642787609686539));
#4781=DIRECTION('ref_axis',(1.09492633295873E-14,0.64278760968654,0.766044443118978));
#4782=DIRECTION('',(2.73267830974502E-13,-0.642787609686539,-0.766044443118978));
#4783=DIRECTION('center_axis',(-9.26991626957946E-15,0.766044443118978,
-0.642787609686539));
#4784=DIRECTION('ref_axis',(-1.09492633295873E-14,-0.64278760968654,-0.766044443118978));
#4785=DIRECTION('',(-1.,-6.31170430356592E-17,1.43462096507941E-14));
#4786=DIRECTION('center_axis',(-9.26991626957946E-15,0.766044443118978,
-0.642787609686539));
#4787=DIRECTION('ref_axis',(-1.,7.70097442124873E-14,1.06198068943579E-13));
#4788=DIRECTION('',(1.09492633295873E-14,0.642787609686539,0.766044443118978));
#4789=DIRECTION('center_axis',(-9.26991626957946E-15,0.766044443118978,
-0.642787609686539));
#4790=DIRECTION('ref_axis',(1.09492633295873E-14,0.64278760968654,0.766044443118978));
#4791=DIRECTION('',(1.,-2.84823871256725E-14,-4.8365416796265E-14));
#4792=DIRECTION('center_axis',(-9.26991626957946E-15,0.766044443118978,
-0.642787609686539));
#4793=DIRECTION('ref_axis',(1.,7.99905287154298E-14,8.0907570356538E-14));
#4794=DIRECTION('',(-2.95166357633626E-13,-0.642787609686539,-0.766044443118978));
#4795=DIRECTION('center_axis',(9.26991626957946E-15,-0.766044443118978,
0.642787609686539));
#4796=DIRECTION('ref_axis',(-1.,-7.99905287154071E-14,-8.09075703565109E-14));
#4797=DIRECTION('',(1.,6.31170430356592E-17,-1.43462096507941E-14));
#4798=DIRECTION('center_axis',(9.26991626957946E-15,-0.766044443118978,
0.642787609686539));
#4799=DIRECTION('ref_axis',(-1.09492633295873E-14,-0.642787609686539,-0.766044443118978));
#4800=DIRECTION('',(1.09492633295873E-14,0.642787609686539,0.766044443118978));
#4801=DIRECTION('center_axis',(-9.26991626957946E-15,0.766044443118978,
-0.642787609686539));
#4802=DIRECTION('ref_axis',(2.95166357633724E-13,0.64278760968654,0.766044443118978));
#4803=DIRECTION('',(1.,6.31170430356592E-17,-1.43462096507941E-14));
#4804=DIRECTION('center_axis',(-9.26991626957946E-15,0.766044443118978,
-0.642787609686539));
#4805=DIRECTION('ref_axis',(1.,1.01463384865935E-15,-1.32122360792783E-14));
#4806=DIRECTION('center_axis',(1.,0.,0.));
#4807=DIRECTION('ref_axis',(0.,-1.,0.));
#4808=DIRECTION('',(0.,0.,-1.));
#4809=DIRECTION('',(0.,-1.,0.));
#4810=DIRECTION('',(0.,0.,-1.));
#4811=DIRECTION('center_axis',(1.,1.5419764230905E-16,0.));
#4812=DIRECTION('ref_axis',(0.,0.,-1.));
#4813=DIRECTION('',(1.,1.54105517787103E-16,0.));
#4814=DIRECTION('center_axis',(-1.,-1.5419764230905E-16,0.));
#4815=DIRECTION('ref_axis',(0.,0.766044443118977,-0.64278760968654));
#4816=DIRECTION('center_axis',(0.,1.,6.12323399573677E-17));
#4817=DIRECTION('ref_axis',(1.,0.,0.));
#4818=DIRECTION('',(-1.,0.,0.));
#4819=DIRECTION('',(0.,-6.12323399573677E-17,1.));
#4820=DIRECTION('center_axis',(0.,1.,6.12323399573677E-17));
#4821=DIRECTION('ref_axis',(0.,-6.12323399573677E-17,1.));
#4822=DIRECTION('',(-1.,-1.55386408393928E-31,2.53765262771461E-15));
#4823=DIRECTION('center_axis',(0.,1.,6.12323399573677E-17));
#4824=DIRECTION('ref_axis',(0.,6.12323399573677E-17,-1.));
#4825=DIRECTION('',(0.,-6.12323399573677E-17,1.));
#4826=DIRECTION('center_axis',(0.,1.,6.12323399573677E-17));
#4827=DIRECTION('ref_axis',(-1.,0.,0.));
#4828=DIRECTION('',(0.998936609848685,2.82310274262076E-18,-0.0461047666084019));
#4829=DIRECTION('center_axis',(0.,1.,6.12323399573677E-17));
#4830=DIRECTION('ref_axis',(0.0461047666085012,-6.11672260901148E-17,0.998936609848681));
#4831=DIRECTION('',(0.,-6.12323399573677E-17,1.));
#4832=DIRECTION('center_axis',(0.,1.,6.12323399573677E-17));
#4833=DIRECTION('ref_axis',(-0.0870141383636912,6.1000090390202E-17,-0.996207076728942));
#4834=DIRECTION('',(-0.996207076728948,-5.32807930137864E-18,0.0870141383636205));
#4835=DIRECTION('center_axis',(0.,1.,6.12323399573677E-17));
#4836=DIRECTION('ref_axis',(1.,1.08770485875748E-30,-1.77635683940019E-14));
#4837=DIRECTION('',(0.,6.12323399573677E-17,-1.));
#4838=DIRECTION('center_axis',(0.,1.,6.12323399573677E-17));
#4839=DIRECTION('ref_axis',(-1.,-2.17540971751492E-30,3.55271367880032E-14));
#4840=DIRECTION('',(0.998936609848685,2.82310274262069E-18,-0.0461047666084007));
#4841=DIRECTION('center_axis',(0.,1.,6.12323399573677E-17));
#4842=DIRECTION('ref_axis',(0.0461047666083833,-6.11672260901151E-17,0.998936609848686));
#4843=DIRECTION('',(2.44095113740858E-15,-6.12323399573677E-17,1.));
#4844=DIRECTION('center_axis',(0.,1.,6.12323399573677E-17));
#4845=DIRECTION('ref_axis',(1.,-1.08770485875757E-30,1.77635683940035E-14));
#4846=DIRECTION('',(1.,0.,0.));
#4847=DIRECTION('center_axis',(0.,1.,6.12323399573677E-17));
#4848=DIRECTION('ref_axis',(0.,6.12323399573677E-17,-1.));
#4849=DIRECTION('',(0.,6.12323399573677E-17,-1.));
#4850=DIRECTION('center_axis',(0.,1.,6.12323399573677E-17));
#4851=DIRECTION('ref_axis',(0.087014138363646,-6.10000903902023E-17,0.996207076728946));
#4852=DIRECTION('',(-0.996207076728948,-5.32807930137865E-18,0.0870141383636207));
#4853=DIRECTION('center_axis',(0.,1.,6.12323399573677E-17));
#4854=DIRECTION('ref_axis',(-1.,0.,0.));
#4855=DIRECTION('',(0.,-6.12323399573677E-17,1.));
#4856=DIRECTION('',(0.866025403784439,3.06161699786838E-17,-0.499999999999999));
#4857=DIRECTION('',(0.866025403784439,-3.06161699786838E-17,0.5));
#4858=DIRECTION('',(0.,-6.12323399573677E-17,1.));
#4859=DIRECTION('',(-0.866025403784438,-3.06161699786839E-17,0.500000000000001));
#4860=DIRECTION('',(-0.866025403784437,3.0616169978684E-17,-0.500000000000002));
#4861=DIRECTION('',(0.,6.12323399573677E-17,-1.));
#4862=DIRECTION('',(-0.866025403784437,3.0616169978684E-17,-0.500000000000002));
#4863=DIRECTION('',(0.,6.12323399573677E-17,-1.));
#4864=DIRECTION('',(0.866025403784439,3.06161699786838E-17,-0.499999999999999));
#4865=DIRECTION('',(0.866025403784439,-3.06161699786838E-17,0.5));
#4866=DIRECTION('',(0.,-6.12323399573677E-17,1.));
#4867=DIRECTION('',(-0.866025403784438,-3.06161699786839E-17,0.500000000000001));
#4868=DIRECTION('center_axis',(1.,0.,0.));
#4869=DIRECTION('ref_axis',(0.,0.,-1.));
#4870=DIRECTION('',(0.,1.,6.12323399573677E-17));
#4871=DIRECTION('center_axis',(-1.,0.,0.));
#4872=DIRECTION('ref_axis',(0.,0.,-1.));
#4873=DIRECTION('',(0.,0.,1.));
#4874=DIRECTION('center_axis',(-1.,0.,0.));
#4875=DIRECTION('ref_axis',(0.,0.,-1.));
#4876=DIRECTION('center_axis',(-1.,0.,0.));
#4877=DIRECTION('ref_axis',(0.,0.,-1.));
#4878=DIRECTION('',(1.,0.,0.));
#4879=DIRECTION('center_axis',(1.,0.,0.));
#4880=DIRECTION('ref_axis',(0.,-1.,-9.25185853854298E-17));
#4881=DIRECTION('center_axis',(-1.,0.,0.));
#4882=DIRECTION('ref_axis',(0.,6.12323399573677E-17,-1.));
#4883=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#4884=DIRECTION('',(0.,6.12323399573677E-17,-1.));
#4885=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#4886=DIRECTION('center_axis',(1.,0.,0.));
#4887=DIRECTION('ref_axis',(0.,-6.12323399573677E-17,1.));
#4888=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#4889=DIRECTION('',(0.,-6.12323399573677E-17,1.));
#4890=DIRECTION('center_axis',(1.18122247032895E-16,-0.766044443118978,
0.642787609686539));
#4891=DIRECTION('ref_axis',(1.,5.50813083899156E-17,-1.18122247032895E-16));
#4892=DIRECTION('',(1.,5.50813083899156E-17,-1.18122247032895E-16));
#4893=DIRECTION('center_axis',(-1.,0.,0.));
#4894=DIRECTION('ref_axis',(0.,1.,0.));
#4895=DIRECTION('',(0.,0.,-1.));
#4896=DIRECTION('',(0.,-1.,0.));
#4897=DIRECTION('',(0.,0.,-1.));
#4898=DIRECTION('center_axis',(-1.,0.,0.));
#4899=DIRECTION('ref_axis',(0.,0.,-1.));
#4900=DIRECTION('',(-1.,0.,0.));
#4901=DIRECTION('center_axis',(-1.,0.,0.));
#4902=DIRECTION('ref_axis',(0.,0.,-1.));
#4903=DIRECTION('center_axis',(2.33805592120562E-16,-0.766044443118978,
0.642787609686539));
#4904=DIRECTION('ref_axis',(1.,1.09025338125282E-16,-2.33805592120562E-16));
#4905=DIRECTION('',(1.,3.05075883014833E-16,-1.61650918386773E-19));
#4906=DIRECTION('',(4.16106321812532E-15,-0.642787609686539,-0.766044443118978));
#4907=DIRECTION('center_axis',(2.33805592120562E-16,-0.766044443118978,
0.642787609686539));
#4908=DIRECTION('ref_axis',(1.09025338125282E-16,0.642787609686539,0.766044443118978));
#4909=DIRECTION('',(1.,1.09025338125282E-16,-2.33805592120562E-16));
#4910=DIRECTION('center_axis',(2.33805592120562E-16,-0.766044443118978,
0.642787609686539));
#4911=DIRECTION('ref_axis',(-1.09025338125282E-16,-0.642787609686539,-0.766044443118978));
#4912=DIRECTION('',(-1.09025338125282E-16,-0.642787609686539,-0.766044443118978));
#4913=DIRECTION('center_axis',(2.33805592120562E-16,-0.766044443118978,
0.642787609686539));
#4914=DIRECTION('ref_axis',(1.09025338125282E-16,0.642787609686539,0.766044443118978));
#4915=DIRECTION('',(1.,1.09025338125282E-16,-2.33805592120562E-16));
#4916=DIRECTION('center_axis',(2.33805592120562E-16,-0.766044443118978,
0.642787609686539));
#4917=DIRECTION('ref_axis',(1.,-2.74552507874579E-15,-3.63572630666796E-15));
#4918=DIRECTION('',(-1.09025338125282E-16,-0.642787609686539,-0.766044443118978));
#4919=DIRECTION('center_axis',(2.33805592120562E-16,-0.766044443118978,
0.642787609686539));
#4920=DIRECTION('ref_axis',(-1.,2.74552507874579E-15,3.63572630666796E-15));
#4921=DIRECTION('',(1.,8.22662942343055E-16,6.16674586516295E-16));
#4922=DIRECTION('center_axis',(2.33805592120562E-16,-0.766044443118978,
0.642787609686539));
#4923=DIRECTION('ref_axis',(-1.09025338125282E-16,-0.642787609686539,-0.766044443118978));
#4924=DIRECTION('',(1.09025338125282E-16,0.642787609686539,0.766044443118978));
#4925=DIRECTION('center_axis',(2.33805592120562E-16,-0.766044443118978,
0.642787609686539));
#4926=DIRECTION('ref_axis',(1.,-2.74552507874579E-15,-3.63572630666796E-15));
#4927=DIRECTION('',(-1.,-1.09025338125282E-16,2.33805592120562E-16));
#4928=DIRECTION('center_axis',(2.33805592120562E-16,-0.766044443118978,
0.642787609686539));
#4929=DIRECTION('ref_axis',(1.09025338125282E-16,0.642787609686539,0.766044443118978));
#4930=DIRECTION('',(-1.78725937321275E-14,-0.642787609686539,-0.766044443118978));
#4931=DIRECTION('center_axis',(2.33805592120562E-16,-0.766044443118978,
0.642787609686539));
#4932=DIRECTION('ref_axis',(-1.09025338125282E-16,-0.642787609686539,-0.766044443118978));
#4933=DIRECTION('',(-1.,-1.09025338125282E-16,2.33805592120562E-16));
#4934=DIRECTION('center_axis',(2.33805592120562E-16,-0.766044443118978,
0.642787609686539));
#4935=DIRECTION('ref_axis',(-1.,-5.81812617186742E-15,-6.57003583697423E-15));
#4936=DIRECTION('',(1.76545430558768E-14,-0.642787609686539,-0.766044443118978));
#4937=DIRECTION('center_axis',(2.33805592120562E-16,-0.766044443118978,
0.642787609686539));
#4938=DIRECTION('ref_axis',(1.,1.09025338125282E-16,-2.33805592120562E-16));
#4939=DIRECTION('',(-1.,-1.09025338125282E-16,2.33805592120562E-16));
#4940=DIRECTION('center_axis',(2.33805592120562E-16,-0.766044443118978,
0.642787609686539));
#4941=DIRECTION('ref_axis',(1.09025338125282E-16,0.642787609686539,0.766044443118978));
#4942=DIRECTION('',(-1.09025338125282E-16,-0.642787609686539,-0.766044443118978));
#4943=DIRECTION('center_axis',(-1.,-3.0521152424083E-16,0.));
#4944=DIRECTION('ref_axis',(0.,0.,1.));
#4945=DIRECTION('',(2.33805592120562E-16,-0.766044443118978,0.642787609686539));
#4946=DIRECTION('center_axis',(1.,3.0521152424083E-16,0.));
#4947=DIRECTION('ref_axis',(0.,1.85037170770859E-16,-1.));
#4948=DIRECTION('',(0.,0.,1.));
#4949=DIRECTION('center_axis',(-1.,-3.0521152424083E-16,0.));
#4950=DIRECTION('ref_axis',(0.,0.766044443118978,-0.642787609686539));
#4951=DIRECTION('center_axis',(1.,3.0521152424083E-16,0.));
#4952=DIRECTION('ref_axis',(0.,0.,-1.));
#4953=DIRECTION('',(-1.,-3.05075883014833E-16,1.61650918386773E-19));
#4954=DIRECTION('center_axis',(-1.,-3.0521152424083E-16,0.));
#4955=DIRECTION('ref_axis',(0.,0.766044443118978,-0.64278760968654));
#4956=DIRECTION('center_axis',(1.,2.85378535434745E-15,3.03727201802115E-15));
#4957=DIRECTION('ref_axis',(4.16106321812532E-15,-0.642787609686539,-0.766044443118978));
#4958=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#4959=DIRECTION('',(4.16106321812532E-15,-0.642787609686539,-0.766044443118978));
#4960=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#4961=DIRECTION('center_axis',(-1.,-1.09025338125282E-16,2.33805592120562E-16));
#4962=DIRECTION('ref_axis',(1.09025338125282E-16,0.642787609686539,0.766044443118978));
#4963=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#4964=DIRECTION('',(1.09025338125282E-16,0.642787609686539,0.766044443118978));
#4965=DIRECTION('center_axis',(0.,1.,6.12323399573677E-17));
#4966=DIRECTION('ref_axis',(1.,0.,0.));
#4967=DIRECTION('center_axis',(0.,1.,6.12323399573677E-17));
#4968=DIRECTION('ref_axis',(-1.,0.,0.));
#4969=DIRECTION('',(0.996207076728948,5.32807930137865E-18,-0.0870141383636207));
#4970=DIRECTION('center_axis',(0.,1.,6.12323399573677E-17));
#4971=DIRECTION('ref_axis',(0.087014138363646,-6.10000903902023E-17,0.996207076728946));
#4972=DIRECTION('',(0.,6.12323399573677E-17,-1.));
#4973=DIRECTION('center_axis',(0.,-1.,-6.12323399573677E-17));
#4974=DIRECTION('ref_axis',(0.,6.12323399573677E-17,-1.));
#4975=DIRECTION('',(1.,0.,0.));
#4976=DIRECTION('center_axis',(0.,-1.,-6.12323399573677E-17));
#4977=DIRECTION('ref_axis',(1.,-1.08770485875757E-30,1.77635683940035E-14));
#4978=DIRECTION('',(2.44095113740858E-15,-6.12323399573677E-17,1.));
#4979=DIRECTION('center_axis',(0.,-1.,-6.12323399573677E-17));
#4980=DIRECTION('ref_axis',(0.0461047666083833,-6.11672260901151E-17,0.998936609848686));
#4981=DIRECTION('',(-0.998936609848685,-2.82310274262069E-18,0.0461047666084007));
#4982=DIRECTION('center_axis',(0.,-1.,-6.12323399573677E-17));
#4983=DIRECTION('ref_axis',(-1.,-2.17540971751492E-30,3.55271367880032E-14));
#4984=DIRECTION('',(0.,6.12323399573677E-17,-1.));
#4985=DIRECTION('center_axis',(0.,1.,6.12323399573677E-17));
#4986=DIRECTION('ref_axis',(1.,1.08770485875748E-30,-1.77635683940019E-14));
#4987=DIRECTION('',(-0.996207076728948,-5.32807930137864E-18,0.0870141383636205));
#4988=DIRECTION('center_axis',(0.,1.,6.12323399573677E-17));
#4989=DIRECTION('ref_axis',(-0.0870141383636912,6.1000090390202E-17,-0.996207076728942));
#4990=DIRECTION('',(0.,-6.12323399573677E-17,1.));
#4991=DIRECTION('center_axis',(0.,-1.,-6.12323399573677E-17));
#4992=DIRECTION('ref_axis',(0.0461047666085012,-6.11672260901148E-17,0.998936609848681));
#4993=DIRECTION('',(-0.998936609848685,-2.82310274262076E-18,0.0461047666084019));
#4994=DIRECTION('center_axis',(0.,-1.,-6.12323399573677E-17));
#4995=DIRECTION('ref_axis',(-1.,0.,0.));
#4996=DIRECTION('',(0.,6.12323399573677E-17,-1.));
#4997=DIRECTION('center_axis',(0.,-1.,-6.12323399573677E-17));
#4998=DIRECTION('ref_axis',(0.,6.12323399573677E-17,-1.));
#4999=DIRECTION('',(1.,1.55386408393928E-31,-2.53765262771461E-15));
#5000=DIRECTION('center_axis',(0.,1.,6.12323399573677E-17));
#5001=DIRECTION('ref_axis',(0.,-6.12323399573677E-17,1.));
#5002=DIRECTION('',(-0.866025403784439,-3.06161699786838E-17,0.499999999999999));
#5003=DIRECTION('',(0.,-6.12323399573677E-17,1.));
#5004=DIRECTION('',(0.866025403784437,-3.0616169978684E-17,0.500000000000002));
#5005=DIRECTION('',(0.866025403784438,3.06161699786839E-17,-0.500000000000001));
#5006=DIRECTION('',(0.,6.12323399573677E-17,-1.));
#5007=DIRECTION('',(-0.866025403784439,3.06161699786838E-17,-0.5));
#5008=DIRECTION('',(0.866025403784437,-3.0616169978684E-17,0.500000000000002));
#5009=DIRECTION('',(0.866025403784438,3.06161699786839E-17,-0.500000000000001));
#5010=DIRECTION('',(0.,6.12323399573677E-17,-1.));
#5011=DIRECTION('',(-0.866025403784439,3.06161699786838E-17,-0.5));
#5012=DIRECTION('',(-0.866025403784439,-3.06161699786838E-17,0.499999999999999));
#5013=DIRECTION('',(0.,-6.12323399573677E-17,1.));
#5014=DIRECTION('center_axis',(1.,4.2700885562506E-15,0.));
#5015=DIRECTION('ref_axis',(4.2700885562506E-15,-1.,0.));
#5016=DIRECTION('',(0.,0.,-1.));
#5017=DIRECTION('',(4.2700885562506E-15,-1.,0.));
#5018=DIRECTION('',(0.,0.,-1.));
#5019=DIRECTION('center_axis',(1.,3.0521152424083E-16,0.));
#5020=DIRECTION('ref_axis',(0.,0.,-1.));
#5021=DIRECTION('',(1.,3.05401820406212E-16,0.));
#5022=DIRECTION('center_axis',(-1.,-3.0521152424083E-16,0.));
#5023=DIRECTION('ref_axis',(0.,0.766044443118978,-0.64278760968654));
#5024=DIRECTION('center_axis',(1.,7.83686840911875E-16,-7.68475213676692E-16));
#5025=DIRECTION('ref_axis',(-7.68475213676692E-16,-6.12323399573683E-17,
-1.));
#5026=DIRECTION('',(-7.83686840911875E-16,1.,-6.12323399573677E-17));
#5027=DIRECTION('',(-7.68475213676692E-16,-6.12323399573683E-17,-1.));
#5028=DIRECTION('',(-7.83686840911875E-16,1.,-6.12323399573677E-17));
#5029=DIRECTION('',(-7.68475213676692E-16,-6.12323399573683E-17,-1.));
#5030=DIRECTION('center_axis',(1.,7.83686840911875E-16,0.));
#5031=DIRECTION('ref_axis',(0.,0.,-1.));
#5032=DIRECTION('',(1.,7.83686840911875E-16,-3.17125494102139E-19));
#5033=DIRECTION('center_axis',(-1.,-7.83686840911875E-16,0.));
#5034=DIRECTION('ref_axis',(-7.40148683083438E-16,1.,0.));
#5035=DIRECTION('',(1.,7.83369715417773E-16,0.));
#5036=DIRECTION('center_axis',(-1.,-7.83686840911875E-16,0.));
#5037=DIRECTION('ref_axis',(-7.40148683083438E-16,1.,0.));
#5038=DIRECTION('center_axis',(-1.,-7.83686840911875E-16,0.));
#5039=DIRECTION('ref_axis',(0.,0.,1.));
#5040=DIRECTION('',(7.83686840911875E-16,-1.,6.12323399573677E-17));
#5041=DIRECTION('center_axis',(1.,7.83686840911875E-16,0.));
#5042=DIRECTION('ref_axis',(0.,1.48029736616688E-15,-1.));
#5043=DIRECTION('',(0.,0.,1.));
#5044=DIRECTION('center_axis',(7.83686840911875E-16,-1.,6.12323399573677E-17));
#5045=DIRECTION('ref_axis',(1.,7.83686840911875E-16,-7.83686840911875E-16));
#5046=DIRECTION('',(-1.,-7.83686840911875E-16,3.17125494102139E-19));
#5047=DIRECTION('',(7.83686840911875E-16,6.12323399573683E-17,1.));
#5048=DIRECTION('center_axis',(-7.83686840911875E-16,1.,-6.12323399573677E-17));
#5049=DIRECTION('ref_axis',(0.15685749214111,1.83401512423483E-16,0.987621246814386));
#5050=DIRECTION('',(0.987621246814386,7.64381023649769E-16,-0.156857492141109));
#5051=DIRECTION('center_axis',(-7.83686840911875E-16,1.,-6.12323399573677E-17));
#5052=DIRECTION('ref_axis',(1.,7.83686840911875E-16,3.2653618371328E-16));
#5053=DIRECTION('center_axis',(7.83686840911875E-16,-1.,6.12323399573677E-17));
#5054=DIRECTION('ref_axis',(1.,7.83686840911875E-16,-7.83686840911875E-16));
#5055=DIRECTION('center_axis',(7.83686840911875E-16,-1.,6.12323399573677E-17));
#5056=DIRECTION('ref_axis',(1.,7.83686840911875E-16,3.2653618371328E-16));
#5057=DIRECTION('',(0.987621246814386,7.64381023649769E-16,-0.156857492141109));
#5058=DIRECTION('center_axis',(7.83686840911875E-16,-1.,6.12323399573677E-17));
#5059=DIRECTION('ref_axis',(0.15685749214111,1.83401512423483E-16,0.987621246814386));
#5060=DIRECTION('',(-7.83686840911875E-16,-6.12323399573683E-17,-1.));
#5061=DIRECTION('center_axis',(-1.,0.,0.));
#5062=DIRECTION('ref_axis',(0.,1.,0.));
#5063=DIRECTION('',(0.,0.,-1.));
#5064=DIRECTION('',(0.,-1.,0.));
#5065=DIRECTION('center_axis',(2.33805592120562E-16,-0.766044443118978,
0.642787609686539));
#5066=DIRECTION('ref_axis',(1.,1.09025338125282E-16,-2.33805592120562E-16));
#5067=DIRECTION('center_axis',(-2.33805592120562E-16,0.766044443118978,
-0.642787609686539));
#5068=DIRECTION('ref_axis',(1.09025338125282E-16,0.642787609686539,0.766044443118978));
#5069=DIRECTION('',(1.,1.09025338125282E-16,-2.33805592120562E-16));
#5070=DIRECTION('center_axis',(-2.33805592120562E-16,0.766044443118978,
-0.642787609686539));
#5071=DIRECTION('ref_axis',(1.,1.09025338125282E-16,-2.33805592120562E-16));
#5072=DIRECTION('',(1.76545430558768E-14,-0.642787609686539,-0.766044443118978));
#5073=DIRECTION('center_axis',(2.33805592120562E-16,-0.766044443118978,
0.642787609686539));
#5074=DIRECTION('ref_axis',(-1.,-5.81812617186742E-15,-6.57003583697423E-15));
#5075=DIRECTION('',(1.,1.09025338125282E-16,-2.33805592120562E-16));
#5076=DIRECTION('center_axis',(2.33805592120562E-16,-0.766044443118978,
0.642787609686539));
#5077=DIRECTION('ref_axis',(-1.09025338125282E-16,-0.642787609686539,-0.766044443118978));
#5078=DIRECTION('',(1.78725937321275E-14,0.642787609686539,0.766044443118978));
#5079=DIRECTION('center_axis',(-2.33805592120562E-16,0.766044443118978,
-0.642787609686539));
#5080=DIRECTION('ref_axis',(1.09025338125282E-16,0.642787609686539,0.766044443118978));
#5081=DIRECTION('',(1.,1.09025338125282E-16,-2.33805592120562E-16));
#5082=DIRECTION('center_axis',(-2.33805592120562E-16,0.766044443118978,
-0.642787609686539));
#5083=DIRECTION('ref_axis',(1.,-2.74552507874579E-15,-3.63572630666796E-15));
#5084=DIRECTION('',(-1.09025338125282E-16,-0.642787609686539,-0.766044443118978));
#5085=DIRECTION('center_axis',(-2.33805592120562E-16,0.766044443118978,
-0.642787609686539));
#5086=DIRECTION('ref_axis',(-1.09025338125282E-16,-0.642787609686539,-0.766044443118978));
#5087=DIRECTION('',(-1.,-8.22662942343055E-16,-6.16674586516295E-16));
#5088=DIRECTION('center_axis',(-2.33805592120562E-16,0.766044443118978,
-0.642787609686539));
#5089=DIRECTION('ref_axis',(-1.,2.74552507874579E-15,3.63572630666796E-15));
#5090=DIRECTION('',(1.09025338125282E-16,0.642787609686539,0.766044443118978));
#5091=DIRECTION('center_axis',(2.33805592120562E-16,-0.766044443118978,
0.642787609686539));
#5092=DIRECTION('ref_axis',(1.,-2.74552507874579E-15,-3.63572630666796E-15));
#5093=DIRECTION('',(-1.,-1.09025338125282E-16,2.33805592120562E-16));
#5094=DIRECTION('center_axis',(2.33805592120562E-16,-0.766044443118978,
0.642787609686539));
#5095=DIRECTION('ref_axis',(1.09025338125282E-16,0.642787609686539,0.766044443118978));
#5096=DIRECTION('',(-1.09025338125282E-16,-0.642787609686539,-0.766044443118978));
#5097=DIRECTION('center_axis',(-2.33805592120562E-16,0.766044443118978,
-0.642787609686539));
#5098=DIRECTION('ref_axis',(-1.09025338125282E-16,-0.642787609686539,-0.766044443118978));
#5099=DIRECTION('',(-1.,-1.09025338125282E-16,2.33805592120562E-16));
#5100=DIRECTION('center_axis',(2.33805592120562E-16,-0.766044443118978,
0.642787609686539));
#5101=DIRECTION('ref_axis',(1.09025338125282E-16,0.642787609686539,0.766044443118978));
#5102=DIRECTION('center_axis',(1.,7.83686840911875E-16,0.));
#5103=DIRECTION('ref_axis',(0.,0.,-1.));
#5104=DIRECTION('',(0.,0.,-1.));
#5105=DIRECTION('center_axis',(-1.,-7.83686840911875E-16,0.));
#5106=DIRECTION('ref_axis',(-7.40148683083438E-16,1.,-9.25185853854297E-17));
#5107=DIRECTION('center_axis',(1.,0.,0.));
#5108=DIRECTION('ref_axis',(0.,-6.12323399573677E-17,1.));
#5109=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5110=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5111=DIRECTION('center_axis',(0.499999999999999,-5.30287619362454E-17,
0.866025403784439));
#5112=DIRECTION('ref_axis',(-0.866025403784439,-3.06161699786838E-17,0.499999999999999));
#5113=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5114=DIRECTION('center_axis',(-0.5,-5.30287619362453E-17,0.866025403784439));
#5115=DIRECTION('ref_axis',(-0.866025403784439,3.06161699786838E-17,-0.5));
#5116=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5117=DIRECTION('center_axis',(-1.,0.,0.));
#5118=DIRECTION('ref_axis',(0.,6.12323399573677E-17,-1.));
#5119=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5120=DIRECTION('center_axis',(-0.500000000000001,5.30287619362453E-17,
-0.866025403784438));
#5121=DIRECTION('ref_axis',(0.866025403784438,3.06161699786839E-17,-0.500000000000001));
#5122=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5123=DIRECTION('center_axis',(0.500000000000002,5.30287619362453E-17,-0.866025403784437));
#5124=DIRECTION('ref_axis',(0.866025403784437,-3.0616169978684E-17,0.500000000000002));
#5125=DIRECTION('center_axis',(-0.5,-5.30287619362453E-17,0.866025403784439));
#5126=DIRECTION('ref_axis',(-0.866025403784439,3.06161699786838E-17,-0.5));
#5127=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5128=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5129=DIRECTION('center_axis',(-1.,0.,0.));
#5130=DIRECTION('ref_axis',(0.,6.12323399573677E-17,-1.));
#5131=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5132=DIRECTION('center_axis',(-0.500000000000001,5.30287619362453E-17,
-0.866025403784438));
#5133=DIRECTION('ref_axis',(0.866025403784438,3.06161699786839E-17,-0.500000000000001));
#5134=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5135=DIRECTION('center_axis',(0.500000000000002,5.30287619362453E-17,-0.866025403784437));
#5136=DIRECTION('ref_axis',(0.866025403784437,-3.0616169978684E-17,0.500000000000002));
#5137=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5138=DIRECTION('center_axis',(1.,0.,0.));
#5139=DIRECTION('ref_axis',(0.,-6.12323399573677E-17,1.));
#5140=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5141=DIRECTION('center_axis',(0.499999999999999,-5.30287619362454E-17,
0.866025403784439));
#5142=DIRECTION('ref_axis',(-0.866025403784439,-3.06161699786838E-17,0.499999999999999));
#5143=DIRECTION('center_axis',(0.,-1.,-6.12323399573677E-17));
#5144=DIRECTION('ref_axis',(-1.,-2.17540971751492E-30,3.55271367880032E-14));
#5145=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5146=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5147=DIRECTION('center_axis',(0.0461047666084008,-6.11672260901151E-17,
0.998936609848686));
#5148=DIRECTION('ref_axis',(-0.998936609848685,-2.82310274262069E-18,0.0461047666084007));
#5149=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5150=DIRECTION('center_axis',(0.,-1.,-6.12323399573677E-17));
#5151=DIRECTION('ref_axis',(0.0461047666083833,-6.11672260901151E-17,0.998936609848686));
#5152=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5153=DIRECTION('center_axis',(1.,1.49465149865125E-31,-2.44095113740858E-15));
#5154=DIRECTION('ref_axis',(2.44095113740858E-15,-6.12323399573677E-17,
1.));
#5155=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5156=DIRECTION('center_axis',(0.,-1.,-6.12323399573677E-17));
#5157=DIRECTION('ref_axis',(1.,-1.08770485875757E-30,1.77635683940035E-14));
#5158=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5159=DIRECTION('center_axis',(0.,6.12323399573677E-17,-1.));
#5160=DIRECTION('ref_axis',(1.,0.,0.));
#5161=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5162=DIRECTION('center_axis',(0.,-1.,-6.12323399573677E-17));
#5163=DIRECTION('ref_axis',(0.,6.12323399573677E-17,-1.));
#5164=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5165=DIRECTION('center_axis',(-1.,0.,0.));
#5166=DIRECTION('ref_axis',(0.,6.12323399573677E-17,-1.));
#5167=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5168=DIRECTION('center_axis',(0.,-1.,-6.12323399573677E-17));
#5169=DIRECTION('ref_axis',(0.087014138363646,-6.10000903902023E-17,0.996207076728946));
#5170=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5171=DIRECTION('center_axis',(-0.0870141383636207,6.10000903902024E-17,
-0.996207076728948));
#5172=DIRECTION('ref_axis',(0.996207076728948,5.32807930137865E-18,-0.0870141383636207));
#5173=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5174=DIRECTION('center_axis',(0.,-1.,-6.12323399573677E-17));
#5175=DIRECTION('ref_axis',(-1.,0.,0.));
#5176=DIRECTION('center_axis',(1.,0.,0.));
#5177=DIRECTION('ref_axis',(0.,-1.,0.));
#5178=DIRECTION('',(0.,0.,-1.));
#5179=DIRECTION('',(0.,-1.,0.));
#5180=DIRECTION('center_axis',(0.,0.,-1.));
#5181=DIRECTION('ref_axis',(0.,1.,0.));
#5182=DIRECTION('center_axis',(0.,0.,1.));
#5183=DIRECTION('ref_axis',(0.,1.,0.));
#5184=DIRECTION('',(0.,0.,-1.));
#5185=DIRECTION('center_axis',(-7.90118834947295E-15,-1.,0.));
#5186=DIRECTION('ref_axis',(-1.,7.90118834947295E-15,0.));
#5187=DIRECTION('',(-1.,7.90118834947295E-15,0.));
#5188=DIRECTION('',(0.,0.,-1.));
#5189=DIRECTION('center_axis',(0.,0.,-1.));
#5190=DIRECTION('ref_axis',(0.,-1.,0.));
#5191=DIRECTION('center_axis',(0.,0.,1.));
#5192=DIRECTION('ref_axis',(0.,-1.,0.));
#5193=DIRECTION('',(0.,0.,-1.));
#5194=DIRECTION('center_axis',(1.,0.,0.));
#5195=DIRECTION('ref_axis',(0.,-1.,0.));
#5196=DIRECTION('',(0.,-1.,0.));
#5197=DIRECTION('',(0.,0.,-1.));
#5198=DIRECTION('center_axis',(0.,0.,-1.));
#5199=DIRECTION('ref_axis',(0.,1.,0.));
#5200=DIRECTION('center_axis',(0.,0.,1.));
#5201=DIRECTION('ref_axis',(0.,1.,0.));
#5202=DIRECTION('',(0.,0.,-1.));
#5203=DIRECTION('center_axis',(8.05514838952211E-17,-1.,0.));
#5204=DIRECTION('ref_axis',(-1.,-8.05514838952211E-17,0.));
#5205=DIRECTION('',(1.,8.05514838952211E-17,0.));
#5206=DIRECTION('',(0.,0.,-1.));
#5207=DIRECTION('center_axis',(0.,0.,-1.));
#5208=DIRECTION('ref_axis',(0.742933354233167,0.66936539436085,0.));
#5209=DIRECTION('center_axis',(0.,0.,1.));
#5210=DIRECTION('ref_axis',(0.742933354233167,0.66936539436085,0.));
#5211=DIRECTION('',(0.,0.,-1.));
#5212=DIRECTION('center_axis',(-0.742933354233166,-0.669365394360851,0.));
#5213=DIRECTION('ref_axis',(-0.669365394360851,0.742933354233166,0.));
#5214=DIRECTION('',(0.669365394360851,-0.742933354233166,0.));
#5215=DIRECTION('',(0.,0.,-1.));
#5216=DIRECTION('center_axis',(0.,0.,-1.));
#5217=DIRECTION('ref_axis',(-0.742933354233165,-0.669365394360852,0.));
#5218=DIRECTION('center_axis',(0.,0.,1.));
#5219=DIRECTION('ref_axis',(-0.742933354233165,-0.669365394360852,0.));
#5220=DIRECTION('',(0.,0.,-1.));
#5221=DIRECTION('center_axis',(0.,-1.,0.));
#5222=DIRECTION('ref_axis',(-1.,0.,0.));
#5223=DIRECTION('',(-1.,0.,0.));
#5224=DIRECTION('',(0.,0.,-1.));
#5225=DIRECTION('center_axis',(0.,0.,-1.));
#5226=DIRECTION('ref_axis',(-1.48029736616688E-16,-1.,0.));
#5227=DIRECTION('center_axis',(0.,0.,1.));
#5228=DIRECTION('ref_axis',(-1.48029736616688E-16,-1.,0.));
#5229=DIRECTION('',(0.,0.,-1.));
#5230=DIRECTION('center_axis',(1.,1.52116272351834E-17,0.));
#5231=DIRECTION('ref_axis',(1.52116272351834E-17,-1.,0.));
#5232=DIRECTION('',(1.52116272351834E-17,-1.,0.));
#5233=DIRECTION('center_axis',(-7.83686840911875E-16,1.,-6.12323399573677E-17));
#5234=DIRECTION('ref_axis',(1.,7.83686840911875E-16,3.2653618371328E-16));
#5235=DIRECTION('',(-7.83686840911875E-16,1.,-6.12323399573677E-17));
#5236=DIRECTION('center_axis',(0.156857492141109,1.83401512423483E-16,0.987621246814386));
#5237=DIRECTION('ref_axis',(0.987621246814386,7.64381023649769E-16,-0.156857492141109));
#5238=DIRECTION('',(-7.83686840911875E-16,1.,-6.12323399573677E-17));
#5239=DIRECTION('center_axis',(-7.83686840911875E-16,1.,-6.12323399573677E-17));
#5240=DIRECTION('ref_axis',(0.15685749214111,1.83401512423483E-16,0.987621246814386));
#5241=DIRECTION('',(-7.83686840911875E-16,1.,-6.12323399573677E-17));
#5242=DIRECTION('center_axis',(-1.,-7.83686840911875E-16,7.83686840911875E-16));
#5243=DIRECTION('ref_axis',(7.83686840911875E-16,6.12323399573683E-17,1.));
#5244=DIRECTION('center_axis',(0.,0.,-1.));
#5245=DIRECTION('ref_axis',(-0.0714285714285709,-0.997445717412067,0.));
#5246=DIRECTION('center_axis',(0.,0.,1.));
#5247=DIRECTION('ref_axis',(-0.0714285714285709,-0.997445717412067,0.));
#5248=DIRECTION('',(0.,0.,-1.));
#5249=DIRECTION('center_axis',(0.,0.,-1.));
#5250=DIRECTION('ref_axis',(-0.742933354233167,-0.669365394360849,0.));
#5251=DIRECTION('center_axis',(0.,0.,1.));
#5252=DIRECTION('ref_axis',(-0.742933354233167,-0.669365394360849,0.));
#5253=DIRECTION('',(0.,0.,-1.));
#5254=DIRECTION('center_axis',(0.742933354233167,0.669365394360849,0.));
#5255=DIRECTION('ref_axis',(0.669365394360849,-0.742933354233167,0.));
#5256=DIRECTION('',(0.669365394360849,-0.742933354233167,0.));
#5257=DIRECTION('',(0.,0.,-1.));
#5258=DIRECTION('center_axis',(0.,0.,-1.));
#5259=DIRECTION('ref_axis',(0.742933354233167,0.669365394360849,0.));
#5260=DIRECTION('center_axis',(0.,0.,1.));
#5261=DIRECTION('ref_axis',(0.742933354233167,0.669365394360849,0.));
#5262=DIRECTION('',(0.,0.,-1.));
#5263=DIRECTION('center_axis',(-1.98038310139587E-16,1.,0.));
#5264=DIRECTION('ref_axis',(1.,1.98038310139587E-16,0.));
#5265=DIRECTION('',(1.,1.98038310139587E-16,0.));
#5266=DIRECTION('',(0.,0.,-1.));
#5267=DIRECTION('center_axis',(0.,0.,-1.));
#5268=DIRECTION('ref_axis',(-1.,-8.14163551391777E-15,0.));
#5269=DIRECTION('center_axis',(0.,0.,1.));
#5270=DIRECTION('ref_axis',(-1.,-8.14163551391777E-15,0.));
#5271=DIRECTION('center_axis',(1.,3.0521152424083E-16,0.));
#5272=DIRECTION('ref_axis',(0.,0.,-1.));
#5273=DIRECTION('center_axis',(-2.33805592120562E-16,0.766044443118978,
-0.642787609686539));
#5274=DIRECTION('ref_axis',(1.09025338125282E-16,0.642787609686539,0.766044443118978));
#5275=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#5276=DIRECTION('center_axis',(-1.09025338125282E-16,-0.642787609686539,
-0.766044443118978));
#5277=DIRECTION('ref_axis',(-1.,-1.09025338125282E-16,2.33805592120562E-16));
#5278=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#5279=DIRECTION('center_axis',(-2.33805592120562E-16,0.766044443118978,
-0.642787609686539));
#5280=DIRECTION('ref_axis',(-1.09025338125282E-16,-0.642787609686539,-0.766044443118978));
#5281=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#5282=DIRECTION('center_axis',(1.,1.09025338125282E-16,-2.33805592120562E-16));
#5283=DIRECTION('ref_axis',(-1.09025338125282E-16,-0.642787609686539,-0.766044443118978));
#5284=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#5285=DIRECTION('center_axis',(-2.33805592120562E-16,0.766044443118978,
-0.642787609686539));
#5286=DIRECTION('ref_axis',(1.09025338125282E-16,0.642787609686539,0.766044443118978));
#5287=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#5288=DIRECTION('center_axis',(-1.09025338125282E-16,-0.642787609686539,
-0.766044443118978));
#5289=DIRECTION('ref_axis',(-1.,-1.09025338125282E-16,2.33805592120562E-16));
#5290=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#5291=DIRECTION('center_axis',(-2.33805592120562E-16,0.766044443118978,
-0.642787609686539));
#5292=DIRECTION('ref_axis',(1.,-2.74552507874579E-15,-3.63572630666796E-15));
#5293=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#5294=DIRECTION('center_axis',(-1.,-1.09025338125282E-16,2.33805592120562E-16));
#5295=DIRECTION('ref_axis',(1.09025338125282E-16,0.642787609686539,0.766044443118978));
#5296=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#5297=DIRECTION('center_axis',(-2.33805592120562E-16,0.766044443118978,
-0.642787609686539));
#5298=DIRECTION('ref_axis',(-1.,2.74552507874579E-15,3.63572630666796E-15));
#5299=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#5300=DIRECTION('center_axis',(1.00119768649989E-15,-0.642787609686539,
-0.766044443118978));
#5301=DIRECTION('ref_axis',(-1.,-8.22662942343055E-16,-6.16674586516295E-16));
#5302=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#5303=DIRECTION('center_axis',(-2.33805592120562E-16,0.766044443118978,
-0.642787609686539));
#5304=DIRECTION('ref_axis',(-1.09025338125282E-16,-0.642787609686539,-0.766044443118978));
#5305=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#5306=DIRECTION('center_axis',(1.,1.09025338125282E-16,-2.33805592120562E-16));
#5307=DIRECTION('ref_axis',(-1.09025338125282E-16,-0.642787609686539,-0.766044443118978));
#5308=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#5309=DIRECTION('center_axis',(-2.33805592120562E-16,0.766044443118978,
-0.642787609686539));
#5310=DIRECTION('ref_axis',(1.,-2.74552507874579E-15,-3.63572630666796E-15));
#5311=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#5312=DIRECTION('center_axis',(1.09025338125282E-16,0.642787609686539,0.766044443118978));
#5313=DIRECTION('ref_axis',(1.,1.09025338125282E-16,-2.33805592120562E-16));
#5314=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#5315=DIRECTION('center_axis',(-2.33805592120562E-16,0.766044443118978,
-0.642787609686539));
#5316=DIRECTION('ref_axis',(1.09025338125282E-16,0.642787609686539,0.766044443118978));
#5317=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#5318=DIRECTION('center_axis',(-1.,1.13091763293588E-14,1.38414884503099E-14));
#5319=DIRECTION('ref_axis',(1.78725937321275E-14,0.642787609686539,0.766044443118978));
#5320=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#5321=DIRECTION('center_axis',(-2.33805592120562E-16,0.766044443118978,
-0.642787609686539));
#5322=DIRECTION('ref_axis',(-1.09025338125282E-16,-0.642787609686539,-0.766044443118978));
#5323=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#5324=DIRECTION('center_axis',(1.09025338125282E-16,0.642787609686539,0.766044443118978));
#5325=DIRECTION('ref_axis',(1.,1.09025338125282E-16,-2.33805592120562E-16));
#5326=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#5327=DIRECTION('center_axis',(-2.33805592120562E-16,0.766044443118978,
-0.642787609686539));
#5328=DIRECTION('ref_axis',(-1.,-5.81812617186742E-15,-6.57003583697423E-15));
#5329=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#5330=DIRECTION('center_axis',(1.,1.15272270056092E-14,1.33738772660687E-14));
#5331=DIRECTION('ref_axis',(1.76545430558768E-14,-0.642787609686539,-0.766044443118978));
#5332=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#5333=DIRECTION('center_axis',(-2.33805592120562E-16,0.766044443118978,
-0.642787609686539));
#5334=DIRECTION('ref_axis',(1.,1.09025338125282E-16,-2.33805592120562E-16));
#5335=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#5336=DIRECTION('center_axis',(1.09025338125282E-16,0.642787609686539,0.766044443118978));
#5337=DIRECTION('ref_axis',(1.,1.09025338125282E-16,-2.33805592120562E-16));
#5338=DIRECTION('',(-2.33805592120562E-16,0.766044443118978,-0.642787609686539));
#5339=DIRECTION('center_axis',(-2.33805592120562E-16,0.766044443118978,
-0.642787609686539));
#5340=DIRECTION('ref_axis',(1.09025338125282E-16,0.642787609686539,0.766044443118978));
#5341=DIRECTION('center_axis',(-1.,0.,0.));
#5342=DIRECTION('ref_axis',(0.,1.,0.));
#5343=DIRECTION('',(0.,0.,-1.));
#5344=DIRECTION('',(0.,-1.,0.));
#5345=DIRECTION('center_axis',(0.,0.,-1.));
#5346=DIRECTION('ref_axis',(5.92118946466746E-15,-1.,0.));
#5347=DIRECTION('center_axis',(0.,0.,1.));
#5348=DIRECTION('ref_axis',(5.92118946466746E-15,-1.,0.));
#5349=DIRECTION('',(0.,0.,-1.));
#5350=DIRECTION('center_axis',(0.,1.,0.));
#5351=DIRECTION('ref_axis',(1.,0.,0.));
#5352=DIRECTION('',(-1.,0.,0.));
#5353=DIRECTION('',(0.,0.,-1.));
#5354=DIRECTION('center_axis',(0.,0.,-1.));
#5355=DIRECTION('ref_axis',(-1.,-1.11022302462514E-14,0.));
#5356=DIRECTION('center_axis',(0.,0.,1.));
#5357=DIRECTION('ref_axis',(-1.,-1.11022302462514E-14,0.));
#5358=DIRECTION('center_axis',(1.,1.5419764230905E-16,0.));
#5359=DIRECTION('ref_axis',(0.,0.,-1.));
#5360=DIRECTION('center_axis',(5.50813083899156E-17,0.642787609686539,0.766044443118978));
#5361=DIRECTION('ref_axis',(1.,5.50813083899156E-17,-1.18122247032895E-16));
#5362=DIRECTION('center_axis',(-1.,0.,0.));
#5363=DIRECTION('ref_axis',(0.,1.,0.));
#5364=DIRECTION('',(0.,0.,-1.));
#5365=DIRECTION('',(0.,1.,0.));
#5366=DIRECTION('center_axis',(0.,0.,-1.));
#5367=DIRECTION('ref_axis',(0.,-1.,0.));
#5368=DIRECTION('center_axis',(0.,0.,1.));
#5369=DIRECTION('ref_axis',(0.,-1.,0.));
#5370=DIRECTION('center_axis',(0.,1.,0.));
#5371=DIRECTION('ref_axis',(0.,0.,1.));
#5372=DIRECTION('center_axis',(-1.,0.,8.82055185704388E-15));
#5373=DIRECTION('ref_axis',(8.82055185704388E-15,-3.70074341541722E-16,
1.));
#5374=DIRECTION('',(-1.,0.,8.82055185704388E-15));
#5375=DIRECTION('center_axis',(-8.82055185704388E-15,0.,-1.));
#5376=DIRECTION('ref_axis',(0.,-1.,0.));
#5377=DIRECTION('',(-1.,0.,8.82055185704388E-15));
#5378=DIRECTION('center_axis',(-1.,0.,8.82055185704388E-15));
#5379=DIRECTION('ref_axis',(-8.82055185704388E-15,-7.40148683083445E-16,
-1.));
#5380=DIRECTION('',(-1.,0.,8.82055185704388E-15));
#5381=DIRECTION('center_axis',(0.,1.,0.));
#5382=DIRECTION('ref_axis',(-8.82055185704388E-15,0.,-1.));
#5383=DIRECTION('',(-1.,0.,8.82055185704388E-15));
#5384=DIRECTION('center_axis',(-1.,0.,8.82055185704388E-15));
#5385=DIRECTION('ref_axis',(0.,1.,0.));
#5386=DIRECTION('',(-1.,0.,8.82055185704388E-15));
#5387=DIRECTION('center_axis',(8.82055185704388E-15,-6.45947941600091E-15,
1.));
#5388=DIRECTION('ref_axis',(5.69761731583435E-29,1.,6.45947941600091E-15));
#5389=DIRECTION('',(-1.,0.,8.82055185704388E-15));
#5390=DIRECTION('center_axis',(-1.,0.,8.82055185704388E-15));
#5391=DIRECTION('ref_axis',(8.82055185704388E-15,2.44804176929931E-13,1.));
#5392=DIRECTION('',(-1.,0.,8.82055185704388E-15));
#5393=DIRECTION('center_axis',(2.6055789008517E-29,-1.,2.95398626194907E-15));
#5394=DIRECTION('ref_axis',(8.82055185704388E-15,2.95398626194907E-15,1.));
#5395=DIRECTION('',(-1.,0.,8.82055185704388E-15));
#5396=DIRECTION('center_axis',(-1.,0.,8.82055185704388E-15));
#5397=DIRECTION('ref_axis',(0.,-1.,0.));
#5398=DIRECTION('center_axis',(0.,1.,0.));
#5399=DIRECTION('ref_axis',(-1.,0.,1.96011575733601E-14));
#5400=DIRECTION('',(0.294839026647684,0.,-0.95554693676734));
#5401=DIRECTION('center_axis',(1.,1.7669331956899E-16,-1.4210854715202E-14));
#5402=DIRECTION('ref_axis',(-1.4210854715202E-14,0.,-1.));
#5403=DIRECTION('center_axis',(-9.26991626957946E-15,0.766044443118978,
-0.642787609686539));
#5404=DIRECTION('ref_axis',(1.,1.01463384865935E-15,-1.32122360792783E-14));
#5405=DIRECTION('',(-9.26991626957946E-15,0.766044443118978,-0.642787609686539));
#5406=DIRECTION('center_axis',(1.09492633295873E-14,0.642787609686539,0.766044443118978));
#5407=DIRECTION('ref_axis',(1.,6.31170430356592E-17,-1.43462096507941E-14));
#5408=DIRECTION('',(-9.26991626957946E-15,0.766044443118978,-0.642787609686539));
#5409=DIRECTION('center_axis',(-9.26991626957946E-15,0.766044443118978,
-0.642787609686539));
#5410=DIRECTION('ref_axis',(2.95166357633724E-13,0.64278760968654,0.766044443118978));
#5411=DIRECTION('',(-9.26991626957946E-15,0.766044443118978,-0.642787609686539));
#5412=DIRECTION('center_axis',(-1.,-6.31170430356592E-17,1.43462096507941E-14));
#5413=DIRECTION('ref_axis',(1.09492633295873E-14,0.642787609686539,0.766044443118978));
#5414=DIRECTION('',(-9.26991626957946E-15,0.766044443118978,-0.642787609686539));
#5415=DIRECTION('center_axis',(-9.26991626957946E-15,0.766044443118978,
-0.642787609686539));
#5416=DIRECTION('ref_axis',(-1.09492633295873E-14,-0.642787609686539,-0.766044443118978));
#5417=DIRECTION('',(-9.26991626957946E-15,0.766044443118978,-0.642787609686539));
#5418=DIRECTION('center_axis',(1.09492633295873E-14,0.642787609686539,0.766044443118978));
#5419=DIRECTION('ref_axis',(1.,6.31170430356592E-17,-1.43462096507941E-14));
#5420=DIRECTION('',(-9.26991626957946E-15,0.766044443118978,-0.642787609686539));
#5421=DIRECTION('center_axis',(-9.26991626957946E-15,0.766044443118978,
-0.642787609686539));
#5422=DIRECTION('ref_axis',(-1.,-7.99905287154071E-14,-8.09075703565109E-14));
#5423=DIRECTION('',(-9.26991626957946E-15,0.766044443118978,-0.642787609686539));
#5424=DIRECTION('center_axis',(1.,-1.82628109636711E-13,-2.32069135381825E-13));
#5425=DIRECTION('ref_axis',(-2.95166357633626E-13,-0.642787609686539,-0.766044443118978));
#5426=DIRECTION('',(-9.26991626957946E-15,0.766044443118978,-0.642787609686539));
#5427=DIRECTION('center_axis',(-9.26991626957946E-15,0.766044443118978,
-0.642787609686539));
#5428=DIRECTION('ref_axis',(1.,7.99905287154298E-14,8.0907570356538E-14));
#5429=DIRECTION('',(-9.26991626957946E-15,0.766044443118978,-0.642787609686539));
#5430=DIRECTION('center_axis',(5.53581843145898E-14,0.642787609686539,0.766044443118978));
#5431=DIRECTION('ref_axis',(1.,-2.84823871256725E-14,-4.8365416796265E-14));
#5432=DIRECTION('',(-9.26991626957946E-15,0.766044443118978,-0.642787609686539));
#5433=DIRECTION('center_axis',(-9.26991626957946E-15,0.766044443118978,
-0.642787609686539));
#5434=DIRECTION('ref_axis',(1.09492633295873E-14,0.64278760968654,0.766044443118978));
#5435=DIRECTION('',(-9.26991626957946E-15,0.766044443118978,-0.642787609686539));
#5436=DIRECTION('center_axis',(-1.,-6.31170430356592E-17,1.43462096507941E-14));
#5437=DIRECTION('ref_axis',(1.09492633295873E-14,0.642787609686539,0.766044443118978));
#5438=DIRECTION('',(-9.26991626957946E-15,0.766044443118978,-0.642787609686539));
#5439=DIRECTION('center_axis',(-9.26991626957946E-15,0.766044443118978,
-0.642787609686539));
#5440=DIRECTION('ref_axis',(-1.,7.70097442124873E-14,1.06198068943579E-13));
#5441=DIRECTION('',(-9.26991626957946E-15,0.766044443118978,-0.642787609686539));
#5442=DIRECTION('center_axis',(-1.09492633295873E-14,-0.642787609686539,
-0.766044443118978));
#5443=DIRECTION('ref_axis',(-1.,-6.31170430356592E-17,1.43462096507941E-14));
#5444=DIRECTION('',(-9.26991626957946E-15,0.766044443118978,-0.642787609686539));
#5445=DIRECTION('center_axis',(-9.26991626957946E-15,0.766044443118978,
-0.642787609686539));
#5446=DIRECTION('ref_axis',(-1.09492633295873E-14,-0.64278760968654,-0.766044443118978));
#5447=DIRECTION('',(-9.26991626957946E-15,0.766044443118978,-0.642787609686539));
#5448=DIRECTION('center_axis',(1.,1.82754343722815E-13,2.03376716080276E-13));
#5449=DIRECTION('ref_axis',(2.73267830974502E-13,-0.642787609686539,-0.766044443118978));
#5450=DIRECTION('',(-9.26991626957946E-15,0.766044443118978,-0.642787609686539));
#5451=DIRECTION('center_axis',(-9.26991626957946E-15,0.766044443118978,
-0.642787609686539));
#5452=DIRECTION('ref_axis',(1.09492633295873E-14,0.64278760968654,0.766044443118978));
#5453=DIRECTION('',(-9.26991626957946E-15,0.766044443118978,-0.642787609686539));
#5454=DIRECTION('center_axis',(-1.09492633295873E-14,-0.642787609686539,
-0.766044443118978));
#5455=DIRECTION('ref_axis',(-1.,-6.31170430356592E-17,1.43462096507941E-14));
#5456=DIRECTION('',(-9.26991626957946E-15,0.766044443118978,-0.642787609686539));
#5457=DIRECTION('center_axis',(-9.26991626957946E-15,0.766044443118978,
-0.642787609686539));
#5458=DIRECTION('ref_axis',(1.,6.31170430356592E-17,-1.43462096507941E-14));
#5459=DIRECTION('',(-9.26991626957946E-15,0.766044443118978,-0.642787609686539));
#5460=DIRECTION('center_axis',(-1.,-6.31170430356592E-17,1.43462096507941E-14));
#5461=DIRECTION('ref_axis',(1.09492633295873E-14,0.642787609686539,0.766044443118978));
#5462=DIRECTION('',(-9.26991626957946E-15,0.766044443118978,-0.642787609686539));
#5463=DIRECTION('center_axis',(-9.26991626957946E-15,0.766044443118978,
-0.642787609686539));
#5464=DIRECTION('ref_axis',(-1.,1.42096350413204E-14,3.13558132235319E-14));
#5465=DIRECTION('',(-9.26991626957946E-15,0.766044443118978,-0.642787609686539));
#5466=DIRECTION('center_axis',(-1.68704527942546E-14,-0.642787609686539,
-0.766044443118978));
#5467=DIRECTION('ref_axis',(-1.,3.74295017945894E-15,1.88821039368571E-14));
#5468=DIRECTION('center_axis',(-1.,0.,1.4210854715202E-14));
#5469=DIRECTION('ref_axis',(0.,1.,0.));
#5470=DIRECTION('',(-1.4210854715202E-14,0.,-1.));
#5471=DIRECTION('center_axis',(-1.4210854715202E-14,0.,-1.));
#5472=DIRECTION('ref_axis',(-2.84217094304137E-13,-1.,4.03896783473296E-27));
#5473=DIRECTION('center_axis',(0.,1.,0.));
#5474=DIRECTION('ref_axis',(1.4210854715202E-14,0.,1.));
#5475=DIRECTION('center_axis',(-1.,0.,8.82055185704388E-15));
#5476=DIRECTION('ref_axis',(1.25347580948356E-27,1.,1.42108547152021E-13));
#5477=DIRECTION('',(-1.,0.,8.82055185704388E-15));
#5478=DIRECTION('center_axis',(8.79008353851607E-15,0.0830454798537492,
0.996545758244879));
#5479=DIRECTION('ref_axis',(-7.32506961543088E-16,0.996545758244879,-0.0830454798537492));
#5480=DIRECTION('',(-1.,0.,8.82055185704388E-15));
#5481=DIRECTION('center_axis',(-1.,0.,8.82055185704388E-15));
#5482=DIRECTION('ref_axis',(8.79008353851611E-15,0.0830454798537047,0.996545758244883));
#5483=DIRECTION('center_axis',(3.80691479877441E-16,-1.,-5.40995131185356E-30));
#5484=DIRECTION('ref_axis',(-1.,-3.80691479877441E-16,1.4210854715202E-14));
#5485=DIRECTION('',(-1.4210854715202E-14,0.,-1.));
#5486=DIRECTION('center_axis',(-1.4210854715202E-14,0.,-1.));
#5487=DIRECTION('ref_axis',(4.26325641456067E-13,-1.,-6.05845175209747E-27));
#5488=DIRECTION('',(-1.4210854715202E-14,0.,-1.));
#5489=DIRECTION('center_axis',(0.81973611561291,-0.572741390821248,-1.16491508437791E-14));
#5490=DIRECTION('ref_axis',(-0.572741390821248,-0.81973611561291,8.13914469434348E-15));
#5491=DIRECTION('',(-1.4210854715202E-14,0.,-1.));
#5492=DIRECTION('center_axis',(-1.4210854715202E-14,0.,-1.));
#5493=DIRECTION('ref_axis',(7.10542735760215E-14,1.,-1.00974195868306E-27));
#5494=DIRECTION('center_axis',(0.,-1.,0.));
#5495=DIRECTION('ref_axis',(-1.,0.,0.));
#5496=DIRECTION('',(0.,0.,-1.));
#5497=DIRECTION('',(1.,0.,0.));
#5498=DIRECTION('center_axis',(0.,0.,-1.));
#5499=DIRECTION('ref_axis',(1.,0.,0.));
#5500=DIRECTION('center_axis',(0.,0.,1.));
#5501=DIRECTION('ref_axis',(1.,0.,0.));
#5502=DIRECTION('center_axis',(-1.,0.,0.));
#5503=DIRECTION('ref_axis',(0.,0.,1.));
#5504=DIRECTION('center_axis',(0.,-1.,-6.12323399573677E-17));
#5505=DIRECTION('ref_axis',(0.,-6.12323399573677E-17,1.));
#5506=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5507=DIRECTION('center_axis',(-2.53765262771461E-15,6.12323399573677E-17,
-1.));
#5508=DIRECTION('ref_axis',(1.,1.55386408393928E-31,-2.53765262771461E-15));
#5509=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5510=DIRECTION('center_axis',(0.,-1.,-6.12323399573677E-17));
#5511=DIRECTION('ref_axis',(0.,6.12323399573677E-17,-1.));
#5512=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5513=DIRECTION('center_axis',(-1.,0.,0.));
#5514=DIRECTION('ref_axis',(0.,6.12323399573677E-17,-1.));
#5515=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5516=DIRECTION('center_axis',(0.,-1.,-6.12323399573677E-17));
#5517=DIRECTION('ref_axis',(-1.,0.,0.));
#5518=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5519=DIRECTION('center_axis',(0.0461047666084019,-6.1167226090115E-17,
0.998936609848685));
#5520=DIRECTION('ref_axis',(-0.998936609848685,-2.82310274262076E-18,0.0461047666084019));
#5521=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5522=DIRECTION('center_axis',(0.,-1.,-6.12323399573677E-17));
#5523=DIRECTION('ref_axis',(0.0461047666085012,-6.11672260901148E-17,0.998936609848681));
#5524=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5525=DIRECTION('center_axis',(1.,0.,0.));
#5526=DIRECTION('ref_axis',(0.,-6.12323399573677E-17,1.));
#5527=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5528=DIRECTION('center_axis',(0.,-1.,-6.12323399573677E-17));
#5529=DIRECTION('ref_axis',(-0.0870141383636912,6.1000090390202E-17,-0.996207076728942));
#5530=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5531=DIRECTION('center_axis',(0.0870141383636205,-6.10000903902024E-17,
0.996207076728948));
#5532=DIRECTION('ref_axis',(-0.996207076728948,-5.32807930137864E-18,0.0870141383636205));
#5533=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5534=DIRECTION('center_axis',(0.,-1.,-6.12323399573677E-17));
#5535=DIRECTION('ref_axis',(1.,1.08770485875748E-30,-1.77635683940019E-14));
#5536=DIRECTION('',(0.,-1.,-6.12323399573677E-17));
#5537=DIRECTION('center_axis',(-1.,0.,0.));
#5538=DIRECTION('ref_axis',(0.,6.12323399573677E-17,-1.));
#5539=DIRECTION('center_axis',(0.,0.,1.));
#5540=DIRECTION('ref_axis',(1.,0.,0.));
#5541=DIRECTION('center_axis',(1.,7.83686840911875E-16,0.));
#5542=DIRECTION('ref_axis',(0.,0.,-1.));
#5543=CARTESIAN_POINT('',(0.,0.,0.));
#5544=CARTESIAN_POINT('Origin',(-331.107079934123,-33.7263908158462,13.9136801600068));
#5545=CARTESIAN_POINT('',(-331.107079934123,-16.,3.00000000000004));
#5546=CARTESIAN_POINT('',(-331.107079934123,2.31049219535662E-14,3.00000000000004));
#5547=CARTESIAN_POINT('',(-331.107079934123,-16.8631954079231,3.00000000000011));
#5548=CARTESIAN_POINT('',(-331.107079934123,2.99760216648792E-14,9.70707993412312));
#5549=CARTESIAN_POINT('',(-331.107079934123,6.10622663543836E-15,-13.5929200658769));
#5550=CARTESIAN_POINT('',(-331.107079934123,3.,12.7070799341231));
#5551=CARTESIAN_POINT('Origin',(-331.107079934123,3.,9.70707993412312));
#5552=CARTESIAN_POINT('',(-331.107079934123,11.,12.7070799341231));
#5553=CARTESIAN_POINT('',(-331.107079934123,3.,12.7070799341231));
#5554=CARTESIAN_POINT('',(-331.107079934123,14.,15.7070799341231));
#5555=CARTESIAN_POINT('Origin',(-331.107079934123,11.,15.7070799341231));
#5556=CARTESIAN_POINT('',(-331.107079934123,14.,39.7070799341232));
#5557=CARTESIAN_POINT('',(-331.107079934123,14.,39.7070799341232));
#5558=CARTESIAN_POINT('',(-331.107079934123,13.5,40.2070799341232));
#5559=CARTESIAN_POINT('Origin',(-331.107079934123,13.5,39.7070799341232));
#5560=CARTESIAN_POINT('',(-331.107079934123,2.49999999999999,40.2070799341231));
#5561=CARTESIAN_POINT('',(-331.107079934123,13.5,40.2070799341232));
#5562=CARTESIAN_POINT('',(-331.107079934123,2.00000000000004,39.7070799341231));
#5563=CARTESIAN_POINT('Origin',(-331.107079934123,2.49999999999987,39.7070799341233));
#5564=CARTESIAN_POINT('',(-331.107079934123,2.,25.7070799341231));
#5565=CARTESIAN_POINT('',(-331.107079934123,2.00000000000004,39.7070799341231));
#5566=CARTESIAN_POINT('',(-331.107079934123,0.,25.7070799341231));
#5567=CARTESIAN_POINT('Origin',(-331.107079934123,1.,25.7070799341231));
#5568=CARTESIAN_POINT('',(-331.107079934123,0.,26.5141598682461));
#5569=CARTESIAN_POINT('',(-331.107079934123,0.,25.7070799341231));
#5570=CARTESIAN_POINT('',(-331.107079934123,-16.,26.5141598682461));
#5571=CARTESIAN_POINT('',(-331.107079934123,-16.8631954079231,26.5141598682462));
#5572=CARTESIAN_POINT('',(-331.107079934121,-16.,271.6858651175));
#5573=CARTESIAN_POINT('Origin',(-334.107079934123,-16.,3.00000000000005));
#5574=CARTESIAN_POINT('',(-334.107079934123,-16.,3.00000000000004));
#5575=CARTESIAN_POINT('',(-331.107079934123,-16.,3.00000000000011));
#5576=CARTESIAN_POINT('',(-328.107079934123,-16.,-3.));
#5577=CARTESIAN_POINT('Origin',(-328.107079934123,-16.,3.));
#5578=CARTESIAN_POINT('',(-328.107079934123,-16.,0.));
#5579=CARTESIAN_POINT('',(-328.107079934123,-16.,0.));
#5580=CARTESIAN_POINT('Origin',(-328.107079934123,-16.,3.));
#5581=CARTESIAN_POINT('Origin',(-328.107079934123,-7.99999999999999,3.));
#5582=CARTESIAN_POINT('',(-334.107079934123,2.31049219535662E-14,3.00000000000004));
#5583=CARTESIAN_POINT('',(-334.107079934123,-16.8631954079231,3.00000000000011));
#5584=CARTESIAN_POINT('',(-328.107079934123,1.61531359858615E-14,-3.));
#5585=CARTESIAN_POINT('Origin',(-328.107079934123,2.33146835171283E-14,
3.));
#5586=CARTESIAN_POINT('',(-328.107079934123,-16.8631954079231,-3.));
#5587=CARTESIAN_POINT('Origin',(-331.107079934123,2.99760216648792E-14,
9.70707993412312));
#5588=CARTESIAN_POINT('',(-331.107079934123,2.31049219535662E-14,3.00000000000004));
#5589=CARTESIAN_POINT('',(-334.107079934123,2.66453525910038E-14,9.70707993412312));
#5590=CARTESIAN_POINT('',(-334.107079934123,6.10622663543836E-15,-13.5929200658769));
#5591=CARTESIAN_POINT('',(-331.107079934123,2.99760216648792E-14,9.70707993412312));
#5592=CARTESIAN_POINT('Origin',(-331.107079934123,-16.,-10.892920065877));
#5593=CARTESIAN_POINT('',(-334.107079934123,-16.,26.5141598682461));
#5594=CARTESIAN_POINT('',(-331.107079934123,-16.,26.5141598682462));
#5595=CARTESIAN_POINT('',(-334.107079934121,-16.,271.6858651175));
#5596=CARTESIAN_POINT('Origin',(-345.806600225884,-33.7263908158462,-3.));
#5597=CARTESIAN_POINT('',(-318.3,8.88178419700125E-15,-3.));
#5598=CARTESIAN_POINT('',(-318.3,6.10622663543836E-15,-3.));
#5599=CARTESIAN_POINT('',(-315.3,3.,-3.));
#5600=CARTESIAN_POINT('Origin',(-318.3,3.00000000000001,-3.));
#5601=CARTESIAN_POINT('',(-315.3,4.51504440784608,-3.));
#5602=CARTESIAN_POINT('',(-315.3,3.,-3.));
#5603=CARTESIAN_POINT('',(-243.3,4.51504440784609,-3.));
#5604=CARTESIAN_POINT('',(-172.903300112942,4.5150444078461,-3.));
#5605=CARTESIAN_POINT('',(-243.3,3.,-3.));
#5606=CARTESIAN_POINT('',(-243.3,23.9,-3.));
#5607=CARTESIAN_POINT('',(-240.3,-8.88178419700125E-15,-3.));
#5608=CARTESIAN_POINT('Origin',(-240.3,3.00000000000003,-3.));
#5609=CARTESIAN_POINT('',(-96.3,0.,-3.));
#5610=CARTESIAN_POINT('',(-96.3,0.,-3.));
#5611=CARTESIAN_POINT('',(-93.3,3.,-3.));
#5612=CARTESIAN_POINT('Origin',(-96.3,3.00000000000002,-3.));
#5613=CARTESIAN_POINT('',(-93.3,4.51504440784612,-3.));
#5614=CARTESIAN_POINT('',(-93.3,22.9,-3.));
#5615=CARTESIAN_POINT('',(-78.3,4.51504440784612,-3.));
#5616=CARTESIAN_POINT('',(-172.903300112942,4.51504440784609,-3.));
#5617=CARTESIAN_POINT('',(-78.3,2.99999999999999,-3.));
#5618=CARTESIAN_POINT('',(-78.3,13.4,-3.));
#5619=CARTESIAN_POINT('',(-75.2999999999999,0.,-3.));
#5620=CARTESIAN_POINT('Origin',(-75.2999999999999,3.00000000000001,-3.));
#5621=CARTESIAN_POINT('',(-52.8755908269946,0.,-3.));
#5622=CARTESIAN_POINT('',(-75.2999999999999,-4.44089209850063E-15,-3.));
#5623=CARTESIAN_POINT('',(-41.7315905134971,-4.95951908458727,-3.));
#5624=CARTESIAN_POINT('Origin',(-52.8755908269946,-15.,-3.));
#5625=CARTESIAN_POINT('',(-28.1440003134975,-20.0404809154127,-3.));
#5626=CARTESIAN_POINT('',(-41.7315905134971,-4.95951908458727,-3.));
#5627=CARTESIAN_POINT('',(-18.0714285714286,-24.961685761181,-3.));
#5628=CARTESIAN_POINT('Origin',(-17.,-10.,-3.));
#5629=CARTESIAN_POINT('',(-17.,-23.9642400437689,-3.));
#5630=CARTESIAN_POINT('Origin',(-18.,-23.9642400437689,-3.));
#5631=CARTESIAN_POINT('',(-17.,-22.092920065877,-3.));
#5632=CARTESIAN_POINT('',(-17.,-7.17135743674268,-3.));
#5633=CARTESIAN_POINT('',(-8.68139725592746E-17,-22.092920065877,-3.));
#5634=CARTESIAN_POINT('',(-172.903300112942,-22.0929200658771,-3.));
#5635=CARTESIAN_POINT('',(0.,-27.8,-3.));
#5636=CARTESIAN_POINT('',(-2.77555756156289E-16,-9.55371037791915,-3.));
#5637=CARTESIAN_POINT('',(-15.,-42.8,-3.));
#5638=CARTESIAN_POINT('Origin',(-15.,-27.8,-3.));
#5639=CARTESIAN_POINT('',(-21.9782425395436,-42.8,-3.));
#5640=CARTESIAN_POINT('',(-15.,-42.8,-3.));
#5641=CARTESIAN_POINT('',(-33.122242853041,-37.8404809154128,-3.));
#5642=CARTESIAN_POINT('Origin',(-21.9782425395435,-27.7999999999999,-3.));
#5643=CARTESIAN_POINT('',(-48.3315905134971,-20.9595190845872,-3.));
#5644=CARTESIAN_POINT('',(-48.3315905134971,-20.9595190845872,-3.));
#5645=CARTESIAN_POINT('',(-59.4755908269945,-16.,-3.));
#5646=CARTESIAN_POINT('Origin',(-59.4755908269946,-31.,-3.));
#5647=CARTESIAN_POINT('',(-280.,-16.,-3.));
#5648=CARTESIAN_POINT('',(-280.,-16.,-3.));
#5649=CARTESIAN_POINT('',(-281.,-17.,-3.));
#5650=CARTESIAN_POINT('Origin',(-280.,-17.,-3.));
#5651=CARTESIAN_POINT('',(-281.,-37.4,-3.));
#5652=CARTESIAN_POINT('',(-281.,-17.,-3.));
#5653=CARTESIAN_POINT('',(-282.,-38.4,-3.));
#5654=CARTESIAN_POINT('Origin',(-282.,-37.4,-3.));
#5655=CARTESIAN_POINT('',(-303.920094064468,-38.3999999999998,-3.));
#5656=CARTESIAN_POINT('',(-282.,-38.4,-3.));
#5657=CARTESIAN_POINT('',(-304.920094064468,-39.3999999999998,-3.));
#5658=CARTESIAN_POINT('Origin',(-303.920094064468,-39.3999999999998,-3.));
#5659=CARTESIAN_POINT('',(-304.920094064468,-40.3932528566243,-3.));
#5660=CARTESIAN_POINT('',(-304.920094064468,-39.3999999999998,-3.));
#5661=CARTESIAN_POINT('',(-320.,-40.3932528566243,-3.));
#5662=CARTESIAN_POINT('',(-172.903300112942,-40.3932528566243,-3.));
#5663=CARTESIAN_POINT('',(-320.,-17.,-3.));
#5664=CARTESIAN_POINT('',(-320.,-17.,-3.));
#5665=CARTESIAN_POINT('',(-321.,-16.,-3.));
#5666=CARTESIAN_POINT('Origin',(-321.,-17.,-3.));
#5667=CARTESIAN_POINT('',(-603.578785183377,-16.,-3.));
#5668=CARTESIAN_POINT('Origin',(-341.6,2.99760216648792E-14,0.));
#5669=CARTESIAN_POINT('',(-328.107079934123,1.61531359858615E-14,0.));
#5670=CARTESIAN_POINT('',(-328.107079934123,1.61531359858615E-14,0.));
#5671=CARTESIAN_POINT('',(-318.3,6.10622663543836E-15,0.));
#5672=CARTESIAN_POINT('',(-318.3,6.10622663543836E-15,0.));
#5673=CARTESIAN_POINT('',(-318.3,6.10622663543836E-15,0.));
#5674=CARTESIAN_POINT('Origin',(-328.107079934123,-7.99999999999999,3.));
#5675=CARTESIAN_POINT('',(-328.107079934123,-16.8631954079231,0.));
#5676=CARTESIAN_POINT('Origin',(-328.107079934123,1.66533453693773E-14,
3.));
#5677=CARTESIAN_POINT('Origin',(-317.72076009413,-33.7263908158462,32.5141598682458));
#5678=CARTESIAN_POINT('',(-337.107079934123,-16.,32.5141598682462));
#5679=CARTESIAN_POINT('',(-337.107079934123,0.,32.5141598682462));
#5680=CARTESIAN_POINT('',(-337.107079934123,-16.8631954079231,32.5141598682462));
#5681=CARTESIAN_POINT('',(-536.521239802369,0.,32.514159868249));
#5682=CARTESIAN_POINT('',(-329.514159868246,0.,32.514159868246));
#5683=CARTESIAN_POINT('',(-536.521239802369,-16.,32.514159868249));
#5684=CARTESIAN_POINT('',(-536.521239802369,-16.8631954079231,32.514159868249));
#5685=CARTESIAN_POINT('',(-575.492945051622,-16.,32.5141598682495));
#5686=CARTESIAN_POINT('Origin',(-337.107079934123,-16.,29.5141598682461));
#5687=CARTESIAN_POINT('',(-337.107079934123,-16.,29.5141598682461));
#5688=CARTESIAN_POINT('',(-337.107079934123,-16.,32.5141598682462));
#5689=CARTESIAN_POINT('Origin',(-337.107079934123,-16.,26.5141598682462));
#5690=CARTESIAN_POINT('Origin',(-337.107079934123,-16.,26.5141598682462));
#5691=CARTESIAN_POINT('Origin',(-337.107079934123,-8.,26.5141598682462));
#5692=CARTESIAN_POINT('',(-337.107079934123,0.,29.5141598682461));
#5693=CARTESIAN_POINT('',(-337.107079934123,-16.8631954079231,29.5141598682461));
#5694=CARTESIAN_POINT('',(-334.107079934123,0.,26.5141598682461));
#5695=CARTESIAN_POINT('Origin',(-337.107079934123,0.,26.5141598682462));
#5696=CARTESIAN_POINT('',(-334.107079934123,-16.8631954079231,26.5141598682462));
#5697=CARTESIAN_POINT('Origin',(-572.314159868246,0.,32.5141598682495));
#5698=CARTESIAN_POINT('',(-337.107079934123,0.,32.5141598682462));
#5699=CARTESIAN_POINT('',(-536.521239802369,0.,29.5141598682489));
#5700=CARTESIAN_POINT('',(-329.514159868246,0.,29.5141598682461));
#5701=CARTESIAN_POINT('',(-536.521239802369,0.,32.514159868249));
#5702=CARTESIAN_POINT('Origin',(-292.914159868246,-16.,32.5141598682455));
#5703=CARTESIAN_POINT('',(-536.521239802369,-16.,29.5141598682489));
#5704=CARTESIAN_POINT('',(-536.521239802369,-16.,32.514159868249));
#5705=CARTESIAN_POINT('',(-575.492945051622,-16.,29.5141598682495));
#5706=CARTESIAN_POINT('Origin',(-334.107079934123,-33.7263908158462,13.9136801600068));
#5707=CARTESIAN_POINT('',(-334.107079934123,0.,25.7070799341231));
#5708=CARTESIAN_POINT('',(-334.107079934123,0.,25.7070799341231));
#5709=CARTESIAN_POINT('',(-334.107079934123,2.,25.7070799341231));
#5710=CARTESIAN_POINT('Origin',(-334.107079934123,1.,25.7070799341231));
#5711=CARTESIAN_POINT('',(-334.107079934123,2.00000000000004,39.7070799341231));
#5712=CARTESIAN_POINT('',(-334.107079934123,2.00000000000004,39.7070799341231));
#5713=CARTESIAN_POINT('',(-334.107079934123,2.49999999999999,40.2070799341231));
#5714=CARTESIAN_POINT('Origin',(-334.107079934123,2.49999999999987,39.7070799341233));
#5715=CARTESIAN_POINT('',(-334.107079934123,13.5,40.2070799341232));
#5716=CARTESIAN_POINT('',(-334.107079934123,13.5,40.2070799341232));
#5717=CARTESIAN_POINT('',(-334.107079934123,14.,39.7070799341232));
#5718=CARTESIAN_POINT('Origin',(-334.107079934123,13.5,39.7070799341232));
#5719=CARTESIAN_POINT('',(-334.107079934123,14.,15.7070799341231));
#5720=CARTESIAN_POINT('',(-334.107079934123,14.,39.7070799341232));
#5721=CARTESIAN_POINT('',(-334.107079934123,11.,12.7070799341231));
#5722=CARTESIAN_POINT('Origin',(-334.107079934123,11.,15.7070799341231));
#5723=CARTESIAN_POINT('',(-334.107079934123,3.,12.7070799341231));
#5724=CARTESIAN_POINT('',(-334.107079934123,3.,12.7070799341231));
#5725=CARTESIAN_POINT('Origin',(-334.107079934123,3.,9.70707993412312));
#5726=CARTESIAN_POINT('Origin',(-331.107079934121,0.,268.507079934123));
#5727=CARTESIAN_POINT('',(-331.107079934123,0.,26.5141598682461));
#5728=CARTESIAN_POINT('',(-331.107079934123,0.,25.7070799341231));
#5729=CARTESIAN_POINT('Origin',(-337.107079934123,-8.,26.5141598682462));
#5730=CARTESIAN_POINT('Origin',(-337.107079934123,0.,26.5141598682462));
#5731=CARTESIAN_POINT('Origin',(-542.521239802364,-33.7263908158462,252.100479708242));
#5732=CARTESIAN_POINT('',(-542.521239802369,-16.,26.5141598682491));
#5733=CARTESIAN_POINT('',(-542.521239802369,0.,26.5141598682491));
#5734=CARTESIAN_POINT('',(-542.521239802369,-16.8631954079231,26.514159868249));
#5735=CARTESIAN_POINT('',(-542.521239802369,0.,3.0000000000031));
#5736=CARTESIAN_POINT('',(-542.521239802365,0.,240.307079934126));
#5737=CARTESIAN_POINT('',(-542.521239802369,-16.,3.0000000000031));
#5738=CARTESIAN_POINT('',(-542.521239802369,-16.8631954079231,3.0000000000031));
#5739=CARTESIAN_POINT('',(-542.52123980237,-16.,-5.67170524925046));
#5740=CARTESIAN_POINT('Origin',(-539.521239802369,-16.,26.514159868249));
#5741=CARTESIAN_POINT('',(-539.521239802369,-16.,26.5141598682491));
#5742=CARTESIAN_POINT('',(-542.521239802369,-16.,26.514159868249));
#5743=CARTESIAN_POINT('Origin',(-536.521239802369,-16.,26.5141598682489));
#5744=CARTESIAN_POINT('Origin',(-536.521239802369,-16.,26.5141598682489));
#5745=CARTESIAN_POINT('Origin',(-536.521239802369,-8.,26.5141598682489));
#5746=CARTESIAN_POINT('',(-539.521239802369,0.,26.5141598682491));
#5747=CARTESIAN_POINT('',(-539.521239802369,-16.8631954079231,26.514159868249));
#5748=CARTESIAN_POINT('Origin',(-536.521239802369,0.,26.5141598682489));
#5749=CARTESIAN_POINT('',(-536.521239802369,-16.8631954079231,29.5141598682489));
#5750=CARTESIAN_POINT('Origin',(-542.52123980237,0.,-2.49292006587389));
#5751=CARTESIAN_POINT('',(-542.521239802369,0.,26.5141598682491));
#5752=CARTESIAN_POINT('',(-539.521239802369,0.,3.0000000000031));
#5753=CARTESIAN_POINT('',(-539.521239802365,0.,240.307079934126));
#5754=CARTESIAN_POINT('',(-542.521239802369,0.,3.0000000000031));
#5755=CARTESIAN_POINT('Origin',(-542.521239802364,-16.,276.907079934126));
#5756=CARTESIAN_POINT('',(-539.521239802369,-16.,3.0000000000031));
#5757=CARTESIAN_POINT('',(-542.521239802369,-16.,3.0000000000031));
#5758=CARTESIAN_POINT('',(-539.521239802369,-16.,-5.67170524925046));
#5759=CARTESIAN_POINT('Origin',(-317.72076009413,-33.7263908158462,29.5141598682459));
#5760=CARTESIAN_POINT('Origin',(-337.107079934123,0.,32.5141598682462));
#5761=CARTESIAN_POINT('Origin',(-536.521239802369,-8.,26.5141598682489));
#5762=CARTESIAN_POINT('Origin',(-536.521239802369,0.,26.5141598682489));
#5763=CARTESIAN_POINT('Origin',(-289.634919962376,-33.7263908158462,-4.2632564145606E-13));
#5764=CARTESIAN_POINT('',(-545.521239802369,-16.,3.19744231092045E-12));
#5765=CARTESIAN_POINT('',(-545.521239802369,0.292906040926448,3.19744231092045E-12));
#5766=CARTESIAN_POINT('',(-545.521239802369,-16.8631954079231,3.19744231092045E-12));
#5767=CARTESIAN_POINT('',(-547.228319736492,3.,3.19744231092045E-12));
#5768=CARTESIAN_POINT('Origin',(-544.228319736492,3.,3.05533376376843E-12));
#5769=CARTESIAN_POINT('',(-547.228319736492,4.51504440784604,3.19744231092045E-12));
#5770=CARTESIAN_POINT('',(-547.228319736492,20.9,3.19744231092045E-12));
#5771=CARTESIAN_POINT('',(-584.928319736492,4.51504440784603,3.69482222595252E-12));
#5772=CARTESIAN_POINT('',(-116.731619849434,4.51504440784611,-2.91322521661641E-12));
#5773=CARTESIAN_POINT('',(-584.928319736492,-25.9,3.69482222595252E-12));
#5774=CARTESIAN_POINT('',(-584.928319736492,-25.9,3.69482222595252E-12));
#5775=CARTESIAN_POINT('',(-585.928319736492,-26.8999999999999,3.76587649952853E-12));
#5776=CARTESIAN_POINT('Origin',(-585.928319736492,-25.9000000000003,3.76587649952853E-12));
#5777=CARTESIAN_POINT('',(-586.635399670615,-26.8999999999999,3.76587649952853E-12));
#5778=CARTESIAN_POINT('',(-633.608186854927,-26.9,4.47641923528863E-12));
#5779=CARTESIAN_POINT('',(-586.635399670615,-38.9,3.76587649952853E-12));
#5780=CARTESIAN_POINT('',(-586.635399670615,-16.8631954079231,3.76587649952853E-12));
#5781=CARTESIAN_POINT('',(-564.449534553115,-38.9,3.48165940522449E-12));
#5782=CARTESIAN_POINT('',(-634.44152018826,-38.9,4.47641923528863E-12));
#5783=CARTESIAN_POINT('',(-563.629798437503,-38.4727413908212,3.48165940522449E-12));
#5784=CARTESIAN_POINT('Origin',(-564.449534553116,-37.9,3.48165940522449E-12));
#5785=CARTESIAN_POINT('',(-548.226841035482,-16.4272586091788,3.19744231092045E-12));
#5786=CARTESIAN_POINT('',(-563.629798437503,-38.4727413908212,3.48165940522449E-12));
#5787=CARTESIAN_POINT('',(-547.407104919869,-16.,3.19744231092045E-12));
#5788=CARTESIAN_POINT('Origin',(-547.407104919869,-16.9999999999999,3.19744231092045E-12));
#5789=CARTESIAN_POINT('',(-547.407104919869,-16.,3.19744231092045E-12));
#5790=CARTESIAN_POINT('Origin',(-545.521239802369,-16.,-2.99999999999677));
#5791=CARTESIAN_POINT('',(-545.521239802369,-16.,-2.99999999999677));
#5792=CARTESIAN_POINT('',(-545.521239802369,-16.,3.19744231092045E-12));
#5793=CARTESIAN_POINT('Origin',(-545.521239802369,-16.,3.00000000000317));
#5794=CARTESIAN_POINT('Origin',(-545.521239802369,-16.,3.00000000000317));
#5795=CARTESIAN_POINT('Origin',(-545.521239802369,-8.,3.00000000000317));
#5796=CARTESIAN_POINT('',(-545.521239802369,0.292906040926448,-2.99999999999677));
#5797=CARTESIAN_POINT('',(-545.521239802369,-16.8631954079231,-2.99999999999677));
#5798=CARTESIAN_POINT('',(-543.752205642483,2.66453525910038E-14,-2.73328162060089));
#5799=CARTESIAN_POINT('Ctrl Pts',(-545.521239802369,0.292906040926448,-2.99999999999683));
#5800=CARTESIAN_POINT('Ctrl Pts',(-544.962812475542,0.100876738244207,-2.99999999999684));
#5801=CARTESIAN_POINT('Ctrl Pts',(-544.343544764388,2.88601031141256E-14,
-2.91574241285752));
#5802=CARTESIAN_POINT('Ctrl Pts',(-543.752205642483,2.88657986402541E-14,
-2.73328162060087));
#5803=CARTESIAN_POINT('Origin',(-545.521239802369,0.,3.00000000000317));
#5804=CARTESIAN_POINT('',(-539.521239802369,-16.8631954079231,3.0000000000031));
#5805=CARTESIAN_POINT('Origin',(-544.228319736492,3.,3.05533376376843E-12));
#5806=CARTESIAN_POINT('',(-545.521239802369,0.292906040926448,-1.31999999999671));
#5807=CARTESIAN_POINT('',(-545.521239802369,0.292906040926448,3.19744231092045E-12));
#5808=CARTESIAN_POINT('',(-545.521239802369,0.292906040926448,3.19744231092045E-12));
#5809=CARTESIAN_POINT('',(-547.228319736492,3.,-2.99999999999677));
#5810=CARTESIAN_POINT('Origin',(-544.228319736492,3.,-2.99999999999692));
#5811=CARTESIAN_POINT('',(-547.228319736492,3.,3.19744231092045E-12));
#5812=CARTESIAN_POINT('Origin',(-264.828319736492,-16.,-7.8159700933611E-13));
#5813=CARTESIAN_POINT('',(-547.407104919869,-16.,-2.99999999999677));
#5814=CARTESIAN_POINT('',(-547.407104919869,-16.,3.19744231092045E-12));
#5815=CARTESIAN_POINT('',(-547.407104919869,-16.,-2.99999999999677));
#5816=CARTESIAN_POINT('Origin',(-539.521239802365,-33.7263908158462,252.100479708242));
#5817=CARTESIAN_POINT('Origin',(-542.521239802369,0.,26.5141598682491));
#5818=CARTESIAN_POINT('Origin',(-545.521239802369,-8.,3.00000000000317));
#5819=CARTESIAN_POINT('',(-544.636722722426,2.88657986402541E-14,0.133359189701139));
#5820=CARTESIAN_POINT('Origin',(-545.521239802369,0.,3.00000000000317));
#5821=CARTESIAN_POINT('Ctrl Pts',(-545.521239802369,0.292906040926448,3.1685765122802E-12));
#5822=CARTESIAN_POINT('Ctrl Pts',(-545.242026138956,0.100876738244207,3.16462134275497E-12));
#5823=CARTESIAN_POINT('Ctrl Pts',(-544.932392283379,2.88601031141256E-14,
0.0421287935728222));
#5824=CARTESIAN_POINT('Ctrl Pts',(-544.636722722426,2.88657986402541E-14,
0.133359189701149));
#5825=CARTESIAN_POINT('Origin',(-589.635399670618,-33.7263908158462,-300.786319839989));
#5826=CARTESIAN_POINT('',(-589.635399670615,-38.9,3.00000000000381));
#5827=CARTESIAN_POINT('',(-589.635399670615,-26.8999999999999,3.00000000000381));
#5828=CARTESIAN_POINT('',(-589.635399670615,-16.8631954079231,3.00000000000381));
#5829=CARTESIAN_POINT('',(-589.635399670615,-26.9,43.1869470525611));
#5830=CARTESIAN_POINT('',(-589.635399670615,-26.9,43.1869470525611));
#5831=CARTESIAN_POINT('',(-589.635399670615,-27.8169545201463,44.1834928108058));
#5832=CARTESIAN_POINT('Origin',(-589.635399670615,-27.9,43.1869470525609));
#5833=CARTESIAN_POINT('',(-589.635399670615,-37.8169545201463,45.0168261441392));
#5834=CARTESIAN_POINT('',(-589.635399670615,-37.8169545201463,45.0168261441392));
#5835=CARTESIAN_POINT('',(-589.635399670615,-38.9000000000001,44.0202803858942));
#5836=CARTESIAN_POINT('Origin',(-589.635399670615,-37.9,44.0202803858942));
#5837=CARTESIAN_POINT('',(-589.635399670615,-38.9,44.0202803858942));
#5838=CARTESIAN_POINT('Origin',(-592.635399670615,-38.9,3.00000000000381));
#5839=CARTESIAN_POINT('',(-592.635399670615,-38.9,3.00000000000381));
#5840=CARTESIAN_POINT('',(-589.635399670615,-38.9,3.00000000000381));
#5841=CARTESIAN_POINT('',(-586.635399670616,-38.9,-2.99999999999621));
#5842=CARTESIAN_POINT('Origin',(-586.635399670615,-38.9,3.00000000000381));
#5843=CARTESIAN_POINT('',(-586.635399670615,-38.9,3.76587649952853E-12));
#5844=CARTESIAN_POINT('Origin',(-586.635399670615,-38.9,3.00000000000381));
#5845=CARTESIAN_POINT('Origin',(-586.635399670615,-32.9,3.00000000000381));
#5846=CARTESIAN_POINT('',(-592.635399670615,-26.8999999999999,3.00000000000381));
#5847=CARTESIAN_POINT('',(-592.635399670615,-16.8631954079231,3.00000000000381));
#5848=CARTESIAN_POINT('',(-586.635399670616,-26.8999999999999,-2.99999999999621));
#5849=CARTESIAN_POINT('Origin',(-586.635399670615,-26.8999999999999,3.00000000000381));
#5850=CARTESIAN_POINT('',(-586.635399670616,-16.8631954079231,-2.99999999999621));
#5851=CARTESIAN_POINT('Origin',(-589.635399670615,-26.9,43.1869470525611));
#5852=CARTESIAN_POINT('',(-589.635399670615,-26.8999999999999,3.00000000000381));
#5853=CARTESIAN_POINT('',(-592.635399670615,-26.9,43.1869470525611));
#5854=CARTESIAN_POINT('',(-592.635399670615,-26.9,43.1869470525611));
#5855=CARTESIAN_POINT('',(-589.635399670615,-26.9,43.1869470525611));
#5856=CARTESIAN_POINT('Origin',(-589.635399670616,-38.9,-25.9717052492503));
#5857=CARTESIAN_POINT('',(-592.635399670615,-38.9000000000001,44.0202803858942));
#5858=CARTESIAN_POINT('',(-589.635399670615,-38.9000000000001,44.0202803858942));
#5859=CARTESIAN_POINT('',(-592.635399670615,-38.9,44.0202803858942));
#5860=CARTESIAN_POINT('Origin',(-289.634919962376,-33.7263908158462,-3.00000000000047));
#5861=CARTESIAN_POINT('',(-548.226841035482,-16.4272586091788,-2.99999999999677));
#5862=CARTESIAN_POINT('Origin',(-547.407104919869,-16.9999999999999,-2.99999999999677));
#5863=CARTESIAN_POINT('',(-563.629798437503,-38.4727413908212,-2.99999999999649));
#5864=CARTESIAN_POINT('',(-563.629798437503,-38.4727413908212,-2.99999999999649));
#5865=CARTESIAN_POINT('',(-564.449534553115,-38.9,-2.99999999999649));
#5866=CARTESIAN_POINT('Origin',(-564.449534553116,-37.9,-2.99999999999649));
#5867=CARTESIAN_POINT('',(-634.44152018826,-38.9,-2.99999999999557));
#5868=CARTESIAN_POINT('',(-585.928319736492,-26.8999999999999,-2.99999999999621));
#5869=CARTESIAN_POINT('',(-633.608186854927,-26.9,-2.99999999999557));
#5870=CARTESIAN_POINT('',(-584.928319736493,-25.9,-2.99999999999628));
#5871=CARTESIAN_POINT('Origin',(-585.928319736492,-25.9000000000003,-2.99999999999621));
#5872=CARTESIAN_POINT('',(-584.928319736493,4.51504440784603,-2.99999999999628));
#5873=CARTESIAN_POINT('',(-584.928319736493,-25.9,-2.99999999999628));
#5874=CARTESIAN_POINT('',(-547.228319736492,4.51504440784604,-2.99999999999677));
#5875=CARTESIAN_POINT('',(-116.731619849434,4.51504440784611,-3.00000000000296));
#5876=CARTESIAN_POINT('',(-547.228319736492,20.9,-2.99999999999677));
#5877=CARTESIAN_POINT('Origin',(-633.608186854927,-26.9,4.47641923528863E-12));
#5878=CARTESIAN_POINT('',(-586.635399670615,-26.8999999999999,3.76587649952853E-12));
#5879=CARTESIAN_POINT('',(-585.928319736492,-26.8999999999999,3.76587649952853E-12));
#5880=CARTESIAN_POINT('Origin',(-586.635399670615,-32.9,3.00000000000381));
#5881=CARTESIAN_POINT('Origin',(-586.635399670615,-26.8999999999999,3.00000000000381));
#5882=CARTESIAN_POINT('Origin',(-289.634919962377,-20.1911952001085,-31.110921292855));
#5883=CARTESIAN_POINT('',(-584.928319736492,6.81317773720297,1.07163717094409));
#5884=CARTESIAN_POINT('',(-547.228319736492,6.81317773720297,1.07163717094359));
#5885=CARTESIAN_POINT('',(-116.731619849434,6.81317773720305,1.07163717093748));
#5886=CARTESIAN_POINT('',(-547.228319736492,14.921971978212,10.7353218392733));
#5887=CARTESIAN_POINT('',(-547.228319736492,14.921971978212,10.7353218392733));
#5888=CARTESIAN_POINT('',(-550.228319736492,16.8503348072716,13.0334551686303));
#5889=CARTESIAN_POINT('Origin',(-550.228319736492,14.921971978212,10.7353218392733));
#5890=CARTESIAN_POINT('',(-587.928319736492,16.8503348072716,13.0334551686308));
#5891=CARTESIAN_POINT('',(-584.928319736492,16.8503348072716,13.0334551686308));
#5892=CARTESIAN_POINT('',(-588.428319736492,16.5289410024283,12.6504329470713));
#5893=CARTESIAN_POINT('Origin',(-587.928319736492,16.5289410024284,12.6504329470715));
#5894=CARTESIAN_POINT('',(-588.428319736492,16.2075471975851,12.2674107255119));
#5895=CARTESIAN_POINT('',(-588.428319736492,16.5289410024283,12.6504329470713));
#5896=CARTESIAN_POINT('',(-588.928319736492,15.8861533927418,11.8843885039524));
#5897=CARTESIAN_POINT('Origin',(-588.928319736492,16.2075471975851,12.2674107255119));
#5898=CARTESIAN_POINT('',(-592.928319736492,15.8861533927418,11.8843885039524));
#5899=CARTESIAN_POINT('',(-592.928319736492,15.8861533927418,11.8843885039525));
#5900=CARTESIAN_POINT('',(-593.428319736492,16.2075471975851,12.267410725512));
#5901=CARTESIAN_POINT('Origin',(-592.928319736492,16.2075471975851,12.267410725512));
#5902=CARTESIAN_POINT('',(-593.428319736492,16.5289410024283,12.6504329470715));
#5903=CARTESIAN_POINT('',(-593.428319736492,16.5289410024283,12.6504329470715));
#5904=CARTESIAN_POINT('',(-593.928319736492,16.8503348072715,13.0334551686309));
#5905=CARTESIAN_POINT('Origin',(-593.928319736492,16.5289410024283,12.6504329470714));
#5906=CARTESIAN_POINT('',(-595.928319736492,16.8503348072716,13.0334551686309));
#5907=CARTESIAN_POINT('',(-595.928319736492,16.8503348072716,13.0334551686309));
#5908=CARTESIAN_POINT('',(-596.928319736492,16.2075471975851,12.2674107255119));
#5909=CARTESIAN_POINT('Origin',(-595.928319736492,16.2075471975851,12.2674107255121));
#5910=CARTESIAN_POINT('',(-596.928319736492,11.0652463200927,6.13905518056015));
#5911=CARTESIAN_POINT('',(-596.928319736492,11.0652463200927,6.13905518056015));
#5912=CARTESIAN_POINT('',(-595.928319736492,10.4224587104062,5.37301073744111));
#5913=CARTESIAN_POINT('Origin',(-595.928319736492,11.0652463200927,6.13905518056001));
#5914=CARTESIAN_POINT('',(-593.928319736492,10.4224587104062,5.37301073744118));
#5915=CARTESIAN_POINT('',(-593.928319736492,10.4224587104062,5.37301073744118));
#5916=CARTESIAN_POINT('',(-593.428319736492,10.7438525152495,5.75603295900066));
#5917=CARTESIAN_POINT('Origin',(-593.928319736492,10.7438525152495,5.75603295900066));
#5918=CARTESIAN_POINT('',(-593.428319736492,11.0652463200927,6.13905518056008));
#5919=CARTESIAN_POINT('',(-593.428319736492,11.0652463200927,6.13905518056008));
#5920=CARTESIAN_POINT('',(-592.928319736492,11.386640124936,6.52207740211956));
#5921=CARTESIAN_POINT('Origin',(-592.928319736492,11.0652463200927,6.13905518056008));
#5922=CARTESIAN_POINT('',(-588.928319736492,11.3866401249359,6.52207740211949));
#5923=CARTESIAN_POINT('',(-588.928319736492,11.386640124936,6.52207740211949));
#5924=CARTESIAN_POINT('',(-588.428319736492,11.0652463200927,6.13905518055994));
#5925=CARTESIAN_POINT('Origin',(-588.928319736492,11.0652463200927,6.13905518056001));
#5926=CARTESIAN_POINT('',(-588.428319736492,10.7438525152495,5.75603295900052));
#5927=CARTESIAN_POINT('',(-588.428319736492,11.0652463200927,6.13905518055994));
#5928=CARTESIAN_POINT('',(-587.928319736492,10.4224587104062,5.37301073744104));
#5929=CARTESIAN_POINT('Origin',(-587.928319736492,10.7438525152495,5.75603295900052));
#5930=CARTESIAN_POINT('',(-584.928319736492,10.4224587104062,5.37301073744104));
#5931=CARTESIAN_POINT('',(-584.928319736492,10.4224587104062,5.37301073744104));
#5932=CARTESIAN_POINT('',(-584.928319736493,-15.1604881551181,-25.1155580986944));
#5933=CARTESIAN_POINT('Origin',(-584.928319736492,9.1113110665599,-0.856725658115494));
#5934=CARTESIAN_POINT('',(-584.928319736492,9.1113110665599,-0.856725658115494));
#5935=CARTESIAN_POINT('',(-584.928319736492,6.81317773720297,1.07163717094409));
#5936=CARTESIAN_POINT('Origin',(-584.928319736492,4.51504440784603,3.00000000000381));
#5937=CARTESIAN_POINT('',(-584.928319736492,4.51504440784603,3.69482222595252E-12));
#5938=CARTESIAN_POINT('Origin',(-584.928319736492,4.51504440784603,3.00000000000381));
#5939=CARTESIAN_POINT('Origin',(-566.078319736492,4.51504440784603,3.00000000000345));
#5940=CARTESIAN_POINT('',(-547.228319736492,9.11131106655991,-0.856725658115991));
#5941=CARTESIAN_POINT('',(-116.731619849434,9.11131106655998,-0.856725658122173));
#5942=CARTESIAN_POINT('Origin',(-547.228319736492,4.51504440784604,3.00000000000324));
#5943=CARTESIAN_POINT('Origin',(-547.228319736492,14.921971978212,10.7353218392733));
#5944=CARTESIAN_POINT('',(-547.228319736492,6.81317773720297,1.07163717094359));
#5945=CARTESIAN_POINT('',(-547.228319736492,17.2201053075689,8.80695901021369));
#5946=CARTESIAN_POINT('',(-547.228319736492,17.2201053075689,8.80695901021369));
#5947=CARTESIAN_POINT('',(-547.228319736492,14.921971978212,10.7353218392733));
#5948=CARTESIAN_POINT('Origin',(-584.928319736493,-15.1604881551181,-25.1155580986944));
#5949=CARTESIAN_POINT('',(-584.928319736492,12.7205920397631,3.44464790838146));
#5950=CARTESIAN_POINT('',(-584.928319736492,10.4224587104062,5.37301073744104));
#5951=CARTESIAN_POINT('',(-584.928319736493,-12.8623548257611,-27.0439209277539));
#5952=CARTESIAN_POINT('Origin',(-592.635399670618,-33.7263908158462,-300.786319839989));
#5953=CARTESIAN_POINT('',(-592.635399670615,-37.8169545201463,45.0168261441392));
#5954=CARTESIAN_POINT('Origin',(-592.635399670615,-37.9,44.0202803858942));
#5955=CARTESIAN_POINT('',(-592.635399670615,-27.8169545201463,44.1834928108058));
#5956=CARTESIAN_POINT('',(-592.635399670615,-37.8169545201463,45.0168261441392));
#5957=CARTESIAN_POINT('Origin',(-592.635399670615,-27.9,43.1869470525609));
#5958=CARTESIAN_POINT('Origin',(-547.228319736492,20.9,3.19744231092045E-12));
#5959=CARTESIAN_POINT('',(-547.228319736492,4.51504440784604,3.19744231092045E-12));
#5960=CARTESIAN_POINT('Origin',(-566.078319736492,4.51504440784603,3.00000000000345));
#5961=CARTESIAN_POINT('Origin',(-547.228319736492,4.51504440784604,3.00000000000324));
#5962=CARTESIAN_POINT('Origin',(-345.806600225884,-20.1911952001085,-31.1109212928545));
#5963=CARTESIAN_POINT('',(-315.3,6.81317773720302,1.07163717094038));
#5964=CARTESIAN_POINT('',(-243.3,6.81317773720303,1.07163717094038));
#5965=CARTESIAN_POINT('',(-172.903300112942,6.81317773720304,1.07163717094038));
#5966=CARTESIAN_POINT('',(-243.3,16.8503348072716,13.033455168627));
#5967=CARTESIAN_POINT('',(-243.3,16.8503348072716,13.033455168627));
#5968=CARTESIAN_POINT('',(-315.3,16.8503348072716,13.033455168627));
#5969=CARTESIAN_POINT('',(-315.3,16.8503348072716,13.033455168627));
#5970=CARTESIAN_POINT('',(-315.3,3.41607376482294,-2.97687369255963));
#5971=CARTESIAN_POINT('Origin',(-315.3,9.11131106655995,-0.856725658119238));
#5972=CARTESIAN_POINT('',(-315.3,9.11131106655995,-0.856725658119237));
#5973=CARTESIAN_POINT('',(-315.3,6.81317773720302,1.07163717094038));
#5974=CARTESIAN_POINT('Origin',(-315.3,4.51504440784608,3.));
#5975=CARTESIAN_POINT('',(-315.3,4.51504440784608,0.));
#5976=CARTESIAN_POINT('',(-315.3,4.51504440784608,0.));
#5977=CARTESIAN_POINT('Origin',(-315.3,4.51504440784608,3.));
#5978=CARTESIAN_POINT('Origin',(-279.3,4.51504440784609,3.));
#5979=CARTESIAN_POINT('',(-243.3,9.11131106655996,-0.856725658119237));
#5980=CARTESIAN_POINT('',(-172.903300112942,9.11131106655997,-0.85672565811924));
#5981=CARTESIAN_POINT('Origin',(-243.3,4.51504440784609,3.));
#5982=CARTESIAN_POINT('Origin',(-243.3,16.8503348072716,13.033455168627));
#5983=CARTESIAN_POINT('',(-243.3,6.81317773720303,1.07163717094038));
#5984=CARTESIAN_POINT('',(-243.3,19.1484681366286,11.1050923395674));
#5985=CARTESIAN_POINT('',(-243.3,19.1484681366286,11.1050923395674));
#5986=CARTESIAN_POINT('',(-243.3,16.8503348072716,13.033455168627));
#5987=CARTESIAN_POINT('Origin',(-315.3,3.41607376482294,-2.97687369255963));
#5988=CARTESIAN_POINT('',(-315.3,19.1484681366286,11.1050923395674));
#5989=CARTESIAN_POINT('',(-315.3,16.8503348072716,13.033455168627));
#5990=CARTESIAN_POINT('',(-315.3,5.71420709417988,-4.90523652161925));
#5991=CARTESIAN_POINT('Origin',(-289.634919962377,-17.8930618707516,-33.0392841219145));
#5992=CARTESIAN_POINT('',(-587.928319736492,12.7205920397632,3.44464790838146));
#5993=CARTESIAN_POINT('',(-584.928319736492,12.7205920397631,3.44464790838146));
#5994=CARTESIAN_POINT('',(-588.428319736492,13.0419858446064,3.82767012994094));
#5995=CARTESIAN_POINT('Origin',(-587.928319736492,13.0419858446064,3.82767012994094));
#5996=CARTESIAN_POINT('',(-588.428319736492,13.3633796494496,4.21069235150036));
#5997=CARTESIAN_POINT('',(-588.428319736492,13.3633796494496,4.21069235150036));
#5998=CARTESIAN_POINT('',(-588.928319736492,13.6847734542929,4.59371457305984));
#5999=CARTESIAN_POINT('Origin',(-588.928319736492,13.3633796494496,4.21069235150043));
#6000=CARTESIAN_POINT('',(-592.928319736492,13.6847734542929,4.59371457305991));
#6001=CARTESIAN_POINT('',(-588.928319736492,13.6847734542929,4.59371457305991));
#6002=CARTESIAN_POINT('',(-593.428319736492,13.3633796494496,4.21069235150043));
#6003=CARTESIAN_POINT('Origin',(-592.928319736492,13.3633796494496,4.21069235150043));
#6004=CARTESIAN_POINT('',(-593.428319736492,13.0419858446064,3.82767012994101));
#6005=CARTESIAN_POINT('',(-593.428319736492,13.3633796494496,4.21069235150043));
#6006=CARTESIAN_POINT('',(-593.928319736492,12.7205920397631,3.44464790838153));
#6007=CARTESIAN_POINT('Origin',(-593.928319736492,13.0419858446064,3.82767012994101));
#6008=CARTESIAN_POINT('',(-595.928319736492,12.7205920397631,3.44464790838146));
#6009=CARTESIAN_POINT('',(-593.928319736492,12.7205920397631,3.44464790838153));
#6010=CARTESIAN_POINT('',(-596.928319736492,13.3633796494497,4.2106923515005));
#6011=CARTESIAN_POINT('Origin',(-595.928319736492,13.3633796494496,4.21069235150043));
#6012=CARTESIAN_POINT('',(-596.928319736492,18.505680526942,10.3390478964523));
#6013=CARTESIAN_POINT('',(-596.928319736492,13.3633796494497,4.2106923515005));
#6014=CARTESIAN_POINT('',(-595.928319736492,19.1484681366285,11.1050923395713));
#6015=CARTESIAN_POINT('Origin',(-595.928319736492,18.5056805269421,10.3390478964524));
#6016=CARTESIAN_POINT('',(-593.928319736492,19.1484681366285,11.1050923395712));
#6017=CARTESIAN_POINT('',(-595.928319736492,19.1484681366285,11.1050923395713));
#6018=CARTESIAN_POINT('',(-593.428319736492,18.8270743317853,10.7220701180118));
#6019=CARTESIAN_POINT('Origin',(-593.928319736492,18.8270743317852,10.7220701180118));
#6020=CARTESIAN_POINT('',(-593.428319736492,18.505680526942,10.3390478964523));
#6021=CARTESIAN_POINT('',(-593.428319736492,18.8270743317853,10.7220701180118));
#6022=CARTESIAN_POINT('',(-592.928319736492,18.1842867220987,9.95602567489279));
#6023=CARTESIAN_POINT('Origin',(-592.928319736492,18.505680526942,10.3390478964524));
#6024=CARTESIAN_POINT('',(-588.928319736492,18.1842867220987,9.95602567489279));
#6025=CARTESIAN_POINT('',(-592.928319736492,18.1842867220987,9.95602567489286));
#6026=CARTESIAN_POINT('',(-588.428319736492,18.505680526942,10.3390478964522));
#6027=CARTESIAN_POINT('Origin',(-588.928319736492,18.505680526942,10.3390478964523));
#6028=CARTESIAN_POINT('',(-588.428319736492,18.8270743317852,10.7220701180117));
#6029=CARTESIAN_POINT('',(-588.428319736492,18.8270743317852,10.7220701180117));
#6030=CARTESIAN_POINT('',(-587.928319736492,19.1484681366285,11.1050923395712));
#6031=CARTESIAN_POINT('Origin',(-587.928319736492,18.8270743317854,10.7220701180118));
#6032=CARTESIAN_POINT('',(-550.228319736492,19.1484681366285,11.1050923395707));
#6033=CARTESIAN_POINT('',(-584.928319736492,19.1484681366285,11.1050923395712));
#6034=CARTESIAN_POINT('Origin',(-550.228319736492,17.2201053075689,8.80695901021376));
#6035=CARTESIAN_POINT('Origin',(-243.3,23.9,0.));
#6036=CARTESIAN_POINT('',(-243.3,4.51504440784609,0.));
#6037=CARTESIAN_POINT('',(-243.3,4.51504440784609,0.));
#6038=CARTESIAN_POINT('',(-243.3,3.,0.));
#6039=CARTESIAN_POINT('',(-243.3,23.9,0.));
#6040=CARTESIAN_POINT('',(-243.3,3.,0.));
#6041=CARTESIAN_POINT('Origin',(-279.3,4.51504440784609,3.));
#6042=CARTESIAN_POINT('',(-172.903300112942,4.5150444078461,0.));
#6043=CARTESIAN_POINT('Origin',(-243.3,4.51504440784609,3.));
#6044=CARTESIAN_POINT('Origin',(-345.806600225884,-43.3932528566243,-10.452702172532));
#6045=CARTESIAN_POINT('',(-304.920094064468,-43.3932528566243,3.));
#6046=CARTESIAN_POINT('',(-320.,-43.3932528566243,3.));
#6047=CARTESIAN_POINT('',(-172.903300112942,-43.3932528566243,3.));
#6048=CARTESIAN_POINT('',(-320.,-43.3932528566243,5.72090701162177));
#6049=CARTESIAN_POINT('',(-320.,-43.3932528566243,-27.1790929883782));
#6050=CARTESIAN_POINT('',(-323.,-43.3932528566243,8.72090701162177));
#6051=CARTESIAN_POINT('Origin',(-323.,-43.3932528566243,5.72090701162178));
#6052=CARTESIAN_POINT('',(-330.,-43.3932528566243,8.72090701162179));
#6053=CARTESIAN_POINT('',(-323.,-43.3932528566243,8.72090701162177));
#6054=CARTESIAN_POINT('',(-333.,-43.3932528566243,11.7209070116218));
#6055=CARTESIAN_POINT('Origin',(-330.,-43.3932528566243,11.7209070116218));
#6056=CARTESIAN_POINT('',(-333.,-43.3932528566243,46.1736886433142));
#6057=CARTESIAN_POINT('',(-333.,-43.3932528566243,11.7209070116218));
#6058=CARTESIAN_POINT('',(-331.953895233392,-43.3932528566243,47.1726252531629));
#6059=CARTESIAN_POINT('Origin',(-332.,-43.3932528566243,46.1736886433142));
#6060=CARTESIAN_POINT('',(-320.953895233392,-43.3932528566243,46.6649329454706));
#6061=CARTESIAN_POINT('',(-331.953895233392,-43.3932528566243,47.1726252531629));
#6062=CARTESIAN_POINT('',(-320.,-43.3932528566243,45.6659963356219));
#6063=CARTESIAN_POINT('Origin',(-321.,-43.3932528566243,45.6659963356218));
#6064=CARTESIAN_POINT('',(-320.,-43.3932528566243,43.5373689430449));
#6065=CARTESIAN_POINT('',(-320.,-43.3932528566243,43.5373689430449));
#6066=CARTESIAN_POINT('',(-319.087014138364,-43.3932528566243,42.5411618663159));
#6067=CARTESIAN_POINT('Origin',(-319.,-43.3932528566243,43.5373689430449));
#6068=CARTESIAN_POINT('',(-178.174028276727,-43.3932528566243,30.2330561165999));
#6069=CARTESIAN_POINT('',(-178.174028276727,-43.3932528566243,30.2330561165999));
#6070=CARTESIAN_POINT('',(-176.,-43.3932528566243,32.2254702700578));
#6071=CARTESIAN_POINT('Origin',(-178.,-43.3932528566243,32.2254702700579));
#6072=CARTESIAN_POINT('',(-176.,-43.3932528566243,32.3928496364886));
#6073=CARTESIAN_POINT('',(-176.,-43.3932528566243,32.3928496364886));
#6074=CARTESIAN_POINT('',(-174.953895233392,-43.3932528566243,33.3917862463373));
#6075=CARTESIAN_POINT('Origin',(-175.,-43.3932528566243,32.3928496364886));
#6076=CARTESIAN_POINT('',(-156.753895233392,-43.3932528566243,32.5517862463373));
#6077=CARTESIAN_POINT('',(-174.953895233392,-43.3932528566243,33.3917862463373));
#6078=CARTESIAN_POINT('',(-155.8,-43.3932528566243,31.5528496364886));
#6079=CARTESIAN_POINT('Origin',(-156.8,-43.3932528566243,31.5528496364886));
#6080=CARTESIAN_POINT('',(-155.8,-43.3932528566243,9.72090701162179));
#6081=CARTESIAN_POINT('',(-155.8,-43.3932528566243,9.72090701162179));
#6082=CARTESIAN_POINT('',(-156.8,-43.3932528566243,8.72090701162183));
#6083=CARTESIAN_POINT('Origin',(-156.8,-43.3932528566243,9.72090701162177));
#6084=CARTESIAN_POINT('',(-175.,-43.3932528566243,8.72090701162177));
#6085=CARTESIAN_POINT('',(-175.,-43.3932528566243,8.72090701162177));
#6086=CARTESIAN_POINT('',(-176.,-43.3932528566243,9.72090701162177));
#6087=CARTESIAN_POINT('Origin',(-175.,-43.3932528566243,9.72090701162177));
#6088=CARTESIAN_POINT('',(-176.,-43.3932528566243,21.9654788617441));
#6089=CARTESIAN_POINT('',(-176.,-43.3932528566243,21.9654788617441));
#6090=CARTESIAN_POINT('',(-177.825971723273,-43.3932528566243,23.9578930152019));
#6091=CARTESIAN_POINT('Origin',(-178.,-43.3932528566243,21.9654788617441));
#6092=CARTESIAN_POINT('',(-302.746065787741,-43.3932528566243,34.869092707753));
#6093=CARTESIAN_POINT('',(-177.825971723273,-43.3932528566243,23.9578930152019));
#6094=CARTESIAN_POINT('',(-304.920094064468,-43.3932528566243,32.8766785542951));
#6095=CARTESIAN_POINT('Origin',(-302.920094064468,-43.3932528566243,32.8766785542951));
#6096=CARTESIAN_POINT('',(-304.920094064468,-43.3932528566243,-4.77909298837838));
#6097=CARTESIAN_POINT('',(-172.48586385441,-43.3932528566243,17.4959070116218));
#6098=CARTESIAN_POINT('',(-166.9,-43.3932528566243,14.2709070116218));
#6099=CARTESIAN_POINT('',(-166.9,-43.3932528566243,14.2709070116218));
#6100=CARTESIAN_POINT('',(-161.31413614559,-43.3932528566243,17.4959070116218));
#6101=CARTESIAN_POINT('',(-161.31413614559,-43.3932528566243,17.4959070116218));
#6102=CARTESIAN_POINT('',(-161.31413614559,-43.3932528566243,23.9459070116218));
#6103=CARTESIAN_POINT('',(-161.31413614559,-43.3932528566243,23.9459070116218));
#6104=CARTESIAN_POINT('',(-166.9,-43.3932528566243,27.1709070116218));
#6105=CARTESIAN_POINT('',(-166.9,-43.3932528566243,27.1709070116218));
#6106=CARTESIAN_POINT('',(-172.48586385441,-43.3932528566243,23.9459070116218));
#6107=CARTESIAN_POINT('',(-172.48586385441,-43.3932528566243,23.9459070116218));
#6108=CARTESIAN_POINT('',(-172.48586385441,-43.3932528566243,17.4959070116218));
#6109=CARTESIAN_POINT('',(-321.9,-43.3932528566243,27.1709070116218));
#6110=CARTESIAN_POINT('',(-327.48586385441,-43.3932528566243,23.9459070116218));
#6111=CARTESIAN_POINT('',(-327.48586385441,-43.3932528566243,23.9459070116218));
#6112=CARTESIAN_POINT('',(-327.48586385441,-43.3932528566243,17.4959070116218));
#6113=CARTESIAN_POINT('',(-327.48586385441,-43.3932528566243,17.4959070116218));
#6114=CARTESIAN_POINT('',(-321.9,-43.3932528566243,14.2709070116218));
#6115=CARTESIAN_POINT('',(-321.9,-43.3932528566243,14.2709070116218));
#6116=CARTESIAN_POINT('',(-316.31413614559,-43.3932528566243,17.4959070116218));
#6117=CARTESIAN_POINT('',(-316.31413614559,-43.3932528566243,17.4959070116218));
#6118=CARTESIAN_POINT('',(-316.31413614559,-43.3932528566243,23.9459070116218));
#6119=CARTESIAN_POINT('',(-316.31413614559,-43.3932528566243,23.9459070116218));
#6120=CARTESIAN_POINT('',(-321.9,-43.3932528566243,27.1709070116218));
#6121=CARTESIAN_POINT('Origin',(-304.920094064468,-46.3932528566243,3.));
#6122=CARTESIAN_POINT('',(-304.920094064468,-46.3932528566243,3.));
#6123=CARTESIAN_POINT('',(-304.920094064468,-43.3932528566243,3.));
#6124=CARTESIAN_POINT('Origin',(-304.920094064468,-40.3932528566243,3.));
#6125=CARTESIAN_POINT('',(-304.920094064468,-40.3932528566243,0.));
#6126=CARTESIAN_POINT('',(-304.920094064468,-40.3932528566243,0.));
#6127=CARTESIAN_POINT('Origin',(-304.920094064468,-40.3932528566243,3.));
#6128=CARTESIAN_POINT('Origin',(-312.460047032234,-40.3932528566243,3.));
#6129=CARTESIAN_POINT('',(-320.,-46.3932528566243,3.));
#6130=CARTESIAN_POINT('',(-172.903300112942,-46.3932528566243,3.));
#6131=CARTESIAN_POINT('Origin',(-320.,-40.3932528566243,3.));
#6132=CARTESIAN_POINT('Origin',(-320.,-43.3932528566243,5.72090701162177));
#6133=CARTESIAN_POINT('',(-320.,-43.3932528566243,3.));
#6134=CARTESIAN_POINT('',(-320.,-46.3932528566243,5.72090701162177));
#6135=CARTESIAN_POINT('',(-320.,-46.3932528566243,-27.1790929883782));
#6136=CARTESIAN_POINT('',(-320.,-43.3932528566243,5.72090701162177));
#6137=CARTESIAN_POINT('Origin',(-304.920094064468,-43.3932528566243,-4.77909298837838));
#6138=CARTESIAN_POINT('',(-304.920094064468,-46.3932528566243,32.8766785542951));
#6139=CARTESIAN_POINT('',(-304.920094064468,-43.3932528566243,32.8766785542951));
#6140=CARTESIAN_POINT('',(-304.920094064468,-46.3932528566243,-4.77909298837838));
#6141=CARTESIAN_POINT('Origin',(-345.806600225884,-17.8930618707516,-33.0392841219141));
#6142=CARTESIAN_POINT('',(-315.3,19.1484681366286,11.1050923395674));
#6143=CARTESIAN_POINT('Origin',(-320.,-49.9,0.));
#6144=CARTESIAN_POINT('',(-320.,-40.3932528566243,0.));
#6145=CARTESIAN_POINT('',(-320.,-40.3932528566243,0.));
#6146=CARTESIAN_POINT('',(-320.,-17.,0.));
#6147=CARTESIAN_POINT('',(-320.,-17.,0.));
#6148=CARTESIAN_POINT('',(-320.,-17.,0.));
#6149=CARTESIAN_POINT('Origin',(-312.460047032234,-40.3932528566243,3.));
#6150=CARTESIAN_POINT('',(-172.903300112942,-40.3932528566243,0.));
#6151=CARTESIAN_POINT('Origin',(-320.,-40.3932528566243,3.));
#6152=CARTESIAN_POINT('Origin',(-345.806600225884,-20.1911952001085,-31.1109212928545));
#6153=CARTESIAN_POINT('',(-93.3,6.81317773720305,1.07163717094038));
#6154=CARTESIAN_POINT('',(-78.3,6.81317773720305,1.07163717094038));
#6155=CARTESIAN_POINT('',(-172.903300112942,6.81317773720303,1.07163717094038));
#6156=CARTESIAN_POINT('',(-78.3,10.101064905563,4.98998851587771));
#6157=CARTESIAN_POINT('',(-78.3,10.101064905563,4.98998851587771));
#6158=CARTESIAN_POINT('',(-77.8,10.4224587104062,5.3730107374372));
#6159=CARTESIAN_POINT('Origin',(-77.8,10.101064905563,4.98998851587771));
#6160=CARTESIAN_POINT('',(-75.3,10.4224587104062,5.3730107374372));
#6161=CARTESIAN_POINT('',(-77.8,10.4224587104062,5.3730107374372));
#6162=CARTESIAN_POINT('',(-74.8,10.7438525152495,5.75603295899669));
#6163=CARTESIAN_POINT('Origin',(-75.3,10.7438525152495,5.75603295899669));
#6164=CARTESIAN_POINT('',(-74.8,11.0652463200928,6.13905518055618));
#6165=CARTESIAN_POINT('',(-74.8,11.0652463200928,6.13905518055618));
#6166=CARTESIAN_POINT('',(-74.3,11.386640124936,6.52207740211567));
#6167=CARTESIAN_POINT('Origin',(-74.3,11.0652463200928,6.13905518055618));
#6168=CARTESIAN_POINT('',(-70.3,11.386640124936,6.52207740211566));
#6169=CARTESIAN_POINT('',(-74.3,11.386640124936,6.52207740211567));
#6170=CARTESIAN_POINT('',(-69.8,11.0652463200928,6.13905518055618));
#6171=CARTESIAN_POINT('Origin',(-70.3,11.0652463200928,6.13905518055618));
#6172=CARTESIAN_POINT('',(-69.8,10.7438525152495,5.75603295899669));
#6173=CARTESIAN_POINT('',(-69.8,11.0652463200928,6.13905518055618));
#6174=CARTESIAN_POINT('',(-69.3,10.4224587104062,5.3730107374372));
#6175=CARTESIAN_POINT('Origin',(-69.3,10.7438525152495,5.75603295899669));
#6176=CARTESIAN_POINT('',(-67.3,10.4224587104062,5.3730107374372));
#6177=CARTESIAN_POINT('',(-69.3,10.4224587104062,5.3730107374372));
#6178=CARTESIAN_POINT('',(-66.3,11.0652463200928,6.13905518055618));
#6179=CARTESIAN_POINT('Origin',(-67.3,11.0652463200928,6.13905518055618));
#6180=CARTESIAN_POINT('',(-66.3,16.2075471975851,12.267410725508));
#6181=CARTESIAN_POINT('',(-66.3,11.0652463200928,6.13905518055618));
#6182=CARTESIAN_POINT('',(-67.3,16.8503348072716,13.033455168627));
#6183=CARTESIAN_POINT('Origin',(-67.3,16.2075471975851,12.267410725508));
#6184=CARTESIAN_POINT('',(-69.3,16.8503348072716,13.033455168627));
#6185=CARTESIAN_POINT('',(-67.3,16.8503348072716,13.033455168627));
#6186=CARTESIAN_POINT('',(-69.8,16.5289410024284,12.6504329470675));
#6187=CARTESIAN_POINT('Origin',(-69.3,16.5289410024284,12.6504329470675));
#6188=CARTESIAN_POINT('',(-69.8,16.2075471975851,12.267410725508));
#6189=CARTESIAN_POINT('',(-69.8,16.5289410024284,12.6504329470675));
#6190=CARTESIAN_POINT('',(-70.3,15.8861533927418,11.8843885039485));
#6191=CARTESIAN_POINT('Origin',(-70.3,16.2075471975851,12.267410725508));
#6192=CARTESIAN_POINT('',(-74.3,15.8861533927418,11.8843885039485));
#6193=CARTESIAN_POINT('',(-70.3,15.8861533927418,11.8843885039485));
#6194=CARTESIAN_POINT('',(-74.8,16.2075471975851,12.267410725508));
#6195=CARTESIAN_POINT('Origin',(-74.3,16.2075471975851,12.267410725508));
#6196=CARTESIAN_POINT('',(-74.8,16.5289410024284,12.6504329470675));
#6197=CARTESIAN_POINT('',(-74.8,16.5289410024284,12.6504329470675));
#6198=CARTESIAN_POINT('',(-75.3,16.8503348072716,13.033455168627));
#6199=CARTESIAN_POINT('Origin',(-75.3,16.5289410024284,12.6504329470675));
#6200=CARTESIAN_POINT('',(-92.3,16.8503348072716,13.033455168627));
#6201=CARTESIAN_POINT('',(-75.3,16.8503348072716,13.033455168627));
#6202=CARTESIAN_POINT('',(-93.3,16.2075471975851,12.267410725508));
#6203=CARTESIAN_POINT('Origin',(-92.3,16.2075471975851,12.267410725508));
#6204=CARTESIAN_POINT('',(-93.3,16.2075471975851,12.267410725508));
#6205=CARTESIAN_POINT('Origin',(-93.3,9.11131106655999,-0.856725658119235));
#6206=CARTESIAN_POINT('',(-93.3,9.11131106655998,-0.856725658119235));
#6207=CARTESIAN_POINT('',(-93.3,6.81317773720305,1.07163717094038));
#6208=CARTESIAN_POINT('Origin',(-93.3,4.51504440784612,3.));
#6209=CARTESIAN_POINT('',(-93.3,4.51504440784612,0.));
#6210=CARTESIAN_POINT('',(-93.3,4.51504440784612,0.));
#6211=CARTESIAN_POINT('Origin',(-93.3,4.51504440784612,3.));
#6212=CARTESIAN_POINT('Origin',(-86.0248944653274,4.51504440784612,3.));
#6213=CARTESIAN_POINT('',(-78.3,9.11131106655999,-0.856725658119237));
#6214=CARTESIAN_POINT('',(-172.903300112942,9.11131106655996,-0.856725658119236));
#6215=CARTESIAN_POINT('Origin',(-78.3,4.51504440784612,3.));
#6216=CARTESIAN_POINT('Origin',(-78.3,10.101064905563,4.98998851587771));
#6217=CARTESIAN_POINT('',(-78.3,6.81317773720305,1.07163717094038));
#6218=CARTESIAN_POINT('',(-78.3,12.3991982349199,3.06162568681809));
#6219=CARTESIAN_POINT('',(-78.3,12.3991982349199,3.06162568681809));
#6220=CARTESIAN_POINT('',(-78.3,10.101064905563,4.98998851587771));
#6221=CARTESIAN_POINT('Origin',(-93.3,3.41607376482296,-2.97687369255966));
#6222=CARTESIAN_POINT('',(-93.3,18.505680526942,10.3390478964484));
#6223=CARTESIAN_POINT('',(-93.3,16.2075471975851,12.267410725508));
#6224=CARTESIAN_POINT('',(-93.3,18.505680526942,10.3390478964484));
#6225=CARTESIAN_POINT('Origin',(-345.806600225884,-46.3932528566243,-10.452702172532));
#6226=CARTESIAN_POINT('',(-302.746065787741,-46.3932528566243,34.869092707753));
#6227=CARTESIAN_POINT('Origin',(-302.920094064468,-46.3932528566243,32.8766785542951));
#6228=CARTESIAN_POINT('',(-177.825971723273,-46.3932528566243,23.9578930152019));
#6229=CARTESIAN_POINT('',(-177.825971723273,-46.3932528566243,23.9578930152019));
#6230=CARTESIAN_POINT('',(-176.,-46.3932528566243,21.9654788617441));
#6231=CARTESIAN_POINT('Origin',(-178.,-46.3932528566243,21.9654788617441));
#6232=CARTESIAN_POINT('',(-176.,-46.3932528566243,9.72090701162177));
#6233=CARTESIAN_POINT('',(-176.,-46.3932528566243,21.9654788617441));
#6234=CARTESIAN_POINT('',(-175.,-46.3932528566243,8.72090701162177));
#6235=CARTESIAN_POINT('Origin',(-175.,-46.3932528566243,9.72090701162177));
#6236=CARTESIAN_POINT('',(-156.8,-46.3932528566243,8.72090701162183));
#6237=CARTESIAN_POINT('',(-175.,-46.3932528566243,8.72090701162177));
#6238=CARTESIAN_POINT('',(-155.8,-46.3932528566243,9.72090701162179));
#6239=CARTESIAN_POINT('Origin',(-156.8,-46.3932528566243,9.72090701162177));
#6240=CARTESIAN_POINT('',(-155.8,-46.3932528566243,31.5528496364886));
#6241=CARTESIAN_POINT('',(-155.8,-46.3932528566243,9.72090701162179));
#6242=CARTESIAN_POINT('',(-156.753895233392,-46.3932528566243,32.5517862463373));
#6243=CARTESIAN_POINT('Origin',(-156.8,-46.3932528566243,31.5528496364886));
#6244=CARTESIAN_POINT('',(-174.953895233392,-46.3932528566243,33.3917862463373));
#6245=CARTESIAN_POINT('',(-174.953895233392,-46.3932528566243,33.3917862463373));
#6246=CARTESIAN_POINT('',(-176.,-46.3932528566243,32.3928496364886));
#6247=CARTESIAN_POINT('Origin',(-175.,-46.3932528566243,32.3928496364886));
#6248=CARTESIAN_POINT('',(-176.,-46.3932528566243,32.2254702700578));
#6249=CARTESIAN_POINT('',(-176.,-46.3932528566243,32.3928496364886));
#6250=CARTESIAN_POINT('',(-178.174028276727,-46.3932528566243,30.2330561165999));
#6251=CARTESIAN_POINT('Origin',(-178.,-46.3932528566243,32.2254702700579));
#6252=CARTESIAN_POINT('',(-319.087014138364,-46.3932528566243,42.5411618663159));
#6253=CARTESIAN_POINT('',(-178.174028276727,-46.3932528566243,30.2330561165999));
#6254=CARTESIAN_POINT('',(-320.,-46.3932528566243,43.5373689430449));
#6255=CARTESIAN_POINT('Origin',(-319.,-46.3932528566243,43.5373689430449));
#6256=CARTESIAN_POINT('',(-320.,-46.3932528566243,45.6659963356219));
#6257=CARTESIAN_POINT('',(-320.,-46.3932528566243,43.5373689430449));
#6258=CARTESIAN_POINT('',(-320.953895233392,-46.3932528566243,46.6649329454706));
#6259=CARTESIAN_POINT('Origin',(-321.,-46.3932528566243,45.6659963356218));
#6260=CARTESIAN_POINT('',(-331.953895233392,-46.3932528566243,47.1726252531629));
#6261=CARTESIAN_POINT('',(-331.953895233392,-46.3932528566243,47.1726252531629));
#6262=CARTESIAN_POINT('',(-333.,-46.3932528566243,46.1736886433142));
#6263=CARTESIAN_POINT('Origin',(-332.,-46.3932528566243,46.1736886433142));
#6264=CARTESIAN_POINT('',(-333.,-46.3932528566243,11.7209070116218));
#6265=CARTESIAN_POINT('',(-333.,-46.3932528566243,11.7209070116218));
#6266=CARTESIAN_POINT('',(-330.,-46.3932528566243,8.72090701162179));
#6267=CARTESIAN_POINT('Origin',(-330.,-46.3932528566243,11.7209070116218));
#6268=CARTESIAN_POINT('',(-323.,-46.3932528566243,8.72090701162177));
#6269=CARTESIAN_POINT('',(-323.,-46.3932528566243,8.72090701162177));
#6270=CARTESIAN_POINT('Origin',(-323.,-46.3932528566243,5.72090701162178));
#6271=CARTESIAN_POINT('',(-166.9,-46.3932528566243,14.2709070116218));
#6272=CARTESIAN_POINT('',(-172.48586385441,-46.3932528566243,17.4959070116218));
#6273=CARTESIAN_POINT('',(-166.9,-46.3932528566243,14.2709070116218));
#6274=CARTESIAN_POINT('',(-172.48586385441,-46.3932528566243,23.9459070116218));
#6275=CARTESIAN_POINT('',(-172.48586385441,-46.3932528566243,17.4959070116218));
#6276=CARTESIAN_POINT('',(-166.9,-46.3932528566243,27.1709070116218));
#6277=CARTESIAN_POINT('',(-172.48586385441,-46.3932528566243,23.9459070116218));
#6278=CARTESIAN_POINT('',(-161.31413614559,-46.3932528566243,23.9459070116218));
#6279=CARTESIAN_POINT('',(-166.9,-46.3932528566243,27.1709070116218));
#6280=CARTESIAN_POINT('',(-161.31413614559,-46.3932528566243,17.4959070116218));
#6281=CARTESIAN_POINT('',(-161.31413614559,-46.3932528566243,23.9459070116218));
#6282=CARTESIAN_POINT('',(-161.31413614559,-46.3932528566243,17.4959070116218));
#6283=CARTESIAN_POINT('',(-327.48586385441,-46.3932528566243,23.9459070116218));
#6284=CARTESIAN_POINT('',(-321.9,-46.3932528566243,27.1709070116218));
#6285=CARTESIAN_POINT('',(-327.48586385441,-46.3932528566243,23.9459070116218));
#6286=CARTESIAN_POINT('',(-316.31413614559,-46.3932528566243,23.9459070116218));
#6287=CARTESIAN_POINT('',(-321.9,-46.3932528566243,27.1709070116218));
#6288=CARTESIAN_POINT('',(-316.31413614559,-46.3932528566243,17.4959070116218));
#6289=CARTESIAN_POINT('',(-316.31413614559,-46.3932528566243,23.9459070116218));
#6290=CARTESIAN_POINT('',(-321.9,-46.3932528566243,14.2709070116218));
#6291=CARTESIAN_POINT('',(-316.31413614559,-46.3932528566243,17.4959070116218));
#6292=CARTESIAN_POINT('',(-327.48586385441,-46.3932528566243,17.4959070116218));
#6293=CARTESIAN_POINT('',(-321.9,-46.3932528566243,14.2709070116218));
#6294=CARTESIAN_POINT('',(-327.48586385441,-46.3932528566243,17.4959070116218));
#6295=CARTESIAN_POINT('Origin',(-78.3,13.4,0.));
#6296=CARTESIAN_POINT('',(-78.3,4.51504440784612,0.));
#6297=CARTESIAN_POINT('',(-78.3,4.51504440784612,0.));
#6298=CARTESIAN_POINT('',(-78.2999999999999,2.99999999999999,0.));
#6299=CARTESIAN_POINT('',(-78.3,13.4,0.));
#6300=CARTESIAN_POINT('',(-78.2999999999999,2.99999999999999,0.));
#6301=CARTESIAN_POINT('Origin',(-86.0248944653274,4.51504440784612,3.));
#6302=CARTESIAN_POINT('',(-172.903300112942,4.51504440784609,0.));
#6303=CARTESIAN_POINT('Origin',(-78.3,4.51504440784612,3.));
#6304=CARTESIAN_POINT('Origin',(7.77844948353745E-15,-19.092920065877,8.75336955620386));
#6305=CARTESIAN_POINT('',(3.35712758447271E-15,-19.092920065877,3.));
#6306=CARTESIAN_POINT('',(1.00606706173708E-15,-16.092920065877,3.));
#6307=CARTESIAN_POINT('',(3.35712758447271E-15,-19.092920065877,3.));
#6308=CARTESIAN_POINT('',(5.70494471695811E-15,-16.092920065877,8.75336955620387));
#6309=CARTESIAN_POINT('',(5.42738896080182E-15,-16.092920065877,8.75336955620386));
#6310=CARTESIAN_POINT('',(7.77844948353745E-15,-19.092920065877,8.75336955620386));
#6311=CARTESIAN_POINT('',(7.77844948353745E-15,-19.092920065877,8.75336955620386));
#6312=CARTESIAN_POINT('',(7.77844948353745E-15,-19.092920065877,8.75336955620386));
#6313=CARTESIAN_POINT('Origin',(-8.5,-22.092920065877,3.));
#6314=CARTESIAN_POINT('',(-17.,-19.092920065877,3.));
#6315=CARTESIAN_POINT('',(-172.903300112942,-19.0929200658771,3.));
#6316=CARTESIAN_POINT('',(-17.,-22.092920065877,0.));
#6317=CARTESIAN_POINT('Origin',(-17.,-22.092920065877,3.));
#6318=CARTESIAN_POINT('',(-8.68139725592746E-17,-22.092920065877,0.));
#6319=CARTESIAN_POINT('',(-172.903300112942,-22.0929200658771,0.));
#6320=CARTESIAN_POINT('Origin',(5.55111512312578E-15,-22.092920065877,3.));
#6321=CARTESIAN_POINT('Origin',(-17.,-16.092920065877,3.));
#6322=CARTESIAN_POINT('',(-17.,-16.092920065877,3.));
#6323=CARTESIAN_POINT('',(-17.,-19.092920065877,3.));
#6324=CARTESIAN_POINT('Origin',(-17.,-22.092920065877,3.));
#6325=CARTESIAN_POINT('',(-17.,-22.092920065877,0.));
#6326=CARTESIAN_POINT('Origin',(-345.806600225884,-16.0929200658772,-15.4193108817229));
#6327=CARTESIAN_POINT('',(-172.903300112942,-16.0929200658771,3.));
#6328=CARTESIAN_POINT('',(-17.,-16.092920065877,11.1357224973804));
#6329=CARTESIAN_POINT('',(-17.,-16.092920065877,11.1357224973804));
#6330=CARTESIAN_POINT('',(-15.8431425078589,-16.092920065877,12.1233437441947));
#6331=CARTESIAN_POINT('Origin',(-16.,-16.092920065877,11.1357224973803));
#6332=CARTESIAN_POINT('',(-0.843142507858901,-16.092920065877,9.74099080301825));
#6333=CARTESIAN_POINT('',(-15.8431425078589,-16.092920065877,12.1233437441947));
#6334=CARTESIAN_POINT('Origin',(-0.999999999999996,-16.092920065877,8.75336955620386));
#6335=CARTESIAN_POINT('Origin',(-345.806600225884,-19.0929200658772,-15.4193108817229));
#6336=CARTESIAN_POINT('',(-0.843142507858885,-19.092920065877,9.74099080301825));
#6337=CARTESIAN_POINT('Origin',(-0.999999999999994,-19.092920065877,8.75336955620386));
#6338=CARTESIAN_POINT('',(-15.8431425078589,-19.092920065877,12.1233437441947));
#6339=CARTESIAN_POINT('',(-15.8431425078589,-19.092920065877,12.1233437441947));
#6340=CARTESIAN_POINT('',(-17.,-19.092920065877,11.1357224973804));
#6341=CARTESIAN_POINT('Origin',(-16.,-19.092920065877,11.1357224973803));
#6342=CARTESIAN_POINT('',(-17.,-19.092920065877,11.1357224973804));
#6343=CARTESIAN_POINT('Origin',(-17.,-23.9642400437689,0.));
#6344=CARTESIAN_POINT('',(-17.,-23.9642400437689,0.));
#6345=CARTESIAN_POINT('',(-17.,-23.9642400437689,0.));
#6346=CARTESIAN_POINT('',(-17.,-7.17135743674268,0.));
#6347=CARTESIAN_POINT('Origin',(-345.806600225884,-17.8930618707516,-33.0392841219141));
#6348=CARTESIAN_POINT('',(-92.3,19.1484681366286,11.1050923395674));
#6349=CARTESIAN_POINT('Origin',(-92.3,18.505680526942,10.3390478964484));
#6350=CARTESIAN_POINT('',(-75.3,19.1484681366286,11.1050923395674));
#6351=CARTESIAN_POINT('',(-75.3,19.1484681366286,11.1050923395674));
#6352=CARTESIAN_POINT('',(-74.8,18.8270743317853,10.7220701180079));
#6353=CARTESIAN_POINT('Origin',(-75.3,18.8270743317853,10.7220701180079));
#6354=CARTESIAN_POINT('',(-74.8,18.505680526942,10.3390478964484));
#6355=CARTESIAN_POINT('',(-74.8,18.8270743317853,10.7220701180079));
#6356=CARTESIAN_POINT('',(-74.3,18.1842867220988,9.9560256748889));
#6357=CARTESIAN_POINT('Origin',(-74.3,18.505680526942,10.3390478964484));
#6358=CARTESIAN_POINT('',(-70.3,18.1842867220988,9.9560256748889));
#6359=CARTESIAN_POINT('',(-70.3,18.1842867220988,9.9560256748889));
#6360=CARTESIAN_POINT('',(-69.8,18.505680526942,10.3390478964484));
#6361=CARTESIAN_POINT('Origin',(-70.3,18.505680526942,10.3390478964484));
#6362=CARTESIAN_POINT('',(-69.8,18.8270743317853,10.7220701180079));
#6363=CARTESIAN_POINT('',(-69.8,18.8270743317853,10.7220701180079));
#6364=CARTESIAN_POINT('',(-69.3,19.1484681366286,11.1050923395674));
#6365=CARTESIAN_POINT('Origin',(-69.3,18.8270743317853,10.7220701180079));
#6366=CARTESIAN_POINT('',(-67.3,19.1484681366286,11.1050923395674));
#6367=CARTESIAN_POINT('',(-67.3,19.1484681366286,11.1050923395674));
#6368=CARTESIAN_POINT('',(-66.3,18.505680526942,10.3390478964484));
#6369=CARTESIAN_POINT('Origin',(-67.3,18.505680526942,10.3390478964484));
#6370=CARTESIAN_POINT('',(-66.3,13.3633796494497,4.21069235149656));
#6371=CARTESIAN_POINT('',(-66.3,13.3633796494497,4.21069235149656));
#6372=CARTESIAN_POINT('',(-67.3,12.7205920397632,3.44464790837758));
#6373=CARTESIAN_POINT('Origin',(-67.3,13.3633796494497,4.21069235149656));
#6374=CARTESIAN_POINT('',(-69.3,12.7205920397632,3.44464790837758));
#6375=CARTESIAN_POINT('',(-69.3,12.7205920397632,3.44464790837758));
#6376=CARTESIAN_POINT('',(-69.8,13.0419858446064,3.82767012993707));
#6377=CARTESIAN_POINT('Origin',(-69.3,13.0419858446064,3.82767012993707));
#6378=CARTESIAN_POINT('',(-69.8,13.3633796494497,4.21069235149656));
#6379=CARTESIAN_POINT('',(-69.8,13.3633796494497,4.21069235149656));
#6380=CARTESIAN_POINT('',(-70.3,13.684773454293,4.59371457305605));
#6381=CARTESIAN_POINT('Origin',(-70.3,13.3633796494497,4.21069235149656));
#6382=CARTESIAN_POINT('',(-74.3,13.684773454293,4.59371457305605));
#6383=CARTESIAN_POINT('',(-74.3,13.684773454293,4.59371457305605));
#6384=CARTESIAN_POINT('',(-74.8,13.3633796494497,4.21069235149656));
#6385=CARTESIAN_POINT('Origin',(-74.3,13.3633796494497,4.21069235149656));
#6386=CARTESIAN_POINT('',(-74.8,13.0419858446064,3.82767012993708));
#6387=CARTESIAN_POINT('',(-74.8,13.3633796494497,4.21069235149656));
#6388=CARTESIAN_POINT('',(-75.3,12.7205920397632,3.44464790837759));
#6389=CARTESIAN_POINT('Origin',(-75.3,13.0419858446064,3.82767012993707));
#6390=CARTESIAN_POINT('',(-77.8,12.7205920397632,3.44464790837759));
#6391=CARTESIAN_POINT('',(-77.8,12.7205920397632,3.44464790837758));
#6392=CARTESIAN_POINT('Origin',(-77.8,12.3991982349199,3.0616256868181));
#6393=CARTESIAN_POINT('Origin',(3.33066907387547E-15,-19.092920065877,3.));
#6394=CARTESIAN_POINT('',(-8.68139725592754E-17,-22.092920065877,0.));
#6395=CARTESIAN_POINT('Origin',(5.55111512312578E-15,-22.092920065877,3.));
#6396=CARTESIAN_POINT('Origin',(-327.48586385441,-43.3932528566243,17.4959070116218));
#6397=CARTESIAN_POINT('',(-327.48586385441,-43.3932528566243,23.9459070116218));
#6398=CARTESIAN_POINT('',(-327.48586385441,-43.3932528566243,17.4959070116218));
#6399=CARTESIAN_POINT('Origin',(-321.9,-43.3932528566243,14.2709070116218));
#6400=CARTESIAN_POINT('',(-321.9,-43.3932528566243,14.2709070116218));
#6401=CARTESIAN_POINT('Origin',(-316.31413614559,-43.3932528566243,17.4959070116218));
#6402=CARTESIAN_POINT('',(-316.31413614559,-43.3932528566243,17.4959070116218));
#6403=CARTESIAN_POINT('Origin',(-316.31413614559,-43.3932528566243,23.9459070116218));
#6404=CARTESIAN_POINT('',(-316.31413614559,-43.3932528566243,23.9459070116218));
#6405=CARTESIAN_POINT('Origin',(-321.9,-43.3932528566243,27.1709070116218));
#6406=CARTESIAN_POINT('',(-321.9,-43.3932528566243,27.1709070116218));
#6407=CARTESIAN_POINT('Origin',(-327.48586385441,-43.3932528566243,23.9459070116218));
#6408=CARTESIAN_POINT('Origin',(-161.31413614559,-43.3932528566243,17.4959070116218));
#6409=CARTESIAN_POINT('',(-166.9,-43.3932528566243,14.2709070116218));
#6410=CARTESIAN_POINT('',(-161.31413614559,-43.3932528566243,17.4959070116218));
#6411=CARTESIAN_POINT('Origin',(-161.31413614559,-43.3932528566243,23.9459070116218));
#6412=CARTESIAN_POINT('',(-161.31413614559,-43.3932528566243,23.9459070116218));
#6413=CARTESIAN_POINT('Origin',(-166.9,-43.3932528566243,27.1709070116218));
#6414=CARTESIAN_POINT('',(-166.9,-43.3932528566243,27.1709070116218));
#6415=CARTESIAN_POINT('Origin',(-172.48586385441,-43.3932528566243,23.9459070116218));
#6416=CARTESIAN_POINT('',(-172.48586385441,-43.3932528566243,23.9459070116218));
#6417=CARTESIAN_POINT('Origin',(-172.48586385441,-43.3932528566243,17.4959070116218));
#6418=CARTESIAN_POINT('',(-172.48586385441,-43.3932528566243,17.4959070116218));
#6419=CARTESIAN_POINT('Origin',(-166.9,-43.3932528566243,14.2709070116218));
#6420=CARTESIAN_POINT('Origin',(-175.,-43.3932528566243,32.3928496364886));
#6421=CARTESIAN_POINT('',(-176.,-43.3932528566243,32.3928496364886));
#6422=CARTESIAN_POINT('',(-174.953895233392,-43.3932528566243,33.3917862463373));
#6423=CARTESIAN_POINT('Origin',(-156.753895233392,-43.3932528566243,32.5517862463373));
#6424=CARTESIAN_POINT('',(-156.753895233392,-43.3932528566243,32.5517862463373));
#6425=CARTESIAN_POINT('Origin',(-156.8,-43.3932528566243,31.5528496364886));
#6426=CARTESIAN_POINT('',(-155.8,-43.3932528566243,31.5528496364886));
#6427=CARTESIAN_POINT('Origin',(-155.8,-43.3932528566243,9.72090701162179));
#6428=CARTESIAN_POINT('',(-155.8,-43.3932528566243,9.72090701162179));
#6429=CARTESIAN_POINT('Origin',(-156.8,-43.3932528566243,9.72090701162177));
#6430=CARTESIAN_POINT('',(-156.8,-43.3932528566243,8.72090701162183));
#6431=CARTESIAN_POINT('Origin',(-175.,-43.3932528566243,8.72090701162177));
#6432=CARTESIAN_POINT('',(-175.,-43.3932528566243,8.72090701162177));
#6433=CARTESIAN_POINT('Origin',(-175.,-43.3932528566243,9.72090701162177));
#6434=CARTESIAN_POINT('',(-176.,-43.3932528566243,9.72090701162177));
#6435=CARTESIAN_POINT('Origin',(-176.,-43.3932528566243,21.9654788617441));
#6436=CARTESIAN_POINT('',(-176.,-43.3932528566243,21.9654788617441));
#6437=CARTESIAN_POINT('Origin',(-178.,-43.3932528566243,21.9654788617441));
#6438=CARTESIAN_POINT('',(-177.825971723273,-43.3932528566243,23.9578930152019));
#6439=CARTESIAN_POINT('Origin',(-302.746065787741,-43.3932528566243,34.869092707753));
#6440=CARTESIAN_POINT('',(-302.746065787741,-43.3932528566243,34.869092707753));
#6441=CARTESIAN_POINT('Origin',(-302.920094064468,-43.3932528566243,32.8766785542951));
#6442=CARTESIAN_POINT('Origin',(-304.920094064468,-39.3999999999998,0.));
#6443=CARTESIAN_POINT('',(-304.920094064468,-39.3999999999998,0.));
#6444=CARTESIAN_POINT('',(-304.920094064468,-39.3999999999998,0.));
#6445=CARTESIAN_POINT('',(-304.920094064468,-39.3999999999998,0.));
#6446=CARTESIAN_POINT('Origin',(-303.920094064468,-39.3999999999998,0.));
#6447=CARTESIAN_POINT('',(-303.920094064468,-38.3999999999998,0.));
#6448=CARTESIAN_POINT('Origin',(-303.920094064468,-39.3999999999998,0.));
#6449=CARTESIAN_POINT('',(-303.920094064468,-38.3999999999998,0.));
#6450=CARTESIAN_POINT('Origin',(-282.,-38.4,0.));
#6451=CARTESIAN_POINT('',(-282.,-38.4,0.));
#6452=CARTESIAN_POINT('',(-282.,-38.4,0.));
#6453=CARTESIAN_POINT('',(-282.,-38.4,0.));
#6454=CARTESIAN_POINT('Origin',(-282.,-37.4,0.));
#6455=CARTESIAN_POINT('',(-281.,-37.4,0.));
#6456=CARTESIAN_POINT('Origin',(-282.,-37.4,0.));
#6457=CARTESIAN_POINT('',(-281.,-37.4,0.));
#6458=CARTESIAN_POINT('Origin',(-281.,-17.,0.));
#6459=CARTESIAN_POINT('',(-281.,-17.,0.));
#6460=CARTESIAN_POINT('',(-281.,-17.,0.));
#6461=CARTESIAN_POINT('',(-281.,-17.,0.));
#6462=CARTESIAN_POINT('Origin',(-280.,-17.,0.));
#6463=CARTESIAN_POINT('',(-280.,-16.,0.));
#6464=CARTESIAN_POINT('Origin',(-280.,-17.,0.));
#6465=CARTESIAN_POINT('',(-280.,-16.,0.));
#6466=CARTESIAN_POINT('Origin',(-59.4755908269945,-16.,0.));
#6467=CARTESIAN_POINT('',(-59.4755908269945,-16.,0.));
#6468=CARTESIAN_POINT('',(-280.,-16.,0.));
#6469=CARTESIAN_POINT('',(-59.4755908269945,-16.,0.));
#6470=CARTESIAN_POINT('Origin',(-59.4755908269946,-31.,0.));
#6471=CARTESIAN_POINT('',(-48.3315905134971,-20.9595190845872,0.));
#6472=CARTESIAN_POINT('Origin',(-59.4755908269946,-31.,0.));
#6473=CARTESIAN_POINT('',(-48.3315905134971,-20.9595190845872,0.));
#6474=CARTESIAN_POINT('Origin',(-33.122242853041,-37.8404809154128,0.));
#6475=CARTESIAN_POINT('',(-33.122242853041,-37.8404809154128,0.));
#6476=CARTESIAN_POINT('',(-48.3315905134971,-20.9595190845872,0.));
#6477=CARTESIAN_POINT('',(-33.122242853041,-37.8404809154128,0.));
#6478=CARTESIAN_POINT('Origin',(-21.9782425395435,-27.7999999999999,0.));
#6479=CARTESIAN_POINT('',(-21.9782425395435,-42.8,0.));
#6480=CARTESIAN_POINT('Origin',(-21.9782425395435,-27.7999999999999,0.));
#6481=CARTESIAN_POINT('',(-21.9782425395435,-42.8,0.));
#6482=CARTESIAN_POINT('Origin',(-15.,-42.8,0.));
#6483=CARTESIAN_POINT('',(-15.,-42.8,0.));
#6484=CARTESIAN_POINT('',(-15.,-42.8,0.));
#6485=CARTESIAN_POINT('',(-15.,-42.8,0.));
#6486=CARTESIAN_POINT('Origin',(-15.,-27.8,0.));
#6487=CARTESIAN_POINT('',(-4.44089209850063E-15,-27.8,0.));
#6488=CARTESIAN_POINT('Origin',(-15.,-27.8,0.));
#6489=CARTESIAN_POINT('',(-4.44089209850063E-15,-27.8,0.));
#6490=CARTESIAN_POINT('Origin',(-2.77555756156289E-16,-9.55371037791915,
0.));
#6491=CARTESIAN_POINT('',(-2.77555756156289E-16,-9.55371037791915,0.));
#6492=CARTESIAN_POINT('Origin',(-0.999999999999994,-19.092920065877,8.75336955620386));
#6493=CARTESIAN_POINT('',(-0.843142507858885,-19.092920065877,9.74099080301825));
#6494=CARTESIAN_POINT('Origin',(-15.8431425078589,-19.092920065877,12.1233437441947));
#6495=CARTESIAN_POINT('',(-15.8431425078589,-19.092920065877,12.1233437441947));
#6496=CARTESIAN_POINT('Origin',(-16.,-19.092920065877,11.1357224973803));
#6497=CARTESIAN_POINT('',(-17.,-19.092920065877,11.1357224973804));
#6498=CARTESIAN_POINT('Origin',(-17.,-19.092920065877,-5.65716010964591));
#6499=CARTESIAN_POINT('Origin',(-18.,-23.9642400437689,0.));
#6500=CARTESIAN_POINT('',(-18.0714285714286,-24.961685761181,0.));
#6501=CARTESIAN_POINT('Origin',(-18.,-23.9642400437689,0.));
#6502=CARTESIAN_POINT('',(-18.0714285714286,-24.961685761181,0.));
#6503=CARTESIAN_POINT('Origin',(-17.,-10.,0.));
#6504=CARTESIAN_POINT('',(-28.1440003134975,-20.0404809154127,0.));
#6505=CARTESIAN_POINT('Origin',(-17.,-10.,0.));
#6506=CARTESIAN_POINT('',(-28.1440003134975,-20.0404809154127,0.));
#6507=CARTESIAN_POINT('Origin',(-41.7315905134971,-4.95951908458727,0.));
#6508=CARTESIAN_POINT('',(-41.7315905134971,-4.95951908458727,0.));
#6509=CARTESIAN_POINT('',(-41.7315905134971,-4.95951908458727,0.));
#6510=CARTESIAN_POINT('',(-41.7315905134971,-4.95951908458727,0.));
#6511=CARTESIAN_POINT('Origin',(-52.8755908269946,-15.,0.));
#6512=CARTESIAN_POINT('',(-52.8755908269946,2.22044604925031E-15,0.));
#6513=CARTESIAN_POINT('Origin',(-52.8755908269946,-15.,0.));
#6514=CARTESIAN_POINT('',(-52.8755908269946,2.22044604925031E-15,0.));
#6515=CARTESIAN_POINT('Origin',(-75.2999999999999,-4.44089209850063E-15,
0.));
#6516=CARTESIAN_POINT('',(-75.2999999999999,-4.44089209850063E-15,0.));
#6517=CARTESIAN_POINT('',(-75.2999999999999,-4.44089209850063E-15,0.));
#6518=CARTESIAN_POINT('',(-75.2999999999999,-4.44089209850063E-15,0.));
#6519=CARTESIAN_POINT('Origin',(-75.2999999999999,3.00000000000001,0.));
#6520=CARTESIAN_POINT('Origin',(-75.2999999999999,3.00000000000001,0.));
#6521=CARTESIAN_POINT('Origin',(-78.3,6.81317773720305,1.07163717094038));
#6522=CARTESIAN_POINT('Origin',(-77.8,10.101064905563,4.98998851587771));
#6523=CARTESIAN_POINT('',(-77.8,10.4224587104062,5.3730107374372));
#6524=CARTESIAN_POINT('Origin',(-75.3,10.4224587104062,5.3730107374372));
#6525=CARTESIAN_POINT('',(-75.3,10.4224587104062,5.3730107374372));
#6526=CARTESIAN_POINT('Origin',(-75.3,10.7438525152495,5.75603295899669));
#6527=CARTESIAN_POINT('',(-74.8,10.7438525152495,5.75603295899669));
#6528=CARTESIAN_POINT('Origin',(-74.8,11.0652463200928,6.13905518055618));
#6529=CARTESIAN_POINT('',(-74.8,11.0652463200928,6.13905518055618));
#6530=CARTESIAN_POINT('Origin',(-74.3,11.0652463200928,6.13905518055618));
#6531=CARTESIAN_POINT('',(-74.3,11.386640124936,6.52207740211567));
#6532=CARTESIAN_POINT('Origin',(-70.3,11.386640124936,6.52207740211566));
#6533=CARTESIAN_POINT('',(-70.3,11.386640124936,6.52207740211566));
#6534=CARTESIAN_POINT('Origin',(-70.3,11.0652463200928,6.13905518055618));
#6535=CARTESIAN_POINT('',(-69.8,11.0652463200928,6.13905518055618));
#6536=CARTESIAN_POINT('Origin',(-69.8,10.7438525152495,5.75603295899669));
#6537=CARTESIAN_POINT('',(-69.8,10.7438525152495,5.75603295899669));
#6538=CARTESIAN_POINT('Origin',(-69.3,10.7438525152495,5.75603295899669));
#6539=CARTESIAN_POINT('',(-69.3,10.4224587104062,5.3730107374372));
#6540=CARTESIAN_POINT('Origin',(-67.3,10.4224587104062,5.3730107374372));
#6541=CARTESIAN_POINT('',(-67.3,10.4224587104062,5.3730107374372));
#6542=CARTESIAN_POINT('Origin',(-67.3,11.0652463200928,6.13905518055618));
#6543=CARTESIAN_POINT('',(-66.3,11.0652463200928,6.13905518055618));
#6544=CARTESIAN_POINT('Origin',(-66.3,16.2075471975851,12.267410725508));
#6545=CARTESIAN_POINT('',(-66.3,16.2075471975851,12.267410725508));
#6546=CARTESIAN_POINT('Origin',(-67.3,16.2075471975851,12.267410725508));
#6547=CARTESIAN_POINT('',(-67.3,16.8503348072716,13.033455168627));
#6548=CARTESIAN_POINT('Origin',(-69.3,16.8503348072716,13.033455168627));
#6549=CARTESIAN_POINT('',(-69.3,16.8503348072716,13.033455168627));
#6550=CARTESIAN_POINT('Origin',(-69.3,16.5289410024284,12.6504329470675));
#6551=CARTESIAN_POINT('',(-69.8,16.5289410024284,12.6504329470675));
#6552=CARTESIAN_POINT('Origin',(-69.8,16.2075471975851,12.267410725508));
#6553=CARTESIAN_POINT('',(-69.8,16.2075471975851,12.267410725508));
#6554=CARTESIAN_POINT('Origin',(-70.3,16.2075471975851,12.267410725508));
#6555=CARTESIAN_POINT('',(-70.3,15.8861533927418,11.8843885039485));
#6556=CARTESIAN_POINT('Origin',(-74.3,15.8861533927418,11.8843885039485));
#6557=CARTESIAN_POINT('',(-74.3,15.8861533927418,11.8843885039485));
#6558=CARTESIAN_POINT('Origin',(-74.3,16.2075471975851,12.267410725508));
#6559=CARTESIAN_POINT('',(-74.8,16.2075471975851,12.267410725508));
#6560=CARTESIAN_POINT('Origin',(-74.8,16.5289410024284,12.6504329470675));
#6561=CARTESIAN_POINT('',(-74.8,16.5289410024284,12.6504329470675));
#6562=CARTESIAN_POINT('Origin',(-75.3,16.5289410024284,12.6504329470675));
#6563=CARTESIAN_POINT('',(-75.3,16.8503348072716,13.033455168627));
#6564=CARTESIAN_POINT('Origin',(-92.3,16.8503348072716,13.033455168627));
#6565=CARTESIAN_POINT('',(-92.3,16.8503348072716,13.033455168627));
#6566=CARTESIAN_POINT('Origin',(-92.3,16.2075471975851,12.267410725508));
#6567=CARTESIAN_POINT('Origin',(-93.3,3.,0.));
#6568=CARTESIAN_POINT('',(-93.3,3.,0.));
#6569=CARTESIAN_POINT('',(-93.3,3.,0.));
#6570=CARTESIAN_POINT('',(-93.3,22.9,0.));
#6571=CARTESIAN_POINT('Origin',(-96.3,3.00000000000002,0.));
#6572=CARTESIAN_POINT('',(-96.3,0.,0.));
#6573=CARTESIAN_POINT('Origin',(-96.3,3.00000000000002,0.));
#6574=CARTESIAN_POINT('',(-96.3,0.,0.));
#6575=CARTESIAN_POINT('Origin',(-240.3,0.,0.));
#6576=CARTESIAN_POINT('',(-240.3,-1.11022302462516E-14,0.));
#6577=CARTESIAN_POINT('',(-96.3,0.,0.));
#6578=CARTESIAN_POINT('',(-240.3,-1.11022302462516E-14,0.));
#6579=CARTESIAN_POINT('Origin',(-240.3,3.00000000000003,0.));
#6580=CARTESIAN_POINT('Origin',(-240.3,3.00000000000003,0.));
#6581=CARTESIAN_POINT('Origin',(-243.3,6.81317773720303,1.07163717094038));
#6582=CARTESIAN_POINT('Origin',(-315.3,16.8503348072716,13.033455168627));
#6583=CARTESIAN_POINT('Origin',(-315.3,3.,0.));
#6584=CARTESIAN_POINT('',(-315.3,3.,0.));
#6585=CARTESIAN_POINT('',(-315.3,3.,0.));
#6586=CARTESIAN_POINT('',(-315.3,3.,0.));
#6587=CARTESIAN_POINT('Origin',(-318.3,3.00000000000001,0.));
#6588=CARTESIAN_POINT('Origin',(-318.3,3.00000000000001,0.));
#6589=CARTESIAN_POINT('Origin',(-331.107079934123,2.33146835171283E-14,
3.00000000000003));
#6590=CARTESIAN_POINT('Origin',(-331.107079934123,3.,9.70707993412312));
#6591=CARTESIAN_POINT('',(-331.107079934123,3.,12.7070799341231));
#6592=CARTESIAN_POINT('Origin',(-331.107079934123,11.,12.7070799341231));
#6593=CARTESIAN_POINT('',(-331.107079934123,11.,12.7070799341231));
#6594=CARTESIAN_POINT('Origin',(-331.107079934123,11.,15.7070799341231));
#6595=CARTESIAN_POINT('',(-331.107079934123,14.,15.7070799341231));
#6596=CARTESIAN_POINT('Origin',(-331.107079934123,14.,39.7070799341232));
#6597=CARTESIAN_POINT('',(-331.107079934123,14.,39.7070799341232));
#6598=CARTESIAN_POINT('Origin',(-331.107079934123,13.5,39.7070799341232));
#6599=CARTESIAN_POINT('',(-331.107079934123,13.5,40.2070799341232));
#6600=CARTESIAN_POINT('Origin',(-331.107079934123,2.49999999999999,40.2070799341231));
#6601=CARTESIAN_POINT('',(-331.107079934123,2.49999999999999,40.2070799341231));
#6602=CARTESIAN_POINT('Origin',(-331.107079934123,2.49999999999987,39.7070799341233));
#6603=CARTESIAN_POINT('',(-331.107079934123,2.00000000000004,39.7070799341231));
#6604=CARTESIAN_POINT('Origin',(-331.107079934123,2.,25.7070799341231));
#6605=CARTESIAN_POINT('',(-331.107079934123,2.,25.7070799341231));
#6606=CARTESIAN_POINT('Origin',(-331.107079934123,1.,25.7070799341231));
#6607=CARTESIAN_POINT('Origin',(-542.521239802369,0.,3.0000000000031));
#6608=CARTESIAN_POINT('',(-544.636722722426,2.88657986402541E-14,0.133359189701139));
#6609=CARTESIAN_POINT('Ctrl Pts',(-544.636722722426,2.88657986402541E-14,
0.133359189701149));
#6610=CARTESIAN_POINT('Ctrl Pts',(-544.932392283379,2.88601031141256E-14,
0.0421287935728221));
#6611=CARTESIAN_POINT('Ctrl Pts',(-545.242026138956,0.100876738244207,3.16460864747493E-12));
#6612=CARTESIAN_POINT('Ctrl Pts',(-545.521239802369,0.292906040926448,3.1685765122802E-12));
#6613=CARTESIAN_POINT('Ctrl Pts',(-544.341883695779,2.88657986402541E-14,
-0.82218774706619));
#6614=CARTESIAN_POINT('Ctrl Pts',(-544.736109777048,2.88601031141256E-14,
-0.943828275237293));
#6615=CARTESIAN_POINT('Ctrl Pts',(-545.148954917818,0.100876738244207,-0.999999999996837));
#6616=CARTESIAN_POINT('Ctrl Pts',(-545.521239802369,0.292906040926448,-0.999999999996832));
#6617=CARTESIAN_POINT('Ctrl Pts',(-544.047044669131,2.88657986402541E-14,
-1.77773468383353));
#6618=CARTESIAN_POINT('Ctrl Pts',(-544.539827270718,2.88601031141256E-14,
-1.92978534404741));
#6619=CARTESIAN_POINT('Ctrl Pts',(-545.05588369668,0.100876738244207,-1.99999999999684));
#6620=CARTESIAN_POINT('Ctrl Pts',(-545.521239802369,0.292906040926448,-1.99999999999683));
#6621=CARTESIAN_POINT('Ctrl Pts',(-543.752205642483,2.88657986402541E-14,
-2.73328162060087));
#6622=CARTESIAN_POINT('Ctrl Pts',(-544.343544764388,2.88601031141256E-14,
-2.91574241285752));
#6623=CARTESIAN_POINT('Ctrl Pts',(-544.962812475542,0.100876738244207,-2.99999999999684));
#6624=CARTESIAN_POINT('Ctrl Pts',(-545.521239802369,0.292906040926448,-2.99999999999683));
#6625=CARTESIAN_POINT('Origin',(-547.228319736492,6.81317773720297,1.07163717094359));
#6626=CARTESIAN_POINT('Origin',(-550.228319736492,14.921971978212,10.7353218392733));
#6627=CARTESIAN_POINT('',(-550.228319736492,16.8503348072716,13.0334551686303));
#6628=CARTESIAN_POINT('Origin',(-587.928319736492,16.8503348072716,13.0334551686308));
#6629=CARTESIAN_POINT('',(-587.928319736492,16.8503348072716,13.0334551686308));
#6630=CARTESIAN_POINT('Origin',(-587.928319736492,16.5289410024284,12.6504329470715));
#6631=CARTESIAN_POINT('',(-588.428319736492,16.5289410024283,12.6504329470713));
#6632=CARTESIAN_POINT('Origin',(-588.428319736492,16.2075471975851,12.2674107255119));
#6633=CARTESIAN_POINT('',(-588.428319736492,16.2075471975851,12.2674107255119));
#6634=CARTESIAN_POINT('Origin',(-588.928319736492,16.2075471975851,12.2674107255119));
#6635=CARTESIAN_POINT('',(-588.928319736492,15.8861533927418,11.8843885039524));
#6636=CARTESIAN_POINT('Origin',(-592.928319736492,15.8861533927418,11.8843885039525));
#6637=CARTESIAN_POINT('',(-592.928319736492,15.8861533927418,11.8843885039524));
#6638=CARTESIAN_POINT('Origin',(-592.928319736492,16.2075471975851,12.267410725512));
#6639=CARTESIAN_POINT('',(-593.428319736492,16.2075471975851,12.267410725512));
#6640=CARTESIAN_POINT('Origin',(-593.428319736492,16.5289410024283,12.6504329470715));
#6641=CARTESIAN_POINT('',(-593.428319736492,16.5289410024283,12.6504329470715));
#6642=CARTESIAN_POINT('Origin',(-593.928319736492,16.5289410024283,12.6504329470714));
#6643=CARTESIAN_POINT('',(-593.928319736492,16.8503348072715,13.0334551686309));
#6644=CARTESIAN_POINT('Origin',(-595.928319736492,16.8503348072716,13.0334551686309));
#6645=CARTESIAN_POINT('',(-595.928319736492,16.8503348072716,13.0334551686309));
#6646=CARTESIAN_POINT('Origin',(-595.928319736492,16.2075471975851,12.2674107255121));
#6647=CARTESIAN_POINT('',(-596.928319736492,16.2075471975851,12.2674107255119));
#6648=CARTESIAN_POINT('Origin',(-596.928319736492,11.0652463200927,6.13905518056015));
#6649=CARTESIAN_POINT('',(-596.928319736492,11.0652463200927,6.13905518056015));
#6650=CARTESIAN_POINT('Origin',(-595.928319736492,11.0652463200927,6.13905518056001));
#6651=CARTESIAN_POINT('',(-595.928319736492,10.4224587104062,5.37301073744111));
#6652=CARTESIAN_POINT('Origin',(-593.928319736492,10.4224587104062,5.37301073744118));
#6653=CARTESIAN_POINT('',(-593.928319736492,10.4224587104062,5.37301073744118));
#6654=CARTESIAN_POINT('Origin',(-593.928319736492,10.7438525152495,5.75603295900066));
#6655=CARTESIAN_POINT('',(-593.428319736492,10.7438525152495,5.75603295900066));
#6656=CARTESIAN_POINT('Origin',(-593.428319736492,11.0652463200927,6.13905518056008));
#6657=CARTESIAN_POINT('',(-593.428319736492,11.0652463200927,6.13905518056008));
#6658=CARTESIAN_POINT('Origin',(-592.928319736492,11.0652463200927,6.13905518056008));
#6659=CARTESIAN_POINT('',(-592.928319736492,11.386640124936,6.52207740211956));
#6660=CARTESIAN_POINT('Origin',(-588.928319736492,11.386640124936,6.52207740211949));
#6661=CARTESIAN_POINT('',(-588.928319736492,11.3866401249359,6.52207740211949));
#6662=CARTESIAN_POINT('Origin',(-588.928319736492,11.0652463200927,6.13905518056001));
#6663=CARTESIAN_POINT('',(-588.428319736492,11.0652463200927,6.13905518055994));
#6664=CARTESIAN_POINT('Origin',(-588.428319736492,10.7438525152495,5.75603295900052));
#6665=CARTESIAN_POINT('',(-588.428319736492,10.7438525152495,5.75603295900052));
#6666=CARTESIAN_POINT('Origin',(-587.928319736492,10.7438525152495,5.75603295900052));
#6667=CARTESIAN_POINT('',(-587.928319736492,10.4224587104062,5.37301073744104));
#6668=CARTESIAN_POINT('Origin',(-584.928319736492,10.4224587104062,5.37301073744104));
#6669=CARTESIAN_POINT('Origin',(-584.928319736492,-25.9,3.69482222595252E-12));
#6670=CARTESIAN_POINT('',(-584.928319736492,-25.9,3.69482222595252E-12));
#6671=CARTESIAN_POINT('Origin',(-585.928319736492,-25.9000000000003,3.76587649952853E-12));
#6672=CARTESIAN_POINT('Origin',(-589.635399670615,-26.8999999999999,3.00000000000381));
#6673=CARTESIAN_POINT('Origin',(-589.635399670615,-27.9,43.1869470525609));
#6674=CARTESIAN_POINT('',(-589.635399670615,-27.8169545201463,44.1834928108058));
#6675=CARTESIAN_POINT('Origin',(-589.635399670615,-37.8169545201463,45.0168261441392));
#6676=CARTESIAN_POINT('',(-589.635399670615,-37.8169545201463,45.0168261441392));
#6677=CARTESIAN_POINT('Origin',(-589.635399670615,-37.9,44.0202803858942));
#6678=CARTESIAN_POINT('Origin',(-564.449534553115,-38.9,3.48165940522449E-12));
#6679=CARTESIAN_POINT('',(-564.449534553115,-38.9,3.48165940522449E-12));
#6680=CARTESIAN_POINT('Origin',(-564.449534553116,-37.9,3.48165940522449E-12));
#6681=CARTESIAN_POINT('',(-563.629798437503,-38.4727413908212,3.48165940522449E-12));
#6682=CARTESIAN_POINT('Origin',(-548.226841035482,-16.4272586091787,3.19744231092045E-12));
#6683=CARTESIAN_POINT('',(-548.226841035482,-16.4272586091788,3.19744231092045E-12));
#6684=CARTESIAN_POINT('Origin',(-547.407104919869,-16.9999999999999,3.19744231092045E-12));
#6685=CARTESIAN_POINT('Origin',(-321.,-16.,0.));
#6686=CARTESIAN_POINT('',(-321.,-16.,0.));
#6687=CARTESIAN_POINT('',(-321.,-16.,0.));
#6688=CARTESIAN_POINT('',(-603.578785183377,-16.,0.));
#6689=CARTESIAN_POINT('Origin',(-321.,-17.,0.));
#6690=CARTESIAN_POINT('Origin',(-321.,-17.,0.));
#6691=CARTESIAN_POINT('Origin',(-320.,-43.3932528566243,3.));
#6692=CARTESIAN_POINT('Origin',(-323.,-43.3932528566243,5.72090701162178));
#6693=CARTESIAN_POINT('',(-323.,-43.3932528566243,8.72090701162177));
#6694=CARTESIAN_POINT('Origin',(-330.,-43.3932528566243,8.72090701162179));
#6695=CARTESIAN_POINT('',(-330.,-43.3932528566243,8.72090701162179));
#6696=CARTESIAN_POINT('Origin',(-330.,-43.3932528566243,11.7209070116218));
#6697=CARTESIAN_POINT('',(-333.,-43.3932528566243,11.7209070116218));
#6698=CARTESIAN_POINT('Origin',(-333.,-43.3932528566243,46.1736886433142));
#6699=CARTESIAN_POINT('',(-333.,-43.3932528566243,46.1736886433142));
#6700=CARTESIAN_POINT('Origin',(-332.,-43.3932528566243,46.1736886433142));
#6701=CARTESIAN_POINT('',(-331.953895233392,-43.3932528566243,47.1726252531629));
#6702=CARTESIAN_POINT('Origin',(-320.953895233392,-43.3932528566243,46.6649329454706));
#6703=CARTESIAN_POINT('',(-320.953895233392,-43.3932528566243,46.6649329454706));
#6704=CARTESIAN_POINT('Origin',(-321.,-43.3932528566243,45.6659963356218));
#6705=CARTESIAN_POINT('',(-320.,-43.3932528566243,45.6659963356219));
#6706=CARTESIAN_POINT('Origin',(-320.,-43.3932528566243,43.5373689430449));
#6707=CARTESIAN_POINT('',(-320.,-43.3932528566243,43.5373689430449));
#6708=CARTESIAN_POINT('Origin',(-319.,-43.3932528566243,43.5373689430449));
#6709=CARTESIAN_POINT('',(-319.087014138364,-43.3932528566243,42.5411618663159));
#6710=CARTESIAN_POINT('Origin',(-178.174028276727,-43.3932528566243,30.2330561165999));
#6711=CARTESIAN_POINT('',(-178.174028276727,-43.3932528566243,30.2330561165999));
#6712=CARTESIAN_POINT('Origin',(-178.,-43.3932528566243,32.2254702700579));
#6713=CARTESIAN_POINT('',(-176.,-43.3932528566243,32.2254702700578));
#6714=CARTESIAN_POINT('Origin',(-176.,-43.3932528566243,32.3928496364886));
#6715=CARTESIAN_POINT('Origin',(-345.806600225884,-33.7263908158462,0.));
#6716=CARTESIAN_POINT('Origin',(-8.5,-22.092920065877,3.));
#6717=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#6721,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#6718=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#6721,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#6719=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#6717))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#6721,#6723,#6724))
REPRESENTATION_CONTEXT('','3D')
);
#6720=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#6718))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#6721,#6723,#6724))
REPRESENTATION_CONTEXT('','3D')
);
#6721=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#6722=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT($,.METRE.)
);
#6723=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#6724=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#6725=SHAPE_DEFINITION_REPRESENTATION(#6726,#6727);
#6726=PRODUCT_DEFINITION_SHAPE('',$,#6729);
#6727=SHAPE_REPRESENTATION('',(#3982),#6719);
#6728=PRODUCT_DEFINITION_CONTEXT('part definition',#6733,'design');
#6729=PRODUCT_DEFINITION('S_1125','S_1125 v5',#6730,#6728);
#6730=PRODUCT_DEFINITION_FORMATION('',$,#6735);
#6731=PRODUCT_RELATED_PRODUCT_CATEGORY('S_1125 v5','S_1125 v5',(#6735));
#6732=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2009,#6733);
#6733=APPLICATION_CONTEXT(
'Core Data for Automotive Mechanical Design Process');
#6734=PRODUCT_CONTEXT('part definition',#6733,'mechanical');
#6735=PRODUCT('S_1125','S_1125 v5',$,(#6734));
#6736=PRESENTATION_STYLE_ASSIGNMENT((#6737));
#6737=SURFACE_STYLE_USAGE(.BOTH.,#6738);
#6738=SURFACE_SIDE_STYLE('',(#6739));
#6739=SURFACE_STYLE_FILL_AREA(#6740);
#6740=FILL_AREA_STYLE('Steel - Satin',(#6741));
#6741=FILL_AREA_STYLE_COLOUR('Steel - Satin',#6742);
#6742=COLOUR_RGB('Steel - Satin',0.627450980392157,0.627450980392157,0.627450980392157);
ENDSEC;
END-ISO-10303-21;
