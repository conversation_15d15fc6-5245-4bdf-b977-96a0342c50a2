ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ (''),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 
'G:/Shared drives/TIP/Validation/SMF KION/Tset SMF Shapes/S_1145.step',

/* time_stamp */ '2021-07-13T13:25:04+02:00',
/* author */ (''),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v18.1',
/* originating_system */ 'Autodesk Translation Framework v10.9.0.1377',

/* authorisation */ '');

FILE_SCHEMA (('AUTOMOTIVE_DESIGN { 1 0 10303 214 3 1 1 }'));
ENDSEC;

DATA;
#10=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#13),#1527);
#11=SHAPE_REPRESENTATION_RELATIONSHIP('SRR','None',#1534,#12);
#12=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#14),#1526);
#13=STYLED_ITEM('',(#1543),#14);
#14=MANIFOLD_SOLID_BREP('Body1',#880);
#15=FACE_BOUND('',#138,.T.);
#16=FACE_BOUND('',#181,.T.);
#17=B_SPLINE_SURFACE_WITH_KNOTS('',3,3,((#1302,#1303,#1304,#1305),(#1306,
#1307,#1308,#1309),(#1310,#1311,#1312,#1313),(#1314,#1315,#1316,#1317)),
 .UNSPECIFIED.,.F.,.F.,.F.,(4,4),(4,4),(0.,1.33333333333334),(-0.148595732207514,
0.),.UNSPECIFIED.);
#18=B_SPLINE_CURVE_WITH_KNOTS('',3,(#1266,#1267,#1268,#1269),
 .UNSPECIFIED.,.F.,.F.,(4,4),(-0.148595732207514,0.),.UNSPECIFIED.);
#19=B_SPLINE_CURVE_WITH_KNOTS('',3,(#1323,#1324,#1325,#1326),
 .UNSPECIFIED.,.F.,.F.,(4,4),(0.,0.148595732207514),.UNSPECIFIED.);
#20=CIRCLE('',#896,4.);
#21=CIRCLE('',#897,4.);
#22=CIRCLE('',#899,8.);
#23=CIRCLE('',#901,0.0999999999999948);
#24=CIRCLE('',#902,0.100000000000005);
#25=CIRCLE('',#903,0.1);
#26=CIRCLE('',#904,0.0999999999999948);
#27=CIRCLE('',#905,0.100000000000005);
#28=CIRCLE('',#906,236.412080168352);
#29=CIRCLE('',#907,0.0999999999999937);
#30=CIRCLE('',#909,4.99999999999998);
#31=CIRCLE('',#910,8.50000000000001);
#32=CIRCLE('',#911,5.00000000000006);
#33=CIRCLE('',#912,5.);
#34=CIRCLE('',#913,2.99999999999998);
#35=CIRCLE('',#914,5.2);
#36=CIRCLE('',#917,8.);
#37=CIRCLE('',#920,8.);
#38=CIRCLE('',#921,8.);
#39=CIRCLE('',#923,4.);
#40=CIRCLE('',#925,0.100000000000027);
#41=CIRCLE('',#926,0.100000000000002);
#42=CIRCLE('',#927,0.100000000000002);
#43=CIRCLE('',#928,2.99999999999996);
#44=CIRCLE('',#930,2.99999999999996);
#45=CIRCLE('',#931,0.100000000000002);
#46=CIRCLE('',#932,0.100000000000002);
#47=CIRCLE('',#933,0.100000000000027);
#48=CIRCLE('',#936,0.0999999999999937);
#49=CIRCLE('',#937,236.412080168352);
#50=CIRCLE('',#938,0.100000000000005);
#51=CIRCLE('',#939,0.0999999999999948);
#52=CIRCLE('',#940,0.1);
#53=CIRCLE('',#941,0.100000000000005);
#54=CIRCLE('',#942,0.0999999999999948);
#55=CIRCLE('',#944,3.99999999999999);
#56=CIRCLE('',#946,5.2);
#57=CIRCLE('',#951,2.99999999999998);
#58=CIRCLE('',#954,5.);
#59=CIRCLE('',#965,5.00000000000006);
#60=CIRCLE('',#968,8.50000000000001);
#61=CIRCLE('',#971,4.99999999999998);
#62=CYLINDRICAL_SURFACE('',#895,4.);
#63=CYLINDRICAL_SURFACE('',#916,8.);
#64=CYLINDRICAL_SURFACE('',#919,8.);
#65=CYLINDRICAL_SURFACE('',#945,5.2);
#66=CYLINDRICAL_SURFACE('',#947,236.412080168352);
#67=CYLINDRICAL_SURFACE('',#948,0.0999999999999937);
#68=CYLINDRICAL_SURFACE('',#950,2.99999999999998);
#69=CYLINDRICAL_SURFACE('',#953,5.);
#70=CYLINDRICAL_SURFACE('',#956,2.99999999999996);
#71=CYLINDRICAL_SURFACE('',#958,0.100000000000002);
#72=CYLINDRICAL_SURFACE('',#960,0.100000000000002);
#73=CYLINDRICAL_SURFACE('',#962,0.100000000000027);
#74=CYLINDRICAL_SURFACE('',#964,5.00000000000006);
#75=CYLINDRICAL_SURFACE('',#967,8.50000000000001);
#76=CYLINDRICAL_SURFACE('',#970,4.99999999999998);
#77=CYLINDRICAL_SURFACE('',#973,0.0999999999999948);
#78=CYLINDRICAL_SURFACE('',#975,0.100000000000005);
#79=CYLINDRICAL_SURFACE('',#977,0.1);
#80=CYLINDRICAL_SURFACE('',#979,0.0999999999999948);
#81=CYLINDRICAL_SURFACE('',#981,0.100000000000005);
#82=CYLINDRICAL_SURFACE('',#983,4.);
#83=FACE_OUTER_BOUND('',#132,.T.);
#84=FACE_OUTER_BOUND('',#133,.T.);
#85=FACE_OUTER_BOUND('',#134,.T.);
#86=FACE_OUTER_BOUND('',#135,.T.);
#87=FACE_OUTER_BOUND('',#136,.T.);
#88=FACE_OUTER_BOUND('',#137,.T.);
#89=FACE_OUTER_BOUND('',#139,.T.);
#90=FACE_OUTER_BOUND('',#140,.T.);
#91=FACE_OUTER_BOUND('',#141,.T.);
#92=FACE_OUTER_BOUND('',#142,.T.);
#93=FACE_OUTER_BOUND('',#143,.T.);
#94=FACE_OUTER_BOUND('',#144,.T.);
#95=FACE_OUTER_BOUND('',#145,.T.);
#96=FACE_OUTER_BOUND('',#146,.T.);
#97=FACE_OUTER_BOUND('',#147,.T.);
#98=FACE_OUTER_BOUND('',#148,.T.);
#99=FACE_OUTER_BOUND('',#149,.T.);
#100=FACE_OUTER_BOUND('',#150,.T.);
#101=FACE_OUTER_BOUND('',#151,.T.);
#102=FACE_OUTER_BOUND('',#152,.T.);
#103=FACE_OUTER_BOUND('',#153,.T.);
#104=FACE_OUTER_BOUND('',#154,.T.);
#105=FACE_OUTER_BOUND('',#155,.T.);
#106=FACE_OUTER_BOUND('',#156,.T.);
#107=FACE_OUTER_BOUND('',#157,.T.);
#108=FACE_OUTER_BOUND('',#158,.T.);
#109=FACE_OUTER_BOUND('',#159,.T.);
#110=FACE_OUTER_BOUND('',#160,.T.);
#111=FACE_OUTER_BOUND('',#161,.T.);
#112=FACE_OUTER_BOUND('',#162,.T.);
#113=FACE_OUTER_BOUND('',#163,.T.);
#114=FACE_OUTER_BOUND('',#164,.T.);
#115=FACE_OUTER_BOUND('',#165,.T.);
#116=FACE_OUTER_BOUND('',#166,.T.);
#117=FACE_OUTER_BOUND('',#167,.T.);
#118=FACE_OUTER_BOUND('',#168,.T.);
#119=FACE_OUTER_BOUND('',#169,.T.);
#120=FACE_OUTER_BOUND('',#170,.T.);
#121=FACE_OUTER_BOUND('',#171,.T.);
#122=FACE_OUTER_BOUND('',#172,.T.);
#123=FACE_OUTER_BOUND('',#173,.T.);
#124=FACE_OUTER_BOUND('',#174,.T.);
#125=FACE_OUTER_BOUND('',#175,.T.);
#126=FACE_OUTER_BOUND('',#176,.T.);
#127=FACE_OUTER_BOUND('',#177,.T.);
#128=FACE_OUTER_BOUND('',#178,.T.);
#129=FACE_OUTER_BOUND('',#179,.T.);
#130=FACE_OUTER_BOUND('',#180,.T.);
#131=FACE_OUTER_BOUND('',#182,.T.);
#132=EDGE_LOOP('',(#552,#553,#554,#555));
#133=EDGE_LOOP('',(#556,#557,#558,#559,#560));
#134=EDGE_LOOP('',(#561,#562,#563,#564));
#135=EDGE_LOOP('',(#565,#566,#567,#568,#569,#570,#571,#572,#573,#574,#575,
#576,#577,#578));
#136=EDGE_LOOP('',(#579,#580,#581,#582,#583));
#137=EDGE_LOOP('',(#584,#585,#586,#587,#588,#589,#590,#591,#592,#593,#594,
#595,#596));
#138=EDGE_LOOP('',(#597));
#139=EDGE_LOOP('',(#598,#599,#600,#601));
#140=EDGE_LOOP('',(#602,#603,#604,#605,#606));
#141=EDGE_LOOP('',(#607,#608,#609,#610));
#142=EDGE_LOOP('',(#611,#612,#613,#614));
#143=EDGE_LOOP('',(#615,#616,#617,#618));
#144=EDGE_LOOP('',(#619,#620,#621,#622,#623,#624,#625,#626,#627,#628));
#145=EDGE_LOOP('',(#629,#630,#631,#632,#633,#634,#635,#636,#637,#638));
#146=EDGE_LOOP('',(#639,#640,#641,#642));
#147=EDGE_LOOP('',(#643,#644,#645,#646,#647,#648,#649,#650,#651,#652,#653,
#654,#655,#656));
#148=EDGE_LOOP('',(#657,#658,#659,#660));
#149=EDGE_LOOP('',(#661,#662,#663,#664));
#150=EDGE_LOOP('',(#665,#666,#667,#668));
#151=EDGE_LOOP('',(#669,#670,#671,#672));
#152=EDGE_LOOP('',(#673,#674,#675,#676));
#153=EDGE_LOOP('',(#677,#678,#679,#680,#681));
#154=EDGE_LOOP('',(#682,#683,#684,#685));
#155=EDGE_LOOP('',(#686,#687,#688,#689));
#156=EDGE_LOOP('',(#690,#691,#692,#693));
#157=EDGE_LOOP('',(#694,#695,#696,#697));
#158=EDGE_LOOP('',(#698,#699,#700,#701));
#159=EDGE_LOOP('',(#702,#703,#704,#705));
#160=EDGE_LOOP('',(#706,#707,#708,#709));
#161=EDGE_LOOP('',(#710,#711,#712,#713));
#162=EDGE_LOOP('',(#714,#715,#716,#717));
#163=EDGE_LOOP('',(#718,#719,#720,#721));
#164=EDGE_LOOP('',(#722,#723,#724,#725));
#165=EDGE_LOOP('',(#726,#727,#728,#729));
#166=EDGE_LOOP('',(#730,#731,#732,#733));
#167=EDGE_LOOP('',(#734,#735,#736,#737));
#168=EDGE_LOOP('',(#738,#739,#740,#741));
#169=EDGE_LOOP('',(#742,#743,#744,#745));
#170=EDGE_LOOP('',(#746,#747,#748,#749));
#171=EDGE_LOOP('',(#750,#751,#752,#753));
#172=EDGE_LOOP('',(#754,#755,#756,#757));
#173=EDGE_LOOP('',(#758,#759,#760,#761));
#174=EDGE_LOOP('',(#762,#763,#764,#765));
#175=EDGE_LOOP('',(#766,#767,#768,#769));
#176=EDGE_LOOP('',(#770,#771,#772,#773));
#177=EDGE_LOOP('',(#774,#775,#776,#777));
#178=EDGE_LOOP('',(#778,#779,#780,#781));
#179=EDGE_LOOP('',(#782,#783,#784,#785));
#180=EDGE_LOOP('',(#786,#787,#788,#789,#790,#791,#792,#793,#794,#795,#796,
#797,#798));
#181=EDGE_LOOP('',(#799));
#182=EDGE_LOOP('',(#800,#801,#802,#803));
#183=LINE('',#1252,#265);
#184=LINE('',#1254,#266);
#185=LINE('',#1256,#267);
#186=LINE('',#1257,#268);
#187=LINE('',#1260,#269);
#188=LINE('',#1264,#270);
#189=LINE('',#1273,#271);
#190=LINE('',#1276,#272);
#191=LINE('',#1278,#273);
#192=LINE('',#1280,#274);
#193=LINE('',#1284,#275);
#194=LINE('',#1288,#276);
#195=LINE('',#1292,#277);
#196=LINE('',#1296,#278);
#197=LINE('',#1319,#279);
#198=LINE('',#1321,#280);
#199=LINE('',#1327,#281);
#200=LINE('',#1330,#282);
#201=LINE('',#1334,#283);
#202=LINE('',#1338,#284);
#203=LINE('',#1342,#285);
#204=LINE('',#1344,#286);
#205=LINE('',#1346,#287);
#206=LINE('',#1350,#288);
#207=LINE('',#1356,#289);
#208=LINE('',#1357,#290);
#209=LINE('',#1360,#291);
#210=LINE('',#1364,#292);
#211=LINE('',#1366,#293);
#212=LINE('',#1368,#294);
#213=LINE('',#1369,#295);
#214=LINE('',#1372,#296);
#215=LINE('',#1376,#297);
#216=LINE('',#1380,#298);
#217=LINE('',#1382,#299);
#218=LINE('',#1384,#300);
#219=LINE('',#1386,#301);
#220=LINE('',#1390,#302);
#221=LINE('',#1394,#303);
#222=LINE('',#1398,#304);
#223=LINE('',#1404,#305);
#224=LINE('',#1408,#306);
#225=LINE('',#1412,#307);
#226=LINE('',#1415,#308);
#227=LINE('',#1418,#309);
#228=LINE('',#1419,#310);
#229=LINE('',#1428,#311);
#230=LINE('',#1432,#312);
#231=LINE('',#1436,#313);
#232=LINE('',#1440,#314);
#233=LINE('',#1443,#315);
#234=LINE('',#1445,#316);
#235=LINE('',#1449,#317);
#236=LINE('',#1452,#318);
#237=LINE('',#1453,#319);
#238=LINE('',#1458,#320);
#239=LINE('',#1462,#321);
#240=LINE('',#1463,#322);
#241=LINE('',#1467,#323);
#242=LINE('',#1469,#324);
#243=LINE('',#1471,#325);
#244=LINE('',#1473,#326);
#245=LINE('',#1475,#327);
#246=LINE('',#1477,#328);
#247=LINE('',#1479,#329);
#248=LINE('',#1481,#330);
#249=LINE('',#1483,#331);
#250=LINE('',#1488,#332);
#251=LINE('',#1491,#333);
#252=LINE('',#1492,#334);
#253=LINE('',#1496,#335);
#254=LINE('',#1499,#336);
#255=LINE('',#1500,#337);
#256=LINE('',#1504,#338);
#257=LINE('',#1506,#339);
#258=LINE('',#1508,#340);
#259=LINE('',#1510,#341);
#260=LINE('',#1512,#342);
#261=LINE('',#1514,#343);
#262=LINE('',#1516,#344);
#263=LINE('',#1518,#345);
#264=LINE('',#1520,#346);
#265=VECTOR('',#988,10.);
#266=VECTOR('',#989,10.);
#267=VECTOR('',#990,10.);
#268=VECTOR('',#991,10.);
#269=VECTOR('',#994,10.);
#270=VECTOR('',#997,10.);
#271=VECTOR('',#1002,10.);
#272=VECTOR('',#1005,10.);
#273=VECTOR('',#1008,10.);
#274=VECTOR('',#1009,10.);
#275=VECTOR('',#1012,10.);
#276=VECTOR('',#1015,10.);
#277=VECTOR('',#1018,10.);
#278=VECTOR('',#1021,10.);
#279=VECTOR('',#1028,10.);
#280=VECTOR('',#1029,10.);
#281=VECTOR('',#1030,10.);
#282=VECTOR('',#1033,10.);
#283=VECTOR('',#1036,10.);
#284=VECTOR('',#1039,10.);
#285=VECTOR('',#1042,10.);
#286=VECTOR('',#1043,10.);
#287=VECTOR('',#1044,10.);
#288=VECTOR('',#1047,10.);
#289=VECTOR('',#1054,10.);
#290=VECTOR('',#1055,10.);
#291=VECTOR('',#1060,10.);
#292=VECTOR('',#1063,10.);
#293=VECTOR('',#1064,10.);
#294=VECTOR('',#1065,10.);
#295=VECTOR('',#1066,10.);
#296=VECTOR('',#1069,10.);
#297=VECTOR('',#1072,10.);
#298=VECTOR('',#1077,10.);
#299=VECTOR('',#1080,10.);
#300=VECTOR('',#1083,10.);
#301=VECTOR('',#1084,10.);
#302=VECTOR('',#1087,10.);
#303=VECTOR('',#1090,10.);
#304=VECTOR('',#1093,10.);
#305=VECTOR('',#1100,10.);
#306=VECTOR('',#1103,10.);
#307=VECTOR('',#1106,10.);
#308=VECTOR('',#1109,10.);
#309=VECTOR('',#1112,10.);
#310=VECTOR('',#1113,10.);
#311=VECTOR('',#1122,10.);
#312=VECTOR('',#1125,10.);
#313=VECTOR('',#1128,10.);
#314=VECTOR('',#1131,10.);
#315=VECTOR('',#1134,10.);
#316=VECTOR('',#1137,10.);
#317=VECTOR('',#1142,5.19999999999999);
#318=VECTOR('',#1147,10.);
#319=VECTOR('',#1148,10.);
#320=VECTOR('',#1155,10.);
#321=VECTOR('',#1160,10.);
#322=VECTOR('',#1161,10.);
#323=VECTOR('',#1166,10.);
#324=VECTOR('',#1169,10.);
#325=VECTOR('',#1172,10.);
#326=VECTOR('',#1175,10.);
#327=VECTOR('',#1178,10.);
#328=VECTOR('',#1181,10.);
#329=VECTOR('',#1184,10.);
#330=VECTOR('',#1187,10.);
#331=VECTOR('',#1190,10.);
#332=VECTOR('',#1197,10.);
#333=VECTOR('',#1200,10.);
#334=VECTOR('',#1201,10.);
#335=VECTOR('',#1206,10.);
#336=VECTOR('',#1209,10.);
#337=VECTOR('',#1210,10.);
#338=VECTOR('',#1217,10.);
#339=VECTOR('',#1220,10.);
#340=VECTOR('',#1223,10.);
#341=VECTOR('',#1226,10.);
#342=VECTOR('',#1229,10.);
#343=VECTOR('',#1232,10.);
#344=VECTOR('',#1235,10.);
#345=VECTOR('',#1238,10.);
#346=VECTOR('',#1241,10.);
#347=VERTEX_POINT('',#1250);
#348=VERTEX_POINT('',#1251);
#349=VERTEX_POINT('',#1253);
#350=VERTEX_POINT('',#1255);
#351=VERTEX_POINT('',#1259);
#352=VERTEX_POINT('',#1261);
#353=VERTEX_POINT('',#1263);
#354=VERTEX_POINT('',#1265);
#355=VERTEX_POINT('',#1272);
#356=VERTEX_POINT('',#1274);
#357=VERTEX_POINT('',#1279);
#358=VERTEX_POINT('',#1281);
#359=VERTEX_POINT('',#1283);
#360=VERTEX_POINT('',#1285);
#361=VERTEX_POINT('',#1287);
#362=VERTEX_POINT('',#1289);
#363=VERTEX_POINT('',#1291);
#364=VERTEX_POINT('',#1293);
#365=VERTEX_POINT('',#1295);
#366=VERTEX_POINT('',#1297);
#367=VERTEX_POINT('',#1299);
#368=VERTEX_POINT('',#1318);
#369=VERTEX_POINT('',#1320);
#370=VERTEX_POINT('',#1322);
#371=VERTEX_POINT('',#1329);
#372=VERTEX_POINT('',#1331);
#373=VERTEX_POINT('',#1333);
#374=VERTEX_POINT('',#1335);
#375=VERTEX_POINT('',#1337);
#376=VERTEX_POINT('',#1339);
#377=VERTEX_POINT('',#1341);
#378=VERTEX_POINT('',#1343);
#379=VERTEX_POINT('',#1345);
#380=VERTEX_POINT('',#1347);
#381=VERTEX_POINT('',#1349);
#382=VERTEX_POINT('',#1352);
#383=VERTEX_POINT('',#1355);
#384=VERTEX_POINT('',#1362);
#385=VERTEX_POINT('',#1363);
#386=VERTEX_POINT('',#1365);
#387=VERTEX_POINT('',#1367);
#388=VERTEX_POINT('',#1371);
#389=VERTEX_POINT('',#1373);
#390=VERTEX_POINT('',#1375);
#391=VERTEX_POINT('',#1379);
#392=VERTEX_POINT('',#1385);
#393=VERTEX_POINT('',#1387);
#394=VERTEX_POINT('',#1389);
#395=VERTEX_POINT('',#1391);
#396=VERTEX_POINT('',#1393);
#397=VERTEX_POINT('',#1395);
#398=VERTEX_POINT('',#1397);
#399=VERTEX_POINT('',#1401);
#400=VERTEX_POINT('',#1403);
#401=VERTEX_POINT('',#1405);
#402=VERTEX_POINT('',#1407);
#403=VERTEX_POINT('',#1409);
#404=VERTEX_POINT('',#1411);
#405=VERTEX_POINT('',#1413);
#406=VERTEX_POINT('',#1417);
#407=VERTEX_POINT('',#1421);
#408=VERTEX_POINT('',#1423);
#409=VERTEX_POINT('',#1425);
#410=VERTEX_POINT('',#1427);
#411=VERTEX_POINT('',#1429);
#412=VERTEX_POINT('',#1431);
#413=VERTEX_POINT('',#1433);
#414=VERTEX_POINT('',#1435);
#415=VERTEX_POINT('',#1437);
#416=VERTEX_POINT('',#1439);
#417=VERTEX_POINT('',#1441);
#418=VERTEX_POINT('',#1448);
#419=VERTEX_POINT('',#1457);
#420=VERTEX_POINT('',#1461);
#421=VERTEX_POINT('',#1465);
#422=VERTEX_POINT('',#1486);
#423=VERTEX_POINT('',#1490);
#424=VERTEX_POINT('',#1494);
#425=VERTEX_POINT('',#1498);
#426=EDGE_CURVE('',#347,#348,#183,.T.);
#427=EDGE_CURVE('',#348,#349,#184,.T.);
#428=EDGE_CURVE('',#349,#350,#185,.T.);
#429=EDGE_CURVE('',#347,#350,#186,.T.);
#430=EDGE_CURVE('',#351,#347,#187,.T.);
#431=EDGE_CURVE('',#351,#352,#20,.T.);
#432=EDGE_CURVE('',#352,#353,#188,.T.);
#433=EDGE_CURVE('',#353,#354,#18,.T.);
#434=EDGE_CURVE('',#354,#347,#21,.T.);
#435=EDGE_CURVE('',#355,#351,#189,.T.);
#436=EDGE_CURVE('',#355,#356,#22,.T.);
#437=EDGE_CURVE('',#356,#352,#190,.T.);
#438=EDGE_CURVE('',#348,#355,#191,.T.);
#439=EDGE_CURVE('',#357,#355,#192,.T.);
#440=EDGE_CURVE('',#357,#358,#23,.T.);
#441=EDGE_CURVE('',#359,#358,#193,.T.);
#442=EDGE_CURVE('',#360,#359,#24,.T.);
#443=EDGE_CURVE('',#361,#360,#194,.T.);
#444=EDGE_CURVE('',#362,#361,#25,.T.);
#445=EDGE_CURVE('',#363,#362,#195,.T.);
#446=EDGE_CURVE('',#363,#364,#26,.T.);
#447=EDGE_CURVE('',#365,#364,#196,.T.);
#448=EDGE_CURVE('',#365,#366,#27,.T.);
#449=EDGE_CURVE('',#366,#367,#28,.T.);
#450=EDGE_CURVE('',#367,#349,#29,.T.);
#451=EDGE_CURVE('',#353,#368,#197,.T.);
#452=EDGE_CURVE('',#368,#369,#198,.T.);
#453=EDGE_CURVE('',#370,#369,#19,.T.);
#454=EDGE_CURVE('',#370,#354,#199,.T.);
#455=EDGE_CURVE('',#352,#371,#200,.T.);
#456=EDGE_CURVE('',#371,#372,#30,.T.);
#457=EDGE_CURVE('',#372,#373,#201,.T.);
#458=EDGE_CURVE('',#373,#374,#31,.T.);
#459=EDGE_CURVE('',#374,#375,#202,.T.);
#460=EDGE_CURVE('',#375,#376,#32,.T.);
#461=EDGE_CURVE('',#376,#377,#203,.T.);
#462=EDGE_CURVE('',#378,#377,#204,.T.);
#463=EDGE_CURVE('',#378,#379,#205,.T.);
#464=EDGE_CURVE('',#379,#380,#33,.T.);
#465=EDGE_CURVE('',#380,#381,#206,.T.);
#466=EDGE_CURVE('',#381,#353,#34,.T.);
#467=EDGE_CURVE('',#382,#382,#35,.T.);
#468=EDGE_CURVE('',#356,#383,#207,.T.);
#469=EDGE_CURVE('',#383,#371,#208,.T.);
#470=EDGE_CURVE('',#370,#348,#36,.T.);
#471=EDGE_CURVE('',#369,#356,#209,.T.);
#472=EDGE_CURVE('',#384,#385,#210,.T.);
#473=EDGE_CURVE('',#386,#385,#211,.T.);
#474=EDGE_CURVE('',#387,#386,#212,.T.);
#475=EDGE_CURVE('',#384,#387,#213,.T.);
#476=EDGE_CURVE('',#388,#384,#214,.T.);
#477=EDGE_CURVE('',#389,#388,#37,.T.);
#478=EDGE_CURVE('',#389,#390,#215,.T.);
#479=EDGE_CURVE('',#390,#384,#38,.T.);
#480=EDGE_CURVE('',#391,#388,#216,.T.);
#481=EDGE_CURVE('',#377,#391,#39,.T.);
#482=EDGE_CURVE('',#377,#389,#217,.T.);
#483=EDGE_CURVE('',#385,#391,#218,.T.);
#484=EDGE_CURVE('',#391,#392,#219,.T.);
#485=EDGE_CURVE('',#392,#393,#40,.T.);
#486=EDGE_CURVE('',#393,#394,#220,.T.);
#487=EDGE_CURVE('',#394,#395,#41,.T.);
#488=EDGE_CURVE('',#395,#396,#221,.T.);
#489=EDGE_CURVE('',#396,#397,#42,.T.);
#490=EDGE_CURVE('',#397,#398,#222,.T.);
#491=EDGE_CURVE('',#398,#386,#43,.T.);
#492=EDGE_CURVE('',#387,#399,#44,.T.);
#493=EDGE_CURVE('',#400,#399,#223,.T.);
#494=EDGE_CURVE('',#400,#401,#45,.T.);
#495=EDGE_CURVE('',#401,#402,#224,.T.);
#496=EDGE_CURVE('',#403,#402,#46,.T.);
#497=EDGE_CURVE('',#403,#404,#225,.T.);
#498=EDGE_CURVE('',#404,#405,#47,.T.);
#499=EDGE_CURVE('',#388,#405,#226,.T.);
#500=EDGE_CURVE('',#406,#376,#227,.T.);
#501=EDGE_CURVE('',#406,#389,#228,.T.);
#502=EDGE_CURVE('',#350,#407,#48,.T.);
#503=EDGE_CURVE('',#407,#408,#49,.T.);
#504=EDGE_CURVE('',#408,#409,#50,.T.);
#505=EDGE_CURVE('',#409,#410,#229,.T.);
#506=EDGE_CURVE('',#410,#411,#51,.T.);
#507=EDGE_CURVE('',#411,#412,#230,.T.);
#508=EDGE_CURVE('',#412,#413,#52,.T.);
#509=EDGE_CURVE('',#413,#414,#231,.T.);
#510=EDGE_CURVE('',#414,#415,#53,.T.);
#511=EDGE_CURVE('',#415,#416,#232,.T.);
#512=EDGE_CURVE('',#416,#417,#54,.T.);
#513=EDGE_CURVE('',#417,#351,#233,.T.);
#514=EDGE_CURVE('',#390,#378,#234,.T.);
#515=EDGE_CURVE('',#385,#378,#55,.T.);
#516=EDGE_CURVE('',#382,#418,#235,.T.);
#517=EDGE_CURVE('',#418,#418,#56,.T.);
#518=EDGE_CURVE('',#366,#408,#236,.T.);
#519=EDGE_CURVE('',#367,#407,#237,.T.);
#520=EDGE_CURVE('',#419,#381,#238,.T.);
#521=EDGE_CURVE('',#369,#419,#57,.T.);
#522=EDGE_CURVE('',#419,#420,#239,.T.);
#523=EDGE_CURVE('',#420,#380,#240,.T.);
#524=EDGE_CURVE('',#421,#420,#58,.T.);
#525=EDGE_CURVE('',#421,#379,#241,.T.);
#526=EDGE_CURVE('',#421,#390,#242,.T.);
#527=EDGE_CURVE('',#399,#398,#243,.T.);
#528=EDGE_CURVE('',#400,#397,#244,.T.);
#529=EDGE_CURVE('',#401,#396,#245,.T.);
#530=EDGE_CURVE('',#402,#395,#246,.T.);
#531=EDGE_CURVE('',#403,#394,#247,.T.);
#532=EDGE_CURVE('',#404,#393,#248,.T.);
#533=EDGE_CURVE('',#405,#392,#249,.T.);
#534=EDGE_CURVE('',#422,#406,#59,.T.);
#535=EDGE_CURVE('',#422,#375,#250,.T.);
#536=EDGE_CURVE('',#422,#423,#251,.T.);
#537=EDGE_CURVE('',#423,#374,#252,.T.);
#538=EDGE_CURVE('',#423,#424,#60,.T.);
#539=EDGE_CURVE('',#424,#373,#253,.T.);
#540=EDGE_CURVE('',#425,#424,#254,.T.);
#541=EDGE_CURVE('',#425,#372,#255,.T.);
#542=EDGE_CURVE('',#383,#425,#61,.T.);
#543=EDGE_CURVE('',#357,#417,#256,.T.);
#544=EDGE_CURVE('',#358,#416,#257,.T.);
#545=EDGE_CURVE('',#359,#415,#258,.T.);
#546=EDGE_CURVE('',#360,#414,#259,.T.);
#547=EDGE_CURVE('',#361,#413,#260,.T.);
#548=EDGE_CURVE('',#362,#412,#261,.T.);
#549=EDGE_CURVE('',#363,#411,#262,.T.);
#550=EDGE_CURVE('',#364,#410,#263,.T.);
#551=EDGE_CURVE('',#365,#409,#264,.T.);
#552=ORIENTED_EDGE('',*,*,#426,.T.);
#553=ORIENTED_EDGE('',*,*,#427,.T.);
#554=ORIENTED_EDGE('',*,*,#428,.T.);
#555=ORIENTED_EDGE('',*,*,#429,.F.);
#556=ORIENTED_EDGE('',*,*,#430,.F.);
#557=ORIENTED_EDGE('',*,*,#431,.T.);
#558=ORIENTED_EDGE('',*,*,#432,.T.);
#559=ORIENTED_EDGE('',*,*,#433,.T.);
#560=ORIENTED_EDGE('',*,*,#434,.T.);
#561=ORIENTED_EDGE('',*,*,#435,.F.);
#562=ORIENTED_EDGE('',*,*,#436,.T.);
#563=ORIENTED_EDGE('',*,*,#437,.T.);
#564=ORIENTED_EDGE('',*,*,#431,.F.);
#565=ORIENTED_EDGE('',*,*,#438,.T.);
#566=ORIENTED_EDGE('',*,*,#439,.F.);
#567=ORIENTED_EDGE('',*,*,#440,.T.);
#568=ORIENTED_EDGE('',*,*,#441,.F.);
#569=ORIENTED_EDGE('',*,*,#442,.F.);
#570=ORIENTED_EDGE('',*,*,#443,.F.);
#571=ORIENTED_EDGE('',*,*,#444,.F.);
#572=ORIENTED_EDGE('',*,*,#445,.F.);
#573=ORIENTED_EDGE('',*,*,#446,.T.);
#574=ORIENTED_EDGE('',*,*,#447,.F.);
#575=ORIENTED_EDGE('',*,*,#448,.T.);
#576=ORIENTED_EDGE('',*,*,#449,.T.);
#577=ORIENTED_EDGE('',*,*,#450,.T.);
#578=ORIENTED_EDGE('',*,*,#427,.F.);
#579=ORIENTED_EDGE('',*,*,#451,.T.);
#580=ORIENTED_EDGE('',*,*,#452,.T.);
#581=ORIENTED_EDGE('',*,*,#453,.F.);
#582=ORIENTED_EDGE('',*,*,#454,.T.);
#583=ORIENTED_EDGE('',*,*,#433,.F.);
#584=ORIENTED_EDGE('',*,*,#432,.F.);
#585=ORIENTED_EDGE('',*,*,#455,.T.);
#586=ORIENTED_EDGE('',*,*,#456,.T.);
#587=ORIENTED_EDGE('',*,*,#457,.T.);
#588=ORIENTED_EDGE('',*,*,#458,.T.);
#589=ORIENTED_EDGE('',*,*,#459,.T.);
#590=ORIENTED_EDGE('',*,*,#460,.T.);
#591=ORIENTED_EDGE('',*,*,#461,.T.);
#592=ORIENTED_EDGE('',*,*,#462,.F.);
#593=ORIENTED_EDGE('',*,*,#463,.T.);
#594=ORIENTED_EDGE('',*,*,#464,.T.);
#595=ORIENTED_EDGE('',*,*,#465,.T.);
#596=ORIENTED_EDGE('',*,*,#466,.T.);
#597=ORIENTED_EDGE('',*,*,#467,.T.);
#598=ORIENTED_EDGE('',*,*,#437,.F.);
#599=ORIENTED_EDGE('',*,*,#468,.T.);
#600=ORIENTED_EDGE('',*,*,#469,.T.);
#601=ORIENTED_EDGE('',*,*,#455,.F.);
#602=ORIENTED_EDGE('',*,*,#438,.F.);
#603=ORIENTED_EDGE('',*,*,#470,.F.);
#604=ORIENTED_EDGE('',*,*,#453,.T.);
#605=ORIENTED_EDGE('',*,*,#471,.T.);
#606=ORIENTED_EDGE('',*,*,#436,.F.);
#607=ORIENTED_EDGE('',*,*,#472,.T.);
#608=ORIENTED_EDGE('',*,*,#473,.F.);
#609=ORIENTED_EDGE('',*,*,#474,.F.);
#610=ORIENTED_EDGE('',*,*,#475,.F.);
#611=ORIENTED_EDGE('',*,*,#476,.F.);
#612=ORIENTED_EDGE('',*,*,#477,.F.);
#613=ORIENTED_EDGE('',*,*,#478,.T.);
#614=ORIENTED_EDGE('',*,*,#479,.T.);
#615=ORIENTED_EDGE('',*,*,#480,.F.);
#616=ORIENTED_EDGE('',*,*,#481,.F.);
#617=ORIENTED_EDGE('',*,*,#482,.T.);
#618=ORIENTED_EDGE('',*,*,#477,.T.);
#619=ORIENTED_EDGE('',*,*,#483,.T.);
#620=ORIENTED_EDGE('',*,*,#484,.T.);
#621=ORIENTED_EDGE('',*,*,#485,.T.);
#622=ORIENTED_EDGE('',*,*,#486,.T.);
#623=ORIENTED_EDGE('',*,*,#487,.T.);
#624=ORIENTED_EDGE('',*,*,#488,.T.);
#625=ORIENTED_EDGE('',*,*,#489,.T.);
#626=ORIENTED_EDGE('',*,*,#490,.T.);
#627=ORIENTED_EDGE('',*,*,#491,.T.);
#628=ORIENTED_EDGE('',*,*,#473,.T.);
#629=ORIENTED_EDGE('',*,*,#476,.T.);
#630=ORIENTED_EDGE('',*,*,#475,.T.);
#631=ORIENTED_EDGE('',*,*,#492,.T.);
#632=ORIENTED_EDGE('',*,*,#493,.F.);
#633=ORIENTED_EDGE('',*,*,#494,.T.);
#634=ORIENTED_EDGE('',*,*,#495,.T.);
#635=ORIENTED_EDGE('',*,*,#496,.F.);
#636=ORIENTED_EDGE('',*,*,#497,.T.);
#637=ORIENTED_EDGE('',*,*,#498,.T.);
#638=ORIENTED_EDGE('',*,*,#499,.F.);
#639=ORIENTED_EDGE('',*,*,#482,.F.);
#640=ORIENTED_EDGE('',*,*,#461,.F.);
#641=ORIENTED_EDGE('',*,*,#500,.F.);
#642=ORIENTED_EDGE('',*,*,#501,.T.);
#643=ORIENTED_EDGE('',*,*,#430,.T.);
#644=ORIENTED_EDGE('',*,*,#429,.T.);
#645=ORIENTED_EDGE('',*,*,#502,.T.);
#646=ORIENTED_EDGE('',*,*,#503,.T.);
#647=ORIENTED_EDGE('',*,*,#504,.T.);
#648=ORIENTED_EDGE('',*,*,#505,.T.);
#649=ORIENTED_EDGE('',*,*,#506,.T.);
#650=ORIENTED_EDGE('',*,*,#507,.T.);
#651=ORIENTED_EDGE('',*,*,#508,.T.);
#652=ORIENTED_EDGE('',*,*,#509,.T.);
#653=ORIENTED_EDGE('',*,*,#510,.T.);
#654=ORIENTED_EDGE('',*,*,#511,.T.);
#655=ORIENTED_EDGE('',*,*,#512,.T.);
#656=ORIENTED_EDGE('',*,*,#513,.T.);
#657=ORIENTED_EDGE('',*,*,#472,.F.);
#658=ORIENTED_EDGE('',*,*,#479,.F.);
#659=ORIENTED_EDGE('',*,*,#514,.T.);
#660=ORIENTED_EDGE('',*,*,#515,.F.);
#661=ORIENTED_EDGE('',*,*,#467,.F.);
#662=ORIENTED_EDGE('',*,*,#516,.T.);
#663=ORIENTED_EDGE('',*,*,#517,.F.);
#664=ORIENTED_EDGE('',*,*,#516,.F.);
#665=ORIENTED_EDGE('',*,*,#449,.F.);
#666=ORIENTED_EDGE('',*,*,#518,.T.);
#667=ORIENTED_EDGE('',*,*,#503,.F.);
#668=ORIENTED_EDGE('',*,*,#519,.F.);
#669=ORIENTED_EDGE('',*,*,#450,.F.);
#670=ORIENTED_EDGE('',*,*,#519,.T.);
#671=ORIENTED_EDGE('',*,*,#502,.F.);
#672=ORIENTED_EDGE('',*,*,#428,.F.);
#673=ORIENTED_EDGE('',*,*,#426,.F.);
#674=ORIENTED_EDGE('',*,*,#434,.F.);
#675=ORIENTED_EDGE('',*,*,#454,.F.);
#676=ORIENTED_EDGE('',*,*,#470,.T.);
#677=ORIENTED_EDGE('',*,*,#451,.F.);
#678=ORIENTED_EDGE('',*,*,#466,.F.);
#679=ORIENTED_EDGE('',*,*,#520,.F.);
#680=ORIENTED_EDGE('',*,*,#521,.F.);
#681=ORIENTED_EDGE('',*,*,#452,.F.);
#682=ORIENTED_EDGE('',*,*,#522,.F.);
#683=ORIENTED_EDGE('',*,*,#520,.T.);
#684=ORIENTED_EDGE('',*,*,#465,.F.);
#685=ORIENTED_EDGE('',*,*,#523,.F.);
#686=ORIENTED_EDGE('',*,*,#524,.T.);
#687=ORIENTED_EDGE('',*,*,#523,.T.);
#688=ORIENTED_EDGE('',*,*,#464,.F.);
#689=ORIENTED_EDGE('',*,*,#525,.F.);
#690=ORIENTED_EDGE('',*,*,#514,.F.);
#691=ORIENTED_EDGE('',*,*,#526,.F.);
#692=ORIENTED_EDGE('',*,*,#525,.T.);
#693=ORIENTED_EDGE('',*,*,#463,.F.);
#694=ORIENTED_EDGE('',*,*,#492,.F.);
#695=ORIENTED_EDGE('',*,*,#474,.T.);
#696=ORIENTED_EDGE('',*,*,#491,.F.);
#697=ORIENTED_EDGE('',*,*,#527,.F.);
#698=ORIENTED_EDGE('',*,*,#493,.T.);
#699=ORIENTED_EDGE('',*,*,#527,.T.);
#700=ORIENTED_EDGE('',*,*,#490,.F.);
#701=ORIENTED_EDGE('',*,*,#528,.F.);
#702=ORIENTED_EDGE('',*,*,#494,.F.);
#703=ORIENTED_EDGE('',*,*,#528,.T.);
#704=ORIENTED_EDGE('',*,*,#489,.F.);
#705=ORIENTED_EDGE('',*,*,#529,.F.);
#706=ORIENTED_EDGE('',*,*,#495,.F.);
#707=ORIENTED_EDGE('',*,*,#529,.T.);
#708=ORIENTED_EDGE('',*,*,#488,.F.);
#709=ORIENTED_EDGE('',*,*,#530,.F.);
#710=ORIENTED_EDGE('',*,*,#496,.T.);
#711=ORIENTED_EDGE('',*,*,#530,.T.);
#712=ORIENTED_EDGE('',*,*,#487,.F.);
#713=ORIENTED_EDGE('',*,*,#531,.F.);
#714=ORIENTED_EDGE('',*,*,#497,.F.);
#715=ORIENTED_EDGE('',*,*,#531,.T.);
#716=ORIENTED_EDGE('',*,*,#486,.F.);
#717=ORIENTED_EDGE('',*,*,#532,.F.);
#718=ORIENTED_EDGE('',*,*,#498,.F.);
#719=ORIENTED_EDGE('',*,*,#532,.T.);
#720=ORIENTED_EDGE('',*,*,#485,.F.);
#721=ORIENTED_EDGE('',*,*,#533,.F.);
#722=ORIENTED_EDGE('',*,*,#480,.T.);
#723=ORIENTED_EDGE('',*,*,#499,.T.);
#724=ORIENTED_EDGE('',*,*,#533,.T.);
#725=ORIENTED_EDGE('',*,*,#484,.F.);
#726=ORIENTED_EDGE('',*,*,#534,.T.);
#727=ORIENTED_EDGE('',*,*,#500,.T.);
#728=ORIENTED_EDGE('',*,*,#460,.F.);
#729=ORIENTED_EDGE('',*,*,#535,.F.);
#730=ORIENTED_EDGE('',*,*,#536,.F.);
#731=ORIENTED_EDGE('',*,*,#535,.T.);
#732=ORIENTED_EDGE('',*,*,#459,.F.);
#733=ORIENTED_EDGE('',*,*,#537,.F.);
#734=ORIENTED_EDGE('',*,*,#538,.F.);
#735=ORIENTED_EDGE('',*,*,#537,.T.);
#736=ORIENTED_EDGE('',*,*,#458,.F.);
#737=ORIENTED_EDGE('',*,*,#539,.F.);
#738=ORIENTED_EDGE('',*,*,#540,.T.);
#739=ORIENTED_EDGE('',*,*,#539,.T.);
#740=ORIENTED_EDGE('',*,*,#457,.F.);
#741=ORIENTED_EDGE('',*,*,#541,.F.);
#742=ORIENTED_EDGE('',*,*,#542,.T.);
#743=ORIENTED_EDGE('',*,*,#541,.T.);
#744=ORIENTED_EDGE('',*,*,#456,.F.);
#745=ORIENTED_EDGE('',*,*,#469,.F.);
#746=ORIENTED_EDGE('',*,*,#435,.T.);
#747=ORIENTED_EDGE('',*,*,#513,.F.);
#748=ORIENTED_EDGE('',*,*,#543,.F.);
#749=ORIENTED_EDGE('',*,*,#439,.T.);
#750=ORIENTED_EDGE('',*,*,#440,.F.);
#751=ORIENTED_EDGE('',*,*,#543,.T.);
#752=ORIENTED_EDGE('',*,*,#512,.F.);
#753=ORIENTED_EDGE('',*,*,#544,.F.);
#754=ORIENTED_EDGE('',*,*,#441,.T.);
#755=ORIENTED_EDGE('',*,*,#544,.T.);
#756=ORIENTED_EDGE('',*,*,#511,.F.);
#757=ORIENTED_EDGE('',*,*,#545,.F.);
#758=ORIENTED_EDGE('',*,*,#442,.T.);
#759=ORIENTED_EDGE('',*,*,#545,.T.);
#760=ORIENTED_EDGE('',*,*,#510,.F.);
#761=ORIENTED_EDGE('',*,*,#546,.F.);
#762=ORIENTED_EDGE('',*,*,#443,.T.);
#763=ORIENTED_EDGE('',*,*,#546,.T.);
#764=ORIENTED_EDGE('',*,*,#509,.F.);
#765=ORIENTED_EDGE('',*,*,#547,.F.);
#766=ORIENTED_EDGE('',*,*,#444,.T.);
#767=ORIENTED_EDGE('',*,*,#547,.T.);
#768=ORIENTED_EDGE('',*,*,#508,.F.);
#769=ORIENTED_EDGE('',*,*,#548,.F.);
#770=ORIENTED_EDGE('',*,*,#445,.T.);
#771=ORIENTED_EDGE('',*,*,#548,.T.);
#772=ORIENTED_EDGE('',*,*,#507,.F.);
#773=ORIENTED_EDGE('',*,*,#549,.F.);
#774=ORIENTED_EDGE('',*,*,#446,.F.);
#775=ORIENTED_EDGE('',*,*,#549,.T.);
#776=ORIENTED_EDGE('',*,*,#506,.F.);
#777=ORIENTED_EDGE('',*,*,#550,.F.);
#778=ORIENTED_EDGE('',*,*,#447,.T.);
#779=ORIENTED_EDGE('',*,*,#550,.T.);
#780=ORIENTED_EDGE('',*,*,#505,.F.);
#781=ORIENTED_EDGE('',*,*,#551,.F.);
#782=ORIENTED_EDGE('',*,*,#448,.F.);
#783=ORIENTED_EDGE('',*,*,#551,.T.);
#784=ORIENTED_EDGE('',*,*,#504,.F.);
#785=ORIENTED_EDGE('',*,*,#518,.F.);
#786=ORIENTED_EDGE('',*,*,#471,.F.);
#787=ORIENTED_EDGE('',*,*,#521,.T.);
#788=ORIENTED_EDGE('',*,*,#522,.T.);
#789=ORIENTED_EDGE('',*,*,#524,.F.);
#790=ORIENTED_EDGE('',*,*,#526,.T.);
#791=ORIENTED_EDGE('',*,*,#478,.F.);
#792=ORIENTED_EDGE('',*,*,#501,.F.);
#793=ORIENTED_EDGE('',*,*,#534,.F.);
#794=ORIENTED_EDGE('',*,*,#536,.T.);
#795=ORIENTED_EDGE('',*,*,#538,.T.);
#796=ORIENTED_EDGE('',*,*,#540,.F.);
#797=ORIENTED_EDGE('',*,*,#542,.F.);
#798=ORIENTED_EDGE('',*,*,#468,.F.);
#799=ORIENTED_EDGE('',*,*,#517,.T.);
#800=ORIENTED_EDGE('',*,*,#483,.F.);
#801=ORIENTED_EDGE('',*,*,#515,.T.);
#802=ORIENTED_EDGE('',*,*,#462,.T.);
#803=ORIENTED_EDGE('',*,*,#481,.T.);
#804=PLANE('',#894);
#805=PLANE('',#898);
#806=PLANE('',#900);
#807=PLANE('',#908);
#808=PLANE('',#915);
#809=PLANE('',#918);
#810=PLANE('',#922);
#811=PLANE('',#924);
#812=PLANE('',#929);
#813=PLANE('',#934);
#814=PLANE('',#935);
#815=PLANE('',#943);
#816=PLANE('',#949);
#817=PLANE('',#952);
#818=PLANE('',#955);
#819=PLANE('',#957);
#820=PLANE('',#959);
#821=PLANE('',#961);
#822=PLANE('',#963);
#823=PLANE('',#966);
#824=PLANE('',#969);
#825=PLANE('',#972);
#826=PLANE('',#974);
#827=PLANE('',#976);
#828=PLANE('',#978);
#829=PLANE('',#980);
#830=PLANE('',#982);
#831=ADVANCED_FACE('',(#83),#804,.T.);
#832=ADVANCED_FACE('',(#84),#62,.F.);
#833=ADVANCED_FACE('',(#85),#805,.T.);
#834=ADVANCED_FACE('',(#86),#806,.T.);
#835=ADVANCED_FACE('',(#87),#17,.F.);
#836=ADVANCED_FACE('',(#88,#15),#807,.F.);
#837=ADVANCED_FACE('',(#89),#808,.T.);
#838=ADVANCED_FACE('',(#90),#63,.T.);
#839=ADVANCED_FACE('',(#91),#809,.T.);
#840=ADVANCED_FACE('',(#92),#64,.T.);
#841=ADVANCED_FACE('',(#93),#810,.T.);
#842=ADVANCED_FACE('',(#94),#811,.F.);
#843=ADVANCED_FACE('',(#95),#812,.T.);
#844=ADVANCED_FACE('',(#96),#813,.T.);
#845=ADVANCED_FACE('',(#97),#814,.F.);
#846=ADVANCED_FACE('',(#98),#815,.T.);
#847=ADVANCED_FACE('',(#99),#65,.F.);
#848=ADVANCED_FACE('',(#100),#66,.T.);
#849=ADVANCED_FACE('',(#101),#67,.T.);
#850=ADVANCED_FACE('',(#102),#816,.T.);
#851=ADVANCED_FACE('',(#103),#68,.T.);
#852=ADVANCED_FACE('',(#104),#817,.T.);
#853=ADVANCED_FACE('',(#105),#69,.F.);
#854=ADVANCED_FACE('',(#106),#818,.T.);
#855=ADVANCED_FACE('',(#107),#70,.T.);
#856=ADVANCED_FACE('',(#108),#819,.T.);
#857=ADVANCED_FACE('',(#109),#71,.T.);
#858=ADVANCED_FACE('',(#110),#820,.T.);
#859=ADVANCED_FACE('',(#111),#72,.F.);
#860=ADVANCED_FACE('',(#112),#821,.T.);
#861=ADVANCED_FACE('',(#113),#73,.T.);
#862=ADVANCED_FACE('',(#114),#822,.T.);
#863=ADVANCED_FACE('',(#115),#74,.F.);
#864=ADVANCED_FACE('',(#116),#823,.T.);
#865=ADVANCED_FACE('',(#117),#75,.T.);
#866=ADVANCED_FACE('',(#118),#824,.T.);
#867=ADVANCED_FACE('',(#119),#76,.F.);
#868=ADVANCED_FACE('',(#120),#825,.T.);
#869=ADVANCED_FACE('',(#121),#77,.T.);
#870=ADVANCED_FACE('',(#122),#826,.T.);
#871=ADVANCED_FACE('',(#123),#78,.F.);
#872=ADVANCED_FACE('',(#124),#827,.T.);
#873=ADVANCED_FACE('',(#125),#79,.F.);
#874=ADVANCED_FACE('',(#126),#828,.T.);
#875=ADVANCED_FACE('',(#127),#80,.T.);
#876=ADVANCED_FACE('',(#128),#829,.T.);
#877=ADVANCED_FACE('',(#129),#81,.T.);
#878=ADVANCED_FACE('',(#130,#16),#830,.T.);
#879=ADVANCED_FACE('',(#131),#82,.F.);
#880=CLOSED_SHELL('',(#831,#832,#833,#834,#835,#836,#837,#838,#839,#840,
#841,#842,#843,#844,#845,#846,#847,#848,#849,#850,#851,#852,#853,#854,#855,
#856,#857,#858,#859,#860,#861,#862,#863,#864,#865,#866,#867,#868,#869,#870,
#871,#872,#873,#874,#875,#876,#877,#878,#879));
#881=DERIVED_UNIT_ELEMENT(#883,1.);
#882=DERIVED_UNIT_ELEMENT(#1529,-3.);
#883=(
MASS_UNIT()
NAMED_UNIT(*)
SI_UNIT(.KILO.,.GRAM.)
);
#884=DERIVED_UNIT((#881,#882));
#885=MEASURE_REPRESENTATION_ITEM('density measure',
POSITIVE_RATIO_MEASURE(7850.),#884);
#886=PROPERTY_DEFINITION_REPRESENTATION(#891,#888);
#887=PROPERTY_DEFINITION_REPRESENTATION(#892,#889);
#888=REPRESENTATION('material name',(#890),#1526);
#889=REPRESENTATION('density',(#885),#1526);
#890=DESCRIPTIVE_REPRESENTATION_ITEM('Steel','Steel');
#891=PROPERTY_DEFINITION('material property','material name',#1536);
#892=PROPERTY_DEFINITION('material property','density of part',#1536);
#893=AXIS2_PLACEMENT_3D('placement',#1248,#984,#985);
#894=AXIS2_PLACEMENT_3D('',#1249,#986,#987);
#895=AXIS2_PLACEMENT_3D('',#1258,#992,#993);
#896=AXIS2_PLACEMENT_3D('',#1262,#995,#996);
#897=AXIS2_PLACEMENT_3D('',#1270,#998,#999);
#898=AXIS2_PLACEMENT_3D('',#1271,#1000,#1001);
#899=AXIS2_PLACEMENT_3D('',#1275,#1003,#1004);
#900=AXIS2_PLACEMENT_3D('',#1277,#1006,#1007);
#901=AXIS2_PLACEMENT_3D('',#1282,#1010,#1011);
#902=AXIS2_PLACEMENT_3D('',#1286,#1013,#1014);
#903=AXIS2_PLACEMENT_3D('',#1290,#1016,#1017);
#904=AXIS2_PLACEMENT_3D('',#1294,#1019,#1020);
#905=AXIS2_PLACEMENT_3D('',#1298,#1022,#1023);
#906=AXIS2_PLACEMENT_3D('',#1300,#1024,#1025);
#907=AXIS2_PLACEMENT_3D('',#1301,#1026,#1027);
#908=AXIS2_PLACEMENT_3D('',#1328,#1031,#1032);
#909=AXIS2_PLACEMENT_3D('',#1332,#1034,#1035);
#910=AXIS2_PLACEMENT_3D('',#1336,#1037,#1038);
#911=AXIS2_PLACEMENT_3D('',#1340,#1040,#1041);
#912=AXIS2_PLACEMENT_3D('',#1348,#1045,#1046);
#913=AXIS2_PLACEMENT_3D('',#1351,#1048,#1049);
#914=AXIS2_PLACEMENT_3D('',#1353,#1050,#1051);
#915=AXIS2_PLACEMENT_3D('',#1354,#1052,#1053);
#916=AXIS2_PLACEMENT_3D('',#1358,#1056,#1057);
#917=AXIS2_PLACEMENT_3D('',#1359,#1058,#1059);
#918=AXIS2_PLACEMENT_3D('',#1361,#1061,#1062);
#919=AXIS2_PLACEMENT_3D('',#1370,#1067,#1068);
#920=AXIS2_PLACEMENT_3D('',#1374,#1070,#1071);
#921=AXIS2_PLACEMENT_3D('',#1377,#1073,#1074);
#922=AXIS2_PLACEMENT_3D('',#1378,#1075,#1076);
#923=AXIS2_PLACEMENT_3D('',#1381,#1078,#1079);
#924=AXIS2_PLACEMENT_3D('',#1383,#1081,#1082);
#925=AXIS2_PLACEMENT_3D('',#1388,#1085,#1086);
#926=AXIS2_PLACEMENT_3D('',#1392,#1088,#1089);
#927=AXIS2_PLACEMENT_3D('',#1396,#1091,#1092);
#928=AXIS2_PLACEMENT_3D('',#1399,#1094,#1095);
#929=AXIS2_PLACEMENT_3D('',#1400,#1096,#1097);
#930=AXIS2_PLACEMENT_3D('',#1402,#1098,#1099);
#931=AXIS2_PLACEMENT_3D('',#1406,#1101,#1102);
#932=AXIS2_PLACEMENT_3D('',#1410,#1104,#1105);
#933=AXIS2_PLACEMENT_3D('',#1414,#1107,#1108);
#934=AXIS2_PLACEMENT_3D('',#1416,#1110,#1111);
#935=AXIS2_PLACEMENT_3D('',#1420,#1114,#1115);
#936=AXIS2_PLACEMENT_3D('',#1422,#1116,#1117);
#937=AXIS2_PLACEMENT_3D('',#1424,#1118,#1119);
#938=AXIS2_PLACEMENT_3D('',#1426,#1120,#1121);
#939=AXIS2_PLACEMENT_3D('',#1430,#1123,#1124);
#940=AXIS2_PLACEMENT_3D('',#1434,#1126,#1127);
#941=AXIS2_PLACEMENT_3D('',#1438,#1129,#1130);
#942=AXIS2_PLACEMENT_3D('',#1442,#1132,#1133);
#943=AXIS2_PLACEMENT_3D('',#1444,#1135,#1136);
#944=AXIS2_PLACEMENT_3D('',#1446,#1138,#1139);
#945=AXIS2_PLACEMENT_3D('',#1447,#1140,#1141);
#946=AXIS2_PLACEMENT_3D('',#1450,#1143,#1144);
#947=AXIS2_PLACEMENT_3D('',#1451,#1145,#1146);
#948=AXIS2_PLACEMENT_3D('',#1454,#1149,#1150);
#949=AXIS2_PLACEMENT_3D('',#1455,#1151,#1152);
#950=AXIS2_PLACEMENT_3D('',#1456,#1153,#1154);
#951=AXIS2_PLACEMENT_3D('',#1459,#1156,#1157);
#952=AXIS2_PLACEMENT_3D('',#1460,#1158,#1159);
#953=AXIS2_PLACEMENT_3D('',#1464,#1162,#1163);
#954=AXIS2_PLACEMENT_3D('',#1466,#1164,#1165);
#955=AXIS2_PLACEMENT_3D('',#1468,#1167,#1168);
#956=AXIS2_PLACEMENT_3D('',#1470,#1170,#1171);
#957=AXIS2_PLACEMENT_3D('',#1472,#1173,#1174);
#958=AXIS2_PLACEMENT_3D('',#1474,#1176,#1177);
#959=AXIS2_PLACEMENT_3D('',#1476,#1179,#1180);
#960=AXIS2_PLACEMENT_3D('',#1478,#1182,#1183);
#961=AXIS2_PLACEMENT_3D('',#1480,#1185,#1186);
#962=AXIS2_PLACEMENT_3D('',#1482,#1188,#1189);
#963=AXIS2_PLACEMENT_3D('',#1484,#1191,#1192);
#964=AXIS2_PLACEMENT_3D('',#1485,#1193,#1194);
#965=AXIS2_PLACEMENT_3D('',#1487,#1195,#1196);
#966=AXIS2_PLACEMENT_3D('',#1489,#1198,#1199);
#967=AXIS2_PLACEMENT_3D('',#1493,#1202,#1203);
#968=AXIS2_PLACEMENT_3D('',#1495,#1204,#1205);
#969=AXIS2_PLACEMENT_3D('',#1497,#1207,#1208);
#970=AXIS2_PLACEMENT_3D('',#1501,#1211,#1212);
#971=AXIS2_PLACEMENT_3D('',#1502,#1213,#1214);
#972=AXIS2_PLACEMENT_3D('',#1503,#1215,#1216);
#973=AXIS2_PLACEMENT_3D('',#1505,#1218,#1219);
#974=AXIS2_PLACEMENT_3D('',#1507,#1221,#1222);
#975=AXIS2_PLACEMENT_3D('',#1509,#1224,#1225);
#976=AXIS2_PLACEMENT_3D('',#1511,#1227,#1228);
#977=AXIS2_PLACEMENT_3D('',#1513,#1230,#1231);
#978=AXIS2_PLACEMENT_3D('',#1515,#1233,#1234);
#979=AXIS2_PLACEMENT_3D('',#1517,#1236,#1237);
#980=AXIS2_PLACEMENT_3D('',#1519,#1239,#1240);
#981=AXIS2_PLACEMENT_3D('',#1521,#1242,#1243);
#982=AXIS2_PLACEMENT_3D('',#1522,#1244,#1245);
#983=AXIS2_PLACEMENT_3D('',#1523,#1246,#1247);
#984=DIRECTION('axis',(0.,0.,1.));
#985=DIRECTION('refdir',(1.,0.,0.));
#986=DIRECTION('center_axis',(0.,1.,0.));
#987=DIRECTION('ref_axis',(6.12323399573677E-17,0.,-1.));
#988=DIRECTION('',(1.,0.,6.12323399573677E-17));
#989=DIRECTION('',(6.12323399573677E-17,0.,-1.));
#990=DIRECTION('',(-1.,0.,-6.12323399573677E-17));
#991=DIRECTION('',(6.12323399573677E-17,0.,-1.));
#992=DIRECTION('center_axis',(0.,1.,0.));
#993=DIRECTION('ref_axis',(0.,0.,1.));
#994=DIRECTION('',(0.,1.,0.));
#995=DIRECTION('center_axis',(0.,-1.,0.));
#996=DIRECTION('ref_axis',(1.,0.,-8.32667268468868E-16));
#997=DIRECTION('',(0.,1.,0.));
#998=DIRECTION('center_axis',(0.,1.,0.));
#999=DIRECTION('ref_axis',(0.0770327173725494,0.,0.997028565515653));
#1000=DIRECTION('center_axis',(0.,-1.,0.));
#1001=DIRECTION('ref_axis',(0.,0.,-1.));
#1002=DIRECTION('',(-1.,0.,-6.12323399573677E-17));
#1003=DIRECTION('center_axis',(0.,-1.,0.));
#1004=DIRECTION('ref_axis',(1.,0.,-8.32667268468868E-16));
#1005=DIRECTION('',(0.,0.,-1.));
#1006=DIRECTION('center_axis',(1.,0.,6.12323399573677E-17));
#1007=DIRECTION('ref_axis',(6.12323399573677E-17,0.,-1.));
#1008=DIRECTION('',(0.,-1.,0.));
#1009=DIRECTION('',(-6.12323399573677E-17,-2.03905113693134E-17,1.));
#1010=DIRECTION('center_axis',(1.,0.,6.12323399573677E-17));
#1011=DIRECTION('ref_axis',(-2.71926214689392E-30,-1.,4.44089209850085E-14));
#1012=DIRECTION('',(2.01160865894109E-17,-0.944496796706159,-0.328520624941273));
#1013=DIRECTION('center_axis',(1.,0.,6.12323399573677E-17));
#1014=DIRECTION('ref_axis',(-1.9931918965651E-17,0.945537585866064,0.32551293939654));
#1015=DIRECTION('',(-5.78974789002201E-17,-0.325512939396516,0.945537585866072));
#1016=DIRECTION('center_axis',(1.,0.,6.12323399573677E-17));
#1017=DIRECTION('ref_axis',(5.78738197071863E-17,0.326633135958221,-0.945151201921734));
#1018=DIRECTION('',(-2.00005112223363E-17,0.945151201921726,0.326633135958244));
#1019=DIRECTION('center_axis',(1.,0.,6.12323399573677E-17));
#1020=DIRECTION('ref_axis',(-5.78738197071866E-17,-0.326633135958206,0.945151201921739));
#1021=DIRECTION('',(-6.12323399573677E-17,0.,1.));
#1022=DIRECTION('center_axis',(1.,0.,6.12323399573677E-17));
#1023=DIRECTION('ref_axis',(2.76175061793887E-31,-1.,-4.51028103753949E-15));
#1024=DIRECTION('center_axis',(1.,0.,6.12323399573677E-17));
#1025=DIRECTION('ref_axis',(5.9893628785418E-17,0.207960675547499,-0.978137187426093));
#1026=DIRECTION('center_axis',(1.,0.,6.12323399573677E-17));
#1027=DIRECTION('ref_axis',(5.72388509081918E-17,0.355223566108243,-0.934781374483544));
#1028=DIRECTION('',(0.,0.,1.));
#1029=DIRECTION('',(0.,0.,1.));
#1030=DIRECTION('',(-0.0770327173725504,0.,-0.997028565515653));
#1031=DIRECTION('center_axis',(0.,0.,1.));
#1032=DIRECTION('ref_axis',(1.,0.,0.));
#1033=DIRECTION('',(-1.,-2.03905113693134E-17,0.));
#1034=DIRECTION('center_axis',(0.,0.,1.));
#1035=DIRECTION('ref_axis',(3.55271367880052E-15,1.,0.));
#1036=DIRECTION('',(-0.260298460573282,-0.965528203328717,0.));
#1037=DIRECTION('center_axis',(0.,0.,-1.));
#1038=DIRECTION('ref_axis',(-0.966214650493122,-0.257738722687247,0.));
#1039=DIRECTION('',(-0.257738722687241,0.966214650493124,0.));
#1040=DIRECTION('center_axis',(0.,0.,1.));
#1041=DIRECTION('ref_axis',(0.966214650493121,0.257738722687251,0.));
#1042=DIRECTION('',(-1.,-3.20834253731334E-16,0.));
#1043=DIRECTION('',(0.,-1.,0.));
#1044=DIRECTION('',(1.,-6.88035329049497E-17,0.));
#1045=DIRECTION('center_axis',(0.,0.,1.));
#1046=DIRECTION('ref_axis',(0.,-1.,0.));
#1047=DIRECTION('',(0.292218535599021,0.956351571051339,0.));
#1048=DIRECTION('center_axis',(0.,0.,-1.));
#1049=DIRECTION('ref_axis',(0.,1.,0.));
#1050=DIRECTION('center_axis',(0.,0.,1.));
#1051=DIRECTION('ref_axis',(1.,0.,0.));
#1052=DIRECTION('center_axis',(2.03905113693134E-17,-1.,0.));
#1053=DIRECTION('ref_axis',(-1.,-2.03905113693134E-17,0.));
#1054=DIRECTION('',(-1.,-2.03905113693134E-17,0.));
#1055=DIRECTION('',(0.,0.,-1.));
#1056=DIRECTION('center_axis',(0.,1.,0.));
#1057=DIRECTION('ref_axis',(0.,0.,1.));
#1058=DIRECTION('center_axis',(0.,1.,0.));
#1059=DIRECTION('ref_axis',(0.0770327173725505,0.,0.997028565515653));
#1060=DIRECTION('',(0.,-1.,0.));
#1061=DIRECTION('center_axis',(1.26432627567711E-31,1.,6.88035329049497E-17));
#1062=DIRECTION('ref_axis',(1.83758917935762E-15,-6.88035329049497E-17,
1.));
#1063=DIRECTION('',(1.,0.,-1.83758917935762E-15));
#1064=DIRECTION('',(1.83758917935762E-15,-6.88035329049497E-17,1.));
#1065=DIRECTION('',(1.,0.,-1.83758917935762E-15));
#1066=DIRECTION('',(-1.83758917935762E-15,6.88035329049497E-17,-1.));
#1067=DIRECTION('center_axis',(0.,-1.,0.));
#1068=DIRECTION('ref_axis',(0.,0.,1.));
#1069=DIRECTION('',(0.,1.,0.));
#1070=DIRECTION('center_axis',(0.,-1.,0.));
#1071=DIRECTION('ref_axis',(0.,0.,1.));
#1072=DIRECTION('',(0.,1.,0.));
#1073=DIRECTION('center_axis',(0.,-1.,0.));
#1074=DIRECTION('ref_axis',(0.,0.,1.));
#1075=DIRECTION('center_axis',(0.,-1.,0.));
#1076=DIRECTION('ref_axis',(0.,0.,-1.));
#1077=DIRECTION('',(-1.,0.,1.83758917935762E-15));
#1078=DIRECTION('center_axis',(0.,-1.,0.));
#1079=DIRECTION('ref_axis',(0.,0.,1.));
#1080=DIRECTION('',(0.,0.,1.));
#1081=DIRECTION('center_axis',(-1.,0.,1.83758917935762E-15));
#1082=DIRECTION('ref_axis',(1.83758917935762E-15,0.,1.));
#1083=DIRECTION('',(0.,-1.,0.));
#1084=DIRECTION('',(-1.83758917935762E-15,-3.20834253731334E-16,-1.));
#1085=DIRECTION('center_axis',(1.,0.,-1.83758917935762E-15));
#1086=DIRECTION('ref_axis',(-1.74024001435722E-15,0.321164468818237,-0.94702343369565));
#1087=DIRECTION('',(5.90168352695009E-16,0.947023433695562,0.321164468818498));
#1088=DIRECTION('center_axis',(-1.,0.,1.83758917935762E-15));
#1089=DIRECTION('ref_axis',(1.740240014357E-15,-0.321164468818595,0.947023433695529));
#1090=DIRECTION('',(-1.73750963646342E-15,0.325512939396517,-0.945537585866072));
#1091=DIRECTION('center_axis',(1.,0.,-1.83758917935762E-15));
#1092=DIRECTION('ref_axis',(-1.73750963646337E-15,0.325512939396593,-0.945537585866046));
#1093=DIRECTION('',(5.98159055175912E-16,0.945537585866076,0.325512939396506));
#1094=DIRECTION('center_axis',(1.,0.,-1.83758917935762E-15));
#1095=DIRECTION('ref_axis',(0.,1.,0.));
#1096=DIRECTION('center_axis',(-1.,0.,1.83758917935762E-15));
#1097=DIRECTION('ref_axis',(1.83758917935762E-15,0.,1.));
#1098=DIRECTION('center_axis',(-1.,0.,1.83758917935762E-15));
#1099=DIRECTION('ref_axis',(0.,1.,0.));
#1100=DIRECTION('',(5.98159055175912E-16,0.945537585866076,0.325512939396506));
#1101=DIRECTION('center_axis',(-1.,0.,1.83758917935762E-15));
#1102=DIRECTION('ref_axis',(-1.73750963646337E-15,0.325512939396593,-0.945537585866046));
#1103=DIRECTION('',(1.73750963646342E-15,-0.325512939396517,0.945537585866072));
#1104=DIRECTION('center_axis',(-1.,0.,1.83758917935762E-15));
#1105=DIRECTION('ref_axis',(1.740240014357E-15,-0.321164468818595,0.947023433695529));
#1106=DIRECTION('',(-5.90168352695009E-16,-0.947023433695562,-0.321164468818498));
#1107=DIRECTION('center_axis',(-1.,0.,1.83758917935762E-15));
#1108=DIRECTION('ref_axis',(-1.74024001435722E-15,0.321164468818237,-0.94702343369565));
#1109=DIRECTION('',(-1.83758917935762E-15,-3.20834253731334E-16,-1.));
#1110=DIRECTION('center_axis',(3.20834253731334E-16,-1.,0.));
#1111=DIRECTION('ref_axis',(-1.,-3.20834253731334E-16,0.));
#1112=DIRECTION('',(0.,0.,-1.));
#1113=DIRECTION('',(-1.,-3.20834253731334E-16,0.));
#1114=DIRECTION('center_axis',(1.,0.,6.12323399573677E-17));
#1115=DIRECTION('ref_axis',(6.12323399573677E-17,0.,-1.));
#1116=DIRECTION('center_axis',(-1.,0.,-6.12323399573677E-17));
#1117=DIRECTION('ref_axis',(5.72388509081918E-17,0.355223566108243,-0.934781374483544));
#1118=DIRECTION('center_axis',(-1.,0.,-6.12323399573677E-17));
#1119=DIRECTION('ref_axis',(5.9893628785418E-17,0.207960675547499,-0.978137187426093));
#1120=DIRECTION('center_axis',(-1.,0.,-6.12323399573677E-17));
#1121=DIRECTION('ref_axis',(2.76175061793887E-31,-1.,-4.51028103753949E-15));
#1122=DIRECTION('',(-6.12323399573677E-17,0.,1.));
#1123=DIRECTION('center_axis',(-1.,0.,-6.12323399573677E-17));
#1124=DIRECTION('ref_axis',(-5.78738197071866E-17,-0.326633135958206,0.945151201921739));
#1125=DIRECTION('',(-2.00005112223363E-17,0.945151201921726,0.326633135958244));
#1126=DIRECTION('center_axis',(1.,0.,6.12323399573677E-17));
#1127=DIRECTION('ref_axis',(5.78738197071863E-17,0.326633135958221,-0.945151201921734));
#1128=DIRECTION('',(-5.78974789002201E-17,-0.325512939396516,0.945537585866072));
#1129=DIRECTION('center_axis',(1.,0.,6.12323399573677E-17));
#1130=DIRECTION('ref_axis',(-1.9931918965651E-17,0.945537585866064,0.32551293939654));
#1131=DIRECTION('',(2.01160865894109E-17,-0.944496796706159,-0.328520624941273));
#1132=DIRECTION('center_axis',(-1.,0.,-6.12323399573677E-17));
#1133=DIRECTION('ref_axis',(-2.71926214689392E-30,-1.,4.44089209850085E-14));
#1134=DIRECTION('',(-6.12323399573677E-17,-2.03905113693134E-17,1.));
#1135=DIRECTION('center_axis',(0.,1.,0.));
#1136=DIRECTION('ref_axis',(0.,0.,1.));
#1137=DIRECTION('',(0.,0.,-1.));
#1138=DIRECTION('center_axis',(0.,1.,0.));
#1139=DIRECTION('ref_axis',(-1.,0.,1.94289029309403E-15));
#1140=DIRECTION('center_axis',(0.,0.,-1.));
#1141=DIRECTION('ref_axis',(1.,0.,0.));
#1142=DIRECTION('',(0.,0.,1.));
#1143=DIRECTION('center_axis',(0.,0.,-1.));
#1144=DIRECTION('ref_axis',(1.,0.,0.));
#1145=DIRECTION('center_axis',(-1.,0.,-6.12323399573677E-17));
#1146=DIRECTION('ref_axis',(5.9893628785418E-17,0.207960675547499,-0.978137187426093));
#1147=DIRECTION('',(-1.,0.,-6.12323399573677E-17));
#1148=DIRECTION('',(-1.,0.,-6.12323399573677E-17));
#1149=DIRECTION('center_axis',(-1.,0.,-6.12323399573677E-17));
#1150=DIRECTION('ref_axis',(5.72388509081918E-17,0.355223566108243,-0.934781374483544));
#1151=DIRECTION('center_axis',(0.,1.,0.));
#1152=DIRECTION('ref_axis',(0.,0.,1.));
#1153=DIRECTION('center_axis',(0.,0.,-1.));
#1154=DIRECTION('ref_axis',(0.,1.,0.));
#1155=DIRECTION('',(0.,0.,-1.));
#1156=DIRECTION('center_axis',(0.,0.,1.));
#1157=DIRECTION('ref_axis',(0.,1.,0.));
#1158=DIRECTION('center_axis',(-0.956351571051339,0.292218535599021,0.));
#1159=DIRECTION('ref_axis',(0.292218535599021,0.956351571051339,0.));
#1160=DIRECTION('',(-0.292218535599021,-0.956351571051339,0.));
#1161=DIRECTION('',(0.,0.,-1.));
#1162=DIRECTION('center_axis',(0.,0.,-1.));
#1163=DIRECTION('ref_axis',(0.,-1.,0.));
#1164=DIRECTION('center_axis',(0.,0.,1.));
#1165=DIRECTION('ref_axis',(0.,-1.,0.));
#1166=DIRECTION('',(0.,0.,-1.));
#1167=DIRECTION('center_axis',(6.88035329049497E-17,1.,0.));
#1168=DIRECTION('ref_axis',(1.,-6.88035329049497E-17,0.));
#1169=DIRECTION('',(-1.,6.88035329049497E-17,0.));
#1170=DIRECTION('center_axis',(1.,0.,-1.83758917935762E-15));
#1171=DIRECTION('ref_axis',(0.,1.,0.));
#1172=DIRECTION('',(1.,0.,-1.83758917935762E-15));
#1173=DIRECTION('center_axis',(-1.73750963646343E-15,0.325512939396506,
-0.945537585866076));
#1174=DIRECTION('ref_axis',(5.98159055175912E-16,0.945537585866076,0.325512939396506));
#1175=DIRECTION('',(1.,0.,-1.83758917935762E-15));
#1176=DIRECTION('center_axis',(1.,0.,-1.83758917935762E-15));
#1177=DIRECTION('ref_axis',(-1.73750963646337E-15,0.325512939396593,-0.945537585866046));
#1178=DIRECTION('',(1.,0.,-1.83758917935762E-15));
#1179=DIRECTION('center_axis',(-5.98159055175931E-16,-0.945537585866072,
-0.325512939396517));
#1180=DIRECTION('ref_axis',(-1.73750963646342E-15,0.325512939396517,-0.945537585866072));
#1181=DIRECTION('',(1.,0.,-1.83758917935762E-15));
#1182=DIRECTION('center_axis',(1.,0.,-1.83758917935762E-15));
#1183=DIRECTION('ref_axis',(1.740240014357E-15,-0.321164468818595,0.947023433695529));
#1184=DIRECTION('',(1.,0.,-1.83758917935762E-15));
#1185=DIRECTION('center_axis',(-1.74024001435706E-15,0.321164468818498,
-0.947023433695562));
#1186=DIRECTION('ref_axis',(5.90168352695009E-16,0.947023433695562,0.321164468818498));
#1187=DIRECTION('',(1.,0.,-1.83758917935762E-15));
#1188=DIRECTION('center_axis',(1.,0.,-1.83758917935762E-15));
#1189=DIRECTION('ref_axis',(-1.74024001435722E-15,0.321164468818237,-0.94702343369565));
#1190=DIRECTION('',(1.,0.,-1.83758917935762E-15));
#1191=DIRECTION('center_axis',(5.89561553023975E-31,-1.,3.20834253731334E-16));
#1192=DIRECTION('ref_axis',(-1.83758917935762E-15,-3.20834253731334E-16,
-1.));
#1193=DIRECTION('center_axis',(0.,0.,-1.));
#1194=DIRECTION('ref_axis',(0.966214650493121,0.257738722687251,0.));
#1195=DIRECTION('center_axis',(0.,0.,1.));
#1196=DIRECTION('ref_axis',(0.966214650493121,0.257738722687251,0.));
#1197=DIRECTION('',(0.,0.,-1.));
#1198=DIRECTION('center_axis',(-0.966214650493124,-0.257738722687241,0.));
#1199=DIRECTION('ref_axis',(-0.257738722687241,0.966214650493124,0.));
#1200=DIRECTION('',(0.257738722687241,-0.966214650493124,0.));
#1201=DIRECTION('',(0.,0.,-1.));
#1202=DIRECTION('center_axis',(0.,0.,-1.));
#1203=DIRECTION('ref_axis',(-0.966214650493122,-0.257738722687247,0.));
#1204=DIRECTION('center_axis',(0.,0.,1.));
#1205=DIRECTION('ref_axis',(-0.966214650493122,-0.257738722687247,0.));
#1206=DIRECTION('',(0.,0.,-1.));
#1207=DIRECTION('center_axis',(0.965528203328717,-0.260298460573282,0.));
#1208=DIRECTION('ref_axis',(-0.260298460573282,-0.965528203328717,0.));
#1209=DIRECTION('',(-0.260298460573282,-0.965528203328717,0.));
#1210=DIRECTION('',(0.,0.,-1.));
#1211=DIRECTION('center_axis',(0.,0.,-1.));
#1212=DIRECTION('ref_axis',(3.55271367880052E-15,1.,0.));
#1213=DIRECTION('center_axis',(0.,0.,1.));
#1214=DIRECTION('ref_axis',(3.55271367880052E-15,1.,0.));
#1215=DIRECTION('center_axis',(1.24855872407037E-33,-1.,-2.03905113693134E-17));
#1216=DIRECTION('ref_axis',(-6.12323399573677E-17,-2.03905113693134E-17,
1.));
#1217=DIRECTION('',(-1.,0.,-6.12323399573677E-17));
#1218=DIRECTION('center_axis',(-1.,0.,-6.12323399573677E-17));
#1219=DIRECTION('ref_axis',(-2.71926214689392E-30,-1.,4.44089209850085E-14));
#1220=DIRECTION('',(-1.,0.,-6.12323399573677E-17));
#1221=DIRECTION('center_axis',(5.78337489445563E-17,0.328520624941273,-0.944496796706159));
#1222=DIRECTION('ref_axis',(2.01160865894109E-17,-0.944496796706159,-0.328520624941273));
#1223=DIRECTION('',(-1.,0.,-6.12323399573677E-17));
#1224=DIRECTION('center_axis',(-1.,0.,-6.12323399573677E-17));
#1225=DIRECTION('ref_axis',(-1.9931918965651E-17,0.945537585866064,0.32551293939654));
#1226=DIRECTION('',(-1.,0.,-6.12323399573677E-17));
#1227=DIRECTION('center_axis',(1.99319189656495E-17,-0.945537585866072,
-0.325512939396516));
#1228=DIRECTION('ref_axis',(-5.78974789002201E-17,-0.325512939396516,0.945537585866072));
#1229=DIRECTION('',(-1.,0.,-6.12323399573677E-17));
#1230=DIRECTION('center_axis',(-1.,0.,-6.12323399573677E-17));
#1231=DIRECTION('ref_axis',(5.78738197071863E-17,0.326633135958221,-0.945151201921734));
#1232=DIRECTION('',(-1.,0.,-6.12323399573677E-17));
#1233=DIRECTION('center_axis',(-5.78738197071858E-17,-0.326633135958244,
0.945151201921726));
#1234=DIRECTION('ref_axis',(-2.00005112223363E-17,0.945151201921726,0.326633135958244));
#1235=DIRECTION('',(-1.,0.,-6.12323399573677E-17));
#1236=DIRECTION('center_axis',(-1.,0.,-6.12323399573677E-17));
#1237=DIRECTION('ref_axis',(-5.78738197071866E-17,-0.326633135958206,0.945151201921739));
#1238=DIRECTION('',(-1.,0.,-6.12323399573677E-17));
#1239=DIRECTION('center_axis',(0.,-1.,0.));
#1240=DIRECTION('ref_axis',(-6.12323399573677E-17,0.,1.));
#1241=DIRECTION('',(-1.,0.,-6.12323399573677E-17));
#1242=DIRECTION('center_axis',(-1.,0.,-6.12323399573677E-17));
#1243=DIRECTION('ref_axis',(2.76175061793887E-31,-1.,-4.51028103753949E-15));
#1244=DIRECTION('center_axis',(0.,0.,1.));
#1245=DIRECTION('ref_axis',(1.,0.,0.));
#1246=DIRECTION('center_axis',(0.,-1.,0.));
#1247=DIRECTION('ref_axis',(0.,0.,1.));
#1248=CARTESIAN_POINT('',(0.,0.,0.));
#1249=CARTESIAN_POINT('Origin',(-34.1238934211693,35.,0.60363838371619));
#1250=CARTESIAN_POINT('',(-38.1238934211693,35.,-8.00000000000001));
#1251=CARTESIAN_POINT('',(-34.1238934211693,35.,-8.00000000000001));
#1252=CARTESIAN_POINT('',(-34.1238934211693,35.,-8.00000000000001));
#1253=CARTESIAN_POINT('',(-34.1238934211693,35.,-30.7071411696954));
#1254=CARTESIAN_POINT('',(-34.1238934211693,35.,0.60363838371619));
#1255=CARTESIAN_POINT('',(-38.1238934211693,35.,-30.7071411696954));
#1256=CARTESIAN_POINT('',(-34.1238934211693,35.,-30.7071411696954));
#1257=CARTESIAN_POINT('',(-38.1238934211693,35.,0.60363838371619));
#1258=CARTESIAN_POINT('Origin',(-42.1238934211693,17.5,-8.));
#1259=CARTESIAN_POINT('',(-38.1238934211693,1.55566020960586E-15,-8.00000000000001));
#1260=CARTESIAN_POINT('',(-38.1238934211693,-1.625,-8.00000000000001));
#1261=CARTESIAN_POINT('',(-42.1238934211693,1.37117120913003E-15,-4.));
#1262=CARTESIAN_POINT('Origin',(-42.1238934211693,2.22044604925031E-15,
-8.));
#1263=CARTESIAN_POINT('',(-42.1238934211693,34.9669398623335,-4.));
#1264=CARTESIAN_POINT('',(-42.1238934211693,-1.625,-4.));
#1265=CARTESIAN_POINT('',(-41.8157625516791,35.,-4.01188573793739));
#1266=CARTESIAN_POINT('Ctrl Pts',(-42.1238934211693,34.9669398623335,-4.));
#1267=CARTESIAN_POINT('Ctrl Pts',(-42.0218391153223,34.9889393841394,-4.));
#1268=CARTESIAN_POINT('Ctrl Pts',(-41.9186474056545,35.,-4.0039366177694));
#1269=CARTESIAN_POINT('Ctrl Pts',(-41.8157625516791,35.,-4.01188573793739));
#1270=CARTESIAN_POINT('Origin',(-42.1238934211693,35.,-8.));
#1271=CARTESIAN_POINT('Origin',(-34.1238934211693,2.22044604925031E-15,
-8.00000000000001));
#1272=CARTESIAN_POINT('',(-34.1238934211693,1.55566020960586E-15,-8.00000000000001));
#1273=CARTESIAN_POINT('',(-34.1238934211693,1.55566020960586E-15,-8.00000000000001));
#1274=CARTESIAN_POINT('',(-42.1238934211693,1.37117120913003E-15,0.));
#1275=CARTESIAN_POINT('Origin',(-42.1238934211693,2.22044604925031E-15,
-8.));
#1276=CARTESIAN_POINT('',(-42.1238934211693,1.37117120913003E-15,0.));
#1277=CARTESIAN_POINT('Origin',(-34.1238934211693,-3.25000000000001,51.6720724275545));
#1278=CARTESIAN_POINT('',(-34.1238934211693,-1.625,-8.00000000000001));
#1279=CARTESIAN_POINT('',(-34.1238934211693,1.82145964977565E-15,-21.0354474861186));
#1280=CARTESIAN_POINT('',(-34.1238934211693,1.82145964977565E-15,-21.0354474861186));
#1281=CARTESIAN_POINT('',(-34.1238934211693,0.132852062494127,-21.1298971657893));
#1282=CARTESIAN_POINT('Origin',(-34.1238934211693,0.0999999999999967,-21.0354474861187));
#1283=CARTESIAN_POINT('',(-34.1238934211693,11.4052492382761,-17.2090633655173));
#1284=CARTESIAN_POINT('',(-34.1238934211693,11.4052492382761,-17.2090633655173));
#1285=CARTESIAN_POINT('',(-34.1238934211693,11.5326550593569,-17.2709617512482));
#1286=CARTESIAN_POINT('Origin',(-34.1238934211693,11.4381013007702,-17.3035130451879));
#1287=CARTESIAN_POINT('',(-34.1238934211693,13.5674872552457,-23.181664796449));
#1288=CARTESIAN_POINT('',(-34.1238934211693,13.5674872552457,-23.181664796449));
#1289=CARTESIAN_POINT('',(-34.1238934211693,13.5055968102549,-23.3087312105809));
#1290=CARTESIAN_POINT('Origin',(-34.1238934211693,13.472933496659,-23.2142160903887));
#1291=CARTESIAN_POINT('',(-34.1238934211693,0.067336686404176,-27.9528358122057));
#1292=CARTESIAN_POINT('',(-34.1238934211693,0.067336686404176,-27.9528358122057));
#1293=CARTESIAN_POINT('',(-34.1238934211693,8.67361737988404E-17,-28.0473509323979));
#1294=CARTESIAN_POINT('Origin',(-34.1238934211693,0.0999999999999949,-28.0473509323979));
#1295=CARTESIAN_POINT('',(-34.1238934211693,0.,-40.9526435135339));
#1296=CARTESIAN_POINT('',(-34.1238934211693,0.,-40.9526435135339));
#1297=CARTESIAN_POINT('',(-34.1238934211693,0.120796067554751,-41.0504572322766));
#1298=CARTESIAN_POINT('Origin',(-34.1238934211693,0.100000000000005,-40.9526435135339));
#1299=CARTESIAN_POINT('',(-34.1238934211693,34.9355223566108,-30.8006193071437));
#1300=CARTESIAN_POINT('Origin',(-34.1238934211693,-49.0436198318451,190.192989937147));
#1301=CARTESIAN_POINT('Origin',(-34.1238934211693,34.9,-30.7071411696954));
#1302=CARTESIAN_POINT('Ctrl Pts',(-42.1238934211693,34.9669398623335,0.));
#1303=CARTESIAN_POINT('Ctrl Pts',(-41.9197848094753,34.9889393841394,0.));
#1304=CARTESIAN_POINT('Ctrl Pts',(-41.7134013901397,35.,-0.00787323553879238));
#1305=CARTESIAN_POINT('Ctrl Pts',(-41.5076316821889,35.,-0.0237714758747765));
#1306=CARTESIAN_POINT('Ctrl Pts',(-42.1238934211693,34.9669398623335,-1.33333333333333));
#1307=CARTESIAN_POINT('Ctrl Pts',(-41.9538029114243,34.9889393841394,-1.33333333333333));
#1308=CARTESIAN_POINT('Ctrl Pts',(-41.7818167286447,35.,-1.33989436294899));
#1309=CARTESIAN_POINT('Ctrl Pts',(-41.610341972019,35.,-1.35314289656231));
#1310=CARTESIAN_POINT('Ctrl Pts',(-42.1238934211693,34.9669398623335,-2.66666666666667));
#1311=CARTESIAN_POINT('Ctrl Pts',(-41.9878210133733,34.9889393841394,-2.66666666666667));
#1312=CARTESIAN_POINT('Ctrl Pts',(-41.8502320671496,35.,-2.6719154903592));
#1313=CARTESIAN_POINT('Ctrl Pts',(-41.713052261849,35.,-2.68251431724985));
#1314=CARTESIAN_POINT('Ctrl Pts',(-42.1238934211693,34.9669398623335,-4.));
#1315=CARTESIAN_POINT('Ctrl Pts',(-42.0218391153223,34.9889393841394,-4.));
#1316=CARTESIAN_POINT('Ctrl Pts',(-41.9186474056545,35.,-4.0039366177694));
#1317=CARTESIAN_POINT('Ctrl Pts',(-41.8157625516791,35.,-4.01188573793739));
#1318=CARTESIAN_POINT('',(-42.1238934211693,34.9669398623335,-2.24));
#1319=CARTESIAN_POINT('',(-42.1238934211693,34.9669398623335,0.));
#1320=CARTESIAN_POINT('',(-42.1238934211693,34.9669398623335,0.));
#1321=CARTESIAN_POINT('',(-42.1238934211693,34.9669398623335,0.));
#1322=CARTESIAN_POINT('',(-41.5076316821889,35.,-0.0237714758747765));
#1323=CARTESIAN_POINT('Ctrl Pts',(-41.5076316821889,35.,-0.0237714758747765));
#1324=CARTESIAN_POINT('Ctrl Pts',(-41.7134013901397,35.,-0.00787323553879238));
#1325=CARTESIAN_POINT('Ctrl Pts',(-41.9197848094753,34.9889393841394,0.));
#1326=CARTESIAN_POINT('Ctrl Pts',(-42.1238934211693,34.9669398623335,0.));
#1327=CARTESIAN_POINT('',(-41.5076316821889,35.,-0.0237714758747765));
#1328=CARTESIAN_POINT('Origin',(-92.7481790063852,-3.25000000000001,-4.));
#1329=CARTESIAN_POINT('',(-109.369446232246,0.,-4.));
#1330=CARTESIAN_POINT('',(-20.0406590927121,1.82145964977565E-15,-4.));
#1331=CARTESIAN_POINT('',(-114.19708724889,-3.69850769713359,-4.));
#1332=CARTESIAN_POINT('Origin',(-109.369446232246,-4.99999999999998,-4.));
#1333=CARTESIAN_POINT('',(-122.693010271706,-35.2125369148729,-4.));
#1334=CARTESIAN_POINT('',(-114.19708724889,-3.69850769713359,-4.));
#1335=CARTESIAN_POINT('',(-139.112824529192,-35.1907791428416,-4.));
#1336=CARTESIAN_POINT('Origin',(-130.9,-33.,-4.));
#1337=CARTESIAN_POINT('',(-147.510005316018,-3.71130638656378,-4.));
#1338=CARTESIAN_POINT('',(-147.510005316018,-3.71130638656378,-4.));
#1339=CARTESIAN_POINT('',(-152.341078568483,4.44089209850063E-15,-4.));
#1340=CARTESIAN_POINT('Origin',(-152.341078568483,-5.00000000000005,-4.));
#1341=CARTESIAN_POINT('',(-161.076106578831,-2.80249619302206E-15,-4.));
#1342=CARTESIAN_POINT('',(-152.341078568483,0.,-4.));
#1343=CARTESIAN_POINT('',(-161.076106578831,17.,-4.));
#1344=CARTESIAN_POINT('',(-161.076106578831,-1.625,-4.));
#1345=CARTESIAN_POINT('',(-53.1004250624219,17.,-4.));
#1346=CARTESIAN_POINT('',(-53.1004250624219,17.,-4.));
#1347=CARTESIAN_POINT('',(-48.3186672071652,20.5389073220049,-4.));
#1348=CARTESIAN_POINT('Origin',(-53.1004250624219,22.,-4.));
#1349=CARTESIAN_POINT('',(-44.5487996757009,32.8766556067971,-4.));
#1350=CARTESIAN_POINT('',(-44.5487996757009,32.8766556067971,-4.));
#1351=CARTESIAN_POINT('Origin',(-41.6797449625469,32.,-4.));
#1352=CARTESIAN_POINT('',(-136.1,-33.,-4.));
#1353=CARTESIAN_POINT('Origin',(-130.9,-33.,-4.));
#1354=CARTESIAN_POINT('Origin',(-20.0406590927121,1.82145964977565E-15,
0.));
#1355=CARTESIAN_POINT('',(-109.369446232246,0.,0.));
#1356=CARTESIAN_POINT('',(-20.0406590927121,1.82145964977565E-15,0.));
#1357=CARTESIAN_POINT('',(-109.369446232246,0.,0.));
#1358=CARTESIAN_POINT('Origin',(-42.1238934211693,17.5,-8.));
#1359=CARTESIAN_POINT('Origin',(-42.1238934211693,35.,-8.));
#1360=CARTESIAN_POINT('',(-42.1238934211693,-1.625,0.));
#1361=CARTESIAN_POINT('Origin',(-169.076106578831,17.,-20.0656034084132));
#1362=CARTESIAN_POINT('',(-169.076106578831,17.,-8.00000000000001));
#1363=CARTESIAN_POINT('',(-165.076106578831,17.,-8.00000000000001));
#1364=CARTESIAN_POINT('',(-169.076106578831,17.,-7.99999999999997));
#1365=CARTESIAN_POINT('',(-165.076106578831,17.,-20.0656034084132));
#1366=CARTESIAN_POINT('',(-165.07610657883,17.,109.023468358747));
#1367=CARTESIAN_POINT('',(-169.076106578831,17.,-20.0656034084132));
#1368=CARTESIAN_POINT('',(-169.076106578831,17.,-20.0656034084132));
#1369=CARTESIAN_POINT('',(-169.07610657883,17.,109.023468358747));
#1370=CARTESIAN_POINT('Origin',(-161.076106578831,8.5,-8.));
#1371=CARTESIAN_POINT('',(-169.076106578831,-5.70533613250395E-15,-8.00000000000001));
#1372=CARTESIAN_POINT('',(-169.076106578831,-1.625,-8.00000000000001));
#1373=CARTESIAN_POINT('',(-161.076106578831,-2.80249619302206E-15,0.));
#1374=CARTESIAN_POINT('Origin',(-161.076106578831,-3.33066907387547E-15,
-8.));
#1375=CARTESIAN_POINT('',(-161.076106578831,17.,0.));
#1376=CARTESIAN_POINT('',(-161.076106578831,-1.625,0.));
#1377=CARTESIAN_POINT('Origin',(-161.076106578831,17.,-8.));
#1378=CARTESIAN_POINT('Origin',(-165.076106578831,-5.55111512312578E-15,
-7.99999999999999));
#1379=CARTESIAN_POINT('',(-165.076106578831,-5.70533613250395E-15,-8.00000000000001));
#1380=CARTESIAN_POINT('',(-169.076106578831,-5.70533613250395E-15,-8.00000000000001));
#1381=CARTESIAN_POINT('Origin',(-161.076106578831,-3.33066907387547E-15,
-8.));
#1382=CARTESIAN_POINT('',(-161.076106578831,-2.80249619302206E-15,0.));
#1383=CARTESIAN_POINT('Origin',(-165.076106578831,-3.25000000000001,69.3757144147841));
#1384=CARTESIAN_POINT('',(-165.076106578831,-1.625,-8.00000000000001));
#1385=CARTESIAN_POINT('',(-165.076106578831,-8.88178419700125E-15,-21.0365995273882));
#1386=CARTESIAN_POINT('',(-165.076106578831,0.,9.78281485268587));
#1387=CARTESIAN_POINT('',(-165.076106578831,0.132116446881851,-21.1313018707578));
#1388=CARTESIAN_POINT('Origin',(-165.076106578831,0.100000000000017,-21.0365995273882));
#1389=CARTESIAN_POINT('',(-165.076106578831,11.4057318474613,-17.3080757783873));
#1390=CARTESIAN_POINT('',(-165.076106578831,11.4057318474613,-17.3080757783873));
#1391=CARTESIAN_POINT('',(-165.076106578831,11.5324020529298,-17.3702268278172));
#1392=CARTESIAN_POINT('Origin',(-165.076106578831,11.4378482943432,-17.4027781217568));
#1393=CARTESIAN_POINT('',(-165.076106578831,13.5674487060603,-23.2815528202441));
#1394=CARTESIAN_POINT('',(-165.076106578831,13.5674487060603,-23.2815528202441));
#1395=CARTESIAN_POINT('',(-165.076106578831,13.6945537585866,-23.3435552848911));
#1396=CARTESIAN_POINT('Origin',(-165.076106578831,13.662002464647,-23.2490015263044));
#1397=CARTESIAN_POINT('',(-165.076106578831,14.9765388181895,-22.9022161660114));
#1398=CARTESIAN_POINT('',(-165.076106578831,13.6945537585866,-23.3435552848911));
#1399=CARTESIAN_POINT('Origin',(-165.076106578831,14.0000000000001,-20.0656034084132));
#1400=CARTESIAN_POINT('Origin',(-169.076106578831,-3.25000000000001,69.3757144147841));
#1401=CARTESIAN_POINT('',(-169.076106578831,14.9765388181895,-22.9022161660114));
#1402=CARTESIAN_POINT('Origin',(-169.076106578831,14.0000000000001,-20.0656034084132));
#1403=CARTESIAN_POINT('',(-169.076106578831,13.6945537585866,-23.3435552848911));
#1404=CARTESIAN_POINT('',(-169.076106578831,13.6945537585866,-23.3435552848911));
#1405=CARTESIAN_POINT('',(-169.076106578831,13.5674487060604,-23.2815528202441));
#1406=CARTESIAN_POINT('Origin',(-169.076106578831,13.662002464647,-23.2490015263044));
#1407=CARTESIAN_POINT('',(-169.076106578831,11.5324020529298,-17.3702268278172));
#1408=CARTESIAN_POINT('',(-169.076106578831,13.5674487060603,-23.2815528202441));
#1409=CARTESIAN_POINT('',(-169.076106578831,11.4057318474613,-17.3080757783873));
#1410=CARTESIAN_POINT('Origin',(-169.076106578831,11.4378482943432,-17.4027781217568));
#1411=CARTESIAN_POINT('',(-169.076106578831,0.132116446881849,-21.1313018707578));
#1412=CARTESIAN_POINT('',(-169.076106578831,11.4057318474613,-17.3080757783873));
#1413=CARTESIAN_POINT('',(-169.076106578831,-9.8879238130678E-15,-21.0365995273882));
#1414=CARTESIAN_POINT('Origin',(-169.076106578831,0.100000000000017,-21.0365995273882));
#1415=CARTESIAN_POINT('',(-169.076106578831,0.,9.78281485268587));
#1416=CARTESIAN_POINT('Origin',(-152.341078568483,0.,0.));
#1417=CARTESIAN_POINT('',(-152.341078568483,4.44089209850063E-15,0.));
#1418=CARTESIAN_POINT('',(-152.341078568483,4.44089209850063E-15,0.));
#1419=CARTESIAN_POINT('',(-152.341078568483,0.,0.));
#1420=CARTESIAN_POINT('Origin',(-38.1238934211693,-3.25000000000001,51.6720724275545));
#1421=CARTESIAN_POINT('',(-38.1238934211693,34.9355223566108,-30.8006193071437));
#1422=CARTESIAN_POINT('Origin',(-38.1238934211693,34.9,-30.7071411696954));
#1423=CARTESIAN_POINT('',(-38.1238934211693,0.120796067554751,-41.0504572322766));
#1424=CARTESIAN_POINT('Origin',(-38.1238934211693,-49.0436198318451,190.192989937147));
#1425=CARTESIAN_POINT('',(-38.1238934211693,0.,-40.9526435135339));
#1426=CARTESIAN_POINT('Origin',(-38.1238934211693,0.100000000000005,-40.9526435135339));
#1427=CARTESIAN_POINT('',(-38.1238934211693,0.,-28.0473509323979));
#1428=CARTESIAN_POINT('',(-38.1238934211693,0.,-40.9526435135339));
#1429=CARTESIAN_POINT('',(-38.1238934211693,0.067336686404178,-27.9528358122057));
#1430=CARTESIAN_POINT('Origin',(-38.1238934211693,0.0999999999999949,-28.0473509323979));
#1431=CARTESIAN_POINT('',(-38.1238934211693,13.5055968102549,-23.3087312105808));
#1432=CARTESIAN_POINT('',(-38.1238934211693,0.067336686404176,-27.9528358122057));
#1433=CARTESIAN_POINT('',(-38.1238934211693,13.5674872552457,-23.181664796449));
#1434=CARTESIAN_POINT('Origin',(-38.1238934211693,13.472933496659,-23.2142160903887));
#1435=CARTESIAN_POINT('',(-38.1238934211693,11.5326550593569,-17.2709617512482));
#1436=CARTESIAN_POINT('',(-38.1238934211693,13.5674872552457,-23.181664796449));
#1437=CARTESIAN_POINT('',(-38.1238934211693,11.4052492382761,-17.2090633655173));
#1438=CARTESIAN_POINT('Origin',(-38.1238934211693,11.4381013007702,-17.3035130451879));
#1439=CARTESIAN_POINT('',(-38.1238934211693,0.132852062494129,-21.1298971657893));
#1440=CARTESIAN_POINT('',(-38.1238934211693,11.4052492382761,-17.2090633655173));
#1441=CARTESIAN_POINT('',(-38.1238934211693,0.,-21.0354474861187));
#1442=CARTESIAN_POINT('Origin',(-38.1238934211693,0.0999999999999967,-21.0354474861187));
#1443=CARTESIAN_POINT('',(-38.1238934211693,1.82145964977565E-15,-21.0354474861186));
#1444=CARTESIAN_POINT('Origin',(-169.076106578831,17.,-7.99999999999999));
#1445=CARTESIAN_POINT('',(-161.076106578831,17.,0.));
#1446=CARTESIAN_POINT('Origin',(-161.076106578831,17.,-8.));
#1447=CARTESIAN_POINT('Origin',(-130.9,-33.,0.));
#1448=CARTESIAN_POINT('',(-136.1,-33.,0.));
#1449=CARTESIAN_POINT('',(-136.1,-33.,0.));
#1450=CARTESIAN_POINT('Origin',(-130.9,-33.,0.));
#1451=CARTESIAN_POINT('Origin',(-34.1238934211693,-49.0436198318451,190.192989937147));
#1452=CARTESIAN_POINT('',(-34.1238934211693,0.120796067554751,-41.0504572322766));
#1453=CARTESIAN_POINT('',(-34.1238934211693,34.9355223566108,-30.8006193071437));
#1454=CARTESIAN_POINT('Origin',(-34.1238934211693,34.9,-30.7071411696954));
#1455=CARTESIAN_POINT('Origin',(-38.1238934211693,35.,-8.));
#1456=CARTESIAN_POINT('Origin',(-41.6797449625469,32.,0.));
#1457=CARTESIAN_POINT('',(-44.5487996757009,32.8766556067971,0.));
#1458=CARTESIAN_POINT('',(-44.5487996757009,32.8766556067971,0.));
#1459=CARTESIAN_POINT('Origin',(-41.6797449625469,32.,0.));
#1460=CARTESIAN_POINT('Origin',(-48.3186672071652,20.5389073220049,0.));
#1461=CARTESIAN_POINT('',(-48.3186672071652,20.5389073220049,0.));
#1462=CARTESIAN_POINT('',(-44.5487996757009,32.8766556067971,0.));
#1463=CARTESIAN_POINT('',(-48.3186672071652,20.5389073220049,0.));
#1464=CARTESIAN_POINT('Origin',(-53.1004250624219,22.,0.));
#1465=CARTESIAN_POINT('',(-53.1004250624219,17.,0.));
#1466=CARTESIAN_POINT('Origin',(-53.1004250624219,22.,0.));
#1467=CARTESIAN_POINT('',(-53.1004250624219,17.,0.));
#1468=CARTESIAN_POINT('Origin',(-182.189496829582,17.,0.));
#1469=CARTESIAN_POINT('',(-53.1004250624219,17.,0.));
#1470=CARTESIAN_POINT('Origin',(-169.076106578831,14.0000000000001,-20.0656034084132));
#1471=CARTESIAN_POINT('',(-169.076106578831,14.9765388181895,-22.9022161660114));
#1472=CARTESIAN_POINT('Origin',(-169.076106578831,13.6945537585866,-23.3435552848911));
#1473=CARTESIAN_POINT('',(-169.076106578831,13.6945537585866,-23.3435552848911));
#1474=CARTESIAN_POINT('Origin',(-169.076106578831,13.662002464647,-23.2490015263044));
#1475=CARTESIAN_POINT('',(-169.076106578831,13.5674487060604,-23.2815528202441));
#1476=CARTESIAN_POINT('Origin',(-169.076106578831,11.5324020529298,-17.3702268278172));
#1477=CARTESIAN_POINT('',(-169.076106578831,11.5324020529298,-17.3702268278172));
#1478=CARTESIAN_POINT('Origin',(-169.076106578831,11.4378482943432,-17.4027781217568));
#1479=CARTESIAN_POINT('',(-169.076106578831,11.4057318474613,-17.3080757783873));
#1480=CARTESIAN_POINT('Origin',(-169.076106578831,0.132116446881849,-21.1313018707578));
#1481=CARTESIAN_POINT('',(-169.076106578831,0.132116446881849,-21.1313018707578));
#1482=CARTESIAN_POINT('Origin',(-169.076106578831,0.100000000000017,-21.0365995273882));
#1483=CARTESIAN_POINT('',(-169.076106578831,-9.8879238130678E-15,-21.0365995273882));
#1484=CARTESIAN_POINT('Origin',(-169.076106578831,0.,9.78281485268587));
#1485=CARTESIAN_POINT('Origin',(-152.341078568483,-5.00000000000005,0.));
#1486=CARTESIAN_POINT('',(-147.510005316018,-3.71130638656378,0.));
#1487=CARTESIAN_POINT('Origin',(-152.341078568483,-5.00000000000005,0.));
#1488=CARTESIAN_POINT('',(-147.510005316018,-3.71130638656378,0.));
#1489=CARTESIAN_POINT('Origin',(-139.112824529192,-35.1907791428416,0.));
#1490=CARTESIAN_POINT('',(-139.112824529192,-35.1907791428416,0.));
#1491=CARTESIAN_POINT('',(-147.510005316018,-3.71130638656378,0.));
#1492=CARTESIAN_POINT('',(-139.112824529192,-35.1907791428416,0.));
#1493=CARTESIAN_POINT('Origin',(-130.9,-33.,0.));
#1494=CARTESIAN_POINT('',(-122.693010271706,-35.2125369148729,0.));
#1495=CARTESIAN_POINT('Origin',(-130.9,-33.,0.));
#1496=CARTESIAN_POINT('',(-122.693010271706,-35.2125369148729,0.));
#1497=CARTESIAN_POINT('Origin',(-114.19708724889,-3.69850769713359,0.));
#1498=CARTESIAN_POINT('',(-114.19708724889,-3.69850769713359,0.));
#1499=CARTESIAN_POINT('',(-114.19708724889,-3.69850769713359,0.));
#1500=CARTESIAN_POINT('',(-114.19708724889,-3.69850769713359,0.));
#1501=CARTESIAN_POINT('Origin',(-109.369446232246,-4.99999999999998,0.));
#1502=CARTESIAN_POINT('Origin',(-109.369446232246,-4.99999999999998,0.));
#1503=CARTESIAN_POINT('Origin',(-34.1238934211693,1.82145964977565E-15,
-21.0354474861186));
#1504=CARTESIAN_POINT('',(-34.1238934211693,1.82145964977565E-15,-21.0354474861186));
#1505=CARTESIAN_POINT('Origin',(-34.1238934211693,0.0999999999999967,-21.0354474861187));
#1506=CARTESIAN_POINT('',(-34.1238934211693,0.132852062494127,-21.1298971657893));
#1507=CARTESIAN_POINT('Origin',(-34.1238934211693,11.4052492382761,-17.2090633655173));
#1508=CARTESIAN_POINT('',(-34.1238934211693,11.4052492382761,-17.2090633655173));
#1509=CARTESIAN_POINT('Origin',(-34.1238934211693,11.4381013007702,-17.3035130451879));
#1510=CARTESIAN_POINT('',(-34.1238934211693,11.5326550593569,-17.2709617512482));
#1511=CARTESIAN_POINT('Origin',(-34.1238934211693,13.5674872552457,-23.181664796449));
#1512=CARTESIAN_POINT('',(-34.1238934211693,13.5674872552457,-23.181664796449));
#1513=CARTESIAN_POINT('Origin',(-34.1238934211693,13.472933496659,-23.2142160903887));
#1514=CARTESIAN_POINT('',(-34.1238934211693,13.5055968102549,-23.3087312105809));
#1515=CARTESIAN_POINT('Origin',(-34.1238934211693,0.067336686404176,-27.9528358122057));
#1516=CARTESIAN_POINT('',(-34.1238934211693,0.067336686404176,-27.9528358122057));
#1517=CARTESIAN_POINT('Origin',(-34.1238934211693,0.0999999999999949,-28.0473509323979));
#1518=CARTESIAN_POINT('',(-34.1238934211693,8.67361737988404E-17,-28.0473509323979));
#1519=CARTESIAN_POINT('Origin',(-34.1238934211693,0.,-40.9526435135339));
#1520=CARTESIAN_POINT('',(-34.1238934211693,0.,-40.9526435135339));
#1521=CARTESIAN_POINT('Origin',(-34.1238934211693,0.100000000000005,-40.9526435135339));
#1522=CARTESIAN_POINT('Origin',(-92.7481790063852,-3.25000000000001,0.));
#1523=CARTESIAN_POINT('Origin',(-161.076106578831,8.5,-8.));
#1524=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#1528,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#1525=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#1528,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#1526=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1524))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1528,#1530,#1531))
REPRESENTATION_CONTEXT('','3D')
);
#1527=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1525))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1528,#1530,#1531))
REPRESENTATION_CONTEXT('','3D')
);
#1528=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#1529=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT($,.METRE.)
);
#1530=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#1531=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#1532=SHAPE_DEFINITION_REPRESENTATION(#1533,#1534);
#1533=PRODUCT_DEFINITION_SHAPE('',$,#1536);
#1534=SHAPE_REPRESENTATION('',(#893),#1526);
#1535=PRODUCT_DEFINITION_CONTEXT('part definition',#1540,'design');
#1536=PRODUCT_DEFINITION('S_1145','S_1145 v1',#1537,#1535);
#1537=PRODUCT_DEFINITION_FORMATION('',$,#1542);
#1538=PRODUCT_RELATED_PRODUCT_CATEGORY('S_1145 v1','S_1145 v1',(#1542));
#1539=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2009,#1540);
#1540=APPLICATION_CONTEXT(
'Core Data for Automotive Mechanical Design Process');
#1541=PRODUCT_CONTEXT('part definition',#1540,'mechanical');
#1542=PRODUCT('S_1145','S_1145 v1',$,(#1541));
#1543=PRESENTATION_STYLE_ASSIGNMENT((#1544));
#1544=SURFACE_STYLE_USAGE(.BOTH.,#1545);
#1545=SURFACE_SIDE_STYLE('',(#1546));
#1546=SURFACE_STYLE_FILL_AREA(#1547);
#1547=FILL_AREA_STYLE('Steel - Satin',(#1548));
#1548=FILL_AREA_STYLE_COLOUR('Steel - Satin',#1549);
#1549=COLOUR_RGB('Steel - Satin',0.627450980392157,0.627450980392157,0.627450980392157);
ENDSEC;
END-ISO-10303-21;
