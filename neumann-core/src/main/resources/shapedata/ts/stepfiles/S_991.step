ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ (''),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 
'G:/Shared drives/TIP/Validation/SMF KION/Tset SMF Shapes/S_991.step',
/* time_stamp */ '2021-10-04T17:11:55+02:00',
/* author */ (''),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v18.1',
/* originating_system */ 'Autodesk Translation Framework v10.10.0.1391',

/* authorisation */ '');

FILE_SCHEMA (('AUTOMOTIVE_DESIGN { 1 0 10303 214 3 1 1 }'));
ENDSEC;

DATA;
#10=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#13),#1186);
#11=SHAPE_REPRESENTATION_RELATIONSHIP('SRR','None',#1193,#12);
#12=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#14),#1185);
#13=STYLED_ITEM('',(#1202),#14);
#14=MANIFOLD_SOLID_BREP('Body1',#694);
#15=FACE_BOUND('',#108,.T.);
#16=FACE_BOUND('',#109,.T.);
#17=FACE_BOUND('',#110,.T.);
#18=FACE_BOUND('',#112,.T.);
#19=FACE_BOUND('',#113,.T.);
#20=FACE_BOUND('',#114,.T.);
#21=PLANE('',#717);
#22=PLANE('',#721);
#23=PLANE('',#725);
#24=PLANE('',#726);
#25=PLANE('',#730);
#26=PLANE('',#734);
#27=PLANE('',#738);
#28=PLANE('',#742);
#29=PLANE('',#746);
#30=PLANE('',#750);
#31=PLANE('',#754);
#32=PLANE('',#755);
#33=PLANE('',#759);
#34=PLANE('',#763);
#35=PLANE('',#767);
#36=PLANE('',#771);
#37=PLANE('',#775);
#38=PLANE('',#776);
#39=FACE_OUTER_BOUND('',#74,.T.);
#40=FACE_OUTER_BOUND('',#75,.T.);
#41=FACE_OUTER_BOUND('',#76,.T.);
#42=FACE_OUTER_BOUND('',#77,.T.);
#43=FACE_OUTER_BOUND('',#78,.T.);
#44=FACE_OUTER_BOUND('',#79,.T.);
#45=FACE_OUTER_BOUND('',#80,.T.);
#46=FACE_OUTER_BOUND('',#81,.T.);
#47=FACE_OUTER_BOUND('',#82,.T.);
#48=FACE_OUTER_BOUND('',#83,.T.);
#49=FACE_OUTER_BOUND('',#84,.T.);
#50=FACE_OUTER_BOUND('',#85,.T.);
#51=FACE_OUTER_BOUND('',#86,.T.);
#52=FACE_OUTER_BOUND('',#87,.T.);
#53=FACE_OUTER_BOUND('',#88,.T.);
#54=FACE_OUTER_BOUND('',#89,.T.);
#55=FACE_OUTER_BOUND('',#90,.T.);
#56=FACE_OUTER_BOUND('',#91,.T.);
#57=FACE_OUTER_BOUND('',#92,.T.);
#58=FACE_OUTER_BOUND('',#93,.T.);
#59=FACE_OUTER_BOUND('',#94,.T.);
#60=FACE_OUTER_BOUND('',#95,.T.);
#61=FACE_OUTER_BOUND('',#96,.T.);
#62=FACE_OUTER_BOUND('',#97,.T.);
#63=FACE_OUTER_BOUND('',#98,.T.);
#64=FACE_OUTER_BOUND('',#99,.T.);
#65=FACE_OUTER_BOUND('',#100,.T.);
#66=FACE_OUTER_BOUND('',#101,.T.);
#67=FACE_OUTER_BOUND('',#102,.T.);
#68=FACE_OUTER_BOUND('',#103,.T.);
#69=FACE_OUTER_BOUND('',#104,.T.);
#70=FACE_OUTER_BOUND('',#105,.T.);
#71=FACE_OUTER_BOUND('',#106,.T.);
#72=FACE_OUTER_BOUND('',#107,.T.);
#73=FACE_OUTER_BOUND('',#111,.T.);
#74=EDGE_LOOP('',(#444,#445,#446,#447));
#75=EDGE_LOOP('',(#448,#449,#450,#451));
#76=EDGE_LOOP('',(#452,#453,#454,#455));
#77=EDGE_LOOP('',(#456,#457,#458,#459));
#78=EDGE_LOOP('',(#460,#461,#462,#463));
#79=EDGE_LOOP('',(#464,#465,#466,#467));
#80=EDGE_LOOP('',(#468,#469,#470,#471));
#81=EDGE_LOOP('',(#472,#473,#474,#475));
#82=EDGE_LOOP('',(#476,#477,#478,#479));
#83=EDGE_LOOP('',(#480,#481,#482,#483));
#84=EDGE_LOOP('',(#484,#485,#486,#487));
#85=EDGE_LOOP('',(#488,#489,#490,#491));
#86=EDGE_LOOP('',(#492,#493,#494,#495));
#87=EDGE_LOOP('',(#496,#497,#498,#499));
#88=EDGE_LOOP('',(#500,#501,#502,#503));
#89=EDGE_LOOP('',(#504,#505,#506,#507));
#90=EDGE_LOOP('',(#508,#509,#510,#511));
#91=EDGE_LOOP('',(#512,#513,#514,#515));
#92=EDGE_LOOP('',(#516,#517,#518,#519));
#93=EDGE_LOOP('',(#520,#521,#522,#523));
#94=EDGE_LOOP('',(#524,#525,#526,#527));
#95=EDGE_LOOP('',(#528,#529,#530,#531));
#96=EDGE_LOOP('',(#532,#533,#534,#535));
#97=EDGE_LOOP('',(#536,#537,#538,#539));
#98=EDGE_LOOP('',(#540,#541,#542,#543));
#99=EDGE_LOOP('',(#544,#545,#546,#547));
#100=EDGE_LOOP('',(#548,#549,#550,#551));
#101=EDGE_LOOP('',(#552,#553,#554,#555));
#102=EDGE_LOOP('',(#556,#557,#558,#559));
#103=EDGE_LOOP('',(#560,#561,#562,#563));
#104=EDGE_LOOP('',(#564,#565,#566,#567));
#105=EDGE_LOOP('',(#568,#569,#570,#571));
#106=EDGE_LOOP('',(#572,#573,#574,#575));
#107=EDGE_LOOP('',(#576,#577,#578,#579,#580,#581,#582,#583,#584,#585,#586,
#587,#588,#589,#590,#591,#592,#593,#594,#595,#596,#597,#598,#599,#600,#601,
#602,#603,#604,#605));
#108=EDGE_LOOP('',(#606));
#109=EDGE_LOOP('',(#607));
#110=EDGE_LOOP('',(#608));
#111=EDGE_LOOP('',(#609,#610,#611,#612,#613,#614,#615,#616,#617,#618,#619,
#620,#621,#622,#623,#624,#625,#626,#627,#628,#629,#630,#631,#632,#633,#634,
#635,#636,#637,#638));
#112=EDGE_LOOP('',(#639));
#113=EDGE_LOOP('',(#640));
#114=EDGE_LOOP('',(#641));
#115=LINE('',#987,#180);
#116=LINE('',#993,#181);
#117=LINE('',#999,#182);
#118=LINE('',#1004,#183);
#119=LINE('',#1006,#184);
#120=LINE('',#1008,#185);
#121=LINE('',#1009,#186);
#122=LINE('',#1015,#187);
#123=LINE('',#1018,#188);
#124=LINE('',#1020,#189);
#125=LINE('',#1021,#190);
#126=LINE('',#1027,#191);
#127=LINE('',#1030,#192);
#128=LINE('',#1032,#193);
#129=LINE('',#1033,#194);
#130=LINE('',#1036,#195);
#131=LINE('',#1038,#196);
#132=LINE('',#1039,#197);
#133=LINE('',#1045,#198);
#134=LINE('',#1048,#199);
#135=LINE('',#1050,#200);
#136=LINE('',#1051,#201);
#137=LINE('',#1057,#202);
#138=LINE('',#1060,#203);
#139=LINE('',#1062,#204);
#140=LINE('',#1063,#205);
#141=LINE('',#1069,#206);
#142=LINE('',#1072,#207);
#143=LINE('',#1074,#208);
#144=LINE('',#1075,#209);
#145=LINE('',#1081,#210);
#146=LINE('',#1084,#211);
#147=LINE('',#1086,#212);
#148=LINE('',#1087,#213);
#149=LINE('',#1093,#214);
#150=LINE('',#1096,#215);
#151=LINE('',#1098,#216);
#152=LINE('',#1099,#217);
#153=LINE('',#1105,#218);
#154=LINE('',#1108,#219);
#155=LINE('',#1110,#220);
#156=LINE('',#1111,#221);
#157=LINE('',#1117,#222);
#158=LINE('',#1120,#223);
#159=LINE('',#1122,#224);
#160=LINE('',#1123,#225);
#161=LINE('',#1126,#226);
#162=LINE('',#1128,#227);
#163=LINE('',#1129,#228);
#164=LINE('',#1135,#229);
#165=LINE('',#1138,#230);
#166=LINE('',#1140,#231);
#167=LINE('',#1141,#232);
#168=LINE('',#1147,#233);
#169=LINE('',#1150,#234);
#170=LINE('',#1152,#235);
#171=LINE('',#1153,#236);
#172=LINE('',#1159,#237);
#173=LINE('',#1162,#238);
#174=LINE('',#1164,#239);
#175=LINE('',#1165,#240);
#176=LINE('',#1171,#241);
#177=LINE('',#1174,#242);
#178=LINE('',#1176,#243);
#179=LINE('',#1177,#244);
#180=VECTOR('',#783,10.);
#181=VECTOR('',#790,10.);
#182=VECTOR('',#797,1.49999999999999);
#183=VECTOR('',#802,10.);
#184=VECTOR('',#803,10.);
#185=VECTOR('',#804,10.);
#186=VECTOR('',#805,10.);
#187=VECTOR('',#812,10.);
#188=VECTOR('',#815,10.);
#189=VECTOR('',#816,10.);
#190=VECTOR('',#817,10.);
#191=VECTOR('',#824,10.);
#192=VECTOR('',#827,10.);
#193=VECTOR('',#828,10.);
#194=VECTOR('',#829,10.);
#195=VECTOR('',#832,10.);
#196=VECTOR('',#833,10.);
#197=VECTOR('',#834,10.);
#198=VECTOR('',#841,10.);
#199=VECTOR('',#844,10.);
#200=VECTOR('',#845,10.);
#201=VECTOR('',#846,10.);
#202=VECTOR('',#853,10.);
#203=VECTOR('',#856,10.);
#204=VECTOR('',#857,10.);
#205=VECTOR('',#858,10.);
#206=VECTOR('',#865,10.);
#207=VECTOR('',#868,10.);
#208=VECTOR('',#869,10.);
#209=VECTOR('',#870,10.);
#210=VECTOR('',#877,10.);
#211=VECTOR('',#880,10.);
#212=VECTOR('',#881,10.);
#213=VECTOR('',#882,10.);
#214=VECTOR('',#889,10.);
#215=VECTOR('',#892,10.);
#216=VECTOR('',#893,10.);
#217=VECTOR('',#894,10.);
#218=VECTOR('',#901,10.);
#219=VECTOR('',#904,10.);
#220=VECTOR('',#905,10.);
#221=VECTOR('',#906,10.);
#222=VECTOR('',#913,10.);
#223=VECTOR('',#916,10.);
#224=VECTOR('',#917,10.);
#225=VECTOR('',#918,10.);
#226=VECTOR('',#921,10.);
#227=VECTOR('',#922,10.);
#228=VECTOR('',#923,10.);
#229=VECTOR('',#930,10.);
#230=VECTOR('',#933,10.);
#231=VECTOR('',#934,10.);
#232=VECTOR('',#935,10.);
#233=VECTOR('',#942,10.);
#234=VECTOR('',#945,10.);
#235=VECTOR('',#946,10.);
#236=VECTOR('',#947,10.);
#237=VECTOR('',#954,10.);
#238=VECTOR('',#957,10.);
#239=VECTOR('',#958,10.);
#240=VECTOR('',#959,10.);
#241=VECTOR('',#966,10.);
#242=VECTOR('',#969,10.);
#243=VECTOR('',#970,10.);
#244=VECTOR('',#971,10.);
#245=CIRCLE('',#709,10.);
#246=CIRCLE('',#710,10.);
#247=CIRCLE('',#712,10.);
#248=CIRCLE('',#713,10.);
#249=CIRCLE('',#715,1.49999999999999);
#250=CIRCLE('',#716,1.49999999999999);
#251=CIRCLE('',#719,2.);
#252=CIRCLE('',#720,2.);
#253=CIRCLE('',#723,20.);
#254=CIRCLE('',#724,20.);
#255=CIRCLE('',#728,2.5);
#256=CIRCLE('',#729,2.5);
#257=CIRCLE('',#732,5.);
#258=CIRCLE('',#733,5.);
#259=CIRCLE('',#736,5.);
#260=CIRCLE('',#737,5.);
#261=CIRCLE('',#740,12.5);
#262=CIRCLE('',#741,12.5);
#263=CIRCLE('',#744,5.);
#264=CIRCLE('',#745,5.);
#265=CIRCLE('',#748,5.00000000000001);
#266=CIRCLE('',#749,5.00000000000001);
#267=CIRCLE('',#752,2.5);
#268=CIRCLE('',#753,2.5);
#269=CIRCLE('',#757,20.);
#270=CIRCLE('',#758,20.);
#271=CIRCLE('',#761,40.);
#272=CIRCLE('',#762,40.);
#273=CIRCLE('',#765,2.00000000000002);
#274=CIRCLE('',#766,2.00000000000002);
#275=CIRCLE('',#769,2.00000000000001);
#276=CIRCLE('',#770,2.00000000000001);
#277=CIRCLE('',#773,2.);
#278=CIRCLE('',#774,2.);
#279=VERTEX_POINT('',#984);
#280=VERTEX_POINT('',#986);
#281=VERTEX_POINT('',#990);
#282=VERTEX_POINT('',#992);
#283=VERTEX_POINT('',#996);
#284=VERTEX_POINT('',#998);
#285=VERTEX_POINT('',#1002);
#286=VERTEX_POINT('',#1003);
#287=VERTEX_POINT('',#1005);
#288=VERTEX_POINT('',#1007);
#289=VERTEX_POINT('',#1011);
#290=VERTEX_POINT('',#1013);
#291=VERTEX_POINT('',#1017);
#292=VERTEX_POINT('',#1019);
#293=VERTEX_POINT('',#1023);
#294=VERTEX_POINT('',#1025);
#295=VERTEX_POINT('',#1029);
#296=VERTEX_POINT('',#1031);
#297=VERTEX_POINT('',#1035);
#298=VERTEX_POINT('',#1037);
#299=VERTEX_POINT('',#1041);
#300=VERTEX_POINT('',#1043);
#301=VERTEX_POINT('',#1047);
#302=VERTEX_POINT('',#1049);
#303=VERTEX_POINT('',#1053);
#304=VERTEX_POINT('',#1055);
#305=VERTEX_POINT('',#1059);
#306=VERTEX_POINT('',#1061);
#307=VERTEX_POINT('',#1065);
#308=VERTEX_POINT('',#1067);
#309=VERTEX_POINT('',#1071);
#310=VERTEX_POINT('',#1073);
#311=VERTEX_POINT('',#1077);
#312=VERTEX_POINT('',#1079);
#313=VERTEX_POINT('',#1083);
#314=VERTEX_POINT('',#1085);
#315=VERTEX_POINT('',#1089);
#316=VERTEX_POINT('',#1091);
#317=VERTEX_POINT('',#1095);
#318=VERTEX_POINT('',#1097);
#319=VERTEX_POINT('',#1101);
#320=VERTEX_POINT('',#1103);
#321=VERTEX_POINT('',#1107);
#322=VERTEX_POINT('',#1109);
#323=VERTEX_POINT('',#1113);
#324=VERTEX_POINT('',#1115);
#325=VERTEX_POINT('',#1119);
#326=VERTEX_POINT('',#1121);
#327=VERTEX_POINT('',#1125);
#328=VERTEX_POINT('',#1127);
#329=VERTEX_POINT('',#1131);
#330=VERTEX_POINT('',#1133);
#331=VERTEX_POINT('',#1137);
#332=VERTEX_POINT('',#1139);
#333=VERTEX_POINT('',#1143);
#334=VERTEX_POINT('',#1145);
#335=VERTEX_POINT('',#1149);
#336=VERTEX_POINT('',#1151);
#337=VERTEX_POINT('',#1155);
#338=VERTEX_POINT('',#1157);
#339=VERTEX_POINT('',#1161);
#340=VERTEX_POINT('',#1163);
#341=VERTEX_POINT('',#1167);
#342=VERTEX_POINT('',#1169);
#343=VERTEX_POINT('',#1173);
#344=VERTEX_POINT('',#1175);
#345=EDGE_CURVE('',#279,#279,#245,.T.);
#346=EDGE_CURVE('',#279,#280,#115,.T.);
#347=EDGE_CURVE('',#280,#280,#246,.T.);
#348=EDGE_CURVE('',#281,#281,#247,.T.);
#349=EDGE_CURVE('',#281,#282,#116,.T.);
#350=EDGE_CURVE('',#282,#282,#248,.T.);
#351=EDGE_CURVE('',#283,#283,#249,.T.);
#352=EDGE_CURVE('',#283,#284,#117,.T.);
#353=EDGE_CURVE('',#284,#284,#250,.T.);
#354=EDGE_CURVE('',#285,#286,#118,.T.);
#355=EDGE_CURVE('',#285,#287,#119,.T.);
#356=EDGE_CURVE('',#288,#287,#120,.T.);
#357=EDGE_CURVE('',#286,#288,#121,.T.);
#358=EDGE_CURVE('',#286,#289,#251,.T.);
#359=EDGE_CURVE('',#290,#288,#252,.T.);
#360=EDGE_CURVE('',#289,#290,#122,.T.);
#361=EDGE_CURVE('',#291,#289,#123,.T.);
#362=EDGE_CURVE('',#292,#290,#124,.T.);
#363=EDGE_CURVE('',#291,#292,#125,.T.);
#364=EDGE_CURVE('',#293,#291,#253,.T.);
#365=EDGE_CURVE('',#294,#292,#254,.T.);
#366=EDGE_CURVE('',#293,#294,#126,.T.);
#367=EDGE_CURVE('',#295,#293,#127,.T.);
#368=EDGE_CURVE('',#296,#294,#128,.T.);
#369=EDGE_CURVE('',#295,#296,#129,.T.);
#370=EDGE_CURVE('',#297,#295,#130,.T.);
#371=EDGE_CURVE('',#298,#296,#131,.T.);
#372=EDGE_CURVE('',#297,#298,#132,.T.);
#373=EDGE_CURVE('',#299,#297,#255,.T.);
#374=EDGE_CURVE('',#300,#298,#256,.T.);
#375=EDGE_CURVE('',#299,#300,#133,.T.);
#376=EDGE_CURVE('',#299,#301,#134,.T.);
#377=EDGE_CURVE('',#302,#300,#135,.T.);
#378=EDGE_CURVE('',#301,#302,#136,.T.);
#379=EDGE_CURVE('',#301,#303,#257,.T.);
#380=EDGE_CURVE('',#304,#302,#258,.T.);
#381=EDGE_CURVE('',#303,#304,#137,.T.);
#382=EDGE_CURVE('',#303,#305,#138,.T.);
#383=EDGE_CURVE('',#306,#304,#139,.T.);
#384=EDGE_CURVE('',#305,#306,#140,.T.);
#385=EDGE_CURVE('',#307,#305,#259,.T.);
#386=EDGE_CURVE('',#308,#306,#260,.T.);
#387=EDGE_CURVE('',#307,#308,#141,.T.);
#388=EDGE_CURVE('',#309,#307,#142,.T.);
#389=EDGE_CURVE('',#310,#308,#143,.T.);
#390=EDGE_CURVE('',#309,#310,#144,.T.);
#391=EDGE_CURVE('',#309,#311,#261,.T.);
#392=EDGE_CURVE('',#312,#310,#262,.T.);
#393=EDGE_CURVE('',#311,#312,#145,.T.);
#394=EDGE_CURVE('',#311,#313,#146,.T.);
#395=EDGE_CURVE('',#314,#312,#147,.T.);
#396=EDGE_CURVE('',#313,#314,#148,.T.);
#397=EDGE_CURVE('',#315,#313,#263,.T.);
#398=EDGE_CURVE('',#316,#314,#264,.T.);
#399=EDGE_CURVE('',#315,#316,#149,.T.);
#400=EDGE_CURVE('',#317,#315,#150,.T.);
#401=EDGE_CURVE('',#318,#316,#151,.T.);
#402=EDGE_CURVE('',#317,#318,#152,.T.);
#403=EDGE_CURVE('',#317,#319,#265,.T.);
#404=EDGE_CURVE('',#320,#318,#266,.T.);
#405=EDGE_CURVE('',#319,#320,#153,.T.);
#406=EDGE_CURVE('',#321,#319,#154,.T.);
#407=EDGE_CURVE('',#322,#320,#155,.T.);
#408=EDGE_CURVE('',#321,#322,#156,.T.);
#409=EDGE_CURVE('',#323,#321,#267,.T.);
#410=EDGE_CURVE('',#324,#322,#268,.T.);
#411=EDGE_CURVE('',#323,#324,#157,.T.);
#412=EDGE_CURVE('',#323,#325,#158,.T.);
#413=EDGE_CURVE('',#326,#324,#159,.T.);
#414=EDGE_CURVE('',#325,#326,#160,.T.);
#415=EDGE_CURVE('',#325,#327,#161,.T.);
#416=EDGE_CURVE('',#328,#326,#162,.T.);
#417=EDGE_CURVE('',#327,#328,#163,.T.);
#418=EDGE_CURVE('',#329,#327,#269,.T.);
#419=EDGE_CURVE('',#330,#328,#270,.T.);
#420=EDGE_CURVE('',#329,#330,#164,.T.);
#421=EDGE_CURVE('',#329,#331,#165,.T.);
#422=EDGE_CURVE('',#332,#330,#166,.T.);
#423=EDGE_CURVE('',#331,#332,#167,.T.);
#424=EDGE_CURVE('',#333,#331,#271,.T.);
#425=EDGE_CURVE('',#334,#332,#272,.T.);
#426=EDGE_CURVE('',#333,#334,#168,.T.);
#427=EDGE_CURVE('',#335,#333,#169,.T.);
#428=EDGE_CURVE('',#336,#334,#170,.T.);
#429=EDGE_CURVE('',#335,#336,#171,.T.);
#430=EDGE_CURVE('',#335,#337,#273,.T.);
#431=EDGE_CURVE('',#338,#336,#274,.T.);
#432=EDGE_CURVE('',#337,#338,#172,.T.);
#433=EDGE_CURVE('',#337,#339,#173,.T.);
#434=EDGE_CURVE('',#340,#338,#174,.T.);
#435=EDGE_CURVE('',#339,#340,#175,.T.);
#436=EDGE_CURVE('',#341,#339,#275,.T.);
#437=EDGE_CURVE('',#342,#340,#276,.T.);
#438=EDGE_CURVE('',#341,#342,#176,.T.);
#439=EDGE_CURVE('',#341,#343,#177,.T.);
#440=EDGE_CURVE('',#344,#342,#178,.T.);
#441=EDGE_CURVE('',#343,#344,#179,.T.);
#442=EDGE_CURVE('',#285,#343,#277,.T.);
#443=EDGE_CURVE('',#287,#344,#278,.T.);
#444=ORIENTED_EDGE('',*,*,#345,.F.);
#445=ORIENTED_EDGE('',*,*,#346,.T.);
#446=ORIENTED_EDGE('',*,*,#347,.T.);
#447=ORIENTED_EDGE('',*,*,#346,.F.);
#448=ORIENTED_EDGE('',*,*,#348,.F.);
#449=ORIENTED_EDGE('',*,*,#349,.T.);
#450=ORIENTED_EDGE('',*,*,#350,.T.);
#451=ORIENTED_EDGE('',*,*,#349,.F.);
#452=ORIENTED_EDGE('',*,*,#351,.F.);
#453=ORIENTED_EDGE('',*,*,#352,.T.);
#454=ORIENTED_EDGE('',*,*,#353,.T.);
#455=ORIENTED_EDGE('',*,*,#352,.F.);
#456=ORIENTED_EDGE('',*,*,#354,.F.);
#457=ORIENTED_EDGE('',*,*,#355,.T.);
#458=ORIENTED_EDGE('',*,*,#356,.F.);
#459=ORIENTED_EDGE('',*,*,#357,.F.);
#460=ORIENTED_EDGE('',*,*,#358,.F.);
#461=ORIENTED_EDGE('',*,*,#357,.T.);
#462=ORIENTED_EDGE('',*,*,#359,.F.);
#463=ORIENTED_EDGE('',*,*,#360,.F.);
#464=ORIENTED_EDGE('',*,*,#361,.T.);
#465=ORIENTED_EDGE('',*,*,#360,.T.);
#466=ORIENTED_EDGE('',*,*,#362,.F.);
#467=ORIENTED_EDGE('',*,*,#363,.F.);
#468=ORIENTED_EDGE('',*,*,#364,.T.);
#469=ORIENTED_EDGE('',*,*,#363,.T.);
#470=ORIENTED_EDGE('',*,*,#365,.F.);
#471=ORIENTED_EDGE('',*,*,#366,.F.);
#472=ORIENTED_EDGE('',*,*,#367,.T.);
#473=ORIENTED_EDGE('',*,*,#366,.T.);
#474=ORIENTED_EDGE('',*,*,#368,.F.);
#475=ORIENTED_EDGE('',*,*,#369,.F.);
#476=ORIENTED_EDGE('',*,*,#370,.T.);
#477=ORIENTED_EDGE('',*,*,#369,.T.);
#478=ORIENTED_EDGE('',*,*,#371,.F.);
#479=ORIENTED_EDGE('',*,*,#372,.F.);
#480=ORIENTED_EDGE('',*,*,#373,.T.);
#481=ORIENTED_EDGE('',*,*,#372,.T.);
#482=ORIENTED_EDGE('',*,*,#374,.F.);
#483=ORIENTED_EDGE('',*,*,#375,.F.);
#484=ORIENTED_EDGE('',*,*,#376,.F.);
#485=ORIENTED_EDGE('',*,*,#375,.T.);
#486=ORIENTED_EDGE('',*,*,#377,.F.);
#487=ORIENTED_EDGE('',*,*,#378,.F.);
#488=ORIENTED_EDGE('',*,*,#379,.F.);
#489=ORIENTED_EDGE('',*,*,#378,.T.);
#490=ORIENTED_EDGE('',*,*,#380,.F.);
#491=ORIENTED_EDGE('',*,*,#381,.F.);
#492=ORIENTED_EDGE('',*,*,#382,.F.);
#493=ORIENTED_EDGE('',*,*,#381,.T.);
#494=ORIENTED_EDGE('',*,*,#383,.F.);
#495=ORIENTED_EDGE('',*,*,#384,.F.);
#496=ORIENTED_EDGE('',*,*,#385,.T.);
#497=ORIENTED_EDGE('',*,*,#384,.T.);
#498=ORIENTED_EDGE('',*,*,#386,.F.);
#499=ORIENTED_EDGE('',*,*,#387,.F.);
#500=ORIENTED_EDGE('',*,*,#388,.T.);
#501=ORIENTED_EDGE('',*,*,#387,.T.);
#502=ORIENTED_EDGE('',*,*,#389,.F.);
#503=ORIENTED_EDGE('',*,*,#390,.F.);
#504=ORIENTED_EDGE('',*,*,#391,.F.);
#505=ORIENTED_EDGE('',*,*,#390,.T.);
#506=ORIENTED_EDGE('',*,*,#392,.F.);
#507=ORIENTED_EDGE('',*,*,#393,.F.);
#508=ORIENTED_EDGE('',*,*,#394,.F.);
#509=ORIENTED_EDGE('',*,*,#393,.T.);
#510=ORIENTED_EDGE('',*,*,#395,.F.);
#511=ORIENTED_EDGE('',*,*,#396,.F.);
#512=ORIENTED_EDGE('',*,*,#397,.T.);
#513=ORIENTED_EDGE('',*,*,#396,.T.);
#514=ORIENTED_EDGE('',*,*,#398,.F.);
#515=ORIENTED_EDGE('',*,*,#399,.F.);
#516=ORIENTED_EDGE('',*,*,#400,.T.);
#517=ORIENTED_EDGE('',*,*,#399,.T.);
#518=ORIENTED_EDGE('',*,*,#401,.F.);
#519=ORIENTED_EDGE('',*,*,#402,.F.);
#520=ORIENTED_EDGE('',*,*,#403,.F.);
#521=ORIENTED_EDGE('',*,*,#402,.T.);
#522=ORIENTED_EDGE('',*,*,#404,.F.);
#523=ORIENTED_EDGE('',*,*,#405,.F.);
#524=ORIENTED_EDGE('',*,*,#406,.T.);
#525=ORIENTED_EDGE('',*,*,#405,.T.);
#526=ORIENTED_EDGE('',*,*,#407,.F.);
#527=ORIENTED_EDGE('',*,*,#408,.F.);
#528=ORIENTED_EDGE('',*,*,#409,.T.);
#529=ORIENTED_EDGE('',*,*,#408,.T.);
#530=ORIENTED_EDGE('',*,*,#410,.F.);
#531=ORIENTED_EDGE('',*,*,#411,.F.);
#532=ORIENTED_EDGE('',*,*,#412,.F.);
#533=ORIENTED_EDGE('',*,*,#411,.T.);
#534=ORIENTED_EDGE('',*,*,#413,.F.);
#535=ORIENTED_EDGE('',*,*,#414,.F.);
#536=ORIENTED_EDGE('',*,*,#415,.F.);
#537=ORIENTED_EDGE('',*,*,#414,.T.);
#538=ORIENTED_EDGE('',*,*,#416,.F.);
#539=ORIENTED_EDGE('',*,*,#417,.F.);
#540=ORIENTED_EDGE('',*,*,#418,.T.);
#541=ORIENTED_EDGE('',*,*,#417,.T.);
#542=ORIENTED_EDGE('',*,*,#419,.F.);
#543=ORIENTED_EDGE('',*,*,#420,.F.);
#544=ORIENTED_EDGE('',*,*,#421,.F.);
#545=ORIENTED_EDGE('',*,*,#420,.T.);
#546=ORIENTED_EDGE('',*,*,#422,.F.);
#547=ORIENTED_EDGE('',*,*,#423,.F.);
#548=ORIENTED_EDGE('',*,*,#424,.T.);
#549=ORIENTED_EDGE('',*,*,#423,.T.);
#550=ORIENTED_EDGE('',*,*,#425,.F.);
#551=ORIENTED_EDGE('',*,*,#426,.F.);
#552=ORIENTED_EDGE('',*,*,#427,.T.);
#553=ORIENTED_EDGE('',*,*,#426,.T.);
#554=ORIENTED_EDGE('',*,*,#428,.F.);
#555=ORIENTED_EDGE('',*,*,#429,.F.);
#556=ORIENTED_EDGE('',*,*,#430,.F.);
#557=ORIENTED_EDGE('',*,*,#429,.T.);
#558=ORIENTED_EDGE('',*,*,#431,.F.);
#559=ORIENTED_EDGE('',*,*,#432,.F.);
#560=ORIENTED_EDGE('',*,*,#433,.F.);
#561=ORIENTED_EDGE('',*,*,#432,.T.);
#562=ORIENTED_EDGE('',*,*,#434,.F.);
#563=ORIENTED_EDGE('',*,*,#435,.F.);
#564=ORIENTED_EDGE('',*,*,#436,.T.);
#565=ORIENTED_EDGE('',*,*,#435,.T.);
#566=ORIENTED_EDGE('',*,*,#437,.F.);
#567=ORIENTED_EDGE('',*,*,#438,.F.);
#568=ORIENTED_EDGE('',*,*,#439,.F.);
#569=ORIENTED_EDGE('',*,*,#438,.T.);
#570=ORIENTED_EDGE('',*,*,#440,.F.);
#571=ORIENTED_EDGE('',*,*,#441,.F.);
#572=ORIENTED_EDGE('',*,*,#442,.T.);
#573=ORIENTED_EDGE('',*,*,#441,.T.);
#574=ORIENTED_EDGE('',*,*,#443,.F.);
#575=ORIENTED_EDGE('',*,*,#355,.F.);
#576=ORIENTED_EDGE('',*,*,#443,.T.);
#577=ORIENTED_EDGE('',*,*,#440,.T.);
#578=ORIENTED_EDGE('',*,*,#437,.T.);
#579=ORIENTED_EDGE('',*,*,#434,.T.);
#580=ORIENTED_EDGE('',*,*,#431,.T.);
#581=ORIENTED_EDGE('',*,*,#428,.T.);
#582=ORIENTED_EDGE('',*,*,#425,.T.);
#583=ORIENTED_EDGE('',*,*,#422,.T.);
#584=ORIENTED_EDGE('',*,*,#419,.T.);
#585=ORIENTED_EDGE('',*,*,#416,.T.);
#586=ORIENTED_EDGE('',*,*,#413,.T.);
#587=ORIENTED_EDGE('',*,*,#410,.T.);
#588=ORIENTED_EDGE('',*,*,#407,.T.);
#589=ORIENTED_EDGE('',*,*,#404,.T.);
#590=ORIENTED_EDGE('',*,*,#401,.T.);
#591=ORIENTED_EDGE('',*,*,#398,.T.);
#592=ORIENTED_EDGE('',*,*,#395,.T.);
#593=ORIENTED_EDGE('',*,*,#392,.T.);
#594=ORIENTED_EDGE('',*,*,#389,.T.);
#595=ORIENTED_EDGE('',*,*,#386,.T.);
#596=ORIENTED_EDGE('',*,*,#383,.T.);
#597=ORIENTED_EDGE('',*,*,#380,.T.);
#598=ORIENTED_EDGE('',*,*,#377,.T.);
#599=ORIENTED_EDGE('',*,*,#374,.T.);
#600=ORIENTED_EDGE('',*,*,#371,.T.);
#601=ORIENTED_EDGE('',*,*,#368,.T.);
#602=ORIENTED_EDGE('',*,*,#365,.T.);
#603=ORIENTED_EDGE('',*,*,#362,.T.);
#604=ORIENTED_EDGE('',*,*,#359,.T.);
#605=ORIENTED_EDGE('',*,*,#356,.T.);
#606=ORIENTED_EDGE('',*,*,#351,.T.);
#607=ORIENTED_EDGE('',*,*,#348,.T.);
#608=ORIENTED_EDGE('',*,*,#345,.T.);
#609=ORIENTED_EDGE('',*,*,#442,.F.);
#610=ORIENTED_EDGE('',*,*,#354,.T.);
#611=ORIENTED_EDGE('',*,*,#358,.T.);
#612=ORIENTED_EDGE('',*,*,#361,.F.);
#613=ORIENTED_EDGE('',*,*,#364,.F.);
#614=ORIENTED_EDGE('',*,*,#367,.F.);
#615=ORIENTED_EDGE('',*,*,#370,.F.);
#616=ORIENTED_EDGE('',*,*,#373,.F.);
#617=ORIENTED_EDGE('',*,*,#376,.T.);
#618=ORIENTED_EDGE('',*,*,#379,.T.);
#619=ORIENTED_EDGE('',*,*,#382,.T.);
#620=ORIENTED_EDGE('',*,*,#385,.F.);
#621=ORIENTED_EDGE('',*,*,#388,.F.);
#622=ORIENTED_EDGE('',*,*,#391,.T.);
#623=ORIENTED_EDGE('',*,*,#394,.T.);
#624=ORIENTED_EDGE('',*,*,#397,.F.);
#625=ORIENTED_EDGE('',*,*,#400,.F.);
#626=ORIENTED_EDGE('',*,*,#403,.T.);
#627=ORIENTED_EDGE('',*,*,#406,.F.);
#628=ORIENTED_EDGE('',*,*,#409,.F.);
#629=ORIENTED_EDGE('',*,*,#412,.T.);
#630=ORIENTED_EDGE('',*,*,#415,.T.);
#631=ORIENTED_EDGE('',*,*,#418,.F.);
#632=ORIENTED_EDGE('',*,*,#421,.T.);
#633=ORIENTED_EDGE('',*,*,#424,.F.);
#634=ORIENTED_EDGE('',*,*,#427,.F.);
#635=ORIENTED_EDGE('',*,*,#430,.T.);
#636=ORIENTED_EDGE('',*,*,#433,.T.);
#637=ORIENTED_EDGE('',*,*,#436,.F.);
#638=ORIENTED_EDGE('',*,*,#439,.T.);
#639=ORIENTED_EDGE('',*,*,#353,.F.);
#640=ORIENTED_EDGE('',*,*,#350,.F.);
#641=ORIENTED_EDGE('',*,*,#347,.F.);
#642=CYLINDRICAL_SURFACE('',#708,10.);
#643=CYLINDRICAL_SURFACE('',#711,10.);
#644=CYLINDRICAL_SURFACE('',#714,1.49999999999999);
#645=CYLINDRICAL_SURFACE('',#718,2.);
#646=CYLINDRICAL_SURFACE('',#722,20.);
#647=CYLINDRICAL_SURFACE('',#727,2.5);
#648=CYLINDRICAL_SURFACE('',#731,5.);
#649=CYLINDRICAL_SURFACE('',#735,5.);
#650=CYLINDRICAL_SURFACE('',#739,12.5);
#651=CYLINDRICAL_SURFACE('',#743,5.);
#652=CYLINDRICAL_SURFACE('',#747,5.00000000000001);
#653=CYLINDRICAL_SURFACE('',#751,2.5);
#654=CYLINDRICAL_SURFACE('',#756,20.);
#655=CYLINDRICAL_SURFACE('',#760,40.);
#656=CYLINDRICAL_SURFACE('',#764,2.00000000000002);
#657=CYLINDRICAL_SURFACE('',#768,2.00000000000001);
#658=CYLINDRICAL_SURFACE('',#772,2.);
#659=ADVANCED_FACE('',(#39),#642,.F.);
#660=ADVANCED_FACE('',(#40),#643,.F.);
#661=ADVANCED_FACE('',(#41),#644,.F.);
#662=ADVANCED_FACE('',(#42),#21,.T.);
#663=ADVANCED_FACE('',(#43),#645,.F.);
#664=ADVANCED_FACE('',(#44),#22,.T.);
#665=ADVANCED_FACE('',(#45),#646,.T.);
#666=ADVANCED_FACE('',(#46),#23,.T.);
#667=ADVANCED_FACE('',(#47),#24,.T.);
#668=ADVANCED_FACE('',(#48),#647,.T.);
#669=ADVANCED_FACE('',(#49),#25,.T.);
#670=ADVANCED_FACE('',(#50),#648,.F.);
#671=ADVANCED_FACE('',(#51),#26,.T.);
#672=ADVANCED_FACE('',(#52),#649,.T.);
#673=ADVANCED_FACE('',(#53),#27,.T.);
#674=ADVANCED_FACE('',(#54),#650,.F.);
#675=ADVANCED_FACE('',(#55),#28,.T.);
#676=ADVANCED_FACE('',(#56),#651,.T.);
#677=ADVANCED_FACE('',(#57),#29,.T.);
#678=ADVANCED_FACE('',(#58),#652,.F.);
#679=ADVANCED_FACE('',(#59),#30,.T.);
#680=ADVANCED_FACE('',(#60),#653,.T.);
#681=ADVANCED_FACE('',(#61),#31,.T.);
#682=ADVANCED_FACE('',(#62),#32,.T.);
#683=ADVANCED_FACE('',(#63),#654,.T.);
#684=ADVANCED_FACE('',(#64),#33,.T.);
#685=ADVANCED_FACE('',(#65),#655,.T.);
#686=ADVANCED_FACE('',(#66),#34,.T.);
#687=ADVANCED_FACE('',(#67),#656,.F.);
#688=ADVANCED_FACE('',(#68),#35,.T.);
#689=ADVANCED_FACE('',(#69),#657,.T.);
#690=ADVANCED_FACE('',(#70),#36,.T.);
#691=ADVANCED_FACE('',(#71),#658,.T.);
#692=ADVANCED_FACE('',(#72,#15,#16,#17),#37,.T.);
#693=ADVANCED_FACE('',(#73,#18,#19,#20),#38,.F.);
#694=CLOSED_SHELL('',(#659,#660,#661,#662,#663,#664,#665,#666,#667,#668,
#669,#670,#671,#672,#673,#674,#675,#676,#677,#678,#679,#680,#681,#682,#683,
#684,#685,#686,#687,#688,#689,#690,#691,#692,#693));
#695=DERIVED_UNIT_ELEMENT(#697,1.);
#696=DERIVED_UNIT_ELEMENT(#1188,-3.);
#697=(
MASS_UNIT()
NAMED_UNIT(*)
SI_UNIT(.KILO.,.GRAM.)
);
#698=DERIVED_UNIT((#695,#696));
#699=MEASURE_REPRESENTATION_ITEM('density measure',
POSITIVE_RATIO_MEASURE(7850.),#698);
#700=PROPERTY_DEFINITION_REPRESENTATION(#705,#702);
#701=PROPERTY_DEFINITION_REPRESENTATION(#706,#703);
#702=REPRESENTATION('material name',(#704),#1185);
#703=REPRESENTATION('density',(#699),#1185);
#704=DESCRIPTIVE_REPRESENTATION_ITEM('Steel','Steel');
#705=PROPERTY_DEFINITION('material property','material name',#1195);
#706=PROPERTY_DEFINITION('material property','density of part',#1195);
#707=AXIS2_PLACEMENT_3D('placement',#982,#777,#778);
#708=AXIS2_PLACEMENT_3D('',#983,#779,#780);
#709=AXIS2_PLACEMENT_3D('',#985,#781,#782);
#710=AXIS2_PLACEMENT_3D('',#988,#784,#785);
#711=AXIS2_PLACEMENT_3D('',#989,#786,#787);
#712=AXIS2_PLACEMENT_3D('',#991,#788,#789);
#713=AXIS2_PLACEMENT_3D('',#994,#791,#792);
#714=AXIS2_PLACEMENT_3D('',#995,#793,#794);
#715=AXIS2_PLACEMENT_3D('',#997,#795,#796);
#716=AXIS2_PLACEMENT_3D('',#1000,#798,#799);
#717=AXIS2_PLACEMENT_3D('',#1001,#800,#801);
#718=AXIS2_PLACEMENT_3D('',#1010,#806,#807);
#719=AXIS2_PLACEMENT_3D('',#1012,#808,#809);
#720=AXIS2_PLACEMENT_3D('',#1014,#810,#811);
#721=AXIS2_PLACEMENT_3D('',#1016,#813,#814);
#722=AXIS2_PLACEMENT_3D('',#1022,#818,#819);
#723=AXIS2_PLACEMENT_3D('',#1024,#820,#821);
#724=AXIS2_PLACEMENT_3D('',#1026,#822,#823);
#725=AXIS2_PLACEMENT_3D('',#1028,#825,#826);
#726=AXIS2_PLACEMENT_3D('',#1034,#830,#831);
#727=AXIS2_PLACEMENT_3D('',#1040,#835,#836);
#728=AXIS2_PLACEMENT_3D('',#1042,#837,#838);
#729=AXIS2_PLACEMENT_3D('',#1044,#839,#840);
#730=AXIS2_PLACEMENT_3D('',#1046,#842,#843);
#731=AXIS2_PLACEMENT_3D('',#1052,#847,#848);
#732=AXIS2_PLACEMENT_3D('',#1054,#849,#850);
#733=AXIS2_PLACEMENT_3D('',#1056,#851,#852);
#734=AXIS2_PLACEMENT_3D('',#1058,#854,#855);
#735=AXIS2_PLACEMENT_3D('',#1064,#859,#860);
#736=AXIS2_PLACEMENT_3D('',#1066,#861,#862);
#737=AXIS2_PLACEMENT_3D('',#1068,#863,#864);
#738=AXIS2_PLACEMENT_3D('',#1070,#866,#867);
#739=AXIS2_PLACEMENT_3D('',#1076,#871,#872);
#740=AXIS2_PLACEMENT_3D('',#1078,#873,#874);
#741=AXIS2_PLACEMENT_3D('',#1080,#875,#876);
#742=AXIS2_PLACEMENT_3D('',#1082,#878,#879);
#743=AXIS2_PLACEMENT_3D('',#1088,#883,#884);
#744=AXIS2_PLACEMENT_3D('',#1090,#885,#886);
#745=AXIS2_PLACEMENT_3D('',#1092,#887,#888);
#746=AXIS2_PLACEMENT_3D('',#1094,#890,#891);
#747=AXIS2_PLACEMENT_3D('',#1100,#895,#896);
#748=AXIS2_PLACEMENT_3D('',#1102,#897,#898);
#749=AXIS2_PLACEMENT_3D('',#1104,#899,#900);
#750=AXIS2_PLACEMENT_3D('',#1106,#902,#903);
#751=AXIS2_PLACEMENT_3D('',#1112,#907,#908);
#752=AXIS2_PLACEMENT_3D('',#1114,#909,#910);
#753=AXIS2_PLACEMENT_3D('',#1116,#911,#912);
#754=AXIS2_PLACEMENT_3D('',#1118,#914,#915);
#755=AXIS2_PLACEMENT_3D('',#1124,#919,#920);
#756=AXIS2_PLACEMENT_3D('',#1130,#924,#925);
#757=AXIS2_PLACEMENT_3D('',#1132,#926,#927);
#758=AXIS2_PLACEMENT_3D('',#1134,#928,#929);
#759=AXIS2_PLACEMENT_3D('',#1136,#931,#932);
#760=AXIS2_PLACEMENT_3D('',#1142,#936,#937);
#761=AXIS2_PLACEMENT_3D('',#1144,#938,#939);
#762=AXIS2_PLACEMENT_3D('',#1146,#940,#941);
#763=AXIS2_PLACEMENT_3D('',#1148,#943,#944);
#764=AXIS2_PLACEMENT_3D('',#1154,#948,#949);
#765=AXIS2_PLACEMENT_3D('',#1156,#950,#951);
#766=AXIS2_PLACEMENT_3D('',#1158,#952,#953);
#767=AXIS2_PLACEMENT_3D('',#1160,#955,#956);
#768=AXIS2_PLACEMENT_3D('',#1166,#960,#961);
#769=AXIS2_PLACEMENT_3D('',#1168,#962,#963);
#770=AXIS2_PLACEMENT_3D('',#1170,#964,#965);
#771=AXIS2_PLACEMENT_3D('',#1172,#967,#968);
#772=AXIS2_PLACEMENT_3D('',#1178,#972,#973);
#773=AXIS2_PLACEMENT_3D('',#1179,#974,#975);
#774=AXIS2_PLACEMENT_3D('',#1180,#976,#977);
#775=AXIS2_PLACEMENT_3D('',#1181,#978,#979);
#776=AXIS2_PLACEMENT_3D('',#1182,#980,#981);
#777=DIRECTION('axis',(0.,0.,1.));
#778=DIRECTION('refdir',(1.,0.,0.));
#779=DIRECTION('center_axis',(0.,0.,1.));
#780=DIRECTION('ref_axis',(1.,0.,0.));
#781=DIRECTION('center_axis',(0.,0.,-1.));
#782=DIRECTION('ref_axis',(1.,0.,0.));
#783=DIRECTION('',(0.,0.,-1.));
#784=DIRECTION('center_axis',(0.,0.,-1.));
#785=DIRECTION('ref_axis',(1.,0.,0.));
#786=DIRECTION('center_axis',(0.,0.,1.));
#787=DIRECTION('ref_axis',(1.,0.,0.));
#788=DIRECTION('center_axis',(0.,0.,-1.));
#789=DIRECTION('ref_axis',(1.,0.,0.));
#790=DIRECTION('',(0.,0.,-1.));
#791=DIRECTION('center_axis',(0.,0.,-1.));
#792=DIRECTION('ref_axis',(1.,0.,0.));
#793=DIRECTION('center_axis',(0.,0.,1.));
#794=DIRECTION('ref_axis',(1.,0.,0.));
#795=DIRECTION('center_axis',(0.,0.,-1.));
#796=DIRECTION('ref_axis',(1.,0.,0.));
#797=DIRECTION('',(0.,0.,-1.));
#798=DIRECTION('center_axis',(0.,0.,-1.));
#799=DIRECTION('ref_axis',(1.,0.,0.));
#800=DIRECTION('center_axis',(-0.996194698091746,-0.0871557427476584,0.));
#801=DIRECTION('ref_axis',(0.0871557427476584,-0.996194698091746,0.));
#802=DIRECTION('',(-0.0871557427476584,0.996194698091746,0.));
#803=DIRECTION('',(0.,0.,1.));
#804=DIRECTION('',(0.0871557427476584,-0.996194698091746,0.));
#805=DIRECTION('',(0.,0.,1.));
#806=DIRECTION('center_axis',(0.,0.,1.));
#807=DIRECTION('ref_axis',(0.996194698091746,0.0871557427476575,0.));
#808=DIRECTION('center_axis',(0.,0.,1.));
#809=DIRECTION('ref_axis',(0.996194698091746,0.0871557427476575,0.));
#810=DIRECTION('center_axis',(0.,0.,-1.));
#811=DIRECTION('ref_axis',(0.996194698091746,0.0871557427476575,0.));
#812=DIRECTION('',(0.,0.,1.));
#813=DIRECTION('center_axis',(0.0871557427476586,-0.996194698091746,0.));
#814=DIRECTION('ref_axis',(0.996194698091746,0.0871557427476586,0.));
#815=DIRECTION('',(0.996194698091746,0.0871557427476586,0.));
#816=DIRECTION('',(0.996194698091746,0.0871557427476586,0.));
#817=DIRECTION('',(0.,0.,1.));
#818=DIRECTION('center_axis',(0.,0.,1.));
#819=DIRECTION('ref_axis',(-0.996194698091746,-0.0871557427476584,0.));
#820=DIRECTION('center_axis',(0.,0.,1.));
#821=DIRECTION('ref_axis',(0.723181897732469,0.690657616183348,0.));
#822=DIRECTION('center_axis',(0.,0.,1.));
#823=DIRECTION('ref_axis',(0.723181897732469,0.690657616183348,0.));
#824=DIRECTION('',(0.,0.,1.));
#825=DIRECTION('center_axis',(-0.0871557427476582,0.996194698091746,0.));
#826=DIRECTION('ref_axis',(-0.996194698091746,-0.0871557427476582,0.));
#827=DIRECTION('',(-0.996194698091746,-0.0871557427476582,0.));
#828=DIRECTION('',(-0.996194698091746,-0.0871557427476582,0.));
#829=DIRECTION('',(0.,0.,1.));
#830=DIRECTION('center_axis',(-0.972634337510743,0.232341226421481,0.));
#831=DIRECTION('ref_axis',(-0.232341226421481,-0.972634337510743,0.));
#832=DIRECTION('',(-0.232341226421481,-0.972634337510743,0.));
#833=DIRECTION('',(-0.232341226421481,-0.972634337510743,0.));
#834=DIRECTION('',(0.,0.,1.));
#835=DIRECTION('center_axis',(0.,0.,1.));
#836=DIRECTION('ref_axis',(0.917512205861482,0.397707621369264,0.));
#837=DIRECTION('center_axis',(0.,0.,1.));
#838=DIRECTION('ref_axis',(0.917512205861482,0.397707621369264,0.));
#839=DIRECTION('center_axis',(0.,0.,1.));
#840=DIRECTION('ref_axis',(0.917512205861482,0.397707621369264,0.));
#841=DIRECTION('',(0.,0.,1.));
#842=DIRECTION('center_axis',(0.917512205861483,0.397707621369262,0.));
#843=DIRECTION('ref_axis',(-0.397707621369262,0.917512205861483,0.));
#844=DIRECTION('',(0.397707621369262,-0.917512205861483,0.));
#845=DIRECTION('',(-0.397707621369262,0.917512205861483,0.));
#846=DIRECTION('',(0.,0.,1.));
#847=DIRECTION('center_axis',(0.,0.,1.));
#848=DIRECTION('ref_axis',(-0.917512205861483,-0.397707621369262,0.));
#849=DIRECTION('center_axis',(0.,0.,1.));
#850=DIRECTION('ref_axis',(-0.917512205861483,-0.397707621369262,0.));
#851=DIRECTION('center_axis',(0.,0.,-1.));
#852=DIRECTION('ref_axis',(-0.917512205861483,-0.397707621369262,0.));
#853=DIRECTION('',(0.,0.,1.));
#854=DIRECTION('center_axis',(0.,1.,0.));
#855=DIRECTION('ref_axis',(-1.,0.,5.85592771055431E-17));
#856=DIRECTION('',(1.,0.,-5.85592771055431E-17));
#857=DIRECTION('',(-1.,0.,5.85592771055431E-17));
#858=DIRECTION('',(0.,0.,1.));
#859=DIRECTION('center_axis',(0.,0.,1.));
#860=DIRECTION('ref_axis',(1.,4.44089209850062E-16,0.));
#861=DIRECTION('center_axis',(-1.83697019872103E-16,-8.15778644068133E-32,
1.));
#862=DIRECTION('ref_axis',(1.,4.44089209850062E-16,1.83697019872103E-16));
#863=DIRECTION('center_axis',(-1.83697019872103E-16,-8.15778644068133E-32,
1.));
#864=DIRECTION('ref_axis',(1.,4.44089209850062E-16,1.83697019872103E-16));
#865=DIRECTION('',(0.,0.,1.));
#866=DIRECTION('center_axis',(1.,-2.61993535786082E-16,0.));
#867=DIRECTION('ref_axis',(2.61993535786082E-16,1.,2.56483575704639E-17));
#868=DIRECTION('',(2.61993535786082E-16,1.,2.56483575704639E-17));
#869=DIRECTION('',(2.61993535786082E-16,1.,2.56483575704639E-17));
#870=DIRECTION('',(0.,0.,1.));
#871=DIRECTION('center_axis',(0.,0.,1.));
#872=DIRECTION('ref_axis',(-1.,0.,0.));
#873=DIRECTION('center_axis',(0.,0.,1.));
#874=DIRECTION('ref_axis',(-1.,0.,0.));
#875=DIRECTION('center_axis',(0.,0.,-1.));
#876=DIRECTION('ref_axis',(-1.,0.,0.));
#877=DIRECTION('',(0.,0.,1.));
#878=DIRECTION('center_axis',(-1.,0.,0.));
#879=DIRECTION('ref_axis',(0.,-1.,0.));
#880=DIRECTION('',(0.,1.,0.));
#881=DIRECTION('',(0.,-1.,0.));
#882=DIRECTION('',(0.,0.,1.));
#883=DIRECTION('center_axis',(0.,0.,1.));
#884=DIRECTION('ref_axis',(-8.88178419700124E-16,1.,0.));
#885=DIRECTION('center_axis',(0.,0.,1.));
#886=DIRECTION('ref_axis',(-8.88178419700124E-16,1.,0.));
#887=DIRECTION('center_axis',(0.,0.,1.));
#888=DIRECTION('ref_axis',(-8.88178419700124E-16,1.,0.));
#889=DIRECTION('',(0.,0.,1.));
#890=DIRECTION('center_axis',(0.,1.,0.));
#891=DIRECTION('ref_axis',(-1.,0.,0.));
#892=DIRECTION('',(-1.,0.,0.));
#893=DIRECTION('',(-1.,0.,0.));
#894=DIRECTION('',(0.,0.,1.));
#895=DIRECTION('center_axis',(0.,0.,1.));
#896=DIRECTION('ref_axis',(8.88178419700124E-16,-1.,0.));
#897=DIRECTION('center_axis',(0.,0.,1.));
#898=DIRECTION('ref_axis',(8.88178419700124E-16,-1.,0.));
#899=DIRECTION('center_axis',(0.,0.,-1.));
#900=DIRECTION('ref_axis',(8.88178419700124E-16,-1.,0.));
#901=DIRECTION('',(0.,0.,1.));
#902=DIRECTION('center_axis',(-0.917512205861483,0.397707621369262,0.));
#903=DIRECTION('ref_axis',(-0.397707621369262,-0.917512205861483,0.));
#904=DIRECTION('',(-0.397707621369262,-0.917512205861483,0.));
#905=DIRECTION('',(-0.397707621369262,-0.917512205861483,0.));
#906=DIRECTION('',(0.,0.,1.));
#907=DIRECTION('center_axis',(0.,0.,1.));
#908=DIRECTION('ref_axis',(0.972634337510743,0.232341226421482,0.));
#909=DIRECTION('center_axis',(0.,0.,1.));
#910=DIRECTION('ref_axis',(0.972634337510743,0.232341226421482,0.));
#911=DIRECTION('center_axis',(0.,0.,1.));
#912=DIRECTION('ref_axis',(0.972634337510743,0.232341226421482,0.));
#913=DIRECTION('',(0.,0.,1.));
#914=DIRECTION('center_axis',(0.972634337510743,0.232341226421481,0.));
#915=DIRECTION('ref_axis',(-0.232341226421481,0.972634337510743,0.));
#916=DIRECTION('',(0.232341226421481,-0.972634337510743,0.));
#917=DIRECTION('',(-0.232341226421481,0.972634337510743,0.));
#918=DIRECTION('',(0.,0.,1.));
#919=DIRECTION('center_axis',(0.0871557427476582,0.996194698091746,0.));
#920=DIRECTION('ref_axis',(-0.996194698091746,0.0871557427476582,0.));
#921=DIRECTION('',(0.996194698091746,-0.0871557427476582,0.));
#922=DIRECTION('',(-0.996194698091746,0.0871557427476582,0.));
#923=DIRECTION('',(0.,0.,1.));
#924=DIRECTION('center_axis',(0.,0.,1.));
#925=DIRECTION('ref_axis',(0.996194698091746,-0.0871557427476583,0.));
#926=DIRECTION('center_axis',(0.,0.,1.));
#927=DIRECTION('ref_axis',(-0.832126576167042,-0.554585756431335,0.));
#928=DIRECTION('center_axis',(0.,0.,1.));
#929=DIRECTION('ref_axis',(-0.832126576167042,-0.554585756431335,0.));
#930=DIRECTION('',(0.,0.,1.));
#931=DIRECTION('center_axis',(-0.0871557427476581,-0.996194698091746,0.));
#932=DIRECTION('ref_axis',(0.996194698091746,-0.0871557427476581,0.));
#933=DIRECTION('',(-0.996194698091746,0.0871557427476581,0.));
#934=DIRECTION('',(0.996194698091746,-0.0871557427476581,0.));
#935=DIRECTION('',(0.,0.,1.));
#936=DIRECTION('center_axis',(0.,0.,1.));
#937=DIRECTION('ref_axis',(-0.919066866126239,-0.39410163104064,0.));
#938=DIRECTION('center_axis',(0.,0.,1.));
#939=DIRECTION('ref_axis',(-0.919066866126239,-0.39410163104064,0.));
#940=DIRECTION('center_axis',(0.,0.,1.));
#941=DIRECTION('ref_axis',(-0.919066866126239,-0.39410163104064,0.));
#942=DIRECTION('',(0.,0.,1.));
#943=DIRECTION('center_axis',(0.0871557427476579,-0.996194698091746,0.));
#944=DIRECTION('ref_axis',(0.996194698091746,0.0871557427476579,0.));
#945=DIRECTION('',(0.996194698091746,0.0871557427476579,0.));
#946=DIRECTION('',(0.996194698091746,0.0871557427476579,0.));
#947=DIRECTION('',(0.,0.,1.));
#948=DIRECTION('center_axis',(0.,0.,1.));
#949=DIRECTION('ref_axis',(-0.0871557427476679,0.996194698091745,0.));
#950=DIRECTION('center_axis',(0.,0.,1.));
#951=DIRECTION('ref_axis',(-0.0871557427476679,0.996194698091745,0.));
#952=DIRECTION('center_axis',(0.,0.,-1.));
#953=DIRECTION('ref_axis',(-0.0871557427476679,0.996194698091745,0.));
#954=DIRECTION('',(0.,0.,1.));
#955=DIRECTION('center_axis',(0.996194698091746,0.0871557427476584,0.));
#956=DIRECTION('ref_axis',(-0.0871557427476584,0.996194698091746,0.));
#957=DIRECTION('',(0.0871557427476584,-0.996194698091746,0.));
#958=DIRECTION('',(-0.0871557427476584,0.996194698091746,0.));
#959=DIRECTION('',(0.,0.,1.));
#960=DIRECTION('center_axis',(0.,0.,1.));
#961=DIRECTION('ref_axis',(0.0871557427476641,-0.996194698091745,0.));
#962=DIRECTION('center_axis',(0.,0.,1.));
#963=DIRECTION('ref_axis',(0.0871557427476641,-0.996194698091745,0.));
#964=DIRECTION('center_axis',(0.,0.,1.));
#965=DIRECTION('ref_axis',(0.0871557427476641,-0.996194698091745,0.));
#966=DIRECTION('',(0.,0.,1.));
#967=DIRECTION('center_axis',(0.0871557427476579,-0.996194698091746,0.));
#968=DIRECTION('ref_axis',(0.996194698091746,0.0871557427476579,0.));
#969=DIRECTION('',(-0.996194698091746,-0.0871557427476579,0.));
#970=DIRECTION('',(0.996194698091746,0.0871557427476579,0.));
#971=DIRECTION('',(0.,0.,1.));
#972=DIRECTION('center_axis',(0.,0.,1.));
#973=DIRECTION('ref_axis',(-0.996194698091746,-0.0871557427476579,0.));
#974=DIRECTION('center_axis',(0.,0.,1.));
#975=DIRECTION('ref_axis',(-0.996194698091746,-0.0871557427476579,0.));
#976=DIRECTION('center_axis',(0.,0.,1.));
#977=DIRECTION('ref_axis',(-0.996194698091746,-0.0871557427476579,0.));
#978=DIRECTION('center_axis',(0.,0.,1.));
#979=DIRECTION('ref_axis',(1.,0.,0.));
#980=DIRECTION('center_axis',(0.,0.,1.));
#981=DIRECTION('ref_axis',(1.,0.,0.));
#982=CARTESIAN_POINT('',(0.,0.,0.));
#983=CARTESIAN_POINT('Origin',(-109.581416790092,-9.5871317022424,0.));
#984=CARTESIAN_POINT('',(-119.581416790092,-9.5871317022424,3.));
#985=CARTESIAN_POINT('Origin',(-109.581416790092,-9.5871317022424,3.));
#986=CARTESIAN_POINT('',(-119.581416790092,-9.5871317022424,0.));
#987=CARTESIAN_POINT('',(-119.581416790092,-9.5871317022424,0.));
#988=CARTESIAN_POINT('Origin',(-109.581416790092,-9.5871317022424,0.));
#989=CARTESIAN_POINT('Origin',(109.581416790092,-9.5871317022424,0.));
#990=CARTESIAN_POINT('',(99.581416790092,-9.5871317022424,3.));
#991=CARTESIAN_POINT('Origin',(109.581416790092,-9.5871317022424,3.));
#992=CARTESIAN_POINT('',(99.581416790092,-9.5871317022424,0.));
#993=CARTESIAN_POINT('',(99.581416790092,-9.5871317022424,0.));
#994=CARTESIAN_POINT('Origin',(109.581416790092,-9.5871317022424,0.));
#995=CARTESIAN_POINT('Origin',(-62.881484680429,-5.50141705521657,0.));
#996=CARTESIAN_POINT('',(-64.381484680429,-5.50141705521657,3.));
#997=CARTESIAN_POINT('Origin',(-62.881484680429,-5.50141705521657,3.));
#998=CARTESIAN_POINT('',(-64.381484680429,-5.50141705521657,0.));
#999=CARTESIAN_POINT('',(-64.381484680429,-5.50141705521657,0.));
#1000=CARTESIAN_POINT('Origin',(-62.881484680429,-5.50141705521657,0.));
#1001=CARTESIAN_POINT('Origin',(-76.0210876125557,-21.2063709993882,0.));
#1002=CARTESIAN_POINT('',(-75.4981531560697,-27.1835391879387,0.));
#1003=CARTESIAN_POINT('',(-76.0210876125557,-21.2063709993882,0.));
#1004=CARTESIAN_POINT('',(-75.4981531560697,-27.1835391879387,0.));
#1005=CARTESIAN_POINT('',(-75.4981531560697,-27.1835391879387,3.));
#1006=CARTESIAN_POINT('',(-75.4981531560697,-27.1835391879387,0.));
#1007=CARTESIAN_POINT('',(-76.0210876125557,-21.2063709993882,3.));
#1008=CARTESIAN_POINT('',(-75.4981531560697,-27.1835391879387,3.));
#1009=CARTESIAN_POINT('',(-76.0210876125557,-21.2063709993882,0.));
#1010=CARTESIAN_POINT('Origin',(-78.0134770087392,-21.3806824848835,0.));
#1011=CARTESIAN_POINT('',(-78.1877884942345,-19.3882930887,0.));
#1012=CARTESIAN_POINT('Origin',(-78.0134770087392,-21.3806824848835,0.));
#1013=CARTESIAN_POINT('',(-78.1877884942345,-19.3882930887,3.));
#1014=CARTESIAN_POINT('Origin',(-78.0134770087392,-21.3806824848835,3.));
#1015=CARTESIAN_POINT('',(-78.1877884942345,-19.3882930887,0.));
#1016=CARTESIAN_POINT('Origin',(-92.9388852667512,-20.6788468308691,0.));
#1017=CARTESIAN_POINT('',(-92.9388852667512,-20.6788468308691,0.));
#1018=CARTESIAN_POINT('',(-92.9388852667512,-20.6788468308691,0.));
#1019=CARTESIAN_POINT('',(-92.9388852667512,-20.6788468308691,3.));
#1020=CARTESIAN_POINT('',(-92.9388852667512,-20.6788468308691,3.));
#1021=CARTESIAN_POINT('',(-92.9388852667512,-20.6788468308691,0.));
#1022=CARTESIAN_POINT('Origin',(-109.581416790092,-9.5871317022424,0.));
#1023=CARTESIAN_POINT('',(-95.1177788354426,4.22602062142455,0.));
#1024=CARTESIAN_POINT('Origin',(-109.581416790092,-9.5871317022424,0.));
#1025=CARTESIAN_POINT('',(-95.1177788354426,4.22602062142455,3.));
#1026=CARTESIAN_POINT('Origin',(-109.581416790092,-9.5871317022424,3.));
#1027=CARTESIAN_POINT('',(-95.1177788354426,4.22602062142455,0.));
#1028=CARTESIAN_POINT('Origin',(-38.941568213741,9.14080221066804,0.));
#1029=CARTESIAN_POINT('',(-38.941568213741,9.14080221066804,0.));
#1030=CARTESIAN_POINT('',(-38.941568213741,9.14080221066804,0.));
#1031=CARTESIAN_POINT('',(-38.941568213741,9.14080221066804,3.));
#1032=CARTESIAN_POINT('',(-38.941568213741,9.14080221066804,3.));
#1033=CARTESIAN_POINT('',(-38.941568213741,9.14080221066804,0.));
#1034=CARTESIAN_POINT('Origin',(-33.3368527134144,32.6034430495664,0.));
#1035=CARTESIAN_POINT('',(-33.3368527134144,32.6034430495664,0.));
#1036=CARTESIAN_POINT('',(-33.3368527134144,32.6034430495664,0.));
#1037=CARTESIAN_POINT('',(-33.3368527134144,32.6034430495664,3.));
#1038=CARTESIAN_POINT('',(-33.3368527134144,32.6034430495664,3.));
#1039=CARTESIAN_POINT('',(-33.3368527134144,32.6034430495664,0.));
#1040=CARTESIAN_POINT('Origin',(-30.9052668696376,32.0225899835127,0.));
#1041=CARTESIAN_POINT('',(-28.6114863549839,33.0168590369359,0.));
#1042=CARTESIAN_POINT('Origin',(-30.9052668696376,32.0225899835127,0.));
#1043=CARTESIAN_POINT('',(-28.6114863549839,33.0168590369359,3.));
#1044=CARTESIAN_POINT('Origin',(-30.9052668696376,32.0225899835127,3.));
#1045=CARTESIAN_POINT('',(-28.6114863549839,33.0168590369359,0.));
#1046=CARTESIAN_POINT('Origin',(-25.1199377022011,24.9618499444322,0.));
#1047=CARTESIAN_POINT('',(-25.1199377022011,24.9618499444322,0.));
#1048=CARTESIAN_POINT('',(-28.6114863549839,33.0168590369359,0.));
#1049=CARTESIAN_POINT('',(-25.1199377022011,24.9618499444322,3.));
#1050=CARTESIAN_POINT('',(-28.6114863549839,33.0168590369359,3.));
#1051=CARTESIAN_POINT('',(-25.1199377022011,24.9618499444322,0.));
#1052=CARTESIAN_POINT('Origin',(-20.5323766728937,26.9503880512785,0.));
#1053=CARTESIAN_POINT('',(-20.5323766728937,21.9503880512785,0.));
#1054=CARTESIAN_POINT('Origin',(-20.5323766728937,26.9503880512785,0.));
#1055=CARTESIAN_POINT('',(-20.5323766728937,21.9503880512785,3.));
#1056=CARTESIAN_POINT('Origin',(-20.5323766728937,26.9503880512785,3.));
#1057=CARTESIAN_POINT('',(-20.5323766728937,21.9503880512785,0.));
#1058=CARTESIAN_POINT('Origin',(-17.5,21.9503880512785,-1.77573785876366E-16));
#1059=CARTESIAN_POINT('',(-17.5,21.9503880512785,-4.83735485663204E-16));
#1060=CARTESIAN_POINT('',(-20.5323766728937,21.9503880512785,0.));
#1061=CARTESIAN_POINT('',(-17.5,21.9503880512785,3.));
#1062=CARTESIAN_POINT('',(-20.5323766728937,21.9503880512785,3.));
#1063=CARTESIAN_POINT('',(-17.5,21.9503880512785,-4.83735485663204E-16));
#1064=CARTESIAN_POINT('Origin',(-17.5,16.9503880512785,-4.83735485663204E-16));
#1065=CARTESIAN_POINT('',(-12.5,16.9503880512785,4.3474961369731E-16));
#1066=CARTESIAN_POINT('Origin',(-17.5,16.9503880512785,-4.83735485663204E-16));
#1067=CARTESIAN_POINT('',(-12.5,16.9503880512785,3.));
#1068=CARTESIAN_POINT('Origin',(-17.5,16.9503880512785,3.));
#1069=CARTESIAN_POINT('',(-12.5,16.9503880512785,4.3474961369731E-16));
#1070=CARTESIAN_POINT('Origin',(-12.5,0.,0.));
#1071=CARTESIAN_POINT('',(-12.5,0.,0.));
#1072=CARTESIAN_POINT('',(-12.5,0.,0.));
#1073=CARTESIAN_POINT('',(-12.5,0.,3.));
#1074=CARTESIAN_POINT('',(-12.5,0.,3.));
#1075=CARTESIAN_POINT('',(-12.5,0.,0.));
#1076=CARTESIAN_POINT('Origin',(0.,0.,0.));
#1077=CARTESIAN_POINT('',(12.5,-1.53080849893419E-15,0.));
#1078=CARTESIAN_POINT('Origin',(0.,0.,0.));
#1079=CARTESIAN_POINT('',(12.5,-1.11022302462516E-15,3.));
#1080=CARTESIAN_POINT('Origin',(0.,0.,3.));
#1081=CARTESIAN_POINT('',(12.5,-1.53080849893419E-15,0.));
#1082=CARTESIAN_POINT('Origin',(12.5,16.9503880512785,0.));
#1083=CARTESIAN_POINT('',(12.5,16.9503880512785,0.));
#1084=CARTESIAN_POINT('',(12.5,0.,0.));
#1085=CARTESIAN_POINT('',(12.5,16.9503880512785,3.));
#1086=CARTESIAN_POINT('',(12.5,0.,3.));
#1087=CARTESIAN_POINT('',(12.5,16.9503880512785,0.));
#1088=CARTESIAN_POINT('Origin',(17.5,16.9503880512785,0.));
#1089=CARTESIAN_POINT('',(17.5,21.9503880512785,0.));
#1090=CARTESIAN_POINT('Origin',(17.5,16.9503880512785,0.));
#1091=CARTESIAN_POINT('',(17.5,21.9503880512785,3.));
#1092=CARTESIAN_POINT('Origin',(17.5,16.9503880512785,3.));
#1093=CARTESIAN_POINT('',(17.5,21.9503880512785,0.));
#1094=CARTESIAN_POINT('Origin',(20.5323766728937,21.9503880512785,0.));
#1095=CARTESIAN_POINT('',(20.5323766728937,21.9503880512785,0.));
#1096=CARTESIAN_POINT('',(20.5323766728937,21.9503880512785,0.));
#1097=CARTESIAN_POINT('',(20.5323766728937,21.9503880512785,3.));
#1098=CARTESIAN_POINT('',(20.5323766728937,21.9503880512785,3.));
#1099=CARTESIAN_POINT('',(20.5323766728937,21.9503880512785,0.));
#1100=CARTESIAN_POINT('Origin',(20.5323766728937,26.9503880512785,0.));
#1101=CARTESIAN_POINT('',(25.1199377022011,24.9618499444322,0.));
#1102=CARTESIAN_POINT('Origin',(20.5323766728937,26.9503880512785,0.));
#1103=CARTESIAN_POINT('',(25.1199377022011,24.9618499444322,3.));
#1104=CARTESIAN_POINT('Origin',(20.5323766728937,26.9503880512785,3.));
#1105=CARTESIAN_POINT('',(25.1199377022011,24.9618499444322,0.));
#1106=CARTESIAN_POINT('Origin',(28.6114863549839,33.0168590369359,0.));
#1107=CARTESIAN_POINT('',(28.6114863549839,33.0168590369359,0.));
#1108=CARTESIAN_POINT('',(28.6114863549839,33.0168590369359,0.));
#1109=CARTESIAN_POINT('',(28.6114863549838,33.0168590369359,3.));
#1110=CARTESIAN_POINT('',(28.6114863549839,33.0168590369359,3.));
#1111=CARTESIAN_POINT('',(28.6114863549839,33.0168590369359,0.));
#1112=CARTESIAN_POINT('Origin',(30.9052668696376,32.0225899835127,0.));
#1113=CARTESIAN_POINT('',(33.3368527134144,32.6034430495664,0.));
#1114=CARTESIAN_POINT('Origin',(30.9052668696376,32.0225899835127,0.));
#1115=CARTESIAN_POINT('',(33.3368527134144,32.6034430495664,3.));
#1116=CARTESIAN_POINT('Origin',(30.9052668696376,32.0225899835127,3.));
#1117=CARTESIAN_POINT('',(33.3368527134144,32.6034430495664,0.));
#1118=CARTESIAN_POINT('Origin',(38.941568213741,9.14080221066804,0.));
#1119=CARTESIAN_POINT('',(38.941568213741,9.14080221066804,0.));
#1120=CARTESIAN_POINT('',(33.3368527134144,32.6034430495664,0.));
#1121=CARTESIAN_POINT('',(38.941568213741,9.14080221066804,3.));
#1122=CARTESIAN_POINT('',(33.3368527134144,32.6034430495664,3.));
#1123=CARTESIAN_POINT('',(38.941568213741,9.14080221066804,0.));
#1124=CARTESIAN_POINT('Origin',(95.1177788354426,4.22602062142455,0.));
#1125=CARTESIAN_POINT('',(95.1177788354426,4.22602062142454,0.));
#1126=CARTESIAN_POINT('',(38.941568213741,9.14080221066804,0.));
#1127=CARTESIAN_POINT('',(95.1177788354426,4.22602062142454,3.));
#1128=CARTESIAN_POINT('',(38.941568213741,9.14080221066804,3.));
#1129=CARTESIAN_POINT('',(95.1177788354426,4.22602062142454,0.));
#1130=CARTESIAN_POINT('Origin',(109.581416790092,-9.5871317022424,0.));
#1131=CARTESIAN_POINT('',(92.9388852667512,-20.6788468308691,0.));
#1132=CARTESIAN_POINT('Origin',(109.581416790092,-9.5871317022424,0.));
#1133=CARTESIAN_POINT('',(92.9388852667512,-20.6788468308691,3.));
#1134=CARTESIAN_POINT('Origin',(109.581416790092,-9.5871317022424,3.));
#1135=CARTESIAN_POINT('',(92.9388852667512,-20.6788468308691,0.));
#1136=CARTESIAN_POINT('Origin',(36.7626746450496,-15.7640652416256,0.));
#1137=CARTESIAN_POINT('',(36.7626746450496,-15.7640652416256,0.));
#1138=CARTESIAN_POINT('',(92.9388852667512,-20.6788468308691,0.));
#1139=CARTESIAN_POINT('',(36.7626746450496,-15.7640652416256,3.));
#1140=CARTESIAN_POINT('',(92.9388852667512,-20.6788468308691,3.));
#1141=CARTESIAN_POINT('',(36.7626746450496,-15.7640652416256,0.));
#1142=CARTESIAN_POINT('Origin',(0.,0.,0.));
#1143=CARTESIAN_POINT('',(-36.7626746450496,-15.7640652416256,0.));
#1144=CARTESIAN_POINT('Origin',(0.,0.,0.));
#1145=CARTESIAN_POINT('',(-36.7626746450496,-15.7640652416256,3.));
#1146=CARTESIAN_POINT('Origin',(0.,0.,3.));
#1147=CARTESIAN_POINT('',(-36.7626746450496,-15.7640652416256,0.));
#1148=CARTESIAN_POINT('Origin',(-59.2600892304913,-17.7323339764945,0.));
#1149=CARTESIAN_POINT('',(-59.2600892304913,-17.7323339764945,0.));
#1150=CARTESIAN_POINT('',(-59.2600892304913,-17.7323339764945,0.));
#1151=CARTESIAN_POINT('',(-59.2600892304913,-17.7323339764945,3.));
#1152=CARTESIAN_POINT('',(-59.2600892304913,-17.7323339764945,3.));
#1153=CARTESIAN_POINT('',(-59.2600892304913,-17.7323339764945,0.));
#1154=CARTESIAN_POINT('Origin',(-59.085777744996,-19.724723372678,0.));
#1155=CARTESIAN_POINT('',(-61.0781671411795,-19.8990348581733,0.));
#1156=CARTESIAN_POINT('Origin',(-59.085777744996,-19.724723372678,0.));
#1157=CARTESIAN_POINT('',(-61.0781671411795,-19.8990348581733,3.));
#1158=CARTESIAN_POINT('Origin',(-59.085777744996,-19.724723372678,3.));
#1159=CARTESIAN_POINT('',(-61.0781671411795,-19.8990348581733,0.));
#1160=CARTESIAN_POINT('Origin',(-60.5552326846936,-25.8762030467238,0.));
#1161=CARTESIAN_POINT('',(-60.5552326846936,-25.8762030467238,0.));
#1162=CARTESIAN_POINT('',(-61.0781671411795,-19.8990348581733,0.));
#1163=CARTESIAN_POINT('',(-60.5552326846936,-25.8762030467238,3.));
#1164=CARTESIAN_POINT('',(-61.0781671411795,-19.8990348581733,3.));
#1165=CARTESIAN_POINT('',(-60.5552326846936,-25.8762030467238,0.));
#1166=CARTESIAN_POINT('Origin',(-62.547622080877,-26.0505145322191,0.));
#1167=CARTESIAN_POINT('',(-62.3733105953817,-28.0429039284026,0.));
#1168=CARTESIAN_POINT('Origin',(-62.547622080877,-26.0505145322191,0.));
#1169=CARTESIAN_POINT('',(-62.3733105953817,-28.0429039284026,3.));
#1170=CARTESIAN_POINT('Origin',(-62.547622080877,-26.0505145322191,3.));
#1171=CARTESIAN_POINT('',(-62.3733105953817,-28.0429039284026,0.));
#1172=CARTESIAN_POINT('Origin',(-73.3314522743909,-29.0016170986269,0.));
#1173=CARTESIAN_POINT('',(-73.3314522743909,-29.0016170986269,0.));
#1174=CARTESIAN_POINT('',(-62.3733105953817,-28.0429039284026,0.));
#1175=CARTESIAN_POINT('',(-73.3314522743909,-29.0016170986269,3.));
#1176=CARTESIAN_POINT('',(-62.3733105953817,-28.0429039284026,3.));
#1177=CARTESIAN_POINT('',(-73.3314522743909,-29.0016170986269,0.));
#1178=CARTESIAN_POINT('Origin',(-73.5057637598862,-27.0092277024434,0.));
#1179=CARTESIAN_POINT('Origin',(-73.5057637598862,-27.0092277024434,0.));
#1180=CARTESIAN_POINT('Origin',(-73.5057637598862,-27.0092277024434,3.));
#1181=CARTESIAN_POINT('Origin',(0.,-2.73870500824364,3.));
#1182=CARTESIAN_POINT('Origin',(0.,-2.73870500824364,0.));
#1183=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#1187,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#1184=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#1187,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#1185=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1183))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1187,#1189,#1190))
REPRESENTATION_CONTEXT('','3D')
);
#1186=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1184))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1187,#1189,#1190))
REPRESENTATION_CONTEXT('','3D')
);
#1187=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#1188=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT($,.METRE.)
);
#1189=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#1190=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#1191=SHAPE_DEFINITION_REPRESENTATION(#1192,#1193);
#1192=PRODUCT_DEFINITION_SHAPE('',$,#1195);
#1193=SHAPE_REPRESENTATION('',(#707),#1185);
#1194=PRODUCT_DEFINITION_CONTEXT('part definition',#1199,'design');
#1195=PRODUCT_DEFINITION('S_991','S_991 v1',#1196,#1194);
#1196=PRODUCT_DEFINITION_FORMATION('',$,#1201);
#1197=PRODUCT_RELATED_PRODUCT_CATEGORY('S_991 v1','S_991 v1',(#1201));
#1198=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2009,#1199);
#1199=APPLICATION_CONTEXT(
'Core Data for Automotive Mechanical Design Process');
#1200=PRODUCT_CONTEXT('part definition',#1199,'mechanical');
#1201=PRODUCT('S_991','S_991 v1',$,(#1200));
#1202=PRESENTATION_STYLE_ASSIGNMENT((#1203));
#1203=SURFACE_STYLE_USAGE(.BOTH.,#1204);
#1204=SURFACE_SIDE_STYLE('',(#1205));
#1205=SURFACE_STYLE_FILL_AREA(#1206);
#1206=FILL_AREA_STYLE('Steel - Satin',(#1207));
#1207=FILL_AREA_STYLE_COLOUR('Steel - Satin',#1208);
#1208=COLOUR_RGB('Steel - Satin',0.627450980392157,0.627450980392157,0.627450980392157);
ENDSEC;
END-ISO-10303-21;
