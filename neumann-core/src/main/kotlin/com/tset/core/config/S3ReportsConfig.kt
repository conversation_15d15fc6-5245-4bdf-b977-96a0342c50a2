package com.tset.core.config

import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Profile
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3Client

@Configuration
@Profile("cloud")
class S3ReportsConfig {

    @Value("\${aws.s3.reports.region}")
    lateinit var region: String

    @Value("\${aws.s3.reports.access_key}")
    lateinit var accessKey: String

    @Value("\${aws.s3.reports.secret_key}")
    lateinit var secretKey: String

    @Bean(value = arrayOf("s3ClientForReports"), destroyMethod = "close")
    fun s3ClientForReports(): S3Client {
        return S3Client.builder()
            .region(Region.of(region))
            .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create(accessKey, secretKey)))
            .build()
    }
}
