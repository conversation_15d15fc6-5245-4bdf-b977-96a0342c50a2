package com.tset.core.service.domain.calculation

import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.RoughManufacturing
import com.nu.bom.core.manufacturing.enums.Entities
import kotlin.reflect.KClass

enum class CalculationType(
    val entityClass: KClass<out BaseManufacturing>,
    private val hasSubEntities: Boolean,
    val hasCostModule: Boolean,
    val canBeSub: Boolean,
    private val entitiesWhitelist: List<Entities>,
) {
    DETAILED_CALCULATION(Manufacturing::class, true, true, true, emptyList()),
    MANUAL_CALCULATION(ManualManufacturing::class, true, true, true, emptyList()),
    ROUGH_CALCULATION(RoughManufacturing::class, false, false, true, listOf(Entities.ATTACHMENT)),
    EXCEL_FILE_IMPORT(ManualManufacturing::class, true, false, true, emptyList()),
    TSET_FILE_IMPORT(Manufacturing::class, false, false, false, emptyList()),
    ACCOUNT_SPECIFIC_IMPORT(Manufacturing::class, false, false, false, emptyList()),
    ;

    fun isSubEntityAllowed(entity: Entities): Boolean {
        return hasSubEntities || entitiesWhitelist.contains(entity)
    }

    companion object {
        const val DETAILED_CALCULATION_VALUE = "DETAILED_CALCULATION"
        const val MANUAL_CALCULATION_VALUE = "MANUAL_CALCULATION"
        const val ROUGH_CALCULATION_VALUE = "ROUGH_CALCULATION"
        const val EXCEL_FILE_IMPORT_VALUE = "EXCEL_FILE_IMPORT"
        const val TSET_FILE_IMPORT_VALUE = "TSET_FILE_IMPORT"
        const val ACCOUNT_SPECIFIC_IMPORT_VALUE = "ACCOUNT_SPECIFIC_IMPORT"

        fun fromManufacturingEntityClass(className: String?): CalculationType =
            CalculationType.entries.firstOrNull { it.entityClass.simpleName == className } ?: DETAILED_CALCULATION

        fun fromManufacturingEntityClass(kClass: KClass<out ManufacturingEntity>): CalculationType =
            CalculationType.entries.firstOrNull { it.entityClass == kClass } ?: DETAILED_CALCULATION

        fun fromManufacturingEntityClass(clazz: Class<ManufacturingEntity>): CalculationType =
            CalculationType.entries.firstOrNull { it.entityClass == clazz.kotlin } ?: DETAILED_CALCULATION
    }
}
