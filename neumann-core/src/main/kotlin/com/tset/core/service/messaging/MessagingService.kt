package com.tset.core.service

import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

interface Message

interface MessagingService<EVENT : Message> {

    fun send(message: EVENT) = send(Mono.just(message))
    fun send(messages: List<EVENT>) = send(Flux.fromIterable(messages))

    fun send(message: Mono<EVENT>): Mono<Void>
    fun send(messages: Flux<EVENT>): Mono<Void>

    fun receive(consumer: (EVENT) -> Mono<Void>): Mono<Void>
}
