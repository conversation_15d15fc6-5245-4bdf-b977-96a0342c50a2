package com.tset.core.service.export

import com.tset.core.module.export.dto.ExportMode

enum class BuiltInExportFormat(
    val formatRequirement: ExportFormatRequirement,
) {
    TSET_XLSX(
        BuiltInXlsxExportFormatRequirement(
            formatId = "TSET_XLSX",
            formatName = ExportFormat.XLSX,
            formatLabel = ".xlsx (TSET)",
            configResourceFile = "cbd_export_config_tset.json",
            templateResourceFile = "cbd_export_template_tset.xlsx",
            exportMode = ExportMode.COST,
        ),
    ),
    TSET_CO2_XLSX(
        BuiltInXlsxExportFormatRequirement(
            formatId = "TSET_CO2_XLSX",
            formatName = ExportFormat.XLSX,
            formatLabel = ".xlsx (TSET)",
            configResourceFile = "cbd_export_config_tset_co2.json",
            templateResourceFile = "cbd_export_template_tset_co2.xlsx",
            exportMode = ExportMode.CO2,
        ),
    ),
    PPC_V9_XLSX(
        PcmV9ExportFormatRequirement(
            "PPC_V9_XLSX",
            ExportFormat.XLSX,
            ".xlsx v9",
            "cbd_export_config_v9.json",
        ),
    ),
    PPC_V9_CSV(
        PcmV9ExportFormatRequirement(
            "PPC_V9_CSV",
            ExportFormat.CSV,
            ".csv v9",
            "cbd_export_config_v9.json",
        ),
    ),
    TSET_FILE(BuiltInExportFormatRequirement("TSET_FILE", ExportFormat.TSET, ".tset", exportMode = ExportMode.COST)),
}
