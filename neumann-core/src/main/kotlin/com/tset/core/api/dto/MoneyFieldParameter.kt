package com.tset.core.api.dto

import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.utils.applyIfExists
import com.tset.bom.clients.common.DenominatorUnit
import com.tset.core.module.bom.exchangerates.ExchangeRateMap
import org.bson.types.Decimal128
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.core.convert.converter.Converter
import java.math.BigDecimal

interface NumberBasedFieldParameter<T : NumberBasedFieldParameter<T>> {
    val name: String
    val type: String
    val unit: String?
    val source: String?
    val value: BigDecimal
    val denominatorUnit: DenominatorUnit?

    fun plus(other: NumberBasedFieldParameter<*>?): T

    fun minus(other: NumberBasedFieldParameter<*>?): T

    fun copy(denominatorUnit: DenominatorUnit): T
}

data class BigDecimalFieldParameter(
    override val name: String,
    override val value: BigDecimal,
    override val type: String,
    override val unit: String? = null,
    override val source: String? = null,
    override val denominatorUnit: DenominatorUnit? = null,
) : NumberBasedFieldParameter<BigDecimalFieldParameter> {
    override fun plus(other: NumberBasedFieldParameter<*>?): BigDecimalFieldParameter =
        this.copy(value = other.applyIfExists { this.value.plus((it.value)) } ?: this.value)

    override fun minus(other: NumberBasedFieldParameter<*>?): BigDecimalFieldParameter =
        this.copy(value = other.applyIfExists { this.value.minus((it.value)) } ?: this.value)

    companion object {
        fun zero(
            name: String,
            unit: String?,
            denominatorUnit: DenominatorUnit?,
        ) = BigDecimalFieldParameter(
            name = name,
            value = BigDecimal.ZERO,
            type = "CO2",
            unit = unit ?: "KILOGRAM",
            denominatorUnit = denominatorUnit,
        )
    }

    override fun copy(denominatorUnit: DenominatorUnit) =
        BigDecimalFieldParameter(
            name = name,
            value = value,
            type = type,
            unit = unit,
            source = source,
            denominatorUnit = denominatorUnit,
        )
}

data class MoneyFieldParameter(
    override val name: String,
    override val value: BigDecimal,
    override val type: String = "Money",
    override val unit: String? = null,
    override val source: String? = null,
    override val denominatorUnit: DenominatorUnit?,
    val currencyInfo: CurrencyInfo? = null,
) : NumberBasedFieldParameter<MoneyFieldParameter> {
    override fun plus(other: NumberBasedFieldParameter<*>?): MoneyFieldParameter = aggregate(other, BigDecimal::plus)

    override fun minus(other: NumberBasedFieldParameter<*>?): MoneyFieldParameter = aggregate(other, BigDecimal::minus)

    override fun copy(denominatorUnit: DenominatorUnit) =
        MoneyFieldParameter(
            name = name,
            value = value,
            type = type,
            unit = unit,
            source = source,
            denominatorUnit = denominatorUnit,
            currencyInfo = currencyInfo,
        )

    fun aggregate(
        other: NumberBasedFieldParameter<*>?,
        aggregateFunction: (BigDecimal, BigDecimal) -> BigDecimal,
    ) = if (other != null && other is MoneyFieldParameter) {
        require(this.value == BigDecimal.ZERO || other.value == BigDecimal.ZERO || this.denominatorUnit == other.denominatorUnit)
        val leftMap = this.currencyInfo?.values ?: emptyMap()
        val rightMap = other.currencyInfo?.values ?: emptyMap()
        val combinedMap =
            if (rightMap.isNotEmpty()) {
                leftMap.plus(rightMap).keys
            } else {
                leftMap.keys
            }
        MoneyFieldParameter(
            name = this.name,
            value = aggregateFunction(this.value, other.value),
            currencyInfo =
                CurrencyInfo(
                    combinedMap
                        .fold(emptyMap()) { acc, next ->
                            val leftValue = leftMap[next] ?: BigDecimal.ZERO
                            val rightValue = rightMap[next] ?: BigDecimal.ZERO
                            acc.plus(Pair(next, aggregateFunction(leftValue, rightValue)))
                        },
                ),
            denominatorUnit = this.denominatorUnit ?: other.denominatorUnit,
        )
    } else {
        if (other != null) logger.warn("Failed trying to aggregate $this to $other due to different types")
        this
    }

    companion object {
        val logger: Logger = LoggerFactory.getLogger(MoneyFieldParameter::class.java)

        fun zero(name: String) =
            MoneyFieldParameter(
                name = name,
                value = BigDecimal.ZERO,
                currencyInfo = CurrencyInfo.empty(),
                denominatorUnit = null,
            )

        fun fromEurValue(
            name: String,
            value: BigDecimal?,
            exchangeRateMap: ExchangeRateMap,
            costDenominatorUnit: DenominatorUnit,
        ) = value?.let { eurValue ->
            MoneyFieldParameter(
                name = name,
                value = eurValue,
                currencyInfo = exchangeRateMap.eurToCurrencyInfo(eurValue),
                denominatorUnit = costDenominatorUnit,
            )
        } ?: MoneyFieldParameter(
            name = name,
            value = BigDecimal.ZERO,
            currencyInfo = CurrencyInfo.empty(),
            denominatorUnit = costDenominatorUnit,
        )

        fun fromFieldParameter(other: FieldParameter): MoneyFieldParameter =
            MoneyFieldParameter(
                name = other.name,
                value =
                    when (other.value) {
                        is BigDecimal -> other.value
                        else -> BigDecimal.ZERO
                    },
                currencyInfo = other.currencyInfo,
                denominatorUnit = other.denominatorUnit,
            )

        class LegacyDecimal128ReadConverter : Converter<Decimal128, MoneyFieldParameter> {
            override fun convert(source: Decimal128): MoneyFieldParameter =
                MoneyFieldParameter(
                    name = "",
                    value = source.bigDecimalValue(),
                    denominatorUnit = null,
                )
        }

        class LegacyStringReadConverter : Converter<String, MoneyFieldParameter> {
            override fun convert(source: String): MoneyFieldParameter =
                MoneyFieldParameter(
                    name = "",
                    value = BigDecimal(source),
                    denominatorUnit = null,
                )
        }
    }
}
