package com.tset.core.api.admin

import com.nu.bom.core.user.AccessCheckProvider
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.tset.core.module.export.dto.ExportApiConfigDto
import com.tset.core.module.export.dto.ExportMode
import com.tset.core.module.mongodb.infrastructure.dto.AccountDataEncodingType
import com.tset.core.module.mongodb.infrastructure.dto.AccountDataHeaderDto
import com.tset.core.service.account.AccountDataService
import com.tset.core.service.export.ExportFormat
import org.springframework.http.codec.multipart.FilePart
import org.springframework.http.codec.multipart.Part
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.io.InputStream
import java.io.SequenceInputStream

@RestController
@RequestMapping("/api/admin/accountData")
class AccountDataController(
    private val accessCheckProvider: AccessCheckProvider,
    private val accountDataService: AccountDataService,
) {
    @PatchMapping("/label")
    fun updateAccountLabel(
        @AuthenticationPrincipal jwt: Jwt?,
        @RequestParam accountId: String,
        @RequestParam label: String,
    ): Mono<Boolean> =
        accessCheckProvider.adminOnly(jwt) {
            accountDataService.updateAccountLabel(accountId, label).thenReturn(true)
        }

    @GetMapping("/{dataId}")
    fun getData(
        @AuthenticationPrincipal jwt: Jwt?,
        @RequestParam accountId: String,
        @PathVariable dataId: String,
    ): Mono<ByteArray> =
        accessCheckProvider.adminOnly(jwt) {
            accountDataService.getDataById(dataId)
        }

    @PostMapping("", consumes = ["multipart/form-data"])
    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    fun uploadAccountData(
        @AuthenticationPrincipal jwt: Jwt?,
        @RequestParam accountId: String,
        @RequestParam encoding: AccountDataEncodingType,
        @RequestParam fileName: String,
        @RequestBody parts: Flux<Part>,
    ): Mono<String> =
        accessCheckProvider.adminOnly(jwt) {
            parts
                .ofType(FilePart::class.java)
                .flatMap { it.content() }
                .reduce(
                    object : InputStream() {
                        override fun read() = -1
                    },
                ) { s: InputStream, d -> SequenceInputStream(s, d.asInputStream()) }
                .map {
                    it.readAllBytes()
                }.flatMap {
                    accountDataService.upload(accountId, encoding, fileName, it)
                }
        }

    @GetMapping("")
    fun getAccountDataHeaders(
        @AuthenticationPrincipal jwt: Jwt?,
        @RequestParam accountId: String,
    ): Mono<List<AccountDataHeaderDto>> =
        accessCheckProvider.adminOnly(jwt) {
            accountDataService.getDataHeadersForAccount(accountId)
        }

    @DeleteMapping("")
    fun deleteAccountData(
        @AuthenticationPrincipal jwt: Jwt?,
        @RequestParam accountId: String,
    ) = accessCheckProvider.adminOnly(jwt) {
        accountDataService.delete(accountId).thenReturn(true)
    }

    @PutMapping("/export")
    fun addAccountDataSpecificExport(
        @AuthenticationPrincipal jwt: Jwt?,
        @RequestParam accountId: String,
        @RequestParam formatId: String,
        @RequestParam formatName: ExportFormat,
        @RequestParam templateAccountDataId: String,
        @RequestParam configAccountDataId: String,
        @RequestParam mode: ExportMode?,
    ): Mono<Boolean> =
        accessCheckProvider.adminOnly(jwt) {
            accountDataService.addFileExportMapping(
                accountId,
                formatId,
                formatName,
                templateAccountDataId,
                configAccountDataId,
                mode ?: ExportMode.COST,
            ).thenReturn(true)
        }

    @PutMapping("/export-api")
    fun addAccountDataSpecificApiExport(
        @AuthenticationPrincipal jwt: Jwt?,
        @RequestParam accountId: String,
        @RequestParam formatId: String,
        @RequestParam formatName: ExportFormat,
        @RequestBody apiConfigDto: ExportApiConfigDto,
        @RequestParam mode: ExportMode?,
    ): Mono<Boolean> =
        accessCheckProvider.adminOnly(jwt) {
            accountDataService.addApiExportMapping(accountId, formatId, formatName, apiConfigDto, mode ?: ExportMode.COST)
                .thenReturn(true)
        }
}
