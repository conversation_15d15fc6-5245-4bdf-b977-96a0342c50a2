package com.tset.core.api.calculation

import com.nu.bom.core.exception.InternalServerException
import com.nu.bom.core.exception.readable.ErrorCode
import com.nu.bom.core.manufacturing.factories.FieldFactoryService
import com.nu.bom.core.manufacturing.service.CalculationService
import com.nu.bom.core.manufacturing.service.ConfigurationManagementService
import com.nu.bom.core.service.BomNodeService
import com.nu.bom.core.service.UnitConversionService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.ExtensionManager
import com.tset.core.api.calculation.dto.CalculationPosition.ROOT
import com.tset.core.api.calculation.dto.CalculationPosition.SUB
import com.tset.core.api.calculation.dto.CalculationUpdateAction
import com.tset.core.api.calculation.dto.CalculationUpdatePayloadDto
import com.tset.core.api.calculation.dto.CalculationUpdateRequestDto
import com.tset.core.api.calculation.dto.CalculationUpdateResponseDto
import com.tset.core.service.domain.calculation.CalculationType.ACCOUNT_SPECIFIC_IMPORT
import com.tset.core.service.domain.calculation.CalculationType.DETAILED_CALCULATION
import com.tset.core.service.domain.calculation.CalculationType.EXCEL_FILE_IMPORT
import com.tset.core.service.domain.calculation.CalculationType.MANUAL_CALCULATION
import com.tset.core.service.domain.calculation.CalculationType.ROUGH_CALCULATION
import com.tset.core.service.domain.calculation.CalculationType.TSET_FILE_IMPORT
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono

@Service
class CalculationUpdateModalService(
    private val bomNodeService: BomNodeService,
    private val entityManager: EntityManager,
    private val extensionManager: ExtensionManager,
    private val unitConversionService: UnitConversionService,
    private val fieldFactoryService: FieldFactoryService,
    private val configurationService: ConfigurationManagementService,
    private val calculationService: CalculationService,
) {
    fun process(
        accessCheck: AccessCheck,
        projectId: String,
        dispatchableRequest: CalculationUpdateRequestDto,
    ): Mono<CalculationUpdateResponseDto> {
        val services =
            ModalProcessorServices(
                entityManager,
                unitConversionService,
                fieldFactoryService,
                bomNodeService,
                configurationService,
                calculationService,
            )
        val isCo2ExtensionEnabled = extensionManager.isCO2Enabled()
        val processor =
            when (
                Pair(dispatchableRequest.payload.selectedType, dispatchableRequest.payload.input.position)
            ) {
                Pair(DETAILED_CALCULATION, ROOT) -> {
                    CreateRootWithCostModuleProcessor(
                        accessCheck,
                        projectId,
                        dispatchableRequest.payload,
                        CalculationUpdateAction.SAVE_AND_START_WIZARD,
                        services,
                        isCo2ExtensionEnabled,
                    )
                }

                Pair(MANUAL_CALCULATION, ROOT) ->
                    CreateRootWithManualCostModuleProcessor(
                        accessCheck,
                        projectId,
                        dispatchableRequest.payload,
                        services,
                        isCo2ExtensionEnabled,
                    )

                Pair(ROUGH_CALCULATION, ROOT) ->
                    CreateRootNoCostModuleProcessor(
                        accessCheck,
                        projectId,
                        dispatchableRequest.payload,
                        services,
                        isCo2ExtensionEnabled,
                    )

                Pair(TSET_FILE_IMPORT, ROOT) ->
                    CreateRootNoCostModuleImportProcessor(
                        accessCheck,
                        projectId,
                        dispatchableRequest.payload,
                        services,
                        isCo2ExtensionEnabled,
                    )

                Pair(ACCOUNT_SPECIFIC_IMPORT, ROOT) ->
                    CreateRootAccountSpecificImportProcessor(
                        accessCheck,
                        projectId,
                        dispatchableRequest.payload,
                        services,
                        isCo2ExtensionEnabled,
                    )

                Pair(EXCEL_FILE_IMPORT, ROOT) ->
                    CreateRootNoCostModuleExcelImportProcessor(
                        accessCheck,
                        projectId,
                        dispatchableRequest.payload,
                        services,
                        isCo2ExtensionEnabled,
                    )

                Pair(EXCEL_FILE_IMPORT, SUB) ->
                    CreateSubNoCostModuleExcelImportProcessor(
                        accessCheck,
                        projectId,
                        dispatchableRequest.payload,
                        services = services,
                        isCo2ExtensionEnabled = isCo2ExtensionEnabled,
                    )

                Pair(DETAILED_CALCULATION, SUB) ->
                    CreateSubWithCostModuleProcessor(
                        accessCheck,
                        projectId,
                        dispatchableRequest.payload,
                        CalculationUpdateAction.SAVE_AND_START_WIZARD,
                        services,
                        isCo2ExtensionEnabled,
                    )

                Pair(MANUAL_CALCULATION, SUB) ->
                    CreateSubWithManualCostModuleProcessor(
                        accessCheck,
                        projectId,
                        dispatchableRequest.payload,
                        services,
                        isCo2ExtensionEnabled,
                    )

                Pair(ROUGH_CALCULATION, SUB) ->
                    CreateSubNoCostModuleProcessor(
                        accessCheck,
                        projectId,
                        dispatchableRequest.payload,
                        services,
                        isCo2ExtensionEnabled,
                    )

                Pair(TSET_FILE_IMPORT, SUB) ->
                    CreateSubImportModuleProcessor(
                        accessCheck,
                        projectId,
                        dispatchableRequest.payload,
                        services,
                        isCo2ExtensionEnabled,
                    )

                else -> throw InternalServerException(ErrorCode.NON_EXHAUSTIVE_MATCH_ERROR)
            }
        return processor.process() // ignore command for now
    }

    fun createRootWithCostModuleProcessor(
        accessCheck: AccessCheck,
        projectId: String,
        calculationUpdatePayload: CalculationUpdatePayloadDto,
        saveAndStartWizard: CalculationUpdateAction,
    ) = CreateRootWithCostModuleProcessor(
        accessCheck,
        projectId,
        calculationUpdatePayload,
        saveAndStartWizard,
        ModalProcessorServices(
            entityManager,
            unitConversionService,
            fieldFactoryService,
            bomNodeService,
            configurationService,
            calculationService,
        ),
        extensionManager.isCO2Enabled(),
    )

    fun createSubWithCostModuleProcessor(
        accessCheck: AccessCheck,
        projectId: String,
        calculationUpdatePayload: CalculationUpdatePayloadDto,
        saveAndStartWizard: CalculationUpdateAction,
    ) = CreateSubWithCostModuleProcessor(
        accessCheck,
        projectId,
        calculationUpdatePayload,
        saveAndStartWizard,
        ModalProcessorServices(
            entityManager,
            unitConversionService,
            fieldFactoryService,
            bomNodeService,
            configurationService,
            calculationService,
        ),
        extensionManager.isCO2Enabled(),
    )
}
