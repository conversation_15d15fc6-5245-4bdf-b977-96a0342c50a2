package com.tset.core.module.messaging.json

import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.nu.bomrads.enumeration.BomNodeStatus
import com.tset.core.service.Message
import com.tset.core.service.event.BomNodeCreatedEvent
import com.tset.core.service.event.BomNodeDeletedEvent
import com.tset.core.service.event.BomNodeSnapshotAccessedEvent
import com.tset.core.service.event.BomNodeSnapshotUpdatedEvent
import com.tset.core.service.event.BomNodeStatusUpdatedEvent
import com.tset.core.service.event.BomNodeUpdatedEvent
import com.tset.core.service.event.Event
import com.tset.core.service.event.FolderDeletedEvent
import com.tset.core.service.event.FolderUpdatedEvent
import com.tset.core.service.event.GlobalBranchEvent
import com.tset.core.service.event.MessageObjectFactory
import com.tset.core.service.event.ProjectAccessedEvent
import com.tset.core.service.event.ProjectCreatedEvent
import com.tset.core.service.event.ProjectDeletedEvent
import com.tset.core.service.event.ProjectUpdatedEvent
import com.tset.core.service.event.RootBomNodeDeletedEvent
import com.tset.core.service.event.RootBomNodeUpdatedEvent
import com.tset.core.service.event.WorkspaceDeletedEvent
import com.tset.core.service.event.WorkspaceUpdatedEvent
import java.util.Date

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY)
sealed interface MessageObject<out T : Event> : Message {
    fun toEvent(): T
}

data class FolderDeletedEventMessage(
    val accountId: String,
    val accountName: String,
    val environment: String?,
    val folderId: String,
) : MessageObject<FolderDeletedEvent> {
    override fun toEvent(): FolderDeletedEvent = FolderDeletedEvent(accountId, accountName, environment, folderId)
}

data class FolderUpdatedEventMessage(
    val accountId: String,
    val accountName: String,
    val environment: String?,
    val folderId: String,
) : MessageObject<FolderUpdatedEvent> {
    override fun toEvent(): FolderUpdatedEvent = FolderUpdatedEvent(accountId, accountName, environment, folderId)
}

data class WorkspaceUpdatedEventMessage(
    val accountId: String,
    val accountName: String,
    val environment: String?,
    val workspaceId: String,
) : MessageObject<WorkspaceUpdatedEvent> {
    override fun toEvent(): WorkspaceUpdatedEvent = WorkspaceUpdatedEvent(accountId, accountName, environment, workspaceId)
}

data class WorkspaceDeletedEventMessage(
    val accountId: String,
    val accountName: String,
    val environment: String?,
    val workspaceId: String,
) : MessageObject<WorkspaceDeletedEvent> {
    override fun toEvent(): WorkspaceDeletedEvent = WorkspaceDeletedEvent(accountId, accountName, environment, workspaceId)
}

data class ProjectCreatedEventMessage(
    val accountId: String,
    val accountName: String,
    val environment: String?,
    val projectId: String,
) : MessageObject<ProjectCreatedEvent> {
    override fun toEvent(): ProjectCreatedEvent = ProjectCreatedEvent(accountId, accountName, environment, projectId)
}

data class GlobalBranchEventMessage(
    val accountId: String,
    val accountName: String,
    val environment: String?,
    val projectId: String,
    val branchId: String,
    val deleted: Boolean,
) : MessageObject<GlobalBranchEvent> {
    override fun toEvent(): GlobalBranchEvent = GlobalBranchEvent(accountId, accountName, environment, projectId, branchId, deleted)
}

data class ProjectAccessedEventMessage(
    val accountId: String,
    val accountName: String,
    val environment: String?,
    val projectId: String,
) : MessageObject<ProjectAccessedEvent> {
    override fun toEvent(): ProjectAccessedEvent = ProjectAccessedEvent(accountId, accountName, environment, projectId)
}

data class ProjectUpdatedEventMessage(
    val accountId: String,
    val accountName: String,
    val environment: String?,
    val projectId: String,
) : MessageObject<ProjectUpdatedEvent> {
    override fun toEvent(): ProjectUpdatedEvent = ProjectUpdatedEvent(accountId, accountName, environment, projectId)
}

data class ProjectDeletedEventMessage(
    val accountId: String,
    val accountName: String,
    val environment: String?,
    val projectId: String,
) : MessageObject<ProjectDeletedEvent> {
    override fun toEvent(): ProjectDeletedEvent = ProjectDeletedEvent(accountId, accountName, environment, projectId)
}

data class BomNodeSnapshotUpdatedEventMessage(
    val accountId: String,
    val accountName: String,
    val environment: String?,
    val projectId: String,
    val bomNodeId: String,
    val branchId: String,
    val userId: String,
    val accessedDate: Date,
) : MessageObject<BomNodeSnapshotUpdatedEvent> {
    override fun toEvent(): BomNodeSnapshotUpdatedEvent =
        BomNodeSnapshotUpdatedEvent(accountId, accountName, environment, projectId, bomNodeId, branchId, userId, accessedDate)
}

data class BomNodeStatusUpdatedEventMessage(
    val accountId: String,
    val accountName: String,
    val environment: String?,
    val projectId: String,
    val bomNodeId: String,
    val bomNodeStatus: String,
) : MessageObject<BomNodeStatusUpdatedEvent> {
    override fun toEvent(): BomNodeStatusUpdatedEvent =
        BomNodeStatusUpdatedEvent(accountId, accountName, environment, projectId, bomNodeId, BomNodeStatus.valueOf(bomNodeStatus))
}

data class BomNodeCreatedEventMessage(
    val accountId: String,
    val accountName: String,
    val environment: String?,
    val projectId: String,
    val bomNodeId: String,
) : MessageObject<BomNodeCreatedEvent> {
    override fun toEvent(): BomNodeCreatedEvent = BomNodeCreatedEvent(accountId, accountName, environment, projectId, bomNodeId)
}

data class RootBomNodeUpdatedEventMessage(
    val accountId: String,
    val accountName: String,
    val environment: String?,
    val projectId: String,
    val bomNodeId: String,
) : MessageObject<RootBomNodeUpdatedEvent> {
    override fun toEvent(): RootBomNodeUpdatedEvent = RootBomNodeUpdatedEvent(accountId, accountName, environment, projectId, bomNodeId)
}

data class RootBomNodeDeletedEventMessage(
    val accountId: String,
    val accountName: String,
    val environment: String?,
    val projectId: String,
    val bomNodeId: String,
) : MessageObject<RootBomNodeDeletedEvent> {
    override fun toEvent(): RootBomNodeDeletedEvent = RootBomNodeDeletedEvent(accountId, accountName, environment, projectId, bomNodeId)
}

data class BomNodeUpdatedEventMessage(
    val accountId: String,
    val accountName: String,
    val environment: String?,
    val projectId: String,
    val bomNodeId: String,
) : MessageObject<BomNodeUpdatedEvent> {
    override fun toEvent(): BomNodeUpdatedEvent = BomNodeUpdatedEvent(accountId, accountName, environment, projectId, bomNodeId)
}

data class BomNodeDeletedEventMessage(
    val accountId: String,
    val accountName: String,
    val environment: String?,
    val projectId: String,
    val bomNodeId: String,
) : MessageObject<BomNodeDeletedEvent> {
    override fun toEvent(): BomNodeDeletedEvent = BomNodeDeletedEvent(accountId, accountName, environment, projectId, bomNodeId)
}

data class BomNodeSnapshotAccessedEventMessage(
    val accountId: String,
    val accountName: String,
    val environment: String?,
    val projectId: String,
    val bomNodeId: String,
    val branchId: String,
    val userId: String,
    val accessedDate: Date,
) : MessageObject<BomNodeSnapshotAccessedEvent> {
    override fun toEvent(): BomNodeSnapshotAccessedEvent =
        BomNodeSnapshotAccessedEvent(accountId, accountName, environment, projectId, bomNodeId, branchId, userId, accessedDate)
}

object JsonMessageObjectFactory : MessageObjectFactory<Event, Event> {
    override fun createFrom(event: Event): MessageObject<Event> =
        when (event) {
            is ProjectCreatedEvent ->
                ProjectCreatedEventMessage(
                    accountId = event.accountId,
                    accountName = event.accountName,
                    environment = event.environment,
                    projectId = event.projectId,
                )
            is ProjectAccessedEvent ->
                ProjectAccessedEventMessage(
                    accountId = event.accountId,
                    accountName = event.accountName,
                    environment = event.environment,
                    projectId = event.projectId,
                )
            is ProjectUpdatedEvent ->
                ProjectUpdatedEventMessage(
                    accountId = event.accountId,
                    accountName = event.accountName,
                    environment = event.environment,
                    projectId = event.projectId,
                )
            is ProjectDeletedEvent ->
                ProjectDeletedEventMessage(
                    accountId = event.accountId,
                    accountName = event.accountName,
                    environment = event.environment,
                    projectId = event.projectId,
                )
            is BomNodeSnapshotUpdatedEvent ->
                BomNodeSnapshotUpdatedEventMessage(
                    accountId = event.accountId,
                    accountName = event.accountName,
                    environment = event.environment,
                    projectId = event.projectId,
                    bomNodeId = event.bomNodeId,
                    branchId = event.branchId,
                    userId = event.userId,
                    accessedDate = event.accessedDate,
                )
            is BomNodeStatusUpdatedEvent ->
                BomNodeStatusUpdatedEventMessage(
                    accountId = event.accountId,
                    accountName = event.accountName,
                    environment = event.environment,
                    projectId = event.projectId,
                    bomNodeId = event.bomNodeId,
                    bomNodeStatus = event.status.name,
                )
            is BomNodeCreatedEvent ->
                BomNodeCreatedEventMessage(
                    accountId = event.accountId,
                    accountName = event.accountName,
                    environment = event.environment,
                    projectId = event.projectId,
                    bomNodeId = event.bomNodeId,
                )
            is BomNodeDeletedEvent ->
                BomNodeDeletedEventMessage(
                    accountId = event.accountId,
                    accountName = event.accountName,
                    environment = event.environment,
                    projectId = event.projectId,
                    bomNodeId = event.bomNodeId,
                )
            is BomNodeSnapshotAccessedEvent ->
                BomNodeSnapshotAccessedEventMessage(
                    accountId = event.accountId,
                    accountName = event.accountName,
                    environment = event.environment,
                    projectId = event.projectId,
                    bomNodeId = event.bomNodeId,
                    branchId = event.branchId,
                    userId = event.userId,
                    accessedDate = event.accessedDate,
                )
            is BomNodeUpdatedEvent ->
                BomNodeUpdatedEventMessage(
                    accountId = event.accountId,
                    accountName = event.accountName,
                    environment = event.environment,
                    projectId = event.projectId,
                    bomNodeId = event.bomNodeId,
                )
            is GlobalBranchEvent ->
                GlobalBranchEventMessage(
                    accountId = event.accountId,
                    accountName = event.accountName,
                    environment = event.environment,
                    projectId = event.projectId,
                    branchId = event.branchId,
                    deleted = event.deleted,
                )
            is FolderDeletedEvent ->
                FolderDeletedEventMessage(
                    accountId = event.accountId,
                    accountName = event.accountName,
                    environment = event.environment,
                    folderId = event.folderId,
                )
            is RootBomNodeUpdatedEvent ->
                RootBomNodeUpdatedEventMessage(
                    accountId = event.accountId,
                    accountName = event.accountName,
                    environment = event.environment,
                    projectId = event.projectId,
                    bomNodeId = event.bomNodeId,
                )
            is RootBomNodeDeletedEvent ->
                RootBomNodeDeletedEventMessage(
                    accountId = event.accountId,
                    accountName = event.accountName,
                    environment = event.environment,
                    projectId = event.projectId,
                    bomNodeId = event.bomNodeId,
                )
            is FolderUpdatedEvent ->
                FolderUpdatedEventMessage(
                    accountId = event.accountId,
                    accountName = event.accountName,
                    environment = event.environment,
                    folderId = event.folderId,
                )
            is WorkspaceDeletedEvent ->
                WorkspaceDeletedEventMessage(
                    accountId = event.accountId,
                    accountName = event.accountName,
                    environment = event.environment,
                    workspaceId = event.workspaceId,
                )
            is WorkspaceUpdatedEvent ->
                WorkspaceUpdatedEventMessage(
                    accountId = event.accountId,
                    accountName = event.accountName,
                    environment = event.environment,
                    workspaceId = event.workspaceId,
                )
            else -> TODO("code should be unreachable")
        }
}
