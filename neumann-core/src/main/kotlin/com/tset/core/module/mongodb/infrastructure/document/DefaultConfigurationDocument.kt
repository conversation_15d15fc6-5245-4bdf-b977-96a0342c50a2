package com.tset.core.module.mongodb.infrastructure.document

import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer
import org.bson.types.ObjectId
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document

@Document(collection = DefaultConfigurationDocument.COLLECTION_NAME)
data class DefaultConfigurationDocument(
    val accountId: ObjectId,
    val groupKey: String,
    val type: String,
    val activeConfigurationId: ObjectId,
) {
    @Id
    @JsonSerialize(using = ToStringSerializer::class)
    @Suppress("ktlint:standard:backing-property-naming", "PropertyName")
    var _id: ObjectId? = null

    companion object {
        const val COLLECTION_NAME = "DefaultConfiguration"

        fun fromActive(activeDoc: ActiveConfigurationDocument): DefaultConfigurationDocument =
            DefaultConfigurationDocument(
                accountId = activeDoc.accountIdUser,
                groupKey = activeDoc.groupKey,
                type = activeDoc.type,
                activeConfigurationId = activeDoc._id!!,
            )
    }
}
