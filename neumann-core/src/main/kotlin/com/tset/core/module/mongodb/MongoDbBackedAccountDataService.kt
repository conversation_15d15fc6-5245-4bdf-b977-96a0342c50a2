package com.tset.core.module.mongodb

import com.nu.bom.core.exception.InternalServerException
import com.nu.bom.core.exception.readable.ErrorCode
import com.nu.bom.core.model.Account
import com.nu.bom.core.repository.AccountDataRepository
import com.nu.bom.core.service.AccountService
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.tset.core.module.export.dto.ExportApiConfigDto
import com.tset.core.module.export.dto.ExportMode
import com.tset.core.module.mongodb.infrastructure.document.AccountDataDocument
import com.tset.core.module.mongodb.infrastructure.dto.AccountDataEncodingType
import com.tset.core.module.mongodb.infrastructure.dto.AccountDataHeaderDto
import com.tset.core.service.account.AccountData
import com.tset.core.service.account.AccountDataMappings
import com.tset.core.service.account.AccountDataService
import com.tset.core.service.account.AccountDataType
import com.tset.core.service.account.AccountExportFormatTypes
import com.tset.core.service.export.AccountSpecificApiExportFormatRequirement
import com.tset.core.service.export.AccountSpecificXlsxExportFormatRequirement
import com.tset.core.service.export.ExportFormat
import org.bson.types.ObjectId
import org.springframework.data.mongodb.core.ReactiveMongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.Update
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import java.util.Base64

@Service
class MongoDbBackedAccountDataService(
    private val accountDataRepository: AccountDataRepository,
    private val accountService: AccountService,
    private val reactiveMongoTemplate: ReactiveMongoTemplate,
) : AccountDataService {
    override fun updateAccountLabel(
        accountId: String,
        label: String,
    ): Mono<Void> =
        reactiveMongoTemplate.updateFirst(
            Query(Criteria.where("_id").`is`(accountId)),
            Update().set("label", label),
            Account::class.java,
        ).then()

    override fun getDataById(dataId: String): Mono<ByteArray> {
        return accountDataRepository.findById(ObjectId(dataId)).map { accountData ->
            when (accountData.type) {
                AccountDataEncodingType.RAW -> accountData.data.toByteArray()
                AccountDataEncodingType.BASE64 -> Base64.getDecoder().decode(accountData.data.toByteArray())
            }
        }
    }

    override fun upload(
        accountId: String,
        encoding: AccountDataEncodingType,
        fileName: String,
        data: ByteArray,
    ): Mono<String> {
        val document =
            when (encoding) {
                AccountDataEncodingType.RAW -> AccountDataDocument(accountId, encoding, fileName, data.decodeToString())
                AccountDataEncodingType.BASE64 ->
                    AccountDataDocument(
                        accountId,
                        encoding,
                        fileName,
                        Base64.getEncoder().encode(data).decodeToString(),
                    )
            }
        return accountDataRepository.save(document).map { savedDocument ->
            savedDocument._id!!.toHexString()
        }
    }

    override fun getDataHeadersForAccount(accountId: String): Mono<List<AccountDataHeaderDto>> {
        return accountDataRepository.findByAccountId(accountId).map { accountDataDocument ->
            AccountDataHeaderDto(
                id = accountDataDocument._id!!.toHexString(),
                fileName = accountDataDocument.fileName,
                encodingType = accountDataDocument.type,
                createdBy = accountDataDocument.createdBy,
                createdDate = accountDataDocument.createdDate,
            )
        }.collectList()
    }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    override fun delete(accountId: String): Mono<Void> {
        return accountService.getOne(accountId).flatMap { account ->
            account.accountData = AccountDataMappings(emptyMap())
            accountService.update(account).flatMap {
                accountDataRepository.findByAccountId(accountId).flatMap {
                    accountDataRepository.deleteById(it._id!!)
                }.then()
            }
        }
    }

    data class RequiredDataIds(
        val templateAccountDataId: String? = null,
        val configAccountDataId: String? = null,
    )

    override fun addFileExportMapping(
        accountId: String,
        formatId: String,
        formatName: ExportFormat,
        templateAccountDataId: String,
        configAccountDataId: String,
        exportMode: ExportMode,
    ): Mono<Void> {
        return accountDataRepository.findByAccountId(accountId).collectList().flatMap { dataItems ->
            val foundDataItems =
                dataItems.fold(RequiredDataIds()) { acc, dataItem ->
                    val currentId = dataItem._id!!.toHexString()
                    when {
                        currentId == templateAccountDataId && dataItem.accountId == accountId ->
                            acc.copy(
                                templateAccountDataId = currentId,
                            )
                        currentId == configAccountDataId && dataItem.accountId == accountId -> acc.copy(configAccountDataId = currentId)
                        else -> acc
                    }
                }
            if (foundDataItems.configAccountDataId != null && foundDataItems.templateAccountDataId != null) {
                accountService.getOne(accountId).flatMap { account ->
                    val updatedAccount =
                        setAccountDataFormats(
                            account,
                            formatId,
                            formatName,
                            templateAccountDataId,
                            configAccountDataId,
                            exportMode,
                        )
                    accountService.update(updatedAccount).then()
                }
            } else {
                Mono.error(InternalServerException(ErrorCode.DATA_ITEM_NOT_FOUND))
            }
        }
    }

    override fun addApiExportMapping(
        accountId: String,
        formatId: String,
        formatName: ExportFormat,
        exportApiConfigDto: ExportApiConfigDto,
        exportMode: ExportMode,
    ): Mono<Void> {
        val format =
            AccountSpecificApiExportFormatRequirement(
                formatId = formatId,
                formatName = formatName,
                exportApiConfigDto = exportApiConfigDto,
                exportMode = exportMode,
            )
        return accountDataRepository.findByAccountId(accountId).collectList().flatMap {
            accountService.getOne(accountId).flatMap { account ->
                val updatedAccount = setAccountDataExportApi(account, format)
                accountService.update(updatedAccount).then()
            }
        }
    }

    private fun setAccountDataExportApi(
        account: Account,
        exportApiConfigDto: AccountSpecificApiExportFormatRequirement,
    ): Account {
        upsertAccountData(
            account,
            mapOf(
                AccountDataType.EXPORT_FORMATS to
                    AccountExportFormatTypes(
                        formats =
                            mapOf(
                                exportApiConfigDto.formatId to exportApiConfigDto,
                            ),
                    ),
            ),
        )
        return account
    }

    private fun setAccountDataFormats(
        account: Account,
        formatId: String,
        formatName: ExportFormat,
        templateAccountDataId: String,
        configAccountDataId: String,
        exportMode: ExportMode,
    ): Account {
        val accountExportFormatRequirement =
            AccountSpecificXlsxExportFormatRequirement(
                formatId,
                formatName,
                templateAccountDataId,
                configAccountDataId,
                exportMode,
            )
        upsertAccountData(
            account,
            mapOf(
                AccountDataType.EXPORT_FORMATS to
                    AccountExportFormatTypes(
                        formats =
                            mapOf(
                                formatId to accountExportFormatRequirement,
                            ),
                    ),
            ),
        )
        return account
    }

    private fun upsertAccountData(
        account: Account,
        newData: Map<AccountDataType, AccountData>,
    ) {
        account.accountData = account.accountData?.let {
            account.accountData!!.copy(
                mappings = account.accountData!!.mappings.plus(newData),
            )
        } ?: AccountDataMappings(newData)
    }
}
