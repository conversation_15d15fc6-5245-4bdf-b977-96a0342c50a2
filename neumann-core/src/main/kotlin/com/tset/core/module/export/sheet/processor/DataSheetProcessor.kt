package com.tset.core.module.export.sheet.processor

import com.nu.bom.core.api.dtos.FieldConversionService
import com.tset.core.module.export.BomNodeEnvironment
import com.tset.core.module.export.ExportServiceModule
import com.tset.core.module.export.dto.ExportTemplateWithConfig
import com.tset.core.module.export.sheet.CbdSheetProcessor
import com.tset.core.service.export.ExportCurrency
import com.tset.core.service.export.ExportFormatRequirement
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import reactor.core.publisher.Mono

class DataSheetProcessor : CbdSheetProcessor() {
    companion object {
        val logger: Logger = LoggerFactory.getLogger(DataSheetProcessor::class.java)
    }

    override fun process(
        sheetName: String,
        exportTemplateWithConfig: ExportTemplateWithConfig,
        snapshotEnvironment: BomNodeEnvironment,
        fieldConversionService: FieldConversionService,
        exportServiceModule: ExportServiceModule,
        format: ExportFormatRequirement,
        currency: ExportCurrency,
    ): Mono<ExportTemplateWithConfig> {
        logger.info("...export processing $sheetName sheet (images)")
        val sheet = exportTemplateWithConfig.template.getSheet(sheetName)
        for (row in sheet) {
            for (cell in row) {
                cell.cellStyle.dataFormat =
                    exportTemplateWithConfig.template.creationHelper
                        .createDataFormat()
                        .getFormat(snapshotEnvironment.getCostlikeFormat())
            }
        }
        return Mono.just(exportTemplateWithConfig)
    }
}
