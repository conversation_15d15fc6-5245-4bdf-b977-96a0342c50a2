package com.tset.core.module.mongodb.infrastructure.dto

import com.fasterxml.jackson.annotation.JsonValue
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.nu.bom.core.exception.InternalServerException
import com.nu.bom.core.exception.readable.ErrorCode
import com.tset.core.service.domain.EntityGroupType

@JsonSerialize
data class CounterGroupDto(@JsonValue val counterGroupType: String) {

    companion object {
        private val TSET_MASTERDATA_COUNTER_GROUP = "T"
        private val ACCOUNT_MASTERDATA_COUNTER_GROUP = "M"
        private val ANALYTICS_COUNTER_GROUP = "A"
        private val PROJECT_COUNTER_GROUP = "P"

        fun fromEntityGroupType(entityGroupType: EntityGroupType): CounterGroupDto =
            when (entityGroupType) {
                EntityGroupType.TSET_MASTERDATA -> CounterGroupDto(TSET_MASTERDATA_COUNTER_GROUP)
                EntityGroupType.ACCOUNT_MASTERDATA -> CounterGroupDto(ACCOUNT_MASTERDATA_COUNTER_GROUP)
                EntityGroupType.ANALYTICS -> CounterGroupDto(ANALYTICS_COUNTER_GROUP)
                EntityGroupType.PROJECT -> CounterGroupDto(PROJECT_COUNTER_GROUP)
                else -> throw InternalServerException(
                    ErrorCode.COUNTER_NOT_AVAILABLE,
                    "EntityGroupType $entityGroupType has no counter configured"
                )
            }

        fun toEntityGroupType(dto: CounterGroupDto): EntityGroupType =
            when (dto.counterGroupType) {
                TSET_MASTERDATA_COUNTER_GROUP -> EntityGroupType.TSET_MASTERDATA
                ACCOUNT_MASTERDATA_COUNTER_GROUP -> EntityGroupType.ACCOUNT_MASTERDATA
                ANALYTICS_COUNTER_GROUP -> EntityGroupType.ANALYTICS
                PROJECT_COUNTER_GROUP -> EntityGroupType.PROJECT
                else -> throw InternalServerException(
                    ErrorCode.COUNTER_GROUP_PARSE_EXCEPTION,
                    "Cannot parse ${dto.counterGroupType} to EntityGroup"
                )
            }
    }
}
