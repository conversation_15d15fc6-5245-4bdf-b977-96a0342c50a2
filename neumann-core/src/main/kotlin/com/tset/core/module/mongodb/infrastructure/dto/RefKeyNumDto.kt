package com.tset.core.module.mongodb.infrastructure.dto

import org.bson.Document
import org.springframework.core.convert.converter.Converter

data class RefKeyNumDto(
    val refKey_num: Long
) {
    companion object {
        class RefKeyNumDtoReadConverter : Converter<Document, RefKeyNumDto> {
            override fun convert(source: Document): RefKeyNumDto? =
                RefKeyNumDto(source.get("refKey", Document::class.java).get("num", java.lang.Long::class.java).toLong())
        }
    }
}
