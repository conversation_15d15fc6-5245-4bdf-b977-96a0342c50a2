package com.tset.core.module.export.sheet.processor

import com.nu.bom.core.api.dtos.FieldConversionService
import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.nu.bom.core.utils.exceptionOnEmpty
import com.tset.core.module.bom.exchangerates.ExchangeRateMap
import com.tset.core.module.export.BomNodeEnvironment
import com.tset.core.module.export.ExportServiceModule
import com.tset.core.module.export.dto.ExportTemplateWithConfig
import com.tset.core.module.export.sheet.BomSourcer
import com.tset.core.module.export.sheet.CbdSheetProcessor
import com.tset.core.module.export.sheet.DateFieldResult
import com.tset.core.module.export.sheet.FieldValueType
import com.tset.core.module.export.sheet.FullNumericFieldResult
import com.tset.core.module.export.sheet.MoneyFieldResult
import com.tset.core.module.export.sheet.NumberFieldResult
import com.tset.core.module.export.sheet.TextFieldResult
import com.tset.core.module.export.sheet.getDenominatorString
import com.tset.core.service.export.ExportCurrency
import com.tset.core.service.export.ExportFormatRequirement
import org.apache.poi.ss.usermodel.Cell
import org.apache.poi.ss.usermodel.DataFormatter
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toFlux
import reactor.kotlin.core.publisher.toMono

class SummarySheetProcessorSimple : CbdSheetProcessor() {
    companion object {
        val logger: Logger = LoggerFactory.getLogger(SummarySheetProcessorSimple::class.java)
        val processorPrefixes: List<String> = listOf("@_")
    }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    override fun process(
        sheetName: String,
        exportTemplateWithConfig: ExportTemplateWithConfig,
        snapshotEnvironment: BomNodeEnvironment,
        fieldConversionService: FieldConversionService,
        exportServiceModule: ExportServiceModule,
        format: ExportFormatRequirement,
        currency: ExportCurrency,
    ): Mono<ExportTemplateWithConfig> {
        logger.info("...export processing $sheetName sheet (simple fields)")

        val dataFormatter = DataFormatter()
        val sheet = exportTemplateWithConfig.template.getSheet(sheetName)
        return sheet
            .toFlux()
            .flatMap { row -> row.toFlux() }
            .flatMap { cell ->
                val cellText: String = dataFormatter.formatCellValue(cell)
                exportTemplateWithConfig.config.fieldMap[cellText]
                    .takeIf { processorPrefixes.any { cellText.startsWith(it) } }
                    ?.let(snapshotEnvironment.fieldSourcers.sourcers::get)
                    .toMono()
                    .flatMap { sourcer ->
                        when (sourcer.fieldValueType) {
                            FieldValueType.TEXT,
                            FieldValueType.TRANSLATED_TEXT,
                            ->
                                updateTextCell(cellText, cell, sourcer, snapshotEnvironment)
                                    ?.let(::TextFieldResult)
                                    .toMono()

                            FieldValueType.NUMBER ->
                                updateCellWithNumber(
                                    snapshotEnvironment,
                                    sourcer,
                                    cell,
                                    exportTemplateWithConfig,
                                    fieldConversionService,
                                )

                            FieldValueType.DATE ->
                                updateDateCell(cell, sourcer, snapshotEnvironment.snapshot)
                                    ?.let(::DateFieldResult)
                                    .toMono()

                            FieldValueType.MONEY -> {
                                val sourcedMoney =
                                    updateNumberCell(
                                        cell,
                                        sourcer,
                                        snapshotEnvironment.snapshot,
                                        snapshotEnvironment.exchangeRateMap,
                                        snapshotEnvironment.getCurrency(),
                                    )
                                val denominator =
                                    snapshotEnvironment.snapshot.manufacturing
                                        ?.getFieldResult("costUnit")
                                        ?.res as? String

                                cell.cellStyle.dataFormat =
                                    exportTemplateWithConfig.template.creationHelper
                                        .createDataFormat()
                                        .getFormat(
                                            snapshotEnvironment.getCostlikeFormat() + "\"${
                                                getDenominatorString(
                                                    denominator,
                                                    snapshotEnvironment.translationService,
                                                )
                                            }\"",
                                        )
                                sourcedMoney?.let { MoneyFieldResult(it) }.toMono()
                            }

                            else -> Mono.empty()
                        }
                    }.map { fieldResult ->
                        exportTemplateWithConfig.fieldResults[cellText] = fieldResult
                    }
            }.last()
            .then(exportTemplateWithConfig.toMono())
    }

    private fun updateCellWithNumber(
        bomEnv: BomNodeEnvironment,
        sourcer: BomSourcer,
        cell: Cell,
        exportTemplateWithConfig: ExportTemplateWithConfig,
        fieldConversionService: FieldConversionService,
    ): Mono<NumberFieldResult> {
        // I would be lying if I would say that I am proud of this.
        val fieldResult = bomEnv.snapshot.manufacturing?.getFieldResult(sourcer.fieldName)
        return if (fieldResult is Emission) {
            fieldConversionService
                .getFieldAsFieldParameter(
                    bomEnv.accessCheck,
                    bomEnv.snapshot.manufacturing,
                    sourcer.fieldName,
                    ExchangeRateMap.empty(),
                ).exceptionOnEmpty(
                    IllegalStateException("Expected ${sourcer.fieldName} to be present"),
                ).map { fieldParameter ->
                    val exportFieldResult = FullNumericFieldResult.create(bomEnv, fieldResult, fieldParameter)
                    cell.cellStyle.dataFormat =
                        exportTemplateWithConfig.template.creationHelper
                            .createDataFormat()
                            .getFormat(exportFieldResult.getFormat(bomEnv.translationService))

                    updateNumberCellWithValue(cell, exportFieldResult.res)
                    NumberFieldResult(exportFieldResult.res)
                }
        } else {
            updateNumberCell(cell, sourcer, bomEnv.snapshot)
                ?.let { sourcedNumber ->
                    NumberFieldResult(sourcedNumber)
                }.toMono()
        }
    }
}
