package com.tset.core.module.export.sheet.processor

import com.nu.bom.core.api.dtos.FieldConversionService
import com.tset.core.module.export.BomNodeEnvironment
import com.tset.core.module.export.ExportServiceModule
import com.tset.core.module.export.dto.ExportTemplateWithConfig
import com.tset.core.module.export.sheet.AggregatedBomSourcer
import com.tset.core.module.export.sheet.CbdSheetProcessor
import com.tset.core.module.export.sheet.SourcerUtils
import com.tset.core.service.domain.Currency
import com.tset.core.service.export.ExportCurrency
import com.tset.core.service.export.ExportFormatRequirement
import org.apache.poi.ss.usermodel.DataFormatter
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import reactor.core.publisher.Mono
import java.math.BigDecimal

class OverheadsSheetProcessor : CbdSheetProcessor() {
    companion object {
        val logger: Logger = LoggerFactory.getLogger(OverheadsSheetProcessor::class.java)
        private const val VALUE_PREFIX = "@_"
        private const val PERCENTAGE_VALUE_PREFIX = "%_"
        val processorPrefixes: List<String> = listOf(VALUE_PREFIX, PERCENTAGE_VALUE_PREFIX)
    }

    override fun process(
        sheetName: String,
        exportTemplateWithConfig: ExportTemplateWithConfig,
        snapshotEnvironment: BomNodeEnvironment,
        fieldConversionService: FieldConversionService,
        exportServiceModule: ExportServiceModule,
        format: ExportFormatRequirement,
        currency: ExportCurrency,
    ): Mono<ExportTemplateWithConfig> {
        logger.info("...export processing $sheetName sheet")
        val totalValue =
            AggregatedBomSourcer
                .computeAggregateValue(
                    snapshotEnvironment.entityManager,
                    snapshotEnvironment.snapshot,
                    exportTemplateWithConfig.config.cbd.totalValue,
                ).sum
        logger.info("   -> total costs = $totalValue")
        val dataFormatter = DataFormatter()
        val sheet = exportTemplateWithConfig.template.getSheet(sheetName)

        for (row in sheet) {
            for (cell in row) {
                val cellText: String = dataFormatter.formatCellValue(cell)
                if (cellText.contains("@currency")) {
                    updateTextCellWithValue(cell, cellText.replace("@currency", snapshotEnvironment.getDisplayUnit()))
                }
                if (processorPrefixes.any { cellText.startsWith(it) }) {
                    val overheadsValue =
                        exportTemplateWithConfig.config.overheads.firstOrNull { overheadsValue ->
                            overheadsValue.valueKey == cellText
                        }
                    if (overheadsValue != null) {
                        updateNumberCellWithValue(
                            cell,
                            snapshotEnvironment.exchangeRateMap.ccyToCcy(
                                snapshotEnvironment.fieldSourcers.overheadsSourcer.sourceAsNumber(
                                    snapshotEnvironment.snapshot.manufacturing,
                                    overheadsValue.fieldName,
                                ),
                                Currency.EUR,
                                snapshotEnvironment.getCurrency(),
                            ),
                        )
                        cell.cellStyle.dataFormat =
                            exportTemplateWithConfig.template.creationHelper
                                .createDataFormat()
                                .getFormat(
                                    snapshotEnvironment.getCostlikeFormat(),
                                )
                    } else {
                        val overheadsPercentage =
                            exportTemplateWithConfig.config.overheads.firstOrNull { overheadsValueP ->
                                overheadsValueP.percentageKey == cellText
                            }
                        if (overheadsPercentage != null) {
                            val absoluteValue: BigDecimal? =
                                snapshotEnvironment.fieldSourcers.overheadsSourcer.sourceAsNumber(
                                    snapshotEnvironment.snapshot.manufacturing,
                                    overheadsPercentage.fieldName,
                                )
                            if (absoluteValue != null) {
                                updateNumberCellWithValue(
                                    cell,
                                    SourcerUtils.computePercentage(
                                        absoluteValue,
                                        totalValue,
                                    ),
                                )
                            } else {
                                updateTextCellWithValue(cell, "")
                            }
                        } else {
                            updateTextCellWithValue(cell, "")
                        }
                    }
                }
            }
        }

        return Mono.just(exportTemplateWithConfig)
    }
}
