package com.tset.core.module.elasticsearch.document
import com.nu.bom.core.api.dtos.FieldParameter
import java.util.Date

@Suppress("ktlint:standard:value-parameter-comment")
data class BomNodeSearchDto(
    val id: String,
    // non-searchable id fields
    val rootBomNodeId: String,
    val bomNodeSnapshotId: String,
    val bomNodeId: String,
    val branchId: String,
    val projectId: String,
    val accountId: String,
    val folderId: String?,
    val workspaceId: String?,
    val projectPath: String?,
    // searchable fields (text)
    // used for searching, may be forced to null
    val partName: String?,
    // used for frontend reporting
    val partNameValue: String?,
    val partNumber: String?,
    val material: List<String>?,
    val step: List<String>?,
    val title: String?,
    val shapeId: String?,
    val shapeIdNoPrefix: String?,
    val responsible: String?,
    val technology: String?,
    val location: String?,
    val machine: List<String>?,
    val status: String?,
    val stepConfiguration: List<String>?,
    val procurementType: String?,
    val consumable: List<String>?,
    val labor: List<String>?,
    val tool: List<String>?,
    val cpart: List<String>?,
    val projectName: String?,
    val projectKey: String?,
    val isMaster: Boolean,
    val netWeightPerPartValue: Double?,
    val costPerPartValue: Double?,
    val cO2PerPartValue: Double?,
    val avgVolumeValue: Double?,
    val peakVolumeValue: Double?,
    // stored fields, for frontend access
    val netWeightPerPart: FieldParameter?,
    val costPerPart: FieldParameter?,
    val co2PerPart: FieldParameter?,
    val avgVolume: FieldParameter?,
    val peakVolume: FieldParameter?,
    val lastModifiedDate: Date?,
    val partImage: List<String>?,
    // debug
    val lastAccessedDate: Date?,
    val lastModifiedBy: String?,
    // copy search entity fields
    val entityId: String?,
    val calculationType: String?,
    val parentId: String?,
    val generated: Boolean,
)
