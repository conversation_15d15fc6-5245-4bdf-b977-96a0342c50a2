package com.tset.core.module.elasticsearch

import com.nu.bom.core.api.CO2_PER_PART
import com.nu.bom.core.api.COST_PER_PART
import com.nu.bom.core.api.dtos.FieldConversionService
import com.nu.bom.core.exception.NoMasterAvailableException
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost.CommercialCalculationCostManufacturedMaterial
import com.nu.bom.core.manufacturing.entities.Attachment
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.UnitOverrideContext
import com.nu.bom.core.manufacturing.fieldTypes.NumericFieldResult
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.NET_WEIGHT_PER_PART_WIZARD
import com.nu.bom.core.model.SnapshotId
import com.nu.bom.core.model.toBomNodeId
import com.nu.bom.core.model.toBranchId
import com.nu.bom.core.model.toMongoBomNodeId
import com.nu.bom.core.model.toMongoFormatStr
import com.nu.bom.core.model.toMongoProjectId
import com.nu.bom.core.model.toMongoSnapshotId
import com.nu.bom.core.model.toObjectId
import com.nu.bom.core.model.toUUID
import com.nu.bom.core.service.bomnode.SnapshotLoaderService
import com.nu.bom.core.service.bomrads.BomradsBomNodeService
import com.nu.bom.core.service.bomrads.BomradsFolderService
import com.nu.bom.core.service.bomrads.BomradsProjectService
import com.nu.bom.core.service.bomrads.BomradsWorkspaceService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.user.AccessCheckProvider
import com.nu.bom.core.user.UserService
import com.nu.bom.core.user.withAccessCheck
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.nu.bom.core.utils.applyIfExists
import com.nu.bom.core.utils.subscribeWithContextCapture
import com.nu.bom.core.utils.toObjectId
import com.nu.bom.core.utils.wrapIntoMaybe
import com.nu.bomrads.dto.BranchViewDTO
import com.nu.bomrads.dto.MinimalBranchDTO
import com.nu.bomrads.dto.NodeSnapshotDTO
import com.nu.bomrads.enumeration.BomNodeStatus
import com.nu.bomrads.id.BranchId
import com.nu.bomrads.id.FolderId
import com.nu.bomrads.id.ProjectId
import com.nu.bomrads.id.WorkspaceId
import com.nu.http.EnvironmentNameSupplier
import com.tset.core.module.SearchIndexUpdaterService
import com.tset.core.module.bom.exchangerates.ExchangeRateMap
import com.tset.core.module.elasticsearch.client.ReactiveElasticSearchClient
import com.tset.core.module.elasticsearch.document.BomNodeSearchDto
import com.tset.core.module.elasticsearch.document.UserAccessDto
import com.tset.core.service.domain.calculation.CalculationType
import com.tset.core.service.event.BomNodeCreatedEvent
import com.tset.core.service.event.BomNodeDeletedEvent
import com.tset.core.service.event.BomNodeEvent
import com.tset.core.service.event.BomNodeSnapshotAccessedEvent
import com.tset.core.service.event.BomNodeSnapshotEvent
import com.tset.core.service.event.BomNodeSnapshotUpdatedEvent
import com.tset.core.service.event.BomNodeStatusUpdatedEvent
import com.tset.core.service.event.BomNodeUpdatedEvent
import com.tset.core.service.event.EnvironmentEvent
import com.tset.core.service.event.Event
import com.tset.core.service.event.EventSourcingService
import com.tset.core.service.event.FolderDeletedEvent
import com.tset.core.service.event.FolderEvent
import com.tset.core.service.event.FolderUpdatedEvent
import com.tset.core.service.event.GlobalBranchEvent
import com.tset.core.service.event.ProjectDeletedEvent
import com.tset.core.service.event.ProjectEvent
import com.tset.core.service.event.ProjectUpdatedEvent
import com.tset.core.service.event.RootBomNodeDeletedEvent
import com.tset.core.service.event.RootBomNodeUpdatedEvent
import com.tset.core.service.event.WorkspaceDeletedEvent
import com.tset.core.service.event.WorkspaceUpdatedEvent
import org.apache.commons.lang3.exception.ExceptionUtils
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.DisposableBean
import org.springframework.beans.factory.InitializingBean
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import org.springframework.transaction.NoTransactionException
import reactor.core.Disposable
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.io.Closeable
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import java.util.Date
import java.util.concurrent.ConcurrentHashMap
import com.nu.bom.core.model.BomNodeId as LegacyBomNodeId
import com.nu.bom.core.model.BranchId as LegacyBranchId

@Profile("elasticsearch")
@Service
class ElasticSearchIndexUpdaterModule(
    private val eventSourcingService: EventSourcingService,
    private val bomradProjectService: BomradsProjectService,
    private val bomradsBomNodeService: BomradsBomNodeService,
    private val bomradsWorkspaceService: BomradsWorkspaceService,
    private val bomradsFolderService: BomradsFolderService,
    private val snapshotLoaderService: SnapshotLoaderService,
    private val fieldConversionService: FieldConversionService,
    private val reactiveElasticSearchClient: ReactiveElasticSearchClient,
    private val userService: UserService,
    private val accessCheckProvider: AccessCheckProvider,
    private val environmentNameSupplier: EnvironmentNameSupplier,
) : SearchIndexUpdaterService, InitializingBean, DisposableBean, Closeable {
    companion object {
        private val logger = LoggerFactory.getLogger(ElasticSearchIndexUpdaterModule::class.java)!!
        const val TENTATIVE_BOMNODE_UPDATE_TIMEOUT_MINS = 30
        const val USERACCESS_HISTORY_LIMIT = 1000
    }

    lateinit var disposables: Disposable

    private val tentativeBomNodeUpdates: ConcurrentHashMap<String, LocalDateTime> = ConcurrentHashMap()

    data class SnapshotExtension(
        val snapshot: BomNodeSnapshot,
        val status: BomNodeStatus,
        val mainBranch: Boolean,
        val title: String,
        val parentId: String?,
    ) {
        fun bomNodeSnapshotId() = snapshot._id?.toHexString()!!
    }

    override fun afterPropertiesSet() {
        disposables =
            eventSourcingService.registerEventListener { event ->
                logger.info(" * event received: {}", event)
                /* Mono.using(
                    { tracer.startScopedSpan("ElasticSearch Update") },
                    { processMessage(event) },
                    { span -> span.finish() },
                ) */
                processMessage(event)
            }.onErrorContinue { exc, any ->
                if (exc !is NoTransactionException && exc !is NoMasterAvailableException) {
                    if (logger.isTraceEnabled) {
                        logger.error("Error happened during processing ES Index update: $any - $exc")
                    } else {
                        logger.error(
                            "Error happened during processing ES Index update: ${
                                ExceptionUtils.getStackTrace(
                                    exc,
                                )
                            }",
                        )
                    }
                }
            }.subscribeWithContextCapture()
        logger.info("Consuming events from $eventSourcingService")
    }

    override fun destroy() {
        synchronized(this) {
            if (!disposables.isDisposed) {
                disposables.dispose()
            }
        }
    }

    override fun close() {
        destroy()
    }

    // run with a minute delay
    @Scheduled(fixedDelayString = "PT1M")
    protected fun updateTentativeBomNodeUpdates() {
        val now = LocalDateTime.now()
        val bomNodesToRemove =
            tentativeBomNodeUpdates.entries.filter { entry ->
                ChronoUnit.MINUTES.between(entry.value, now) > TENTATIVE_BOMNODE_UPDATE_TIMEOUT_MINS
            }.map { entry -> entry.key }
        bomNodesToRemove.forEach { tentativeBomNodeUpdates.remove(it) }
        if (bomNodesToRemove.isNotEmpty()) {
            logger.debug(
                "Search Index Tentative BomNode Updater cleanup: removed ${bomNodesToRemove.size} nodes, " +
                    "${tentativeBomNodeUpdates.size} still blocked",
            )
        } else {
            logger.debug("Search Index Tentative BomNode Updater status: ${tentativeBomNodeUpdates.size} nodes")
        }
    }

    private fun processMessage(event: Event): Mono<Void> {
        if (event is EnvironmentEvent) {
            setEnvironmentName(event.environment)
        }
        logger.info("processing $event")
        return when (event) {
            is BomNodeSnapshotAccessedEvent ->
                // No need to update the index on BomNodeSnapshotAccessedEvent
                processBomNodeSnapshotAccessedorUpdatedEvent(event)

            is BomNodeSnapshotUpdatedEvent ->
                processBomNodeSnapshotAccessedorUpdatedEvent(event)
                    .then(processBomNodeEvent(event))
            // is BomNodeSnapshotEvent -> processBomNodeSnapshotEvent(event)
            is BomNodeEvent -> processBomNodeEvent(event)
            is ProjectUpdatedEvent -> processProjectEvent(event)
            is ProjectDeletedEvent -> processProjectDeletedEvent(event)
            is FolderDeletedEvent -> processFolderDeletedEvent(event)
            is WorkspaceDeletedEvent -> processWorkspaceDeletedEvent(event)
            is WorkspaceUpdatedEvent -> processWorkspaceUpdatedEvent(event)
            is FolderUpdatedEvent -> processFolderUpdatedEvent(event)
            is GlobalBranchEvent -> processBranchEvent(event)
            //  is ProjectEvent -> processProjectEvent(event, false)
            else -> Mono.empty()
        }.doOnError {
            logger.error("processMessage() error of $event -> ${ExceptionUtils.getStackTrace(it)}")
        }.doFirst {
            logger.info("processMessage() started of $event")
        }.doFinally {
            logger.info("processMessage() finished of $event")
        }
    }

    /**
     * From and account and project id, this function loads all the global branches, and branch views for it, and hand it over to the selector, which could filter out the changed nodes.
     */
    private fun processEventWithBomrads(
        accountId: String,
        accountName: String,
        environmentName: String?,
        projectId: ProjectId,
        branchId: BranchId?,
        selector: (List<BranchViewDTO>) -> List<Pair<MinimalBranchDTO, List<NodeSnapshotDTO>>>,
    ): Flux<BomNodeSearchDto> {
        logger.info("processEventWithBomrads $accountName, environmentName: $environmentName projectId: $projectId")
        return bomradProjectService.projectForAdmin(
            accountName = accountName,
            id = projectId,
            includePath = true,
            includeDeleted = false,
            environmentName = environmentName,
        )
            .flatMapMany { project ->
                // if branchId was specified, load just that one
                val branchViewsMono =
                    if (branchId != null) {
                        bomradsBomNodeService.getAdminBranchView(accountName, projectId, branchId, environmentName)
                            .map {
                                if (isIndexable(it)) {
                                    listOf(it)
                                } else {
                                    emptyList()
                                }
                            }
                    } else {
                        bomradsBomNodeService.getAdminGlobalBranches(accountName, projectId, environmentName)
                            .concatMap { branchStatus ->
                                if (isIndexable(branchStatus.branch)) {
                                    bomradsBomNodeService.getAdminBranchView(
                                        accountName,
                                        projectId,
                                        branchStatus.branch().id(),
                                        environmentName,
                                    )
                                } else {
                                    Mono.empty()
                                }
                            }.collectList()
                    }
                branchViewsMono.flatMapMany { branchViews ->
                    val branchWithChangedNodes = selector(branchViews)
                    processNodesFromBomrads(
                        accountId = accountId,
                        accountName = accountName,
                        ProjectDetailsDTO(
                            projectId,
                            project.name,
                            project.path,
                            project.folderId,
                            project.workspaceId,
                            project.key,
                        ),
                        branchWithChangedNodes = branchWithChangedNodes,
                        environmentName,
                    )
                }
            }
    }

    private fun isIndexable(branch: MinimalBranchDTO) = branch.main || (branch.global && !branch.published)

    private fun isIndexable(branchView: BranchViewDTO) = isIndexable(branchView.branch)

    /**
     * Based on the given branchWithChangedNodes - which contains all the nodes, which needs to be re-indexed, this function will load all the snapshots from MongoDB and reindex it.
     */
    private fun processNodesFromBomrads(
        accountId: String,
        accountName: String,
        projectDetails: ProjectDetailsDTO,
        branchWithChangedNodes: List<Pair<MinimalBranchDTO, List<NodeSnapshotDTO>>>,
        environmentName: String? = null,
    ): Flux<BomNodeSearchDto> {
        val allSnapshotIds =
            branchWithChangedNodes.flatMap { changedNodes ->
                changedNodes.second.mapNotNull {
                    it.treeId()?.toMongoSnapshotId()
                }
            }.distinct()
        logger.info("Need to load ${allSnapshotIds.size} nodes ...")
        return Flux.fromIterable(allSnapshotIds.windowed(10, 10, true)).concatMapDelayError { snapshotIds ->
            snapshotLoaderService.loadAdminSnapshots(
                accountId.toObjectId()!!,
                accountName,
                snapshotIds,
                environmentName,
            )
                .flatMapMany { snapshotMapping ->
                    logger.info("Snapshots requested: ${snapshotIds.size}, loaded: ${snapshotMapping.size}")
                    processBlockOfNodes(accountId, projectDetails, snapshotMapping, branchWithChangedNodes)
                }
        }
    }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun processBlockOfNodes(
        accountId: String,
        projectDetails: ProjectDetailsDTO,
        snapshotMapping: Map<SnapshotId, BomNodeSnapshot>,
        branchWithChangedNodes: List<Pair<MinimalBranchDTO, List<NodeSnapshotDTO>>>,
    ): Flux<BomNodeSearchDto> {
        val nodes =
            branchWithChangedNodes.flatMap { (branch, snapshots) ->
                snapshots.map { nodeSnapshotDTO ->
                    val mongoSnapshot = snapshotMapping[nodeSnapshotDTO.treeId()?.toMongoSnapshotId()]
                    mongoSnapshot to nodeSnapshotDTO
                }.filter {
                    it.first != null
                }.map {
                    Triple(it.first!!, it.second, branch)
                }
            }
        return Flux.fromIterable(nodes).flatMap { (mongoSnapshot, nodeSnapshotDTO, branch) ->
            processNode(accountId, projectDetails, branch, nodeSnapshotDTO, mongoSnapshot)
        }
    }

    private fun processNode(
        accountId: String,
        projectDetails: ProjectDetailsDTO,
        branch: MinimalBranchDTO,
        nodeSnapshotDTO: NodeSnapshotDTO,
        mongoSnapshot: BomNodeSnapshot,
    ): Mono<BomNodeSearchDto> {
        val snapshotExt =
            SnapshotExtension(
                mongoSnapshot,
                nodeSnapshotDTO.status,
                mainBranch = branch.main(),
                title = nodeSnapshotDTO.title,
                parentId = nodeSnapshotDTO.externalParents.firstOrNull()?.bomNodeId()?.toMongoSafe()?.idToString(),
            )
        return updateElasticIndex(
            accountId = accountId,
            projectDetails,
            bomNodeId = nodeSnapshotDTO.bomNodeId().toMongoBomNodeId().toHexString(),
            rootId =
                nodeSnapshotDTO.rootId?.toMongoBomNodeId()?.toHexString()
                    ?: nodeSnapshotDTO.bomNodeId.toMongoBomNodeId().toHexString(),
            snapshotWithPart = snapshotExt,
            branchId = branch.id().toMongoFormatStr(),
        )
    }

    private fun processProjectEvent(event: ProjectEvent): Mono<Void> {
        logger.info("force processProjectEvent: $event")
        return processBomradsProjectEvent(event)
    }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun processBranchEvent(event: GlobalBranchEvent): Mono<Void> {
        return if (event.deleted) {
            reactiveElasticSearchClient.findByBranchId(
                ElasticSearchModule.BOMNODE_INDEX_NAME,
                event.branchId,
            ).flatMap { bomNodeSearchDto ->
                logger.debug("Deleting ${bomNodeSearchDto.id} from the search index...")
                reactiveElasticSearchClient.delete(ElasticSearchModule.BOMNODE_INDEX_NAME, bomNodeSearchDto.id)
            }.then()
        } else {
            processEventWithBomrads(
                event.accountId,
                event.accountName,
                event.environment,
                projectId = event.getProjectId(),
                branchId = event.getBranchId(),
            ) { branchViews ->
                branchViews.map { it.branch to it.snapshots }
            }.then()
        }
    }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun processProjectDeletedEvent(event: ProjectDeletedEvent): Mono<Void> {
        return reactiveElasticSearchClient.findByProjectId(
            ElasticSearchModule.BOMNODE_INDEX_NAME,
            event.projectId,
        ).flatMap { bomNodeSearchDto ->
            logger.debug("Deleting ${bomNodeSearchDto.id} from the search index...")
            reactiveElasticSearchClient.delete(ElasticSearchModule.BOMNODE_INDEX_NAME, bomNodeSearchDto.id)
        }.then()
    }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun processFolderDeletedEvent(event: FolderDeletedEvent): Mono<Void> {
        return reactiveElasticSearchClient.findByFolderId(
            ElasticSearchModule.BOMNODE_INDEX_NAME,
            event.folderId,
        ).flatMap { bomNodeSearchDto ->
            logger.debug("Deleting ${bomNodeSearchDto.id} from the search index...")
            reactiveElasticSearchClient.delete(ElasticSearchModule.BOMNODE_INDEX_NAME, bomNodeSearchDto.id)
        }.then()
    }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun processWorkspaceDeletedEvent(event: WorkspaceDeletedEvent): Mono<Void> {
        return reactiveElasticSearchClient.findByWorkspaceId(
            ElasticSearchModule.BOMNODE_INDEX_NAME,
            event.workspaceId,
        ).flatMap { bomNodeSearchDto ->
            logger.debug("Deleting ${bomNodeSearchDto.id} from the search index...")
            reactiveElasticSearchClient.delete(ElasticSearchModule.BOMNODE_INDEX_NAME, bomNodeSearchDto.id)
        }.then()
    }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun processFolderUpdatedEvent(event: FolderEvent): Mono<Void> {
        return reactiveElasticSearchClient.findByWorkspaceId(
            ElasticSearchModule.BOMNODE_INDEX_NAME,
            event.folderId,
        ).flatMap { bomNodeSearchDto ->
            logger.debug("Deleting ${bomNodeSearchDto.id} from the search index...")
            reactiveElasticSearchClient.delete(ElasticSearchModule.BOMNODE_INDEX_NAME, bomNodeSearchDto.id)
        }.then(
            processFolderEvent(event),
        )
    }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun processWorkspaceUpdatedEvent(event: WorkspaceUpdatedEvent): Mono<Void> {
        return reactiveElasticSearchClient.findByWorkspaceId(
            ElasticSearchModule.BOMNODE_INDEX_NAME,
            event.workspaceId,
        ).flatMap { bomNodeSearchDto ->
            logger.debug("Deleting ${bomNodeSearchDto.id} from the search index...")
            reactiveElasticSearchClient.delete(ElasticSearchModule.BOMNODE_INDEX_NAME, bomNodeSearchDto.id)
        }.then(
            processWorkspaceEvent(event),
        )
    }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun processFolderEvent(event: FolderEvent): Mono<Void> {
        return bomradsFolderService.getProjectIdsForFolder(event.accountName, event.folderId.toUUID().toString())
            .flatMap { projectId ->
                processEventWithBomrads(
                    event.accountId,
                    event.accountName,
                    event.environment,
                    projectId = projectId,
                    branchId = null,
                ) { branchViews ->
                    getSelector(branchViews)
                }
            }.then()
    }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun processWorkspaceEvent(event: WorkspaceUpdatedEvent): Mono<Void> {
        return bomradsWorkspaceService.getProjectIdsForWorkspace(
            event.accountName,
            event.workspaceId.toUUID().toString(),
        ).flatMap { projectId ->
            processEventWithBomrads(
                event.accountId,
                event.accountName,
                event.environment,
                projectId = projectId,
                branchId = null,
            ) { branchViews ->
                getSelector(branchViews)
            }
        }.then()
    }

    private fun processBomradsProjectEvent(event: ProjectEvent): Mono<Void> {
        return processEventWithBomrads(
            event.accountId,
            event.accountName,
            event.environment,
            projectId = event.getProjectId(),
            branchId = null,
        ) { branchViews ->
            getSelector(branchViews)
        }.then()
    }

    private fun getSelector(branchViews: List<BranchViewDTO>): List<Pair<MinimalBranchDTO, List<NodeSnapshotDTO>>> {
        val result =
            branchViews.map { branchView ->
                branchView.branch() to
                    branchView.snapshots()
            }
        result.forEach { filteredResult ->
            filteredResult.second.forEach {
                tentativeBomNodeUpdates[it.bomNodeId.toMongoFormatStr()] = LocalDateTime.now()
            }
        }
        return result
    }

    private fun processBomNodeEvent(event: BomNodeEvent): Mono<Void> {
        logger.info("processBomNodeEvent $event")

        return when (event) {
            is BomNodeStatusUpdatedEvent -> updateBomNodeStatus(event)
            is BomNodeDeletedEvent -> processBomNodeDeletedEvent(event)
            is RootBomNodeUpdatedEvent,
            is RootBomNodeDeletedEvent,
            -> processRootBomNodeEvent(event)

            is BomNodeSnapshotAccessedEvent,
            is BomNodeSnapshotUpdatedEvent,
            is BomNodeUpdatedEvent,
            is BomNodeCreatedEvent,
            -> processBomradsBomNodeEvent(event)

            else -> Mono.empty()
        }
    }

    private fun processBomradsBomNodeEvent(event: BomNodeEvent): Mono<Void> {
        val bomNodeId = LegacyBomNodeId(event.bomNodeId).toBomNodeId()
        val branchId =
            if (event is BomNodeSnapshotEvent) {
                LegacyBranchId(event.branchId).toBranchId()
            } else {
                null
            }
        return processEventWithBomrads(
            event.accountId,
            event.accountName,
            event.environment,
            branchId = branchId,
            projectId = event.getProjectId(),
        ) { branchViews ->
            branchViews.map { branchView ->
                branchView.branch() to branchView.collectChildren(bomNodeId).toList()
            }
        }.then()
    }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun processBomNodeDeletedEvent(event: BomNodeDeletedEvent): Mono<Void> {
        return reactiveElasticSearchClient.findByBomNodeId(
            ElasticSearchModule.BOMNODE_INDEX_NAME,
            event.bomNodeId,
        ).flatMap { bomNodeSearchDto ->
            logger.debug("Deleting ${bomNodeSearchDto.id} from the search index...")
            reactiveElasticSearchClient.delete(ElasticSearchModule.BOMNODE_INDEX_NAME, bomNodeSearchDto.id)
        }.then()
    }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun processRootBomNodeEvent(event: BomNodeEvent): Mono<Void> {
        return reactiveElasticSearchClient.findByRootBomNodeId(ElasticSearchModule.BOMNODE_INDEX_NAME, event.bomNodeId)
            .flatMap { bomNodeSearchDto ->
                logger.debug("Deleting ${bomNodeSearchDto.id} from the search index...")
                reactiveElasticSearchClient.delete(ElasticSearchModule.BOMNODE_INDEX_NAME, bomNodeSearchDto.id)
            }
            .then(
                when (event) {
                    // we want to delete all the indexes associated with the rootId and reindex so that we dont
                    // have to inject lot of special handling at the time of publish to check which variants
                    // have been deleted, which bomentries from the tree are still existing
                    is RootBomNodeUpdatedEvent -> processBomradsBomNodeEvent(event)
                    else -> Mono.empty()
                },
            )
    }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun updateBomNodeStatus(event: BomNodeStatusUpdatedEvent): Mono<Void> {
        return reactiveElasticSearchClient.findByBomNodeId(
            ElasticSearchModule.BOMNODE_INDEX_NAME,
            event.bomNodeId,
        ).flatMap { bomNodeSearchDto ->
            reactiveElasticSearchClient.save(
                ElasticSearchModule.BOMNODE_INDEX_NAME,
                bomNodeSearchDto.id,
                bomNodeSearchDto.copy(status = event.status.name),
            )
        }.then()
    }

    private fun processBomNodeSnapshotAccessedorUpdatedEvent(event: BomNodeSnapshotEvent): Mono<Void> {
        logger.debug("processBomNodeSnapshotAccessedorUpdatedEvent $event")

        val calculationId =
            ElasticSearchModule.getIdString(event.accountId, event.projectId, event.bomNodeId, event.branchId)
        val userId = ElasticSearchModule.getUserIdString(event.accountId, event.userId)

        return reactiveElasticSearchClient.get(
            ElasticSearchModule.USER_ACCESS_INDEX_NAME,
            userId,
            UserAccessDto::class.java,
        )
            .onErrorResume {
                logger.warn(it.message, it)
                Mono.empty()
            }
            .map { userAccess ->
                addToUserAccessMapEnsuringSize(userAccess, Pair(calculationId, event.accessedDate))
            }.defaultIfEmpty(
                UserAccessDto(
                    id = userId,
                    accountId = event.accountId,
                    userId = event.userId,
                    lastReadCalculations = mapOf(calculationId to event.accessedDate),
                ),
            ).flatMap { updatedUserAccess ->
                reactiveElasticSearchClient
                    .save(ElasticSearchModule.USER_ACCESS_INDEX_NAME, updatedUserAccess.id, updatedUserAccess)
                    .doOnSuccess { created ->
                        val createUpdateMsg = if (created) "created" else "updated"
                        logger.info(
                            "ElasticSearch user access index update: $createUpdateMsg " +
                                "accountId=${event.accountId} " +
                                "projectId=${event.projectId} " +
                                "bomNodeId=${event.bomNodeId} " +
                                "branchId=${event.branchId}",
                        )
                    }
                    .doOnError { error -> logger.error("ElasticSearch user access index update: $error") }
            }.then()
    }

    private fun addToUserAccessMapEnsuringSize(
        userAccessMap: UserAccessDto,
        newEntry: Pair<String, Date>,
    ): UserAccessDto =
        if (userAccessMap.lastReadCalculations.size > USERACCESS_HISTORY_LIMIT) {
            val limitedHistory: List<Pair<String, Date>> =
                userAccessMap.lastReadCalculations.entries
                    .sortedByDescending { it.value }
                    .take(USERACCESS_HISTORY_LIMIT)
                    .map { Pair(it.key, it.value) }
                    .plus(newEntry)
            userAccessMap.copy(lastReadCalculations = mapOf(*limitedHistory.toTypedArray()))
        } else {
            userAccessMap.copy(lastReadCalculations = userAccessMap.lastReadCalculations.plus(newEntry))
        }

    private fun updateElasticIndex(
        accountId: String,
        projectDetails: ProjectDetailsDTO,
        bomNodeId: String,
        rootId: String,
        snapshotWithPart: SnapshotExtension,
        branchId: String,
    ) = accessCheckProvider.getDefaultAccessCheckForAccount(accountId).flatMap { accessCheck ->
        val projectId = projectDetails.projectId.toMongoProjectId().toHexString()
        updateBomNodeSearchDto(
            accessCheck,
            accountId,
            projectDetails,
            projectId,
            bomNodeId,
            rootId,
            snapshotWithPart,
            branchId,
        ).withAccessCheck(accessCheck).flatMap { bomNodeSearchDto ->
            reactiveElasticSearchClient.save(
                ElasticSearchModule.BOMNODE_INDEX_NAME,
                bomNodeSearchDto.id,
                bomNodeSearchDto,
            ).doOnSuccess { created ->
                val createUpdateMsg = if (created) "created" else "updated"
                logger.info(
                    "ElasticSearch bomnode index update: $createUpdateMsg " +
                        "accountId=$accountId " +
                        "projectId=$projectId " +
                        "folderId=${projectDetails.folderId!!.id().toObjectId().toHexString()} " +
                        "bomNodeId=$bomNodeId " +
                        "branchId=$branchId",
                )
            }.map {
                bomNodeSearchDto
            }
        }
    }

    private fun updateBomNodeSearchDto(
        accessCheck: AccessCheck,
        accountId: String,
        projectDetails: ProjectDetailsDTO,
        projectId: String,
        bomNodeId: String,
        rootId: String,
        snapshot: SnapshotExtension,
        branchId: String,
    ): Mono<BomNodeSearchDto> {
        val id = ElasticSearchModule.getIdString(accountId, projectId, bomNodeId, branchId)
        val exchangeRates = snapshot.snapshot.manufacturing?.getExchangeRateMap() ?: ExchangeRateMap.empty()
        return reactiveElasticSearchClient.get(
            ElasticSearchModule.BOMNODE_INDEX_NAME,
            id,
            BomNodeSearchDto::class.java,
        ).flatMap { currentDto ->
            updateFields(accessCheck, snapshot, projectDetails, currentDto, exchangeRates)
        }.switchIfEmpty(
            updateFields(
                accessCheck,
                snapshot,
                projectDetails,
                BomNodeSearchDto(
                    id = id,
                    rootId,
                    bomNodeSnapshotId = snapshot.bomNodeSnapshotId(),
                    bomNodeId = bomNodeId,
                    branchId = branchId,
                    projectId = projectId,
                    accountId = accountId,
                    folderId = projectDetails.folderId!!.id().toObjectId().toHexString(),
                    workspaceId = projectDetails.workspaceId!!.id().toObjectId().toHexString(),
                    projectPath = projectDetails.projectPath,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    projectName = projectDetails.projectName,
                    projectKey = projectDetails.projectKey,
                    true,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    false,
                ),
                exchangeRates,
            ),
        )
    }

    private fun updateFields(
        accessCheck: AccessCheck,
        snapshotExt: SnapshotExtension,
        projectDetails: ProjectDetailsDTO,
        currentDto: BomNodeSearchDto,
        exchangeRates: ExchangeRateMap,
    ): Mono<BomNodeSearchDto> {
        val snapshot = snapshotExt.snapshot
        val baseManufacturing = snapshot.getBaseManufacturing()
        val realPartName = baseManufacturing?.getFieldResultSafe("partDesignation")?.res?.toString()
        val man = snapshot.manufacturing!!
        val realTechnology = getTechnologyFieldValue(man)

        val unitOverrideContext = UnitOverrideContext.fromEntity(man)

        fun getField(name: String) =
            fieldConversionService.getFieldAsFieldParameter(accessCheck, man, name, exchangeRates, unitOverrideContext)
                .wrapIntoMaybe()

        return Mono.zip(
            getField(COST_PER_PART),
            getField(NET_WEIGHT_PER_PART_WIZARD),
            getField("averageUsableProductionVolumePerYear"),
            getField("peakUsableProductionVolumePerYear"),
            getField(CO2_PER_PART),
        ).map { fieldParams ->
            val responsibleUser =
                snapshot
                    .manufacturing
                    .getResponsibleUser()
                    ?.let {
                        userService.getUserById(accessCheck, it.res).map { user -> user.name }.orElse(null)
                    }

            currentDto.copy(
                folderId = projectDetails.folderId!!.id().toObjectId().toHexString(),
                isMaster = snapshotExt.mainBranch,
                lastModifiedDate = snapshot.lastModifiedDate,
                partName = realPartName,
                partNameValue = realPartName,
                partNumber = baseManufacturing?.getFieldResultSafe("partNumber")?.res?.toString(),
                partImage =
                    baseManufacturing?.getAttachments()?.mapNotNull {
                        it.getFieldResultSafe(Attachment::fileId.name)?.res?.toString()
                    },
                responsible = responsibleUser,
                status = snapshotExt.status.name,
                title = snapshotExt.title,
                projectName = projectDetails.projectName,
                projectKey = projectDetails.projectKey,
                projectPath = projectDetails.projectPath,
                technology = realTechnology,
                shapeId = getFieldTextValue(man, "shapeId"),
                shapeIdNoPrefix = getShapeIdNoPrefix(getFieldTextValue(man, "shapeId")),
                location = getFieldTextValue(man, "locationName"),
                material =
                    getFieldTextValue(man, "materialName")?.let { listOf(it) }.orEmpty() +
                        getEntitiesByTypeRecursively(man, Entities.MATERIAL),
                procurementType =
                    getFieldTextValue(
                        man,
                        CommercialCalculationCostManufacturedMaterial::customProcurementTypeShortName.name,
                    ),
                machine = getEntitiesByTypeRecursively(man, Entities.MACHINE),
                consumable = getEntitiesByTypeRecursively(man, Entities.CONSUMABLE),
                labor = getEntitiesByTypeRecursively(man, Entities.LABOR),
                tool = getEntitiesByTypeRecursively(man, Entities.TOOL),
                cpart = getEntitiesByTypeRecursively(man, Entities.C_PART),
                step =
                    getFieldTextValuesRecursively(
                        man,
                        Entities.MANUFACTURING_STEP,
                        "displayDesignation",
                    ),
                stepConfiguration =
                    getFieldTextValuesRecursively(
                        man,
                        Entities.MANUFACTURING_STEP,
                        "templateName",
                    ),
                netWeightPerPartValue = getFieldDoubleValue(man, NET_WEIGHT_PER_PART_WIZARD),
                costPerPartValue = getFieldDoubleValue(man, "costPerPart"),
                cO2PerPartValue = getFieldDoubleValue(man, "cO2PerPart"),
                avgVolumeValue = getFieldDoubleValue(man, "averageUsableProductionVolumePerYear"),
                peakVolumeValue = getFieldDoubleValue(man, "peakUsableProductionVolumePerYear"),
                costPerPart = fieldParams.t1.value,
                netWeightPerPart = fieldParams.t2.value,
                avgVolume = fieldParams.t3.value,
                peakVolume = fieldParams.t4.value,
                co2PerPart = fieldParams.t5.value,
                entityId = snapshot.manufacturing.entityId,
                calculationType = CalculationType.fromManufacturingEntityClass(snapshot.manufacturing.getEntityClass()).name,
                parentId = snapshotExt.parentId,
                generated = !snapshot.manufacturing.canBeCopied(),
                lastModifiedBy =
                    snapshot.lastModifiedBy.applyIfExists { user ->
                        userService.getUserById(accessCheck, user).map { it.name }.orElse(null)
                    },
            )
        }
    }

    private fun getEntitiesByTypeRecursively(
        startEntity: ManufacturingEntity,
        entityType: Entities,
    ): List<String> {
        return startEntity.visitChildren { entity, _ ->
            if (entity.getEntityTypeAnnotation() == entityType) {
                entity.getFieldResultSafe("displayDesignation")?.res?.toString()
            } else {
                null
            }
        }
    }

    private fun getTechnologyFieldValue(entity: ManufacturingEntity): String? {
        val technologyKey = entity.getFieldResultSafe("technologyKey")?.res?.toString()
        return Model.entries.find { value -> value.path == technologyKey }?.displayName
    }

    private fun getFieldTextValue(
        entity: ManufacturingEntity,
        fieldName: String,
    ): String? {
        return entity.getFieldResultSafe(fieldName)?.res?.toString()
    }

    private fun getFieldTextValuesRecursively(
        startEntity: ManufacturingEntity,
        entityType: Entities,
        fieldName: String,
    ): List<String> {
        return startEntity.visitChildren { entity, _ ->
            if (entity.getEntityTypeAnnotation() == entityType) {
                removeManufacturingStepPrefix(entity.getFieldResultSafe(fieldName)?.res?.toString())
            } else {
                null
            }
        }
    }

    private fun removeManufacturingStepPrefix(value: String?): String? = value?.removePrefix("ManufacturingStep")

    private fun getFieldDoubleValue(
        entity: ManufacturingEntity,
        fieldName: String,
    ): Double? {
        val fieldValue = entity.getFieldResultSafe(fieldName) ?: return null
        return when (fieldValue) {
            is NumericFieldResult<*> -> fieldValue.res.toDouble()
            else -> null
        }
    }

    private fun getShapeIdNoPrefix(shapeId: String?): String? = shapeId?.dropWhile { it != '_' }?.drop(1)

    private fun setEnvironmentName(name: String?) {
        if (name != null) {
            environmentNameSupplier.setEnv(name)
            logger.debug("set environment name to $name")
        } else {
            logger.debug("environment name null")
        }
    }

    data class ProjectDetailsDTO(
        val projectId: ProjectId,
        val projectName: String,
        val projectPath: String?,
        val folderId: FolderId?,
        val workspaceId: WorkspaceId?,
        val projectKey: String,
    )
}
