package com.tset.core.module.bom

import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationRole
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.fieldnamebuilders.ValueFieldNameBuilder
import com.nu.bom.core.manufacturing.commercialcalculation.legacyfieldnames.ManufacturingLegacyFieldNames
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCO2eCalculationElementType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCostCalculationElementType
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.UnitOverrideContext
import com.nu.bom.core.manufacturing.fieldTypes.DynamicQuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.NumericFieldResult
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.service.FilteredBomService
import com.nu.bom.core.utils.applyIfExists
import com.tset.core.api.dto.CurrencyInfo
import com.tset.core.api.dto.DrillDownType
import com.tset.core.module.bom.exchangerates.ExchangeRateMap
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.RoundingMode
import java.util.UUID

@Service
class CbdModule {
    data class CbdTreeNode(
        val id: String, // bomNodeId, or generated uuid
        val source: String, // field name in the bom structure
        val cost: CurrencyInfo,
        val quantity: BigDecimal?, // quantity if BOM_ENTRY
        val entityId: String?,
        val entityType: String, // should this be an enum?
        val drillDownType: DrillDownType, // should this reuse the API dto or not?
        val children: List<CbdTreeNode>,
        val debug: String? = null,
        val displayDesignation: String?,
    )

    /**
     * WARNING: the multiplication of cost by quantity happens outside this function,
     * here we only use the request quantity to compute the absolute threshold value!
     */
    fun getWaterfallBreakdown(
        requestedDrillDownType: DrillDownType,
        totalCost: BigDecimal?,
        requestQuantity: BigDecimal,
        isCo2: Boolean,
        exchangeRateMap: ExchangeRateMap,
        bomNodeSnapshot: BomNodeSnapshot,
    ): Pair<List<CbdTreeNode>, CurrencyInfo> {
        val costUnitBaseFactor =
            UnitOverrideContext
                .fromEntity(
                    bomNodeSnapshot.manufacturing!!,
                ).getDynamicUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
                .baseFactor

        val cbdNodes =
            if (isCo2) {
                getInitialCO2BreakdownFromManufacturing(
                    bomNodeSnapshot,
                    bomNodeSnapshot.manufacturing,
                    requestedDrillDownType,
                    costUnitBaseFactor = costUnitBaseFactor,
                    exchangeRateMap = exchangeRateMap,
                )
            } else {
                getInitialCbdFromManufacturing(
                    bomNodeSnapshot,
                    bomNodeSnapshot.manufacturing,
                    requestedDrillDownType,
                    costUnitBaseFactor = costUnitBaseFactor,
                    exchangeRateMap = exchangeRateMap,
                )
            }
        return Pair(
            groupNodesBelowThreshold(
                cbdNodes,
                cbdTotalThresholdValue(totalCost ?: BigDecimal.ZERO, requestQuantity),
            ),
            exchangeRateMap.eurToCurrencyInfo(totalCost),
        )
    }

    private fun groupNodesBelowThreshold(
        cbdNodes: List<CbdTreeNode>,
        thresholdValue: BigDecimal,
    ): List<CbdTreeNode> {
        val nodesGrouped = cbdNodes.groupBy { node -> node.cost.getEurValueOrZero() <= thresholdValue }
        val nodesOther =
            nodesGrouped[true]
                ?.reduce { acc, next -> acc.copy(cost = acc.cost.plus(next.cost), debug = "${acc.debug}, ${next.debug}") }
                ?.copy(
                    id = UUID.randomUUID().toString(),
                    displayDesignation = CBD_ROW_OTHERS,
                    source = CBD_ROW_OTHERS,
                    children = emptyList(),
                )
        val nodesOtherList = nodesOther.applyIfExists { listOf(it) } ?: emptyList()
        val nodesDetails =
            nodesGrouped[false]?.map { node ->
                node.copy(children = groupNodesBelowThreshold(node.children, thresholdValue))
            } ?: emptyList()
        return nodesDetails + nodesOtherList
    }

    private fun getInitialCbdFromManufacturing(
        bomNodeSnapshot: BomNodeSnapshot,
        manufacturing: ManufacturingEntity,
        requestedDrillDownType: DrillDownType,
        costUnitBaseFactor: BigDecimal,
        exchangeRateMap: ExchangeRateMap,
    ): List<CbdTreeNode> {
        val materialCosts =
            getFieldRow(
                bomNodeSnapshot,
                entity = manufacturing,
                fieldName = "materialCosts",
                source = CBD_ROW_TOTAL_MATERIAL_COSTS,
                children =
                    getFieldsRecursive(
                        bomNodeSnapshot,
                        manufacturing,
                        Entities.MATERIAL,
                        DrillDownType.MATERIAL,
                        costUnitBaseFactor = costUnitBaseFactor,
                        exchangeRateMap = exchangeRateMap,
                        "costPerPart",
                    ) +
                        getFieldsRecursive(
                            bomNodeSnapshot,
                            manufacturing,
                            Entities.CONSUMABLE,
                            DrillDownType.MATERIAL,
                            costUnitBaseFactor = costUnitBaseFactor,
                            exchangeRateMap = exchangeRateMap,
                            "costPerPart",
                        ) +
                        getFieldsRecursive(
                            bomNodeSnapshot,
                            manufacturing,
                            Entities.C_PART,
                            DrillDownType.MATERIAL,
                            costUnitBaseFactor = costUnitBaseFactor,
                            exchangeRateMap = exchangeRateMap,
                            "costPerPart",
                        ) +
                        getFieldsRecursiveForBomEntryMaterial(
                            bomNodeSnapshot,
                            manufacturing,
                            costUnitBaseFactor = costUnitBaseFactor,
                            exchangeRateMap = exchangeRateMap,
                            "costPerPart",
                        ) +
                        listOf(
                            getFieldRow(
                                bomNodeSnapshot,
                                entity = manufacturing,
                                fieldName = "materialScrapCosts",
                                costUnitBaseFactor = costUnitBaseFactor,
                                source = CBD_ROW_MATERIAL_SCRAP_COSTS,
                                drillDownType = DrillDownType.MATERIAL,
                                exchangeRateMap = exchangeRateMap,
                            ),
                            getFieldRow(
                                bomNodeSnapshot,
                                entity = manufacturing,
                                fieldName = ManufacturingLegacyFieldNames.MATERIAL_OVERHEAD_COSTS.fieldName,
                                costUnitBaseFactor = costUnitBaseFactor,
                                source = ManufacturingLegacyFieldNames.MATERIAL_OVERHEAD_COSTS.fieldName,
                                drillDownType = DrillDownType.MATERIAL,
                                exchangeRateMap = exchangeRateMap,
                            ),
                            getFieldRow(
                                bomNodeSnapshot,
                                entity = manufacturing,
                                fieldName = ManufacturingLegacyFieldNames.INTEREST_ON_MATERIAL_STOCKS.fieldName,
                                costUnitBaseFactor = costUnitBaseFactor,
                                source = ManufacturingLegacyFieldNames.INTEREST_ON_MATERIAL_STOCKS.fieldName,
                                drillDownType = DrillDownType.MATERIAL,
                                exchangeRateMap = exchangeRateMap,
                            ),
                        ),
                drillDownType = DrillDownType.MATERIAL,
                costUnitBaseFactor = costUnitBaseFactor,
                exchangeRateMap = exchangeRateMap,
            )

        val manufacturingCosts =
            getFieldRow(
                bomNodeSnapshot,
                manufacturing,
                fieldName = "manufacturingCosts3",
                source = CBD_ROW_TOTAL_MANUFACTURING_COSTS,
                exchangeRateMap = exchangeRateMap,
                children =
                    flattenChildren(entity = manufacturing, entityType = Entities.MANUFACTURING_STEP)
                        .map {
                            getFieldRow(
                                bomNodeSnapshot,
                                entity = it,
                                fieldName = "manufacturingCosts2Step",
                                children =
                                    getFields(
                                        bomNodeSnapshot,
                                        it,
                                        true,
                                        DrillDownType.MANUFACTURING,
                                        costUnitBaseFactor = costUnitBaseFactor,
                                        exchangeRateMap = exchangeRateMap,
                                        "costPerPartMachine",
                                        "costPerPartSetup",
                                        "costPerPartLabor",
                                        "maintenanceCostPerPartTool",
                                        "costPerPartRMOC",
                                        "costPerPartManufacturingScrap",
                                        "allocationInterestCostPerPartTool",
                                    ),
                                drillDownType = DrillDownType.MANUFACTURING,
                                costUnitBaseFactor = costUnitBaseFactor,
                                exchangeRateMap = exchangeRateMap,
                            )
                        }.reversed() +

                        getFieldsRecursiveWithFilter(
                            bomNodeSnapshot,
                            manufacturing,
                            Entities.BOM_ENTRY,
                            DrillDownType.MANUFACTURING,
                            { entity -> entity.getFieldResult("procurementType")?.res?.equals(ManufacturingType.INHOUSE) ?: false },
                            costUnitBaseFactor = costUnitBaseFactor,
                            exchangeRateMap = exchangeRateMap,
                            "manufacturingCosts2Step",
                        ) +

                        listOf(
// Removed because this is now replaced by the individual BOM_ENTRY columns
//                    getFieldRow(bomNodeSnapshot, manufacturing, fieldName = "manufacturingCosts2SubManufacturing", source = CBD_ROW_MANUFACTURING_COSTS_SUBMANUFACTURING, drillDownType = DrillDownType.MANUFACTURING),
                            getFieldRow(
                                bomNodeSnapshot,
                                manufacturing,
                                fieldName = "interestOnWorkInProgressStep",
                                source = CBD_ROW_MANUFACTURING_INTEREST_ON_WIP,
                                drillDownType = DrillDownType.MANUFACTURING,
                                costUnitBaseFactor = costUnitBaseFactor,
                                exchangeRateMap = exchangeRateMap,
                            ),
                        ),
                drillDownType = DrillDownType.MANUFACTURING,
                costUnitBaseFactor = costUnitBaseFactor,
            )

        val overheadsList =
            listOf(
                getFieldRow(
                    bomNodeSnapshot,
                    manufacturing,
                    fieldName = "overheadCosts",
                    source = CBD_ROW_TOTAL_OVERHEAD_COSTS,
                    children =
                        listOf(
                            getFieldRow(
                                bomNodeSnapshot,
                                manufacturing,
                                fieldName =
                                    ValueFieldNameBuilder(
                                        ValueType.COST,
                                        AggregationLevel.SOLD_MATERIAL,
                                        AggregationRole.TOTAL,
                                        TsetCostCalculationElementType.SPECIAL_DIRECT_COSTS.fieldName,
                                    ).fieldName,
                                source = CBD_ROW_OVERHEADS_SPECIAL_DIRECT_COSTS,
                                drillDownType = DrillDownType.ALL,
                                costUnitBaseFactor = costUnitBaseFactor,
                                exchangeRateMap = exchangeRateMap,
                            ),
                            getFieldRow(
                                bomNodeSnapshot,
                                manufacturing,
                                fieldName =
                                    ValueFieldNameBuilder(
                                        ValueType.COST,
                                        AggregationLevel.SOLD_MATERIAL,
                                        AggregationRole.TOTAL,
                                        TsetCostCalculationElementType.OVERHEADS_AFTER_PC.fieldName,
                                    ).fieldName,
                                source = CBD_ROW_OVERHEADS_AFTER_PC,
                                drillDownType = DrillDownType.ALL,
                                costUnitBaseFactor = costUnitBaseFactor,
                                exchangeRateMap = exchangeRateMap,
                            ),
                            getFieldRow(
                                bomNodeSnapshot,
                                manufacturing,
                                fieldName =
                                    ValueFieldNameBuilder(
                                        ValueType.COST,
                                        AggregationLevel.SOLD_MATERIAL,
                                        AggregationRole.TOTAL,
                                        TsetCostCalculationElementType.PROFIT.fieldName,
                                    ).fieldName,
                                source = CBD_ROW_OVERHEADS_CPP_PROFIT,
                                drillDownType = DrillDownType.ALL,
                                costUnitBaseFactor = costUnitBaseFactor,
                                exchangeRateMap = exchangeRateMap,
                            ),
                            getFieldRow(
                                bomNodeSnapshot,
                                manufacturing,
                                fieldName =
                                    ValueFieldNameBuilder(
                                        ValueType.COST,
                                        AggregationLevel.SOLD_MATERIAL,
                                        AggregationRole.TOTAL,
                                        TsetCostCalculationElementType.TERMS_OF_PAYMENT.fieldName,
                                    ).fieldName,
                                source = CBD_ROW_OVERHEADS_CPP_TERMS_OF_PAYMENT,
                                drillDownType = DrillDownType.ALL,
                                costUnitBaseFactor = costUnitBaseFactor,
                                exchangeRateMap = exchangeRateMap,
                            ),
                            getFieldRow(
                                bomNodeSnapshot,
                                manufacturing,
                                fieldName =
                                    ValueFieldNameBuilder(
                                        ValueType.COST,
                                        AggregationLevel.SOLD_MATERIAL,
                                        AggregationRole.TOTAL,
                                        TsetCostCalculationElementType.INCO_TERMS.fieldName,
                                    ).fieldName,
                                source = CBD_ROW_OVERHEADS_CPP_INCOTERMS,
                                drillDownType = DrillDownType.ALL,
                                costUnitBaseFactor = costUnitBaseFactor,
                                exchangeRateMap = exchangeRateMap,
                            ),
                        ),
                    drillDownType = DrillDownType.ALL,
                    costUnitBaseFactor = costUnitBaseFactor,
                    exchangeRateMap = exchangeRateMap,
                ),
            )

        val specialDirectCosts =
            listOf(
                getFieldRow(
                    bomNodeSnapshot,
                    manufacturing,
                    fieldName = "specialDirectCosts",
                    source = CBD_ROW_TOTAL_SPECIAL_DIRECT_COSTS,
                    children =
                        listOf(
                            getFieldRow(
                                bomNodeSnapshot,
                                manufacturing,
                                fieldName = "developmentCosts",
                                source = CBD_ROW_SDC_DEVELOPMENT_COSTS,
                                drillDownType = DrillDownType.ALL,
                                costUnitBaseFactor = costUnitBaseFactor,
                                exchangeRateMap = exchangeRateMap,
                            ),
                            getFieldRow(
                                bomNodeSnapshot,
                                manufacturing,
                                fieldName = "rampUpCosts",
                                source = CBD_ROW_SDC_RAMP_UP_COSTS,
                                drillDownType = DrillDownType.ALL,
                                costUnitBaseFactor = costUnitBaseFactor,
                                exchangeRateMap = exchangeRateMap,
                            ),
                            getFieldRow(
                                bomNodeSnapshot,
                                manufacturing,
                                fieldName = "packageCarrierCosts",
                                source = CBD_ROW_SDC_PACKAGE_CARRIER_COSTS,
                                drillDownType = DrillDownType.ALL,
                                costUnitBaseFactor = costUnitBaseFactor,
                                exchangeRateMap = exchangeRateMap,
                            ),
                        ),
                    drillDownType = DrillDownType.ALL,
                    costUnitBaseFactor = costUnitBaseFactor,
                    exchangeRateMap = exchangeRateMap,
                ),
            )

        return when (requestedDrillDownType) {
            DrillDownType.ALL -> listOf(materialCosts, manufacturingCosts) + overheadsList + specialDirectCosts
            DrillDownType.MATERIAL -> listOf(materialCosts)
            DrillDownType.MANUFACTURING -> listOf(manufacturingCosts)
        }
    }

    private fun getInitialCO2BreakdownFromManufacturing(
        bomNodeSnapshot: BomNodeSnapshot,
        manufacturing: ManufacturingEntity,
        requestedDrillDownType: DrillDownType,
        costUnitBaseFactor: BigDecimal,
        exchangeRateMap: ExchangeRateMap,
    ): List<CbdTreeNode> {
        val materialCosts =
            getFieldRow(
                bomNodeSnapshot,
                entity = manufacturing,
                fieldName = "cO2Material",
                source = CBD_ROW_TOTAL_MATERIAL_COSTS,
                children =
                    getFieldsRecursive(
                        bomNodeSnapshot,
                        manufacturing,
                        Entities.MATERIAL,
                        DrillDownType.MATERIAL,
                        costUnitBaseFactor = costUnitBaseFactor,
                        exchangeRateMap = exchangeRateMap,
                        "cO2PerPart",
                    ) +
                        getFieldsRecursive(
                            bomNodeSnapshot,
                            manufacturing,
                            Entities.CONSUMABLE,
                            DrillDownType.MATERIAL,
                            costUnitBaseFactor = costUnitBaseFactor,
                            exchangeRateMap = exchangeRateMap,
                            "cO2PerPart",
                        ) +
                        getFieldsRecursive(
                            bomNodeSnapshot,
                            manufacturing,
                            Entities.C_PART,
                            DrillDownType.MATERIAL,
                            costUnitBaseFactor = costUnitBaseFactor,
                            exchangeRateMap = exchangeRateMap,
                            "cO2PerPart",
                        ) +
                        getFieldsRecursiveForBomEntryMaterial(
                            bomNodeSnapshot,
                            manufacturing,
                            costUnitBaseFactor = costUnitBaseFactor,
                            exchangeRateMap = exchangeRateMap,
                            "cO2PerPart",
                        ) +
                        listOf(
                            getFieldRow(
                                bomNodeSnapshot,
                                entity = manufacturing,
                                fieldName = "cO2MaterialScrap",
                                source = CBD_ROW_MATERIAL_SCRAP_COSTS,
                                drillDownType = DrillDownType.MATERIAL,
                                costUnitBaseFactor = costUnitBaseFactor,
                                exchangeRateMap = exchangeRateMap,
                            ),
                            getFieldRow(
                                bomNodeSnapshot,
                                entity = manufacturing,
                                fieldName = ManufacturingLegacyFieldNames.MATERIAL_OVERHEAD_CO2.fieldName,
                                source = ManufacturingLegacyFieldNames.MATERIAL_OVERHEAD_COSTS.fieldName,
                                drillDownType = DrillDownType.MATERIAL,
                                costUnitBaseFactor = costUnitBaseFactor,
                                exchangeRateMap = exchangeRateMap,
                            ),
                        ),
                drillDownType = DrillDownType.MATERIAL,
                costUnitBaseFactor = costUnitBaseFactor,
                exchangeRateMap = exchangeRateMap,
            )

        val manufacturingCosts =
            getFieldRow(
                bomNodeSnapshot,
                manufacturing,
                fieldName = "cO2Manufacturing3",
                source = CBD_ROW_TOTAL_MANUFACTURING_COSTS,
                exchangeRateMap = exchangeRateMap,
                children =
                    flattenChildren(entity = manufacturing, entityType = Entities.MANUFACTURING_STEP)
                        .map {
                            getFieldRow(
                                bomNodeSnapshot,
                                entity = it,
                                fieldName = "cO2ManufacturingStep2",
                                children =
                                    getFields(
                                        bomNodeSnapshot,
                                        it,
                                        true,
                                        DrillDownType.MANUFACTURING,
                                        costUnitBaseFactor = costUnitBaseFactor,
                                        exchangeRateMap = exchangeRateMap,
                                        "cO2Manufacturing3SubManufacturing",
                                    ),
                                drillDownType = DrillDownType.MANUFACTURING,
                                costUnitBaseFactor = costUnitBaseFactor,
                                exchangeRateMap = exchangeRateMap,
                            )
                        }.reversed() +

                        getFieldsRecursiveWithFilter(
                            bomNodeSnapshot,
                            manufacturing,
                            Entities.BOM_ENTRY,
                            DrillDownType.MANUFACTURING,
                            { entity -> entity.getFieldResult("procurementType")?.res?.equals(ManufacturingType.INHOUSE) ?: false },
                            costUnitBaseFactor = costUnitBaseFactor,
                            exchangeRateMap = exchangeRateMap,
                            "co2Manufacturing2",
                        ),
                drillDownType = DrillDownType.MANUFACTURING,
                costUnitBaseFactor = costUnitBaseFactor,
            )

        val overheadsList =
            listOf(
                getFieldRow(
                    bomNodeSnapshot,
                    manufacturing,
                    fieldName = "cO2Overhead",
                    source = CBD_ROW_TOTAL_OVERHEAD_COSTS,
                    exchangeRateMap = exchangeRateMap,
                    children =
                        listOf(
                            getFieldRow(
                                bomNodeSnapshot,
                                manufacturing,
                                fieldName =
                                    ValueFieldNameBuilder(
                                        ValueType.CO2,
                                        AggregationLevel.SOLD_MATERIAL,
                                        AggregationRole.TOTAL,
                                        TsetCO2eCalculationElementType.SPECIAL_DIRECT_CO2E.fieldName,
                                    ).fieldName,
                                source = CBD_ROW_OVERHEADS_SPECIAL_DIRECT_COSTS,
                                drillDownType = DrillDownType.ALL,
                                costUnitBaseFactor = costUnitBaseFactor,
                                exchangeRateMap = exchangeRateMap,
                            ),
                            getFieldRow(
                                bomNodeSnapshot,
                                manufacturing,
                                fieldName =
                                    ValueFieldNameBuilder(
                                        ValueType.CO2,
                                        AggregationLevel.SOLD_MATERIAL,
                                        AggregationRole.TOTAL,
                                        TsetCO2eCalculationElementType.OVERHEADS_CO2E_AFTER_P.fieldName,
                                    ).fieldName,
                                source = CBD_ROW_OVERHEADS_AFTER_PC,
                                drillDownType = DrillDownType.ALL,
                                costUnitBaseFactor = costUnitBaseFactor,
                                exchangeRateMap = exchangeRateMap,
                            ),
                            getFieldRow(
                                bomNodeSnapshot,
                                manufacturing,
                                fieldName =
                                    ValueFieldNameBuilder(
                                        ValueType.CO2,
                                        AggregationLevel.SOLD_MATERIAL,
                                        AggregationRole.TOTAL,
                                        TsetCO2eCalculationElementType.PROFIT_CO2E.fieldName,
                                    ).fieldName,
                                source = CBD_ROW_OVERHEADS_CPP_PROFIT,
                                drillDownType = DrillDownType.ALL,
                                costUnitBaseFactor = costUnitBaseFactor,
                                exchangeRateMap = exchangeRateMap,
                            ),
                            getFieldRow(
                                bomNodeSnapshot,
                                manufacturing,
                                fieldName =
                                    ValueFieldNameBuilder(
                                        ValueType.CO2,
                                        AggregationLevel.SOLD_MATERIAL,
                                        AggregationRole.TOTAL,
                                        TsetCO2eCalculationElementType.TERMS_OF_PAYMENT_CO2E.fieldName,
                                    ).fieldName,
                                source = CBD_ROW_OVERHEADS_CPP_TERMS_OF_PAYMENT,
                                drillDownType = DrillDownType.ALL,
                                costUnitBaseFactor = costUnitBaseFactor,
                                exchangeRateMap = exchangeRateMap,
                            ),
                            getFieldRow(
                                bomNodeSnapshot,
                                manufacturing,
                                fieldName =
                                    ValueFieldNameBuilder(
                                        ValueType.CO2,
                                        AggregationLevel.SOLD_MATERIAL,
                                        AggregationRole.TOTAL,
                                        TsetCO2eCalculationElementType.INCO_TERMS_CO2E.fieldName,
                                    ).fieldName,
                                source = CBD_ROW_OVERHEADS_CPP_INCOTERMS,
                                drillDownType = DrillDownType.ALL,
                                costUnitBaseFactor = costUnitBaseFactor,
                                exchangeRateMap = exchangeRateMap,
                            ),
                        ),
                    drillDownType = DrillDownType.ALL,
                    costUnitBaseFactor = costUnitBaseFactor,
                ),
            )

        val specialDirectCosts =
            listOf(
                getFieldRow(
                    bomNodeSnapshot,
                    manufacturing,
                    fieldName = "cO2SpecialDirectCosts",
                    source = CBD_ROW_TOTAL_SPECIAL_DIRECT_COSTS,
                    children =
                        listOf(
                            getFieldRow(
                                bomNodeSnapshot,
                                manufacturing,
                                fieldName = "cO2DevelopmentCosts",
                                source = CBD_ROW_SDC_DEVELOPMENT_COSTS,
                                drillDownType = DrillDownType.ALL,
                                costUnitBaseFactor = costUnitBaseFactor,
                                exchangeRateMap = exchangeRateMap,
                            ),
                            getFieldRow(
                                bomNodeSnapshot,
                                manufacturing,
                                fieldName = "cO2RampUpCosts",
                                source = CBD_ROW_SDC_RAMP_UP_COSTS,
                                drillDownType = DrillDownType.ALL,
                                costUnitBaseFactor = costUnitBaseFactor,
                                exchangeRateMap = exchangeRateMap,
                            ),
                            getFieldRow(
                                bomNodeSnapshot,
                                manufacturing,
                                fieldName = "cO2PackageCarrierCosts",
                                source = CBD_ROW_SDC_PACKAGE_CARRIER_COSTS,
                                drillDownType = DrillDownType.ALL,
                                costUnitBaseFactor = costUnitBaseFactor,
                                exchangeRateMap = exchangeRateMap,
                            ),
                        ),
                    drillDownType = DrillDownType.ALL,
                    costUnitBaseFactor = costUnitBaseFactor,
                    exchangeRateMap = exchangeRateMap,
                ),
            )

        return when (requestedDrillDownType) {
            DrillDownType.ALL -> listOf(materialCosts, manufacturingCosts) + overheadsList + specialDirectCosts
            DrillDownType.MATERIAL -> listOf(materialCosts)
            DrillDownType.MANUFACTURING -> listOf(manufacturingCosts)
        }
    }

    private fun flattenChildren(
        entity: ManufacturingEntity,
        entityType: Entities,
    ): List<ManufacturingEntity> =
        listOfNotNull(entity.takeIf { FilteredBomService.isOfType(entity, entityType) }) +
            entity.children.flatMap { flattenChildren(it, entityType) }

    private fun getFieldRow(
        bomNodeSnapshot: BomNodeSnapshot,
        entity: ManufacturingEntity,
        fieldName: String,
        children: List<CbdTreeNode> = emptyList(),
        source: String? = null,
        drillDownType: DrillDownType,
        costUnitBaseFactor: BigDecimal,
        exchangeRateMap: ExchangeRateMap,
    ): CbdTreeNode =
        getFieldRow(
            bomNodeSnapshot,
            entity,
            fieldName,
            source,
            drillDownType,
            costUnitBaseFactor,
            exchangeRateMap,
        ).copy(children = children)

    private fun getFieldsRecursive(
        bomNodeSnapshot: BomNodeSnapshot,
        entity: ManufacturingEntity,
        entityType: Entities,
        drillDownType: DrillDownType,
        costUnitBaseFactor: BigDecimal,
        exchangeRateMap: ExchangeRateMap,
        vararg fieldNames: String,
    ): List<CbdTreeNode> =
        getFieldsRecursiveWithFilter(bomNodeSnapshot, entity, entityType, drillDownType, {
            true
        }, costUnitBaseFactor, exchangeRateMap = exchangeRateMap, *fieldNames)

    private fun getFieldsRecursiveWithFilter(
        bomNodeSnapshot: BomNodeSnapshot,
        entity: ManufacturingEntity,
        entityType: Entities,
        drillDownType: DrillDownType,
        filterCondition: (ManufacturingEntity) -> Boolean,
        costUnitBaseFactor: BigDecimal,
        exchangeRateMap: ExchangeRateMap,
        vararg fieldNames: String,
    ): List<CbdTreeNode> {
        val resultRow =
            if (FilteredBomService.isOfType(entity, entityType) && filterCondition(entity)) {
                getFields(bomNodeSnapshot, entity, false, drillDownType, costUnitBaseFactor, exchangeRateMap = exchangeRateMap, *fieldNames)
            } else {
                listOf()
            }

        return resultRow +
            entity.children.flatMap {
                getFieldsRecursive(
                    bomNodeSnapshot,
                    it,
                    entityType,
                    drillDownType,
                    costUnitBaseFactor,
                    exchangeRateMap = exchangeRateMap,
                    *fieldNames,
                )
            }
    }

    private fun getFieldsRecursiveForBomEntryMaterial(
        bomNodeSnapshot: BomNodeSnapshot,
        entity: ManufacturingEntity,
        costUnitBaseFactor: BigDecimal,
        exchangeRateMap: ExchangeRateMap,
        vararg fieldNames: String,
    ): List<CbdTreeNode> {
        val resultRow =
            if (FilteredBomService.isOfType(entity, Entities.BOM_ENTRY)) {
                if (entity.getFieldResult("procurementType")?.equals(ManufacturingType.INHOUSE) != false) {
                    getFields(
                        bomNodeSnapshot,
                        entity,
                        false,
                        DrillDownType.MATERIAL,
                        costUnitBaseFactor,
                        exchangeRateMap = exchangeRateMap,
                        *fieldNames,
                    )
                } else {
                    getFields(
                        bomNodeSnapshot,
                        entity,
                        false,
                        DrillDownType.ALL,
                        costUnitBaseFactor,
                        exchangeRateMap = exchangeRateMap,
                        *fieldNames,
                    )
                }
            } else {
                listOf()
            }

        return resultRow +
            entity.children.flatMap {
                getFieldsRecursiveForBomEntryMaterial(
                    bomNodeSnapshot,
                    it,
                    costUnitBaseFactor,
                    exchangeRateMap = exchangeRateMap,
                    *fieldNames,
                )
            }
    }

    private fun getFields(
        bomNodeSnapshot: BomNodeSnapshot,
        entity: ManufacturingEntity,
        useFieldAsLabel: Boolean = false,
        drillDownType: DrillDownType,
        costUnitBaseFactor: BigDecimal,
        exchangeRateMap: ExchangeRateMap,
        vararg fieldNames: String,
    ): List<CbdTreeNode> =
        fieldNames.map { fieldName ->
            getFieldRow(
                bomNodeSnapshot,
                entity,
                fieldName,
                emptyList(),
                if (useFieldAsLabel) fieldName else null,
                drillDownType,
                costUnitBaseFactor,
                exchangeRateMap = exchangeRateMap,
            )
        }

    private fun getFieldRow(
        bomNodeSnapshot: BomNodeSnapshot,
        entity: ManufacturingEntity,
        fieldName: String,
        label: String?,
        drillDownType: DrillDownType,
        costUnitBaseFactor: BigDecimal,
        exchangeRateMap: ExchangeRateMap,
    ): CbdTreeNode {
        val id =
            if (FilteredBomService.isOfType(entity, Entities.BOM_ENTRY)) {
                bomNodeSnapshot.findChildrenByEntityId(entity._id)?.bomNodeId?.toHexString() ?: "UNKNOWN_BOMNODE_ID"
            } else {
                UUID.randomUUID().toString()
            }
        val fieldValue = getField(entity, fieldName, costUnitBaseFactor)
        return CbdTreeNode(
            id = id,
            source = getSource(label, entity),
            cost = exchangeRateMap.eurToCurrencyInfo(fieldValue),
            quantity =
                if (FilteredBomService.isOfType(entity, Entities.BOM_ENTRY)) {
                    getField(entity, "quantity", costUnitBaseFactor)
                } else {
                    null
                },
            drillDownType = drillDownType,
            entityType = entity.getEntityType(),
            entityId = entity.entityId,
            children = emptyList(),
            debug = "${entity.getEntityType()}:${entity.entityId}=$fieldValue",
            displayDesignation = entity.getFieldResult("displayDesignation")?.res as String?,
        )
    }

    private fun getSource(
        label: String?,
        entity: ManufacturingEntity,
    ): String = label ?: getDesignationField(entity)?.res.toString()

    private fun getDesignationField(entity: ManufacturingEntity) =
        entity.getFieldResult("displayDesignation") ?: entity.getFieldResult("entityDesignation")

    fun getField(
        entity: ManufacturingEntity,
        fieldName: String,
        costUnitBaseFactor: BigDecimal,
    ): BigDecimal {
        val fieldResult = entity.getFieldResult(fieldName) ?: return BigDecimal.ZERO

        return when (fieldResult) {
            is Emission -> fieldResult.inKilogramCO2e * costUnitBaseFactor
            is Money -> fieldResult.res * costUnitBaseFactor
            is DynamicQuantityUnit -> fieldResult.toInputUnit()
            is NumericFieldResult<*> -> fieldResult.res
            else -> BigDecimal.ZERO
        }
    }

    companion object {
        private val CBD_TOTAL_THRESHOLD_PCT = 4.0.toBigDecimal()
        private val CBD_TOTAL_THRESHOLD_MULTIPLIER = CBD_TOTAL_THRESHOLD_PCT.times(0.01.toBigDecimal())

        fun cbdTotalThresholdValue(
            totalCost: BigDecimal,
            requestQuantity: BigDecimal = BigDecimal.ONE,
        ): BigDecimal =
            if (requestQuantity == BigDecimal.ZERO) {
                BigDecimal.ZERO
            } else {
                totalCost.times(CBD_TOTAL_THRESHOLD_MULTIPLIER).divide(requestQuantity, 10, RoundingMode.HALF_UP)
            }

        const val CBD_ROW_TOTAL_MATERIAL_COSTS = "materialCosts"
        const val CBD_ROW_TOTAL_MANUFACTURING_COSTS = "Manufacturing"
        const val CBD_ROW_TOTAL_OVERHEAD_COSTS = "Overheads"
        const val CBD_ROW_TOTAL_SPECIAL_DIRECT_COSTS = "SpecialDirectCosts"
        const val CBD_ROW_OTHERS = "Others"

        const val CBD_ROW_MATERIAL_SCRAP_COSTS = "materialScrapCosts"

        const val CBD_ROW_MANUFACTURING_INTEREST_ON_WIP = "interestOnWorkInProgress"

        const val CBD_ROW_OVERHEADS_SPECIAL_DIRECT_COSTS = "costPerPartSpecialDirectCosts"
        const val CBD_ROW_OVERHEADS_AFTER_PC = "costPerPartOverheadsAfterPC"
        const val CBD_ROW_OVERHEADS_CPP_PROFIT = "costPerPartProfit"
        const val CBD_ROW_OVERHEADS_CPP_TERMS_OF_PAYMENT = "costPerPartTermsOfPayment"
        const val CBD_ROW_OVERHEADS_CPP_INCOTERMS = "costPerPartIncoterms"

        const val CBD_ROW_SDC_DEVELOPMENT_COSTS = "developmentCosts"
        const val CBD_ROW_SDC_RAMP_UP_COSTS = "rampUpCosts"
        const val CBD_ROW_SDC_PACKAGE_CARRIER_COSTS = "packageCarrierCosts"
    }
}
