package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.machining.cycletimestep.TurningCycleTimeStepGroup
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.entities.BaseMaterial
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.RawMaterialBar
import com.nu.bom.core.manufacturing.entities.RawMaterialCastingAlloy
import com.nu.bom.core.manufacturing.entities.RawMaterialCoatingPcba
import com.nu.bom.core.manufacturing.entities.RawMaterialCoil
import com.nu.bom.core.manufacturing.entities.RawMaterialIngot
import com.nu.bom.core.manufacturing.entities.RawMaterialInk
import com.nu.bom.core.manufacturing.entities.RawMaterialLamella
import com.nu.bom.core.manufacturing.entities.RawMaterialMetallicCoating
import com.nu.bom.core.manufacturing.entities.RawMaterialPaint
import com.nu.bom.core.manufacturing.entities.RawMaterialPaperCoil
import com.nu.bom.core.manufacturing.entities.RawMaterialPaperSheet
import com.nu.bom.core.manufacturing.entities.RawMaterialPipe
import com.nu.bom.core.manufacturing.entities.RawMaterialPlasticGranulate
import com.nu.bom.core.manufacturing.entities.RawMaterialPowder
import com.nu.bom.core.manufacturing.entities.RawMaterialRareEarth
import com.nu.bom.core.manufacturing.entities.RawMaterialRubber
import com.nu.bom.core.manufacturing.entities.RawMaterialSand
import com.nu.bom.core.manufacturing.entities.RawMaterialSheet
import com.nu.bom.core.manufacturing.entities.RawMaterialVarnish
import com.nu.bom.core.manufacturing.entities.RawMaterialWax
import com.nu.bom.core.manufacturing.entities.RawMaterialWireRod
import com.nu.bom.core.manufacturing.entities.masterdata.MasterdataEntity
import com.nu.bom.core.manufacturing.entities.masterdata.material.MasterdataMaterialClassificationSchemaEntity
import com.nu.bom.core.manufacturing.entities.masterdata.material.MasterdataMaterialClassificationValueEntity
import com.nu.bom.core.manufacturing.entities.masterdata.material.MasterdataMaterialEmissionEntity
import com.nu.bom.core.manufacturing.entities.masterdata.material.MasterdataMaterialParent
import com.nu.bom.core.manufacturing.entities.masterdata.material.MasterdataMaterialPriceEntity
import com.nu.bom.core.manufacturing.entities.masterdata.material.MaterialClassificationExtension
import com.nu.bom.core.manufacturing.entities.masterdata.material.MaterialConsumerExtension
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.EmissionUnits
import com.nu.bom.core.manufacturing.fieldTypes.EntityGeneration
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.MaterialFurnaceType
import com.nu.bom.core.manufacturing.fieldTypes.MdHeaderInfoFieldData
import com.nu.bom.core.manufacturing.fieldTypes.MdLookupRequestField
import com.nu.bom.core.manufacturing.fieldTypes.MdLookupRequestFieldData
import com.nu.bom.core.manufacturing.fieldTypes.MultiSelect
import com.nu.bom.core.manufacturing.fieldTypes.PaintCoatType
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.masterdata.LovFieldDefinition
import com.nu.bom.core.manufacturing.fieldTypes.masterdata.LovFieldSchema
import com.nu.bom.core.manufacturing.masterdata.operationsAndExecutionContext.MasterdataContextOperationConfigBuilders
import com.nu.bom.core.manufacturing.service.FieldKey
import com.nu.bom.core.manufacturing.service.virtualfield.DataSourcerUpdaterProvider
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.InputDependencyModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.publicapi.dtos.configurations.masterdata.HeaderTypeConsumer
import com.nu.bom.core.service.configurations.MasterdataTsetConfigurationService
import com.nu.bom.core.service.configurations.MasterdataTsetConfigurationService.Companion.MATERIAL_HEADER_TYPE_KEY
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MdLazyMigrationUtils
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import com.nu.bom.core.utils.findAllInTree
import com.nu.bom.core.utils.visitTree
import org.bson.types.Decimal128
import org.bson.types.ObjectId
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDate
import kotlin.reflect.KClass
import kotlin.reflect.full.companionObject
import kotlin.reflect.full.companionObjectInstance

@Service
class RawMaterialMdMigration : ManufacturingModelEntityMapper {
    override val changeSetId: MigrationChangeSetId
        get() = MigrationChangeSetId("2025-05-18-rawmaterial-md-migration")

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity {
        val materialWithParent =
            (entity to entity).findAllInTree({ manu -> manu.first.type == Entities.MATERIAL.name }) { parentPair ->
                parentPair.first.children.map { it to parentPair.first }
            }

        val alteredMaterials = mutableListOf<ManufacturingModelEntity>()
        val masterdataEntityConvertor =
            entity
                .findAllInTree({ it.masterDataSelector != null }, { it.children })
                .map {
                    it.masterDataSelector?.key to it.masterDataSelector?.key?.ensureSuffix("-${it.masterDataSelector?.type}")
                }.toMap()
        val updatedEntity = updateMaterialNameFieldsRecursively(entity, alteredMaterials, null, masterdataEntityConvertor)
        return createMdEntitiesForMaterial(updatedEntity, materialWithParent)
    }

    private fun createMdEntitiesForMaterial(
        entity: ManufacturingModelEntity,
        materialWithParent: Sequence<Pair<ManufacturingModelEntity, ManufacturingModelEntity>>,
    ): ManufacturingModelEntity {
        val materialFromMd =
            materialWithParent
                .filter { it.first.masterDataSelector?.key != null }
                .distinctBy { it.first.masterDataSelector }
        if (materialFromMd.none()) {
            return entity
        }

        val existingMdParent = entity.children.find { it.type == Entities.MD_MATERIAL_PARENT.name }

        val updatedChildren =
            if (existingMdParent != null) {
                val mdTimestampField = entity.fieldWithResults[Manufacturing::masterdataTimestamp.name]
                val mdTimestamp = (mdTimestampField?.value as? Decimal128)?.bigDecimalValue()

                val newCostAndEmissionChildren =
                    createMdPriceEntities(materialFromMd, existingMdParent.id, mdTimestamp) +
                        createMdEmissionEntities(materialFromMd, existingMdParent.id, mdTimestamp)

                val updatedMdParent =
                    existingMdParent.copyAll(
                        children = existingMdParent.children + newCostAndEmissionChildren,
                    )

                entity.children.map {
                    if (it.id == existingMdParent.id) updatedMdParent else it
                }
            } else {
                entity.children + createMdParent(entity, materialFromMd)
            }

        return entity.copyAll(children = updatedChildren)
    }

    private fun updateMaterialNameFieldsRecursively(
        entity: ManufacturingModelEntity,
        alteredMaterials: MutableList<ManufacturingModelEntity>,
        manufacturingUpdatedFields: Map<String, FieldResultModel>?,
        masterdataEntityConvertor: Map<String?, String?>,
    ): ManufacturingModelEntity {
        val updatedFields = entity.fieldWithResults.toMutableMap()
        val updatedInitialFields = entity.initialFieldWithResults.toMutableMap()
        val materialNameKey = "materialName"
        val originalField = updatedFields[materialNameKey]
        var isAltered = false

        if (entity.type == Entities.BOM_ENTRY.name && entity.name == "SubManufacturingPrintedCardboardBoxBomEntry") {
            val originalValueOuterLinerWithSuffix =
                (updatedFields["materialKeyOuterLiner"]?.value as? String)
                    ?.ensureSuffix("-${MasterDataType.RAW_MATERIAL_PAPER_SHEET.name}")
            updatedInitialFields["materialKeyOuterLiner"] =
                FieldResultModel(
                    version = entity.version,
                    newVersion = entity.version,
                    type = Text::class.simpleName!!,
                    value = originalValueOuterLinerWithSuffix,
                    source = FieldResult.SOURCE.C.name,
                )
            updatedFields["materialKeyOuterLiner"] =
                FieldResultModel(
                    version = entity.version,
                    newVersion = entity.version,
                    type = Text::class.simpleName!!,
                    value = originalValueOuterLinerWithSuffix,
                    source = FieldResult.SOURCE.C.name,
                )
            isAltered = true
        }

        if (entity.type == Entities.MATERIAL.name) {
            val headerKey = entity.masterDataSelector?.key + "-${entity.masterDataSelector?.type}"

            // COST-85360
            if (entity.masterDataSelector?.type == MasterDataType.RAW_MATERIAL_BAR &&
                updatedFields.containsKey("temperatureDifferenceForging")
            ) {
                updatedInitialFields.putIfAbsent(
                    "temperatureDifferenceForging",
                    updatedFields["temperatureDifferenceForging"]!!,
                )
            }

            // COST-85393
            if (entity.masterDataSelector?.type == MasterDataType.RAW_MATERIAL_PAINT) {
                addPaintCoatTypeField(entity, updatedFields, FieldResult.SOURCE.C)
                addPaintCoatTypeField(entity, updatedInitialFields, FieldResult.SOURCE.I)
            }

            updatedInitialFields["headerKey"] =
                FieldResultModel(
                    version = entity.version,
                    newVersion = entity.version,
                    type = Text::class.simpleName!!,
                    value = headerKey,
                    source = FieldResult.SOURCE.C.name,
                )
            updatedInitialFields["masterDataType"] =
                FieldResultModel(
                    version = entity.version,
                    newVersion = entity.version,
                    type = Text::class.simpleName!!,
                    value = entity.masterDataSelector?.type?.name,
                    source = FieldResult.SOURCE.C.name,
                )
            isAltered = true
        }

        if (originalField != null) {
            val originalValue = originalField.value as? String

            val inferredMaterialType =
                when {
                    entity.masterDataSelector?.type != null -> entity.masterDataSelector?.type
                    originalValue != null -> {
                        val match =
                            entity
                                .visitTree(
                                    func = { node, _ ->
                                        if (node.masterDataSelector?.key == originalValue) node else null
                                    },
                                    children = { it.children },
                                ).firstOrNull()
                        match?.masterDataSelector?.type
                    }
                    else -> null
                }

            if (originalValue != null && inferredMaterialType != null) {
                val suffixedField =
                    originalField.copyAll(value = "$originalValue-$inferredMaterialType", source = FieldResult.SOURCE.C.name)
                updatedFields[materialNameKey] = suffixedField

                // handle isMagnesium for RawMaterialCastingAlloy
                if (entity.masterDataSelector?.type == MasterDataType.RAW_MATERIAL_CASTING_ALLOY) {
                    val magnesiumFieldKey = "isMagnesium"
                    val newMagnesiumFieldKey = RawMaterialCastingAlloy::hasMagnesium.name
                    val magnesiumField = updatedFields.remove(magnesiumFieldKey)

                    if (magnesiumField != null) {
                        val updatedMagnesiumField = magnesiumField.copyAll()
                        updatedFields[newMagnesiumFieldKey] = updatedMagnesiumField
                    }
                }
                isAltered = true
            }

            // This is needed because some fields in manufacturing entities ex. ManufacturingInjection2.density
            // pick up the materialName without the suffix and then cannot resolve from MD
            val entitiesWithMaterialName =
                listOf(
                    Entities.MANUFACTURING.name,
                    Entities.CYCLETIME_STEP_GROUP.name,
                    Entities.PROCESSED_MATERIAL.name,
                )
            if (masterdataEntityConvertor.keys.contains(originalValue) && entity.type in entitiesWithMaterialName) {
                updatedFields["materialName"] =
                    FieldResultModel(
                        version = entity.version,
                        newVersion = entity.version,
                        type = Text::class.simpleName!!,
                        value = masterdataEntityConvertor[originalValue],
                        source = FieldResult.SOURCE.C.name,
                    )
                if (updatedInitialFields["materialName"] != null) {
                    updatedInitialFields["materialName"] =
                        FieldResultModel(
                            version = entity.version,
                            newVersion = entity.version,
                            type = Text::class.simpleName!!,
                            value = masterdataEntityConvertor[originalValue],
                            source = FieldResult.SOURCE.C.name,
                        )
                }
                isAltered = true
            }

            if (entity.name == "ManufacturingPrintedCardboardBox") {
                val originalValueInnerLinerWithSuffix = updatedFields["materialKeyInnerLiner"]?.value
                updatedInitialFields["materialKeyInnerLiner"] =
                    FieldResultModel(
                        version = entity.version,
                        newVersion = entity.version,
                        type = Text::class.simpleName!!,
                        value = masterdataEntityConvertor[originalValueInnerLinerWithSuffix],
                        source = FieldResult.SOURCE.C.name,
                    )
                updatedFields["materialKeyInnerLiner"] =
                    FieldResultModel(
                        version = entity.version,
                        newVersion = entity.version,
                        type = Text::class.simpleName!!,
                        value = masterdataEntityConvertor[originalValueInnerLinerWithSuffix],
                        source = FieldResult.SOURCE.C.name,
                    )

                val originalValueCorrugatedFluteWithSuffix = updatedFields["materialKeyCorrugatedFlute"]?.value
                updatedInitialFields["materialKeyCorrugatedFlute"] =
                    FieldResultModel(
                        version = entity.version,
                        newVersion = entity.version,
                        type = Text::class.simpleName!!,
                        value = masterdataEntityConvertor[originalValueCorrugatedFluteWithSuffix],
                        source = FieldResult.SOURCE.C.name,
                    )
                updatedFields["materialKeyCorrugatedFlute"] =
                    FieldResultModel(
                        version = entity.version,
                        newVersion = entity.version,
                        type = Text::class.simpleName!!,
                        value = masterdataEntityConvertor[originalValueCorrugatedFluteWithSuffix],
                        source = FieldResult.SOURCE.C.name,
                    )
                isAltered = true
            }
        }

        if (entity.type == Entities.CYCLETIME_STEP_GROUP.name &&
            entity.clazz == TurningCycleTimeStepGroup::class.java.simpleName &&
            manufacturingUpdatedFields != null
        ) {
            val manufacturingMaterialNameKeyField = manufacturingUpdatedFields[materialNameKey]
            val materialNameKeyField = updatedFields[materialNameKey]
            if (materialNameKeyField != null && manufacturingMaterialNameKeyField?.value != null) {
                val copiedField =
                    materialNameKeyField.copyAll(value = manufacturingMaterialNameKeyField.value)
                updatedFields[materialNameKey] = copiedField

                isAltered = true
            }
        }

        // Recursively update children
        val updatedChildren =
            entity.children.map { child ->
                updateMaterialNameFieldsRecursively(
                    entity = child,
                    alteredMaterials = alteredMaterials,
                    manufacturingUpdatedFields = manufacturingUpdatedFields ?: updatedFields,
                    masterdataEntityConvertor = masterdataEntityConvertor,
                )
            }

        val updatedEntity =
            if (isAltered) {
                entity
                    .copyAll(
                        fieldWithResults = updatedFields,
                        initialFieldWithResults = updatedInitialFields,
                        children = updatedChildren,
                    ).also { alteredMaterials.add(it) }
            } else if (updatedChildren != entity.children) {
                entity.copyAll(children = updatedChildren)
            } else {
                entity
            }

        return updatedEntity
    }

    private fun createMdParent(
        entity: ManufacturingModelEntity,
        materialWithParent: Sequence<Pair<ManufacturingModelEntity, ManufacturingModelEntity>>,
    ): ManufacturingModelEntity {
        val mdParentId = ObjectId.get()

        val mdTimestampField = entity.fieldWithResults[Manufacturing::masterdataTimestamp.name]
        val mdTimestamp = (mdTimestampField?.value as? Decimal128)?.bigDecimalValue()

        val childrenPriceEntities = createMdPriceEntities(materialWithParent, mdParentId, mdTimestamp)
        val childrenEmissionEntities = createMdEmissionEntities(materialWithParent, mdParentId, mdTimestamp)

        return ManufacturingModelEntity(
            id = mdParentId,
            name = "MasterdataMaterialParentNew",
            type = Entities.MD_MATERIAL_PARENT.name,
            clazz = MasterdataMaterialParent::class.simpleName!!,
            args = emptyMap(),
            fieldWithResults =
                mapOf(
                    MasterdataMaterialParent::createMaterialsPrices.name to
                        createMdCostFactorParentCreateCostFactorField(
                            entity,
                        ),
                    MasterdataMaterialParent::createMaterialsEmissions.name to
                        createMdCostFactorParentCreateCostFactorField(
                            entity,
                        ),
                    MasterdataMaterialParent::masterDataTypeInternal.name to
                        FieldResultModel(
                            version = entity.version,
                            newVersion = entity.version,
                            type = Text::class.java.simpleName,
                            source = FieldResult.SOURCE.C.name,
                            value = MasterDataType.NONE.name,
                        ),
                    MasterdataMaterialParent::entityDesignation.name to
                        FieldResultModel(
                            version = entity.version,
                            newVersion = entity.version,
                            type = Text::class.java.simpleName,
                            source = FieldResult.SOURCE.C.name,
                            value = "MasterdataMaterialParentNew",
                        ),
                    MasterdataMaterialParent::displayDesignation.name to
                        FieldResultModel(
                            version = entity.version,
                            newVersion = entity.version,
                            type = Text::class.java.simpleName,
                            source = FieldResult.SOURCE.C.name,
                            value = "Material",
                        ),
                ),
            initialFieldWithResults = emptyMap(),
            children = childrenPriceEntities + childrenEmissionEntities,
        ).also {
            it.version = entity.version
            it.createdBy =
                FieldKey(
                    "materialParentCreationNew",
                    entity.id.toHexString(),
                    "MANUFACTURING",
                    "MD_MATERIAL_PARENT",
                    entity.version,
                    entity.version,
                    entity.name,
                )
        }
    }

    private fun createMdCostFactorParentCreateCostFactorField(entity: ManufacturingModelEntity): FieldResultModel =
        FieldResultModel(
            version = entity.version,
            newVersion = entity.version,
            type = EntityGeneration::class.java.simpleName,
            source = FieldResult.SOURCE.C.name,
            value = true,
            inputs =
                setOf(
                    InputDependencyModel(
                        MaterialConsumerExtension::location.name,
                        entity.id.toHexString(),
                        entity.version,
                    ),
                    InputDependencyModel(
                        MaterialConsumerExtension::headerKey.name,
                        entity.id.toHexString(),
                        entity.version,
                    ),
                ),
        )

    private fun createMdEmissionEntities(
        materials: Sequence<Pair<ManufacturingModelEntity, ManufacturingModelEntity>>,
        mdParentId: ObjectId,
        mdTimestamp: BigDecimal?,
    ): List<ManufacturingModelEntity> =
        materials
            .map { (material, parentEntity) ->
                val emissionFields =
                    mapOf(
                        MasterdataMaterialEmissionEntity::denominatorUnit.name to
                            createFieldResultModel(
                                parentEntity.version,
                                "Text",
                                material.fieldWithResults["costUnit"]?.value,
                            ),
                        MasterdataMaterialEmissionEntity::numeratorUnit.name to
                            createFieldResultModel(
                                parentEntity.version,
                                "Text",
                                material.fieldWithResults["cO2PerUnit"]?.unit,
                            ),
                        MasterdataMaterialEmissionEntity::value.name to
                            createFieldResultModel(
                                parentEntity.version,
                                "Emission",
                                material.fieldWithResults["cO2PerUnit"]?.value,
                                unit = EmissionUnits.KILOGRAM_CO2E.name,
                            ),
                        MasterdataMaterialEmissionEntity::materialProcessFurnaceType.name to
                            createFieldResultModel(
                                parentEntity.version,
                                "MaterialFurnaceType",
                                material.fieldWithResults["materialProcessFurnaceType"]?.value ?: MaterialFurnaceType.BLAST_FURNACE.res,
                            ),
                    )
                val headerKey = material.masterDataSelector?.key + "-${material.masterDataSelector?.type}"
                ManufacturingModelEntity(
                    id = ObjectId.get(),
                    name = headerKey,
                    type = Entities.MD_MATERIAL_EMISSION.name,
                    clazz = MasterdataMaterialEmissionEntity::class.simpleName!!,
                    args = emptyMap(),
                    children =
                        listOf(
                            createMaterialClassificationSchema(
                                material,
                                parentEntity.version,
                                mdParentId,
                                Entities.MD_MATERIAL_EMISSION.name,
                            ),
                        ),
                    fieldWithResults =
                        createFieldWithResults(
                            parentEntity,
                            material,
                            emissionFields,
                            HeaderTypeConsumer.MATERIAL_CO2,
                            mdTimestamp,
                            "MasterdataMaterialEmissionEntity",
                            false,
                        ),
                    initialFieldWithResults =
                        createFieldWithResults(
                            parentEntity,
                            material,
                            emissionFields,
                            HeaderTypeConsumer.MATERIAL_CO2,
                            mdTimestamp,
                            "MasterdataMaterialEmissionEntity",
                            false,
                        ),
                ).apply {
                    version = parentEntity.version
                    entityRef = headerKey
                    createdBy =
                        FieldKey(
                            name = MasterdataMaterialParent::createMaterialsEmissions.name,
                            entityId = mdParentId.toHexString(),
                            entityType = Entities.MD_MATERIAL_PARENT.name,
                            type = Entities.MD_MATERIAL_EMISSION.name,
                            version = parentEntity.version,
                            newVersion = parentEntity.version,
                            entityRef = "MasterdataMaterialParentNew",
                        )
                }
            }.toList()

    private fun createMaterialClassificationSchema(
        material: ManufacturingModelEntity,
        parentVersion: Int,
        mdParentId: ObjectId,
        createdByEntityType: String,
    ): ManufacturingModelEntity =
        ManufacturingModelEntity(
            id = ObjectId.get(),
            name = "md-material-classification-schema",
            type = Entities.MD_MATERIAL_CLASSIFICATION_SCHEMA.name,
            clazz = MasterdataMaterialClassificationSchemaEntity::class.simpleName!!,
            args = emptyMap(),
            fieldWithResults =
                createMaterialClassificationSchemaFields(parentVersion, material),
            initialFieldWithResults =
                createMaterialClassificationSchemaFields(parentVersion, material),
            children =
                listOf(
                    createMaterialClassificationValue(material, parentVersion, mdParentId),
                ),
        ).apply {
            version = parentVersion
            entityRef = "md-material-classification-schema"
            createdBy =
                FieldKey(
                    name = MaterialClassificationExtension::createClassificationSchemaEntity.name,
                    entityId = mdParentId.toHexString(),
                    entityType = createdByEntityType,
                    type = Entities.MD_MATERIAL_CLASSIFICATION_SCHEMA.name,
                    version = parentVersion,
                    newVersion = parentVersion,
                    entityRef = "MasterdataMaterialParentNew",
                )
        }

    private fun createMaterialClassificationSchemaFields(
        parentVersion: Int,
        material: ManufacturingModelEntity,
    ): Map<String, FieldResultModel> {
        val materialClassificationTypeKey = getMaterialClassificationTypeKey(material)

        val technologies =
            material.fieldWithResults["technology"]
                ?.value
                ?.toString()
                ?.let { MultiSelect(it).res }
                ?.map { Model.valueOf(it.key) }
                ?: emptyList()

        return mapOf(
            MaterialConsumerExtension::headerKey.name to
                createFieldResultModel(
                    parentVersion,
                    "Text",
                    material.masterDataSelector?.key + "-${material.masterDataSelector?.type}",
                ),
            MasterdataMaterialClassificationSchemaEntity::headerTypeKey.name to
                createFieldResultModel(
                    parentVersion,
                    "Text",
                    MATERIAL_HEADER_TYPE_KEY,
                ),
            MasterdataMaterialClassificationSchemaEntity::mdHeaderInfo.name to
                createFieldResultModel(
                    parentVersion,
                    "MdHeaderInfoField",
                    MdHeaderInfoFieldData(
                        classifications =
                            mapOf(
                                MasterdataTsetConfigurationService.createMaterialConfig().materialClassificationTypeKey to
                                    materialClassificationTypeKey,
                                MasterdataTsetConfigurationService.createMaterialConfig().technologyClassificationTypeKey to
                                    technologies.map {
                                        "tset.ref.classification.$it"
                                    },
                            ),
                        fieldDefinitions = getMaterialFieldDefinitions(material),
                    ),
                ),
        )
    }

    private fun addPaintCoatTypeField(
        entity: ManufacturingModelEntity,
        fields: MutableMap<String, FieldResultModel>,
        source: FieldResult.SOURCE,
    ): Map<String, FieldResultModel> {
        val fieldName =
            when (entity.name) {
                "MaterialPaintBase" -> PaintCoatType.Selection.BASE
                "MaterialPaintClear" -> PaintCoatType.Selection.CLEAR
                "MaterialPaintTop" -> PaintCoatType.Selection.TOP
                else -> {
                    return fields
                }
            }

        fields.computeIfAbsent(RawMaterialPaint::paintCoatType.name) {
            FieldResultModel(
                version = entity.version,
                newVersion = entity.version,
                type = PaintCoatType::class.java.simpleName,
                value = fieldName,
                source = source.name,
            )
        }

        return fields
    }

    private fun createMaterialClassificationValue(
        material: ManufacturingModelEntity,
        parentVersion: Int,
        mdParentId: ObjectId,
    ): ManufacturingModelEntity {
        val kClass = materialClass[material.masterDataSelector?.type]
        val companionInstance = kClass?.companionObjectInstance
        val companionKClass = kClass?.companionObject

        val property = companionKClass?.members?.firstOrNull { it.name == "classificationFields" }
        val classificationFields = property?.call(companionInstance) as? Map<String, String>

        val fieldWithResults: Map<String, FieldResultModel> =
            (
                classificationFields
                    ?.mapNotNull { (fieldName, classificationKey) ->
                        material.fieldWithResults[fieldName]?.let { fieldResult ->
                            classificationKey to if (fieldName == "gluePrice") fieldResult.copyAll(type = "Num") else fieldResult
                        }
                    }?.toMap() ?: emptyMap()
            ) + (
                MaterialConsumerExtension::headerKey.name to
                    createFieldResultModel(
                        parentVersion,
                        "Text",
                        (material.masterDataSelector?.key + "-${material.masterDataSelector?.type}"),
                    )
            )

        return ManufacturingModelEntity(
            id = ObjectId.get(),
            name = "md-material-classification-value",
            type = Entities.MD_MATERIAL_CLASSIFICATION_VALUE.name,
            clazz = MasterdataMaterialClassificationValueEntity::class.simpleName!!,
            args = emptyMap(),
            fieldWithResults = fieldWithResults,
            initialFieldWithResults = fieldWithResults,
        ).apply {
            version = parentVersion
            entityRef = "md-material-classification-value"
            createdBy =
                FieldKey(
                    name = MasterdataMaterialClassificationSchemaEntity::createClassificationValueEntity.name,
                    entityId = mdParentId.toHexString(),
                    entityType = Entities.MD_MATERIAL_CLASSIFICATION_SCHEMA.name,
                    type = Entities.MD_MATERIAL_CLASSIFICATION_VALUE.name,
                    version = parentVersion,
                    newVersion = parentVersion,
                    entityRef = "MasterdataMaterialParentNew",
                )
        }
    }

    private fun getMaterialFieldDefinitions(material: ManufacturingModelEntity): List<LovFieldDefinition> =
        fieldDefinitionMap[material.clazz]?.invoke() ?: emptyList()

    private val fieldDefinitionMap: Map<String, () -> List<LovFieldDefinition>> =
        mapOf(
            RawMaterialCoil::class.simpleName!! to ::rawMaterialCoilFieldDefinitions,
            RawMaterialSheet::class.simpleName!! to ::rawMaterialSheetFieldDefinitions,
            RawMaterialCastingAlloy::class.simpleName!! to ::rawMaterialCastingAlloyFieldDefinitions,
            RawMaterialIngot::class.simpleName!! to ::rawMaterialIngotFieldDefinitions,
            RawMaterialLamella::class.simpleName!! to ::rawMaterialLamellaFieldDefinitions,
            RawMaterialPlasticGranulate::class.simpleName!! to ::rawMaterialPlasticGranulateFieldDefinitions,
            RawMaterialRubber::class.simpleName!! to ::rawMaterialRubberFieldDefinitions,
            RawMaterialPipe::class.simpleName!! to ::rawMaterialPipeFieldDefinitions,
            RawMaterialWireRod::class.simpleName!! to ::rawMaterialWireRodFieldDefinitions,
            RawMaterialBar::class.simpleName!! to ::rawMaterialBarFieldDefinitions,
            RawMaterialWax::class.simpleName!! to ::rawMaterialWaxFieldDefinitions,
            RawMaterialPaint::class.simpleName!! to ::rawMaterialPaintFieldDefinitions,
            RawMaterialMetallicCoating::class.simpleName!! to ::rawMaterialMetallicCoatingFieldDefinitions,
            RawMaterialPaperCoil::class.simpleName!! to ::rawMaterialPaperCoilFieldDefinitions,
            RawMaterialPaperSheet::class.simpleName!! to ::rawMaterialPaperSheetFieldDefinitions,
        )

    private fun getMaterialClassificationTypeKey(material: ManufacturingModelEntity): List<String> {
        val type = material.masterDataSelector?.type
        return type?.let { classificationTypeKeyMap[it]?.invoke() } ?: emptyList()
    }

    private val classificationTypeKeyMap: Map<MasterDataType, () -> List<String>> by lazy {
        val rawMaterialClassifications =
            MasterdataTsetConfigurationService.createMaterialConfig().rawMaterialConfiguration.rawMaterialClassifications
        mapOf(
            MasterDataType.RAW_MATERIAL_COIL to { rawMaterialClassifications.coilClassificationKeys },
            MasterDataType.RAW_MATERIAL_SHEET to { rawMaterialClassifications.sheetClassificationKeys },
            MasterDataType.RAW_MATERIAL_CASTING_ALLOY to { rawMaterialClassifications.castingAlloyClassificationKeys },
            MasterDataType.RAW_MATERIAL_INGOT to { rawMaterialClassifications.ingotClassificationKeys },
            MasterDataType.RAW_MATERIAL_LAMELLA to { rawMaterialClassifications.lamellaClassificationKeys },
            MasterDataType.RAW_MATERIAL_PLASTIC_GRANULATE to { rawMaterialClassifications.plasticGranulateClassificationKeys },
            MasterDataType.RAW_MATERIAL_RUBBER to { rawMaterialClassifications.rubberClassificationKeys },
            MasterDataType.RAW_MATERIAL_SAND to { rawMaterialClassifications.sandClassificationKeys },
            MasterDataType.RAW_MATERIAL_PIPE to { rawMaterialClassifications.pipeClassificationKeys },
            MasterDataType.RAW_MATERIAL_WIRE_ROD to { rawMaterialClassifications.wireRodClassificationKeys },
            MasterDataType.RAW_MATERIAL_BAR to { rawMaterialClassifications.barClassificationKeys },
            MasterDataType.RAW_MATERIAL_POWDER to { rawMaterialClassifications.powderClassificationKeys },
            MasterDataType.RAW_MATERIAL_WAX to { rawMaterialClassifications.waxClassificationKeys },
            MasterDataType.RAW_MATERIAL_RARE_EARTH to { rawMaterialClassifications.rareEarthClassificationKeys },
            MasterDataType.RAW_MATERIAL_PAINT to { rawMaterialClassifications.paintClassificationKeys },
            MasterDataType.RAW_MATERIAL_METALLIC_COATING to { rawMaterialClassifications.metallicCoatingClassificationKeys },
            MasterDataType.RAW_MATERIAL_COATING_PCBA to { rawMaterialClassifications.coatingPcbaClassificationKeys },
            MasterDataType.RAW_MATERIAL_INK to { rawMaterialClassifications.inkClassificationKeys },
            MasterDataType.RAW_MATERIAL_PAPER_COIL to { rawMaterialClassifications.paperCoilClassificationKeys },
            MasterDataType.RAW_MATERIAL_PAPER_SHEET to { rawMaterialClassifications.paperSheetClassificationKeys },
            MasterDataType.RAW_MATERIAL_VARNISH to { rawMaterialClassifications.varnishClassificationKeys },
        )
    }

    private val materialClass: Map<MasterDataType, KClass<out ManufacturingEntity>> =
        mapOf(
            MasterDataType.RAW_MATERIAL_COIL to RawMaterialCoil::class,
            MasterDataType.RAW_MATERIAL_SHEET to RawMaterialSheet::class,
            MasterDataType.RAW_MATERIAL_CASTING_ALLOY to RawMaterialCastingAlloy::class,
            MasterDataType.RAW_MATERIAL_INGOT to RawMaterialIngot::class,
            MasterDataType.RAW_MATERIAL_LAMELLA to RawMaterialLamella::class,
            MasterDataType.RAW_MATERIAL_PLASTIC_GRANULATE to RawMaterialPlasticGranulate::class,
            MasterDataType.RAW_MATERIAL_RUBBER to RawMaterialRubber::class,
            MasterDataType.RAW_MATERIAL_SAND to RawMaterialSand::class,
            MasterDataType.RAW_MATERIAL_PIPE to RawMaterialPipe::class,
            MasterDataType.RAW_MATERIAL_WIRE_ROD to RawMaterialWireRod::class,
            MasterDataType.RAW_MATERIAL_BAR to RawMaterialBar::class,
            MasterDataType.RAW_MATERIAL_POWDER to RawMaterialPowder::class,
            MasterDataType.RAW_MATERIAL_WAX to RawMaterialWax::class,
            MasterDataType.RAW_MATERIAL_RARE_EARTH to RawMaterialRareEarth::class,
            MasterDataType.RAW_MATERIAL_PAINT to RawMaterialPaint::class,
            MasterDataType.RAW_MATERIAL_METALLIC_COATING to RawMaterialMetallicCoating::class,
            MasterDataType.RAW_MATERIAL_COATING_PCBA to RawMaterialCoatingPcba::class,
            MasterDataType.RAW_MATERIAL_INK to RawMaterialInk::class,
            MasterDataType.RAW_MATERIAL_PAPER_COIL to RawMaterialPaperCoil::class,
            MasterDataType.RAW_MATERIAL_PAPER_SHEET to RawMaterialPaperSheet::class,
            MasterDataType.RAW_MATERIAL_VARNISH to RawMaterialVarnish::class,
        )

    private fun createMdPriceEntities(
        materials: Sequence<Pair<ManufacturingModelEntity, ManufacturingModelEntity>>,
        mdParentId: ObjectId,
        mdTimestamp: BigDecimal?,
    ): List<ManufacturingModelEntity> =
        materials
            .map { (material, parentEntity) ->
                val priceFields =
                    mapOf(
                        MasterdataMaterialPriceEntity::denominatorUnit.name to
                            createFieldResultModel(
                                parentEntity.version,
                                "Text",
                                material.fieldWithResults["costUnit"]?.value,
                            ),
                        MasterdataMaterialPriceEntity::numeratorCurrency.name to
                            createFieldResultModel(
                                parentEntity.version,
                                "Text",
                                material.fieldWithResults["baseCurrency"]?.value,
                            ),
                        MasterdataMaterialPriceEntity::value.name to
                            createFieldResultModel(
                                parentEntity.version,
                                "Money",
                                when (material.clazz) {
                                    "MaterialLaminationStack" ->
                                        material.fieldWithResults["basePrice"]?.value
                                            ?: material.fieldWithResults["pricePerUnit"]?.value

                                    else -> material.fieldWithResults["pricePerUnit"]?.value
                                },
                            ),
                        MasterdataMaterialPriceEntity::materialProcessFurnaceType.name to
                            createFieldResultModel(
                                parentEntity.version,
                                "MaterialFurnaceType",
                                material.fieldWithResults["materialProcessFurnaceType"]?.value ?: MaterialFurnaceType.BLAST_FURNACE.res,
                            ),
                    )
                val headerKey = material.masterDataSelector?.key + "-${material.masterDataSelector?.type}"
                ManufacturingModelEntity(
                    id = ObjectId.get(),
                    name = headerKey,
                    type = Entities.MD_MATERIAL_PRICE.name,
                    clazz = MasterdataMaterialPriceEntity::class.simpleName!!,
                    args = emptyMap(),
                    children =
                        listOf(
                            createMaterialClassificationSchema(
                                material,
                                parentEntity.version,
                                mdParentId,
                                Entities.MD_MATERIAL_PRICE.name,
                            ),
                        ),
                    fieldWithResults =
                        createFieldWithResults(
                            parentEntity,
                            material,
                            priceFields,
                            HeaderTypeConsumer.MATERIAL_PRICE,
                            mdTimestamp,
                            "MasterdataMaterialPriceEntity",
                            false,
                        ),
                    initialFieldWithResults =
                        createFieldWithResults(
                            parentEntity,
                            material,
                            priceFields,
                            HeaderTypeConsumer.MATERIAL_PRICE,
                            mdTimestamp,
                            "MasterdataMaterialPriceEntity",
                            false,
                        ),
                ).apply {
                    version = parentEntity.version
                    entityRef = headerKey
                    createdBy =
                        FieldKey(
                            name = MasterdataMaterialParent::createMaterialsPrices.name,
                            entityId = mdParentId.toHexString(),
                            entityType = Entities.MD_MATERIAL_PARENT.name,
                            type = Entities.MD_MATERIAL_PRICE.name,
                            version = parentEntity.version,
                            newVersion = parentEntity.version,
                            entityRef = "MasterdataMaterialParentNew",
                        )
                }
            }.toList()

    private fun createFieldResultModel(
        version: Int,
        type: String,
        value: Any?,
        systemValue: Any? = null,
        source: String = FieldResult.SOURCE.C.name,
        unit: String? = null,
    ): FieldResultModel =
        FieldResultModel(
            version = version,
            newVersion = version,
            type = type,
            value = value,
            source = source,
            systemValue = systemValue,
            unit = unit,
        )

    private fun createFieldWithResults(
        parentEntity: ManufacturingModelEntity,
        material: ManufacturingModelEntity,
        fields: Map<String, FieldResultModel>,
        headerConsumer: HeaderTypeConsumer,
        mdTimestamp: BigDecimal?,
        simpleClassName: String,
        initial: Boolean = false,
    ): Map<String, FieldResultModel> {
        val headerKey = material.masterDataSelector?.key + "-${material.masterDataSelector?.type}"
        val dimension =
            material.fieldWithResults["dimension"]?.value ?: material.initialFieldWithResults["dimension"]?.value
        val location =
            parentEntity.fieldWithResults["location"]?.value ?: parentEntity.initialFieldWithResults["location"]?.value
        val locationName =
            parentEntity.fieldWithResults["locationName"]?.value
                ?: parentEntity.initialFieldWithResults["locationName"]?.value
        val behaviourField =
            MdLazyMigrationUtils.createMdBehaviourField(
                parentManufacturing = parentEntity,
                material,
                simpleClassName,
            )

        val lookupField = createMdCostFactorLookupField(material, mdTimestamp, headerConsumer, headerKey)
        val additionalFields =
            mapOf(
                MaterialConsumerExtension::headerKey.name to
                    createFieldResultModel(
                        parentEntity.version,
                        "Text",
                        headerKey,
                    ),
                MaterialConsumerExtension::location.name to
                    createFieldResultModel(
                        parentEntity.version,
                        "Text",
                        location,
                    ),
                MasterdataContextOperationConfigBuilders.LOOKUP_FIELD_NAME to lookupField,
                MasterdataEntity::masterDataTypeInternal.name to
                    FieldResultModel(
                        version = material.version,
                        newVersion = material.version,
                        type = Text::class.java.simpleName,
                        source = FieldResult.SOURCE.C.name,
                        value = MasterDataType.NONE.name,
                    ),
                DataSourcerUpdaterProvider.VIRTUAL_FIELD_NAME to lookupField.copyAll(),
                MasterdataEntity::createBehaviour.name to behaviourField,
                REQUEST_EFFECTIVITY_TSET_REF_FIELD_REGION to
                    createFieldResultModel(
                        parentEntity.version,
                        "Text",
                        location,
                    ),
                REQUEST_EFFECTIVITY_TSET_REF_FIELD_REGION_DISPLAY_NAME to
                    createFieldResultModel(
                        parentEntity.version,
                        "Text",
                        locationName,
                        locationName,
                    ),
                "executeLookup" to
                    createFieldResultModel(
                        parentEntity.version,
                        "Bool",
                        false,
                        null,
                        FieldResult.SOURCE.C.name,
                    ),
                ACTUAL_EFFECTIVITY_TSET_DATA_SOURCE_FIELD to
                    createFieldResultModel(
                        parentEntity.version,
                        "Text",
                        "tset.tset",
                    ),
                ACTUAL_EFFECTIVITY_TSET_DATA_SOURCE_FIELD_DISPLAY_NAME to
                    createFieldResultModel(
                        parentEntity.version,
                        "Text",
                        "Tset",
                    ),
                ACTUAL_EFFECTIVITY_TSET_REF_FIELD_REGION to
                    createFieldResultModel(
                        parentEntity.version,
                        "Null",
                        null,
                    ),
                ACTUAL_EFFECTIVITY_TSET_REF_FIELD_REGION_DISPLAY_NAME to
                    createFieldResultModel(
                        parentEntity.version,
                        "Null",
                        null,
                    ),
                "mdDetailModifier" to createFieldResultModel(parentEntity.version, "Text", "TSET reference data"),
                "mdDetailModificationDate" to createFieldResultModel(parentEntity.version, "Date", LocalDate.now()),
                "dimension" to createFieldResultModel(parentEntity.version, "Dimension", dimension),
            )
        return if (initial) {
            (fields + additionalFields).filterKeys { key ->
                key in
                    setOf(
                        "headerKey",
                        "location",
                        "headerDisplayName",
                        "mdDetailModificationDate",
                        "mdDetailModifier",
                        "value",
                        BaseMaterial::baseCurrency.name,
                        BaseMaterial::dimension.name,
                        MasterdataMaterialPriceEntity::denominatorUnit.name,
                        MasterdataMaterialPriceEntity::numeratorCurrency.name,
                        MasterdataMaterialEmissionEntity::numeratorUnit.name,
                        ACTUAL_EFFECTIVITY_TSET_DATA_SOURCE_FIELD,
                        ACTUAL_EFFECTIVITY_TSET_DATA_SOURCE_FIELD_DISPLAY_NAME,
                    )
            }
        } else {
            fields + additionalFields
        }
    }

    private fun createMdCostFactorLookupField(
        locationEntity: ManufacturingModelEntity,
        mdTimestamp: BigDecimal?,
        headerTypeConsumer: HeaderTypeConsumer,
        headerKey: String,
    ) = FieldResultModel(
        version = locationEntity.version,
        newVersion = locationEntity.version,
        type = MdLookupRequestField::class.java.simpleName,
        source = FieldResult.SOURCE.C.name,
        value =
            MdLookupRequestFieldData(
                strategyKey = "tset.ref.strategy.material",
                headerTypeKey = "tset.ref.header-type.material",
                headerKey = headerKey,
                headerTypeConsumer = headerTypeConsumer,
                effectivities = emptyList(),
                executeLookup = false,
                timestampEpochMillis = mdTimestamp,
            ),
    )

    fun rawMaterialCoilFieldDefinitions() =
        listOf(
            LovFieldDefinition(
                key = "tset.ref.field.anisotropyCoefficient",
                fieldSchema = LovFieldSchema("Anisotropy Coefficient", "tset.ref.lov-type.anisotropyCoefficient"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.density",
                fieldSchema = LovFieldSchema("Density", "tset.ref.lov-type.density"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.sheetMaterialGroup",
                fieldSchema = LovFieldSchema("Sheet Material Group", "tset.ref.lov-type.sheetMaterialGroup"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.poissonRatio",
                fieldSchema = LovFieldSchema("Poisson Ratio", "tset.ref.lov-type.poissonRatio"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.strainHardeningExponent",
                fieldSchema = LovFieldSchema("Strain Hardening Exponent", "tset.ref.lov-type.strainHardeningExponent"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.stressStrainPowerCurveCoefficient",
                fieldSchema =
                    LovFieldSchema(
                        "Stress Strain Power Curve Coefficient",
                        "tset.ref.lov-type.stressStrainPowerCurveCoefficient",
                    ),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.ultimateTensileStrength",
                fieldSchema = LovFieldSchema("Ultimate Tensile Strength", "tset.ref.lov-type.ultimateTensileStrength"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.yieldStress",
                fieldSchema = LovFieldSchema("Yield Stress", "tset.ref.lov-type.yieldStress"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.youngModulus",
                fieldSchema = LovFieldSchema("Young Modulus", "tset.ref.lov-type.youngModulus"),
            ),
        )

    fun rawMaterialBarFieldDefinitions() =
        listOf(
            LovFieldDefinition(
                key = "tset.ref.field.ageingTime",
                fieldSchema = LovFieldSchema("Ageing Time", "tset.ref.lov-type.ageingTime"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.barLength",
                fieldSchema = LovFieldSchema("Bar Length", "tset.ref.lov-type.barLength"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.barProcess",
                fieldSchema = LovFieldSchema("Bar Process", "tset.ref.lov-type.barProcess"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.preheated",
                fieldSchema = LovFieldSchema("Preheated", "tset.ref.lov-type.bool"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.soakingTime",
                fieldSchema = LovFieldSchema("Soaking Time", "tset.ref.lov-type.soakingTime"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.solutionAnnealingTime",
                fieldSchema = LovFieldSchema("Solution Annealing Time", "tset.ref.lov-type.solutionAnnealingTime"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.specificThermalCapacity",
                fieldSchema = LovFieldSchema("Specific Thermal Capacity", "tset.ref.lov-type.specificThermalCapacity"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.surfaceFinish",
                fieldSchema = LovFieldSchema("Surface Finish", "tset.ref.lov-type.surfaceFinish"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.temperatureDifferenceForging",
                fieldSchema = LovFieldSchema("Temperature Difference Forging", "tset.ref.lov-type.temperatureDifferenceForging"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.temperingSoakingTime",
                fieldSchema = LovFieldSchema("Tempering Soaking Time", "tset.ref.lov-type.temperingSoakingTime"),
            ),
        )

    fun rawMaterialSheetFieldDefinitions() =
        listOf(
            LovFieldDefinition(
                key = "tset.ref.field.elongationAtBreak",
                fieldSchema = LovFieldSchema("Elongation At Break", "tset.ref.lov-type.elongationAtBreak"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.sheetMaterialGroup",
                fieldSchema = LovFieldSchema("Sheet Material Group", "tset.ref.lov-type.sheetMaterialGroup"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.rollingType",
                fieldSchema = LovFieldSchema("Rolling Type", "tset.ref.lov-type.rollingType"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.tensileStrength",
                fieldSchema = LovFieldSchema("Tensile Strength", "tset.ref.lov-type.tensileStrength"),
            ),
        )

    fun rawMaterialCastingAlloyFieldDefinitions() =
        listOf(
            LovFieldDefinition(
                key = "tset.ref.field.materialGroup",
                fieldSchema = LovFieldSchema("Material Group", "tset.ref.lov-type.materialGroup"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.ageingTime",
                fieldSchema = LovFieldSchema("Ageing Time", "tset.ref.lov-type.ageingTime"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.temperingSoakingTime",
                fieldSchema = LovFieldSchema("Tempering Soaking Time", "tset.ref.lov-type.temperingSoakingTime"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.solutionAnnealingTime",
                fieldSchema = LovFieldSchema("Solution Annealing Time", "tset.ref.lov-type.solutionAnnealingTime"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.soakingTime",
                fieldSchema = LovFieldSchema("Soaking Time", "tset.ref.lov-type.soakingTime"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.liquidusTemperature",
                fieldSchema = LovFieldSchema("Liquidus Temperature", "tset.ref.lov-type.liquidusTemperature"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.maxCastingTemperature",
                fieldSchema = LovFieldSchema("Max Casting Temperature", "tset.ref.lov-type.maxCastingTemperature"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.maxChillCastingTemperature",
                fieldSchema = LovFieldSchema("Max Chill Casting Temperature", "tset.ref.lov-type.maxChillCastingTemperature"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.minCastingTemperature",
                fieldSchema = LovFieldSchema("Min Casting Temperature", "tset.ref.lov-type.minCastingTemperature"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.minChillCastingTemperature",
                fieldSchema = LovFieldSchema("Min Chill Casting Temperature", "tset.ref.lov-type.minChillCastingTemperature"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.specificThermalCapacity",
                fieldSchema = LovFieldSchema("Specific Thermal Capacity", "tset.ref.lov-type.specificThermalCapacity"),
            ),
        )

    fun rawMaterialIngotFieldDefinitions() =
        listOf(
            LovFieldDefinition(
                key = "tset.ref.field.ageingType",
                fieldSchema = LovFieldSchema("Ageing Time", "tset.ref.lov-type.ageingType"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.caseHardeningPossible",
                fieldSchema = LovFieldSchema("Case Hardening Possible", "tset.ref.lov-type.caseHardeningPossible"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.preheated",
                fieldSchema = LovFieldSchema("Preheated", "tset.ref.lov-type.preheated"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.soaking",
                fieldSchema = LovFieldSchema("Soaking Time", "tset.ref.lov-type.soaking"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.solutionAnnealingTime",
                fieldSchema = LovFieldSchema("Solution Annealing Time", "tset.ref.lov-type.solutionAnnealingTime"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.specificThermalCapacity",
                fieldSchema = LovFieldSchema("Specific Thermal Capacity", "tset.ref.lov-type.specificThermalCapacity"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.temperingSoakingTime",
                fieldSchema = LovFieldSchema("Tempering Soaking Time", "tset.ref.lov-type.temperingSoakingTime"),
            ),
        )

    fun rawMaterialLamellaFieldDefinitions() =
        listOf(
            LovFieldDefinition(
                key = "tset.ref.field.lamellaThickness",
                fieldSchema = LovFieldSchema("Lamella Thickness", "tset.ref.lov-type.lamellaThickness"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.stackingFactor",
                fieldSchema = LovFieldSchema("Stacking Factor", "tset.ref.lov-type.stackingFactor"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.gluePrice",
                fieldSchema = LovFieldSchema("Glue Price", "tset.ref.lov-type.gluePrice"),
            ),
        )

    fun rawMaterialMetallicCoatingFieldDefinitions() =
        listOf(
            LovFieldDefinition(
                key = "tset.ref.field.equivalentWeight",
                fieldSchema = LovFieldSchema("Equivalent Weight", "tset.ref.lov-type.equivalentWeight"),
            ),
        )

    fun rawMaterialPaintFieldDefinitions() =
        listOf(
            LovFieldDefinition(
                key = "tset.ref.field.paintCoatType",
                fieldSchema = LovFieldSchema("Paint Coat Type", "tset.ref.lov-type.paintCoatType"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.flashOffTime",
                fieldSchema = LovFieldSchema("Flash Off Time", "tset.ref.lov-type.flashOffTime"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.hardenerRate",
                fieldSchema = LovFieldSchema("Hardener Rate", "tset.ref.lov-type.hardenerRate"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.spreadingRatePerMicrometerWet",
                fieldSchema = LovFieldSchema("Spreading Rate Per Micrometer Wet", "tset.ref.lov-type.spreadingRatePerMicrometerWet"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.thinnerRate",
                fieldSchema = LovFieldSchema("Thinner Rate", "tset.ref.lov-type.thinnerRate"),
            ),
        )

    fun rawMaterialPaperCoilFieldDefinitions() =
        listOf(
            LovFieldDefinition(
                key = "tset.ref.field.paperQuality",
                fieldSchema = LovFieldSchema("Paper Quality", "tset.ref.lov-type.paperQuality"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.paperCategoryPaperCoil",
                fieldSchema = LovFieldSchema("Paper Category Paper Coil", "tset.ref.lov-type.paperCategoryPaperCoil"),
            ),
        )

    fun rawMaterialPaperSheetFieldDefinitions() =
        listOf(
            LovFieldDefinition(
                key = "tset.ref.field.paperQuality",
                fieldSchema = LovFieldSchema("Paper Quality", "tset.ref.lov-type.paperQuality"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.paperCategoryPaperSheet",
                fieldSchema = LovFieldSchema("Paper Category Paper Sheet", "tset.ref.lov-type.paperCategoryPaperSheet"),
            ),
        )

    fun rawMaterialPipeFieldDefinitions() =
        listOf(
            LovFieldDefinition(
                key = "tset.ref.field.specificThermalCapacity",
                fieldSchema = LovFieldSchema("Specific Thermal Capacity", "tset.ref.lov-type.specificThermalCapacity"),
            ),
        )

    fun rawMaterialPlasticGranulateFieldDefinitions() =
        listOf(
            LovFieldDefinition(
                key = "tset.ref.field.averageInjectionVelocity",
                fieldSchema = LovFieldSchema("Average Injection Velocity", "tset.ref.lov-type.averageInjectionVelocity"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.fillerMaterial",
                fieldSchema = LovFieldSchema("Filler Material", "tset.ref.lov-type.fillerMaterial"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.fillerRatio",
                fieldSchema = LovFieldSchema("Filler Ratio", "tset.ref.lov-type.fillerRatio"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.injectingTemperature",
                fieldSchema = LovFieldSchema("Injecting Temperature", "tset.ref.lov-type.injectingTemperature"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.materialFactor",
                fieldSchema = LovFieldSchema("Material Factor", "tset.ref.lov-type.materialFactor"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.maxInternalMoldPressure",
                fieldSchema = LovFieldSchema("Max Internal Mold Pressure", "tset.ref.lov-type.maxInternalMoldPressure"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.maxMoldTemperature",
                fieldSchema = LovFieldSchema("Max Mold Temperature", "tset.ref.lov-type.maxMoldTemperature"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.minInternalMoldPressure",
                fieldSchema = LovFieldSchema("Min Internal Mold Pressure", "tset.ref.lov-type.minInternalMoldPressure"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.minMoldTemperature",
                fieldSchema = LovFieldSchema("Min Mold Temperature", "tset.ref.lov-type.minMoldTemperature"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.moldSeparationTemperature",
                fieldSchema = LovFieldSchema("Mold Separation Temperature", "tset.ref.lov-type.moldSeparationTemperature"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.moldTemperature",
                fieldSchema = LovFieldSchema("Mold Temperature", "tset.ref.lov-type.moldTemperature"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.shrinkageBehavior",
                fieldSchema = LovFieldSchema("Shrinkage Behavior", "tset.ref.lov-type.shrinkageBehavior"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.thermalDiffusivity",
                fieldSchema = LovFieldSchema("Thermal Diffusivity", "tset.ref.lov-type.thermalDiffusivity"),
            ),
        )

    fun rawMaterialRubberFieldDefinitions() =
        listOf(
            LovFieldDefinition(
                key = "tset.ref.field.rubberType",
                fieldSchema = LovFieldSchema("Rubber Type", "tset.ref.lov-type.rubberType"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.specificThermalCapacity",
                fieldSchema = LovFieldSchema("Specific Thermal Capacity", "tset.ref.lov-type.specificThermalCapacity"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.moldTemperature",
                fieldSchema = LovFieldSchema("Mold Temperature", "tset.ref.lov-type.moldTemperature"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.internalMoldPressure",
                fieldSchema = LovFieldSchema("Internal Mold Pressure", "tset.ref.lov-type.internalMoldPressure"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.heatingTimeFactor",
                fieldSchema = LovFieldSchema("Heating Time Factor", "tset.ref.lov-type.heatingTimeFactor"),
            ),
        )

    fun rawMaterialWaxFieldDefinitions() =
        listOf(
            LovFieldDefinition(
                key = "tset.ref.field.moldTemperature",
                fieldSchema = LovFieldSchema("Mold Temperature", "tset.ref.lov-type.moldTemperature"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.injectingTemperature",
                fieldSchema = LovFieldSchema("Injecting Temperature", "tset.ref.lov-type.injectingTemperature"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.moldSeparationTemperature",
                fieldSchema = LovFieldSchema("Mold Separation Temperature", "tset.ref.lov-type.moldSeparationTemperature"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.thermalDiffusivity",
                fieldSchema = LovFieldSchema("Thermal Diffusivity", "tset.ref.lov-type.thermalDiffusivity"),
            ),
        )

    fun rawMaterialWireRodFieldDefinitions() =
        listOf(
            LovFieldDefinition(
                key = "tset.ref.field.specificThermalCapacity",
                fieldSchema = LovFieldSchema("Specific Thermal Capacity", "tset.ref.lov-type.specificThermalCapacity"),
            ),
            LovFieldDefinition(
                key = "tset.ref.field.preheated",
                fieldSchema = LovFieldSchema("Preheated", "tset.ref.lov-type.preheated"),
            ),
        )

    fun String.ensureSuffix(suffix: String): String = if (this.endsWith(suffix)) this else this + suffix
}
