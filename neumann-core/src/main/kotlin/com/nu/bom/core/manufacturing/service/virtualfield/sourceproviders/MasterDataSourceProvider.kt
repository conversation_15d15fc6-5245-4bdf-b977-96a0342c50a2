package com.nu.bom.core.manufacturing.service.virtualfield.sourceproviders

import com.nu.bom.core.manufacturing.service.Field
import com.nu.bom.core.manufacturing.service.VersionedResult
import com.nu.bom.core.model.AccountMasterData
import com.nu.bom.core.model.MasterData
import com.nu.bom.core.service.MasterDataService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.MasterDataSearchUtils
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.nu.bom.core.utils.component1
import com.nu.bom.core.utils.component2
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux

@Service
class MasterDataSourceProvider(private val masterDataService: MasterDataService) : SourceProvider {
    override fun isSourceProviderFor(source: DataSourceProviders?): Boolean {
        return source == DataSourceProviders.MASTER_DATA_SOURCE
    }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    override fun findSourceEntityData(
        accessCheck: AccessCheck,
        sourceKeys: List<Any>,
    ): Flux<IndexedValue<SourceData>> =
        Flux.fromIterable(sourceKeys)
            .index()
            .flatMap { (idx, sourceKey) ->
                masterDataService.findLatestMasterData(
                    accessCheck,
                    listOfNotNull(MasterDataSearchUtils.masterDataKeyIs(sourceKey as String)),
                ).collectList().map { sourceEntities ->
                    assert(sourceEntities.size == 1) {
                        "The masterdata entity should be unique for the provided key: $sourceKey"
                    }
                    val latestMasterData = sourceEntities.first()
                    val versionInfo =
                        VersionInfo(
                            when (latestMasterData) {
                                is AccountMasterData -> latestMasterData._id
                                is MasterData -> latestMasterData._id
                                else -> error("Invalid masterdata type")
                            },
                            latestMasterData.version,
                        )
                    IndexedValue(idx.toInt(), SourceData(latestMasterData.data, versionInfo))
                }
            }

    override fun updateSelector(
        calculation: Field,
        newSourceDataKey: VersionedResult,
        versionInfo: VersionInfo?,
    ) {
        calculation.entity.masterDataSelector?.let {
            val selector = it.copy(key = (newSourceDataKey.result.res as String))
            calculation.entity.masterDataSelector = selector
        }
        calculation.entity.masterDataObjectId = versionInfo?.id
        calculation.entity.masterDataVersion = versionInfo?.version
    }
}
