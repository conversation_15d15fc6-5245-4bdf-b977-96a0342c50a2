package com.nu.bom.core.manufacturing.fieldTypes.fti

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.KotlinModule
import com.fasterxml.jackson.module.kotlin.readValue
import com.nu.bom.core.config.JacksonConfig
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Json
import com.nu.bom.core.service.fti.ProcessStep
import com.nu.bom.core.service.fti.ProgressiveNestingLayout

open class ListOfProgressiveNestingLayout(res: List<ProgressiveNestingLayout>) :
    FieldResult<List<ProgressiveNestingLayout>, ListOfProgressiveNestingLayout>(res) {
    override fun dbValue(): String {
        return JacksonConfig.staticMapper.writeValueAsString(res)
    }

    constructor(value: ArrayList<ProgressiveNestingLayout>) : this(value.toList())

    constructor(value: String) : this(JacksonConfig.staticMapper.readValue<List<ProgressiveNestingLayout>>(value))
}

open class Stage(res: ProcessStep) :
    FieldResult<ProcessStep, Stage>(res) {
    companion object {
        val mapper: ObjectMapper =
            ObjectMapper().registerModule(KotlinModule())
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
    }

    override fun dbValue(): String {
        return Json.mapper.writeValueAsString(res)
    }

    constructor(value: String) : this(JacksonConfig.staticMapper.readValue<ProcessStep>(value))
}

open class ListOfProcessStep(res: List<ProcessStep>) :
    FieldResult<List<ProcessStep>, ListOfProcessStep>(res) {
    override fun dbValue(): String {
        return JacksonConfig.staticMapper.writeValueAsString(res)
    }

    constructor(value: ArrayList<ProcessStep>) : this(value.toList())

    constructor(value: String) : this(JacksonConfig.staticMapper.readValue<List<ProcessStep>>(value))
}

open class ListOfProcessSubStep(res: List<OperationContentType.Selection>) :
    FieldResult<List<OperationContentType.Selection>, ListOfProcessSubStep>(res) {
    override fun dbValue(): String {
        return JacksonConfig.staticMapper.writeValueAsString(res)
    }

    constructor(value: ArrayList<OperationContentType.Selection>) : this(value.toList())

    constructor(value: String) : this(JacksonConfig.staticMapper.readValue<List<OperationContentType.Selection>>(value))
}
