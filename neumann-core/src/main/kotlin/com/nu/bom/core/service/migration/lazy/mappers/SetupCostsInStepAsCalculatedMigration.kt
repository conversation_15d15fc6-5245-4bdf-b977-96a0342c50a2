package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.commercialcalculation.legacyfieldnames.ManufacturingStepLegacyFieldNames
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import org.springframework.stereotype.Service

@Service
class SetupCostsInStepAsCalculatedMigration : ManufacturingModelEntityMapper {
    override val changeSetId = MigrationChangeSetId("2023-09-19-setup-step-adaption")

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity =
        entity.copyAll(
            initialFieldWithResults = migrateFields(entity.initialFieldWithResults),
            fieldWithResults = migrateFields(entity.fieldWithResults),
        )

    private val setupFieldNames =
        setOf(
            ManufacturingStepLegacyFieldNames.COST_PER_PART_SETUP_LABOR.fieldName,
            ManufacturingStepLegacyFieldNames.COST_PER_PART_SETUP_DIRECT_LABOR.fieldName,
            ManufacturingStepLegacyFieldNames.COST_PER_PART_SETUP_SYSTEM.fieldName,
        )

    private fun migrateFields(fields: Map<String, FieldResultModel>): Map<String, FieldResultModel> =
        fields.mapValues { (fieldName, field) ->
            if (setupFieldNames.contains(fieldName)) {
                field.copyAll(source = FieldResult.SOURCE.C.name)
            } else {
                field
            }
        }
}
