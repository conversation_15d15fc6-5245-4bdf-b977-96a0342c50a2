package com.nu.bom.core.manufacturing.entities

import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Label
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.CustomProcurementTypeHelper
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.executioncontext.fieldtypes.MaterialClassField
import com.nu.bom.core.manufacturing.defaults.NullProvider
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CO2CalculationOperationsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.ConfigIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.CostCalculationOperationsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.CustomProcurementType
import com.nu.bom.core.manufacturing.fieldTypes.MasterdataConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Text
import reactor.core.publisher.Mono
import java.math.BigDecimal

@EntityType(Entities.MANUFACTURING)
open class RoughManufacturing(
    name: String,
) : BaseManufacturing(name) {
    override val extends: ManufacturingEntity = Manufacturing(name)

    @Input(active = false)
    open fun costModuleConfigurationIdentifier(): ConfigIdentifier = ConfigIdentifier(ConfigurationIdentifier.empty())

    // this is not needed, but let's avoid open dependencies
    @Input(active = false)
    open fun configurationTechnology(): Text = Text("")

    @Input
    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun costPerPart(): Money = Money(BigDecimal.ZERO)

    @ReadOnly
    @Input(active = false)
    fun overheadMethod(masterdataConfigurationKey: MasterdataConfigurationKey): Mono<Text> =
        services
            .getConfiguration(masterdataConfigurationKey)
            .map { mdConfig -> Text(mdConfig.overheadMethodConfiguration.noOverheadMethod) }

    // region commercial calculation

    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun purchasePrice(costPerPart: Money): Money = costPerPart

    @Input(active = false)
    @ReadOnly
    @Label("customProcurementTypeName")
    fun customProcurementType(
        costCalculationOperationKey: CostCalculationOperationsConfigurationKey,
        @Default(NullProvider::class)
        cO2CalculationOperationKey: CO2CalculationOperationsConfigurationKey?,
        commercialMaterialType: MaterialClassField,
    ): Mono<CustomProcurementType> =
        CustomProcurementTypeHelper.getCustomProcurementTypeDefault(
            costCalculationOperationKey,
            cO2CalculationOperationKey,
            commercialMaterialType,
            services,
        )

    // endregion
}
