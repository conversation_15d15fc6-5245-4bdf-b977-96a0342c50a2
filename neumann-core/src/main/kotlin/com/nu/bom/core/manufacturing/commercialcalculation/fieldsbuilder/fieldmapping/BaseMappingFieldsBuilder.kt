package com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.fieldmapping

import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.fieldnamebuilders.RateTimeFieldNameBuilder
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.executioncontext.CommercialCalculationOperationConfigBuilders
import com.nu.bom.core.manufacturing.configurablefields.config.ChildRelation
import com.nu.bom.core.manufacturing.configurablefields.config.EntityFieldConfigurations
import com.nu.bom.core.manufacturing.configurablefields.config.ParentRelation
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYears
import com.nu.bom.core.utils.FieldInfo
import com.nu.bom.core.utils.combine
import kotlin.reflect.KClass

sealed class BaseMappingFieldsBuilder(
    staticMappings: Map<KClass<out ManufacturingEntity>, List<BackMapping>>,
) {
    private val staticFieldConfigs = createFieldConfigs(staticMappings)

    fun getFieldConfigs(valueType: ValueType): EntityFieldConfigurations {
        return staticFieldConfigs[valueType] ?: EntityFieldConfigurations()
    }

    fun getFieldInfosForEntity(entityKClass: KClass<out ManufacturingEntity>): Map<String, FieldInfo> {
        val staticBackMappedFields =
            staticFieldConfigs.values.map { config ->
                config.getFieldInfoForEntity(entityKClass)
            }.reduce { acc, map -> acc.combine(map) }

        return staticBackMappedFields
    }

    private fun createFieldConfigs(
        rawMappings: Map<KClass<out ManufacturingEntity>, List<BackMapping>>,
    ): Map<ValueType, EntityFieldConfigurations> {
        val configs: Map<ValueType, EntityFieldConfigurations> =
            ValueType.values()
                .associateWith {
                    EntityFieldConfigurations()
                }

        rawMappings.forEach { (kClass, mappings) ->
            mappings.forEach { mapping ->
                check(mapping.sourceFieldNames.any()) { "Invalid mapping, no source provided!" }

                val operationBuilder = CommercialCalculationOperationConfigBuilders(mapping.valueType)

                val fieldClass = autoDetectFieldType(mapping)

                val fieldConfig = createSingleFieldConfig(mapping, operationBuilder, fieldClass)

                checkNotNull(configs[mapping.valueType]).add(kClass, fieldConfig)
            }
        }

        return configs
    }

    private fun createSingleFieldConfig(
        mapping: BackMapping,
        operationBuilder: CommercialCalculationOperationConfigBuilders,
        fieldClass: KClass<out FieldResult<*, *>>,
    ) = when (autoDetectOperation(mapping)) {
        MappingOperation.ASSIGN -> {
            val parentTarget = mapping.parentTarget
            val relation = if (parentTarget != null) ParentRelation(parentTarget) else null
            operationBuilder.assign(
                targetFieldName = mapping.targetFieldName,
                sourceFieldName = mapping.sourceFieldNames.single(),
                sourceRelation = relation,
                fieldClass = fieldClass,
                metaInfo = mapping.metaInfo,
                availableOutsideTheEngine = true,
            )
        }
        MappingOperation.SUM -> {
            val childTarget = mapping.childTarget
            val relation = if (childTarget != null) ChildRelation(childTarget) else null
            operationBuilder.sumOfFieldNames(
                targetFieldName = mapping.targetFieldName,
                summands =
                    mapping.sourceFieldNames.map {
                        CommercialCalculationOperationConfigBuilders.Summand(it, relation)
                    },
                fieldClass = fieldClass,
                metaInfo = mapping.metaInfo,
                availableOutsideTheEngine = true,
            )
        }
    }

    private fun autoDetectFieldType(mapping: BackMapping): KClass<out FieldResult<*, *>> {
        val rateBuilder = RateTimeFieldNameBuilder.decomposeOrNull(mapping.targetFieldName)
        return rateBuilder?.let {
            when (it.extensionName) {
                RateTimeFieldNameBuilder.ExtensionName.RATE,
                RateTimeFieldNameBuilder.ExtensionName.INTEREST_RATE,
                -> Rate::class

                RateTimeFieldNameBuilder.ExtensionName.TIME,
                -> TimeInYears::class
            }
        } ?: mapping.valueType.fieldClass
    }

    private fun autoDetectOperation(mapping: BackMapping): MappingOperation {
        return when {
            mapping.sourceFieldNames.size == 1 && mapping.childTarget == null -> MappingOperation.ASSIGN
            else -> MappingOperation.SUM
        }
    }
}
