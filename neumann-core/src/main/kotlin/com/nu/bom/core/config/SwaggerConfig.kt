package com.nu.bom.core.config

import com.fasterxml.jackson.annotation.JsonTypeName
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.type.SimpleType
import com.nu.bom.core.publicapi.dtos.configurations.ConfigurationValueDto
import com.nu.bom.core.publicapi.service.ConfigurationMapper
import com.nu.bom.core.service.configurations.TsetConfigurationFactory
import io.swagger.v3.core.converter.AnnotatedType
import io.swagger.v3.core.converter.ModelConverter
import io.swagger.v3.core.converter.ModelConverterContext
import io.swagger.v3.core.converter.ModelConverters
import io.swagger.v3.oas.models.OpenAPI
import io.swagger.v3.oas.models.examples.Example
import io.swagger.v3.oas.models.media.Schema
import jakarta.annotation.PostConstruct
import org.springdoc.core.models.GroupedOpenApi
import org.springdoc.core.properties.AbstractSwaggerUiConfigProperties.SwaggerUrl
import org.springdoc.core.properties.SwaggerUiConfigProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.MediaType
import kotlin.reflect.full.findAnnotation

@Configuration
class SwaggerConfig(
    private val tsetConfigurationFactory: TsetConfigurationFactory,
    private val objectMapper: ObjectMapper,
    private val configMapper: ConfigurationMapper,
) {
    @PostConstruct
    fun customizeConverter() {
        ModelConverters.getInstance().addConverter(::resolveSealedClass)
    }

    @Bean
    fun apis(swaggerUiConfig: SwaggerUiConfigProperties): Set<SwaggerUrl> {
        val swaggerUrlSet: MutableSet<SwaggerUrl> = HashSet()
        val wsResource = SwaggerUrl("Group", "/v1/openapi.yaml", "Documentation")
        swaggerUrlSet.add(wsResource)
        swaggerUiConfig.urls = swaggerUrlSet
        return swaggerUrlSet
    }

    /*
     Unlike the rest of our API documentation, the spec for configurations is not handwritten.
     We decided to generate the spec including schemas and examples (see custom code below).
     This does mean it is not "in the same file" as the rest of our documentation, but given the already *substantial* overhead
     of creating and changing a configuration type, we decided this is the only way to keep schemas and examples reliably up to date.
     */
    @Bean
    fun configurationApi(): GroupedOpenApi =
        GroupedOpenApi.builder()
            .group("configurations")
            .displayName("Configurations")
            .pathsToMatch("/v1/configs/**")
            .addOpenApiCustomizer(::addExampleConfigs)
            .addOpenApiCustomizer(::sortPaths)
            .addOpenApiCustomizer(::setMeta)
            .build()

    private fun schemaRef(name: String) = "#/components/schemas/$name"

    private fun exampleRef(name: String) = "#/components/examples/$name"

    private fun addExampleConfigs(api: OpenAPI) {
        val examples = swaggerExamples()
        examples.entries.forEach { api.components.addExamples(it.key, it.value) }
        // Tailor-made to the existing config endpoints, generalize with annotations if needed
        api.paths.values.asSequence()
            .mapNotNull { it.post?.requestBody?.content?.get(MediaType.APPLICATION_JSON_VALUE) }
            .filter { it.schema.`$ref` == schemaRef(ConfigurationValueDto::class.simpleName!!) }
            .forEach { mediaType ->
                examples.keys.forEach { key -> mediaType.addExamples(key, Example().`$ref`(exampleRef(key))) }
            }
    }

    private fun sortPaths(api: OpenAPI) {
        // String sort order coincides with the order we want:
        // Short paths first, path variables (in {braces}) after static paths
        val sorted = api.paths.entries.sortedBy { it.key }
        api.paths.clear()
        sorted.forEach { api.paths.addPathItem(it.key, it.value) }
    }

    private fun setMeta(api: OpenAPI) {
        api.info.title = "Configuration API definition"
        api.info.version = "v1"
    }

    private fun swaggerExamples() =
        tsetConfigurationFactory
            .getSwaggerExamples()
            .mapValues { (_, v) ->
                val (name, value) = v
                val dto = configMapper.toDto(value)
                Example().summary(name).value(objectMapper.valueToTree(dto))
            }

    // This method makes no claim to be a general-purpose approach to polymorphism w/ OpenAPI.
    // It works with how we do configurations. That's it.
    private fun resolveSealedClass(
        type: AnnotatedType,
        ctx: ModelConverterContext,
        chain: Iterator<ModelConverter>,
    ): Schema<*>? {
        // Defer to default converters to build base schema
        val schema = if (chain.hasNext()) chain.next().resolve(type, ctx, chain) else return null
        val clazz = (type.type as? Class<*>) ?: (type.type as? SimpleType)?.rawClass
        val sealedClass =
            clazz?.kotlin
                // if ref is set, the schema has previously been resolved already
                ?.takeIf { it.isSealed && schema.`$ref` == null }
                ?: return schema

        val discriminator = checkNotNull(schema.discriminator) { "Could not resolve discriminator for $schema" }
        sealedClass.sealedSubclasses.forEach { subClass ->
            val subSchema = ctx.resolve(AnnotatedType(subClass.java))
            val ref = schemaRef(subSchema.name)
            // I'd much rather get this straight from jackson, but I can't for the life of me figure out how
            // Note this does not handle @JsonSubTypes mappings on the parent class
            // Even springdoc's resolver manually resolves jackson annotations
            val typeName =
                checkNotNull(subClass.findAnnotation<JsonTypeName>()) {
                    "Expected ${subClass.qualifiedName} to have a @JsonTypeName annotation"
                }.value
            discriminator.mapping(typeName, ref)
            schema.addOneOfItem(Schema<Any>().`$ref`(ref))
        }

        return schema
    }
}
