package com.nu.bom.core.service.configurations.migrators

import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorWageEntity
import com.nu.bom.core.manufacturing.fieldTypes.Date
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.TsetDefaultSkillType
import com.nu.bom.core.model.configurations.CustomFieldsConfiguration
import com.nu.bom.core.model.configurations.masterdata.CustomFieldInfo
import com.nu.bom.core.model.configurations.masterdata.EffectivityDefinition
import com.nu.bom.core.model.configurations.masterdata.MasterdataConfigurationV5
import com.nu.bom.core.model.configurations.masterdata.MasterdataConfigurationV6
import com.nu.bom.core.publicapi.dtos.configurations.masterdata.EffectivityType
import com.nu.bom.core.publicapi.dtos.configurations.masterdata.HeaderTypeConsumer
import com.nu.bom.core.service.configurations.MasterdataTsetConfigurationService
import com.nu.bom.core.service.configurations.MasterdataTsetConfigurationService.Companion.COST_FACTOR_REGION_FIELD_DEF_KEY
import com.nu.bom.core.service.configurations.MasterdataTsetConfigurationService.Companion.COST_FACTOR_SHIFT_FIELD_DEF_KEY
import com.nu.bom.core.service.configurations.MasterdataTsetConfigurationService.Companion.COST_FACTOR_SKILL_TYPE_FIELD_DEF_KEY
import com.nu.bom.core.service.configurations.MasterdataTsetConfigurationService.Companion.SHIFT_LOV_ENTRY_KEY
import org.springframework.stereotype.Component

@Component
class MdConfigMigratorToV6 : MasterdataConfigMigrator<MasterdataConfigurationV5, MasterdataConfigurationV6> {
    override fun migrate(configurationValue: MasterdataConfigurationV5): MasterdataConfigurationV6 =
        MasterdataConfigurationV6(
            effectivityDefinitions =
                configurationValue.effectivityDefinitions.map(::migrateEffDef) +
                    EffectivityDefinition(
                        fieldDefinitionKey = COST_FACTOR_SHIFT_FIELD_DEF_KEY,
                        fieldType = EffectivityType.LOV,
                        sourceFieldName = Manufacturing::shiftsPerDay.name,
                        valueMapping =
                            mapOf(
                                "1" to "$SHIFT_LOV_ENTRY_KEY-1",
                                "2" to "$SHIFT_LOV_ENTRY_KEY-2",
                                "3" to "$SHIFT_LOV_ENTRY_KEY-3",
                                "4" to "$SHIFT_LOV_ENTRY_KEY-4",
                            ),
                    ) +
                    EffectivityDefinition(
                        fieldDefinitionKey = COST_FACTOR_REGION_FIELD_DEF_KEY,
                        fieldType = EffectivityType.CLASSIFICATION,
                        sourceFieldName = Manufacturing::location.name,
                    ) +
                    EffectivityDefinition(
                        fieldDefinitionKey = COST_FACTOR_SKILL_TYPE_FIELD_DEF_KEY,
                        fieldType = EffectivityType.LOV,
                        sourceFieldName = MasterdataCostFactorWageEntity::skillType.name,
                        // this mapping is needed to be equal to the migrated tset config
                        valueMapping =
                            mapOf(
                                TsetDefaultSkillType.UNSKILLED_WORKER.mdKey to TsetDefaultSkillType.UNSKILLED_WORKER.mdKey,
                                TsetDefaultSkillType.SKILLED_WORKER.mdKey to TsetDefaultSkillType.SKILLED_WORKER.mdKey,
                                TsetDefaultSkillType.SETUP_TECHNICIAN.mdKey to TsetDefaultSkillType.SETUP_TECHNICIAN.mdKey,
                                TsetDefaultSkillType.PRODUCTION_SUPERVISOR.mdKey to TsetDefaultSkillType.PRODUCTION_SUPERVISOR.mdKey,
                                TsetDefaultSkillType.INSPECTOR.mdKey to TsetDefaultSkillType.INSPECTOR.mdKey,
                                TsetDefaultSkillType.PRODUCTION_ENGINEER.mdKey to TsetDefaultSkillType.PRODUCTION_ENGINEER.mdKey,
                                TsetDefaultSkillType.FINANCIAL_ACCOUNTANT.mdKey to TsetDefaultSkillType.FINANCIAL_ACCOUNTANT.mdKey,
                                TsetDefaultSkillType.ADMINISTRATION_PROFESSIONAL.mdKey to
                                    TsetDefaultSkillType.ADMINISTRATION_PROFESSIONAL.mdKey,
                                TsetDefaultSkillType.MIDDLE_MANAGEMENT.mdKey to TsetDefaultSkillType.MIDDLE_MANAGEMENT.mdKey,
                                TsetDefaultSkillType.TECHNICAL_MANAGER.mdKey to TsetDefaultSkillType.TECHNICAL_MANAGER.mdKey,
                                TsetDefaultSkillType.CHIEF_EXECUTIVE_OFFICER.mdKey to TsetDefaultSkillType.CHIEF_EXECUTIVE_OFFICER.mdKey,
                            ),
                    ),
            overheadsConfiguration = configurationValue.overheadsConfiguration,
            interestsConfiguration = configurationValue.interestsConfiguration,
            exchangeRatesConfiguration = configurationValue.exchangeRatesConfiguration,
            overheadMethodConfiguration = configurationValue.overheadMethodConfiguration,
            wageConfiguration =
                MasterdataTsetConfigurationService.createCostFactorLookupConfig(
                    HeaderTypeConsumer.WAGE,
                    MasterdataTsetConfigurationService.costFactorWageEffectivities,
                ),
            laborBurdenConfiguration =
                MasterdataTsetConfigurationService.createCostFactorLookupConfig(
                    HeaderTypeConsumer.LABOR_BURDEN,
                    MasterdataTsetConfigurationService.costFactorShiftEffectivities,
                ),
            electricityPriceConfiguration =
                MasterdataTsetConfigurationService.createCostFactorLookupConfig(
                    HeaderTypeConsumer.ELECTRICITY_PRICE,
                    MasterdataTsetConfigurationService.costFactorEffectivities,
                ),
            naturalGasPriceConfiguration =
                MasterdataTsetConfigurationService.createCostFactorLookupConfig(
                    HeaderTypeConsumer.NATURAL_GAS_PRICE,
                    MasterdataTsetConfigurationService.costFactorEffectivities,
                ),
            electricityEmissionsConfiguration =
                MasterdataTsetConfigurationService.createCostFactorLookupConfig(
                    HeaderTypeConsumer.ELECTRICITY_EMISSIONS,
                    MasterdataTsetConfigurationService.costFactorEffectivities,
                ),
            naturalGasEmissionsConfiguration =
                MasterdataTsetConfigurationService.createCostFactorLookupConfig(
                    HeaderTypeConsumer.NATURAL_GAS_EMISSIONS,
                    MasterdataTsetConfigurationService.costFactorEffectivities,
                ),
            floorSpacePriceConfiguration =
                MasterdataTsetConfigurationService.createCostFactorLookupConfig(
                    HeaderTypeConsumer.FLOOR_SPACE_PRICE,
                    MasterdataTsetConfigurationService.costFactorEffectivities,
                ),
            costFactorsInterestConfiguration =
                MasterdataTsetConfigurationService.createCostFactorLookupConfig(
                    HeaderTypeConsumer.COSTFACTOR_INTEREST,
                    MasterdataTsetConfigurationService.costFactorEffectivities,
                ),
            aluminiumShareConfiguration =
                MasterdataTsetConfigurationService.createCostFactorLookupConfig(
                    HeaderTypeConsumer.ALUMINIUM_SHARE,
                    MasterdataTsetConfigurationService.costFactorEffectivities,
                ),
            aluminiumEmissionConfiguration =
                MasterdataTsetConfigurationService.createCostFactorLookupConfig(
                    HeaderTypeConsumer.ALUMINIUM_EMISSIONS,
                    MasterdataTsetConfigurationService.costFactorEffectivities,
                ),
            castExcipientsPriceConfiguration =
                MasterdataTsetConfigurationService.createCostFactorLookupConfig(
                    HeaderTypeConsumer.CAST_EXCIPIENTS,
                    MasterdataTsetConfigurationService.costFactorEffectivities,
                ),
            oxygenPriceConfiguration =
                MasterdataTsetConfigurationService.createCostFactorLookupConfig(
                    HeaderTypeConsumer.OXYGEN_PRICE,
                    MasterdataTsetConfigurationService.costFactorEffectivities,
                ),
            countryIdConfiguration =
                MasterdataTsetConfigurationService.createCostFactorLookupConfig(
                    HeaderTypeConsumer.COUNTRY_INFO,
                    MasterdataTsetConfigurationService.costFactorEffectivities,
                ),
            regionConfiguration = MasterdataTsetConfigurationService.regionConfiguration,
            costFactorsConfiguration = MasterdataTsetConfigurationService.costFactorsConfiguration,
        )

    private fun migrateEffDef(def: EffectivityDefinition): EffectivityDefinition =
        def
            .takeUnless {
                it.sourceFieldName.startsWith(CustomFieldsConfiguration.CUSTOM_FIELD_PREFIX) &&
                    it.customFieldInfo == null
            }
            ?: def.copy(customFieldInfo = customFieldInfo(def))

    private fun customFieldInfo(def: EffectivityDefinition): CustomFieldInfo {
        val displayName = def.sourceFieldName.removePrefix(CustomFieldsConfiguration.CUSTOM_FIELD_PREFIX)
        return when (def.fieldType) {
            EffectivityType.LOV -> CustomFieldInfo.Lov(displayName)
            EffectivityType.DATE -> CustomFieldInfo.Cost(displayName, Date::class.simpleName!!, null)
            EffectivityType.NUMERIC -> CustomFieldInfo.Cost(displayName, Num::class.simpleName!!, null)
            else -> error("this should not exist - ${def.fieldType} are not yet supported")
        }
    }

    override fun sourceClass() = MasterdataConfigurationV5::class

    override fun targetClass() = MasterdataConfigurationV6::class
}
