package com.nu.bom.core.publicapi.controller

import com.nu.bom.core.publicapi.dtos.ProjectCreation
import com.nu.bom.core.publicapi.dtos.ProjectDetails
import com.nu.bom.core.publicapi.dtos.PublicAPIPaginatedResponse
import com.nu.bom.core.publicapi.service.PublicAPIProjectService
import com.nu.bom.core.user.AccessCheckProvider
import com.nu.bomrads.id.FolderId
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Mono

@RestController
@RequestMapping("/v1/projects")
class PublicAPIProjectController(
    private val accessCheckProvider: AccessCheckProvider,
    private val publicAPIProjectService: PublicAPIProjectService,
) {
    @GetMapping("")
    fun getDefault(
        @AuthenticationPrincipal jwt: Jwt?,
        @RequestParam(required = false) folderId: String?,
    ): Mono<PublicAPIPaginatedResponse<ProjectDetails>> =
        accessCheckProvider.doAs(jwt) { accessCheck ->
            publicAPIProjectService.list(accessCheck, folderId = folderId?.let { FolderId(it) })
        }

    @PostMapping("")
    fun create(
        @AuthenticationPrincipal jwt: Jwt?,
        @RequestBody dto: ProjectCreation,
    ): Mono<ProjectDetails> =
        accessCheckProvider.doAs(jwt) { accessCheck ->
            publicAPIProjectService.create(accessCheck, dto)
        }

    // For update endoint, if it ever here, make sure to forbid empty key. Otherwise, it'll be regenerated
    // on a project name update, for example.

    @GetMapping("/{projectKey}")
    fun getByKey(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable projectKey: String,
    ): Mono<ProjectDetails> =
        accessCheckProvider.doAs(jwt) { accessCheck ->
            publicAPIProjectService.getByKey(accessCheck, projectKey)
        }

    @DeleteMapping("/{projectKey}")
    fun delete(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable projectKey: String,
    ): Mono<Void> =
        accessCheckProvider.doAs(jwt) { accessCheck ->
            publicAPIProjectService.delete(accessCheck, projectKey).then()
        }

    @GetMapping("/{projectKey}/exists")
    fun checkIfProjectExistsByKey(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable projectKey: String,
    ): Mono<Boolean> =
        accessCheckProvider.doAs(jwt) { accessCheck ->
            publicAPIProjectService.checkIfProjectExistsByKey(accessCheck, projectKey)
        }
}
