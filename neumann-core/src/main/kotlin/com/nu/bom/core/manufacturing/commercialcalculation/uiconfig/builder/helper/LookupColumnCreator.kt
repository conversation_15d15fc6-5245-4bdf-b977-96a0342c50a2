package com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.helper

import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.UniqueOperationIdentifier
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.FieldName
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityLocator
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableColumnDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.LocatorType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.LookupCellFeDto

data class LookupColumnCreator(
    override val columnDefinition: FieldTableColumnDefinitionFeDto,
    val fieldNameBuilder: (id: UniqueOperationIdentifier) -> FieldName?,
    val entityLocator: (id: UniqueOperationIdentifier) -> EntityLocator? =
        { EntityLocator(type = LocatorType.SELF) },
) : ColumnCreator {
    override fun defineCell(id: UniqueOperationIdentifier) =
        fieldNameBuilder(id)?.let { fieldName ->
            entityLocator(id)?.let { locator ->
                LookupCellFeDto(
                    columnDefinition.id,
                    locator,
                    fieldName,
                )
            }
        }
}
