package com.nu.bom.core.service.file

import com.nu.bom.core.exception.ErrorCodedException.Companion.internalServerError
import com.nu.bom.core.exception.readable.ErrorCode
import com.nu.bom.core.exception.readable.ErrorCode.CONTAMINATED_FILE
import com.nu.bom.core.exception.userException.ResourceNotFoundException
import com.nu.bom.core.exception.userException.UnsupportedMediaTypeException
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.model.toBomNodeId
import com.nu.bom.core.model.toFileUploadId
import com.nu.bom.core.model.toMongoID
import com.nu.bom.core.model.toUUID
import com.nu.bom.core.service.bomrads.BomradsFileUploadService
import com.nu.bom.core.service.file.module.UpdateMode
import com.nu.bom.core.service.file.module.UploadModule
import com.nu.bom.core.threedb.ThreeDbFieldType
import com.nu.bom.core.threedb.ThreeDbHeader
import com.nu.bom.core.threedb.ThreeDbService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.user.UserService
import com.nu.bom.core.utils.exceptionOnEmpty
import com.nu.bom.core.utils.then
import com.nu.bom.core.utils.uuidIsValid
import com.nu.bomrads.dto.FileUploadDto
import com.nu.bomrads.dto.OwnerType
import com.nu.bomrads.enumeration.MigratedEnum
import com.nu.bomrads.id.FileUploadId
import com.tset.bom.clients.VirusScanner
import org.bson.types.ObjectId
import org.slf4j.LoggerFactory
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.util.function.component1
import reactor.kotlin.core.util.function.component2
import reactor.util.function.Tuple2
import java.io.File
import java.util.Optional
import kotlin.jvm.optionals.getOrNull

class SecureFileServiceImpl(
    private val s3FileService: S3CloudAwareFileService,
    private val virusScanner: VirusScanner,
    private val mediaTypeDetector: MediaTypeDetectionService,
    private val userService: UserService,
    private val bomradsFileUploadService: BomradsFileUploadService,
    private val threeDbService: ThreeDbService,
    uploadModules: List<UploadModule>,
) : SecureFileService {
    private val uploadModules: Map<UploadType, UploadModule> =
        uploadModules
            .associateBy { it.getUploadType() }
            .also { uploadModuleMap ->
                check(uploadModuleMap.size == uploadModules.size) {
                    uploadModules
                        .map { it.getUploadType() }
                        .minus(uploadModuleMap.keys)
                        .distinct()
                        .let {
                            "Invalid configuration: only one module per upload type is allowed $it."
                        }
                }
            }

    companion object {
        val LOG = LoggerFactory.getLogger(SecureFileServiceImpl::class.java)!!

        private val FILE_ENDINGS_3D =
            listOf(
                "stl",
                "stp",
                "step",
                "prt",
                "catpart",
                "igs",
                "jt",
            )

        private fun isThreeDimensional(filename: String): Boolean = FILE_ENDINGS_3D.contains(File(filename).extension.lowercase())
    }

    private fun uploadThreeDimensional(
        accessCheck: AccessCheck,
        filename: String,
        payload: ByteArray,
    ) = threeDbService
        .createPart(accessCheck)
        .flatMap { threeDbVersionedPart ->
            threeDbService
                .setBinaryResource(
                    ThreeDbHeader(accessCheck, threeDbVersionedPart),
                    "original_file",
                    payload,
                ).flatMap { threeDbVersionedPart2 ->
                    threeDbService.setResource(
                        ThreeDbHeader(accessCheck, threeDbVersionedPart2),
                        ThreeDbFieldType.Resource.FILE_FORMAT,
                        Text(File(filename).extension.lowercase()),
                    )
                }
        }

    override fun upload(
        accessCheck: AccessCheck,
        uploadType: UploadType,
        ownerId: String?,
        file: UploadablePayload,
    ): Mono<UploadResponse> =
        checkUploadType(uploadType)
            .then(getOwner(accessCheck, uploadType, ownerId))
            .flatMap { (ownerType, ownerId) ->
                LOG.info(
                    "Uploading {} filename={}, ownerType={}, ownerId={}",
                    uploadType,
                    file.getFilename(),
                    ownerType,
                    ownerId,
                )
                checkOwnerAccessibility(accessCheck, uploadType, ownerType, ownerId)
                    .then(loadFile(uploadType, file))
                    .flatMap {
                        checkMimeType(it.mimeType, it.uploadType, it.filename).thenReturn(it)
                    }.flatMap(::performVirusScan)
                    .flatMap { scanned ->
                        s3FileService
                            .upload(scanned.payload, accessCheck)
                            .flatMap { uploadId ->
                                bomradsFileUploadService
                                    .save(
                                        accessCheck,
                                        FileUploadDto(
                                            id = ObjectId().toFileUploadId(),
                                            uploadType = uploadType.name,
                                            uploadId = uploadId,
                                            ownerType = ownerType,
                                            ownerId =
                                                if (ownerType == OwnerType.USER) {
                                                    ownerId
                                                } else {
                                                    ownerId.toMongoID().toBomNodeId().idToString()
                                                },
                                            filename = scanned.filename,
                                            mimeType = scanned.mimeType,
                                            migrated = MigratedEnum.CREATED,
                                        ),
                                    ).onErrorResume { throwable ->
                                        s3FileService
                                            .delete(uploadId, "")
                                            .flatMap { Mono.error(throwable) }
                                    }.flatMap { fileUpload ->
                                        val threeDbMono =
                                            if (isThreeDimensional(fileUpload.filename)) {
                                                uploadThreeDimensional(
                                                    accessCheck,
                                                    fileUpload.filename,
                                                    scanned.payload,
                                                ).map { Optional.of(it) }
                                            } else {
                                                Mono.just(Optional.empty())
                                            }
                                        threeDbMono.map { fileUpload to it }
                                    }
                            }
                    }
            }.flatMap { (fileUpload, threeDb) -> updateOwnerResource(accessCheck, fileUpload, UpdateMode.ATTACH).map { it to threeDb } }
            .map { (fileUpload, threeDb) ->
                UploadResponse(
                    id =
                        fileUpload.id
                            .idToString()
                            .toMongoID()
                            .toHexString(),
                    uploadType = fileUpload.uploadType,
                    filename = fileUpload.filename,
                    mimeType = fileUpload.mimeType,
                    createdBy = userService.getUserById(accessCheck, fileUpload.createdBy),
                    createdDate = fileUpload.createdDate!!.toInstant(),
                    threeDbVersionedPart = threeDb.getOrNull(),
                )
            }

    override fun createFileUploadFromS3uuid(
        accessCheck: AccessCheck,
        s3uuid: String,
        fileName: String,
        uploadType: UploadType,
        mimeType: String,
        ownerId: String?,
    ): Mono<UploadResponse> =
        checkUploadType(uploadType)
            .then(getOwner(accessCheck, uploadType, ownerId))
            .flatMap { (ownerType, ownerId) ->
                LOG.info(
                    "Generating FileUploadId {} s3uuid={}, ownerType={}, ownerId={}",
                    uploadType,
                    s3uuid,
                    ownerType,
                    ownerId,
                )
                checkOwnerAccessibility(accessCheck, uploadType, ownerType, ownerId)
                    .then(
                        Mono.defer {
                            bomradsFileUploadService.save(
                                accessCheck,
                                FileUploadDto(
                                    id = ObjectId().toFileUploadId(),
                                    uploadType = uploadType.name,
                                    uploadId = s3uuid,
                                    ownerType = ownerType,
                                    ownerId =
                                        if (ownerType == OwnerType.USER) {
                                            ownerId
                                        } else {
                                            ownerId
                                                .toMongoID()
                                                .toBomNodeId()
                                                .idToString()
                                        },
                                    filename = fileName,
                                    mimeType = mimeType,
                                    migrated = MigratedEnum.CREATED,
                                ),
                            )
                        },
                    ).flatMap { updateOwnerResource(accessCheck, it, UpdateMode.ATTACH) }
                    .map { fileUpload ->
                        UploadResponse(
                            id =
                                fileUpload.id
                                    .idToString()
                                    .toMongoID()
                                    .toHexString(),
                            uploadType = fileUpload.uploadType,
                            filename = fileUpload.filename,
                            mimeType = fileUpload.mimeType,
                            createdBy = userService.getUserById(accessCheck, fileUpload.createdBy),
                            createdDate = fileUpload.createdDate!!.toInstant(),
                            threeDbVersionedPart = null,
                        )
                    }
            }

    override fun save(
        uploadType: UploadType,
        file: UploadablePayload,
        accessCheck: AccessCheck,
    ): Mono<String> =
        checkUploadType(uploadType)
            .then(getOwner(accessCheck, uploadType, ownerId = null))
            .flatMap { (ownerType, ownerId) ->
                LOG.info(
                    "Saving {} filename={}, ownerType={}, ownerId={}",
                    uploadType,
                    file.getFilename(),
                    ownerType,
                    ownerId,
                )

                checkOwnerAccessibility(accessCheck, uploadType, ownerType, ownerId)
                    .then(loadFile(uploadType, file))
                    .flatMap {
                        checkMimeType(it.mimeType, it.uploadType, it.filename).thenReturn(it)
                    }.flatMap(::performVirusScan)
                    .flatMap { scanned ->
                        s3FileService.uploadAndContinueIfExists(scanned.payload, file.getFilename(), accessCheck)
                    }
            }.flatMap { Mono.just(it) }

    override fun download(
        accessCheck: AccessCheck,
        id: String,
    ): Mono<DownloadResponse> {
        val fileUploadId = FileUploadId(id.toUUID())

        LOG.debug("Downloading {}", fileUploadId.idToString())

        return getFileUpload(accessCheck, fileUploadId)
            .exceptionOnEmpty(ResourceNotFoundException(fileUploadId.idToString()))
            .flatMap { fileUpload ->
                checkOwnerAccessibility(accessCheck, fileUpload)
                    .then(
                        // legacy path
                        if (fileUpload.migrated == null) {
                            s3FileService
                                .download(fileUpload.uploadId, accessCheck)
                                .map { payload ->
                                    DownloadResponse(
                                        id =
                                            fileUpload.id
                                                .idToString()
                                                .toMongoID()
                                                .toHexString(),
                                        uploadType = fileUpload.uploadType,
                                        filename = fileUpload.filename,
                                        mimeType = fileUpload.mimeType,
                                        createdBy = userService.getUserById(accessCheck, fileUpload.createdBy),
                                        createdDate = fileUpload.createdDate!!.toInstant(),
                                        payload = payload,
                                    )
                                }
                        } else {
                            // new path
                            s3FileService
                                .download(fileUpload.uploadId, accessCheck)
                                .map { payload ->
                                    DownloadResponse(
                                        id =
                                            fileUpload.id
                                                .idToString()
                                                .toMongoID()
                                                .toHexString(),
                                        uploadType = fileUpload.uploadType,
                                        filename = fileUpload.filename,
                                        mimeType = fileUpload.mimeType,
                                        createdBy = userService.getUserById(accessCheck, fileUpload.createdBy),
                                        createdDate = fileUpload.createdDate!!.toInstant(),
                                        payload = payload,
                                        migrated = fileUpload.migrated,
                                    )
                                }
                        },
                    )
            }
    }

    override fun get(
        accessCheck: AccessCheck,
        id: FileUploadId,
    ): Mono<FileUploadDto> = getFileUpload(accessCheck, id)

    override fun list(
        accessCheck: AccessCheck,
        uploadType: UploadType,
        ownerId: String,
    ): Flux<DownloadListResponse> =
        checkUploadType(uploadType)
            .then(getOwner(accessCheck, uploadType, ownerId))
            .flatMapMany { (ownerType, ownerId) ->
                LOG.info("Listing {} uploads of {}={}", uploadType, ownerType, ownerId)
                checkOwnerAccessibility(accessCheck, uploadType, ownerType, ownerId)
                    .thenMany(
                        bomradsFileUploadService.findByOwnerIdAndUploadType(accessCheck, ownerId, uploadType),
                    )
            }.map { fileUpload ->
                DownloadListResponse(
                    id =
                        fileUpload.id
                            .idToString()
                            .toMongoID()
                            .toHexString(),
                    uploadType = fileUpload.uploadType,
                    filename = fileUpload.filename,
                    mimeType = fileUpload.mimeType,
                    createdBy = userService.getUserById(accessCheck, fileUpload.createdBy),
                    createdDate = fileUpload.createdDate!!.toInstant(),
                    migrated = fileUpload.migrated,
                )
            }

    override fun delete(
        accessCheck: AccessCheck,
        id: String,
    ): Mono<Void> {
        val fileUploadId = FileUploadId(id.toUUID())
        LOG.info("Deleting {}", fileUploadId.idToString())

        return getFileUpload(accessCheck, fileUploadId)
            .exceptionOnEmpty(ResourceNotFoundException(fileUploadId.idToString()))
            .flatMap { fileUpload ->
                checkOwnerAccessibility(accessCheck, fileUpload).thenReturn(fileUpload)
            }.flatMap { fileUpload ->
                updateOwnerResource(accessCheck, fileUpload, UpdateMode.DETACH)
            }.flatMap { fileUpload ->
                s3FileService.delete(fileUpload.uploadId, accessCheck.accountName)
            }.flatMap {
                bomradsFileUploadService.delete(accessCheck, fileUploadId)
            }.then()
    }

    override fun patch(
        accessCheck: AccessCheck,
        ids: List<String>,
        ownerId: String,
    ): Flux<UploadResponse> =
        Flux.concat(
            ids.map { id ->
                changeOwner(accessCheck, id = id, ownerId = ownerId)
            },
        )

    override fun changeOwner(
        accessCheck: AccessCheck,
        id: String,
        ownerId: String,
    ): Mono<UploadResponse> =
        Mono
            .fromCallable { FileUploadId(id.toUUID()) }
            .flatMap { fileUploadId ->
                getFileUpload(accessCheck, fileUploadId)
                    .exceptionOnEmpty(ResourceNotFoundException(fileUploadId.idToString()))
                    .flatMap { fileUpload ->
                        checkOwnerAccessibility(accessCheck, fileUpload)
                            .then(getOwner(accessCheck, UploadType.valueOf(fileUpload.uploadType), ownerId))
                            .map { (newOwnerType, newOwnerId) ->
                                fileUpload.setOwner(
                                    ownerId = newOwnerId,
                                    ownerType = newOwnerType,
                                )
                            }.flatMap { modifiedFileUpload ->
                                // check if the new owner is accessible
                                LOG.info(
                                    "Changing owner of {} {} to {}={}",
                                    fileUpload.uploadType,
                                    fileUploadId.idToString(),
                                    modifiedFileUpload.ownerType,
                                    modifiedFileUpload.ownerId,
                                )
                                updateOwnerResource(accessCheck, fileUpload, UpdateMode.DETACH)
                                    .then(bomradsFileUploadService.save(accessCheck, modifiedFileUpload))
                            }.flatMap { modifiedFileUpload ->
                                checkOwnerAccessibility(accessCheck, modifiedFileUpload)
                                    .then {
                                        updateOwnerResource(accessCheck, modifiedFileUpload, UpdateMode.ATTACH)
                                    }.map { updatedFileUpload ->
                                        UploadResponse(
                                            id =
                                                updatedFileUpload.id
                                                    .idToString()
                                                    .toMongoID()
                                                    .toHexString(),
                                            uploadType = updatedFileUpload.uploadType,
                                            filename = updatedFileUpload.filename,
                                            mimeType = updatedFileUpload.mimeType,
                                            createdBy =
                                                userService.getUserById(
                                                    accessCheck,
                                                    updatedFileUpload.createdBy,
                                                ),
                                            createdDate = updatedFileUpload.createdDate!!.toInstant(),
                                            threeDbVersionedPart = null,
                                        )
                                    }
                            }
                    }
            }

    override fun copy(
        accessCheck: AccessCheck,
        id: String,
        ownerId: String,
        uploadType: String?,
    ): Mono<UploadResponse> {
        val fileUploadId = FileUploadId(id.toUUID())

        LOG.info("Copying {}", fileUploadId.idToString())

        return getFileUpload(accessCheck, fileUploadId)
            .exceptionOnEmpty(ResourceNotFoundException(fileUploadId.idToString()))
            .flatMap { fileUpload ->
                checkOwnerAccessibility(accessCheck, fileUpload)
                    .then(getOwner(accessCheck, UploadType.valueOf(fileUpload.uploadType), ownerId))
                    .map { (targetOwnerType, targetOwnerId) ->
                        LOG.info(
                            "Copying {} {} to {}={}",
                            fileUpload.uploadType,
                            fileUploadId.idToString(),
                            targetOwnerType,
                            targetOwnerId,
                        )

                        fileUpload.copy(
                            ownerId = targetOwnerId,
                            ownerType = targetOwnerType,
                            uploadType = uploadType ?: fileUpload.uploadType,
                        )
                    }.flatMap { copiedFileUpload ->
                        checkOwnerAccessibility(accessCheck, copiedFileUpload)
                            .then(bomradsFileUploadService.save(accessCheck, copiedFileUpload))
                    }.flatMap { updateOwnerResource(accessCheck, it, UpdateMode.ATTACH) }
                    .map { copiedFileUpload ->
                        UploadResponse(
                            id =
                                copiedFileUpload.id
                                    .idToString()
                                    .toMongoID()
                                    .toHexString(),
                            uploadType = copiedFileUpload.uploadType,
                            filename = copiedFileUpload.filename,
                            mimeType = copiedFileUpload.mimeType,
                            createdBy = userService.getUserById(accessCheck, copiedFileUpload.createdBy),
                            createdDate = copiedFileUpload.createdDate!!.toInstant(),
                            threeDbVersionedPart = null,
                        )
                    }
            }
    }

    override fun addOwner(
        accessCheck: AccessCheck,
        id: String,
        ownerId: String,
    ): Mono<UploadResponse> {
        val fileUploadId = FileUploadId(id.toUUID())

        LOG.info("Attempting to add owner {} to file {}", ownerId, fileUploadId.idToString())

        return getFileUpload(accessCheck, fileUploadId)
            .exceptionOnEmpty(ResourceNotFoundException(fileUploadId.idToString()))
            .flatMap { fileUpload ->
                checkOwnerAccessibility(accessCheck, fileUpload)
                    .then(
                        getOwner(accessCheck, UploadType.valueOf(fileUpload.uploadType), ownerId)
                            .map { (targetOwnerType, targetOwnerId) ->
                                LOG.info(
                                    "Adding owner {} with type {} to file {}",
                                    targetOwnerId,
                                    targetOwnerType,
                                    fileUploadId.idToString(),
                                )
                                fileUpload.setOwner(targetOwnerId, targetOwnerType)
                            }.flatMap { file ->
                                bomradsFileUploadService.save(accessCheck, file)
                            }.flatMap { updateOwnerResource(accessCheck, it, UpdateMode.ATTACH) }
                            .map { updatedFile ->
                                UploadResponse(
                                    id =
                                        updatedFile.id
                                            .idToString()
                                            .toMongoID()
                                            .toHexString(),
                                    uploadType = updatedFile.uploadType,
                                    filename = updatedFile.filename,
                                    mimeType = updatedFile.mimeType,
                                    createdBy = userService.getUserById(accessCheck, updatedFile.createdBy),
                                    createdDate = updatedFile.createdDate!!.toInstant(),
                                    threeDbVersionedPart = null,
                                )
                            },
                    )
            }
    }

    private fun getFileUpload(
        accessCheck: AccessCheck,
        id: FileUploadId,
    ): Mono<FileUploadDto> = bomradsFileUploadService.findByIdAndDeletedIsFalse(accessCheck, id)

    private fun checkOwnerAccessibility(
        accessCheck: AccessCheck,
        fileUpload: FileUploadDto,
    ): Mono<Void> =
        checkOwnerAccessibility(
            accessCheck,
            UploadType.valueOf(fileUpload.uploadType),
            fileUpload.ownerType,
            fileUpload.ownerId,
        )

    private fun checkOwnerAccessibility(
        accessCheck: AccessCheck,
        uploadType: UploadType,
        ownerType: OwnerType,
        ownerId: String,
    ): Mono<Void> {
        LOG.debug("Checking owner accessibility uploadType={} ownerType={}, ownerId={}", uploadType, ownerType, ownerId)

        return when (ownerType) {
            OwnerType.USER -> Mono.just(accessCheck.userId == ownerId)
            else -> Mono.empty()
        }.flatMap { isAccessible ->
            when (isAccessible) {
                true -> Mono.empty()
                false -> Mono.error(ResourceNotFoundException(null))
            }
        }
    }

    private fun updateOwnerResource(
        accessCheck: AccessCheck,
        fileUploadDto: FileUploadDto,
        updateMode: UpdateMode,
    ): Mono<FileUploadDto> {
        LOG.debug(
            "Updating owner={} uploadType={} uploadId={} updateMode={}",
            fileUploadDto.ownerId,
            fileUploadDto.uploadType,
            fileUploadDto.uploadId,
            updateMode,
        )

        return if (fileUploadDto.ownerType == OwnerType.USER) {
            Mono.just(fileUploadDto)
        } else {
            getModule(UploadType.valueOf(fileUploadDto.uploadType))
                .flatMap { it.updateOwner(accessCheck, fileUploadDto, updateMode) }
                .thenReturn(fileUploadDto)
        }
    }

    private fun loadFile(
        uploadType: UploadType,
        file: UploadablePayload,
    ): Mono<LoadedFile> {
        val filename = file.getFilename()

        LOG.debug("Reading bytes of {} filename={}", uploadType, filename)

        return file
            .getBytes()
            .map { payload ->
                val mimeType = mediaTypeDetector.detect(prefix = payload, name = filename)
                LoadedFile(uploadType, filename, mimeType, payload)
            }
    }

    private fun checkMimeType(
        mimeType: String,
        uploadType: UploadType,
        filename: String,
    ): Mono<Void> {
        LOG.debug(
            "Validating mimeType={} of {} filename={} against whitelist",
            mimeType,
            uploadType,
            filename,
        )

        return getModule(uploadType)
            .flatMap {
                it.isMimeTypeAllowed(mimeType, filename)
            }.flatMap { canUploadFile ->
                when (canUploadFile) {
                    true -> Mono.empty()
                    false -> Mono.error(UnsupportedMediaTypeException(mimeType))
                }
            }
    }

    private fun performVirusScan(loadedFile: LoadedFile): Mono<LoadedFile> {
        LOG.debug("Performing virus scan")

        return virusScanner
            .scan(loadedFile.payload)
            .onErrorMap(::virusExceptionTransformer)
            .map { scannedPayload ->
                LoadedFile(loadedFile.uploadType, loadedFile.filename, loadedFile.mimeType, scannedPayload)
            }
    }

    private fun getModule(uploadType: UploadType): Mono<UploadModule> =
        checkUploadType(uploadType)
            .then { Mono.just(uploadModules[uploadType]!!) }

    private fun checkUploadType(uploadType: UploadType): Mono<UploadType> =
        when (uploadModules.containsKey(uploadType)) {
            true -> Mono.just(uploadType)
            false ->
                Mono.error(
                    internalServerError(
                        ErrorCode.UNSUPPORTED_OPERATION,
                        "module missing for uploadType=$uploadType",
                    ),
                )
        }

    private fun checkOwnerId(
        uploadType: UploadType,
        ownerId: String,
    ): Mono<String> =
        getModule(uploadType)
            .flatMap {
                when (uuidIsValid(ownerId)) {
                    true -> Mono.just(ownerId)
                    false -> Mono.error(internalServerError(ErrorCode.INVALID_INPUT, "ownerId=$ownerId"))
                }
            }

    /**
     * Handles the missing ownerId for user space requests.
     * */
    private fun getOwner(
        accessCheck: AccessCheck,
        uploadType: UploadType,
        ownerId: String?,
    ): Mono<Tuple2<OwnerType, String>> =
        when (ownerId) {
            null -> Mono.zip(Mono.just(OwnerType.USER), Mono.just(accessCheck.userId))
            else ->
                Mono.zip(
                    getModule(uploadType).map { it.getOwnerType() },
                    checkOwnerId(uploadType, ownerId.toMongoID().toBomNodeId().idToString()),
                )
        }
}

private class LoadedFile(
    val uploadType: UploadType,
    val filename: String,
    val mimeType: String,
    val payload: ByteArray,
)

private fun virusExceptionTransformer(t: Throwable): Throwable =
    when (t) {
        is VirusScanner.VirusFoundException -> internalServerError(CONTAMINATED_FILE, t.message ?: "Virus found")
        else -> t
    }
