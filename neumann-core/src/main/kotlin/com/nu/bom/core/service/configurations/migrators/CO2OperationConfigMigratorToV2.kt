package com.nu.bom.core.service.configurations.migrators

import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.CO2OperationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.CO2OperationConfigurationV2
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCO2eCalculationElementType
import com.nu.bom.core.service.configurations.ConfigType
import com.nu.bom.core.service.configurations.ConfigurationMigrator
import com.nu.bom.core.service.configurations.migrators.helper.OperationConfigurationMigratorHelper.adaptOperationToHaveTotalOverheadValueAndTransportCalculator
import org.springframework.stereotype.Component
import kotlin.reflect.KClass

/**
 * Migration that sets Total Overhead value
 */
@Component
class CO2OperationConfigMigratorToV2 : ConfigurationMigrator<CO2OperationConfiguration, CO2OperationConfigurationV2> {
    override fun sourceClass(): KClass<CO2OperationConfiguration> = CO2OperationConfiguration::class

    override fun targetClass(): KClass<CO2OperationConfigurationV2> = CO2OperationConfigurationV2::class

    override fun configType() = ConfigType.CommercialCo2Operations

    override fun migrate(configurationValue: CO2OperationConfiguration): CO2OperationConfigurationV2 {
        return CO2OperationConfigurationV2(
            configurationValue.operations.map {
                adaptOperationToHaveTotalOverheadValueAndTransportCalculator(
                    it,
                    TsetCO2eCalculationElementType.INDIRECT_CO2E_AFTER_PRODUCTION,
                    TsetCO2eCalculationElementType.TRANSPORT_CO2E,
                )
            },
        )
    }
}
