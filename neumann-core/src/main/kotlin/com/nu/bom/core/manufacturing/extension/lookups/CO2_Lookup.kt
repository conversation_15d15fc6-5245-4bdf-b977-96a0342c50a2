package com.nu.bom.core.manufacturing.extension.lookups

import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.manufacturing.fieldTypes.EmissionUnits
import com.nu.bom.core.manufacturing.fieldTypes.Energy
import com.nu.bom.core.manufacturing.fieldTypes.EnergyUnits
import com.nu.bom.core.manufacturing.fieldTypes.MaterialFurnaceType
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text

data class CO2SteelLookup(
    val legClass: Num,
    val furnaceType: MaterialFurnaceType,
    val pigIron: Pair<Num, Num>,
    val ownScrap: Pair<Num, Num>,
    val purchasedScrap: Pair<Num, Num>,
    val quantityElectricityMelting: QuantityUnit,
    val quantityErdgasHeatSteelPipeBar: QuantityUnit,
    val quantityErdgasHeatSteelSheetWire: QuantityUnit,
    val quantityErdgasFirstRolling: QuantityUnit,
    val quantityElectricityFirstRolling: QuantityUnit,
    val quantityErdgasSecondRolling: QuantityUnit,
    val quantityElectricitySecondRolling: QuantityUnit,
    val quantityHeatRolled: QuantityUnit,
    val quantityBlockForBarForged: QuantityUnit,
    val quantityBlockForHZ: QuantityUnit,
    val quantityHZForBarRolled: QuantityUnit,
    val quantityHZForSheetRolled: QuantityUnit,
    val quantityForAluExtruded: QuantityUnit,
) {
    companion object {
        val defaultZero =
            CO2SteelLookup(
                Num(0),
                MaterialFurnaceType.ELECTRIC_FURNACE,
                Pair(Num(0), Num(0)),
                Pair(Num(0), Num(0)),
                Pair(Num(0), Num(0)),
                QuantityUnit(0.0),
                QuantityUnit(0.0),
                QuantityUnit(0.0),
                QuantityUnit(0.0),
                QuantityUnit(0.0),
                QuantityUnit(0.0),
                QuantityUnit(0.0),
                QuantityUnit(0.0),
                QuantityUnit(0.0),
                QuantityUnit(0.0),
                QuantityUnit(0.0),
                QuantityUnit(0.0),
                QuantityUnit(0.0),
            )
    }
}

val co2SteelReader: (row: List<String>) -> CO2SteelLookup = { row ->
    CO2SteelLookup(
        legClass = Num(row[0]),
        furnaceType = MaterialFurnaceType.valueOf(row[1]),
        pigIron = Pair(Num(row[2].split("_")[0]), Num(row[2].split("_")[1])),
        ownScrap = Pair(Num(row[3].split("_")[0]), Num(row[3].split("_")[1])),
        purchasedScrap = Pair(Num(row[4].split("_")[0]), Num(row[4].split("_")[1])),
        quantityElectricityMelting = QuantityUnit(row[5]),
        quantityErdgasHeatSteelPipeBar = QuantityUnit(row[6]),
        quantityErdgasHeatSteelSheetWire = QuantityUnit(row[7]),
        quantityErdgasFirstRolling = QuantityUnit(row[8]),
        quantityElectricityFirstRolling = QuantityUnit(row[9]),
        quantityErdgasSecondRolling = QuantityUnit(row[10]),
        quantityElectricitySecondRolling = QuantityUnit(row[11]),
        quantityHeatRolled = QuantityUnit(row[12]),
        quantityBlockForBarForged = QuantityUnit(row[13]),
        quantityBlockForHZ = QuantityUnit(row[14]),
        quantityHZForBarRolled = QuantityUnit(row[15]),
        quantityHZForSheetRolled = QuantityUnit(row[16]),
        quantityForAluExtruded = QuantityUnit(row[17]),
    )
}

data class LocationPrefills(
    val countryId: Text,
    val electricityCarbon: Emission,
    val heatingPerAreaPerYear: Emission,
    val requiredEnergyForLightPerQmPerYear: Energy,
    val emissionPerShitPerEmployee: Emission,
    val depreciationEmissionPerQmPerYear: Emission,
    val primaryShare: Rate,
    val secondaryShare: Rate,
    val primaryAluEmissionPerKg: Emission,
    val secondaryAluEmissionPerKg: Emission,
    val minPigIronSandCasting: Num,
    val maxPigIronSandCasting: Num,
)

val locationReader: (row: List<String>) -> LocationPrefills = { row ->
    LocationPrefills(
        countryId = Text(row[0]),
        electricityCarbon = Emission(row[1].toBigDecimal(), EmissionUnits.GRAM_CO2E),
        heatingPerAreaPerYear = Emission(row[2].toBigDecimal(), EmissionUnits.GRAM_CO2E),
        requiredEnergyForLightPerQmPerYear = Energy(row[3].toBigDecimal(), EnergyUnits.KILOWATTHOUR),
        emissionPerShitPerEmployee = Emission(row[4].toBigDecimal(), EmissionUnits.GRAM_CO2E),
        depreciationEmissionPerQmPerYear = Emission(row[5].toBigDecimal(), EmissionUnits.GRAM_CO2E),
        primaryShare = Rate(row[6].toBigDecimal()),
        secondaryShare = Rate(row[7].toBigDecimal()),
        primaryAluEmissionPerKg = Emission(row[8].toBigDecimal(), EmissionUnits.KILOGRAM_CO2E),
        secondaryAluEmissionPerKg = Emission(row[9].toBigDecimal(), EmissionUnits.KILOGRAM_CO2E),
        minPigIronSandCasting = Num(row[10]),
        maxPigIronSandCasting = Num(row[10]),
    )
}
