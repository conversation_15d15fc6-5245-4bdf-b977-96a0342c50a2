package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import org.springframework.stereotype.Service

/**
 * COST-65314 - Manual cycle time steps should not be ignored in Progressive/Transfer die stamping
 * Resets the createCycleTimeSteps field within CycleTimeStepGroupFromTemplate when is created by
 * ManufacturingStepTransferDieStamping so the missing cycletime step Transfer die stamping appears on the calculation.
 */
@Service
class ResetsCreateCycleTimeStepsForTds : ManufacturingModelEntityMapper {
    override val changeSetId: MigrationChangeSetId
        get() = MigrationChangeSetId("2024-11-29-create-missing-transfer-die-cycletime-step")

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity =
        if (entity.createdBy?.entityRef == "ManufacturingStepTransferDieStamping") {
            entity.copyAll(
                fieldWithResults = resetCreateCycleTimeSteps(entity.fieldWithResults),
            )
        } else {
            entity
        }

    private fun resetCreateCycleTimeSteps(fields: Map<String, FieldResultModel>): Map<String, FieldResultModel> =
        fields.mapValues { (fieldName, field) ->
            if (fieldName == "createCycleTimeSteps") {
                field.copyAll(source = FieldResult.SOURCE.R.name)
            } else {
                field
            }
        }
}
