package com.nu.bom.core.manufacturing.masterdata.tsetdefaultconfiguration

import com.nu.bom.core.manufacturing.fieldTypes.Text

/**
 * These are the default overhead Methods that are part of the reference data in masterdata service.
 * They are also the same methods that existed in the legacy OverheadMethod type
 */
enum class TsetOverheadMethod(
    /**
     * default label / display name for this overhead method
     */
    val defaultLabel: String,
) {
    BUILD_TO_PRINT_AUTO("Build to print (automotive)"),
    SIMULT_ENG_AUTO("Simultaneous engineering (automotive)"),
    HIGH_INNO_AUTO("Highly innovative (automotive)"),
    BUILD_TO_PRINT_NON_AUTO("Build to print (non-automotive)"),
    SIMULT_ENG_NON_AUTO("Simultaneous engineering (non-automotive)"),
    HIGH_INNO_NON_AUTO("Highly innovative (non-automotive)"),
    NO_OVERHEADS("No overheads"),
    ;

    /**
     * wraps the string name of the enum into a [Text] type
     */
    val fieldType get() = Text(this.name)
}
