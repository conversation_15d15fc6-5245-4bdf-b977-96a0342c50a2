package com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder

import com.nu.bom.core.manufacturing.commercialcalculation.rollupconfiguration.RollUpConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.CalculationOperationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType

data class CommercialFieldsBuilderConfiguration(
    val valueType: ValueType,
    val calculationConfiguration: CalculationOperationConfiguration,
    val rollUpConfiguration: RollUpConfiguration,
)
