package com.nu.bom.core.model

import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer
import com.nu.bom.core.manufacturing.enums.MasterDataCategory
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.model.AccountMasterData.Companion.COLLECTION_NAME
import com.tset.core.module.mongodb.infrastructure.dto.RefKeyForAccountMongoDto
import org.bson.types.ObjectId
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import java.util.Date

@Document(collection = COLLECTION_NAME)
data class AccountMasterData(
    @JsonSerialize(using = ToStringSerializer::class)
    val accountId: ObjectId,
    // --- start of embedded MasterData
    override val type: MasterDataType,
    override val key: String,
    override val year: Int,
    override val location: String,
    override val data: Map<String, FieldResult<*, *>>,
    override val version: Int = 0,
    override val active: Boolean?,
    // --- end of embedded MasterData
    // JsonUnwrapped annotation is ignored, so we have to wait for features like DATAMONGO-1902
    // to get unwrap support - unwrapped form is needed for reusing queries
    /**
     * The global [MasterData] source of this [AccountMasterData] entry in the **latest** collection
     * */
    @JsonSerialize(using = ToStringSerializer::class)
    val currentGlobal: ObjectId?,
    /**
     * The global [MasterData] source of this [AccountMasterData] entry in the **history** collection.
     * */
    @JsonSerialize(using = ToStringSerializer::class)
    val historicGlobal: ObjectId?,
    override val category: MasterDataCategory = MasterDataCategory.ofType(type),
    override val manage: Boolean = true,
    override val refKey: RefKeyForAccountMongoDto? = null
) : IMasterData {

    val masterData
        get() = MasterData(
            type = type,
            key = key,
            year = year,
            location = location,
            version = version,
            active = active,
            manage = manage,
            data = data,
            refKey = refKey
        ).apply {
            _id = this@AccountMasterData._id
            lastModifiedDate = <EMAIL>
            latestHistory = <EMAIL>
        }

    // maintained manually when updating latest collection entries
    var lastModifiedDate: Date? = null

    /**
     * The corresponding [AccountMasterData] entry in the **history** collection (only filled for entries in the latest collection).
     *
     * */
    // set on save by the MasterDataService
    @JsonSerialize(using = ToStringSerializer::class)
    var latestHistory: ObjectId? = null

    @Id
    @JsonSerialize(using = ToStringSerializer::class)
    var _id: ObjectId? = null

    constructor(accountId: ObjectId, masterData: MasterData, currentGlobal: ObjectId?, historicGlobal: ObjectId?) : this(
        accountId = accountId,
        type = masterData.type,
        key = masterData.key,
        year = masterData.year,
        location = masterData.location,
        data = masterData.data,
        version = masterData.version,
        active = masterData.active,
        currentGlobal = currentGlobal,
        historicGlobal = historicGlobal,
        refKey = masterData.refKey
    )

    init {
        data.values.forEach { it.source = FieldResult.SOURCE.M }
    }

    override fun copy(justdata: Map<String, FieldResult<*, *>>): AccountMasterData {
        return copy(
            data = justdata
        ).also {
            it.lastModifiedDate = lastModifiedDate
            it.latestHistory = latestHistory
            it._id = _id
        }
    }

    override fun newVersion(active: Boolean): AccountMasterData {
        return copy(
            version = version + 1,
            active = active
        )
    }

    companion object {
        const val COLLECTION_NAME = "AccountMasterData"
        const val HISTORY_COLLECTION_NAME = "AccountMasterDataHistory"
    }
}
