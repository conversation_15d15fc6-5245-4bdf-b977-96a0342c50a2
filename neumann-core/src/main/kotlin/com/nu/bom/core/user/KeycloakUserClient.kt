package com.nu.bom.core.user

import com.nu.security.config.MultitenancyConfig
import com.nu.user.UserInfoDto
import com.tset.common.util.Memoize
import org.keycloak.admin.client.Keycloak
import org.keycloak.representations.idm.UserRepresentation
import org.slf4j.LoggerFactory
import org.springframework.security.oauth2.jwt.Jwt
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono
import java.util.Locale
import java.util.Optional

private const val SAMPLE_ALWAYS_ROLE = "SampleAlways"
private const val SAMPLE_NEVER_ROLE = "SampleNever"
private const val ENABLE_SESSION_REPLAY_ROLE = "SessionReplay"

class KeycloakUserClient(
    private val keycloakAdminClient: Keycloak,
    private val multitenancyConfig: MultitenancyConfig,
) : UserClient {
    companion object {
        val logger = LoggerFactory.getLogger(KeycloakUserClient::class.java)!!
    }

    private val groupIdCache =
        Memoize(
            { (groupPath, realm): Pair<String, String> ->
                keycloakAdminClient
                    .realm(realm)
                    .groups()
                    .groups().first {
                        it.path == groupPath
                    }
                    .id
            },
            1L,
        )

    private val groupMemberCache =
        Memoize(
            { (groupId, realm): Pair<String, String> ->
                keycloakAdminClient
                    .realm(realm)
                    .groups()
                    .group(groupId)
                    .members(0, 8192)
            },
            1L,
        )

    private fun getGroupId(
        groupPath: String,
        realm: String,
    ): String = groupIdCache.apply(groupPath to realm)

    private fun getGroupMembers(
        groupId: String,
        realm: String,
    ): List<UserRepresentation> = groupMemberCache.apply(groupId to realm)

    private fun toDto(user: UserRepresentation): UserInfoDto =
        UserInfoDto(user.id, ("" + user.firstName + ' ' + user.lastName).trim(), user.email)

    override fun getUsers(
        accessCheck: AccessCheck,
        filterTerm: String?,
    ): Flux<UserInfoDto> {
        val groupPath = accessCheck.getFirstRegularGroup() ?: return Flux.empty()

        val groupId = getGroupId(groupPath, accessCheck.realm)

        return Flux.fromIterable(
            getGroupMembers(groupId, accessCheck.realm)
                .filter {
                    filterTerm == null ||
                        it.firstName.lowercase(Locale.getDefault())
                            .contains(filterTerm.lowercase(Locale.getDefault())) ||
                        it.lastName.lowercase(Locale.getDefault()).contains(filterTerm.lowercase(Locale.getDefault()))
                }.map {
                    toDto(it)
                },
        )
    }

    override fun getCurrentUser(jwt: Jwt): Mono<String> {
        return jwt.claims[multitenancyConfig.usernameClaim].let {
            it as String
        }.toMono()
    }

    override fun getCurrentUserAndAccount(accessCheck: AccessCheck): Mono<UserAccountInfoDto> {
        val userInfoDto =
            UserInfoDto(
                accessCheck.userId,
                accessCheck.userName ?: accessCheck.userId,
                accessCheck.claims.getEmail(),
            )
        val tracking =
            accessCheck.getRoles().let { roles ->
                TrackingDto(
                    samplingRate =
                        when {
                            roles.contains(SAMPLE_ALWAYS_ROLE) -> 100
                            roles.contains(SAMPLE_NEVER_ROLE) -> 0
                            else -> 10
                        },
                    replayRate =
                        when {
                            roles.contains(ENABLE_SESSION_REPLAY_ROLE) -> 100
                            else -> 0
                        },
                )
            }
        return Mono.just(UserAccountInfoDto(userInfoDto, accessCheck.accountName, tracking))
    }

    override fun getUserById(
        accessCheck: AccessCheck,
        id: String?,
    ): Optional<UserInfoDto> {
        return try {
            val groupPath = accessCheck.getFirstRegularGroup()
            if (groupPath == null || id == null) {
                return Optional.empty()
            }
            val groupId = getGroupId(groupPath, accessCheck.realm)
            val user = getGroupMembers(groupId, accessCheck.realm).find { user -> user.id == id }
            Optional.ofNullable(user).map { toDto(it) }
        } catch (e: Exception) {
            logger.error("Cannot get user $id: ${e.message}")
            Optional.empty()
        }
    }

    override fun getUsersByIds(
        accessCheck: AccessCheck,
        ids: List<String>,
    ): List<UserInfoDto> {
        return try {
            val groupPath = accessCheck.getFirstRegularGroup()
            if (groupPath == null || ids.isEmpty()) {
                return emptyList()
            }

            val groupId = getGroupId(groupPath, accessCheck.realm)
            getGroupMembers(groupId, accessCheck.realm).filter { user -> ids.contains(user.id) }.map { toDto(it) }
        } catch (e: Exception) {
            logger.error("Cannot get users $ids: ${e.message}")
            emptyList()
        }
    }

    override fun getServiceToken(): String = keycloakAdminClient.tokenManager().accessTokenString
}
