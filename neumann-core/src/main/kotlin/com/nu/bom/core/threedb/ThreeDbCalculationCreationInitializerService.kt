package com.nu.bom.core.threedb

import com.nu.bom.core.api.dtos.CalculationModel
import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.behaviours.ShapeBasedCostModuleBehaviorThreeDb
import com.nu.bom.core.manufacturing.enums.UnitOverrideContext
import com.nu.bom.core.manufacturing.factories.FieldFactoryService
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.ThreeDbOverwriteTracker
import com.nu.bom.core.manufacturing.fieldTypes.ThreeDbOverwrites
import com.nu.bom.core.manufacturing.fieldTypes.VersionedPart
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.InterpolationData
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono

@Service
class ThreeDbCalculationCreationInitializerService(
    private val entityManager: EntityManager,
    private val threeDbService: ThreeDbService,
    private val fieldFactoryService: FieldFactoryService,
) {
    fun createInitialThreeDbFieldParameter(
        accessCheck: AccessCheck,
        model: CalculationModel,
        shapeId: String,
    ): Mono<List<FieldParameter>> =
        getShapeIdentifier(accessCheck, shapeId, model.path)
            .flatMap {
                threeDbService.copyPart(accessCheck, it)
            }.map {
                val versionedPart =
                    FieldParameter(
                        name = ShapeBasedCostModuleBehaviorThreeDb::versionedPart.name,
                        type = VersionedPart::class.java.simpleName,
                        value = it,
                        source = FieldResult.SOURCE.C.toString(),
                        metaInfo =
                            entityManager.getMetaInfo(
                                ShapeBasedCostModuleBehaviorThreeDb::class.java,
                                ShapeBasedCostModuleBehaviorThreeDb::versionedPart.name,
                                InterpolationData.fromUnitOverrideContext(
                                    UnitOverrideContext.noContext,
                                ),
                            ),
                    )

                val resourceTracker =
                    FieldParameter(
                        name = ShapeBasedCostModuleBehaviorThreeDb::threeDbResourceTracker.name,
                        type = ThreeDbOverwriteTracker::class.java.simpleName,
                        value = ThreeDbOverwrites.empty(),
                        source = FieldResult.SOURCE.C.toString(),
                        metaInfo =
                            entityManager.getMetaInfo(
                                ShapeBasedCostModuleBehaviorThreeDb::class.java,
                                ShapeBasedCostModuleBehaviorThreeDb::threeDbResourceTracker.name,
                                InterpolationData.fromUnitOverrideContext(
                                    UnitOverrideContext.noContext,
                                ),
                            ),
                    )

                listOf(versionedPart, resourceTracker)
            }

    fun createInitialThreeDbFieldParameterIfApplicable(
        accessCheck: AccessCheck,
        model: CalculationModel?,
        shapeId: String?,
    ): Mono<List<FieldParameter>> =
        if (model != null && shapeId != null && model.isThreeDbEnabled) {
            createInitialThreeDbFieldParameter(
                accessCheck,
                model,
                shapeId,
            )
        } else {
            Mono.just(emptyList())
        }

    fun createInitialThreeDbFieldParameterIfApplicable(
        accessCheck: AccessCheck,
        entity: String?,
        shapeId: String?,
    ): Mono<List<FieldParameter>> =
        createInitialThreeDbFieldParameterIfApplicable(
            accessCheck,
            entity?.let { Model.fromEntity(entity)?.toCalculationModel() },
            shapeId,
        )

    fun toFieldResultMap(threeDbFields: List<FieldParameter>): Map<String, FieldResultStar> =
        fieldFactoryService.toFieldResultMap(threeDbFields)

    private fun getShapeIdentifier(
        accessCheck: AccessCheck,
        shapeId: String,
        technology: String,
    ): Mono<ThreeDbShapeIdentifier> =
        threeDbService
            .getShapes(accessCheck)
            .map { shapes ->
                shapes.find { it.shape_id == shapeId } ?: throw IllegalArgumentException(
                    "could not find shape $shapeId for technology $technology",
                )
            }
}
