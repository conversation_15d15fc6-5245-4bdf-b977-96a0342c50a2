package com.nu.bom.core.manufacturing.fieldTypes

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.nu.bom.core.config.JacksonConfig
import com.nu.bom.core.publicapi.dtos.configurations.masterdata.HeaderTypeConsumer
import java.math.BigDecimal

// create dedicated classes that are used as field values for the Masterdata lookup and don't re-use the DTO classes
// we need to be careful what we store in the manufacturing, and we have more control about what we store
// and how (lazy) migrations are done, when using our own classes here instead of the DTOs in nu-lib

@JsonIgnoreProperties(ignoreUnknown = true)
data class MdLookupRequestFieldData(
    val strategyKey: String,
    val headerTypeKey: String,
    val headerKey: String,
    // I would like to get rid of this, but [MdLookupSourceProvider] needs it know which fields to generate
    val headerTypeConsumer: HeaderTypeConsumer,
    val effectivities: List<MdEffectivity.Value<*>>,
    val timestampEpochMillis: BigDecimal? = null,
    val executeLookup: Boolean = true,
    val effectivityMappings: Map<MdFieldDefinitionKey, MdEffectivityValueMapping> = emptyMap(),
) {
    fun asField() = MdLookupRequestField(this)

    fun isBulkableWith(other: MdLookupRequestFieldData) =
        strategyKey == other.strategyKey &&
            headerTypeKey == other.headerTypeKey &&
            effectivities == other.effectivities &&
            timestampEpochMillis == other.timestampEpochMillis
}

typealias MdFieldDefinitionKey = String

typealias MdEffectivityValueMapping = Map<String, String>

class MdLookupRequestField(res: MdLookupRequestFieldData) : FieldResult<MdLookupRequestFieldData, MdLookupRequestField>(res) {
    companion object {
        val mapper: ObjectMapper =
            JacksonConfig
                .mutableMapper()
                .disable(DeserializationFeature.FAIL_ON_MISSING_CREATOR_PROPERTIES)
    }

    override fun dbValue(): MdLookupRequestFieldData = res

    // Keep for backwards compatibility reasons
    constructor(value: String) : this(
        mapper.readValue<MdLookupRequestFieldData>(value),
    )
}
