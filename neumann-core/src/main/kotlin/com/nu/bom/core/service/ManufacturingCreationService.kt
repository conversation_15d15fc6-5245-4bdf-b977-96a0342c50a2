package com.nu.bom.core.service

import com.nu.bom.core.controller.CalculationResultDto
import com.nu.bom.core.controller.CalculationResultWithSnapshot
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.service.CalculationService
import com.nu.bom.core.model.BomCreation
import com.nu.bom.core.model.BomId
import com.nu.bom.core.model.BomNode
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.Branch
import com.nu.bom.core.model.ChangesetId
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.model.SnapshotId
import com.nu.bom.core.model.TriggerAction
import com.nu.bom.core.model.toBomNodeId
import com.nu.bom.core.model.toMongoBomNodeId
import com.nu.bom.core.model.toMongoBranchId
import com.nu.bom.core.model.toMongoProjectId
import com.nu.bom.core.model.toProjectId
import com.nu.bom.core.service.bomrads.BomradsBomNodeService
import com.nu.bom.core.service.bomrads.BomradsNodeCreationData
import com.nu.bom.core.service.bomrads.BomradsTreeService
import com.nu.bom.core.service.bomrads.PushChangesService
import com.nu.bom.core.service.change.ChangeContext
import com.nu.bom.core.service.change.ChangedEntities
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.nu.bomrads.dto.BomNodeCreationResultDTO
import com.nu.bomrads.dto.BranchViewDTO
import com.nu.bomrads.dto.MinimalBranchDTO
import com.nu.bomrads.dto.MinimalChangesetDTO
import com.nu.bomrads.dto.admin.ProjectDTO
import com.nu.bomrads.enumeration.BranchTarget
import com.nu.bomrads.enumeration.ChangeType
import com.tset.core.module.bom.EventSourcingModule
import com.tset.core.service.domain.calculation.CalculationType
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import kotlin.reflect.KClass
import com.nu.bomrads.id.BomNodeId as BomradsBomNodeId
import com.nu.bomrads.id.BranchId as BomradsBranchId
import com.nu.bomrads.id.ChangesetId as BomradsChangesetId
import com.nu.bomrads.id.ProjectId as BomradsProjectId

typealias TriggerActionCreator = (
    node: BomNodeId,
    titledEntity: ManufacturingCreationService.TitledEntity,
) -> TriggerAction

@Service
class ManufacturingCreationService(
    private val manufacturingUpdateService: ManufacturingUpdateService,
    private val manufacturingCalculationService: ManufacturingCalculationService,
    private val bomNodeConversionService: BomNodeConversionService,
    private val bomradsTreeService: BomradsTreeService,
    private val pushChangesService: PushChangesService,
    private val changeContextHelper: ChangeContextHelper,
    private val eventSourcingModule: EventSourcingModule,
) {
    companion object {
        val logger: Logger = LoggerFactory.getLogger(ManufacturingCreationService::class.java)

        fun standardArgs(
            accessCheck: AccessCheck,
            project: ProjectDTO,
            title: String,
        ): Map<String, Any> =
            mapOf(
                "isPartModelConverted" to true,
                "key" to project.key,
                "user" to accessCheck.userId,
                "title" to title,
                "isPart" to true,
            )
    }

    class EntityCreationParameters(
        val index: Int = 0,
        val name: String,
        val title: String,
        val mainBranch: Boolean,
        val type: String?,
        val args: Map<String, Any>,
        val fields: Map<String, FieldResultStar>?,
    )

    class TitledEntity(
        val entity: ManufacturingEntity,
        val title: String,
        val mainBranch: Boolean,
    )

    // create single (if manufacturingEntity is not defined) -> calculate -> convert -> persist
    fun create(
        accessCheck: AccessCheck,
        year: Int,
        projectId: BomradsProjectId,
        name: String,
        type: KClass<out BaseManufacturing>,
        args: Map<String, Any>,
        fields: Map<String, FieldResultStar>?,
        uploadId: String? = null,
        title: String,
        wizardManufacturing: ManufacturingEntity? = null,
    ): Mono<CalculationResultWithSnapshot> =
        createMany(
            accessCheck,
            year,
            projectId,
            entityCreationParameters =
                listOf(
                    EntityCreationParameters(
                        name = name,
                        title = title,
                        type = type.simpleName!!,
                        args = args,
                        fields = fields,
                        mainBranch = true,
                    ),
                ),
            uploadId = uploadId,
            wizardManufacturing = wizardManufacturing,
        ).map { it.first() }

    // create many (if manufacturingEntity is not defined) -> calculate -> convert -> persist
    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    fun createMany(
        accessCheck: AccessCheck,
        year: Int,
        projectId: BomradsProjectId,
        entityCreationParameters: List<EntityCreationParameters>,
        uploadId: String?,
        wizardManufacturing: ManufacturingEntity? = null,
    ): Mono<List<CalculationResultWithSnapshot>> =
        Flux
            .fromIterable(entityCreationParameters)
            .flatMap { entityParam ->
                (
                    if (wizardManufacturing == null) {
                        manufacturingUpdateService.createManufacturingEntity(
                            accessCheck = accessCheck,
                            year = year,
                            type = Entities.MANUFACTURING,
                            clazz = entityParam.type,
                            name = entityParam.name,
                            args = entityParam.args,
                            initialFields = entityParam.fields,
                        )
                    } else {
                        Mono.just(wizardManufacturing)
                    }
                ).map {
                    TitledEntity(it, entityParam.title, entityParam.mainBranch)
                }
            }.collectList()
            .flatMap { titledEntities ->
                val main = entityCreationParameters.find { it.mainBranch }!!
                calculateAndConvertOneBomNodeManyVariants(
                    accessCheck = accessCheck,
                    year = year,
                    projectId = projectId,
                    name = main.name,
                    entities = titledEntities,
                    uploadId = uploadId,
                )
            }

    // calculate -> convert -> persist
    fun calculateAndConvertOneBomNodeManyVariants(
        accessCheck: AccessCheck,
        year: Int,
        projectId: BomradsProjectId,
        name: String,
        entities: List<TitledEntity>,
        uploadId: String?,
        triggerActionCreator: TriggerActionCreator = ::createTriggerAction,
        mandatoryFields: List<String> = emptyList(),
    ): Mono<List<CalculationResultWithSnapshot>> =
        processOneMainIntoBomrads(
            accessCheck = accessCheck,
            bomNodeName = name,
            year = year,
            projectId = projectId.toMongoProjectId(),
            entities = entities,
            triggerActionCreator = triggerActionCreator,
            uploadId = uploadId,
            mandatoryFields = mandatoryFields,
        ).collectList().map { it.flatten() }

    // calculate -> convert -> persist
    fun calculateAndConvertManyBomNodes(
        accessCheck: AccessCheck,
        year: Int,
        projectId: ProjectId,
        name: String,
        entities: List<TitledEntity>,
        uploadId: String?,
        triggerActionCreator: TriggerActionCreator = ::createTriggerAction,
    ): Flux<CalculationResultWithSnapshot> {
        val main =
            getSnapshotCreations(entities, name, year, projectId, uploadId, triggerActionCreator)
        return createVariantsIntoBomrads(accessCheck, main, projectId = projectId).map { mainResult ->
            mainResult.toCalculationResultWithSnapshot()
        }
    }

    private fun getSnapshotCreations(
        entities: List<TitledEntity>,
        name: String,
        year: Int,
        projectId: ProjectId,
        uploadId: String?,
        triggerActionCreator: TriggerActionCreator,
    ) = entities.map { entity ->
        val bomNodeId = BomNodeId()
        val node =
            BomNode(
                name = name,
                year = year,
                projectId = projectId,
                // rootOf is only needed to properly trigger the dashboardUpdateSender.bomNodeCreated call
                rootOf = BomId(),
                uploadId = uploadId,
            )
        node._id = bomNodeId
        SnapshotCreation(
            bomNode = node,
            bomNodeId = bomNodeId,
            bomNodeName = name,
            titledEntity = entity,
            year = year,
            projectId = projectId,
            triggerActionCreator = triggerActionCreator,
            uploadId = uploadId,
        )
    }

    private data class SnapshotCreation(
        val bomNode: BomNode,
        val bomNodeId: BomNodeId,
        val bomNodeName: String,
        val year: Int,
        val projectId: ProjectId,
        val titledEntity: TitledEntity,
        val uploadId: String?,
        val triggerActionCreator: TriggerActionCreator,
    )

    data class BomradsCalculationResultDto(
        val calculationResult: CalculationResultDto,
        val branchId: BomradsBranchId,
        val changesetId: BomradsChangesetId,
        val newRootSnapshot: BomNodeSnapshot,
    ) {
        fun toCalculationResultWithSnapshot(): CalculationResultWithSnapshot =
            CalculationResultWithSnapshot(calculationResult, newRootSnapshot)
    }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun processOneMainIntoBomrads(
        accessCheck: AccessCheck,
        bomNodeName: String,
        year: Int,
        projectId: ProjectId,
        entities: List<TitledEntity>,
        triggerActionCreator: TriggerActionCreator,
        uploadId: String?,
        mandatoryFields: List<String> = emptyList(),
    ): Flux<List<CalculationResultWithSnapshot>> {
        val (mainList, notMain) = entities.partition { it.mainBranch }
        check(mainList.size == 1) {
            "Unexpected number of main variants, expected 1, found ${mainList.size}"
        }
        val bomNodeId = BomNodeId()
        val node =
            BomNode(
                name = bomNodeName,
                year = year,
                projectId = projectId,
                // rootOf is only needed to properly trigger the dashboardUpdateSender.bomNodeCreated call
                rootOf = BomId(),
                uploadId = uploadId,
            )
        node._id = bomNodeId
        val main =
            mainList.map { entity ->
                SnapshotCreation(
                    bomNode = node,
                    bomNodeId = bomNodeId,
                    bomNodeName = bomNodeName,
                    titledEntity = entity,
                    year = year,
                    projectId = projectId,
                    triggerActionCreator = triggerActionCreator,
                    uploadId = uploadId,
                )
            }
        return createVariantsIntoBomrads(
            accessCheck,
            main,
            projectId = projectId,
            mandatoryFields = mandatoryFields,
        ).flatMap { mainResult ->
            if (notMain.isNotEmpty()) {
                val mainBranchState =
                    BomradsBomNodeService.BranchState(
                        projectId = projectId.toProjectId(),
                        branchId = mainResult.branchId,
                        changesetId = mainResult.changesetId,
                    )
                createVariantsIntoBomrads(
                    accessCheck,
                    snapshotsCreation =
                        getSnapshotCreations(
                            notMain,
                            bomNodeName,
                            year,
                            projectId,
                            uploadId,
                            triggerActionCreator,
                        ),
                    mainBranchState = mainBranchState,
                    projectId = projectId,
                ).collectList().map { nonMainResults ->
                    (nonMainResults + mainResult).map(BomradsCalculationResultDto::toCalculationResultWithSnapshot)
                }
            } else {
                Mono.just(listOf(mainResult.toCalculationResultWithSnapshot()))
            }
        }
    }

    @TsetSuppress("tset:reactive:flux-flatmapsequential") // whitelisted already existing calls
    private fun createVariantsIntoBomrads(
        accessCheck: AccessCheck,
        snapshotsCreation: List<SnapshotCreation>,
        mainBranchState: BomradsBomNodeService.BranchState? = null,
        projectId: ProjectId,
        mandatoryFields: List<String> = emptyList(),
    ): Flux<BomradsCalculationResultDto> =
        Flux
            .fromIterable(snapshotsCreation)
            .flatMapSequential { snapshotCreation ->
                manufacturingCalculationService
                    .calculate(
                        accessCheck = accessCheck,
                        manufacturing = snapshotCreation.titledEntity.entity,
                        year = snapshotCreation.year,
                        mandatoryFields = mandatoryFields,
                    ).flatMap { (manufacturingEntity, calculationResult) ->
                        getBomNodeCreationData(snapshotCreation, manufacturingEntity, accessCheck, calculationResult)
                    }
            }.collectList()
            .map { it.reversed() }
            .flatMapMany { nodesCreationData ->
                saveNodesToBomrads(
                    accessCheck = accessCheck,
                    projectId = projectId,
                    nodesCreationData = nodesCreationData,
                    mainBranch = mainBranchState,
                ).concatMap { (branchId, changesetId, bomNodeId) ->
                    val nodeCreationData = nodesCreationData.find { it.root.bomNodeId() == bomNodeId }!!
                    nodeCreationData.root.branchEntity!!._id = branchId.toMongoBranchId()
                    manufacturingCalculationService
                        .finishConversion(
                            accessCheck = accessCheck,
                            conversionResult = nodeCreationData.conversionResult,
                            res = nodeCreationData.calculationResult,
                        ).map {
                            BomradsCalculationResultDto(
                                calculationResult = it.second,
                                branchId = branchId,
                                changesetId = changesetId,
                                newRootSnapshot = nodeCreationData.root,
                            )
                        }
                }
            }

    private fun getBomNodeCreationData(
        snapshotCreation: SnapshotCreation,
        manufacturingEntity: ManufacturingEntity,
        accessCheck: AccessCheck,
        calculationResult: CalculationService.CalculationResults,
    ): Mono<BomradsNodeCreationData> {
        val snapshot =
            BomNodeSnapshot(
                name = snapshotCreation.bomNodeName,
                manufacturing = manufacturingEntity,
                year = snapshotCreation.year,
                title = snapshotCreation.titledEntity.title,
                accountId = accessCheck.asAccountId(),
            )

        // Let sacrifice a couple of goats to fulfill every requirement
        snapshot.bomNode = snapshotCreation.bomNode
        snapshot._id = SnapshotId()

        manufacturingEntity.snapshot = snapshot

        val changesetId = ChangesetId()
        // TODO: remove this
        val branch =
            Branch(
                _id = null,
                // if it's not set to true, frontend will display the calculation as not-saved
                global = true,
                creatorChangeset = ChangesetId(),
                published = true,
                fromMaster = true,
                sourceBranch = null,
                user = "mock-branch-user",
                _latestChangeset = changesetId,
                futureChangeset = changesetId,
                project = snapshotCreation.projectId,
                rootNode = snapshotCreation.bomNodeId,
            )

        snapshot.branchEntity = branch
        if (snapshotCreation.titledEntity.mainBranch) {
            snapshotCreation.bomNode.lastPublishedBranch = null
        } else {
            snapshotCreation.bomNode.setBranchHead(snapshot)
        }
        return createChangedEntitites(accessCheck, snapshotCreation, snapshot)
            .flatMap { changedEntities ->
                bomNodeConversionService
                    .createConversionResult(
                        accessCheck = accessCheck,
                        requestedNodeId = snapshotCreation.bomNodeId,
                        projectId = snapshotCreation.projectId,
                        entity = manufacturingEntity,
                        nodesToCheckMerge = null,
                    ).map {
                        BomradsNodeCreationData(
                            root = snapshot,
                            changedEntities = changedEntities,
                            conversionResult = it,
                            calculationResult = calculationResult,
                        )
                    }
            }
    }

    private fun createChangedEntitites(
        accessCheck: AccessCheck,
        snapshotCreation: SnapshotCreation,
        snapshot: BomNodeSnapshot,
    ): Mono<ChangedEntities> {
        snapshot.checkNotYetPersisted()
        val triggerAction =
            snapshotCreation.triggerActionCreator(
                snapshotCreation.bomNodeId,
                snapshotCreation.titledEntity,
            )

        return changeContextHelper
            .triggerBuilder(
                accessCheck = accessCheck,
                triggerAction = triggerAction,
                snapshots = emptyMap(),
                rootSnapshot = null,
            ).map { triggerBuilder ->
                val changeContext =
                    ChangeContext(
                        triggerAction = triggerAction,
                        triggerBuilder = triggerBuilder,
                    )
                // This is based on the 'old' snapshots - as it is a creation, they are not existent
                ChangedEntities.snapshotWithNode(changeContext, snapshot)
            }
    }

    private fun createTriggerAction(
        nodeId: BomNodeId,
        titledEntity: TitledEntity,
    ): TriggerAction {
        val title = titledEntity.title
        val manufacturingClass = titledEntity.entity.javaClass
        return BomCreation(
            bomNodeId = nodeId,
            calculationType = CalculationType.fromManufacturingEntityClass(manufacturingClass).name,
            branchName = title,
        )
    }

    data class BomNodeSaveResult(
        val branchId: BomradsBranchId,
        val changesetId: BomradsChangesetId,
        val bomNodeId: BomNodeId,
    )

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun saveNodesToBomrads(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        nodesCreationData: List<BomradsNodeCreationData>,
        mainBranch: BomradsBomNodeService.BranchState?,
    ): Flux<BomNodeSaveResult> =
        Mono.just(nodesCreationData).flatMapMany { bomradsNodeCreationData ->
            if (mainBranch == null) {
                bomradsTreeService
                    .createNodes(
                        accessCheck,
                        nodesCreationData = bomradsNodeCreationData,
                        projectId = projectId.toProjectId(),
                    ).concatMap { nodeCreationResult ->
                        notifyNodeCreation(accessCheck, projectId, bomradsNodeCreationData, nodeCreationResult)
                    }
            } else {
                Flux.fromIterable(bomradsNodeCreationData).flatMap { nodeCreationData ->
                    pushChangesService
                        .pushChangesToVariant(
                            accessCheck,
                            nodeCreationData.root,
                            nodeCreationData.pushChangesContext,
                            mainBranch,
                            branchTarget = BranchTarget.global,
                        ).map { branchView ->
                            nodeCreationData.root.setBranchView(branchView)
                            BomNodeSaveResult(
                                branchView.branch.id(),
                                branchView.changeset.id(),
                                nodeCreationData.root.bomNodeId(),
                            )
                        }
                }
            }
        }

    private fun notifyNodeCreation(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        bomradsNodeCreationData: List<BomradsNodeCreationData>,
        nodeCreationResult: BomNodeCreationResultDTO,
    ): Mono<BomNodeSaveResult> {
        val nodeCreationData =
            bomradsNodeCreationData.find { it.root.bomNodeId().toBomNodeId() == nodeCreationResult.rootBomNodeId }
        val minimalBranchViewDTO =
            createMinimalBranchView(
                projectId.toProjectId(),
                branchId = nodeCreationResult.branchId,
                changesetId = nodeCreationResult.changesetId,
                rootBomNodeId = nodeCreationResult.rootBomNodeId,
            )
        bomradsNodeCreationData.forEach { data ->
            data.root.setBranchView(minimalBranchViewDTO, null)
        }
        return eventSourcingModule
            .publishBomNodeCreated(
                accessCheck.accountId,
                accessCheck.accountName,
                projectId,
                nodeCreationResult.rootBomNodeId.toMongoBomNodeId(),
            ).thenReturn(
                BomNodeSaveResult(
                    nodeCreationResult.branchId,
                    nodeCreationResult.changesetId(),
                    nodeCreationData!!.root.bomNodeId(),
                ),
            )
    }
}

fun createMinimalBranchView(
    projectId: BomradsProjectId,
    branchId: BomradsBranchId,
    changesetId: BomradsChangesetId,
    rootBomNodeId: BomradsBomNodeId?,
): BranchViewDTO =
    BranchViewDTO(
        projectId,
        MinimalBranchDTO(
            branchId,
            main = false,
            global = false,
            published = false,
            creator = "",
            fromMaster = false,
            undoable = false,
            redoable = false,
            changesetId = changesetId,
            sourceId = null,
            bomNodeId = rootBomNodeId,
        ),
        changeset =
            MinimalChangesetDTO(
                changesetId,
                ChangeType.CREATION,
                "",
                null,
                "",
                isPreview = false,
                previousChangesetId = null,
                mergedChangesetId = null,
                trigger = null,
            ),
        sourceChangeset = null,
        snapshots = emptyList(),
    )
