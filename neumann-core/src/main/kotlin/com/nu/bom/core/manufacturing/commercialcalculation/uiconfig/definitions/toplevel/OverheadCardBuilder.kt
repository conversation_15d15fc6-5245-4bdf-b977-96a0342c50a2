package com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.toplevel

import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.api.dtos.ManufacturingDto
import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.StandardCalculationValue
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.CalculationOperationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.UniqueOperationIdentifier
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.IndirectOperationWithRateOrigin
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.OperationWithStandardCalculationValue
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.SubCalculatorWithRole
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.SumProdOperation
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.InternalCommercialCalculationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.TopLevelUiCardBuilder
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.helper.ColumnCreator
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.helper.LookupColumnCreator
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.CommercialFieldNameAndLookupCreatorHelper
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.CommercialFieldTableHelper
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.CommercialUiCardBuilderHelper
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.toplevel.CollapsingHelper.isDirectAddendOfEntryPoint
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.TableOption
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityLocator
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EqualCriteria
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableColumnDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.ListCriteria
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.LocatorType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.options.ColumnOptionsFeDto
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service

@Service
// @Profile("!test", "overheadCardBuilderTest")
class OverheadCardBuilder : TopLevelUiCardBuilder {
    override val cardIdentifier = "overhead"

    private val maxDepth = Int.MAX_VALUE
    private val fieldAndLookupHelper =
        CommercialFieldNameAndLookupCreatorHelper(
            allowedAggregationLevels = setOf(AggregationLevel.MANUFACTURED_MATERIAL, AggregationLevel.SOLD_MATERIAL),
            fallbackAggregationLevel = AggregationLevel.MANUFACTURED_MATERIAL,
        )
    private val cardHelper = CommercialUiCardBuilderHelper(::getEntryPoint, maxDepth, fieldAndLookupHelper)
    private val tableHelper = CommercialFieldTableHelper(::getEntryPoint, ::getColumnCreators, ::getIsCollapsed)

    override fun getTitles(vararg configs: InternalCommercialCalculationConfiguration) = cardHelper.getTitles(*configs)

    override fun getKpis(vararg configs: InternalCommercialCalculationConfiguration) = cardHelper.getKpis(*configs)

    override fun getCardFields(vararg configs: InternalCommercialCalculationConfiguration) = null

    override fun getTablesVariations(vararg configs: InternalCommercialCalculationConfiguration) = cardHelper.getTablesVariations(*configs)

    override fun getTableConfig(
        valueType: ValueType,
        tableOption: TableOption,
        procurementType: ManufacturingType,
        vararg configs: InternalCommercialCalculationConfiguration,
    ) = tableHelper.getTableConfig(valueType, tableOption, procurementType, *configs) { config, id ->
        getSubCalculatorEntityLocator(config, id, true)
    }

    private fun getIsCollapsed(
        opConfig: CalculationOperationConfiguration,
        operation: SumProdOperation,
    ): Boolean = !isDirectAddendOfEntryPoint(getEntryPoint(opConfig), operation) || operation.isDifferentiatedOverhead

    private fun getEntryPoint(opConfig: CalculationOperationConfiguration): OperationWithStandardCalculationValue =
        opConfig.getOperationOfStandardValue(StandardCalculationValue.TOTAL_OVERHEAD_VALUE)

    private fun getColumnCreators(config: InternalCommercialCalculationConfiguration): List<ColumnCreator> {
        return listOfNotNull(
            fieldAndLookupHelper.displayValueColumnCreator(config),
            LookupColumnCreator(
                FieldTableColumnDefinitionFeDto(
                    "subCalculatorUsage",
                    ColumnOptionsFeDto(displayDesignation = "Usage of sub calculator result", widthGrow = 2),
                ),
                { getMaybeSubCalculatorFromConfigForId(config.opConfig, it)?.subCalculator?.getDecisionFieldName() },
                { getSubCalculatorEntityLocator(config.opConfig, it, false) },
            ),
            fieldAndLookupHelper.baseLookupColumnCreator(config.valueType),
            fieldAndLookupHelper.interestTimeLookupColumnCreator(config.opConfig),
            fieldAndLookupHelper.rateLookupColumnCreator(config.opConfig),
            fieldAndLookupHelper.roleInHouseLookupColumnCreator(config.valueType),
            fieldAndLookupHelper.roleTotalLookupColumnCreator(config.valueType),
        )
    }

    fun getSubCalculatorEntityLocator(
        opConfig: CalculationOperationConfiguration,
        id: UniqueOperationIdentifier,
        showOnlyIfUsed: Boolean,
    ): EntityLocator? {
        val maybeSubCalculator = getMaybeSubCalculatorFromConfigForId(opConfig, id) ?: return null

        val entityCriteria = EqualCriteria(ManufacturingDto::ref.name, maybeSubCalculator.getEntityName())
        val usedCriteria =
            ListCriteria(
                ManufacturingDto::fields.name,
                listOf(
                    EqualCriteria(FieldParameter::name.name, maybeSubCalculator.subCalculator.getDecisionFieldName()),
                    EqualCriteria(FieldParameter::value.name, true),
                ),
            )

        return EntityLocator(
            criteria = if (showOnlyIfUsed) listOf(entityCriteria, usedCriteria) else listOf(entityCriteria),
            type = LocatorType.CHILD,
        )
    }

    fun getMaybeSubCalculatorFromConfigForId(
        opConfig: CalculationOperationConfiguration,
        id: UniqueOperationIdentifier,
    ): SubCalculatorWithRole? {
        return opConfig.tryGetOperation<IndirectOperationWithRateOrigin>(id)?.subCalculatorWithRole
    }
}
