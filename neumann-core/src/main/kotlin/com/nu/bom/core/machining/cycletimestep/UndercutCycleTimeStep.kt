package com.nu.bom.core.machining.cycletimestep

import com.nu.bom.core.machining.model.PrimaryMachiningCycleTimeStepWithTool
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.UndercutType

@EntityType(Entities.CYCLETIME_STEP)
class UndercutCycleTimeStep(name: String) : ManufacturingEntity(name) {

    override val extends = PrimaryMachiningCycleTimeStepWithTool(name)

    @Input
    @ReadOnly
    val displayToolType: Text? = null

    @Input
    @ReadOnly
    val undercutType: UndercutType? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    val cornerRadius: Length? = null
}
