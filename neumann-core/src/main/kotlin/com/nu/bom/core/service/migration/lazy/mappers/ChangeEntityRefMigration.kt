package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import org.springframework.stereotype.Service

@Service
class ChangeEntityRefMigration : ManufacturingModelEntityMapper {
    override val changeSetId = MigrationChangeSetId("2023-12-12-change-entityRef-adaption")

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity =
        entity.copyAll(
            entityRef = "ManufacturingStepCoreShooting",
        )
}
