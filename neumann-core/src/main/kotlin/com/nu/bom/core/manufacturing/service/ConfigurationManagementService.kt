package com.nu.bom.core.manufacturing.service

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.CalculationOperationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externaltointernalmapper.ExternalConfigToInternalConfigMapper.mapToInternalConfig
import com.nu.bom.core.manufacturing.commercialcalculation.rollupconfiguration.RollUpConfiguration
import com.nu.bom.core.manufacturing.fieldTypes.CO2CalculationOperationsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.CostCalculationOperationsConfigurationKey
import com.nu.bom.core.model.configurations.ConfigurationValue
import com.nu.bom.core.service.configurations.ConfigType
import com.nu.bom.core.user.AccessCheck
import reactor.core.publisher.Mono

interface ConfigurationManagementService {
    fun <T : ConfigurationValue, C : ConfigType<T>> getConfiguration(
        accessCheck: AccessCheck,
        type: C,
        key: ConfigurationKey<C>,
    ): Mono<T> = getConfigurationById(accessCheck, type, key.res)

    /**
     * Prefer method with typed configuration key if key is available
     * Use this method for test mocks (as the other will defer to this) or if you have a raw identifier
     */
    fun <T : ConfigurationValue> getConfigurationById(
        accessCheck: AccessCheck,
        type: ConfigType<T>,
        id: ConfigurationIdentifier,
    ): Mono<T>

    fun <T : ConfigurationValue> getDefaultConfiguration(
        accessCheck: AccessCheck,
        type: ConfigType<T>,
    ): Mono<T>

    fun getDefaultConfigurationKey(
        accessCheck: AccessCheck,
        type: ConfigType<*>,
    ): Mono<ConfigurationIdentifier>

    fun getDefaultCostModuleConfigurationIdentifier(
        accessCheck: AccessCheck,
        type: Model,
    ): Mono<ConfigurationIdentifier>

    fun getInternalCostCalculationConfiguration(
        accessCheck: AccessCheck,
        key: CostCalculationOperationsConfigurationKey,
        rollUpConfiguration: RollUpConfiguration,
    ): Mono<CalculationOperationConfiguration> =
        getConfiguration(accessCheck, ConfigType.CommercialCostOperations, key).map {
            mapToInternalConfig(ValueType.COST, it, rollUpConfiguration)
        }

    fun getInternalCO2CalculationConfiguration(
        accessCheck: AccessCheck,
        key: CO2CalculationOperationsConfigurationKey,
        rollUpConfiguration: RollUpConfiguration,
    ): Mono<CalculationOperationConfiguration> =
        getConfiguration(accessCheck, ConfigType.CommercialCo2Operations, key).map {
            mapToInternalConfig(ValueType.CO2, it, rollUpConfiguration)
        }
}
