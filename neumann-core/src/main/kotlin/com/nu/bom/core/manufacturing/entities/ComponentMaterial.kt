package com.nu.bom.core.manufacturing.entities

import com.nu.bom.core.manufacturing.annotations.CompositeMandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.FieldName
import com.nu.bom.core.manufacturing.annotations.FilteredChildren
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntityContext
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.RequiredFor
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.executioncontext.fieldtypes.MaterialClassField
import com.nu.bom.core.manufacturing.defaults.NullProvider
import com.nu.bom.core.manufacturing.entities.RawMaterial.Companion.DENSITY_MASTER_DATA_FIELD
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Currency
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.DensityUnits
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.manufacturing.fieldTypes.EmissionUnits
import com.nu.bom.core.manufacturing.fieldTypes.MaterialFurnaceType
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.utils.findResultByTextAttribute
import com.nu.bom.core.utils.getResultWithSelectedAttributesNullable
import org.bson.types.ObjectId
import java.math.BigDecimal

@EntityType(Entities.COMPONENT_MATERIAL, userCreatable = true)
class ComponentMaterial(
    res: String,
) : ManufacturingEntity(res) {
    override val extends = BaseMaterial(res)

    @ReadOnly
    fun commercialMaterialType(): MaterialClassField = MaterialClassField.RAW_MATERIAL

    @Input
    @Path("/api/currency{bomPath}{branchPath}", false)
    fun baseCurrency(materialBaseCurrency: Currency?): Currency = materialBaseCurrency ?: Currency("EUR")

    @Input
    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    @CompositeMandatoryForEntity(
        [
            MandatoryForEntity(index = 100, context = MandatoryForEntityContext.CREATE_FROM_MASTERDATA),
        ],
    )
    fun pricePerUnit(materialBasePrice: Money?): Money? = materialBasePrice

    // needed for the case of "Add manual material component" option in masterdata tab
    // when adding a Material Component
    @Input
    @ObjectView(ObjectView.MATERIAL, 50)
    @CompositeMandatoryForEntity(
        [
            MandatoryForEntity(index = 10, context = MandatoryForEntityContext.CREATE_MANUAL, computed = true),
        ],
    )
    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    @RequiredFor([])
    fun materialBasePrice(
        headerKey: Text?,
        @Default(NullProvider::class)
        location: Text?,
        materialProcessFurnaceType: MaterialFurnaceType,
        @Default(NullProvider::class)
        @FieldName("value")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_PRICE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToValue: Map<ObjectId, Money?>?,
        @Default(NullProvider::class)
        @FieldName("location")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_PRICE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToLocationKey: Map<ObjectId, Text?>?,
        @Default(NullProvider::class)
        @FieldName("materialProcessFurnaceType")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_PRICE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        furnaceTypeFromMasterdataPrice: Map<ObjectId, MaterialFurnaceType?>?,
        @Default(NullProvider::class)
        @FieldName("headerKey")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_PRICE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToHeaderKey: Map<ObjectId, Text>,
    ): Money? =
        getResultWithSelectedAttributesNullable(
            resultMap =
                mapIdToValue?.let { map ->
                    map.filter { it.value != null }.mapValues { e -> e.value as Money }
                },
            attribute1Map = mapIdToHeaderKey,
            attribute2Map = mapIdToLocationKey,
            attribute3Map = furnaceTypeFromMasterdataPrice,
            currentAttribute1 = headerKey,
            currentAttribute2 = location,
            currentAttribute3 = materialProcessFurnaceType,
        )

    @Input
    @CompositeMandatoryForEntity(
        [
            MandatoryForEntity(index = 200, context = MandatoryForEntityContext.CREATE_MANUAL),
            MandatoryForEntity(index = 200, context = MandatoryForEntityContext.CREATE_FROM_MASTERDATA, readOnly = true),
        ],
    )
    @DefaultUnit(DefaultUnit.GRAM_PER_CCM)
    fun density(
        headerKey: Text?,
        @Default(NullProvider::class)
        @FieldName(DENSITY_MASTER_DATA_FIELD)
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        densityFromClassification: Map<ObjectId, Density?>?,
        @Default(NullProvider::class)
        @FieldName("headerKey")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToHeaderKeyFromClassification: Map<ObjectId, Text>?,
    ): Density? =
        findResultByTextAttribute(
            headerKey,
            densityFromClassification,
            mapIdToHeaderKeyFromClassification,
        )

    @Input
    @CompositeMandatoryForEntity(
        [
            MandatoryForEntity(index = 4, context = MandatoryForEntityContext.CREATE_FROM_MASTERDATA),
        ],
    )
    fun dimension() = Dimension.getDefault(calculationContextEntity!!.getEntityTypeAnnotationOrThrow())!!

    // todo: link to classification field
    @Input
    @CompositeMandatoryForEntity(
        [
            MandatoryForEntity(index = 300, context = MandatoryForEntityContext.CREATE_MANUAL),
            MandatoryForEntity(index = 300, context = MandatoryForEntityContext.CREATE_FROM_MASTERDATA),
        ],
    )
    fun ratio(): Rate = Rate(BigDecimal.ZERO)

    fun weightedRatio(
        @Parent(Entities.MATERIAL)
        ratioSum: Rate,
        ratio: Rate,
    ): Rate = if (ratioSum.res > BigDecimal.ZERO) ratio.div(ratioSum) else Rate(BigDecimal.ZERO)

    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun weightedPrice(
        weightedRatio: Rate,
        pricePerUnit: Money,
    ): Money = Money(weightedRatio.res * pricePerUnit.res)

    fun weightedDensity(
        weightedRatio: Rate,
        density: Density,
    ): Density = Density(weightedRatio.res * density.inGPerCcm, DensityUnits.GRAM_PER_CCM)

    @Input
    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun gCO2perKg() = Emission(0.0, EmissionUnits.KILOGRAM_CO2E)
}
