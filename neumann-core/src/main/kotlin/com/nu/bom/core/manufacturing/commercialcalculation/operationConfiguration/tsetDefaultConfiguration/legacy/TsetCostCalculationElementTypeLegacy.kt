package com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.legacy

import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCalculationElementType

@Deprecated("Dont Use", replaceWith = ReplaceWith("TsetCostCalculationElementType"))
@Suppress("squid:S1192") // Suppresses duplicate string warning
enum class TsetCostCalculationElementTypeLegacy(override val fieldName: String, override val short: String, override val long: String) :
    TsetCalculationElementType {
    // material
    DIRECT_RAW_MATERIAL_COSTS("DirectRawMaterialCost", "DiRm", "Direct raw material"),
    DIRECT_PURCHASE_PARTS_COSTS("DirectPurchasePartCosts", "DiPp", "Direct purchase part"),
    DIRECT_MATERIAL_COSTS("DirectMaterialCosts", "DiM", "Direct material"),

    RAW_MATERIAL_SCRAP_COSTS("RawMaterialScrapCosts", "RmScrap", "Raw material scrap"),
    PURCHASE_PARTS_SCRAP_COSTS("PurchasePartsScrapCosts", "PpScrap", "Purchase part scrap"),
    MATERIAL_SCRAP_COSTS("MaterialScrapCosts", "MScrap", "Material scrap"),

    RAW_MATERIAL_OVERHEAD_COSTS("RawMaterialOverheadCosts", "RmOv", "Raw material overhead"),
    PURCHASE_PARTS_OVERHEAD_COSTS("PurchasePartsOverheadCosts", "PpMOv", "Purchase part material overhead"),
    MATERIAL_OVERHEAD_COSTS("MaterialOverheadCosts", "MOv", "Material overhead"),

    RAW_MATERIAL_INTEREST_COSTS("RawMaterialInterestCosts", "RmInt", "Raw material interest"),
    PURCHASE_PARTS_INTEREST_COSTS("PurchasePartsInterestCosts", "PpInt", "Purchase part interest"),
    MATERIAL_INTEREST_COSTS("MaterialInterestCosts", "MInt", "Material interest"),

    MATERIAL_COSTS("MaterialCosts", "M", "Material"),

    // manufacturing
    LABOR_COSTS("LaborCosts", "Labor", "Labor"),
    MACHINE_DEPRECIATION_COSTS("MachineDepreciationCosts", "MachDeprec", "Machine depreciation"),
    MACHINE_INTEREST_COSTS("MachineInterestCosts", "MachInter", "Machine interest"),
    MACHINE_AREA_COSTS("MachineAreaCosts", "MachArea", "Machine area"),
    MACHINE_FIX_COSTS("MachineFixCosts", "MachFix", "Machine fix"),
    MACHINE_ENERGY_COSTS("MachineEnergyCosts", "MachEnergy", "Machine energy"),
    MACHINE_MAINTENANCE_COSTS("MachineMaintenanceCosts", "MachMaint", "Machine maintenance"),
    MACHINE_OPERATION_SUPPLY_COSTS("MachineOperationSupplyCosts", "MachOpSupply", "Machine operation supply"),
    MACHINE_VARIABLE_COSTS("MachineVariableCosts", "MachVar", "Machine variable"),
    MACHINE_COSTS("MachineCosts", "Mach", "Machine"),
    ROUGH_MACHINE_COSTS("RoughMachineCosts", "RoughMach", "Rough machine"),
    TOOL_ALLOCATION_COSTS("ToolAllocationCosts", "ToolAlloc", "Tool allocation"),
    TOOL_INTEREST_COSTS("ToolInterestCosts", "ToolInter", "Tool interest"),
    TOOL_ALLOCATION_AND_INTEREST_COSTS("ToolAllocationAndInterestCosts", "ToolAlloc&Int", "Tool allocation and interest"),
    TOOL_MAINTENANCE_COSTS("ToolMaintenanceCosts", "ToolMaint", "Tool maintenance"),
    TOOL_COSTS("ToolCosts", "Tool", "Tool"),
    ROUGH_PROCESS_COSTS("RoughProcessCosts", "Rough", "Rough manufacturing step"),
    DIRECT_MANUFACTURING_COSTS("DirectManufacturingCosts", "DMC", "Direct manufacturing"),
    MANUFACTURING_SCRAP_COSTS("ManufacturingScrapCosts", "MfgScrap", "Manufacturing scrap"),
    MANUFACTURING_OVERHEAD_COSTS("ManufacturingOverheadCosts", "MfgOver", "Manufacturing overhead"),
    MANUFACTURING_COSTS_AFTER_DIRECT_MANUFACTURING_COSTS(
        "ManufacturingCostsAfterDirectManufacturingCosts",
        "AftDiMan",
        "Manufacturing after direct manufacturing",
    ),
    MANUFACTURING_COSTS_2("ManufacturingCosts2", "Mfg2", "Manufacturing 2"),
    MANUFACTURING_INTEREST_COSTS("ManufacturingInterestCosts", "MfgInter", "Manufacturing interest"),
    MANUFACTURING_COSTS_3("ManufacturingCosts3", "Mfg3", "Manufacturing 3"),
    PRODUCTION_COSTS("ProductionCosts", "Prod", "Production"),

    // overheads
    OVERHEAD_BASE_RM("OverheadBaseRm", "OverBaseRm", "Overhead base raw material"),
    OVERHEAD_BASE_PP("OverheadBasePp", "OverBasePp", "Overhead base purchase parts"),
    OVERHEAD_BASE_MFG("OverheadBaseMfg", "OverBaseMfg", "Overhead base manufacturing"),

    // special direct costs
    DEVELOPMENT_COSTS("DevelopmentCosts", "Dev", "Development"),
    RAMP_UP_COSTS("RampUpCosts", "RampUp", "Ramp up"),
    PACKAGING_AND_CARRIER_COSTS("PackagingAndCarrierCosts", "Pack&Carrier", "Packaging and carrier"),
    SPECIAL_DIRECT_COSTS("SpecialDirectCosts", "SpecialDC", "Special direct cost"),

    // overheads after production
    SALES_AND_GENERAL_ADMINISTRATION_COSTS("SalesAndGeneralAdministrationCosts", "SG&A", "Sales, general and administration"),
    RESEARCH_AND_DEVELOPMENT_COSTS("ResearchAndDevelopmentCosts", "R&D", "Research and development"),
    BUSINESS_RISK_COSTS("BusinessRiskCosts", "Risk", "Business risk"),
    INTEREST_ON_FINISHED_PRODUCT("InterestOnFinishProductStock", "ProdInter", "Interest on finished product stock"),
    OTHER_EXPENDITURES_AFTER_PC("OtherExpendituresAfterPC", "OtherAP", "Other expenditures after production"),
    DIRECT_OVERHEADS_AFTER_PC("DirectOverheadsAfterPC", "DiOverheadsAP", "Direct overheads after production"),
    OVERHEADS_AFTER_PC("OverheadsAfterPC", "OverheadsAP", "Overheads after production"),

    // profit
    BEFORE_PROFIT_COSTS("BeforeProfitCosts", "BeforeProfit", "Before profit"),
    PROFIT("Profit", "Profit", "Profit"),

    // terms of payment
    INTEREST_FOR_TERMS_OF_PAYMENT("InterestForTermsOfPayment", "PaymentInter", "Interest for terms of payment"),
    DISCOUNT("Discount", "Discount", "Discount"),
    TERMS_OF_PAYMENT("TermsOfPayment", "ToP", "Terms of payment"),

    // inco terms
    TRANSPORT_COSTS("TransportCosts", "Transport", "Transport"),
    CUSTOM_DUTY("CustomsDuty", "Custom", "Customs duty"),
    INCO_TERMS("IncoTerms", "Inco", "Inco terms"),

    // summation
    INDIRECT_COSTS_AFTER_PRODUCTION("IndirectCostsAfterProduction", "CostAP", "Indirect after production"),

    // special direct costs for invests and one time payments
    DEVELOPMENT_COSTS_FOR_INVEST("DevelopmentCostsForInvest", "Dev-Inv", "Development for invest"),
    RAMP_UP_COSTS_FOR_INVEST("RampUpCostsForInvest", "RampUp-Inv", "Ramp up for invest"),
    PACKAGING_AND_CARRIER_COSTS_FOR_INVEST(
        "PackagingAndCarrierCostsForInvest",
        "Pack&Carrier-Inv",
        "Packaging and carrier for invest",
    ),
    TOTAL_COSTS_FOR_INVEST("TotalCostsForInvest", "Tot-Inv", "Total invest"),
    ALLOCATED_COSTS_FOR_INVEST("AllocatedCostForInvest", "All-Inv", "Allocated invest"),
    INTEREST_COSTS_FOR_INVEST("InterestCostsForInvest", "Int-Inv", "Allocated interest invest"),
    ALLOCATED_AND_INTEREST_COSTS_FOR_INVEST("AllocatedAndInterestCostsForInvest", "All&Int-Inv", "Allocated"),

    SALES_PRICE("SalesPrice", "Sales-Price", "Sales price"),
}
