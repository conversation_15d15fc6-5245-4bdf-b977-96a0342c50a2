package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.entities.KnowledgeManufacturingStep
import com.nu.bom.core.manufacturing.enums.ManufacturingStepType
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import org.springframework.stereotype.Service
import java.math.BigDecimal

@Service
class MdRmocOverwriteMigration : BaseMdRmocMigration() {
    companion object {
        private val defaultRmocRatesFromMd =
            mapOf(
                ManufacturingStepType.STANDARD to 0.0.toBigDecimal(),
                ManufacturingStepType.ELECTRONICS_PCBA to 0.30.toBigDecimal(),
            )

        private val fallbackRmocRate = 0.15.toBigDecimal()
    }

    override val changeSetId = MigrationChangeSetId("md-rmoc-overwrite")

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity =
        entity.copyAll(
            fieldWithResults = setRmocToInput(entity, entity.fieldWithResults),
        )

    private fun setRmocToInput(
        entity: ManufacturingModelEntity,
        fields: Map<String, FieldResultModel>,
    ): Map<String, FieldResultModel> {
        if (entity.clazz == KnowledgeManufacturingStep::class.simpleName) {
            val existingRmocRate = (fields["RMOCRate"]?.value as Number?)?.let { BigDecimal(it.toString()) }
            val defaultMdRmocRate = getStepTypeForKnowledgeStep(fields)?.let { defaultRmocRatesFromMd[it] ?: fallbackRmocRate }
            if (defaultMdRmocRate?.compareTo(existingRmocRate) == 0) {
                // no need to set rmoc rate to input, since the MD default rates and the legacy nu-ledge rates match
                return fields
            }
        }
        return fields
            .toMutableMap()
            .apply {
                computeIfPresent("RMOCRate") { _, field -> field.copyAll(source = FieldResult.SOURCE.I.name) }
            }
    }
}
