package com.nu.bom.core.manufacturing.enums

import com.nu.bom.core.manufacturing.entities.Attachment
import com.nu.bom.core.manufacturing.entities.BaseEntityFields
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.BomEntry
import com.nu.bom.core.manufacturing.entities.ComponentMaterial
import com.nu.bom.core.manufacturing.entities.Consumable
import com.nu.bom.core.manufacturing.entities.CycleTimeStep
import com.nu.bom.core.manufacturing.entities.CycleTimeStepGroup
import com.nu.bom.core.manufacturing.entities.ElectronicComponent
import com.nu.bom.core.manufacturing.entities.ExchangeRate
import com.nu.bom.core.manufacturing.entities.ExchangeRates
import com.nu.bom.core.manufacturing.entities.Labor
import com.nu.bom.core.manufacturing.entities.Machine
import com.nu.bom.core.manufacturing.entities.ManualCopiedManufacturingStep
import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.entities.ManualMaterialV2
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ManufacturingStep
import com.nu.bom.core.manufacturing.entities.ManufacturingStepLine
import com.nu.bom.core.manufacturing.entities.Material
import com.nu.bom.core.manufacturing.entities.MaterialGeometry
import com.nu.bom.core.manufacturing.entities.PriceComponentEntity
import com.nu.bom.core.manufacturing.entities.RawMaterial
import com.nu.bom.core.manufacturing.entities.Setup
import com.nu.bom.core.manufacturing.entities.SpecialDirectCost
import com.nu.bom.core.manufacturing.entities.TestObjectA
import com.nu.bom.core.manufacturing.entities.TestObjectB
import com.nu.bom.core.manufacturing.entities.Tool
import com.nu.bom.core.manufacturing.entities.fti.MethodPlanFeature
import com.nu.bom.core.manufacturing.entities.fti.MethodPlanStage
import com.nu.bom.core.manufacturing.entities.fti.ToolCostRow
import com.nu.bom.core.manufacturing.entities.fti.ToolCostStructure
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorAluminiumEmissionsEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorAluminiumShareEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorCastExcipientsEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorCountryInfoEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorElectricityEmissionsEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorElectricityPriceEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorFloorSpacePriceEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorInterestEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorLaborBurdenEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorNaturalGasEmissionsEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorNaturalGasPriceEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorOxygenPriceEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorWageEntity
import com.nu.bom.core.manufacturing.entities.masterdata.exchangerates.MasterdataExchangeRateEntity
import com.nu.bom.core.manufacturing.entities.masterdata.material.MasterdataMaterialClassificationSchemaEntity
import com.nu.bom.core.manufacturing.entities.masterdata.material.MasterdataMaterialClassificationValueEntity
import com.nu.bom.core.manufacturing.entities.masterdata.material.MasterdataMaterialEmissionEntity
import com.nu.bom.core.manufacturing.entities.masterdata.material.MasterdataMaterialParent
import com.nu.bom.core.manufacturing.entities.masterdata.material.MasterdataMaterialPriceEntity
import com.nu.bom.core.manufacturing.entities.masterdata.overheads.MasterdataInterestEntity
import com.nu.bom.core.manufacturing.entities.masterdata.overheads.MasterdataOverheadEntity
import com.nu.bom.core.manufacturing.entities.toolmaintenance.DetailedToolMaintenanceCalculationEntity
import com.nu.bom.core.manufacturing.entities.transport.TransportRoute
import com.nu.bom.core.manufacturing.extension.BaseCO2MaterialProcessing
import kotlin.reflect.KClass

enum class Entities(
    val clazz: KClass<out ManufacturingEntity>? = null,
    val plural: String = "",
    val manualClazz: KClass<out ManufacturingEntity>? = null,
    val factory: ManufacturingEntityFactory = ManufacturingWithRef,
    val showInBomExplorer: Boolean = false,
) {
    MANUFACTURING(
        clazz = Manufacturing::class,
        manualClazz = ManualManufacturing::class,
        factory = BaseManufacturingFactory,
    ),
    MANUFACTURING_STEP(ManufacturingStep::class, manualClazz = ManualCopiedManufacturingStep::class, showInBomExplorer = true),
    CYCLETIME_STEP(CycleTimeStep::class, "CycleTimeSteps"),
    CYCLETIME_STEP_GROUP(CycleTimeStepGroup::class, "CycleTimeStepGroups"),
    MACHINE(Machine::class, "Machines", showInBomExplorer = true),
    LABOR(Labor::class, "Labor", showInBomExplorer = true),
    TOOL(Tool::class, "Tools", showInBomExplorer = true),
    SETUP(Setup::class, "Setup"),
    MATERIAL(RawMaterial::class, "RawMaterial", showInBomExplorer = true),
    PROCESSED_MATERIAL(Material::class, "RawMaterial", manualClazz = ManualMaterialV2::class, showInBomExplorer = true),
    CONSUMABLE(Consumable::class, "Consumables", showInBomExplorer = true),
    C_PART(ElectronicComponent::class, "CParts", showInBomExplorer = true),

    EXCHANGE_RATES(ExchangeRates::class),
    EXCHANGE_RATE(ExchangeRate::class, "ExchangeRates"),
    ATTACHMENT(Attachment::class, "Attachments"),
    TTYPE(TestObjectA::class, "Testing"),
    TTYPE2(TestObjectB::class, "Testing"),
    BOM_ENTRY(BomEntry::class, "BomEntries", showInBomExplorer = true),
    SYSTEM_PARAMETER(null, "parameter"),
    OVERHEAD_SUB_CALCULATOR,
    DETAILED_TOOL_MAINTENANCE_CALCULATOR(DetailedToolMaintenanceCalculationEntity::class),
    PRICE_COMPONENT(PriceComponentEntity::class),

    BASE(BaseEntityFields::class),
    CO2_PROCESSING_MATERIAL(BaseCO2MaterialProcessing::class),
    NONE,
    ANY,

    @Deprecated("Dont use")
    PART,

    @Deprecated("Dont use")
    PART_TARGET,
    COMPONENT_MATERIAL(ComponentMaterial::class, "ComponentMaterials"),
    TRANSPORT_ROUTE(TransportRoute::class),
    SPECIAL_DIRECT_COST(SpecialDirectCost::class, "SpecialDirectCosts"),
    GROUP(ManufacturingStepLine::class),

    MD_OVERHEADS_PARENT,
    MD_OVERHEAD(MasterdataOverheadEntity::class),
    MD_INTERESTS_PARENT,
    MD_INTEREST(MasterdataInterestEntity::class),
    METHOD_PLAN_STAGE(MethodPlanStage::class),
    METHOD_PLAN_FEATURE(MethodPlanFeature::class),
    TOOL_COST_STRUCTURE(ToolCostStructure::class),
    TOOL_COST_ROW(ToolCostRow::class),
    MATERIAL_GEOMETRY(MaterialGeometry::class),

    MD_EXCHANGERATE_PARENT,
    MD_EXCHANGERATE(MasterdataExchangeRateEntity::class),

    MD_COSTFACTORS_PARENT,
    MD_COSTFACTORS_WAGE(MasterdataCostFactorWageEntity::class),
    MD_COSTFACTORS_LABOR_BURDEN(MasterdataCostFactorLaborBurdenEntity::class),
    MD_COSTFACTORS_ELECTRICITY_PRICE(MasterdataCostFactorElectricityPriceEntity::class),
    MD_COSTFACTORS_NATURAL_GAS_PRICE(MasterdataCostFactorNaturalGasPriceEntity::class),
    MD_COSTFACTORS_ELECTRICITY_EMISSIONS(MasterdataCostFactorElectricityEmissionsEntity::class),
    MD_COSTFACTORS_NATURAL_GAS_EMISSIONS(MasterdataCostFactorNaturalGasEmissionsEntity::class),
    MD_COSTFACTORS_FLOOR_SPACE_PRICE(MasterdataCostFactorFloorSpacePriceEntity::class),
    MD_COSTFACTORS_INTEREST(MasterdataCostFactorInterestEntity::class),
    MD_COSTFACTORS_ALUMINIUM_SHARE(MasterdataCostFactorAluminiumShareEntity::class),
    MD_COSTFACTORS_ALUMINIUM_EMISSIONS(MasterdataCostFactorAluminiumEmissionsEntity::class),
    MD_COSTFACTORS_OXYGEN_PRICE(MasterdataCostFactorOxygenPriceEntity::class),
    MD_COSTFACTORS_CAST_EXCIPIENTS_PRICE(MasterdataCostFactorCastExcipientsEntity::class),
    MD_COSTFACTORS_COUNTRY_INFO(MasterdataCostFactorCountryInfoEntity::class),

    MD_MATERIAL_PARENT(MasterdataMaterialParent::class),
    MD_MATERIAL_PRICE(MasterdataMaterialPriceEntity::class),
    MD_MATERIAL_CLASSIFICATION_SCHEMA(MasterdataMaterialClassificationSchemaEntity::class),
    MD_MATERIAL_CLASSIFICATION_VALUE(MasterdataMaterialClassificationValueEntity::class),
    MD_MATERIAL_EMISSION(MasterdataMaterialEmissionEntity::class),
    ;

    companion object {
        private val valuesOfEnities: Set<String> = Entities.values().map { it.name }.toSet()

        private fun isEntitiesValue(value: String): Boolean = valuesOfEnities.contains(value)

        fun tryValueOf(value: String): Entities? {
            if (isEntitiesValue(value)) {
                return Entities.valueOf(value)
            }
            return null
        }
    }
}

interface ManufacturingEntityFactory {
    fun create(
        clazz: Class<out ManufacturingEntity>,
        entityName: String,
        args: Map<String, Any?>,
    ): ManufacturingEntity

    /**
     * Creates a new ManufacturingEntity instance via the 'entityId' single-argument constructor.
     */
    fun createNamedEntity(
        clazz: Class<out ManufacturingEntity>,
        entityName: String,
    ): ManufacturingEntity {
        val constructor =
            checkNotNull(clazz.getConstructor(String::class.java)) {
                "Entity class ${clazz.simpleName} does not provide a constructor that takes a single string parameter!"
            }
        val instance =
            checkNotNull(constructor.newInstance(entityName)) {
                "Could not create instance of class ${clazz.simpleName} with argument ´$entityName´"
            }

        return checkNotNull(instance as? ManufacturingEntity) {
            "Class ${clazz.simpleName} cannot be cast to a ´ManufacturingEntity´"
        }
    }

    companion object {
        const val ENTITY_REF_ARG = "ref"
    }
}

object ManufacturingWithRef : ManufacturingEntityFactory {
    override fun create(
        clazz: Class<out ManufacturingEntity>,
        entityName: String,
        args: Map<String, Any?>,
    ): ManufacturingEntity {
        val entity = createNamedEntity(clazz, entityName)
        val ref = args[ManufacturingEntityFactory.ENTITY_REF_ARG] as String?

        if (ref != null) {
            entity.entityRef = ref
        }

        return entity
    }
}

/**
 * Creates a new instance of a Manufacturing type.
 */
object BaseManufacturingFactory : ManufacturingEntityFactory {
    override fun create(
        clazz: Class<out ManufacturingEntity>,
        entityName: String,
        args: Map<String, Any?>,
    ): ManufacturingEntity {
        // val partName = args["partName"] as String?

        val key = args["key"] as String?
        val ref = args[ManufacturingEntityFactory.ENTITY_REF_ARG] as String?
        val user = args["user"] as String?
        val title = args["title"] as String?

        val entity =
            clazz.getConstructor(String::class.java)
                .newInstance(entityName) as BaseManufacturing

        // TODO kw can we nuke this nonsense or not
        // entity.partInfo = PartInfo(args)

        // if (partName != null) {
        //     entity.partName = partName
        // }
        if (key != null) {
            entity.key = key
        }
        if (ref != null) {
            entity.entityRef = ref
        }
        if (user != null) {
            entity.responsible = user
        }
        if (title != null) {
            entity.title = title
        }
        return entity
    }
}
