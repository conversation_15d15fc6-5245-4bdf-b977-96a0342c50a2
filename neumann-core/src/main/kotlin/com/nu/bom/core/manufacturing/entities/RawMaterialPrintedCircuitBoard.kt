package com.nu.bom.core.manufacturing.entities

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MasterDataType
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import java.math.BigDecimal

@EntityType(Entities.MATERIAL)
@MasterDataType(com.nu.bom.core.manufacturing.enums.MasterDataType.RAW_MATERIAL_PCB)
@Deprecated("Dead code which we cannot delete due to BCT, see COST-73273")
class RawMaterialPrintedCircuitBoard(name: String) : ManufacturingEntity(name) {
    override val extends = RawMaterial(name)

    @Input
    fun designation(materialBaseDisplayDesignation: Text): Text = materialBaseDisplayDesignation

    fun quantity() = QuantityUnit(BigDecimal.ZERO)

    // do not calculate, it's just a baseCurrency vehicle
    fun costPerPart(): Money = Money(BigDecimal.ZERO)
}
