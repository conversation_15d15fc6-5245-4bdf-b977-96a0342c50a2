package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import org.springframework.stereotype.Service

@Service
class PcbsPerPanelMigration : ManufacturingModelEntityMapper {
    override val changeSetId = MigrationChangeSetId("2023-02-02-pcbs-per-panel-migration")

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity =
        entity.copyAll(
            initialFieldWithResults = migrateProcessField(entity.initialFieldWithResults),
            fieldWithResults = migrateProcessField(entity.fieldWithResults),
        )

    private fun migrateProcessField(fields: Map<String, FieldResultModel>): Map<String, FieldResultModel> {
        val deprecatedKey = "pcbsPerPanel"
        val newKey = "partsPerCycle"
        val deprecatedField = fields[deprecatedKey] ?: return fields
        val newField = deprecatedField.copyAll(type = "QuantityUnit", unit = null)
        return fields - deprecatedKey + (newKey to newField)
    }
}
