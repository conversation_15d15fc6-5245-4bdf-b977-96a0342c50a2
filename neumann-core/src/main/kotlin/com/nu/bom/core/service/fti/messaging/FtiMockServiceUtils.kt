package com.nu.bom.core.service.fti.messaging

import com.nu.bom.core.manufacturing.fieldTypes.fti.OperationContentType
import com.nu.bom.core.service.fti.BlankingResponseEvent
import com.nu.bom.core.service.fti.GeometryResponseEvent
import com.nu.bom.core.service.fti.NestingRequestEvent
import com.nu.bom.core.service.fti.NestingResponseEvent
import com.nu.bom.core.service.fti.ProcessResponseEvent
import com.nu.bom.core.service.fti.ProcessStep
import com.nu.bom.core.service.fti.ProgressiveNestingLayout
import com.nu.bom.core.service.fti.StripDetails
import java.math.BigDecimal

object FtiMockServiceUtils {
    val MATERIAL_THICKNESS = BigDecimal("50.0")
    val NET_WEIGHT = BigDecimal("2.5")
    val UNFOLDED_PART_WIDTH = BigDecimal("200.0")
    val UNFOLDED_PART_LENGTH = BigDecimal("250.0")

    val DEFAULT_DISTANCE_BETWEEN_BLANKS: BigDecimal = BigDecimal.ONE
    val DEFAULT_BLANK_TO_COIL_EDGE_DISTANCE: BigDecimal = BigDecimal.ONE

    val DEFAULT_ROTATION_ANGLE = BigDecimal("75.3")
    val DEFAULT_MATERIAL_UTILIZATION = BigDecimal("65.3")
    val DEFAULT_STRIP_WIDTH = BigDecimal("305.57")
    val DEFAULT_PITCH = BigDecimal("395.29")
    val DEFAULT_DEPLOYED_WEIGHT = BigDecimal("101.2")
    const val DEFAULT_WORKBENCH = 1
    const val DEFAULT_FEATURE = 1

    val DEFAULT_TOOL_WIDTH = BigDecimal("200.0")
    val DEFAULT_THEORETICAL_TOOL_LENGTH = BigDecimal("200.0")
    val DEFAULT_CALCULATED_PRESSING_FORCE = BigDecimal("300.0")

    private const val STEP_ONE = 1
    private const val STEP_TWO = 2
    private const val STEP_THREE = 3
    private const val STEP_FOUR = 4

    private const val LAYOUT_ONE = 0
    private const val LAYOUT_TWO = 1
    private const val LAYOUT_THREE = 2
    private const val LAYOUT_FOUR = 3

    private val DEFAULT_EXECUTION_TIME = BigDecimal.ONE
    const val GEOMETRY_IMAGE = "geometry.png"
    const val PART_FEATURES_IMAGE = "part-features.png"
    const val LAYOUT_IMAGE = "layout.png"
    const val PROCESS_IMAGE = "process.png"
    const val PROJECT_FTC = "project.ftc"

    fun geometryResponseEvent() =
        GeometryResponseEvent(
            ftcFileId = PROJECT_FTC,
            executionTimeInSeconds = DEFAULT_EXECUTION_TIME,
            importedGeometryFileId = GEOMETRY_IMAGE,
            materialThickness = MATERIAL_THICKNESS,
        )

    fun blankingResponseEvent() =
        BlankingResponseEvent(
            ftcFileId = PROJECT_FTC,
            executionTimeInSeconds = DEFAULT_EXECUTION_TIME,
            finalGeometryFileId = PART_FEATURES_IMAGE,
            netWeight = NET_WEIGHT,
            minBoxWidth = UNFOLDED_PART_WIDTH,
            minBoxHeight = UNFOLDED_PART_LENGTH,
            defaultStripDetails =
                StripDetails(
                    distanceBetweenBlanks = DEFAULT_DISTANCE_BETWEEN_BLANKS,
                    blankToCoilEdgeDistance = DEFAULT_BLANK_TO_COIL_EDGE_DISTANCE,
                    sideCarrierWidth = null,
                    centerCarrierWidth = null,
                    carrierToBlankInstance = null,
                ),
        )

    fun nestingResponseEvent(request: NestingRequestEvent): NestingResponseEvent {
        return NestingResponseEvent(
            ftcFileId = PROJECT_FTC,
            executionTimeInSeconds = DEFAULT_EXECUTION_TIME,
            isSelfConnecting = false,
            progressiveNestingLayouts = progressiveNestingLayouts(request),
        )
    }

    private fun progressiveNestingLayouts(request: NestingRequestEvent): List<ProgressiveNestingLayout> {
        val layouts =
            mutableListOf(
                layout(LAYOUT_ONE, null),
                layout(LAYOUT_TWO, null),
                layout(LAYOUT_THREE, null),
            )
        if (request.rotationAngles.size == 1) {
            layout(LAYOUT_FOUR, request.rotationAngles[0])
        }
        return layouts
    }

    private fun layout(
        x: Int,
        rotationAngle: BigDecimal?,
    ): ProgressiveNestingLayout {
        return ProgressiveNestingLayout(
            option = x,
            rotationAngle = rotationAngle ?: (DEFAULT_ROTATION_ANGLE + BigDecimal(x)),
            materialUtilization = DEFAULT_MATERIAL_UTILIZATION + BigDecimal(x),
            stripWidth = DEFAULT_STRIP_WIDTH + BigDecimal(x),
            pitch = DEFAULT_PITCH + BigDecimal(x),
            deployedWeight = DEFAULT_DEPLOYED_WEIGHT + BigDecimal(x),
            nestingFileId = LAYOUT_IMAGE,
            workbenchIdx = DEFAULT_WORKBENCH + x,
            featureIdx = DEFAULT_FEATURE + x,
        )
    }

    fun processResponseEvent() =
        ProcessResponseEvent(
            ftcFileId = PROJECT_FTC,
            executionTimeInSeconds = DEFAULT_EXECUTION_TIME,
            sizeFB = DEFAULT_TOOL_WIDTH,
            sizeLR = DEFAULT_THEORETICAL_TOOL_LENGTH,
            load = DEFAULT_CALCULATED_PRESSING_FORCE,
            summaryProcessFileId = PROCESS_IMAGE,
            process = processSteps(),
        )

    private fun processSteps(): List<ProcessStep> {
        return listOf(
            processStep(STEP_ONE),
            processStep(STEP_TWO),
            processStep(STEP_THREE),
            processStep(STEP_FOUR),
        )
    }

    private fun processStep(x: Int): ProcessStep {
        return ProcessStep(
            processId = "OP${x}0",
            numberOfHolesDirect = x,
            numberOfHolesCam = null,
            numberOfScrapsDirect = x,
            totalCuttingLengthDirect = BigDecimal(x),
            numberOfTrimSegmentsDirect = null,
            totalTrimLengthDirect = BigDecimal(x),
            numberOfTrimSegmentsCam = null,
            totalTrimLengthCam = null,
            numberOfFlangesDirect = x,
            totalFlangeLengthDirect = BigDecimal(x),
            numberOfFlangesCam = null,
            totalFlangeLengthCam = null,
            features =
                MutableList(x) {
                    OperationContentType.Selection.PIERCE
                },
        )
    }
}
