package com.nu.bom.core.service.configurations

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.CO2ElementConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.CalculationMethodologyConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.CostElementConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.CurrentCO2OperationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.CurrentCostOperationConfiguration
import com.nu.bom.core.model.configurations.BurrWeightConfiguration
import com.nu.bom.core.model.configurations.CalculationQualityConfiguration
import com.nu.bom.core.model.configurations.ConfigurationValue
import com.nu.bom.core.model.configurations.CostModulesConfiguration
import com.nu.bom.core.model.configurations.CurrentMasterdataConfiguration
import com.nu.bom.core.model.configurations.CustomFieldsConfiguration
import com.nu.bom.core.model.configurations.DefaultValuesConfiguration
import com.nu.bom.core.model.configurations.InjectionConfigurationV3
import com.nu.bom.core.model.configurations.PcbaConfiguration
import com.nu.bom.core.model.configurations.SandCastingConfiguration
import kotlin.reflect.KClass

enum class ConfigurationGroup(
    val value: String,
) {
    CALCULATION_ASSUMPTIONS("CalculationAssumptions"),
    COMMERCIAL_CALCULATION("CommercialCalculation"),
    COST_MODULES_VERSION("CostModulesVersion"),
}

open class ConfigType<T : ConfigurationValue>(
    val group: String,
    val type: String,
    val configClass: KClass<T>,
) {
    companion object {
        fun any(
            group: String,
            type: String,
        ) = ConfigType(group, type, ConfigurationValue::class)
    }

    override fun toString(): String = "${this::class.simpleName}(group=$group, type=$type, configClass=${configClass.simpleName})"

    sealed class Valid<T : ConfigurationValue>(
        group: ConfigurationGroup,
        type: String,
        configClass: KClass<T>,
    ) : ConfigType<T>(group.value, type, configClass)

    object DefaultValues : Valid<DefaultValuesConfiguration>(
        ConfigurationGroup.CALCULATION_ASSUMPTIONS,
        "DefaultValues",
        DefaultValuesConfiguration::class,
    )

    object CalculationQuality : Valid<CalculationQualityConfiguration>(
        ConfigurationGroup.CALCULATION_ASSUMPTIONS,
        "CalculationQuality",
        CalculationQualityConfiguration::class,
    )

    object BurrWeight : Valid<BurrWeightConfiguration>(
        ConfigurationGroup.CALCULATION_ASSUMPTIONS,
        "BurrWeight",
        BurrWeightConfiguration::class,
    )

    object InjectionCost : Valid<InjectionConfigurationV3>(
        ConfigurationGroup.CALCULATION_ASSUMPTIONS,
        "Injection",
        InjectionConfigurationV3::class,
    )

    object CalculationMethodology : Valid<CalculationMethodologyConfiguration>(
        ConfigurationGroup.COMMERCIAL_CALCULATION,
        "CalculationMethodology",
        CalculationMethodologyConfiguration::class,
    )

    object CommercialCostElements : Valid<CostElementConfiguration>(
        ConfigurationGroup.COMMERCIAL_CALCULATION,
        "CostCalculationElements",
        CostElementConfiguration::class,
    )

    object CommercialCostOperations : Valid<CurrentCostOperationConfiguration>(
        ConfigurationGroup.COMMERCIAL_CALCULATION,
        "CostCalculationOperations",
        CurrentCostOperationConfiguration::class,
    )

    object CommercialCo2Elements : Valid<CO2ElementConfiguration>(
        ConfigurationGroup.COMMERCIAL_CALCULATION,
        "Co2CalculationElements",
        CO2ElementConfiguration::class,
    )

    object CommercialCo2Operations : Valid<CurrentCO2OperationConfiguration>(
        ConfigurationGroup.COMMERCIAL_CALCULATION,
        "Co2CalculationOperations",
        CurrentCO2OperationConfiguration::class,
    )

    object Masterdata : Valid<CurrentMasterdataConfiguration>(
        ConfigurationGroup.COMMERCIAL_CALCULATION,
        "MasterdataConfiguration",
        CurrentMasterdataConfiguration::class,
    )

    object SandCasting : Valid<SandCastingConfiguration>(
        ConfigurationGroup.CALCULATION_ASSUMPTIONS,
        "SandCasting",
        SandCastingConfiguration::class,
    )

    object CustomFields : Valid<CustomFieldsConfiguration>(
        ConfigurationGroup.CALCULATION_ASSUMPTIONS,
        "CustomFields",
        CustomFieldsConfiguration::class,
    )

    object PcbaCost : Valid<PcbaConfiguration>(
        ConfigurationGroup.CALCULATION_ASSUMPTIONS,
        "Pcba",
        PcbaConfiguration::class,
    )

    object ChillCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.CHILL.name,
        CostModulesConfiguration::class,
    )

    object PrecCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.PREC.name,
        CostModulesConfiguration::class,
    )

    object VPrecCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.VPREC.name,
        CostModulesConfiguration::class,
    )

    object DcaCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.DCA.name,
        CostModulesConfiguration::class,
    )

    object OldInjCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.INJ.name,
        CostModulesConfiguration::class,
    )

    object InjCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.INJ2.name,
        CostModulesConfiguration::class,
    )

    object MInjCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.MINJ.name,
        CostModulesConfiguration::class,
    )

    object RInjCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.RINJ.name,
        CostModulesConfiguration::class,
    )

    object WhatCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.WHAT.name,
        CostModulesConfiguration::class,
    )

    object ChatCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.CHAT.name,
        CostModulesConfiguration::class,
    )

    object RRolCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.RROL.name,
        CostModulesConfiguration::class,
    )

    object DForCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.DFOR.name,
        CostModulesConfiguration::class,
    )

    object AForCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.AFOR.name,
        CostModulesConfiguration::class,
    )

    object SintCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.SINT.name,
        CostModulesConfiguration::class,
    )

    object SandCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.SAND.name,
        CostModulesConfiguration::class,
    )

    object CRolCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.CROL.name,
        CostModulesConfiguration::class,
    )

    object CExtCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.CEXT.name,
        CostModulesConfiguration::class,
    )

    object AlExCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.ALEX.name,
        CostModulesConfiguration::class,
    )

    object CoresCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.CORES.name,
        CostModulesConfiguration::class,
    )

    object RSwaCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.RSWA.name,
        CostModulesConfiguration::class,
    )

    object BarTCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.BART.name,
        CostModulesConfiguration::class,
    )

    object RawTCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.RAWT.name,
        CostModulesConfiguration::class,
    )

    object MillCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.MILL.name,
        CostModulesConfiguration::class,
    )

    object PCBCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.PCB.name,
        CostModulesConfiguration::class,
    )

    object PCBACostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.PCBA.name,
        CostModulesConfiguration::class,
    )

    object CuBeCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.CUBE.name,
        CostModulesConfiguration::class,
    )

    object LaStCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.LAST.name,
        CostModulesConfiguration::class,
    )

    object MagnCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.MAGN.name,
        CostModulesConfiguration::class,
    )

    object FTIPDSCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.FTIPDS.name,
        CostModulesConfiguration::class,
    )

    object FTITDSCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.FTITDS.name,
        CostModulesConfiguration::class,
    )

    object PboxCostModuleType : Valid<CostModulesConfiguration>(
        ConfigurationGroup.COST_MODULES_VERSION,
        Model.PBOX.name,
        CostModulesConfiguration::class,
    )
}
