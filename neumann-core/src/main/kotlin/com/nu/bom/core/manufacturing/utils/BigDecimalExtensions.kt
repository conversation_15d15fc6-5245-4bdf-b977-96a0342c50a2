package com.nu.bom.core.manufacturing.utils

import java.math.BigDecimal
import java.math.MathContext

fun BigDecimal.isZero(): Boolean = this.compareTo(BigDecimal.ZERO) == 0

fun BigDecimal.safeDivision(divisor: BigDecimal?): BigDecimal? =
    if (divisor == null || divisor.isZero()) {
        null
    } else {
        this.divide(divisor, MathContext.DECIMAL64)
    }

fun BigDecimal.safeDivision(
    divisor: BigDecimal?,
    errorDefault: BigDecimal,
): BigDecimal =
    if (divisor == null || divisor.isZero()) {
        errorDefault
    } else {
        this.divide(divisor, MathContext.DECIMAL64)
    }
