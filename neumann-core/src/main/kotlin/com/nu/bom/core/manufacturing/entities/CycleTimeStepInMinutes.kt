package com.nu.bom.core.manufacturing.entities

import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.Rate

@EntityType(Entities.CYCLETIME_STEP)
class CycleTimeStepInMinutes(name: String) : ManufacturingEntity(name) {

    override val extends = CycleTimeStep(name)

    @ObjectView(ObjectView.CYCLETIME_STEP, 3)
    @ReadOnly
    @DefaultUnit(DefaultUnit.MINUTE)
    fun adjustedTime(adjustmentRate: Rate, time: CycleTime): CycleTime {
        return extends.adjustedTime(adjustmentRate, time)
    }
}
