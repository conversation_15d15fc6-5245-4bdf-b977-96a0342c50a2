package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import kotlin.reflect.KClass

/**
 * This migration takes care of setting a newly added configuration key field
 * @param T type parameter of the Configuration Key class
 * @param configurationKeyFieldName Name of the field in the Entity
 * @param configurationIdentifier The configuration Identifier that should be used as value for the [configurationKeyFieldName] field
 * @param configurationKeyClazz The concrete type of the configuration key
 */
abstract class BaseConfigKeyMigration<T : ConfigurationKey<*>>(
    private val configurationKeyFieldName: String,
    private val configurationIdentifier: ConfigurationIdentifier,
    private val configurationKeyClazz: KClass<T>,
) : ManufacturingModelEntityMapper {
    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity =
        entity.copyAll(
            fieldWithResults = addConfigurationKey(entity.fieldWithResults, entity.version),
            initialFieldWithResults = addConfigurationKey(entity.initialFieldWithResults, entity.version),
        )

    private fun addConfigurationKey(
        fields: Map<String, FieldResultModel>,
        version: Int,
    ): Map<String, FieldResultModel> =
        if (fields.containsKey(configurationKeyFieldName)) {
            fields
        } else {
            fields +
                mapOf(
                    configurationKeyFieldName to
                        FieldResultModel(
                            version = version,
                            newVersion = version,
                            type = configurationKeyClazz.java.simpleName,
                            value = configurationIdentifier,
                            source = FieldResult.SOURCE.C.name,
                        ),
                )
        }
}
