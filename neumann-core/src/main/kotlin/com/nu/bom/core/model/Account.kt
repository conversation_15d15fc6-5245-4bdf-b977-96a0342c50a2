package com.nu.bom.core.model

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer
import com.querydsl.core.annotations.QueryEntity
import com.tset.core.service.account.AccountDataMappings
import org.bson.types.ObjectId
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedBy
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.mapping.Document
import java.util.Date

@Document(collection = Account.COLLECTION_NAME)
@QueryEntity
@JsonIgnoreProperties(ignoreUnknown = true)
@Suppress("ktlint:standard:backing-property-naming")
data class Account(
    val externalRoleName: String,
) {
    @Id
    @JsonSerialize(using = ToStringSerializer::class)
    var _id: ObjectId? = null

    @CreatedBy
    var createdBy: String? = null

    @CreatedDate
    var createdDate: Date? = null

    @LastModifiedBy
    var lastModifiedBy: String? = null

    @LastModifiedDate
    var lastModifiedDate: Date? = null

    var label: String? = null

    fun getAccountLabel() = label ?: externalRoleName

    var accountData: AccountDataMappings? = null

    fun getAccountId() = this._id ?: error("Id is not available for $this")

    fun getAccountIdStr() = this._id?.toHexString() ?: error("Id is not available for $this")

    companion object {
        fun from(
            id: String,
            roleName: String,
        ): Account = from(ObjectId(id), roleName)

        fun from(
            id: ObjectId,
            roleName: String,
        ): Account {
            val account = Account(externalRoleName = roleName)
            account._id = id
            return account
        }

        const val COLLECTION_NAME = "Accounts"
    }
}
