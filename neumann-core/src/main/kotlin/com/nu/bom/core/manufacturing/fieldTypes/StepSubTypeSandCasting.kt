package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.utils.toUpperSnakeCase

class StepSubTypeSandCasting(res: Selection) : SelectEnumFieldResult<StepSubTypeSandCasting.Selection, StepSubTypeSandCasting>(res) {

    enum class Selection(val value: String) {
        VERTICAL_SAND_CASTING("Vertical"),
        HORIZONTAL_SAND_CASTING("Horizontal")
    }

    companion object {

        val VERTICAL_SAND_CASTING = StepSubTypeSandCasting(Selection.VERTICAL_SAND_CASTING)
        val HORIZONTAL_SAND_CASTING = StepSubTypeSandCasting(Selection.HORIZONTAL_SAND_CASTING)

        fun valueOf(name: String): StepSubTypeSandCasting {

            return Selection.values().find {
                // check if any of
                it.value == name
            }?.let {
                StepSubTypeSandCasting(it)
            } // else try to cast from name
                ?: StepSubTypeSandCasting(Selection.valueOf(name.toUpperSnakeCase()))
        }
    }
}
