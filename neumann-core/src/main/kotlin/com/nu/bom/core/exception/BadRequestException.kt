package com.nu.bom.core.exception

import com.nu.bom.core.exception.readable.ErrorCode
import org.springframework.http.HttpStatus
import org.springframework.web.server.ResponseStatusException

@Deprecated("Use [UserException]s instead! Migrate existing [BadRequestException]s wherever you find them.")
class BadRequestException(val errorCode: ErrorCode, message: String = "") :
    ResponseStatusException(HttpStatus.BAD_REQUEST, message)
