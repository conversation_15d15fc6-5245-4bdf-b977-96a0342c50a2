package com.nu.bom.core.service

import com.nu.bom.core.api.dtos.AutocompleteContainer
import com.nu.bom.core.api.dtos.AutocompleteResponse
import com.nu.bom.core.manufacturing.entities.masterdata.material.MaterialConsumerExtension
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.service.ConfigurationManagementService
import com.nu.bom.core.manufacturing.utils.ManufacturingTreeUtils
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.configurations.masterdata.TechnologyToClassificationKeyMapper
import com.nu.bom.core.service.bomrads.AllChildren
import com.nu.bom.core.service.configurations.ConfigType
import com.nu.bom.core.service.configurations.MasterdataTsetConfigurationService.Companion.COST_FACTOR_REGION_FIELD_DEF_KEY
import com.nu.bom.core.service.configurations.MasterdataTsetConfigurationService.Companion.COST_FACTOR_VALID_FROM_FIELD_DEF_KEY
import com.nu.bom.core.service.masterdata.MdDetailCrudService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.mapOfNotNull
import com.nu.masterdata.dto.v1.detail.table.ClassificationFilterDto
import com.nu.masterdata.dto.v1.detail.table.DetailQueryDto
import com.nu.masterdata.dto.v1.detail.table.HeaderAndDetailDto
import com.nu.masterdata.dto.v1.detail.table.NullFilterDto
import com.nu.masterdata.dto.v1.header.HeaderDetailQueryResponseDto
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

@Service
class MaterialService(
    private val bomNodeService: BomNodeService,
    private val bomNodeConversionService: BomNodeConversionService,
    private val mdDetailCrudService: MdDetailCrudService,
    private val configurationManagementService: ConfigurationManagementService,
) {
    /**
     * Finds the first masterdata detail entry for the given key.
     *
     * @param key master data key
     */
    fun findMaterialByKey(
        accessCheck: AccessCheck,
        key: String,
    ): Mono<HeaderDetailQueryResponseDto> {
        return configurationManagementService
            .getDefaultConfiguration(accessCheck, ConfigType.Masterdata)
            .flatMap { mdConfiguration ->
                mdDetailCrudService.getHeaderDetailForHeaderKey(
                    accessCheck = accessCheck,
                    headerTypeKey = SimpleKeyDto(mdConfiguration.materialCo2Configuration.headerTypeKey),
                    headerKey = SimpleKeyDto(key),
                )
            }
    }

    fun getMaterialByTechnology(
        accessCheck: AccessCheck,
        technology: String,
    ): Mono<List<HeaderDetailQueryResponseDto>> {
        return configurationManagementService
            .getDefaultConfiguration(accessCheck, ConfigType.Masterdata)
            .flatMap { mdConfiguration ->
                mdDetailCrudService.postDetailEntriesSearchAsListOfHeaderDto(
                    accessCheck = accessCheck,
                    headerTypeKey = SimpleKeyDto(mdConfiguration.materialCo2Configuration.headerTypeKey),
                    detailQueryDto =
                        DetailQueryDto(
                            filters =
                                mapOf(
                                    SimpleKeyDto(COST_FACTOR_REGION_FIELD_DEF_KEY) to listOf(NullFilterDto()),
                                    SimpleKeyDto(COST_FACTOR_VALID_FROM_FIELD_DEF_KEY) to listOf(NullFilterDto()),
                                ),
                            classificationFilters =
                                mapOf(
                                    SimpleKeyDto(mdConfiguration.materialConfiguration.technologyClassificationTypeKey) to
                                        listOf(
                                            ClassificationFilterDto(
                                                TechnologyToClassificationKeyMapper.mapToClassificationKey(
                                                    technology,
                                                    mdConfiguration.technologyMapToClassificationKeys,
                                                ),
                                                false,
                                            ),
                                        ),
                                    SimpleKeyDto(mdConfiguration.materialConfiguration.materialClassificationTypeKey) to
                                        TechnologyToClassificationKeyMapper.mapToClassificationTypeMaterial(
                                            technology,
                                            mdConfiguration.materialConfiguration.rawMaterialConfiguration.rawMaterialClassifications,
                                        ).map {
                                            ClassificationFilterDto(
                                                it,
                                                false,
                                            )
                                        },
                                ),
                            sortOrder = null,
                        ),
                )
            }
    }

    fun getMaterialsByTypeAndTechnology(
        accessCheck: AccessCheck,
        rawMaterialType: MasterDataType,
        technology: String?,
    ): Flux<HeaderAndDetailDto> {
        return configurationManagementService
            .getDefaultConfiguration(accessCheck, ConfigType.Masterdata)
            .flatMap { mdConfiguration ->
                mdDetailCrudService.postAllDetailEntries(
                    accessCheck = accessCheck,
                    headerTypeKey = SimpleKeyDto(mdConfiguration.materialCo2Configuration.headerTypeKey),
                    detailQueryDto =
                        DetailQueryDto(
                            filters =
                                mapOf(
                                    SimpleKeyDto(COST_FACTOR_REGION_FIELD_DEF_KEY) to listOf(NullFilterDto()),
                                    SimpleKeyDto(COST_FACTOR_VALID_FROM_FIELD_DEF_KEY) to listOf(NullFilterDto()),
                                ),
                            classificationFilters =
                                mapOfNotNull(
                                    SimpleKeyDto(mdConfiguration.materialConfiguration.materialClassificationTypeKey) to
                                        TechnologyToClassificationKeyMapper.mapMaterialTypeToClassificationTypeMaterial(
                                            rawMaterialType,
                                            mdConfiguration.materialConfiguration.rawMaterialConfiguration.rawMaterialClassifications,
                                        ).map {
                                            ClassificationFilterDto(
                                                it,
                                                false,
                                            )
                                        },
                                    SimpleKeyDto(mdConfiguration.materialConfiguration.technologyClassificationTypeKey) to
                                        technology?.let {
                                            listOf(
                                                ClassificationFilterDto(
                                                    TechnologyToClassificationKeyMapper.mapToClassificationKey(
                                                        technology,
                                                        mdConfiguration.technologyMapToClassificationKeys,
                                                    ),
                                                    false,
                                                ),
                                            )
                                        },
                                ),
                            sortOrder = null,
                        ),
                ).map { headerAndDetails ->
                    headerAndDetails.content
                        .distinctBy { it.headerDto.key }
                        .sortedBy { it.headerDto.name }
                }
            }.flatMapMany { Flux.fromIterable(it) }
    }

    fun findMaterialsWithGroupInCalculation(
        accessCheck: AccessCheck,
        bomNodeId: BomNodeId,
        branchId: BranchId?,
    ) = bomNodeService.getBomNode(
        accessCheck,
        bomNodeId,
        branchId,
        loadingMode = AllChildren(bomNodeId),
    ).map { bomNode ->
        val manu = bomNodeConversionService.bomNodeToManufacturingCalculationTree(bomNode, null)
        val children = ManufacturingTreeUtils.getDescendantsWithType(manu, Entities.MATERIAL)
        val items =
            listOf(AutocompleteResponse("Other", "null", null)) +
                children.mapNotNull { material ->
                    val designation = (material.getFieldResult("displayDesignation")?.res as? String)
                    val headerKey = (material.getFieldResult(MaterialConsumerExtension::headerKey.name)?.res as? String)
                    if (headerKey != null) {
                        AutocompleteResponse(designation ?: headerKey, headerKey, null)
                    } else {
                        null
                    }
                }.distinct().sortedBy { it.name }

        AutocompleteContainer(null, items)
    }
}
