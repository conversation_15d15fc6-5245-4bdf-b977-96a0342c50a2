package com.nu.bom.core.service

import com.nu.bom.core.manufacturing.entities.Consumable
import com.nu.bom.core.manufacturing.entities.CycleTimeStep
import com.nu.bom.core.manufacturing.entities.Machine
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingStep
import com.nu.bom.core.manufacturing.entities.RawMaterial
import com.nu.bom.core.manufacturing.entities.StandardTool
import com.nu.bom.core.manufacturing.entities.Tool
import com.nu.bom.core.manufacturing.entities.ToolRough
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.utils.simpleName
import com.nu.bom.core.utils.toUpperSnakeCase
import org.apache.commons.text.StringEscapeUtils
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class MasterDataTypeService {
    val logger = LoggerFactory.getLogger(MasterDataTypeService::class.java)!!

    fun getMasterDataType(
        entityType: Entities,
        entityClass: String? = null,
    ): MasterDataType {
        if (entityClass == null) {
            return getMasterDataTypeByEntityType(entityType)
        }

        return (
            getMasterDataTypeByEntityClass(entityType, entityClass)
                .takeUnless { it == MasterDataType.NONE }
                // fallback to type inference by entity type for unknown classes
                ?: getMasterDataTypeByEntityType(entityType)
        ).also {
            val sanitizedLog =
                StringEscapeUtils.escapeJava(
                    "Resolved masterDataType=$it (for entityType=${entityType.name}, entityClass=$entityClass)",
                )
            logger.debug(sanitizedLog)
        }
    }

    private fun getMasterDataTypeByEntityClass(
        entityType: Entities,
        entityClass: String,
    ): MasterDataType {
        val sanitizedEntityClassName = StringEscapeUtils.escapeJava(entityClass)
        return when (val compString = entityClass.takeUnless { sanitizedEntityClassName.contains(".") } ?: entityClass.simpleName()) {
            Manufacturing::class.simpleName -> MasterDataType.MANUFACTURING
            ManufacturingStep::class.simpleName -> MasterDataType.MANUFACTURING_STEP
            CycleTimeStep::class.simpleName -> MasterDataType.CYCLETIME_STEP
            Machine::class.simpleName -> MasterDataType.MACHINE
            Tool::class.simpleName -> MasterDataType.TOOL
            ToolRough::class.simpleName -> MasterDataType.TOOL_ROUGH
            StandardTool::class.simpleName -> MasterDataType.STANDARD_TOOL
            // TODO: remove usage with NEUM-3479
            RawMaterial::class.simpleName -> MasterDataType.MATERIAL
            Consumable::class.simpleName -> MasterDataType.CONSUMABLE

            else ->
                when (entityType) {
                    // type inference by class name for system parameter entities
                    Entities.SYSTEM_PARAMETER -> MasterDataType.valueOf(compString.toUpperSnakeCase())
                    Entities.CO2_PROCESSING_MATERIAL -> MasterDataType.BASE_CO2_MATERIAL_PROCESSING
                    else -> MasterDataType.NONE
                }
        }
    }

    private fun getMasterDataTypeByEntityType(entityType: Entities): MasterDataType {
        val classFromType = entityType.clazz!!.java.canonicalName

        logger.trace(
            "Resolving MasterDataType based on EntityType default class: {} -> {} ",
            entityType.name,
            classFromType,
        )

        return getMasterDataTypeByEntityClass(entityType, classFromType)
    }
}
