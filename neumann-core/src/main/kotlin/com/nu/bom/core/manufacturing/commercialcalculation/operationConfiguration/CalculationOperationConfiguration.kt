package com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration

import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.StandardCalculationValue
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.CalculatedOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.IndirectOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.IndirectOperationWithRateOrigin
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.InputOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.InternalConfigurationOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.OperationWithStandardCalculationValue
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.SubCalculatorWithRole
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.SumProdOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.TransferOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.ProcurementTypeConfiguration
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.fieldTypes.NoCalcData
import com.nu.bom.core.utils.findDuplicates
import kotlin.reflect.KClass

/***
 * This class represents an aggregated configuration of element types and operations.
 * Operations are uses for the internal field building, whereas element types are mainly
 * used to provide customer facing naming.
 */
data class CalculationOperationConfiguration(
    val valueType: ValueType,
    // can not be private because of reified inline function
    val elementKeyAndLevelToCalculatedOperation: Map<UniqueOperationIdentifier, CalculatedOperation>,
    // can not be private because of reified inline function
    val elementKeyAndLevelToInputOperation: Map<UniqueOperationIdentifier, InputOperation>,
    val procurementTypeConfiguration: ProcurementTypeConfiguration,
) : NoCalcData {
    val allOperations = defineAllOperations()

    val allOperationsByElementKeys: Map<String, List<InternalConfigurationOperation>> =
        allOperations.values.groupBy { it.destinationElementKey }

    val inputOperationsByLevel: Map<AggregationLevel, List<InputOperation>> =
        elementKeyAndLevelToInputOperation.values.groupBy { it.origin }

    val calculatedOperationsByLevel: Map<AggregationLevel, List<CalculatedOperation>> =
        elementKeyAndLevelToCalculatedOperation.values.groupBy { it.origin }

    val allSubCalculatorEntities: Map<SubCalculatorWithRole, KClass<out ManufacturingEntity>> = defineAllNeededSubCalculatorEntities()

    private fun defineAllOperations(): Map<UniqueOperationIdentifier, InternalConfigurationOperation> {
        // if an input operation has the same key it will be overridden by the calculated operation
        // we need to keep the order of the calculated elements and have all inputs upfront
        val inputOnlyOperations =
            elementKeyAndLevelToInputOperation
                .filter { !elementKeyAndLevelToCalculatedOperation.containsKey(it.key) }
        return inputOnlyOperations + elementKeyAndLevelToCalculatedOperation
    }

    inline fun <reified T : InputOperation> getInputOperationsOfLevel(aggregationLevel: AggregationLevel): List<T> {
        return (inputOperationsByLevel[aggregationLevel] ?: listOf()).filterIsInstance<T>()
    }

    inline fun <reified T : CalculatedOperation> getCalculatedOperationsOfLevel(aggregationLevel: AggregationLevel): List<T> {
        return (calculatedOperationsByLevel[aggregationLevel] ?: listOf()).filterIsInstance<T>()
    }

    inline fun <reified T : InternalConfigurationOperation> getOperations(): List<T> {
        return allOperations.values.filterIsInstance<T>()
    }

    fun getAllOperationsIncludingPotentiallyDuplicatedDestinations(): List<InternalConfigurationOperation> {
        return elementKeyAndLevelToInputOperation.values + elementKeyAndLevelToCalculatedOperation.values
    }

    inline fun <reified T : InternalConfigurationOperation> tryGetOperation(uniqueOperationIdentifier: UniqueOperationIdentifier): T? {
        return allOperations[uniqueOperationIdentifier] as? T
    }

    inline fun <reified T : InternalConfigurationOperation> getAllOperations(elementKey: String): List<T> {
        return (allOperationsByElementKeys[elementKey] ?: listOf()).filterIsInstance<T>()
    }

    inline fun <reified T : InternalConfigurationOperation> getOperation(uniqueOperationIdentifier: UniqueOperationIdentifier): T {
        return checkNotNull(
            tryGetOperation(uniqueOperationIdentifier),
        ) {
            "No operation found for $uniqueOperationIdentifier"
        }
    }

    private fun defineAllNeededSubCalculatorEntities(): Map<SubCalculatorWithRole, KClass<out ManufacturingEntity>> {
        val subCalculatorWithRole = getOperations<IndirectOperationWithRateOrigin>().mapNotNull { it.subCalculatorWithRole }
        val duplicates = subCalculatorWithRole.findDuplicates()
        if (duplicates.isNotEmpty()) {
            throw CalculationConfigurationException(
                CalculationConfigurationException.ErrorCode.INVALID_SUB_CALCULATOR,
                "One sub calculator can not be defined in several operations in one configuration with the same role: " +
                    "${duplicates.distinct()}!",
            )
        }
        return subCalculatorWithRole.associateWith {
            it.subCalculator.getCalculator(valueType)
                ?: throw CalculationConfigurationException(
                    CalculationConfigurationException.ErrorCode.INVALID_SUB_CALCULATOR,
                    "$it is not available, but was configured for `$valueType`.",
                )
        }
    }

    // region standard values

    private val standardCalculationValuesToOps: Map<StandardCalculationValue, OperationWithStandardCalculationValue> =
        getOperations<OperationWithStandardCalculationValue>().associateBy {
            it.standardCalculationValue
        }.filterKeys { it != null }.mapKeys { it.key!! }

    fun getOperationOfStandardValue(standardCalculationValue: StandardCalculationValue): OperationWithStandardCalculationValue {
        return checkNotNull(
            standardCalculationValuesToOps[standardCalculationValue],
        ) {
            "Missing sum operation for `$standardCalculationValue`!"
        }
    }

    fun getAllStandardCalculationOperations(): List<OperationWithStandardCalculationValue> = standardCalculationValuesToOps.values.toList()

    // endregion

    fun getLeafOperationDestinationElements(op: InternalConfigurationOperation): Set<String> {
        val addendOps = getAddends(op).flatMap { getAllOperations<InternalConfigurationOperation>(it) }

        return if (addendOps.isEmpty()) {
            setOf(op.destinationElementKey)
        } else {
            addendOps.flatMap { getLeafOperationDestinationElements(it) }.toSet()
        }
    }

    private fun getAddends(op: InternalConfigurationOperation): List<String> =
        when (op) {
            is IndirectOperation -> op.baseAddends
            is SumProdOperation -> op.addends
            is InputOperation -> emptyList()
            is TransferOperation -> emptyList()
            is OperationWithStandardCalculationValue -> emptyList()
        }.map { it.elementKey }
}
