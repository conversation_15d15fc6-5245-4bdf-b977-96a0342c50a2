package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.entities.Machine
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.masterdata.MasterdataEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorAluminiumEmissionsEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorAluminiumShareEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorCastExcipientsEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorCountryInfoEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorElectricityEmissionsEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorElectricityPriceEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorFloorSpacePriceEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorInterestEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorLaborBurdenEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorNaturalGasEmissionsEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorNaturalGasPriceEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorOxygenPriceEntity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_ALUMINIUM_EMISSIONS_PRIMARY_ENTITY_NAME
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_ALUMINIUM_EMISSIONS_SECONDARY_ENTITY_NAME
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_ALUMINIUM_SHARE_PRIMARY_ENTITY_NAME
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_ALUMINIUM_SHARE_SECONDARY_ENTITY_NAME
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_CAST_EXCIPIENTS_ENTITY_NAME
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_COUNTRY_ID_ENTITY_NAME
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_ELECTRICITY_EMISSIONS_ENTITY_NAME
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_ELECTRICITY_PRICE_ENTITY_NAME
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_FACTOR_INTEREST_RATES_ENTITY_NAME
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_FLOOR_SPACE_ENTITY_NAME
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_LABOR_BURDEN_ENTITY_NAME
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_NATURAL_GAS_EMISSIONS_ACTIVE_ENTITY_NAME
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_NATURAL_GAS_EMISSIONS_PASSIVE_ENTITY_NAME
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_NATURAL_GAS_PRICE_ENTITY_NAME
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_OXYGEN_PRICE_ENTITY_NAME
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_WAGE_ENTITY_NAME
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorWageEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.extension.MachineCO2Extension
import com.nu.bom.core.manufacturing.extension.RawMaterialBarCO2Extension
import com.nu.bom.core.manufacturing.extension.RawMaterialCO2Extension
import com.nu.bom.core.manufacturing.fieldTypes.Date
import com.nu.bom.core.manufacturing.fieldTypes.EntityGeneration
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.MdLookupRequestField
import com.nu.bom.core.manufacturing.fieldTypes.MdLookupRequestFieldData
import com.nu.bom.core.manufacturing.fieldTypes.MdTableConfigColumnData
import com.nu.bom.core.manufacturing.fieldTypes.MdTableConfigFieldData
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.TsetDefaultSkillType
import com.nu.bom.core.manufacturing.masterdata.operationsAndExecutionContext.MasterdataContextOperationConfigBuilders
import com.nu.bom.core.manufacturing.service.FieldKey
import com.nu.bom.core.manufacturing.service.virtualfield.DataSourcerUpdaterProvider
import com.nu.bom.core.model.MasterDataSelector
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.InputDependencyModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.publicapi.dtos.configurations.masterdata.HeaderTypeConsumer
import com.nu.bom.core.service.configurations.MasterdataTsetConfigurationService
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MdLazyMigrationUtils
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import com.nu.bom.core.utils.mapOfNotNull
import com.nu.bom.core.utils.simpleName
import com.nu.bom.core.utils.visitTree
import org.bson.types.Decimal128
import org.bson.types.ObjectId
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDate
import kotlin.reflect.KClass

const val ACTUAL_EFFECTIVITY_TSET_REF_FIELD_SKILL_TYPE = "#actual_effectivity_tset-ref-field-skill-type"
const val ACTUAL_EFFECTIVITY_TSET_REF_FIELD_REGION = "#actual_effectivity_tset-ref-field-region"
const val REQUEST_EFFECTIVITY_TSET_REF_FIELD_REGION = "#request_effectivity_tset-ref-field-region"
private const val ACTUAL_EFFECTIVITY_VALID_FROM_DATE_FIELD = "#actual_effectivity_validFromDateField"
const val ACTUAL_EFFECTIVITY_TSET_REF_FIELD_REGION_DISPLAY_NAME =
    "#actual_effectivity_tset-ref-field-region_display_name"
const val REQUEST_EFFECTIVITY_TSET_REF_FIELD_REGION_DISPLAY_NAME =
    "#request_effectivity_tset-ref-field-region_display_name"
private const val REQUEST_EFFECTIVITY_VALID_FROM_DATE_FIELD = "#request_effectivity_validFromDateField"
const val ACTUAL_EFFECTIVITY_TSET_REF_FIELD_SHIFT_DISPLAY_NAME =
    "#actual_effectivity_tset-ref-field-shift_display_name"
const val ACTUAL_EFFECTIVITY_TSET_REF_FIELD_SHIFT = "#actual_effectivity_tset-ref-field-shift"
const val ACTUAL_EFFECTIVITY_TSET_REF_FIELD_SKILL_TYPE_DISPLAY_NAME =
    "#actual_effectivity_tset-ref-field-skill-type_display_name"
const val ACTUAL_EFFECTIVITY_TSET_DATA_SOURCE_FIELD_DISPLAY_NAME =
    "#actual_effectivity_tset-data-source-field_display_name"
const val ACTUAL_EFFECTIVITY_TSET_DATA_SOURCE_FIELD = "#actual_effectivity_tset-data-source-field"

private const val TSET_COST_FLOOR_SPACE_KEY = "tset.ref.header.floor-space-price"
private const val TSET_COST_SHIFT_SURCHARGE_KEY = "tset.ref.header.labor-burden"
private const val TSET_COST_ELECTRICITY_PRICE_KEY = "tset.ref.header.electricity-price"
private const val TSET_COST_NATURAL_GAS_PRICE_KEY = "tset.ref.header.natural-gas-price"
private const val TSET_COST_ELECTRICITY_EMISSIONS_KEY = "tset.ref.header.electricity-emission"
private const val TSET_COST_NATURAL_GAS_EMISSIONS_ACTIVE_KEY = "tset.ref.header.natural-gas-emission-active"
private const val TSET_COST_NATURAL_GAS_EMISSIONS_PASSIVE_KEY = "tset.ref.header.natural-gas-emission-passive"
private const val TSET_COST_FACTOR_INTEREST_RATES_KEY = "tset.ref.header.interest-rate"
private const val TSET_COST_ALUMINIUM_SHARE_PRIMARY_KEY = "tset.ref.header.aluminium-share-primary"
private const val TSET_COST_ALUMINIUM_SHARE_SECONDARY_KEY = "tset.ref.header.aluminium-share-secondary"
private const val TSET_COST_ALUMINIUM_EMISSIONS_PRIMARY_KEY = "tset.ref.header.aluminium-emission-primary"
private const val TSET_COST_ALUMINIUM_EMISSIONS_SECONDARY_KEY = "tset.ref.header.aluminium-emission-secondary"
private const val TSET_COST_CAST_EXCIPIENTS_KEY = "tset.ref.header.cast-excipients"
private const val TSET_COST_OXYGEN_PRICE_KEY = "tset.ref.header.oxygen-price"
private const val TSET_COST_WAGE_KEY = "tset.ref.header.wage"
private const val TSET_COST_COUNTRY_ID_KEY = "tset.ref.header.country-id"

@Service
class LocationFieldMigration : ManufacturingModelEntityMapper {
    override val changeSetId = MigrationChangeSetId("2024-08-28-location-cost-factors-migration")

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity = processEntityRecursively(entity)

    private fun processEntityRecursively(entity: ManufacturingModelEntity): ManufacturingModelEntity {
        val entityWithRegion = upsertRegionClassificationTypeKey(entity)
        val processedEntity =
            if (entity.type == Entities.MANUFACTURING.name) {
                addCostFactorsToManufacturing(entityWithRegion)
            } else {
                entityWithRegion.copyAll(
                    children = entity.children.map { processEntityRecursively(it) },
                )
            }
        val updatedEntity =
            if (processedEntity.type.simpleName() in listOf("MANUFACTURING_STEP", "MATERIAL", "MACHINE")) {
                addLocationNameField(processedEntity)
            } else {
                processedEntity
            }

        return updatedEntity.removeAllLocationAndWageEntities()
    }

    private fun addCostFactorsToManufacturing(entity: ManufacturingModelEntity): ManufacturingModelEntity {
        val costFactorParentCreationNew =
            "costFactorParentCreationNew" to
                FieldResultModel(
                    entity.version,
                    entity.version,
                    "EntityGeneration",
                    true,
                    FieldResult.SOURCE.C.name,
                )
        val hasCostFactorsParent =
            entity.children.any {
                it.clazz == MasterdataCostFactorParent::class.simpleName
            }
        return if (hasCostFactorsParent) {
            entity.copyAll(
                children =
                    entity.children.map {
                        processEntityRecursively(it)
                    },
                fieldWithResults = entity.fieldWithResults + costFactorParentCreationNew,
            )
        } else {
            entity.copyAll(
                children =
                    entity.children.map {
                        processEntityRecursively(it)
                    } + createCostFactorsParent(entity),
                fieldWithResults = entity.fieldWithResults + costFactorParentCreationNew,
            )
        }
    }

    private fun addLocationNameField(entity: ManufacturingModelEntity): ManufacturingModelEntity {
        val (laborEntities, nonLaborEntities) = entity.children.partition { it.type.simpleName() == "LABOR" }
        val locationEntity = nonLaborEntities.firstOrNull { it.type.simpleName() == "LOCATION" }
        val locationName = locationEntity?.fieldWithResults?.get("designation")
        val updatedLaborEntities =
            locationName?.let {
                laborEntities.map { laborEntity ->
                    laborEntity.copyAll(fieldWithResults = laborEntity.fieldWithResults + ("locationName" to it))
                }
            } ?: laborEntities
        val newLocationName =
            locationName?.let { "locationName" to it.copyAll(source = FieldResult.SOURCE.C.name) }
        val newFieldsWithResults =
            if (newLocationName != null) {
                entity.fieldWithResults + (newLocationName)
            } else {
                entity.fieldWithResults
            }
        return entity.copyAll(
            children = nonLaborEntities + updatedLaborEntities,
            fieldWithResults = newFieldsWithResults,
        )
    }

    private fun upsertRegionClassificationTypeKey(entity: ManufacturingModelEntity): ManufacturingModelEntity {
        val isEntityWithRegionClassification =
            entity.type in listOf(Entities.MANUFACTURING_STEP.name, Entities.MATERIAL.name, Entities.MANUFACTURING.name)
        return if (isEntityWithRegionClassification && fieldMissing(entity, "regionClassificationTypeKey")) {
            val fields = entity.fieldWithResults
            val regionClassificationTypeKey =
                "regionClassificationTypeKey" to
                    FieldResultModel(
                        entity.version,
                        entity.version,
                        "Text",
                        MasterdataTsetConfigurationService.regionConfiguration.regionClassificationTypeKey,
                        FieldResult.SOURCE.C.name,
                    )
            entity.copyAll(fieldWithResults = fields + regionClassificationTypeKey)
        } else {
            entity
        }
    }

    private fun fieldMissing(
        entity: ManufacturingModelEntity,
        fieldName: String,
    ) = entity.fieldWithResults[fieldName]?.value == null

    private fun createCostFactorsParent(entity: ManufacturingModelEntity): ManufacturingModelEntity {
        val cfParentId = ObjectId.get()
        val uniqueLocations = entity.collectUniqueLocations()
        val date =
            entity.fieldWithResults["calculationDate"]?.copyAll(source = FieldResult.SOURCE.C.name, systemValue = null)
                ?: FieldResultModel(
                    entity.version,
                    entity.version,
                    type = "Date",
                    value = Date.utcNow().dbValue(),
                    source = FieldResult.SOURCE.C.name,
                )
        val mdTimestampField = entity.fieldWithResults[Manufacturing::masterdataTimestamp.name]
        val mdTimestamp = (mdTimestampField?.value as? Decimal128)?.bigDecimalValue()

        // check which entities are present
        val isStepPresent = entity.collectEntitiesByType("MANUFACTURING_STEP").isNotEmpty()
        val isMachinePresent = entity.collectEntitiesByType("MACHINE").isNotEmpty()
        val isLaborPresent = entity.collectEntitiesByType("LABOR").isNotEmpty()
        val isSetupPresent = entity.collectEntitiesByType("SETUP").isNotEmpty()
        val isMaterialPresent =
            entity
                .collectEntitiesByType(
                    "MATERIAL",
                ).isNotEmpty() ||
                entity.collectEntitiesByType("PROCESSED_MATERIAL").isNotEmpty()

        val fields =
            listOf(
                CostFactorFieldInfo(
                    Machine::leasingFeeProduction.name,
                    Entities.MD_COSTFACTORS_FLOOR_SPACE_PRICE.name,
                    MasterdataCostFactorFloorSpacePriceEntity::class,
                    "floorSpacePrice",
                    "Floor space price",
                    TSET_COST_FLOOR_SPACE_KEY,
                    TSET_COST_FLOOR_SPACE_ENTITY_NAME,
                    HeaderTypeConsumer.FLOOR_SPACE_PRICE,
                    MasterdataCostFactorParent::createFloorSpace.name,
                ),
                CostFactorFieldInfo(
                    Machine::energyCost.name,
                    Entities.MD_COSTFACTORS_ELECTRICITY_PRICE.name,
                    MasterdataCostFactorElectricityPriceEntity::class,
                    Machine::energyCost.name,
                    "Electricity price",
                    TSET_COST_ELECTRICITY_PRICE_KEY,
                    TSET_COST_ELECTRICITY_PRICE_ENTITY_NAME,
                    HeaderTypeConsumer.ELECTRICITY_PRICE,
                    MasterdataCostFactorParent::createElectricityPrice.name,
                ),
                CostFactorFieldInfo(
                    Machine::gasPrice.name,
                    Entities.MD_COSTFACTORS_NATURAL_GAS_PRICE.name,
                    MasterdataCostFactorNaturalGasPriceEntity::class,
                    Machine::gasPrice.name,
                    "Natural gas price",
                    TSET_COST_NATURAL_GAS_PRICE_KEY,
                    TSET_COST_NATURAL_GAS_PRICE_ENTITY_NAME,
                    HeaderTypeConsumer.NATURAL_GAS_PRICE,
                    MasterdataCostFactorParent::createNaturalGasPrice.name,
                ),
                CostFactorFieldInfo(
                    MachineCO2Extension::electricityCarbon.name,
                    Entities.MD_COSTFACTORS_ELECTRICITY_EMISSIONS.name,
                    MasterdataCostFactorElectricityEmissionsEntity::class,
                    MachineCO2Extension::electricityCarbon.name,
                    "Electricity emissions",
                    TSET_COST_ELECTRICITY_EMISSIONS_KEY,
                    TSET_COST_ELECTRICITY_EMISSIONS_ENTITY_NAME,
                    HeaderTypeConsumer.ELECTRICITY_EMISSIONS,
                    MasterdataCostFactorParent::createElectricityEmissions.name,
                ),
                CostFactorFieldInfo(
                    RawMaterialCO2Extension::naturalGasEmissionDirect.name,
                    Entities.MD_COSTFACTORS_NATURAL_GAS_EMISSIONS.name,
                    MasterdataCostFactorNaturalGasEmissionsEntity::class,
                    RawMaterialCO2Extension::naturalGasEmissionDirect.name,
                    "Natural gas emissions active",
                    TSET_COST_NATURAL_GAS_EMISSIONS_ACTIVE_KEY,
                    TSET_COST_NATURAL_GAS_EMISSIONS_ACTIVE_ENTITY_NAME,
                    HeaderTypeConsumer.NATURAL_GAS_EMISSIONS,
                    MasterdataCostFactorParent::createNaturalGasEmissionsActive.name,
                ),
                CostFactorFieldInfo(
                    RawMaterialCO2Extension::naturalGasEmissionInDirect.name,
                    Entities.MD_COSTFACTORS_NATURAL_GAS_EMISSIONS.name,
                    MasterdataCostFactorNaturalGasEmissionsEntity::class,
                    RawMaterialCO2Extension::naturalGasEmissionInDirect.name,
                    "Natural gas emissions passive",
                    TSET_COST_NATURAL_GAS_EMISSIONS_PASSIVE_KEY,
                    TSET_COST_NATURAL_GAS_EMISSIONS_PASSIVE_ENTITY_NAME,
                    HeaderTypeConsumer.NATURAL_GAS_EMISSIONS,
                    MasterdataCostFactorParent::createNaturalGasEmissionsPassive.name,
                ),
                CostFactorFieldInfo(
                    Machine::interestRate.name,
                    Entities.MD_COSTFACTORS_INTEREST.name,
                    MasterdataCostFactorInterestEntity::class,
                    Machine::interestRate.name,
                    "Interest rate",
                    TSET_COST_FACTOR_INTEREST_RATES_KEY,
                    TSET_COST_FACTOR_INTEREST_RATES_ENTITY_NAME,
                    HeaderTypeConsumer.COSTFACTOR_INTEREST,
                    MasterdataCostFactorParent::createCostFactorInterestRates.name,
                ),
                CostFactorFieldInfo(
                    RawMaterialBarCO2Extension::primaryShare.name,
                    Entities.MD_COSTFACTORS_ALUMINIUM_SHARE.name,
                    MasterdataCostFactorAluminiumShareEntity::class,
                    RawMaterialBarCO2Extension::primaryShare.name,
                    "Primary Aluminium Share",
                    TSET_COST_ALUMINIUM_SHARE_PRIMARY_KEY,
                    TSET_COST_ALUMINIUM_SHARE_PRIMARY_ENTITY_NAME,
                    HeaderTypeConsumer.ALUMINIUM_SHARE,
                    MasterdataCostFactorParent::createCostAluminiumSharePrimary.name,
                ),
                CostFactorFieldInfo(
                    RawMaterialBarCO2Extension::secondaryShare.name,
                    Entities.MD_COSTFACTORS_ALUMINIUM_SHARE.name,
                    MasterdataCostFactorAluminiumShareEntity::class,
                    RawMaterialBarCO2Extension::secondaryShare.name,
                    "Secondary Aluminium Share",
                    TSET_COST_ALUMINIUM_SHARE_SECONDARY_KEY,
                    TSET_COST_ALUMINIUM_SHARE_SECONDARY_ENTITY_NAME,
                    HeaderTypeConsumer.ALUMINIUM_SHARE,
                    MasterdataCostFactorParent::createCostAluminiumShareSecondary.name,
                ),
                CostFactorFieldInfo(
                    RawMaterialBarCO2Extension::primaryAluEmissionPerKg.name,
                    Entities.MD_COSTFACTORS_ALUMINIUM_EMISSIONS.name,
                    MasterdataCostFactorAluminiumEmissionsEntity::class,
                    RawMaterialBarCO2Extension::primaryAluEmissionPerKg.name,
                    "Primary Aluminium Emissions",
                    TSET_COST_ALUMINIUM_EMISSIONS_PRIMARY_KEY,
                    TSET_COST_ALUMINIUM_EMISSIONS_PRIMARY_ENTITY_NAME,
                    HeaderTypeConsumer.ALUMINIUM_EMISSIONS,
                    MasterdataCostFactorParent::createCostAluminiumEmissionsPrimary.name,
                ),
                CostFactorFieldInfo(
                    RawMaterialBarCO2Extension::secondaryAluEmissionPerKg.name,
                    Entities.MD_COSTFACTORS_ALUMINIUM_EMISSIONS.name,
                    MasterdataCostFactorAluminiumEmissionsEntity::class,
                    RawMaterialBarCO2Extension::secondaryAluEmissionPerKg.name,
                    "Secondary Aluminium Emissions",
                    TSET_COST_ALUMINIUM_EMISSIONS_SECONDARY_KEY,
                    TSET_COST_ALUMINIUM_EMISSIONS_SECONDARY_ENTITY_NAME,
                    HeaderTypeConsumer.ALUMINIUM_EMISSIONS,
                    MasterdataCostFactorParent::createCostAluminiumEmissionsSecondary.name,
                ),
                CostFactorFieldInfo(
                    Machine::castExcipientsPrice.name,
                    Entities.MD_COSTFACTORS_CAST_EXCIPIENTS_PRICE.name,
                    MasterdataCostFactorCastExcipientsEntity::class,
                    Machine::castExcipientsPrice.name,
                    "Cast excipients price",
                    TSET_COST_CAST_EXCIPIENTS_KEY,
                    TSET_COST_CAST_EXCIPIENTS_ENTITY_NAME,
                    HeaderTypeConsumer.CAST_EXCIPIENTS,
                    MasterdataCostFactorParent::createCastExcipients.name,
                ),
                CostFactorFieldInfo(
                    Machine::oxygenPrice.name,
                    Entities.MD_COSTFACTORS_OXYGEN_PRICE.name,
                    MasterdataCostFactorOxygenPriceEntity::class,
                    Machine::oxygenPrice.name,
                    "Oxygen price",
                    TSET_COST_OXYGEN_PRICE_KEY,
                    TSET_COST_OXYGEN_PRICE_ENTITY_NAME,
                    HeaderTypeConsumer.OXYGEN_PRICE,
                    MasterdataCostFactorParent::createOxygenPrice.name,
                ),
                CostFactorFieldInfo(
                    "countryId",
                    Entities.MD_COSTFACTORS_COUNTRY_INFO.name,
                    MasterdataCostFactorCountryInfoEntity::class,
                    "countryId",
                    "countryId",
                    TSET_COST_COUNTRY_ID_KEY,
                    TSET_COST_COUNTRY_ID_ENTITY_NAME,
                    HeaderTypeConsumer.COUNTRY_INFO,
                    MasterdataCostFactorParent::createCountryIdInfo.name,
                ),
            )

        val neededEntities =
            fields.filter { fieldInfo ->
                when (fieldInfo.fieldName) {
                    "floorSpacePrice", "castExcipientsPrice", "oxygenPrice" -> isMachinePresent
                    "shiftSurcharge", "wage" -> isLaborPresent || isSetupPresent
                    "energyCost", "gasPrice" -> isStepPresent || isMachinePresent
                    "electricityCarbon", "naturalGasEmissionDirect", "naturalGasEmissionInDirect" -> isMaterialPresent || isMachinePresent
                    "interestRate" -> true
                    "primaryShare", "secondaryShare", "primaryAluEmissionPerKg", "secondaryAluEmissionPerKg" -> isMaterialPresent
                    "countryId" -> isStepPresent || isMaterialPresent
                    else -> false
                }
            }

        val childEntities =
            uniqueLocations.flatMap { location ->
                neededEntities.mapNotNull { fieldInfo ->
                    location.fieldWithResults[fieldInfo.nbkField]?.let {
                        createChildEntity(
                            fieldInfo,
                            location,
                            mapOf(fieldInfo.fieldName to it),
                            date,
                            cfParentId,
                            entity,
                            mdTimestamp,
                        )
                    }
                }
            }

        val laborBurdenEntities =
            if (isLaborPresent || isSetupPresent) {
                uniqueLocations
                    .flatMap { location ->
                        entity
                            .collectManufacturingStepEntities()
                            .mapNotNull { it.fieldWithResults["shiftsPerDay"] }
                            .distinctBy { it.value }
                            .map {
                                createLaborBurdenEntities(
                                    entity,
                                    location,
                                    it.value.toString(),
                                    date,
                                    cfParentId,
                                    entity.version,
                                    mdTimestamp,
                                )
                            }
                    }.filterNotNull()
                    .distinctBy {
                        Pair(
                            it.fieldWithResults["location"]?.value,
                            it.fieldWithResults["skillType"]?.value,
                        )
                    }
            } else {
                emptyList()
            }

        val wageEntities =
            if (isLaborPresent || isSetupPresent) {
                uniqueLocations.flatMap { location ->
                    entity.collectLaborAndSetupEntities().distinctBy { it.fieldWithResults["skillType"]?.value }.map {
                        createWageEntities(
                            entity,
                            location,
                            it.fieldWithResults,
                            date,
                            cfParentId,
                            entity.version,
                            mdTimestamp,
                        )
                    }.filterNotNull()
                }
            } else {
                emptyList()
            }

        val fieldWithResults =
            fields.associateBy(
                { fieldInfo -> fieldInfo.createdBy },
                { createMdCostFactorParentCreateCostFactorField(entity) },
            )

        return ManufacturingModelEntity(
            id = cfParentId,
            name = "CostFactorsParentNew",
            type = Entities.MD_COSTFACTORS_PARENT.name,
            clazz = MasterdataCostFactorParent::class.simpleName!!,
            args = emptyMap(),
            fieldWithResults =
                fieldWithResults +
                    mapOf(
                        MasterdataCostFactorParent::createLaborBurden.name to
                            createMdCostFactorParentCreateCostFactorField(
                                entity,
                            ),
                        MasterdataCostFactorParent::createWage.name to
                            createMdCostFactorParentCreateCostFactorField(
                                entity,
                            ),
                        MasterdataCostFactorParent::masterDataTypeInternal.name to
                            FieldResultModel(
                                version = entity.version,
                                newVersion = entity.version,
                                type = Text::class.java.simpleName,
                                source = FieldResult.SOURCE.C.name,
                                value = MasterDataType.NONE.name,
                            ),
                        MasterdataCostFactorParent::entityDesignation.name to
                            FieldResultModel(
                                version = entity.version,
                                newVersion = entity.version,
                                type = Text::class.java.simpleName,
                                source = FieldResult.SOURCE.C.name,
                                value = "CostFactorsParentNew",
                            ),
                        MasterdataCostFactorParent::displayDesignation.name to
                            FieldResultModel(
                                version = entity.version,
                                newVersion = entity.version,
                                type = Text::class.java.simpleName,
                                source = FieldResult.SOURCE.C.name,
                                value = "Location Factors",
                            ),
                        MasterdataCostFactorParent::mdTableConfig.name to
                            FieldResultModel(
                                version = 0,
                                newVersion = 0,
                                type = "MdTableConfigField",
                                value =
                                    MdTableConfigFieldData(
                                        columns = mdTableConfigColumnData(),
                                    ),
                                source = FieldResult.SOURCE.C.name,
                                systemValue = null,
                            ),
                    ),
            initialFieldWithResults = emptyMap(),
            children = childEntities + wageEntities + laborBurdenEntities,
        ).also {
            it.version = entity.version
            it.createdBy =
                FieldKey(
                    "costFactorParentCreationNew",
                    entity.id.toHexString(),
                    "MANUFACTURING",
                    "MD_COSTFACTORS_PARENT",
                    entity.version,
                    entity.version,
                    entity.name,
                )
        }
    }

    private fun mdTableConfigColumnData() =
        listOf(
            MdTableConfigColumnData(
                fieldName = "displayDesignation",
                displayDesignationFieldName = null,
                alias = "Location Factor",
                editable = false,
                hideIfContainsSourceI = false,
            ),
            MdTableConfigColumnData(
                alias = "Value",
                displayDesignationFieldName = null,
                editable = true,
                fieldName = "value",
                hideIfContainsSourceI = false,
            ),
            MdTableConfigColumnData(
                alias = "Actual - Region",
                displayDesignationFieldName = ACTUAL_EFFECTIVITY_TSET_REF_FIELD_REGION_DISPLAY_NAME,
                editable = false,
                fieldName = ACTUAL_EFFECTIVITY_TSET_REF_FIELD_REGION,
                hideIfContainsSourceI = true,
            ),
            MdTableConfigColumnData(
                alias = "Requested - Region",
                displayDesignationFieldName = REQUEST_EFFECTIVITY_TSET_REF_FIELD_REGION_DISPLAY_NAME,
                editable = false,
                fieldName = REQUEST_EFFECTIVITY_TSET_REF_FIELD_REGION,
                hideIfContainsSourceI = true,
            ),
            MdTableConfigColumnData(
                alias = "Actual - Valid from date",
                displayDesignationFieldName = ACTUAL_EFFECTIVITY_VALID_FROM_DATE_FIELD,
                editable = false,
                fieldName = ACTUAL_EFFECTIVITY_VALID_FROM_DATE_FIELD,
                hideIfContainsSourceI = true,
            ),
            MdTableConfigColumnData(
                alias = "Requested - Valid from date",
                displayDesignationFieldName = REQUEST_EFFECTIVITY_VALID_FROM_DATE_FIELD,
                editable = false,
                fieldName = REQUEST_EFFECTIVITY_VALID_FROM_DATE_FIELD,
                hideIfContainsSourceI = true,
            ),
            MdTableConfigColumnData(
                alias = "Shift",
                displayDesignationFieldName = ACTUAL_EFFECTIVITY_TSET_REF_FIELD_SHIFT_DISPLAY_NAME,
                editable = false,
                fieldName = ACTUAL_EFFECTIVITY_TSET_REF_FIELD_SHIFT,
                hideIfContainsSourceI = true,
            ),
            MdTableConfigColumnData(
                alias = "Skill type",
                displayDesignationFieldName = ACTUAL_EFFECTIVITY_TSET_REF_FIELD_SKILL_TYPE_DISPLAY_NAME,
                editable = false,
                fieldName = ACTUAL_EFFECTIVITY_TSET_REF_FIELD_SKILL_TYPE,
                hideIfContainsSourceI = true,
            ),
            MdTableConfigColumnData(
                alias = "Data source",
                displayDesignationFieldName = ACTUAL_EFFECTIVITY_TSET_DATA_SOURCE_FIELD_DISPLAY_NAME,
                editable = false,
                fieldName = ACTUAL_EFFECTIVITY_TSET_DATA_SOURCE_FIELD,
                hideIfContainsSourceI = true,
            ),
            MdTableConfigColumnData(
                alias = "Modifier",
                displayDesignationFieldName = null,
                editable = false,
                fieldName = "mdDetailModifier",
                hideIfContainsSourceI = true,
            ),
            MdTableConfigColumnData(
                alias = "Modification Date",
                displayDesignationFieldName = null,
                editable = false,
                fieldName = "mdDetailModificationDate",
                hideIfContainsSourceI = true,
            ),
        )

    private fun createChildEntity(
        fieldInfo: CostFactorFieldInfo,
        location: ManufacturingModelEntity,
        fields: Map<String, FieldResultModel>,
        date: FieldResultModel,
        cfParentId: ObjectId,
        parentEntity: ManufacturingModelEntity,
        mdTimestamp: BigDecimal?,
    ): ManufacturingModelEntity? {
        val value = location.masterDataSelector?.key ?: location.initialFieldWithResults["entityDesignation"]?.value
        val mdShiftFields = emptyMdShiftFields()
        val mdTableSkillTypes = emptyMdSkillFields()

        val locationDisplayDesignation = location.fieldWithResults["displayDesignation"]
        return if (locationDisplayDesignation == null) {
            null
        } else {
            val valueResultModel = fields[fieldInfo.fieldName]
            val mdTableFields =
                mapOfNotNull(
                    "headerDisplayName" to
                        createFieldResultModel(
                            parentEntity.version,
                            "Text",
                            fieldInfo.mdTableDisplayDesignation,
                        ),
                    "displayDesignation" to
                        createFieldResultModel(
                            parentEntity.version,
                            "Text",
                            fieldInfo.mdTableDisplayDesignation,
                        ),
                    "location" to createFieldResultModel(parentEntity.version, "Text", value),
                    "locationName" to
                        createFieldResultModel(
                            parentEntity.version,
                            "Text",
                            locationDisplayDesignation.value,
                        ),
                    "value" to valueResultModel,
                    ACTUAL_EFFECTIVITY_VALID_FROM_DATE_FIELD to date,
                    REQUEST_EFFECTIVITY_VALID_FROM_DATE_FIELD to date,
                )

            ManufacturingModelEntity(
                id = ObjectId.get(),
                name = fieldInfo.entityName,
                type = fieldInfo.type,
                clazz = fieldInfo.clazz.simpleName!!,
                args = emptyMap(),
                fieldWithResults =
                    createFieldWithResults(
                        parentEntity,
                        location,
                        mdTableFields,
                        mdTableSkillTypes,
                        mdShiftFields,
                        fieldInfo.headerConsumer,
                        fieldInfo.key,
                        mdTimestamp,
                        fieldInfo.clazz.simpleName!!,
                    ),
                initialFieldWithResults =
                    createFieldWithResults(
                        parentEntity,
                        location,
                        mdTableFields,
                        mdTableSkillTypes,
                        mdShiftFields,
                        fieldInfo.headerConsumer,
                        fieldInfo.key,
                        mdTimestamp,
                        fieldInfo.clazz.simpleName!!,
                        true,
                    ),
            ).apply {
                version = parentEntity.version
                entityRef = "${fieldInfo.entityName}_${value}_${fieldInfo.key}"
                createdBy =
                    FieldKey(
                        name = fieldInfo.createdBy,
                        entityId = cfParentId.toHexString(),
                        entityType = Entities.MD_COSTFACTORS_PARENT.name,
                        type = fieldInfo.type,
                        version = parentEntity.version,
                        newVersion = parentEntity.version,
                        entityRef = "CostFactorsParentNew",
                    )
            }
        }
    }

    private fun createLaborBurdenEntities(
        parentEntity: ManufacturingModelEntity,
        location: ManufacturingModelEntity,
        shiftPerDay: String,
        date: FieldResultModel,
        cfParentId: ObjectId,
        parentVersion: Int,
        mdTimestamp: BigDecimal?,
    ): ManufacturingModelEntity? {
        val value = classificationLocationKey(location.masterDataSelector)
        val mdTableSkillTypes = emptyMdSkillFields()

        val mdShiftFields =
            mapOf(
                ACTUAL_EFFECTIVITY_TSET_REF_FIELD_SHIFT to
                    createFieldResultModel(
                        parentEntity.version,
                        "Num",
                        shiftPerDay.toBigDecimal(),
                    ),
                ACTUAL_EFFECTIVITY_TSET_REF_FIELD_SHIFT_DISPLAY_NAME to
                    createFieldResultModel(
                        parentEntity.version,
                        "Text",
                        shiftPerDay.toBigDecimal().toString(),
                    ),
                "#request_effectivity_tset-ref-field-shift" to
                    createFieldResultModel(
                        parentEntity.version,
                        "Num",
                        shiftPerDay.toBigDecimal(),
                    ),
            )

        val locationDisplayDesignation = location.fieldWithResults["displayDesignation"]
        return if (locationDisplayDesignation == null) {
            null
        } else {
            val laborBurdenValue = location.fieldWithResults["laborBurden"]

            val mdTableFields =
                mapOfNotNull(
                    "headerDisplayName" to createFieldResultModel(parentEntity.version, "Text", "Shift Surcharge"),
                    "displayDesignation" to createFieldResultModel(parentEntity.version, "Text", "Shift Surcharge"),
                    "location" to createFieldResultModel(parentEntity.version, "Text", value),
                    "locationName" to
                        createFieldResultModel(
                            parentEntity.version,
                            "Text",
                            locationDisplayDesignation.value,
                        ),
                    "value" to laborBurdenValue,
                    ACTUAL_EFFECTIVITY_VALID_FROM_DATE_FIELD to date,
                    REQUEST_EFFECTIVITY_VALID_FROM_DATE_FIELD to date,
                )

            return ManufacturingModelEntity(
                id = ObjectId.get(),
                name = TSET_COST_LABOR_BURDEN_ENTITY_NAME,
                type = Entities.MD_COSTFACTORS_LABOR_BURDEN.name,
                clazz = MasterdataCostFactorLaborBurdenEntity::class.simpleName!!,
                args = emptyMap(),
                fieldWithResults =
                    createFieldWithResults(
                        parentEntity,
                        location,
                        mdTableFields,
                        mdTableSkillTypes,
                        mdShiftFields,
                        HeaderTypeConsumer.LABOR_BURDEN,
                        TSET_COST_SHIFT_SURCHARGE_KEY,
                        mdTimestamp,
                        MasterdataCostFactorLaborBurdenEntity::class.simpleName!!,
                    ),
                initialFieldWithResults =
                    createFieldWithResults(
                        parentEntity,
                        location,
                        mdTableFields,
                        mdTableSkillTypes,
                        mdShiftFields,
                        HeaderTypeConsumer.LABOR_BURDEN,
                        TSET_COST_SHIFT_SURCHARGE_KEY,
                        mdTimestamp,
                        MasterdataCostFactorLaborBurdenEntity::class.simpleName!!,
                        true,
                    ),
            ).apply {
                version = parentVersion
                entityRef = "${TSET_COST_LABOR_BURDEN_ENTITY_NAME}_${value}_${TSET_COST_SHIFT_SURCHARGE_KEY}"
                createdBy =
                    FieldKey(
                        name = MasterdataCostFactorParent::createLaborBurden.name,
                        entityId = cfParentId.toHexString(),
                        entityType = Entities.MD_COSTFACTORS_PARENT.name,
                        type = Entities.MD_COSTFACTORS_LABOR_BURDEN.name,
                        version = parentVersion,
                        newVersion = parentVersion,
                        entityRef = "CostFactorsParentNew",
                    )
            }
        }
    }

    private fun createWageEntities(
        parentEntity: ManufacturingModelEntity,
        location: ManufacturingModelEntity,
        fieldWithResults: Map<String, FieldResultModel>,
        date: FieldResultModel,
        cfParentId: ObjectId,
        parentVersion: Int,
        mdTimestamp: BigDecimal?,
    ): ManufacturingModelEntity? {
        val locationKey =
            classificationLocationKey(location.masterDataSelector)
        val mdShiftFields = emptyMdShiftFields()
        val skillTypeFieldResult = fieldWithResults["skillType"]
        val skillType =
            if (skillTypeFieldResult?.value != null) {
                TsetDefaultSkillType.valueOf(skillTypeFieldResult.value.toString())
            } else {
                TsetDefaultSkillType.SETUP_TECHNICIAN
            }
        val skillTypeValue = skillType.translation
        val mdTableSkillTypes =
            mapOf(
                ACTUAL_EFFECTIVITY_TSET_REF_FIELD_SKILL_TYPE to (
                    skillTypeFieldResult
                        ?: FieldResultModel(
                            version = parentEntity.version,
                            newVersion = parentEntity.version,
                            type = "SkillType",
                            value = "SETUP_TECHNICIAN",
                            source = FieldResult.SOURCE.C.name,
                            systemValue = null,
                        )
                ),
                "#request_effectivity_tset-ref-field-skill-type" to (
                    skillTypeFieldResult
                        ?: FieldResultModel(
                            version = parentEntity.version,
                            newVersion = parentEntity.version,
                            type = "SkillType",
                            value = "SETUP_TECHNICIAN",
                            source = FieldResult.SOURCE.C.name,
                            systemValue = null,
                        )
                ),
                ACTUAL_EFFECTIVITY_TSET_REF_FIELD_SKILL_TYPE_DISPLAY_NAME to
                    FieldResultModel(
                        version = parentEntity.version,
                        newVersion = parentEntity.version,
                        type = "Text",
                        value = skillTypeValue,
                        source = FieldResult.SOURCE.C.name,
                        systemValue = null,
                    ),
            )
        val locationDisplayDesignation = location.fieldWithResults["displayDesignation"]
        return if (locationDisplayDesignation == null) {
            null
        } else {
            val wagePerHourValue = fieldWithResults["wagePerHour"]
            val mdTableFields =
                fieldWithResults.filter { it.key in listOf("skillType") } +
                    mapOfNotNull(
                        "headerDisplayName" to createFieldResultModel(parentEntity.version, "Text", "Wage"),
                        "displayDesignation" to
                            FieldResultModel(
                                version = parentEntity.version,
                                newVersion = parentEntity.version,
                                type = "Text",
                                value = "Wage",
                                source = FieldResult.SOURCE.C.name,
                                systemValue = null,
                            ),
                        "location" to
                            FieldResultModel(
                                version = parentEntity.version,
                                newVersion = parentEntity.version,
                                type = "Text",
                                value = locationKey,
                                source = FieldResult.SOURCE.C.name,
                                systemValue = null,
                            ),
                        "locationName" to
                            FieldResultModel(
                                version = parentEntity.version,
                                newVersion = parentEntity.version,
                                type = "Text",
                                value = locationDisplayDesignation.value,
                                source = FieldResult.SOURCE.C.name,
                                systemValue = null,
                            ),
                        "value" to wagePerHourValue,
                        ACTUAL_EFFECTIVITY_VALID_FROM_DATE_FIELD to date,
                        REQUEST_EFFECTIVITY_VALID_FROM_DATE_FIELD to date,
                    )
            return ManufacturingModelEntity(
                id = ObjectId.get(),
                name = TSET_COST_WAGE_ENTITY_NAME,
                type = Entities.MD_COSTFACTORS_WAGE.name,
                clazz = MasterdataCostFactorWageEntity::class.simpleName!!,
                args = emptyMap(),
                fieldWithResults =
                    createFieldWithResults(
                        parentEntity,
                        location,
                        mdTableFields,
                        mdTableSkillTypes,
                        mdShiftFields,
                        HeaderTypeConsumer.WAGE,
                        TSET_COST_WAGE_KEY,
                        mdTimestamp,
                        MasterdataCostFactorWageEntity::class.simpleName!!,
                    ),
                initialFieldWithResults =
                    createFieldWithResults(
                        parentEntity,
                        location,
                        mdTableFields,
                        mdTableSkillTypes,
                        mdShiftFields,
                        HeaderTypeConsumer.WAGE,
                        TSET_COST_WAGE_KEY,
                        mdTimestamp,
                        MasterdataCostFactorWageEntity::class.simpleName!!,
                        true,
                    ),
            ).apply {
                version = parentVersion
                entityRef = "${TSET_COST_WAGE_ENTITY_NAME}_${locationKey}_${skillType}_$TSET_COST_WAGE_KEY\""
                createdBy =
                    FieldKey(
                        name = MasterdataCostFactorParent::createWage.name,
                        entityId = cfParentId.toHexString(),
                        entityType = Entities.MD_COSTFACTORS_PARENT.name,
                        type = Entities.MD_COSTFACTORS_WAGE.name,
                        version = parentVersion,
                        newVersion = parentVersion,
                        entityRef = "CostFactorsParentNew",
                    )
            }
        }
    }

    private fun createFieldWithResults(
        parentEntity: ManufacturingModelEntity,
        location: ManufacturingModelEntity,
        fields: Map<String, FieldResultModel>,
        skillFields: Map<String, FieldResultModel>,
        mdShiftFields: Map<String, FieldResultModel>,
        headerConsumer: HeaderTypeConsumer,
        headerKey: String,
        mdTimestamp: BigDecimal?,
        simpleClassName: String,
        initial: Boolean = false,
    ): Map<String, FieldResultModel> {
        val value = location.masterDataSelector?.key ?: location.initialFieldWithResults["entityDesignation"]?.value
        val systemValue = location.fieldWithResults["designation"]?.systemValue
        val displayName = location.masterDataSelector?.key ?: location.fieldWithResults["displayDesignation"]?.value
        val behaviourField =
            MdLazyMigrationUtils.createMdBehaviourField(
                parentManufacturing = parentEntity,
                location,
                simpleClassName,
            )

        val lookupField = createMdCostFactorLookupField(location, mdTimestamp, headerConsumer, headerKey)
        val additionalFields =
            mapOf(
                "headerKey" to createFieldResultModel(parentEntity.version, "Text", headerKey),
                MasterdataContextOperationConfigBuilders.LOOKUP_FIELD_NAME to lookupField,
                MasterdataEntity::masterDataTypeInternal.name to
                    FieldResultModel(
                        version = location.version,
                        newVersion = location.version,
                        type = Text::class.java.simpleName,
                        source = FieldResult.SOURCE.C.name,
                        value = MasterDataType.NONE.name,
                    ),
                DataSourcerUpdaterProvider.VIRTUAL_FIELD_NAME to lookupField.copyAll(),
                MasterdataEntity::createBehaviour.name to behaviourField,
                REQUEST_EFFECTIVITY_TSET_REF_FIELD_REGION to
                    createFieldResultModel(
                        parentEntity.version,
                        "Text",
                        value,
                    ),
                REQUEST_EFFECTIVITY_TSET_REF_FIELD_REGION_DISPLAY_NAME to
                    createFieldResultModel(
                        parentEntity.version,
                        "Text",
                        displayName,
                        systemValue,
                    ),
                "lookupImplementationVersion" to createFieldResultModel(parentEntity.version, "Num", 1),
                "executeLookup" to
                    createFieldResultModel(
                        parentEntity.version,
                        "Bool",
                        false,
                        null,
                        FieldResult.SOURCE.C.name,
                    ),
                ACTUAL_EFFECTIVITY_TSET_DATA_SOURCE_FIELD to
                    createFieldResultModel(
                        parentEntity.version,
                        "Text",
                        "tset.tset",
                    ),
                ACTUAL_EFFECTIVITY_TSET_DATA_SOURCE_FIELD_DISPLAY_NAME to
                    createFieldResultModel(
                        parentEntity.version,
                        "Text",
                        "Tset",
                    ),
                ACTUAL_EFFECTIVITY_TSET_REF_FIELD_REGION to
                    createFieldResultModel(
                        parentEntity.version,
                        "Text",
                        value,
                    ),
                ACTUAL_EFFECTIVITY_TSET_REF_FIELD_REGION_DISPLAY_NAME to
                    createFieldResultModel(
                        parentEntity.version,
                        "Text",
                        displayName,
                    ),
                "mdDetailModifier" to createFieldResultModel(parentEntity.version, "Text", "TSET reference data"),
                "mdDetailModificationDate" to createFieldResultModel(parentEntity.version, "Date", LocalDate.now()),
            )
        return if (initial) {
            (fields + skillFields + mdShiftFields + additionalFields).filterKeys { key ->
                key in
                    setOf(
                        "headerKey",
                        "location",
                        "headerDisplayName",
                        "lookupImplementationVersion",
                        "mdDetailModificationDate",
                        "mdDetailModifier",
                        "value",
                        ACTUAL_EFFECTIVITY_TSET_REF_FIELD_REGION,
                        ACTUAL_EFFECTIVITY_TSET_REF_FIELD_REGION_DISPLAY_NAME,
                        ACTUAL_EFFECTIVITY_TSET_DATA_SOURCE_FIELD,
                        ACTUAL_EFFECTIVITY_TSET_DATA_SOURCE_FIELD_DISPLAY_NAME,
                        ACTUAL_EFFECTIVITY_TSET_REF_FIELD_SHIFT,
                        ACTUAL_EFFECTIVITY_TSET_REF_FIELD_SHIFT_DISPLAY_NAME,
                        ACTUAL_EFFECTIVITY_TSET_REF_FIELD_SKILL_TYPE,
                        ACTUAL_EFFECTIVITY_TSET_REF_FIELD_SKILL_TYPE_DISPLAY_NAME,
                        "executeLookup",
                        "skillType",
                    )
            }
        } else {
            fields + skillFields + mdShiftFields + additionalFields
        }
    }

    fun classificationLocationKey(masterDataSelector: MasterDataSelector?) = "${masterDataSelector?.key}"

    private fun ManufacturingModelEntity.collectUniqueLocations(): List<ManufacturingModelEntity> {
        val uniqueLocations = mutableMapOf<String, ManufacturingModelEntity>()

        this.visitTree(
            func = { node, _ ->
                if (node.type == "LOCATION") {
                    val designation = node.fieldWithResults["designation"]?.value?.toString()
                    if (designation != null && !uniqueLocations.containsKey(designation)) {
                        uniqueLocations[designation] = node
                    }
                }
                null
            },
            children = { node -> node.children.filterNot { it.type == Entities.MANUFACTURING.name } },
        )

        return uniqueLocations.values.toList()
    }

    private fun ManufacturingModelEntity.collectLaborAndSetupEntities(): List<ManufacturingModelEntity> =
        this.collectEntitiesByType("LABOR", "SETUP")

    private fun ManufacturingModelEntity.collectManufacturingStepEntities(): List<ManufacturingModelEntity> =
        this.collectEntitiesByType("MANUFACTURING_STEP")

    private fun ManufacturingModelEntity.removeAllLocationAndWageEntities(): ManufacturingModelEntity {
        fun removeLocations(node: ManufacturingModelEntity): ManufacturingModelEntity {
            val filteredChildren =
                node.children
                    .filter { it.type != "LOCATION" && it.type != "WAGE" }
            return node.copyAll(children = filteredChildren)
        }
        return removeLocations(this)
    }

    private fun ManufacturingModelEntity.collectEntitiesByType(vararg types: String): List<ManufacturingModelEntity> =
        this.visitTree(
            func = { node, _ -> if (node.type in types) node else null },
            children = { node -> node.children },
        )

    private fun emptyMdShiftFields() =
        mapOf(
            ACTUAL_EFFECTIVITY_TSET_REF_FIELD_SHIFT to createFieldResultModel("Null"),
            ACTUAL_EFFECTIVITY_TSET_REF_FIELD_SHIFT_DISPLAY_NAME to createFieldResultModel("Null"),
        )

    private fun emptyMdSkillFields() =
        mapOf(
            ACTUAL_EFFECTIVITY_TSET_REF_FIELD_SKILL_TYPE to createFieldResultModel("Null"),
            ACTUAL_EFFECTIVITY_TSET_REF_FIELD_SKILL_TYPE_DISPLAY_NAME to createFieldResultModel("Null"),
        )

    private fun createFieldResultModel(
        version: Int,
        type: String,
        value: Any?,
        systemValue: Any? = null,
        source: String = FieldResult.SOURCE.C.name,
    ): FieldResultModel =
        FieldResultModel(
            version = version,
            newVersion = version,
            type = type,
            value = value,
            source = source,
            systemValue = systemValue,
        )

    private fun createFieldResultModel(type: String): FieldResultModel =
        FieldResultModel(
            version = 0,
            newVersion = 0,
            type = type,
            value = null,
            source = FieldResult.SOURCE.C.name,
            systemValue = null,
        )

    private fun createMdCostFactorLookupField(
        locationEntity: ManufacturingModelEntity,
        mdTimestamp: BigDecimal?,
        headerTypeConsumer: HeaderTypeConsumer,
        headerKey: String,
    ) = FieldResultModel(
        version = locationEntity.version,
        newVersion = locationEntity.version,
        type = MdLookupRequestField::class.java.simpleName,
        source = FieldResult.SOURCE.C.name,
        value =
            MdLookupRequestFieldData(
                strategyKey = "tset.ref.strategy.location-factor",
                headerTypeKey = "tset.ref.header-type.location-factor",
                headerKey = headerKey,
                headerTypeConsumer = headerTypeConsumer,
                effectivities = emptyList(),
                executeLookup = false,
                timestampEpochMillis = mdTimestamp,
            ),
    )

    private fun createMdCostFactorParentCreateCostFactorField(entity: ManufacturingModelEntity): FieldResultModel =
        FieldResultModel(
            version = entity.version,
            newVersion = entity.version,
            type = EntityGeneration::class.java.simpleName,
            source = FieldResult.SOURCE.C.name,
            value = true,
            inputs =
                setOf(
                    InputDependencyModel(
                        Manufacturing::masterdataConfigurationKey.name,
                        entity.id.toHexString(),
                        entity.fieldWithResults[Manufacturing::masterdataConfigurationKey.name]?.version
                            ?: entity.version,
                    ),
                    InputDependencyModel(
                        Manufacturing::masterdataTimestamp.name,
                        entity.id.toHexString(),
                        entity.fieldWithResults[Manufacturing::masterdataTimestamp.name]?.version
                            ?: entity.version,
                    ),
                ),
        )

    data class CostFactorFieldInfo(
        val nbkField: String,
        val type: String,
        val clazz: KClass<*>,
        val fieldName: String,
        val mdTableDisplayDesignation: String,
        val key: String,
        val entityName: String,
        val headerConsumer: HeaderTypeConsumer,
        val createdBy: String,
    )
}
