package com.nu.bom.core.manufacturing.entities

import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Hidden
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.AddSubtract
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Volume
import java.math.BigDecimal

@EntityType(Entities.MATERIAL_GEOMETRY)
class MaterialGeometry(name: String) : ManufacturingEntity(name) {
    override val extends = BaseEntityFields(name)

    fun quantityUnit(
        @Parent(Entities.MATERIAL)
        quantityUnit: Text,
    ) = quantityUnit

    @ReadOnly
    @MandatoryForEntity(index = 200, readOnly = true)
    @DynamicDenominatorUnit(unit = DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun mass(
        density: Density,
        volume: Volume,
    ): QuantityUnit = QuantityUnit(density.res * volume.res)

    fun calculatedMass(
        mass: QuantityUnit,
        addSubtract: AddSubtract,
    ): QuantityUnit {
        return when (addSubtract.res) {
            AddSubtract.Selection.ADD -> mass
            AddSubtract.Selection.SUBTRACT -> mass.times(-BigDecimal.ONE)
        }
    }

    @Input
    @MandatoryForEntity(index = 20, refresh = true)
    fun addSubtract(): AddSubtract = AddSubtract.ADD

    @Hidden
    @Input
    @MandatoryForEntity
    fun parentFieldIdentifier(): Text = throw MissingInputError()
}
