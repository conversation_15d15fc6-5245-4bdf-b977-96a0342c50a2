package com.nu.bom.core.manufacturing.fieldTypes

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.nu.bom.core.manufacturing.annotations.Units
import java.math.BigDecimal

enum class PowerUnits(override val baseFactor: BigDecimal) : TypeUnit {
    WATT(BigDecimal.ONE),
    KILOWATT(1000.toBigDecimal()),
    MEGAWATT(1000000.toBigDecimal())
    ;

    override val type = TypeUnits.POWER
    override val hideInDropdown = false
}

@Units(PowerUnits::class)
@JsonIgnoreProperties(value = [ "inkW", "inWatt"])
class Power(res: BigDecimal, unit: PowerUnits) : NumericFieldResultWithUnit<Power, PowerUnits>(res, unit) {
    constructor(res: Double, unit: PowerUnits) : this(res.toBigDecimal(), unit)
    constructor(res: String, unit: String) : this(BigDecimal(res), PowerUnits.valueOf(unit))
    constructor(res: BigDecimal, unit: String) : this(res, PowerUnits.valueOf(unit))

    val inWatt: BigDecimal get() { return to(PowerUnits.WATT) }
    val inkW: BigDecimal get() { return to(PowerUnits.KILOWATT) }
    val inMW: BigDecimal get() { return to(PowerUnits.MEGAWATT) }
}
