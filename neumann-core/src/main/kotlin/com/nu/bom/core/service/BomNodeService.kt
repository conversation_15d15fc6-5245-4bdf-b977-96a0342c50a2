package com.nu.bom.core.service

import com.nu.bom.core.api.dtos.BranchDto
import com.nu.bom.core.api.dtos.ReorderDirection
import com.nu.bom.core.api.dtos.bomradsToBranchDTO
import com.nu.bom.core.manufacturing.service.CalculationRequest
import com.nu.bom.core.model.BaseTriggerAction
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.ChangesetId
import com.nu.bom.core.model.toBomNodeId
import com.nu.bom.core.model.toMongoSnapshotId
import com.nu.bom.core.repository.BomNodeSnapshotRepository
import com.nu.bom.core.service.bomnode.LoadingMode
import com.nu.bom.core.service.bomrads.AllChildren
import com.nu.bom.core.service.bomrads.AllParents
import com.nu.bom.core.service.bomrads.BomNodeLoaderService
import com.nu.bom.core.service.bomrads.BomradsBomNodeService
import com.nu.bom.core.service.bomrads.SingleNode
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.user.UserClient
import com.nu.bom.core.utils.Either
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.nu.bomrads.dto.BranchViewDTO
import com.nu.bomrads.id.BomNodeId
import com.nu.bomrads.id.BranchId
import com.nu.bomrads.id.ProjectId
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import com.nu.bom.core.model.BomNodeId as MongoBomNodeId
import com.nu.bom.core.model.BranchId as MongoBranchId

@Service
class BomNodeService(
    private val bomNodeSnapshotRepository: BomNodeSnapshotRepository,
    private val bomradsBomNodeService: BomradsBomNodeService,
    private val bomNodeLoaderService: BomNodeLoaderService,
    private val userClient: UserClient,
) {
    fun renameNode(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        branchId: BranchId,
        bomNodeId: BomNodeId,
        name: String,
        renameDescendants: Boolean,
        trigger: BaseTriggerAction,
    ): Mono<BranchViewDTO> {
        return bomradsBomNodeService.renameNode(accessCheck, projectId, branchId, bomNodeId, name, renameDescendants, trigger)
    }

    fun getNodesRecursive(
        accessCheck: AccessCheck,
        loadingMode: LoadingMode,
        nodeId: MongoBomNodeId,
        branch: MongoBranchId?,
    ): Mono<BomNodeSnapshot> {
        val loadingMode =
            when (loadingMode) {
                LoadingMode.DIRECT ->
                    AllChildren(nodeId.toBomNodeId())
                LoadingMode.CALCULATION_CONTEXT ->
                    AllChildren(nodeId.toBomNodeId())
                LoadingMode.LAZY_CHILDREN ->
                    AllParents(nodeId.toBomNodeId())
            }
        return bomNodeLoaderService.getBomNode(
            accessCheck,
            bomNodeId = nodeId,
            branchId = branch,
            loadingMode = loadingMode,
        )
    }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    fun getManufacturingBranches(
        accessCheck: AccessCheck,
        bomNodeId: String,
    ): Flux<BranchDto> {
        val id = MongoBomNodeId(bomNodeId)
        return bomradsBomNodeService.getGlobalBranches(accessCheck, id.toBomNodeId())
            .flatMap { branchStatusDto ->
                /*
                Dirty hack - but due to Branching 1.0 compatibility we need to override the masters branch name with the title of the calculation
                as this is what the user expects with bomnode based branch as of branching 1.0
                 */
                val treeId = branchStatusDto.manufacturingTreeId
                if (branchStatusDto.snapshotTitle != null) {
                    Mono.just(Pair(branchStatusDto, branchStatusDto.snapshotTitle))
                } else if (treeId != null) {
                    bomNodeSnapshotRepository.findById(treeId.toMongoSnapshotId())
                        .map { Pair(branchStatusDto, it.title) }
                } else {
                    Mono.just(Pair(branchStatusDto, branchStatusDto.changeset.variantName))
                }
            }.map { (branchStatusDto, branchTitle) ->
                val branchDto = bomradsToBranchDTO(branchStatusDto, branchTitle)
                branchDto.copy(
                    lastModifiedBy =
                        userClient.getUserById(accessCheck, branchDto.lastModifiedBy).map { it.name }
                            .orElseGet { branchDto.lastModifiedBy },
                )
            }
    }

    fun getNodeWithBranch(
        accessCheck: AccessCheck,
        nodeId: MongoBomNodeId,
        branch: MongoBranchId?,
        loadingMode: com.nu.bom.core.service.bomrads.LoadingMode = SingleNode(nodeId),
    ): Mono<BomNodeSnapshot> {
        // This is already calling Bomrads, no need to fill the branch again
        return getBomNode(accessCheck, nodeId, branch, null, loadingMode)
    }

    fun getRootBomNodes(
        accountName: String,
        projectId: ProjectId,
    ): Mono<List<BomNodeId>> {
        return bomradsBomNodeService.getAdminBranchView(accountName = accountName, projectId = projectId, branchId = null).map {
                branchView ->
            branchView.snapshots().map { it.bomNodeId }
        }
    }

    fun lazyLoadSnapshot(
        request: CalculationRequest,
        bomNodeId: MongoBomNodeId,
    ): Mono<BomNodeSnapshot> {
        return bomNodeLoaderService.loadSnapshotsRoot(
            accessCheck = request.accessCheck,
            dto = request.getOriginalBranchViewDTO(),
            loadingMode = SingleNode(bomNodeId),
        ).map { it.first }
    }

    /**
     * Return a BomNodeSnapshot with BomNode linked
     */
    fun getBomNode(
        accessCheck: AccessCheck,
        nodeId: MongoBomNodeId,
        branch: MongoBranchId?,
        historical: Either<Int, ChangesetId>? = null,
        loadingMode: com.nu.bom.core.service.bomrads.LoadingMode = SingleNode(nodeId),
    ): Mono<BomNodeSnapshot> {
        if (historical != null) {
            // TODO: implement it in Bomrads
            throw NotImplementedError("☹️ Loading historical snapshots is not implemented yet in Bomrads!")
        }
        return bomNodeLoaderService.getBomNode(accessCheck, branchId = branch, bomNodeId = nodeId, loadingMode = loadingMode)
    }

    fun nodeExists(
        accessCheck: AccessCheck,
        bomNodeId: MongoBomNodeId,
        deletedMode: DeletedMode = DeletedMode.Check,
    ): Mono<Boolean> {
        return this.nodeExists(
            accessCheck,
            listOf(bomNodeId),
            deletedMode,
        )
    }

    fun nodeExists(
        accessCheck: AccessCheck,
        bomNodeIds: List<MongoBomNodeId>,
        deletedMode: DeletedMode = DeletedMode.Check,
    ): Mono<Boolean> {
        return bomradsBomNodeService.checkBomNodeExists(
            accessCheck,
            bomNodeIds.map { it.toBomNodeId() },
            deletedMode == DeletedMode.Check,
        )
    }

    fun filterAccessibleBomNodeIds(
        accessCheck: AccessCheck,
        bomNodeIds: Collection<MongoBomNodeId>,
    ): Flux<BomNodeId> {
        return bomradsBomNodeService.filterAccessibleBomNodes(
            accessCheck,
            bomNodeIds.map { it.toBomNodeId() },
        )
    }

    fun reorderRootNodes(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        bomNodeIds: List<MongoBomNodeId>,
        direction: ReorderDirection,
        steps: Int,
    ) = bomradsBomNodeService.reorderRootNodes(
        accessCheck,
        projectId,
        bomNodeIds.map(MongoBomNodeId::toBomNodeId),
        direction,
        steps,
    )

    enum class DeletedMode {
        Ignore,
        Check,
    }
}
