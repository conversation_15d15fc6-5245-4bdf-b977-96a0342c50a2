package com.nu.bom.core.manufacturing.extension.lookups

import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.manufacturing.fieldTypes.EmissionUnits

data class CO2RawMaterialPlasticGranulateco2emission(
    val gCo2PerKg: Emission,
    val materialDesignation: String,
)

val cO2gramCo2PlasticGranulateReader: (row: List<String>) -> CO2RawMaterialPlasticGranulateco2emission = { row ->
    CO2RawMaterialPlasticGranulateco2emission(
        materialDesignation = row[0],
        gCo2PerKg = Emission(row[1].toBigDecimal(), EmissionUnits.GRAM_CO2E),
    )
}

data class CO2RawMaterialCoilco2emission(
    val kgCo2PerKg: Emission,
    val materialDesignation: String,
)

val cO2gramCo2CoilReader: (row: List<String>) -> CO2RawMaterialCoilco2emission = { row ->
    CO2RawMaterialCoilco2emission(
        materialDesignation = row[0],
        kgCo2PerKg = Emission(row[1].toBigDecimal(), EmissionUnits.KILOGRAM_CO2E),
    )
}
