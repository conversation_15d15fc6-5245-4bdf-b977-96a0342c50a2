package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.threedb.bounds
import com.nu.bom.core.utils.toUpperSnakeCase

class InputGroup(
    res: Selection,
) : SelectEnumFieldResult<InputGroup.Selection, InputGroup>(res) {
    enum class Selection {
        CUBOID,
        HOLLOW_CUBOID,
        CYLINDER,
        HOLLOW_CYCLE,
        HOLLOW_CYCLE_LYING,
        DISC,
        HOLLOW_RING,
        RING_NO_HEIGHT,
        PIPE,
        SHAFT,
        T_SEGMENT,
        SHEET,
    }

    companion object {
        val CUBOID = InputGroup(Selection.CUBOID)
        val HOLLOW_CUBOID = InputGroup(Selection.HOLLOW_CUBOID)
        val CYLINDER = InputGroup(Selection.CYLINDER)
        val HOLLOW_CYCLE = InputGroup(Selection.HOLLOW_CYCLE)
        val HOLLOW_CYCLE_LYING = InputGroup(Selection.HOLLOW_CYCLE_LYING)
        val DISC = InputGroup(Selection.DISC)
        val HOLLOW_RING = InputGroup(Selection.HOLLOW_CYCLE)
        val RING_NO_HEIGHT = InputGroup(Selection.RING_NO_HEIGHT)
        val PIPE = InputGroup(Selection.PIPE)
        val SHAFT = InputGroup(Selection.SHAFT)
        val T_SEGMENT = InputGroup(Selection.T_SEGMENT)
        val SHEET = InputGroup(Selection.SHEET)

        fun valueOf(name: String): InputGroup = InputGroup(Selection.valueOf(name.toUpperSnakeCase()))

        fun dimensionsAreGreaterThanZeroForPartInputGroup(
            partInputGroup: InputGroup,
            partLength: Length?,
            partWidth: Length?,
            partHeight: Length?,
            partOuterDiameter: Length?,
            partUpperWidth: Length?,
        ): Boolean {
            val bounds = partInputGroup.res.bounds()
            val partDimensions =
                listOf(
                    bounds.length,
                    bounds.width,
                    bounds.height,
                ).map { dimension ->
                    dimension.select(partLength, partWidth, partHeight, partOuterDiameter, Length.ZERO, partUpperWidth)
                }

            return allPartDimensionGreaterThanZero(partDimensions)
        }

        private fun allPartDimensionGreaterThanZero(dimensionList: List<Length?>): Boolean =
            dimensionList.all {
                it != null &&
                    it > Length.ZERO
            }
    }
}
