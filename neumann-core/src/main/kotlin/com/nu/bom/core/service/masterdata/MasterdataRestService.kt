package com.nu.bom.core.service.masterdata

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.nu.bom.core.remote.NuService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.user.UserClient
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.nu.http.TsetService
import com.tset.bom.clients.exception.UserResponseStatusException
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpStatusCode
import org.springframework.stereotype.Service
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.reactive.function.client.ClientResponse
import org.springframework.web.util.UriBuilder
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.net.URI
import kotlin.reflect.KClass

/**
 * this service exists so that it can be lazy-autowired into MasterdataRestService.
 * This gives the advantage, that in the Integration tests we only need to create the masterdata testcontainer,
 * when a test really accesses the MD service
 */
@Service
@EnableConfigurationProperties(value = [ MasterdataClientProperties::class ])
class MasterdataConnectionService(
    val config: MasterdataClientProperties,
    val additionalHeaders: List<Pair<String, String>>,
)

@Service
class MasterdataRestService(
    private val tsetService: TsetService,
    private val userClient: UserClient,
    private val objectMapper: ObjectMapper,
) {
    companion object {
        private val logger: Logger = LoggerFactory.getLogger(MasterdataRestService::class.java)
        private const val RESILIENCE_NAME = "md"
    }

    @Autowired
    private lateinit var mdConnectionService: MasterdataConnectionService

    // add new methods, e.g. getToFlux when needed

    fun <R : Any> getToMono(
        uri: (UriBuilder) -> URI,
        responseType: KClass<R>,
        accessCheck: AccessCheck,
    ): Mono<R> {
        logger.debug("MD request getToMono to {}", uri)
        return tsetService.getToMono(
            baseUrl = getBaseUrl(),
            uri = uri,
            headers = getHeaders(accessCheck),
            jwtToken = getJwtToken(accessCheck),
            successHandler = { response ->
                response.bodyToMono(responseType.java)
            },
            errorHandler = ::handleMonoError,
            resilienceName = RESILIENCE_NAME,
        )
    }

    fun <R : Any> getToFlux(
        uri: (UriBuilder) -> URI,
        responseType: KClass<R>,
        accessCheck: AccessCheck,
    ): Flux<R> {
        logger.debug("MD request getToFlux to {}", uri)
        return tsetService.getToFlux(
            baseUrl = getBaseUrl(),
            uri = uri,
            headers = getHeaders(accessCheck),
            jwtToken = getJwtToken(accessCheck),
            successHandler = { response ->
                response.bodyToFlux(responseType.java)
            },
            errorHandler = ::handleFluxError,
            resilienceName = RESILIENCE_NAME,
        )
    }

    fun <R : Any> getToMono(
        uri: (UriBuilder) -> URI,
        responseType: ParameterizedTypeReference<R>,
        accessCheck: AccessCheck,
    ): Mono<R> {
        logger.debug("MD request getToMono to {}", uri)
        return tsetService.getToMono(
            baseUrl = getBaseUrl(),
            uri = uri,
            headers = getHeaders(accessCheck),
            jwtToken = getJwtToken(accessCheck),
            successHandler = { response ->
                response.bodyToMono(responseType)
            },
            errorHandler = ::handleMonoError,
            resilienceName = RESILIENCE_NAME,
        )
    }

    fun deleteToVoidMono(
        uri: (UriBuilder) -> URI,
        accessCheck: AccessCheck,
    ): Mono<Void> {
        logger.debug("MD request deleteToMono to {}", uri)
        return tsetService.deleteToMono(
            baseUrl = getBaseUrl(),
            uri = uri,
            headers = getHeaders(accessCheck),
            jwtToken = getJwtToken(accessCheck),
            successHandler = { response -> response.releaseBody() },
            errorHandler = ::handleMonoError,
            resilienceName = RESILIENCE_NAME,
        )
    }

    fun <B : Any, R : Any> postToMono(
        uri: (UriBuilder) -> URI,
        requestBody: B,
        responseType: KClass<R>,
        accessCheck: AccessCheck,
    ): Mono<R> {
        return postToMono(
            uri = uri,
            requestBody = requestBody,
            successHandler = { response -> response.bodyToMono(responseType.java) },
            accessCheck = accessCheck,
        )
    }

    fun <B : Any, R : Any> postToMono(
        uri: (UriBuilder) -> URI,
        requestBody: B,
        responseType: ParameterizedTypeReference<R>,
        accessCheck: AccessCheck,
    ): Mono<R> {
        return postToMono(
            uri = uri,
            requestBody = requestBody,
            successHandler = { response -> response.bodyToMono(responseType) },
            accessCheck = accessCheck,
        )
    }

    private fun <B : Any, R : Any> postToMono(
        uri: (UriBuilder) -> URI,
        requestBody: B,
        successHandler: (ClientResponse) -> Mono<R>,
        accessCheck: AccessCheck,
    ): Mono<R> {
        logger.debug("MD request postToMono to {}", uri)
        return tsetService.postToMono(
            baseUrl = getBaseUrl(),
            requestBody = requestBody,
            uri = uri,
            headers = getHeaders(accessCheck),
            jwtToken = getJwtToken(accessCheck),
            successHandler = successHandler,
            errorHandler = ::handleMonoError,
            resilienceName = RESILIENCE_NAME,
        )
    }

    fun <B : Any, R : Any> putToMono(
        uri: (UriBuilder) -> URI,
        requestBody: B,
        responseType: KClass<R>,
        accessCheck: AccessCheck,
    ): Mono<R> {
        return putToMono(
            uri = uri,
            requestBody = requestBody,
            successHandler = { response -> response.bodyToMono(responseType.java) },
            accessCheck = accessCheck,
        )
    }

    fun <B : Any, R : Any> putToMono(
        uri: (UriBuilder) -> URI,
        requestBody: B,
        responseType: ParameterizedTypeReference<R>,
        accessCheck: AccessCheck,
    ): Mono<R> {
        return putToMono(
            uri = uri,
            requestBody = requestBody,
            successHandler = { response -> response.bodyToMono(responseType) },
            accessCheck = accessCheck,
        )
    }

    private fun <B : Any, R : Any> putToMono(
        uri: (UriBuilder) -> URI,
        requestBody: B,
        successHandler: (ClientResponse) -> Mono<R>,
        accessCheck: AccessCheck,
    ): Mono<R> {
        logger.debug("MD request putToMono to {}", uri)
        return tsetService.putToMono(
            baseUrl = getBaseUrl(),
            requestBody = requestBody,
            uri = uri,
            headers = getHeaders(accessCheck),
            jwtToken = getJwtToken(accessCheck),
            successHandler = successHandler,
            errorHandler = ::handleMonoError,
            resilienceName = RESILIENCE_NAME,
        )
    }

    fun <S : Any> handleMonoError(response: ClientResponse): Mono<S> =
        response.bodyToMono(String::class.java).flatMap { strError ->
            val exception = handleError(strError, response.statusCode())
            Mono.error(exception)
        }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    fun <S : Any> handleFluxError(response: ClientResponse): Flux<S> =
        response.bodyToFlux(String::class.java).flatMap { strError ->
            Flux.error(handleError(strError, response.statusCode()))
        }

    private fun handleError(
        strError: String,
        statusCode: HttpStatusCode,
    ): Exception {
        logger.error("Error calling {}: {}", getBaseUrl(), strError)
        return runCatching {
            val jsonError: Map<String, Any> = objectMapper.readValue(strError)
            (jsonError["userErrorCode"] as? String)?.let { errorCode ->
                UserResponseStatusException(
                    statusCode,
                    errorCode,
                    (jsonError["fallbackMessage"] as? String) ?: "",
                    (jsonError["userParameters"] as? List<Any?>) ?: emptyList(),
                )
            }
        }.getOrNull() ?: HttpClientErrorException(statusCode, "Error calling ${getBaseUrl()}: $strError")
    }

    private fun getJwtToken(accessCheck: AccessCheck): String {
        val config = mdConnectionService.config
        val overrideToken = config.overrideToken
        return if (overrideToken != null) {
            logger.trace("using override token: $overrideToken")
            overrideToken
        } else {
            if (accessCheck.token == AccessCheck.EMPTY_TOKEN && config.useServiceTokenAsFallback) {
                NuService.logger.trace("use service token for ${accessCheck.userName}")
                userClient.getServiceToken()
            } else {
                NuService.logger.trace("using client token: ${accessCheck.token}")
                accessCheck.token
            }
        }
    }

    private fun getAccountName(accessCheck: AccessCheck): String {
        val config = mdConnectionService.config
        return if (accessCheck.token == AccessCheck.EMPTY_TOKEN && config.accountNameForServiceTokens != null) {
            logger.debug("For ${accessCheck.userName} use service token, and account=${config.accountNameForServiceTokens}")
            config.accountNameForServiceTokens
        } else {
            accessCheck.accountName
        }
    }

    private fun getBaseUrl(): String = mdConnectionService.config.url ?: ""

    private fun getHeaders(accessCheck: AccessCheck): Map<String, String> {
        val listHeaders =
            (
                listOf(
                    com.nu.security.BomradsService.NU_ACCOUNT_HEADER to getAccountName(accessCheck),
                    com.nu.security.BomradsService.NU_USER_ID_HEADER to accessCheck.userId,
                ) +
                    mdConnectionService.additionalHeaders
            )
        return listHeaders.toMap()
    }
}
