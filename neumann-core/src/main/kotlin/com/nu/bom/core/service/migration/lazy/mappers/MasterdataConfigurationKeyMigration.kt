package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.MasterdataConfigurationKey
import com.nu.bom.core.model.configurations.SemanticVersion
import com.nu.bom.core.service.configurations.MasterdataTsetConfigurationService
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import org.springframework.stereotype.Service

@Service
class MasterdataConfigurationKeyMigration : BaseConfigKeyMigration<MasterdataConfigurationKey>(
    configurationKeyFieldName = Manufacturing::masterdataConfigurationKey.name,
    configurationIdentifier =
        ConfigurationIdentifier.tset(
            MasterdataTsetConfigurationService.TSET_CONFIGURATION_KEY,
            SemanticVersion.initialVersion(),
        ),
    configurationKeyClazz = MasterdataConfigurationKey::class,
) {
    override val changeSetId: MigrationChangeSetId
        get() = MigrationChangeSetId("2023-10-20-masterdata-configuration-key")
}
