package com.nu.bom.core.service

import com.nu.bom.core.api.dtos.CalculationModel
import com.nu.bom.core.manufacturing.Model
import org.springframework.stereotype.Service

@Service
class ManufacturingModelsUtils {
    companion object {
        private val MODELS =
            listOf(
                Model.BART,
                Model.RAWT,
                Model.CHILL,
                Model.DCA,
                Model.PREC,
                Model.CERAMIC_MOLD,
                Model.SAND,
                Model.CORE,
                Model.CORES,
                Model.VPREC,
                Model.CEXT,
                Model.ALEX,
                Model.CROL,
                Model.DFOR,
                Model.AFOR,
                Model.CHAT,
                Model.WHAT,
                Model.RROL,
                Model.RSWA,
                Model.INJ,
                Model.INJ2,
                Model.MINJ,
                Model.RINJ,
                Model.SINT,
                Model.MAGN,
                Model.MANUAL,
                Model.MILL,
                Model.DFORT, // for compatibility with old calculations only
                Model.PCB,
                Model.PCBA,
                Model.CUBE,
                Model.LAST,
                Model.FTIPDS,
                Model.FTITDS,
                Model.PBOX,
            )

        private fun findAll(): List<CalculationModel> = MODELS.map { it.toCalculationModel() }

        fun findAllModels(): List<Model> = MODELS

        fun findAll(predicate: (CalculationModel) -> Boolean): List<CalculationModel> = findAll().filter(predicate)

        fun findByEntity(entity: String?): CalculationModel? =
            findAll().find {
                it.entity == entity
            }
    }
}
