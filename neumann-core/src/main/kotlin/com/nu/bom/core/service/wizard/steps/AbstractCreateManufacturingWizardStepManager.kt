package com.nu.bom.core.service.wizard.steps

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.nu.bom.core.model.Wizard
import com.nu.bom.core.repository.WizardRepository
import com.nu.bom.core.service.wizard.IWizardStepManager
import com.nu.bom.core.user.AccessCheck
import com.tset.core.api.calculation.dto.CalculationCreationModalMode
import com.tset.core.api.calculation.dto.CalculationCreationModalMode.CALCULATION_MODE_CHANGE_TYPE
import com.tset.core.api.calculation.dto.CalculationCreationModalMode.CALCULATION_MODE_EDIT
import com.tset.core.api.calculation.dto.CalculationCreationModalMode.CALCULATION_MODE_NEW
import com.tset.core.service.domain.Currency
import org.bson.types.ObjectId
import reactor.core.publisher.Mono
import kotlin.reflect.KClass

@JsonIgnoreProperties(ignoreUnknown = true)
open class WizardStep(
    var no: Int = 0,
    var summary: String = "",
)

interface WizardStepInterface

sealed class AbstractCreateManufacturingWizardStepManager<T : WizardStep>(
    val wizardRepository: WizardRepository,
) : IWizardStepManager<T> {
    override fun saveStep(
        wizardId: ObjectId,
        wizardStep: T,
        accessCheck: AccessCheck,
        currency: Currency,
    ): Mono<Wizard> =
        wizardRepository
            .findById(wizardId)
            .map { wizard ->
                wizard.steps[wizardStep::class.simpleName!!] = wizardStep
                wizard.steps =
                    wizard.steps
                        .filter {
                            it.value.no <= wizardStep.no
                        }.toMutableMap()
                wizard
            }.flatMap {
                wizardRepository.save(it)
            }

    override fun getStep(
        accessCheck: AccessCheck,
        wizardId: ObjectId,
        step: KClass<out WizardStep>,
        mode: CalculationCreationModalMode,
    ): Mono<out T> =
        wizardRepository
            .findById(wizardId)
            .switchIfEmpty(Mono.error(Error("wizard not found")))
            .flatMap { wizard ->
                when (val maybeStep = wizard.getSteps(step)) {
                    null -> requestStep(accessCheck, wizard, mode)
                    else -> Mono.just(migrate(accessCheck, wizard, maybeStep as T))
                }
            }

    open fun migrate(
        accessCheck: AccessCheck,
        wizard: Wizard,
        wizardPage: T,
    ): T = wizardPage

    open fun requestStep(
        accessCheck: AccessCheck,
        wizard: Wizard,
        mode: CalculationCreationModalMode,
    ): Mono<out T> =
        when (mode) {
            CALCULATION_MODE_NEW ->
                prefillStep(accessCheck, wizard).flatMap {
                    prepopulateNew(accessCheck, wizard, it)
                }
            CALCULATION_MODE_EDIT ->
                prefillStep(accessCheck, wizard).flatMap { prefilledStep ->
                    prepopulateStep(accessCheck, wizard, prefilledStep)
                }
            CALCULATION_MODE_CHANGE_TYPE -> prefillStep(accessCheck, wizard)
        }

    open fun prepopulateNew(
        accessCheck: AccessCheck,
        wizard: Wizard,
        prefilledStep: T,
    ): Mono<out T> = Mono.just(prefilledStep)

    // prefill values from defaults
    abstract fun prefillStep(
        accessCheck: AccessCheck,
        wizard: Wizard,
    ): Mono<out T>

    // load values from existing manufacturing entity (only in EDIT mode)
    abstract fun prepopulateStep(
        accessCheck: AccessCheck,
        wizard: Wizard,
        prefilledStep: T,
    ): Mono<out T>
}
