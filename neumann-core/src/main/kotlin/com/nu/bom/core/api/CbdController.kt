package com.nu.bom.core.api

import com.nu.bom.core.api.dtos.Cbd
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.createBranchId
import com.nu.bom.core.service.BomNodeService
import com.nu.bom.core.service.CbdService
import com.nu.bom.core.user.AccessCheckProvider
import com.tset.core.service.domain.Currency
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Mono

@RestController
@RequestMapping("/api")
class CbdController(
    private val bomNodeService: BomNodeService,
    private val accessCheckProvider: AccessCheckProvider,
    private val cbdService: CbdService,
) {
    val logger: Logger = LoggerFactory.getLogger(CbdController::class.java)

    @GetMapping("/man/{bomNodeId}/cbd")
    fun getManufacturing(
        @AuthenticationPrincipal jwt: Jwt?,
        @RequestParam branch: String?,
        @RequestParam(required = false) ccy: List<Currency>?,
        @PathVariable bomNodeId: String,
    ): Mono<Cbd> =
        accessCheckProvider.doAs(jwt) { accessCheck ->
            bomNodeService
                .getBomNode(accessCheck, BomNodeId(bomNodeId), branch = createBranchId(branch))
                .map { it.manufacturing!! }
                .map { manu ->
                    cbdService.createActiveCbdFromManufacturing(manu, manu.getExchangeRateMap(ccy), normalizeCostUnit = false)
                }
        }
}
