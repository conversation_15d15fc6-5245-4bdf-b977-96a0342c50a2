package com.nu.bom.core.service.migration.lazy.mappers

import com.google.common.annotations.VisibleForTesting
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost.CommercialCalculationCostManufacturedMaterial
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost.CommercialCalculationCostMaterialUsage
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.CustomProcurementTypeWrapper
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetProcurementType
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CustomProcurementType
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import com.nu.bom.core.service.migration.lazy.mappers.utils.ValueReader.readProcurementType
import org.springframework.stereotype.Service

@Service
class ProcurementTypeConfigurationFieldsMigration : ManufacturingModelEntityMapper {
    override val changeSetId = MigrationChangeSetId("2025-02-30-proc-type-config-field")

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity {
        val allowedTypes =
            when (entity.type) {
                Entities.CONSUMABLE.name,
                Entities.C_PART.name,
                Entities.MATERIAL.name,
                ->
                    TsetProcurementType.entries
                        .filterNot { it == TsetProcurementType.INHOUSE }
                        .map { it.customProcurementType }
                else ->
                    TsetProcurementType.entries
                        .map { it.customProcurementType }
            }
        return entity.copyAll(
            initialFieldWithResults = updateFields(entity.initialFieldWithResults, allowedTypes),
            fieldWithResults = updateFields(entity.fieldWithResults, allowedTypes),
        )
    }

    @VisibleForTesting
    fun updateFields(
        fields: Map<String, FieldResultModel>,
        allowedTypes: List<CustomProcurementTypeWrapper>,
    ): Map<String, FieldResultModel> {
        val oldProcurementTypeField =
            fields[CommercialCalculationCostMaterialUsage::procurementType.name]
                ?: return fields

        val newFields =
            getNewFields(
                readProcurementType(fields),
                allowedTypes,
                oldProcurementTypeField.source,
                oldProcurementTypeField.version,
                oldProcurementTypeField.newVersion,
            )

        return fields.mapValues { (fieldName, field) ->
            if (fieldName == CommercialCalculationCostMaterialUsage::procurementType.name) {
                field.copyAll(source = FieldResult.SOURCE.C.name)
            } else {
                field
            }
        } + newFields
    }

    private fun getNewFields(
        oldProcurementType: ManufacturingType.Type,
        allowedTypes: List<CustomProcurementTypeWrapper>,
        oldSource: String,
        version: Int,
        newVersion: Int,
    ): Map<String, FieldResultModel> {
        val newType =
            when (oldProcurementType) {
                ManufacturingType.Type.INHOUSE -> TsetProcurementType.INHOUSE.customProcurementType.value
                ManufacturingType.Type.PURCHASE -> TsetProcurementType.PURCHASE.customProcurementType.value
            }

        val newTranslation =
            when (oldProcurementType) {
                ManufacturingType.Type.INHOUSE -> "In-house manufacturing"
                ManufacturingType.Type.PURCHASE -> "Purchased"
            }

        val newShortName =
            when (oldProcurementType) {
                ManufacturingType.Type.INHOUSE -> "IH"
                ManufacturingType.Type.PURCHASE -> "PU"
            }

        return mapOf(
            CommercialCalculationCostManufacturedMaterial::customProcurementType.name to
                FieldResultModel(
                    version = version,
                    newVersion = newVersion,
                    type = CustomProcurementType::class.simpleName!!,
                    value = newType,
                    source = oldSource,
                ),
            CommercialCalculationCostManufacturedMaterial::allowedCustomProcurementTypes.name to
                FieldResultModel(
                    version = version,
                    newVersion = newVersion,
                    type = Text::class.simpleName!!,
                    value = allowedTypes.joinToString(",") { it.value },
                    source = FieldResult.SOURCE.C.name,
                ),
            CommercialCalculationCostManufacturedMaterial::customProcurementTypeName.name to
                FieldResultModel(
                    version = version,
                    newVersion = newVersion,
                    type = Text::class.simpleName!!,
                    value = newTranslation,
                    source = FieldResult.SOURCE.C.name,
                ),
            CommercialCalculationCostManufacturedMaterial::customProcurementTypeShortName.name to
                FieldResultModel(
                    version = version,
                    newVersion = newVersion,
                    type = Text::class.simpleName!!,
                    value = newShortName,
                    source = FieldResult.SOURCE.C.name,
                ),
            CommercialCalculationCostManufacturedMaterial::customProcurementTypeLovTypeKey.name to
                FieldResultModel(
                    version = version,
                    newVersion = newVersion,
                    type = Text::class.simpleName!!,
                    value = "tset.ref.lov-type.procurement-type",
                    source = FieldResult.SOURCE.C.name,
                ),
        )
    }
}
