package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.utils.toUpperSnakeCase

class PressType(res: Selection) : SelectEnumFieldResult<PressType.Selection, PressType>(res) {
    enum class Selection(val value: String) {
        TOGGLE("Toggle"),
        SERVO("Servo"),
    }

    companion object {
        val TOGGLE = PressType(Selection.TOGGLE)
        val SERVO = PressType(Selection.SERVO)
    }

    fun valueOf(name: String): PressType {
        return Selection.entries.find {
            it.value == name
        }?.let {
            PressType(it)
        } ?: PressType(PressType.Selection.valueOf(name.toUpperSnakeCase()))
    }
}
