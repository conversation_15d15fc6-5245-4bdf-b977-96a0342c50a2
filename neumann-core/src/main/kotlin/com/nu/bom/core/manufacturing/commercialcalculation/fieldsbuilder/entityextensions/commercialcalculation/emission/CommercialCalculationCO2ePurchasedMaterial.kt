package com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.emission

import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Extends
import com.nu.bom.core.manufacturing.annotations.InterfaceField
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.CommercialCalculationException
import com.nu.bom.core.manufacturing.entities.BaseMaterial
import com.nu.bom.core.manufacturing.entities.ManualBaseMaterial
import com.nu.bom.core.manufacturing.entities.ManualMasterdataMaterial
import com.nu.bom.core.manufacturing.entities.ManualMaterial
import com.nu.bom.core.manufacturing.entities.ManufacturingEntityExtension
import com.nu.bom.core.manufacturing.entities.RawMaterial
import com.nu.bom.core.manufacturing.entities.RawMaterialManual
import com.nu.bom.core.manufacturing.entities.RoughManufacturing
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.extension.COMMERCIAL_CALCULATION_CO2_FIELD_PACKAGE
import com.nu.bom.core.manufacturing.fieldTypes.Emission

@Extends(
    [
        BaseMaterial::class,
        ManualBaseMaterial::class,
        RawMaterial::class,
        RawMaterialManual::class,
        ManualMaterial::class,
        ManualMasterdataMaterial::class,
        RoughManufacturing::class,
    ],
    COMMERCIAL_CALCULATION_CO2_FIELD_PACKAGE,
)
@EntityType(Entities.NONE)
@Suppress("DEPRECATION")
class CommercialCalculationCO2ePurchasedMaterial(name: String) : ManufacturingEntityExtension(name) {
    @InterfaceField
    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun purchaseCO2(): Emission =
        throw CommercialCalculationException.missingRequiredField(
            CommercialCalculationCO2ePurchasedMaterial::purchaseCO2.name,
            CommercialCalculationCO2ePurchasedMaterial::class.simpleName!!,
        )
}
