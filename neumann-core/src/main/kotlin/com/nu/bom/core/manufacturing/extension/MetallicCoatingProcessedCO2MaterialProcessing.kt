package com.nu.bom.core.manufacturing.extension

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.extension.lookups.cO2MaterialReader
import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.manufacturing.fieldTypes.EmissionUnits
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Text
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono
import java.math.BigDecimal

@EntityType(Entities.CO2_PROCESSING_MATERIAL)
class MetallicCoatingProcessedCO2MaterialProcessing(name: String) : ManufacturingEntity(name) {
    override val extends = BaseCO2MaterialProcessing(name)

    @ReadOnly
    @Parent(Entities.CO2_PROCESSING_MATERIAL)
    fun location(): Text = Text("Austria")

    fun quantity() = Num(BigDecimal.ONE)

    @Input
    fun cO2PerUnit(
        @Parent(Entities.MATERIAL) designation: Text?,
    ): Mono<Emission> {
        return services.getLookupTable(
            "CO2_RawMaterialMetallicCoating",
            cO2MaterialReader,
        )
            .filter { it.materialDesignation == designation?.res }
            .singleOrEmpty().map { it.gCo2PerKg }
            .switchIfEmpty(Emission(0.0, EmissionUnits.KILOGRAM_CO2E).toMono())
    }

    @Input
    fun cO2Passive(cO2PerUnit: Emission) = cO2PerUnit
}
