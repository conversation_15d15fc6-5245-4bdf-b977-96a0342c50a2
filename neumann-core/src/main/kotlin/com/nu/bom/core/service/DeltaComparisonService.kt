package com.nu.bom.core.service

import com.nu.bom.core.api.dtos.Cbd
import com.nu.bom.core.api.dtos.CbdRow
import com.nu.bom.core.api.dtos.DeltaBranchId
import com.nu.bom.core.api.dtos.DeltaPartInfo
import com.nu.bom.core.api.dtos.DeltaRow
import com.nu.bom.core.api.dtos.DeltaTotal
import com.nu.bom.core.api.dtos.FieldConversionService
import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.manufacturing.entities.Attachment
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.UnitOverrideContext
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.model.AccountId
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.DeltaComparison
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.component1
import com.nu.bom.core.utils.component2
import com.nu.bom.core.utils.percentageDifference
import com.nu.bom.core.utils.tryCast
import com.nu.bom.core.utils.wrapIntoMaybe
import com.tset.core.api.dto.MoneyFieldParameter
import com.tset.core.module.bom.exchangerates.ExchangeRateMap
import com.tset.core.module.mongodb.infrastructure.dto.RefKeyForAccountMongoDto
import com.tset.core.service.RefKeyService
import com.tset.core.service.domain.AnalyticsDeltaComparison
import com.tset.core.service.domain.RefKeyForAccount
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono

@Service
class DeltaComparisonService(
    private val bomNodeService: BomNodeService,
    private val cbdService: CbdService,
    private val fieldConversionService: FieldConversionService,
    private val refKeyService: RefKeyService,
) {
    companion object {
        private val NO_MATCHERS = emptyList<Matcher>()
    }

    fun compare(
        accessCheck: AccessCheck,
        name: String? = null,
        bomNodeA: BomNodeId?,
        bomNodeB: BomNodeId?,
        branchA: BranchId? = null,
        branchB: BranchId? = null,
    ): Mono<DeltaComparison> {
        // fetch CBD of any or both sides
        val left =
            Mono.justOrEmpty(bomNodeA)
                .flatMap { getCbdWithPartAndBranch(accessCheck, it, branchA) }
                .wrapIntoMaybe()

        val right =
            Mono.justOrEmpty(bomNodeB)
                .flatMap { getCbdWithPartAndBranch(accessCheck, it, branchB) }
                .wrapIntoMaybe()

        return Mono.zip(left, right).flatMap { (left, right) ->
            require(left.exists() || right.exists()) { "Invalid response from cbd service" }
            createNewRefKey(accessCheck.asAccountId()).flatMap { refKey ->
                toDeltaComparison(accessCheck, name ?: "", left.value, right.value, refKey)
            }
        }
    }

    private fun toDeltaComparison(
        accessCheck: AccessCheck,
        name: String,
        left: Pair<BomNodeSnapshot, Cbd>?,
        right: Pair<BomNodeSnapshot, Cbd>?,
        refKey: RefKeyForAccount,
    ): Mono<DeltaComparison> {
        val leftMono =
            Mono.justOrEmpty(left?.first).flatMap { snap ->
                toDeltaPartInfo(accessCheck, snap)
            }.wrapIntoMaybe()

        val rightMono =
            Mono.justOrEmpty(right?.first).flatMap { snap ->
                toDeltaPartInfo(accessCheck, snap)
            }.wrapIntoMaybe()

        return Mono.zip(leftMono, rightMono).map { (maybePartA, maybePartB) ->
            val deltaTotal =
                if (maybePartA.exists() && maybePartB.exists()) {
                    val leftDimension = left!!.first.manufacturing!!.getFieldResult("dimension")!! as Dimension
                    val rightDimension = right!!.first.manufacturing!!.getFieldResult("dimension")!! as Dimension
                    require(leftDimension == rightDimension) {
                        "Cannot compare calculation with different dimensions: $leftDimension != $rightDimension"
                    }
                    toDeltaTotal(maybePartA.value!!, maybePartB.value!!)
                } else {
                    null
                }

            val deltaRows = toDeltaRows(left = left, right = right)

            DeltaComparison(
                name = name,
                sideA = maybePartA.value,
                sideB = maybePartB.value,
                deltaTotal = deltaTotal,
                deltaResult = deltaRows,
                refKey = RefKeyForAccountMongoDto.fromRefKey(refKey),
            )
        }
    }

    private fun toDeltaPartInfo(
        accessCheck: AccessCheck,
        snapshot: BomNodeSnapshot,
    ): Mono<DeltaPartInfo> {
        // assume snapshot was fetched with part and branch
        val baseManufacturingEntity = snapshot.getBaseManufacturing()!!
        val partName =
            baseManufacturingEntity.getFieldResult(
                "partDesignation",
            )?.res?.toString() ?: ""//baseManufacturingEntity.partInfo.partDesignation

        val manufacturing = snapshot.manufacturing!!

        val averageProductionVolumePerYearRes =
            getFieldParameter(accessCheck, "averageUsableProductionVolumePerYear", manufacturing)
        val lifeTimeRes = getFieldParameter(accessCheck, "lifeTime", manufacturing)
        val locationNameRes = getFieldParameter(accessCheck, "locationName", manufacturing)
        val technologyModelRes = getFieldParameter(accessCheck, "technologyModel", manufacturing)
        val costPerPartRes = getFieldParameter(accessCheck, "costPerPart", manufacturing)
        return Mono.zip(
            averageProductionVolumePerYearRes,
            lifeTimeRes,
            locationNameRes,
            technologyModelRes,
            costPerPartRes,
        ).map {
            DeltaPartInfo(
                partName = partName,
                partNumber = baseManufacturingEntity.getFieldResult("partNumber")?.res?.toString(),
                partImages = snapshot.getBaseManufacturing()?.getImages() ?: emptyList(),
                averageProductionVolumePerYear = it.t1,
                lifeTime = it.t2,
                location = it.t3,
                technology = it.t4,
                costPerPart = it.t5,
                lastModifiedDate = snapshot.lastModifiedDate!!,
                branch = DeltaBranchId(snapshot.branchIdStr()),
                bomNodeId = snapshot.bomNodeId().toHexString(),
                snapshotId = snapshot.id().toHexString(),
                title = snapshot.title,
            )
        }
    }

    private fun getFieldParameter(
        accessCheck: AccessCheck,
        field: String,
        manufacturing: ManufacturingEntity,
    ): Mono<FieldParameter> {
        // ignore cost and quantity unit set by user for delta comparison
        val customUnitOverrideContext = UnitOverrideContext.defaultFromDimension(UnitOverrideContext.fromEntity(manufacturing).dimension)
        return fieldConversionService.fieldResultToFieldParameterWithOthers(
            accessCheck,
            field,
            manufacturing.getFieldResult(field)!!,
            manufacturing.javaClass.simpleName,
            otherFields = manufacturing,
            null,
            exchangeRateMap = ExchangeRateMap.empty(),
            unitOverrideContext = customUnitOverrideContext,
            entityForDynamicMetaData = manufacturing,
        )
    }

    private fun toDeltaTotal(
        left: DeltaPartInfo,
        right: DeltaPartInfo,
    ): DeltaTotal {
        val totalLeft = MoneyFieldParameter.fromFieldParameter(left.costPerPart)
        val totalRight = MoneyFieldParameter.fromFieldParameter(right.costPerPart)

        return DeltaTotal(
            deltaValueMoney = totalRight.minus(totalLeft),
            deltaValuePercent = percentageDifference(first = totalLeft.value, second = totalRight.value),
        )
    }

    private fun toDeltaRows(
        left: Pair<BomNodeSnapshot, Cbd>?,
        right: Pair<BomNodeSnapshot, Cbd>?,
    ): List<DeltaRow> {
        // configure matching logic for comparable CbdRows
        val matchers =
            configureMatchers(
                leftRoot = left?.first?.manufacturing,
                rightRoot = right?.first?.manufacturing,
            )

        return matchAndFlatten(
            leftList = left?.second?.cost,
            rightList = right?.second?.cost,
            matchers = matchers,
            level = 0,
        )
    }

    private fun configureMatchers(
        leftRoot: ManufacturingEntity?,
        rightRoot: ManufacturingEntity?,
    ): List<Matcher> {
        val isSameTechnology = isSameTechnology(leftRoot, rightRoot)

        // match static rows by name
        val staticMatcher =
            Matcher(
                predicate = ::isStatic,
                finderFactory = FinderFactory.from(::findByNameStrict),
            )

        // match step rows by name
        val stepMatcher =
            Matcher(
                predicate = { row ->
                    isSameTechnology && row.entityType == Entities.MANUFACTURING_STEP.name
                },
                finderFactory = FinderFactory.from(::findByName),
            )

        // match generated materials
        val generatedMaterialMatcher =
            Matcher(
                predicate = { row ->
                    row.entityType == Entities.MATERIAL.name && leftRoot?.findByEntityId(row.entityId)?.createdBy != null
                },
                finderFactory =
                    FinderFactory.from { otherList ->
                        otherList.find { row ->
                            row.entityType == Entities.MATERIAL.name && rightRoot?.findByEntityId(row.entityId)?.createdBy != null
                        }
                    },
            )

        return listOf(staticMatcher, stepMatcher, generatedMaterialMatcher)
    }

    private fun matchAndFlatten(
        leftList: List<CbdRow>?,
        rightList: List<CbdRow>?,
        level: Int,
        matchers: List<Matcher>,
    ): List<DeltaRow> {
        val deltaRows = mutableListOf<DeltaRow>()

        // empty list
        if (leftList == null && rightList == null) {
            return deltaRows
            // flatten left only (no matchers)
        } else if (rightList == null) {
            leftList!!.forEach { left ->
                deltaRows += Pair(left, null).toDeltaRow(level = level)
                deltaRows +=
                    matchAndFlatten(
                        leftList = left.nestedTable,
                        rightList = null,
                        level = level + 1,
                        matchers = NO_MATCHERS,
                    )
            }
            // flatten right only (no matchers)
        } else if (leftList == null) {
            rightList.forEach { right ->
                deltaRows += Pair(null, right).toDeltaRow(level = level)
                deltaRows +=
                    matchAndFlatten(
                        leftList = null,
                        rightList = right.nestedTable,
                        level = level + 1,
                        matchers = NO_MATCHERS,
                    )
            }
            // match & flatten
        } else {
            val rightListMutable = rightList.toMutableList()

            // sort to ensure structure: (matching -> left-only -> right-only)
            // within the same matchable type (excluding static rows)
            val leftListSorted = leftList.sortByMatchability(matchers = matchers, otherList = rightList)

            // match pairs and remove other pair from right list
            leftListSorted.forEach { left ->

                // pop a matching row for "left" from "rightListMutable" (if any)
                val rightMatch =
                    matchers
                        .find { matcher -> matcher.predicate(left) }
                        ?.finderFactory?.getFinderFor(left)
                        ?.find(rightListMutable)
                        ?.also { rightListMutable.remove(it) }

                deltaRows += Pair(left, rightMatch).toDeltaRow(level = level)
                deltaRows +=
                    matchAndFlatten(
                        leftList = left.nestedTable,
                        rightList = rightMatch?.nestedTable,
                        level = level + 1,
                        matchers = matchers,
                    )
            }
            // flatten remaining unmatched right entries (no matchers)
            rightListMutable.forEach { right ->
                deltaRows += Pair(null, right).toDeltaRow(level = level)
                deltaRows +=
                    matchAndFlatten(
                        leftList = null,
                        rightList = right.nestedTable,
                        level = level + 1,
                        matchers = NO_MATCHERS,
                    )
            }
        }

        return deltaRows
    }

    private fun Pair<CbdRow?, CbdRow?>.toDeltaRow(level: Int): DeltaRow {
        val (left, right) = this
        val leftCost = left?.cost?.tryCast<MoneyFieldParameter>()
        val rightCost = right?.cost?.tryCast<MoneyFieldParameter>()

        return DeltaRow(
            level = level,
            leftLabel = left?.source,
            leftValue = leftCost,
            rightLabel = right?.source,
            rightValue = rightCost,
            leftType = left?.entityType,
            rightType = right?.entityType,
            deltaValueMoney = rightCost?.minus(leftCost) ?: leftCost,
            deltaValuePercent = percentageDifference(leftCost?.value, rightCost?.value),
        )
    }

    private fun List<CbdRow>.sortByMatchability(
        matchers: List<Matcher>,
        otherList: List<CbdRow>,
    ): List<CbdRow> {
        return this.sortedWith { o1, o2 ->
            if (o1.entityType == o2.entityType && !isStatic(o1) && !isStatic(o2)) {
                val matcher = matchers.find { it.predicate.invoke(o1) && it.predicate.invoke(o2) }
                if (matcher != null) {
                    val o1HasMatch = matcher.finderFactory.getFinderFor(o1).find(otherList) != null
                    val o2HasMatch = matcher.finderFactory.getFinderFor(o2).find(otherList) != null

                    when {
                        o1HasMatch && !o2HasMatch -> -1
                        !o1HasMatch && o2HasMatch -> 1
                        else -> 0
                    }
                } else {
                    0
                }
            } else {
                0
            }
        }
    }

    // Predicate used for strict matching
    private fun isStatic(row: CbdRow): Boolean {
        return row.entityType == Entities.MANUFACTURING.name
    }

    private fun findByName(
        row: CbdRow,
        otherList: List<CbdRow>,
    ): CbdRow? {
        return otherList.find { it.source == row.source }
    }

    private fun findByNameStrict(
        row: CbdRow,
        otherList: List<CbdRow>,
    ): CbdRow {
        return findByName(row = row, otherList = otherList)
            ?: error("Strict match not found for row=${row.source} in otherList=${otherList.map { it.source }}")
    }

    private fun isSameTechnology(
        leftRoot: ManufacturingEntity?,
        rightRoot: ManufacturingEntity?,
    ): Boolean {
        val leftTech = leftRoot?.getFieldResult("technologyModel")?.res
        return leftTech != null && leftTech == rightRoot?.getFieldResult("technologyModel")?.res
    }

    private fun getCbdWithPartAndBranch(
        accessCheck: AccessCheck,
        bomNodeId: BomNodeId,
        branchId: BranchId?,
    ): Mono<Pair<BomNodeSnapshot, Cbd>> =
        bomNodeService.getNodeWithBranch(accessCheck, bomNodeId, branchId).map { snapshot ->
            val manu = snapshot.manufacturing!!
            cbdService
                .createCbdFromManufacturing(manu, ExchangeRateMap.empty(), normalizeCostUnit = true)
                .let { snapshot to it }
        }

    private data class Matcher(
        val predicate: (CbdRow) -> Boolean,
        val finderFactory: FinderFactory,
    )

    private interface FinderFactory {
        fun getFinderFor(row: CbdRow): Finder

        companion object {
            fun from(strategy: (CbdRow, List<CbdRow>) -> CbdRow?): FinderFactory {
                return object : FinderFactory {
                    override fun getFinderFor(row: CbdRow): Finder {
                        return object : Finder {
                            override fun find(list: List<CbdRow>): CbdRow? {
                                return strategy.invoke(row, list)
                            }
                        }
                    }
                }
            }

            fun from(strategy: (List<CbdRow>) -> CbdRow?): FinderFactory {
                return object : FinderFactory {
                    override fun getFinderFor(row: CbdRow): Finder {
                        return object : Finder {
                            override fun find(list: List<CbdRow>): CbdRow? {
                                return strategy.invoke(list)
                            }
                        }
                    }
                }
            }
        }
    }

    private interface Finder {
        fun find(list: List<CbdRow>): CbdRow?
    }

    private fun createNewRefKey(accountId: AccountId): Mono<RefKeyForAccount> =
        refKeyService.getNewRefKeyForAccount(accountId.toHexString(), AnalyticsDeltaComparison)
}
