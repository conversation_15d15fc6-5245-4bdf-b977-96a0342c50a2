package com.nu.bom.core.startup

import com.nu.bom.core.manufacturing.annotations.Extends
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ManufacturingEntityExtension
import com.nu.bom.core.manufacturing.extension.CLEANING_EXTENSION_PACKAGE
import com.nu.bom.core.manufacturing.extension.CO2_EXTENSION_PACKAGE
import com.nu.bom.core.manufacturing.extension.COMMERCIAL_CALCULATION_CO2_FIELD_PACKAGE
import com.nu.bom.core.manufacturing.extension.COMMERCIAL_CALCULATION_COST_FIELD_PACKAGE
import com.nu.bom.core.manufacturing.extension.CURRENT_TARGET_EXTENSION_PACKAGE
import com.nu.bom.core.manufacturing.extension.DIMENSION_EXTENSION_PACKAGE
import com.nu.bom.core.manufacturing.extension.MASTER_DATA_MATERIAL_CONSUMER
import com.nu.bom.core.manufacturing.extension.NESTING_EXTENSION_PACKAGE
import com.nu.bom.core.manufacturing.extension.PAID_INVEST_COMMERCIAL_CALCULATION_FIELD_EXTENSION
import com.nu.bom.core.manufacturing.extension.TOOL_CALCULATION_PACKAGE
import com.nu.bom.core.manufacturing.extension.TSET_DEL_EXTENSION_PACKAGE
import com.nu.bom.core.manufacturing.extension.VOLUME_AND_SCRAP_CALCULATION_FIELD_PACKAGE
import com.nu.bom.core.manufacturing.extension.WSI_EXTENSION_PACKAGE
import com.nu.bom.core.manufacturing.factories.createEntity
import com.nu.bom.core.utils.EntityMetaInfo
import com.nu.bom.core.utils.EntityMetaInfoExtractionService
import com.nu.bom.core.utils.ExtensionManager
import com.nu.bom.core.utils.changelog.EntityChangelog
import com.nu.bom.core.utils.changelog.EntityChangelogReader
import com.tset.common.util.timedInfo
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider
import org.springframework.context.annotation.Configuration
import org.springframework.context.event.EventListener
import org.springframework.core.annotation.Order
import org.springframework.core.io.Resource
import org.springframework.core.type.filter.AnnotationTypeFilter
import org.springframework.core.type.filter.AssignableTypeFilter

@Configuration
class ExtensionLoader(
    private val extensionManager: ExtensionManager,
    private val entityMetaInfoExtractionService: EntityMetaInfoExtractionService,
    @Value("classpath:$CHANGELOG_FILE_NAME") private val changelogResource: Resource,
) {
    val logger = LoggerFactory.getLogger(ExtensionLoader::class.java)!!

    @EventListener(ApplicationReadyEvent::class)
    @Order(1)
    fun onStartup() {
        extensionManager.enablePackage(CO2_EXTENSION_PACKAGE)
        extensionManager.enablePackage(DIMENSION_EXTENSION_PACKAGE)
        extensionManager.enablePackage(CURRENT_TARGET_EXTENSION_PACKAGE)
        extensionManager.enablePackage(WSI_EXTENSION_PACKAGE)
        extensionManager.enablePackage(CLEANING_EXTENSION_PACKAGE)
        extensionManager.enablePackage(COMMERCIAL_CALCULATION_COST_FIELD_PACKAGE)
        extensionManager.enablePackage(PAID_INVEST_COMMERCIAL_CALCULATION_FIELD_EXTENSION)
        extensionManager.enablePackage(COMMERCIAL_CALCULATION_CO2_FIELD_PACKAGE)
        extensionManager.enablePackage(VOLUME_AND_SCRAP_CALCULATION_FIELD_PACKAGE)
        extensionManager.enablePackage(TSET_DEL_EXTENSION_PACKAGE)
        extensionManager.enablePackage(NESTING_EXTENSION_PACKAGE)
        extensionManager.enablePackage(TOOL_CALCULATION_PACKAGE)
        extensionManager.enablePackage(MASTER_DATA_MATERIAL_CONSUMER)

        findAnnotatedClasses("com.nu.bom.core")
    }

    fun findAnnotatedClasses(scanPackage: String) {
        logger.timedInfo("Extension loader") {
            val provider = createComponentScanner()
            val changelog = EntityChangelogReader(changelogResource.inputStream).getChangelog()
            val beanDefinitions = provider.findCandidateComponents(scanPackage)
            for (beanDef in beanDefinitions) {
                // TODO Overwrite name with annotation
                val clazz = Class.forName(beanDef.beanClassName)

                if (ManufacturingEntityExtension::class.java.isAssignableFrom(clazz)) {
                    val extendsAnnotation = clazz.getAnnotationsByType(Extends::class.java)
                    val extensionPoints = extendsAnnotation.flatMap { it.extensionPoint.toList() }.map { it.java }
                    val packageName = extendsAnnotation[0].packageName
                    loadExtension(clazz, packageName, extensionPoints, changelog)
                }
            }
            if (extensionManager.extensionsAreCircular()) {
                error("Extensions are circular! This would result in infinite loops!")
            }
            val typeCount = beanDefinitions.count()
            val packageCount = beanDefinitions.map { Class.forName(it.beanClassName).packageName }.distinct().count()
            logger.info("extensions loaded $typeCount classes from $packageCount packages")
        }
    }

    fun loadExtension(
        clazz: Class<*>,
        packageName: String,
        extensionPoints: List<Class<out ManufacturingEntity>>,
        changelog: EntityChangelog,
    ) {
        val extensionClass = clazz as Class<out ManufacturingEntityExtension>

        val extensionMetaInfo = extractMetaInfo(extensionClass)

        val changelogEntries = changelog.getClassLog(clazz) ?: emptyList()
        extensionMetaInfo.changelogEntries = changelogEntries

        val extension =
            ExtensionManager.Extension(
                extensionClass = extensionClass,
                extensionPackage = packageName,
                extensionMetaInfo = extensionMetaInfo,
            )

        logger.debug("adding extension: ${clazz.simpleName} to $extensionPoints")
        extensionManager.addExtension(extensionPoints = extensionPoints, extension = extension)
    }

    private fun createComponentScanner(): ClassPathScanningCandidateComponentProvider {
        // Don't pull default filters (@WizardField, etc.):
        val provider = ClassPathScanningCandidateComponentProvider(false)
        provider.addIncludeFilter { metadataREader, other ->
            AnnotationTypeFilter(Extends::class.java).match(metadataREader, other) &&
                AssignableTypeFilter(ManufacturingEntityExtension::class.java).match(metadataREader, other)
        }
        return provider
    }

    private fun extractMetaInfo(extensionClass: Class<out ManufacturingEntityExtension>): EntityMetaInfo {
        logger.trace("loading extensionClass: {}", extensionClass.canonicalName)
        val extensionEntity = createEntity("extensionManagerObject", extensionClass)!!

        return entityMetaInfoExtractionService.extractEntityMetaInfo(
            entity = extensionClass,
            extends = extensionEntity.extends?.getEntityClass(),
        )
    }
}
