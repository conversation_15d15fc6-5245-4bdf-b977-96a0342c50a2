package com.nu.bom.core.smf

import com.nu.bom.core.service.file.DownloadResponse
import com.nu.bom.core.smf.model.SmfUnfoldedBoundingBox3Content
import com.nu.bom.core.smf.model.SmfUnfoldedPart
import com.nu.bom.core.smf.model.SmfUnfoldedPartComponentLine
import com.nu.bom.core.smf.model.SmfUnfoldedPartLineContent
import com.nu.bom.core.smf.model.SmfUnfoldedPartPoint
import com.nu.bom.core.smf.model.SmfUnfoldedPartPoint3D
import com.nu.bom.core.smf.model.SmfUnfoldedPartPolygon
import com.nu.bom.core.smf.model.WsiStepToGltfResponse
import com.nu.bom.core.smf.model.WsiUnfolderResponse
import com.nu.bom.core.user.AccessCheck
import org.bson.types.ObjectId
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono

@Service
@Profile("test")
class WsiApiMock : WsiApiService {
    override fun getUnfoldResponse(id: ObjectId): Mono<WsiUnfolderResponse> {
        TODO()
    }

    override fun getNestorResponse(
        id: ObjectId,
        accessCheck: AccessCheck,
    ): Mono<NestingData> {
        TODO()
    }

    override fun unfolder(
        wizardId: String,
        stepContent: String,
        enforceSheetPart: Boolean,
    ): Mono<WsiUnfolderResponse> = Mono.just(getMockUnfolderResponse(enforceSheetPart))

    override fun stepToGltf(
        wizardId: String,
        file: DownloadResponse,
    ): Mono<WsiStepToGltfResponse> {
        TODO()
    }

    override fun getSequenceForEntity(
        unfoldedPartId: ObjectId,
        operationId: String,
    ): Mono<FeUnfolded> {
        TODO()
    }

    companion object {
        private fun createSmfLine(
            from: Pair<Double, Double>,
            to: Pair<Double, Double>,
        ) = SmfUnfoldedPartComponentLine(
            content =
                SmfUnfoldedPartLineContent(
                    from = SmfUnfoldedPartPoint(from.first, from.second),
                    to = SmfUnfoldedPartPoint(to.first, to.second),
                ),
        )

        private val mockPartBoundingBox3 =
            SmfUnfoldedBoundingBox3Content(
                lower = SmfUnfoldedPartPoint3D(0.0, 0.0, 0.0),
                upper = SmfUnfoldedPartPoint3D(6.0, 3.0, 2.0),
            )

        private val rectanglePoints =
            listOf(
                Pair(0.0, 0.0),
                Pair(mockPartBoundingBox3.getLength(), 0.0),
                Pair(mockPartBoundingBox3.getLength(), mockPartBoundingBox3.getWidth()),
                Pair(0.0, mockPartBoundingBox3.getWidth()),
            )

        private val mockOuterPoly: SmfUnfoldedPartPolygon =
            listOf(
                createSmfLine(
                    from = rectanglePoints[0],
                    to = rectanglePoints[1],
                ),
                createSmfLine(
                    from = rectanglePoints[1],
                    to = rectanglePoints[2],
                ),
                createSmfLine(
                    from = rectanglePoints[2],
                    to = rectanglePoints[3],
                ),
                createSmfLine(
                    from = rectanglePoints[3],
                    to = rectanglePoints[0],
                ),
            )

        private val mockPart =
            SmfUnfoldedPart(
                outerPolygon = mockOuterPoly,
                innerPolygons = emptyList(),
            )

        fun getMockUnfolderResponse(sheetPartEnforced: Boolean) =
            WsiUnfolderResponse(
                tag = "mock",
                sheetPartEnforced = sheetPartEnforced,
                innerOuterPolygons = listOf(mockPart),
                bendLines = emptyList(),
                twoDimSvg = "",
                boundingBox2 = null,
                boundingBox3 = mockPartBoundingBox3,
            )
    }
}
