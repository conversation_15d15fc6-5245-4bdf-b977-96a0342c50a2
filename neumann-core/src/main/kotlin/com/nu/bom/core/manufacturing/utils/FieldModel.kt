package com.nu.bom.core.manufacturing.utils

import com.fasterxml.jackson.annotation.JsonIgnore
import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.manufacturing.annotations.ChildLoadingTrigger
import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.DisableEntityCreationInModularizedEntity
import com.nu.bom.core.manufacturing.annotations.EnginePrivateFieldMetaInfo
import com.nu.bom.core.manufacturing.annotations.EngineTransient
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityCreationChildrenScope
import com.nu.bom.core.manufacturing.annotations.EntityLinkField
import com.nu.bom.core.manufacturing.annotations.EntityLinkProvider
import com.nu.bom.core.manufacturing.annotations.ExternalDataProvider
import com.nu.bom.core.manufacturing.annotations.ExternalDependency
import com.nu.bom.core.manufacturing.annotations.ForceRecalculate
import com.nu.bom.core.manufacturing.annotations.FreezeImplementation
import com.nu.bom.core.manufacturing.annotations.Group
import com.nu.bom.core.manufacturing.annotations.InterfaceField
import com.nu.bom.core.manufacturing.annotations.OrderedEntityCreation
import com.nu.bom.core.manufacturing.annotations.SourceDataInput
import com.nu.bom.core.manufacturing.annotations.SourceDataKey
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.annotations.ThreeDb
import com.nu.bom.core.manufacturing.annotations.ThreeDbResourceTracker
import com.nu.bom.core.manufacturing.annotations.ThreeDbVersionedPart
import com.nu.bom.core.manufacturing.annotations.defaultUnitFromMetaInfo
import com.nu.bom.core.manufacturing.defaults.DefaultProvider
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.UnitOverrideContext
import com.nu.bom.core.manufacturing.fieldTypes.DynamicQuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Result
import com.nu.bom.core.manufacturing.service.CurrencyConverterField
import com.nu.bom.core.manufacturing.service.Field
import com.nu.bom.core.manufacturing.service.FieldKey
import com.nu.bom.core.manufacturing.service.FieldType
import com.nu.bom.core.manufacturing.service.InjectableEngineService
import com.nu.bom.core.manufacturing.service.UnitConverterField
import com.nu.bom.core.manufacturing.service.VersionedResult
import com.nu.bom.core.manufacturing.service.virtualfield.sourceproviders.DataSourceProviders
import com.nu.bom.core.threedb.ThreeDbFieldType
import com.nu.bom.core.utils.DenominatorUnitInfo
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.applyIfExists
import com.nu.bom.core.utils.simpleName
import com.tset.core.quantities.types.Valuable
import org.bson.types.ObjectId
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import kotlin.reflect.KCallable
import kotlin.reflect.KClass
import kotlin.reflect.KParameter
import kotlin.reflect.KType
import kotlin.reflect.full.createInstance
import kotlin.reflect.full.createType
import kotlin.reflect.full.findAnnotation
import kotlin.reflect.full.hasAnnotation
import kotlin.reflect.full.isSubtypeOf
import kotlin.reflect.full.isSuperclassOf
import kotlin.reflect.full.withNullability
import kotlin.reflect.jvm.jvmErasure

private val logger: Logger by lazy { LoggerFactory.getLogger(FieldModel::class.java) }

data class FieldModel(
    val name: String,
    val fieldType: FieldType,
    val returnTypeCouldBeNonFieldResult: String,
    val function: KCallable<*>,
    @JsonIgnore
    val parameters: List<AnnotatedDependency>,
    @JsonIgnore
    val childrenOrder: List<String>,
    @JsonIgnore
    val modelInput: Boolean,
    val keepOld: Boolean = false,
    val isNocalcField: Boolean,
    // These shouldn't really be fields, would be nice to have FieldModel disentangled from reflection
    @JsonIgnore
    private val childLoadingTriggerOverride: Boolean? = null,
    @JsonIgnore
    private val dataSourceProviderOverride: DataSourceProviders? = null,
    @JsonIgnore
    private val availableOutsideTheEngineOverride: Boolean? = null,
    @JsonIgnore
    private val returnTypeOverride: KType? = null,
    @JsonIgnore
    private val dataSourceInputOverride: Boolean? = null,
) {
    @JsonIgnore
    val specialLink: SpecialLink? = function.findAnnotation()

    @JsonIgnore
    val isInterfaceField: Boolean = function.findAnnotation<InterfaceField>() != null

    private val availableOutsideTheEngineFromAnnotation =
        when (function.findAnnotation<EnginePrivateFieldMetaInfo>()?.availableOutsideTheEngine) {
            null, true -> true
            false -> false
        }

    @JsonIgnore
    val availableOutsideTheEngine: Boolean =
        availableOutsideTheEngineOverride ?: availableOutsideTheEngineFromAnnotation

    @JsonIgnore
    val isEngineTransient: Boolean = function.hasAnnotation<EngineTransient>()

    @JsonIgnore
    val isGroup: Boolean = function.findAnnotation<Group>() != null

    val childLoadingTrigger = childLoadingTriggerOverride ?: (function.findAnnotation<ChildLoadingTrigger>() != null)
    val sourceDataKey = dataSourceProviderOverride ?: function.findAnnotation<SourceDataKey>()?.value
    val sourceDataInput = dataSourceInputOverride ?: function.findAnnotation<SourceDataInput>()?.value
    val isExternalDataProvider = function.findAnnotation<ExternalDataProvider>() != null
    val freezeImplementation = function.findAnnotation<FreezeImplementation>() != null
    val alwaysForceRecalculate = function.findAnnotation<ForceRecalculate>() != null
    val externalDependency = function.findAnnotation<ExternalDependency>()
    val isExternalDependency: Boolean get() = externalDependency != null
    val disableEntityCreationInModularized = function.findAnnotation<DisableEntityCreationInModularizedEntity>() != null
    val entityLinkField = function.findAnnotation<EntityLinkField>()
    val entityLinkProvider = function.findAnnotation<EntityLinkProvider>()

    val threeDbFieldType =
        function.findAnnotation<ThreeDb>()?.let { ThreeDbFieldType.ThreeDbField(it.resourceType, it.isNbkCalculated) }
            ?: function.findAnnotation<ThreeDbVersionedPart>()?.let { ThreeDbFieldType.VersionedPart }
            ?: function.findAnnotation<ThreeDbResourceTracker>()?.let { ThreeDbFieldType.ResourceTracker }

    private val actualReturnType = returnTypeOverride ?: buildActualReturnType()
    private val childCreationTypes: List<String>

    val onlyDirectChild: Boolean
    val recreateWithoutMerge: Boolean
    val entityCreationChildrenScope: EntityCreationChildrenScope

    val annotatedDependency: AnnotatedDependency? =
        DependencyCreationUtils.getDependencyAnnotation(function).applyIfExists {
            AnnotatedDependency(
                callable = function,
                dependencyAnnotation = it,
                name = name,
                index = 0,
            )
        }

    var entityModel: EntityModel? = null

    init {
        val entityCreation = function.findAnnotation<EntityCreation>()
        if (entityCreation != null) {
            childCreationTypes = entityCreation.childCreations.toList().map { it.toString() }
            onlyDirectChild = entityCreation.onlyDirectChild
            recreateWithoutMerge = entityCreation.recreateWithoutMerge
            entityCreationChildrenScope = entityCreation.entityCreationChildrenScope
        } else {
            val orderedEntityCreation = function.findAnnotation<OrderedEntityCreation>()
            recreateWithoutMerge = false
            entityCreationChildrenScope = EntityCreationChildrenScope.ONLY_FROM_ENTITY_CREATION
            if (orderedEntityCreation != null) {
                childCreationTypes = orderedEntityCreation.create.map { it.toString() }
                onlyDirectChild = false
            } else {
                childCreationTypes = listOf()
                onlyDirectChild = true
            }
        }
    }

    val isMonetaryType =
        this.returnTypeCouldBeNonFieldResult == Money::class.qualifiedName

    val isDynamicQuantityUnit =
        this.returnTypeCouldBeNonFieldResult == DynamicQuantityUnit::class.qualifiedName

    fun inputAsFallback() = fieldType is FieldType.Calculation && fieldType.isInputAsFallback()

    fun input() = fieldType is FieldType.Calculation && fieldType.isInput()

    fun functionName() = function.toString()

    private fun getRealFunctionParameters(): List<KParameter> = function.parameters.drop(1)

    data class FunctionParameterMetaInfo(
        val index: Int,
        val type: KType,
        val isNullable: Boolean,
        val isInjectableEngineService: Boolean,
        val collectionType: Any?,
        val isValuable: Boolean,
        val defaultProvider: DefaultProvider<FieldResultStar>?,
    ) {
        fun getDefaultValue(): FieldResultStar? = defaultProvider?.provide()

        fun hasDefault() = defaultProvider != null

        /**
         * this parameter is mandatory, if it's not marked as nullable, and doesn't have a default provider.
         */
        fun isMandatoryParameter() = !isInjectableEngineService && !isNullable && defaultProvider == null
    }

    private fun KType.isValuable() = this.withNullability(true).isSubtypeOf(nullableValuableType)

    private fun isCollectionType(param: KParameter): Any? {
        val classifier = param.type.classifier as? KClass<*>
        return when {
            classifier == null -> null
            classifier.isSuperclassOf(List::class) -> emptyList<Any>()
            classifier.isSuperclassOf(Set::class) -> emptySet<Any>()
            classifier.isSuperclassOf(Map::class) -> emptyMap<Any, Any>()
            classifier.isSuperclassOf(NamedFields::class) -> NamedFields<Any>(emptyList())
            else -> null
        }
    }

    val functionParameterMetaInfo: List<FunctionParameterMetaInfo> =
        getRealFunctionParameters().map { param ->
            val defaultProvider = param.findAnnotation<Default>()?.provider?.createInstance()
            val isInjectableEngineService = param.type.isSubtypeOf(InjectableEngineService::class.createType())
            when (param.type.arguments.size) {
                0 ->
                    FunctionParameterMetaInfo(
                        param.index,
                        param.type,
                        param.type.isMarkedNullable,
                        isValuable = param.type.isValuable(),
                        collectionType = null,
                        isInjectableEngineService = isInjectableEngineService,
                        defaultProvider = defaultProvider,
                    )

                1 -> {
                    val resultingType =
                        param.type.arguments
                            .first()
                            .type!!
                    FunctionParameterMetaInfo(
                        param.index,
                        resultingType,
                        param.type.isMarkedNullable,
                        collectionType = isCollectionType(param),
                        isValuable = resultingType.isValuable(),
                        isInjectableEngineService = isInjectableEngineService,
                        defaultProvider = defaultProvider,
                    )
                } // todo: add further tests, if really list type
                2 -> getMapParamMetaInfo(param, isInjectableEngineService, defaultProvider)
                else -> throw Exception("Engine does not support function parameters with more than 2 generic types")
            }
        }

    private fun getMapParamMetaInfo(
        param: KParameter,
        isInjectableEngineService: Boolean,
        defaultProvider: DefaultProvider<FieldResultStar>?,
    ): FunctionParameterMetaInfo {
        // For now the only type with two generic type parameters we handle is Map,
        // where the second argument is the value.
        // The key is fixed as the id of the object the files comes from
        require(
            param.type.arguments[0]
                .type
                ?.classifier == ObjectId::class,
        ) {
            "Map parameter must have key of ObjectId"
        }
        val resultingType = param.type.arguments[1].type!!
        return FunctionParameterMetaInfo(
            param.index,
            resultingType,
            param.type.isMarkedNullable,
            collectionType = isCollectionType(param),
            isValuable = resultingType.isValuable(),
            isInjectableEngineService = isInjectableEngineService,
            defaultProvider = defaultProvider,
        )
    }

    fun fieldCanCreateType(entityType: String) = (entityType == returnTypeCouldBeNonFieldResult || childCreationTypes.contains(entityType))

    private fun buildActualReturnType() =
        when (fieldType) {
            // Only get return types if fields are actually yielding FieldResult/Values - so far only for CALCULATION fields
            is FieldType.Calculation ->
                when {
                    isResultType(function.returnType) -> {
                        function.returnType
                    }

                    function.returnType.arguments.isNotEmpty() -> {
                        val typeProjection = function.returnType.arguments[0]
                        typeProjection.type
                    }

                    else -> {
                        function.returnType
                    }
                }

            FieldType.ChildLoadTrigger,
            FieldType.Creation,
            FieldType.ManualEntityCreator,
            FieldType.OrderedEntityCreation,
            FieldType.DataSourcerUpdater,
            FieldType.CurrencyConverter,
            FieldType.UnitConverter,
            FieldType.EntityProvider,
            FieldType.BehaviourCreation,
            FieldType.DynamicField,
            FieldType.EntityDeletion,
            FieldType.EntityLinkProvider,
            -> null
        }

    fun getActualReturnType() = actualReturnType

    private fun isResultType(type: KType): Boolean = EntityModelHelper.isPotentiallyNullableSubtypeOf<Result>(type)

    fun call(args: List<Any?>) =
        try {
            function.call(*args.toTypedArray())
        } catch (e: IllegalArgumentException) {
            // is thrown when any argument type mismatch
            val newArgs = convertArgumentsIfPossible(e, args)
            if (newArgs == args) {
                // when no conversion done just don't try it again and throw the initial exception
                throw e
            }
            function.call(*newArgs.toTypedArray())
        }

    private fun convertArgumentsIfPossible(
        e: Throwable,
        args: List<Any?>,
    ) = function.parameters.zip(args).map { (param, arg) ->
        arg?.let {
            val argType = arg::class.createType()
            if (!argType.isSubtypeOf(param.type)) {
                // try to find constructor building that class with usage of another value
                val constructor =
                    param.type.jvmErasure.constructors.find {
                        it.parameters.size == 1 && argType.isSubtypeOf(it.parameters.first().type)
                    }
                constructor?.let {
                    logger.debug("trying to convert {}({}) to {} by {}", argType, arg, param.type, constructor)
                    constructor.call(arg)
                } ?: throw e
            } else {
                arg
            }
        }
    }

    private fun getReturnTypeStringForDtos(): String {
        if (isNocalcField) {
            return "NoCalcFieldResult"
        }

        val naiveReturnTypeName = returnTypeCouldBeNonFieldResult.simpleName()

        if (naiveReturnTypeName == "Mass") {
            return "Weight"
        }

        if (naiveReturnTypeName == "Dimensionless") {
            return "Num"
        }

        return naiveReturnTypeName
    }

    /**
     * Defines if the field must be calculated in dependency chain before successors
     * even when it has a manual overwrite as calculation of the field itself is executing necessary side effects
     *
     */
    fun mustBeCalculated(): Boolean = fieldType.mustBeCalculated()

    fun toFieldParameter(
        metadata: Map<String, Any>,
        denominatorUnitInfo: DenominatorUnitInfo?,
        unitOverrideContext: UnitOverrideContext,
        entityManager: EntityManager,
    ): FieldParameter {
        val staticType = getReturnTypeStringForDtos()
        val (type, unit) = unitOverrideContext.getFieldTypeAndUnit(staticType, entityManager)

        val defaultUnit =
            if (staticType == "QuantityUnit") {
                if (defaultUnitFromMetaInfo(metadata) == null) {
                    throw Exception(
                        "QuantityUnit fields should have gotten a default unit in the metaInfo" +
                            " via interpolateFields(...) before they are passed to here.",
                    )
                }
                unitOverrideContext.getDynamicUnit(DynamicUnitOverride.QUANTITY_UNIT).toString()
            } else {
                defaultUnitFromMetaInfo(metadata)
            }

        return FieldParameter(
            name = name,
            type = type,
            // TODO! Weird. Why now defaultUnit and not baseUnit?
            unit = defaultUnit ?: unit,
            metaInfo = metadata,
            denominatorUnit = denominatorUnitInfo?.getDenominatorUnitAndFactor(unitOverrideContext)?.first,
        )
    }

    fun create(
        calcKey: FieldKey,
        entity: ManufacturingEntity,
        executionContext: ManufacturingEntity,
        oldInput: VersionedResult?,
        oldResult: VersionedResult?,
        initialResult: VersionedResult?,
    ): Field =
        when (fieldType) {
            FieldType.CurrencyConverter ->
                CurrencyConverterField(
                    this,
                    calcKey,
                    executionContext,
                    entity,
                    oldInput,
                    oldResult,
                    initialResult,
                )
            FieldType.UnitConverter ->
                UnitConverterField(
                    this,
                    calcKey,
                    executionContext,
                    entity,
                    oldInput,
                    oldResult,
                    initialResult,
                )

            else -> Field(this, calcKey, executionContext, entity, oldInput, oldResult, initialResult)
        }

    companion object {
        private val nullableValuableType = Valuable::class.createType(nullable = true)
    }
}
