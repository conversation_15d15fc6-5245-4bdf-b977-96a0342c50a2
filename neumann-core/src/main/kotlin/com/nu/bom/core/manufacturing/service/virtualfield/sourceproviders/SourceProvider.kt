package com.nu.bom.core.manufacturing.service.virtualfield.sourceproviders

import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.service.Field
import com.nu.bom.core.manufacturing.service.VersionedResult
import com.nu.bom.core.user.AccessCheck
import org.bson.types.ObjectId
import reactor.core.publisher.Flux

enum class DataSourceProviders {
    MASTER_DATA_SOURCE,
    NEW_MASTER_DATA_SOURCE,
    NEW_MASTER_DATA_HEADER_FIELDS,
    NEW_MASTER_DATA_HEADER_FIELDS_VALUES,
}

data class VersionInfo(val id: ObjectId?, val version: Int?)

data class SourceData(
    val fieldInfo: Map<String, FieldResultStar>,
    val versionInfo: VersionInfo?,
)

interface SourceProvider {
    fun isSourceProviderFor(source: DataSourceProviders?): Boolean

    /**
     * Fetch source data for a list of source keys
     *
     * @return A flux of source data that matches the input keys in size and order.
     */
    fun findSourceEntityData(
        accessCheck: AccessCheck,
        sourceKeys: List<Any>,
    ): Flux<IndexedValue<SourceData>>

    fun updateSelector(
        calculation: Field,
        newSourceDataKey: VersionedResult,
        versionInfo: VersionInfo?,
    )
}
