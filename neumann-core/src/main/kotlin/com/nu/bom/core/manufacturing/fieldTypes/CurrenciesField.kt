package com.nu.bom.core.manufacturing.fieldTypes

import com.tset.core.service.currency.Currencies
import com.tset.core.service.currency.CurrencyDescription
import com.tset.core.service.domain.Currency

class CurrenciesField(res: Jsondata<Currencies>) :
    <PERSON>son<Currencies>(res) {
    constructor(value: Currencies) : this(Jsondata(value))

    constructor(value: String) : this(
        mapper.readValue<Jsondata<Currencies>>(
            value,
            mapper.typeFactory
                .constructParametricType(
                    Jsondata::class.java,
                    Currencies::class.java
                )
        )
    )

    companion object {
        fun empty() = CurrenciesField(
            Currencies(
                mapOf(Currency("EUR") to CurrencyDescription("Euro", "#,##0.00\" €\""))
            )
        )
    }
}
