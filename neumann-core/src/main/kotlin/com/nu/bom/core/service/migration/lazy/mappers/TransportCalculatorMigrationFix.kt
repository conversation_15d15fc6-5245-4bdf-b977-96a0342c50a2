package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.fieldTypes.DynamicBehaviourGeneration
import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.TransportCalculatorType
import com.nu.bom.core.manufacturing.service.behaviour.EntityBasedDynamicBehaviour
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import com.nu.bom.core.service.migration.lazy.mappers.utils.EmissionMapping
import org.springframework.stereotype.Service
import java.math.BigDecimal

@Service
class TransportCalculatorMigrationFix : ManufacturingModelEntityMapper {
    override val changeSetId = MigrationChangeSetId("2023-09-27-transport-calculator-migration-fix-2")

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity {
        if (entity.name != "TransportCosts") {
            return entity
        }

        return entity.copyAll(
            clazz = "TransportCosts",
            fieldWithResults = addBehaviourResult(entity.fieldWithResults.toMutableMap(), entity.version),
        )
    }

    private fun addBehaviourResult(
        fields: MutableMap<String, FieldResultModel>,
        version: Int,
    ): Map<String, FieldResultModel> {
        if (!fields.containsKey("createBehaviour")) {
            fields["createBehaviour"] =
                FieldResultModel(
                    version,
                    version,
                    DynamicBehaviourGeneration::class.simpleName!!,
                    DynamicBehaviourGeneration(
                        DynamicBehaviourGeneration.DynamicBehaviourTypeMapping(
                            "TransportCosts",
                            "TransportCalculatorOverheadCalculation",
                            EntityBasedDynamicBehaviour::class.qualifiedName!!,
                            null,
                        ),
                    ).dbValue(),
                    FieldResult.SOURCE.C.name,
                )
        }

        val activeTransportCalculatorTypeResult = fields["activeTransportCalculator"]
        if (activeTransportCalculatorTypeResult?.value == null ||
            activeTransportCalculatorTypeResult.value::class != TransportCalculatorType.Selection::class
        ) {
            val calcType =
                when (activeTransportCalculatorTypeResult?.value) {
                    TransportCalculatorType.DETAILED, "DETAILED" -> TransportCalculatorType.Selection.DETAILED
                    else -> TransportCalculatorType.Selection.DIRECT
                }
            fields["activeTransportCalculator"] =
                FieldResultModel(
                    version,
                    version,
                    TransportCalculatorType::class.simpleName!!,
                    calcType,
                    activeTransportCalculatorTypeResult?.source ?: FieldResult.SOURCE.C.name,
                )
        }

        addCostAndCO2(fields, version)

        return fields
    }

    private fun addCostAndCO2(
        fields: MutableMap<String, FieldResultModel>,
        version: Int,
    ) {
        val existingCostBase = fields["overheadBase"]
        if (existingCostBase != null && existingCostBase.source == FieldResult.SOURCE.I.name) {
            fields["overheadBase"] =
                existingCostBase.copyAll(source = FieldResult.SOURCE.C.name)
        }
        val existingCosts = fields["costPerPart"]
        if (existingCosts != null && existingCosts.source == FieldResult.SOURCE.I.name) {
            fields["costPerPart"] = existingCosts.copyAll(source = FieldResult.SOURCE.C.name)
        }
        if (!fields.containsKey("costPerPartFromBehaviour")) {
            fields["costPerPartFromBehaviour"] =
                FieldResultModel(
                    version = version,
                    newVersion = version,
                    type = Money::class.simpleName!!,
                    value = existingCosts?.value ?: BigDecimal.ZERO,
                    source = existingCosts?.source ?: FieldResult.SOURCE.C.name,
                    unit = existingCosts?.unit,
                )
        }

        val existingCo2 = fields["cO2PerPart"]
        if (existingCo2 != null && existingCo2.source == FieldResult.SOURCE.I.name) {
            fields["cO2PerPart"] = existingCo2.copyAll(source = FieldResult.SOURCE.C.name)
        }
        if (!fields.containsKey("cO2PerPartFromBehaviour")) {
            fields["cO2PerPartFromBehaviour"] =
                FieldResultModel(
                    version = version,
                    newVersion = version,
                    type = Emission::class.simpleName!!,
                    value = existingCo2?.value ?: BigDecimal.ZERO,
                    source = existingCo2?.source ?: FieldResult.SOURCE.C.name,
                    unit = EmissionMapping.convertWeightUnitsToEmissionUnits(existingCo2?.unit),
                )
        }
    }
}
