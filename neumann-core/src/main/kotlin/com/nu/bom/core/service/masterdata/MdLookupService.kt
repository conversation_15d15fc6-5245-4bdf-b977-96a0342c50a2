package com.nu.bom.core.service.masterdata

import com.nu.bom.core.user.AccessCheck
import com.nu.http.EnvironmentNameSupplier
import com.nu.masterdata.dto.v1.detail.GetLatestVersionResponseDto
import com.nu.masterdata.dto.v1.lookup.request.MasterdataLookupRequest
import com.nu.masterdata.dto.v1.lookup.request.MasterdataLookupResponse
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import java.time.Duration

@Service
class MdLookupService(
    private val restService: MasterdataRestService,
    mdClientProperties: MasterdataClientProperties,
    environmentNameSupplier: EnvironmentNameSupplier,
) : MdBaseCachedService(environmentNameSupplier) {
    private val lookupCache = createCache(::internalLookup, Duration.ofSeconds(1))
    private val latestMdVersionCache = createCache(::internalLatestMdVersion, mdClientProperties.latestVersionLookupCacheDuration)

    fun lookup(
        accessCheck: AccessCheck,
        request: MasterdataLookupRequest,
    ): Mono<MasterdataLookupResponse> = lookupCache(accessCheck, request)

    fun latestMdVersion(
        accessCheck: AccessCheck,
        headerKeys: HeaderKeys,
    ): Mono<GetLatestVersionResponseDto> = latestMdVersionCache(accessCheck, headerKeys)

    private fun internalLatestMdVersion(
        accessCheck: AccessCheck,
        headerKeys: HeaderKeys,
    ): Mono<GetLatestVersionResponseDto> {
        return restService.postToMono(
            uri = { it.path("/api/md/v1/details/latest-version").build() },
            requestBody = headerKeys,
            responseType = GetLatestVersionResponseDto::class,
            accessCheck = accessCheck,
        )
    }

    private fun internalLookup(
        accessCheck: AccessCheck,
        request: MasterdataLookupRequest,
    ): Mono<MasterdataLookupResponse> {
        return restService.postToMono(
            uri = {
                it.path("/api/md/v1/headertypes/{headerTypeKey}/details/lookup")
                    .build(request.headerTypeKey.key)
            },
            requestBody = request,
            responseType = MasterdataLookupResponse::class,
            accessCheck = accessCheck,
        )
    }
}
