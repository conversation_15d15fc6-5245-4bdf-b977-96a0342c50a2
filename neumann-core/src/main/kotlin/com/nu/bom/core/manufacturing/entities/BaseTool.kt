package com.nu.bom.core.manufacturing.entities

import com.nu.bom.core.manufacturing.annotations.AdditionalFieldInfo
import com.nu.bom.core.manufacturing.annotations.BehaviourCreation
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.FieldName
import com.nu.bom.core.manufacturing.annotations.FilteredChildren
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.Nocalc
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.StaticDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.TranslationLabel
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.AllocationQuantity
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent
import com.nu.bom.core.manufacturing.entities.toolmaintenance.DetailedToolMaintenanceCalculator
import com.nu.bom.core.manufacturing.entities.toolmaintenance.PercentPerLifetimeToolMaintenanceCalculator
import com.nu.bom.core.manufacturing.entities.toolmaintenance.PercentPerLotToolMaintenanceCalculator
import com.nu.bom.core.manufacturing.entities.toolmaintenance.PercentPerYearToolMaintenanceCalculator
import com.nu.bom.core.manufacturing.entities.toolmaintenance.TotalPerLifetimeToolMaintenanceCalculator
import com.nu.bom.core.manufacturing.entities.toolmaintenance.TotalPerLotToolMaintenanceCalculator
import com.nu.bom.core.manufacturing.entities.toolmaintenance.TotalPerYearToolMaintenanceCalculator
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.StaticUnitOverride
import com.nu.bom.core.manufacturing.fieldTypes.Count
import com.nu.bom.core.manufacturing.fieldTypes.Currency
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYears
import com.nu.bom.core.manufacturing.fieldTypes.ToolAllocationMode
import com.nu.bom.core.manufacturing.fieldTypes.ToolDetailViewConfig
import com.nu.bom.core.manufacturing.fieldTypes.ToolMaintenanceType
import com.nu.bom.core.manufacturing.fieldTypes.configuration.CalculationMethodologyConfigurationField
import com.nu.bom.core.manufacturing.service.behaviour.DynamicBehaviour
import com.nu.bom.core.manufacturing.service.behaviour.EntityBasedDynamicBehaviour
import com.nu.bom.core.utils.getResultWithSelectedAttributes
import org.bson.types.ObjectId
import java.math.BigDecimal
import java.math.RoundingMode

@EntityType(Entities.TOOL)
class BaseTool(name: String) : ManufacturingEntity(name) {
    override val extends = BaseEntityFields(name)

    // region core functionality

    fun toolDetailView(): ToolDetailViewConfig = ToolDetailViewConfig.DEFAULT

    @Input
    @ObjectView(ObjectView.TOOL, 20)
    @MandatoryForEntity(index = 1)
    fun serviceLifeInCycles(): Num? = null

    @Input
    @Path("/api/currency{bomPath}{branchPath}", false)
    fun baseCurrency(): Currency = Currency("EUR")

    @Input
    @Path("/api/currency{bomPath}{branchPath}", false)
    @MandatoryForEntity(index = 2)
    fun masterdataBaseCurrency(): Currency = Currency("EUR")

    // endregion

    // region quantity calculation

    fun numberOfConcurrentlyUsedToolsPerSystem(): Num = Num.ONE

    @ReadOnly
    @Parent(Entities.MANUFACTURING_STEP)
    fun systemCount(): Count? = null

    @ReadOnly
    @Parent(Entities.MANUFACTURING_STEP)
    fun cyclesOverLifeTime(): Num? = null

    @TranslationLabel("toolQuantity")
    @ObjectView(ObjectView.TOOL, 30)
    fun quantity(
        serviceLifeInCycles: Num,
        cyclesOverLifeTime: Num,
        systemCount: Count,
        numberOfConcurrentlyUsedToolsPerSystem: Num,
    ): Num {
        val minimalNumberOfToolsDueToSystemCount = numberOfConcurrentlyUsedToolsPerSystem * systemCount
        val minimalNumberOfToolsDueToSystemCountScaled =
            Num(minimalNumberOfToolsDueToSystemCount.res.setScale(0, RoundingMode.CEILING))

        val minimalNumberOfToolsDueToServiceLife = cyclesOverLifeTime.safeDivision(serviceLifeInCycles, Num.ONE)

        // We divide by minimalNumberOfToolsDueToSystemCountScaled, scale and multiply with it to get a multiple of it
        val factor = minimalNumberOfToolsDueToServiceLife.safeDivision(minimalNumberOfToolsDueToSystemCountScaled, Num.ZERO)
        return minimalNumberOfToolsDueToSystemCountScaled * factor.res.setScale(0, RoundingMode.CEILING)
    }

    // endregion

    // region allocation volume

    @Nocalc
    private fun selectAllocationVolume(
        allocationQuantity: AllocationQuantity,
        @Parent(Entities.MANUFACTURING_STEP) averageUsableVolumeOverLifeTime: QuantityUnit,
        @Parent(Entities.MANUFACTURING_STEP) averageProcessedVolumeOverLifeTime: QuantityUnit,
        @Parent(Entities.MANUFACTURING_STEP) lotFractionApportionmentFactor: Rate,
        @Parent(Entities.MANUFACTURING_STEP) familyAllocationRatio: Rate,
    ): QuantityUnit? {
        // As we cannot throw errors from the calculation we return NULL here as a proxy for the error and handle this
        // at the callers side
        return when (allocationQuantity) {
            AllocationQuantity.USABLE_VOLUME -> {
                averageUsableVolumeOverLifeTime.safeDivision(familyAllocationRatio)
            }
            AllocationQuantity.PROCESSED_VOLUME -> {
                averageProcessedVolumeOverLifeTime.safeDivision(lotFractionApportionmentFactor * familyAllocationRatio)
            }
        }
    }

    fun investAndInterestAllocationVolume(
        @Parent(Entities.MANUFACTURING_STEP) averageUsableVolumeOverLifeTime: QuantityUnit,
        @Parent(Entities.MANUFACTURING_STEP) averageProcessedVolumeOverLifeTime: QuantityUnit,
        @Parent(Entities.MANUFACTURING_STEP) lotFractionApportionmentFactor: Rate,
        @Parent(Entities.MANUFACTURING_STEP) familyAllocationRatio: Rate,
        @Parent(Entities.MANUFACTURING) calculationMethodologyConfiguration: CalculationMethodologyConfigurationField,
    ): QuantityUnit? =
        selectAllocationVolume(
            calculationMethodologyConfiguration.res.toolAllocationConfiguration.allocationQuantityForInvestAndInterest,
            averageUsableVolumeOverLifeTime,
            averageProcessedVolumeOverLifeTime,
            lotFractionApportionmentFactor,
            familyAllocationRatio,
        )

    @AdditionalFieldInfo(
        "Changing a volume sets it as-is, ignoring any quantity of subcalculations. " +
            "If you want granular allocation volumes, use the table view.",
    )
    fun maintenanceAllocationVolume(
        @Parent(Entities.MANUFACTURING_STEP) averageUsableVolumeOverLifeTime: QuantityUnit,
        @Parent(Entities.MANUFACTURING_STEP) averageProcessedVolumeOverLifeTime: QuantityUnit,
        @Parent(Entities.MANUFACTURING_STEP) lotFractionApportionmentFactor: Rate,
        @Parent(Entities.MANUFACTURING_STEP) familyAllocationRatio: Rate,
        @Parent(Entities.MANUFACTURING) calculationMethodologyConfiguration: CalculationMethodologyConfigurationField,
    ): QuantityUnit? =
        selectAllocationVolume(
            calculationMethodologyConfiguration.res.toolAllocationConfiguration.allocationQuantityForMaintenance,
            averageUsableVolumeOverLifeTime,
            averageProcessedVolumeOverLifeTime,
            lotFractionApportionmentFactor,
            familyAllocationRatio,
        )

    // endregion

    // region allocation mode
    fun toolAllocationMode(
        @Parent(Entities.MANUFACTURING_STEP)
        toolAllocationMode: ToolAllocationMode,
    ): ToolAllocationMode = toolAllocationMode

    fun directlyPaidQuantityPerPart(
        toolAllocationMode: ToolAllocationMode,
        quantity: Num,
    ): Num =
        when (toolAllocationMode.res) {
            ToolAllocationMode.Mode.PAY_NONE -> Num.ZERO
            ToolAllocationMode.Mode.PAY_ONE -> Num(BigDecimal.ONE)
            ToolAllocationMode.Mode.PAY_ALL -> quantity
        }

    fun allocatedToolQuantity(
        quantity: Num,
        directlyPaidQuantityPerPart: Num,
    ): Num = quantity - directlyPaidQuantityPerPart

    @ObjectView(ObjectView.TOOL, 10)
    fun allocationRatio(
        quantity: Num,
        allocatedToolQuantity: Num,
    ): Rate {
        return Rate(allocatedToolQuantity.safeDivision(quantity, Num.ONE))
    }

    // endregion

    // region invest calculation

    @Input
    @ObjectView(ObjectView.TOOL, 40)
    @MandatoryForEntity(index = 3)
    fun investPerTool(): Money? = null

    @Input
    fun conceptCost(): Money = Money(BigDecimal.ZERO)

    @Input
    @ObjectView(ObjectView.TOOL, 50)
    fun proportionalInvest(): Rate = Rate.ONE

    @ObjectView(ObjectView.TOOL, 60)
    @ReadOnly
    fun investWithoutConceptCost(
        investPerTool: Money,
        quantity: Num,
        proportionalInvest: Rate,
    ): Money = investPerTool * quantity * proportionalInvest

    @ObjectView(ObjectView.TOOL, 60)
    @ReadOnly
    fun invest(
        investWithoutConceptCost: Money,
        conceptCost: Money,
    ): Money = investWithoutConceptCost + conceptCost

    fun allocatedInvestTotal(
        invest: Money,
        allocationRatio: Rate,
        investWithoutConceptCost: Money,
        toolAllocationMode: ToolAllocationMode,
    ): Money {
        // Concept costs are only allocated if all tools are allocated
        // The assumption is here that concept costs are paid with the first tool and thus never need to be allocated if one tool is paid
        return if (toolAllocationMode.res == ToolAllocationMode.Mode.PAY_NONE) {
            invest
        } else {
            investWithoutConceptCost * allocationRatio
        }
    }

    fun directlyPaidInvest(
        invest: Money,
        allocatedInvestTotal: Money,
    ): Money = invest - allocatedInvestTotal

    @ObjectView(ObjectView.TOOL, 70)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun investCost(
        allocatedInvestTotal: Money,
        investAndInterestAllocationVolume: QuantityUnit,
    ): Money = allocatedInvestTotal.safeDivision(investAndInterestAllocationVolume, Money.ZERO)

    // endregion

    // region interest calculation

    @StaticDenominatorUnit(StaticUnitOverride.YEAR)
    fun interestRate(
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_INTEREST,
            nameFilter = MasterdataCostFactorParent.TSET_COST_FACTOR_INTEREST_RATES_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        ) value: Map<ObjectId, Rate>?,
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_INTEREST,
            nameFilter = MasterdataCostFactorParent.TSET_COST_FACTOR_INTEREST_RATES_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        @FieldName("location")
        costFactorLocations: Map<ObjectId, Text>,
        @Parent(Entities.MANUFACTURING_STEP)
        location: Text,
    ): Rate? {
        return getResultWithSelectedAttributes(value, costFactorLocations, location)
    }

    @DefaultUnit(DefaultUnit.YEAR)
    @ObjectView(ObjectView.TOOL, 100)
    @ReadOnly
    fun interestCalcPeriod(
        quantity: Num,
        @Parent(Entities.MANUFACTURING)
        lifeTime: TimeInYears,
        @Parent(Entities.MANUFACTURING_STEP)
        systemCount: Count,
    ): TimeInYears = lifeTime * systemCount.safeDivision(quantity, Count(0.0))

    @ObjectView(ObjectView.TOOL, 110)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun interestCost(
        allocatedInvestTotal: Money,
        interestCalcPeriod: TimeInYears,
        investAndInterestAllocationVolume: QuantityUnit,
        interestRate: Rate,
    ): Money {
        // It is assumed that we have a linear payback of the tool invest.
        // Hence, the capital to pay interest decreases linearly over the period.
        // This linear decrease of capital is accounted for by using half of the interest rate.
        val effectiveRate = interestRate / BigDecimal("2")

        // The interest cost of the tool depends on the initial invest, the interest rate and the time of payback
        val interestCostPerTool = allocatedInvestTotal * effectiveRate * interestCalcPeriod

        // allocate the interest cost to get to the costs per quantity
        return interestCostPerTool.safeDivision(investAndInterestAllocationVolume, Money.ZERO)
    }

    // endregion

    // region generic allocation

    @ReadOnly
    fun allocatedToolCostPerTool(
        // from commercial aggregation
        allocatedCostPerPart: Money,
        @Parent(Entities.MANUFACTURING_STEP)
        averageUsableVolumeOverLifeTime: QuantityUnit,
    ): Money = allocatedCostPerPart * averageUsableVolumeOverLifeTime

    // endregion

    // region maintenance calculation

    fun toolMaintenanceType(): ToolMaintenanceType = ToolMaintenanceType.PERCENT_LIFETIME

    @BehaviourCreation
    fun createToolMaintenanceBehaviour(toolMaintenanceType: ToolMaintenanceType): DynamicBehaviour {
        return when (toolMaintenanceType.res) {
            ToolMaintenanceType.Selection.DETAILED_TOOL_MAINTENANCE ->
                EntityBasedDynamicBehaviour(this, DetailedToolMaintenanceCalculator("Detailed Tool Calculator"))
            ToolMaintenanceType.Selection.PERCENT_LIFETIME ->
                EntityBasedDynamicBehaviour(this, PercentPerLifetimeToolMaintenanceCalculator("Percent Per Life Tool Calculator"))
            ToolMaintenanceType.Selection.PERCENT_LOT ->
                EntityBasedDynamicBehaviour(this, PercentPerLotToolMaintenanceCalculator("Percent Per Lot Tool Calculator"))
            ToolMaintenanceType.Selection.PERCENT_YEAR ->
                EntityBasedDynamicBehaviour(this, PercentPerYearToolMaintenanceCalculator("Percent Per Year Tool Calculator"))
            ToolMaintenanceType.Selection.TOTAL_LIFETIME ->
                EntityBasedDynamicBehaviour(this, TotalPerLifetimeToolMaintenanceCalculator("Total Per Life Tool Calculator"))
            ToolMaintenanceType.Selection.TOTAL_LOT ->
                EntityBasedDynamicBehaviour(this, TotalPerLotToolMaintenanceCalculator("Total Per Lot Tool Calculator"))
            ToolMaintenanceType.Selection.TOTAL_YEAR ->
                EntityBasedDynamicBehaviour(this, TotalPerYearToolMaintenanceCalculator("Total Per Year Tool Calculator"))
        }
    }

    @ObjectView(ObjectView.TOOL, 90)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun maintenanceCost(
        maintenanceCostOverLifetime: Money,
        maintenanceAllocationVolume: QuantityUnit,
    ): Money = maintenanceCostOverLifetime.safeDivision(maintenanceAllocationVolume, Money.ZERO)

    // endregion
}
