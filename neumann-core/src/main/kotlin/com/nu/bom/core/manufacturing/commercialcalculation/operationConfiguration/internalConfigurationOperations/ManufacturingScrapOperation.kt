package com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations

import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel

data class ManufacturingScrapOperation(
    override val destinationElementKey: String,
    override val origin: AggregationLevel,
    override val baseAddends: List<WeightedCalculationElement>,
    override val previousBaseAddends: List<WeightedCalculationElement>,
    override val rateFieldName: String,
) : ScrapOperation
