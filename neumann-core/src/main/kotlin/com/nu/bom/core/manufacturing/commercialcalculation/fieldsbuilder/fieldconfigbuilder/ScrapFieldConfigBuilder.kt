package com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.fieldconfigbuilder

import com.nu.bom.core.manufacturing.annotations.entityfieldmetainfo.EntityFieldMetaInfo
import com.nu.bom.core.manufacturing.annotations.entityfieldmetainfo.ReadOnlyEntityFieldMetaInfo
import com.nu.bom.core.manufacturing.commercialcalculation.common.AggregationLevelAndRole
import com.nu.bom.core.manufacturing.commercialcalculation.fieldnamebuilders.RateTimeFieldNameBuilder
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.FieldsBuilderContext
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.InternalConfigurationOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.ProcurementTypeAndMaterialClass
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.WeightedCalculationElement
import com.nu.bom.core.manufacturing.configurablefields.config.FieldConfig
import com.nu.bom.core.manufacturing.fieldTypes.Rate

abstract class ScrapFieldConfigBuilder : FieldConfigBuilder() {
    protected fun createScrapBaseField(
        fieldsBuilderContext: FieldsBuilderContext,
        transfers: Map<String, List<ProcurementTypeAndMaterialClass>>,
        destinationElementKey: String,
        baseAddends: List<WeightedCalculationElement>,
        previousBaseAddends: List<WeightedCalculationElement>,
        useLotFraction: Boolean,
    ): FieldConfig {
        val baseElementKeys =
            baseAddends
                .flatMap {
                    fieldsBuilderContext.getAllOperations<InternalConfigurationOperation>(it.elementKey).filter { op ->
                        fieldsBuilderContext.hasOperation(op)
                    }.map { op -> op.destinationElementKey }
                }

        val previousBaseLevelAndRoles =
            fieldsBuilderContext.creationContext.previousBase.map {
                AggregationLevelAndRole(it.aggregationLevel, it.aggregationRole)
            }

        return if (previousBaseLevelAndRoles.isEmpty()) {
            // simple base
            fieldsBuilderContext.operationBuilder.sumOfBase(
                aggregation = fieldsBuilderContext.aggregation,
                elementTypeKey = destinationElementKey,
                baseElementTypeKeys = baseElementKeys,
                metaInfo = listOf(ReadOnlyEntityFieldMetaInfo(), fieldsBuilderContext.getDynamicDenominatorUnit()),
                availableOutsideTheEngine =
                    fieldsBuilderContext.availableOutsideTheEngine &&
                        !fieldsBuilderContext.isUnneededLeafMaterialField(op, transfers),
            )
        } else {
            // we have a base and a previous base
            val previousBaseElementKeys = previousBaseAddends.map { it.elementKey }

            if (useLotFraction) {
                fieldsBuilderContext.operationBuilder.sumScrapBaseOf(
                    aggregation = fieldsBuilderContext.aggregation,
                    elementTypeKey = destinationElementKey,
                    baseElementTypeKeys = baseElementKeys,
                    previousLevelAndRoles = previousBaseLevelAndRoles,
                    previousBaseElementTypeKeys = previousBaseElementKeys,
                    metaInfo = listOf(ReadOnlyEntityFieldMetaInfo(), fieldsBuilderContext.getDynamicDenominatorUnit()),
                    availableOutsideTheEngine =
                        fieldsBuilderContext.availableOutsideTheEngine &&
                            !fieldsBuilderContext.isUnneededLeafMaterialField(op, transfers),
                )
            } else {
                fieldsBuilderContext.operationBuilder.sumOfBase(
                    aggregation = fieldsBuilderContext.aggregation,
                    elementTypeKey = destinationElementKey,
                    baseElementTypeKeys = baseElementKeys,
                    previousLevelAndRoles = previousBaseLevelAndRoles,
                    previousBaseElementTypeKeys = previousBaseElementKeys,
                    metaInfo = listOf(ReadOnlyEntityFieldMetaInfo(), fieldsBuilderContext.getDynamicDenominatorUnit()),
                    availableOutsideTheEngine =
                        fieldsBuilderContext.availableOutsideTheEngine &&
                            !fieldsBuilderContext.isUnneededLeafMaterialField(op, transfers),
                )
            }
        }
    }

    protected fun createScrapRateField(
        fieldsBuilderContext: FieldsBuilderContext,
        transfers: Map<String, List<ProcurementTypeAndMaterialClass>>,
        destinationElementKey: String,
        rateFieldName: String?,
        metaInfo: List<EntityFieldMetaInfo>,
    ): FieldConfig {
        return if (rateFieldName == null) {
            fieldsBuilderContext.operationBuilder.assignZero(
                destinationElementKey,
                Rate::class,
                metaInfo,
                availableOutsideTheEngine =
                    fieldsBuilderContext.availableOutsideTheEngine &&
                        !fieldsBuilderContext.isUnneededLeafMaterialField(op, transfers),
                extensionName = RateTimeFieldNameBuilder.ExtensionName.RATE,
            )
        } else {
            fieldsBuilderContext.operationBuilder.assignRate(
                destinationElementKey,
                rateFieldName,
                fieldClass = Rate::class,
                metaInfo = metaInfo,
                availableOutsideTheEngine =
                    fieldsBuilderContext.availableOutsideTheEngine &&
                        !fieldsBuilderContext.isUnneededLeafMaterialField(op, transfers),
                extensionName = RateTimeFieldNameBuilder.ExtensionName.RATE,
            )
        }
    }
}
