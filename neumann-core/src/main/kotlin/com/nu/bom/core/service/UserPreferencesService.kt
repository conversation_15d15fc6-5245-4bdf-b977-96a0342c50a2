package com.nu.bom.core.service

import com.nu.bom.core.model.CurrencyPreferences
import com.nu.bom.core.model.UserPreferences
import com.nu.bom.core.model.UserPreferencesId
import com.nu.bom.core.repository.UserPreferencesRepository
import com.nu.bom.core.user.AccessCheck
import com.nu.http.EnvironmentNameSupplier
import org.bson.types.Binary
import org.slf4j.LoggerFactory
import org.springframework.data.mongodb.core.FindAndModifyOptions
import org.springframework.data.mongodb.core.ReactiveMongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.Update
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono

@Service
class UserPreferencesService(
    private val reactiveMongoTemplate: ReactiveMongoTemplate,
    private val userPreferencesRepository: UserPreferencesRepository,
    private val environmentNameSupplier: EnvironmentNameSupplier,
) {
    companion object {
        private val logger = LoggerFactory.getLogger(UserPreferencesService::class.java)

        val EMPTY_DATA = Binary("{}".toByteArray())
    }

    private fun getEnvironment() = environmentNameSupplier.getEnv() ?: ""

    private fun createUserPreferencesId(accessCheck: AccessCheck) =
        UserPreferencesId(
            userId = accessCheck.userId,
            accountId = accessCheck.asAccountId(),
            environment = getEnvironment(),
        )

    fun getOrCreateUserPreferences(accessCheck: AccessCheck): Mono<UserPreferences> {
        val id = createUserPreferencesId(accessCheck)
        return userPreferencesRepository
            .findById(id)
            .switchIfEmpty(
                userPreferencesRepository.save(
                    UserPreferences(
                        _id = id,
                        uiStore = EMPTY_DATA,
                        currencyPreferences = CurrencyPreferences.getDefaults(),
                    ),
                ),
            )
    }

    fun createOrUpdateUiStore(
        accessCheck: AccessCheck,
        data: ByteArray,
    ): Mono<UserPreferences> {
        val id = createUserPreferencesId(accessCheck)
        val query = Query(Criteria.where("_id").`is`(id))
        val update = Update().set("uiStore", Binary(data))
        return reactiveMongoTemplate
            .findAndModify(
                query,
                update,
                FindAndModifyOptions.options().returnNew(true),
                UserPreferences::class.java,
            )
    }

    fun getDebugValue(
        accessCheck: AccessCheck,
        key: String,
    ): Mono<Any> =
        getOrCreateUserPreferences(accessCheck)
            .flatMap { userPreferences ->
                if (userPreferences.debugSettings != null) {
                    val setting = userPreferences.debugSettings[key]
                    if (setting != null) {
                        logger.debug("UserPreferences debug setting found: {} = {}", key, setting)
                        Mono.just(setting)
                    } else {
                        logger.debug("UserPreferences debug setting not found: $key")
                        Mono.empty()
                    }
                } else {
                    logger.debug("UserPreferences debug settings not found")
                    Mono.empty()
                }
            }

    fun setDebugValue(
        accessCheck: AccessCheck,
        key: String,
        value: Any,
    ): Mono<UserPreferences> =
        getOrCreateUserPreferences(accessCheck)
            .flatMap { userPreferences ->
                userPreferencesRepository
                    .save(
                        if (userPreferences.debugSettings != null) {
                            userPreferences.copy(
                                debugSettings = userPreferences.debugSettings + (key to value),
                            )
                        } else {
                            userPreferences.copy(
                                debugSettings = mapOf(key to value),
                            )
                        },
                    )
            }

    fun updateCurrencyPreferences(
        accessCheck: AccessCheck,
        currencyPreferences: CurrencyPreferences,
    ): Mono<CurrencyPreferences> =
        getOrCreateUserPreferences(accessCheck).flatMap { userPreferences ->
            // TODO: check available ccys
            userPreferencesRepository
                .save(
                    userPreferences.copy(
                        currencyPreferences = currencyPreferences,
                    ),
                ).mapNotNull {
                    it.currencyPreferences
                }
        }
}
