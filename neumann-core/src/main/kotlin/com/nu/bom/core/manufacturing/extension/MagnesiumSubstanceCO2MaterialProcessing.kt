package com.nu.bom.core.manufacturing.extension

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.manufacturing.fieldTypes.EmissionUnits
import com.nu.bom.core.manufacturing.fieldTypes.MaterialSubstances
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Text
import java.math.BigDecimal

@EntityType(Entities.CO2_PROCESSING_MATERIAL)
class MagnesiumSubstanceCO2MaterialProcessing(name: String) : ManufacturingEntity(name) {

    override val extends = BaseCO2MaterialProcessing(name)

    @Input
    fun designation(): Text? = null

    @Input
    fun substanceName(): Text? = null

    @Parent(Entities.MATERIAL)
    fun materialSubstances(): MaterialSubstances = MaterialSubstances(emptyList())

    fun quantity(materialSubstances: MaterialSubstances): Num {
        val otherSubstances = materialSubstances.res.sumOf {
            if (it.substance != "Mg") it.max.toDouble() else 0.0
        }
        return Num(BigDecimal.ONE - otherSubstances.toBigDecimal())
    }

    @Input
    fun cO2Passive(): Emission = Emission(BigDecimal.ZERO, EmissionUnits.KILOGRAM_CO2E)

    @Input
    fun cO2Active(): Emission = Emission(BigDecimal.ZERO, EmissionUnits.KILOGRAM_CO2E)
}
