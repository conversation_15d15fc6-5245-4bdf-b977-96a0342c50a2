package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.manufacturing.annotations.AcceptSubclasses
import java.math.BigDecimal
import java.math.MathContext

abstract class DynamicUnit<T : NumericFieldResult<T>>(
    res: BigDecimal,
) : NumericFieldResult<T>(res)

class QuantityUnit(
    res: BigDecimal,
) : DynamicUnit<QuantityUnit>(res) {
    constructor(res: Double) : this(res.toBigDecimal())
    constructor(res: String) : this(BigDecimal(res))
    constructor(res: DynamicQuantityUnit) : this(res.inBaseUnit())

    // Currently, how we convert back after a FE round trip.
    constructor(res: Pieces) : this(res.res)
    constructor(res: Length) : this(res.res)
    constructor(res: Area) : this(res.res)
    constructor(res: Weight) : this(res.res)
    constructor(res: Volume) : this(res.res)

    // Additionally, required for Backwards compatibility.
    constructor(res: Num) : this(res.res)

    companion object {
        val ZERO = QuantityUnit(BigDecimal.ZERO)

        fun isSpecificQuantityUnit(shortTypeName: String) = validSpecificQuantityTypes.contains(shortTypeName)

        private val validSpecificQuantityTypes =
            setOf(
                Pieces::class.simpleName!!,
                Length::class.simpleName!!,
                Area::class.simpleName!!,
                Weight::class.simpleName!!,
                Volume::class.simpleName!!,
                Num::class.simpleName!!,
            )
    }
}

class DynamicQuantityUnit(
    res: BigDecimal,
    numeratorUnit: TypeUnit?,
    denominatorUnit: TypeUnit?,
    val defaultValue: Boolean = false,
) : NumericFieldResultWithNumeratorAndDenominator<
        DynamicQuantityUnit,
        TypeUnit,
        TypeUnit,
    >(res, numeratorUnit, denominatorUnit) {
    constructor(quantityUnit: QuantityUnit) : this(quantityUnit.res, null, null, false)
    constructor(quantityUnit: Num) : this(quantityUnit.res, null, null, false)
    constructor(res: BigDecimal) : this(res, null, null, false)
    constructor(
        res: Weight,
        denominatorDimension: Dimension,
        denominatorUnit: Text,
    ) : this(res.res, res.unit, denominatorDimension.res.typeUnit(denominatorUnit.res))

    @AcceptSubclasses
    constructor(res: NumericFieldResultWithUnit<*, *>) : this(res.res / res.inputUnit().baseFactor, res.inputUnit(), null)
    constructor(
        res: Double,
        numeratorUnit: TypeUnit?,
        denominatorUnit: TypeUnit?,
    ) : this(res.toBigDecimal(), numeratorUnit, denominatorUnit) {}
    constructor(
        res: String,
        numeratorUnit: TypeUnit,
        denominatorUnit: TypeUnit?,
    ) : this(res.toBigDecimal(), numeratorUnit, denominatorUnit) {}

    constructor(res: BigDecimal, numeratorDimension: Dimension, numeratorUnit: Text) : this(
        res,
        numeratorDimension.res.typeUnit(numeratorUnit.res),
        null,
    )

    constructor(res: BigDecimal, numeratorDimension: Dimension, numeratorUnit: Text, denominatorUnit: TypeUnit) : this(
        res,
        numeratorDimension.res.typeUnit(numeratorUnit.res),
        denominatorUnit,
    )
    constructor(res: Double, numeratorDimension: Dimension, numeratorUnit: Text) : this(
        res.toBigDecimal(),
        numeratorDimension.res.typeUnit(numeratorUnit.res),
        null,
    )

    constructor(
        res: BigDecimal,
        numeratorDimension: Dimension,
        numeratorUnit: Text,
        denominatorDimension: Dimension,
        denominatorUnit: Text,
    ) : this(
        res,
        numeratorDimension.res.typeUnit(numeratorUnit.res),
        denominatorDimension.res.typeUnit(denominatorUnit.res),
    )

    override fun create(res: BigDecimal): DynamicQuantityUnit =
        DynamicQuantityUnit(
            res
                .divide((numeratorUnit?.baseFactor ?: BigDecimal.ONE), MathContext.DECIMAL64)
                .multiply((denominatorUnit?.baseFactor ?: BigDecimal.ONE), MathContext.DECIMAL64),
            numeratorUnit,
            denominatorUnit,
            defaultValue,
        )

    override fun copy(source: SOURCE?): FieldResult<*, *> =
        create(super.res).also {
            if (source != null) {
                it.source = source
            }
        }

    operator fun compareTo(selectedVal: DynamicQuantityUnit): Int = res.compareTo(selectedVal.res)

    override operator fun times(other: NumericFieldResult<*>): DynamicQuantityUnit =
        when (other) {
            is DynamicQuantityUnit -> {
                opDynamicQuantityUnit(other, Int::plus) {
                    this * it
                }
            }

            is NumericFieldResultWithUnit<*, *> ->
                opDynamicQuantityUnit(
                    DynamicQuantityUnit(other),
                    Int::plus,
                ) {
                    this * it
                }

            else -> create(res * other.res)
        }

    override operator fun div(other: NumericFieldResult<*>): DynamicQuantityUnit =
        when (other) {
            is DynamicQuantityUnit -> {
                opDynamicQuantityUnit(other, Int::minus) { this / it }
            }
            is NumericFieldResultWithUnit<*, *> ->
                opDynamicQuantityUnit(
                    DynamicQuantityUnit(other),
                    Int::minus,
                ) { this / it }

            else -> create(res / other.res)
        }

    private fun opDynamicQuantityUnit(
        other: DynamicQuantityUnit,
        potentOperator: Int.(Int) -> Int,
        valueOperator: BigDecimal.(BigDecimal) -> BigDecimal,
    ): DynamicQuantityUnit {
        val potentMap: MutableMap<TypeUnit, Int> = calculatePotencyMap(other, potentOperator)

        var resultNumerator: TypeUnit? = null
        var resultDenominator: TypeUnit? = null

        potentMap.entries.forEach { (unit, potency) ->
            when {
                potency == 1 && resultNumerator == null ->
                    resultNumerator = unit
                potency == 1 && resultNumerator != null -> error("Cannot construct a complex unit, provide manually please")
                potency == -1 && resultDenominator == null -> resultDenominator = unit
                potency == -1 && resultDenominator != null -> error("Cannot construct a complex unit, provide manually please")
            }
        }

        val baseFactor =
            potentMap.toList().fold(BigDecimal.ONE) { result, (typeunit, potent) ->
                result.multiply(typeunit.baseFactor.pow(potent, MathContext.DECIMAL64), MathContext.DECIMAL64)
            }
        val resInBaseUnit = valueOperator(inBaseUnit(), other.inBaseUnit()).divide(baseFactor, MathContext.DECIMAL64)

        return DynamicQuantityUnit(resInBaseUnit, resultNumerator, resultDenominator)
    }

    private fun calculatePotencyMap(
        other: DynamicQuantityUnit,
        potentOperator: Int.(Int) -> Int,
    ): MutableMap<TypeUnit, Int> {
        val potent: MutableMap<TypeUnit, Int> = mutableMapOf()
        numeratorUnit?.let {
            potent.compute(it) { _, i ->
                if (i == null) 1 else i + 1
            }
        }
        denominatorUnit?.let {
            potent.compute(it) { _, i ->
                if (i == null) -1 else i - 1
            }
        }
        other.numeratorUnit?.let {
            potent.compute(it) { _, i ->
                potentOperator(i ?: 0, 1)
            }
        }
        other.denominatorUnit?.let {
            potent.compute(it) { _, i ->
                potentOperator(i ?: 0, -1)
            }
        }
        return potent
    }

    override val res: BigDecimal
        get() {
            return super.res
        }

    fun withInputUnit(
        otherNumerator: TypeUnit?,
        otherDenominator: TypeUnit?,
    ): DynamicQuantityUnit =
        if (otherNumerator == numeratorUnit && otherDenominator == denominatorUnit) {
            this
        } else {
            val convert =
                when {
                    otherDenominator != null && otherNumerator != null -> res / otherNumerator.baseFactor * otherDenominator.baseFactor
                    otherNumerator == null && otherDenominator != null -> res * otherDenominator.baseFactor
                    otherNumerator != null && otherDenominator == null -> res.divide(otherNumerator.baseFactor, MathContext.DECIMAL64)
                    else -> res
                }
            DynamicQuantityUnit(convert, otherNumerator, otherDenominator).also {
                copyAttributesFromThisTo(it)
            }
        }

    fun isEmptyValue(): Boolean = defaultValue == true && res.compareTo(BigDecimal.ZERO) == 0

    fun resultIn(
        otherUnit: TypeUnit,
        otherDenominator: TypeUnit,
    ): BigDecimal {
        if (denominatorUnit == null || numeratorUnit == null) {
            error("Converting Dynamic Quantity Unit to result without the appropriate base units")
        }
        if (denominatorUnit.type != otherDenominator.type || otherUnit.type != numeratorUnit.type) {
            error("Converting Dynamic Quantity Unit to result with different units")
        }

        return res / otherUnit.baseFactor * otherDenominator.baseFactor
    }

    fun resultIn(otherUnit: TypeUnit): BigDecimal = res / otherUnit.baseFactor

    fun toQuantityUnit() = QuantityUnit(res)
}
