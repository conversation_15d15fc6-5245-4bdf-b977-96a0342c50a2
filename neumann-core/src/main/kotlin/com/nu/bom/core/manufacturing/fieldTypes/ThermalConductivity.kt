package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.manufacturing.annotations.Units
import java.math.BigDecimal

enum class ThermalConductivityUnits(override val baseFactor: BigDecimal) : TypeUnit {
    WATTS_PER_METER_KELVIN(BigDecimal.ONE);

    override val type = TypeUnits.THERMAL_CONDUCTIVITY
    override val hideInDropdown = false
}

@Units(ThermalConductivityUnits::class)
class ThermalConductivity(res: BigDecimal, unit: ThermalConductivityUnits) : NumericFieldResultWithUnit<ThermalConductivity, ThermalConductivityUnits>(res, unit) {
    constructor(res: Double, unit: ThermalConductivityUnits) : this(res.toBigDecimal(), unit)

    constructor(res: String, unit: String) : this(BigDecimal(res), ThermalConductivityUnits.valueOf(unit))
    constructor(res: BigDecimal, unit: String) : this(res, ThermalConductivityUnits.valueOf(unit))

    val inWattsPerMK: BigDecimal
        get() { return to(ThermalConductivityUnits.WATTS_PER_METER_KELVIN) }
}
