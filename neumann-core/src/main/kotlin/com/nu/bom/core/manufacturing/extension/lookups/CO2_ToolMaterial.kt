package com.nu.bom.core.manufacturing.extension.lookups

import com.nu.bom.core.manufacturing.fieldTypes.ToolMaterial
import java.math.BigDecimal

data class CO2_ToolMaterial(
    val toolMaterial: ToolMaterial,
    val toolMaterialDensity: BigDecimal,
    val toolMaterialEmission: BigDecimal,
)

val cO2_ToolMaterialReader: (row: List<String>) -> CO2_ToolMaterial = { row ->
    CO2_ToolMaterial(
        toolMaterial = ToolMaterial.valueOf(row[0]),
        toolMaterialDensity = row[1].toBigDecimal(),
        toolMaterialEmission = row[2].toBigDecimal(),
    )
}
