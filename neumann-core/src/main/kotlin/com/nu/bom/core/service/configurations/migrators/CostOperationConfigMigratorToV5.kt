package com.nu.bom.core.service.configurations.migrators

import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.CostOperationConfigurationV4
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.CostOperationConfigurationV5
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetProcurementTypeConfigurationFactory.getDefaultProcurementTypeConfiguration
import com.nu.bom.core.service.configurations.ConfigType
import com.nu.bom.core.service.configurations.ConfigurationMigrator
import com.nu.bom.core.service.configurations.migrators.helper.OperationConfigurationMigratorHelper.defineLegacyCostTransferOperations
import com.nu.bom.core.service.configurations.migrators.helper.OperationConfigurationMigratorHelper.replaceTransferOperationsToWorkWithNewProcurementType
import org.springframework.stereotype.Component
import kotlin.reflect.KClass

/**
 * Migration that adds procurementType config and adapts transfer ops
 */
@Component
class CostOperationConfigMigratorToV5 : ConfigurationMigrator<CostOperationConfigurationV4, CostOperationConfigurationV5> {
    override fun sourceClass(): KClass<CostOperationConfigurationV4> = CostOperationConfigurationV4::class

    override fun targetClass(): KClass<CostOperationConfigurationV5> = CostOperationConfigurationV5::class

    override fun configType() = ConfigType.CommercialCostOperations

    override fun migrate(configurationValue: CostOperationConfigurationV4): CostOperationConfigurationV5 {
        return CostOperationConfigurationV5(
            replaceTransferOperationsToWorkWithNewProcurementType(
                configurationValue.operations,
                defineLegacyCostTransferOperations(),
            ),
            getDefaultProcurementTypeConfiguration(),
        )
    }
}
