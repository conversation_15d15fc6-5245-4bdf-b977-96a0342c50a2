package com.nu.bom.core.service.nuledge

import com.nu.bom.core.remote.NuService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.user.UserClient
import com.nu.http.TsetService
import com.tset.bom.clients.nuledge.KnowledgeEntityDto
import com.tset.bom.clients.nuledge.KnowledgeEntitySearchResult
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.data.domain.Sort
import reactor.core.publisher.Mono
import java.net.URI

class NuLedgeServiceImpl(
    userClient: UserClient,
    config: NuLedgeClientProperties,
    tsetService: TsetService,
) : NuLedgeService {
    private val nuService = NuService(userClient, config.url, tsetService)

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(NuLedgeServiceImpl::class.java)
    }

    override fun nuService() = nuService

    override fun getConfigurations(
        accessCheck: AccessCheck,
        key: String,
    ): Mono<KnowledgeEntityDto> {
        return nuService.withTsetService().getToMono(
            baseUrl = nuService.baseUrl(),
            uri = {
                val builder = it.path("/api/internal/knowledge/configurations")
                builder.queryParam("key", key).queryParam("accountId", accessCheck.accountId)
                    .queryParam("userId", accessCheck.userId)
                val uri: URI = builder.build()
                logRequest(uri)
                uri
            },
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            successHandler = { response -> response.bodyToMono(KnowledgeEntityDto::class.java) },
        )
    }

    override fun getTemplates(
        accessCheck: AccessCheck,
        term: String?,
        page: Int,
        size: Int,
        sortBy: String?,
        orderBy: Sort.Direction?,
        manufacturingStepType: List<String>?,
        isTset: Boolean?,
    ): Mono<KnowledgeEntitySearchResult> {
        return nuService.withTsetService().getToMono(
            baseUrl = nuService.baseUrl(),
            uri = {
                val builder = it.path("/api/internal/knowledge/templates")
                builder.queryParam("term", term).queryParam("page", page).queryParam("size", size)
                    .queryParam("sortBy", sortBy).queryParam("orderBy", orderBy)
                    .queryParam("manufacturingStepType", manufacturingStepType).queryParam("isTset", isTset)
                val uri: URI = builder.build()
                logRequest(uri)
                uri
            },
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            successHandler = { response -> response.bodyToMono(KnowledgeEntitySearchResult::class.java) },
        )
    }

    override fun templateExists(
        accessCheck: AccessCheck,
        key: String,
    ): Mono<Boolean> {
        return nuService.withTsetService().getToMono(
            baseUrl = nuService.baseUrl(),
            uri = {
                val builder = it.path("/api/internal/knowledge/template")
                builder.queryParam("key", key).queryParam("accountId", accessCheck.accountId)
                    .queryParam("userId", accessCheck.userId)
                val uri: URI = builder.build()
                logRequest(uri)
                uri
            },
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            successHandler = { response -> response.bodyToMono(Boolean::class.java) },
        )
    }

    override fun getTemplateObject(
        accessCheck: AccessCheck,
        key: String,
    ): Mono<KnowledgeEntityDto> {
        return nuService.withTsetService().getToMono(
            baseUrl = nuService.baseUrl(),
            uri = {
                val builder = it.path("/api/internal/knowledge/templateObject")
                builder.queryParam("key", key).queryParam("accountId", accessCheck.accountId)
                    .queryParam("userId", accessCheck.userId)
                val uri: URI = builder.build()
                logRequest(uri)
                uri
            },
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            successHandler = { response -> response.bodyToMono(KnowledgeEntityDto::class.java) },
        )
    }

    override fun createTemplate(
        accessCheck: AccessCheck,
        knowledgeEntityDto: KnowledgeEntityDto,
    ): Mono<KnowledgeEntityDto> {
        return nuService.withTsetService().postToMono(
            baseUrl = nuService.baseUrl(),
            uri = {
                val builder = it.path("/api/internal/knowledge")
                builder.queryParam("accountId", accessCheck.accountId).queryParam("userId", accessCheck.userId)
                val uri: URI = builder.build()
                logRequest(uri)
                uri
            },
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            requestBody = knowledgeEntityDto,
            successHandler = { response -> response.bodyToMono(KnowledgeEntityDto::class.java) },
        )
    }

    override fun deleteTemplate(
        accessCheck: AccessCheck,
        key: String,
    ): Mono<Void> {
        return nuService.withTsetService().deleteToMono(
            baseUrl = nuService.baseUrl(),
            uri = {
                val builder = it.path("/api/internal/knowledge/template/{key}")
                builder.queryParam("accountId", accessCheck.accountId).queryParam("userId", accessCheck.userId)
                val uri: URI = builder.build(key)
                logRequest(uri)
                uri
            },
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            successHandler = { response -> response.bodyToMono(Void::class.java) },
        )
    }

    override fun updateTemplate(
        accessCheck: AccessCheck,
        template: KnowledgeEntityDto,
    ): Mono<KnowledgeEntityDto> {
        return nuService.withTsetService().putToMono(
            baseUrl = nuService.baseUrl(),
            uri = {
                val builder = it.path("/api/internal/knowledge/template")
                builder.queryParam("accountId", accessCheck.accountId).queryParam("userId", accessCheck.userId)
                val uri: URI = builder.build()
                logRequest(uri)
                uri
            },
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            requestBody = template,
            successHandler = { response -> response.bodyToMono(KnowledgeEntityDto::class.java) },
        )
    }

    private fun logRequest(uri: URI) {
        logger.info("Sending query to: {}", uri)
    }
}
