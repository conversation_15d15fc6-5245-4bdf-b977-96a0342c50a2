package com.nu.bom.core.api

import com.nu.bom.core.api.dtos.AutocompleteConfigurationResponse
import com.nu.bom.core.exception.readable.InputMissing
import com.nu.bom.core.exception.readable.InputMissingException
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.service.CalculationQualitiesService
import com.nu.bom.core.user.AccessCheckProvider
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Flux

@RestController
@RequestMapping("/api/calculationQualities")
class CalculationQualityController(
    private val accessCheckProvider: AccessCheckProvider,
    private val calculationQualitiesService: CalculationQualitiesService,
) {
    @GetMapping
    fun getQualities(
        @RequestParam(required = false) currentConfigKey: String?,
        @RequestParam(required = false) currentConfigMajorVersion: Int?,
        @RequestParam(required = false) currentConfigMinorVersion: Int?,
        @AuthenticationPrincipal jwt: Jwt?,
    ): Flux<AutocompleteConfigurationResponse> {
        val noneNullParams = listOf(currentConfigKey, currentConfigMajorVersion, currentConfigMinorVersion).count { it != null }
        if (noneNullParams != 0 && noneNullParams != 3) {
            return Flux.error(InputMissingException(InputMissing.INVALID_REQUEST_PARAMS, "Either all params must be null or none"))
        }

        return accessCheckProvider.doAsReturnMany(jwt) { accessCheck ->
            calculationQualitiesService.getCalculationQualities(
                accessCheck,
                currentConfigKey?.let { ConfigurationIdentifier(it, currentConfigMajorVersion!!, currentConfigMinorVersion!!) },
            )
        }
    }
}
