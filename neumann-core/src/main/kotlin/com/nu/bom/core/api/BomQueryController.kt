package com.nu.bom.core.api

import com.nu.bom.core.api.dtos.ManufacturingDto
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.toMongoID
import com.nu.bom.core.service.BomNodeAccessInfo
import com.nu.bom.core.service.InvestmentTabService
import com.nu.bom.core.user.AccessCheckProvider
import com.tset.core.service.domain.Currency
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Mono

/**
 * This a specific controller for the invest page.
 * Instead of querying a singular bom entry, this endpoints works on the complete BOM, be careful when using those.
 * They have been designed as one offs to fulfill some customer criteria. Please do not build on top of them!
 */
@RestController
@RequestMapping("/api/bom/filter")
class BomQueryController(
    private val accessCheckProvider: AccessCheckProvider,
    private val investmentTabService: InvestmentTabService,
) {
    @GetMapping("/man/{bomNodeId}")
    fun getManufacturing(
        @AuthenticationPrincipal jwt: Jwt?,
        @RequestParam branch: String,
        @RequestParam entityType: Entities,
        @PathVariable bomNodeId: String,
        @RequestParam(required = false) ccy: List<Currency>?,
    ): Mono<List<ManufacturingDto>> =
        accessCheckProvider.doAs(jwt) { accessCheck ->
            val bomNodeAccessInfo =
                BomNodeAccessInfo(
                    accessCheck,
                    BomNodeId(bomNodeId),
                    branch.toMongoID(),
                )
            investmentTabService.subManufacturingsForInvestTab(bomNodeAccessInfo, entityType, ccy)
        }
}
