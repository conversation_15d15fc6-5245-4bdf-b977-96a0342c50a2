package com.nu.bom.core.manufacturing.commercialcalculation.fieldnamebuilders

import com.nu.bom.core.manufacturing.commercialcalculation.common.AggregationLevelAndRole
import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationRole
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.ElementConfiguration

/**
 *  Build a field name for native engine
 *  #ValueType_AggregationLevel_Role_ElementType_[Base|Rate]
 */
open class ValueFieldNameBuilder(
    valueType: ValueType,
    aggregationLevel: AggregationLevel,
    aggregationRole: AggregationRole = AggregationRole.THIS,
    val elementTypeKey: String,
    val extensionName: ExtensionName? = null,
) : FieldNameBuilderLevelRole(valueType, aggregationLevel, aggregationRole) {
    constructor(
        valueType: ValueType,
        aggregation: AggregationLevelAndRole,
        elementTypeKey: String,
        extensionName: ExtensionName? = null,
    ) : this(
        valueType,
        aggregation.level,
        aggregation.role,
        elementTypeKey,
        extensionName,
    )

    enum class ExtensionName(override val fieldNameSection: String) : FieldNameSection {
        BASE("Base"),
    }

    companion object {
        fun create(composedFieldName: String): ValueFieldNameBuilder {
            return checkNotNull(decomposeOrNull(composedFieldName)) {
                "Invalid field name format: $composedFieldName"
            }
        }

        fun decomposeOrNull(composedFieldName: String): ValueFieldNameBuilder? {
            val numberOfRequiredAdditionalNames = 1 // elementTypeKey
            val numberOfNullableAdditionalNames = 1 // extensionName
            return decomposeOrNull(
                composedFieldName,
                numberOfRequiredAdditionalNames,
                numberOfNullableAdditionalNames,
            )?.let { result ->
                val elementTypeKey = result.nonProcessedNames[0]
                if (result.nonProcessedNames.size == numberOfRequiredAdditionalNames) {
                    ValueFieldNameBuilder(
                        result.valueType,
                        result.aggregationLevel,
                        result.aggregationRole,
                        elementTypeKey,
                        null,
                    )
                } else {
                    val extension = result.nonProcessedNames[1].asEnumOrNull<ExtensionName>()
                    if (extension != null) {
                        ValueFieldNameBuilder(
                            result.valueType,
                            result.aggregationLevel,
                            result.aggregationRole,
                            elementTypeKey,
                            extension,
                        )
                    } else {
                        null
                    }
                }
            }
        }
    }

    override fun getNameParts(): List<String?> = super.getNameParts() + listOf(elementTypeKey, extensionName?.fieldNameSection)

    override fun getDisplayName(elementConfiguration: ElementConfiguration): String {
        val prefix =
            when (extensionName) {
                ExtensionName.BASE -> "Base for "
                null -> ""
            }
        return prefix +
            elementConfiguration.getDisplayableNames(elementTypeKey).longName.replaceFirstChar {
                if (prefix.isEmpty()) it.uppercase() else it.lowercase()
            }
    }
}
