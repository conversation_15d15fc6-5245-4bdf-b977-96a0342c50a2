package com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.toplevel

import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.legacyfieldnames.BaseToolLegacyFieldNames
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.InternalCommercialCalculationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.TopLevelUiCardBuilder
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.helper.EntityCardBuilderHelper.createSimpleEntityTable
import com.nu.bom.core.manufacturing.entities.BaseEntityFields
import com.nu.bom.core.manufacturing.entities.BaseTool
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.extension.ToolCO2Extension
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.CardIdentifier
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.FieldName
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.TableOption
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityLocator
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableActionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EqualCriteria
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.LocatorType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.options.ColumnOptionsFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.tableactiondefinitions.TrivialTableActionDefinition
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service

@Service
@Profile("!test", "toolInvestmentEntityCardBuilder")
class ToolInvestmentEntityCardBuilder : TopLevelUiCardBuilder {
    override val cardIdentifier: CardIdentifier = "toolInvestEntityTable"

    companion object {
        private const val SIMPLE_TABLE_OPTION = "Simple"
    }

    override fun getTitles(vararg configs: InternalCommercialCalculationConfiguration) = ValueType.entries.associateWith { "Tools" }

    override fun getKpis(vararg configs: InternalCommercialCalculationConfiguration) = null

    override fun getCardFields(vararg configs: InternalCommercialCalculationConfiguration) = null

    override fun getTablesVariations(vararg configs: InternalCommercialCalculationConfiguration) =
        ValueType.entries.associateWith { listOf(SIMPLE_TABLE_OPTION) }

    override fun getTableConfig(
        valueType: ValueType,
        tableOption: TableOption,
        procurementType: ManufacturingType,
        vararg configs: InternalCommercialCalculationConfiguration,
    ): EntityTableConfigFeDto =
        createSimpleEntityTable(
            "tools",
            getColumns(valueType),
            EntityLocator(
                listOf(EqualCriteria("type", Entities.TOOL.name)),
                LocatorType.ANCESTOR,
            ),
            mapOf(
                EntityTableActionFeDto.DELETE to TrivialTableActionDefinition,
                EntityTableActionFeDto.OPEN_IN_NEW_TAB to TrivialTableActionDefinition,
                EntityTableActionFeDto.COPY_LINK_TO_CLIPBOARD to TrivialTableActionDefinition,
            ),
        )

    private fun getColumns(valueType: ValueType): Map<FieldName, ColumnOptionsFeDto?> =
        when (valueType) {
            ValueType.COST ->
                mapOf(
                    BaseEntityFields::parentStepRef.name to ColumnOptionsFeDto(editable = false),
                    BaseTool::quantity.name to null,
                    BaseTool::investPerTool.name to ColumnOptionsFeDto(hasTotal = true),
                    BaseTool::invest.name to ColumnOptionsFeDto(hasTotal = true),
                    BaseTool::baseCurrency.name to null,
                    BaseTool::allocationRatio.name to null,
                    BaseToolLegacyFieldNames.ALLOCATED_COST_PER_PART.fieldName to ColumnOptionsFeDto(hasTotal = true),
                    BaseTool::allocatedInvestTotal.name to ColumnOptionsFeDto(hasTotal = true),
                    BaseTool::directlyPaidInvest.name to ColumnOptionsFeDto(hasTotal = true),
                )
            ValueType.CO2 ->
                mapOf(
                    BaseEntityFields::parentStepRef.name to ColumnOptionsFeDto(editable = false),
                    BaseTool::quantity.name to null,
                    ToolCO2Extension::cO2ToolTotal.name to null,
                    ToolCO2Extension::cO2Tool.name to ColumnOptionsFeDto(hasTotal = true),
                )
        }
}
