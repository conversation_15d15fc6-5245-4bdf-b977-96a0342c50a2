package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.manufacturing.annotations.Units
import java.math.BigDecimal

enum class TorqueUnits(override val baseFactor: BigDecimal) : TypeUnit {
    NEWTONMETER(BigDecimal.ONE),
    ;

    override val type = TypeUnits.TORQUE
    override val hideInDropdown = false
}

@Units(TorqueUnits::class)
class Torque(res: BigDecimal, unit: TorqueUnits) : NumericFieldResultWithUnit<Torque, TorqueUnits>(res, unit) {
    constructor(res: Double, unit: TorqueUnits) : this(res.toBigDecimal(), unit)
    constructor(res: String, unit: String) : this(BigDecimal(res), TorqueUnits.valueOf(unit))
    constructor(res: BigDecimal, unit: String) : this(res, TorqueUnits.valueOf(unit))
}
