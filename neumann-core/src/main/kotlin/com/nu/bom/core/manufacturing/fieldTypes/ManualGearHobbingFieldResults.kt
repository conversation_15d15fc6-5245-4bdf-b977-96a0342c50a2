package com.nu.bom.core.manufacturing.fieldTypes

import com.fasterxml.jackson.module.kotlin.readValue
import com.nu.bom.core.config.JacksonConfig
import com.nu.bom.core.manufacturing.fieldTypes.turn.GearHobInfo
import com.nu.bom.core.manufacturing.fieldTypes.turn.GearHobbingOperationInfoResponse
import com.nu.bom.core.manufacturing.fieldTypes.turn.GearHobbingToothingInfo
import com.nu.bom.core.manufacturing.fieldTypes.turn.GearingToothingType
import com.nu.bom.core.manufacturing.fieldTypes.turn.SingleCutRequest

class GearHobbingToothTypeFieldResult(
    res: GearingToothingType,
) : FieldResult<GearingToothingType, GearHobbingToothTypeFieldResult>(res) {
    override fun dbValue(): String = JacksonConfig.staticMapper.writeValueAsString(res)
    constructor(value: String) : this(JacksonConfig.staticMapper.readValue<GearingToothingType>(value))
}

class GearHobbingToothingInfoFieldResult(
    res: GearHobbingToothingInfo,
) : FieldResult<GearHobbingToothingInfo, GearHobbingToothingInfoFieldResult>(res) {
    override fun dbValue(): String = JacksonConfig.staticMapper.writeValueAsString(res)
    constructor(value: String) : this(JacksonConfig.staticMapper.readValue<GearHobbingToothingInfo>(value))
}

class GearHobInfoFieldResult(
    res: GearHobInfo,
) : FieldResult<GearHobInfo, GearHobInfoFieldResult>(res) {
    override fun dbValue(): String = JacksonConfig.staticMapper.writeValueAsString(res)
    constructor(value: String) : this(JacksonConfig.staticMapper.readValue<GearHobInfo>(value))
}

class SingleCutRequestFieldResult(
    res: SingleCutRequest,
) : FieldResult<SingleCutRequest, SingleCutRequestFieldResult>(res) {
    override fun dbValue(): String = JacksonConfig.staticMapper.writeValueAsString(res)
    constructor(value: String) : this(JacksonConfig.staticMapper.readValue<SingleCutRequest>(value))
}

class GearHobbingOperationInfoResponseFieldResult(
    res: GearHobbingOperationInfoResponse,
) : FieldResult<GearHobbingOperationInfoResponse, GearHobbingOperationInfoResponseFieldResult>(res) {
    override fun dbValue(): String = JacksonConfig.staticMapper.writeValueAsString(res)
    constructor(value: String) : this(JacksonConfig.staticMapper.readValue<GearHobbingOperationInfoResponse>(value))
}
