package com.nu.bom.core.turn

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import org.springframework.stereotype.Service

@Service
class TurningMarginService {
    companion object {
        fun margin(
            model: Model
        ): Length = when (model) {
            Model.DFOR -> Length(3.0, LengthUnits.MILLIMETER)
            Model.AFOR -> Length(3.0, LengthUnits.MILLIMETER)
            Model.DFORT -> Length(3.0, LengthUnits.MILLIMETER) // for compatibility with old calculations only
            Model.WHAT -> Length(1.5, LengthUnits.MILLIMETER)
            Model.CHAT -> Length(0.5, LengthUnits.MILLIMETER)
            Model.CEXT -> Length(1.0, LengthUnits.MILLIMETER)
            Model.CROL -> Length(2.0, LengthUnits.MILLIMETER)
            Model.DCA -> Length(0.5, LengthUnits.MILLIMETER)
            Model.RROL -> Length(2.0, LengthUnits.MILLIMETER)
            Model.CHILL -> Length(1.0, LengthUnits.MILLIMETER)
            Model.PREC -> Length(0.3, LengthUnits.MILLIMETER)
            Model.VPREC -> Length(0.3, LengthUnits.MILLIMETER)
            Model.SAND -> Length(3.0, LengthUnits.MILLIMETER)
            Model.RSWA -> Length(0.5, LengthUnits.MILLIMETER)
            else -> throw NotImplementedError("No margin width prefill defined yet for ${model.path}.")
        }
    }
}
