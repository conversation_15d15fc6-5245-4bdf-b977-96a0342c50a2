package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.fieldnamebuilders.RateTimeFieldNameBuilder
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCO2eCalculationElementType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCostCalculationElementType
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import com.nu.bom.core.utils.filterNotNull
import org.springframework.stereotype.Service

@Service
class LegacyManufacturingStepRatesMigration : ManufacturingModelEntityMapper {
    override val changeSetId = MigrationChangeSetId("2024-12-30-legacy-manufacturing-step-rates")

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity {
        return entity.copyAll(
            initialFieldWithResults = entity.initialFieldWithResults + getNewFields(entity.initialFieldWithResults),
            fieldWithResults = entity.fieldWithResults + getNewFields(entity.fieldWithResults),
        )
    }

    private fun getNewFields(fields: Map<String, FieldResultModel>): Map<String, FieldResultModel> =
        mapOf(
            RateTimeFieldNameBuilder(
                ValueType.COST,
                TsetCostCalculationElementType.MANUFACTURING_OVERHEAD_COSTS.fieldName,
                RateTimeFieldNameBuilder.ExtensionName.RATE,
            ).fieldName to "RMOCRate",
            RateTimeFieldNameBuilder(
                ValueType.CO2,
                TsetCO2eCalculationElementType.MANUFACTURING_OVERHEAD_CO2E.fieldName,
                RateTimeFieldNameBuilder.ExtensionName.RATE,
            ).fieldName to "cO2RMOCRate",
        ).mapValues { (_, oldName) ->
            val oldField = fields[oldName]
            if (oldField?.source == FieldResult.SOURCE.I.name) {
                oldField
            } else {
                null
            }
        }.filterNotNull()
}
