package com.nu.bom.core.manufacturing.fieldTypes

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.databind.deser.std.StdDeserializer
import com.fasterxml.jackson.module.kotlin.readValue
import com.nu.bom.core.config.JacksonConfig
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.fieldTypes.masterdata.FieldDefinition
import com.nu.bom.core.manufacturing.service.behaviour.ConfigBasedDynamicBehaviour
import com.nu.bom.core.manufacturing.service.behaviour.EntityBasedDynamicBehaviour
import com.nu.bom.core.manufacturing.service.behaviour.GeneralDynamicBehaviour
import com.nu.bom.core.model.configurations.ConfigId
import com.nu.bom.core.model.configurations.SemanticVersion
import com.nu.bom.core.service.configurations.ConfigurationService
import kotlin.reflect.full.isSubclassOf
import kotlin.reflect.full.primaryConstructor

class DynamicBehaviourGeneration(res: DynamicBehaviourTypeMapping) :
    FieldResult<DynamicBehaviourGeneration.DynamicBehaviourTypeMapping, DynamicBehaviourGeneration>(res) {
    override fun dbValue(): String {
        return JacksonConfig.staticMapper.writeValueAsString(res)
    }

    enum class DynamicBehaviourType {
        EntityBased,
        ConfigBased,
        General,
    }

    fun createConfigBasedBehaviour(entityForFieldInjection: ManufacturingEntity): ConfigBasedDynamicBehaviour {
        val behaviourImplementationClassName = res.behaviourImplementationClassName
        val kClass = Class.forName(behaviourImplementationClassName).kotlin

        val configurationIdentifier =
            checkNotNull(res.configurationIdentifier) {
                "Need config Identifier for Config Based Dynamic Behaviour: ${res.behaviourImplementationClassName}"
            }
        // create behaviour
        return kClass.primaryConstructor!!.call(
            entityForFieldInjection,
            configurationIdentifier,
        ) as ConfigBasedDynamicBehaviour
    }

    fun createGeneralBasedBehaviour(entityForFieldInjection: ManufacturingEntity): GeneralDynamicBehaviour {
        val behaviourImplementationClassName = res.behaviourImplementationClassName
        val kClass = Class.forName(behaviourImplementationClassName).kotlin

        // create behaviour
        return kClass.primaryConstructor!!.call(
            entityForFieldInjection,
            this.res.dynamicBehaviourInfo,
        ) as GeneralDynamicBehaviour
    }

    /**
     * if you add a field here, please check [DynamicBehaviourTypeMapping.Deserializer] to correctly
     * deserialize the field
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonDeserialize(using = DynamicBehaviourTypeMapping.Deserializer::class)
    data class DynamicBehaviourTypeMapping(
        val hostType: String,
        val behaviourType: String,
        val behaviourImplementationClassName: String,
        val configurationIdentifier: ConfigId?,
        @JsonInclude(JsonInclude.Include.NON_NULL)
        val dynamicBehaviourInfo: DynamicBehaviourInfo? = null,
    ) {
        fun dynamicBehaviourType(): DynamicBehaviourType {
            val clazz = Class.forName(behaviourImplementationClassName).kotlin

            return when {
                clazz.isSubclassOf(EntityBasedDynamicBehaviour::class) -> DynamicBehaviourType.EntityBased
                clazz.isSubclassOf(ConfigBasedDynamicBehaviour::class) -> DynamicBehaviourType.ConfigBased
                clazz.isSubclassOf(GeneralDynamicBehaviour::class) -> DynamicBehaviourType.General
                else -> throw InternalError("Behaviour of class $clazz is not supported!")
            }
        }

        /**
         * this custom deserializer exists so that legacy fields can be deserialized as well
         * (changed configurationIdentifier type)
         */
        class Deserializer : StdDeserializer<DynamicBehaviourTypeMapping>(DynamicBehaviourTypeMapping::class.java) {
            override fun deserialize(
                parser: JsonParser,
                ctx: DeserializationContext,
            ): DynamicBehaviourTypeMapping {
                val tree = parser.readValueAsTree<JsonNode>()
                val configId =
                    tree[DynamicBehaviourTypeMapping::configurationIdentifier.name]
                        ?.takeUnless { it.isNull }
                        ?.let { configIdNode ->
                            if (configIdNode.has(Legacy::isTestConfiguration.name)) {
                                val legacy = ctx.readTreeAsValue(configIdNode, Legacy::class.java)
                                legacy.migrate()
                            } else {
                                ctx.readTreeAsValue(configIdNode, ConfigId::class.java)
                            }
                        }

                val dynamicBehaviourInfo =
                    tree[DynamicBehaviourTypeMapping::dynamicBehaviourInfo.name]
                        ?.takeUnless { it.isNull }
                        ?.let { behaviourInfoNode ->
                            ctx.readTreeAsValue(behaviourInfoNode, DynamicBehaviourInfo::class.java)
                        }

                return DynamicBehaviourTypeMapping(
                    tree[DynamicBehaviourTypeMapping::hostType.name].textValue(),
                    tree[DynamicBehaviourTypeMapping::behaviourType.name].textValue(),
                    tree[DynamicBehaviourTypeMapping::behaviourImplementationClassName.name].textValue(),
                    configId,
                    dynamicBehaviourInfo,
                )
            }
        }

        private data class Legacy(
            val groupKey: String,
            val typeKey: String,
            val key: String,
            val version: Int,
            // sic! This took me > 2 hours
            val isTestConfiguration: Boolean,
        ) {
            fun migrate(): ConfigId =
                if (isTestConfiguration) {
                    ConfigId(groupKey, typeKey, ConfigurationService.TSET_KEY_PREFIX + key, SemanticVersion.initialVersion())
                } else {
                    ConfigId(groupKey, typeKey, key, SemanticVersion(version + 1, 0))
                }
        }
    }

    // This constructor is called by reflection when the field is build, so it is used
    @Suppress("unused")
    constructor(value: String) : this(JacksonConfig.staticMapper.readValue<DynamicBehaviourTypeMapping>(value))
}

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "@type")
sealed interface DynamicBehaviourInfo

@JsonTypeName("classificationFields")
data class ClassificationFieldsDynamicBehaviourInfo(
    val hostClassName: String,
    val classificationTypeKey: String,
    val classificationKeys: List<String>,
    val objectView: String,
    val startIndex: Int? = null,
) : DynamicBehaviourInfo

@JsonTypeName("headerFields")
data class HeaderFieldsDynamicBehaviourInfo(
    val hostClassName: String,
    val fieldDefinitions: List<FieldDefinition>,
    val includeObjectView: Boolean,
) : DynamicBehaviourInfo
