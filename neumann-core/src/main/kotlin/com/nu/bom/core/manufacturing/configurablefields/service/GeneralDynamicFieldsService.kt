package com.nu.bom.core.manufacturing.configurablefields.service

import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.fieldTypes.DynamicBehaviourGeneration
import com.nu.bom.core.manufacturing.fieldTypes.DynamicBehaviourInfo
import com.nu.bom.core.manufacturing.service.behaviour.DynamicBehaviour
import com.nu.bom.core.manufacturing.service.behaviour.DynamicFieldBuilder
import com.nu.bom.core.manufacturing.service.behaviour.GeneralDynamicBehaviour
import com.nu.bom.core.manufacturing.service.behaviour.GeneralDynamicFieldBuilder
import com.nu.bom.core.service.cache.RefreshableAsyncNbkCache
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.FieldInfo
import com.nu.http.EnvironmentNameSupplier
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import reactor.core.scheduler.Schedulers
import java.util.concurrent.Executor
import kotlin.reflect.full.isSubclassOf

private typealias GeneralDynamicFieldsServiceCache =
    RefreshableAsyncNbkCache<DynamicFieldCacheKey<GeneralDynamicFieldsService.CacheKey>, DynamicFieldCacheEntry>

// unfortunately due to legacy and non-legacy access functions this class is quite large
// to avoid combing refactor and new functionality, we leave it a large interface for now
@Suppress("TooManyFunctions")
@EnableConfigurationProperties(value = [ DynamicFieldsProperties::class ])
@Service
class GeneralDynamicFieldsService(
    private val dynamicFieldBuilders: List<GeneralDynamicFieldBuilder<*>>,
    entityManager: EntityManager,
    environmentNameSupplier: EnvironmentNameSupplier,
    properties: DynamicFieldsProperties,
) : CachedDynamicFieldsHandler<GeneralDynamicFieldsService.CacheKey, GeneralDynamicFieldsServiceCache>(
        entityManager,
        environmentNameSupplier,
        RefreshableAsyncNbkCache(properties.refreshAfter, properties.expireAfterAccess),
    ) {
    data class CacheKey(
        val behaviourType: String,
        val behaviourInfo: DynamicBehaviourInfo,
    ) {
        companion object {
            fun fromDynamicBehaviour(dynamicBehaviour: GeneralDynamicBehaviour) =
                CacheKey(dynamicBehaviour.behaviourType(), dynamicBehaviour.dynamicBehaviourInfo())
        }
    }

    override fun canHandleDynamicBehaviour(dynamicBehaviour: DynamicBehaviour): Boolean {
        return dynamicBehaviour::class.isSubclassOf(GeneralDynamicBehaviour::class)
    }

    override fun canHandleDynamicBehaviour(dynamicBehaviourGeneration: DynamicBehaviourGeneration): Boolean {
        return dynamicBehaviourGeneration.res.dynamicBehaviourType() == DynamicBehaviourGeneration.DynamicBehaviourType.General
    }

    override fun createAndCacheDynamicEntityFieldModelsAndInfos(
        accessCheck: AccessCheck,
        dynamicBehaviour: DynamicBehaviour,
    ): Mono<EntityFieldModelsWithFieldInfo> {
        if (dynamicBehaviour is GeneralDynamicBehaviour) {
            return getOrCreateBuilderConfig(accessCheck, getFieldBuilder(dynamicBehaviour), dynamicBehaviour)
        }
        throw IllegalArgumentException("illegal dynamicBehaviour class ${dynamicBehaviour::class.simpleName}")
    }

    @Deprecated("Always returns empty map since no fields exist in default config.", ReplaceWith("reactive method"))
    override fun getDynamicFieldInfo(
        dynamicBehaviourGeneration: DynamicBehaviourGeneration,
        entity: ManufacturingEntity,
    ): Map<String, FieldInfo> = emptyMap()

    @Deprecated("Always returns null since no fields exist in default config.", ReplaceWith("reactive method"))
    override fun getDynamicFieldInfo(
        dynamicBehaviourGeneration: DynamicBehaviourGeneration,
        entity: ManufacturingEntity,
        fieldName: String,
    ): FieldInfo? = null

    @Deprecated("Always returns empty map since no fields exist in default config.", ReplaceWith("reactive method"))
    override fun getDynamicMetaData(
        dynamicBehaviourGeneration: DynamicBehaviourGeneration,
        entity: ManufacturingEntity,
    ): Map<String, Map<String, Any>> = emptyMap()

    @Deprecated("Always returns empty map since no fields exist in default config.", ReplaceWith("reactive method"))
    override fun getDynamicMetaData(
        dynamicBehaviourGeneration: DynamicBehaviourGeneration,
        entity: ManufacturingEntity,
        fieldName: String,
    ): Map<String, Any> = emptyMap()

    fun getDynamicCachedFieldModelsAndInfos(
        accessCheck: AccessCheck,
        dynamicBehaviour: GeneralDynamicBehaviour,
    ): EntityFieldModelsWithFieldInfo {
        return checkNotNull(
            getEntityFieldModelsWithFieldInfosIfCached(
                accessCheck,
                CacheKey.fromDynamicBehaviour(dynamicBehaviour),
            ),
        ) {
            "No cached field model available for configuration $dynamicBehaviour"
        }
    }

    override fun <T : Any> getOrCreateBuilderConfig(
        accessCheck: AccessCheck,
        builder: DynamicFieldBuilder<T>,
        dynamicBehaviour: DynamicBehaviour,
    ): Mono<EntityFieldModelsWithFieldInfo> {
        require(dynamicBehaviour is GeneralDynamicBehaviour)
        require(builder is GeneralDynamicFieldBuilder<T>)

        val cacheKey = buildCacheKey(accessCheck, CacheKey.fromDynamicBehaviour(dynamicBehaviour))

        return dynamicFieldModelCache.getOrFetchAsync(cacheKey) { _, exec ->
            fetchNewAsFuture(builder, accessCheck, dynamicBehaviour, exec)
        }
            .let { Mono.fromFuture(it, true) }
            .map { it.fieldModel }
    }

    private fun <T : Any> fetchNewAsFuture(
        builder: GeneralDynamicFieldBuilder<T>,
        accessCheck: AccessCheck,
        dynamicBehaviour: GeneralDynamicBehaviour,
        executor: Executor,
    ) = builder
        .getConfiguration(accessCheck, dynamicBehaviour)
        .map { createCacheEntry(it, builder) }
        .subscribeOn(Schedulers.fromExecutor(executor))
        .contextCapture()
        .toFuture()

    override fun getFieldBuilder(dynamicBehaviour: DynamicBehaviour): GeneralDynamicFieldBuilder<*> {
        val fieldBuildersForConfiguration = dynamicFieldBuilders.filter { it.canBuildDynamicFields(dynamicBehaviour) }

        return checkNotNull(fieldBuildersForConfiguration.singleOrNull()) {
            if (fieldBuildersForConfiguration.isEmpty()) {
                "There is no field builder registered for the GeneralDynamicBehaviour: $dynamicBehaviour!"
            } else {
                "More than one field builders are registered for the GeneralDynamicBehaviour $dynamicBehaviour!"
            }
        }
    }
}
