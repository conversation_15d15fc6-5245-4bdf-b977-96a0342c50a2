package com.nu.bom.core.api

import com.nu.bom.core.service.CycleTimeStepService
import com.nu.bom.core.user.AccessCheckProvider
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Flux

@RestController
@RequestMapping("/api/projects/{projectId}/calculations/{bomNodeId}/{branchId}/cycleTimeSteps")
class CycleTimeStepController(
    private val accessCheckProvider: AccessCheckProvider,
    private val cycleTimeStepService: CycleTimeStepService,
) {
    @GetMapping("")
    fun getClassesForType(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable projectId: String,
        @PathVariable bomNodeId: String,
        @PathVariable branchId: String,
        @RequestParam parentId: String
    ): Flux<CycleTimeStepService.AvailableCycleTimeSteps> {
        return accessCheckProvider.doAsReturnMany(jwt) { accessCheck ->
            cycleTimeStepService.getAvailableCts(accessCheck, bomNodeId, branchId, parentId)
        }
    }
}
