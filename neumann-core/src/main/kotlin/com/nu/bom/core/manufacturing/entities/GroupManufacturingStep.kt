package com.nu.bom.core.manufacturing.entities

import com.nu.bom.core.manufacturing.annotations.Children
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Nocalc
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeGroupTimeInfo
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeGroupType
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeGrouping
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeUnit
import java.math.BigDecimal

@EntityType(Entities.MANUFACTURING_STEP)
class GroupManufacturingStep(name: String) : ManufacturingEntity(name) {
    override val extends = ManufacturingStep(name)

    @Input
    fun grouping(): CycleTimeGrouping = CycleTimeGrouping.Sequential

    fun internalCycleTime(
        @Children(Entities.CYCLETIME_STEP_GROUP)
        timeInfo: List<CycleTimeGroupTimeInfo>?,
        grouping: CycleTimeGrouping
    ): CycleTime {
        return when (grouping.res) {
            CycleTimeGrouping.Selection.PARALLEL -> {
                val processingCycleTimes = getCycleTimes(timeInfo, CycleTimeGroupType.Processing.res.name)
                val positioningCycleTimes = getCycleTimes(timeInfo, CycleTimeGroupType.Positioning.res.name)
                val machiningCycleTimes = getCycleTimes(timeInfo, CycleTimeGroupType.Machining.res.name)

                val maxProcessing = max(processingCycleTimes, CycleTime(BigDecimal.ZERO, CycleTimeUnit.SECOND))
                val maxPositioning = max(positioningCycleTimes, CycleTime(BigDecimal.ZERO, CycleTimeUnit.SECOND))
                val maxMachining = max(machiningCycleTimes, CycleTime(BigDecimal.ZERO, CycleTimeUnit.SECOND))

                max(
                    listOf(maxProcessing, maxMachining),
                    CycleTime(BigDecimal.ZERO, CycleTimeUnit.SECOND)
                ) + maxPositioning
            }
            CycleTimeGrouping.Selection.SEQUENTIAL -> {
                sum(
                    timeInfo?.map { CycleTime(it.res.data.time_s, CycleTimeUnit.SECOND) },
                    default = CycleTime(BigDecimal.ZERO, CycleTimeUnit.SECOND)
                )
            }
        }
    }

    @Nocalc
    private fun getCycleTimes(timeInfo: List<CycleTimeGroupTimeInfo>?, type: String) =
        timeInfo?.filter { it.res.data.type_selection == type }
            ?.map { CycleTime(it.res.data.time_s, CycleTimeUnit.SECOND) }
}
