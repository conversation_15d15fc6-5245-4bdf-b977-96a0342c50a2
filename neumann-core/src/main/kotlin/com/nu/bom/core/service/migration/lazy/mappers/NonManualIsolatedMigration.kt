package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import org.springframework.stereotype.Service
import java.util.Deque
import java.util.LinkedList

@Service
class NonManualIsolatedMigration : ManufacturingModelEntityMapper {
    override val changeSetId =
        MigrationChangeSetId("2024-05-15-only-manual-manufacturing-should-have-isolated-children")
    private val affectedClasses = setOf("ManufacturingInjection2", "ManufacturingSintering")

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity =
        when {
            entity.clazz == ManualManufacturing::class.simpleName -> entity
            entity.clazz in affectedClasses && containsIsolate(LinkedList(listOf(entity))) ->
                entity.copy(children = migrateChildren(entity.children))

            else -> entity
        }

    private tailrec fun containsIsolate(entities: Deque<ManufacturingModelEntity>): Boolean {
        if (entities.isEmpty()) return false
        val current = entities.removeFirst()

        if (current.isolated == true) return true

        entities.addAll(current.children)
        return containsIsolate(entities)
    }

    private fun migrateChildren(manufacturingModelEnities: List<ManufacturingModelEntity>): List<ManufacturingModelEntity> =
        manufacturingModelEnities.map {
            it.copy(isolated = false, children = migrateChildren(it.children))
        }
}
