package com.nu.bom.core.service

import java.io.ByteArrayOutputStream
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream

// TODO VZ: move to the nu-lib/util package
class ZipArchiveCreator : AutoCloseable {
    private val bos = ByteArrayOutputStream()
    private val zipOut = ZipOutputStream(bos)

    fun addFile(
        fileName: String,
        fileContent: (ZipOutputStream) -> Unit
    ) {
        zipOut.putNextEntry(ZipEntry(fileName))
        fileContent(zipOut)
        zipOut.flush()
    }
    fun addFile(
        fileName: String,
        fileContent: ByteArray
    ) {
        zipOut.putNextEntry(ZipEntry(fileName))
        zipOut.write(fileContent)
        zipOut.flush()
    }

    override fun close() {
        zipOut.close()
    }

    fun getZipArchiveByteArray(): ByteArray {
        return bos.toByteArray()
    }
}
