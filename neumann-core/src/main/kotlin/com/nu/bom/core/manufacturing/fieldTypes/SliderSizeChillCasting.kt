package com.nu.bom.core.manufacturing.fieldTypes

class SliderSizeChillCasting(res: Selection) : SelectEnumFieldResult<SliderSizeChillCasting.Selection, SliderSizeChillCasting>(res) {

    enum class Selection(val intValue: Int) {
        NO_SLIDER(0),
        VERY_SMALL(1),
        SMALL(2),
        MEDIUM(3),
        LARGE(4),
        VERY_LARGE(5)
    }

    companion object {
        fun valueOf(intValue: Int): SliderSizeChillCasting {
            return Selection.values().find {
                it.intValue == intValue
            }?.let { SliderSizeChillCasting(it) } ?: throw IllegalArgumentException("intValue=$intValue is not a valid enum value")
        }
    }
}
