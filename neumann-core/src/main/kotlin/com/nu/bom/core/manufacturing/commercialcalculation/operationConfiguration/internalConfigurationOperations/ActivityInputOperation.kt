package com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations

import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import java.math.BigDecimal

data class ActivityInputOperation(
    override val destinationElementKey: String,
    override val origin: AggregationLevel,
    override val subElements: Map<String, InputSubElement>,
    val variableCostFractionForSetupMachine: BigDecimal? = null
) : InputWithSubElementsOperation
