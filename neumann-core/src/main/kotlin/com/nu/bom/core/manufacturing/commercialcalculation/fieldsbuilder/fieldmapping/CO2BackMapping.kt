package com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.fieldmapping

import com.nu.bom.core.manufacturing.annotations.entityfieldmetainfo.EntityFieldMetaInfo
import com.nu.bom.core.manufacturing.commercialcalculation.common.AggregationLevelAndRole
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetActivityCalculationElementType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCO2eCalculationElementType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCalculationElementType
import com.nu.bom.core.manufacturing.enums.Entities

open class CO2BackMapping(
    targetFieldName: String,
    fieldNameBuilderInputs: List<FieldNameBuilderInput>,
    metaInfo: List<EntityFieldMetaInfo>,
    parentTarget: Entities? = null,
) : BackMapping(
        targetFieldName,
        ValueType.CO2,
        fieldNameBuilderInputs,
        metaInfo,
        parentTarget = parentTarget,
    ) {
    constructor(
        targetFieldName: String,
        aggregation: AggregationLevelAndRole,
        elementType: TsetCalculationElementType,
        metaInfo: List<EntityFieldMetaInfo>,
        parentTarget: Entities? = null,
    ) : this(
        targetFieldName,
        listOf(ValueFieldNameBuilderInput(aggregation.level, aggregation.role, elementType.fieldName)),
        metaInfo,
        parentTarget,
    )

    constructor(
        targetFieldName: String,
        aggregation: AggregationLevelAndRole,
        elementTypeKeys: List<TsetCO2eCalculationElementType>,
        metaInfo: List<EntityFieldMetaInfo>,
        parentTarget: Entities? = null,
    ) : this(
        targetFieldName,
        elementTypeKeys.map { ValueFieldNameBuilderInput(aggregation.level, aggregation.role, it.fieldName) },
        metaInfo,
        parentTarget,
    )

    constructor(
        targetFieldName: String,
        aggregation: AggregationLevelAndRole,
        activityTypeKey: TsetActivityCalculationElementType,
        elementTypeKeys: List<TsetCO2eCalculationElementType>,
        metaInfo: List<EntityFieldMetaInfo>,
        parentTarget: Entities? = null,
    ) : this(
        targetFieldName,
        elementTypeKeys.map {
            ActivityFieldNameBuilderInput(aggregation.level, aggregation.role, activityTypeKey.fieldName, it.fieldName)
        },
        metaInfo,
        parentTarget,
    )
}
