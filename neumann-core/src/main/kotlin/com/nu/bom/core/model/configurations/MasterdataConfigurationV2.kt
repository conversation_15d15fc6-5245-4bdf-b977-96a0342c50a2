package com.nu.bom.core.model.configurations

import com.nu.bom.core.model.configurations.masterdata.EffectivityDefinition
import com.nu.bom.core.model.configurations.masterdata.LookupConfiguration
import com.nu.bom.core.model.configurations.masterdata.OverheadMethodConfiguration
import com.nu.bom.core.service.configurations.MasterdataTsetConfigurationService

open class MasterdataConfigurationV2(
    effectivityDefinitions: List<EffectivityDefinition>,
    lookupConfigurations: List<LookupConfiguration>,
    overheadMethodConfiguration: OverheadMethodConfiguration,
) : MasterdataConfiguration(effectivityDefinitions, lookupConfigurations, overheadMethodConfiguration) {
    override val overheadMethodConfiguration: OverheadMethodConfiguration
        get() = super.overheadMethodConfiguration ?: MasterdataTsetConfigurationService.overheadMethodConfiguration

    override fun equals(other: Any?): <PERSON><PERSON>an {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        return contentEquals(other as MasterdataConfigurationV2)
    }

    override fun hashCode(): Int {
        return super.hashCode()
    }
}
