package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.utils.toUpperSnakeCase

class StepSubTypeSandFettling(res: Selection) : SelectEnumFieldResult<StepSubTypeSandFettling.Selection, StepSubTypeSandFettling>(res) {

    enum class Selection {
        AUTOMATED,
        MANU<PERSON>,
        NO_FETTLING
    }

    companion object {
        val NO_FETTLING = StepSubTypeSandFettling(Selection.NO_FETTLING)
        val AUTOMATED = StepSubTypeSandFettling(Selection.AUTOMATED)
        val MANUAL = StepSubTypeSandFettling(Selection.MANUAL)

        fun valueOf(name: String): StepSubTypeSandFettling {
            return StepSubTypeSandFettling(Selection.valueOf(name.toUpperSnakeCase()))
        }
    }
}
