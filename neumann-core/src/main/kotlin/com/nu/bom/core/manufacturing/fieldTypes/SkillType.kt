package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.manufacturing.annotations.Nocalc
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.service.CalculationContextService
import com.nu.bom.core.manufacturing.utils.CalculationContextServiceImplReach
import com.nu.bom.core.model.configurations.CurrentMasterdataConfiguration
import com.nu.bom.core.model.configurations.masterdata.SkillTypeConfiguration
import com.nu.bom.core.service.masterdata.MdBasicDataService
import com.nu.bom.core.user.AccessCheck
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import reactor.core.publisher.Mono

fun TsetDefaultSkillType.mdKeyAsText() = Text(mdKey)

enum class TsetDefaultSkillType(
    val mdKey: String,
    val translation: String,
) {
    UNSKILLED_WORKER("tset.ref.lov-entry.unskilled-worker", "Unskilled worker"),
    SKILLED_WORKER("tset.ref.lov-entry.skilled-worker", "Skilled worker"),
    SETUP_TECHNICIAN("tset.ref.lov-entry.setup-technician", "Setup technician"),
    PRODUCTION_SUPERVISOR("tset.ref.lov-entry.production-supervisor", "Production supervisor"),
    INSPECTOR("tset.ref.lov-entry.inspector", "Inspector"),
    PRODUCTION_ENGINEER("tset.ref.lov-entry.production-engineer", "Production engineer"),
    FINANCIAL_ACCOUNTANT("tset.ref.lov-entry.average", "Financial accountant"),
    ADMINISTRATION_PROFESSIONAL("tset.ref.lov-entry.administration-professional", "Administration professional"),
    MIDDLE_MANAGEMENT("tset.ref.lov-entry.middle-management", "Middle management"),
    TECHNICAL_MANAGER("tset.ref.lov-entry.technical-manager", "Technical manager"),
    CHIEF_EXECUTIVE_OFFICER("tset.ref.lov-entry.minimum", "Chief executive officer"),
}

const val SKILL_TYPE_PATH = "/api/md/v1/lovtypes/{field:skillTypeLovTypeKey}/entries"

object SkillTypeConsumerHelper {
    fun mapCostModuleSkillTypeToMdSkillType(
        costModuleSkillType: String,
        accessCheck: AccessCheck,
        services: CalculationContextService,
    ): Mono<String> =
        services.getDefaultMasterdataConfiguration(accessCheck).map {
            val mapping = it.skillTypeConfiguration.costModuleSkillTypeMapping
            val mdSkillType =
                when (costModuleSkillType) {
                    TsetDefaultSkillType.UNSKILLED_WORKER.name -> {
                        mapping.unskilledWorker
                    }
                    TsetDefaultSkillType.SKILLED_WORKER.name -> {
                        mapping.skilledWorker
                    }
                    TsetDefaultSkillType.SETUP_TECHNICIAN.name -> {
                        mapping.setupTechnician
                    }
                    TsetDefaultSkillType.PRODUCTION_SUPERVISOR.name -> {
                        mapping.productionSupervisor
                    }
                    TsetDefaultSkillType.INSPECTOR.name -> {
                        mapping.inspector
                    }
                    TsetDefaultSkillType.PRODUCTION_ENGINEER.name -> {
                        mapping.productionEngineer
                    }
                    TsetDefaultSkillType.FINANCIAL_ACCOUNTANT.name -> {
                        mapping.financialAccountant
                    }
                    TsetDefaultSkillType.ADMINISTRATION_PROFESSIONAL.name -> {
                        mapping.administrationProfessional
                    }
                    TsetDefaultSkillType.MIDDLE_MANAGEMENT.name -> {
                        mapping.middleManagement
                    }
                    TsetDefaultSkillType.TECHNICAL_MANAGER.name -> {
                        mapping.technicalManager
                    }
                    TsetDefaultSkillType.CHIEF_EXECUTIVE_OFFICER.name -> {
                        mapping.chiefExecutiveOfficer
                    }
                    else -> {
                        costModuleSkillType
                    }
                }
            mdSkillType
        }

    @Nocalc
    fun afterTemplateCreated(
        entity: ManufacturingEntity,
        calculationContextService: CalculationContextService,
        accessCheck: AccessCheck,
    ): Mono<ManufacturingEntity> {
        if (SkillTypeConsumer::class.java.isAssignableFrom(this::class.java) ||
            entity.extensionSet().any { SkillTypeConsumer::class.java.isAssignableFrom(it) }
        ) {
            val skillTypeField = entity.getInitialField(SkillTypeConsumer::skillType.name)
            val skillTypeFieldResult = skillTypeField?.result
            if (skillTypeFieldResult is Text) {
                return mapCostModuleSkillTypeToMdSkillType(
                    skillTypeFieldResult.res,
                    accessCheck,
                    calculationContextService,
                ).map { mdSkillType ->
                    entity.initialFieldsWithResults.removeIf { it.name.name == skillTypeField.name.name }
                    entity.addInitialFieldResult(skillTypeField.name.name) {
                        skillTypeFieldResult.withRes(mdSkillType)
                    }
                    entity
                }
            }
        }

        return Mono.just(entity)
    }
}

interface SkillTypeConsumer {
    fun skillType(masterdataConfigurationKey: MasterdataConfigurationKey): Mono<Text>

    fun skillTypeName(
        skillType: Text,
        mdBasicDataService: MdBasicDataService,
    ): Mono<Text>

    fun skillTypeLovTypeKey(masterdataConfigurationKey: MasterdataConfigurationKey?): Mono<Text>

    @Nocalc
    fun skillTypeLovTypeKeyImpl(
        masterdataConfigurationKey: MasterdataConfigurationKey?,
        services: CalculationContextServiceImplReach,
    ): Mono<Text> =
        Mono
            .justOrEmpty(masterdataConfigurationKey)
            .switchIfEmpty(services.getDefaultConfigurationKey(::MasterdataConfigurationKey))
            .flatMap { key ->
                requireNotNull(key) { "Default master data configuration is missing" }
                getSkillTypeConfiguration(key, services)
            }.map { Text(it.skillTypeLovTypeKey) }

    @Nocalc
    fun getSkillTypeConfiguration(
        masterdataConfigurationKey: MasterdataConfigurationKey,
        services: CalculationContextServiceImplReach,
    ): Mono<SkillTypeConfiguration> =
        services
            .getConfiguration(masterdataConfigurationKey)
            .map(CurrentMasterdataConfiguration::skillTypeConfiguration)

    @Nocalc
    fun getSkillTypeName(
        skillType: Text,
        mdBasicDataService: MdBasicDataService,
        services: CalculationContextServiceImplReach,
    ): Mono<Text> =
        mdBasicDataService
            .getLovEntry(services.accessCheck, SimpleKeyDto(skillType.res))
            .map { Text(it.name) }
            .switchIfEmpty(Mono.just(skillType))
            // not existing skillType error is swallowed here, to have a seamless export/import experience
            // even when skillType does not exist in target system
            .onErrorResume { Mono.just(skillType) }
}
