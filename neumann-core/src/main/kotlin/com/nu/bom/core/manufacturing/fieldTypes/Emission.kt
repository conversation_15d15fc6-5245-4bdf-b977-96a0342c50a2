package com.nu.bom.core.manufacturing.fieldTypes

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.nu.bom.core.manufacturing.annotations.Units
import java.math.BigDecimal

enum class EmissionUnits(override val baseFactor: BigDecimal) : TypeUnit {
    GRAM_CO2E(0.001.toBigDecimal()),
    MILLIGRAM_CO2E(0.000_001.toBigDecimal()),
    KILOGRAM_CO2E(BigDecimal.ONE),
    TON_CO2E(1000.toBigDecimal()),
    ;

    override val hideInDropdown = false
    override val type = TypeUnits.CO2
}

@Units(EmissionUnits::class)
@JsonIgnoreProperties(value = ["inGramCO2e", "inKilogramCO2e"])
class Emission constructor(res: BigDecimal, unit: EmissionUnits) :
    NumericFieldResultWithUnit<Emission, EmissionUnits>(res, unit) {
        constructor(res: Double, unit: EmissionUnits) : this(res.toBigDecimal(), unit)
        constructor(res: String, unit: String) : this(BigDecimal(res), EmissionUnits.valueOf(unit))
        constructor(res: BigDecimal, unit: String) : this(res, EmissionUnits.valueOf(unit))

        constructor(weight: Weight) : this(weight.res, EmissionUnits.KILOGRAM_CO2E)
        constructor(
            @Suppress("DEPRECATION")
            co2: CO2,
        ) : this(co2.res, EmissionUnits.KILOGRAM_CO2E)

        val inGramCO2e: BigDecimal
            get() = to(EmissionUnits.GRAM_CO2E)
        val inKilogramCO2e: BigDecimal
            get() = to(EmissionUnits.KILOGRAM_CO2E)

        companion object {
            val ZERO = Emission(BigDecimal.ZERO, EmissionUnits.KILOGRAM_CO2E)
        }
    }
