package com.nu.bom.core.manufacturing.extension

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.StaticDenominatorUnit
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.StaticUnitOverride
import com.nu.bom.core.manufacturing.fieldTypes.Emission

@EntityType(Entities.CO2_PROCESSING_MATERIAL)
class NaturalGasCO2MaterialProcessing(name: String) : ManufacturingEntity(name) {

    override val extends = BaseCO2MaterialProcessing(name)

    @Parent(Entities.MATERIAL)
    @StaticDenominatorUnit(StaticUnitOverride.CM)
    fun naturalGasEmissionDirect(): Emission? = null

    @Parent(Entities.MATERIAL)
    @StaticDenominatorUnit(StaticUnitOverride.CM)
    fun naturalGasEmissionInDirect(): Emission? = null

    @Input
    @StaticDenominatorUnit(StaticUnitOverride.CM)
    fun cO2Active(@Parent(Entities.MATERIAL) naturalGasEmissionDirect: Emission): Emission = naturalGasEmissionDirect

    @Input
    @StaticDenominatorUnit(StaticUnitOverride.CM)
    fun cO2Passive(@Parent(Entities.MATERIAL) naturalGasEmissionInDirect: Emission): Emission = naturalGasEmissionInDirect

    @StaticDenominatorUnit(StaticUnitOverride.CM)
    fun cO2PerUnit(
        cO2Active: Emission,
        cO2Passive: Emission
    ): Emission {
        return cO2Active + cO2Passive
    }
}
