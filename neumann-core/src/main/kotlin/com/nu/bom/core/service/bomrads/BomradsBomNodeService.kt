@file:Suppress("ktlint:standard:max-line-length")

package com.nu.bom.core.service.bomrads

import com.nu.bom.core.api.dtos.ReorderDirection
import com.nu.bom.core.exception.ErrorCodedException
import com.nu.bom.core.exception.userException.BomNodeNotFoundException
import com.nu.bom.core.exception.userException.BranchNotFoundException
import com.nu.bom.core.exception.userException.NodeAlreadyProtectedException
import com.nu.bom.core.exception.userException.NodeIsNotRootOrNotFoundException
import com.nu.bom.core.exception.userException.ProjectNotFoundException
import com.nu.bom.core.model.BaseTriggerAction
import com.nu.bom.core.remote.NuService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.nu.bom.core.utils.doOnEmpty
import com.nu.bom.core.utils.visitTree
import com.nu.bomrads.dto.BomExplorerNode
import com.nu.bomrads.dto.BomExplorerResponseDTO
import com.nu.bomrads.dto.BomNodeCreationResultDTO
import com.nu.bomrads.dto.BranchStatusDTO
import com.nu.bomrads.dto.BranchViewDTO
import com.nu.bomrads.dto.ImportViewDTO
import com.nu.bomrads.dto.NewBranchCreationDTO
import com.nu.bomrads.dto.NodeHistoryDTO
import com.nu.bomrads.dto.NodeSnapshotsRequestDTO
import com.nu.bomrads.dto.NodeTreeDTO
import com.nu.bomrads.dto.PublishActionsDTO
import com.nu.bomrads.dto.PublishResultDTO
import com.nu.bomrads.dto.PushChangesDTO
import com.nu.bomrads.dto.PushMultiNodesChangesDTO
import com.nu.bomrads.dto.RecalculationResultDTO
import com.nu.bomrads.dto.StatusInfoDTO
import com.nu.bomrads.dto.admin.BomNodeDTO
import com.nu.bomrads.dto.admin.ClientAccountDTO
import com.nu.bomrads.enumeration.BomNodeStatus
import com.nu.bomrads.enumeration.BranchTarget
import com.nu.bomrads.enumeration.PublishProcessState
import com.nu.bomrads.id.BomNodeId
import com.nu.bomrads.id.BranchId
import com.nu.bomrads.id.ChangesetId
import com.nu.bomrads.id.ManufacturingTreeId
import com.nu.bomrads.id.ProjectId
import io.github.resilience4j.bulkhead.BulkheadConfig
import io.github.resilience4j.bulkhead.BulkheadRegistry
import io.github.resilience4j.reactor.bulkhead.operator.BulkheadOperator
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.data.domain.PageRequest
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.web.reactive.function.client.WebClientResponseException
import org.springframework.web.reactive.function.client.WebClientResponseException.Conflict
import org.springframework.web.reactive.function.client.WebClientResponseException.NotFound
import org.springframework.web.util.UriBuilder
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.net.URI
import kotlin.math.ceil

/**
 * Service to call Bomrads REST endpoints, related to BomNode management.
 */
@Service
class BomradsBomNodeService(
    private val bomrads: BomradsService,
    private val mapperService: BomradsObjectMapperService,
    private val bomradsErrorHandler: BomradsErrorHandler,
) {
    /**
     * The curren, expected state of the branch.
     */
    data class BranchState(
        val projectId: ProjectId,
        val branchId: BranchId,
        val changesetId: ChangesetId,
    ) {
        constructor(dto: BranchViewDTO) : this(dto.projectId, dto.branch.id, dto.changeset.id)
    }

    private val nuService: NuService get() = bomrads.nuService()

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(BomradsBomNodeService::class.java)
    }

    private val bulkheadRegistry: BulkheadRegistry = BulkheadRegistry.of(BulkheadConfig.ofDefaults())

    fun listRootNodes(
        accessCheck: AccessCheck,
        id: ProjectId,
        branchId: BranchId? = null,
        pageRequest: Pair<PageRequest, String>?,
    ): Flux<BomExplorerNode> {
        return nuService.withTsetService().getToFlux(
            baseUrl = nuService.baseUrl(),
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            uri = {
                val builder = it.path("/api/bom/{id}/root")
                var qb = builder
                if (branchId != null) {
                    qb = qb.queryParam("branchId", branchId.idToString())
                }
                if (pageRequest != null) {
                    builder.queryParam("pageSize", pageRequest.first.pageSize)
                    builder.queryParam("pageNumber", pageRequest.first.pageNumber)
                    builder.queryParam("offset", pageRequest.first.offset)
                    builder.queryParam("sort", pageRequest.second)
                }
                val query = qb.build(mapOf("id" to id.idToString()))
                logger.info("Sending query to: {}", query)
                query
            },
            successHandler = { response -> response.bodyToFlux(BomExplorerNode::class.java) },
        ).doOnEmpty {
            logger.warn("No root nodes found for id: $id branchId: $branchId for $accessCheck")
        }.handleFluxExceptions(accessCheck, projectId = id, branchId = branchId)
    }

    fun getStatusInfo(
        accessCheck: AccessCheck,
        projectId: ProjectId,
    ): Mono<StatusInfoDTO> {
        return nuService.withTsetService().getToMono(
            baseUrl = nuService.baseUrl(),
            uri = { it.path("/api/bom/{projectId}/statusInfo").build(mapOf("projectId" to projectId.idToString())) },
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            successHandler = { response -> response.bodyToMono(StatusInfoDTO::class.java) },
        ).doOnEmpty {
            logger.warn("No status info found for id: $projectId for $accessCheck")
        }
    }

    fun getChildNodes(
        accessCheck: AccessCheck,
        id: ProjectId,
        branchId: BranchId? = null,
    ): Mono<BranchViewDTO> {
        logger.info("getChildNodes ${accessCheck.accountName} - ${accessCheck.userName} - projectId=$id branchId=$branchId")
        return nuService.withTsetService().getToMono(
            baseUrl = nuService.baseUrl(),
            uri = {
                it.path("/api/bom/{id}/branch/{branchId}/children").build(
                    mapOf(
                        "id" to id.idToString(),
                        "branchId" to branchId?.idToString(),
                    ),
                )
            },
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            successHandler = { response -> response.bodyToMono(BranchViewDTO::class.java) },
            errorHandler = { response ->
                response.createException().flatMap {
                    Mono.error(it)
                }
            },
        ).doOnEmpty {
            logger.info("no response for getChildNodes $id - branchId = $branchId")
        }.handleExceptions(accessCheck, branchId = branchId, projectId = id)
    }

    fun getGlobalBranches(
        accessCheck: AccessCheck,
        bomNodeId: BomNodeId,
    ): Flux<BranchStatusDTO> {
        logger.info("getGlobalBranches ${accessCheck.accountName} - ${accessCheck.userName} - bomNodeId=$bomNodeId")
        return nuService.withTsetService().getToFlux(
            baseUrl = nuService.baseUrl(),
            uri = {
                it.path("/api/compatibility/branches/{bomNodeId}").build(mapOf("bomNodeId" to bomNodeId.idToString()))
            },
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            successHandler = { response -> response.bodyToFlux(BranchStatusDTO::class.java) },
        ).handleFluxExceptions(accessCheck, bomNodeId = bomNodeId)
    }

    fun getGlobalBranches(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        bomNodeIds: List<BomNodeId> = emptyList(),
    ): Flux<BranchStatusDTO> {
        logger.info("getGlobalBranches ${accessCheck.accountName} - ${accessCheck.userName} - projectId=$projectId")
        return nuService.withTsetService().postToFlux(
            baseUrl = nuService.baseUrl(),
            uri = {
                it.path("/api/bom/{projectId}/globalBranches").build(projectId.idToString())
            },
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            requestBody = bomNodeIds,
            successHandler = { response -> response.bodyToFlux(BranchStatusDTO::class.java) },
        ).handleFluxExceptions(accessCheck, projectId = projectId)
    }

    fun getImportView(
        accessCheck: AccessCheck,
        projectId: ProjectId,
    ): Mono<List<ImportViewDTO>> {
        logger.info("getGlobalBranches ${accessCheck.accountName} - ${accessCheck.userName} - projectId=$projectId")
        return nuService.withTsetService().getToFlux(
            baseUrl = nuService.baseUrl(),
            uri = { it.path("/api/import/getview/{projectId}").build(projectId.idToString()) },
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            successHandler = { response -> response.bodyToFlux(ImportViewDTO::class.java) },
        ).handleFluxExceptions(accessCheck, projectId = projectId).collectList()
    }

    private fun <T> bulkheadOperator(): BulkheadOperator<T> = BulkheadOperator.of(bulkheadRegistry.bulkhead("bomrads.updateMeta"))

    fun updateMeta(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        manufacturingTreeId: ManufacturingTreeId,
        meta: String,
    ): Mono<Int> {
        logger.info("update meta ${accessCheck.accountName} - ${accessCheck.userName} - for $projectId - treeId: $manufacturingTreeId")
        return nuService.withTsetService().postToMono(
            baseUrl = nuService.baseUrl(),
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            uri = {
                it.path("/api/bom/{projectId}/updateMeta/{manufacturingTreeId}").build(
                    mapOf(
                        "projectId" to projectId.idToString(),
                        "manufacturingTreeId" to manufacturingTreeId.idToString(),
                    ),
                )
            },
            requestBody = meta,
            successHandler = { response -> response.bodyToMono(Int::class.java) },
        ).transformDeferred(bulkheadOperator())
    }

    fun getBomNodeTreeForUpdate(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        branchId: BranchId,
        bomNodeId: BomNodeId,
        trigger: BaseTriggerAction? = null,
    ): Mono<BranchViewDTO> {
        val wireTrigger = mapperService.convertToJsonTrigger(trigger)
        logger.info(
            "getBomNodeTreeForUpdate ${accessCheck.accountName} - ${accessCheck.userName} - projectId=$projectId bomNodeId=$bomNodeId branchId= $branchId trigger=$wireTrigger",
        )
        return nuService.withTsetService().postToMono(
            baseUrl = nuService.baseUrl(),
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            uri = {
                it.path("/api/bom/{projectId}/branch/{branchId}/forUpdate/{bomNodeId}").build(
                    mapOf(
                        "bomNodeId" to bomNodeId.idToString(),
                        "projectId" to projectId.idToString(),
                        "branchId" to branchId.idToString(),
                    ),
                )
            },
            requestBody = wireTrigger,
            successHandler = { response -> response.bodyToMono(BranchViewDTO::class.java) },
            errorHandler = { response ->
                response.createException().flatMap {
                    Mono.error(it)
                }
            },
        ).handleExceptions(accessCheck, projectId = projectId, bomNodeId = bomNodeId, branchId = branchId)
    }

    @Deprecated(message = "When projectId is available, use getBomNodeTreeForUpdate()")
    fun getBomNodeTreeForUpdateCompatibility(
        accessCheck: AccessCheck,
        branchId: BranchId?,
        bomNodeId: BomNodeId?,
        currentChangesetId: ChangesetId? = null,
        trigger: BaseTriggerAction? = null,
    ): Mono<BranchViewDTO> {
        logger.info(
            "getBomNodeTreeForUpdateCompatibility ${accessCheck.accountName} - ${accessCheck.userName} - " +
                "bomNodeId= $bomNodeId - branchId= $branchId, currentChangesetId=$currentChangesetId trigger=$trigger",
        )
        val body =
            NewBranchCreationDTO(
                branchId,
                bomNodeId,
                trigger = mapperService.convertToJsonTrigger(trigger),
                target = BranchTarget.user,
                branchName = null,
                currentChangesetId = currentChangesetId,
            )

        return nuService.withTsetService().postToMono(
            baseUrl = nuService.baseUrl(),
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            uri = {
                it.path("/api/compatibility/getBomNodeTreeForUpdate").build()
            },
            requestBody = body,
            successHandler = { response -> response.bodyToMono(BranchViewDTO::class.java) },
            errorHandler = { response ->
                response.createException().flatMap {
                    Mono.error(it)
                }
            },
        ).handleExceptions(accessCheck, bomNodeId = bomNodeId, branchId = branchId)
    }

    /**
     * Return all the bomNodes and their status by projectId and branchId.
     */
    fun getBranchView(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        branchId: BranchId?,
        loadSourceChangeset: Boolean = false,
        loadingMode: LoadingMode? = null,
    ): Mono<BranchViewDTO> {
        logger.info(
            "branchView for ${accessCheck.accountName} - ${accessCheck.userName} - projectId= " +
                "$projectId - branchId= $branchId - loadingMode = $loadingMode",
        )
        return getBranchViewRequest(
            accessCheck,
            projectId,
            branchId,
            loadSourceChangeset,
            loadingMode,
        ).validateBranchView(
            accountName = accessCheck.accountName,
            branchId = branchId,
            projectId = projectId,
            loadingMode = loadingMode,
        )
    }

    private fun getBranchViewRequest(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        branchId: BranchId?,
        loadSourceChangeset: Boolean,
        loadingMode: LoadingMode?,
    ): Mono<BranchViewDTO> {
        val path =
            if (branchId != null) {
                "/api/bom/{projectId}/branch/{branchId}/view"
            } else {
                "/api/bom/{projectId}/branch/main"
            }

        return nuService.withTsetService().getToMono(
            baseUrl = nuService.baseUrl(),
            uri = {
                val builder = it.path(path).queryParam("source", loadSourceChangeset)
                loadingMode?.addToRequest(builder)
                builder.build(
                    mapOf(
                        "projectId" to projectId.idToString(),
                        "branchId" to branchId?.idToString(),
                    ),
                )
            },
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            successHandler = { response -> response.bodyToMono(BranchViewDTO::class.java) },
        )
    }

    fun getAdminBranchView(
        accountName: String,
        projectId: ProjectId,
        branchId: BranchId?,
        environmentName: String? = null,
    ): Mono<BranchViewDTO> {
        logger.info("admin branchView for $accountName - projectId= $projectId - branchId= $branchId")
        return bomrads.nuService(environmentName).sendGetAdminRequestWithTsetService(
            uri = {
                val path =
                    if (branchId != null) {
                        "/api/bom/{projectId}/branch/{branchId}/view"
                    } else {
                        "/api/bom/{projectId}/branch/main"
                    }
                it.path(path).build(
                    mapOf(
                        "projectId" to projectId.idToString(),
                        "branchId" to branchId?.idToString(),
                    ),
                )
            },
            accountName = accountName,
            responseType = BranchViewDTO::class,
            overrideEnvironment = true,
        )
    }

    fun getSnapshotsForBomExplorer(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        request: NodeSnapshotsRequestDTO? = null,
    ): Mono<BomExplorerResponseDTO> {
        return nuService.withTsetService().postToMono(
            baseUrl = nuService.baseUrl(),
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            uri = {
                it.path("/api/bom/{id}/node-snapshots").build(
                    mapOf(
                        "id" to projectId.idToString(),
                    ),
                )
            },
            requestBody = request,
            successHandler = { response -> response.bodyToMono(BomExplorerResponseDTO::class.java) },
            errorHandler = { response ->
                response.createException().flatMap {
                    Mono.error(it)
                }
            },
        ).doOnEmpty {
            logger.warn("No nodes found for projectId: $projectId for $accessCheck")
        }.handleExceptions(
            accessCheck,
            projectId,
            request?.branchId,
        )
    }

    fun reorderRootNodes(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        bomNodeIds: List<BomNodeId>,
        direction: ReorderDirection,
        steps: Int,
    ): Mono<BomExplorerResponseDTO> =
        nuService.withTsetService().postToMono(
            baseUrl = nuService.baseUrl(),
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            uri = {
                it.path("api/bom/{projectId}/reorder-roots")
                    .queryParam("direction", direction)
                    .queryParam("steps", steps).build(
                        mapOf(
                            "projectId" to projectId.idToString(),
                        ),
                    )
            },
            requestBody = bomNodeIds,
            successHandler = { response -> response.bodyToMono(BomExplorerResponseDTO::class.java) },
        ).doOnEmpty {
            logger.warn("No nodes found for projectId: $projectId for $accessCheck")
        }

    fun getAdminGlobalBranches(
        accountName: String,
        projectId: ProjectId,
        environmentName: String? = null,
    ): Flux<BranchStatusDTO> {
        logger.info("admin global branches for $accountName - projectId= $projectId")
        return bomrads.nuService(environmentName).sendGetFluxAdminRequestWithTsetService(
            uri = {
                it.path("/api/bom/{projectId}/globalBranches").build(
                    mapOf(
                        "projectId" to projectId.idToString(),
                    ),
                )
            },
            BranchStatusDTO::class,
            accountName,
            overrideEnvironment = true,
        )
    }

    /**
     * Return the BomNode structure for a BomNodeId and BranchId.
     */
    fun getBranchViewFromNode(
        accessCheck: AccessCheck,
        branchId: BranchId?,
        bomNodeId: BomNodeId,
        loadSourceChangeset: Boolean,
        loadingMode: LoadingMode? = null,
    ): Mono<BranchViewDTO> {
        logger.info(
            "branchViewCompat for ${accessCheck.accountName} - ${accessCheck.userName} - bomNodeId= $bomNodeId - branchId= $branchId",
        )
        return nuService.withTsetService().getToMono(
            baseUrl = nuService.baseUrl(),
            uri = {
                getUriBuilderForBranchView(
                    builder = it,
                    loadSourceChangeset = loadSourceChangeset,
                    branchId = branchId,
                    bomNodeId = bomNodeId,
                    loadingMode = loadingMode,
                )
            },
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            successHandler = { response -> response.bodyToMono(BranchViewDTO::class.java) },
            errorHandler = { response ->
                response.createException().flatMap { Mono.error(it) }
            },
        ).validateBranchView(
            accessCheck.accountName,
            bomNodeId = bomNodeId,
            branchId = branchId,
            loadingMode = loadingMode,
        )
    }

    fun renameNode(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        branchId: BranchId,
        bomNodeId: BomNodeId,
        name: String,
        renameDescendants: Boolean,
        trigger: BaseTriggerAction,
    ): Mono<BranchViewDTO> {
        val wireTrigger = mapperService.convertToJsonTrigger(trigger)
        logger.info(
            "renameNode ${accessCheck.accountName} - ${accessCheck.userName} - " +
                "projectId=$projectId bomNodeId=$bomNodeId branchId=$branchId renameDescendants=$renameDescendants trigger=$wireTrigger",
        )
        return nuService.withTsetService().postToMono(
            baseUrl = nuService.baseUrl(),
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            uri = {
                val builder = it.path("/api/bom/{projectId}/branch/{branchId}/rename/{bomNodeId}")
                builder.queryParam("renameDescendants", renameDescendants)
                builder.queryParam("name", name)
                builder.build(
                    mapOf(
                        "projectId" to projectId.idToString(),
                        "branchId" to branchId.idToString(),
                        "bomNodeId" to bomNodeId.idToString(),
                    ),
                )
            },
            requestBody = wireTrigger,
            successHandler = { response -> response.bodyToMono(BranchViewDTO::class.java) },
        )
    }

    /**
     * Return the BomNode structure for a BomNodeId and BranchId.
     */
    fun getAdminBranchViewFromNode(
        accountName: String,
        branchId: BranchId?,
        bomNodeId: BomNodeId,
        loadSourceChangeset: Boolean,
        loadingMode: LoadingMode? = null,
    ): Mono<BranchViewDTO> {
        logger.info("admin branchViewCompat for $accountName - bomNodeId= $bomNodeId - branchId= $branchId")
        return nuService.sendGetAdminRequestWithTsetService(
            {
                getUriBuilderForBranchView(it, loadSourceChangeset, branchId, bomNodeId, loadingMode)
            },
            BranchViewDTO::class,
            accountName,
        ).validateBranchView(accountName, bomNodeId = bomNodeId, branchId = branchId, loadingMode = loadingMode)
    }

    private fun getUriBuilderForBranchView(
        builder: UriBuilder,
        loadSourceChangeset: Boolean,
        branchId: BranchId?,
        bomNodeId: BomNodeId,
        loadingMode: LoadingMode?,
    ): URI {
        builder.path("/api/compatibility/status")
        builder.queryParam("bomNodeId", bomNodeId.idToString())
        branchId?.let { branch ->
            builder.queryParam("branchId", branch.idToString())
        }
        builder.queryParam("source", loadSourceChangeset)
        loadingMode?.addToRequest(builder)
        return builder.build()
    }

    /**
     * Checks, that the returned BranchViewDTO contains the expected node - according to the LoadingMode.
     */
    private fun Mono<BranchViewDTO>.validateBranchView(
        accountName: String,
        projectId: ProjectId? = null,
        branchId: BranchId? = null,
        bomNodeId: BomNodeId? = null,
        loadingMode: LoadingMode? = null,
    ): Mono<BranchViewDTO> {
        return this.handleExceptions(
            accountName = accountName,
            projectId = projectId,
            bomNodeId = bomNodeId,
            branchId = branchId,
        ).flatMap { dto ->
            if (loadingMode != null && !loadingMode.isValidResponse(dto)) {
                Mono.error(BomNodeNotFoundException(bomNodeId?.idToString(), branchId?.idToString()))
            } else {
                Mono.just(dto)
            }
        }
    }

    fun pushChanges(
        accessCheck: AccessCheck,
        branchState: BranchState,
        changes: PushChangesDTO,
        branchTarget: BranchTarget?,
    ): Mono<BranchViewDTO> {
        val dump = dumpTree(changes.nodeTree)
        logger.info("pushing changes to ${branchState.projectId} with: ${changes.trigger} - target: $branchTarget\n$dump")

        return nuService.withTsetService().postToMono(
            baseUrl = nuService.baseUrl(),
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            uri = {
                val builder = it.path("/api/bom/{projectId}/branch/{branchId}/{changesetId}")
                if (branchTarget != null) {
                    builder.queryParam("branchTarget", branchTarget.name)
                }
                builder.build(
                    mapOf(
                        "projectId" to branchState.projectId.idToString(),
                        "branchId" to branchState.branchId.idToString(),
                        "changesetId" to branchState.changesetId.idToString(),
                    ),
                )
            },
            requestBody = changes,
            successHandler = { response -> response.bodyToMono(BranchViewDTO::class.java) },
        )
    }

    private fun dumpTree(nodeTree: NodeTreeDTO): String {
        val lines =
            nodeTree.visitTree(
                func = {
                        node,
                        depth,
                    ->
                    " ".repeat(depth) + "${node.labels?.name} - trigger: ${node.changeTriggerNode} - " +
                        "rel: ${node.triggerRelation}"
                },
                children = { node -> node.children },
            )
        return lines.joinToString(separator = "\n")
    }

    fun publishBranch(
        accessCheck: AccessCheck,
        branchId: BranchId,
        publishTriggerAction: BaseTriggerAction?,
        unpublishTriggerAction: BaseTriggerAction?,
        bomNodeId: BomNodeId?,
    ): Mono<PublishResultDTO> {
        logger.info("publishing branch $branchId")
        // TODO: make it non-null again
        val publish = mapperService.convertToJsonTrigger(publishTriggerAction, null)
        val unpublish = mapperService.convertToJsonTrigger(unpublishTriggerAction, null)
        return nuService.withTsetService().postToMono(
            baseUrl = nuService.baseUrl(),
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            uri = {
                val builder = it.path("/api/compatibility/{branchId}/startPublish")
                builder.queryParam("preview", false)
                builder.build(
                    mapOf(
                        "branchId" to branchId.idToString(),
                    ),
                )
            },
            requestBody = PublishActionsDTO(publish, unpublish, bomNode = bomNodeId),
            successHandler = { response -> response.bodyToMono(PublishResultDTO::class.java) },
            errorHandler = { response ->
                response.createException().flatMap {
                    Mono.error(it)
                }
            },
        ).doOnSuccess {
            logger.trace("The publish result is {}", it)
        }.handleExceptions(accessCheck, branchId = branchId)
    }

    fun updateCalculation(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        branchId: BranchId,
        recalculationResultDTO: RecalculationResultDTO,
    ): Mono<RecalculationResultDTO> {
        logger.info("publishing branch $branchId")
        return nuService.withTsetService().postToMono(
            baseUrl = nuService.baseUrl(),
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            uri = {
                val builder = it.path("/api/bom/{projectId}/updateCalculation/{branchId}")
                builder.build(
                    mapOf(
                        "projectId" to projectId.idToString(),
                        "branchId" to branchId.idToString(),
                    ),
                )
            },
            requestBody = recalculationResultDTO,
            successHandler = { response -> response.bodyToMono(PublishProcessState::class.java) },
            errorHandler = { response ->
                response.createException().flatMap {
                    Mono.error(it)
                }
            },
        ).doOnError {
            logger.error(
                "Update of projectId= $projectId branchId=$branchId - result: $recalculationResultDTO " +
                    "failed: ${it.message}",
                it,
            )
        }.handleExceptions(accessCheck, branchId = branchId)
            .map {
                logger.info(
                    "publish process state is $it, calculation updated with projectId= " +
                        "$projectId branchId=$branchId - result: $recalculationResultDTO",
                )
                recalculationResultDTO
            }
    }

    fun createNodes(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        multiNodeChanges: PushMultiNodesChangesDTO,
    ): Flux<BomNodeCreationResultDTO> {
        logger.debug(
            "creating nodes in {} as variantName={} for trees={}",
            projectId,
            multiNodeChanges.variantName,
            multiNodeChanges.nodeTrees,
        )
        return nuService.withTsetService().postToFlux(
            baseUrl = nuService.baseUrl(),
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            uri = {
                val builder = it.path("/api/bom/{projectId}/multinodes")
                builder.build(
                    mapOf(
                        "projectId" to projectId.idToString(),
                    ),
                )
            },
            requestBody = multiNodeChanges,
            successHandler = { response -> response.bodyToFlux(BomNodeCreationResultDTO::class.java) },
        )
    }

    fun deleteRootBomNode(
        accessCheck: AccessCheck,
        bomNodeId: BomNodeId,
        branchId: BranchId?,
        trigger: BaseTriggerAction,
    ): Flux<BomNodeDTO> {
        val triggerJson = mapperService.convertToJsonTrigger(trigger)
        logger.info("deleting root BomNode $bomNodeId and descendants")
        return nuService.withTsetService().deleteToFluxWithBody(
            baseUrl = nuService.baseUrl(),
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            uri = {
                val builder = it.path("/api/compatibility/bomNode/{bomNodeId}")
                if (branchId != null) {
                    builder.queryParam("branchId", branchId.idToString())
                }
                builder.build(mapOf("bomNodeId" to bomNodeId.idToString()))
            },
            requestBody = triggerJson,
            successHandler = { response -> response.bodyToFlux(BomNodeDTO::class.java) },
        ).doOnEmpty {
            logger.warn("No root nodes found for id: $bomNodeId for $accessCheck")
        }.handleFluxExceptions(accessCheck, bomNodeId = bomNodeId)
    }

    fun updateStatus(
        accessCheck: AccessCheck,
        bomNodeId: BomNodeId,
        status: BomNodeStatus,
        branchId: BranchId,
    ): Mono<BomNodeDTO> {
        logger.info("updating BomNode $bomNodeId status to $status")
        return nuService.withTsetService().putToMono(
            baseUrl = nuService.baseUrl(),
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            uri = {
                val builder = it.path("/api/bomNode/{bomNodeId}/status")
                builder.queryParam("branchId", branchId.idToString())
                builder.build(
                    mapOf("bomNodeId" to bomNodeId.idToString()),
                )
            },
            requestBody = status,
            successHandler = { response -> response.bodyToMono(BomNodeDTO::class.java) },
            errorHandler = { response ->
                response.createException().flatMap {
                    Mono.error(it)
                }
            },
        ).doOnEmpty {
            logger.warn("No bomNode found for id: $bomNodeId for $accessCheck")
        }.handleExceptions(accessCheck, bomNodeId = bomNodeId, branchId = branchId)
    }

    fun getBomNode(
        accessCheck: AccessCheck,
        bomNodeId: BomNodeId,
    ): Mono<BomNodeDTO> {
        logger.info("getting BomNode $bomNodeId")
        return nuService.withTsetService().getToMono(
            baseUrl = nuService.baseUrl(),
            uri = {
                it.path("/api/bomNode/{bomNodeId}").build(mapOf("bomNodeId" to bomNodeId.idToString()))
            },
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            successHandler = { response -> response.bodyToMono(BomNodeDTO::class.java) },
            errorHandler = { response ->
                response.createException().flatMap {
                    Mono.error(it)
                }
            },
        ).doOnEmpty {
            logger.warn("No bomNode found for id: $bomNodeId for $accessCheck")
        }.handleExceptions(accessCheck, bomNodeId = bomNodeId)
    }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    fun getBomNodeForProject(
        accessCheck: AccessCheck,
        projectId: ProjectId,
    ): Mono<List<BomNodeDTO>> {
        val pageSize = 2000 // 2000 bom_node entries is 8mb, not that expensive (but we can lower this).

        logger.info("getting BomNodes for $projectId")
        return nuService.withTsetService().getToMono(
            baseUrl = nuService.baseUrl(),
            uri = {
                it.path("/api/bomNode/count").queryParam("projectId.equals", projectId.idToString()).build()
            },
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            successHandler = { response -> response.bodyToMono(Long::class.java) },
        ).flatMapMany { totalRecords ->
            val numberOfPages = ceil(totalRecords / pageSize.toDouble()).toInt()
            val pages = (0 until numberOfPages)
            Flux.fromIterable(pages).flatMap { pageNumber ->
                nuService.withTsetService().getToFlux(
                    baseUrl = nuService.baseUrl(),
                    uri = {
                        it.path("/api/bomNode/query")
                            .queryParam("projectId.equals", projectId.idToString())
                            .queryParam("page", pageNumber)
                            .queryParam("size", pageSize)
                            .queryParam("sort", "id")
                            .build()
                    },
                    headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
                    jwtToken = accessCheck.token,
                    successHandler = { response -> response.bodyToFlux(BomNodeDTO::class.java) },
                    errorHandler = { response ->
                        response.createException().flatMapMany { Flux.error(it) }
                    },
                )
            }
        }.doOnEmpty {
            logger.warn("No bomNode found for projectId = $projectId for $accessCheck")
        }.collectList().handleExceptions(accessCheck, projectId = projectId)
    }

    fun checkBomNodeExists(
        accessCheck: AccessCheck,
        bomNodeIds: List<BomNodeId>,
        includeDeleted: Boolean = false,
    ): Mono<Boolean> {
        logger.info("getting BomNode $bomNodeIds - or empty response")
        return nuService.withTsetService().postToMono(
            baseUrl = nuService.baseUrl(),
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            uri = {
                val builder = it.path("/api/bomNode/exists")
                builder.queryParam("includeDeleted", includeDeleted).build()
                builder.build()
            },
            requestBody = bomNodeIds,
            successHandler = { response -> response.bodyToMono(Boolean::class.java) },
        ).doOnEmpty {
            logger.warn("No bomNode found for id: $bomNodeIds for $accessCheck")
        }.onErrorResume(emptyOnNotFound())
    }

    fun filterAccessibleBomNodes(
        accessCheck: AccessCheck,
        bomNodeIds: List<BomNodeId>,
    ): Flux<BomNodeId> {
        logger.info("getting accessible bomnodes $bomNodeIds")
        return nuService.withTsetService().postToFlux(
            baseUrl = nuService.baseUrl(),
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            uri = {
                it.path("/api/bomNode/filterAccess").build()
            },
            requestBody = bomNodeIds,
            successHandler = { response -> response.bodyToFlux(BomNodeId::class.java) },
        )
    }

    fun getHistory(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        bomNodeId: BomNodeId,
        branchId: BranchId?,
        startChangesetId: ChangesetId?,
        includeCurrentBranch: Boolean,
    ): Flux<NodeHistoryDTO> {
        logger.info(
            "getting history for project: $projectId branch: $branchId and user: ${accessCheck.userName} - " +
                accessCheck.accountName,
        )
        return nuService.withTsetService().getToFlux(
            baseUrl = nuService.baseUrl(),
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            uri = {
                val builder = it.path("/api/bom/{projectId}/node/{bomNodeId}/history")
                if (branchId != null) {
                    builder.queryParam("branchId", branchId.idToString())
                }
                builder.queryParam("includeCurrentBranch", includeCurrentBranch)
                if (startChangesetId != null) {
                    builder.queryParam("startChangesetId", startChangesetId.idToString())
                }
                builder.build(
                    mapOf(
                        "projectId" to projectId.idToString(),
                        "bomNodeId" to bomNodeId.idToString(),
                    ),
                )
            },
            successHandler = { response -> response.bodyToFlux(NodeHistoryDTO::class.java) },
        )
    }

    /**
     * Return the BomNode structure for a BomNodeId and BranchId.
     */
    fun getUsedSnapshots(
        accountName: String,
        projectId: ProjectId?,
    ): Flux<ManufacturingTreeId> {
        logger.info("get SnapshotIds for $accountName  - project= $projectId")
        return nuService.sendPostFluxAdminRequestWithTsetService(body = null, accountName = accountName, uri = {
            val builder = it.path("/api/snapshots/collectUsedSnapshots")
            projectId?.let { pi -> builder.queryParam("projectId", pi.idToString()) }
            builder.build()
        }, responseType = ManufacturingTreeId::class)
    }

    fun migrate(
        clientAccount: ClientAccountDTO,
        doneCounter: Int,
    ): Mono<String> {
        return nuService.sendPostMonoAdminRequestWithTsetService(
            body = null,
            accountName = clientAccount.key,
            uri = {
                it.path("/api/migration").queryParam("doneCounter", doneCounter).build()
            },
            responseType = String::class,
            longRunning = true,
        )
    }

    fun protectBomNode(
        accessCheck: AccessCheck,
        bomNodeIds: List<BomNodeId>,
    ): Mono<Void> {
        logger.info("Protecting bomnode with id $bomNodeIds")
        return nuService.withTsetService().putToMono(
            baseUrl = nuService.baseUrl(),
            headers = nuService.getDefaultAccessCheckHeaders(accessCheck),
            jwtToken = accessCheck.token,
            uri = {
                it.path("/api/bomNode/protect").build()
            },
            requestBody = bomNodeIds,
            successHandler = { _ -> Mono.empty() },
            errorHandler = { response ->
                response.createException().flatMap {
                    when (it) {
                        is Conflict -> {
                            Mono.error(NodeAlreadyProtectedException())
                        }
                        else -> {
                            if (it.statusCode == HttpStatus.PRECONDITION_FAILED) {
                                Mono.error(
                                    NodeIsNotRootOrNotFoundException(
                                        bomradsErrorHandler.getErrorTitle(it)
                                            ?: "Failed preconditions on calculation protect",
                                    ),
                                )
                            } else {
                                error("Error protecting node: ${it.message}")
                            }
                        }
                    }
                }
            },
        )
    }

    private fun <T> Mono<T>.handleExceptions(
        accessCheck: AccessCheck? = null,
        projectId: ProjectId? = null,
        branchId: BranchId? = null,
        bomNodeId: BomNodeId? = null,
        accountName: String =
            accessCheck?.accountName
                ?: throw IllegalArgumentException("Either accessCheck or accountName must be specified!"),
    ): Mono<T> = this.onErrorResume(errorHandler(accountName, projectId, branchId, bomNodeId))

    private fun <T> Flux<T>.handleFluxExceptions(
        accessCheck: AccessCheck,
        projectId: ProjectId? = null,
        branchId: BranchId? = null,
        bomNodeId: BomNodeId? = null,
    ): Flux<T> = this.onErrorResume(errorHandler(accessCheck.accountName, projectId, branchId, bomNodeId))

    private fun <E> errorHandler(
        accountName: String,
        projectId: ProjectId?,
        branchId: BranchId?,
        bomNodeId: BomNodeId?,
    ): (Throwable) -> Mono<E> {
        return { error: Throwable ->
            val newError =
                when (error) {
                    is Conflict -> {
                        logger.info(
                            "BomNode with id=$bomNodeId, in branch $branchId is in conflict, projectId=$projectId, " +
                                "for account $accountName!",
                        )
                        ErrorCodedException.branchMergeNeeded()
                    }

                    is NotFound ->
                        run {
                            logger.info(
                                "BomNode with id=$bomNodeId, in branch $branchId not found, projectId=$projectId, " +
                                    "for account $accountName!",
                            )
                            if (bomNodeId != null) {
                                BomNodeNotFoundException(bomNodeId.idToString(), branchId?.toString())
                            } else if (branchId != null) {
                                BranchNotFoundException(branchId.idToString())
                            } else {
                                ProjectNotFoundException(projectId?.idToString(), null)
                            }
                        }

                    is WebClientResponseException -> bomradsErrorHandler.convertWebClientResponseException(error)
                    else -> error
                }
            Mono.error(newError)
        }
    }

    private fun <E> emptyOnNotFound(): (Throwable) -> Mono<E> {
        return { error: Throwable ->
            when (error) {
                is NotFound -> Mono.empty()
                else -> Mono.error(error)
            }
        }
    }
}
