package com.nu.bom.core.turn.model

fun SingleCutResponse.toDomain(): com.nu.bom.core.manufacturing.fieldTypes.turn.SingleCutResponse =
    com.nu.bom.core.manufacturing.fieldTypes.turn.SingleCutResponse(
        cuttingSpeedMpmin = this.cuttingSpeedMpmin,
        feedRatePerRevolutionMm = this.feedRatePerRevolutionMm,
        chipLoadMm = this.chipLoadMm,
        depthFeedMarkingMm = this.depthFeedMarkingMm,
    )

fun DoubleCutResponse.toDomain(): com.nu.bom.core.manufacturing.fieldTypes.turn.DoubleCutResponse =
    com.nu.bom.core.manufacturing.fieldTypes.turn.DoubleCutResponse(
        firstCut = this.firstCut.toDomain(),
        secondCut = this.secondCut.toDomain(),
        ratioFirstCut = this.ratioFirstCut,
    )

fun GearHobbingCuttingMethodResponse.toDomain() =
    when (this) {
        is SingleCutResponse -> this.toDomain()
        is DoubleCutResponse -> this.toDomain()
    }

fun TurningOperationCycleTime.toDomain(): com.nu.bom.core.manufacturing.fieldTypes.turn.TurningOperationCycleTime =
    com.nu.bom.core.manufacturing.fieldTypes.turn
        .TurningOperationCycleTime(this.primaryTimeS, this.secondaryTimeS)

fun GearHobbingOperationInfoResponse.toDomain(): com.nu.bom.core.manufacturing.fieldTypes.turn.GearHobbingOperationInfoResponse =
    com.nu.bom.core.manufacturing.fieldTypes.turn.GearHobbingOperationInfoResponse(
        this.gearHobInfo.toDomain(),
        this.method.toDomain(),
        this.cycleTimes.toDomain(),
    )

fun GearHobInfo.toDomain(): com.nu.bom.core.manufacturing.fieldTypes.turn.GearHobInfo =
    com.nu.bom.core.manufacturing.fieldTypes.turn
        .GearHobInfo(
            hobTipDiameterMm = this.hobTipDiameterMm,
            hobNumberOfTeeth = this.hobNumberOfTeeth,
            hobNumberOfStarts = this.hobNumberOfStarts,
        )
