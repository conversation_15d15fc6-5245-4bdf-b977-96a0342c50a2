package com.nu.bom.core.manufacturing.entities

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.FieldName
import com.nu.bom.core.manufacturing.annotations.FilteredChildren
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntityContext
import com.nu.bom.core.manufacturing.annotations.MasterDataType
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.Technologies
import com.nu.bom.core.manufacturing.defaults.NullProvider
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Diffusivity
import com.nu.bom.core.manufacturing.fieldTypes.DiffusivityUnits
import com.nu.bom.core.manufacturing.fieldTypes.Pressure
import com.nu.bom.core.manufacturing.fieldTypes.PressureUnits
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.RubberType
import com.nu.bom.core.manufacturing.fieldTypes.Temperature
import com.nu.bom.core.manufacturing.fieldTypes.TemperatureUnits
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.utils.findResultByTextAttribute
import com.nu.bom.core.utils.findResultByTextAttributeSafe
import org.bson.types.ObjectId

@EntityType(Entities.MATERIAL)
@MasterDataType(com.nu.bom.core.manufacturing.enums.MasterDataType.RAW_MATERIAL_RUBBER)
@Technologies([Model.RINJ])
class RawMaterialRubber(name: String) : ManufacturingEntity(name) {
    companion object {
        private const val RUBBER_TYPE_MASTER_DATA_FIELD = "#classification_field_tset-ref-field-rubberType"
        private const val SPECIFIC_THERMAL_CAPACITY_MASTER_DATA_FIELD =
            "#classification_field_tset-ref-field-specificThermalCapacity"
        private const val MOLD_TEMPERATURE_MASTER_DATA_FIELD =
            "#classification_field_tset-ref-field-moldTemperature"
        private const val INTERNAL_MOLD_PRESSURE_MASTER_DATA_FIELD =
            "#classification_field_tset-ref-field-internalMoldPressure"
        private const val HEATING_TIME_FACTOR_MASTER_DATA_FIELD =
            "#classification_field_tset-ref-field-heatingTimeFactor"

        // classificationFields used in the lazy migration
        val classificationFields =
            mapOf(
                "rubberType" to RUBBER_TYPE_MASTER_DATA_FIELD,
                "specificThermalCapacity" to SPECIFIC_THERMAL_CAPACITY_MASTER_DATA_FIELD,
                "moldTemperature" to MOLD_TEMPERATURE_MASTER_DATA_FIELD,
                "internalMoldPressure" to INTERNAL_MOLD_PRESSURE_MASTER_DATA_FIELD,
                "heatingTimeFactor" to HEATING_TIME_FACTOR_MASTER_DATA_FIELD,
            )
    }

    override val extends = RawMaterial(name)

    @Input
    fun designation(materialBaseDisplayDesignation: Text): Text = materialBaseDisplayDesignation

    @Input
    @MandatoryForEntity(context = MandatoryForEntityContext.CREATE_FOR_MASTERDATA)
    fun rubberType(
        headerKey: Text?,
        @Default(NullProvider::class)
        @FieldName(RUBBER_TYPE_MASTER_DATA_FIELD)
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        rubberTypeFromClassification: Map<ObjectId, RubberType?>?,
        @Default(NullProvider::class)
        @FieldName("headerKey")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToHeaderKeyFromClassification: Map<ObjectId, Text>?,
    ): RubberType? =
        findResultByTextAttribute(
            headerKey,
            rubberTypeFromClassification,
            mapIdToHeaderKeyFromClassification,
        )

    @Input
    @DefaultUnit(DefaultUnit.QMM_PER_SECONDS)
    fun specificThermalCapacity(
        headerKey: Text?,
        @Default(NullProvider::class)
        @FieldName(SPECIFIC_THERMAL_CAPACITY_MASTER_DATA_FIELD)
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        specificThermalCapacityFromClassification: Map<ObjectId, Diffusivity?>?,
        @Default(NullProvider::class)
        @FieldName("headerKey")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToHeaderKeyFromClassification: Map<ObjectId, Text>?,
    ): Diffusivity =
        findResultByTextAttributeSafe(
            headerKey,
            specificThermalCapacityFromClassification,
            mapIdToHeaderKeyFromClassification,
            Diffusivity(1.2, DiffusivityUnits.QMM_PER_SECONDS),
        )

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.CELSIUS)
    fun moldTemperature(
        headerKey: Text?,
        @Default(NullProvider::class)
        @FieldName(MOLD_TEMPERATURE_MASTER_DATA_FIELD)
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        moldTemperatureFromClassification: Map<ObjectId, Temperature?>?,
        @Default(NullProvider::class)
        @FieldName("headerKey")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToHeaderKeyFromClassification: Map<ObjectId, Text>?,
    ): Temperature =
        findResultByTextAttributeSafe(
            headerKey,
            moldTemperatureFromClassification,
            mapIdToHeaderKeyFromClassification,
            Temperature(170.0, TemperatureUnits.CELSIUS),
        )

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.BAR)
    fun internalMoldPressure(
        headerKey: Text?,
        @Default(NullProvider::class)
        @FieldName(INTERNAL_MOLD_PRESSURE_MASTER_DATA_FIELD)
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        internalMoldPressureFromClassification: Map<ObjectId, Pressure?>?,
        @Default(NullProvider::class)
        @FieldName("headerKey")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToHeaderKeyFromClassification: Map<ObjectId, Text>?,
    ): Pressure =
        findResultByTextAttributeSafe(
            headerKey,
            internalMoldPressureFromClassification,
            mapIdToHeaderKeyFromClassification,
            Pressure(100.0, PressureUnits.BAR),
        )

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.CELSIUS)
    fun moldSeparationTemperature(): Temperature = Temperature(160.0, TemperatureUnits.CELSIUS)

    @Input
    fun heatingTimeFactor(
        headerKey: Text?,
        @Default(NullProvider::class)
        @FieldName(HEATING_TIME_FACTOR_MASTER_DATA_FIELD)
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        heatingTimeFactorFromClassification: Map<ObjectId, Rate?>?,
        @Default(NullProvider::class)
        @FieldName("headerKey")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToHeaderKeyFromClassification: Map<ObjectId, Text>?,
    ): Rate =
        findResultByTextAttributeSafe(
            headerKey,
            heatingTimeFactorFromClassification,
            mapIdToHeaderKeyFromClassification,
            Rate(1.toBigDecimal()),
        )
}
