package com.nu.bom.core.turn

import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.toMetaInfoEntry
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Rz
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.service.MaterialService
import com.nu.bom.core.turn.model.BartInputProperties
import com.nu.bom.core.turn.model.RawProfileRequest
import com.nu.bom.core.turn.model.RawProfileResponse
import com.nu.bom.core.turn.model.Section
import com.nu.bom.core.turn.model.Sketch
import com.nu.bom.core.turn.model.Technology
import com.nu.bom.core.turn.model.Tolerance
import com.nu.bom.core.turn.model.ToleranceUnit
import com.nu.bom.core.user.AccessCheck
import com.nu.masterdata.dto.v1.header.HeaderDetailQueryResponseDto
import jakarta.ws.rs.BadRequestException
import org.bson.types.ObjectId
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono

@Service
class BarTurningParametersService(
    private val turningApiService: TurningApiService,
    private val materialService: MaterialService,
) : TurningParameters() {
    override fun fillParameters(
        sketch: Sketch,
        material: String,
        accessCheck: AccessCheck,
        sketchId: ObjectId,
        rawModel: Model,
        partInputGroup: String?,
        marginWidth: Length?,
        netWeightPerPart: Weight,
        barDiameterOverride: Length?,
    ): Mono<List<Section>> {
        if (marginWidth != null) {
            throw BadRequestException("Bar turning with margin width")
        }
        return getTurningQualifiedMaterialMasterData(materialService, material, accessCheck).flatMap { masterdata ->
            val barDiameter = barDiameterOverride ?: getBarDiameter(sketch, masterdata)
            val partLength = TurningPropertiesService.getPartLength(sketch)
            val barLength = TurningPropertiesService.getBarLength(masterdata)
            val surfaceFinish = TurningPropertiesService.getSurfaceFinish(masterdata)
            val density = TurningPropertiesService.getDensity(masterdata)
            getRawProfile(sketch, sketchId, barDiameter, partLength, surfaceFinish)
                .map { rawProfileResponse ->
                    val weightSection = getWeightSection(rawProfileResponse, netWeightPerPart, density)
                    val blankSection = getBlankSection(sketch, barLength, barDiameter)
                    val partSection = getPartSection(rawProfileResponse, sketch)
                    listOf(weightSection, blankSection, partSection)
                }
        }
    }

    private fun getBlankSection(
        sketch: Sketch,
        barLength: Length,
        barDiameter: Length,
    ): Section {
        val partLength = TurningPropertiesService.getPartLength(sketch)
        val blankLength = getBlankLength(partLength)
        val blankLengthField =
            FieldParameter(
                name = "blankLength",
                type = Length::class.simpleName!!,
                unit = DefaultUnit.MILLIMETER,
                value = blankLength.inMillimeter,
                valueInDefaultUnit = blankLength.inMillimeter,
                metaInfo =
                    mapOf(
                        toMetaInfoEntry(DefaultUnit.MILLIMETER),
                        ReadOnly.META_INFO to true,
                        "min" to blankLength.inMillimeter,
                    ),
                denominatorUnit = null,
            )

        val barLengthField =
            FieldParameter(
                name = "barLength",
                type = Length::class.simpleName!!,
                unit = DefaultUnit.METER,
                value = barLength.inMeter,
                valueInDefaultUnit = barLength.inMeter,
                metaInfo =
                    mapOf(
                        toMetaInfoEntry(DefaultUnit.METER),
                        ReadOnly.META_INFO to true,
                        "min" to blankLength.inMeter,
                    ),
                denominatorUnit = null,
            )

        val partDiameter = TurningPropertiesService.getPartDiameter(sketch)
        val barDiameterInMM = barDiameter.inMillimeter
        val barDiameterField =
            FieldParameter(
                name = "barDiameter",
                type = Length::class.simpleName!!,
                unit = DefaultUnit.MILLIMETER,
                value = barDiameterInMM,
                valueInDefaultUnit = barDiameterInMM,
                metaInfo =
                    mapOf(
                        toMetaInfoEntry(DefaultUnit.MILLIMETER),
                        // COST-9458 allow overwriting calculated bar diameter
                        ReadOnly.META_INFO to false,
                        "min" to partDiameter.inMillimeter,
                    ),
                denominatorUnit = null,
            )

        return Section(
            title = "Blank",
            fields =
                listOf(
                    blankLengthField,
                    barLengthField,
                    barDiameterField,
                ),
        )
    }

    private fun getPartSection(
        response: RawProfileResponse,
        sketch: Sketch,
    ): Section {
        val partDiameterField =
            createLengthFieldParameterReadOnly(
                fieldName = "partOuterDiameter",
                valueInMm = TurningPropertiesService.getPartDiameter(sketch).inMillimeter,
            )
        val partLengthField =
            createLengthFieldParameterReadOnly(
                fieldName = "partLength",
                valueInMm = TurningPropertiesService.getPartLength(sketch).inMillimeter,
            )
        val wallThicknessField =
            createLengthFieldParameterReadOnly(
                fieldName = "wallThickness",
                valueInMm = response.turned_part.max_wallthickness,
            )

        return Section(
            title = "part",
            fields = listOf(partDiameterField, partLengthField, wallThicknessField),
        )
    }

    private fun getRawProfile(
        sketch: Sketch,
        sketchId: ObjectId,
        barDiameter: Length,
        partLength: Length,
        surfaceFinishBar: Rz,
    ): Mono<RawProfileResponse> {
        // Check if point properties are correct.
        // Could throw, e.g. if freistich is not filled out correctly.
        sketch.turning_point_attributes

        val blankLength = getBlankLength(partLength)
        val request =
            RawProfileRequest(
                geometry = sketch.geometry,
                technology =
                    Technology(
                        type = "BART",
                        part_input_group = null,
                        properties =
                            BartInputProperties(
                                bar_diameter = barDiameter.inMillimeter.toDouble(),
                                margin_on_side = getActualAufschlag(partLength, blankLength).inMillimeter.toDouble(),
                                surface_finish = Tolerance(surfaceFinishBar.res.toDouble(), ToleranceUnit.Rz),
                            ),
                    ),
                line_attributes = sketch.turning_line_attributes,
            )
        return turningApiService.rawProfile(
            sketchId.toHexString(),
            request,
        )
    }

    private fun getBarDiameter(
        sketch: Sketch,
        masterdata: HeaderDetailQueryResponseDto,
    ): Length {
        // Bar and Blank diameter are the same for BART
        return TurningPropertiesService.calculateBarDiameter(
            sketch,
            TurningPropertiesService.getSurfaceFinish(masterdata),
        )
    }

    private fun getBlankLength(partLength: Length): Length = partLength + TurningPropertiesService.BLANK_LENGTH_OFFSET

    private fun getActualAufschlag(
        partLength: Length,
        blankLength: Length,
    ): Length = blankLength - partLength
}
