package com.nu.bom.core.manufacturing.entities.masterdata.exchangerates

import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.FieldBasedUnits
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.KeepOld
import com.nu.bom.core.manufacturing.annotations.Label
import com.nu.bom.core.manufacturing.annotations.RequiredFor
import com.nu.bom.core.manufacturing.annotations.SourceDataInput
import com.nu.bom.core.manufacturing.entities.masterdata.MasterdataEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Text

@EntityType(Entities.MD_EXCHANGERATE, userCreatable = false, default = true)
class MasterdataExchangeRateEntity(name: String) : MasterdataEntity(name) {
    @Label("entityDesignation")
    @Input
    override fun headerKey(): Text? = null

    fun displayDesignation(
        headerDisplayName: Text?,
        entityDesignation: Text?,
    ): Text? = headerDisplayName ?: entityDesignation

    @FieldBasedUnits(
        numeratorUnitField = "numeratorCurrency",
        denominatorUnitField = "denominatorCurrency",
        denominatorUnitType = FieldBasedUnits.DENOMINATOR_UNIT_TYPE_MONEY,
    )
    @DefaultUnit(unit = "numeratorCurrency", isFixed = true, unitReferencesField = true)
    @KeepOld
    @Input
    @SourceDataInput
    @RequiredFor([RequiredFor.KpiName.COST])
    fun rate(): Num? = null

    @KeepOld
    @Input
    @SourceDataInput
    @RequiredFor([RequiredFor.KpiName.COST])
    fun numeratorCurrency(): Text? = null

    @KeepOld
    @Input
    @SourceDataInput
    @RequiredFor([RequiredFor.KpiName.COST])
    fun denominatorCurrency(): Text? = null
}
