package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.entities.BaseMaterial
import com.nu.bom.core.manufacturing.entities.ElectronicComponent
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.masterdata.MasterdataEntity
import com.nu.bom.core.manufacturing.entities.masterdata.material.MasterdataMaterialClassificationSchemaEntity
import com.nu.bom.core.manufacturing.entities.masterdata.material.MasterdataMaterialClassificationValueEntity
import com.nu.bom.core.manufacturing.entities.masterdata.material.MasterdataMaterialEmissionEntity
import com.nu.bom.core.manufacturing.entities.masterdata.material.MasterdataMaterialParent
import com.nu.bom.core.manufacturing.entities.masterdata.material.MasterdataMaterialPriceEntity
import com.nu.bom.core.manufacturing.entities.masterdata.material.MaterialClassificationExtension
import com.nu.bom.core.manufacturing.entities.masterdata.material.MaterialConsumerExtension
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.manufacturing.fieldTypes.EmissionUnits
import com.nu.bom.core.manufacturing.fieldTypes.EntityGeneration
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.MdHeaderInfoFieldData
import com.nu.bom.core.manufacturing.fieldTypes.MdLookupRequestField
import com.nu.bom.core.manufacturing.fieldTypes.MdLookupRequestFieldData
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.masterdata.LovFieldDefinition
import com.nu.bom.core.manufacturing.fieldTypes.masterdata.LovFieldSchema
import com.nu.bom.core.manufacturing.masterdata.operationsAndExecutionContext.MasterdataContextOperationConfigBuilders
import com.nu.bom.core.manufacturing.service.FieldKey
import com.nu.bom.core.manufacturing.service.virtualfield.DataSourcerUpdaterProvider
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.InputDependencyModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.publicapi.dtos.configurations.masterdata.HeaderTypeConsumer
import com.nu.bom.core.service.configurations.MasterdataTsetConfigurationService.Companion.MATERIAL_HEADER_TYPE_KEY
import com.nu.bom.core.service.masterdata.MdClassificationTypes
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MdLazyMigrationUtils
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import com.nu.bom.core.utils.findAllInTree
import org.bson.types.Decimal128
import org.bson.types.ObjectId
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDate

@Service
class ElectronicComponentMdMigration : ManufacturingModelEntityMapper {
    override val changeSetId: MigrationChangeSetId
        get() = MigrationChangeSetId("2025-03-31-elco-md-migration")

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity {
        val elcoWithParent =
            (entity to entity).findAllInTree({ manu -> manu.first.type == Entities.C_PART.name }) { parentPair ->
                parentPair.first.children.map { it to parentPair.first }
            }
        return createMdEntitiesForElco(entity, elcoWithParent)
    }

    private fun createMdEntitiesForElco(
        entity: ManufacturingModelEntity,
        elcoWithParent: Sequence<Pair<ManufacturingModelEntity, ManufacturingModelEntity>>,
    ): ManufacturingModelEntity {
        val elcoFromMd =
            elcoWithParent
                .filter { it.first.masterDataSelector?.key != null }
                .distinctBy { it.first.masterDataSelector }
        if (elcoFromMd.none()) {
            return entity
        }
        return entity.copyAll(children = entity.children + createMdParent(entity, elcoFromMd))
    }

    private fun createMdParent(
        entity: ManufacturingModelEntity,
        elcoWithParent: Sequence<Pair<ManufacturingModelEntity, ManufacturingModelEntity>>,
    ): ManufacturingModelEntity {
        val mdParentId = ObjectId.get()

        val mdTimestampField = entity.fieldWithResults[Manufacturing::masterdataTimestamp.name]
        val mdTimestamp = (mdTimestampField?.value as? Decimal128)?.bigDecimalValue()

        val childrenPriceEntities = createMdPriceEntities(elcoWithParent, mdParentId, mdTimestamp)
        val childrenEmissionEntities = createMdEmissionEntities(elcoWithParent, mdParentId, mdTimestamp)

        return ManufacturingModelEntity(
            id = mdParentId,
            name = "MasterdataMaterialParentNew",
            type = Entities.MD_MATERIAL_PARENT.name,
            clazz = MasterdataMaterialParent::class.simpleName!!,
            args = emptyMap(),
            fieldWithResults =
                mapOf(
                    MasterdataMaterialParent::createMaterialPrices.name to
                        createMdCostFactorParentCreateCostFactorField(
                            entity,
                        ),
                    MasterdataMaterialParent::createMaterialEmissions.name to
                        createMdCostFactorParentCreateCostFactorField(
                            entity,
                        ),
                    MasterdataMaterialParent::masterDataTypeInternal.name to
                        FieldResultModel(
                            version = entity.version,
                            newVersion = entity.version,
                            type = Text::class.java.simpleName,
                            source = FieldResult.SOURCE.C.name,
                            value = MasterDataType.NONE.name,
                        ),
                    MasterdataMaterialParent::entityDesignation.name to
                        FieldResultModel(
                            version = entity.version,
                            newVersion = entity.version,
                            type = Text::class.java.simpleName,
                            source = FieldResult.SOURCE.C.name,
                            value = "MasterdataMaterialParentNew",
                        ),
                    MasterdataMaterialParent::displayDesignation.name to
                        FieldResultModel(
                            version = entity.version,
                            newVersion = entity.version,
                            type = Text::class.java.simpleName,
                            source = FieldResult.SOURCE.C.name,
                            value = "Material",
                        ),
                ),
            initialFieldWithResults = emptyMap(),
            children = childrenPriceEntities + childrenEmissionEntities,
        ).also {
            it.version = entity.version
            it.createdBy =
                FieldKey(
                    "materialParentCreationNew",
                    entity.id.toHexString(),
                    "MANUFACTURING",
                    "MD_MATERIAL_PARENT",
                    entity.version,
                    entity.version,
                    entity.name,
                )
        }
    }

    private fun createMdCostFactorParentCreateCostFactorField(entity: ManufacturingModelEntity): FieldResultModel =
        FieldResultModel(
            version = entity.version,
            newVersion = entity.version,
            type = EntityGeneration::class.java.simpleName,
            source = FieldResult.SOURCE.C.name,
            value = true,
            inputs =
                setOf(
                    InputDependencyModel(
                        MaterialConsumerExtension::location.name,
                        entity.id.toHexString(),
                        entity.version,
                    ),
                    InputDependencyModel(
                        MaterialConsumerExtension::headerKey.name,
                        entity.id.toHexString(),
                        entity.version,
                    ),
                ),
        )

    private fun createMdEmissionEntities(
        elcos: Sequence<Pair<ManufacturingModelEntity, ManufacturingModelEntity>>,
        mdParentId: ObjectId,
        mdTimestamp: BigDecimal?,
    ): List<ManufacturingModelEntity> =
        elcos
            .map { (elco, parentEntity) ->
                val emissionFields =
                    mapOf(
                        MasterdataMaterialEmissionEntity::denominatorUnit.name to
                            createFieldResultModel(
                                parentEntity.version,
                                Text::class.java.simpleName,
                                elco.fieldWithResults["costUnit"]?.value,
                            ),
                        MasterdataMaterialEmissionEntity::numeratorUnit.name to
                            createFieldResultModel(
                                parentEntity.version,
                                Text::class.java.simpleName,
                                elco.fieldWithResults["cO2PerUnit"]?.unit,
                            ),
                        MasterdataMaterialEmissionEntity::value.name to
                            createFieldResultModel(
                                parentEntity.version,
                                Emission::class.java.simpleName,
                                elco.fieldWithResults["cO2PerUnit"]?.value,
                                unit = EmissionUnits.KILOGRAM_CO2E.name,
                            ),
                    )

                ManufacturingModelEntity(
                    id = ObjectId.get(),
                    name = elco.name,
                    type = Entities.MD_MATERIAL_EMISSION.name,
                    clazz = MasterdataMaterialEmissionEntity::class.simpleName!!,
                    args = emptyMap(),
                    children =
                        listOf(
                            createMaterialClassificationSchema(elco, parentEntity.version, mdParentId, Entities.MD_MATERIAL_EMISSION.name),
                        ),
                    fieldWithResults =
                        createFieldWithResults(
                            parentEntity,
                            elco,
                            emissionFields,
                            HeaderTypeConsumer.MATERIAL_CO2,
                            mdTimestamp,
                            "MasterdataMaterialEmissionEntity",
                            false,
                        ),
                    initialFieldWithResults =
                        createFieldWithResults(
                            parentEntity,
                            elco,
                            emissionFields,
                            HeaderTypeConsumer.MATERIAL_CO2,
                            mdTimestamp,
                            "MasterdataMaterialEmissionEntity",
                            true,
                        ),
                ).apply {
                    version = parentEntity.version
                    entityRef = elco.name
                    createdBy =
                        FieldKey(
                            name = MasterdataMaterialParent::createMaterialEmissions.name,
                            entityId = mdParentId.toHexString(),
                            entityType = Entities.MD_MATERIAL_PARENT.name,
                            type = Entities.MD_MATERIAL_EMISSION.name,
                            version = parentEntity.version,
                            newVersion = parentEntity.version,
                            entityRef = "MasterdataMaterialParentNew",
                        )
                }
            }.toList()

    private fun createMaterialClassificationSchema(
        elco: ManufacturingModelEntity,
        parentVersion: Int,
        mdParentId: ObjectId,
        createdByEntityType: String,
    ): ManufacturingModelEntity =
        ManufacturingModelEntity(
            id = ObjectId.get(),
            name = elco.name,
            type = Entities.MD_MATERIAL_CLASSIFICATION_SCHEMA.name,
            clazz = MasterdataMaterialClassificationSchemaEntity::class.simpleName!!,
            args = emptyMap(),
            fieldWithResults =
                createMaterialClassificationSchemaFields(parentVersion, elco),
            initialFieldWithResults =
                createMaterialClassificationSchemaFields(parentVersion, elco),
            children =
                listOf(
                    createMaterialClassificationValue(elco, parentVersion, mdParentId),
                ),
        ).apply {
            version = parentVersion
            entityRef = elco.name
            createdBy =
                FieldKey(
                    name = MaterialClassificationExtension::createClassificationSchemaEntity.name,
                    entityId = mdParentId.toHexString(),
                    entityType = createdByEntityType,
                    type = Entities.MD_MATERIAL_CLASSIFICATION_SCHEMA.name,
                    version = parentVersion,
                    newVersion = parentVersion,
                    entityRef = "MasterdataMaterialParentNew",
                )
        }

    private fun createMaterialClassificationSchemaFields(
        parentVersion: Int,
        elco: ManufacturingModelEntity,
    ) = mapOf(
        MaterialConsumerExtension::headerKey.name to createFieldResultModel(parentVersion, "Text", elco.masterDataSelector?.key!!),
        MasterdataMaterialClassificationSchemaEntity::headerTypeKey.name to
            createFieldResultModel(
                parentVersion,
                Text::class.java.simpleName,
                MATERIAL_HEADER_TYPE_KEY,
            ),
        MasterdataMaterialClassificationSchemaEntity::mdHeaderInfo.name to
            createFieldResultModel(
                parentVersion,
                "MdHeaderInfoField",
                MdHeaderInfoFieldData(
                    classifications =
                        mapOf(
                            MdClassificationTypes.MATERIAL to listOf("tset.ref.classification.electronic-component"),
                        ),
                    fieldDefinitions =
                        listOf(
                            LovFieldDefinition(
                                key = "tset.ref.field.manufacturer",
                                fieldSchema = LovFieldSchema("Manufacturer", "tset.ref.lov-type.elco.manufacturer"),
                            ),
                            LovFieldDefinition(
                                key = "tset.ref.field.mounting-type",
                                fieldSchema = LovFieldSchema("Mounting type", "tset.ref.lov-type.mounting-type"),
                            ),
                        ),
                ),
            ),
    )

    private fun createMaterialClassificationValue(
        elco: ManufacturingModelEntity,
        parentVersion: Int,
        mdParentId: ObjectId,
    ): ManufacturingModelEntity {
        val manufacturer = elco.fieldWithResults["manufacturer"]!!
        val mountingType = elco.fieldWithResults["mountingType"]!!
        return ManufacturingModelEntity(
            id = ObjectId.get(),
            name = elco.name,
            type = Entities.MD_MATERIAL_CLASSIFICATION_VALUE.name,
            clazz = MasterdataMaterialClassificationValueEntity::class.simpleName!!,
            args = emptyMap(),
            fieldWithResults =
                createMaterialClassificationValueFields(parentVersion, elco, manufacturer, mountingType),
            initialFieldWithResults =
                createMaterialClassificationValueFields(parentVersion, elco, manufacturer, mountingType),
        ).apply {
            version = parentVersion
            entityRef = elco.name
            createdBy =
                FieldKey(
                    name = MasterdataMaterialClassificationSchemaEntity::createClassificationValueEntity.name,
                    entityId = mdParentId.toHexString(),
                    entityType = Entities.MD_MATERIAL_CLASSIFICATION_SCHEMA.name,
                    type = Entities.MD_MATERIAL_CLASSIFICATION_VALUE.name,
                    version = parentVersion,
                    newVersion = parentVersion,
                    entityRef = "MasterdataMaterialParentNew",
                )
        }
    }

    private fun createMaterialClassificationValueFields(
        parentVersion: Int,
        elco: ManufacturingModelEntity,
        manufacturer: FieldResultModel,
        mountingType: FieldResultModel,
    ) = mapOf(
        MaterialConsumerExtension::headerKey.name to
            createFieldResultModel(parentVersion, Text::class.java.simpleName, elco.masterDataSelector?.key!!),
        ElectronicComponent.MANUFACTURER_TYPE_MASTER_DATA_FIELD_NAME to manufacturer,
        ElectronicComponent.MOUNTING_TYPE_MASTER_DATA_FIELD_NAME to mountingType,
    )

    private fun createMdPriceEntities(
        elcos: Sequence<Pair<ManufacturingModelEntity, ManufacturingModelEntity>>,
        mdParentId: ObjectId,
        mdTimestamp: BigDecimal?,
    ): List<ManufacturingModelEntity> =
        elcos
            .map { (elco, parentEntity) ->
                val priceFields =
                    mapOf(
                        MasterdataMaterialPriceEntity::denominatorUnit.name to
                            createFieldResultModel(
                                parentEntity.version,
                                Text::class.java.simpleName,
                                elco.fieldWithResults["costUnit"]?.value,
                            ),
                        MasterdataMaterialPriceEntity::numeratorCurrency.name to
                            createFieldResultModel(
                                parentEntity.version,
                                Text::class.java.simpleName,
                                elco.fieldWithResults["baseCurrency"]?.value,
                            ),
                        MasterdataMaterialPriceEntity::value.name to
                            createFieldResultModel(
                                parentEntity.version,
                                "Money",
                                elco.fieldWithResults["pricePerUnit"]?.value,
                            ),
                    )
                ManufacturingModelEntity(
                    id = ObjectId.get(),
                    name = elco.name,
                    type = Entities.MD_MATERIAL_PRICE.name,
                    clazz = MasterdataMaterialPriceEntity::class.simpleName!!,
                    args = emptyMap(),
                    children =
                        listOf(
                            createMaterialClassificationSchema(elco, parentEntity.version, mdParentId, Entities.MD_MATERIAL_PRICE.name),
                        ),
                    fieldWithResults =
                        createFieldWithResults(
                            parentEntity,
                            elco,
                            priceFields,
                            HeaderTypeConsumer.MATERIAL_PRICE,
                            mdTimestamp,
                            "MasterdataMaterialPriceEntity",
                            false,
                        ),
                    initialFieldWithResults =
                        createFieldWithResults(
                            parentEntity,
                            elco,
                            priceFields,
                            HeaderTypeConsumer.MATERIAL_PRICE,
                            mdTimestamp,
                            "MasterdataMaterialPriceEntity",
                            false,
                        ),
                ).apply {
                    version = parentEntity.version
                    entityRef = elco.name
                    createdBy =
                        FieldKey(
                            name = MasterdataMaterialParent::createMaterialPrices.name,
                            entityId = mdParentId.toHexString(),
                            entityType = Entities.MD_MATERIAL_PARENT.name,
                            type = Entities.MD_MATERIAL_PRICE.name,
                            version = parentEntity.version,
                            newVersion = parentEntity.version,
                            entityRef = "MasterdataMaterialParentNew",
                        )
                }
            }.toList()

    private fun createFieldResultModel(
        version: Int,
        type: String,
        value: Any?,
        systemValue: Any? = null,
        source: String = FieldResult.SOURCE.C.name,
        unit: String? = null,
    ): FieldResultModel =
        FieldResultModel(
            version = version,
            newVersion = version,
            type = type,
            value = value,
            source = source,
            systemValue = systemValue,
            unit = unit,
        )

    private fun createFieldWithResults(
        parentEntity: ManufacturingModelEntity,
        elco: ManufacturingModelEntity,
        fields: Map<String, FieldResultModel>,
        headerConsumer: HeaderTypeConsumer,
        mdTimestamp: BigDecimal?,
        simpleClassName: String,
        initial: Boolean = false,
    ): Map<String, FieldResultModel> {
        val headerKey = elco.masterDataSelector?.key!!
        val dimension =
            elco.fieldWithResults["dimension"]?.value ?: elco.initialFieldWithResults["dimension"]?.value
        val location =
            parentEntity.fieldWithResults["location"]?.value ?: parentEntity.initialFieldWithResults["location"]?.value
        val locationName =
            parentEntity.fieldWithResults["locationName"]?.value
                ?: parentEntity.initialFieldWithResults["locationName"]?.value
        val behaviourField =
            MdLazyMigrationUtils.createMdBehaviourField(
                parentManufacturing = parentEntity,
                elco,
                simpleClassName,
            )

        val lookupField = createMdCostFactorLookupField(elco, mdTimestamp, headerConsumer, headerKey)
        val additionalFields =
            mapOf(
                MaterialConsumerExtension::headerKey.name to createFieldResultModel(parentEntity.version, "Text", headerKey),
                MaterialConsumerExtension::location.name to createFieldResultModel(parentEntity.version, "Text", location),
                MasterdataContextOperationConfigBuilders.LOOKUP_FIELD_NAME to lookupField,
                MasterdataEntity::masterDataTypeInternal.name to
                    FieldResultModel(
                        version = elco.version,
                        newVersion = elco.version,
                        type = Text::class.java.simpleName,
                        source = FieldResult.SOURCE.C.name,
                        value = MasterDataType.NONE.name,
                    ),
                DataSourcerUpdaterProvider.VIRTUAL_FIELD_NAME to lookupField.copyAll(),
                MasterdataEntity::createBehaviour.name to behaviourField,
                REQUEST_EFFECTIVITY_TSET_REF_FIELD_REGION to
                    createFieldResultModel(
                        parentEntity.version,
                        Text::class.java.simpleName,
                        location,
                    ),
                REQUEST_EFFECTIVITY_TSET_REF_FIELD_REGION_DISPLAY_NAME to
                    createFieldResultModel(
                        parentEntity.version,
                        Text::class.java.simpleName,
                        locationName,
                        locationName,
                    ),
                "executeLookup" to
                    createFieldResultModel(
                        parentEntity.version,
                        "Bool",
                        false,
                        null,
                        FieldResult.SOURCE.C.name,
                    ),
                ACTUAL_EFFECTIVITY_TSET_DATA_SOURCE_FIELD to
                    createFieldResultModel(
                        parentEntity.version,
                        Text::class.java.simpleName,
                        "tset.tset",
                    ),
                ACTUAL_EFFECTIVITY_TSET_DATA_SOURCE_FIELD_DISPLAY_NAME to
                    createFieldResultModel(
                        parentEntity.version,
                        Text::class.java.simpleName,
                        "Tset",
                    ),
                ACTUAL_EFFECTIVITY_TSET_REF_FIELD_REGION to
                    createFieldResultModel(
                        parentEntity.version,
                        "Null",
                        null,
                    ),
                ACTUAL_EFFECTIVITY_TSET_REF_FIELD_REGION_DISPLAY_NAME to
                    createFieldResultModel(
                        parentEntity.version,
                        "Null",
                        null,
                    ),
                "mdDetailModifier" to createFieldResultModel(parentEntity.version, Text::class.java.simpleName, "TSET reference data"),
                "mdDetailModificationDate" to createFieldResultModel(parentEntity.version, "Date", LocalDate.now()),
                "dimension" to createFieldResultModel(parentEntity.version, "Dimension", dimension),
            )
        return if (initial) {
            (fields + additionalFields).filterKeys { key ->
                key in
                    setOf(
                        "headerKey",
                        "location",
                        "headerDisplayName",
                        "mdDetailModificationDate",
                        "mdDetailModifier",
                        "value",
                        BaseMaterial::baseCurrency.name,
                        BaseMaterial::dimension.name,
                        MasterdataMaterialPriceEntity::denominatorUnit.name,
                        MasterdataMaterialPriceEntity::numeratorCurrency.name,
                        MasterdataMaterialEmissionEntity::numeratorUnit.name,
                        ACTUAL_EFFECTIVITY_TSET_DATA_SOURCE_FIELD,
                        ACTUAL_EFFECTIVITY_TSET_DATA_SOURCE_FIELD_DISPLAY_NAME,
                    )
            }
        } else {
            fields + additionalFields
        }
    }

    private fun createMdCostFactorLookupField(
        locationEntity: ManufacturingModelEntity,
        mdTimestamp: BigDecimal?,
        headerTypeConsumer: HeaderTypeConsumer,
        headerKey: String,
    ) = FieldResultModel(
        version = locationEntity.version,
        newVersion = locationEntity.version,
        type = MdLookupRequestField::class.java.simpleName,
        source = FieldResult.SOURCE.C.name,
        value =
            MdLookupRequestFieldData(
                strategyKey = "tset.ref.strategy.material",
                headerTypeKey = "tset.ref.header-type.material",
                headerKey = headerKey,
                headerTypeConsumer = headerTypeConsumer,
                effectivities = emptyList(),
                executeLookup = false,
                timestampEpochMillis = mdTimestamp,
            ),
    )
}
