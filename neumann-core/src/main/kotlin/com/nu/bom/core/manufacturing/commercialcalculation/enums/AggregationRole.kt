package com.nu.bom.core.manufacturing.commercialcalculation.enums

import com.nu.bom.core.manufacturing.commercialcalculation.fieldnamebuilders.FieldNameSection

/**
 * This enum defines the roles within an aggregation level, if there are several results that
 * need to be kept separately and aggregated.
 * The enum also defines one section of the field name
 */
enum class AggregationRole(override val fieldNameSection: String) : FieldNameSection {
    THIS("This"),
    SUB("Sub"),
    TOTAL("Total"),

    INHOUSE("InHouse"),
    INHOUSE_SUB("InHouseSub"),
    INHOUSE_THIS("InHouseThis"),
    PURCHASE("Purchase"),
}
