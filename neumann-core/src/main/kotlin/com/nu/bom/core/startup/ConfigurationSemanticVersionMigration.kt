package com.nu.bom.core.startup

import com.nu.bom.core.model.AccountId
import com.nu.bom.core.service.configurations.ConfigurationService
import com.nu.bom.core.utils.Constants
import com.tset.core.module.mongodb.infrastructure.document.ActiveConfigurationDocument
import com.tset.core.module.mongodb.infrastructure.document.ConfigurationDocument
import com.tset.core.module.mongodb.infrastructure.document.DefaultConfigurationDocument
import com.tset.core.module.mongodb.infrastructure.document.VersionedConfigurationDocument
import io.mongock.api.annotations.ChangeUnit
import io.mongock.api.annotations.Execution
import io.mongock.api.annotations.RollbackExecution
import org.bson.Document
import org.bson.types.ObjectId
import org.slf4j.LoggerFactory
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.aggregate
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.AggregationOperation
import org.springframework.data.mongodb.core.aggregation.ArithmeticOperators
import org.springframework.data.mongodb.core.aggregation.ArrayOperators
import org.springframework.data.mongodb.core.aggregation.BooleanOperators
import org.springframework.data.mongodb.core.aggregation.ComparisonOperators
import org.springframework.data.mongodb.core.aggregation.ConditionalOperators
import org.springframework.data.mongodb.core.aggregation.EvaluationOperators
import org.springframework.data.mongodb.core.aggregation.LiteralOperators
import org.springframework.data.mongodb.core.aggregation.MergeOperation
import org.springframework.data.mongodb.core.aggregation.ObjectOperators
import org.springframework.data.mongodb.core.aggregation.StringOperators
import org.springframework.data.mongodb.core.aggregation.VariableOperators
import org.springframework.data.mongodb.core.asType
import org.springframework.data.mongodb.core.distinct
import org.springframework.data.mongodb.core.index.Index
import org.springframework.data.mongodb.core.index.PartialIndexFilter
import org.springframework.data.mongodb.core.indexOps
import org.springframework.data.mongodb.core.query.isEqualTo
import org.springframework.data.mongodb.core.query.ne
import org.springframework.data.mongodb.core.query.nin
import kotlin.reflect.KProperty

@ChangeUnit(
    id = "config-semantic-version",
    order = "2024-09-16_001",
)
@Suppress("unused")
class ConfigurationSemanticVersionMigration(private val template: MongoTemplate) {
    companion object {
        private val logger = LoggerFactory.getLogger(ConfigurationSemanticVersionMigration::class.java)
        private const val ACTIVE = "active"
        private const val DEFAULT = "default"

        private fun reference(prop: KProperty<*>): String = "$${prop.name}"

        private fun variable(name: String): String = "$$$name"

        // We don't touch data from the legacy collections, but lookups and templates are no longer stored in config service
        private val excludeLegacyGroups = Aggregation.match(ConfigurationDocument::groupKey nin listOf("Lookups", "Templates"))

        private val latestPerConfig =
            listOf(
                Aggregation.group(
                    ConfigurationDocument::groupKey.name,
                    ConfigurationDocument::type.name,
                    ConfigurationDocument::key.name,
                ).and("latest") {
                    // No top operator in the builders...
                    Document()
                        .append(
                            "\$top",
                            Document()
                                .append("output", variable("ROOT"))
                                .append("sortBy", Document().append(ConfigurationDocument::instanceVersion.name, -1)),
                        )
                },
                Aggregation.replaceRoot("latest"),
            )

        private val joinConfigCollections =
            Aggregation
                // Lookup is like a left-outer join. Instead of several "rows", the results are placed in an array in the document
                .lookup()
                .from(ActiveConfigurationDocument.COLLECTION_NAME)
                .let(
                    // Bind variables from ConfigurationDocument to be used in pipeline
                    VariableOperators.Let.just(
                        VariableOperators.Let.ExpressionVariable
                            .newVariable("id")
                            .forField(ConfigurationDocument::_id.name),
                        VariableOperators.Let.ExpressionVariable
                            .newVariable("account")
                            .forField(ConfigurationDocument::accountId.name),
                    ),
                ).pipeline(
                    // Match on id and account
                    BooleanOperators.And.and()
                        // These builders are pretty terrible and I can't for the life of me figure out how to reference let-variables
                        .andExpression {
                            Document().append(
                                "\$eq",
                                listOf(reference(ActiveConfigurationDocument::configurationId), variable("id")),
                            )
                        }.andExpression {
                            Document().append(
                                "\$eq",
                                listOf(reference(ActiveConfigurationDocument::accountIdUser), variable("account")),
                            )
                        }.let { Aggregation.match(EvaluationOperators.Expr.valueOf(it)) },
                    // Nested lookup for default. We don't need the account id as active and default entries are already account-specific
                    Aggregation
                        .lookup()
                        .from(DefaultConfigurationDocument.COLLECTION_NAME)
                        .localField(ActiveConfigurationDocument::_id.name)
                        .foreignField(DefaultConfigurationDocument::activeConfigurationId.name)
                        .`as`(DEFAULT),
                ).`as`(ACTIVE)

        private val projectToVersioned =
            Aggregation
                .project()
                .andExclude(ConfigurationDocument::_id.name)
                .and(ConfigurationDocument::accountId.name).`as`(VersionedConfigurationDocument::accountId.name)
                .and(ConfigurationDocument::groupKey.name).`as`(VersionedConfigurationDocument::group.name)
                .and(ConfigurationDocument::type.name).`as`(VersionedConfigurationDocument::type.name)
                .and(ConfigurationDocument::key.name).`as`(VersionedConfigurationDocument::key.name)
                .and(
                    // Increment by one as instance versions start at 0
                    ArithmeticOperators.Add
                        .valueOf(ConfigurationDocument::instanceVersion.name)
                        .add(1),
                ).`as`(VersionedConfigurationDocument::majorVersion.name)
                .and(LiteralOperators.Literal.asLiteral(0)).`as`(VersionedConfigurationDocument::minorVersion.name)
                .and(ConfigurationDocument::configurationValue.name).`as`(VersionedConfigurationDocument::configurationValue.name)
                .and(ConfigurationDocument::displayName.name).`as`(VersionedConfigurationDocument::displayName.name)
                .and(
                    ComparisonOperators
                        .valueOf(ArrayOperators.Size.lengthOfArray(ACTIVE))
                        .greaterThanValue(0),
                ).`as`(VersionedConfigurationDocument::isActive.name)
                .and(
                    ObjectOperators.GetField
                        .getField(DEFAULT)
                        .of(ArrayOperators.First.firstOf(ACTIVE))
                        .let { defaultArrayOrNull ->
                            ConditionalOperators
                                .ifNull(defaultArrayOrNull)
                                .then(emptyList<Any>())
                        }.let { defaultArray ->
                            ComparisonOperators
                                .valueOf(ArrayOperators.Size.lengthOfArray(defaultArray))
                                .greaterThanValue(0)
                        },
                ).`as`(VersionedConfigurationDocument::isDefault.name)
                .and(
                    LiteralOperators.Literal.asLiteral(VersionedConfigurationDocument::class.qualifiedName!!),
                ).`as`("_class")
    }

    @Execution
    fun migrate() {
        val accountIds = getAccounts()
        if (accountIds.isEmpty()) {
            logger.info("No legacy configurations to migrate, skipping migration.")
            return
        }

        if (template.collectionExists(VersionedConfigurationDocument::class.java)) {
            logger.error("VersionedConfiguration collection already exists, this migration should be run before config initialization.")
            error("VersionedConfiguration collection already exists")
        }

        val global = migrationPipeline(globalPipelineStages(accountIds))
        logger.info("Migrating global configurations...")
        template.aggregate<ConfigurationDocument, VersionedConfigurationDocument>(global)

        val accountLocal =
            migrationPipeline(
                listOf(Aggregation.match(ConfigurationDocument::accountId ne Constants.GLOBAL_ACCOUNT_ID)),
            )
        logger.info("Migrating user configurations...")
        template.aggregate<ConfigurationDocument, VersionedConfigurationDocument>(accountLocal)

        logger.info("Creating indices...")
        createIndices()

        logger.info("Finished semantic version migration")
    }

    private fun migrationPipeline(injectStages: List<AggregationOperation>): Aggregation {
        val stages = mutableListOf<AggregationOperation>()
        stages.add(excludeLegacyGroups)
        stages.addAll(injectStages)
        stages.add(joinConfigCollections)
        stages.add(projectToVersioned)
        stages.add(
            Aggregation
                .merge()
                .intoCollection(VersionedConfigurationDocument.COLLECTION_NAME)
                .whenMatched(MergeOperation.WhenDocumentsMatch.failOnMatch())
                .build(),
        )

        return Aggregation.newAggregation(stages)
    }

    private fun globalPipelineStages(accountIds: List<AccountId>): List<AggregationOperation> =
        listOf(Aggregation.match(ConfigurationDocument::accountId isEqualTo Constants.GLOBAL_ACCOUNT_ID))
            .plus(latestPerConfig)
            .plus(createAccountCopies(accountIds))

    private fun createAccountCopies(accountIds: List<AccountId>): List<AggregationOperation> =
        listOf(
            Aggregation
                .addFields()
                // Prefix key with .tset
                .addFieldWithValueOf(
                    ConfigurationDocument::key.name,
                    StringOperators.Concat
                        .stringValue(ConfigurationService.TSET_KEY_PREFIX)
                        .concatValueOf(ConfigurationDocument::key.name),
                ).addFieldWithValue(ConfigurationDocument::accountId.name, accountIds)
                // hard-code to zero to create single 1.0 document later
                .addFieldWithValue(ConfigurationDocument::instanceVersion.name, 0)
                .build(),
            // Create copy of config for each entry in array (= for each account)
            Aggregation.unwind(ConfigurationDocument::accountId.name),
        )

    private fun getAccounts(): List<AccountId> =
        template
            .distinct(DefaultConfigurationDocument::accountId)
            .asType<ObjectId>()
            .all()

    private fun createIndices() {
        val uniqueId =
            Index()
                .named("UniqueConfigurationId")
                .on(VersionedConfigurationDocument::accountId.name, Sort.DEFAULT_DIRECTION)
                .on(VersionedConfigurationDocument::group.name, Sort.DEFAULT_DIRECTION)
                .on(VersionedConfigurationDocument::type.name, Sort.DEFAULT_DIRECTION)
                .on(VersionedConfigurationDocument::key.name, Sort.DEFAULT_DIRECTION)
                .on(VersionedConfigurationDocument::majorVersion.name, Sort.Direction.DESC)
                .on(VersionedConfigurationDocument::minorVersion.name, Sort.Direction.DESC)
                .unique()

        val uniqueDefaultPerType =
            Index()
                .named("UniqueDefaultPerType")
                .partial(PartialIndexFilter.of(VersionedConfigurationDocument::isDefault isEqualTo true))
                .on(VersionedConfigurationDocument::accountId.name, Sort.DEFAULT_DIRECTION)
                .on(VersionedConfigurationDocument::group.name, Sort.DEFAULT_DIRECTION)
                .on(VersionedConfigurationDocument::type.name, Sort.DEFAULT_DIRECTION)
                .unique()

        val indexOps = template.indexOps<VersionedConfigurationDocument>()
        indexOps.ensureIndex(uniqueId)
        indexOps.ensureIndex(uniqueDefaultPerType)
    }

    @RollbackExecution
    fun rollback() {
        template.dropCollection(VersionedConfigurationDocument::class.java)
    }
}
