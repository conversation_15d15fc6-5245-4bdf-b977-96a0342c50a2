package com.nu.bom.core.manufacturing.service

import com.google.common.annotations.VisibleForTesting
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.InputDependency
import com.tset.common.util.BARE_BONE_NOT_NULL_STYLE
import org.apache.commons.lang3.builder.ToStringBuilder

data class InputKey(
    val paramPos: Int,
    val name: String,
    val entityId: String,
    val entityRef: String? = null,
    val type: String,
    val entityType: String,
    val relType: RelType? = null,
    val default: FieldResultStar?,
    val nullable: Boolean = false,
    val linkedField: Field?,
    val extResult: ExtResult? = null,
    val childDependency: ChildDependency = ChildDependency(),
) {
    /**
     * Return true, if there is already a result usable for the given version, or if it's nullable, then null result would be also fine.
     */
    fun hasResultForVersion(
        version: Int,
        visitedFields: Set<Field>,
    ): Boolean = hasResultForVersionCore(version) { visitedFields.contains(it) }

    fun hasResultForVersion(
        version: Int,
        visitedField: Field,
    ): Boolean = hasResultForVersionCore(version) { it == visitedField }

    fun hasResultForVersion(version: Int): Boolean = hasResultForVersionCore(version) { false }

    private inline fun hasResultForVersionCore(
        version: Int,
        crossinline isVisitedField: (Field) -> Boolean,
    ): Boolean {
        if (extResult != null) return true
        return if (linkedField == null) {
            default != null
        } else {
            if (isVisitedField(linkedField)) {
                false
            } else {
                linkedField.hasResultForVersion(version, nullResultOk = nullable)
            }
        }
    }

    /**
     * Return true, if there is already an external result, or the linked field is calculated, and has result.
     */
    fun hasCurrentResult(): Boolean = extResult != null || (linkedField != null && linkedField.hasCurrentResult())

    fun toInputDependency(): InputDependency =
        linkedField?.toInputDependency()
            ?: (
                extResult?.inputDependency
                    ?: error("LinkedField and extResult both empty - ($this)")
            )

    fun addSuccessor(successor: Field) {
        linkedField?.addSuccessorField(successor)
    }

    fun removeSuccessor(successor: Field) {
        linkedField?.removeSuccessorField(successor)
    }

    fun getFieldResult(version: Int): FieldResultStar? {
        val fieldResult = linkedField?.getResultForVersion(version)
        if (fieldResult != null) {
            return fieldResult
        }
        if (extResult?.result != null) {
            return extResult.result
        }

        if (nullable) {
            return null
        }

        if (default != null) {
            return default
        }
        throw IllegalArgumentException("No result calculated for $this with version=$version!")
    }

    fun fieldIdKey() = FieldIdKey(name = this.name, entityId = this.entityId)

    @VisibleForTesting
    fun toDetailedName(): String {
        val builder =
            ToStringBuilder(this, BARE_BONE_NOT_NULL_STYLE)
                .append("pos", paramPos)
                .append("name", name)
                .append("type", entityType)
                .append("entity", entityRef ?: entityId)
                .append("rel", relType)
                .append("extResult", extResult)
                .append("result", linkedField?.getCurrentResult())
                .append("resultVersion", linkedField?.resultVersion)
                .append("resultNewVersion", linkedField?.resultNewVersion)
        if (linkedField == null) {
            builder.append("no-field-found")
        }
        return builder.toString()
    }
}
