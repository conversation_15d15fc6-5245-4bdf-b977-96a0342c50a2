package com.nu.bom.core.service

import com.nu.bom.core.api.dtos.AutocompleteResponse
import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.TranslationSection
import com.nu.bom.core.manufacturing.annotations.toMetaInfoEntry
import com.nu.bom.core.manufacturing.fieldTypes.Pressure
import com.nu.bom.core.manufacturing.fieldTypes.PressureUnits
import com.nu.bom.core.manufacturing.fieldTypes.ThreeDbVersionedPart
import com.nu.bom.core.manufacturing.fieldTypes.Volume
import com.nu.bom.core.publicapi.service.PublicApiMetaInfoService.Companion.isUserUploadShape
import com.nu.bom.core.threedb.ThreeDbService
import com.nu.bom.core.threedb.ThreeDbShapeIdentifier
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.Constants.TSET_3DB_SHARED_ACCOUNT_IDENTIFIER
import com.nu.bom.core.utils.Constants.TSET_ACCOUNT_IDENTIFIER
import com.nu.bom.core.utils.ShapesData
import com.nu.bom.core.utils.getPage
import com.tset.bom.clients.common.WithLog
import com.tset.bom.clients.common.log
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono

data class ExtendedShapeInfo(
    val shapeInfo: ShapesData.ShapeInfo,
    val threeDbShapeIdentifier: ThreeDbShapeIdentifier,
) {
    init {
        require(shapeInfo.shapeId == threeDbShapeIdentifier.shape_id)

        val beingSharedIsConsistent =
            (shapeInfo.accountIdentifier == TSET_ACCOUNT_IDENTIFIER) ==
                (threeDbShapeIdentifier.account == TSET_3DB_SHARED_ACCOUNT_IDENTIFIER)

        val couldBeShared = shapeInfo.accountIdentifier == TSET_ACCOUNT_IDENTIFIER

        val sameAccount = shapeInfo.accountIdentifier == threeDbShapeIdentifier.account

        require(
            beingSharedIsConsistent && (couldBeShared || sameAccount),
        )
    }

    fun shapeId() = shapeInfo.shapeId

    fun isAccountSpecificShape(): Boolean = shapeInfo.accountIdentifier != TSET_ACCOUNT_IDENTIFIER

    companion object {
        fun fromShapeInfo(
            shapeInfo: ShapesData.ShapeInfo,
            part: ThreeDbVersionedPart?,
        ): ExtendedShapeInfo {
            val threeDbAccount =
                when (shapeInfo.accountIdentifier) {
                    TSET_ACCOUNT_IDENTIFIER -> TSET_3DB_SHARED_ACCOUNT_IDENTIFIER
                    else -> shapeInfo.accountIdentifier
                }

            return ExtendedShapeInfo(shapeInfo, ThreeDbShapeIdentifier(shapeInfo.shapeId, threeDbAccount, part))
        }
    }
}

/**
 * Provides interface for shapes data
 *
 * @see ShapesData
 */
@Service
class ShapesService(
    private val shapesData: ShapesData,
    private val threeDbService: ThreeDbService,
) : WithLog {
    fun getActiveShapes(
        accessCheck: AccessCheck,
        wizardId: String,
        technology: String,
        terms: List<String>? = null,
        volume: Volume? = null,
    ): Mono<List<ExtendedShapeInfo>> {
        val termsAsSet = terms?.toSet() ?: emptySet()
        val activeShapes = shapesData.getShapes(accessCheck, technology)
        if (activeShapes.isEmpty()) {
            log.warn("getActiveShapes was called for '$technology', but there are no active shapes •`_´•")
            return Mono.just(emptyList())
        }

        return threeDbService.getShapes(accessCheck).map { listOfThreeDbShapes ->
            val threeDbShapes = listOfThreeDbShapes.associateBy { it.shape_id }
            activeShapes.mapNotNull { activeShape ->
                val maybeThreeDbShape = threeDbShapes[activeShape.shapeId]
                when {
                    isUserUploadShape(activeShape) -> {
                        require(
                            maybeThreeDbShape == null,
                        ) { "userUploadShape '${activeShape.shapeId}' of technology $technology is not a real shape" }
                        ExtendedShapeInfo.fromShapeInfo(activeShape, null)
                    }
                    maybeThreeDbShape == null -> {
                        log.warn("for $technology, ${activeShape.shapeId} is active but nowhere to be found in threeDb")
                        null
                    }
                    else -> ExtendedShapeInfo(activeShape, maybeThreeDbShape)
                }
            }.filter {
                (termsAsSet.isEmpty() || it.shapeInfo.keywords.intersect(termsAsSet).isNotEmpty()) &&
                    volume.isBetween(it.shapeInfo.minVolume, it.shapeInfo.maxVolume)
            }
        }
    }

    /**
     * Gets list of keywords for specified technology for auto-completion.
     *
     * @param technology Technology name (DCA, BART, ...)
     * @param subTerm Searched term (substring) in keywords
     * @param volume Requested volume
     * @return list of auto-complete records
     */
    fun getKeywords(
        accessCheck: AccessCheck,
        wizardId: String,
        technology: String,
        subTerm: String?,
        volume: Volume?,
    ): Mono<List<AutocompleteResponse>> =
        getActiveShapes(accessCheck, wizardId, technology, terms = null, volume).map { activeShapes ->
            activeShapes.flatMap { it.shapeInfo.keywords }
                .filter {
                    subTerm == null || it.contains(subTerm.lowercase())
                }
                .distinct()
                .map {
                    AutocompleteResponse(name = it, key = it, section = null)
                }
        }

    fun getShapeIdsBy(
        accessCheck: AccessCheck,
        wizardId: String,
        technology: String,
    ): Mono<List<AutocompleteResponse>> =
        getActiveShapes(accessCheck, wizardId, technology).map { activeShapes ->
            activeShapes.map { AutocompleteResponse(it.shapeId(), it.shapeId(), section = null) }
        }

    /**
     * Gets paged list of shape information.
     *
     * @param technology Technology name (DCA, BART, ...)
     * @param terms Searched terms (substring) in keywords
     * @param volume Requested volume
     * @param page Page number (0-based)
     * @param size Size of the page
     * @return list of shape info records
     */
    fun getShapesPaged(
        accessCheck: AccessCheck,
        wizardId: String,
        technology: String,
        terms: List<String>?,
        volume: Volume?,
        page: Int,
        size: Int,
    ): Mono<List<ThreeDbShapeIdentifier>> =
        getActiveShapes(accessCheck, wizardId, technology, terms, volume).map { activeShapes ->
            activeShapes.map { it.threeDbShapeIdentifier }.getPage(page, size)
        }

    /**
     * Gets one shape info by technology & ID.
     *
     * @param technology Technology name (DCA, BART, ...)
     * @param shapeId ID of shape in particular [technology]
     * @return Shape info
     */
    fun getById(
        accessCheck: AccessCheck,
        technology: String,
        shapeId: String,
    ): ShapesData.ShapeInfo? = shapesData.getById(accessCheck, technology = technology, shapeId = shapeId)

    /**
     * Gets shape by specified active technology and ID with existing check.
     *
     * @param technology Technology model
     * @param shapeId ID
     * @return shape data if shape exists and inputs are not null, null if any input is null or shape not found
     */
    fun getByIdCheck(
        accessCheck: AccessCheck,
        technology: Model?,
        shapeId: String?,
    ): ShapesData.ShapeInfo? =
        if (shapeId != null && technology != null) {
            getById(accessCheck, technology.path, shapeId) ?: null.also {
                log.warn("Unable to find shape information for rawPartTechnology=$technology and shapeId=$shapeId")
            }
        } else {
            null
        }

    /**
     * Gets fields related to one shape.
     *
     * @param technology Technology name (DCA, BART, ...)
     * @param shapeId ID of shape in particular [technology]
     * @return list of attached fields
     */
    fun getShapeFields(
        accessCheck: AccessCheck,
        technology: String,
        shapeId: String,
    ): List<FieldParameter> {
        val shapeInfo = getById(accessCheck, technology, shapeId) ?: return emptyList()
        return shapesData.getTechnologyFields(accessCheck.accountName, technology).mapNotNull { fieldName ->
            shapeInfo.getField(fieldName)?.let {
                val baseFieldParameter =
                    FieldParameter(
                        name = fieldName,
                        type = it.type,
                        unit = it.unit,
                        value = it.value,
                        metaInfo =
                            mapOf(
                                "translationSection" to TranslationSection.SELECTABLES,
                                "translationLabel" to "${fieldName}_shapeField",
                            ),
                        // TODO: Why the heck do we have a special ShapeField type which is basically a FE field, but lacks e.g. a denominator unit? …
                        denominatorUnit = null,
                    )
                shapeFieldsLastTouch(baseFieldParameter)
            }
        }
    }

    private fun ShapesData.ShapeInfo.getField(name: String) = fields.find { it.name == name }

    private fun Volume?.isBetween(
        minVolume: Volume?,
        maxVolume: Volume?,
    ): Boolean =
        this == null || (
            (minVolume == null || minVolume.inCm <= this.inCm) &&
                (maxVolume == null || maxVolume.inCm >= this.inCm)
        )

    companion object {
        private fun shapeFieldsLastTouch(shapeField: FieldParameter): FieldParameter {
            return when (shapeField.name) {
                "castingPressure" ->
                    shapeField.copy(
                        valueInDefaultUnit =
                            Pressure(
                                shapeField.value.toString().toBigDecimalOrNull()!!,
                                PressureUnits.PASCAL,
                            ).inBar.toPlainString(),
                        metaInfo = shapeField.metaInfo!!.plus(toMetaInfoEntry(PressureUnits.BAR.name)),
                    )
                else -> shapeField
            }
        }
    }
}
