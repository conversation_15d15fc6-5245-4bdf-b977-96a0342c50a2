package com.nu.bom.core.utils

import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.manufacturing.fieldTypes.ThreadingType
import com.nu.bom.core.smf.model.SmfUnfoldedPartComponentLine
import com.nu.bom.core.smf.model.SmfUnfoldedPartLineContent
import com.nu.bom.core.smf.model.SmfUnfoldedPartPoint
import com.tset.bom.clients.geometry.Point2D
import kotlin.math.abs
import kotlin.math.roundToInt

class SketchScaling {
    data class BoundingBox(
        val min_x: Double,
        val min_y: Double,
        val max_x: Double,
        val max_y: Double,
    ) {
        constructor(points: List<Point2D<Double>>) :
            this(
                min_x = points.minOfOrNull { it.x }!!,
                max_x = points.maxOfOrNull { it.x }!!,
                min_y = points.minOfOrNull { it.y }!!,
                max_y = points.maxOfOrNull { it.y }!!,
            )

        fun toPolygon() =
            listOf(
                SmfUnfoldedPartComponentLine(
                    SmfUnfoldedPartLineContent(
                        SmfUnfoldedPartPoint(min_x, min_y),
                        SmfUnfoldedPartPoint(max_x, min_y),
                    ),
                ),
                SmfUnfoldedPartComponentLine(
                    SmfUnfoldedPartLineContent(
                        SmfUnfoldedPartPoint(max_x, min_y),
                        SmfUnfoldedPartPoint(max_x, max_y),
                    ),
                ),
                SmfUnfoldedPartComponentLine(
                    SmfUnfoldedPartLineContent(
                        SmfUnfoldedPartPoint(max_x, max_y),
                        SmfUnfoldedPartPoint(min_x, max_y),
                    ),
                ),
                SmfUnfoldedPartComponentLine(
                    SmfUnfoldedPartLineContent(
                        SmfUnfoldedPartPoint(min_x, max_y),
                        SmfUnfoldedPartPoint(min_x, min_y),
                    ),
                ),
            )
    }

    companion object {
        fun transformBb(
            bb: BoundingBox,
            trans: (Point2D<Double>) -> Point2D<Double>,
        ): BoundingBox {
            val originalPoints =
                listOf(
                    Point2D(bb.min_x, bb.min_y),
                    Point2D(bb.min_x, bb.max_y),
                    Point2D(bb.max_x, bb.min_y),
                    Point2D(bb.max_x, bb.min_y),
                )
            return BoundingBox(originalPoints.map(trans))
        }

        // returns true if A is completely included in B
        fun bbIsSubsetOf(
            A: BoundingBox,
            B: BoundingBox,
        ): Boolean =
            (A.min_x >= B.min_x) and (A.min_y >= B.min_y) and
                (A.max_x <= B.max_x) and (A.max_y <= B.max_y)

        fun scaleMaxToolDiameterFieldParameter(
            field: FieldParameter?,
            scalingFactor: Double,
        ): FieldParameter? {
            return field?.copy(
                name = field.name,
                type = field.type,
                source = field.source,
                value = round((field.value.toString().toDouble()) * scalingFactor),
                metaInfo = field.metaInfo,
                context = field.context,
                label = field.label,
                systemValue = field.systemValue,
                unit = field.unit,
                valueInDefaultUnit = field.valueInDefaultUnit,
            )
        }

        fun scaleDepthFieldParameter(
            field: FieldParameter?,
            depthFactor: Double,
        ): FieldParameter? {
            return field?.copy(
                name = field.name,
                type = field.type,
                source = field.source,
                value = round((field.value.toString().toDouble()) * depthFactor),
                metaInfo = field.metaInfo,
                context = field.context,
                label = field.label,
                systemValue = field.systemValue,
                unit = field.unit,
                valueInDefaultUnit = field.valueInDefaultUnit,
            )
        }

        fun scaleThreadingTypeFieldParameter(
            field: FieldParameter?,
            scalingFactor: Double,
        ): FieldParameter? {
            return field?.copy(
                name = field.name,
                type = field.type,
                source = field.source,
                value =
                    if (field.value.toString().isNotEmpty()) {
                        scaleThreadingType(field.value.toString(), scalingFactor)
                    } else {
                        field.value
                    },
                metaInfo = field.metaInfo,
                context = field.context,
                label = field.label,
                systemValue = field.systemValue,
                unit = field.unit,
                valueInDefaultUnit = field.valueInDefaultUnit,
            )
        }

        fun scaleMarginFieldParameter(
            field: FieldParameter?,
            tolerance: String,
        ): FieldParameter? {
            return field?.copy(
                name = field.name,
                type = field.type,
                source = field.source,
                value = getTolerance(tolerance),
                metaInfo = field.metaInfo,
                context = field.context,
                label = field.label,
                systemValue = field.systemValue,
                unit = field.unit,
                valueInDefaultUnit = field.valueInDefaultUnit,
            )
        }

        fun getTolerance(tolerance: String): Double {
            return when (tolerance) {
                "PRECISE" -> 2.0
                "STANDARD" -> 3.0
                "BURRFREE" -> 1.5
                else -> 3.0
            }
        }

        private fun scaleThreadingType(
            type: String,
            scalingFactor: Double,
        ): String {
            val scaledThreading = type.filter { char -> char.isDigit() }.toDouble() * scalingFactor

            val closestValue =
                ThreadingType.Selection.entries.map { threadingType ->
                    threadingType.diameter
                }.minByOrNull { threadingDiameter ->
                    abs(scaledThreading - threadingDiameter)
                }!!

            return ThreadingType.valueOf(closestValue).res.value
        }

        private fun round(x: Double): Double {
            return ((x * 100).roundToInt()) / 100.toDouble()
        }
    }
}
