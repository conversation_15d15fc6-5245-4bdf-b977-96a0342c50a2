package com.nu.bom.core.service.wizard

import com.nu.bom.core.api.dtos.AutocompleteContainer
import com.nu.bom.core.api.dtos.CalculationModel
import com.nu.bom.core.api.dtos.CardRequestDto
import com.nu.bom.core.api.dtos.CardResponseDto
import com.nu.bom.core.api.dtos.MultiTermCardRequestDto
import com.nu.bom.core.api.dtos.ShapeDto
import com.nu.bom.core.api.dtos.ShapeDtoConversionService
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.factories.FieldFactoryService
import com.nu.bom.core.manufacturing.fieldTypes.Volume
import com.nu.bom.core.manufacturing.service.ConfigurationManagementServiceImpl
import com.nu.bom.core.repository.WizardRepository
import com.nu.bom.core.service.ManufacturingModelsUtils
import com.nu.bom.core.service.MaterialService
import com.nu.bom.core.service.ShapesService
import com.nu.bom.core.service.configurations.ConfigurationGroup
import com.nu.bom.core.service.wizard.steps.WizardTechStep
import com.nu.bom.core.service.wizard.steps.WizardWamLikeStepInterface
import com.nu.bom.core.user.AccessCheck
import org.bson.types.ObjectId
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toFlux
import java.util.Locale

@Service
class WizardCardService(
    private val materialService: MaterialService,
    private val shapesService: ShapesService,
    private val shapeDtoConversionService: ShapeDtoConversionService,
    private val wizardRepository: WizardRepository,
    private val fieldFactoryService: FieldFactoryService,
    private val configurationManagementServiceImpl: ConfigurationManagementServiceImpl,
) {
    fun getModels(
        accessCheck: AccessCheck,
        request: CardRequestDto,
    ): Flux<CardResponseDto> = getModels(accessCheck, request.term)

    private fun getModels(
        accessCheck: AccessCheck,
        term: String?,
    ): Flux<CardResponseDto> =
        getModels(term)
            .filter {
                it.displayState != CalculationModel.DisplayState.HIDDEN
            }.toFlux()
            .concatMap {
                configurationManagementServiceImpl
                    .getDefaultCostModuleConfigurationIdentifier(
                        accessCheck,
                        Model.valueOf(it.path.uppercase()),
                    ).map { configurationValue ->

                        val model = Model.valueOf(it.path.uppercase())

                        CardResponseDto(
                            id = it.entity,
                            images = listOf("/img/technologies/${it.entity}.png"),
                            title = it.name,
                            description = "",
                            version = configurationValue,
                            costModuleVersionUrl =
                                "/api/configuration/getAll?${ConfigurationGroup.COST_MODULES_VERSION.name}&${it.path.uppercase()}",
                            technologyPageViewGroup = model.technologyPageViewGroup,
                            label = model.label,
                        )
                    }
            }

    private fun getModels(term: String?): List<CalculationModel> =
        ManufacturingModelsUtils.findAll {
            it.name.lowercase(Locale.getDefault()).contains(term?.lowercase(Locale.getDefault()) ?: "")
        }

    fun getShapes(
        accessCheck: AccessCheck,
        request: MultiTermCardRequestDto,
    ): Flux<ShapeDto> =
        getManufacturingModelFromWizard(accessCheck, request.wizardId).flatMapMany { (model, volume) ->
            shapesService
                .getShapesPaged(
                    accessCheck,
                    request.wizardId,
                    technology = model,
                    request.term,
                    volume,
                    request.page,
                    request.size,
                ).map {
                    it.map { shapeDtoConversionService.threeDBShapeIdentifierToDto(accessCheck, it, technology = model) }
                }.flatMapIterable { it }
        }

    fun getShapeKeywords(
        accessCheck: AccessCheck,
        term: String?,
        wizardId: String,
    ): Mono<AutocompleteContainer> =
        getManufacturingModelFromWizard(accessCheck, wizardId).flatMap { (model, volume) ->
            shapesService.getKeywords(accessCheck, wizardId = wizardId, model, term, volume).map { keywords ->
                AutocompleteContainer(
                    sections = null,
                    items = keywords,
                )
            }
        }

    /**
     * Return the previously picked CalculationModel and Weight for the current part.
     */

    private fun getManufacturingModelFromWizard(
        accessCheck: AccessCheck,
        wizardId: String,
    ): Mono<Pair<String, Volume?>> =
        wizardRepository.findById(ObjectId(wizardId)).flatMap { wizard ->

            val modelId =
                wizard
                    .getSteps(WizardTechStep::class)
                    ?.modelId()
                    ?.value
                    ?.toString()
                    ?: error("Missing modelId in wizard=$wizardId")
            val model =
                ManufacturingModelsUtils.findByEntity(modelId)
                    ?: error("Missing manufacturingModel with id=$modelId, in wizard=$wizardId")

            // For turning and milling use underlying technology, for others use the one selected
            val rawTech =
                wizard.rawPartTechnology?.let {
                    try {
                        Model.valueOf(it).path
                    } catch (e: IllegalArgumentException) {
                        null
                    }
                } ?: model.path

            val wamLikeStep = wizard.getStepLike<WizardWamLikeStepInterface>()

            wamLikeStep
                ?.volume(accessCheck, fieldFactoryService, materialService)
                ?.map {
                    Pair(rawTech, it)
                }?.defaultIfEmpty(
                    Pair(rawTech, null),
                ) ?: Mono.just(Pair(rawTech, null))
        }
}
