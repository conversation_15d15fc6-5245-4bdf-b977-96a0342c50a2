package com.nu.bom.core.service.configurations

import com.nu.bom.core.model.configurations.InjectionConfigurationV2
import com.nu.bom.core.model.configurations.InjectionConfigurationV3
import org.springframework.stereotype.Component
import kotlin.reflect.KClass

@Component
class InjectionConfigMigratorToV3 : ConfigurationMigrator<InjectionConfigurationV2, InjectionConfigurationV3> {
    override fun configType() = ConfigType.InjectionCost

    override fun sourceClass(): KClass<InjectionConfigurationV2> = InjectionConfigurationV2::class

    override fun targetClass(): KClass<InjectionConfigurationV3> = InjectionConfigurationV3::class

    override fun migrate(configurationValue: InjectionConfigurationV2): InjectionConfigurationV3 =
        InjectionConfigurationV3(
            partsPerCycleBehaviour = configurationValue.partsPerCycleBehaviour,
            coolingTimeBehaviour = configurationValue.coolingTimeBehaviour,
            setupBehaviour = configurationValue.setupBehaviour,
            lockingForceSafetyFactor = configurationValue.lockingForceSafetyFactor,
            moldOpenSafetyFactor = configurationValue.moldOpenSafetyFactor,
            moldCloseSafetyFactor = configurationValue.moldCloseSafetyFactor,
            injectionFlowReductionFactor = configurationValue.injectionFlowReductionFactor,
            temperatureCorrectionType = InjectionTsetConfigurationService.temperatureCorrectionType,
            pressureCorrectionType = InjectionTsetConfigurationService.pressureCorrectionType,
        )
}
