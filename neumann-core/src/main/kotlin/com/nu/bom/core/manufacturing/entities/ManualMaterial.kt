package com.nu.bom.core.manufacturing.entities

import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.executioncontext.fieldtypes.MaterialClassField
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.ManualMaterialCostMode
import com.nu.bom.core.manufacturing.fieldTypes.MaterialSubstances
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import java.math.BigDecimal

@Deprecated("kept for backwards compatibility", replaceWith = ReplaceWith("ManualMaterialV2"))
@EntityType(Entities.MATERIAL)
class ManualMaterial(name: String) : ManufacturingEntity(name) {
    override val extends = BaseMaterial(name)

    @Input
    fun materialSubstances(): MaterialSubstances = MaterialSubstances(emptyList())

    @Input
    @ObjectView(ObjectView.MATERIAL, 35)
    @MandatoryForEntity(index = 3)
    fun costMode(): ManualMaterialCostMode = ManualMaterialCostMode.BUY

    @Input
    @ObjectView(ObjectView.MATERIAL, 30)
    @MandatoryForEntity(index = 4)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun inputQuantity(): QuantityUnit? = null

    @Input
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun quantity(
        inputQuantity: QuantityUnit,
        costMode: ManualMaterialCostMode,
    ) = when (costMode.res) {
        ManualMaterialCostMode.Selection.BUY -> inputQuantity
        ManualMaterialCostMode.Selection.SELL -> inputQuantity * Num(-1.0)
    }

    @Input
    fun itemNumber(): Text = Text("")

    // region commercial calculation

    @Input
    @ObjectView(ObjectView.MATERIAL, 20)
    @MandatoryForEntity(index = 1)
    fun reuseOfScrap(): Bool = Bool(false)

    @ReadOnly
    fun commercialMaterialType(): MaterialClassField = MaterialClassField.RAW_MATERIAL

    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun purchasePrice(pricePerUnit: Money): Money = pricePerUnit

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun purchasedQuantity(quantity: QuantityUnit?): QuantityUnit = quantity ?: QuantityUnit(BigDecimal.ZERO)

    // endregion
}
