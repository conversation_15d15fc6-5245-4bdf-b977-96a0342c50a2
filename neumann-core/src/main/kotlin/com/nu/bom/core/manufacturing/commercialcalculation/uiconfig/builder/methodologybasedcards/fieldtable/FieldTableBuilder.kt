package com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.fieldtable

import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.UniqueOperationIdentifier
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.helper.ColumnCreator
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.FieldTableRowIdentifier
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityLocator
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableRowDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.options.TableOptionsFeDto

object FieldTableBuilder {
    fun createFieldTable(
        fieldTableRowStructure: FieldTableRowStructure,
        columnCreators: List<ColumnCreator>,
        allOperationsForTableIncludingEntryPoint: List<OperationWithAdditionalInfoForFieldTable>,
        navigatorsForRows: (opId: UniqueOperationIdentifier) -> EntityLocator? = { null },
        tableOptionsFeDto: TableOptionsFeDto? = TableOptionsFeDto(couldBeNested = true),
    ): FieldTableConfigFeDto {
        return FieldTableConfigFeDto(
            rows = getTopRowNames(allOperationsForTableIncludingEntryPoint, fieldTableRowStructure),
            rowDefinitions =
                getRowDefinitions(
                    allOperationsForTableIncludingEntryPoint,
                    columnCreators,
                    fieldTableRowStructure,
                    navigatorsForRows,
                ),
            columns = columnCreators.map { it.columnDefinition },
            options = tableOptionsFeDto,
        )
    }

    private fun getTopRowNames(
        allOperationsForTable: List<OperationWithAdditionalInfoForFieldTable>,
        fieldTableRowStructure: FieldTableRowStructure,
    ): List<String> {
        val newSectionTopRowNames =
            allOperationsForTable
                .filter { it.hasAdditionalNewSection }
                .map { FieldTableConfigFeDto.NEW_SECTION_PREFIX + it.operationId.toFeKey() }

        val entryPointOperationWithInfo =
            allOperationsForTable.singleOrNull { it.operationId.matchesOperation(fieldTableRowStructure.entryPoint) }

        return when {
            !fieldTableRowStructure.includeTopLevel -> {
                newSectionTopRowNames + (entryPointOperationWithInfo?.childrenIds?.map { it.toFeKey() } ?: emptyList())
            }

            entryPointOperationWithInfo != null -> {
                newSectionTopRowNames + entryPointOperationWithInfo.operationId.toFeKey()
            }

            else -> {
                newSectionTopRowNames
            }
        }
    }

    private fun getRowDefinitions(
        allOpsInclEntryPoint: List<OperationWithAdditionalInfoForFieldTable>,
        columnCreators: List<ColumnCreator>,
        fieldTableRowStructure: FieldTableRowStructure,
        navigatorsForRows: (opId: UniqueOperationIdentifier) -> EntityLocator?,
    ): Map<FieldTableRowIdentifier, FieldTableRowDefinitionFeDto> {
        val allOperationsForTable =
            if (fieldTableRowStructure.includeTopLevel) allOpsInclEntryPoint else allOpsInclEntryPoint.dropLast(1)
        return allOperationsForTable
            .flatMap { opWithInfo ->
                val keyWithOutPrefix = opWithInfo.operationId.toFeKey()
                val keyWithPrefix = FieldTableConfigFeDto.NEW_SECTION_PREFIX + keyWithOutPrefix
                if (opWithInfo.hasAdditionalNewSection) {
                    listOf(
                        keyWithOutPrefix to
                            getRowDefinition(
                                keyWithOutPrefix,
                                opWithInfo,
                                columnCreators,
                                navigatorsForRows(opWithInfo.operationId),
                                showChildren = false,
                                isCollapsed = opWithInfo.isCollapsed,
                            ),
                        keyWithPrefix to
                            getRowDefinition(
                                keyWithPrefix,
                                opWithInfo,
                                columnCreators,
                                navigatorsForRows(opWithInfo.operationId),
                                showChildren = true,
                                isCollapsed = opWithInfo.isCollapsed,
                            ),
                    )
                } else {
                    listOf(
                        keyWithOutPrefix to
                            getRowDefinition(
                                keyWithOutPrefix,
                                opWithInfo,
                                columnCreators,
                                navigatorsForRows(opWithInfo.operationId),
                                showChildren = true,
                                isCollapsed = opWithInfo.isCollapsed,
                            ),
                    )
                }
            }.toMap()
    }

    private fun getRowDefinition(
        key: FieldTableRowIdentifier,
        operationWithInfo: OperationWithAdditionalInfoForFieldTable,
        columnCreators: List<ColumnCreator>,
        entityLocator: EntityLocator?,
        showChildren: Boolean,
        isCollapsed: Boolean,
    ): FieldTableRowDefinitionFeDto {
        val childrenKeys = operationWithInfo.childrenIds.map { it.toFeKey() }.ifEmpty { null }
        return FieldTableRowDefinitionFeDto(
            id = key,
            cells = columnCreators.mapNotNull { it.defineCell(operationWithInfo.operationId) },
            rows = if (showChildren) childrenKeys else null,
            navigateToEntityLocator = entityLocator,
            isCollapsed = isCollapsed.takeIf { it },
        )
    }
}
