package com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.fieldconfigbuilder

import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.executioncontext.CommercialCalculationOperationConfigBuilders
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.OverheadOperation

data class OverheadFieldConfigBuilder(
    override val op: OverheadOperation,
) : IndirectWithRateOriginFieldConfigBuilder() {
    override val indirectType = CommercialCalculationOperationConfigBuilders.IndirectFunction.OVERHEAD
}
