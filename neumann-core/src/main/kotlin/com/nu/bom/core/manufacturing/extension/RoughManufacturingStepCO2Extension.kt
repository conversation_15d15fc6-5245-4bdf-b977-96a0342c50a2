package com.nu.bom.core.manufacturing.extension

import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.Extends
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.entities.ManufacturingEntityExtension
import com.nu.bom.core.manufacturing.entities.RoughManufacturingStep
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.manufacturing.fieldTypes.EmissionUnits
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import java.math.BigDecimal

@Extends([RoughManufacturingStep::class], CO2_EXTENSION_PACKAGE)
class RoughManufacturingStepCO2Extension(name: String) : ManufacturingEntityExtension(name) {
    @Input
    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun cO2PerManufacturingStepUnit(): Emission = Emission(BigDecimal.ZERO, EmissionUnits.KILOGRAM_CO2E)

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun co2PerPartRoughProcess(
        cO2PerManufacturingStepUnit: Emission,
        quantityOfManuStep: QuantityUnit,
        lotFractionApportionmentFactor: Rate,
    ): Emission {
        return cO2PerManufacturingStepUnit * lotFractionApportionmentFactor * quantityOfManuStep
    }
}
