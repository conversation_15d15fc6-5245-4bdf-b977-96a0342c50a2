package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.manufacturing.annotations.DontSortOptions
import com.nu.bom.core.utils.toUpperSnakeCase

@DontSortOptions
class ThreadProcessingType(res: Selection) : SelectEnumFieldResult<ThreadProcessingType.Selection, ThreadProcessingType>(res) {

    enum class Selection(val value: String) {
        CUTTER("Cutter"),
        DRILL("Drill")
    }

    companion object {
        fun valueOf(name: String): ThreadProcessingType {
            return Selection.values().find {
                it.value == name
            }?.let {
                ThreadProcessingType(it)
            } // else try to cast from name
                ?: ThreadProcessingType(Selection.valueOf(name.toUpperSnakeCase()))
        }
    }
}
