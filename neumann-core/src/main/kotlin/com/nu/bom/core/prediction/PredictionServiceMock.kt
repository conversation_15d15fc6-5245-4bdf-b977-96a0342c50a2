package com.nu.bom.core.prediction

import com.tset.bom.clients.prediction.PredictionResponse
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import java.math.BigDecimal
import java.util.concurrent.ConcurrentHashMap

@Service
@Profile("test")
class PredictionServiceMock : PredictionServiceApi {
    private val resultMap: ConcurrentHashMap<Pair<String, String>, Any> = ConcurrentHashMap()

    fun withPrediction(
        technology: String,
        variable: String,
        result: Any,
    ): PredictionServiceMock {
        // we changed the expected result type of the prediction service from Double to BigDecimal
        // this here is easier and more flexible than adapting all tests
        val adjustedResult =
            when (result) {
                is Double -> BigDecimal(result)
                else -> result
            }
        resultMap[technology to variable] = adjustedResult
        return this
    }

    fun clear(): PredictionServiceMock {
        resultMap.clear()
        return this
    }

    override fun <T> predict(
        technology: String,
        variable: String,
        inputs: Map<String, String>,
    ): Mono<PredictionResponse<T>> {
        return Mono.justOrEmpty(
            resultMap[technology to variable]?.let {
                PredictionResponse(success = false, default_value = false, predicted_value = it as T)
            },
        )
    }
}
