package com.nu.bom.core.service.configurations

import com.nu.bom.core.exception.ConfigurationUserException
import com.nu.bom.core.utils.annotations.MaybeEmpty
import com.nu.bom.core.utils.annotations.NotEmpty
import com.nu.bom.core.utils.exceptionOnEmpty
import org.springframework.context.annotation.Profile
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

interface ConfigurationTypeRegistry {
    @MaybeEmpty
    operator fun get(
        group: String,
        type: String,
    ): Mono<ConfigType<*>>

    @NotEmpty
    fun getValue(
        group: String,
        type: String,
    ): Mono<ConfigType<*>> =
        this[group, type]
            .exceptionOnEmpty {
                ConfigurationUserException(
                    ConfigurationUserException.ErrorCode.CFG_INVALID_TYPE,
                    "group '$group' type '$type' is not a valid config type",
                    HttpStatus.UNPROCESSABLE_ENTITY,
                    listOf(group, type),
                )
            }

    fun getAll(): Flux<ConfigType<*>>
}

@Service
@Profile("!test", "disable-registry-mock")
class ValidConfigTypeRegistry : ConfigurationTypeRegistry {
    private val typeRegistry: Map<Pair<String, String>, ConfigType<*>> =
        ConfigType.Valid::class.sealedSubclasses.map {
            checkNotNull(it.objectInstance) { "ConfigType $it must have an objectInstance" }
        }.associateBy { it.group to it.type }

    override operator fun get(
        group: String,
        type: String,
    ): Mono<ConfigType<*>> = Mono.justOrEmpty(typeRegistry[group to type])

    override fun getAll(): Flux<ConfigType<*>> = Flux.fromIterable(typeRegistry.values)
}
