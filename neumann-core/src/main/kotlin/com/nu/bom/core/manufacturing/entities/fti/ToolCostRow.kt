package com.nu.bom.core.manufacturing.entities.fti

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.entities.BaseEntityFields
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Text

@EntityType(Entities.TOOL_COST_ROW)
class ToolCostRow(name: String) : ManufacturingEntity(name) {
    override val extends = BaseEntityFields(name)

    @ReadOnly
    @Input
    fun displayDesignation(): Text = Text(this.name)

    @Input
    fun design(): Money = Money.ZERO

    @Input
    fun material(): Money = Money.ZERO

    @Input
    fun machine(): Money = Money.ZERO

    @Input
    fun build(): Money = Money.ZERO

    @Input
    fun tryout(): Money = Money.ZERO

    @ReadOnly
    fun total(
        design: Money,
        material: Money,
        machine: Money,
        build: Money,
        tryout: Money,
    ): Money =
        Money(
            design.res + material.res + machine.res + build.res + tryout.res
        )
}
