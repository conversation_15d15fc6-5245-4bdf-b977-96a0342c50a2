package com.nu.bom.core.manufacturing.entities

import com.nu.bom.core.manufacturing.annotations.CompositeMandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntityContext.CREATE_FOR_MASTERDATA
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntityContext.CREATE_FROM_MASTERDATA
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntityContext.CREATE_MANUAL
import com.nu.bom.core.manufacturing.annotations.MasterDataCalculation
import com.nu.bom.core.manufacturing.annotations.MasterDataType
import com.nu.bom.core.manufacturing.annotations.Optional
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.Selectable
import com.nu.bom.core.manufacturing.annotations.StaticDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.SubLabel
import com.nu.bom.core.manufacturing.annotations.TranslationLabel
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.StaticUnitOverride
import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.AreaUnits
import com.nu.bom.core.manufacturing.fieldTypes.Currency
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Power
import com.nu.bom.core.manufacturing.fieldTypes.PowerUnits
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.Volume
import com.nu.bom.core.manufacturing.fieldTypes.VolumeUnits
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import java.math.BigDecimal

@EntityType(Entities.MACHINE)
@MasterDataType(com.nu.bom.core.manufacturing.enums.MasterDataType.MACHINE)
open class MachineMasterData(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = BaseEntityFields(name)

    @Input
    @SubLabel(100)
    @Selectable("machineType")
    @MandatoryForEntity(index = -100, context = CREATE_FOR_MASTERDATA)
    fun type(): Text? = null

    @Input
    @Optional
    @CompositeMandatoryForEntity(
        [
            MandatoryForEntity(index = -100, context = CREATE_FROM_MASTERDATA, readOnly = true),
        ],
    )
    @MasterDataCalculation
    fun technicalDescription(): Text = Text("")

    @Input
    @Optional
    @CompositeMandatoryForEntity(
        [
            MandatoryForEntity(index = 100, context = CREATE_FROM_MASTERDATA, readOnly = true),
            MandatoryForEntity(index = 100, context = CREATE_MANUAL),
        ],
    )
    @MasterDataCalculation
    fun manufacturer(): Text = Text("")

    @Input
    @Path("/api/currency{bomPath}{branchPath}", false)
    fun baseCurrency(): Currency = Currency("EUR")

    @Input
    @Path("/api/currency{bomPath}{branchPath}", false)
    @CompositeMandatoryForEntity(
        [
            MandatoryForEntity(index = 200),
            MandatoryForEntity(index = 200, context = CREATE_FROM_MASTERDATA, section = "machineCost", readOnly = true),
            MandatoryForEntity(false, context = CREATE_FOR_MASTERDATA),
        ],
    )
    fun masterdataBaseCurrency(): Currency = Currency("EUR")

    @Input
    @CompositeMandatoryForEntity(
        [
            MandatoryForEntity(index = 300, context = CREATE_MANUAL),
            MandatoryForEntity(index = 300, context = CREATE_FROM_MASTERDATA, readOnly = true, section = "investment"),
        ],
    )
    fun investBase(): Money = Money(BigDecimal.TEN)

    @Input
    @MandatoryForEntity(index = 400, context = CREATE_FROM_MASTERDATA, readOnly = true, section = "investment")
    fun investFundament(): Money = Money(BigDecimal.ZERO)

    @Input
    @MandatoryForEntity(index = 500, context = CREATE_FROM_MASTERDATA, readOnly = true, section = "investment")
    fun investMisc(): Money = Money(BigDecimal.ZERO)

    @Input
    @MandatoryForEntity(index = 600, context = CREATE_FROM_MASTERDATA, readOnly = true, section = "investment")
    fun investSetup(): Money = Money(BigDecimal.ZERO)

    @Input
    @DefaultUnit(DefaultUnit.YEAR)
    @MandatoryForEntity(index = 700, context = CREATE_FROM_MASTERDATA, readOnly = true, section = "fixedCost")
    fun depreciationTime(): Time = Time(10.toBigDecimal(), TimeUnits.YEAR)

    @Input
    @DefaultUnit(DefaultUnit.QM)
    @CompositeMandatoryForEntity(
        [
            MandatoryForEntity(index = 800, context = CREATE_MANUAL),
            MandatoryForEntity(index = 800, context = CREATE_FROM_MASTERDATA, readOnly = true, section = "fixedCost"),
        ],
    )
    fun requiredSpaceGross(): Area = Area(BigDecimal.ZERO, AreaUnits.QM)

    @Input
    @MandatoryForEntity(index = 900, context = CREATE_FROM_MASTERDATA, readOnly = true, section = "fixedCost")
    fun requiredSpaceNet(): Area = Area(BigDecimal.ZERO, AreaUnits.QM)

    @Input
    @MandatoryForEntity(index = 1000, context = CREATE_FROM_MASTERDATA, readOnly = true, section = "variableCost")
    fun powerOnTimeRate(): Rate = Rate(BigDecimal.ONE)

    @Input
    @DefaultUnit(DefaultUnit.KILOWATT)
    @CompositeMandatoryForEntity(
        [
            MandatoryForEntity(index = 1100, context = CREATE_MANUAL),
            MandatoryForEntity(index = 1100, context = CREATE_FROM_MASTERDATA, readOnly = true, section = "variableCost"),
        ],
    )
    fun connectedLoad(): Power = Power(BigDecimal.ZERO, PowerUnits.WATT)

    @Input
    @CompositeMandatoryForEntity(
        [
            MandatoryForEntity(index = 1200, context = CREATE_MANUAL),
            MandatoryForEntity(index = 1200, context = CREATE_FROM_MASTERDATA, readOnly = true, section = "variableCost"),
        ],
    )
    @StaticDenominatorUnit(StaticUnitOverride.YEAR)
    fun maintenanceRate(): Rate = Rate(BigDecimal.ZERO)

    // cost is per one machine and thus without dynamic denominator unit
    @ReadOnly
    @MasterDataCalculation
    @TranslationLabel("maintenanceCostMasterData")
    @MandatoryForEntity(index = 1300, context = CREATE_FROM_MASTERDATA, readOnly = true, section = "variableCost")
    fun maintenanceCost(
        maintenanceRate: Rate,
        totalInvest: Money,
    ): Money = Money(maintenanceRate.res * totalInvest.res)

    @Input
    @CompositeMandatoryForEntity(
        [
            MandatoryForEntity(index = 1400, context = CREATE_MANUAL),
            MandatoryForEntity(index = 1400, context = CREATE_FROM_MASTERDATA, readOnly = true, section = "variableCost"),
        ],
    )
    @StaticDenominatorUnit(StaticUnitOverride.YEAR)
    fun consumableRate(): Rate = Rate(BigDecimal.ZERO)

    // cost is per one machine and thus without dynamic denominator unit
    @ReadOnly
    @MasterDataCalculation
    @MandatoryForEntity(index = 1500, context = CREATE_FROM_MASTERDATA, readOnly = true, section = "variableCost")
    fun consumableCost(
        consumableRate: Rate,
        totalInvest: Money,
    ): Money = totalInvest * consumableRate

    @Input
    @MasterDataCalculation
    fun residualValue(): Money = Money.ZERO

    @Input
    @StaticDenominatorUnit(StaticUnitOverride.HOUR)
    fun gasConsumptionMeltingPerHour(): Volume = Volume(BigDecimal.ZERO, VolumeUnits.CM)

    @Input
    @StaticDenominatorUnit(StaticUnitOverride.HOUR)
    fun gasConsumptionKeepWarmPerHour(): Volume = Volume(BigDecimal.ZERO, VolumeUnits.CM)

    @Input
    @MandatoryForEntity(index = 1600, context = CREATE_MANUAL)
    @StaticDenominatorUnit(StaticUnitOverride.HOUR)
    fun gasConsumptionAdditional(): Volume = Volume(BigDecimal.ZERO, VolumeUnits.CM)

    @Input
    @ReadOnly
    @MasterDataCalculation(false)
    @StaticDenominatorUnit(StaticUnitOverride.HOUR)
    fun gasConsumption(
        gasConsumptionMeltingPerHour: Volume,
        gasConsumptionKeepWarmPerHour: Volume,
        gasConsumptionAdditional: Volume,
    ): Volume = gasConsumptionMeltingPerHour + gasConsumptionKeepWarmPerHour + gasConsumptionAdditional

    @Input
    @StaticDenominatorUnit(StaticUnitOverride.HOUR)
    fun oxigenConsumptionPerHour(): Weight = Weight(BigDecimal.ZERO, WeightUnits.KILOGRAM)

    @Input
    @StaticDenominatorUnit(StaticUnitOverride.HOUR)
    fun castExcipientsConsumptionPerHour(): Weight = Weight(BigDecimal.ZERO, WeightUnits.KILOGRAM)

    // Per machine
    @ReadOnly
    @TranslationLabel("investPerMachine")
    @MasterDataCalculation
    fun totalInvest(
        investBase: Money,
        investFundament: Money,
        investMisc: Money,
        investSetup: Money,
    ): Money = investBase + investFundament + investMisc + investSetup
}
