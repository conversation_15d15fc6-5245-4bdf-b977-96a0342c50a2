package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import org.springframework.stereotype.Service

@Service
class TransportCalcFixMigration : ManufacturingModelEntityMapper {
    override val changeSetId = MigrationChangeSetId("2023-05-16-transport-calc-fix")

    companion object {
        val resetFields = setOf("costPerPart", "cO2PerPart")
    }

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity {
        val newFields =
            entity.fieldWithResults.mapValues { (k, v) ->
                if (k in resetFields && v.source == "I") {
                    v.copyAll(source = "C")
                } else {
                    v
                }
            }

        return entity.copyAll(
            fieldWithResults = newFields,
        )
    }
}
