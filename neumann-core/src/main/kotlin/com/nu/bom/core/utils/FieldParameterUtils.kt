package com.nu.bom.core.utils

import com.nu.bom.core.api.dtos.FieldConversionService
import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.ManualClassSelector
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.UnitOverrideContext
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.DynamicQuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.tset.bom.clients.common.FieldStructure
import com.tset.core.module.bom.exchangerates.ExchangeRateMap
import com.tset.core.service.domain.Currency
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.util.Locale
import kotlin.reflect.KClass

fun MutableList<FieldParameter>.fillDimensionFields(fieldConversionService: FieldConversionService): MutableList<FieldParameter> {
    val dimension = Dimension.getDefault(Entities.MANUFACTURING)!!
    return fillFields(
        mapOf(
            "dimension" to dimension,
            "costUnit" to Text(dimension.res.getDefaultCostUnit()),
            "quantityUnit" to Text(dimension.res.getDefaultCostUnit()),
        ),
        fieldConversionService,
        null,
        UnitOverrideContext.noContext,
    )
}

fun MutableList<FieldParameter>.fillFields(
    fields: Map<String, Any>,
    fieldConversionService: FieldConversionService,
    entity: ManufacturingEntity? = null,
    unitOverrideContext: UnitOverrideContext,
): MutableList<FieldParameter> {
    return fields.entries.fold(initial = this) { acc, (name, field) ->
        acc.fillField(name, field, fieldConversionService, entity, unitOverrideContext)
    }
}

fun MutableList<FieldParameter>.fillField(
    name: String,
    input: Any,
    fieldConversionService: FieldConversionService,
    entity: ManufacturingEntity? = null,
    unitOverrideContext: UnitOverrideContext,
): MutableList<FieldParameter> {
    val original =
        find { it.name == name }
            ?: error("FieldParameter '$name' was not found. Available fields: ${this.map { it.name }}")

    val replacement =
        original.fill(
            input,
            fieldConversionService = fieldConversionService,
            entity = entity,
            unitOverrideContext = unitOverrideContext,
        )
    remove(original)
    add(replacement)

    return this
}

fun FieldParameter.fill(
    input: Any,
    fieldConversionService: FieldConversionService,
    entity: ManufacturingEntity?,
    unitOverrideContext: UnitOverrideContext,
    metaInfoOverrides: Map<String, Any>? = null,
    fillSource: Boolean = false,
    fillSystemValue: Boolean = false,
): FieldParameter {
    return when (input) {
        is FieldParameter -> {
            checkType(input)
            input
        }

        is FieldResultStar -> {
            checkType(input)
            val update =
                fieldConversionService.fieldWithResultToFieldParameter(
                    name = name,
                    resultPreConversion = input,
                    entityClass = (entity?.let { it::class } ?: Manufacturing::class).java.simpleName,
                    entity = entity,
                    exchangeRateMap = ExchangeRateMap.empty(),
                    baseCurrency = Currency.EUR,
                    unitOverrideContext = unitOverrideContext,
                    entityForDynamicMetaData = entity,
                )
            val defaultUnitMetaInfo =
                update.metaInfo?.get(DefaultUnit.META_INFO)?.let { mapOf(DefaultUnit.META_INFO to it) } ?: mapOf()
            copy(
                type = update.type,
                value = update.value,
                unit = update.unit,
                metaInfo =
                    when (metaInfoOverrides) {
                        null -> this.metaInfo?.plus(defaultUnitMetaInfo) ?: defaultUnitMetaInfo
                        else ->
                            this.metaInfo?.plus(metaInfoOverrides)?.plus(defaultUnitMetaInfo) ?: metaInfoOverrides.plus(
                                defaultUnitMetaInfo,
                            )
                    },
                source = input.source.name.takeIf { fillSource },
                systemValue = update.systemValue.takeIf { fillSystemValue },
            )
        }

        else ->
            copy(
                value = input,
                metaInfo =
                    when (metaInfo) {
                        null -> this.metaInfo
                        else -> this.metaInfo.plus(metaInfo)
                    },
            )
    }
}

/**
 * Gets the field by [name], with an optional type check.
 *
 * @throws IllegalStateException if the field is missing, or if the type is specified but not matching, or if the value is null or blank.
 * */
fun <X : FieldStructure> List<X>.getRequired(
    name: String,
    type: KClass<out FieldResultStar>,
): X {
    return getRequired(name = name, type = type.simpleName!!)
}

fun <X : FieldStructure> List<X>.getUnitOverrideContext(entityManager: EntityManager): UnitOverrideContext {
    val dimension = Dimension.Selection.valueOf(getRequired("dimension", Dimension::class).value.toString())

    val costUnitString = getRequired("costUnit", Text::class).value as String
    val quantityUnitString = getRequired("quantityUnit", Text::class).value as String
    val dimensionTypeUnits = entityManager.getUnits(dimension.type)
    val costUnit = dimensionTypeUnits.find { (it as Enum<*>).name == costUnitString }!!
    val quantityUnit = dimensionTypeUnits.find { (it as Enum<*>).name == quantityUnitString }!!

    return UnitOverrideContext(
        dimension = dimension,
        costUnitString = costUnit.toString(),
        quantityUnitString = quantityUnit.toString(),
        manufacturingDimension = dimension,
        manufacturingCostUnitString = costUnit.toString(),
        manufacturingQuantityUnitString = quantityUnit.toString(),
    )
}

/**
 * Gets the field by [name], with an optional type check.
 *
 * @throws IllegalStateException if the field is missing, or if the type is specified but not matching, or if the value is null or blank.
 * */
fun <X : FieldStructure> List<X>.getRequired(
    name: String,
    type: String? = null,
): X {
    val field = this.find { it.name == name } ?: error("$name is missing")

    if (!field.hasValue()) {
        error("$name value is missing: ${field.value}")
    }

    if (type == "QuantityUnit") {
        // TODO! I would love to have better type checking here …
        return field
    }

    if (type != null && field.type != type) {
        error("$name has invalid type: expected=$type, actual=${field.type}")
    }
    return field
}

/**
 * Gets the optional field by [name], with type check, or null if the field is missing.
 *
 * @throws IllegalStateException if the field is present but not matching the specified type.
 * */
fun <X : FieldStructure> List<X>.getOptional(
    name: String,
    type: KClass<out FieldResultStar>,
): X? {
    return getOptional(name = name, type = type.simpleName!!)
}

/**
 * Gets the field by [name], with an optional type check, or null if the field is missing.
 *
 * @throws IllegalStateException if the field is present but not matching the specified type.
 * */
fun <X : FieldStructure> List<X>.getOptional(
    name: String,
    type: String? = null,
): X? {
    val field = this.find { it.name == name }

    if (type == "QuantityUnit") {
        // TODO! I would love to have better type checking here …
        return field
    }

    if (field != null && type != null && field.type != type) {
        error("$name has invalid type: expected=$type, actual=${field.type}")
    }
    return field
}

fun FieldParameter.hasMetaInfo(
    name: String,
    value: Any,
): Boolean {
    return this.metaInfo?.get(name)?.equals(value) ?: false
}

fun FieldParameter.addMetaInfo(meta: Map<String, Any>?) =
    meta?.let {
        this.copy(metaInfo = (metaInfo ?: emptyMap()) + meta)
    } ?: this

fun FieldParameter.addMetaInfo(
    name: String,
    value: Any,
) = addMetaInfo(mapOf(name to value))

/**
 * True if the field has a non-null / non-blank value, false otherwise.
 * */
fun FieldStructure.hasValue(): Boolean {
    return this.value?.let { value ->
        if (value.toString().lowercase(Locale.getDefault()) == "null") {
            logger.warn(this.toString(), Thread.dumpStack())
        }
        when (value) {
            is String -> value.isNotBlank() && value.lowercase(Locale.getDefault()) != "null"
            else -> value.toString().lowercase(Locale.getDefault()) != "null"
        }
    } ?: false
}

private fun FieldStructure.checkType(field: FieldResultStar) {
    if (this.type != field.getType() &&
        (field.getType() != DynamicQuantityUnit::class.simpleName && field.getType() != QuantityUnit::class.simpleName)
    ) {
        error("Incompatible field types: expected=${this.type}, input=${field.getType()}")
    }
}

private fun FieldStructure.checkType(field: FieldStructure) {
    if (this.type != field.type) {
        error("Incompatible field types: expected=${this.type}, input=${field.type}")
    }
}

private val logger: Logger = LoggerFactory.getLogger(FieldParameter::class.java)

fun List<FieldParameter>.getSelectedClass() =
    find {
        it.metaInfo?.get(ManualClassSelector.META_INFO) == true && it.source != FieldResult.SOURCE.R.name
    }?.value as String?
