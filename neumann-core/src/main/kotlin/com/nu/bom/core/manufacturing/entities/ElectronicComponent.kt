package com.nu.bom.core.manufacturing.entities

import com.nu.bom.core.manufacturing.annotations.CompositeMandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.ExternalDataProvider
import com.nu.bom.core.manufacturing.annotations.FieldName
import com.nu.bom.core.manufacturing.annotations.FilteredChildren
import com.nu.bom.core.manufacturing.annotations.Hidden
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntityContext
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.RequiredFor
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.executioncontext.fieldtypes.MaterialClassField
import com.nu.bom.core.manufacturing.defaults.HighElcoPurchaseVolume
import com.nu.bom.core.manufacturing.defaults.NullProvider
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.ElcoPurchaseVolume
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.MountingType
import com.nu.bom.core.manufacturing.fieldTypes.Part
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.utils.getFirstResultWithSelectedAttributes
import com.nu.bom.core.utils.getResultWithSelectedAttributes
import org.bson.types.ObjectId
import reactor.core.publisher.Mono
import java.math.BigDecimal

enum class DefaultDiscount(
    val factor: Double,
) {
    LOW(0.4),
    MEDIUM(0.35),
    HIGH(0.3),
}

@EntityType(Entities.C_PART)
class ElectronicComponent(
    name: String,
) : ManufacturingEntity(name) {
    companion object {
        const val MOUNTING_TYPE_MASTER_DATA_FIELD_NAME = "#classification_field_tset-ref-field-mounting-type"
        const val MANUFACTURER_TYPE_MASTER_DATA_FIELD_NAME = "#classification_field_tset-ref-field-manufacturerName"
    }

    override val extends = BaseMaterial(name)

    @Input
    @Hidden
    @MandatoryForEntity(context = MandatoryForEntityContext.CREATE_FROM_EXTERNALDATA)
    fun externalPartId(): Text? = null

    @Input
    @ReadOnly
    @MandatoryForEntity(context = MandatoryForEntityContext.CREATE_FROM_EXTERNALDATA)
    fun dimension(materialBaseDimension: Dimension): Dimension = materialBaseDimension

    @Input
    fun elcoVolume(
        @Parent(Entities.MANUFACTURING)
        @Default(HighElcoPurchaseVolume::class)
        elcoPurchaseVolume: ElcoPurchaseVolume,
    ): ElcoPurchaseVolume = elcoPurchaseVolume

    @ExternalDataProvider
    fun externalPartData(externalPartId: Text?): Mono<Part>? =
        externalPartId?.let {
            services.octoPartService().getPartById(it.res)
        }

    @ReadOnly
    fun discountFactor(
        elcoVolume: ElcoPurchaseVolume,
        externalPartData: Part?,
    ): Mono<Rate> =
        if (externalPartData != null) {
            lookupDiscountOrDefault(externalPartData, elcoVolume).map { result -> Rate(result.discountFactor) }
        } else {
            Mono.just(Rate(1.0))
        }

    @ReadOnly
    fun defaultDiscountApplied(
        elcoVolume: ElcoPurchaseVolume,
        externalPartData: Part?,
    ): Mono<Bool> =
        if (externalPartData != null) {
            lookupDiscountOrDefault(externalPartData, elcoVolume).map { result -> Bool(result.defaultDiscountApplied) }
        } else {
            Mono.just(Bool(false))
        }

    private fun lookupDiscountOrDefault(
        externalPartData: Part,
        elcoVolume: ElcoPurchaseVolume,
    ): Mono<ElcoDiscountResult> =
        services
            .getLookupTable("ElcoDiscount", rowParser = discountLookupReader)
            .filter {
                it.category == externalPartData.categoryId
            }.defaultIfEmpty(ElcoDiscount(category = 0, low = null, medium = null, high = null))
            .single()
            .map { discount ->
                getDiscountPriceOrDefaultFor(
                    externalPartData,
                    elcoVolume,
                    discount,
                    ElcoDiscount(
                        category = 0,
                        low = DefaultDiscount.LOW.factor,
                        medium = DefaultDiscount.MEDIUM.factor,
                        high = DefaultDiscount.HIGH.factor,
                    ),
                )
            }

    private fun getDiscountPriceOrDefaultFor(
        externalPartData: Part,
        volume: ElcoPurchaseVolume,
        discount: ElcoDiscount,
        defaultDiscount: ElcoDiscount,
    ): ElcoDiscountResult {
        val discountFactor = discountForPurchaseVolume(volume, discount)
        val defaultDiscountFactor = discountForPurchaseVolume(volume, defaultDiscount)
        val selectedDiscountFactor = discountFactor ?: defaultDiscountFactor ?: error("Fallback discount must not be null")

        val discountedPrice = services.octoPartService().getPreferredVendorPrice(externalPartData) * selectedDiscountFactor

        return ElcoDiscountResult(
            discountedPrice = discountedPrice,
            discountFactor = selectedDiscountFactor,
            defaultDiscountApplied = (discountFactor == null),
        )
    }

    private fun discountForPurchaseVolume(
        volume: ElcoPurchaseVolume,
        discount: ElcoDiscount,
    ): Double? =
        when (volume.res) {
            ElcoPurchaseVolume.Selection.HIGH -> discount.high
            ElcoPurchaseVolume.Selection.MEDIUM -> discount.medium
            ElcoPurchaseVolume.Selection.LOW -> discount.low
        }

    private data class ElcoDiscountResult(
        val discountedPrice: Money,
        val discountFactor: Double,
        val defaultDiscountApplied: Boolean,
    )

    private val discountLookupReader: (row: List<String>) -> ElcoDiscount = { row ->
        ElcoDiscount(
            // always present
            category = row[0].toInt(),
            // discounts may be empty in provided lookup CSV, we need to detect and act on that later based on the qty
            low = if (row[1].isNotEmpty()) row[1].toDouble() else null,
            medium = if (row[2].isNotEmpty()) row[2].toDouble() else null,
            high = if (row[3].isNotEmpty()) row[3].toDouble() else null,
        )
    }

    internal data class ElcoDiscount(
        val category: Int,
        val low: Double?,
        val medium: Double?,
        val high: Double?,
    )

    @Input
    @CompositeMandatoryForEntity(
        [
            MandatoryForEntity(index = 240),
            MandatoryForEntity(index = 240, context = MandatoryForEntityContext.CREATE_FROM_MASTERDATA, readOnly = true),
        ],
    )
    fun mpn(
        externalPartData: Part?,
        headerKey: Text?,
    ): Text = externalPartData?.let { Text(it.mpn) } ?: headerKey?.let { Text(it.res) } ?: Text("")

    @ReadOnly
    fun itemNumber(mpn: Text): Text = mpn

    @Input
    @CompositeMandatoryForEntity(
        [
            MandatoryForEntity(index = 240),
            MandatoryForEntity(index = 240, context = MandatoryForEntityContext.CREATE_FROM_MASTERDATA, readOnly = true),
        ],
    )
    fun mountingType(
        headerKey: Text?,
        @Default(NullProvider::class)
        @FieldName(MOUNTING_TYPE_MASTER_DATA_FIELD_NAME)
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mountingTypeFromMasterdataPrice: Map<ObjectId, Text?>?,
        @Default(NullProvider::class)
        @FieldName("headerKey")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToHeaderKeyFromMasterdataPrice: Map<ObjectId, Text>?,
    ): MountingType =
        headerKey?.let { existingHeaderKey ->
            val lovEntryKey =
                getFirstResultWithSelectedAttributes(
                    resultMap =
                        mountingTypeFromMasterdataPrice?.let { map -> map.filter { it.value != null }.mapValues { e -> e.value as Text } }
                            ?: emptyMap(),
                    attribute1Map = mapIdToHeaderKeyFromMasterdataPrice ?: emptyMap(),
                    currentAttribute1 = existingHeaderKey,
                    defaultValue = Text(MountingType.Selection.OTHER.mdKey),
                )
            lovEntryKey?.let {
                MountingType.valueOfMdKey(it.res)
            }
        } ?: MountingType.OTHER

    @Input
    @CompositeMandatoryForEntity(
        [
            MandatoryForEntity(index = 240),
            MandatoryForEntity(index = 240, context = MandatoryForEntityContext.CREATE_FROM_MASTERDATA, readOnly = true),
        ],
    )
    fun manufacturer(
        externalPartData: Part?,
        headerKey: Text?,
        @Default(NullProvider::class)
        @FieldName(MANUFACTURER_TYPE_MASTER_DATA_FIELD_NAME)
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        manufacturerFromMasterdata: Map<ObjectId, Text?>?,
        @Default(NullProvider::class)
        @FieldName("headerKey")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToHeaderKeyFromMasterdata: Map<ObjectId, Text>?,
    ): Text =
        externalPartData?.let { Text(it.manufacturer) }
            ?: headerKey?.let { existingHeaderKey ->
                val manufacturerName =
                    getFirstResultWithSelectedAttributes(
                        resultMap =
                            manufacturerFromMasterdata?.let { map -> map.filter { it.value != null }.mapValues { e -> e.value as Text } }
                                ?: emptyMap(),
                        attribute1Map = mapIdToHeaderKeyFromMasterdata ?: emptyMap(),
                        currentAttribute1 = existingHeaderKey,
                        defaultValue = Text(""),
                    )
                manufacturerName?.let {
                    Text(it.res)
                }
            } ?: Text("")

    @Input
    @CompositeMandatoryForEntity(
        [
            MandatoryForEntity(index = 0),
            MandatoryForEntity(index = 0, context = MandatoryForEntityContext.CREATE_FROM_MASTERDATA, readOnly = true),
        ],
    )
    fun displayDesignation(
        externalPartData: Part?,
        designation: Text?,
        entityDesignation: Text,
    ): Text = externalPartData?.let { Text(it.short_description) } ?: designation ?: entityDesignation

    @Input
    @MandatoryForEntity(index = 215)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    fun quantity(): QuantityUnit? = null

    @Input
    @Hidden
    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun materialBasePrice(): Money = Money(BigDecimal.ZERO)

    @Input
    @ReadOnly(value = false)
    @CompositeMandatoryForEntity(
        [
            MandatoryForEntity(
                index = 260,
                context = MandatoryForEntityContext.CREATE_MANUAL,
            ),
            MandatoryForEntity(
                index = 260,
                context = MandatoryForEntityContext.CREATE_FROM_MASTERDATA,
                readOnly = true,
            ),
            MandatoryForEntity(
                index = 260,
                context = MandatoryForEntityContext.CREATE_FROM_EXTERNALDATA,
                readOnly = true,
                computed = true,
            ),
        ],
    )
    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun pricePerUnit(
        externalPartData: Part?,
        elcoVolume: ElcoPurchaseVolume,
        headerKey: Text?,
        @Default(NullProvider::class)
        @FieldName("value")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_PRICE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToValue: Map<ObjectId, Money?>?,
        @Default(NullProvider::class)
        @FieldName("headerKey")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_PRICE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToHeaderKey: Map<ObjectId, Text>,
    ): Mono<Money?> =
        if (externalPartData != null) {
            lookupDiscountOrDefault(externalPartData, elcoVolume).map { result -> result.discountedPrice }
        } else {
            val price =
                headerKey?.let { nonNullHeaderKey ->
                    getResultWithSelectedAttributes(
                        resultMap =
                            mapIdToValue?.let { map -> map.filter { it.value != null }.mapValues { e -> e.value as Money } } ?: emptyMap(),
                        attribute1Map = mapIdToHeaderKey,
                        currentAttribute1 = nonNullHeaderKey,
                    )
                }
            Mono.justOrEmpty(price)
        }

    // region commercial calculation

    @ReadOnly
    fun commercialMaterialType(): MaterialClassField = MaterialClassField.ELECTRONIC_PART

    // endregion
}
