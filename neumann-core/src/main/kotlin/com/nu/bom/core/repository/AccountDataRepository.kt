package com.nu.bom.core.repository

import com.tset.core.module.mongodb.infrastructure.document.AccountDataDocument
import org.bson.types.ObjectId
import org.springframework.data.mongodb.repository.ReactiveMongoRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux

@Repository
interface AccountDataRepository : ReactiveMongoRepository<AccountDataDocument, ObjectId> {
    fun findByAccountId(accountId: String): Flux<AccountDataDocument>
}
