package com.nu.bom.core.lit

import com.nu.bom.core.manufacturing.service.CalculationRequestBuilder
import com.nu.bom.core.manufacturing.service.CalculationService
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.LitAnalysisResults
import com.nu.bom.core.repository.ErrorRepository
import com.nu.bom.core.repository.LitAnalysisResultsRepository
import com.nu.bom.core.service.BomNodeConversionService
import com.nu.bom.core.service.BomNodeService
import com.nu.bom.core.service.bomnode.LoadingMode
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.InstanceBasedMetaService
import org.bson.types.ObjectId
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

@Service
class LitAnalysisService(
    private val errorRepository: ErrorRepository,
    private val bomNodeService: BomNodeService,
    private val calculationService: CalculationService,
    private val bomNodeConversionService: BomNodeConversionService,
    private val analysisResultsRepository: LitAnalysisResultsRepository,
    private val litConverterService: LitConverterService,
    val entityManager: InstanceBasedMetaService,
) {
    fun analyseAndSaveBomNode(
        accessCheck: AccessCheck,
        nodeId: BomNodeId,
        branch: BranchId? = null,
        forceRecalc: Boolean = true,
        dirtyChildLoading: Boolean = true,
        loadingMode: LoadingMode = LoadingMode.DIRECT,
        justOpenFields: Boolean,
    ): Mono<LitAnalysisResults> {
        return analyseBomNode(accessCheck, nodeId, branch, forceRecalc, dirtyChildLoading, loadingMode, justOpenFields).flatMap {
            saveAnalysis(it, LitAnalysisResults.AnalysisSource.CALCULATION, "$nodeId:$branch")
        }
    }

    fun saveAnalysis(
        analysis: LitAfDto,
        source: LitAnalysisResults.AnalysisSource,
        sourceId: String,
    ): Mono<LitAnalysisResults> {
        return analysisResultsRepository.save(
            LitAnalysisResults(
                _id = analysis.id,
                results = analysis,
                source = source,
                sourceId = sourceId,
            ),
        )
    }

    fun analyseBomNode(
        accessCheck: AccessCheck,
        nodeId: BomNodeId,
        branch: BranchId?,
        forceRecalc: Boolean,
        dirtyChildLoading: Boolean,
        loadingMode: LoadingMode,
        justOpenFields: Boolean,
    ): Mono<LitAfDto> {
        return bomNodeService.getNodesRecursive(
            accessCheck,
            loadingMode = loadingMode,
            nodeId = nodeId,
            branch = branch,
        ).flatMap { snapshot ->
            val contextBoundaries = snapshot.collectChildrenBomNodeIds().toSet()
            val entity = bomNodeConversionService.bomNodeToManufacturingCalculationTree(snapshot, contextBoundaries)
            calculationService.calculate(
                requestBuilder =
                    CalculationRequestBuilder(
                        accessCheck = accessCheck,
                        entity = entity,
                        year = null,
                        forceRecalculate = forceRecalc,
                        dirtyChildLoading = dirtyChildLoading,
                        contextBoundaries = contextBoundaries,
                    ),
            ).map { calcResult ->
                litConverterService.toLitAfDto(calcResult, snapshot, entity, justOpenFields)
            }
        }
    }

    fun getAnalysisResult(id: ObjectId) = analysisResultsRepository.findById(id)

    fun getAnalysisForError(requestId: String): Mono<LitAnalysisResults> {
        return errorRepository.findByrequestId(requestId).flatMap {
            analysisResultsRepository.findBySourceId(it._id!!.toHexString())
        }
    }

    fun getAllSavedAnalysis(): Flux<LitAnalysisResults> {
        return analysisResultsRepository.findBy_idIsNotNull()
    }
}
