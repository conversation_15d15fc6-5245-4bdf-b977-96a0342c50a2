package com.nu.bom.core.manufacturing.annotations

/**
 * Declares that this dependency needs fields from another entity
 */
@Target(AnnotationTarget.FUNCTION, AnnotationTarget.PROPERTY)
annotation class ExternalDependency(
    val section: SECTIONS = SECTIONS.NONE,
    val subSection: String = "",
) {
    companion object {
        const val META_INFO = "externalDependency"
        const val SECTION_META_INFO = "sectionName"
        const val SUBSECTION_META_INFO = "subSectionName"
    }

    enum class SECTIONS {
        SELF,
        NONE,
        PART,
        MATERIAL,
        STEP,
    }
}
