package com.nu.bom.core.manufacturing.service

import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities

data class ChildDependency(
    val recursive: Boolean = false,
    val skipDependencyWhenFieldDoesNotExist: Boolean = true,
    val nameFilter: String? = null,
    val startFromFirstAncestor: Entities? = null,
    val parentFieldIdentifierFilter: String? = null,
) {
    fun getSearchRoot(entity: ManufacturingEntity): ManufacturingEntity? =
        startFromFirstAncestor?.let {
            // Return even if null - ?.let pitfall!
            return entity.findParentWithEntityType(listOf(startFromFirstAncestor))
        } ?: entity

    companion object {
        val ALL_CHILD_DEP =
            ChildDependency(
                recursive = true,
            )
        val NON_RECURSIVE_CHILD_DEP =
            ChildDependency(
                recursive = false,
            )
    }
}
