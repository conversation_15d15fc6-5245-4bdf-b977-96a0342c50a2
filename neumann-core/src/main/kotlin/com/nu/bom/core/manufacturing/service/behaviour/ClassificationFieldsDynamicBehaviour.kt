package com.nu.bom.core.manufacturing.service.behaviour

import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.fieldTypes.ClassificationFieldsDynamicBehaviourInfo

class ClassificationFieldsDynamicBehaviour(
    entityForFieldInjection: ManufacturingEntity,
    private val dynamicBehaviourInfo: ClassificationFieldsDynamicBehaviourInfo,
) : AbstractGeneralDynamicBehaviour(entityForFieldInjection) {
    private val key: String =
        behaviourType() + dynamicBehaviourInfo.classificationTypeKey + "-" + (dynamicBehaviourInfo.objectView ?: "") + "-" +
            dynamicBehaviourInfo.classificationKeys.joinToString("-")

    override fun dynamicBehaviourInfo(): ClassificationFieldsDynamicBehaviourInfo = dynamicBehaviourInfo

    // identifier for one calculation cycle to avoid multiple processing of the field
    override fun key(): String = key

    override fun behaviourType(): String = "CLASSIFICATION_FIELDS"
}
