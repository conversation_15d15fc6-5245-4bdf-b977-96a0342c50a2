package com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.helper

import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.FieldName
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityLocator
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableActionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableColumnDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableRowDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.options.ColumnOptionsFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.tableactiondefinitions.TableActionDefinition

object EntityCardBuilderHelper {
    fun createSimpleEntityTable(
        rowId: String,
        columnsWithOptions: Map<FieldName, ColumnOptionsFeDto?>,
        entityLocator: EntityLocator,
        actionKeys: Map<EntityTableActionFeDto, TableActionDefinition> = emptyMap(),
    ): EntityTableConfigFeDto {
        val columns =
            columnsWithOptions.map { (column, options) ->
                EntityTableColumnDefinitionFeDto(
                    id = column,
                    field = column,
                    options = options,
                )
            }

        @Suppress("MagicNumber")
        return EntityTableConfigFeDto(
            rows = listOf(rowId),
            rowDefinitions =
                mapOf(
                    rowId to
                        EntityTableRowDefinitionFeDto(rowId, collectBy = entityLocator, actionKeys = actionKeys.takeIf { it.isNotEmpty() }),
                ),
            columns = listOf(EntityTableColumns.mainColumn(5)) + columns,
        )
    }
}
