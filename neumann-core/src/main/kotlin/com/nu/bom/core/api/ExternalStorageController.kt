package com.nu.bom.core.api

import com.nu.bom.core.manufacturing.fieldTypes.NoCalcData
import com.nu.bom.core.manufacturing.service.ExternalStorageService
import com.nu.bom.core.model.AccountId
import com.nu.bom.core.user.AccessCheckProvider
import org.bson.types.ObjectId
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Mono

@RestController
@RequestMapping("/api/externalStorage")
class ExternalStorageController(
    private val accessCheckProvider: AccessCheckProvider,
    private val externalStorageService: ExternalStorageService,
) {
    @GetMapping
    fun get(
        @AuthenticationPrincipal jwt: Jwt?,
        @RequestParam objectId: String,
        @RequestParam(required = false) accountId: String?,
    ): Mono<NoCalcData> {
        return accessCheckProvider.doAs(jwt) {
            externalStorageService.retrieve(ObjectId(objectId), accountId?.let { AccountId(it) })
        }
    }
}
