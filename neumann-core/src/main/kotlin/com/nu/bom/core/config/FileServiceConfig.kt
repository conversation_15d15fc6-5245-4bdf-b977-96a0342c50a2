package com.nu.bom.core.config

import com.nu.bom.core.service.BomNodeService
import com.nu.bom.core.service.bomrads.BomradsFileUploadService
import com.nu.bom.core.service.file.FileSystemFileService
import com.nu.bom.core.service.file.MediaTypeDetectionService
import com.nu.bom.core.service.file.S3CloudAwareFileService
import com.nu.bom.core.service.file.S3FileService
import com.nu.bom.core.service.file.S3MultiPartUploadService
import com.nu.bom.core.service.file.SecureFileService
import com.nu.bom.core.service.file.SecureFileServiceImpl
import com.nu.bom.core.service.file.UploadType
import com.nu.bom.core.service.file.module.BomNodeAttachmentUploadModule
import com.nu.bom.core.service.file.module.StepFileUploadModule
import com.nu.bom.core.service.file.module.SvgFileUploadModule
import com.nu.bom.core.service.file.module.UploadModule
import com.nu.bom.core.threedb.ThreeDbService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.user.UserService
import com.tset.bom.clients.VirusScanner
import org.slf4j.LoggerFactory
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.context.annotation.Profile
import org.springframework.core.env.Environment
import org.springframework.core.env.get
import reactor.core.publisher.Mono

@Configuration
@ConfigurationProperties(prefix = "upload.mimetype")
class UploadProperties {
    lateinit var whitelist: List<String>
}

@Configuration
@ConfigurationProperties(prefix = "aws")
class AwsConfig {
    lateinit var s3: Map<String, S3ConfigValues>
}

@Suppress("ktlint:standard:property-naming")
class S3ConfigValues {
    lateinit var bucket: String
    lateinit var region: String
    lateinit var access_key: String
    lateinit var secret_key: String
}

@Configuration
class FileServiceConfig(
    private val virusScanner: VirusScanner,
    private val bomNodeService: BomNodeService,
    private val mediaTypeDetectionService: MediaTypeDetectionService,
    private val uploadProperties: UploadProperties,
    private val awsConfig: AwsConfig,
    private val userService: UserService,
    private val bomradsFileUploadService: BomradsFileUploadService,
    private val threeDbService: ThreeDbService,
    environment: Environment,
) {
    private val namespace = environment[NAMESPACE]

    companion object {
        val logger = LoggerFactory.getLogger(FileServiceConfig::class.java)
        private const val NAMESPACE = "NAMESPACE"
    }

    // we don’t need to mock the secureFileService for tests, since it just refers
    // to other services under the hood, which are already mocked
    @Bean
    fun secureFileService(s3FileService: S3CloudAwareFileService): SecureFileService =
        SecureFileServiceImpl(
            s3FileService,
            virusScanner,
            mediaTypeDetectionService,
            userService,
            bomradsFileUploadService,
            threeDbService,
            createUploadModules(),
        )

    interface InternalBlobStorage {
        fun upload(
            payload: ByteArray,
            accessCheck: AccessCheck,
        ): Mono<String>

        fun download(
            uploadId: String,
            accessCheck: AccessCheck,
        ): Mono<ByteArray>

        fun upload(
            payload: ByteArray,
            fileName: String,
            accessCheck: AccessCheck?,
        ): Mono<String>
    }

    @Bean
    @Profile("cloud")
    @Primary
    fun s3AccountAndFeatureFileService(): S3CloudAwareFileService = S3FileService(awsConfig.s3["files"]!!, namespace)

    @Bean
    @Profile("local", "test")
    @Primary
    fun fileSystemFileService(): S3CloudAwareFileService = FileSystemFileService()

    @Bean("exports")
    @Profile("cloud")
    fun exportBucket(): S3MultiPartUploadService = S3FileService(awsConfig.s3["exports"]!!, namespace)

    @Bean("exports")
    @Profile("local", "test")
    fun exportFileSystem(): S3MultiPartUploadService = FileSystemFileService()

    private fun createUploadModules(): List<UploadModule> =
        UploadType.entries.map { uploadType ->
            when (uploadType) {
                UploadType.BOM_NODE_ATTACHMENT -> BomNodeAttachmentUploadModule(bomNodeService, uploadProperties)
                UploadType.STEP_FILE -> StepFileUploadModule(bomNodeService, uploadProperties)
                UploadType.NESTING_SVG -> SvgFileUploadModule(bomNodeService)
            }
        }

    @Bean
    @Profile("cloud")
    fun s3InternalBlockStorage(): InternalBlobStorage = S3FileService(awsConfig.s3["blobs"]!!, namespace)

    @Bean
    @Profile("local", "test")
    fun fileSystemInternalBlockStorage(): InternalBlobStorage = FileSystemFileService("blobs")
}
