package com.nu.bom.core.service.wizard.steps

import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.model.Wizard
import com.nu.bom.core.repository.WizardRepository
import com.nu.bom.core.user.AccessCheck
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono

class WizardShapeStep(
    val wizardId: String = "",
    fields: List<FieldParameter>,
) : WizardFieldStep(fields, 300) {
    companion object {
        const val SPECIAL_HARDCODED_FIELD_NAME = "shapeId"
    }

    fun shapeId(): FieldParameter? = getField(SPECIAL_HARDCODED_FIELD_NAME)
}

@Service
final class WizardShapeStepManager(
    wizardRepository: WizardRepository,
) : WizardFieldStepManager<WizardShapeStep>(wizardRepository) {
    override fun createStepWithFields(
        fields: List<FieldParameter>,
        accessCheck: AccessCheck,
        wizard: Wizard,
    ): Mono<WizardShapeStep> {
        val wizardId = wizard._id!!.toHexString()

        val actualFields =
            fields.map {
                when (it.name) {
                    WizardShapeStep.SPECIAL_HARDCODED_FIELD_NAME -> {
                        val metaInfo = it.metaInfo?.toMutableMap() ?: mutableMapOf()
                        metaInfo["path"] = "/api/card/$wizardId/shapes"
                        metaInfo["keyWordpath"] = "/api/card/$wizardId/shapekeys"
                        metaInfo["card"] = true

                        it.copy(metaInfo = metaInfo)
                    }
                    else -> it
                }
            }

        return Mono.just(WizardShapeStep(wizard._id!!.toString(), actualFields))
    }

    override val stepType = WizardShapeStep::class
}
