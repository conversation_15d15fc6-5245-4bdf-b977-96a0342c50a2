package com.nu.bom.core.manufacturing.entities

import com.nu.bom.core.manufacturing.annotations.Children
import com.nu.bom.core.manufacturing.annotations.CompositeMandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntityContext
import com.nu.bom.core.manufacturing.annotations.MasterDataCalculation
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.executioncontext.fieldtypes.MaterialClassField
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Currency
import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.manufacturing.fieldTypes.EmissionUnits
import com.nu.bom.core.manufacturing.fieldTypes.ManualMaterialCostMode
import com.nu.bom.core.manufacturing.fieldTypes.MaterialEmissionType
import com.nu.bom.core.manufacturing.fieldTypes.MaterialPriceType
import com.nu.bom.core.manufacturing.fieldTypes.MaterialSubstances
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import java.math.BigDecimal

@Deprecated("kept for backwards compatibility", replaceWith = ReplaceWith("RawMaterialManual"))
@EntityType(Entities.MATERIAL)
class ManualMasterdataMaterial(name: String) : ManufacturingEntity(name) {
    override val extends = BaseEntityFields(name)

    @Input
    @ObjectView(ObjectView.MATERIAL, 35)
    @MandatoryForEntity(index = 3)
    fun costMode(): ManualMaterialCostMode = ManualMaterialCostMode.BUY

    @Input
    @ObjectView(ObjectView.MATERIAL, 30)
    @MandatoryForEntity(index = 4)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun inputQuantity(): QuantityUnit? = null

    @Input
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun quantity(
        inputQuantity: QuantityUnit,
        costMode: ManualMaterialCostMode,
    ) = when (costMode.res) {
        ManualMaterialCostMode.Selection.BUY -> inputQuantity
        ManualMaterialCostMode.Selection.SELL -> inputQuantity * Num(-1.0)
    }

    @Input
    @MandatoryForEntity(context = MandatoryForEntityContext.CREATE_FOR_MASTERDATA)
    @Path("/api/type/material")
    fun materialType(): Text? = null

    @Input
    @Path("/api/currency", false)
    fun baseCurrency(): Currency = Currency("EUR")

    @Input
    @Path("/api/currency", false)
    @ReadOnly
    @CompositeMandatoryForEntity(
        [
            MandatoryForEntity(index = 1, context = MandatoryForEntityContext.CREATE_MANUAL),
            MandatoryForEntity(index = 1, context = MandatoryForEntityContext.CREATE_FROM_MASTERDATA, readOnly = true),
        ],
    )
    fun masterdataBaseCurrency(): Currency = Currency("EUR")

    @ObjectView(ObjectView.MATERIAL, 60)
    @ReadOnly
    @MasterDataCalculation
    @CompositeMandatoryForEntity(
        [
            MandatoryForEntity(index = 1, context = MandatoryForEntityContext.CREATE_MANUAL),
            MandatoryForEntity(index = 1, context = MandatoryForEntityContext.CREATE_FROM_MASTERDATA, readOnly = true),
        ],
    )
    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun pricePerUnit(
        materialPriceType: MaterialPriceType,
        materialBasePrice: Money?,
        composedPricePerUnit: Money?,
    ): Money =
        when (materialPriceType.res) {
            MaterialPriceType.Selection.SIMPLE_PRICE -> (materialBasePrice ?: Money.ZERO)
            MaterialPriceType.Selection.COMPOSED_PRICE -> (composedPricePerUnit ?: Money.ZERO)
        }

    fun materialPriceType() = MaterialPriceType.SIMPLE_PRICE

    fun materialEmissionType() = MaterialEmissionType.SIMPLE_EMISSION

    @Input
    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun materialBasePrice(): Money = Money(BigDecimal.ZERO)

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun composedPricePerUnit(
        @Children(Entities.PRICE_COMPONENT)
        priceComponentPricePerUnit: List<Money>?,
        @Children(Entities.COMPONENT_MATERIAL, directOnly = false)
        weightedPrice: List<Money>?,
    ): Money {
        val fromComponents = if (priceComponentPricePerUnit == null) Money.ZERO else sum(priceComponentPricePerUnit)
        val fromWeightedPrices = if (weightedPrice == null) Money.ZERO else sum(weightedPrice)
        return fromComponents + fromWeightedPrices
    }

    @Input
    @MandatoryForEntity(index = 20, context = MandatoryForEntityContext.CREATE_FOR_MASTERDATA)
    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun materialBaseCO2(): Emission = Emission(BigDecimal.ZERO, EmissionUnits.KILOGRAM_CO2E)

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun cO2PerUnit(
        materialEmissionType: MaterialEmissionType,
        materialBaseCO2: Emission?,
        cO2PerUnitCalculated: Emission?,
    ): Emission? {
        return when (materialEmissionType.res) {
            MaterialEmissionType.Selection.SIMPLE_EMISSION -> materialBaseCO2
            MaterialEmissionType.Selection.COMPOSED_EMISSION -> cO2PerUnitCalculated
        }
    }

    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun cO2PerUnitCalculated(
        @Children(Entities.CO2_PROCESSING_MATERIAL)
        cO2PerUnit: List<Emission>?,
        @Children(Entities.COMPONENT_MATERIAL)
        weightedCO2PerUnit: List<Emission>?,
    ): Emission {
        val emissionComponents = sum(cO2PerUnit, Emission.ZERO)
        val composition = sum(weightedCO2PerUnit, Emission.ZERO)
        return emissionComponents + composition
    }

    @Input
    fun materialSubstances(): MaterialSubstances = MaterialSubstances(emptyList())

    @Input
    @ObjectView(ObjectView.MATERIAL, 20)
    @CompositeMandatoryForEntity(
        [
            MandatoryForEntity(index = 1, context = MandatoryForEntityContext.CREATE_MANUAL),
            MandatoryForEntity(index = 1, context = MandatoryForEntityContext.CREATE_FROM_MASTERDATA),
        ],
    )
    fun reuseOfScrap(): Bool = Bool(false)

    @Input
    fun itemNumber(): Text = Text("")

    // region commercial calculation
    @ReadOnly
    fun commercialMaterialType(): MaterialClassField = MaterialClassField.RAW_MATERIAL

    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun purchasePrice(pricePerUnit: Money): Money = pricePerUnit

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun purchasedQuantity(quantity: QuantityUnit?): QuantityUnit = quantity ?: QuantityUnit(BigDecimal.ZERO)

    // endregion
}
