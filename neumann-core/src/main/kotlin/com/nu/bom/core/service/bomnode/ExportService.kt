package com.nu.bom.core.service.bomnode

import com.nu.bom.core.Trace
import com.nu.bom.core.api.dtos.BomNodeDto
import com.nu.bom.core.api.dtos.BomNodeDtoConversionService
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.model.toBomNodeId
import com.nu.bom.core.model.toMongoBranchId
import com.nu.bom.core.model.toProjectId
import com.nu.bom.core.service.bomrads.BomNodeLoaderService
import com.nu.bom.core.service.bomrads.BomradsBomNodeService
import com.nu.bom.core.service.bomrads.SingleNode
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.tset.core.module.bom.exchangerates.ExchangeRateMap
import com.tset.core.service.domain.Currency
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

@Trace
@Service
class ExportService(
    private val bomNodeDtoConversionService: BomNodeDtoConversionService,
    private val bomNodeLoaderService: BomNodeLoaderService,
    private val bomradsBomNodeService: BomradsBomNodeService,
) {
    companion object {
        val logger = LoggerFactory.getLogger(ExportService::class.java)
    }

    /**
     * Export all the current, root BomNodeSnapshots as BomNodeDtos for the given project.
     * The caller <b>must</b> check that the projectId is allowed for the current user!
     */
    fun exportRootBomNodesByProjectId(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        ccy: List<Currency>?,
    ): Flux<BomNodeDto> =
        findRootBomNodeByProjectId(accessCheck, projectId = projectId) { snapshot ->
            bomNodeDtoConversionService.bomNodeToDtoDirect(
                accessCheck,
                snapshot,
                null,
                emptyList(),
                waterfall = null,
                snapshot.manufacturing,
                exchangeRateMap = snapshot.manufacturing?.getExchangeRateMap() ?: ExchangeRateMap.empty(),
            )
        }

    /**
     * Load all the current, root BomNodeSnapshots for the given project, and apply a transformation on it.
     */
    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun <T> findRootBomNodeByProjectId(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        snapshotFunc: (BomNodeSnapshot) -> Mono<T>,
    ): Flux<T> {
        val projectId = projectId.toProjectId()
        return bomradsBomNodeService
            .listRootNodes(accessCheck = accessCheck, id = projectId, branchId = null, pageRequest = null)
            .flatMap { explorerNode ->
                bomradsBomNodeService.getGlobalBranches(accessCheck, explorerNode.bomNodeId).map {
                    explorerNode.bomNodeId to it
                }
            }.collectList()
            .flatMapMany { nodesWithBranches ->
                val branchIds = nodesWithBranches.map { it.second.branch.id }
                Flux
                    .fromIterable(branchIds)
                    .flatMap { branchId ->
                        bomradsBomNodeService.getBranchView(accessCheck, projectId, branchId)
                    }.flatMap { branchView ->
                        val branchId = branchView.branch.id
                        val snapshotds =
                            nodesWithBranches.filter { it.second.branch.id == branchId }.mapNotNull {
                                branchView.findByBomNodeId(it.first)
                            }
                        bomNodeLoaderService.loadSnapshotsForBranchView(
                            accessCheck = accessCheck,
                            selectedSnapshots = snapshotds,
                            dto = branchView,
                        )
                    }.flatMapIterable {
                        it.snapshotMap.values
                    }
            }.flatMap {
                snapshotFunc(it)
            }
    }

    /**
     * Return all the branch snapshot for the given BomNode.
     */
    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    fun getAllSnapshotWithPart(
        accessCheck: AccessCheck,
        nodeId: BomNodeId,
    ): Flux<BomNodeSnapshot> {
        val bomNodeId = nodeId.toBomNodeId()
        return bomradsBomNodeService.getGlobalBranches(accessCheck, bomNodeId).flatMap { branch ->
            bomNodeLoaderService.getBomNode(
                accessCheck = accessCheck,
                branchId = branch.branch.id.toMongoBranchId(),
                bomNodeId = nodeId,
                loadingMode = SingleNode(nodeId),
            )
        }
    }
}
