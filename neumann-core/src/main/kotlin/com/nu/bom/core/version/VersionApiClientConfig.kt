package com.nu.bom.core.version

import com.nu.bom.core.version.gitlab.PipelineService
import com.nu.http.EnvironmentNameSupplier
import com.nu.k8s.NuKubernetesDiscoveryClient
import org.slf4j.LoggerFactory
import org.springframework.cloud.kubernetes.fabric8.Fabric8PodUtils
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Lazy
import org.springframework.context.annotation.Profile

private val logger = LoggerFactory.getLogger(VersionApiClientConfig::class.java)!!

@Configuration
@Profile("cloud")
class VersionApiClientConfig {
    @Bean
    @Lazy
    fun versionApiClient(
        fabric8PodUtils: Fabric8PodUtils,
        discoveryClient: NuKubernetesDiscoveryClient,
        pipelineService: PipelineService,
        environmentNameSupplier: EnvironmentNameSupplier,
    ): VersionApiClient {
        return ClusterVersionApiClient(
            fabric8PodUtils,
            discoveryClient,
            pipelineService,
            environmentNameSupplier,
        )
    }
}

@Configuration
@Profile("!cloud")
class VersionApiClientConfigMock {
    @Bean
    fun versionApiClient(): VersionApiClient {
        logger.info("Using VersionApiClient mock")
        return LocalVersionApiClient()
    }
}
