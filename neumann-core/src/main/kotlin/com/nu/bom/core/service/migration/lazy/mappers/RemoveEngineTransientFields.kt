package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import org.springframework.stereotype.Service

@Service
class RemoveEngineTransientFields : ManufacturingModelEntityMapper {
    override val changeSetId =
        MigrationChangeSetId("2025-01-31-remove-engine-transient-fields")

    private val engineTransientFields =
        setOf(
            "costModuleVersionProvider",
            "calculationMethodologyConfiguration",
            "calculationQualityConfiguration",
            "shiftModelConfiguration",
            "costFactorsConfiguration",
            "burrWeightConfiguration",
            "injectionConfiguration",
            "sandCastingConfiguration",
            "pcbaConfiguration",
        )

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity =
        entity.copyAll(
            initialFieldWithResults =
                entity.initialFieldWithResults.filterNot { engineTransientFields.contains(it.key) },
            fieldWithResults = entity.fieldWithResults.filterNot { engineTransientFields.contains(it.key) },
        )
}
