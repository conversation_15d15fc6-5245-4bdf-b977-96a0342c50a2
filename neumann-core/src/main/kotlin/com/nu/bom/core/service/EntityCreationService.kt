package com.nu.bom.core.service

import com.nu.bom.core.api.dtos.BatchReorderDto
import com.nu.bom.core.api.dtos.BomNodeDto
import com.nu.bom.core.exception.userException.IncompatibleTechnologiesException
import com.nu.bom.core.exception.userException.ManufacturingEntityNotFoundException
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.Modularized
import com.nu.bom.core.manufacturing.entities.Attachment
import com.nu.bom.core.manufacturing.entities.BaseEntityFields
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.BaseManufacturingFields
import com.nu.bom.core.manufacturing.entities.BaseModelManufacturing
import com.nu.bom.core.manufacturing.entities.BomNodeReference
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.extension.ManufacturingDimensionExtension
import com.nu.bom.core.manufacturing.fieldTypes.ConfigIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.EntityRef
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Json
import com.nu.bom.core.manufacturing.fieldTypes.Quantity
import com.nu.bom.core.manufacturing.service.ConfigurationManagementService
import com.nu.bom.core.manufacturing.service.EntityClassOrName
import com.nu.bom.core.manufacturing.utils.ManufacturingTreeUtils
import com.nu.bom.core.model.BomEntryRelation
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.CompositeTriggerAction
import com.nu.bom.core.model.EntityCreation
import com.nu.bom.core.model.TriggerAction
import com.nu.bom.core.service.bomrads.AllChildren
import com.nu.bom.core.service.change.ChangedEntities
import com.nu.bom.core.service.file.SecureFileService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.FieldResultUtils
import org.bson.types.ObjectId
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono
import kotlin.reflect.full.findAnnotation

@Service
class EntityCreationService(
    private val entityManager: EntityManager,
    private val manufacturingCalculationService: ManufacturingCalculationService,
    private val manufacturingEntityFactoryService: ManufacturingEntityFactoryService,
    private val bomNodeConversionService: BomNodeConversionService,
    private val bomNodeService: BomNodeService,
    private val configurationManagementService: ConfigurationManagementService,
    private val secureFileService: SecureFileService,
) {
    companion object {
        private val LOG = LoggerFactory.getLogger(EntityCreationService::class.java)

        val DEFAULT_BEFORE_ADD_CUSTOMIZER: BeforeAddCustomizer = { baseManufacturing, _ ->
            Mono.just(Pair(baseManufacturing, emptyList()))
        }
    }

    // called when adding e.g. materials or consumables
    fun createAndCalculate(
        accessCheck: AccessCheck,
        creationData: EntityCreationDataConversionService.EntityCreationData,
        beforeAddCustomizer: BeforeAddCustomizer? = null,
        dirtyChildLoading: Boolean = false,
    ): Mono<BomNodeDto> {
        val bomNodeInfo =
            BomNodeInfo(
                creationData.bomNodeId,
                creationData.branchId,
                creationData.parentId,
                creationData.childBomNodeId,
            )
        val bulkEntityCreation = Flux.fromIterable(creationData.items).concatMap { createEntity(accessCheck, it) }
        val trigger = CompositeTriggerAction(bomNodeId = bomNodeInfo.parentBomNodeId)
        val configMono =
            creationData.technology?.let {
                configurationManagementService.getDefaultCostModuleConfigurationIdentifier(
                    accessCheck,
                    it,
                )
            } ?: Mono.just(ConfigurationIdentifier.empty())
        return configMono.flatMap {
            addEntity(
                accessCheck = accessCheck,
                bomNodeInfo = bomNodeInfo,
                trigger = trigger,
                bulkEntityCreation = bulkEntityCreation,
                insertBeforeEntityId = creationData.insertBeforeEntityId,
                insertAfterEntityId = creationData.insertAfterEntityId,
                entityLink =
                    creationData.entityLink?.let { entityLinkInformation ->
                        EntityLink(entityLinkInformation.entityLinkField, entityLinkInformation.linkEntityId)
                    },
                beforeAddCustomizer = beforeAddCustomizer,
                dirtyChildLoading = dirtyChildLoading,
                configIdentifier = it,
            ).map {
                LOG.debug("result: snapshot: {}", it.result.manufacturing?.snapshot)
                it.result.bomNode
            }
        }
    }

    data class EntityLink(
        val entityLinkField: String,
        val linkEntityId: ObjectId,
    )

    data class BomNodeInfo(
        val parentBomNodeId: BomNodeId,
        val branchId: BranchId?,
        val parentManufacturingEntityId: ObjectId,
        val childBomNodeId: BomNodeId? = null,
    )

    fun addEntity(
        accessCheck: AccessCheck,
        bomNodeInfo: BomNodeInfo,
        trigger: CompositeTriggerAction,
        bulkEntityCreation: Flux<Pair<ManufacturingEntity, Entities>>,
        insertBeforeEntityId: ObjectId? = null,
        insertAfterEntityId: ObjectId? = null,
        entityLink: EntityLink? = null,
        beforeAddCustomizer: BeforeAddCustomizer? = null,
        dirtyChildLoading: Boolean = false,
        configIdentifier: ConfigurationIdentifier = ConfigurationIdentifier.empty(),
        transformRoot: ((BaseManufacturing, BomNodeSnapshot) -> Mono<BaseManufacturing>)? = null,
        context: ManufacturingCalculationService.Context = ManufacturingCalculationService.Context.LIMITED,
    ) = manufacturingCalculationService.transformAndCalculate(
        accessCheck = accessCheck,
        bomNodeId = bomNodeInfo.parentBomNodeId,
        triggerAction = trigger,
        context = context,
        dirtyChildLoading = dirtyChildLoading,
        branchId = bomNodeInfo.branchId,
        transformRoot = transformRoot,
    ) { baseManufacturing, snapshot, changed ->
        (beforeAddCustomizer ?: DEFAULT_BEFORE_ADD_CUSTOMIZER)
            .invoke(baseManufacturing, snapshot)
            .flatMap { (customizedManufacturing, triggers) ->
                setUpConfigurationForModularization(customizedManufacturing, configIdentifier)
                val (manufacturing, parentEntity) = findParentById(customizedManufacturing, bomNodeInfo.parentManufacturingEntityId)
                trigger.extTriggers += triggers
                bulkEntityCreation
                    .concatMap { (entity, entityType) ->
                        validateModularizedEntityAdd(entity, manufacturing)
                        val extTrigger =
                            EntityCreation.Ext(
                                entityId = entity._id,
                                entityName = entity.name,
                                entityClass = entity.getEntityType(),
                                displayName = entity.displayName,
                            )

                        trigger.extTriggers +=
                            EntityCreation(
                                bomNodeId = bomNodeInfo.parentBomNodeId,
                                entityType = entityType.name,
                                parentId = bomNodeInfo.parentManufacturingEntityId,
                            ).also { innerTrigger ->
                                innerTrigger.ext = extTrigger
                            }

                        val entityTypeAnnotation = entity.getEntityTypeAnnotationOrThrow()
                        if (ManufacturingEntityRearrangementService.isReorderableDirectMaterial(entityTypeAnnotation) &&
                            entity.getFieldOrInitialFieldResult("sortIndex")?.res == null
                        ) {
                            // when adding materials, consumables, or elco
                            // we need to sort the existing items first and also apply a sortIndex to the new item
                            // this is necessary, to insert the new entities at the correct position
                            ManufacturingEntityRearrangementService.reorderDirectMaterialsInSnapshot(
                                parentSnapshot = snapshot,
                                reorderRequest =
                                    BatchReorderDto.createForDirectMaterialOrder(
                                        bomNodeInfo.branchId?.toHexString(),
                                        bomNodeInfo.parentManufacturingEntityId,
                                    ),
                                newDirectMaterial = entity,
                            )
                            val appliedSortIndex = entity.getFieldOrInitialFieldResult("sortIndex") as Quantity?
                            if (appliedSortIndex != null) {
                                entity.addInitialFieldResult("sortIndex") { appliedSortIndex.withSource(FieldResult.SOURCE.I) }
                            }
                        }
                        insertChild(
                            entityType = entityTypeAnnotation,
                            parent = parentEntity,
                            child = entity,
                            insertBefore =
                                insertBeforeEntityId?.let {
                                    ManufacturingTreeUtils.findEntityById(parentEntity, it)
                                        ?: error("Could not find entityId (insert before) = $insertBeforeEntityId")
                                },
                            insertAfter =
                                insertAfterEntityId?.let {
                                    ManufacturingTreeUtils.findEntityById(parentEntity, it)
                                        ?: error("Could not find entityId (insert after) = $insertAfterEntityId")
                                },
                        )

                        if (entityLink != null) {
                            LOG.info("Automatically setting link for entity link information $entityLink")
                            parentEntity.findByEntityId(entityLink.linkEntityId.toString())?.let { linkEntity ->
                                linkEntity.addFieldResult(entityLink.entityLinkField) {
                                    EntityRef(entity.entityId).withSource(FieldResult.SOURCE.I)
                                }
                            } ?: run {
                                LOG.warn("Setting link for entity couldn't find entity with id ${entityLink.linkEntityId}")
                            }
                        }

                        // TODO kw idk if here is the appropriate place to change the attachment owner
                        // maybe transformLoadedAndCalculateWithNewSnapshot is better
                        // because there we are already working with the fully calculated manufacturing tree
                        // at this point, we are transforming the snapshot but are still pre-calculation
                        val changeOwnerMono =
                            if (entityTypeAnnotation == Entities.ATTACHMENT) {
                                // patch the owner of the file to the bom node id based on the attachment file id
                                val fileId = entity.getFieldOrInitialFieldResult(Attachment::fileId.name)?.res as String
                                secureFileService.changeOwner(accessCheck, fileId, bomNodeInfo.parentBomNodeId.toHexString())
                            } else {
                                Mono.just(Unit)
                            }

                        val manuMono =
                            if (entity is BomNodeReference && bomNodeInfo.childBomNodeId != null) {
                                addSubManufacturing(
                                    accessCheck,
                                    bomNodeInfo.childBomNodeId,
                                    bomNodeInfo.branchId,
                                    bomEntry = entity,
                                    mainSnapshot = snapshot,
                                    changedEntities = changed,
                                ).thenReturn(manufacturing)
                            } else {
                                Mono.just(manufacturing)
                            }
                        changeOwnerMono.then(manuMono)
                    }.takeLast(1)
                    .toMono()
            }
    }

    private fun setUpConfigurationForModularization(
        manufacturing: BaseManufacturing,
        configIdentifier: ConfigurationIdentifier,
    ) {
        if (configIdentifier.isEmpty() || manufacturing !is BaseModelManufacturing) {
            return
        }
        val technologyModel = manufacturing.getFieldResult(BaseModelManufacturing::technologyModel.name)?.res as String
        // Only set when modularizing manual manufacturing
        if (Model.fromEntity(technologyModel) == Model.MANUAL) {
            // We need to change user overwrites since it could be a different technology
            val fieldName = BaseManufacturingFields::costModuleConfigurationIdentifier.name
            manufacturing.replaceOrAddFieldResult(fieldName) { oldResult ->
                (oldResult as ConfigIdentifier?)?.withRes(Json.Jsondata(configIdentifier))
                    ?: ConfigIdentifier(configIdentifier)
            }
            // We set initial field so no broken chain icon is visible
            manufacturing.replaceOrAddInitialFieldResult(fieldName) { oldResult ->
                (oldResult as ConfigIdentifier?)?.withRes(Json.Jsondata(configIdentifier))
                    ?: ConfigIdentifier(configIdentifier)
            }
        }
    }

    private fun createEntity(
        accessCheck: AccessCheck,
        creationItemData: EntityCreationDataConversionService.EntityCreationData.ItemData,
    ): Mono<Pair<ManufacturingEntity, Entities>> {
        val fields = creationItemData.fields
        val overwrites = creationItemData.overwrites
        val entityName = fields[BaseEntityFields::displayDesignation.name]?.res.toString()
        val entityType = creationItemData.entityType
        val entityClass = getEntityClass(creationItemData)
        val masterDataSelector = creationItemData.masterDataSelector

        return when {
            creationItemData.entityType == Entities.MANUFACTURING_STEP && masterDataSelector != null ->
                Mono.just(
                    manufacturingEntityFactoryService.createEntity(
                        name = entityName,
                        entityType = entityType,
                        clazz = entityClass,
                        fields = fields,
                        overwrites = overwrites,
                        masterDataSelector = masterDataSelector,
                        isolated = creationItemData.isolated,
                    ),
                )

            masterDataSelector != null -> {
                manufacturingEntityFactoryService.createEntityWithMasterData(
                    accessCheck,
                    name = creationItemData.masterDataSelector.key,
                    entityType = entityType,
                    clazz = entityClass,
                    masterDataSelector = masterDataSelector,
                    fields = fields,
                    overwrites = overwrites,
                    isolated = creationItemData.isolated,
                )
            }

            else ->
                Mono.just(
                    manufacturingEntityFactoryService.createEntity(
                        name = entityName,
                        entityType = entityType,
                        masterDataSelector = creationItemData.sourceMasterDataSelector,
                        clazz = entityClass,
                        fields = fields + FieldResultUtils.overrideBaseCurrency(fields, fields),
                        overwrites = overwrites,
                        isolated = creationItemData.isolated,
                    ),
                )
        }.map {
            Pair(it, creationItemData.entityType)
        }
    }

    private fun getEntityClass(creationItemData: EntityCreationDataConversionService.EntityCreationData.ItemData): EntityClassOrName =
        with(creationItemData) {
            entityManager.getActualCreationClass(
                entityType,
                fields["entityClass"]?.res as? String ?: entityClass?.simpleName,
                fields,
                masterDataSelector,
            )
        }

    private fun addSubManufacturing(
        accessCheck: AccessCheck,
        childBomNodeId: BomNodeId,
        branchId: BranchId?,
        bomEntry: ManufacturingEntity,
        mainSnapshot: BomNodeSnapshot,
        changedEntities: ChangedEntities,
    ): Mono<ManufacturingEntity> =
        loadNode(accessCheck, childBomNodeId, branchId)
            .map { childSnapshot ->
                changedEntities.addOriginalSnapshot(childSnapshot)
                val newChildSnapshot = childSnapshot.newSnapshot()
                val childRelation =
                    BomEntryRelation(
                        bomEntryId = bomEntry._id,
                        bomNodeId = childSnapshot.bomNodeId(),
                        version = bomEntry.version,
                        generated = false,
                    ).setSnapshot(newChildSnapshot)
                mainSnapshot.subNodes += childRelation

                val parentRelation =
                    BomEntryRelation(
                        bomEntryId = bomEntry._id,
                        bomNodeId = mainSnapshot.bomNodeId(),
                        version = bomEntry.version,
                        generated = false,
                    ).setSnapshot(mainSnapshot)
                newChildSnapshot.parentsToUse = childSnapshot.parentsToUse + parentRelation

                newChildSnapshot.setAsHeadVersion()
                changedEntities.addSnapshotAndNode(newChildSnapshot)
                bomNodeConversionService.bomNodeToManufacturingCalculationTree(
                    newChildSnapshot,
                    setOf(mainSnapshot.bomNodeId(), childSnapshot.bomNodeId()),
                )
                bomEntry
            }

    private fun loadNode(
        accessCheck: AccessCheck,
        bomNodeId: BomNodeId,
        branchId: BranchId?,
    ): Mono<BomNodeSnapshot> {
        // TODO: we could technically optimize this, to avoid the extra Bomrad request
        return bomNodeService.getBomNode(
            accessCheck,
            nodeId = bomNodeId,
            branch = branchId,
            loadingMode = AllChildren(bomNodeId),
        )
    }

    private fun validateModularizedEntityAdd(
        entity: ManufacturingEntity,
        manufacturingEntity: ManufacturingEntity,
    ) {
        val modularized = entity::class.findAnnotation<Modularized>()
        if (modularized != null) {
            val manuDimension = manufacturingEntity.getField(ManufacturingDimensionExtension::dimension.name)
            val modularizedDimensions = modularized.dimensions
            if (!modularizedDimensions.contains(manuDimension?.result?.res)) {
                throw IncompatibleTechnologiesException()
            }
        }
    }

    private fun insertChild(
        entityType: Entities,
        parent: ManufacturingEntity,
        child: ManufacturingEntity,
        insertBefore: ManufacturingEntity? = null,
        insertAfter: ManufacturingEntity? = null,
    ) {
        when (entityType) {
            Entities.MANUFACTURING_STEP -> ManufacturingTreeUtils.insertStep(parent, child, insertBefore, insertAfter)
            else -> simpleAddChild(parent, child, insertBefore, insertAfter)
        }
    }

    private fun simpleAddChild(
        parent: ManufacturingEntity,
        entityToInsert: ManufacturingEntity,
        insertBefore: ManufacturingEntity? = null,
        insertAfter: ManufacturingEntity? = null,
    ) {
        parent.addChild(entityToInsert, insertBefore, insertAfter)
    }

    private fun findParentById(
        manufacturing: BaseManufacturing,
        parentId: ObjectId,
    ): Pair<BaseManufacturing, ManufacturingEntity> {
        val parentEntity = ManufacturingTreeUtils.findEntityById(manufacturing, parentId)
        return if (parentEntity != null) {
            manufacturing to parentEntity
        } else {
            throw ManufacturingEntityNotFoundException(
                parentId.toHexString(),
                manufacturing.bomNodeId?.toHexString(),
            )
        }
    }
}

typealias BeforeAddCustomizer = (BaseManufacturing, BomNodeSnapshot) -> Mono<Pair<BaseManufacturing, List<TriggerAction>>>
