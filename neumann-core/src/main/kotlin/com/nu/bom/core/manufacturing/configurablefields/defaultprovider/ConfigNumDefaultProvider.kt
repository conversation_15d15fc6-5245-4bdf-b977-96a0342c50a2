package com.nu.bom.core.manufacturing.configurablefields.defaultprovider

import com.nu.bom.core.manufacturing.defaults.DefaultProvider
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Num
import java.math.BigDecimal

class ConfigNumDefaultProvider(
    private val value: BigDecimal = BigDecimal.ZERO,
) : DefaultProvider<Num> {
    override fun provide(): Num {
        val inputValue = Num(value)
        inputValue.source = FieldResult.SOURCE.I
        return inputValue
    }
}
