package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.entities.BomEntry
import com.nu.bom.core.manufacturing.entities.ElectronicComponent
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import com.nu.bom.core.utils.mapOfNotNull
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class AddingItemNumberMigration : ManufacturingModelEntityMapper {
    companion object {
        private val logger = LoggerFactory.getLogger(AddingItemNumberMigration::class.java)
    }

    override val changeSetId = MigrationChangeSetId("2024-12-31-adding-item-number")

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity {
        val oldFieldName =
            when (entity.type) {
                Entities.BOM_ENTRY.name -> BomEntry::partNumber.name
                Entities.C_PART.name -> ElectronicComponent::mpn.name
                else -> {
                    logger.error("In the $changeSetId migration a invalid entity type occurred: ${entity.type}")
                    "invalid key"
                }
            }

        val newFieldAsMap = getNewField(entity.initialFieldWithResults + entity.fieldWithResults, oldFieldName)
        return entity.copyAll(fieldWithResults = entity.fieldWithResults + newFieldAsMap)
    }

    private fun getNewField(
        fields: Map<String, FieldResultModel>,
        oldFieldName: String,
    ): Map<String, FieldResultModel> {
        return if (fields.contains(BomEntry::itemNumber.name)) {
            emptyMap()
        } else {
            mapOfNotNull(BomEntry::itemNumber.name to fields[oldFieldName])
        }
    }
}
