package com.nu.bom.core.api

import com.nu.bom.core.config.NuGenericConfiguration
import com.nu.bom.core.model.Account
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.model.toMongoProjectId
import com.nu.bom.core.service.AccountService
import com.nu.bom.core.service.GarbageCollectorService
import com.nu.bom.core.service.ProjectService
import com.nu.bom.core.user.AccessCheckProvider
import com.nu.bom.core.utils.Maybe
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.nu.bomrads.dto.admin.ProjectDTO
import com.tset.core.module.bom.EventSourcingModule
import org.springframework.amqp.core.AmqpAdmin
import org.springframework.amqp.core.QueueInformation
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toFlux

@RestController
@RequestMapping("/api/admin")
class AdminController(
    private val accessCheckProvider: AccessCheckProvider,
    private val accountService: AccountService,
    private val projectService: ProjectService,
    private val garbageCollectorService: GarbageCollectorService,
    private val nuGenericConfiguration: NuGenericConfiguration,
    private val eventSourcingModule: EventSourcingModule,
    private val ampqAdmin: AmqpAdmin,
) {
    @GetMapping("/re-index")
    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    fun reindexSearch(
        @AuthenticationPrincipal jwt: Jwt?,
        @RequestParam(required = false) accountName: String?,
        @RequestParam(required = false) projectKey: String?,
        @RequestParam(required = false) search: Boolean = true,
    ): Flux<String> {
        return accessCheckProvider.adminOnlyFlux(jwt) {
            getAccounts(accountName)
                .flatMap { account -> getProjects(account, projectKey) }
                .flatMap { (account, project) -> sendUpdateEvent(account, project, search) }
        }
    }

    @GetMapping("/cleanup-mongodb/{accountKey}")
    fun cleanupMongoDatabase(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable accountKey: String,
    ): Mono<Long> {
        return accessCheckProvider.doAs(jwt) {
            garbageCollectorService.cleanupAccount(accountKey)
        }
    }

    @PostMapping("/mongo-snapshots/delete-all-in-environment")
    fun deleteSnapshotsForEnvironment(
        @AuthenticationPrincipal jwt: Jwt?,
        @RequestParam(required = true) env: String,
        @RequestParam(required = false) accountName: String?,
    ): Mono<Long> {
        return accessCheckProvider.adminOnly(jwt) {
            garbageCollectorService.deleteBomNodesForEnvironment(environmentName = env, accountName = accountName, deleted = false)
        }
    }

    @PostMapping("/mongo-snapshots/delete-trash-in-environment")
    fun deleteTrashedSnapshotsForEnvironment(
        @AuthenticationPrincipal jwt: Jwt?,
        @RequestParam(required = true) env: String,
        @RequestParam(required = false) accountName: String?,
    ): Mono<Long> {
        return accessCheckProvider.adminOnly(jwt) {
            garbageCollectorService.deleteBomNodesForEnvironment(environmentName = env, accountName = accountName, deleted = true)
        }
    }

    /**
     * If 'allEnv=true' we do a cleanup on every environment, which maybe break things
     * Otherwise, we either use 'std' or the value, specified as 'env'
     */
    @GetMapping("/snapshot-cleanup")
    fun snapshotCleanup(
        @AuthenticationPrincipal jwt: Jwt?,
        @RequestParam(required = false) env: String?,
        @RequestParam(required = false, defaultValue = "false") allEnv: Boolean,
        @RequestParam(required = false) disableRefresh: Boolean?,
        @RequestParam(required = false, defaultValue = "false") readOnly: Boolean,
    ): Mono<Long> {
        return accessCheckProvider.doAs(jwt) {
            val filterEnv =
                if (allEnv) {
                    null
                } else {
                    env ?: garbageCollectorService.deployedFeatureName ?: "std"
                }
            val q =
                GarbageCollectorService.QueryParameters(
                    env = filterEnv,
                    readOnly = readOnly,
                    disableRefresh = disableRefresh ?: nuGenericConfiguration.disableViewRefresh,
                )
            garbageCollectorService.archiveAndCleanupWithQuery(q)
        }
    }

    @GetMapping("/rbmq/queueInfo/{queueName}")
    fun queueInfo(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable queueName: String,
    ): Mono<QueueInformation> {
        return accessCheckProvider.adminOnly(jwt) {
            ampqAdmin.getQueueInfo(queueName)?.let { Mono.just(it) } ?: Mono.empty()
        }
    }

    @GetMapping("/rbmq/purgeQueue/{queueName}")
    fun purgeQueue(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable queueName: String,
    ): Mono<Int> {
        return accessCheckProvider.adminOnly(jwt) {
            Mono.just(ampqAdmin.purgeQueue(queueName))
        }
    }

    private fun sendUpdateEvent(
        account: Account,
        project: ProjectDTO,
        search: Boolean,
    ): Mono<String> {
        return maybeUpdateSearch(search, account, project.id.toMongoProjectId())
            .map { "${account.externalRoleName} - ${project.key} updated" }
    }

    private fun getProjects(
        account: Account,
        projectKey: String?,
    ) = projectService.getProjectsByAccountId(accountName = account.externalRoleName)
        .filter { projectKey == null || projectKey == it.key }
        .map { account to it }

    private fun getAccounts(accountName: String?) =
        (
            accountName?.let { accountService.getAccountFromExternalRoleName(accountName).toFlux() }
                ?: accountService.getAll()
        )

    private fun maybeUpdateSearch(
        search: Boolean,
        account: Account,
        project: ProjectId,
    ) = if (search) {
        eventSourcingModule.publishProjectUpdated(
            account._id!!.toHexString(),
            account.externalRoleName,
            project.toHexString(),
        ).map { Maybe(it) }
    } else {
        Mono.just(Maybe())
    }
}
