package com.nu.bom.core.turn

import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.exception.userException.LimitType
import com.nu.bom.core.exception.userException.NumericInputExceedsLimitException
import com.nu.bom.core.manufacturing.behaviours.ShapeBasedCostModuleBehaviorLookup
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.turn.model.Path
import com.tset.bom.clients.geometry.Point2D
import java.math.BigDecimal

data class TurningPartProperties(
    val length: Length,
    val dmin: Length,
    val dmax: Length,
) {
    companion object {
        fun empty() =
            TurningPartProperties(
                Length(0.0, LengthUnits.MILLIMETER),
                Length(0.0, LengthUnits.MILLIMETER),
                Length(0.0, LengthUnits.MILLIMETER),
            )

        fun fromGeometry(geometry: Geometry): TurningPartProperties =
            fromPoint(
                geometry.map {
                    Point2D(x = it.x.toBigDecimal(), y = it.y.toBigDecimal())
                },
            )

        fun fromClosedPath(closedPath: Path): TurningPartProperties =
            fromPoint(
                closedPath.map {
                    Point2D(x = it.x.toBigDecimal(), y = it.d.toBigDecimal())
                },
            )

        private fun getIfNonZero(
            fields: List<FieldParameter>,
            fieldName: String,
        ): FieldParameter? =
            fields.find { it.name == fieldName }?.let {
                if ((it.value as? BigDecimal)?.compareTo(BigDecimal.ZERO) == 0) {
                    null
                } else {
                    it
                }
            }

        fun fromFields(fields: List<FieldParameter>): TurningPartProperties {
            val partLengthFP = getIfNonZero(fields, "partLength")
            val partHeightFP = getIfNonZero(fields, "partHeight")
            val partInnerDiameterFP = getIfNonZero(fields, "partInnerDiameter")
            val partOuterDiameterFP = getIfNonZero(fields, "partOuterDiameter")

            if ((partLengthFP == null) == (partHeightFP == null)) {
                throw IllegalArgumentException("For turning, only partInputGroups with exactly one, partLength or partHeight, are allowed.")
            }
            val length = Length(partLengthFP ?: partHeightFP!!)

            val dmin =
                if (partInnerDiameterFP == null) {
                    Length(BigDecimal.ZERO, LengthUnits.METER)
                } else {
                    Length(partInnerDiameterFP)
                }

            if (partOuterDiameterFP == null) {
                throw IllegalArgumentException("For turning, only partInputGroups with partOuterDiameter are allowed.")
            }
            val dmax = Length(partOuterDiameterFP)

            checkConstraints(length, dmin, dmax)

            return TurningPartProperties(length = length, dmin = dmin, dmax = dmax)
        }

        private fun fromPoint(points: List<Point2D<BigDecimal>>): TurningPartProperties =
            if (points.size >= 3) {
                val length = Length(points.maxOf { it.x } - points.minOf { it.x }, LengthUnits.MILLIMETER)
                val dmin = Length(points.minOf { it.y }, LengthUnits.MILLIMETER)
                val dmax = Length(points.maxOf { it.y }, LengthUnits.MILLIMETER)

                // If the user isn't done drawing yet, it might not yet be possible
                // to create non-empty TurningPartProperties.
                try {
                    checkConstraints(length, dmin, dmax)
                    TurningPartProperties(length, dmin, dmax)
                } catch (_: NumericInputExceedsLimitException) {
                    empty()
                }
            } else {
                empty()
            }

        private fun checkConstraints(
            length: Length,
            dmin: Length,
            dmax: Length,
        ) {
            if (length.res <= BigDecimal.ZERO) {
                throw NumericInputExceedsLimitException(
                    fieldName = ShapeBasedCostModuleBehaviorLookup::partLength.name,
                    limitType = LimitType.LESS_EQ,
                    limit = BigDecimal.ZERO,
                )
            }
            if (dmin.res < BigDecimal.ZERO) {
                throw NumericInputExceedsLimitException(
                    fieldName = ShapeBasedCostModuleBehaviorLookup::partInnerDiameter.name,
                    limitType = LimitType.LESS,
                    limit = BigDecimal.ZERO,
                )
            }
            if (dmax.res <= dmin.res) {
                throw NumericInputExceedsLimitException(
                    fieldName = ShapeBasedCostModuleBehaviorLookup::partOuterDiameter.name,
                    limitType = LimitType.LESS_EQ,
                    limit = ShapeBasedCostModuleBehaviorLookup::partInnerDiameter.name,
                )
            }
        }
    }
}
