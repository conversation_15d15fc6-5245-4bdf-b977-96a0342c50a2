package com.nu.bom.core.service.exports.tasks

import com.nu.bom.core.model.ExportTask
import com.nu.bom.core.model.toProjectId
import com.nu.bom.core.service.bomrads.BomradsBomNodeService
import com.nu.bom.core.service.exports.ExportNameUtil
import org.springframework.data.mongodb.core.ReactiveMongoTemplate
import org.springframework.stereotype.Component
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

@Component
class BomNodesExportTask(
    override val mongo: ReactiveMongoTemplate,
    private val bomradsBomNodeService: BomradsBomNodeService,
) : AbstractExportTask(mongo) {
    override val exportTask = ExportTask.BOM_NODES
    override val dirName = ExportNameUtil.BOMNODES_DIR

    override fun exportInternal(context: ExportContext): Mono<Int> =
        bomradsBomNodeService.getBomNodeForProject(context.accessCheck, context.projectId.toProjectId())
            .flatMap { nodes ->
                Flux.fromIterable(nodes.distinctBy { it.id }).concatMap { bomNode ->
                    bomNode.id?.idToString()?.let { context.bomNodeIds.add(it) }
                    processDto(context, bomNode.id?.id, bomNode)
                }.collectList().map { it.size }
            }
}
