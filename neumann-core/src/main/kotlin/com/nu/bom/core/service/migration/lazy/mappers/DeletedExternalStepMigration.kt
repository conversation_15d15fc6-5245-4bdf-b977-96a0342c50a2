package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.entities.BomEntry
import com.nu.bom.core.manufacturing.entities.ExternalBomEntry
import com.nu.bom.core.manufacturing.entities.ExternalManufacturingStep
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import org.springframework.stereotype.Service

@Service
class DeletedExternalStepMigration : ManufacturingModelEntityMapper {
    override val changeSetId = MigrationChangeSetId("2023-10-10-deleted-external-step")

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity {
        if (entity.clazz == ExternalManufacturingStep::class.simpleName!!) return entity
        return entity.copyAll(
            children =
                entity.children.map {
                    if (it.clazz == ExternalBomEntry::class.simpleName!!) {
                        it.copyAll(clazz = BomEntry::class.simpleName!!)
                    } else {
                        it
                    }
                },
        )
    }
}
