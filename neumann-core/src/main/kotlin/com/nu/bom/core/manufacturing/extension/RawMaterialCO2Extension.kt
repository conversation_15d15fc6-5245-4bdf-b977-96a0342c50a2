package com.nu.bom.core.manufacturing.extension

import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.Extends
import com.nu.bom.core.manufacturing.annotations.FieldName
import com.nu.bom.core.manufacturing.annotations.FilteredChildren
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.StaticDenominatorUnit
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ManufacturingEntityExtension
import com.nu.bom.core.manufacturing.entities.RawMaterial
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_ELECTRICITY_EMISSIONS_ENTITY_NAME
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_NATURAL_GAS_EMISSIONS_ACTIVE_ENTITY_NAME
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_NATURAL_GAS_EMISSIONS_PASSIVE_ENTITY_NAME
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.StaticUnitOverride
import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.manufacturing.fieldTypes.MaterialFurnaceType
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.utils.getResultWithSelectedAttributes
import org.bson.types.ObjectId
import reactor.core.publisher.Flux
import java.math.BigDecimal

@Extends([RawMaterial::class], CO2_EXTENSION_PACKAGE)
class RawMaterialCO2Extension(
    name: String,
) : ManufacturingEntityExtension(name) {
    companion object {
        val CASTING_ALLOYS = listOf("AluminiumAlloys", "MagnesiumAlloys", "AluminiumAlloys_BarExtruded")
        val FTI_TECHS = listOf("FTIPDS", "FTITDS")
    }

    @Input
    fun templateName(): Text = Text("DefaultZero")

    @Input
    @Parent(Entities.MANUFACTURING_STEP)
    fun location(): Text = Text("Austria")

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun naturalGasEmissionInDirect(
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_NATURAL_GAS_EMISSIONS,
            nameFilter = TSET_COST_NATURAL_GAS_EMISSIONS_PASSIVE_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        ) value: Map<ObjectId, Emission>?,
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_NATURAL_GAS_EMISSIONS,
            nameFilter = TSET_COST_NATURAL_GAS_EMISSIONS_PASSIVE_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        @FieldName("location")
        costFactorLocations: Map<ObjectId, Text>,
        location: Text,
    ): Emission? {
        return getResultWithSelectedAttributes(value, costFactorLocations, location)
    }

    @StaticDenominatorUnit(StaticUnitOverride.CM)
    fun naturalGasEmissionDirect(
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_NATURAL_GAS_EMISSIONS,
            nameFilter = TSET_COST_NATURAL_GAS_EMISSIONS_ACTIVE_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        ) value: Map<ObjectId, Emission>?,
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_NATURAL_GAS_EMISSIONS,
            nameFilter = TSET_COST_NATURAL_GAS_EMISSIONS_ACTIVE_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        @FieldName("location")
        costFactorLocations: Map<ObjectId, Text>,
        location: Text,
    ): Emission? {
        return getResultWithSelectedAttributes(value, costFactorLocations, location)
    }

    @StaticDenominatorUnit(StaticUnitOverride.KILOWATTHOUR)
    fun electricityCarbon(
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_ELECTRICITY_EMISSIONS,
            nameFilter = TSET_COST_ELECTRICITY_EMISSIONS_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        ) value: Map<ObjectId, Emission>?,
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_ELECTRICITY_EMISSIONS,
            nameFilter = TSET_COST_ELECTRICITY_EMISSIONS_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        @FieldName("location")
        costFactorLocations: Map<ObjectId, Text>,
        location: Text,
    ): Emission? {
        return getResultWithSelectedAttributes(value, costFactorLocations, location)
    }

    @Parent(Entities.MANUFACTURING_STEP)
    fun locationName(): Text = Text("")

    @Parent(Entities.MANUFACTURING)
    fun technologyKey(): Text = Text("")

    @EntityCreation(Entities.CO2_PROCESSING_MATERIAL)
    fun createCO2Materials(): Flux<ManufacturingEntity> = Flux.empty()

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun cO2PerPartMaterial(
        purchasedWeightPerPart: QuantityUnit,
        cO2PerUnit: Emission,
    ): Emission = cO2PerUnit * purchasedWeightPerPart

    fun alloyClass(): Num = Num(BigDecimal.ONE)

    @Input
    fun materialProcessFurnaceType(): MaterialFurnaceType = MaterialFurnaceType.BLAST_FURNACE

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun cO2PerPart(cO2PerPartMaterial: Emission): Emission = cO2PerPartMaterial

    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun purchaseCO2(
        purchasedWeightPerPart: QuantityUnit,
        cO2PerPartMaterial: Emission,
    ): Emission {
        if (purchasedWeightPerPart.res.compareTo(BigDecimal.ZERO) == 0) return Emission.ZERO
        return cO2PerPartMaterial / purchasedWeightPerPart
    }
}
