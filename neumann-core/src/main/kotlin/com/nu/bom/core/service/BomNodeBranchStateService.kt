package com.nu.bom.core.service

import com.nu.bom.core.model.BomNodeBranchState
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.MergeSourceType
import com.nu.bom.core.repository.BomNodeBranchStateRepository
import com.nu.bom.core.utils.component1
import com.nu.bom.core.utils.component2
import com.nu.bom.core.utils.optional
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import java.util.Optional

@Service
class BomNodeBranchStateService(
    private val bomNodeBranchStateRepository: BomNodeBranchStateRepository,
) {
    fun getOpenMergesAvailable(
        bomNodeId: BomNodeId,
        branchId: BranchId,
    ): Mono<List<MergeSourceType>> =
        getMergeStates(
            branchId,
            bomNodeId,
        ).map { (branchMergeState: Optional<BomNodeBranchState>, bomNodeMergeState: Optional<BomNodeBranchState>) ->
            val branchMerges = branchMergeState.map { it.openMergesAvailable ?: emptyList() }.orElseGet { emptyList() }
            val bomNodeMerges =
                bomNodeMergeState.map { it.openMergesAvailable ?: emptyList() }.orElseGet { emptyList() }

            branchMerges + bomNodeMerges
        }.switchIfEmpty(Mono.just(emptyList()))

    fun setOpenMergesAvailable(
        bomNodeId: BomNodeId,
        branchId: BranchId,
        openMergesAvailable: List<MergeSourceType>,
    ): Mono<List<MergeSourceType>> =

        getMergeStates(
            branchId,
            bomNodeId,
        ).flatMap { (branchMergeState: Optional<BomNodeBranchState>, bomNodeMergeState: Optional<BomNodeBranchState>) ->

            // Masterdata Merges are bomnode related, non-masterdata merges are branch related
            val (bomNodeMerges, branchBasedMerges) = openMergesAvailable.partition { it == MergeSourceType.MASTERDATA }

            // Create or Update Branch based merges
            val brachMerge =
                branchMergeState
                    .map {
                        it.openMergesAvailable = branchBasedMerges
                        it
                    }.orElseGet {
                        BomNodeBranchState(
                            null,
                            branchId,
                            branchBasedMerges,
                        )
                    }

            // Create or Update BomNode based merges
            val bomNodeMerge =
                bomNodeMergeState
                    .map {
                        it.openMergesAvailable = bomNodeMerges
                        it
                    }.orElseGet {
                        BomNodeBranchState(bomNodeId, branchId, bomNodeMerges)
                    }

            // Save and return mergeResult
            bomNodeBranchStateRepository
                .saveAll(listOf(brachMerge, bomNodeMerge))
                .collectList()
                .map {
                    it.flatMap { it.openMergesAvailable ?: listOf() }
                }
        }

    private fun getMergeStates(
        branchId: BranchId,
        bomNodeId: BomNodeId,
    ) = Mono.zip(
        bomNodeBranchStateRepository.findByBranchIdAndBomNodeIdIsNull(branchId).optional(),
        bomNodeBranchStateRepository.findByBranchIdAndBomNodeId(branchId, bomNodeId).optional(),
    )

    fun removeOpenMerges(
        bomNodeId: BomNodeId,
        branchId: BranchId,
        mergeTypes: List<MergeSourceType>,
    ): Mono<List<MergeSourceType>> =
        bomNodeBranchStateRepository
            .findByBranchId(branchId)
            .map { bomNodeBranchState ->
                if (bomNodeBranchState.bomNodeId == bomNodeId) {
                    bomNodeBranchState.openMergesAvailable =
                        bomNodeBranchState.openMergesAvailable?.filterNot { mergeTypes.contains(it) } ?: emptyList()
                } else {
                    bomNodeBranchState.openMergesAvailable =
                        bomNodeBranchState.openMergesAvailable?.filterNot {
                            it == MergeSourceType.MASTERDATA ||
                                mergeTypes.contains(it)
                        } ?: emptyList()
                }
                bomNodeBranchState
            }.collectList()
            .flatMap { states ->
                bomNodeBranchStateRepository.saveAll(states).map { it.openMergesAvailable!! }.collectList()
            }.map { it.flatten().distinct() }
            .switchIfEmpty(Mono.just(emptyList()))

    fun updateOpenMerges(
        bomNodeId: BomNodeId,
        sourceBranchId: BranchId,
        targetBranchId: BranchId,
    ): Mono<List<MergeSourceType>> =
        getOpenMergesAvailable(sourceBranchId, bomNodeId).flatMap { sourceMerges ->
            setOpenMergesAvailable(bomNodeId, targetBranchId, sourceMerges)
        }
}
