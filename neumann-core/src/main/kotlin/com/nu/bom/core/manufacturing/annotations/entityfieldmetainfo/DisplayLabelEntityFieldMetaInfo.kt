package com.nu.bom.core.manufacturing.annotations.entityfieldmetainfo

import com.nu.bom.core.manufacturing.annotations.DisplayLabel
import com.nu.bom.core.model.manufacturing.schema.persistence.PersistedDisplayLabelEntityFieldMetaInfo
import com.nu.bom.core.model.manufacturing.schema.persistence.PersistedMetaInfo

data class DisplayLabelEntityFieldMetaInfo(val label: String) : NonUnitEntityFieldMetaInfo {
    override fun toMap() = mapOf(DisplayLabel.META_INFO to label)

    override fun toPersistedEntityFieldMetaInfo(): PersistedMetaInfo? = PersistedDisplayLabelEntityFieldMetaInfo(label)
}
