package com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration

import com.google.common.annotations.VisibleForTesting
import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.StandardCalculationValue
import com.nu.bom.core.manufacturing.commercialcalculation.enums.SubCalculator
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.DisplayableNames
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.DifferentiatedCalculationType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalActivityInputOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalConfigurationOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalCostInputOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalDiscountOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalInterestOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalInvestInputOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalManufacturingScrapOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalMaterialScrapOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalSumProdOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalWeightedCalculationElement
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.staticElements.StaticActivityElement
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.staticElements.StaticCostElement
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.staticElements.StaticInvestElement
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.overheadLookup.DefaultOverheadType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.CostElementConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.CostOperationConfigurationV6
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetFactoriesOverheadHelper.getDifferentiatedOverheadElements
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetFactoriesOverheadHelper.getDifferentiatedOverheadItems
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetFactoriesOverheadHelper.getSimpleOverheadItems
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetFactoriesTransferHelper.defineCostTransferOperations
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetProcurementTypeConfigurationFactory.getDefaultProcurementTypeConfiguration
import java.math.BigDecimal

@Suppress(
    "LongMethod",
    "TooManyFunctions",
    "LargeClass",
    "MaxLineLength",
)
object TsetCostConfigurationFactory {
    fun getDefaultCostElementTsetConfiguration() =
        CostElementConfiguration(
            TsetCostCalculationElementType.entries.associateBy({ it.fieldName }, { DisplayableNames(it.short, it.long) }) +
                getDifferentiatedOverheadElements(ValueType.COST) +
                TsetActivityCalculationElementType.entries.associateBy({ it.fieldName }, { DisplayableNames(it.short, it.long) }),
        )

    fun getDefaultCostOperationTsetConfiguration() =
        CostOperationConfigurationV6(
            getDefaultCostOperations(),
            getDefaultProcurementTypeConfiguration(),
        )

    @VisibleForTesting
    fun getDefaultCostOperations() =
        configureCostsMaterialCalculation() +
            configureCostsManufacturingCalculation() +
            listOf(
                ExternalSumProdOperation(
                    TsetCostCalculationElementType.PRODUCTION_COSTS.fieldName,
                    AggregationLevel.MANUFACTURING_STEP,
                    listOf(
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.MATERIAL_COSTS.fieldName),
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.MANUFACTURING_COSTS_3.fieldName),
                    ),
                    StandardCalculationValue.TOTAL_PRODUCTION_VALUE,
                ),
            ) +
            configureCostsInvestCalculation() +
            configureCostsOverheadCalculation() +
            listOf(
                ExternalSumProdOperation(
                    TsetCostCalculationElementType.SALES_PRICE.fieldName,
                    AggregationLevel.SOLD_MATERIAL,
                    listOf(
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.PRODUCTION_COSTS.fieldName),
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.INDIRECT_COSTS_AFTER_PRODUCTION.fieldName),
                        ExternalWeightedCalculationElement(
                            TsetCostCalculationElementType.ALLOCATED_AND_INTEREST_COSTS_FOR_INVEST.fieldName,
                        ),
                    ),
                    StandardCalculationValue.TOTAL_SALE_VALUE,
                ),
            ) +
            configureActivitiesCalculation()

    private fun configureCostsMaterialCalculation(): List<ExternalConfigurationOperation> =
        listOf(
            // defines the input of material leaf nodes
            ExternalCostInputOperation(
                sourceElement = StaticCostElement.PURCHASE_PRICE,
                destinationElementKey = TsetCostCalculationElementType.SALES_PRICE.fieldName,
                origin = AggregationLevel.SOLD_MATERIAL,
            ),
        ) + defineCostTransferOperations() +
            listOf(
                ExternalSumProdOperation(
                    TsetCostCalculationElementType.DIRECT_MATERIAL_COSTS.fieldName,
                    AggregationLevel.SUB_MATERIAL,
                    listOf(
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_RAW_MATERIAL_COSTS.fieldName),
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_PURCHASE_PARTS_COSTS.fieldName),
                    ),
                    StandardCalculationValue.TOTAL_DIRECT_MATERIAL_VALUE,
                ),
            ) +
            configureCostsMaterialOverheadCalculation() +
            configureCostsMaterialInterestCalculation() +
            configureCostsMaterialScrapCalculation() +
            listOf(
                ExternalSumProdOperation(
                    TsetCostCalculationElementType.MATERIAL_COSTS.fieldName,
                    AggregationLevel.MANUFACTURED_MATERIAL,
                    listOf(
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_MATERIAL_COSTS.fieldName),
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.MATERIAL_SCRAP_COSTS.fieldName),
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.MATERIAL_OVERHEAD_COSTS.fieldName),
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.MATERIAL_INTEREST_COSTS.fieldName),
                    ),
                    StandardCalculationValue.TOTAL_MATERIAL_VALUE,
                ),
            )

    private fun configureCostsMaterialOverheadCalculation(): List<ExternalConfigurationOperation> =
        listOf(
            getSimpleOverheadItems(
                DefaultOverheadType.RawMaterialOverheadCosts,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_RAW_MATERIAL_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_PROVIDED_RAW_MATERIAL_COSTS.fieldName),
                ),
                AggregationLevel.MATERIAL_USAGE,
            ),
            getSimpleOverheadItems(
                DefaultOverheadType.PurchasePartsOverheadCosts,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_PURCHASE_PARTS_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_PROVIDED_PARTS_COSTS.fieldName),
                ),
                AggregationLevel.MATERIAL_USAGE,
            ),
            ExternalSumProdOperation(
                TsetCostCalculationElementType.MATERIAL_OVERHEAD_COSTS.fieldName,
                AggregationLevel.MATERIAL_USAGE,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.RAW_MATERIAL_OVERHEAD_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.PURCHASE_PARTS_OVERHEAD_COSTS.fieldName),
                ),
            ),
        )

    private fun configureCostsMaterialInterestCalculation(): List<ExternalConfigurationOperation> =
        listOf(
            ExternalInterestOperation(
                TsetCostCalculationElementType.RAW_MATERIAL_INTEREST_COSTS.fieldName,
                AggregationLevel.MANUFACTURED_MATERIAL,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_RAW_MATERIAL_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.RAW_MATERIAL_OVERHEAD_COSTS.fieldName),
                ),
                headerKey = DefaultOverheadType.RawMaterialInterestCosts.buildMasterdataHeaderKey(),
            ),
            ExternalInterestOperation(
                TsetCostCalculationElementType.PURCHASE_PARTS_INTEREST_COSTS.fieldName,
                AggregationLevel.MANUFACTURED_MATERIAL,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_PURCHASE_PARTS_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.PURCHASE_PARTS_OVERHEAD_COSTS.fieldName),
                ),
                headerKey = DefaultOverheadType.PurchasePartsInterestCosts.buildMasterdataHeaderKey(),
            ),
            ExternalSumProdOperation(
                TsetCostCalculationElementType.MATERIAL_INTEREST_COSTS.fieldName,
                AggregationLevel.MANUFACTURED_MATERIAL,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.RAW_MATERIAL_INTEREST_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.PURCHASE_PARTS_INTEREST_COSTS.fieldName),
                ),
            ),
        )

    private fun configureCostsMaterialScrapCalculation(): List<ExternalConfigurationOperation> =
        listOf(
            ExternalMaterialScrapOperation(
                TsetCostCalculationElementType.RAW_MATERIAL_SCRAP_COSTS.fieldName,
                AggregationLevel.MATERIAL_USAGE,
                baseAddends =
                    listOf(
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_RAW_MATERIAL_COSTS.fieldName),
                    ),
                previousBaseAddends =
                    listOf(
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_RAW_MATERIAL_COSTS.fieldName),
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.RAW_MATERIAL_SCRAP_COSTS.fieldName),
                    ),
            ),
            ExternalMaterialScrapOperation(
                TsetCostCalculationElementType.PROVIDED_PARTS_SCRAP_COSTS.fieldName,
                AggregationLevel.MATERIAL_USAGE,
                baseAddends =
                    listOf(
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_PROVIDED_PARTS_COSTS.fieldName),
                    ),
                previousBaseAddends =
                    listOf(
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_PROVIDED_PARTS_COSTS.fieldName),
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.PROVIDED_PARTS_SCRAP_COSTS.fieldName),
                    ),
            ),
            ExternalMaterialScrapOperation(
                TsetCostCalculationElementType.PROVIDED_RAW_MATERIAL_SCRAP_COSTS.fieldName,
                AggregationLevel.MATERIAL_USAGE,
                baseAddends =
                    listOf(
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_PROVIDED_RAW_MATERIAL_COSTS.fieldName),
                    ),
                previousBaseAddends =
                    listOf(
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_PROVIDED_RAW_MATERIAL_COSTS.fieldName),
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.PROVIDED_RAW_MATERIAL_SCRAP_COSTS.fieldName),
                    ),
            ),
            ExternalMaterialScrapOperation(
                TsetCostCalculationElementType.PURCHASE_PARTS_SCRAP_COSTS.fieldName,
                AggregationLevel.MATERIAL_USAGE,
                baseAddends =
                    listOf(
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_PURCHASE_PARTS_COSTS.fieldName),
                    ),
                previousBaseAddends =
                    listOf(
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_PURCHASE_PARTS_COSTS.fieldName),
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.PURCHASE_PARTS_SCRAP_COSTS.fieldName),
                    ),
            ),
            ExternalSumProdOperation(
                TsetCostCalculationElementType.MATERIAL_SCRAP_COSTS.fieldName,
                AggregationLevel.MATERIAL_USAGE,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.RAW_MATERIAL_SCRAP_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.PURCHASE_PARTS_SCRAP_COSTS.fieldName),
                ),
            ),
        )

    private fun configureCostsManufacturingCalculation(): List<ExternalConfigurationOperation> =
        listOf(
            ExternalCostInputOperation(
                StaticCostElement.LABOR_COSTS,
                TsetCostCalculationElementType.LABOR_COSTS.fieldName,
                AggregationLevel.LABOR,
            ),
            ExternalCostInputOperation(
                StaticCostElement.MACHINE_DEPRECIATION_COSTS,
                TsetCostCalculationElementType.MACHINE_DEPRECIATION_COSTS.fieldName,
                AggregationLevel.MACHINE,
            ),
            ExternalCostInputOperation(
                StaticCostElement.MACHINE_INTEREST_COSTS,
                TsetCostCalculationElementType.MACHINE_INTEREST_COSTS.fieldName,
                AggregationLevel.MACHINE,
            ),
            ExternalCostInputOperation(
                StaticCostElement.MACHINE_AREA_COSTS,
                TsetCostCalculationElementType.MACHINE_AREA_COSTS.fieldName,
                AggregationLevel.MACHINE,
            ),
            ExternalCostInputOperation(
                StaticCostElement.MACHINE_ENERGY_COSTS,
                TsetCostCalculationElementType.MACHINE_ENERGY_COSTS.fieldName,
                AggregationLevel.MACHINE,
            ),
            ExternalCostInputOperation(
                StaticCostElement.MACHINE_MAINTENANCE_COSTS,
                TsetCostCalculationElementType.MACHINE_MAINTENANCE_COSTS.fieldName,
                AggregationLevel.MACHINE,
            ),
            ExternalCostInputOperation(
                StaticCostElement.MACHINE_OPERATION_SUPPLY_COSTS,
                TsetCostCalculationElementType.MACHINE_OPERATION_SUPPLY_COSTS.fieldName,
                AggregationLevel.MACHINE,
            ),
            ExternalCostInputOperation(
                StaticCostElement.ROUGH_MACHINE_COSTS,
                TsetCostCalculationElementType.ROUGH_MACHINE_COSTS.fieldName,
                AggregationLevel.MACHINE,
            ),
            ExternalCostInputOperation(
                StaticCostElement.TOOL_ALLOCATION_COSTS,
                TsetCostCalculationElementType.TOOL_ALLOCATION_COSTS.fieldName,
                AggregationLevel.TOOL,
            ),
            ExternalCostInputOperation(
                StaticCostElement.TOOL_INTEREST_COSTS,
                TsetCostCalculationElementType.TOOL_INTEREST_COSTS.fieldName,
                AggregationLevel.TOOL,
            ),
            ExternalCostInputOperation(
                StaticCostElement.TOOL_MAINTENANCE_COSTS,
                TsetCostCalculationElementType.TOOL_MAINTENANCE_COSTS.fieldName,
                AggregationLevel.TOOL,
            ),
            ExternalCostInputOperation(
                StaticCostElement.ROUGH_PROCESS_COSTS,
                TsetCostCalculationElementType.ROUGH_PROCESS_COSTS.fieldName,
                AggregationLevel.ROUGH_PROCESS,
            ),
            ExternalSumProdOperation(
                TsetCostCalculationElementType.MACHINE_FIX_COSTS.fieldName,
                AggregationLevel.MACHINE,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.MACHINE_DEPRECIATION_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.MACHINE_INTEREST_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.MACHINE_AREA_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.ROUGH_MACHINE_COSTS.fieldName),
                ),
            ),
            ExternalSumProdOperation(
                TsetCostCalculationElementType.MACHINE_VARIABLE_COSTS.fieldName,
                AggregationLevel.MACHINE,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.MACHINE_ENERGY_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.MACHINE_MAINTENANCE_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.MACHINE_OPERATION_SUPPLY_COSTS.fieldName),
                ),
            ),
            ExternalSumProdOperation(
                TsetCostCalculationElementType.MACHINE_COSTS.fieldName,
                AggregationLevel.MACHINE,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.MACHINE_FIX_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.MACHINE_VARIABLE_COSTS.fieldName),
                ),
            ),
            ExternalSumProdOperation(
                TsetCostCalculationElementType.TOOL_ALLOCATION_AND_INTEREST_COSTS.fieldName,
                AggregationLevel.TOOL,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.TOOL_ALLOCATION_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.TOOL_INTEREST_COSTS.fieldName),
                ),
            ),
            ExternalSumProdOperation(
                TsetCostCalculationElementType.TOOL_COSTS.fieldName,
                AggregationLevel.TOOL,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.TOOL_ALLOCATION_AND_INTEREST_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.TOOL_MAINTENANCE_COSTS.fieldName),
                ),
            ),
            ExternalSumProdOperation(
                TsetCostCalculationElementType.DIRECT_MANUFACTURING_COSTS.fieldName,
                AggregationLevel.MANUFACTURING_STEP,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.LABOR_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.MACHINE_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.TOOL_MAINTENANCE_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.ROUGH_PROCESS_COSTS.fieldName),
                ),
                StandardCalculationValue.TOTAL_DIRECT_MANUFACTURING_VALUE,
            ),
            getSimpleOverheadItems(
                DefaultOverheadType.ManufacturingOverheadCosts,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_MANUFACTURING_COSTS.fieldName),
                ),
                AggregationLevel.MANUFACTURING_STEP,
            ),
            ExternalInterestOperation(
                TsetCostCalculationElementType.MANUFACTURING_INTEREST_COSTS.fieldName,
                AggregationLevel.MANUFACTURING_STEP,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_MANUFACTURING_COSTS.fieldName),
                ),
                headerKey = null,
            ),
            ExternalManufacturingScrapOperation(
                TsetCostCalculationElementType.MANUFACTURING_SCRAP_COSTS.fieldName,
                AggregationLevel.MANUFACTURING_STEP,
                baseAddends =
                    listOf(
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_MANUFACTURING_COSTS.fieldName),
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.MANUFACTURING_OVERHEAD_COSTS.fieldName),
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.MANUFACTURING_INTEREST_COSTS.fieldName),
                    ),
                previousBaseAddends =
                    listOf(
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.MANUFACTURING_COSTS_3.fieldName),
                    ),
            ),
            ExternalSumProdOperation(
                TsetCostCalculationElementType.MANUFACTURING_COSTS_AFTER_DIRECT_MANUFACTURING_COSTS.fieldName,
                AggregationLevel.MANUFACTURING_STEP,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.MANUFACTURING_SCRAP_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.MANUFACTURING_OVERHEAD_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.TOOL_ALLOCATION_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.TOOL_INTEREST_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.MANUFACTURING_INTEREST_COSTS.fieldName),
                ),
            ),
            ExternalSumProdOperation(
                TsetCostCalculationElementType.MANUFACTURING_COSTS_3.fieldName,
                AggregationLevel.MANUFACTURING_STEP,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_MANUFACTURING_COSTS.fieldName),
                    ExternalWeightedCalculationElement(
                        TsetCostCalculationElementType.MANUFACTURING_COSTS_AFTER_DIRECT_MANUFACTURING_COSTS.fieldName,
                    ),
                ),
                StandardCalculationValue.TOTAL_MANUFACTURING_VALUE,
            ),
        )

    private fun configureCostsInvestCalculation(): List<ExternalConfigurationOperation> =
        listOf(
            ExternalCostInputOperation(
                StaticCostElement.INTEREST_COST_PER_QUANTITY_FOR_INVEST,
                TsetCostCalculationElementType.INTEREST_COSTS_FOR_INVEST.fieldName,
                AggregationLevel.INVEST,
            ),
            ExternalCostInputOperation(
                StaticCostElement.ALLOCATED_COST_PER_QUANTITY_FOR_INVEST,
                TsetCostCalculationElementType.ALLOCATED_COSTS_FOR_INVEST.fieldName,
                AggregationLevel.INVEST,
            ),
            ExternalSumProdOperation(
                TsetCostCalculationElementType.ALLOCATED_AND_INTEREST_COSTS_FOR_INVEST.fieldName,
                AggregationLevel.INVEST,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.INTEREST_COSTS_FOR_INVEST.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.ALLOCATED_COSTS_FOR_INVEST.fieldName),
                ),
                StandardCalculationValue.TOTAL_INVEST_VALUE,
            ),
            ExternalInvestInputOperation(
                StaticInvestElement.DEVELOPMENT,
                TsetCostCalculationElementType.DEVELOPMENT_COSTS_FOR_INVEST.fieldName,
                AggregationLevel.INVEST,
            ),
            ExternalInvestInputOperation(
                StaticInvestElement.RAMP_UP,
                TsetCostCalculationElementType.RAMP_UP_COSTS_FOR_INVEST.fieldName,
                AggregationLevel.INVEST,
            ),
            ExternalInvestInputOperation(
                StaticInvestElement.PACKAGING_AND_CARRIER,
                TsetCostCalculationElementType.PACKAGING_AND_CARRIER_COSTS_FOR_INVEST.fieldName,
                AggregationLevel.INVEST,
            ),
            ExternalSumProdOperation(
                TsetCostCalculationElementType.TOTAL_COSTS_FOR_INVEST.fieldName,
                AggregationLevel.INVEST,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.DEVELOPMENT_COSTS_FOR_INVEST.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.RAMP_UP_COSTS_FOR_INVEST.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.PACKAGING_AND_CARRIER_COSTS_FOR_INVEST.fieldName),
                ),
            ),
        )

    private fun configureCostsOverheadCalculation(): List<ExternalConfigurationOperation> =
        configureCostsOverheadBaseCalculation() +
            configureCostsSpecialDirectCostsCalculation() +
            configureCostsAfterProductionOverheadsCalculation() +
            listOf(
                ExternalSumProdOperation(
                    TsetCostCalculationElementType.BEFORE_PROFIT_COSTS.fieldName,
                    AggregationLevel.SOLD_MATERIAL,
                    listOf(
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.PRODUCTION_COSTS.fieldName),
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.SPECIAL_DIRECT_COSTS.fieldName),
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.OVERHEADS_AFTER_PC.fieldName),
                    ),
                ),
            ) +
            configureCostsProfitCalculation() +
            configureCostsTermsOfPaymentCalculation() +
            configureCostsIncotermsCalculation() +
            listOf(
                ExternalSumProdOperation(
                    TsetCostCalculationElementType.INDIRECT_COSTS_AFTER_PRODUCTION.fieldName,
                    AggregationLevel.SOLD_MATERIAL,
                    listOf(
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.SPECIAL_DIRECT_COSTS.fieldName),
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.OVERHEADS_AFTER_PC.fieldName),
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.PROFIT.fieldName),
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.TERMS_OF_PAYMENT.fieldName),
                        ExternalWeightedCalculationElement(TsetCostCalculationElementType.INCO_TERMS.fieldName),
                    ),
                    standardCalculationValue = StandardCalculationValue.TOTAL_OVERHEAD_VALUE,
                ),
            )

    private fun configureCostsOverheadBaseCalculation(): List<ExternalConfigurationOperation> =
        listOf(
            ExternalSumProdOperation(
                TsetCostCalculationElementType.OVERHEAD_BASE_RM.fieldName,
                AggregationLevel.SOLD_MATERIAL,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_RAW_MATERIAL_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.RAW_MATERIAL_SCRAP_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.RAW_MATERIAL_OVERHEAD_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.RAW_MATERIAL_INTEREST_COSTS.fieldName),
                ),
            ),
            ExternalSumProdOperation(
                TsetCostCalculationElementType.OVERHEAD_BASE_PP.fieldName,
                AggregationLevel.SOLD_MATERIAL,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_PURCHASE_PARTS_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.PURCHASE_PARTS_SCRAP_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.PURCHASE_PARTS_OVERHEAD_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.PURCHASE_PARTS_INTEREST_COSTS.fieldName),
                ),
            ),
            ExternalSumProdOperation(
                TsetCostCalculationElementType.OVERHEAD_BASE_MFG.fieldName,
                AggregationLevel.SOLD_MATERIAL,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.MANUFACTURING_COSTS_3.fieldName),
                ),
            ),
        )

    private fun configureCostsSpecialDirectCostsCalculation(): List<ExternalConfigurationOperation> =
        listOf(
            getDifferentiatedOverheadItems(
                DefaultOverheadType.DevelopmentCosts,
                mapOf(
                    DifferentiatedCalculationType.RAW_MATERIAL to
                        listOf(
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.OVERHEAD_BASE_RM.fieldName,
                            ),
                        ),
                    DifferentiatedCalculationType.PURCHASE_PART to
                        listOf(
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.OVERHEAD_BASE_PP.fieldName,
                            ),
                        ),
                    DifferentiatedCalculationType.MANUFACTURING to
                        listOf(
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.OVERHEAD_BASE_MFG.fieldName,
                            ),
                        ),
                ),
                AggregationLevel.SOLD_MATERIAL,
            ),
            getDifferentiatedOverheadItems(
                DefaultOverheadType.RampUpCosts,
                mapOf(
                    DifferentiatedCalculationType.RAW_MATERIAL to
                        listOf(
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.OVERHEAD_BASE_RM.fieldName,
                            ),
                        ),
                    DifferentiatedCalculationType.PURCHASE_PART to
                        listOf(
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.OVERHEAD_BASE_PP.fieldName,
                            ),
                        ),
                    DifferentiatedCalculationType.MANUFACTURING to
                        listOf(
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.OVERHEAD_BASE_MFG.fieldName,
                            ),
                        ),
                ),
                AggregationLevel.SOLD_MATERIAL,
            ),
            getDifferentiatedOverheadItems(
                DefaultOverheadType.PackagingAndCarrierCosts,
                mapOf(
                    DifferentiatedCalculationType.RAW_MATERIAL to
                        listOf(
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.OVERHEAD_BASE_RM.fieldName,
                            ),
                        ),
                    DifferentiatedCalculationType.PURCHASE_PART to
                        listOf(
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.OVERHEAD_BASE_PP.fieldName,
                            ),
                        ),
                    DifferentiatedCalculationType.MANUFACTURING to
                        listOf(
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.OVERHEAD_BASE_MFG.fieldName,
                            ),
                        ),
                ),
                AggregationLevel.SOLD_MATERIAL,
            ),
            ExternalSumProdOperation(
                TsetCostCalculationElementType.SPECIAL_DIRECT_COSTS.fieldName,
                AggregationLevel.SOLD_MATERIAL,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.DEVELOPMENT_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.RAMP_UP_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.PACKAGING_AND_CARRIER_COSTS.fieldName),
                ),
            ),
        )

    private fun configureCostsAfterProductionOverheadsCalculation(): List<ExternalConfigurationOperation> =
        listOf(
            getDifferentiatedOverheadItems(
                DefaultOverheadType.SalesAndGeneralAdministrationCosts,
                mapOf(
                    DifferentiatedCalculationType.RAW_MATERIAL to
                        listOf(
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.OVERHEAD_BASE_RM.fieldName,
                            ),
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.DIRECT_PROVIDED_RAW_MATERIAL_COSTS.fieldName,
                            ),
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.PROVIDED_RAW_MATERIAL_SCRAP_COSTS.fieldName,
                            ),
                        ),
                    DifferentiatedCalculationType.PURCHASE_PART to
                        listOf(
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.OVERHEAD_BASE_PP.fieldName,
                            ),
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.DIRECT_PROVIDED_PARTS_COSTS.fieldName,
                            ),
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.PROVIDED_PARTS_SCRAP_COSTS.fieldName,
                            ),
                        ),
                    DifferentiatedCalculationType.MANUFACTURING to
                        listOf(
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.OVERHEAD_BASE_MFG.fieldName,
                            ),
                        ),
                ),
                AggregationLevel.SOLD_MATERIAL,
            ),
            getDifferentiatedOverheadItems(
                DefaultOverheadType.ResearchAndDevelopmentCosts,
                mapOf(
                    DifferentiatedCalculationType.RAW_MATERIAL to
                        listOf(
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.OVERHEAD_BASE_RM.fieldName,
                            ),
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.DIRECT_PROVIDED_RAW_MATERIAL_COSTS.fieldName,
                            ),
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.PROVIDED_RAW_MATERIAL_SCRAP_COSTS.fieldName,
                            ),
                        ),
                    DifferentiatedCalculationType.PURCHASE_PART to
                        listOf(
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.OVERHEAD_BASE_PP.fieldName,
                            ),
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.DIRECT_PROVIDED_PARTS_COSTS.fieldName,
                            ),
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.PROVIDED_PARTS_SCRAP_COSTS.fieldName,
                            ),
                        ),
                    DifferentiatedCalculationType.MANUFACTURING to
                        listOf(
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.OVERHEAD_BASE_MFG.fieldName,
                            ),
                        ),
                ),
                AggregationLevel.SOLD_MATERIAL,
            ),
            getDifferentiatedOverheadItems(
                DefaultOverheadType.BusinessRiskCosts,
                mapOf(
                    DifferentiatedCalculationType.RAW_MATERIAL to
                        listOf(
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.OVERHEAD_BASE_RM.fieldName,
                            ),
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.DIRECT_PROVIDED_RAW_MATERIAL_COSTS.fieldName,
                            ),
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.PROVIDED_RAW_MATERIAL_SCRAP_COSTS.fieldName,
                            ),
                        ),
                    DifferentiatedCalculationType.PURCHASE_PART to
                        listOf(
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.OVERHEAD_BASE_PP.fieldName,
                            ),
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.DIRECT_PROVIDED_PARTS_COSTS.fieldName,
                            ),
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.PROVIDED_PARTS_SCRAP_COSTS.fieldName,
                            ),
                        ),
                    DifferentiatedCalculationType.MANUFACTURING to
                        listOf(
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.OVERHEAD_BASE_MFG.fieldName,
                            ),
                        ),
                ),
                AggregationLevel.SOLD_MATERIAL,
            ),
            ExternalInterestOperation(
                TsetCostCalculationElementType.INTEREST_ON_FINISHED_PRODUCT.fieldName,
                AggregationLevel.SOLD_MATERIAL,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.MANUFACTURING_COSTS_3.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_MATERIAL_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.MATERIAL_OVERHEAD_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.MATERIAL_SCRAP_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_PROVIDED_RAW_MATERIAL_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.PROVIDED_RAW_MATERIAL_SCRAP_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_PROVIDED_PARTS_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.PROVIDED_PARTS_SCRAP_COSTS.fieldName),
                ),
                headerKey = DefaultOverheadType.InterestOnFinishProductStock.buildMasterdataHeaderKey(),
            ),
            getDifferentiatedOverheadItems(
                DefaultOverheadType.OtherExpendituresAfterPC,
                mapOf(
                    DifferentiatedCalculationType.RAW_MATERIAL to
                        listOf(
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.OVERHEAD_BASE_RM.fieldName,
                            ),
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.DIRECT_PROVIDED_RAW_MATERIAL_COSTS.fieldName,
                            ),
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.PROVIDED_RAW_MATERIAL_SCRAP_COSTS.fieldName,
                            ),
                        ),
                    DifferentiatedCalculationType.PURCHASE_PART to
                        listOf(
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.OVERHEAD_BASE_PP.fieldName,
                            ),
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.DIRECT_PROVIDED_PARTS_COSTS.fieldName,
                            ),
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.PROVIDED_PARTS_SCRAP_COSTS.fieldName,
                            ),
                        ),
                    DifferentiatedCalculationType.MANUFACTURING to
                        listOf(
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.OVERHEAD_BASE_MFG.fieldName,
                            ),
                        ),
                ),
                AggregationLevel.SOLD_MATERIAL,
            ),
            getDifferentiatedOverheadItems(
                DefaultOverheadType.DirectOverheadsAfterPC,
                mapOf(
                    DifferentiatedCalculationType.RAW_MATERIAL to
                        listOf(
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.OVERHEAD_BASE_RM.fieldName,
                            ),
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.DIRECT_PROVIDED_RAW_MATERIAL_COSTS.fieldName,
                            ),
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.PROVIDED_RAW_MATERIAL_SCRAP_COSTS.fieldName,
                            ),
                        ),
                    DifferentiatedCalculationType.PURCHASE_PART to
                        listOf(
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.OVERHEAD_BASE_PP.fieldName,
                            ),
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.DIRECT_PROVIDED_PARTS_COSTS.fieldName,
                            ),
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.PROVIDED_PARTS_SCRAP_COSTS.fieldName,
                            ),
                        ),
                    DifferentiatedCalculationType.MANUFACTURING to
                        listOf(
                            ExternalWeightedCalculationElement(
                                TsetCostCalculationElementType.OVERHEAD_BASE_MFG.fieldName,
                            ),
                        ),
                ),
                AggregationLevel.SOLD_MATERIAL,
            ),
            ExternalSumProdOperation(
                TsetCostCalculationElementType.OVERHEADS_AFTER_PC.fieldName,
                AggregationLevel.SOLD_MATERIAL,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.SALES_AND_GENERAL_ADMINISTRATION_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.RESEARCH_AND_DEVELOPMENT_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.BUSINESS_RISK_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.INTEREST_ON_FINISHED_PRODUCT.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.OTHER_EXPENDITURES_AFTER_PC.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_OVERHEADS_AFTER_PC.fieldName),
                ),
            ),
        )

    private fun configureCostsProfitCalculation(): List<ExternalConfigurationOperation> =
        listOf(
            getDifferentiatedOverheadItems(
                DefaultOverheadType.Profit,
                mapOf(
                    DifferentiatedCalculationType.RAW_MATERIAL to
                        listOf(
                            ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_RAW_MATERIAL_COSTS.fieldName),
                        ),
                    DifferentiatedCalculationType.PURCHASE_PART to
                        listOf(
                            ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_PURCHASE_PARTS_COSTS.fieldName),
                        ),
                    DifferentiatedCalculationType.MANUFACTURING to
                        listOf(
                            ExternalWeightedCalculationElement(TsetCostCalculationElementType.DIRECT_MANUFACTURING_COSTS.fieldName),
                            ExternalWeightedCalculationElement(TsetCostCalculationElementType.MANUFACTURING_OVERHEAD_COSTS.fieldName),
                        ),
                ),
                AggregationLevel.SOLD_MATERIAL,
            ),
        )

    private fun configureCostsTermsOfPaymentCalculation(): List<ExternalConfigurationOperation> =
        listOf(
            ExternalInterestOperation(
                TsetCostCalculationElementType.INTEREST_FOR_TERMS_OF_PAYMENT.fieldName,
                AggregationLevel.SOLD_MATERIAL,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.BEFORE_PROFIT_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.PROFIT.fieldName),
                ),
                headerKey = DefaultOverheadType.InterestForTermsOfPayment.buildMasterdataHeaderKey(),
            ),
            ExternalDiscountOperation(
                TsetCostCalculationElementType.DISCOUNT.fieldName,
                AggregationLevel.SOLD_MATERIAL,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.BEFORE_PROFIT_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.PROFIT.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.INTEREST_FOR_TERMS_OF_PAYMENT.fieldName),
                ),
                headerKey = DefaultOverheadType.Discount.buildMasterdataHeaderKey(),
            ),
            ExternalSumProdOperation(
                TsetCostCalculationElementType.TERMS_OF_PAYMENT.fieldName,
                AggregationLevel.SOLD_MATERIAL,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.INTEREST_FOR_TERMS_OF_PAYMENT.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.DISCOUNT.fieldName),
                ),
            ),
        )

    private fun configureCostsIncotermsCalculation(): List<ExternalConfigurationOperation> =
        listOf(
            getSimpleOverheadItems(
                DefaultOverheadType.TransportCosts,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.BEFORE_PROFIT_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.PROFIT.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.TERMS_OF_PAYMENT.fieldName),
                ),
                AggregationLevel.SOLD_MATERIAL,
                SubCalculator.TRANSPORT_CALCULATOR,
            ),
            getSimpleOverheadItems(
                DefaultOverheadType.CustomsDuty,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.BEFORE_PROFIT_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.PROFIT.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.TERMS_OF_PAYMENT.fieldName),
                ),
                AggregationLevel.SOLD_MATERIAL,
            ),
            ExternalSumProdOperation(
                TsetCostCalculationElementType.INCO_TERMS.fieldName,
                AggregationLevel.SOLD_MATERIAL,
                listOf(
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.TRANSPORT_COSTS.fieldName),
                    ExternalWeightedCalculationElement(TsetCostCalculationElementType.CUSTOM_DUTY.fieldName),
                ),
            ),
        )

    private fun configureActivitiesCalculation(): List<ExternalConfigurationOperation> =
        listOf(
            ExternalActivityInputOperation(
                StaticActivityElement.OCCUPANCY_MACHINE_ACTIVITY,
                TsetActivityCalculationElementType.OCCUPANCY_MACHINE_ACTIVITY.fieldName,
                AggregationLevel.MACHINE,
            ),
            ExternalActivityInputOperation(
                StaticActivityElement.NON_OCCUPANCY_MACHINE_ACTIVITY,
                TsetActivityCalculationElementType.NON_OCCUPANCY_MACHINE_ACTIVITY.fieldName,
                AggregationLevel.MACHINE,
            ),
            ExternalActivityInputOperation(
                StaticActivityElement.PRODUCTION_LABOR_ACTIVITY,
                TsetActivityCalculationElementType.PRODUCTION_LABOR_ACTIVITY.fieldName,
                AggregationLevel.LABOR,
            ),
            ExternalActivityInputOperation(
                StaticActivityElement.SETUP_OPERATOR_ACTIVITY,
                TsetActivityCalculationElementType.SETUP_OPERATOR_ACTIVITY.fieldName,
                AggregationLevel.LABOR,
            ),
            ExternalActivityInputOperation(
                StaticActivityElement.SETUP_SETUP_WORKER_ACTIVITY,
                TsetActivityCalculationElementType.SETUP_SETUP_WORKER_ACTIVITY.fieldName,
                AggregationLevel.LABOR,
            ),
            ExternalActivityInputOperation(
                StaticActivityElement.SETUP_MACHINE_ACTIVITY,
                TsetActivityCalculationElementType.SETUP_MACHINE_ACTIVITY.fieldName,
                AggregationLevel.MACHINE,
                BigDecimal("0.05"),
            ),
            ExternalActivityInputOperation(
                StaticActivityElement.ROUGH_PROCESS_ACTIVITY,
                TsetActivityCalculationElementType.ROUGH_PROCESS_ACTIVITY.fieldName,
                AggregationLevel.ROUGH_PROCESS,
            ),
            ExternalInvestInputOperation(
                StaticInvestElement.TOOL,
                TsetActivityCalculationElementType.TOOL_COSTS_FOR_INVEST.fieldName,
                AggregationLevel.TOOL,
            ),
            ExternalActivityInputOperation(
                StaticActivityElement.TOOL_MAINTENANCE_ACTIVITY,
                TsetActivityCalculationElementType.TOOL_MAINTENANCE_ACTIVITY.fieldName,
                AggregationLevel.TOOL,
            ),
            ExternalSumProdOperation(
                TsetActivityCalculationElementType.PRODUCTION_MACHINE_ACTIVITY.fieldName,
                AggregationLevel.MACHINE,
                listOf(
                    ExternalWeightedCalculationElement(TsetActivityCalculationElementType.OCCUPANCY_MACHINE_ACTIVITY.fieldName),
                    ExternalWeightedCalculationElement(TsetActivityCalculationElementType.NON_OCCUPANCY_MACHINE_ACTIVITY.fieldName),
                ),
            ),
            ExternalSumProdOperation(
                TsetActivityCalculationElementType.SETUP_LABOR_ACTIVITY.fieldName,
                AggregationLevel.MANUFACTURING_STEP,
                listOf(
                    ExternalWeightedCalculationElement(TsetActivityCalculationElementType.SETUP_OPERATOR_ACTIVITY.fieldName),
                    ExternalWeightedCalculationElement(TsetActivityCalculationElementType.SETUP_SETUP_WORKER_ACTIVITY.fieldName),
                ),
            ),
            ExternalSumProdOperation(
                TsetActivityCalculationElementType.DIRECT_MANUFACTURING_ACTIVITY.fieldName,
                AggregationLevel.MANUFACTURING_STEP,
                listOf(
                    ExternalWeightedCalculationElement(TsetActivityCalculationElementType.PRODUCTION_LABOR_ACTIVITY.fieldName),
                    ExternalWeightedCalculationElement(TsetActivityCalculationElementType.PRODUCTION_MACHINE_ACTIVITY.fieldName),
                    ExternalWeightedCalculationElement(TsetActivityCalculationElementType.SETUP_MACHINE_ACTIVITY.fieldName),
                    ExternalWeightedCalculationElement(TsetActivityCalculationElementType.SETUP_LABOR_ACTIVITY.fieldName),
                    ExternalWeightedCalculationElement(TsetActivityCalculationElementType.TOOL_MAINTENANCE_ACTIVITY.fieldName),
                    ExternalWeightedCalculationElement(TsetActivityCalculationElementType.ROUGH_PROCESS_ACTIVITY.fieldName),
                ),
                StandardCalculationValue.TOTAL_MANUFACTURING_ACTIVITY,
            ),
        )
}
