package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.utils.toUpperSnakeCase

class UndercutType(res: Selection) : SelectEnumFieldResult<UndercutType.Selection, UndercutType>(res) {

    enum class Selection(val value: String) {
        E("E"),
        F("F"),
        G("G"),
        H("H")
    }

    companion object {

        fun valueOf(name: String): UndercutType {

            return Selection.values().find {
                // check if any of its inner values
                it.value == name
            }?.let {
                UndercutType(it)
            } // else try to cast from name
                ?: UndercutType(Selection.valueOf(name.toUpperSnakeCase()))
        }
    }
}
