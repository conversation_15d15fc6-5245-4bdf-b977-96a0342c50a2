package com.nu.bom.core.service

import com.nu.bom.core.api.dtos.EditDto
import com.nu.bom.core.api.dtos.ExternalSourceDataDto
import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.api.dtos.MasterDataCompositeKey
import com.nu.bom.core.api.dtos.MasterDataForEditDto
import com.nu.bom.core.api.dtos.MasterDataId
import com.nu.bom.core.api.dtos.PaginatedResponse
import com.nu.bom.core.api.dtos.SearchEditDto
import com.nu.bom.core.manufacturing.enums.MasterDataCategory
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.model.AccountMasterData
import com.nu.bom.core.model.MasterData
import com.nu.bom.core.service.nexar.NexarQueryService
import com.nu.bom.core.service.nuledge.NuLedgeService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.MasterDataSearchUtils
import com.nu.bom.core.utils.MasterDataSearchUtils.baselessOnly
import com.nu.bom.core.utils.MasterDataSearchUtils.categoryIs
import com.nu.bom.core.utils.MasterDataSearchUtils.dimensionIs
import com.nu.bom.core.utils.MasterDataSearchUtils.excludeBaseless
import com.nu.bom.core.utils.MasterDataSearchUtils.excludeLamella
import com.nu.bom.core.utils.MasterDataSearchUtils.excludeSheet
import com.nu.bom.core.utils.MasterDataSearchUtils.manageable
import com.nu.bom.core.utils.MasterDataSearchUtils.masterDataTypeIn
import com.nu.bom.core.utils.MasterDataSearchUtils.matchSearchableAttributes
import com.nu.bom.core.utils.MasterDataSearchUtils.technologyIn
import com.nu.bom.core.utils.MasterDataSearchUtils.typeIn
import com.nu.bom.core.utils.getPage
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.isEqualTo
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.math.BigDecimal
import java.util.Date
import java.util.Locale

@Service
class MasterDataSearchService(
    private val masterDataService: MasterDataService,
    private val masterDataConversionService: MasterDataConversionService,
    private val nuLedgeService: NuLedgeService,
    private val nexarQueryService: NexarQueryService,
    private val entityManager: EntityManager,
) {
    enum class SearchOption {
        /** Global entries + Account entries where currentGlobal is filled */
        EXCLUDE_BASELESS,

        /** Account entries where currentGlobal is null */
        BASELESS_ONLY,

        /** All entries */
        ALL,
    }

    fun searchMachines(
        accessCheck: AccessCheck,
        terms: List<String>,
        machineType: List<String>,
        option: SearchOptions,
        page: Int,
        size: Int,
        sortBy: String?,
        orderBy: Sort.Direction?,
    ): Mono<MasterDataResponse> {
        val criteria = mutableListOf<Criteria>()

        criteria += Criteria.where(MasterData::type.name).isEqualTo(MasterDataType.MACHINE)

        // Quick fix to make efficient search in case we are not searching for a key which cant be overriden for account data
        if (option.value == SearchOption.ALL && terms.isEmpty() && machineType.isEmpty()) {
            return getLatestMasterDataNoCriteriaOverridesPaged(
                accessCheck = accessCheck,
                criteria = criteria,
                page = page,
                size = size,
                sortFunction = getSortFunction(sortBy, orderBy),
            )
        }

        if (terms.isNotEmpty()) {
            criteria += matchSearchableAttributes(terms)
        }

        if (machineType.isNotEmpty()) {
            criteria += MasterDataSearchUtils.machineTypeIn(machineType)
        }
        when (option.value) {
            SearchOption.EXCLUDE_BASELESS -> criteria += excludeBaseless()
            SearchOption.BASELESS_ONLY -> criteria += baselessOnly()
            SearchOption.ALL -> {}
        }

        return getLatestMasterDataPaged(
            accessCheck = accessCheck,
            criteria = criteria,
            page = page,
            size = size,
            sortFunction = getSortFunction(sortBy, orderBy),
        )
    }

    private fun getLatestMasterDataNoCriteriaOverridesPaged(
        accessCheck: AccessCheck,
        criteria: List<Criteria>,
        page: Int,
        size: Int,
        sortFunction: Comparator<EditDto>? = null,
    ): Mono<MasterDataResponse> =
        masterDataService
            .findLatestMasterData(
                accessCheck = accessCheck,
                criteria = criteria,
                mode = MasterDataService.SearchMode.MERGE_GLOBAL_NO_CRITERIA_OVERRIDE,
            ).map { masterData ->
                when (masterData) {
                    is AccountMasterData ->
                        masterDataConversionService.toDtoWithMetaFields(
                            masterData,
                        )

                    is MasterData -> masterDataConversionService.toDtoWithMetaFields(masterData)
                    else -> throw java.lang.IllegalArgumentException("Invalid Masterdata format")
                }
            }.collectList()
            .flatMap {
                sortAndPage(sortFunction, Mono.just(it), page, size)
            }

    fun searchMaterials(
        accessCheck: AccessCheck,
        terms: List<String>,
        masterDataType: List<String>,
        technology: List<String>,
        option: SearchOptions,
        page: Int,
        size: Int,
        sortBy: String?,
        orderBy: Sort.Direction?,
        dimension: String?,
        isMasterDataTab: Boolean,
        modularized: Boolean,
    ): Mono<MasterDataResponse> {
        val criteria = mutableListOf<Criteria>()

        criteria +=
            if (masterDataType.isNotEmpty()) {
                masterDataTypeIn(masterDataType)
            } else {
                categoryIs(MasterDataCategory.MATERIAL)
            }

        criteria += manageable()
        if (!isMasterDataTab) {
            criteria += excludeLamella().andOperator(excludeSheet()) // COST-29869
        }

        if (terms.isNotEmpty()) {
            criteria += matchSearchableAttributes(terms)
        }

        if (technology.isNotEmpty()) {
            criteria += technologyIn(technology)
        }

        if (!dimension.isNullOrBlank()) {
            criteria += dimensionIs(dimension)
        }

        if (modularized) {
            criteria +=
                typeIn(
                    entityManager.filterAndGetModularizedMasterDataTypes(
                        masterDataType.map {
                            MasterDataType.valueOf(
                                it,
                            )
                        },
                    ),
                )
        }

        when (option.value) {
            SearchOption.EXCLUDE_BASELESS -> criteria += excludeBaseless()
            SearchOption.BASELESS_ONLY -> criteria += baselessOnly()
            SearchOption.ALL -> {}
        }

        // Quick fix to make efficient search in case we're not searching for a key that can't be overridden for account data
        return if (option.value == SearchOption.ALL && terms.isEmpty() && technology.isEmpty()) {
            return getLatestMasterDataNoCriteriaOverridesPaged(
                accessCheck = accessCheck,
                criteria = criteria,
                page = page,
                size = size,
                sortFunction = getSortFunction(sortBy, orderBy),
            )
        } else {
            // normal search in filtered search space
            getLatestMasterDataPaged(
                accessCheck = accessCheck,
                criteria = criteria,
                page = page,
                size = size,
                sortFunction = getSortFunction(sortBy, orderBy),
            )
        }
    }

    fun searchElectronicComponents(
        accessCheck: AccessCheck,
        terms: List<String>,
        masterDataType: List<String>,
        mpn: List<String>,
        mountingType: List<String>,
        option: SearchOptions,
        page: Int,
        size: Int,
        sortBy: String?,
        orderBy: Sort.Direction?,
        isMasterDataTab: Boolean,
    ): Mono<MasterDataResponse> {
        val nexarPartsFlux =
            if (terms.isNotEmpty() && !isMasterDataTab) {
                nexarQueryService.getPartsByMpnForEdit(terms)
            } else {
                Flux.empty()
            }

        return sortAndPage(getSortFunction(sortBy, orderBy), nexarPartsFlux.collectList(), page, size)
    }

    fun searchManufacturingStepTemplates(
        accessCheck: AccessCheck,
        terms: List<String>,
        page: Int,
        size: Int,
        sortBy: String?,
        orderBy: Sort.Direction?,
        manufacturingStepType: List<String>,
        option: SearchOptions,
    ): Mono<MasterDataResponse> {
        val term =
            terms
                .mapNotNull { it.trim().takeIf { it.isNotBlank() } }
                .reduceOrNull { acc, s -> "$acc $s" }

        val isTset =
            when (option.value) {
                SearchOption.EXCLUDE_BASELESS -> true
                SearchOption.BASELESS_ONLY -> false
                SearchOption.ALL -> null
            }

        return nuLedgeService
            .getTemplates(
                accessCheck,
                term,
                page,
                size,
                sortBy,
                orderBy,
                manufacturingStepType,
                isTset,
            ).map {
                it.entities.map {
                    MasterDataForEditDto(
                        current =
                            MasterDataId(
                                id = it.id,
                                composite =
                                    MasterDataCompositeKey(
                                        type = MasterDataType.MANUFACTURING_STEP.toString(),
                                        key = it.key,
                                        location = null,
                                        year = null,
                                    ),
                                version = 0,
                                active = true,
                                lastModifiedDate = null,
                                refKey = null,
                            ),
                        fields =
                            it.fields.map { dto ->
                                FieldParameter(
                                    name = dto.name,
                                    type = dto.type,
                                    unit = dto.unit,
                                    value = dto.value,
                                    metaInfo = dto.metaInfo,
                                    denominatorUnit = dto.denominatorUnit,
                                )
                            },
                        refKey = null,
                        currentGlobalId = it.tsetKey ?: if (it.accountId == "system") it.key else null,
                        historicGlobalId = null,
                        latestGlobalHistoryId = null,
                    )
                } to it.total
            }.map { (entities, total) ->
                MasterDataResponse(
                    currentPage =
                        entities.map {
                            SearchEditDto(it::class.simpleName!!, it)
                        },
                    totalCount = total.toLong(),
                )
            }
    }

    private fun getLatestMasterDataPaged(
        accessCheck: AccessCheck,
        criteria: List<Criteria>,
        page: Int,
        size: Int,
        sortFunction: Comparator<EditDto>? = null,
        externalData: List<EditDto>? = null,
    ): Mono<MasterDataResponse> {
        val accountData =
            masterDataService
                .findLatestAccountMasterData(
                    accountId = accessCheck.asAccountId(),
                    criteria = criteria,
                ).map { masterData ->
                    masterDataConversionService.toDtoWithMetaFields(masterData)
                }
        val globalNotOverridden =
            masterDataService
                .findLatestGlobalMasterDataWithoutOverride(
                    accountId = accessCheck.asAccountId(),
                    criteria = criteria,
                ).map { masterData ->
                    masterDataConversionService.toDtoWithMetaFields(masterData)
                }
        val results =
            if (externalData != null) {
                accountData.concatWith(globalNotOverridden).concatWith(Flux.fromIterable(externalData)).collectList()
            } else {
                accountData.concatWith(globalNotOverridden).collectList()
            }
        // in-memory sorting & pagination
        return sortAndPage(sortFunction, results, page, size)
    }

    private fun <T : EditDto> sortAndPage(
        sortFunction: Comparator<T>?,
        results: Mono<MutableList<T>>,
        page: Int,
        size: Int,
    ): Mono<PaginatedResponse<SearchEditDto>> =
        when (sortFunction != null) {
            true ->
                results.map {
                    it.sortedWith(sortFunction)
                }

            false -> results
        }.map { editDtos ->
            PaginatedResponse(
                currentPage = editDtos.getPage(page, size).map { SearchEditDto(it::class.simpleName!!, it) },
                totalCount = editDtos.size.toLong(),
            )
        }
}

typealias MasterDataResponse = PaginatedResponse<SearchEditDto>

typealias SearchOptions = List<MasterDataSearchService.SearchOption>

val SearchOptions.value: MasterDataSearchService.SearchOption
    get() =
        if (isEmpty() || size == 2) {
            MasterDataSearchService.SearchOption.ALL
        } else {
            get(0)
        }

private fun getSortFunction(
    sortBy: String?,
    orderBy: Sort.Direction?,
): Comparator<EditDto>? =
    if (sortBy != null) {
        when (orderBy) {
            Sort.Direction.ASC, null -> compareBy(fieldComparator(sortBy))
            Sort.Direction.DESC -> compareByDescending(fieldComparator(sortBy))
        }
    } else {
        null
    }

private fun fieldComparator(name: String): (EditDto) -> Comparable<*>? =
    { dto ->
        when (dto) {
            is MasterDataForEditDto -> {
                when (name) {
                    "refKey" -> dto.refKey?.label
                    "lastModifiedDate" -> dto.current.lastModifiedDate
                    else -> {
                        when (val value = dto.fields.find { it.name == name }?.value) {
                            null -> null
                            is String -> value.lowercase(Locale.getDefault())
                            is BigDecimal -> value
                            is Date -> value
                            else -> value.toString().lowercase(Locale.getDefault())
                        }
                    }
                }
            }

            is ExternalSourceDataDto -> {
                when (val value = dto.fields.find { it.name == name }?.value) {
                    null -> null
                    is String -> value.lowercase(Locale.getDefault())
                    is BigDecimal -> value
                    is Date -> value
                    else -> value.toString().lowercase(Locale.getDefault())
                }
            }

            else -> throw IllegalArgumentException("${dto::class.simpleName} is not a valid EditDto.")
        }
    }
