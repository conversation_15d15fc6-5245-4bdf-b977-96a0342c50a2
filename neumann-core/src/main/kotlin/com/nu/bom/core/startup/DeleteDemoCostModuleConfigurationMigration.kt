package com.nu.bom.core.startup

import com.nu.bom.core.service.configurations.ConfigurationGroup
import com.tset.core.module.mongodb.infrastructure.document.VersionedConfigurationDocument
import io.mongock.api.annotations.ChangeUnit
import io.mongock.api.annotations.Execution
import io.mongock.api.annotations.RollbackExecution
import org.slf4j.LoggerFactory
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query

@ChangeUnit(
    id = "COST-84853 remove demo cost module configuration 01",
    order = "2025-05-20",
)
@Suppress("unused")
class DeleteDemoCostModuleConfigurationMigration(
    private val template: MongoTemplate,
) {
    companion object {
        private val LOG = LoggerFactory.getLogger(DeleteDemoCostModuleConfigurationMigration::class.java)
    }

    @Execution
    fun migrate() {
        val query =
            Query.query(
                Criteria
                    .where("group")
                    .`is`(ConfigurationGroup.COST_MODULES_VERSION.value)
                    .and("type")
                    .`is`("DEMO"),
            )

        val removed = template.remove(query, VersionedConfigurationDocument.COLLECTION_NAME)
        LOG.info("Removed ${removed.deletedCount} demo cost module configurations from ${VersionedConfigurationDocument.COLLECTION_NAME}")
    }

    @RollbackExecution
    fun rollback() {
        LOG.error("Oh no! Rollback for DeleteDemoCostModuleConfigurationMigration got triggered ...")
    }
}
