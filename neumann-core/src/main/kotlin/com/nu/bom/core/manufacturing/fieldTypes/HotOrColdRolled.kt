package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.utils.toUpperSnakeCase

class HotOrColdRolled(res: Selection) : SelectEnumFieldResult<HotOrColdRolled.Selection, HotOrColdRolled>(res) {

    enum class Selection(val value: String) {
        HOT_ROLLED("Hot rolled"),
        COLD_ROLLED("Cold rolled")
    }

    companion object {

        val HOT_ROLLED = HotOrColdRolled(Selection.HOT_ROLLED)
        val COLD_ROLLED = HotOrColdRolled(Selection.COLD_ROLLED)

        fun valueOf(name: String): HotOrColdRolled {

            return Selection.values().find {
                // check if any of
                it.value == name
            }?.let {
                HotOrColdRolled(it)
            } // else try to cast from name
                ?: HotOrColdRolled(Selection.valueOf(name.toUpperSnakeCase()))
        }
    }
}
