package com.nu.bom.core.manufacturing.entities

import com.nu.bom.core.manufacturing.annotations.AlternativeNames
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntitySubtypes
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Hidden
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.ManualClassSelector
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.OnChangeRetain
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.TranslationSection
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeGroupType
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import java.math.BigDecimal

// TODO: userCreatable = true only for the class ManualCycleTimeStep? --jgr/21/03
@EntityType(Entities.CYCLETIME_STEP, userCreatable = true, default = true)
@AlternativeNames(["TurningCycleTimeStep"])
@EntitySubtypes(["OTHERS"])
class CycleTimeStep(name: String) : ManufacturingEntity(name) {

    override val extends = BaseEntityFields(name)

    @Input
    @ObjectView(ObjectView.CYCLETIME_STEP, 1)
    @DefaultUnit(DefaultUnit.SECOND)
    @MandatoryForEntity(section = "right")
    fun time(): CycleTime? = null

    @Input
    @ObjectView(ObjectView.CYCLETIME_STEP, 2)
    fun adjustmentRate(): Rate = Rate(BigDecimal.ONE)

    @ObjectView(ObjectView.CYCLETIME_STEP, 3)
    @ReadOnly
    @DefaultUnit(DefaultUnit.SECOND)
    fun adjustedTime(adjustmentRate: Rate, time: CycleTime): CycleTime {
        return time * adjustmentRate
    }

    @Hidden
    @MandatoryForEntity(computed = true)
    fun parentType(@Parent(Entities.CYCLETIME_STEP_GROUP) type: CycleTimeGroupType?) = type

    @Input
    @MandatoryForEntity(index = 3, refresh = true)
    @ManualClassSelector
    @Path("/api/entity/classes?entityType=CYCLETIME_STEP&subtype={field:typeSelected}")
    @TranslationSection("steps")
    @OnChangeRetain(["displayDesignation", "machiningType", "processingType", "typeSelected", "groupId"])
    fun cycleTimeStep(): Text? = null

    @ReadOnly
    fun materialGroup(@Parent(Entities.CYCLETIME_STEP_GROUP) materialGroup: Text?) = materialGroup
}
