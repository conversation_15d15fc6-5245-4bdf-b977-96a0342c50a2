package com.nu.bom.core.usertset

import com.nu.bom.core.api.dtos.CreateIssueDto
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.usertset.exception.ZendeskException
import com.nu.bom.core.usertset.zendesk.Comment
import com.nu.bom.core.usertset.zendesk.CustomField
import com.nu.bom.core.usertset.zendesk.ZendeskClient
import com.nu.bom.core.usertset.zendesk.ZendeskOrganizationDto
import com.nu.bom.core.usertset.zendesk.ZendeskTicketCreateRequestDto
import com.nu.bom.core.usertset.zendesk.ZendeskTokenService
import com.nu.bom.core.usertset.zendesk.ZendeskUserCreateRequest
import com.nu.bom.core.usertset.zendesk.ZendeskUserDto
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.nu.bom.core.utils.component1
import com.nu.bom.core.utils.component2
import org.slf4j.LoggerFactory
import org.springframework.http.codec.multipart.FilePart
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.core.publisher.SynchronousSink
import reactor.kotlin.core.publisher.switchIfEmpty

// If user has it as a claim value for nu_support - user can create tickets in zendesk
const val ZENDESK_SUPPORT_CLAIM_VALUE = "zendesk"

const val ISSUE_TYPE_BUG = "BUG"
const val ZENDESK_CUSTOM_FIELD_TYPE_ID = "4518931154193"
const val ZENDESK_CUSTOM_FIELD_PRODUCT_ID = "4419139522833"

private val logger = LoggerFactory.getLogger(UsertsetService::class.java)

@Service
class UsertsetService(
    private val zendeskClient: ZendeskClient,
    private val usertsetClient: UsertsetClient,
    private val descriptionFormatter: DescriptionFormatter,
    private val zendeskTokenService: ZendeskTokenService,
) {
    fun getUserLoginToken(accessCheck: AccessCheck): Mono<String> {
        return if (accessCheck.isZendeskEligible()) {
            accessCheck.getUserEmailClaim()
                .flatMap { userEmail ->
                    findUser(userEmail)
                        .switchIfEmpty {
                            accessCheck.getUserNameClaim()
                                .zipWith(accessCheck.getZendeskOrgClaim())
                                .flatMap { (userName, orgName) ->
                                    createUser(userName, userEmail, orgName)
                                }
                        }
                }
                .map { foundUser ->
                    zendeskTokenService.generateToken(foundUser.name, foundUser.email).serialize()
                }
        } else {
            logger.info("User ${accessCheck.userId} is not eligible for zendesk")
            Mono.empty()
        }
    }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    fun createIssue(
        accessCheck: AccessCheck,
        createIssueDto: CreateIssueDto,
        attachments: Flux<FilePart>,
    ): Mono<IssueKey> {
        return if (accessCheck.isZendeskEligible()) {
            accessCheck.getUserEmailClaim().flatMap { userEmail ->
                logger.info("[zendesk] User $userEmail is eligible for zendesk ticket")

                findUser(userEmail)
                    .switchIfEmpty {
                        // If no users have been found - create one
                        accessCheck.getUserNameClaim()
                            .zipWith(accessCheck.getZendeskOrgClaim())
                            .doOnNext { logger.info("[zendesk] Creating user in zendesk for email $userEmail and org ${it.t2}") }
                            .flatMap { (userName, orgName) ->
                                createUser(userName, userEmail, orgName)
                            }
                    }
                    .flatMap { zendeskUserDto ->
                        // zendesk requires to upload files first and then attach a "token" of a file to a comment
                        val uploadedFileTokensMono =
                            attachments
                                .doOnNext { logger.info("[zendesk] uploading file ${it.filename()}") }
                                .flatMap { zendeskClient.uploadFile(it) }
                                .map { it.token }
                                .collectList()

                        uploadedFileTokensMono.flatMap {
                            zendeskClient.createTicket(
                                ZendeskTicketCreateRequestDto(
                                    requesterId = zendeskUserDto.id,
                                    subject = createIssueDto.title,
                                    type = createIssueDto.type,
                                    comment =
                                        Comment(
                                            htmlBody = descriptionFormatter.formatZendeskDescriptionBody(accessCheck, createIssueDto),
                                            uploads = it,
                                        ),
                                    customFields = createIssueDto.toZendeskCustomFields(),
                                ),
                            )
                        }
                    }.map {
                        IssueKey(issueKey = it.id, issueUrl = it.url, issueSource = IssueSource.ZENDESK)
                    }
            }
        } else {
            usertsetClient.createIssue(
                issueType = createIssueDto.type,
                title = createIssueDto.title,
                description = descriptionFormatter.formatJiraDescriptionBody(accessCheck, createIssueDto),
                attachments = attachments,
            )
        }
    }

    fun createUser(
        userName: String,
        email: String,
        organizationName: String,
    ): Mono<ZendeskUserDto> {
        return zendeskClient.findOrgByName(organizationName)
            .collectList().handle { orgs, sink: SynchronousSink<List<ZendeskOrganizationDto>> ->
                if (orgs.isEmpty()) {
                    logger.error("[zendesk] Found 0 orgs with name $organizationName")
                    sink.error(ZendeskException("Found 0 orgs with name $organizationName"))
                } else {
                    val orgsWithExactName = orgs.filter { it.name == organizationName }
                    if (orgsWithExactName.count() != 1) {
                        logger.error("[zendesk] Found ${orgsWithExactName.count()} orgs with name $organizationName")
                        sink.error(ZendeskException("Found 0 orgs with name $organizationName"))
                    } else {
                        sink.next(orgsWithExactName)
                    }
                }
            }
            .map { it.first() } // it's guaranteed by previous code that we have 1 element
            .map {
                ZendeskUserCreateRequest(
                    name = userName,
                    email = email,
                    organizationId = it.id,
                )
            }.flatMap { zendeskClient.createUser(it) }
            .onErrorMap {
                logger.error("[zendesk] Failed to find org or create user with email $email")
                it
            }
    }

    private fun findUser(userEmail: String): Mono<ZendeskUserDto> =
        zendeskClient.findUser(userEmail)
            .collectList().handle { users, sink: SynchronousSink<List<ZendeskUserDto>> ->
                when {
                    users.isEmpty() -> {
                        logger.info("[zendesk] Found 0 users with email $userEmail")
                        sink.complete()
                    }
                    users.count() > 1 -> sink.error(ZendeskException("More than 1 user has been returned for email $userEmail"))
                    else -> sink.next(users)
                }
            }
            .map { it.first() } // it's guaranteed by previous code that we have 1 element or empty Mono

    private fun AccessCheck.getUserEmailClaim(): Mono<String> {
        return claims.getEmail()
            ?.run { Mono.just(this) }
            ?: Mono.error(ZendeskException("User $userId token does not have email claim"))
    }

    private fun AccessCheck.getUserNameClaim(): Mono<String> {
        return claims.getName()
            ?.run { Mono.just(this) }
            ?: Mono.error(ZendeskException("User $userId token does not have name claim"))
    }

    private fun AccessCheck.getUserOrgClaim(): Mono<String> {
        return claims.getNuAccountClaim()
            ?.run { Mono.just(this) }
            ?: Mono.error(ZendeskException("User $userId token does not have nu_account_claim claim"))
    }

    private fun AccessCheck.getZendeskOrgClaim(): Mono<String> {
        return claims.getZendeskOrgClaim()
            ?.run { Mono.just(this) }
            ?: Mono.error(ZendeskException("User $userId token does not have nu_zendesk_org claim"))
    }

    // If user has a claim with zendesk - it's eligible for zendesk
    private fun AccessCheck.isZendeskEligible(): Boolean {
        return claims.getNuSupport() == ZENDESK_SUPPORT_CLAIM_VALUE
    }

    private fun CreateIssueDto.toZendeskCustomFields(): List<CustomField> =
        if (type == ISSUE_TYPE_BUG) {
            listOf(CustomField(ZENDESK_CUSTOM_FIELD_TYPE_ID, "software_problem"))
        } else {
            listOf(CustomField(ZENDESK_CUSTOM_FIELD_TYPE_ID, "feature_request"))
        } + CustomField(ZENDESK_CUSTOM_FIELD_PRODUCT_ID, "cost")

    fun addCommentToIssue(
        accessCheck: AccessCheck,
        issueKey: String,
        comment: String,
    ): Mono<Void> {
        return if (accessCheck.isZendeskEligible()) {
            accessCheck.getUserEmailClaim().flatMap { userEmail ->
                logger.info("[zendesk] User $userEmail is eligible for Zendesk ticket")
                zendeskClient.addCommentToTicket(issueKey, comment)
            }
        } else {
            usertsetClient.addCommentToIssue(issueKey, comment)
        }
    }
}
