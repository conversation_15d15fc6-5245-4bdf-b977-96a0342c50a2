package com.nu.bom.core.manufacturing.service

import com.nu.bom.core.exception.MandatoryFieldMissingException
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Null

typealias FieldValidator = (FieldGraph) -> Unit

class RootFieldsPresentValidator(private val fieldnames: List<String>) : FieldValidator {
    private fun missingMandatoryInputs(fieldGraph: FieldGraph): Map<Field, Set<Field>> {
        val openFields = fieldGraph.fields.filter { !it.hasAlreadyCurrentResultForVersion(fieldGraph.version) }
        val allOpenFields = openFields + fieldGraph.calculateableFields.filter { it.resultNewVersion == null }
        val nullInputFields =
            fieldGraph.fields.filter {
                it.getCurrentVersionedResult()?.result is Null
            }.filter { it.model.fieldType is FieldType.Calculation && it.model.fieldType.isInput() }
        val allOpen = allOpenFields + nullInputFields
        val idMap = mutableMapOf<Field, Set<Field>>()

        fun traverse(field: Field): Set<Field> =
            idMap[field] ?: field.inputs.let { inputs ->
                when {
                    inputs.isEmpty() -> setOf(field)
                    else ->
                        inputs.flatMap { inputKey ->
                            allOpen.find { it.name == inputKey.name && it.entity.entityId == inputKey.entityId }?.let { field ->
                                traverse(field).also { dependentOn -> idMap[field] = dependentOn }
                            } ?: emptySet()
                        }.toSet()
                }
            }
        return fieldnames.mapNotNull { mandatoryFieldName ->
            allOpen.find { it.name == mandatoryFieldName && it.entity.getEntityType() == Entities.MANUFACTURING.name }?.let {
                val traverse = traverse(it)
                if (traverse.isEmpty()) {
                    null
                } else {
                    it to traverse
                }
            }
        }.toMap()
    }

    override fun invoke(fieldGraph: FieldGraph) {
        val missingMandatoryInputs = missingMandatoryInputs(fieldGraph)
        if (missingMandatoryInputs.isNotEmpty()) {
            throw MandatoryFieldMissingException(missingMandatoryInputs)
        }
    }
}
