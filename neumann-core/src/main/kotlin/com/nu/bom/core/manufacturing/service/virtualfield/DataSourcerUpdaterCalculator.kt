package com.nu.bom.core.manufacturing.service.virtualfield

import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Null
import com.nu.bom.core.manufacturing.service.Field
import com.nu.bom.core.manufacturing.service.FieldGraph
import com.nu.bom.core.manufacturing.service.VersionedResult
import com.nu.bom.core.manufacturing.service.calculator.CalculationResult
import com.nu.bom.core.manufacturing.service.calculator.SingleFieldCalculationResult
import com.nu.bom.core.manufacturing.service.virtualfield.sourceproviders.SourceProvider
import com.nu.bom.core.utils.annotations.TsetSuppress
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux

@Service
class DataSourcerUpdaterCalculator(
    private val sourceProviders: List<SourceProvider>,
) : VirtualFieldCalculatorWithProvider(DataSourcerUpdaterProvider()) {
    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    override fun calculateFields(
        fields: List<Field>,
        fieldGraph: FieldGraph,
    ): Flux<out CalculationResult> {
        val unchanged = mutableListOf<SingleFieldCalculationResult>()
        val groups =
            fields.mapNotNull { field ->
                val (versionResult, shouldRecalc) =
                    shouldRecalculate(
                        field,
                        fieldGraph,
                        false,
                        alsoConsiderSystemValueRecalculation = false,
                    )
                val maybeSourceFieldAndKey = maybeSourceFieldAndKey(field)
                val maybeNewSourceKey = maybeSourceFieldAndKey?.second
                if (shouldRecalc.isNormalRecalculationNeeded(field) && maybeNewSourceKey?.result !is Null) {
                    val (keySourceField, newSourceKey) =
                        checkNotNull(maybeSourceFieldAndKey) {
                            "Unable to find input for virtual field ${field.name}"
                        }
                    Triple(sourcerForField(keySourceField), field, newSourceKey)
                } else {
                    unchanged +=
                        createFieldCalculationResult(
                            field,
                            maybeNewSourceKey
                                ?: checkNotNull(versionResult) { "Missing old result for partly recalculated virtual field" },
                            versionResult,
                            fieldGraph.version,
                        )
                    null
                }
            }.groupBy({ (sourcer, _, _) -> sourcer }) { (_, field, newKey) -> Pair(field, newKey) }

        val newResults =
            Flux.fromIterable(groups.entries).flatMap { (sourcer, fieldPairs) ->
                calculateSourcer(sourcer, fieldPairs, fieldGraph)
            }
        return Flux.merge(newResults, Flux.fromIterable(unchanged))
    }

    private fun calculateSourcer(
        sourcer: SourceProvider,
        fieldPairs: List<Pair<Field, VersionedResult>>,
        fieldGraph: FieldGraph,
    ): Flux<out CalculationResult> {
        val accessCheck = fieldPairs.first().first.entity.calculationContext().accessCheck
        // In one batch there might be duplicate keys.
        // To prevent redundant work and deal with providers that *filter out* duplicates, we group by the source key
        // while storing the indices. Then we make the request with distinct source keys and expand it back into the
        // full list
        val sourceKeyGroups =
            fieldPairs
                .mapIndexed { idx, res -> idx to res.second.result }
                .groupBy({ it.second.res!! }) { it.first }
                .toList()

        return sourcer.findSourceEntityData(accessCheck, sourceKeyGroups.map { it.first })
            .doOnNext { (idx, sourceData) ->
                val indices = sourceKeyGroups[idx].second
                indices.forEach { j ->
                    val (field, sourceKey) = fieldPairs[j]
                    sourcer.updateSelector(field, sourceKey, sourceData.versionInfo)
                    convertSuccessorFields(field, sourceData.fieldInfo)
                }
            }.thenMany(
                // Create result for lookup even if no matches were found
                Flux.fromIterable(fieldPairs).map { (field, sourceKey) ->
                    createFieldCalculationResult(field, sourceKey, sourceKey, fieldGraph.version)
                },
            )
    }

    private fun createFieldCalculationResult(
        calculation: Field,
        newSourceDataKey: VersionedResult,
        existingResultKey: VersionedResult?,
        version: Int,
    ) = SingleFieldCalculationResult(
        calculation,
        mapFieldResult(
            existingResultKey = existingResultKey,
            version = version,
            fieldResult = newSourceDataKey.result.withSource(FieldResult.SOURCE.C),
            calculation = calculation,
        ),
    )

    private fun sourcerForField(sourceDataKeyField: Field): SourceProvider {
        return checkNotNull(sourceProviders.find { it.isSourceProviderFor(sourceDataKeyField.model.sourceDataKey) }) {
            "No source provider found for $sourceDataKeyField.model.sourceDataKey"
        }
    }

    private fun maybeSourceFieldAndKey(calculation: Field): Pair<Field, VersionedResult>? {
        assert(calculation.inputs.size == 1) { "Virtual field should only have one input as the source" }
        val sourceDataKeyField = calculation.inputs.first().linkedField
        return sourceDataKeyField?. let {
            val newSourceDataKey =
                checkNotNull(sourceDataKeyField.getCurrentVersionedResult()) {
                    "There is no result stored in $sourceDataKeyField"
                }
            Pair(sourceDataKeyField, newSourceDataKey)
        }
    }

    private fun convertSuccessorFields(
        calculation: Field,
        fieldResultMap: Map<String, FieldResult<*, *>>,
    ) {
        calculation.getSuccessorList().distinct().forEach { field ->
            versionResultForField(field, fieldResultMap)?.let(field::updateInitialFieldResult)
        }
    }

    private fun versionResultForField(
        field: Field,
        fieldResultMap: Map<String, FieldResult<*, *>>,
    ): VersionedResult? {
        val fieldName = field.model.sourceDataInput
        val sourceField = fieldResultMap[fieldName] ?: fieldResultMap[field.name]
        return sourceField?.let {
            VersionedResult(
                it.convert(field.model.getActualReturnType()!!).withSourceAndAdditionalValues(FieldResult.SOURCE.R),
                field.getCurrentVersion(),
                field.getNewVersion(),
            )
        }
    }
}
