package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.utils.toUpperSnakeCase

class CardboardBoxType(
    res: Selection,
) : SelectEnumFieldResult<CardboardBoxType.Selection, CardboardBoxType>(res) {
    enum class Selection {
        MA,
        LP,
    }

    companion object {
        val LP = CardboardBoxType(Selection.LP)
        val MA = CardboardBoxType(Selection.MA)

        fun valueOf(name: String): CardboardBoxType = CardboardBoxType(Selection.valueOf(name.toUpperSnakeCase()))
    }

    val mdShortName: String get() = res.name.lowercase()
}
