package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.model.configurations.BurrWeightConfiguration
import com.nu.bom.core.utils.toUpperSnakeCase
import java.math.BigDecimal
import java.math.MathContext
import java.math.RoundingMode
import kotlin.math.pow

class BurrGeometryGroupType(
    res: Selection,
) : SelectEnumFieldResult<BurrGeometryGroupType.Selection, BurrGeometryGroupType>(res) {
    enum class Selection {
        CIRCLE,
        RECTANGLE,
    }

    companion object {
        val CIRCLE = BurrGeometryGroupType(Selection.CIRCLE)
        val RECTANGLE = BurrGeometryGroupType(Selection.RECTANGLE)

        private val BURR_SCALING_FACTOR = BigDecimal(0.015)
        private const val MIN_BURR_HEIGHT = 3.0
        private const val CIRCLE_BURR_SCALING = 0.5

        fun valueOf(name: String): BurrGeometryGroupType = BurrGeometryGroupType(Selection.valueOf(name.toUpperSnakeCase()))
    }

    constructor(value: String) : this(valueOf(value).res)

    constructor(value: Text) : this(value.res)

    fun calculateBurrHeight(burrWeightBase: Length): Length {
        val res =
            when (res) {
                Selection.CIRCLE -> Math.PI * (burrWeightBase.inMillimeter.toDouble() / 2).pow(2)
                Selection.RECTANGLE -> burrWeightBase.inMillimeter.toDouble()
            }

        return Length(
            kotlin.math.max(
                BURR_SCALING_FACTOR
                    .times(kotlin.math.sqrt(res).toBigDecimal())
                    .round(MathContext(0, RoundingMode.HALF_EVEN))
                    .toDouble(),
                MIN_BURR_HEIGHT,
            ),
            LengthUnits.MILLIMETER,
        )
    }

    fun calculateBurrWidth(
        burrFactor: Rate,
        burrWidthBase: Length,
        widthBase: Length,
    ): Length {
        val burrWidth = (widthBase * (burrFactor - 1.0) + burrWidthBase)
        return when (res) {
            Selection.CIRCLE -> burrWidth * CIRCLE_BURR_SCALING
            Selection.RECTANGLE -> burrWidth
        }
    }

    fun calculateBurrLength(
        partLength: Length,
        burrFactor: Rate,
        burrWidthBase: Length,
    ): Length =
        when (res) {
            Selection.RECTANGLE ->
                Length(partLength.res * (burrFactor.res - 1.toBigDecimal()) + burrWidthBase.res, LengthUnits.METER)

            Selection.CIRCLE -> Length.ZERO
        }

    fun calculateBurrWeightCircle(
        widthBase: Length,
        density: Density,
        burrHeight: Length,
        burrWidth: Length,
    ): QuantityUnit {
        val areaWithBurrWidth =
            Math.PI.toBigDecimal() * (widthBase / Num(2.toBigDecimal()) + burrWidth.res).res.pow(2)
        val partArea = Math.PI.toBigDecimal() * ((widthBase) / Num(2.toBigDecimal())).res.pow(2)
        val burrWeight = (areaWithBurrWidth - partArea) * density.res * burrHeight.res
        return QuantityUnit(burrWeight)
    }

    fun calculateBurrWeightRectangle(
        widthBase: Length?,
        density: Density,
        burrHeight: Length,
        burrWidth: Length,
        partLength: Length?,
        burrLength: Length?,
        burrWeightConfiguration: BurrWeightConfiguration.Type,
    ): QuantityUnit? =
        when (burrWeightConfiguration) {
            BurrWeightConfiguration.Type.DETAILED ->
                burrLength?.let {
                    QuantityUnit(burrLength * burrWidth * burrHeight * density)
                }

            BurrWeightConfiguration.Type.DEFAULT ->
                if (widthBase != null && partLength != null && burrLength != null) {
                    QuantityUnit(
                        ((partLength + burrLength) * (widthBase + burrWidth) - partLength * widthBase) *
                            density.res * burrHeight,
                    )
                } else {
                    null
                }
        }
}
