package com.nu.bom.core.service.exports.tasks

import com.nu.bom.core.model.ExportTask
import com.nu.bom.core.service.bomrads.BomradsFileUploadService
import com.nu.bom.core.service.exports.ExportNameUtil
import com.nu.bom.core.service.file.UploadType
import org.springframework.data.mongodb.core.ReactiveMongoTemplate
import org.springframework.stereotype.Component
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

@Component
class FileDocumentsExportTask(
    override val mongo: ReactiveMongoTemplate,
    private val bomradsFileUploadService: BomradsFileUploadService,
) : AbstractExportTask(mongo) {
    override val exportTask = ExportTask.FILE_DOCUMENTS
    override val dirName = ExportNameUtil.FILES_DIR

    override fun exportInternal(context: ExportContext): Mono<Int> =
        Flux.fromIterable(context.bomNodeIds)
            .concatMap { bomNodeId ->
                Flux.merge(
                    bomradsFileUploadService.findByOwnerIdAndUploadType(
                        accessCheck = context.accessCheck,
                        ownerId = bomNodeId,
                        uploadType = UploadType.BOM_NODE_ATTACHMENT,
                    ),
                    bomradsFileUploadService.findByOwnerIdAndUploadType(
                        accessCheck = context.accessCheck,
                        ownerId = bomNodeId,
                        uploadType = UploadType.NESTING_SVG,
                    ),
                ).concatMap { fileDto ->
                    if (!context.fileDocumentIdsToS3Ids.contains(fileDto.id.idToString())) {
                        context.fileDocumentIdsToS3Ids[fileDto.id.idToString()] = fileDto.uploadId
                        processDto(context, fileDto.id.id, fileDto)
                    } else {
                        Mono.just(1)
                    }
                }
            }.collectList().map { it.size }
}
