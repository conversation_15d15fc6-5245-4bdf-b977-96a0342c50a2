package com.nu.bom.core.manufacturing.fieldTypes

import java.math.BigDecimal

class ToolInfo(res: Jsondata<ToolInfoData>) :
    <PERSON><PERSON><ToolInfo.ToolInfoData>(res) {
    constructor(value: ToolInfoData) : this(Jsondata(value))

    constructor(value: String) : this(
        mapper.readValue<Jsondata<ToolInfoData>>(
            value,
            mapper.getTypeFactory()
                .constructParametricType(
                    Jsondata::class.java,
                    ToolInfoData::class.java,
                ),
        ),
    )

    data class ToolInfoData(
        val keySpecific: String = "",
        val keyGeneric: String = "",
        val type: String,
        val secondsOrMillimeterOrCount: BigDecimal = BigDecimal.ZERO,
    )

    @Deprecated("Only added for BCT reasons. Do not use!")
    constructor(value: MillingToolInfo) : this(value.res.data.toToolInfoData())
}
