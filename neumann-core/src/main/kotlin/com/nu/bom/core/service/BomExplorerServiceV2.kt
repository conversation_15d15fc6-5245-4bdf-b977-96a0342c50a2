package com.nu.bom.core.service

import com.nu.bom.core.api.CO2_PER_PART
import com.nu.bom.core.api.COST_PER_PART
import com.nu.bom.core.api.dtos.BomExpBaseDto
import com.nu.bom.core.api.dtos.BomExpEntryDto
import com.nu.bom.core.api.dtos.BomExpEntryType
import com.nu.bom.core.api.dtos.BomExpNodeDto
import com.nu.bom.core.api.dtos.BomExpNodeQueryDto
import com.nu.bom.core.api.dtos.BomExpRequestDto
import com.nu.bom.core.api.dtos.BomExplorerFieldParameterDto
import com.nu.bom.core.api.dtos.ChildWithUniqueId
import com.nu.bom.core.api.dtos.ExtraNodeInfo
import com.nu.bom.core.api.dtos.ManufacturingStructureDto
import com.nu.bom.core.api.dtos.ReorderInfoDto
import com.nu.bom.core.api.dtos.createCacheIdentifier
import com.nu.bom.core.api.dtos.extractDenominatorUnit
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.entities.BaseModelManufacturing
import com.nu.bom.core.manufacturing.entities.ExternalBomEntry
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.manufacturing.fieldTypes.EmissionUnits
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.utils.dependencies.GroupDependencyProvider
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.toMongoBomNodeId
import com.nu.bom.core.model.toMongoFormatStr
import com.nu.bom.core.model.toMongoID
import com.nu.bom.core.model.toMongoSnapshotId
import com.nu.bom.core.model.toProjectId
import com.nu.bom.core.service.bomrads.BomExplorerLoadingMode
import com.nu.bom.core.service.bomrads.BomNodeLoaderService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.applyIfExists
import com.nu.bomrads.dto.NodeReference
import com.nu.bomrads.dto.NodeSnapshotDTO
import com.nu.bomrads.id.BomEntryId
import com.tset.core.service.domain.calculation.CalculationType
import org.bson.types.ObjectId
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import java.math.BigDecimal
import java.math.BigInteger
import com.nu.bom.core.model.BomNodeId as MongoBomNodeId

@Suppress("ktlint:standard:max-line-length")
@Service
class BomExplorerServiceV2(
    private val bomNodeLoaderService: BomNodeLoaderService,
    private val entityManager: EntityManager,
) {
    companion object {
        private val logger = LoggerFactory.getLogger(BomExplorerServiceV2::class.java)!!

        private val directMaterials =
            setOf(BomExpEntryType.MATERIAL, BomExpEntryType.CONSUMABLE, BomExpEntryType.C_PART)
    }

    fun getRootNodes(
        accessCheck: AccessCheck,
        projectId: String,
    ): Mono<List<BomExpBaseDto>> {
        return getBomNodes(accessCheck, projectId, null)
    }

    fun getBomNodes(
        accessCheck: AccessCheck,
        projectId: String,
        request: BomExpRequestDto?,
    ): Mono<List<BomExpBaseDto>> {
        return bomNodeLoaderService.loadSnapshotsForBomExplorer(
            accessCheck,
            BomExplorerLoadingMode(projectId.toMongoID().toProjectId(), request),
        ).map { loadingResult ->
            loadingResultToBomExpDtos(loadingResult, request?.nodes)
        }
    }

    private fun getNodeById(
        nodeId: String,
        allNodes: List<BomExpBaseDto>,
    ) = allNodes.firstOrNull { it.id == nodeId }

    fun loadingResultToBomExpDtos(
        loadingResult: BomNodeLoaderService.BomExplorerLoadingResult,
        requestedNodes: List<BomExpNodeQueryDto>?,
    ): List<BomExpBaseDto> {
        val allNodes =
            loadingResult.snapshotsAndBranches.snapshots.flatMap { n ->
                nodeSnapshotDtoToBomExplorerDtos(n, loadingResult, requestedNodes)
            }
        setReorderInfos(allNodes, requestedNodes == null)
        return allNodes
    }

    private fun nodeSnapshotDtoToBomExplorerDtos(
        nodeSnapshot: NodeSnapshotDTO,
        loadingResult: BomNodeLoaderService.BomExplorerLoadingResult,
        requestedNodes: List<BomExpNodeQueryDto>?,
    ): List<BomExpBaseDto> {
        val manufacturingEntity = getManufacturingEntityForNodeSnapshot(nodeSnapshot, loadingResult)
        val extraNodeInfo = loadingResult.extraNodeInfoMap[nodeSnapshot.bomNodeId.toMongoBomNodeId()]
        return if (extraNodeInfo != null) {
            buildBomExpNodeDto(
                nodeSnapshot,
                loadingResult.snapshotMap[nodeSnapshot.bomNodeId().toMongoBomNodeId()],
                manufacturingEntity,
                extraNodeInfo,
                requestedNodes,
            )
        } else {
            // this case should never happen and is most probably caused by an inconsistency between bomrads and MongoDB
            // (after a cleanup)
            logger.warn(
                "could not find extraNodeInfo for node (bomNodeId={}, treeId={}) - probably snapshot missing in MongoDB",
                nodeSnapshot.bomNodeId(),
                nodeSnapshot.currentOrPreviousTreeId(),
            )
            listOf()
        }
    }

    private fun disableReorderForNonCopyable(allNodes: List<BomExpBaseDto>) {
        allNodes.forEach { node ->
            if (!node.copyable) {
                node.reorderInfo.disableMoveUp()
                node.reorderInfo.disableMoveDown()
            }
        }
    }

    private fun setReorderInfosForRoots(allNodes: List<BomExpBaseDto>) {
        if (allNodes.isNotEmpty()) {
            // all root nodes are reorderable in general
            // however, first one cannot move up, last one cannot move down
            allNodes.first().reorderInfo.disableMoveUp()
            allNodes.last().reorderInfo.disableMoveDown()
        }
    }

    private fun setReorderInfosForChildren(allNodes: List<BomExpBaseDto>) {
        // for children nodes we need to consider the prev and the next node
        // depending on if types are the same
        for (node in allNodes) {
            val children = node.childrenIds
            if (children?.isNotEmpty() == true) {
                val firstCopyableStepIdx =
                    children.asSequence()
                        .withIndex()
                        .mapNotNull { (idx, cid) -> getNodeById(cid.nodeId, allNodes)?.let { IndexedValue(idx, it) } }
                        .filter { (_, v) -> v.copyable && v is BomExpEntryDto && v.type == BomExpEntryType.MANUFACTURING_STEP }
                        .map { it.index }
                        .firstOrNull() ?: 0
                children.forEachIndexed { idx, childId ->
                    val prev = if (idx == 0) null else getNodeById(children[idx - 1].nodeId, allNodes)
                    val next = if (idx == children.size - 1) null else getNodeById(children[idx + 1].nodeId, allNodes)
                    val cur = getNodeById(childId.nodeId, allNodes)
                    cur?.reorderInfo?.updateMoveUp(
                        prev != null &&
                            areSameNodeTypesForReorder(
                                cur,
                                prev,
                            ) && prev.copyable,
                    )
                    cur?.reorderInfo?.updateMoveDown(
                        next != null &&
                            areSameNodeTypesForReorder(
                                cur,
                                next,
                            ) && next.copyable,
                    )
                    if (cur is BomExpEntryDto && cur.type == BomExpEntryType.MANUFACTURING_STEP) {
                        cur.reorderInfo.update(children.map { it.nodeId }, allNodes, idx, firstCopyableStepIdx)
                    }
                }
            }
        }
    }

    private fun setReorderInfos(
        allNodes: List<BomExpBaseDto>,
        nodesAreRoots: Boolean,
    ): List<BomExpBaseDto> {
        disableReorderForNonCopyable(allNodes)
        if (nodesAreRoots) {
            setReorderInfosForRoots(allNodes)
        } else {
            setReorderInfosForChildren(allNodes)
        }
        return allNodes
    }

    private fun areSameNodeTypesForReorder(
        node1: BomExpBaseDto,
        node2: BomExpBaseDto,
    ): Boolean {
        val bothAreSteps =
            node1 is BomExpEntryDto && node2 is BomExpEntryDto && node1.type == BomExpEntryType.MANUFACTURING_STEP &&
                node2.type == BomExpEntryType.MANUFACTURING_STEP
        return bothAreSteps || (isDirectMaterialType(node1) && isDirectMaterialType(node2))
    }

    private fun isDirectMaterialType(node: BomExpBaseDto) =
        when (node) {
            is BomExpNodeDto -> true
            is BomExpEntryDto -> node.type in directMaterials
        }

    /**
     * tries to retrieve the manufacturingEntity for the given nodeSnapshot.
     * The manufacturingEntity is tried to be found in the loadingResult directly or as a child of the parent
     * entity in the loadingResult
     */
    private fun getManufacturingEntityForNodeSnapshot(
        nodeSnapshot: NodeSnapshotDTO,
        loadingResult: BomNodeLoaderService.BomExplorerLoadingResult,
    ): ManufacturingEntity? {
        val snapshotFromMap =
            loadingResult.snapshotMap.values.associateBy { bomNodeSnapshot ->
                bomNodeSnapshot.id()
            }[nodeSnapshot.currentOrPreviousTreeId().toMongoSnapshotId()]
        if (snapshotFromMap != null) {
            return snapshotFromMap.manufacturing
        }
        val parent =
            loadingResult.snapshotsAndBranches.snapshots.firstOrNull {
                it.children.any { child -> child.treeId() == nodeSnapshot.currentOrPreviousTreeId() }
            }
        val childRef =
            parent?.children?.firstOrNull { child -> child.treeId() == nodeSnapshot.currentOrPreviousTreeId() }
        val parentSnapshot =
            parent?.currentOrPreviousTreeId()?.let { loadingResult.snapshotMap[it.toMongoSnapshotId()] }
        return if (childRef != null && parentSnapshot != null) {
            parentSnapshot.manufacturing?.findByEntityId(childRef.bomEntryId.toMongoFormatStr())
        } else {
            null
        }
    }

    /**
     * This helper class takes care of converting a list of ManufacturingStructureDto elements
     * to the format we require them to be in (recursive)
     */
    private class ManufacturingStructureConverter(
        private val man: ManufacturingStructureDto?,
        /**
         * all nodes that should be shown on the frontend will be added to this list
         */
        private val baseDtos: MutableList<BomExpBaseDto>,
        /**
         * this list must contain all BomNode childrens of the bom node that is currently processed.
         * The converter modifies this list, after the conversion is done this list should be used as
         * list of Children of this node.
         * <p>
         *     We use this to remove external calculation from the children list of the BomNode
         * </p>
         */
        private val childrenOfBomNode: MutableList<NodeReference>,
        /**
         * this is the parent bomNode id to be used during the conversion to set the bom explorer unique id
         */
        private val parentBomNodeId: String,
    ) {
        private val rootChildren = mutableListOf<ChildNodeInfo>()

        fun convert(manufacturingStructures: List<ManufacturingStructureDto>): List<ChildNodeInfo> {
            return convertInternal(manufacturingStructures) + rootChildren
        }

        private fun convertInternal(manufacturingStructures: List<ManufacturingStructureDto>): MutableList<ChildNodeInfo> {
            val childIds = mutableListOf<ChildNodeInfo>()
            for (c in manufacturingStructures) {
                var entry: BomExpEntryDto? = null
                val type = Entities.tryValueOf(c.type)
                if (type?.showInBomExplorer == true) {
                    when (type) {
                        Entities.MANUFACTURING_STEP, Entities.MATERIAL, Entities.C_PART, Entities.CONSUMABLE -> {
                            entry = entryDto(c, BomExpEntryType.valueOf(type.name))
                            rootChildren.add(ChildNodeInfo(c.id, null))
                        }

                        Entities.MACHINE, Entities.TOOL, Entities.LABOR -> {
                            entry = entryDto(c, BomExpEntryType.valueOf(type.name))
                            childIds.add(ChildNodeInfo(c.id, null))
                        }

                        Entities.BOM_ENTRY -> {
                            if (c.entityClass == ExternalBomEntry::class.simpleName) {
                                val childOfNode = childrenOfBomNode.find { it.bomEntryId()?.toMongoFormatStr() == c.id }
                                if (childOfNode != null) {
                                    childIds.add(ChildNodeInfo(childOfNode.bomNodeId(), null))
                                    childrenOfBomNode.remove(childOfNode)
                                } else {
                                    entry = entryDto(c, BomExpEntryType.EXTERNAL_CALCULATION)
                                    childIds.add(ChildNodeInfo(c.id, null, true))
                                }
                            }
                            // we should not need to handle all other cases, because bom entries should always come from bomrads
                        }

                        else -> {}
                    }
                }
                val grandChildren =
                    if (c.children.isNotEmpty()) {
                        convertInternal(c.children)
                    } else {
                        listOf()
                    }
                if (entry != null) {
                    baseDtos.add(
                        entry.copy(
                            childrenIds =
                                grandChildren.sortedWith(ChildrenSorter(man, baseDtos))
                                    .map {
                                        it.childrenWithUniqueId(parentBomNodeId)
                                    },
                        ),
                    )
                } else {
                    childIds.addAll(grandChildren)
                }
            }
            return childIds
        }

        private fun entryDto(
            c: ManufacturingStructureDto,
            type: BomExpEntryType,
        ) = BomExpEntryDto(
            id = c.id,
            bomExplorerUniqueId = "$parentBomNodeId${c.id}",
            designation = c.displayDesignation ?: c.name,
            childrenIds = null,
            type = type,
            entityClass = c.entityClass,
            entityType = c.type,
            entityId = c.id,
            copyable = c.copyable,
            sortIndex = c.index,
            groupId = c.groupId,
            reorderInfo =
                ReorderInfoDto(
                    _moveUpEnabled = true,
                    _moveDownEnabled = true,
                ),
            costModule = c.costModule,
        )
    }

    /**
     * Used to store the information of each node to create the bomExplorer node structure, as well as to do
     * the sorting of the nodes and create a unique id for the bom explorer in the FE.
     * @param nodeId id of the node, can be a manufacturing id if it's an entity or a bomNode id if a bomNode
     * @param bomEntryId id of the bom entry that contains this node. Used for sorting the entities
     * @param isBomNode boolean flag that marks this node as a bomNode and not a manufacturing entity
     */
    private class ChildNodeInfo(val nodeId: String, val bomEntryId: BomEntryId?, val isBomNode: Boolean = false) {
        constructor(
            bomNodeId: com.nu.bomrads.id.BomNodeId,
            bomEntryId: BomEntryId?,
        ) : this(bomNodeId.toMongoFormatStr(), bomEntryId, true)

        fun childrenWithUniqueId(bomNodeId: String): ChildWithUniqueId {
            val uniqueId =
                if (isBomNode) {
                    nodeId
                } else {
                    "$bomNodeId$nodeId"
                }
            return ChildWithUniqueId(nodeId, uniqueId)
        }
    }

    private class ChildrenSorter(
        private val man: ManufacturingStructureDto?,
        private val baseDtos: List<BomExpBaseDto>,
        private val orderOfDirectChilds: List<ManufacturingEntity>? = null,
    ) : Comparator<ChildNodeInfo> {
        override fun compare(
            c0: ChildNodeInfo,
            c1: ChildNodeInfo,
        ): Int {
            val entry0 = baseDtos.find { it.id == c0.nodeId }
            val entry1 = baseDtos.find { it.id == c1.nodeId }

            if (orderOfDirectChilds != null) {
                val idxManuEntry0 =
                    orderOfDirectChilds.indexOfFirst {
                        it.entityId == c0.bomEntryId?.toMongoFormatStr() || it._id.toHexString() == c0.nodeId
                    }
                val idxManuEntry1 =
                    orderOfDirectChilds.indexOfFirst {
                        it.entityId == c1.bomEntryId?.toMongoFormatStr() || it._id.toHexString() == c1.nodeId
                    }
                if (idxManuEntry0 != -1 && idxManuEntry1 != -1) {
                    return idxManuEntry0 - idxManuEntry1
                }
            }

            val entry0IsBomNode = entry0?.let { it is BomExpNodeDto } ?: true
            val entry1IsBomNode = entry1?.let { it is BomExpNodeDto } ?: true
            return if (entry0IsBomNode && entry1IsBomNode) {
                compareBomNodes(c0, c1)
            } else if (entry0IsBomNode != entry1IsBomNode) {
                // BomNode on top
                if (entry0IsBomNode) -1 else 1
            } else {
                val typeEntry0 = (entry0 as? BomExpEntryDto)?.type
                val typeEntry1 = (entry1 as? BomExpEntryDto)?.type
                if (typeEntry0 == typeEntry1) {
                    if (typeEntry0 == BomExpEntryType.MANUFACTURING_STEP) {
                        // since both entries are steps, we need to reverse the order of steps
                        // because steps are stored in reverse order
                        val idxOfEntry0 = baseDtos.indexOfFirst { it.id == c0.nodeId }
                        val idxOfEntry1 = baseDtos.indexOfFirst { it.id == c1.nodeId }
                        idxOfEntry0 - idxOfEntry1
                    } else {
                        val idx0 = (entry0 as? BomExpEntryDto)?.sortIndex
                        val idx1 = (entry1 as? BomExpEntryDto)?.sortIndex
                        nullsFirst<Int>().compare(idx0, idx1)
                    }
                } else {
                    (typeEntry0?.sortOrder ?: Int.MAX_VALUE) - (typeEntry1?.sortOrder ?: Int.MAX_VALUE)
                }
            }
        }

        private fun compareBomNodes(
            c0: ChildNodeInfo,
            c1: ChildNodeInfo,
        ) = if (man != null && c0.bomEntryId != null && c1.bomEntryId != null) {
            // we use the manufacturingStructureDto to determine the order of the children
            val idxManuEntry0 =
                man.children.firstOrNull { it.id == c0.bomEntryId.toMongoFormatStr() }?.index
                    ?: (Integer.MAX_VALUE / 2)
            val idxManuEntry1 =
                man.children.firstOrNull { it.id == c1.bomEntryId.toMongoFormatStr() }?.index
                    ?: (Integer.MAX_VALUE / 2)
            idxManuEntry0 - idxManuEntry1
        } else {
            // fallback that should actually never happen
            // whenever we send bom node children, we should also have `man` available
            logger.warn("bomexplorerapi tried to sort bomnode children but no manufacturingstructure given")
            0
        }
    }

    private fun buildBomExpNodeDto(
        nodeSnapshot: NodeSnapshotDTO,
        bomNodeSnapshot: BomNodeSnapshot?,
        manufacturingEntity: ManufacturingEntity?,
        extraNodeInfo: ExtraNodeInfo,
        requestedNodes: List<BomExpNodeQueryDto>?,
    ): List<BomExpBaseDto> {
        val copyable: Boolean = isCopyable(nodeSnapshot, extraNodeInfo, manufacturingEntity)
        val requestedNode =
            requestedNodes
                ?.find { MongoBomNodeId(it.id) == nodeSnapshot.bomNodeId().toMongoBomNodeId() }
        val withChildren = requestedNode?.applyIfExists { it.withChildren } ?: false
        val bomNodeId = nodeSnapshot.bomNodeId.toMongoBomNodeId().toHexString()
        val baseDtos = mutableListOf<BomExpBaseDto>()
        val children =
            if (withChildren && requestedNode?.isCacheIdentifierSameLikeInSnapshot(nodeSnapshot) != true) {
                val childrenOfNode = nodeSnapshot.allChildren().toMutableList()
                var man: ManufacturingStructureDto? = null
                val manufacturingChildren =
                    if (manufacturingEntity != null) {
                        man = toManufacturingStructure(manufacturingEntity)
                        val converter =
                            ManufacturingStructureConverter(
                                man,
                                baseDtos,
                                childrenOfNode,
                                nodeSnapshot.bomNodeId.toMongoBomNodeId().toHexString(),
                            )
                        converter.convert(man.children)
                    } else {
                        listOf()
                    }

                val tempChildren =
                    childrenOfNode
                        .map { ChildNodeInfo(it.bomNodeId(), it.bomEntryId()) }
                        .toSet()
                        .toMutableList()
                tempChildren.addAll(manufacturingChildren)
                tempChildren.sortWith(
                    ChildrenSorter(
                        man,
                        baseDtos,
                        bomNodeSnapshot?.applyIfExists {
                            ManufacturingEntityRearrangementService.getFlatOrderedListOnRoot(it, true)
                        },
                    ),
                )
                tempChildren.map { it.childrenWithUniqueId(bomNodeId) }
            } else {
                null
            }

        val costModule = extraNodeInfo.technologyModel?.let { Model.fromEntity(it) }
        val dimension = extraNodeInfo.unitOverrideContext?.dimension
        val validCopySourceNames: Set<String> =
            calculateValidCopySources(
                dimension,
                extraNodeInfo.calculationType,
                costModule,
            )

        baseDtos.add(
            0,
            BomExpNodeDto(
                id = bomNodeId,
                bomExplorerUniqueId = bomNodeId,
                designation = extraNodeInfo.partName ?: nodeSnapshot.name,
                childrenIds = children,
                branchId = nodeSnapshot.branchId?.toMongoFormatStr() ?: "",
                variantsCount = nodeSnapshot.noBranches ?: 0,
                co2PerPart =
                    BomExplorerFieldParameterDto(
                        name = CO2_PER_PART,
                        type = Emission::class.java.simpleName,
                        unit = EmissionUnits.KILOGRAM_CO2E.name,
                        value = extraNodeInfo.co2PerPart ?: BigDecimal.ZERO,
                        denominatorUnit = extractDenominatorUnit(extraNodeInfo.unitOverrideContext),
                        broken = extraNodeInfo.co2PerPart == null,
                    ),
                costPerPartInBaseCurrency =
                    BomExplorerFieldParameterDto(
                        name = COST_PER_PART,
                        type = "Money",
                        value = extraNodeInfo.costPerPartInBaseCurrency,
                        unit = null,
                        currencyInfo = null,
                        denominatorUnit = extractDenominatorUnit(unitOverrideContext = extraNodeInfo.unitOverrideContext),
                        broken = extraNodeInfo.costPerPartInBaseCurrencyBroken,
                    ),
                baseCurrency = extraNodeInfo.baseCurrency,
                hasChildren = hasChildren(nodeSnapshot, extraNodeInfo, children),
                procurementType = extraNodeInfo.procurementType,
                otherInformation = extraNodeInfo.partNumber ?: "",
                type = extraNodeInfo.calculationType,
                calculationClass = extraNodeInfo.calculationClass ?: "",
                cacheIdentifier = createCacheIdentifier(nodeSnapshot),
                entityId = (extraNodeInfo.manufacturingId ?: manufacturingEntity?.entityId) ?: "",
                copyable = copyable,
                reorderInfo =
                    ReorderInfoDto(
                        _moveUpEnabled = true,
                        _moveDownEnabled = true,
                    ),
                status = nodeSnapshot.status,
                validCopySources = validCopySourceNames,
                dimension = dimension?.name ?: "",
                reportSent = extraNodeInfo.reportSent,
                costModule = costModule,
                protectedAt = nodeSnapshot.protectedAt,
            ),
        )

        return baseDtos
    }

    private fun calculateValidCopySources(
        dimension: Dimension.Selection?,
        calculationType: CalculationType,
        technologyModel: Model?,
    ): Set<String> {
        val validCopySources =
            if (calculationType == CalculationType.MANUAL_CALCULATION) {
                entityManager.getAllModularizedClasses(dimension)
            } else {
                technologyModel?.let { entityManager.getEntitiesForTechnology(it, dimension) }
            } ?: emptyList()
        val validCopySourcesWithManual = validCopySources + entityManager.getCopyableEntities()
        val validCopySourceNames: Set<String> =
            validCopySourcesWithManual.mapTo(mutableSetOf()) {
                it.simpleName
            }
        return validCopySourceNames
    }

    private fun isCopyable(
        nodeSnapshot: NodeSnapshotDTO,
        extraNodeInfo: ExtraNodeInfo,
        manufacturingEntity: ManufacturingEntity?,
    ): Boolean {
        val notCopyableClasses =
            setOf(
                "ManufacturingWaxCluster",
                "ManufacturingCeramicMold",
                "ManufacturingWaxRunnerSystem",
                "ManufacturingWaxModel",
                "ManufacturingCoreShooting",
            )
        return nodeSnapshot.root ||
            (
                manufacturingEntity?.canBeCopied() ?: extraNodeInfo.calculationClass.applyIfExists {
                    !notCopyableClasses.contains(
                        it,
                    )
                } ?: true
            )
    }

    /**
     * this function determines if the node hasChildren, i.e. if the expand symbol should be shown on frontend side
     *
     * this function uses the children from the given [NodeSnapshotDTO] (data from bomrads) and/or the given [children]
     * list. if [children] is empty and there are no children in [nodeSnapshot], the [ExtraNodeInfo.hasChildrenInBomExplorer] flag
     * is used. If this flag is null ("legacy" data in bomrads), a fallback is used which assumes that only calculations
     * of type [CalculationType.ROUGH_CALCULATION] have no children and all other nodes have children
     *
     * @param nodeSnapshot for which it should be decided if it has children (data from bomrads)
     * @param meta [ExtraNodeInfo] for the given node
     * @param children nullable list of children: if children were determined, this list is used to check if there are children.
     * This argument can be null, if the bom explorer API did not evaluate the children (e.g. for requests with withChildren = false)
     */
    private fun hasChildren(
        nodeSnapshot: NodeSnapshotDTO,
        meta: ExtraNodeInfo,
        children: List<ChildWithUniqueId>?,
    ): Boolean {
        val snapshotContainsChildren =
            nodeSnapshot.internalChildren().isNotEmpty() || nodeSnapshot.externalChildren().isNotEmpty()
        if (children != null) {
            // we actually fetched the children, so it is safe to decide if there are children like that
            return snapshotContainsChildren || children.isNotEmpty()
        }
        return snapshotContainsChildren ||
            (meta.hasChildrenInBomExplorer == null && meta.calculationType != CalculationType.ROUGH_CALCULATION) ||
            (meta.hasChildrenInBomExplorer ?: false)
    }

    private fun toManufacturingStructure(
        manufacturing: ManufacturingEntity,
        parent: ManufacturingEntity? = null,
    ): ManufacturingStructureDto {
        manufacturing.setParent(parent)
        val result = manufacturing.getFieldResult("displayDesignation")?.res
        val idx = manufacturing.getFieldOrInitialFieldResult("sortIndex")?.res
        val technologyModelText =
            manufacturing.getFieldOrInitialFieldResult(BaseModelManufacturing::technologyModel.name) as? Text
        val costModule = technologyModelText?.res?.let { Model.fromEntity(it) }
        return ManufacturingStructureDto(
            id = manufacturing.entityId,
            name = manufacturing.name,
            type = manufacturing.getEntityType(),
            entityClass = manufacturing::class.simpleName!!,
            displayDesignation = result as? String,
            children = manufacturing.children.map { toManufacturingStructure(it, manufacturing) },
            copyable = manufacturing.canBeCopied(),
            index = (idx as? BigInteger)?.toInt(),
            groupId =
                manufacturing.getFieldOrInitialFieldResult(GroupDependencyProvider.GROUP_ID_FIELD_NAME)
                    ?.res
                    ?.let { it as? String }
                    ?.let(::ObjectId),
            costModule = costModule,
        )
    }
}
