package com.nu.bom.core.api

import com.nu.bom.core.api.dtos.AutocompleteContainer
import com.nu.bom.core.api.dtos.AutocompleteResponse
import com.nu.bom.core.api.dtos.BomNodeDto
import com.nu.bom.core.api.dtos.BulkActionCalcFilter
import com.nu.bom.core.api.dtos.BulkActionCalcUpdateDto
import com.nu.bom.core.api.dtos.BulkActionCategoryDto
import com.nu.bom.core.api.dtos.BulkActionDetailedCalcUpdate
import com.nu.bom.core.api.dtos.BulkActionFieldOverridesResponse
import com.nu.bom.core.api.dtos.BulkActionFilterResponse
import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.api.dtos.FilterFieldValue
import com.nu.bom.core.config.AccessCheckContext
import com.nu.bom.core.exception.userException.BulkActionNotFoundException
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.createBranchId
import com.nu.bom.core.service.BomNodeAccessInfo
import com.nu.bom.core.service.BomNodeService
import com.nu.bom.core.service.BulkActionService
import com.nu.bom.core.service.ManufacturingEntityQueryService
import com.nu.bom.core.service.bomrads.AllChildren
import com.nu.bom.core.user.AccessCheckProvider
import com.nu.bom.core.utils.exceptionOnEmpty
import com.nu.masterdata.dto.v1.basicdata.ClassificationDto
import org.slf4j.LoggerFactory
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

private const val SKILL_TYPE = "skillType"
private const val SKILL_TYPE_NAME = "skillTypeName"
const val LOCATION = "location"
const val LOCATION_NAME = "locationName"
private const val MATERIAL_BULK_UPDATE_FIELD_NAME = "displayName"

// Type name is not a field, so we need a special way of handling it
const val TYPE_NAME_FAKE_FIELD = "typeName"

@RestController
@AccessCheckContext
@RequestMapping("/api/bulk/categories")
class BulkActionController(
    private val bomNodeService: BomNodeService,
    private val bulkActionService: BulkActionService,
    private val accessCheckProvider: AccessCheckProvider,
    private val manufacturingEntityQueryService: ManufacturingEntityQueryService,
) {
    companion object {
        val log = LoggerFactory.getLogger(BulkActionController::class.java)!!
    }

    @GetMapping
    fun getBulkActionsCategories(
        @AuthenticationPrincipal jwt: Jwt?,
    ): Flux<BulkActionCategoryDto> =
        accessCheckProvider.doAsReturnMany(jwt) { ac ->
            bulkActionService.getBulkActionConfigs(ac).map { it.toCategoryDto() }
        }

    @GetMapping("/{categoryId}")
    fun getCategoryFilters(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable categoryId: String,
        @RequestParam bomNodeId: String?,
        @RequestParam branchId: String?,
    ) = accessCheckProvider.doAs(jwt) {
        bulkActionService.getBulkActionConfig(it, categoryId, bomNodeId, branchId)
    }

    @PostMapping("/{categoryId}/filteredCalculations")
    fun applyFilter(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable categoryId: String,
        @RequestBody bulkActionCalcFilter: BulkActionCalcFilter,
    ): Mono<BulkActionFilterResponse> =
        AccessCheckProvider.getCurrentAccessCheck().flatMap { accessCheck ->
            bulkActionService.applyFilter(
                BomNodeAccessInfo(
                    accessCheck,
                    BomNodeId(bulkActionCalcFilter.calculation.bomNodeId),
                    BranchId(bulkActionCalcFilter.calculation.branchId),
                ),
                categoryId,
                bulkActionCalcFilter.filters,
            )
        }

    @PostMapping("/{categoryId}/fieldOverrides")
    fun getFieldOverrides(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable categoryId: String,
        @RequestBody bulkActionCalcFilter: BulkActionCalcFilter,
    ): Mono<BulkActionFieldOverridesResponse> =
        accessCheckProvider.doAs(jwt) { accessCheck ->
            bomNodeService
                .getBomNode(
                    accessCheck,
                    BomNodeId(bulkActionCalcFilter.calculation.bomNodeId),
                    branch = createBranchId(bulkActionCalcFilter.calculation.branchId),
                    loadingMode = AllChildren(BomNodeId(bulkActionCalcFilter.calculation.bomNodeId)),
                ).flatMap { snapshot ->
                    bulkActionService
                        .getFieldOverrides(
                            accessCheck,
                            categoryId,
                            BomNodeId(bulkActionCalcFilter.calculation.bomNodeId),
                            BranchId(bulkActionCalcFilter.calculation.branchId),
                            bulkActionCalcFilter.filters,
                            snapshot,
                        ).exceptionOnEmpty {
                            BulkActionNotFoundException(categoryId)
                        }
                }
        }

    @PostMapping("/{categoryId}/apply")
    fun applyBulkAction(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable categoryId: String,
        @RequestBody filters: BulkActionCalcUpdateDto,
        @RequestParam ccy: String,
    ): Flux<BomNodeDto> =
        AccessCheckProvider.getCurrentAccessCheck().flatMapMany { accessCheck ->
            val updatesWithCurrency =
                filters.updates.map {
                    if (it.type == "Money" || it.type == "PriceComponents") {
                        it.copy(
                            currencyIsoCode = ccy,
                        )
                    } else {
                        it
                    }
                }

            bulkActionService.applyBulkAction(
                BomNodeAccessInfo(
                    accessCheck,
                    BomNodeId(filters.calculation.bomNodeId),
                    BranchId(filters.calculation.branchId),
                ),
                categoryId,
                filters.filters,
                updatesWithCurrency,
            )
        }

    @PostMapping("/{categoryId}/applyDetailed")
    fun applyDetailedBulkAction(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable categoryId: String,
        @RequestBody filters: BulkActionDetailedCalcUpdate,
    ): Flux<BomNodeDto> =
        AccessCheckProvider.getCurrentAccessCheck().flatMapMany { accessCheck ->
            bulkActionService.applyDetailedBulkAction(
                BomNodeAccessInfo(
                    accessCheck,
                    BomNodeId(filters.calculation.bomNodeId),
                    BranchId(filters.calculation.branchId),
                ),
                categoryId,
                filters.filters,
                filters.updates,
            )
        }

    @GetMapping("/bomNode/{bomNodeId}/branch/{branch}/entities")
    fun getEntityTypes(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable bomNodeId: String,
        @PathVariable branch: String,
        @RequestParam entityType: String,
        @RequestParam field: String?,
        @RequestParam allOption: Boolean = false,
    ): Mono<AutocompleteContainer> =
        accessCheckProvider.doAs(jwt) { accessCheck ->
            bomNodeService
                .getBomNode(
                    accessCheck,
                    BomNodeId(bomNodeId),
                    branch = createBranchId(branch),
                    loadingMode = AllChildren(BomNodeId(bomNodeId)),
                ).map { bomNode ->
                    toEntityTypes(entityType)
                        .map {
                            manufacturingEntityQueryService.getDescendantsOfTypeWithRoot(
                                bomNode,
                                it,
                            )
                        }.flatten()
                        .mapNotNull(transformEntityForBulkActions(bomNode.bomNodeId.toString(), field))
                }.map { results ->
                    AutocompleteContainer(
                        sections = null,
                        AutocompleteResponse.responseWithAll(allOption, results).distinctBy { it.key },
                    )
                }
        }

    // entityType contains comma separated values of entity types
    private fun toEntityTypes(entityType: String): List<Entities> = entityType.split(",").map { Entities.valueOf(it) }

    @PostMapping("/{categoryId}/previewCalculate")
    fun previewCalculation(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable categoryId: String,
        @RequestBody fields: List<FieldParameter>,
    ) = AccessCheckProvider.getCurrentAccessCheck().flatMap { accessCheck ->
        bulkActionService.calculatePreviewFields(accessCheck, categoryId, fields)
    }

    /**
     * In some cases we can't give frontend a link for fetching the field values. We need to ask FE to query
     * NBK in some cases, where NBK generates possible field values.
     */
    @GetMapping("/{categoryId}/bomNode/{bomNodeId}/branch/{branch}/fieldValues/{fieldName}")
    fun getFilterFieldValues(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable bomNodeId: String,
        @PathVariable branch: String,
        @PathVariable fieldName: String,
        @PathVariable categoryId: String,
    ): Flux<FilterFieldValue> =
        accessCheckProvider.doAsReturnMany(jwt) { accessCheck ->
            bomNodeService
                .getBomNode(
                    accessCheck,
                    BomNodeId(bomNodeId),
                    branch = createBranchId(branch),
                    loadingMode = AllChildren(BomNodeId(bomNodeId)),
                ).flatMapMany { bomNode ->
                    bulkActionService.getFilterFieldValues(accessCheck, bomNode.manufacturing!!, categoryId, fieldName)
                }
        }

    /**
     * In some cases we can't give frontend a link for fetching the field values. We need to ask FE to query
     * NBK in some cases, where NBK generates possible field values.
     */
    @GetMapping("/{categoryId}/bomNode/{bomNodeId}/branch/{branch}/classification/{fieldName}")
    fun getClassificationValues(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable bomNodeId: String,
        @PathVariable branch: String,
        @PathVariable fieldName: String,
        @PathVariable categoryId: String,
    ): Flux<ClassificationDto> =
        accessCheckProvider.doAsReturnMany(jwt) { accessCheck ->
            bomNodeService
                .getBomNode(
                    accessCheck,
                    BomNodeId(bomNodeId),
                    branch = createBranchId(branch),
                    loadingMode = AllChildren(BomNodeId(bomNodeId)),
                ).flatMapMany { bomNode ->
                    bulkActionService.getClassificationFieldValues(
                        accessCheck,
                        bomNode.manufacturing!!,
                        categoryId,
                        fieldName,
                    )
                }
        }

    private fun transformEntityForBulkActions(
        bomNodeId: String,
        field: String?,
    ): (ManufacturingEntity) -> AutocompleteResponse? =
        {
            when (field) {
                SKILL_TYPE -> {
                    val skillTypeKey = (it.getFieldResult(SKILL_TYPE) as Text).res
                    val skillTypeName = (it.getFieldResult(SKILL_TYPE_NAME) as Text).res
                    AutocompleteResponse(skillTypeName, skillTypeKey, section = null)
                }

                LOCATION -> {
                    val locationKey =
                        (it.getFieldResult(LOCATION) as? Text)?.res.also { location ->
                            if (location == null) log.error("Location field missing for entity with bomNodeId: $bomNodeId")
                        }

                    val locationName =
                        (it.getFieldResult(LOCATION_NAME) as? Text)?.res.also { locationName ->
                            if (locationName == null) log.error("Location name field missing for entity with bomNodeId: $bomNodeId")
                        }

                    if (!locationKey.isNullOrBlank() && !locationName.isNullOrBlank()) {
                        AutocompleteResponse(locationName, locationKey, section = null)
                    } else {
                        null
                    }
                }

                MATERIAL_BULK_UPDATE_FIELD_NAME -> {
                    requireNotNull(it.displayName) {
                        "Display name field of material is null. Entity name is ${it.name}"
                    }
                    AutocompleteResponse(it.displayName!!, it.displayName!!, section = null)
                }

                TYPE_NAME_FAKE_FIELD -> {
                    AutocompleteResponse(it.getEntityType(), it.getEntityType(), null)
                }

                else -> AutocompleteResponse.getAutoCompleteResponse(it, field)
            }
        }
}
