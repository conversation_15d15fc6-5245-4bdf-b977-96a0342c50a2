package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.utils.toUpperSnakeCase

class StepSubTypeCutToLengthProfile(res: Selection) : SelectEnumFieldResult<StepSubTypeCutToLengthProfile.Selection, StepSubTypeCutToLengthProfile>(res) {

    enum class Selection(val value: String) {
        FULL_MATERIAL("Full material"),
        HOLLOW_MATERIAL("Hollow material")
    }

    companion object {

        val FULL_MATERIAL = StepSubTypeCutToLengthProfile(Selection.FULL_MATERIAL)
        val HOLLOW_MATERIAL = StepSubTypeCutToLengthProfile(Selection.HOLLOW_MATERIAL)

        fun valueOf(name: String): StepSubTypeCutToLengthProfile {

            return Selection.values().find {
                // check if any of
                it.value == name
            }?.let {
                StepSubTypeCutToLengthProfile(it)
            } // else try to cast from name
                ?: StepSubTypeCutToLengthProfile(Selection.valueOf(name.toUpperSnakeCase()))
        }
    }
}
