package com.nu.bom.core.machining.cycletimestep

import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.entities.CycleTimeStep
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Speed
import com.nu.bom.core.manufacturing.fieldTypes.Text

@EntityType(Entities.CYCLETIME_STEP)
class DressingCycleTimeStep(name: String) : ManufacturingEntity(name) {
    override val extends = CycleTimeStep(name)

    @ReadOnly
    @DefaultUnit(DefaultUnit.M_PER_MIN)
    val vc: Speed? = null

    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    val dressing_f: Length? = null

    @ReadOnly
    val dressing_frequency: Num? = null

    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    val dressing_depth: Length? = null

    @ReadOnly
    val toolName: Text? = null
}
