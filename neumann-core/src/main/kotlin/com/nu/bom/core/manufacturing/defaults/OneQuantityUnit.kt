package com.nu.bom.core.manufacturing.defaults

import com.nu.bom.core.manufacturing.fieldTypes.DynamicQuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.PiecesUnits
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import java.math.BigDecimal

class OneDynamicQuantityUnit : DefaultProvider<DynamicQuantityUnit> {
    override fun provide(): DynamicQuantityUnit {
        val p = DynamicQuantityUnit(BigDecimal.ONE, PiecesUnits.PIECE, null)
        p.source = FieldResult.SOURCE.I
        return p
    }
}

class OneQuantityUnit : DefaultProvider<QuantityUnit> {
    override fun provide(): QuantityUnit {
        val p = QuantityUnit(BigDecimal.ONE)
        p.source = FieldResult.SOURCE.I
        return p
    }
}
