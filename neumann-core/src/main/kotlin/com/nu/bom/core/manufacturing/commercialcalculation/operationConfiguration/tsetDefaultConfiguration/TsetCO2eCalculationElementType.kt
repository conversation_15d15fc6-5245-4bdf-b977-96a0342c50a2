package com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration

enum class TsetCO2eCalculationElementType(override val fieldName: String, override val short: String, override val long: String) : TsetCalculationElementType {
    // material
    DIRECT_RAW_MATERIAL_CO2E("DirectRawMaterialCO2e", "DiRm-CO2e", "Raw material direct"),
    DIRECT_PROVIDED_RAW_MATERIAL_CO2E("ProvidedDirectRawMaterialCO2e", "ProDiRm-CO2e", "Provided raw material direct"),
    DIRECT_PURCHASE_PART_CO2E("DirectPurchasePartCO2e", "DiPp-CO2e", "Purchased part direct"),
    DIRECT_PROVIDED_PART_CO2E("DirectProvidedPartCO2e", "DiProP-CO2e", "Provided part direct"),
    DIRECT_MATERIAL_CO2E("DirectMaterialCO2e", "DiM-CO2e", "Direct material"),

    RAW_MATERIAL_SCRAP_CO2E("RawMaterialScrapCO2e", "RmScrap-CO2e", "Raw material scrap"),
    PROVIDED_RAW_MATERIAL_SCRAP_CO2E("ProvidedRawMaterialScrapCO2e", "ProRmScrap-CO2e", "Provided raw material scrap"),
    PURCHASE_PART_SCRAP_CO2E("PurchasePartsScrapCO2e", "PpScrap-CO2e", "Purchased part scrap"),
    PROVIDED_PART_SCRAP_CO2E("ProvidedPartScrapCO2e", "ProPScrap-CO2e", "Provided part scrap"),
    MATERIAL_SCRAP_CO2E("MaterialScrapCO2e", "MScrap-CO2e", "Material scrap"),

    RAW_MATERIAL_OVERHEAD_CO2E("RawMaterialOverheadCO2e", "RmOv-CO2e", "Raw material overhead"),
    PURCHASE_PART_OVERHEAD_CO2E("PurchasePartsOverheadCO2e", "PpOv-CO2e", "Purchased part overhead"),
    MATERIAL_OVERHEAD_CO2E("MaterialOverheadCO2e", "MOv-CO2e", "Material overhead"),

    MATERIAL_CO2E("MaterialCO2e", "M-CO2e", "Material"),

    // manufacturing
    MACHINE_DEPRECIATION_CO2E("MachineDepreciationCO2e", "MacDep-CO2e", "Machine depreciation"),
    MACHINE_FIX_CO2E("MachineFixCO2e", "MacFi-CO2e", "Machine fix"),
    MACHINE_ELECTRIC_ENERGY_CO2E("MachineElectricEnergyCO2e", "MacElEn-CO2e", "Machine electric energy"),
    MACHINE_GAS_ENERGY_ACTIVE_CO2E("MachineGasEnergyActiveCO2e", "MacGasEnAct-CO2e", "Machine gas energy active"),
    MACHINE_GAS_ENERGY_PASSIVE_CO2E("MachineGasEnergyPassiveCO2e", "MacGasEnPas-CO2e", "Machine gas energy passive"),
    MACHINE_GAS_ENERGY_CO2E("MachineGasEnergyCO2e", "MacGasEn-CO2e", "Machine gas energy"),
    MACHINE_VARIABLE_CO2E("MachineVariableCO2e", "MacVa-CO2e", "Machine variable"),
    MACHINE_CO2E("MachineCO2e", "Mac-CO2e", "Machine"),
    ROUGH_MACHINE_CO2E("RoughMachineCO2e", "RoughMach-CO2e", "Rough machine"),
    TOOL_CO2E("ToolCO2e", "To-CO2e", "Tool"),
    ROUGH_PROCESS_CO2E("RoughProcessCO2e", "RoPr-CO2e", "Rough manufacturing step"),
    DIRECT_MANUFACTURING_CO2E("DirectManufacturingCO2e", "DiMan-CO2e", "Direct manufacturing"),
    MANUFACTURING_SCRAP_CO2E("ManufacturingScrapCO2e", "ManScrap-CO2e", "Manufacturing scrap"),
    MANUFACTURING_OVERHEAD_CO2E("ManufacturingOverheadCO2e", "ManOv-CO2e", "Residual manufacturing overhead"),
    MANUFACTURING_AFTER_DIRECT_MANUFACTURING_CO2E(
        "ManufacturingAfterDirectManufacturingCO2e",
        "AftDiMan-CO2e",
        "Indirect manufacturing",
    ),
    MANUFACTURING_CO2E("ManufacturingCO2e", "Man-CO2e", "Manufacturing"),

    PRODUCTION_CO2E("ProductionCO2e", "Pro-CO2e", "Production"),

    // overheads
    OVERHEAD_CO2E_BASE_RM("OverheadCO2eBaseRm", "OvBaseRm-CO2e", "Overhead base raw material"),
    OVERHEAD_CO2E_BASE_PP("OverheadCO2eBasePp", "OvBasePp-CO2e", "Overhead base purchased parts"),
    OVERHEAD_CO2E_BASE_MFG("OverheadCO2eBaseMfg", "OvBaseMfg-CO2e", "Overhead base manufacturing"),

    // special direct co2
    DEVELOPMENT_CO2E("DevelopmentCO2e", "Dev-CO2e", "Development"),
    RAMP_UP_CO2E("RampUpCO2e", "RaUp-CO2e", "Ramp up"),
    PACKAGING_AND_CARRIER_CO2E("PackagingAndCarrierCO2e", "Pa&Ca-CO2e", "Packaging and carrier"),
    SPECIAL_DIRECT_CO2E("SpecialDirectCO2e", "SpeDi-CO2e", "Special direct emission"),

    // overheads after production
    SALES_AND_GENERAL_ADMINISTRATION_CO2E(
        "SalesAndGeneralAdministrationCO2e",
        "Sa&GeAd-CO2e",
        "Sales, general and administration",
    ),
    RESEARCH_AND_DEVELOPMENT_CO2E("ResearchAndDevelopmentCO2e", "R&D-CO2e", "Research and development"),
    BUSINESS_RISK_CO2E("BusinessRiskCO2e", "BuRi-CO2e", "Business risk"),
    OTHER_EXPENDITURES_CO2E_AFTER_P("OtherExpendituresCO2eAfterP", "OthExAP-CO2e", "Other expenditures after production"),
    DIRECT_OVERHEADS_CO2E_AFTER_P("DirectOverheadsCO2eAfterP", "DiOvAP-CO2e", "Direct overheads after production"),
    OVERHEADS_CO2E_AFTER_P("OverheadsCO2eAfterP", "OvAP-CO2e", "Overheads after production"),

    // profit
    BEFORE_PROFIT_CO2E("BeforeProfitCO2e", "BeProf-CO2e", "Before profit"),
    PROFIT_CO2E("ProfitCO2e", "Prof-CO2e", "Profit"),

    // terms of payment
    DISCOUNT_CO2E("DiscountCO2e", "Dis-CO2e", "Discount"),
    TERMS_OF_PAYMENT_CO2E("TermsOfPaymentCO2e", "ToP-CO2e", "Terms of payment"),

    // inco terms
    TRANSPORT_CO2E("TransportCO2e", "Trans-CO2e", "Transport"),
    CUSTOM_DUTY_CO2E("CustomsDutyCO2e", "CuDu-CO2e", "Customs duty"),
    INCO_TERMS_CO2E("IncoTermsCO2e", "Inc-CO2e", "Incoterms"),

    // sum of overheads
    INDIRECT_CO2E_AFTER_PRODUCTION("IndirectCO2eAfterProduction", "AP-CO2e", "Indirect after production"),

    // special direct co2e for invests
    DEVELOPMENT_CO2E_FOR_INVEST("DevelopmentCO2eForInvest", "Dev-Inv-CO2e", "Development for invest"),
    RAMP_UP_CO2E_FOR_INVEST("RampUpCO2eForInvest", "RampUp-Inv-CO2e", "Ramp up for invest"),
    PACKAGING_AND_CARRIER_CO2E_FOR_INVEST(
        "PackagingAndCarrierCO2eForInvest",
        "Pack&Carrier-Inv-CO2e",
        "Packaging and carrier for invest",
    ),
    TOTAL_CO2E_FOR_INVEST("TotalCO2eForInvest", "Tot-Inv-CO2e", "Investment"),

    SCOPE_1_CO2E("Scope1CO2e", "Sco1-CO2e", "Scope 1"),
    SCOPE_2_CO2E("Scope2CO2e", "Sco2-CO2e", "Scope 2"),
    SCOPE_3_CO2E("Scope3CO2e", "Sco3-CO2e", "Scope 3"),
    TOTAL_SCOPE_CO2E("TotalScopeCO2e", "Scope-CO2e", "Total scope"),
    TOTAL_CO2E("TotalCO2e", "Total-CO2e", "Total"),
}
