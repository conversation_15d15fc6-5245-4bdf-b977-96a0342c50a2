package com.nu.bom.core.manufacturing.extension

import com.nu.bom.core.manufacturing.annotations.Extends
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.entities.Labor
import com.nu.bom.core.manufacturing.entities.MachineMasterData
import com.nu.bom.core.manufacturing.entities.ManufacturingEntityExtension
import com.nu.bom.core.manufacturing.entities.RoughMachine
import com.nu.bom.core.manufacturing.entities.Setup
import com.nu.bom.core.manufacturing.entities.SpecialDirectCost
import com.nu.bom.core.manufacturing.entities.Tool
import com.nu.bom.core.manufacturing.entities.transport.TransportCalculatorDetailedCalculation
import com.nu.bom.core.manufacturing.entities.transport.TransportRoute
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.Text

@Extends(
    [
        Labor::class,
        Setup::class,
        Tool::class,
        MachineMasterData::class,
        RoughMachine::class,
        SpecialDirectCost::class,
        TransportCalculatorDetailedCalculation::class,
        TransportRoute::class,
    ],
    DIMENSION_EXTENSION_PACKAGE,
)
class ManufacturingSubobjectDimensionExtension(name: String) : ManufacturingEntityExtension(name) {
    @ReadOnly
    fun manufacturingDimension(
        @Parent(Entities.MANUFACTURING) dimension: Dimension,
    ) = dimension

    @ReadOnly
    fun manufacturingQuantityUnit(
        @Parent(Entities.MANUFACTURING) quantityUnit: Text,
    ) = quantityUnit

    @ReadOnly
    fun manufacturingCostUnit(
        @Parent(Entities.MANUFACTURING) costUnit: Text,
    ) = costUnit
}
