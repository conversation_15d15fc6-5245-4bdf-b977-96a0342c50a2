package com.nu.bom.core.messagebus

import org.slf4j.LoggerFactory
import reactor.core.publisher.DirectProcessor
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.core.scheduler.Scheduler
import reactor.core.scheduler.Schedulers
import java.util.concurrent.ConcurrentHashMap
import kotlin.reflect.KClass

class InMemoryMessageBusService(
    // bind routingKey to queueName
    private val routeBindings: Map<String, String> = emptyMap(),
) : MessageBusService {
    // queueName to queue
    private val queuedMessages = ConcurrentHashMap<String, DirectProcessor<Any>>()

    // todo: check if this property can be removed
    private val sender = Schedulers.newBoundedElastic(4, 10, "sender-pool")
    private val consumer = Schedulers.newSingle("consumer")

    override fun <T : Any> send(
        message: T,
        routingKey: String,
    ): Mono<T> {
        val queueName = routeBindings.getOrDefault(routingKey, routingKey)

        return sendMessageToQueue(queueName = queueName, message = message, routingKey = routingKey)
    }

    override fun <T : Any> sendGlobalMessage(
        message: T,
        routingKey: String,
    ): Mono<T> {
        val queueName = routeBindings.getOrDefault(routingKey, routingKey)

        return sendMessageToQueue(queueName = queueName, message = message, routingKey = routingKey)
    }

    private fun <T : Any> sendMessageToQueue(
        queueName: String,
        message: T,
        routingKey: String,
    ): Mono<T> {
        LOG.trace("Resolved queue=$queueName for routingKey=$routingKey")

        val processor = queuedMessages.getOrPut(queueName) { DirectProcessor.create() }

        return Mono.fromCallable {
            addMessageToQueue(
                message = message,
                queueName = queueName,
                processor = processor,
                routingKey = routingKey,
            )
        }.thenReturn(message)
    }

    private fun addMessageToQueue(
        message: Any,
        queueName: String,
        processor: DirectProcessor<Any>,
        routingKey: String,
    ) {
        processor.onNext(message)
        LOG.trace("Sent message with routingKey={} to queue={}, message={}", routingKey, queueName, message)
    }

    private fun <T : Any> consume(
        queue: String,
        clazz: KClass<T>,
        scheduler: Scheduler,
    ): Flux<T> {
        LOG.trace("consuming on {} for {}", queue, clazz)
        return queuedMessages.getOrPut(
            queue,
            {
                DirectProcessor.create<Any>()
            },
        ).cast(clazz.java)
            .publishOn(scheduler)
    }

    override fun <T : Any> consume(
        routingKey: String,
        clazz: KClass<T>,
    ): Flux<T> {
        val queueName = routeBindings.getOrDefault(routingKey, routingKey)
        LOG.trace("Resolved queue=$queueName for routingKey=$routingKey")

        return consume(queue = queueName, clazz = clazz, scheduler = consumer)
    }

    override fun <T : Any> uniqueConsumer(
        routingKey: String,
        clazz: KClass<T>,
    ): Flux<T> = consume(routingKey, clazz)

    /** For tests to cleanup */
    override fun clear() {
        synchronized(this) {
            queuedMessages.values.forEach {
                it.onComplete()
            }
            queuedMessages.clear()
            LOG.trace("Closed all queues")
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(InMemoryMessageBusService::class.java)
    }
}
