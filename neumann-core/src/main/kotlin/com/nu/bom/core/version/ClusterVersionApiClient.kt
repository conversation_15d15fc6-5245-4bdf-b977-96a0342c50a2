package com.nu.bom.core.version

import com.nu.bom.core.version.gitlab.MASTER
import com.nu.bom.core.version.gitlab.PipelineService
import com.nu.bom.core.version.gitlab.ServiceToProjectMapping
import com.nu.http.EnvironmentNameSupplier
import com.nu.k8s.NuKubernetesDiscoveryClient
import org.slf4j.LoggerFactory
import org.springframework.cloud.client.ServiceInstance
import org.springframework.cloud.kubernetes.fabric8.Fabric8PodUtils
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono
import java.util.SortedMap

private const val VERSION_ANNOTATION_KEY = "tset.com/platform-version"
private const val META_VERSION = "app.kubernetes.io/version"
private const val FEATURE = "-feature-"
const val DEVELOP = "develop"

private val log = LoggerFactory.getLogger(ClusterVersionApiClient::class.java)

class ClusterVersionApiClient(
    private val podUtils: Fabric8PodUtils,
    private val discoveryClient: NuKubernetesDiscoveryClient,
    private val pipelineService: PipelineService,
    private val environmentNameSupplier: EnvironmentNameSupplier,
) : VersionApiClient {
    override fun getPlatformVersion(): Mono<PlatformVersion> {
        val namespace = podUtils.currentPod().get().metadata.namespace
        val currentNamespace = discoveryClient.client.namespaces().withName(namespace).get()
        return if (DEVELOP == namespace) {
            PlatformVersion(if (environment() == null) "develop" else "feature").toMono()
        } else {
            log.info("Requesting platform version in namespace: $namespace")
            PlatformVersion(
                release = currentNamespace.metadata.annotations.getOrDefault(VERSION_ANNOTATION_KEY, "v0.0-000"),
            ).toMono()
        }
    }

    override fun getServicesVersion(): Mono<List<FeatureVersion>> =
        serviceMap().map { s ->
            val environment = environment()
            val fallbackEnvironment = fallback()
            s.keys.map { serviceName ->
                val featureServiceName = "$serviceName-feature-$environment"

                fun retrieveFeature() =
                    serviceInstance(featureServiceName)
                        ?.let { instance ->
                            FeatureVersion(
                                name = serviceName,
                                feature = true,
                                serviceInstanceName = featureServiceName,
                                release = instance.version,
                                pipelineRunning = activePipelineExists(serviceName, "feature/$environment"),
                            )
                        }

                val fallbackFeatureServiceName = "$serviceName-feature-$fallbackEnvironment"

                fun retrieveFallback() =
                    serviceInstance(fallbackFeatureServiceName)
                        ?.let { instance ->
                            FeatureVersion(
                                name = serviceName,
                                feature = true,
                                serviceInstanceName = fallbackFeatureServiceName,
                                release = instance.version,
                                pipelineRunning = activePipelineExists(serviceName, "feature/$fallbackEnvironment"),
                            )
                        }

                fun retrieveBase() =
                    serviceInstance(serviceName)
                        ?.let { instance ->
                            FeatureVersion(
                                name = serviceName,
                                feature = false,
                                serviceInstanceName = serviceName,
                                release = instance.version,
                                pipelineRunning = activePipelineExists(serviceName, MASTER),
                            )
                        }

                fun notFound() =
                    FeatureVersion(
                        name = serviceName,
                        feature = false,
                        serviceInstanceName = "NOT FOUND",
                        release = "did not find at all",
                    )

                val instances = s[serviceName] ?: emptyList()
                when {
                    instances.contains(featureServiceName) -> retrieveFeature() ?: retrieveFallback() ?: retrieveBase() ?: notFound()
                    instances.contains(fallbackFeatureServiceName) -> retrieveFallback() ?: retrieveBase() ?: notFound()
                    instances.contains(serviceName) -> retrieveBase() ?: notFound()
                    else -> notFound()
                }.also {
                    log.info("Retrieved Service $it")
                }
            }
        }

    private fun serviceMap(): Mono<SortedMap<String, List<String>>> =
        Mono.create { sink ->
            val serviceMap =
                discoveryClient.services.also { log.info("${it.size} services fetched") }.groupBy { serviceName ->
                    serviceName.nameByServiceId()
                }.toSortedMap()
            log.info("Found services: ${serviceMap.keys.joinToString()}")
            sink.success(serviceMap)
        }

    private fun String.nameByServiceId(): String =
        when (this.contains(FEATURE)) {
            false -> this
            true -> this.substring(0, this.indexOf(FEATURE))
        }

    private fun serviceInstance(name: String): ServiceInstance? = discoveryClient.getInstances(name).firstOrNull()

    internal fun environment(): String? = environmentNameSupplier.getEnv()

    internal fun fallback(): String? = environmentNameSupplier.getFallback()

    private val ServiceInstance.version: String get() = this.metadata[META_VERSION] ?: "invalid tag"

    private fun activePipelineExists(
        serviceName: String,
        ref: String,
    ): Boolean {
        val project: Long? = ServiceToProjectMapping.projectToServiceMap[serviceName]
        return if (project == null) {
            log.debug("Gitlab project has not been found for the service: $serviceName")
            false
        } else {
            log.debug("Gitlab project Id: $project corresponds to the service $serviceName")
            return pipelineService.activePipelineExists(project, ref)
        }
    }
}
