package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.manufacturing.annotations.Units
import java.math.BigDecimal

enum class RevolutionUnits(override val baseFactor: BigDecimal) : TypeUnit {
    PER_MIN(BigDecimal.ONE),
    PER_SEC(60.0.toBigDecimal()),
    ;

    override val type = TypeUnits.REVOLUTION
    override val hideInDropdown = false
}

@Units(RevolutionUnits::class)
class Revolution(res: BigDecimal, unit: RevolutionUnits) : NumericFieldResultWithUnit<Revolution, RevolutionUnits>(res, unit) {
    constructor(res: Double, unit: RevolutionUnits) : this(res.toBigDecimal(), unit)
    constructor(res: String, unit: String) : this(BigDecimal(res), RevolutionUnits.valueOf(unit))
    constructor(res: BigDecimal, unit: String) : this(res, RevolutionUnits.valueOf(unit))

    constructor(res: Num) : this(BigDecimal(res.toString()), RevolutionUnits.PER_MIN)

    val inPerMin: BigDecimal
        get() {
            return to(RevolutionUnits.PER_MIN)
        }

    val inPerSec: BigDecimal
        get() {
            return to(RevolutionUnits.PER_SEC)
        }
}
