@file:Suppress("unused")

package com.nu.bom.core.manufacturing.commercialcalculation.enums

import com.nu.bom.core.manufacturing.enums.Entities

/***
 * The material class introduces a classification independent of the entity type. The commercial
 * calculation logic is only based on this type and should never use the information of a real
 * entity class, which should be kept internal. The material class serves mainly to control
 * the transfer of material costs to various specific material types, e.g. RawMaterialCosts
 *
 * With the introduction of a configurable dynamic classification for materials, this enum will
 * be replaced with a complete material classification and can be removed.
 */
enum class MaterialClass {
    PURCHASED_PART,
    CONSUMABLE,
    RAW_MATERIAL,
    ELECTRONIC_COMPONENT,
    MANUFACTURED_PART,
    ;

    fun toMaterialEntityClassForMaterialTable(): Entities =
        when (this) {
            CONSUMABLE -> Entities.CONSUMABLE
            ELECTRONIC_COMPONENT -> Entities.C_PART
            RAW_MATERIAL -> Entities.MATERIAL
            MANUFACTURED_PART -> Entities.BOM_ENTRY
            PURCHASED_PART -> Entities.BOM_ENTRY
        }

    companion object {
        fun fromLeafMaterialEntityClass(entity: Entities): MaterialClass? =
            when (entity) {
                Entities.CONSUMABLE -> CONSUMABLE
                Entities.C_PART -> ELECTRONIC_COMPONENT
                Entities.MATERIAL -> RAW_MATERIAL
                else -> null
            }
    }
}
