package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.manufacturing.annotations.Units
import java.math.BigDecimal

enum class DensityUnits(override val baseFactor: BigDecimal) : TypeUnit {
    GRAM_PER_CCM((1000.0).toBigDecimal()),
    KILOGRAM_PER_CM(BigDecimal.ONE),

    @Deprecated("Use GRAM_PER_CCM")
    GRAMM_PER_CCM((1000.0).toBigDecimal()),
    @Deprecated("Use KILOGRAM_PER_CM")
    KILOGRAMM_PER_CM(BigDecimal.ONE),
    ;

    override val hideInDropdown = false
    override val type = TypeUnits.DENSITY
}

@Units(DensityUnits::class)
class Density(res: BigDecimal, unit: DensityUnits) : NumericFieldResultWithUnit<Density, DensityUnits>(res, unit) {
    constructor(res: Double, unit: DensityUnits) : this(res.toBigDecimal(), unit)
    constructor(res: String, unit: String) : this(BigDecimal(res), DensityUnits.valueOf(unit))
    constructor(res: BigDecimal, unit: String) : this(res, DensityUnits.valueOf(unit))

    val inKgPerCm: BigDecimal
        get() {
            return to(DensityUnits.KILOGRAM_PER_CM)
        }
    val inGPerCcm: BigDecimal
        get() {
            return to(DensityUnits.GRAM_PER_CCM)
        }
}
