package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.utils.toUpperSnakeCase

class PlasticCoatingPostProcessingType(res: Selection) : SelectEnumFieldResult<PlasticCoatingPostProcessingType.Selection, PlasticCoatingPostProcessingType>(res) {

    enum class Selection {
        NO_TYPE,
        OVER_DRYING,
        IR_CURING
    }

    companion object {

        val NO_TYPE = PlasticCoatingPostProcessingType(Selection.NO_TYPE)
        val OVER_DRYING = PlasticCoatingPostProcessingType(Selection.OVER_DRYING)
        val IR_CURING = PlasticCoatingPostProcessingType(Selection.IR_CURING)

        fun valueOf(name: String): PlasticCoatingPostProcessingType {
            return PlasticCoatingPostProcessingType(Selection.valueOf(name.toUpperSnakeCase()))
        }
    }
}
