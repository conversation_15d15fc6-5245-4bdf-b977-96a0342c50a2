package com.nu.bom.core.model

import com.querydsl.core.annotations.QueryEntity
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document

@Document(collection = ArchivedSnapshot.COLLECTION_NAME)
@QueryEntity
data class ArchivedSnapshot(
    @Id
    val _id: SnapshotId,
    val previous: SnapshotId? = null,
    val projectId: ProjectId,
    val rootOf: BomId?,
    val bomNodeId: BomNodeId
) {

    companion object {
        const val COLLECTION_NAME = "ArchivedSnapshots"
    }
}
