package com.nu.bom.core.manufacturing.extension

import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.Extends
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.entities.ComponentMaterial
import com.nu.bom.core.manufacturing.entities.ManufacturingEntityExtension
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.manufacturing.fieldTypes.EmissionUnits
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import java.math.BigDecimal

@Extends([ComponentMaterial::class], CO2_EXTENSION_PACKAGE)
class ComponentMaterialCO2Extension(
    name: String,
) : ManufacturingEntityExtension(name) {
    @Input
    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun cO2PerUnit(materialBaseCO2: Emission?): Emission = materialBaseCO2 ?: Emission(BigDecimal.ZERO, EmissionUnits.KILOGRAM_CO2E)

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun cO2PerPartMaterial(cO2PerUnit: Emission) = cO2PerUnit

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun cO2PerPart(cO2PerPartMaterial: Emission) = cO2PerPartMaterial

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun weightedCO2PerUnit(
        weightedRatio: Rate,
        cO2PerUnit: Emission,
    ) = cO2PerUnit * weightedRatio
}
