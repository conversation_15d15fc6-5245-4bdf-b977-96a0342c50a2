package com.nu.bom.core.manufacturing.fieldTypes

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonValue
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import org.bson.types.ObjectId
import org.springframework.data.annotation.Transient

class InternalRef(val type: RefType, val id: ObjectId?) {
    constructor(id: ObjectId) : this(RefType.ONE, id)

    @JsonCreator
    constructor(stringRep: String) : this(
        if (stringRep == RefType.NONE.toString()) RefType.NONE else RefType.ONE,
        if (stringRep != RefType.NONE.toString()) ObjectId(stringRep) else null,
    )

    init {
        require(!(id == null && type != RefType.NONE)) { "Cannot init Ref without ID and type not set to none" }

        require(!(type == RefType.NONE && id != null)) { "Cannot init Ref which is set to NONE but has ID" }
    }

    enum class RefType {
        NONE,
        ONE,
    }

    companion object {
        fun none() = InternalRef(RefType.NONE, null)
    }

    @JsonValue
    override fun toString(): String {
        return if (type == RefType.NONE) {
            RefType.NONE.toString()
        } else {
            id?.toHexString() ?: throw IllegalArgumentException("Reftype Not set to NONE but Id Missing")
        }
    }

    override fun equals(other: Any?): Boolean =
        if (other is InternalRef) {
            this.type == other.type && this.id == other.id
        } else {
            super.equals(other)
        }

    override fun hashCode(): Int = type.hashCode() xor id.hashCode()
}

class EntityRef(res: InternalRef) : FieldResult<InternalRef, EntityRef>(res) {
    constructor(stringRep: String) : this(
        InternalRef(
            stringRep.let {
                if (it.startsWith('"') && it.endsWith('"')) {
                    it.trimStart('"').trimEnd('"')
                } else {
                    it
                }
            },
        ),
    )
    constructor(objectId: ObjectId) : this(InternalRef(objectId))
    constructor() : this(InternalRef.none())

    @Transient
    @JsonIgnore
    var entity: ManufacturingEntity? = null

    override fun dbValue(): String {
        return super.res.toString()
    }
}
