package com.nu.bom.core.service.wizard.steps.fti

import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.model.Wizard
import com.nu.bom.core.repository.WizardRepository
import com.nu.bom.core.service.wizard.steps.WizardFieldStep
import com.nu.bom.core.service.wizard.steps.WizardFieldStepManager
import com.nu.bom.core.user.AccessCheck
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono

private const val WIZARD_ID = 820

open class WizardFtiProcFieldStep(
    fields: List<FieldParameter> = listOf(),
) : WizardFieldStep(fields, WIZARD_ID)

@Service
final class WizardFtiProcStepManager(
    wizardRepository: WizardRepository,
) : WizardFieldStepManager<WizardFtiProcFieldStep>(wizardRepository) {
    override fun createStepWithFields(
        fields: List<FieldParameter>,
        accessCheck: Access<PERSON>he<PERSON>,
        wizard: Wizard,
    ): Mono<WizardFtiProcFieldStep> =
        Mono.just(
            WizardFtiProcFieldStep(
                fields,
            ),
        )

    override fun beforePrecompute(
        accessCheck: AccessCheck,
        wizard: Wizard,
        entity: ManufacturingEntity,
        wizardStep: WizardFieldStep,
    ): Mono<ManufacturingEntity> {
        // Enable process calculation
        entity.addOrUpdateField("processInitiated") { Bool(true) }
        return Mono.just(entity)
    }

    override val stepType get() = WizardFtiProcFieldStep::class
}
