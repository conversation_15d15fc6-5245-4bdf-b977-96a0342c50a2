package com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure

import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalConfigurationOperation

open class CostOperationConfigurationV5(
    operations: List<ExternalConfigurationOperation>,
    val procurementTypeConfiguration: ProcurementTypeConfiguration,
) : CostOperationConfigurationV4(operations) {
    init {
        verifyProcurementConfiguration(procurementTypeConfiguration)
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        return contentEquals(other as CostOperationConfigurationV5)
    }

    override fun hashCode() = operations.hashCode()

    protected fun contentEquals(other: CostOperationConfigurationV5): Boolean =
        super.contentEquals(other) && procurementTypeConfiguration == other.procurementTypeConfiguration
}
