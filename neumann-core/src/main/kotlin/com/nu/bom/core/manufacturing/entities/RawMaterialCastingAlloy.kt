package com.nu.bom.core.manufacturing.entities

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.FieldName
import com.nu.bom.core.manufacturing.annotations.FilteredChildren
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MasterDataType
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.Technologies
import com.nu.bom.core.manufacturing.defaults.NullProvider
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.CastingAlloyMaterialGroup
import com.nu.bom.core.manufacturing.fieldTypes.Diffusivity
import com.nu.bom.core.manufacturing.fieldTypes.DiffusivityUnits
import com.nu.bom.core.manufacturing.fieldTypes.MaterialSubstances
import com.nu.bom.core.manufacturing.fieldTypes.SheetMaterialGroup
import com.nu.bom.core.manufacturing.fieldTypes.Temperature
import com.nu.bom.core.manufacturing.fieldTypes.TemperatureUnits
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.utils.findResultByTextAttributeSafe
import org.bson.types.ObjectId
import reactor.core.publisher.Mono

@EntityType(Entities.MATERIAL)
@MasterDataType(com.nu.bom.core.manufacturing.enums.MasterDataType.RAW_MATERIAL_CASTING_ALLOY)
@Technologies([Model.DCA, Model.CHILL, Model.PREC, Model.SAND, Model.VPREC])
class RawMaterialCastingAlloy(
    name: String,
) : ManufacturingEntity(name) {
    companion object {
        private const val MATERIAL_GROUP_MASTER_DATA_FIELD = "#classification_field_tset-ref-field-materialGroup"
        private const val AGEING_TIME_MASTER_DATA_FIELD = "#classification_field_tset-ref-field-ageingTime"
        private const val TEMPERING_SOAKING_TIME_MASTER_DATA_FIELD =
            "#classification_field_tset-ref-field-temperingSoakingTime"
        private const val SOLUTION_ANNEALING_TIME_MASTER_DATA_FIELD =
            "#classification_field_tset-ref-field-solutionAnnealingTime"
        private const val SOAKING_TIME_MASTER_DATA_FIELD = "#classification_field_tset-ref-field-soakingTime"
        private const val LIQUIDUS_TEMPERATURE_MASTER_DATA_FIELD =
            "#classification_field_tset-ref-field-liquidusTemperature"
        private const val MAX_CASTING_TEMPERATURE_MASTER_DATA_FIELD =
            "#classification_field_tset-ref-field-maxCastingTemperature"
        private const val MAX_CHILL_CASTING_TEMPERATURE_MASTER_DATA_FIELD =
            "#classification_field_tset-ref-field-maxChillCastingTemperature"
        private const val MIN_CASTING_TEMPERATURE_MASTER_DATA_FIELD =
            "#classification_field_tset-ref-field-minCastingTemperature"
        private const val MIN_CHILL_CASTING_TEMPERATURE_MASTER_DATA_FIELD =
            "#classification_field_tset-ref-field-minChillCastingTemperature"
        private const val SPECIFIC_THERMAL_CAPACITY_MASTER_DATA_FIELD =
            "#classification_field_tset-ref-field-specificThermalCapacity"
        private const val HAS_MAGNESIUM_MASTER_DATA_FIELD =
            "#classification_field_tset-ref-field-hasMagnesium"

        // classificationFields used in the lazy migration
        val classificationFields =
            mapOf(
                "materialGroup" to MATERIAL_GROUP_MASTER_DATA_FIELD,
                "ageingTime" to AGEING_TIME_MASTER_DATA_FIELD,
                "temperingSoakingTime" to TEMPERING_SOAKING_TIME_MASTER_DATA_FIELD,
                "solutionAnnealingTime" to SOLUTION_ANNEALING_TIME_MASTER_DATA_FIELD,
                "soakingTime" to SOAKING_TIME_MASTER_DATA_FIELD,
                "liquidusTemperature" to LIQUIDUS_TEMPERATURE_MASTER_DATA_FIELD,
                "maxCastingTemperature" to MAX_CASTING_TEMPERATURE_MASTER_DATA_FIELD,
                "maxChillCastingTemperature" to MAX_CHILL_CASTING_TEMPERATURE_MASTER_DATA_FIELD,
                "minCastingTemperature" to MIN_CASTING_TEMPERATURE_MASTER_DATA_FIELD,
                "minChillCastingTemperature" to MIN_CHILL_CASTING_TEMPERATURE_MASTER_DATA_FIELD,
                "specificThermalCapacity" to SPECIFIC_THERMAL_CAPACITY_MASTER_DATA_FIELD,
            )
    }

    override val extends = RawMaterial(name)

    @Input
    fun designation(materialBaseDisplayDesignation: Text): Text = materialBaseDisplayDesignation

    @Input
    fun materialGroup(headerKey: Text?): Mono<Text> =
        headerKey?.let {
            services
                .findMaterialGroupOrNull(
                    accessCheck = calculationContext().accessCheck,
                    materialKey = headerKey.res,
                ).switchIfEmpty(Mono.just(Text("N4")))
        } ?: Mono.just(Text("N4"))

    fun materialGroupCasting(materialGroup: Text) = CastingAlloyMaterialGroup(materialGroup)

    fun materialGroupSheet(): SheetMaterialGroup? = null

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.CELSIUS)
    fun minCastingTemperature(
        headerKey: Text?,
        @Default(NullProvider::class)
        @FieldName(MIN_CASTING_TEMPERATURE_MASTER_DATA_FIELD)
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        minCastingTemperatureFromClassification: Map<ObjectId, Temperature?>?,
        @Default(NullProvider::class)
        @FieldName("headerKey")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToHeaderKeyFromClassification: Map<ObjectId, Text>?,
    ): Temperature =
        findResultByTextAttributeSafe(
            headerKey,
            minCastingTemperatureFromClassification,
            mapIdToHeaderKeyFromClassification,
            Temperature(650.0, TemperatureUnits.CELSIUS),
        )

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.CELSIUS)
    fun maxCastingTemperature(
        headerKey: Text?,
        @Default(NullProvider::class)
        @FieldName(MAX_CASTING_TEMPERATURE_MASTER_DATA_FIELD)
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        maxCastingTemperatureFromClassification: Map<ObjectId, Temperature?>?,
        @Default(NullProvider::class)
        @FieldName("headerKey")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToHeaderKeyFromClassification: Map<ObjectId, Text>?,
    ): Temperature =
        findResultByTextAttributeSafe(
            headerKey,
            maxCastingTemperatureFromClassification,
            mapIdToHeaderKeyFromClassification,
            Temperature(800.0, TemperatureUnits.CELSIUS),
        )

    @Input
    fun materialSubstances(): MaterialSubstances = MaterialSubstances(emptyList())

    @Input
    @DefaultUnit(DefaultUnit.CELSIUS)
    fun liquidusTemperature(
        headerKey: Text?,
        @Default(NullProvider::class)
        @FieldName(LIQUIDUS_TEMPERATURE_MASTER_DATA_FIELD)
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        liquidusTemperatureFromClassification: Map<ObjectId, Temperature?>?,
        @Default(NullProvider::class)
        @FieldName("headerKey")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToHeaderKeyFromClassification: Map<ObjectId, Text>?,
    ): Temperature =
        findResultByTextAttributeSafe(
            headerKey,
            liquidusTemperatureFromClassification,
            mapIdToHeaderKeyFromClassification,
            Temperature(600.0, TemperatureUnits.CELSIUS),
        )

    @Input
    @DefaultUnit(DefaultUnit.QMM_PER_SECONDS)
    fun specificThermalCapacity(
        headerKey: Text?,
        @Default(NullProvider::class)
        @FieldName(SPECIFIC_THERMAL_CAPACITY_MASTER_DATA_FIELD)
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        specificThermalCapacityFromClassification: Map<ObjectId, Diffusivity?>?,
        @Default(NullProvider::class)
        @FieldName("headerKey")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToHeaderKeyFromClassification: Map<ObjectId, Text>?,
    ): Diffusivity =
        findResultByTextAttributeSafe(
            headerKey,
            specificThermalCapacityFromClassification,
            mapIdToHeaderKeyFromClassification,
            Diffusivity(0.92, DiffusivityUnits.QMM_PER_SECONDS),
        )

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.CELSIUS)
    fun minChillCastingTemperature(
        headerKey: Text?,
        @Default(NullProvider::class)
        @FieldName(MIN_CHILL_CASTING_TEMPERATURE_MASTER_DATA_FIELD)
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        minChillCastingTemperatureFromClassification: Map<ObjectId, Temperature?>?,
        @Default(NullProvider::class)
        @FieldName("headerKey")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToHeaderKeyFromClassification: Map<ObjectId, Text>?,
    ): Temperature =
        findResultByTextAttributeSafe(
            headerKey,
            minChillCastingTemperatureFromClassification,
            mapIdToHeaderKeyFromClassification,
            Temperature(680.0, TemperatureUnits.CELSIUS),
        )

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.CELSIUS)
    fun maxChillCastingTemperature(
        headerKey: Text?,
        @Default(NullProvider::class)
        @FieldName(MAX_CHILL_CASTING_TEMPERATURE_MASTER_DATA_FIELD)
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        maxChillCastingTemperatureFromClassification: Map<ObjectId, Temperature?>?,
        @Default(NullProvider::class)
        @FieldName("headerKey")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToHeaderKeyFromClassification: Map<ObjectId, Text>?,
    ): Temperature =
        findResultByTextAttributeSafe(
            headerKey,
            maxChillCastingTemperatureFromClassification,
            mapIdToHeaderKeyFromClassification,
            Temperature(730.0, TemperatureUnits.CELSIUS),
        )

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.HOUR)
    fun soakingTime(
        headerKey: Text?,
        @Default(NullProvider::class)
        @FieldName(SOAKING_TIME_MASTER_DATA_FIELD)
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        soakingTimeFromClassification: Map<ObjectId, Time?>?,
        @Default(NullProvider::class)
        @FieldName("headerKey")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToHeaderKeyFromClassification: Map<ObjectId, Text>?,
    ): Time =
        findResultByTextAttributeSafe(
            headerKey,
            soakingTimeFromClassification,
            mapIdToHeaderKeyFromClassification,
            Time(3.5, TimeUnits.HOUR),
        )

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.HOUR)
    fun temperingSoakingTime(
        headerKey: Text?,
        @Default(NullProvider::class)
        @FieldName(TEMPERING_SOAKING_TIME_MASTER_DATA_FIELD)
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        temperingSoakingTimeFromClassification: Map<ObjectId, Time?>?,
        @Default(NullProvider::class)
        @FieldName("headerKey")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToHeaderKeyFromClassification: Map<ObjectId, Text>?,
    ): Time =
        findResultByTextAttributeSafe(
            headerKey,
            temperingSoakingTimeFromClassification,
            mapIdToHeaderKeyFromClassification,
            Time(3.5, TimeUnits.HOUR),
        )

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.HOUR)
    fun solutionAnnealingTime(
        headerKey: Text?,
        @Default(NullProvider::class)
        @FieldName(SOLUTION_ANNEALING_TIME_MASTER_DATA_FIELD)
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        solutionAnnealingTimeFromClassification: Map<ObjectId, Time?>?,
        @Default(NullProvider::class)
        @FieldName("headerKey")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToHeaderKeyFromClassification: Map<ObjectId, Text>?,
    ): Time =
        findResultByTextAttributeSafe(
            headerKey,
            solutionAnnealingTimeFromClassification,
            mapIdToHeaderKeyFromClassification,
            Time(4.0, TimeUnits.HOUR),
        )

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.HOUR)
    fun ageingTime(
        headerKey: Text?,
        @Default(NullProvider::class)
        @FieldName(AGEING_TIME_MASTER_DATA_FIELD)
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        ageingTimeFromClassification: Map<ObjectId, Time?>?,
        @Default(NullProvider::class)
        @FieldName("headerKey")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToHeaderKeyFromClassification: Map<ObjectId, Text>?,
    ): Time =
        findResultByTextAttributeSafe(
            headerKey,
            ageingTimeFromClassification,
            mapIdToHeaderKeyFromClassification,
            Time(5.0, TimeUnits.HOUR),
        )

    @Input
    @ReadOnly
    @DefaultUnit(DefaultUnit.HOUR)
    fun hasMagnesium(
        headerKey: Text?,
        @Default(NullProvider::class)
        @FieldName(HAS_MAGNESIUM_MASTER_DATA_FIELD)
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        hasMagnesiumFromClassification: Map<ObjectId, Bool?>?,
        @Default(NullProvider::class)
        @FieldName("headerKey")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToHeaderKeyFromClassification: Map<ObjectId, Text>?,
    ): Bool =
        findResultByTextAttributeSafe(
            headerKey,
            hasMagnesiumFromClassification,
            mapIdToHeaderKeyFromClassification,
            Bool.FALSE,
        )
}
