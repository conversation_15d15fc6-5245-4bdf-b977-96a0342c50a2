package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import org.springframework.stereotype.Service

@Service
class ManufacturingConfigurationElectronicComponentMigration : ManufacturingModelEntityMapper {
    override val changeSetId: MigrationChangeSetId
        get() = MigrationChangeSetId("2025-04-01-manufacturing-configuration-elco-migration")

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity =
        entity.copyAll(
            fieldWithResults =
                entity.fieldWithResults +
                    mapOf(
                        Manufacturing::electronicComponentClassification.name to
                            FieldResultModel(
                                version = entity.version,
                                newVersion = entity.version,
                                type = Text::class.java.simpleName,
                                value = Text("tset.ref.classification.electronic-component"),
                                source = FieldResult.SOURCE.C.name,
                            ),
                    ),
            initialFieldWithResults = entity.initialFieldWithResults,
        )
}
