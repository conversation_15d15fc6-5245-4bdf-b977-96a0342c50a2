package com.nu.bom.core.manufacturing.fieldTypes

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.module.kotlin.readValue
import com.nu.bom.core.config.JacksonConfig
import java.math.BigDecimal
import com.tset.core.service.domain.Currency as ApiCurrency

@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, include = JsonTypeInfo.As.PROPERTY, property = "@class")
@JsonIgnoreProperties(value = [ "asPair"])
class ExchangeRateTuple(res: ExchangeRateTupleInt) : FieldResult<ExchangeRateTuple.ExchangeRateTupleInt, ExchangeRateTuple>(res) {
    @JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, include = JsonTypeInfo.As.PROPERTY, property = "@class")
    data class ExchangeRateTupleInt(
        val first: ApiCurrency?,
        val second: BigDecimal?,
    )

    constructor(value: String) : this(JacksonConfig.staticMapper.readValue<ExchangeRateTupleInt>(value))

    constructor(t1: Currency?, t2: Rate?) : this(ExchangeRateTupleInt(ApiCurrency(t1?.res!!), t2?.res))

    override fun dbValue(): String {
        return JacksonConfig.staticMapper.writeValueAsString(res)
    }
}
