package com.nu.bom.core.manufacturing.entities.masterdata.overheads

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Nocalc
import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.CalculationOperationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.IndirectOperationWithRateOrigin
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.masterdata.MasterdataEntity
import com.nu.bom.core.manufacturing.entities.masterdata.MasterdataParentEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.Text
import reactor.core.publisher.Flux
import reactor.kotlin.core.publisher.toFlux

@EntityType(Entities.NONE)
open class BaseOverheadEntity(name: String) : MasterdataParentEntity(name) {
    @Nocalc
    protected inline fun <reified T : FieldResultStar> createEntitiesPerIndirectionOperation(
        costCalculationConfiguration: CalculationOperationConfiguration,
        manufacturingStepTypes: List<Text>,
        entityType: Entities,
    ): Flux<ManufacturingEntity> {
        val setStepTypes = manufacturingStepTypes.toSet()
        return costCalculationConfiguration.getOperations<IndirectOperationWithRateOrigin>()
            .filter { it.rateOrTimeFieldClass == T::class }
            .flatMap { op ->
                op.lookUpKey?.masterdataHeaderKey?.let { headerKey ->
                    if (op.origin == AggregationLevel.MANUFACTURING_STEP) {
                        // add standard overhead entity + one RMOC overhead entity per manufacturingStepType
                        setStepTypes.map {
                            createOneOverheadEntity(
                                op.destinationElementKey,
                                headerKey,
                                entityType,
                                manufacturingStepType = it,
                            )
                        }
                    } else {
                        // the aggregation level is not step => create standard overhead entity
                        listOf(createOneOverheadEntity(op.destinationElementKey, headerKey, entityType))
                    }
                } ?: listOf()
            }.toFlux()
    }

    @Nocalc
    protected fun createOneOverheadEntity(
        entityName: String,
        headerKey: String,
        entityType: Entities,
        manufacturingStepType: Text? = null,
    ): ManufacturingEntity {
        val fields =
            listOfNotNull(
                MasterdataEntity::headerKey.name to Text(headerKey),
                manufacturingStepType?.let { MasterdataOverheadEntity::manufacturingStepType.name to manufacturingStepType },
            ).toMap()
        return services.createEntity(
            name = entityName,
            entityType = entityType,
            fields = fields,
            version = this.version,
            entityRef = entityName + (manufacturingStepType?.res?.let { ".$it" } ?: ""),
        )
    }
}
