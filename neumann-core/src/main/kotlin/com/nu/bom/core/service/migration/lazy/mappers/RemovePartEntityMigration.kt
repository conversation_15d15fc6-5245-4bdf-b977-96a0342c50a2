package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import org.springframework.stereotype.Service

@Service
class RemovePartEntityMigration : ManufacturingModelEntityMapper {
    override val changeSetId = MigrationChangeSetId("2025-05-21-remove-part-entity")

    override fun map(entity: ManufacturingModelEntity) = removePartChildrenRecursive(entity)

    private fun removePartChildrenRecursive(entity: ManufacturingModelEntity): ManufacturingModelEntity =
        entity.copyAll(
            children =
                entity.children
                    .filterNot { it.type == "PART" }
                    .map { removePartChildrenRecursive(it) },
        )
}
