package com.nu.bom.core.manufacturing.entities

import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.StaticDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.TranslationLabel
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.StaticUnitOverride
import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.AreaUnits
import com.nu.bom.core.manufacturing.fieldTypes.Currency
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.Power
import com.nu.bom.core.manufacturing.fieldTypes.PowerUnits
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.Volume
import com.nu.bom.core.manufacturing.fieldTypes.VolumeUnits
import java.math.BigDecimal

@EntityType(Entities.MACHINE, userCreatable = true)
open class RoughMachine(name: String) : ManufacturingEntity(name) {
    override val extends: ManufacturingEntity = BaseEntityFields(name)

    @Input
    @MandatoryForEntity(index = 1)
    @TranslationLabel("machineQuantity")
    fun quantity() = Num(BigDecimal.ONE)

    @ReadOnly
    @DefaultUnit(DefaultUnit.KILOWATT)
    @ObjectView(ObjectView.MACHINE, 160)
    fun powerMachine(
        powerOnTimeRate: Rate,
        connectedLoad: Power,
    ): Power {
        return connectedLoad * powerOnTimeRate
    }

    @Input
    @MandatoryForEntity(index = 5)
    @ObjectView(ObjectView.MACHINE, 220)
    @StaticDenominatorUnit(StaticUnitOverride.HOUR)
    fun costPerHour(): Money = Money(BigDecimal.ONE)

    @StaticDenominatorUnit(StaticUnitOverride.HOUR)
    fun sumCostPerHour(
        quantity: Pieces,
        costPerHour: Money,
    ): Money {
        return (costPerHour * quantity)
    }

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun roughMachineProductionCostPerPart(
        costPerHour: Money,
        @Parent(Entities.MANUFACTURING_STEP)
        manufacturingTimePerPart: Time,
        quantity: Pieces,
        @Parent(Entities.MANUFACTURING_STEP) lotFractionApportionmentFactor: Rate,
    ): Money {
        return costPerHour * manufacturingTimePerPart.inHours * quantity * lotFractionApportionmentFactor
    }

    @DefaultUnit(DefaultUnit.MINUTE)
    fun systemDownTime(
        @Parent(Entities.MANUFACTURING_STEP) systemDownTime: Time,
    ): Time = systemDownTime

    @ReadOnly
    fun roughMachineSetupCostPerLot(
        systemDownTime: Time,
        costPerHour: Money,
    ): Money {
        return costPerHour * systemDownTime.inHours
    }

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun roughMachineSetupCostPerPart(
        roughMachineSetupCostPerLot: Money,
        @Parent(Entities.MANUFACTURING_STEP) averageProcessedLotSize: QuantityUnit,
        @Parent(Entities.MANUFACTURING_STEP) lotFractionApportionmentFactor: Rate,
        quantity: Pieces,
    ): Money {
        return (roughMachineSetupCostPerLot / averageProcessedLotSize) * quantity * lotFractionApportionmentFactor
    }

    @Input
    @Path("/api/currency{bomPath}{branchPath}", false)
    fun baseCurrency(): Currency = Currency("EUR")

    @Input
    @Path("/api/currency{bomPath}{branchPath}", false)
    @MandatoryForEntity(index = 3)
    fun masterdataBaseCurrency(): Currency = Currency("EUR")

    @Input
    @DefaultUnit(DefaultUnit.QM)
    fun requiredSpaceGross(): Area = Area(BigDecimal.ZERO, AreaUnits.QM)

    @Input
    @StaticDenominatorUnit(StaticUnitOverride.HOUR)
    fun gasConsumptionMeltingPerHour(): Volume = Volume(BigDecimal.ZERO, VolumeUnits.CM)

    @Input
    @StaticDenominatorUnit(StaticUnitOverride.HOUR)
    fun gasConsumptionKeepWarmPerHour(): Volume = Volume(BigDecimal.ZERO, VolumeUnits.CM)

    @Input
    @DefaultUnit(DefaultUnit.KILOWATT)
    fun connectedLoad(): Power = Power(BigDecimal.ZERO, PowerUnits.WATT)

    @Input
    fun powerOnTimeRate(): Rate = Rate(BigDecimal.ONE)

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun depreciationCostPerPart() = Money.ZERO

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun interestCostPerPart() = Money.ZERO

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun surfaceCostPerPart() = Money.ZERO

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun maintenanceCostPerPart() = Money.ZERO

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun consumableCostPerPart() = Money.ZERO

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun energyCostPerPart() = Money.ZERO

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun depreciationCostNonOccupancyPerPart(): Money = Money.ZERO

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun interestCostNonOccupancyPerPart(): Money = Money.ZERO

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun surfaceCostNonOccupancyPerPart(): Money = Money.ZERO

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun maintenanceCostNonOccupancyPerPart(): Money = Money.ZERO

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun consumableCostNonOccupancyPerPart(): Money = Money.ZERO

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun energyCostNonOccupancyPerPart(): Money = Money.ZERO

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun depreciationSetupCostPerPart(): Money = Money.ZERO

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun interestSetupCostPerPart(): Money = Money.ZERO

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun surfaceSetupCostPerPart(): Money = Money.ZERO

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun maintenanceSetupCostPerPart(): Money = Money.ZERO

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun consumableSetupCostPerPart(): Money = Money.ZERO

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun energySetupCostPerPart(): Money = Money.ZERO
}
