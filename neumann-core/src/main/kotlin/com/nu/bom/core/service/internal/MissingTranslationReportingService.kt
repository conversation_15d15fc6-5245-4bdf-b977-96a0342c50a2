package com.nu.bom.core.service.internal

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.core.json.JsonReadFeature
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.nu.bom.core.api.dtos.MissingTranslationDto
import com.nu.bom.core.model.ErrorReport
import com.nu.bom.core.model.internal.MissingTranslation
import com.nu.bom.core.repository.MissingTranslationRepository
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.nu.bom.core.utils.separateByType
import com.nu.http.EnvironmentNameSupplier
import com.tset.core.module.TranslationServiceProvider
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.core.io.ResourceLoader
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.switchIfEmpty
import reactor.kotlin.extra.math.sumAsLong

private class TranslationBaseline(
    resourceLoader: ResourceLoader,
    mapperBuilder: Jackson2ObjectMapperBuilder,
) {
    private val baseline: Set<String>
    private val baselineRegexPattern: List<Regex>

    init {
        val objectMapper =
            mapperBuilder
                .build<ObjectMapper>()
                .enable(JsonReadFeature.ALLOW_JAVA_COMMENTS.mappedFeature())
                .enable(JsonReadFeature.ALLOW_TRAILING_COMMA.mappedFeature())

        val (k, p) =
            resourceLoader.getResource("classpath:/locales/translations-baseline.json").let {
                objectMapper.readValue(
                    it.inputStream,
                    object : TypeReference<List<BaselineEntry>>() {},
                )
            }.separateByType {
                it.key to it.pattern?.toRegex()
            }
        baseline = k.toSet()
        baselineRegexPattern = p
    }

    fun contains(key: String): Boolean {
        return baseline.contains(key) || baselineRegexPattern.any { it.matches(key) }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    private data class BaselineEntry(val key: String? = null, val pattern: String? = null)
}

@Service
class MissingTranslationReportingService(
    private val environmentNameSupplier: EnvironmentNameSupplier,
    private val missingTranslationRepository: MissingTranslationRepository,
    @Qualifier("webApplicationContext") resourceLoader: ResourceLoader,
    mapperBuilder: Jackson2ObjectMapperBuilder,
) {
    private fun environment() = environmentNameSupplier.getEnv() ?: ""

    private val baseline: TranslationBaseline by lazy {
        TranslationBaseline(resourceLoader, mapperBuilder)
    }

    fun report(
        dto: MissingTranslationDto,
        stackTrace: List<ErrorReport.StackTraceElementDto>,
    ): Mono<Void> {
        if (baseline.contains(dto.key)) return Mono.empty()
        // Insert a missing translation if the key + environment combination doesn't already exist.
        return missingTranslationRepository.findByKeyAndEnvironment(dto.key, environment()).next().switchIfEmpty {
            val missingTranslation =
                MissingTranslation(
                    key = dto.key,
                    url = dto.url,
                    component = dto.component,
                    parentComponent = dto.parentComponent,
                    environment = environment(),
                    stackTrace = stackTrace,
                )
            missingTranslationRepository.save(missingTranslation)
        }.then()
    }

    private fun toTruncatedStackTrace(stackTrace: List<ErrorReport.StackTraceElementDto>): String {
        val maxCharReportString = 2_000
        val truncationSuffix = "[…]"

        val stackTraceString =
            stackTrace.joinToString(separator = " || ") { "class: '${it.clazz}' | method: '${it.method}' | line: ${it.line}" }

        require(maxCharReportString >= truncationSuffix.length)
        return if (stackTraceString.length > maxCharReportString) {
            "${stackTraceString.take(maxCharReportString - truncationSuffix.length)}$truncationSuffix"
        } else {
            stackTraceString
        }
    }

    fun findAllForCurrentEnvironment(): Flux<MissingTranslationDto> {
        return missingTranslationRepository.findByEnvironment(environment()).map {
            MissingTranslationDto(
                key = it.key,
                timestamp = it.createdDate?.toString() ?: "",
                url = it.url,
                component = it.component,
                parentComponent = it.parentComponent,
                stackTrace = toTruncatedStackTrace(it.stackTrace),
            )
        }
    }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    fun refresh(translationServiceProvider: TranslationServiceProvider): Mono<Long> {
        return translationServiceProvider.getTranslationsService().flatMapMany { translationService ->
            findAllForCurrentEnvironment().flatMap { missingTranslation ->
                val (key, section) = getKeyAndSection(missingTranslation.key)

                // The translation now exists since the last time we reported it
                // or it was added to the baseline, remove it from the DB
                if (baseline.contains(key) || translationService.translateOrNull(key, section) != null) {
                    deleteForCurrentEnvironment(missingTranslation.key)
                } else {
                    Mono.just(0)
                }
            }
        }.sumAsLong()
    }

    /**
     * If the key is in the format "section.key", returns Pair<key, section>.
     * Otherwise, returns Pair<key, null>
     */
    private fun getKeyAndSection(originalKey: String): Pair<String, String?> {
        val keySplit = originalKey.split(".", limit = 2)
        if (keySplit.size == 2) {
            val section = keySplit[0]
            val key = keySplit[1]
            return Pair(key, section)
        }
        return Pair(originalKey, null)
    }

    fun deleteAllForCurrentEnvironment(): Mono<Long> {
        return missingTranslationRepository.deleteByEnvironment(environment())
    }

    fun deleteForCurrentEnvironment(key: String): Mono<Long> {
        return missingTranslationRepository.deleteByKeyAndEnvironment(key, environment())
    }
}
