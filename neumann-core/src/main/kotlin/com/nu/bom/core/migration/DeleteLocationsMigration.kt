package com.nu.bom.core.migration

import com.nu.bom.core.model.AccountMasterData
import com.nu.bom.core.model.MasterData
import io.mongock.api.annotations.ChangeUnit
import io.mongock.api.annotations.Execution
import io.mongock.api.annotations.RollbackExecution
import org.slf4j.LoggerFactory
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query

@ChangeUnit(
    id = "COST-79834 dynamic skill type remove location master data",
    order = "2025-01-13",
)
@Suppress("unused")
class DeleteLocationsMigration(
    private val template: MongoTemplate,
) {
    companion object {
        private val LOG = LoggerFactory.getLogger(DeleteLocationsMigration::class.java)
    }

    @Execution
    fun migrate() {
        val query =
            Query.query(
                Criteria
                    .where("type")
                    .`is`("LOCATION"),
            )

        val removedAccountLocations = template.remove(query, AccountMasterData.COLLECTION_NAME)
        val removedAccountHistoryLocations = template.remove(query, AccountMasterData.HISTORY_COLLECTION_NAME)
        val removedTsetLocations = template.remove(query, MasterData.COLLECTION_NAME)
        val removedTsetHistoryLocations = template.remove(query, MasterData.HISTORY_COLLECTION_NAME)

        LOG.info(
            "Removed ${removedAccountLocations.deletedCount} locations from account master data",
        )
        LOG.info(
            "Removed ${removedAccountHistoryLocations.deletedCount} locations from account master data history",
        )
        LOG.info(
            "Removed ${removedTsetLocations.deletedCount} locations from tset master data",
        )
        LOG.info(
            "Removed ${removedTsetHistoryLocations.deletedCount} locations from tset master data history",
        )
    }

    @RollbackExecution
    fun rollback() {
        LOG.error("Oh no! Rollback for DeleteLocationsMigration got triggered ...")
    }
}
