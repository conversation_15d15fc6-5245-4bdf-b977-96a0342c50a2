package com.nu.bom.core.service.bomimporter.enrichment

import com.nu.bom.core.exception.ErrorCodedException
import com.nu.bom.core.exception.readable.ErrorCode
import com.nu.bom.core.manufacturing.entities.BaseMaterial
import com.nu.bom.core.manufacturing.entities.ElectronicComponent
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.ElcoPurchaseVolume
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.MasterdataConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.Part
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.masterdata.MdLookupSourceProvider
import com.nu.bom.core.manufacturing.masterdata.operationsAndExecutionContext.MasterdataContext
import com.nu.bom.core.manufacturing.service.ConfigurationManagementService
import com.nu.bom.core.manufacturing.service.asEntityClass
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.service.BomNodeService
import com.nu.bom.core.service.ManufacturingCalculationService
import com.nu.bom.core.service.ManufacturingEntityFactoryService
import com.nu.bom.core.service.bomimporter.ELCO_FIELDS
import com.nu.bom.core.service.configurations.ConfigType
import com.nu.bom.core.service.masterdata.MdDetailCrudService
import com.nu.bom.core.service.masterdata.MdLookupService
import com.nu.bom.core.service.nexar.NexarQueryService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.nu.masterdata.dto.v1.detail.NumericDetailValueDto
import com.nu.masterdata.dto.v1.detail.table.BuiltinLovFilterDto
import com.nu.masterdata.dto.v1.detail.table.DetailQueryDto
import com.nu.masterdata.dto.v1.detail.table.DetailQueryResponseDto
import com.nu.masterdata.dto.v1.header.HeaderDetailQueryResponseDto
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import com.nu.masterdata.dto.v1.lookup.request.Effectivity
import com.nu.masterdata.dto.v1.lookup.request.MasterdataLookupRequest
import com.nu.masterdata.dto.v1.lookup.request.MasterdataLookupResponse
import com.tset.bom.clients.bomimporter.enrichment.EnrichedDataDto
import com.tset.bom.clients.bomimporter.enrichment.EnrichedDataRowDto
import com.tset.bom.clients.bomimporter.enrichment.EnrichmentBySourceDto
import com.tset.bom.clients.bomimporter.enrichment.EnrichmentDataFieldValueDto
import com.tset.bom.clients.bomimporter.enrichment.EnrichmentRequestDataRowDto
import com.tset.bom.clients.bomimporter.enrichment.EnrichmentRequestDto
import com.tset.bom.clients.bomimporter.enrichment.EnrichmentType
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono
import kotlin.reflect.KClass

@Service
class BomImporterEnrichmentService(
    private val nexarQueryService: NexarQueryService,
    private val bomNodeService: BomNodeService,
    private val manufacturingEntityFactoryService: ManufacturingEntityFactoryService,
    private val calculationService: ManufacturingCalculationService,
    private val mdDetailCrudService: MdDetailCrudService,
    private val configurationService: ConfigurationManagementService,
    private val mdLookupService: MdLookupService,
) {
    fun enrichImportedData(
        accessCheck: AccessCheck,
        bomNodeId: BomNodeId,
        branchId: BranchId?,
        entityType: Entities,
        entityClass: KClass<out ManufacturingEntity>?,
        data: EnrichmentRequestDto,
    ): Mono<EnrichedDataDto> {
        when (entityType) {
            Entities.C_PART ->
                when (entityClass) {
                    ElectronicComponent::class -> {
                    }
                    else ->
                        ErrorCodedException.badRequest(
                            ErrorCode.UNSUPPORTED_OPERATION,
                            "entityClass=${entityClass?.simpleName}",
                        )
                }
            else ->
                ErrorCodedException.badRequest(
                    ErrorCode.UNSUPPORTED_OPERATION,
                    "entityType=${entityType.name}",
                )
        }

        return enrichElco(accessCheck, bomNodeId, branchId, data)
    }

    private fun enrichElco(
        accessCheck: AccessCheck,
        bomNodeId: BomNodeId,
        branchId: BranchId?,
        data: EnrichmentRequestDto,
    ): Mono<EnrichedDataDto> =
        Flux
            .concat(
                enrichElcoWithMasterData(accessCheck, bomNodeId, branchId, data),
                enrichElcoWithNexarPart(accessCheck, bomNodeId, branchId, data),
            ).collectList()
            .map { enrichments ->
                EnrichedDataDto(
                    enrichmentType = EnrichmentType.ONLY_ENRICH_VALUES,
                    enrichmentBySource = enrichments,
                    enrichmentTooltip = ENRICHMENT_TOOLTIP,
                )
            }

    private fun getManufacturing(
        accessCheck: AccessCheck,
        bomNodeId: BomNodeId,
        branchId: BranchId?,
    ): Mono<ManufacturingEntity> =
        bomNodeService.getBomNode(accessCheck, bomNodeId, branchId).map { snapshot ->
            // get available snapshot currencies without masterdata augmentation
            snapshot.manufacturing!!
        }

    private fun enrichElcoWithMasterData(
        accessCheck: AccessCheck,
        bomNodeId: BomNodeId,
        branchId: BranchId?,
        data: EnrichmentRequestDto,
    ): Mono<EnrichmentBySourceDto> {
        val mpnToRows = mapEnrichmentRowsToMpn(data)

        val query =
            DetailQueryDto(
                filters =
                    mapOf(
                        SimpleKeyDto("_BUILTIN_headerKey") to
                            mpnToRows.keys.map { mpn ->
                                BuiltinLovFilterDto(
                                    equals = SimpleKeyDto(mpn),
                                )
                            },
                    ),
                classificationFieldFilters = emptyMap(),
                classificationFilters = emptyMap(),
                showStateOf = null,
                showInactive = false,
                sortOrder = emptyList(),
            )

        val manufacturingMono = getManufacturing(accessCheck, bomNodeId, branchId)

        // val defaultConfigMono = configurationService.getDefaultConfiguration(accessCheck, ConfigType.Masterdata)

        fun String.toDto() = SimpleKeyDto(this)

        val resultMono =
            manufacturingMono.flatMap { manufacturing ->
                val mdConfigKey: MasterdataConfigurationKey? =
                    manufacturing.getFieldResultSafeCast(
                        Manufacturing::masterdataConfigurationKey.name,
                    )
                requireNotNull(mdConfigKey) { "Import target has no master data configuration." }

                val mdConfigMono = configurationService.getConfiguration(accessCheck, ConfigType.Masterdata, mdConfigKey)

                mdConfigMono.flatMap { configuration ->
                    val searchResultMono =
                        mdDetailCrudService.postAllDetailEntries(
                            accessCheck,
                            SimpleKeyDto(configuration.materialPriceConfiguration.headerTypeKey),
                            query,
                        )

                    val materialPricelookupConfig = configuration.materialPriceConfiguration

                    val requestEffectivities =
                        materialPricelookupConfig.effectivities.mapNotNull { effect ->
                            val effectivityDefinition =
                                configuration.effectivityDefinitions.firstOrNull {
                                    it.fieldDefinitionKey ==
                                        effect.fieldDefinitionKey
                                }
                            requireNotNull(
                                effectivityDefinition,
                            ) { "Material pricelookup config uses undefined effectivity ${effect.fieldDefinitionKey}." }
                            val fieldResult = manufacturing.getFieldResultSafe(effectivityDefinition.sourceFieldName)

                            fieldResult?.let {
                                val effectivityForRequest =
                                    MasterdataContext.convertFieldResultToEffectivity(
                                        effectivityDefinition,
                                        fieldResult,
                                    )
                                val value = MdLookupSourceProvider.getFieldValue(effectivityForRequest.first)
                                value?.let {
                                    Effectivity(
                                        fieldDefinitionKey = effect.fieldDefinitionKey.toDto(),
                                        value = value,
                                    )
                                }
                            }
                        }

                    val lookupRequest =
                        MasterdataLookupRequest(
                            strategyKey = materialPricelookupConfig.lookupStrategyKey.toDto(),
                            headerTypeKey = materialPricelookupConfig.headerTypeKey.toDto(),
                            headerKeys = mpnToRows.keys.map { it.toDto() },
                            effectivities = requestEffectivities,
                            timestampEpochMillis = null,
                        )
                    val lookupResultMono = mdLookupService.lookup(accessCheck, lookupRequest)

                    Mono.zip(searchResultMono, lookupResultMono)
                }
            }

        val result =
            resultMono.flatMap { result ->
                val searchResult = result.t1
                val lookupResult = result.t2
                EnrichmentBySourceDto(
                    sourceName = "Master data",
                    sourceType = EnrichmentSourceType.MASTER_DATA.name,
                    sourceDescription = null,
                    enrichedRows = enrichRowsWithMasterData(mpnToRows, searchResult, lookupResult),
                ).toMono()
            }

        return result
    }

    private fun enrichRowsWithMasterData(
        mpnToRows: Map<String, EnrichmentRequestDataRowDto>,
        searchResult: DetailQueryResponseDto,
        lookupResult: MasterdataLookupResponse,
    ): List<EnrichedDataRowDto> {
        val mpnToMasterDataSearchResult =
            searchResult
                .content
                .map { it.headerDto }
                .associateBy { (it.key as SimpleKeyDto).key }

        val mpnToMasterDataLookupResult =
            lookupResult
                .items
                .filter { it.detailValueTypeKey?.key == MdLookupSourceProvider.PRICE_KEY }
                .filter { it.value is NumericDetailValueDto }
                .associateBy { (it.headerKey as SimpleKeyDto).key }

        return mpnToRows.entries.mapNotNull { (mpn, row) ->
            if (mpnToMasterDataSearchResult.containsKey(mpn)) {
                val masterDataSearchResult = mpnToMasterDataSearchResult[mpn]!!
                val mpnField =
                    EnrichmentDataFieldValueDto(
                        columnId = ElectronicComponent::mpn.name,
                        value = mpn,
                    )
                val priceField =
                    mpnToMasterDataLookupResult[mpn]?.let { detail ->
                        val price = (detail.value as NumericDetailValueDto)
                        EnrichmentDataFieldValueDto(
                            columnId = BaseMaterial::pricePerUnit.name,
                            value = price.value.toString(),
                        )
                    }

                val displayDesignationField = extractDisplayDesignation(masterDataSearchResult)
                val mountingTypeField = extractMountingType(masterDataSearchResult)
                val manufacturerField = extractManufacturer(masterDataSearchResult)

                EnrichedDataRowDto(
                    businessKey = row.businessKey,
                    enrichedValues =
                        listOfNotNull(
                            mpnField,
                            displayDesignationField,
                            mountingTypeField,
                            manufacturerField,
                            priceField,
                        ),
                    sourceKey = (masterDataSearchResult.key as SimpleKeyDto).key,
                )
            } else {
                null
            }
        }
    }

    private fun extractDisplayDesignation(header: HeaderDetailQueryResponseDto): EnrichmentDataFieldValueDto =
        EnrichmentDataFieldValueDto(
            columnId = ElectronicComponent::displayDesignation.name,
            value = header.name,
        )

    private fun extractMountingType(header: HeaderDetailQueryResponseDto): EnrichmentDataFieldValueDto? =
        header.classificationFieldValues
            ?.get(SimpleKeyDto(ElectronicComponent.MOUNTING_TYPE_MASTER_DATA_FIELD_NAME))
            ?.let {
                EnrichmentDataFieldValueDto(
                    columnId = ElectronicComponent::mountingType.name,
                    value = header.name,
                )
            }

    private fun extractManufacturer(header: HeaderDetailQueryResponseDto): EnrichmentDataFieldValueDto? =
        header.classificationFieldValues
            ?.get(SimpleKeyDto(ElectronicComponent.MANUFACTURER_TYPE_MASTER_DATA_FIELD_NAME))
            ?.let {
                EnrichmentDataFieldValueDto(
                    columnId = ElectronicComponent::manufacturer.name,
                    value = header.name,
                )
            }

    private fun calculateAndGetFields(
        accessCheck: AccessCheck,
        elcoVolume: FieldResultStar,
        externalPartId: String,
    ): Mono<Map<String, FieldResultStar>> =
        manufacturingEntityFactoryService
            .createEntity(
                name = "",
                entityType = Entities.C_PART,
                fields =
                    mapOf(
                        "externalPartId" to Text(externalPartId),
                        "elcoVolume" to elcoVolume,
                    ),
                clazz = ElectronicComponent::class.asEntityClass(),
                masterDataSelector = null,
            ).let { createdEntity ->
                calculationService.calculate(
                    accessCheck = accessCheck,
                    manufacturing = createdEntity,
                )
            }.map { (entity, _) ->
                entity.getFieldResultMap()
            }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun enrichElcoWithNexarPart(
        accessCheck: AccessCheck,
        bomNodeId: BomNodeId,
        branchId: BranchId?,
        data: EnrichmentRequestDto,
    ): Mono<EnrichmentBySourceDto> {
        val mpnToRows = mapEnrichmentRowsToMpn(data)

        return bomNodeService
            .getBomNode(accessCheck, bomNodeId, branchId)
            .map { snapshot ->
                snapshot.getFieldResult("elcoPurchaseVolume") ?: ElcoPurchaseVolume.HIGH
            }.flatMap { elcoVolume ->
                nexarQueryService
                    .getPartsByMpn(mpnToRows.keys.toList())
                    .defaultIfEmpty(emptyMap())
                    .map { filterNexarData(it) }
                    .flatMap { mpnToParts ->
                        Flux
                            .fromIterable(mpnToParts.toList())
                            .flatMap { (mpn, parts) ->
                                calculateAndGetFields(
                                    accessCheck,
                                    elcoVolume,
                                    externalPartId = parts.first().id,
                                ).map { octopartFields ->
                                    mpn to octopartFields
                                }
                            }.collectList()
                            .map { it.toMap() }
                    }.map { mpnToNexarData ->
                        mpnToRows.entries
                            .mapNotNull { (mpn, row) ->
                                if (mpnToNexarData.containsKey(mpn)) {
                                    val nexarData = mpnToNexarData[mpn]!!
                                    EnrichedDataRowDto(
                                        businessKey = row.businessKey,
                                        enrichedValues =
                                            nexarData
                                                .filterKeys { ELCO_FIELDS.containsKey(it) && it != "mpn" }
                                                .map { (key, value) ->
                                                    EnrichmentDataFieldValueDto(
                                                        columnId = key,
                                                        value = value.toString(),
                                                    )
                                                },
                                        sourceKey = nexarData["externalPartId"]!!.toString(),
                                    )
                                } else {
                                    null
                                }
                            }.let { enrichedRows ->
                                EnrichmentBySourceDto(
                                    sourceName = "Nexar",
                                    sourceType = EnrichmentSourceType.NEXAR.name,
                                    sourceDescription = null,
                                    enrichedRows = enrichedRows,
                                )
                            }
                    }
            }
    }

    private fun mapEnrichmentRowsToMpn(data: EnrichmentRequestDto): Map<String, EnrichmentRequestDataRowDto> =
        data.items
            .filter { rowDto -> rowDto.businessKey.any { it.columnId == "mpn" && !it.value.isNullOrBlank() } }
            .associateBy { rowDto -> rowDto.businessKey.find { it.columnId == "mpn" }!!.value!! }

    /**
     * exclude mpn search hits that are missing any price information.
     */
    fun filterNexarData(dataRow: Map<String, List<Part>>): Map<String, List<Part>> =
        dataRow
            .mapNotNull { (mpn, parts) ->
                parts
                    .mapNotNull { part ->
                        part.sellers
                            .mapNotNull { seller ->
                                seller.offers
                                    .filter { it != null && it.converted_price > 0.0 }
                                    .takeIf { it.isNotEmpty() }
                                    ?.let { seller.copy(offers = it) }
                            }.takeIf { it.isNotEmpty() }
                            ?.let { part.copy(sellers = it) }
                    }.takeIf {
                        it.isNotEmpty()
                    }?.let { mpn to it }
            }.toMap()
}

private const val ENRICHMENT_TOOLTIP =
    "Choose which data sources you want to apply (if available) as matched " +
        "by the MPN. You can further define the order in which they are applied via drag & drop"
