package com.nu.bom.core.manufacturing.fieldTypes

class SelectableBoolean(res: Selection) : SelectEnumFieldResult<SelectableBoolean.Selection, SelectableBoolean>(res) {
    enum class Selection(val intValue: Int) {
        FALSE(0),
        TRUE(1),
    }

    constructor(res: Bool) : this(valueOf(res.res).res)
    constructor(boolean: Boolean) : this(valueOf(boolean).res)

    companion object {
        val FALSE = SelectableBoolean(Selection.FALSE)
        val TRUE = SelectableBoolean(Selection.TRUE)

        fun valueOf(intValue: Int): SelectableBoolean {
            return when (intValue) {
                0 -> FALSE
                1 -> TRUE
                else -> throw IllegalArgumentException("intValue=$intValue is not a valid boolean value")
            }
        }

        fun valueOf(booleanValue: Boolean): SelectableBoolean {
            return valueOf(
                intValue =
                    when (booleanValue) {
                        false -> 0
                        true -> 1
                    },
            )
        }
    }

    fun toBoolean(): Boolean {
        return when (this.res) {
            Selection.TRUE -> true
            Selection.FALSE -> false
        }
    }
}
