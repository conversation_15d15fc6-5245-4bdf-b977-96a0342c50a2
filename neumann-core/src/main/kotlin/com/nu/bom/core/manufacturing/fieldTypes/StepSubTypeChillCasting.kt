package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.utils.toUpperSnakeCase

class StepSubTypeChillCasting(res: Selection) :
    SelectEnumFieldResult<StepSubTypeChillCasting.Selection, StepSubTypeChillCasting>(res) {

    enum class Selection(val value: String) {
        GRAVITY_CASTING("Gravity Casting"),
        LOW_PRESSURE_DIE_CASTING("Low pressure die casting"),
        TILT_DIE_CASTING("Tilt die casting")
    }

    companion object {

        val GRAVITY_CASTING = StepSubTypeChillCasting(Selection.GRAVITY_CASTING)
        val LOW_PRESSURE_DIE_CASTING = StepSubTypeChillCasting(Selection.LOW_PRESSURE_DIE_CASTING)
        val TILT_DIE_CASTING = StepSubTypeChillCasting(Selection.TILT_DIE_CASTING)

        fun valueOf(name: String): StepSubTypeChillCasting {
            return Selection.values().find {
                // check if any of
                it.value == name
            }?.let {
                StepSubTypeChillCasting(it)
            } // else try to cast from name
                ?: StepSubTypeChillCasting(Selection.valueOf(name.toUpperSnakeCase()))
        }
    }
}
