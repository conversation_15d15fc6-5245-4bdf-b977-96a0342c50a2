package com.nu.bom.core.messagebus

import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.core.scheduler.Schedulers
import kotlin.reflect.KClass

interface MessageBusService {

    fun <T : Any> send(message: T, routingKey: String): Mono<T>

    // Used in tests
    fun <T : Any> sendGlobalMessage(message: T, routingKey: String): Mono<T>

    fun clear()

    fun <T : Any> consume(routingKey: String, clazz: KClass<T>): Flux<T>

    fun <T : Any> uniqueConsumer(routingKey: String, clazz: KClass<T>): Flux<T>
}

inline fun <reified T : Any> MessageBusService.consume(routingKey: String) =
    consume(routingKey, T::class)

internal fun MessageBusService.uniqueScheduler(clazz: KClass<*>) =
    Schedulers.newSingle("unique-consumer-" + clazz.simpleName)
