package com.nu.bom.core.service.configurations

import com.nu.bom.core.model.configurations.ConfigurationValue
import org.springframework.stereotype.Service

// TODO: naming --dwa/22/10
data class BundledTsetConfigurations(
    val type: ConfigType<*>,
    val configurations: List<TsetConfiguration>,
) {
    fun getDefault(): TsetConfiguration = configurations.first { it.isDefault }
}

@Service
class TsetConfigurationFactory(
    private val tsetConfigurationServices: List<TsetConfigurationService>,
    private val migrator: ConfigurationMigrationFactory,
) {
    companion object {
        private fun tsetKeyPrefix(cfg: TsetConfiguration) {
            check(cfg.key.startsWith(ConfigurationService.TSET_KEY_PREFIX)) {
                "Invalid key ${cfg.key} does not start with ${ConfigurationService.TSET_KEY_PREFIX}"
            }
        }

        private fun validDefault(cfgs: List<TsetConfiguration>) {
            val default =
                requireNotNull(cfgs.singleOrNull { it.isDefault }) {
                    "Did not specify exactly one default configuration"
                }
            require(!default.isDeprecated) { "Default ${default.key} is marked as deprecated" }
        }

        private fun uniqueKeys(cfgs: List<TsetConfiguration>) {
            val nonUniqueKeys =
                cfgs
                    .groupBy { it.key }
                    .filter { it.value.size > 1 }
                    .keys

            require(nonUniqueKeys.isEmpty()) { "Specified non-unique keys: ${nonUniqueKeys.joinToString()}" }
        }

        private fun sequentialVersions(cfg: TsetConfiguration) {
            require(cfg.versions.isNotEmpty()) { "${cfg.key} has empty list of versions" }
            require(cfg.versions.first().version.isInitial()) { "${cfg.key} does not start with the initial version 1.0" }
            val violation =
                cfg.versions
                    .asSequence()
                    .map { it.version }
                    .zipWithNext()
                    .firstOrNull { (a, b) -> !a.isSequential(b) }
                    ?.second

            require(violation == null) {
                "${cfg.key}, version $violation is unsorted or duplicate"
            }
        }
    }

    private fun configClassMatches(
        type: ConfigType<*>,
        cfg: TsetConfiguration,
    ) {
        cfg.versions.forEach { version ->
            val migrated = migrator.tryMigrate(type.group, type.type, version.value)
            require(migrated::class == type.configClass) {
                "${cfg.key}, version ${version.version} cannot be migrated to the current config type of $type"
            }
        }
    }

    init {
        tsetConfigurationServices.forEach { service ->
            val tsetConfigurations = service.getConfigurations()

            try {
                validDefault(tsetConfigurations)
                uniqueKeys(tsetConfigurations)

                tsetConfigurations.forEach { cfg ->
                    tsetKeyPrefix(cfg)
                    sequentialVersions(cfg)
                    configClassMatches(service.type, cfg)
                }
            } catch (e: IllegalArgumentException) {
                throw IllegalArgumentException("${service::class.simpleName} provided invalid configurations: ${e.message}", e)
            }
        }
    }

    /**
     * @return a list of all bundled tset configurations. The configurations are validated, and versions are sorted in ascending order.
     */
    fun getTsetConfigurations(): List<BundledTsetConfigurations> =
        tsetConfigurationServices.map { service ->
            BundledTsetConfigurations(service.type, service.getConfigurations())
        }

    fun getSwaggerExamples(): Map<String, Pair<String, ConfigurationValue>> =
        tsetConfigurationServices
            // Provide only one example per configuration class, unless a manual example is provided
            .distinctBy { Pair(it.type.configClass, it.swaggerExampleOverride()) }
            .associate { service ->
                val (name, value) =
                    service.swaggerExampleOverride()
                        ?: service.getConfigurations().first { it.isDefault }.versions.last().let { it.displayName to it.value }
                val migrated = migrator.tryMigrate(service.type.group, service.type.type, value)
                "${service.type.group}.${service.type.type}" to Pair(name, migrated)
            }
}
