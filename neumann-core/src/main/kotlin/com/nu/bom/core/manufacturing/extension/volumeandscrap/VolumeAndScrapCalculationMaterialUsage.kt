package com.nu.bom.core.manufacturing.extension.volumeandscrap

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Extends
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MasterDataCalculation
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.BaseMaterial
import com.nu.bom.core.manufacturing.entities.BomEntry
import com.nu.bom.core.manufacturing.entities.ElectronicComponent
import com.nu.bom.core.manufacturing.entities.ManualBaseMaterial
import com.nu.bom.core.manufacturing.entities.ManualMasterdataMaterial
import com.nu.bom.core.manufacturing.entities.ManualMaterial
import com.nu.bom.core.manufacturing.entities.ManufacturingEntityExtension
import com.nu.bom.core.manufacturing.entities.RawMaterial
import com.nu.bom.core.manufacturing.entities.RawMaterialManual
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.extension.VOLUME_AND_SCRAP_CALCULATION_FIELD_PACKAGE
import com.nu.bom.core.manufacturing.fieldTypes.Rate

@Extends(
    [
        BaseMaterial::class,
        ElectronicComponent::class,
        ManualBaseMaterial::class,
        RawMaterial::class,
        RawMaterialManual::class,
        ManualMaterial::class,
        ManualMasterdataMaterial::class,
        BomEntry::class,
    ],
    VOLUME_AND_SCRAP_CALCULATION_FIELD_PACKAGE,
)
@EntityType(Entities.NONE)
@Suppress("DEPRECATION")
class VolumeAndScrapCalculationMaterialUsage(name: String) : ManufacturingEntityExtension(name) {
    @Input
    @MasterDataCalculation(false)
    fun accumulatedMaterialScrapRate(
        @Parent(Entities.MANUFACTURING_STEP) accumulatedMaterialScrapRate: Rate?,
    ): Rate = accumulatedMaterialScrapRate ?: Rate.ZERO
}
