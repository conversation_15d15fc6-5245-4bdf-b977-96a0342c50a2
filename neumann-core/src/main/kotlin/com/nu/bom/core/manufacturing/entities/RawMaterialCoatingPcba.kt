package com.nu.bom.core.manufacturing.entities

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MasterDataType
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Text

@EntityType(Entities.MATERIAL)
@MasterDataType(com.nu.bom.core.manufacturing.enums.MasterDataType.RAW_MATERIAL_COATING_PCBA)
class RawMaterialCoatingPcba(name: String) : ManufacturingEntity(name) {
    override val extends = RawMaterial(name)

    @Input
    fun designation(materialBaseDisplayDesignation: Text): Text = materialBaseDisplayDesignation
}
