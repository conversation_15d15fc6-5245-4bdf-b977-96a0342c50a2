package com.nu.bom.core.service.simulation

import com.nu.bom.core.exception.userException.BomNodeNotFoundException
import com.nu.bom.core.model.simulation.Simulation
import com.nu.bom.core.service.BomNodeService
import com.nu.bom.core.service.simulation.insecure.SimulationEntitySaver
import com.nu.bom.core.user.AccessCheck
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono

@Service
class SecureSimulationEntitySaver(
    private val simulationEntitySaver: SimulationEntitySaver,
    private val bomNodeService: BomNodeService,
) {
    fun saveEntity(
        accessCheck: AccessCheck,
        simulation: Simulation,
    ): Mono<Simulation> {
        val nodeIds = simulation.nodes.map { it.bomNodeId }
        return bomNodeService.nodeExists(accessCheck, nodeIds, BomNodeService.DeletedMode.Ignore)
            .flatMap { exist ->
                if (exist) {
                    simulationEntitySaver.saveEntity(simulation)
                } else {
                    error(BomNodeNotFoundException(null))
                }
            }
    }
}
