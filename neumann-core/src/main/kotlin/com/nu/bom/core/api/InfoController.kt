package com.nu.bom.core.api

import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api")
class InfoController {

    @GetMapping("/info")
    fun info(): InfoResponse {
        val version: String = System.getenv("VERSION") ?: "null"
        val commitId: String = System.getenv("COMMIT_ID") ?: "HEAD"
        val buildTimestamp: String = System.getenv("BUILD_TIMESTAMP") ?: "2020-01-01T00:00:00+0100"
        val buildNumber: String = System.getenv("BUILD_NO") ?: "null"

        return InfoResponse(version = version, commit_id = commitId, build_timestamp = buildTimestamp, build_number = buildNumber)
    }
}

data class InfoResponse(val version: String, val commit_id: String, val build_timestamp: String, val build_number: String)
