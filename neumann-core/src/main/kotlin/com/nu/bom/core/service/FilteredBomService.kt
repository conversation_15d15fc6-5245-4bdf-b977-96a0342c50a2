package com.nu.bom.core.service

import com.nu.bom.core.api.dtos.BomNodeDtoConversionService
import com.nu.bom.core.api.dtos.ManufacturingDto
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.service.bomrads.AllChildren
import com.nu.bom.core.service.bomrads.BomNodeLoaderService
import com.nu.bom.core.utils.InterpolationBomInfo
import com.tset.core.service.domain.Currency
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import kotlin.reflect.full.findAnnotation

@Service
class FilteredBomService(
    private val bomNodeLoaderService: BomNodeLoaderService,
    private val bomNodeConversionService: BomNodeConversionService,
    private val bomNodeDtoConversionService: BomNodeDtoConversionService,
) {
    fun getTreeAndSetParents(bomNodeAccessInfo: BomNodeAccessInfo): Mono<ManufacturingEntity> =
        getTree(bomNodeAccessInfo).map {
            it.visitChildren { manufacturingEntity, _ ->
                manufacturingEntity.children.forEach { child ->
                    child.setParent(manufacturingEntity)
                }
            }
            it
        }

    private fun getTree(bomNodeAccessInfo: BomNodeAccessInfo) =
        bomNodeLoaderService
            .getBomNode(
                bomNodeAccessInfo.accessCheck,
                loadingMode = AllChildren(bomNodeAccessInfo.bomNodeId),
                bomNodeId = bomNodeAccessInfo.bomNodeId,
                branchId = bomNodeAccessInfo.branchId,
            ).map { snapshot ->
                bomNodeConversionService.bomNodeToManufacturingCalculationTree(
                    snapshot,
                    snapshot.collectChildrenBomNodeIds().toSet(),
                )
            }

    fun convertToDto(
        manufacturingEntity: ManufacturingEntity,
        bomNodeAccessInfo: BomNodeAccessInfo,
        ccy: List<Currency>?,
    ): Mono<ManufacturingDto> =
        bomNodeDtoConversionService.manufacturingEntityToDto(
            ctx =
                BomNodeDtoConversionService.DtoConversionContext(
                    accessCheck = bomNodeAccessInfo.accessCheck,
                    exchangeRateMap = manufacturingEntity.getExchangeRateMap(ccy),
                    latestMasterDataMap = null,
                    interpolationBomInfo =
                        InterpolationBomInfo(
                            bomNodeAccessInfo.bomNodeId.toHexString(),
                            bomNodeAccessInfo.branchId.toHexString(),
                        ),
                    parentShapeModelManufacturing = null,
                ),
            rootEntity = manufacturingEntity,
        )

    fun findEntitiesOfChildBomEntries(
        bomNodeAccessInfo: BomNodeAccessInfo,
        entityType: Entities,
        ccy: List<Currency>?,
    ): Mono<List<ManufacturingEntity>> =
        getTreeAndSetParents(bomNodeAccessInfo)
            .map {
                fun findBomEntries(manufacturingEntity: ManufacturingEntity): List<ManufacturingEntity> =
                    if (manufacturingEntity.getEntityTypeAnnotation() == Entities.BOM_ENTRY) {
                        listOf(manufacturingEntity)
                    } else {
                        manufacturingEntity.children.flatMap(::findBomEntries)
                    }
                it.children.flatMap(::findBomEntries)
            }.map {
                it.flatMap { children -> flattenChildren(children, entityType) }
            }

    companion object {
        fun flattenChildren(
            entity: ManufacturingEntity,
            entityType: Entities,
        ): List<ManufacturingEntity> =
            listOfNotNull(entity.takeIf { isOfType(entity, entityType) }) +
                entity.children.flatMap { flattenChildren(it, entityType) }

        fun isOfType(
            entity: ManufacturingEntity,
            entityType: Entities,
        ) = entity::class.findAnnotation<com.nu.bom.core.manufacturing.annotations.EntityType>()?.name == entityType
    }
}
