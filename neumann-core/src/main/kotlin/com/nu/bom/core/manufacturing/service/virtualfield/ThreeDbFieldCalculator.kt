package com.nu.bom.core.manufacturing.service.virtualfield

import com.nu.bom.core.manufacturing.behaviours.ShapeBasedCostModuleBehaviorThreeDb
import com.nu.bom.core.manufacturing.entities.SubManufacturing
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.Null
import com.nu.bom.core.manufacturing.fieldTypes.ThreeDbOverwriteTracker
import com.nu.bom.core.manufacturing.fieldTypes.ThreeDbOverwrites
import com.nu.bom.core.manufacturing.fieldTypes.ThreeDbVersionedPart
import com.nu.bom.core.manufacturing.fieldTypes.VersionedPart
import com.nu.bom.core.manufacturing.service.CalculationRequest
import com.nu.bom.core.manufacturing.service.Field
import com.nu.bom.core.manufacturing.service.FieldGraph
import com.nu.bom.core.manufacturing.service.FieldResultCalculation
import com.nu.bom.core.manufacturing.service.FieldType
import com.nu.bom.core.manufacturing.service.InputKey
import com.nu.bom.core.manufacturing.service.calculator.CalculationResult
import com.nu.bom.core.manufacturing.service.calculator.SingleFieldCalculationResult
import com.nu.bom.core.threedb.ThreeDbFieldType
import com.nu.bom.core.threedb.ThreeDbHeader
import com.nu.bom.core.threedb.ThreeDbService
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.nu.bom.core.utils.flatFold
import org.bson.types.ObjectId
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toFlux

@Service
class ThreeDbFieldCalculator(
    private val threeDbService: ThreeDbService,
    private val fieldResultCalculation: FieldResultCalculation,
) : VirtualFieldCalculator() {
    private val logger = LoggerFactory.getLogger(ThreeDbFieldCalculator::class.java)

    override fun isProviderForFieldType(fieldType: FieldType): Boolean =
        fieldType is FieldType.Calculation && fieldType.calculationFieldType == FieldType.CalculationFieldType.THREE_DB

    @TsetSuppress("tset:reactive:flux-flatmap") // threeDb can handle it
    override fun calculateFields(
        fields: List<Field>,
        fieldGraph: FieldGraph,
    ): Flux<out CalculationResult> {
        val fieldsByEntity = fields.groupBy { it.entity._id }
        return fieldsByEntity.entries.toFlux().flatMap { (entityId, fields) ->
            logger.info("bulked fields: ${fields.map { it.name }} for entity ${fields[0].entity::class.simpleName}")
            val fieldsForEntity =
                ThreeDbFieldsForEntity(
                    versionedPart = getUniqueVersionedPartForEntity(fieldGraph, entityId),
                    threeDbResourceTracker = getUniqueThreeDbOverwriteTrackerForEntity(fieldGraph, entityId),
                    request = fieldGraph.request,
                    version = fieldGraph.version,
                )
            calculateFieldsForEntity(fields, fieldsForEntity)
        }
    }

    private fun getUniqueVersionedPartForEntity(
        fieldGraph: FieldGraph,
        entityId: ObjectId,
    ): Pair<VersionedPart, Field>? =
        getUniqueSpecialFieldForEntity(fieldGraph, ShapeBasedCostModuleBehaviorThreeDb::versionedPart.name, entityId)

    private fun getUniqueThreeDbOverwriteTrackerForEntity(
        fieldGraph: FieldGraph,
        entityId: ObjectId,
    ): Pair<ThreeDbOverwriteTracker, Field>? =
        getUniqueSpecialFieldForEntity(fieldGraph, ShapeBasedCostModuleBehaviorThreeDb::threeDbResourceTracker.name, entityId)

    private inline fun <reified T : FieldResultStar> getUniqueSpecialFieldForEntity(
        fieldGraph: FieldGraph,
        fieldName: String,
        entityId: ObjectId,
    ): Pair<T, Field>? {
        val specialFieldAndEntities =
            fieldGraph.fields
                .filter { it.name == fieldName && it.entity._id == entityId }
                .mapNotNull { field ->
                    field.getCurrentResult()?.let {
                        if (it !is Null) {
                            require(it is T) {
                                "found $fieldName with fieldResult $it of type ${it::class.simpleName}" +
                                    " (in field of entity ${field.entity::class.simpleName})"
                            }
                            Pair(it, field)
                        } else {
                            require(field.entity.extends is SubManufacturing) {
                                "found field with $fieldName with fieldResult of FieldType NULL " +
                                    "in an entity that does not extend SubManufacturing."
                            }
                            null
                        }
                    }
                }

        if (specialFieldAndEntities.isEmpty()) {
            return null
        }

        require(specialFieldAndEntities.size == 1) {
            val entityNames = specialFieldAndEntities.map { it.second.entity::class.simpleName }
            "Found more than one $fieldName in fieldGraph. Entities: $entityNames"
        }

        return specialFieldAndEntities.single()
    }

    @TsetSuppress("tset:reactive:flux-flatmap")
    private fun calculateFieldsForEntity(
        fields: List<Field>,
        fieldsForEntity: ThreeDbFieldsForEntity,
    ): Flux<out CalculationResult> {
        require(fields.isNotEmpty())
        require(fields.map { it.entity._id }.distinct().size == 1)

        if (fieldsForEntity.versionedPart == null) {
            // Without any versioned Part, don't calculate any ThreeDb fields.
            require(fields.all { (it.model.threeDbFieldType as ThreeDbFieldType.ThreeDbField).isNbkCalculated })
            return Flux.empty()
        }

        val (initialVersionedPart, versionedPartField) = fieldsForEntity.versionedPart
        val (initialFieldTracker, threeDbOverwriteTrackerField) = fieldsForEntity.threeDbResourceTracker!!

        val initialState =
            CalculationState(
                versionedPart = initialVersionedPart,
                threeDbFieldTracker = initialFieldTracker.res,
            )
        logger.info("local versioned part variable initialized as: $initialVersionedPart")
        logger.info("local overwritten resources variable initialized as: ${initialFieldTracker.res}")

        val initial = Mono.just(BulkedFieldsCalculationResult(listOf(), initialState))

        // it's important to calculate sequentially, since several field calculations may update the versionedPart
        val result =
            fields.flatFold(initial) { bulkedResult, nextField ->
                val resourceType = nextField.model.threeDbFieldType
                requireNotNull(resourceType)

                val nextResult =
                    when (resourceType) {
                        ThreeDbFieldType.VersionedPart -> {
                            val calculationResult =
                                createSingleCalculationResult(
                                    nextField,
                                    bulkedResult.calculationState.versionedPart,
                                    fieldsForEntity,
                                )
                            Mono.just(calculationResult to bulkedResult.calculationState)
                        }
                        ThreeDbFieldType.ResourceTracker -> {
                            val calculationResult =
                                createSingleCalculationResult(
                                    nextField,
                                    ThreeDbOverwriteTracker(bulkedResult.calculationState.threeDbFieldTracker),
                                    fieldsForEntity,
                                )
                            Mono.just(calculationResult to bulkedResult.calculationState)
                        }
                        is ThreeDbFieldType.ThreeDbField ->
                            calculateField(
                                nextField,
                                fieldsForEntity,
                                bulkedResult.calculationState,
                            )
                    }

                nextResult.map { (calculationResult, maybeNewState) ->
                    BulkedFieldsCalculationResult(
                        calculationResults = bulkedResult.calculationResults + listOf(calculationResult),
                        calculationState = maybeNewState,
                    )
                }
            }

        val resultIncludingStateChange =
            result.flatMapMany {
                val calculationResults = it.calculationResults.toMutableList()

                val newVersionedPart = it.calculationState.versionedPart
                val newFieldTracker = it.calculationState.threeDbFieldTracker

                if (newVersionedPart.res != initialState.versionedPart.res) {
                    logger.info("--- versionedPart changed to $newVersionedPart")
                    calculationResults.add(
                        createSingleCalculationResult(versionedPartField, newVersionedPart, fieldsForEntity),
                    )
                }
                if (newFieldTracker != initialState.threeDbFieldTracker) {
                    logger.info("--- overwrites changed to $newFieldTracker")
                    calculationResults.add(
                        createSingleCalculationResult(
                            threeDbOverwriteTrackerField,
                            ThreeDbOverwriteTracker(newFieldTracker),
                            fieldsForEntity,
                        ),
                    )
                }
                calculationResults.toFlux()
            }

        return resultIncludingStateChange
    }

    private fun createSingleCalculationResult(
        field: Field,
        fieldResult: FieldResultStar,
        fieldsForEntity: ThreeDbFieldsForEntity,
    ): SingleFieldCalculationResult {
        val versionedResult =
            mapFieldResult(
                field.getCurrentVersionedResult(),
                fieldsForEntity.version,
                fieldResult,
                field,
            )

        return SingleFieldCalculationResult(field, versionedResult)
    }

    private fun calculateField(
        field: Field,
        fieldsForEntity: ThreeDbFieldsForEntity,
        initialState: CalculationState,
    ): Mono<Pair<SingleFieldCalculationResult, CalculationState>> {
        val (_, recalcType) =
            shouldRecalculate(
                field,
                fieldsForEntity.version,
                fieldsForEntity.request.forceRecalculate,
                hashEvaluation = true,
                fieldsForEntity.request.onlyNewFieldsShouldBeRecalculated,
                alsoConsiderSystemValueRecalculation = true,
            )
        val shouldRecalculate = recalcType.isNormalRecalculationNeeded(field)

        // it would be nice to construct `shouldRecalculate` inside FieldCalculationContext,
        // but we need a protected fun from the VirtualFieldCalculator.
        val context = FieldCalculationContext.create(field, shouldRecalculate, initialState)
        val updatedState =
            initialState.copy(
                threeDbFieldTracker =
                    initialState.threeDbFieldTracker.copy(
                        introducedResources = initialState.threeDbFieldTracker.introducedResources + context.fieldName,
                    ),
            )

        return maybeDeleteResource(context, updatedState, fieldsForEntity)
            .flatMap { maybeNewState ->
                maybePostResource(context, maybeNewState, fieldsForEntity)
            }.flatMap { maybeNewState ->
                calculateNewFieldResult(field, context, fieldsForEntity, maybeNewState)
            }.map { (fieldResult, maybeNewState) ->
                val calculationResult = createSingleCalculationResult(field, fieldResult, fieldsForEntity)
                calculationResult to maybeNewState
            }
    }

    private fun maybeDeleteResource(
        context: FieldCalculationContext,
        state: CalculationState,
        fieldsForEntity: ThreeDbFieldsForEntity,
    ): Mono<CalculationState> =
        // if a field is calculated on NBK side, a reset-to-system-value overwrites the resource in 3DB, so no need to delete.
        if (!context.isNbkCalculated && context.resetToSystemValueNow) {
            deleteResource(fieldsForEntity, state.versionedPart.res, context.resourceType)
                .map {
                    val newVersionedPart = state.versionedPart.withRes(it) as VersionedPart
                    val newOverwrittenResources = state.threeDbFieldTracker.overwrittenResources.toMutableSet()
                    val removed = newOverwrittenResources.remove(context.fieldName)
                    require(removed)
                    state.copy(
                        versionedPart = newVersionedPart,
                        threeDbFieldTracker = state.threeDbFieldTracker.copy(overwrittenResources = newOverwrittenResources),
                    )
                }
        } else {
            Mono.just(state)
        }

    private fun maybePostResource(
        context: FieldCalculationContext,
        state: CalculationState,
        fieldsForEntity: ThreeDbFieldsForEntity,
    ): Mono<CalculationState> =
        if (context.newInputWasSetInThisRound) {
            setResource(
                fieldsForEntity,
                state.versionedPart.res,
                context.resourceType,
                context.fieldValueBeforePotentialNewCalculation!!,
            ).map {
                val newVersionedPart = state.versionedPart.withRes(it) as VersionedPart
                val newOverwrittenResources = state.threeDbFieldTracker.overwrittenResources.toMutableSet()
                newOverwrittenResources.add(context.fieldName)
                state.copy(
                    versionedPart = newVersionedPart,
                    threeDbFieldTracker = state.threeDbFieldTracker.copy(overwrittenResources = newOverwrittenResources),
                )
            }
        } else {
            Mono.just(state)
        }

    private fun calculateNewFieldResult(
        field: Field,
        context: FieldCalculationContext,
        fieldsForEntity: ThreeDbFieldsForEntity,
        state: CalculationState,
    ): Mono<Pair<FieldResult<*, *>, CalculationState>> {
        // Recalculate calculated value, or reuse the value we already know if possible.
        val calculatedValue =
            if (context.shouldRecalculate) {
                if (context.isNbkCalculated) {
                    fieldResultCalculation.computeFieldResult(
                        field,
                        fieldsForEntity.version,
                        fieldsForEntity.request.forceRecalculate,
                    )
                } else {
                    getResource(
                        fieldsForEntity,
                        state.versionedPart.res,
                        context.resourceType,
                        useCalculatedValue = true,
                    ).onErrorResume { err ->
                        logger.warn("unable to calculate ${context.resourceType} - fallback to Null [error: '$err']")
                        Mono.just(Null())
                    }
                }
            } else {
                val oldSystemValue = field.getSystemValue()
                requireNotNull(oldSystemValue)
                // SOURCE.R should only happen if a non-overwritten field is reset to system value
                require(oldSystemValue.source in listOf(FieldResult.SOURCE.C, FieldResult.SOURCE.R))
                Mono.just(oldSystemValue)
            }

        // Assemble new field Result, potentially set value in 3DB for NbkCalculated fields.
        return calculatedValue
            .flatMap { calculated ->
                if (context.isFieldWithUserInput) {
                    val fieldResult = context.fieldValueBeforePotentialNewCalculation!!.withSystemValue(calculated.res)
                    Mono.just(fieldResult to state)
                } else {
                    require(calculated.systemValue == null)
                    val maybeSetResource =
                        if (context.isNbkCalculated && context.valueMightHaveChanged) {
                            setResource(fieldsForEntity, state.versionedPart.res, context.resourceType, calculated)
                                .map {
                                    val newVersionedPart = state.versionedPart.withRes(it) as VersionedPart
                                    state.copy(versionedPart = newVersionedPart)
                                }
                        } else {
                            Mono.just(state)
                        }
                    maybeSetResource.map { maybeNewState ->
                        calculated to maybeNewState
                    }
                }
            }
    }

    private fun getThreeDbHeader(
        fieldsForEntity: ThreeDbFieldsForEntity,
        versionedPart: ThreeDbVersionedPart,
    ): ThreeDbHeader = ThreeDbHeader(fieldsForEntity.request.accessCheck, versionedPart)

    private fun getResource(
        fieldsForEntity: ThreeDbFieldsForEntity,
        versionedPart: ThreeDbVersionedPart,
        resourceType: ThreeDbFieldType.Resource,
        useCalculatedValue: Boolean,
    ): Mono<FieldResultStar> {
        val header = getThreeDbHeader(fieldsForEntity, versionedPart)
        return threeDbService.getResource(header, resourceType, useCalculatedValue)
    }

    private fun setResource(
        fieldsForEntity: ThreeDbFieldsForEntity,
        versionedPart: ThreeDbVersionedPart,
        resourceType: ThreeDbFieldType.Resource,
        value: FieldResultStar,
    ): Mono<ThreeDbVersionedPart> {
        val header = getThreeDbHeader(fieldsForEntity, versionedPart)
        return threeDbService.setResource(header, resourceType, value)
    }

    private fun deleteResource(
        fieldsForEntity: ThreeDbFieldsForEntity,
        versionedPart: ThreeDbVersionedPart,
        resourceType: ThreeDbFieldType.Resource,
    ): Mono<ThreeDbVersionedPart> {
        val header = getThreeDbHeader(fieldsForEntity, versionedPart)
        return threeDbService.deleteResource(header, resourceType)
    }
}

// the whole field graph is too much, since we can have multiple cost module calculations at once
private data class ThreeDbFieldsForEntity(
    val versionedPart: Pair<VersionedPart, Field>?,
    val threeDbResourceTracker: Pair<ThreeDbOverwriteTracker, Field>?,
    val request: CalculationRequest,
    val version: Int,
) {
    init {
        require(versionedPart?.second?.entity?._id == threeDbResourceTracker?.second?.entity?._id)
    }
}

private data class BulkedFieldsCalculationResult(
    val calculationResults: List<SingleFieldCalculationResult>,
    val calculationState: CalculationState,
)

private data class CalculationState(
    val versionedPart: VersionedPart,
    val threeDbFieldTracker: ThreeDbOverwrites,
)

private class FieldCalculationContext private constructor(
    val fieldName: String,
    val isNbkCalculated: Boolean,
    val isFieldWithUserInput: Boolean,
    val resetToSystemValueNow: Boolean,
    val newInputWasSetInThisRound: Boolean,
    val shouldRecalculate: Boolean,
    val fieldValueBeforePotentialNewCalculation: FieldResultStar?,
    val resourceType: ThreeDbFieldType.Resource,
) {
    val valueMightHaveChanged = newInputWasSetInThisRound || resetToSystemValueNow || (!isFieldWithUserInput && shouldRecalculate)

    companion object {
        fun create(
            field: Field,
            shouldRecalculate: Boolean,
            state: CalculationState,
        ): FieldCalculationContext {
            val threeDbResourceField = field.model.threeDbFieldType
            require(threeDbResourceField is ThreeDbFieldType.ThreeDbField)

            val resourceWasOverwritten = state.threeDbFieldTracker.overwrittenResources.contains(field.name)
            val resourceWasIntroduced = state.threeDbFieldTracker.introducedResources.contains(field.name)

            val isNbkCalculated = threeDbResourceField.isNbkCalculated
            val isFieldWithUserInput = field.sourceEquals(FieldResult.SOURCE.I)
            val resetToSystemValueNow = !isFieldWithUserInput && resourceWasOverwritten
            // In wizard-edit-flow, if a resource is overwritten and not changed, the version is updated,
            // and therefore we have to assume it's a new input.
            val newInputWasSetInThisRound = field.hasOldInput() || (isFieldWithUserInput && !resourceWasIntroduced)
            require(!newInputWasSetInThisRound || isFieldWithUserInput)
            val fieldValueBeforePotentialNewCalculation = field.getOldInputOrResultFallbackInitial()?.result

            val nonThreeDbDependencies = getNonThreeDbDependencies(field)

            // Unfortunately, there seem to be cases where
            //      resetToSystemValueNow != field.sourceEquals(FieldResult.SOURCE.R)
            // so we
            //  a) can't require this,
            //  b) have to track resourceWasOverwritten in the threeDbFieldTracker in the first place.

            require(isNbkCalculated || nonThreeDbDependencies.isEmpty()) {
                val fieldNames = nonThreeDbDependencies.map { it.name }
                "field '${field.name}' is 3DB calculated, but has the following non-3db dependencies: $fieldNames"
            }

            require(!(newInputWasSetInThisRound && resetToSystemValueNow)) {
                "issue with ${field.name}"
            }

            return FieldCalculationContext(
                field.name,
                isNbkCalculated,
                isFieldWithUserInput,
                resetToSystemValueNow,
                newInputWasSetInThisRound,
                shouldRecalculate,
                fieldValueBeforePotentialNewCalculation,
                threeDbResourceField.resourceType,
            )
        }

        private fun getNonThreeDbDependencies(field: Field): List<InputKey> {
            requireNotNull(field.model.threeDbFieldType)

            return field.inputs.filter { it.linkedField != null && it.linkedField.model.threeDbFieldType == null }
        }
    }
}
