package com.nu.bom.core.manufacturing.customfields

import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.ValueType
import com.nu.bom.core.manufacturing.annotations.entityfieldmetainfo.DefaultUnitEntityFieldMetaInfo
import com.nu.bom.core.manufacturing.annotations.entityfieldmetainfo.DisplayLabelEntityFieldMetaInfo
import com.nu.bom.core.manufacturing.annotations.entityfieldmetainfo.LabelEntityFieldMetaInfo
import com.nu.bom.core.manufacturing.annotations.entityfieldmetainfo.ObjectViewEntityFieldMetaInfo
import com.nu.bom.core.manufacturing.annotations.entityfieldmetainfo.PathEntityFieldMetaInfo
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.FieldsBuilderConstants
import com.nu.bom.core.manufacturing.configurablefields.builder.SimpleDynamicFieldConfigBuilder
import com.nu.bom.core.manufacturing.configurablefields.config.EntityFieldConfigurations
import com.nu.bom.core.manufacturing.configurablefields.config.EntityFieldConfigurationsWithContext
import com.nu.bom.core.manufacturing.configurablefields.config.FieldConfig
import com.nu.bom.core.manufacturing.configurablefields.config.ParameterConfig
import com.nu.bom.core.manufacturing.configurablefields.defaultprovider.ConfigNumDefaultProvider
import com.nu.bom.core.manufacturing.configurablefields.defaultprovider.ConfigTextDefaultProvider
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.fieldTypes.DynamicBehaviourGeneration
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultWrapper
import com.nu.bom.core.manufacturing.fieldTypes.Null
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.masterdata.masterdataFieldsBuilder.MasterdataEffectivityFieldsHelper
import com.nu.bom.core.manufacturing.masterdata.masterdataFieldsBuilder.MasterdataFieldMappingHelper
import com.nu.bom.core.manufacturing.service.FieldType
import com.nu.bom.core.model.configurations.CustomCostField
import com.nu.bom.core.model.configurations.CustomField
import com.nu.bom.core.model.configurations.CustomFieldSection
import com.nu.bom.core.model.configurations.CustomFieldsConfiguration
import com.nu.bom.core.model.configurations.CustomMasterdataClassificationField
import com.nu.bom.core.model.configurations.CustomMasterdataLovField
import com.nu.bom.core.service.configurations.ConfigType
import com.nu.bom.core.service.configurations.CustomFieldsTsetConfigurationService
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.getParameterNameAndCheckType
import org.springframework.stereotype.Service
import kotlin.reflect.full.valueParameters

@Service
class CustomFieldsBuilder(
    private val entityManager: EntityManager,
) : SimpleDynamicFieldConfigBuilder<CustomFieldsConfiguration>(ConfigType.CustomFields) {
    companion object {
        fun lovPath(lovTypeKey: String) = "/api/md/v1/lovtypes/$lovTypeKey/entries"

        fun classificationPath(classificationTypeKey: String) =
            "/api/md/v1/classificationtypes/$classificationTypeKey/roots?includeDescendants=true"

        fun nameFieldName(fieldKey: String) = fieldKey + "Name"
    }

    override fun createFieldConfigurations(configuration: CustomFieldsConfiguration): EntityFieldConfigurationsWithContext {
        val classificationsWithIndex = mutableListOf<Pair<CustomMasterdataClassificationField, Int>>()
        val idxAdjustmentPerSection = mutableMapOf<CustomFieldSection, Int>()
        val customFields =
            configuration.customFields.flatMapIndexed { idx, cfg ->
                val idxAdjustment = idxAdjustmentPerSection.getOrDefault(cfg.getSectionOrDefault(), 0)
                when (cfg) {
                    is CustomCostField -> listOf(createCostField(cfg, idx + idxAdjustment))
                    is CustomMasterdataLovField -> createLovField(cfg, idx + idxAdjustment)
                    is CustomMasterdataClassificationField -> {
                        val createdFields = createClassificationField(cfg, idx + idxAdjustment)
                        if (cfg.includeClassificationFields) {
                            classificationsWithIndex.add(cfg to idx + idxAdjustment)
                            idxAdjustmentPerSection[cfg.getSectionOrDefault()] = idxAdjustment + MAX_CLASSIFICATION_FIELDS_PER_TYPE
                        }
                        createdFields
                    }
                }
            }
        val classificationBehaviourCreationFields = createClassificationBehaviours(classificationsWithIndex)

        return EntityFieldConfigurationsWithContext(
            EntityFieldConfigurations().apply { add(Manufacturing::class, customFields + classificationBehaviourCreationFields) },
            CustomFieldsContext("CustomFields"),
        )
    }

    private fun createClassificationBehaviours(
        classificationsWithIndex: List<Pair<CustomMasterdataClassificationField, Int>>,
    ): List<FieldConfig> {
        val groupedClassifications =
            classificationsWithIndex.map { it.first }
                .groupBy({ it.classificationTypeKey to it.getSectionOrDefault() }, { it.fieldKey })

        return groupedClassifications.flatMap { (key, fieldKeys) ->
            val (classificationType, section) = key
            val startIndex =
                classificationsWithIndex.firstOrNull {
                    it.first.fieldKey in fieldKeys && it.first.classificationTypeKey == classificationType && it.first.section == section
                }?.second ?: START_INDEX_UNKNOWN_GROUP
            createClassificationBehaviourFields(classificationType, section, fieldKeys, startIndex)
        }
    }

    private fun createCostField(
        config: CustomCostField,
        index: Int,
    ) = FieldConfig(
        fieldName = config.fieldKey,
        kFunction = CustomFieldsContext::identity,
        fieldClass = entityManager.getFieldType(config.fieldType).kotlin,
        parameters = defaultValueParam(config.defaultValue),
        entityFieldMetaInfos = baseMeta(config, index) + costMeta(config),
        availableOutsideTheEngine = true,
    )

    private fun createLovField(
        config: CustomMasterdataLovField,
        index: Int,
    ) = listOf(
        FieldConfig(
            fieldName = config.fieldKey,
            kFunction = CustomFieldsContext::identity,
            fieldClass = MasterdataFieldMappingHelper.getLovFieldClass(config.lovTypeKey),
            parameters = defaultValueParam(config.defaultValue),
            entityFieldMetaInfos = baseMeta(config, index) + lovMeta(config),
            availableOutsideTheEngine = true,
        ),
        FieldConfig(
            fieldName = nameFieldName(config.fieldKey),
            kFunction = CustomFieldsContext::lovEntryName,
            fieldClass = Text::class,
            parameters = lovNameParam(config),
            entityFieldMetaInfos = emptyList(),
            availableOutsideTheEngine = true,
        ),
    )

    private fun createClassificationField(
        config: CustomMasterdataClassificationField,
        index: Int,
    ): List<FieldConfig> {
        val classificationFields =
            listOf(
                FieldConfig(
                    fieldName = config.fieldKey,
                    kFunction = CustomFieldsContext::identity,
                    fieldClass = Text::class,
                    parameters = defaultValueParam(config.defaultValue),
                    entityFieldMetaInfos = baseMeta(config, index) + classificationMeta(config),
                    availableOutsideTheEngine = true,
                ),
                FieldConfig(
                    fieldName = nameFieldName(config.fieldKey),
                    kFunction = CustomFieldsContext::classificationName,
                    fieldClass = Text::class,
                    parameters = classificationNameParam(config),
                    entityFieldMetaInfos = emptyList(),
                    availableOutsideTheEngine = true,
                ),
            )
        return classificationFields
    }

    private fun createClassificationBehaviourFields(
        classificationTypeKey: String,
        section: CustomFieldSection,
        // There may be multiple custom fields referencing the same type
        fieldKeys: List<String>,
        startIndex: Int,
    ): List<FieldConfig> =
        listOf(
            FieldConfig(
                fieldName = classFieldsBehaviourFieldName(classificationTypeKey, section),
                kFunction = CustomFieldsContext::createClassificationFields,
                fieldClass = DynamicBehaviourGeneration::class,
                parameters =
                    mapOf(
                        CustomFieldsContext::createClassificationFields.getParameterNameAndCheckType(
                            ClFieldsParameterIndices.CLASSIFICATION_TYPE_KEY,
                            Text::class,
                        ) to
                            listOf(
                                ParameterConfig(FieldsBuilderConstants.CONST_FIELD_NAME, ConfigTextDefaultProvider(classificationTypeKey)),
                            ),
                        CustomFieldsContext::createClassificationFields.getParameterNameAndCheckType(
                            ClFieldsParameterIndices.CLASSIFICATION_KEYS,
                            List::class,
                        ) to
                            fieldKeys.map(::ParameterConfig),
                        CustomFieldsContext::createClassificationFields.getParameterNameAndCheckType(
                            ClFieldsParameterIndices.OBJECT_VIEW,
                            Text::class,
                        ) to
                            listOf(ParameterConfig(FieldsBuilderConstants.CONST_FIELD_NAME, ConfigTextDefaultProvider(section.objectView))),
                        CustomFieldsContext::createClassificationFields.getParameterNameAndCheckType(
                            ClFieldsParameterIndices.START_INDEX,
                            Num::class,
                        ) to
                            listOf(
                                ParameterConfig(
                                    FieldsBuilderConstants.CONST_FIELD_NAME,
                                    ConfigNumDefaultProvider(startIndex.toBigDecimal()),
                                ),
                            ),
                    ),
                entityFieldMetaInfos = emptyList(),
                availableOutsideTheEngine = true,
                fieldType = FieldType.BehaviourCreation,
            ),
        )

    private fun classFieldsBehaviourFieldName(
        classTypeKey: String,
        section: CustomFieldSection,
    ) = CustomFieldsConfiguration.CUSTOM_FIELD_PREFIX +
        section.objectView + '_' +
        MasterdataEffectivityFieldsHelper.sanitizeFieldName(classTypeKey) +
        "ClassificationFieldsBehaviour"

    private fun lovNameParam(config: CustomMasterdataLovField) =
        mapOf(
            CustomFieldsContext::lovEntryName.valueParameters.first().name!! to
                listOf(
                    ParameterConfig(config.fieldKey),
                ),
        )

    private fun classificationNameParam(config: CustomMasterdataClassificationField) =
        mapOf(
            CustomFieldsContext::classificationName.valueParameters.first().name!! to
                listOf(
                    ParameterConfig(config.fieldKey),
                ),
        )

    private fun defaultValueParam(value: FieldResultStar?) =
        mapOf(
            CustomFieldsContext::identity.valueParameters.single().name!! to
                listOf(
                    ParameterConfig(
                        FieldsBuilderConstants.CONST_FIELD_NAME,
                        { FieldResultWrapper(value ?: Null()) },
                    ),
                ),
        )

    private fun baseMeta(
        config: CustomField,
        index: Int,
    ) = listOf(
        DisplayLabelEntityFieldMetaInfo(config.displayName),
        ObjectViewEntityFieldMetaInfo(config.getSectionOrDefault().objectView, index, mode = ObjectView.Mode.both),
    )

    private fun lovMeta(config: CustomMasterdataLovField) =
        listOf(
            PathEntityFieldMetaInfo(lovPath(config.lovTypeKey), remoteSearch = false, grayOutMissingItem = true),
            LabelEntityFieldMetaInfo(nameFieldName(config.fieldKey)),
        )

    private fun classificationMeta(config: CustomMasterdataClassificationField) =
        listOf(
            PathEntityFieldMetaInfo(
                classificationPath(config.classificationTypeKey),
                remoteSearch = false,
                grayOutMissingItem = true,
                valueType = ValueType.MD_CLASSIFICATION,
            ),
            LabelEntityFieldMetaInfo(nameFieldName(config.fieldKey)),
        )

    private fun costMeta(config: CustomCostField) =
        listOfNotNull(
            config.displayUnit?.let(::DefaultUnitEntityFieldMetaInfo),
        )

    override fun getDefaultConfiguration(): CustomFieldsConfiguration = CustomFieldsTsetConfigurationService.defaultConfiguration
}

// magic 500: assume that we will have at most 500 classification fields
// so the index adjustment makes sure, that the classification fields appear below to
// corresponding classification dropdown
private const val MAX_CLASSIFICATION_FIELDS_PER_TYPE = 500

// very large number to position fields where no dedicated startIndex can be found at the end (should never happen)
private const val START_INDEX_UNKNOWN_GROUP = 10000

private object ClFieldsParameterIndices {
    const val CLASSIFICATION_TYPE_KEY = 1
    const val CLASSIFICATION_KEYS = 2
    const val OBJECT_VIEW = 3
    const val START_INDEX = 4
}
