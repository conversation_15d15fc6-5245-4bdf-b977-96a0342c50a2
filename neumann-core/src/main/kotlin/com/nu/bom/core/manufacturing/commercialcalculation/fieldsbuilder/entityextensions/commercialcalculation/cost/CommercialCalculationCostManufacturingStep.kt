package com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost

import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Extends
import com.nu.bom.core.manufacturing.annotations.FieldName
import com.nu.bom.core.manufacturing.annotations.FilteredChildren
import com.nu.bom.core.manufacturing.annotations.InterfaceField
import com.nu.bom.core.manufacturing.annotations.StaticDenominatorUnit
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.CommercialCalculationException
import com.nu.bom.core.manufacturing.entities.ManufacturingEntityExtension
import com.nu.bom.core.manufacturing.entities.ManufacturingStep
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_FACTOR_INTEREST_RATES_ENTITY_NAME
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.StaticUnitOverride
import com.nu.bom.core.manufacturing.extension.COMMERCIAL_CALCULATION_COST_FIELD_PACKAGE
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.utils.getResultWithSelectedAttributes
import org.bson.types.ObjectId

@Extends(
    [ManufacturingStep::class],
    COMMERCIAL_CALCULATION_COST_FIELD_PACKAGE,
)
@EntityType(Entities.NONE)
class CommercialCalculationCostManufacturingStep(name: String) : ManufacturingEntityExtension(name) {
    @StaticDenominatorUnit(StaticUnitOverride.YEAR)
    fun interestRate(
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_INTEREST,
            nameFilter = TSET_COST_FACTOR_INTEREST_RATES_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        ) value: Map<ObjectId, Rate>?,
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_INTEREST,
            nameFilter = TSET_COST_FACTOR_INTEREST_RATES_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        @FieldName("location")
        costFactorLocations: Map<ObjectId, Text>,
        location: Text,
    ): Rate? {
        return getResultWithSelectedAttributes(value, costFactorLocations, location)
    }

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun costPerPartRoughProcess(): Money = Money.ZERO

    @InterfaceField
    fun manufacturingScrapRate(): Rate =
        throw CommercialCalculationException.missingRequiredField(
            CommercialCalculationCostManufacturingStep::manufacturingScrapRate.name,
            CommercialCalculationCostManufacturingStep::class.simpleName!!,
        )

    @InterfaceField
    fun accumulatedMaterialScrapRate(): Rate =
        throw CommercialCalculationException.missingRequiredField(
            CommercialCalculationCostManufacturingStep::accumulatedMaterialScrapRate.name,
            CommercialCalculationCostManufacturingStep::class.simpleName!!,
        )
}
