package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.exception.ErrorCodedException
import com.nu.bom.core.exception.readable.ErrorCode
import com.nu.bom.core.manufacturing.annotations.Units
import java.math.BigDecimal
import java.math.MathContext

enum class CycleTimeUnit(
    override val baseFactor: BigDecimal,
    override val hideInDropdown: Boolean,
    private val reciprocal: Boolean? = null,
) : TypeUnit {
    SECOND(BigDecimal.ONE, false),
    MINUTE(60.toBigDecimal(), false),
    HOUR(3600.toBigDecimal(), false),
    STROKES_PER_SECONDS(BigDecimal.ONE, false, true),
    STROKES_PER_MINUTES(60.toBigDecimal(), false, true),
    STROKES_PER_HOURS(3600.toBigDecimal(), false, true),

    @Deprecated("Use SECOND")
    SECONDS(BigDecimal.ONE, true),

    @Deprecated("Use MINUTE")
    MINUTES(60.toBigDecimal(), true),

    @Deprecated("Use HOUR")
    HOURS(3600.toBigDecimal(), true),
    ;

    override fun reciprocal() = this.reciprocal

    override val type = TypeUnits.STROKES
}

@Units(CycleTimeUnit::class)
class CycleTime(
    res: BigDecimal,
    unit: CycleTimeUnit,
) : NumericFieldResultWithUnit<CycleTime, CycleTimeUnit>(
        if (unit in strokeUnits && res.toDouble() > 0.0) BigDecimal(1.0 / res.toDouble()) else res,
        unit,
    ) {
    companion object {
        val timeUnits = listOf(CycleTimeUnit.HOUR, CycleTimeUnit.MINUTE, CycleTimeUnit.SECOND)
        val strokeUnits =
            listOf(
                CycleTimeUnit.STROKES_PER_SECONDS,
                CycleTimeUnit.STROKES_PER_MINUTES,
                CycleTimeUnit.STROKES_PER_HOURS,
            )

        fun create(
            length: Length,
            speed: Speed,
        ) = CycleTime(length.inMeter / speed.inMPerSec, CycleTimeUnit.SECOND)
    }
    constructor(res: Double, unit: CycleTimeUnit) : this(res.toBigDecimal(), unit)
    constructor(res: String, unit: String) : this(BigDecimal(res), CycleTimeUnit.valueOf(unit))
    constructor(res: BigDecimal, unit: String) : this(res, CycleTimeUnit.valueOf(unit))

    constructor(res: Time) : this(res.res, CycleTimeUnit.SECOND)

    val inSeconds: BigDecimal get() {
        return toCycleTime(CycleTimeUnit.SECOND)
    }
    val inMinutes: BigDecimal get() {
        return toCycleTime(CycleTimeUnit.MINUTE)
    }
    val inHours: BigDecimal get() {
        return toCycleTime(CycleTimeUnit.HOUR)
    }

    fun toCycleTime(otherUnit: CycleTimeUnit): BigDecimal {
        if (!checkTypeConversion(otherUnit, unit)) {
            throw IllegalArgumentException("invalid type conversion")
        }
        return if (unit in timeUnits && otherUnit in timeUnits) {
            convertTimeUnits(otherUnit)
        } else if (unit in strokeUnits && otherUnit in strokeUnits) {
            convertStrokeUnits(otherUnit)
        } else {
            convertDifferentUnits(otherUnit)
        }
    }

    private fun checkTypeConversion(
        unit1: CycleTimeUnit,
        unit2: CycleTimeUnit,
    ): Boolean = unit1.type == unit2.type

    private fun convertTimeUnits(otherUnit: CycleTimeUnit): BigDecimal = res.divide(otherUnit.baseFactor, MathContext.DECIMAL64)

    private fun convertStrokeUnits(otherUnit: CycleTimeUnit): BigDecimal = res * otherUnit.baseFactor

    private fun convertDifferentUnits(otherUnit: CycleTimeUnit): BigDecimal =
        try {
            if (unit in strokeUnits) {
                BigDecimal((BigDecimal.ONE.toDouble() / res.toDouble()) / otherUnit.baseFactor.toDouble())
            } else {
                BigDecimal((BigDecimal.ONE.toDouble() / res.toDouble()) * otherUnit.baseFactor.toDouble())
            }
        } catch (e: java.lang.NumberFormatException) {
            throw ErrorCodedException.internalServerError(
                ErrorCode.INVALID_NUMBER_VALUE_CONVERSION,
                "${res.toDouble()} conversion leading to Infinite or NaN.",
            )
        }
}
