package com.nu.bom.core.service.wizard.config

import com.fasterxml.jackson.annotation.JsonInclude

@JsonInclude(JsonInclude.Include.NON_NULL)
data class WizardStepPageConfig(
    val title: WizardStepPageTitleConfig?,
    val cards: List<WizardStepCardConfig>,
    val hasCostTable: Boolean? = null,
) {
    init {
        require(cards.isNotEmpty()) { "at least one card must be provided." }
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
data class WizardStepPageTitleConfig(
    val title: String?,
    val kpi: String?,
) {
    init {
        require(title != null || kpi != null) { "either title or kpi must be set" }
        require(title == null || title.isNotBlank()) { "title must not be blank." }
        require(kpi == null || kpi.isNotBlank()) { "kpi must not be blank." }
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
data class WizardStepCardConfig(
    val title: String?,
    val collapsible: Boolean? = null,
    val initiallyCollaped: Boolean? = null,
    val withSeperator: Boolean? = null,
    val columns: List<WizardStepColumnConfig>,
) {
    constructor(
        title: String?,
        collapsible: Boolean? = null,
        initiallyCollaped: Boolean? = null,
        withSeperator: Boolean? = null,
        column: WizardStepColumnConfig,
    ) : this(title, collapsible, initiallyCollaped, withSeperator, listOf(column))

    init {
        require(columns.isNotEmpty()) { "at least one column must be provided." }
        require(title == null || title.isNotBlank()) { "title must not be blank." }
    }
}

data class WizardStepColumnConfig(
    val sections: List<WizardStepSectionConfig>,
) {
    constructor(section: WizardStepSectionConfig) : this(listOf(section))

    init {
        require(sections.isNotEmpty()) { "at least one section must be provided." }
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
data class WizardStepSectionConfig(
    val title: String?,
    val fieldNames: List<String>?,
    val imageSource: String? = null,
) {
    init {
        require(fieldNames == null || fieldNames.isNotEmpty()) { "fieldNames should be null or non-empty" }
        require(fieldNames != null || imageSource != null) { "either fieldNames or image source must be set." }
        require(title == null || title.isNotBlank()) { "title must not be blank." }
        require(imageSource == null || imageSource.isNotBlank()) { "imageSource must not be blank." }
    }
}
