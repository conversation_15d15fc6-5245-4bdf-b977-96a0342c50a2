package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.utils.toUpperSnakeCase

class FieldCalculatorType(res: Selection) : SelectEnumFieldResult<FieldCalculatorType.Selection, FieldCalculatorType>(res) {
    enum class Selection {
        DIRECT,
        DETAILED,
    }

    constructor(res: Text) : this(valueOf(res.res).res)

    companion object {
        val DIRECT = FieldCalculatorType(FieldCalculatorType.Selection.DIRECT)
        val DETAILED = FieldCalculatorType(FieldCalculatorType.Selection.DETAILED)

        fun valueOf(str: String) = FieldCalculatorType(FieldCalculatorType.Selection.valueOf(str.toUpperSnakeCase()))
    }
}
