package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.entities.masterdata.material.MaterialConsumerExtension
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.MaterialFurnaceType
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import org.springframework.stereotype.Service

@Service
class DefaultMdFurnaceTypeMigration : ManufacturingModelEntityMapper {
    override val changeSetId: MigrationChangeSetId
        get() = MigrationChangeSetId("2025-05-17-add-default-furnace-migration")

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity =
        entity.copyAll(
            fieldWithResults = addDefaultFurnaceType(entity.fieldWithResults, entity.version),
            initialFieldWithResults = addDefaultFurnaceType(entity.initialFieldWithResults, entity.version),
        )

    private fun addDefaultFurnaceType(
        fields: Map<String, FieldResultModel>,
        version: Int,
    ): Map<String, FieldResultModel> =
        fields
            .toMutableMap()
            .apply {
                putIfAbsent(
                    MaterialConsumerExtension::materialProcessFurnaceType.name,
                    FieldResultModel(
                        version,
                        version,
                        MaterialFurnaceType::class.simpleName!!,
                        MaterialFurnaceType.BLAST_FURNACE.res,
                        FieldResult.SOURCE.C.name,
                    ),
                )
            }
}
