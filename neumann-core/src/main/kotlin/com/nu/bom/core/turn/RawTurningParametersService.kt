package com.nu.bom.core.turn

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.service.MaterialService
import com.nu.bom.core.turn.model.ProfileData
import com.nu.bom.core.turn.model.RawInputProperties
import com.nu.bom.core.turn.model.RawProfileRequest
import com.nu.bom.core.turn.model.RawProfileResponse
import com.nu.bom.core.turn.model.Section
import com.nu.bom.core.turn.model.Sketch
import com.nu.bom.core.turn.model.Technology
import com.nu.bom.core.user.AccessCheck
import jakarta.ws.rs.BadRequestException
import org.bson.types.ObjectId
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono

@Service
class RawTurningParametersService(
    private val turningApiService: TurningApiService,
    private val materialService: MaterialService,
) : TurningParameters() {
    override fun fillParameters(
        sketch: Sketch,
        material: String,
        accessCheck: AccessCheck,
        sketchId: ObjectId,
        rawModel: Model,
        partInputGroup: String?,
        marginWidth: Length?,
        netWeightPerPart: Weight,
        barDiameterOverride: Length?,
    ): Mono<List<Section>> {
        if (marginWidth == null) {
            throw BadRequestException("Raw turning without margin width")
        }
        return getRawProfile(sketch, sketchId, partInputGroup!!, marginWidth, rawModel)
            .flatMap { maybeRawProfileResponse ->
                getTurningQualifiedMaterialMasterData(materialService, material, accessCheck).map { rawMaterialBar ->
                    val weightSection =
                        getWeightSection(
                            maybeRawProfileResponse,
                            netWeightPerPart,
                            TurningPropertiesService.getDensity(rawMaterialBar),
                        )
                    val rawPartSection =
                        getDimensionSection(
                            "raw_part",
                            "raw",
                            maybeRawProfileResponse.raw_part,
                        )
                    val finishedPartSection =
                        getDimensionSection(
                            "finished_part",
                            "net",
                            maybeRawProfileResponse.turned_part,
                        )
                    listOf(weightSection, rawPartSection, finishedPartSection)
                }
            }
    }

    private fun getDimensionSection(
        sectionTitle: String,
        fieldPrefix: String,
        profileData: ProfileData,
    ): Section {
        val wallThicknessFP =
            createLengthFieldParameterReadOnly(
                fieldName = "${fieldPrefix}PartMaxWallThickness",
                valueInMm = profileData.max_wallthickness,
            )
        val partLengthFP =
            createLengthFieldParameterReadOnly(
                fieldName = "${fieldPrefix}PartLength",
                valueInMm = profileData.length,
            )
        val partInnerDiameterFP =
            createLengthFieldParameterReadOnly(
                fieldName = "${fieldPrefix}PartInnerDiameter",
                valueInMm = profileData.inner_diameter,
            )
        val partOuterDiameterFP =
            createLengthFieldParameterReadOnly(
                fieldName = "${fieldPrefix}PartOuterDiameter",
                valueInMm = profileData.outer_diameter,
            )

        return Section(
            title = sectionTitle,
            fields = listOf(wallThicknessFP, partLengthFP, partInnerDiameterFP, partOuterDiameterFP),
        )
    }

    private fun getRawProfile(
        sketch: Sketch,
        sketchId: ObjectId,
        partInputGroup: String,
        marginWidth: Length,
        rawModel: Model,
    ): Mono<RawProfileResponse> {
        // Check if point properties are correct.
        // Could throw, e.g. if freistich is not filled out correctly.
        sketch.turning_point_attributes

        val request =
            RawProfileRequest(
                geometry = sketch.geometry,
                technology =
                    Technology(
                        type = rawModel.path.uppercase(),
                        part_input_group = partInputGroup,
                        properties = RawInputProperties(marginWidth),
                    ),
                line_attributes = sketch.turning_line_attributes,
            )
        return turningApiService.rawProfile(
            sketchId.toHexString(),
            request,
        )
    }
}
