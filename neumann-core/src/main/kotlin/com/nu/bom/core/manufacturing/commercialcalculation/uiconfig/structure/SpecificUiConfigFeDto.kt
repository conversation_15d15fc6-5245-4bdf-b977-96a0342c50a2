package com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure

import com.fasterxml.jackson.annotation.JsonAutoDetect
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.SpecificUiConfigurationIdentifiers
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.CardIdentifier
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.TableName
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.CardConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.TableConfigFeDto

@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
data class SpecificUiConfigFeDto(
    override val key: SpecificUiConfigurationIdentifiers,
    override val cards: Map<CardIdentifier, CardConfigFeDto>,
    @JsonDeserialize(keyUsing = TableName.ThisKeyDeserializer::class)
    override val tableConfigs: Map<TableName, TableConfigFeDto>,
) : BaseUiConfigFeDto {
    init {
        verifyThatNoTableConfigsAreMissing()
    }
}
