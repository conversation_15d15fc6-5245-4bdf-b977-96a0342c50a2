package com.nu.bom.core.controller

import com.nu.bom.core.service.bomrads.BomradsImportService
import com.nu.bom.core.service.imports.ProjectImportService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.user.AccessCheckProvider
import com.nu.bom.core.utils.CharUtils
import com.nu.bom.core.utils.ZipUtils
import com.nu.bom.core.utils.subscribeWithContextCapture
import com.nu.bomrads.dto.admin.ImportDTO
import com.nu.bomrads.id.ImportId
import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.http.codec.multipart.FilePart
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RequestPart
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Mono
import reactor.core.scheduler.Schedulers
import java.io.File
import java.nio.file.Files
import java.time.Instant
import java.util.UUID

@RestController
@RequestMapping("/api")
class ProjectImportController(
    private val accessCheckProvider: AccessCheckProvider,
    private val projectImportService: ProjectImportService,
    private val bomradsImportService: BomradsImportService,
) {
    val logger = LoggerFactory.getLogger(ProjectImportController::class.java)!!

    @PostMapping("/project/import/file", consumes = ["multipart/form-data"])
    fun importProjectAsync(
        @AuthenticationPrincipal jwt: Jwt?,
        @RequestPart("file") file: FilePart,
        @RequestParam("key") projectKey: String?,
        @RequestParam("folderId") folderId: String,
    ): Mono<String> =
        accessCheckProvider.doAs(jwt) { accessCheck ->
            importProject(
                accessCheck,
                file,
                projectKey,
                folderId,
            ).map {
                it.idToString()
            }
        }

    fun importProject(
        accessCheck: AccessCheck,
        filePart: FilePart,
        projectKey: String?,
        folderId: String,
    ): Mono<ImportId> {
        val newProjectKey = projectKey ?: CharUtils.randomAlphanumericString(5).uppercase()
        return bomradsImportService.startImport(accessCheck).map { importDto ->
            val s = UUID.randomUUID().toString()

            val importProcess =
                Mono.using({
                    Files.createTempDirectory(newProjectKey).toFile()
                }, { file ->
                    Mono
                        .just(importDto to ZipUtils.unzipFlat(destination = file, s))
                        .flatMap { (importDto, files) ->
                            projectImportService.importProjectFromTsetFile(
                                accessCheck,
                                files,
                                newProjectKey,
                                importDto,
                                folderId,
                            )
                        }
                }, { r ->
                    r.deleteRecursively()
                    File(s).deleteRecursively()
                })
            filePart
                .transferTo(File(s).toPath())
                .then(importProcess)
                .subscribeOn(Schedulers.boundedElastic())
                .onErrorResume { e ->
                    logger.error("Import ${importDto.id} failed with an error ${e.message}", e)
                    bomradsImportService.failImport(accessCheck, importDto.id, e.message).then(Mono.error(e))
                }.subscribeWithContextCapture()

            importDto.id
        }
    }

    @GetMapping("/project/import/file/{importId}")
    fun getProjectImport(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable importId: UUID,
    ): Mono<ImportDTO> =
        accessCheckProvider.doAs(jwt) {
            bomradsImportService.getImport(it, ImportId(importId))
        }

    @GetMapping("/project/import/status/{importId}")
    fun getProjectImportState(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable importId: UUID,
    ): Mono<ResponseEntity<String>> =
        accessCheckProvider.doAs(jwt) {
            val importMono: Mono<ImportDTO> =
                bomradsImportService.getImport(it, ImportId(importId)).onErrorResume { throwable ->
                    Mono.just(
                        ImportDTO(
                            id = ImportId(importId),
                            importStartedAt = null,
                            importFinished = false,
                            errorMessage = throwable.message,
                        ),
                    )
                }

            // the flow is simple, we first check if something went wrong contacting bomrads, then if something went wrong
            // during import, then if something might be wrong (long-running processes stuck, for example, or we could not contact
            // bomrads due to transient issues) and finally successes (still ongoing or finished)
            importMono.map { import ->
                when {
                    import.importFinished == false && import.errorMessage != null && import.errorMessage!!.contains("404") ->
                        ResponseEntity.notFound().build()

                    import.importFinished == false && import.errorMessage != null ->
                        ResponseEntity.ok("Error contacting bomrads: ${import.errorMessage}")

                    import.importFinished == true && import.errorMessage != null ->
                        ResponseEntity.ok("Error: ${import.errorMessage}")

                    import.importFinished == false &&
                        import.importStartedAt!!.isBefore(
                            Instant.now().minusSeconds(36000),
                        )
                    ->
                        ResponseEntity.ok(
                            "Import $importId not finished and running for over and hour (started at ${import.importStartedAt})",
                        )

                    import.importFinished == false -> ResponseEntity.ok("Import $importId ongoing since ${import.importStartedAt}")
                    else -> ResponseEntity.ok("Import $importId successful")
                }
            }
        }
}
