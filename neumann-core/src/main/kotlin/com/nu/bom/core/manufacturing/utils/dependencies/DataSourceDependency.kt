package com.nu.bom.core.manufacturing.utils.dependencies

import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.service.CalculationRequest
import com.nu.bom.core.manufacturing.service.Field
import com.nu.bom.core.manufacturing.service.InputKey
import com.nu.bom.core.manufacturing.service.RelType
import com.nu.bom.core.manufacturing.service.virtualfield.DataSourcerUpdaterProvider

class DataSourceDependency : DependencyProvider {
    companion object {
        private val provider = DataSourcerUpdaterProvider()
    }

    override fun addDependencies(
        request: CalculationRequest,
        newFields: List<Field>,
        entity: ManufacturingEntity,
        original: ManufacturingEntity,
    ) {
        val allFields = original.getFieldsAsList()

        // if we do not have a virtual field, there is no need to build any dependencies
        val dataSourceVirtualField = provider.getVirtualField(allFields) ?: return

        val dataSourceKeyFields = newFields.filter { it.model.sourceDataKey != null }
        check(dataSourceKeyFields.size <= 1) {
            "Multiple DataSourceFields are not supported: $dataSourceKeyFields"
        }
        val dataSourceKeyField = dataSourceKeyFields.singleOrNull()
        if (dataSourceKeyField != null) {
            val dep = dataSourceKeyField.toInputKey(RelType.CHANGE_TRIGGER).copy(nullable = true)
            // remove placeholder dependency, if there
            dataSourceVirtualField.removeDependencies()
            // add real dependency
            dataSourceVirtualField.addParameter(dep)
        } else if (dataSourceVirtualField.inputs.isEmpty()) {
            // if we do not have the real key field yet, we need to create a placeholder dependency that
            // blocks the calculation of the virtual field until the key field is available
            val dep =
                InputKey(
                    paramPos = 0,
                    entityId = entity.entityId,
                    entityType = entity.getEntityType(),
                    default = null,
                    relType = RelType.CHANGE_TRIGGER,
                    type = "unknown",
                    name = "placeholder until real key field gets available",
                    linkedField = null,
                )
            dataSourceVirtualField.addParameter(dep)
        }

        newFields.filter { it.model.sourceDataInput != null }.forEach {
            // add dependency from any data source input field to the virtual field
            val dep = dataSourceVirtualField.toInputKey(RelType.DATA_SOURCER_KEY_UPDATE).copy(nullable = true)
            it.addOpenDependency(dep)
        }
    }
}
