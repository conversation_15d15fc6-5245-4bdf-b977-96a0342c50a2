package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.entities.RawMaterialSheet
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import org.springframework.stereotype.Service

private const val STRUTUAL = "StructualSteel"
private const val STRUTURAL_CORRECTED = "StructuralSteel"

@Service
class StructualSteelTypoMigration : ManufacturingModelEntityMapper {
    override val changeSetId: MigrationChangeSetId = MigrationChangeSetId("2025-05-19-structural-steel-migration")

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity =
        entity.copyAll(
            fieldWithResults = adjustMaterialGroup(entity.fieldWithResults),
            initialFieldWithResults = adjustMaterialGroup(entity.initialFieldWithResults),
        )

    private fun adjustMaterialGroup(fields: Map<String, FieldResultModel>): Map<String, FieldResultModel> {
        val updatedFields = fields.toMutableMap()

        val materialGroupField = updatedFields[RawMaterialSheet::materialGroup.name]
        if (materialGroupField?.value == STRUTUAL) {
            updatedFields[RawMaterialSheet::materialGroup.name] = materialGroupField.copyAll(value = STRUTURAL_CORRECTED)
        }

        return updatedFields
    }
}
