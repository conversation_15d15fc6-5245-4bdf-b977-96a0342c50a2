package com.nu.bom.core.service

import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.BomNodeReference
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.utils.ManufacturingTreeUtils
import com.nu.bom.core.model.BomEntryRelation
import com.nu.bom.core.model.BomNode
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.Branch
import com.nu.bom.core.model.Merge
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.model.SnapshotId
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.NoValue
import com.nu.bom.core.utils.applyIfExists
import com.nu.bom.core.utils.visitTree
import org.bson.types.ObjectId
import org.springframework.beans.factory.DisposableBean
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.core.scheduler.Schedulers
import java.io.Closeable

@Service
class BomNodeConversionService : Closeable, DisposableBean {
    private val scheduler = Schedulers.newSingle("BomNodeConversionService")

    data class ConversionResult(
        val manufacturing: ManufacturingEntity,
        val requestedBomNodeId: BomNodeId,
        val projectId: ProjectId,
        val nodes: List<BomNodeSnapshot>,
        val reqestedNode: BomNodeSnapshot,
        val mergeResult: List<Merge>,
    )

    /**
     * Extract external parents somehow for  multiparent calculation
     *
     * TODO: Check to use class hierarchy for Resolved vs non-resolved entiies
     *
     * BomNode (abstract)
     *  ResolvedBomNode : BomNode -> adds resolved fieldWithResults
     */
    fun bomNodeToManufacturingCalculationTree(
        snapshot: BomNodeSnapshot,
        extractionBoundaries: Set<BomNodeId>?,
    ): ManufacturingEntity {
        val manufacturing = snapshot.manufacturing!!
        manufacturing.snapshot = snapshot
        manufacturing.bomNodeId = snapshot.bomNodeId()

        resolveParentManufacturings(manufacturing)

        addSubBomsToTree(manufacturing, extractionBoundaries)

        return manufacturing
    }

    private fun manufacturingCalculationTreeToBomNodes(manufacturing: ManufacturingEntity): List<BomNodeSnapshot> {
        val nodes = extractBomNodes(manufacturing).toSet()
        nodes.forEach { removeExternalSubTrees(it.manufacturing!!) }
        return nodes.toList()
    }

    fun manufacturingCalculationTreeToBomNodes(
        manufacturing: ManufacturingEntity,
        rootId: BomNodeId,
    ): BomNodeSnapshot? {
        val nodes = manufacturingCalculationTreeToBomNodes(manufacturing)
        return nodes.find {
            it.bomNodeId() == rootId
        }
    }

    private fun extractBomNodes(manufacturing: ManufacturingEntity): List<BomNodeSnapshot> {
        return manufacturing.visitChildren { manufacturingEntity, _ ->
            manufacturingEntity.snapshot
        }
    }

    fun createConversionResult(
        accessCheck: AccessCheck,
        requestedNodeId: BomNodeId,
        projectId: ProjectId,
        entity: ManufacturingEntity,
        nodesToCheckMerge: List<BomNode>?,
    ): Mono<ConversionResult> {
        val rootSnapshot =
            entity.snapshot ?: throw NullPointerException("No snapshot is set on ${entity.toHumanReadableName()}")
        cleanUnneededSubNodes(entity, rootSnapshot)
        return createSubNodes(rootSnapshot, entity, rootSnapshot, accessCheck).map {
            val nodes = manufacturingCalculationTreeToBomNodes(entity)
            val resultNode =
                checkNotNull(nodes.find { it.bomNodeId() == requestedNodeId }) {
                    "ResultNode not found with $requestedNodeId in $nodes"
                }
            resultNode.updateKpi()
            val mergeResult =
                if (nodesToCheckMerge != null && resultNode.branchEntity != null) {
                    checkMerge(
                        nodesToCheckMerge,
                        resultNode.branchEntity!!,
                    )
                } else {
                    listOf()
                }
            resultNode.openMerges = mergeResult

            ConversionResult(
                manufacturing = entity,
                requestedBomNodeId = requestedNodeId,
                projectId = projectId,
                nodes = nodes,
                reqestedNode = resultNode,
                mergeResult = mergeResult,
            )
        }
    }

    private fun checkMerge(
        nodes: List<BomNode>,
        branch: Branch,
    ): List<Merge> {
        return nodes.mapNotNull {
            it.compareMainWithTarget(branch)
        }
    }

    private fun getBomNodeReferenceIds(entity: ManufacturingEntity): List<ObjectId> =
        entity.visitTree(
            { manufacturingEntity, _ ->
                if (manufacturingEntity is BomNodeReference) {
                    manufacturingEntity._id
                } else {
                    null
                }
            },
            { manufacturingEntity ->
                if (manufacturingEntity !is BomNodeReference) {
                    manufacturingEntity.children
                } else {
                    emptyList()
                }
            },
        )

    private fun cleanUnneededSubNodes(
        manufacturingRoot: ManufacturingEntity,
        rootSnapshot: BomNodeSnapshot,
    ) {
        // clear subnodes here, which doesn't referred anymore by BomEntries
        val entityIds = getBomNodeReferenceIds(manufacturingRoot).toSet()
        rootSnapshot.subNodes = rootSnapshot.subNodes.filter { entityIds.contains(it.bomEntryId) }
    }

    private fun createSubNodes(
        parentSnapshot: BomNodeSnapshot,
        manufacturingRoot: ManufacturingEntity,
        rootSnapshot: BomNodeSnapshot,
        accessCheck: AccessCheck,
    ): Mono<NoValue> {
        return Flux.fromIterable(manufacturingRoot.children).concatMap { entity ->
            if (entity is BomNodeReference) {
                if (entity.children.size == 1) {
                    val childManufacturing =
                        entity.children[0] // Expect always only exactly 1 Manufacturing per BomEntry
                    val childSnapshot =
                        getOrCreateSubNode(
                            parentSnapshot,
                            childManufacturing as BaseManufacturing,
                            entity._id,
                            rootSnapshot,
                            accessCheck,
                        )
                    childManufacturing.snapshot = childSnapshot
                    val calculationRoot =
                        if (childSnapshot.calculationRoot == rootSnapshot.bomNodeId()) rootSnapshot else childSnapshot
                    cleanUnneededSubNodes(childManufacturing, childSnapshot)
                    createSubNodes(childSnapshot, entity, calculationRoot, accessCheck)
                } else {
                    Mono.just(NoValue)
                }
            } else {
                createSubNodes(parentSnapshot, entity, rootSnapshot, accessCheck)
            }
        }.then(Mono.just(NoValue)).subscribeOn(scheduler)
    }

    private fun getOrCreateSubNode(
        parentSnapshot: BomNodeSnapshot,
        entity: BaseManufacturing,
        bomEntryId: ObjectId,
        calculationRootSnapshot: BomNodeSnapshot,
        accessCheck: AccessCheck,
    ): BomNodeSnapshot {
        val newOrExistingSnapshot =
            entity.snapshot
                ?: createSnapshot(parentSnapshot, entity, bomEntryId, calculationRootSnapshot, accessCheck)

        parentSnapshot.replaceChildRelation(
            bomEntryId = bomEntryId,
            newRelation =
                BomEntryRelation(
                    bomEntryId = bomEntryId,
                    bomNodeId = newOrExistingSnapshot.bomNodeId(),
                    version = entity.version,
                    generated = true,
                ).setSnapshot(newOrExistingSnapshot),
        )
        return newOrExistingSnapshot
    }

    private fun createSnapshot(
        parentSnapshot: BomNodeSnapshot,
        entity: BaseManufacturing,
        bomEntryId: ObjectId,
        calculationRootSnapshot: BomNodeSnapshot,
        accessCheck: AccessCheck,
    ): BomNodeSnapshot {
        val subNodeRelation =
            BomEntryRelation(
                bomEntryId = bomEntryId,
                bomNodeId = parentSnapshot.bomNodeId(),
                version = parentSnapshot.manufacturing?.version,
                generated = true,
            )

        val snapshotId = SnapshotId()
        val bomNodeId = BomNodeId()
        val branch = parentSnapshot.branch

        val partName =
            entity.getFieldResultSafeCast<Text>(Manufacturing::partDesignation.name)?.res?.takeIf { it.isNotBlank() }
                ?: entity.getFieldResultSafeCast<Text>(Manufacturing::partName.name)?.res?.takeIf { it.isNotBlank() }
                ?: "SUB"

        val bomNode =
            BomNode(
                name = partName,
                year = parentSnapshot.year,
                projectId = parentSnapshot.projectId(),
                generated = true,
                rootOf = null,
                lastPublishedBranch = parentSnapshot.bomNode!!.lastPublishedBranch,
            )
        bomNode._id = bomNodeId

        val snapshot =
            BomNodeSnapshot(
                name = partName,
                year = parentSnapshot.year,
                manufacturing = entity,
                generated = true,
                title = entity.title ?: parentSnapshot.title,
                accountId = accessCheck.asAccountId(),
            ).setTransientState(bomNode, branch, parentSnapshot.branchEntity)

        snapshot.parentsToUse = listOf(subNodeRelation)
        snapshot.calculationRoot = if (entity.createdBy != null) calculationRootSnapshot.bomNodeId() else null

        bomNode.setBranchHead(branch, snapshotId)
        snapshot._id = snapshotId
        snapshot.setBranchView(parentSnapshot.originalRootSource, null)
        snapshot.protectedAt = parentSnapshot.protectedAt
        snapshot.protectedBy = parentSnapshot.protectedBy
        return snapshot
    }

    fun removeExternalSubTrees(manufacturing: ManufacturingEntity) {
        manufacturing.children.forEach { child ->
            if (child is BomNodeReference) {
                child.children.removeIf { it.snapshot != null }
            }
            removeExternalSubTrees(child)
        }
    }

    private fun addSubBomsToTree(
        manufacturing: ManufacturingEntity,
        extractionBoundaries: Set<BomNodeId>?,
        node: BomNodeSnapshot = manufacturing.snapshot!!,
    ) {
        manufacturing.children.forEach { child ->
            if (child is BomNodeReference) {
                val relation = node.findChildrenByEntityId(child._id)
                if (inBoundary(extractionBoundaries, relation?.bomNodeId)) {
                    // TODO NULL Checks !!
                    checkNotNull(relation) { "No relation found by ${child._id} in ${node.subNodes}" }
                    addSubManuToTree(relation, child, node)
                }
            }
            if (inBoundary(extractionBoundaries, node.bomNodeId())) {
                addSubBomsToTree(
                    child,
                    extractionBoundaries = extractionBoundaries,
                    node = child.snapshot ?: manufacturing.snapshot ?: node,
                )
            }
        }
    }

    private fun addSubManuToTree(
        relation: BomEntryRelation,
        child: ManufacturingEntity,
        node: BomNodeSnapshot,
    ) {
        relation.snapshot?.let { snapshot ->
            val subManufacturing =
                checkNotNull(snapshot.manufacturing) {
                    "No manufacturing in snapshot ${relation.snapshot} - parent snapshot is $node"
                }
            // Only when child not added yet
            if (child.children.find { it._id == subManufacturing._id } == null) {
                subManufacturing.snapshot = snapshot
                subManufacturing.bomNodeId = snapshot.bomNodeId()
                resolveParentManufacturings(subManufacturing)
                child.addChild(subManufacturing)
            }
        }
    }

    private fun inBoundary(
        extractionBoundaries: Set<BomNodeId>?,
        bomNodeId: BomNodeId?,
    ) = extractionBoundaries == null || extractionBoundaries.contains(bomNodeId)

    private fun resolveParentManufacturings(manufacturing: ManufacturingEntity) {
        val parents =
            manufacturing.snapshot?.parentsToUse?.filter {
                // we only want to calculate with the currently loaded snapshots - parentsToUse could point to
                // external parents, which are not relevant to this calculation
                it.snapshot != null
            }?.mapNotNull { bomEntryRelation ->
                // TODO: Parents not found errorhandling
                val snapshot = bomEntryRelation.snapshot!!
                val relationManufacturing =
                    checkNotNull(snapshot.manufacturing) {
                        "No manufacturing found in relation=$bomEntryRelation, snapshot=$snapshot"
                    }
                val parentBomEntry =
                    checkNotNull(
                        ManufacturingTreeUtils.findEntityById(
                            relationManufacturing,
                            bomEntryRelation.bomEntryId,
                        ),
                    ) {
                        "Not found ${bomEntryRelation.bomEntryId} in $snapshot"
                    }
                // Only add if not added yet
                if (manufacturing.parents.find { it._id == parentBomEntry._id } == null) {
                    parentBomEntry.bomNodeId = snapshot.bomNodeId()
                    parentBomEntry.snapshot = snapshot
                    parentBomEntry
                } else {
                    null
                }
            }
        parents.applyIfExists {
            manufacturing.addParents(it)
        }
    }

    override fun close() {
        scheduler.dispose()
    }

    override fun destroy() {
        close()
    }
}
