package com.nu.bom.core.service

import com.nu.bom.core.exception.userException.UnsupportedClassificationEntityMappingException
import com.nu.bom.core.manufacturing.annotations.IgnoreMasterData
import com.nu.bom.core.manufacturing.entities.ComponentMaterial
import com.nu.bom.core.manufacturing.entities.Consumable
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.RawMaterial
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.ManufacturingEntityFactory
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.FieldWithResult
import com.nu.bom.core.manufacturing.fieldTypes.Null
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.masterdata.masterdataFieldsBuilder.MasterdataClassificationMappingHelper.getEntityNameOrClass
import com.nu.bom.core.manufacturing.masterdata.masterdataFieldsBuilder.MasterdataClassificationMappingHelper.getMasterDataType
import com.nu.bom.core.manufacturing.service.ConfigurationManagementService
import com.nu.bom.core.manufacturing.service.EntityClassOrName
import com.nu.bom.core.manufacturing.service.asEntityClass
import com.nu.bom.core.manufacturing.utils.EntityModel
import com.nu.bom.core.manufacturing.utils.EntityModelService
import com.nu.bom.core.manufacturing.utils.FieldExtractionUtils.Companion.EXCHANGE_RATES_FIELD_NAME
import com.nu.bom.core.model.MasterData
import com.nu.bom.core.model.MasterDataSelector
import com.nu.bom.core.model.configurations.CurrentMaterialMasterdataConfiguration
import com.nu.bom.core.service.configurations.ConfigType
import com.nu.bom.core.service.masterdata.MdClassificationService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.DefaultEntityLoader
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.FieldResultUtils
import com.nu.bom.core.utils.InterpolationData
import com.nu.bom.core.utils.mapOfNotNull
import com.nu.masterdata.dto.v1.basicdata.ClassificationDto
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import org.bson.types.ObjectId
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono

@Service
class ManufacturingEntityFactoryService(
    private val entityManager: EntityManager,
    @Lazy private val masterDataService: MasterDataService,
    @Qualifier("enum") private val defaultEntityLoader: DefaultEntityLoader,
    private val entityModelService: EntityModelService,
    @Lazy private val configurationManagementService: ConfigurationManagementService,
    private val classificationService: MdClassificationService,
) {
    companion object {
        private val logger = LoggerFactory.getLogger(ManufacturingEntityFactoryService::class.java)
    }

    fun createEntity(
        name: String,
        entityType: Entities,
        clazz: EntityClassOrName? = null,
        args: Map<String, Any?> = emptyMap(),
        masterDataSelector: MasterDataSelector? = null,
        masterDataObjectId: ObjectId? = null,
        masterDataVersion: Int? = null,
        fields: Map<String, FieldResultStar> = emptyMap(),
        version: Int = 0,
        entityRef: String? = null,
        entityId: ObjectId? = null,
        overwrites: Map<String, FieldResultStar> = emptyMap(),
        oldFieldWithResults: List<FieldWithResult> = emptyList(),
        entityRefsToOverride: Map<String, ObjectId> = emptyMap(),
        isolated: Boolean = false,
    ): ManufacturingEntity {
        val oldResults = oldFieldWithResults.associate { it.name.name to it.result }
        val argsWithEntityRef =
            if (entityRef != null) {
                args + (ManufacturingEntityFactory.ENTITY_REF_ARG to entityRef)
            } else {
                args
            }

        val factory = defaultEntityLoader.getDefaultFactory(entityType)
        val entityClass = loadEntityClass(entityType, clazz)
        val entity = factory.create(entityClass, name, argsWithEntityRef)
        if (entityId != null) {
            entity._id = entityId
        }
        entity.isolated = isolated
        entity.version = version // TODO FIXME - temp hack to make tests still working
        entity.setVersionHashes(entityManager)

        entity.masterDataSelector = masterDataSelector
        entity.masterDataObjectId = masterDataObjectId
        entity.masterDataVersion = masterDataVersion

        val entityModel = entityModelService.getEntityModel(entity::class, true)

        fields.forEach {
            entity.addInitialFieldResult(
                it.key,
            ) { _ ->
                checkType(entityModel, it.key, it.value)
            }
        }

        oldResults.forEach {
            entity.addFieldResultWithOriginalVersion(
                it.key,
                oldFieldWithResults,
            ) { _ ->
                it.value
            }
        }

        overwrites.forEach {
            it.value.source = FieldResult.SOURCE.I
            entity.replaceOrAddFieldResult(
                it.key,
            ) { _ ->
                it.value
            }
        }

        entityRefsToOverride.forEach { (field, id) ->
            if (entity.getField(field) != null) {
                entity.removeFieldResult(field)
            }
            entity.addOrUpdateField(field) {
                com.nu.bom.core.manufacturing.fieldTypes
                    .EntityRef(id.toHexString())
                    .withSource(FieldResult.SOURCE.I)
            }
        }

        return entity
    }

    private fun getDefaultConfigurationForMaterial(accessCheck: AccessCheck): Mono<CurrentMaterialMasterdataConfiguration> =
        configurationManagementService
            .getDefaultConfiguration(accessCheck, ConfigType.Masterdata)
            .map { it.materialConfiguration }

    private fun getEntityClassBasedOnMasterdataClassification(
        accessCheck: AccessCheck,
        clazz: EntityClassOrName?,
        masterDataSelector: MasterDataSelector,
        materialConfiguration: CurrentMaterialMasterdataConfiguration,
    ): Mono<EntityClassOrName> {
        // quick exit for COMPONENT_MATERIAL because it ignores classifications
        if (clazz == ComponentMaterial::class.asEntityClass()) {
            return clazz.toMono()
        }
        // Modularized materials already provide the correct class
        val isModularized =
            clazz?.firstValue()?.simpleName?.let {
                it in entityManager.getModularizedEntities().map { it.simpleName }
            } == true
        if (isModularized) {
            return clazz.toMono()
        }

        if (masterDataSelector.classificationKey == null) {
            return (
                clazz
                    ?: // fallback to old master data type
                    when (masterDataSelector.type) {
                        MasterDataType.CONSUMABLE -> Consumable::class.asEntityClass()
                        else -> throw UnsupportedClassificationEntityMappingException(null)
                    }
            ).toMono()
        }

        return classificationService
            .getParentClassification(
                accessCheck = accessCheck,
                dto = SimpleKeyDto(masterDataSelector.classificationKey),
            ).flatMap { parents ->
                val startChild = parents.find { it.key.key == masterDataSelector.classificationKey }
                if (startChild == null) {
                    return@flatMap Mono.error(UnsupportedClassificationEntityMappingException(masterDataSelector.classificationKey))
                }

                val orderedParentKeys = getOrderedParents(startChild, parents)
                var effectiveCreationClass: EntityClassOrName? = null
                orderedParentKeys.forEach {
                    val current = getEntityNameOrClass(it, materialConfiguration)
                    if (current != null) {
                        effectiveCreationClass = current
                    }
                }

                if (effectiveCreationClass == null) {
                    return@flatMap Mono.error(UnsupportedClassificationEntityMappingException(masterDataSelector.classificationKey))
                } else {
                    effectiveCreationClass.toMono()
                }
            }
    }

    private fun getOrderedParents(
        startChild: ClassificationDto,
        parents: List<ClassificationDto>,
    ): List<String> {
        val orderedParents = mutableListOf(startChild)
        while (orderedParents.size != parents.size) {
            val directParent = parents.find { orderedParents.last().parentClassificationKey?.key == it.key.key }
            if (directParent != null) {
                orderedParents.add(directParent)
            } else {
                break
            }
        }
        return orderedParents.map { it.key.key }
    }

    private fun checkType(
        entityModel: EntityModel,
        fieldName: String,
        value: FieldResultStar,
    ): FieldResultStar {
        val fieldModel =
            entityModel.fields[fieldName]
                ?: // Dynamic fields do not have a field model defined in the entity model.
                // So, it is a valid state and we skip the type check
                return value

        val expectedType = fieldModel.returnTypeCouldBeNonFieldResult
        val actualType = value::class.qualifiedName!!
        val valid =
            when {
                actualType == expectedType -> true
                actualType == Null::class.qualifiedName!! -> true
                expectedType == QuantityUnit::class.qualifiedName -> QuantityUnit.isSpecificQuantityUnit(value::class.simpleName!!)
                else -> false
            }
        if (!valid) {
            logger.error(
                "Invalid field type '$actualType' for field '$fieldName' in entity type '${entityModel.type.simpleName}'." +
                    " Expected type is: '$expectedType'",
            )
            return value
        }
        return value
    }

    fun createEntityWithMasterData(
        accessCheck: AccessCheck,
        name: String,
        entityType: Entities,
        clazz: EntityClassOrName? = null,
        masterDataSelector: MasterDataSelector,
        version: Int = 0,
        args: Map<String, Any> = emptyMap(),
        overwrites: Map<String, FieldResultStar> = emptyMap(),
        fields: Map<String, FieldResultStar> = emptyMap(),
        entityRef: String? = null,
        entityId: ObjectId? = null,
        isolated: Boolean = false,
    ): Mono<ManufacturingEntity> =
        getDefaultConfigurationForMaterial(accessCheck)
            .flatMap { materialConfiguration ->
                when (entityType) {
                    // this is the case for nu-md
                    // extend this case for every moved entity
                    Entities.COMPONENT_MATERIAL, Entities.CONSUMABLE, Entities.C_PART, Entities.MATERIAL -> {
                        val creationClazzMono =
                            getEntityClassBasedOnMasterdataClassification(
                                accessCheck = accessCheck,
                                clazz = clazz,
                                masterDataSelector = masterDataSelector,
                                materialConfiguration = materialConfiguration,
                            )

                        val updatedFields =
                            if (entityType == Entities.MATERIAL) {
                                val masterDataType =
                                    masterDataSelector.classificationKey?.let {
                                        getMasterDataType(it, materialConfiguration)
                                    } ?: masterDataSelector.type
                                fields +
                                    mapOfNotNull(RawMaterial::masterDataType.name to Text(masterDataType.name))
                            } else {
                                fields
                            }

                        creationClazzMono.map { creationClazz ->
                            createEntity(
                                name = name,
                                entityType = entityType,
                                clazz = creationClazz,
                                args = args,
                                fields = updatedFields,
                                version = version,
                                entityRef = entityRef,
                                overwrites = overwrites,
                                entityId = entityId,
                                isolated = isolated,
                            )
                        }
                    }

                    else ->
                        createEntityWithOldMasterData(
                            accessCheck,
                            name,
                            entityType,
                            clazz,
                            masterDataSelector,
                            version,
                            args,
                            overwrites,
                            fields,
                            entityRef,
                            entityId,
                            isolated,
                        )
                }
            }

    private fun createEntityWithOldMasterData(
        accessCheck: AccessCheck,
        name: String,
        entityType: Entities,
        clazz: EntityClassOrName? = null,
        masterDataSelector: MasterDataSelector,
        version: Int = 0,
        args: Map<String, Any> = emptyMap(),
        overwrites: Map<String, FieldResultStar> = emptyMap(),
        fields: Map<String, FieldResultStar> = emptyMap(),
        entityRef: String? = null,
        entityId: ObjectId? = null,
        isolated: Boolean = false,
    ): Mono<ManufacturingEntity> =
        getFilteredMasterdata(accessCheck, masterDataSelector)?.map { masterData ->
            val entityClass = loadEntityClass(entityType, clazz).simpleName
            val nameMap = entityManager.getMasterDataNameMap(entityClass)
            val meta =
                entityManager.getEntityMetaInfo(
                    loadEntityClass(entityType, clazz).simpleName,
                    interpolationData = InterpolationData(),
                )
            val mappedMasterData =
                masterData.data
                    .mapKeys { (name, _) -> nameMap[name] ?: name }
                    .filterNot { (field, _) -> meta[field]?.get(IgnoreMasterData.META_INFO) == true }

            val assembledFields =
                fields +
                    removeExchangeRates(mappedMasterData) +
                    FieldResultUtils.overrideBaseCurrency(mappedMasterData, fields) +
                    FieldResultUtils.overrideDisplayDesignation(fields)
            createEntity(
                name = name,
                entityType = entityType,
                clazz = clazz,
                args = args,
                masterDataSelector = masterDataSelector,
                masterDataObjectId = masterData._id,
                masterDataVersion = masterData.version,
                fields = assembledFields,
                version = version,
                entityRef = entityRef,
                overwrites = overwrites,
                entityId = entityId,
                isolated = isolated,
            )
        } ?: Mono.empty()

    // FIXME? All the seemingly meaningless stuff is here to make tests work
    // TODO: This non-nullable value can be null for the unit test SkillTypeUpdateIT …                         vvvvvvvvvvvvvvvvv --jgr/21/11
    private fun getFilteredMasterdata(
        accessCheck: AccessCheck,
        masterDataSelector: MasterDataSelector,
    ) = (
        masterDataService.getLatestMasterDataByCompositeKey(
            accessCheck,
            selector = masterDataSelector,
        ) as Mono<MasterData>?
    )?.filter { masterData ->
        masterData.type == masterDataSelector.type && masterData.key == masterDataSelector.key
    }

    private fun removeExchangeRates(masterdataFields: Map<String, FieldResult<*, *>>) =
        masterdataFields.filterKeys { it != EXCHANGE_RATES_FIELD_NAME }

    private fun loadEntityClass(
        type: Entities,
        clazz: EntityClassOrName?,
    ): Class<out ManufacturingEntity> =
        when (clazz != null) {
            true ->
                clazz.fold(
                    { kClass ->
                        kClass.java
                    },
                    { className ->
                        entityManager.getClass(className)
                    },
                )

            false -> defaultEntityLoader.getDefaultClass(type).java
        }
}
