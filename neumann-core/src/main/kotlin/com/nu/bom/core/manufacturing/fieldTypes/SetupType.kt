package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.utils.toUpperSnakeCase

class SetupType(res: Selection) : SelectEnumFieldResult<SetupType.Selection, SetupType>(res) {

    /**
     * Represented as externalSetup: <PERSON><PERSON><PERSON> in the templates.
     *
     * Once the templates are updated, this mapping should not be needed anymore.
     * */
    enum class Selection(val value: <PERSON>olean) {
        EXTERNAL_SETUP(true),
        INTERNAL_SETUP(false)
    }

    companion object {

        val INTERNAL_SETUP = SetupType(Selection.INTERNAL_SETUP)
        val EXTERNAL_SETUP = SetupType(Selection.EXTERNAL_SETUP)

        fun valueOf(isExternalSetup: <PERSON>olean): SetupType {

            return when (isExternalSetup) {
                true -> EXTERNAL_SETUP
                false -> INTERNAL_SETUP
            }
        }

        fun valueOf(name: String): SetupType {

            return SetupType(Selection.valueOf(name.toUpperSnakeCase()))
        }
    }
}
