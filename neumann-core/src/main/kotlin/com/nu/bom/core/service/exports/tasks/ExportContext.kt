package com.nu.bom.core.service.exports.tasks

import com.fasterxml.jackson.databind.ObjectMapper
import com.nu.bom.core.manufacturing.fieldTypes.ThreeDbVersionedPart
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.service.exports.ExportContinuation
import com.nu.bom.core.service.exports.ExportStateHandle
import com.nu.bom.core.service.exports.mapper.IdMapper
import com.nu.bom.core.service.exports.mapper.IdMappingCodec
import com.nu.bom.core.service.exports.mapper.IdMappingSerialization
import com.nu.bom.core.user.AccessCheck
import org.bson.Document
import org.bson.codecs.Codec
import java.util.UUID

data class ExportContext(
    val exportId: UUID,
    val stateHandle: ExportStateHandle,
    val projectId: ProjectId,
    val exportContinuation: ExportContinuation,
    val accessCheck: AccessCheck,
    val idMapper: IdMapper = IdMapper(),
    val mapper: ObjectMapper = IdMappingSerialization.createMapper(idMapper),
    val encoder: Codec<Document> = IdMappingCodec.codec(idMapper),
    val noCalcIds: MutableSet<String> = mutableSetOf(),
    val bomNodeIds: MutableSet<String> = mutableSetOf(),
    val fileDocumentIdsToS3Ids: MutableMap<String, String> = mutableMapOf(),
    val millingProfileIds: MutableSet<String> = mutableSetOf(),
    val versionedParts: MutableSet<ThreeDbVersionedPart> = mutableSetOf(),
)
