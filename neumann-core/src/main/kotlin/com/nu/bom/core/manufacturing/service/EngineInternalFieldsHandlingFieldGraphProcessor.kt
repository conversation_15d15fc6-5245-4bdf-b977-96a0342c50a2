package com.nu.bom.core.manufacturing.service

import com.nu.bom.core.manufacturing.entities.BomNodeReference
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.utils.partitionTo
import org.slf4j.LoggerFactory
import kotlin.math.roundToInt

internal const val RETAINED_FIELDS_RATIO = 0.9

class EngineInternalFieldsHandlingFieldGraphProcessor : FieldGraphProcessor {
    companion object {
        private val logger = LoggerFactory.getLogger(EngineInternalFieldsHandlingFieldGraphProcessor::class.java)
    }

    override fun invoke(fieldGraph: FieldGraph): FieldGraph {
        val (available, toBeRemoved) = defineAvailableAndToBeRemovedFields(fieldGraph)

        log(available, toBeRemoved)

        removeFieldsFromEntities(toBeRemoved)

        removeSuccessors(available, toBeRemoved)

        fieldGraph.fields = available

        return fieldGraph
    }

    private fun defineAvailableAndToBeRemovedFields(fieldGraph: FieldGraph): Pair<List<Field>, Set<Field>> =
        fieldGraph.fields.partitionTo(ArrayList((fieldGraph.fields.size * RETAINED_FIELDS_RATIO).roundToInt()), hashSetOf()) { field ->
            isFieldToBeKept(field)
        }

    private fun isFieldToBeKept(field: Field): Boolean {
        if (field.model.isEngineTransient) return false
        val isInputField = field.getCurrentResult()?.source == FieldResult.SOURCE.I
        val isCacheField = hasChildManufacturingDependency(field)
        if (!field.model.availableOutsideTheEngine && isInputField) {
            logger.debug("Field ${field.name} is available, because it is an input field")
        }
        if (!field.model.availableOutsideTheEngine && isCacheField) {
            logger.debug("Field ${field.name} is available, because it is a cache field")
        }
        return isInputField || isCacheField || field.model.availableOutsideTheEngine
    }

    private fun removeFieldsFromEntities(fieldsToBeRemoved: Set<Field>) {
        val fieldsByEntity = fieldsToBeRemoved.groupBy { field -> field.entity }

        val fieldNamesByEntity =
            fieldsByEntity.mapValues { (_, fields) ->
                fields.map { field -> field.name }.toSet()
            }

        fieldNamesByEntity.forEach { (entity, fieldNames) ->
            entity.fieldWithResults.removeIf { fieldNames.contains(it.name.name) }
        }
    }

    private fun removeSuccessors(
        available: List<Field>,
        toBeRemoved: Set<Field>,
    ) {
        available.forEach { field ->
            val successors = field.getSuccessorList().toSet()
            successors.intersect(toBeRemoved).forEach { removedField ->
                field.removeSuccessorField(removedField)
            }
        }
    }

    /**
     * This function ensures that fields that are used as caches in bom-entries are not deleted.
     * Necessary for lazy loading.
     */
    private fun hasChildManufacturingDependency(field: Field): Boolean =
        if (field.entity is BomNodeReference) {
            field.model.parameters.any { it.getChildDependencyEntityType() == Entities.MANUFACTURING }
        } else {
            false
        }

    private fun log(
        available: List<Field>,
        toBeRemoved: Set<Field>,
    ) {
        val toBeRemoverCount = toBeRemoved.size
        val availableFieldCount = available.size
        val totalFieldCount = toBeRemoverCount + availableFieldCount

        if (totalFieldCount > 0) {
            val toBeRemovedRate = toBeRemoverCount.toDouble() / totalFieldCount.toDouble() * 100.0
            val availableRate = availableFieldCount.toDouble() / totalFieldCount.toDouble() * 100.0
            logger.debug(
                "Removing engine Private fields:\n" +
                    "   - Transient field count: $toBeRemoverCount / $totalFieldCount, ratio: $toBeRemovedRate%\n" +
                    "   - Persistent field count: $availableFieldCount / $totalFieldCount, ratio: $availableRate%",
            )
        }
    }
}
