package com.nu.bom.core.repository

import com.nu.bom.core.model.UserPreferences
import com.nu.bom.core.model.UserPreferencesId
import org.springframework.data.mongodb.repository.ReactiveMongoRepository
import org.springframework.data.querydsl.ReactiveQuerydslPredicateExecutor
import org.springframework.stereotype.Repository

@Repository
interface UserPreferencesRepository :
    ReactiveMongoRepository<UserPreferences, UserPreferencesId>,
    ReactiveQuerydslPredicateExecutor<UserPreferences>
