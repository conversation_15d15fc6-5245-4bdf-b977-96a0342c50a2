package com.nu.bom.core.service.masterdata

import com.nu.bom.core.user.AccessCheck
import com.nu.masterdata.dto.v1.basicdata.LovEntryDto
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono

/**
 * Service to handle crud operations for LOV Entries in MD Service.
 * For now this one is only used in tests
 */
@Service
@Profile("test")
class MdLovEntryCrudService(
    private val restService: MasterdataRestService,
) {

    fun createLovEntry(
        accessCheck: AccessCheck,
        newLovEntry: LovEntryDto,
    ): Mono<LovEntryDto> {
        return restService.postToMono(
            uri = { it.path("/api/md/v1/loventries/{lovEntryKey}").build(mapOf("lovEntryKey" to newLovEntry.key.key)) },
            requestBody = newLovEntry,
            responseType = LovEntryDto::class,
            accessCheck = accessCheck,
        )
    }

    fun deleteLovEntry(
        accessCheck: AccessCheck,
        lovEntryKey: SimpleKeyDto,
    ): Mono<Void> {
        return restService.deleteToVoidMono(
            uri = { it.path("/api/md/v1/loventries/{lovEntryKey}").build(mapOf("lovEntryKey" to lovEntryKey.key)) },
            accessCheck = accessCheck,
        )
    }
}
