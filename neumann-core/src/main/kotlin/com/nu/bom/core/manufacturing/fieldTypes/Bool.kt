package com.nu.bom.core.manufacturing.fieldTypes

class Bool(res: Boolean) : FieldResult<Boolean, Bool>(res) {
    constructor(res: String) : this(res.toBoolean())

    infix fun `is`(other: Boolean): Boolean {
        return res == other
    }

    fun isTrue(): Boolean {
        return res
    }

    fun isFalse(): Boolean {
        return !res
    }

    companion object {
        val TRUE = Bool(true)
        val FALSE = Bool(false)
    }
}
