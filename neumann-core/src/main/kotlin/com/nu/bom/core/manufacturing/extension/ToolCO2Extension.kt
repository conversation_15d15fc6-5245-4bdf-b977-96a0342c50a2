package com.nu.bom.core.manufacturing.extension

import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.Extends
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.StaticDenominatorUnit
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.ToolUiConfigurationIdentifiers
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.keys.ToolUiConfigIdentifierKey
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.values.ToolDetailViewWrapper
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.values.ToolMaintenanceTypeWrapper
import com.nu.bom.core.manufacturing.entities.BaseTool
import com.nu.bom.core.manufacturing.entities.ManufacturingEntityExtension
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.StaticUnitOverride
import com.nu.bom.core.manufacturing.extension.lookups.cO2_ToolMaterialReader
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.DensityUnits
import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.manufacturing.fieldTypes.EmissionUnits
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.ToolDetailViewConfig
import com.nu.bom.core.manufacturing.fieldTypes.ToolMaintenanceType
import com.nu.bom.core.manufacturing.fieldTypes.ToolMaterial
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import com.nu.bom.core.manufacturing.fieldTypes.specificuiconfigkeys.ToolUiConfigKeys
import reactor.core.publisher.Mono

@Extends([BaseTool::class], CO2_EXTENSION_PACKAGE)
class ToolCO2Extension(name: String) : ManufacturingEntityExtension(name) {
    // per tool
    @Input
    val cO2ToolWeight: Weight = Weight(0.0, WeightUnits.KILOGRAM)

    fun cO2ToolDetailView(): ToolDetailViewConfig = ToolDetailViewConfig.CO2_DEFAULT

    @Input
    val toolMaterial: ToolMaterial = ToolMaterial.STEEL

    val cO2MachineFactor: Rate = Rate(0.0)

    @StaticDenominatorUnit(StaticUnitOverride.KILOGRAM)
    fun cO2PerKgMaterial(toolMaterial: ToolMaterial): Mono<Emission> =
        services
            .getLookupTable("CO2_ToolMaterial", rowParser = cO2_ToolMaterialReader)
            .filter {
                it.toolMaterial == toolMaterial
            }.single()
            .map {
                Emission(it.toolMaterialEmission, EmissionUnits.GRAM_CO2E)
            }

    fun toolMaterialDensity(toolMaterial: ToolMaterial): Mono<Density> =
        services
            .getLookupTable("CO2_ToolMaterial", rowParser = cO2_ToolMaterialReader)
            .filter {
                it.toolMaterial == toolMaterial
            }.single()
            .map {
                Density(it.toolMaterialDensity, DensityUnits.KILOGRAM_PER_CM)
            }

    // per tool
    @ReadOnly
    fun cO2ToolMaterial(
        cO2ToolWeight: Weight,
        cO2PerKgMaterial: Emission,
    ): Emission = Emission(cO2PerKgMaterial.inKilogramCO2e * cO2ToolWeight.inKg, EmissionUnits.KILOGRAM_CO2E)

    // per tool
    @ReadOnly
    fun cO2ToolMachining(
        cO2ToolMaterial: Emission,
        cO2MachineFactor: Rate,
    ): Emission = cO2ToolMaterial * cO2MachineFactor

    // per tool
    fun cO2ToolTotal(
        cO2ToolMaterial: Emission,
        cO2ToolMachining: Emission,
    ): Emission = cO2ToolMaterial + cO2ToolMachining

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun cO2Tool(
        cO2ToolTotal: Emission,
        quantity: Num,
        investAndInterestAllocationVolume: QuantityUnit,
    ): Emission = cO2ToolTotal * quantity.safeDivision(investAndInterestAllocationVolume, Num.ZERO)

    // region ui config

    // Needs to be defined here since it needs some co2 information
    fun uiConfigKeys(
        toolMaintenanceType: ToolMaintenanceType,
        toolDetailView: ToolDetailViewConfig,
        cO2ToolDetailView: ToolDetailViewConfig,
    ): ToolUiConfigKeys =
        ToolUiConfigKeys(
            ToolUiConfigurationIdentifiers(
                ToolUiConfigIdentifierKey.TOOL_MAINTENANCE_TYPE_ID to ToolMaintenanceTypeWrapper(toolMaintenanceType.res),
                ToolUiConfigIdentifierKey.TOOL_DETAIL_VIEW_ID to ToolDetailViewWrapper(toolDetailView.res, cO2ToolDetailView.res),
            ),
        )

    // endregion
}
