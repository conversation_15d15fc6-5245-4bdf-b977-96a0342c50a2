package com.nu.bom.core.service.exports

import com.nu.bom.core.model.ExportState
import com.nu.bom.core.model.ExportTask
import com.nu.bom.core.model.TaskStatus
import com.nu.bom.core.model.toObjectId
import com.nu.bom.core.model.toUUID
import com.nu.bom.core.repository.ExportStateRepository
import com.nu.bom.core.service.exports.tasks.TaskResult
import com.nu.bom.core.utils.LockFree
import com.nu.bom.core.utils.subscribeWithContextCapture
import org.springframework.stereotype.Component
import reactor.core.publisher.Mono
import reactor.core.scheduler.Schedulers
import reactor.kotlin.core.publisher.toMono
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap

/**
 * Handle through which an export process' state of given tasks might be updated as the process is executed.
 */
interface ExportStateHandle {
    /**
     * DSL interface for updating a given export process' state.
     */
    interface TaskScope {
        /** Set the progress of the task as a subtask out of the number of all subtasks */
        fun progress(
            subtask: Int,
            count: Int?,
        )

        /** Set the progress of the task as failed w/ a given message */
        fun failed(error: String? = null)

        /** Set the progress of the task as failed w/ a given error */
        fun failed(error: Throwable) = failed(error.message)
    }

    /**
     * Current state of the export
     */
    fun state(): ExportState

    /**
     * Executes an export job in the context of a given task.
     * The task's status is first set to in-progress, then to done the job finishes -- or failed if an error occurred.
     * The status might be changed manually (to divided in-progress or failed) within the job's context.
     *
     * @param task the task which is being executed
     * @param job the export job representing the task
     * @return the result of the job being executed
     */
    fun <T> task(
        task: ExportTask,
        job: TaskScope.() -> T,
    ): T

    /**
     * Same as @see task, but in a reactive context.
     */
    fun <T> taskMono(
        task: ExportTask,
        job: TaskScope.() -> Mono<T>,
    ): Mono<T>
}

/**
 * Lockfree storage-based export task handler. Stores a task's status and allows it to be updated in a thread-safe manner.
 */
private class LockFreeExportStateHandle(
    private val holder: LockFree<ExportState>,
    private val onUpdate: (ExportState) -> Unit,
) : ExportStateHandle {
    private class DefTaskScope(
        private val task: ExportTask,
        private val onState: (ExportState.() -> ExportState) -> Unit,
    ) : ExportStateHandle.TaskScope {
        override fun progress(
            subtask: Int,
            count: Int?,
        ) {
            onState { update(task, TaskStatus.DividedInProgress(subtask, count)) }
        }

        override fun failed(error: String?) {
            onState { failed(task, error) }
        }
    }

    override fun state() = holder.value()

    /**
     * Updates the state manually if the state is not already terminated.
     * If changed, the state gets flagged to be updated -- eventually synced to MongoDB, and if terminated removed from local store.
     */
    private fun handle(update: ExportState.() -> ExportState) {
        val newState =
            holder.optionalUpdate {
                if (active) update() else null
            } ?: return

        onUpdate(newState)
    }

    private fun <R> wrapTask(
        task: ExportTask,
        job: ExportStateHandle.TaskScope.() -> R,
    ): R {
        val scope = DefTaskScope(task) { code -> handle { code() } }
        return try {
            handle { inProgress(task) }
            scope.job()
        } catch (exception: Exception) {
            handle { failed(task, exception.message) }
            throw exception
        }
    }

    override fun <T> task(
        task: ExportTask,
        job: ExportStateHandle.TaskScope.() -> T,
    ): T =
        wrapTask(task, job).also {
            handle { done(task) }
        }

    override fun <T> taskMono(
        task: ExportTask,
        job: ExportStateHandle.TaskScope.() -> Mono<T>,
    ): Mono<T> =
        wrapTask(task) {
            job()
                .doOnSuccess {
                    if (it is TaskResult) {
                        handle { done(task, it.count, it.duration) }
                    } else {
                        handle { done(task) }
                    }
                }.doOnError { cause ->
                    handle { failed(task, cause.message) }
                }
        }

    fun failInitial() {
        handle { failed(ExportTask.INITIALIZATION) }
    }
}

/**
 * Service interface to manage the states of long running project export processes.
 */
interface ExportManagerService {
    /**
     * Register new export process with initial state.
     *
     * @return the generated ID of the new export and the handle through which its state can be updated
     */
    fun register(wrapperJob: (id: UUID, handle: ExportStateHandle) -> Mono<out Any>): Mono<UUID>

    /**
     * Queries the state of an export process.
     *
     * @param id ID of the export
     * @return state of the export if it exists
     */
    fun state(id: UUID): Mono<ExportState>

    /**
     * @see state
     */
    operator fun get(id: UUID) = state(id)
}

/**
 * Default singleton implementation of the ExportManagerService interface.
 * Stores and manages locally running export processes in-memory w/ syncing to MongoDB.
 * States of non-local (running on different MS instances) and terminated exports are queried from MongoDB.
 */
@Component
class MongoBasedExportManagerService(
    private val repo: ExportStateRepository,
) : ExportManagerService {
    private fun wrap(state: ExportState): LockFreeExportStateHandle =
        LockFreeExportStateHandle(LockFree(state)) {
            val updatedId = it.id.toUUID()
            toBeUpdated.update { this + updatedId }
        }

    private val toBeUpdated: LockFree<Set<UUID>> = LockFree(emptySet())
    private val ownedStorage: MutableMap<UUID, ExportStateHandle> = ConcurrentHashMap()

    /**
     * Flushes locally stored export statuses to MongoDB, also removes terminated exports from in-mem storage.
     * Required to be called manually so as to not overwhelm Mongo in the case of frequent updates.
     * Should be called periodically based on the expected update frequency.
     */
    fun updateMongo(): Mono<out Any> {
        val statesToSave =
            toBeUpdated.change(emptySet()).mapNotNull { id ->
                ownedStorage[id]?.state()
            }
        return repo.saveAll(statesToSave).then().doAfterTerminate {
            statesToSave.filter(ExportState::terminal).forEach {
                ownedStorage.remove(it.id.toUUID())
            }
        }
    }

    override fun register(wrapperJob: (id: UUID, handle: ExportStateHandle) -> Mono<out Any>): Mono<UUID> =
        repo
            .save(ExportState.initial())
            .map { saved ->
                val holder = wrap(saved)
                val id = saved.id.toUUID()
                ownedStorage[id] = holder
                id to holder
            }.map { (id, holder) ->
                holder.task(ExportTask.INITIALIZATION) {
                    wrapperJob(id, holder)
                        .thenReturn(id)
                        .doOnError {
                            holder.failInitial()
                        }.subscribeOn(Schedulers.boundedElastic())
                        .subscribeWithContextCapture()
                    id
                }
            }

    override fun state(id: UUID): Mono<ExportState> = ownedStorage[id]?.state()?.toMono() ?: repo.findById(id.toObjectId())
}
