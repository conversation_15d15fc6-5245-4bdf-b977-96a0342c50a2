package com.nu.bom.core.api

import com.nu.bom.core.config.AccessCheckContext
import com.nu.bom.core.service.bomrads.BomradsAccountService
import com.nu.bom.core.service.bomrads.BomradsBomNodeService
import com.nu.bom.core.user.AccessCheckProvider
import com.nu.bomrads.id.AccountId
import com.nu.http.EnvironmentNameSupplier
import jakarta.ws.rs.ForbiddenException
import org.slf4j.LoggerFactory
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Mono

@RestController
@RequestMapping("/api/migration/bomNodeRootId")
@AccessCheckContext
class RootIdMigrationController(
    private val bomradsBomNodeService: BomradsBomNodeService,
    private val bomradsAccountService: BomradsAccountService,
    private val environmentNameSupplier: EnvironmentNameSupplier,
) {
    companion object {
        val LOG = LoggerFactory.getLogger(RootIdMigrationController::class.java)!!
    }

    @PostMapping("")
    fun rootIdMigration(
        @AuthenticationPrincipal jwt: Jwt?,
        @RequestParam("doneCounter") doneCounter: Int,
    ): Mono<Void> {
        return AccessCheckProvider.getCurrentAccessCheck().flatMap { accessCheck ->
            if (!accessCheck.isAdmin()) {
                Mono.error(ForbiddenException("Only admins can call this endpoint"))
            } else {
                val env = environmentNameSupplier.getEnv() ?: ""
                LOG.info("Starting root Id migration for environment {}", env)
                bomradsAccountService.listAccounts(env, 0, 500)
                    .concatMap { clientAccount ->
                        LOG.info("migrating root_ids for account {}", clientAccount)
                        bomradsBomNodeService.migrate(clientAccount, doneCounter)
                            .doOnSuccess {
                                LOG.info("Successfully migrated root_ids for account {}", clientAccount)
                            }.onErrorMap { error ->
                                LOG.error("Failed migration for account ${clientAccount.id}", error)
                                Exception("Failed migration for account ${clientAccount.id}", error)
                            }
                    }.collectList()
                    .flatMap {
                        LOG.info("All migrations completed successfully")
                        Mono.empty()
                    }
            }
        }
    }

    @PostMapping("/{accountId}")
    fun rootIdMigrationByAccount(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable accountId: String,
        @RequestParam("doneCounter") doneCounter: Int,
    ): Mono<Void> {
        return AccessCheckProvider.getCurrentAccessCheck().flatMap { accessCheck ->
            if (!accessCheck.isAdmin()) {
                Mono.error(ForbiddenException("Only admins can call this endpoint"))
            } else {
                val env = environmentNameSupplier.getEnv() ?: ""
                LOG.info("Starting root Id migration for environment {}", env)
                bomradsAccountService.listAccounts(env, 0, 500)
                    .filter { clientAccount ->
                        clientAccount.id == AccountId(accountId)
                    }.concatMap { clientAccount ->
                        bomradsBomNodeService.migrate(clientAccount, doneCounter)
                            .doOnSuccess {
                                LOG.info("Successfully migrated root_ids for account {}", clientAccount)
                            }
                            .onErrorMap { error ->
                                LOG.error("Failed migration for account ${clientAccount.id}", error)
                                Exception("Failed migration for account ${clientAccount.id}", error)
                            }
                    }.collectList()
                    .flatMap {
                        LOG.info("All migrations completed successfully")
                        Mono.empty()
                    }
            }
        }
    }
}
