package com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations

import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.fieldnamebuilders.RateTimeFieldNameBuilder
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.overheadLookup.IndirectLookUpKey
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYears
import kotlin.reflect.KClass

sealed interface IndirectOperationWithRateOrigin : IndirectOperation {
    val rateOrigin: AggregationLevel
    val lookUpKey: IndirectLookUpKey?
    val rateOrTimeFieldClass: KClass<out FieldResult<*, *>>
    val subCalculatorWithRole: SubCalculatorWithRole?

    fun extensionName() =
        when (rateOrTimeFieldClass) {
            Rate::class -> RateTimeFieldNameBuilder.ExtensionName.RATE
            TimeInYears::class -> RateTimeFieldNameBuilder.ExtensionName.TIME
            else -> error("Field class $rateOrTimeFieldClass is not supported!")
        }
}
