package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.manufacturing.annotations.Units
import java.math.BigDecimal

@Suppress("MagicNumber")
enum class SurfaceDensityUnits(
    override val baseFactor: BigDecimal,
) : TypeUnit {
    KILOGRAM_PER_QM((1000.0).toBigDecimal()),
    GRAM_PER_QM(BigDecimal.ONE),
    ;

    override val type = TypeUnits.SURFACE_DENSITY
    override val hideInDropdown = false
}

@Units(SurfaceDensityUnits::class)
class SurfaceDensity(
    res: BigDecimal,
    unit: SurfaceDensityUnits,
) : NumericFieldResultWithUnit<SurfaceDensity, SurfaceDensityUnits>(res, unit) {
    constructor(res: Double, unit: SurfaceDensityUnits) : this(res.toBigDecimal(), unit)
    constructor(res: String, unit: String) : this(BigDecimal(res), SurfaceDensityUnits.valueOf(unit))
    constructor(res: BigDecimal, unit: String) : this(res, SurfaceDensityUnits.valueOf(unit))

    val inKgPerQm: BigDecimal
        get() {
            return to(SurfaceDensityUnits.KILOGRAM_PER_QM)
        }
    val inGPerQm: BigDecimal
        get() {
            return to(SurfaceDensityUnits.GRAM_PER_QM)
        }
}
