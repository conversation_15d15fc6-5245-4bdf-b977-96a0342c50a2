package com.nu.bom.core.model.manufacturing.persistentModel

import com.nu.bom.core.manufacturing.fieldTypes.TypeUnit

data class PersistedUnitInformation(
    val dimension: String,
    val unit: String,
) {
    companion object {
        fun fromTypeUnit(typeUnit: TypeUnit?): PersistedUnitInformation? =
            typeUnit?.let {
                PersistedUnitInformation(
                    it.type.name,
                    it.name2,
                )
            }
    }
}
