package com.nu.bom.core.manufacturing.entities

import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.StaticDenominatorUnit
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.StaticUnitOverride
import com.nu.bom.core.manufacturing.fieldTypes.Currency
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.ToolDetailViewConfig

@EntityType(Entities.TOOL, userCreatable = true)
open class ToolRough(name: String) : ManufacturingEntity(name) {
    override val extends = BaseEntityFields(name)

    @Input
    @ObjectView(ObjectView.TOOL, 110)
    @MandatoryForEntity(index = 1)
    @StaticDenominatorUnit(StaticUnitOverride.HOUR)
    fun toolCostPerHour(): Money? = null

    @Input
    @Path("/api/currency{bomPath}{branchPath}", false)
    fun baseCurrency(
        @Parent(Entities.ANY) baseCurrency: Currency?,
    ): Currency = baseCurrency ?: Currency("EUR")

    @Input
    @Path("/api/currency{bomPath}{branchPath}", false)
    @MandatoryForEntity(index = 3)
    fun masterdataBaseCurrency(): Currency = Currency("EUR")

    @Input
    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun maintenanceCost() = Money(0.0)

    @Input
    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun interestCost() = Money(0.0)

    fun toolDetailView(): ToolDetailViewConfig = ToolDetailViewConfig.ROUGH_TOOL

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.QUANTITY_UNIT)
    fun manufacturingTimePerPart(
        @Parent(Entities.MANUFACTURING_STEP)
        manufacturingTimePerPart: Time,
    ): Time {
        return manufacturingTimePerPart
    }

    // Since this field is the one used in the FE and since it is overridable, this needs to be calculated before the invest costs which are actually displayed
    // TODO: Remove this hack in https://tsetplatform.atlassian.net/browse/COST-26410
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun allocatedCostPerPart(
        toolCostPerHour: Money,
        manufacturingTimePerPart: Time,
    ): Money {
        return toolCostPerHour / 3600.toBigDecimal() * manufacturingTimePerPart
    }

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun investCost(allocatedCostPerPart: Money) = allocatedCostPerPart
}
