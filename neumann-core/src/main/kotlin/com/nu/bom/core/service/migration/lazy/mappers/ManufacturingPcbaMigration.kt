package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeCoating
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import org.springframework.stereotype.Service

@Service
class ManufacturingPcbaMigration : ManufacturingModelEntityMapper {
    override val changeSetId = MigrationChangeSetId("2023-02-02-manufacturing-pcba-migration")

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity =
        entity.copyAll(
            fieldWithResults = addStepSubTypeCoating(entity.fieldWithResults.toMutableMap(), entity.version),
        )

    private fun addStepSubTypeCoating(
        fields: MutableMap<String, FieldResultModel>,
        version: Int,
    ): Map<String, FieldResultModel> {
        fields["stepSubTypeCoating"] =
            FieldResultModel(version, version, "StepSubTypeCoating", StepSubTypeCoating.Selection.ACRYLIC, FieldResult.SOURCE.C.name, "")

        return fields
    }
}
