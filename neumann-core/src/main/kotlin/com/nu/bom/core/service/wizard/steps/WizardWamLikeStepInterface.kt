package com.nu.bom.core.service.wizard.steps

import com.nu.bom.core.manufacturing.factories.FieldFactoryService
import com.nu.bom.core.manufacturing.fieldTypes.Volume
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.service.MaterialService
import com.nu.bom.core.user.AccessCheck
import reactor.core.publisher.Mono

interface WizardWamLikeStepInterface : WizardStepInterface {
    fun netWeightPerPartValue(fieldFactoryService: FieldFactoryService): Weight?

    fun volume(
        accessCheck: AccessCheck,
        fieldFactoryService: FieldFactoryService,
        materialService: MaterialService,
    ): Mono<Volume>
}
