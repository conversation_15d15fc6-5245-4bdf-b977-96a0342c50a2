package com.nu.bom.core.service.configurations

import com.nu.bom.core.model.configurations.BurrWeightConfiguration
import org.springframework.stereotype.Service

@Service
class BurrWeightTsetConfiguration : TsetConfigurationService {
    override val type = ConfigType.BurrWeight

    override fun getConfigurations() =
        listOf(
            TsetConfiguration(
                key = "burrWeight",
                isDefault = true,
                displayName = "BurrWeight",
                value =
                    BurrWeightConfiguration(
                        burrWeightConfigurationBehavior = BurrWeightConfiguration.Type.DEFAULT,
                    ),
            ),
        )
}
