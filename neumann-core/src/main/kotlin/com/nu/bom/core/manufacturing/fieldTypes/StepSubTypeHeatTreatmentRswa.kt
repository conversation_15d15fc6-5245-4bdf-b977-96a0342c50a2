package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.utils.toUpperSnakeCase

class StepSubTypeHeatTreatmentRswa(res: Selection) : SelectEnumFieldResult<StepSubTypeHeatTreatmentRswa.Selection, StepSubTypeHeatTreatmentRswa>(res) {

    enum class Selection {
        CASE_HARDENING_TEMPERING,
        HARDENING_TEMPERING,
        NORMALIZING,
        STRESS_RELIEVING,
        NO_HEAT_TREATMENT
    }

    companion object {
        val CASE_HARDENING_TEMPERING = StepSubTypeHeatTreatmentRswa(Selection.CASE_HARDENING_TEMPERING)
        val HARDENING_TEMPERING = StepSubTypeHeatTreatmentRswa(Selection.HARDENING_TEMPERING)
        val NORMALIZING = StepSubTypeHeatTreatmentRswa(Selection.NORMALIZING)
        val STRESS_RELIEVING = StepSubTypeHeatTreatmentRswa(Selection.STRESS_RELIEVING)
        val NO_HEAT_TREATMENT = StepSubTypeHeatTreatmentRswa(Selection.NO_HEAT_TREATMENT)

        fun valueOf(name: String): StepSubTypeHeatTreatmentRswa {
            return StepSubTypeHeatTreatmentRswa(Selection.valueOf(name.toUpperSnakeCase()))
        }
    }
}
