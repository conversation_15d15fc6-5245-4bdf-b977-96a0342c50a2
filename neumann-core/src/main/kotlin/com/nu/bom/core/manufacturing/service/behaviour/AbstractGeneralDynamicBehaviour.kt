package com.nu.bom.core.manufacturing.service.behaviour

import com.nu.bom.core.manufacturing.configurablefields.service.DynamicFieldsHandler
import com.nu.bom.core.manufacturing.configurablefields.service.EntityFieldModelsWithFieldInfo
import com.nu.bom.core.manufacturing.configurablefields.service.GeneralDynamicFieldsService
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.service.CalculationRequest

abstract class AbstractGeneralDynamicBehaviour(
    entityForFieldInjection: ManufacturingEntity,
) : GeneralOrConfigBasedDynamicBehaviour(entityForFieldInjection), GeneralDynamicBehaviour {
    override fun getEntityFieldModelsWithFieldInfo(
        dynamicFieldsHandler: DynamicFieldsHandler?,
        request: CalculationRequest,
    ): EntityFieldModelsWithFieldInfo {
        require(dynamicFieldsHandler is GeneralDynamicFieldsService) {
            "illegal dynamicFieldHandler of type ${dynamicFieldsHandler?.javaClass?.simpleName} passed to AbstractGeneralDynamicBehaviour"
        }
        return dynamicFieldsHandler.getDynamicCachedFieldModelsAndInfos(
            request.accessCheck,
            this,
        )
    }
}
