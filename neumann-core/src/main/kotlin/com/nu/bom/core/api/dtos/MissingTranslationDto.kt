package com.nu.bom.core.api.dtos

data class MissingTranslationDto(
    val key: String,
    val timestamp: String,
    // The fields below are only relevant for missing translations reported from the frontend.
    //      URL of the browser
    val url: String = "",
    //      Vue component and parent component that the missing translation was encountered in.
    val component: String = "",
    val parentComponent: String = "",
    // And now for the BE reports
    val stackTrace: String = "",
)
