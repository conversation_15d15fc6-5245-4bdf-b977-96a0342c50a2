package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.machining.model.MillingRequest
import com.nu.bom.core.machining.model.MillingResponse
import com.nu.bom.core.machining.model.TurningRequest
import com.nu.bom.core.machining.model.TurningResponse
import com.nu.bom.core.turn.model.InductiveHardeningGroup
import org.bson.types.ObjectId

// By inheriting from the NoCalcData interface, you promise that your type can be stored and retrieved in a ReactiveMongoRepository.
class NoCalcFieldResult(res: String) : FieldResult<String, NoCalcFieldResult>(res) {
    val objectId = ObjectId(res)

    constructor(objectId: ObjectId, externalData: NoCalcData) : this(objectId.toHexString()) {
        this.data = externalData
    }

    var data: NoCalcData? = null

    override fun copy(source: SOURCE?): NoCalcFieldResult {
        val res = super.copy(source) as NoCalcFieldResult
        res.data = data
        return res
    }
}

data class TurningExchange(
    val request: TurningRequest<*>,
    val response: TurningResponse
) : NoCalcData

data class MillingExchange(
    val request: MillingRequest,
    val response: MillingResponse
) : NoCalcData

data class InductiveHardeningGroupsWrapper(val get: List<InductiveHardeningGroup>) : NoCalcData
