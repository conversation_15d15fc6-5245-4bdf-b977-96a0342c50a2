package com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.material

import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.helper.EntityTableColumns
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.MaterialUiConfigurationIdentifiers
import com.nu.bom.core.manufacturing.entities.PriceComponentEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.extension.BaseCO2MaterialProcessing
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.TableOption
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.FieldConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityLocator
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableColumnDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableRowDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EqualCriteria
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.LocatorType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.options.ColumnOptionsFeDto
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service

@Service
@Profile("!test", "materialPriceCardBuilderTest")
class PriceComponentsUiCardBuilder : BaseMaterialUiCardBuilder() {
    override val cardIdentifier = "priceComponents"

    override fun getTitleMappings() =
        mapOf(
            ValueType.COST to "Price components",
            ValueType.CO2 to "Emission components",
        )

    override fun getCardFields(identifiers: MaterialUiConfigurationIdentifiers): Map<ValueType, FieldConfigFeDto>? {
        return null
    }

    enum class TableOptions(val displayableOptionName: TableOption) {
        MATERIAL("Material"),
    }

    override fun getTableOptions(
        identifiers: MaterialUiConfigurationIdentifiers,
        valueType: ValueType,
    ): List<TableOption> {
        return TableOptions.entries.map { it.displayableOptionName }
    }

    override fun getTableConfig(
        valueType: ValueType,
        tableOption: TableOption,
        identifiers: MaterialUiConfigurationIdentifiers,
    ): EntityTableConfigFeDto {
        val rowId = "priceComponent"
        val columns =
            if (valueType == ValueType.COST) {
                getPriceComponentHeaderNames().map {
                    val options =
                        when (it) {
                            PriceComponentEntity::priceComponentPricePerUnit.name,
                            -> ColumnOptionsFeDto(hasTotal = true)
                            else -> null
                        }
                    EntityTableColumnDefinitionFeDto(
                        id = it,
                        field = it,
                        options = options,
                    )
                }
            } else {
                getEmissionComponentHeaderNames().map {
                    val options =
                        when (it) {
                            BaseCO2MaterialProcessing::cO2PerUnit.name,
                            -> ColumnOptionsFeDto(hasTotal = true)
                            else -> null
                        }
                    EntityTableColumnDefinitionFeDto(
                        id = it,
                        field = it,
                        options = options,
                    )
                }
            }
        val entityLocator =
            EntityLocator(
                listOf(
                    EqualCriteria(
                        "type",
                        if (valueType == ValueType.COST) Entities.PRICE_COMPONENT.name else Entities.CO2_PROCESSING_MATERIAL.name,
                    ),
                ),
                LocatorType.CHILD,
            )

        return EntityTableConfigFeDto(
            rows = listOf(rowId),
            rowDefinitions = mapOf(rowId to EntityTableRowDefinitionFeDto(rowId, collectBy = entityLocator)),
            columns = (listOf(EntityTableColumns.mainColumn(5)) + columns).distinctBy { it.id },
        )
    }

    private fun getPriceComponentHeaderNames(): List<String> =
        listOf(
            PriceComponentEntity::displayDesignation.name,
            PriceComponentEntity::priceComponentPricePerUnit.name,
        )

    private fun getEmissionComponentHeaderNames(): List<String> =
        listOf(
            BaseCO2MaterialProcessing::process.name,
            BaseCO2MaterialProcessing::cO2PerUnit.name,
        )
}
