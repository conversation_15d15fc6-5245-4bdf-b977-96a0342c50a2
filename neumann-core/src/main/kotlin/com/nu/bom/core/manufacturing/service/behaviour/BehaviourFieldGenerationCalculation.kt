package com.nu.bom.core.manufacturing.service.behaviour

import com.nu.bom.core.manufacturing.configurablefields.service.DynamicFieldsHandler
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.DynamicBehaviourGeneration
import com.nu.bom.core.manufacturing.service.BaseFieldCalculation
import com.nu.bom.core.manufacturing.service.CalculationServiceProvider
import com.nu.bom.core.manufacturing.service.ExternalStorageService
import com.nu.bom.core.manufacturing.service.Field
import com.nu.bom.core.manufacturing.service.FieldType
import com.nu.bom.core.manufacturing.service.VersionedResult
import com.nu.bom.core.manufacturing.service.asEntityClassName
import com.nu.bom.core.manufacturing.service.calculator.CalculationResult
import com.nu.bom.core.manufacturing.service.calculator.DynamicBehaviourCreationResult
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.AnsiColor
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import kotlin.reflect.KClass
import kotlin.reflect.full.primaryConstructor

private val logger = LoggerFactory.getLogger(BehaviourFieldGenerationCalculation::class.java)

@Service
class BehaviourFieldGenerationCalculation(
    val externalStorageService: ExternalStorageService,
    val calculationServiceProvider: CalculationServiceProvider,
    val dynamicFieldsServices: List<DynamicFieldsHandler>,
) : BaseFieldCalculation() {
    fun generateBehaviour(
        calculation: Field,
        version: Int,
        forceRecalculate: Boolean,
        onlyNewFieldsShouldBeRecalculated: Boolean,
        activeBehaviours: MutableMap<String, MutableMap<String, DynamicBehaviour>>,
        accessCheck: AccessCheck,
    ): Mono<CalculationResult> {
        val (versionedResult, shouldRecalc) =
            shouldRecalculate(
                calculation,
                version,
                forceRecalculate,
                false,
                onlyNewFieldsShouldBeRecalculated = onlyNewFieldsShouldBeRecalculated,
                alsoConsiderSystemValueRecalculation = false,
            )

        return if (shouldRecalc.isNormalRecalculationNeeded(calculation)) {
            logger.debug(
                "{}[{}].{} is generating behaviour of type {}",
                calculation.entity.getEntityTypeAnnotation(),
                AnsiColor.blue(calculation.entity.name),
                AnsiColor.green(calculation.name),
                AnsiColor.cyan(calculation.model.returnTypeCouldBeNonFieldResult),
            )

            val fieldType = calculation.model.fieldType
            performCalculation(calculation, version)
                .flatMap { result ->
                    check(fieldType == FieldType.BehaviourCreation) {
                        "Field type BEHAVIOUR_CREATION unexpected. Found: $fieldType for $calculation"
                    }
                    createCalculationResultForDynamicBehaviour(
                        calculation,
                        version,
                        result,
                        versionedResult,
                        activeBehaviours,
                        accessCheck,
                    )
                }
        } else {
            createCalculationResultFromVersionedResult(calculation, versionedResult!!, version, accessCheck)
        }
    }

    private fun createCalculationResultFromVersionedResult(
        calculation: Field,
        versionedResult: VersionedResult,
        version: Int,
        accessCheck: AccessCheck,
    ): Mono<CalculationResult> {
        val result = versionedResult.result as DynamicBehaviourGeneration

        val behaviourImplementationClassName = result.res.behaviourImplementationClassName
        val clazz = Class.forName(behaviourImplementationClassName).kotlin

        return when (result.res.dynamicBehaviourType()) {
            DynamicBehaviourGeneration.DynamicBehaviourType.EntityBased -> {
                createCalculationResultForEntityBasedDynamicBehaviour(
                    result,
                    calculation,
                    versionedResult,
                    version,
                    clazz,
                )
            }
            DynamicBehaviourGeneration.DynamicBehaviourType.ConfigBased -> {
                createCalculationResultForConfigBasedDynamicBehaviour(
                    result,
                    calculation,
                    clazz,
                    accessCheck,
                ).map {
                    DynamicBehaviourCreationResult(
                        calculation = calculation,
                        dynamicBehaviour = it,
                        versionedResult = versionedResult.copy(newVersion = version),
                    )
                }
            }
            DynamicBehaviourGeneration.DynamicBehaviourType.General -> {
                createCalculationResultForGeneralDynamicBehaviour(
                    result,
                    calculation,
                    clazz,
                    accessCheck,
                ).map {
                    DynamicBehaviourCreationResult(
                        calculation = calculation,
                        dynamicBehaviour = it,
                        versionedResult = versionedResult.copy(newVersion = version),
                    )
                }
            }
        }
    }

    private fun createCalculationResultForConfigBasedDynamicBehaviour(
        result: DynamicBehaviourGeneration,
        calculation: Field,
        clazz: KClass<out Any>,
        accessCheck: AccessCheck,
    ): Mono<ConfigBasedDynamicBehaviour> {
        try {
            val configurationIdentifier =
                checkNotNull(result.res.configurationIdentifier) {
                    "Configuration identifier for dynamic behaviour is null!"
                }

            // create behaviour
            val configBasedDynamicBehaviour =
                clazz
                    .primaryConstructor!!.call(calculation.entity, configurationIdentifier)
                    as ConfigBasedDynamicBehaviour

            // check, if we have already field configs cached for this configurationId and create them if not
            return dynamicFieldHandler(
                configBasedDynamicBehaviour,
            ).createAndCacheDynamicEntityFieldModelsAndInfos(accessCheck, configBasedDynamicBehaviour)
                .map { configBasedDynamicBehaviour }
        } catch (ex: NoSuchMethodException) {
            error("could not create config based behaviour of ${clazz.simpleName}, exception: ${ex.message}")
        }
    }

    private fun dynamicFieldHandler(dynamicBehaviour: DynamicBehaviour): DynamicFieldsHandler {
        return dynamicFieldsServices.firstOrNull { it.canHandleDynamicBehaviour(dynamicBehaviour) }
            ?: throw IllegalArgumentException("no dynamic field handler for ${dynamicBehaviour::class.simpleName}")
    }

    private fun createCalculationResultForGeneralDynamicBehaviour(
        result: DynamicBehaviourGeneration,
        calculation: Field,
        clazz: KClass<out Any>,
        accessCheck: AccessCheck,
    ): Mono<GeneralDynamicBehaviour> {
        try {
            val configurationIdentifier =
                checkNotNull(result.res.dynamicBehaviourInfo) {
                    "dynamicBehaviourInfo for dynamic behaviour is null!"
                }

            // create behaviour
            val generalDynamicBehaviour =
                clazz
                    .primaryConstructor!!.call(calculation.entity, configurationIdentifier)
                    as GeneralDynamicBehaviour

            // check, if we have already field configs cached for this configurationId and create them if not
            return dynamicFieldHandler(
                generalDynamicBehaviour,
            ).createAndCacheDynamicEntityFieldModelsAndInfos(accessCheck, generalDynamicBehaviour)
                .map { generalDynamicBehaviour }
        } catch (ex: NoSuchMethodException) {
            error("could not create general dynamic behaviour of ${clazz.simpleName}, exception: ${ex.message}")
        }
    }

    private fun createCalculationResultForEntityBasedDynamicBehaviour(
        result: DynamicBehaviourGeneration,
        calculation: Field,
        versionedResult: VersionedResult,
        version: Int,
        clazz: KClass<out Any>,
    ): Mono<CalculationResult> {
        val entityClass = result.res.behaviourType.asEntityClassName()
        val behaviourEntity =
            calculation.entity.services.createEntity(
                name = result.res.behaviourType,
                entityType = Entities.NONE,
                clazz = entityClass,
                version = versionedResult.version,
            )
        try {
            val effectiveBehaviour =
                clazz.primaryConstructor!!.call(
                    calculation.entity,
                    behaviourEntity,
                ) as DynamicBehaviour
            val creationResult =
                DynamicBehaviourCreationResult(
                    calculation = calculation,
                    dynamicBehaviour = effectiveBehaviour,
                    versionedResult = versionedResult.copy(newVersion = version),
                )
            return Mono.just(creationResult)
        } catch (ex: NoSuchMethodException) {
            error("Failed to call primary constructor of kClass: $clazz, exception: ${ex.message}")
        }
    }

    private fun createCalculationResultForDynamicBehaviour(
        calculation: Field,
        version: Int,
        newDynamicBehaviour: DynamicBehaviour,
        existingRes: VersionedResult?,
        activeBehaviours: MutableMap<String, MutableMap<String, DynamicBehaviour>>,
        accessCheck: AccessCheck,
    ): Mono<CalculationResult> {
        val entityId = calculation.entity.entityId
        var alreadyCreatedActiveBehaviours = activeBehaviours[entityId]
        if (alreadyCreatedActiveBehaviours == null) {
            alreadyCreatedActiveBehaviours = mutableMapOf()
            activeBehaviours[entityId] = alreadyCreatedActiveBehaviours
        }

        var alreadyCreatedBehaviour = alreadyCreatedActiveBehaviours[newDynamicBehaviour.key()]
        if (alreadyCreatedBehaviour == null) {
            alreadyCreatedBehaviour = newDynamicBehaviour
            alreadyCreatedActiveBehaviours[newDynamicBehaviour.key()] = newDynamicBehaviour
        }

        val effectiveBehaviour = alreadyCreatedBehaviour

        check(effectiveBehaviour.hostEntity() == calculation.entity) {
            "Behaviour host entity ´${effectiveBehaviour.hostEntity()}´ is not the entity " +
                "´${calculation.entity}´ of the behaviour creation field!"
        }

        if (existingRes != null) {
            val result = existingRes.result as DynamicBehaviourGeneration
            if (result.res.behaviourType != effectiveBehaviour.behaviourType()) {
                effectiveBehaviour.resetFields()
            }
        }

        val generation =
            DynamicBehaviourGeneration(
                DynamicBehaviourGeneration.DynamicBehaviourTypeMapping(
                    hostType = calculation.entity.getEntityClass(),
                    behaviourType = effectiveBehaviour.behaviourType(),
                    behaviourImplementationClassName = effectiveBehaviour::class.qualifiedName!!,
                    configurationIdentifier = effectiveBehaviour.configurationIdentifier(),
                    dynamicBehaviourInfo = effectiveBehaviour.dynamicBehaviourInfo(),
                ),
            )

        val creationResult =
            DynamicBehaviourCreationResult(
                calculation = calculation,
                dynamicBehaviour = effectiveBehaviour,
                versionedResult =
                    mapFieldResult(
                        existingRes,
                        version,
                        generation,
                        calculation,
                    ),
            )

        return if (newDynamicBehaviour.cacheFields()) {
            dynamicFieldHandler(newDynamicBehaviour).createAndCacheDynamicEntityFieldModelsAndInfos(accessCheck, newDynamicBehaviour)
                .map { creationResult }
        } else {
            Mono.just(creationResult)
        }
    }

    private fun performCalculation(
        calculation: Field,
        version: Int,
    ): Mono<DynamicBehaviour> {
        return try {
            calculation.resolveParameters(
                externalStorageService,
                version,
                calculationServiceProvider,
            ).flatMap { parameters ->
                convertToDynamic(calculation.call(parameters.resolvedParams))
            }
        } catch (e: Exception) {
            Mono.error(e)
        }
    }

    private fun convertToDynamic(intermediateResult: Any?): Mono<DynamicBehaviour> {
        return when (intermediateResult) {
            null -> Mono.empty()
            is Mono<*> -> intermediateResult.map { it as DynamicBehaviour }
            is DynamicBehaviour -> Mono.just(intermediateResult)
            else -> Mono.error(IllegalArgumentException("Incorrect type to convert for dynamic behavior."))
        }
    }
}
