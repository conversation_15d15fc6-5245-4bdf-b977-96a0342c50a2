package com.nu.bom.core.service

import com.nu.bom.core.model.FilesHashData
import com.nu.bom.core.repository.FileHashDataRepository
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono

@Service
class FileHashDataService(
    private val fileHashDataRepository: FileHashDataRepository
) {

    fun findByFileName(filename: String): Mono<FilesHashData> {
        return fileHashDataRepository.findByFileName(filename)
    }

    fun saveOrUpdateFileHash(filesHashData: FilesHashData): Mono<FilesHashData> {
        return fileHashDataRepository.findByFileName(filesHashData.fileName).defaultIfEmpty(filesHashData).flatMap {
            it.hashKey = filesHashData.hashKey
            fileHashDataRepository.save(it)
        }
    }

    fun deleteAllByType(type: String): Mono<Int> {
        return fileHashDataRepository.deleteAllByType(type)
    }
}
