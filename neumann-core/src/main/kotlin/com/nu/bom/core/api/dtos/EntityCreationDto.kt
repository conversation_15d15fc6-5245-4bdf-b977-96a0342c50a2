package com.nu.bom.core.api.dtos

import com.nu.bom.core.manufacturing.enums.Entities
import com.tset.bom.clients.common.FieldStructure
import com.tset.core.service.domain.Currency

data class EntityCreationDto<X : FieldStructure>(
    val entityType: Entities,
    val entityClass: String? = null,
    val masterDataKey: MasterDataCompositeKey? = null,
    val parentId: String? = null,
    val parentType: Entities? = null,
    val childBomNodeId: String? = null,
    val fields: List<X>,
    val currency: Currency? = Currency.EUR,
    val refreshTrigger: String? = null,
    val technology: String? = null,
    val insertBeforeEntityId: String? = null,
    val insertAfterEntityId: String? = null,
    val linkEntityField: String? = null,
    val linkEntityId: String? = null,
)
