package com.nu.bom.core.api.internal

import com.nu.bom.core.api.dtos.MissingTranslationDto
import com.nu.bom.core.config.AccessCheckContext
import com.nu.bom.core.service.internal.MissingTranslationReportingService
import com.tset.core.module.TranslationServiceProvider
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Mono

@AccessCheckContext
@RestController
@RequestMapping("/api/internal/missingTranslations")
class MissingTranslationReportingController(
    private val missingTranslationReportingService: MissingTranslationReportingService,
    private val translationServiceProvider: TranslationServiceProvider,
) {
    @PostMapping
    fun reportMissingTranslation(
        @RequestBody dto: MissingTranslationDto,
    ): Mono<Void> {
        return missingTranslationReportingService.report(dto, listOf())
    }

    @GetMapping
    fun getMissingTranslations(): Mono<List<MissingTranslationDto>> {
        return missingTranslationReportingService.findAllForCurrentEnvironment().collectList()
    }

    @PostMapping("/refresh")
    fun refreshMissingTranslations(): Mono<Long> {
        return missingTranslationReportingService.refresh(translationServiceProvider)
    }

    @DeleteMapping
    fun deleteMissingTranslations(): Mono<Long> {
        return missingTranslationReportingService.deleteAllForCurrentEnvironment()
    }

    @DeleteMapping("/{key}")
    fun deleteMissingTranslation(
        @PathVariable key: String,
    ): Mono<Long> {
        return missingTranslationReportingService.deleteForCurrentEnvironment(key)
    }
}
