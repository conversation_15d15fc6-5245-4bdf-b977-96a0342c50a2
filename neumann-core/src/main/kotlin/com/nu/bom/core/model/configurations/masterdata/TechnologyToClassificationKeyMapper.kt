package com.nu.bom.core.model.configurations.masterdata

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import java.lang.IllegalArgumentException

object TechnologyToClassificationKeyMapper {
    fun mapToClassificationKey(
        technology: String,
        classificationKeys: TechnologyMapToClassificationKeys,
    ): SimpleKeyDto {
        val key =
            when (Model.valueOf(technology.uppercase())) {
                Model.CHILL -> classificationKeys.classificationChillKey
                Model.PREC -> classificationKeys.classificationPrecKey
                Model.VPREC -> classificationKeys.classificationVprecKey
                Model.DCA -> classificationKeys.classificationDcaKey
                Model.INJ -> classificationKeys.classificationInjKey
                Model.INJ2 -> classificationKeys.classificationInj2Key
                Model.MINJ -> classificationKeys.classificationMinjKey
                Model.RINJ -> classificationKeys.classificationRinjKey
                Model.WHAT -> classificationKeys.classificationWhatKey
                Model.CHAT -> classificationKeys.classificationChatKey
                Model.RROL -> classificationKeys.classificationRrolKey
                Model.DFOR -> classificationKeys.classificationDforKey
                Model.AFOR -> classificationKeys.classificationAforKey
                Model.DFORT -> classificationKeys.classificationDfortKey
                Model.SINT -> classificationKeys.classificationSintKey
                Model.SAND -> classificationKeys.classificationSandKey
                Model.CROL -> classificationKeys.classificationCrolKey
                Model.CEXT -> classificationKeys.classificationCextKey
                Model.ALEX -> classificationKeys.classificationAlexKey
                Model.CORE -> classificationKeys.classificationCoreKey
                Model.CORES -> classificationKeys.classificationCoresKey
                Model.RSWA -> classificationKeys.classificationRswaKey
                Model.BART -> classificationKeys.classificationBartKey
                Model.PCB -> classificationKeys.classificationPcbKey
                Model.PCBA -> classificationKeys.classificationPcbaKey
                Model.CUBE -> classificationKeys.classificationCubeKey
                Model.MAGN -> classificationKeys.classificationMagnKey
                Model.FTIPDS -> classificationKeys.classificationFtipdsKey
                Model.FTITDS -> classificationKeys.classificationFtitdsKey
                Model.PBOX -> classificationKeys.classificationPboxKey
                Model.LAST -> classificationKeys.classificationLastKey
                else -> throw IllegalArgumentException("Technology not valid")
            }
        return SimpleKeyDto(key)
    }

    fun mapToClassificationTypeMaterial(
        technology: String,
        classificationKeys: RawMaterialClassifications,
    ): List<SimpleKeyDto> {
        return when (Model.valueOf(technology.uppercase())) {
            Model.CHILL, Model.PREC, Model.VPREC, Model.DCA, Model.SAND ->
                classificationKeys.castingAlloyClassificationKeys

            Model.INJ, Model.INJ2, Model.MINJ -> classificationKeys.plasticGranulateClassificationKeys
            Model.RINJ -> classificationKeys.rubberClassificationKeys
            Model.CHAT -> classificationKeys.wireRodClassificationKeys
            Model.WHAT, Model.RROL, Model.DFOR, Model.AFOR, Model.DFORT, Model.CROL, Model.CEXT, Model.BART ->
                classificationKeys.barClassificationKeys

            Model.SINT -> classificationKeys.powderClassificationKeys
            Model.ALEX -> classificationKeys.ingotClassificationKeys
            Model.CORE, Model.CORES -> classificationKeys.sandClassificationKeys
            Model.RSWA -> classificationKeys.pipeClassificationKeys
            Model.PCBA -> classificationKeys.coatingPcbaClassificationKeys
            Model.MAGN -> classificationKeys.rareEarthClassificationKeys
            Model.CUBE -> classificationKeys.sheetClassificationKeys
            Model.FTIPDS, Model.FTITDS -> classificationKeys.coilClassificationKeys

            Model.LAST -> classificationKeys.lamellaClassificationKeys
            Model.PBOX -> emptyList()
            Model.PCB -> emptyList()
            else -> throw IllegalArgumentException("Technology not valid")
        }.map { SimpleKeyDto(it) }
    }

    fun mapMaterialTypeToClassificationTypeMaterial(
        masterDataType: MasterDataType,
        classificationKeys: RawMaterialClassifications,
    ): List<SimpleKeyDto> {
        return when (masterDataType) {
            MasterDataType.RAW_MATERIAL_CASTING_ALLOY -> classificationKeys.castingAlloyClassificationKeys
            MasterDataType.RAW_MATERIAL_INGOT -> classificationKeys.ingotClassificationKeys
            MasterDataType.RAW_MATERIAL_INK -> classificationKeys.inkClassificationKeys
            MasterDataType.RAW_MATERIAL_SHEET -> classificationKeys.sheetClassificationKeys
            MasterDataType.RAW_MATERIAL_LAMELLA -> classificationKeys.lamellaClassificationKeys
            MasterDataType.RAW_MATERIAL_PLASTIC_GRANULATE -> classificationKeys.plasticGranulateClassificationKeys
            MasterDataType.RAW_MATERIAL_RUBBER -> classificationKeys.rubberClassificationKeys
            MasterDataType.RAW_MATERIAL_SAND -> classificationKeys.sandClassificationKeys
            MasterDataType.RAW_MATERIAL_PIPE -> classificationKeys.pipeClassificationKeys
            MasterDataType.RAW_MATERIAL_WIRE_ROD -> classificationKeys.wireRodClassificationKeys
            MasterDataType.RAW_MATERIAL_BAR -> classificationKeys.barClassificationKeys
            MasterDataType.RAW_MATERIAL_POWDER -> classificationKeys.powderClassificationKeys
            MasterDataType.RAW_MATERIAL_WAX -> classificationKeys.waxClassificationKeys
            MasterDataType.RAW_MATERIAL_RARE_EARTH -> classificationKeys.rareEarthClassificationKeys
            MasterDataType.RAW_MATERIAL_PAINT -> classificationKeys.paintClassificationKeys
            MasterDataType.RAW_MATERIAL_PAPER_COIL -> classificationKeys.paperCoilClassificationKeys
            MasterDataType.RAW_MATERIAL_PAPER_SHEET -> classificationKeys.paperSheetClassificationKeys
            MasterDataType.RAW_MATERIAL_METALLIC_COATING -> classificationKeys.metallicCoatingClassificationKeys
            MasterDataType.RAW_MATERIAL_COATING_PCBA -> classificationKeys.coatingPcbaClassificationKeys
            MasterDataType.RAW_MATERIAL_COIL -> classificationKeys.coilClassificationKeys
            MasterDataType.RAW_MATERIAL_VARNISH -> classificationKeys.varnishClassificationKeys
            else -> throw IllegalArgumentException("MasterDataType not valid")
        }.map { SimpleKeyDto(it) }
    }
}
