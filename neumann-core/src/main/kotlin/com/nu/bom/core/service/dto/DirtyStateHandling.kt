package com.nu.bom.core.service.dto

import com.nu.bom.core.api.dtos.ApiDirtyStateHandling

/**
 * Strategies for publishing or saving a variant in a dirty state.
 * */
enum class DirtyStateHandling {
    /** Recalculates if the calculation is in a dirty state */
    RECALCULATE_ON_DIRTY,
    /** Throws an exception if the calculation is in a dirty state */
    FAIL_ON_DIRTY,
    /** Does not check dirty state - use this option cautiously in order to avoid unintended publishing in dirty state */
    NONE;

    companion object {
        fun fromApi(dirtyStateHandling: ApiDirtyStateHandling) = when (dirtyStateHandling) {
            ApiDirtyStateHandling.RECALCULATE_ON_DIRTY -> RECALCULATE_ON_DIRTY
            ApiDirtyStateHandling.FAIL_ON_DIRTY -> FAIL_ON_DIRTY
        }
    }
}
