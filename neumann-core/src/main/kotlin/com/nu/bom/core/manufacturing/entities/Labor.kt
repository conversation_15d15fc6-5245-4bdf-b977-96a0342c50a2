package com.nu.bom.core.manufacturing.entities

import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.FieldName
import com.nu.bom.core.manufacturing.annotations.FilteredChildren
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Label
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.StaticDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.TranslationLabel
import com.nu.bom.core.manufacturing.annotations.TranslationSection
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_LABOR_BURDEN_ENTITY_NAME
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_WAGE_ENTITY_NAME
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.StaticUnitOverride
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Currency
import com.nu.bom.core.manufacturing.fieldTypes.MasterdataConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.SKILL_TYPE_PATH
import com.nu.bom.core.manufacturing.fieldTypes.SkillTypeConsumer
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.service.masterdata.MdBasicDataService
import com.nu.bom.core.utils.getResultWithSelectedAttributes
import org.bson.types.ObjectId
import reactor.core.publisher.Mono

@EntityType(Entities.LABOR, userCreatable = true)
class Labor(
    name: String,
) : ManufacturingEntity(name),
    SkillTypeConsumer {
    override val extends = BaseEntityFields(name)

    @Input
    @ObjectView(ObjectView.ALL, 0)
    @TranslationSection(TranslationSection.DEEP)
    @MandatoryForEntity(index = 0)
    fun displayDesignation(): Text = Text("Operator")

    override fun skillTypeLovTypeKey(
        @Parent(Entities.MANUFACTURING)
        masterdataConfigurationKey: MasterdataConfigurationKey?,
    ): Mono<Text> = skillTypeLovTypeKeyImpl(masterdataConfigurationKey, services)

    @Input
    @ObjectView(ObjectView.LABOR, 10)
    @MandatoryForEntity(index = 1)
    @Path(SKILL_TYPE_PATH, remoteSearch = false, grayOutMissingItem = true, getDisplayNameFromPathQuery = true)
    @Label("skillTypeName")
    override fun skillType(
        @Parent(Entities.MANUFACTURING)
        masterdataConfigurationKey: MasterdataConfigurationKey,
    ): Mono<Text> =
        getSkillTypeConfiguration(masterdataConfigurationKey, services)
            .map { skillTypeConfig ->
                Text(skillTypeConfig.laborDefaultSkillType)
            }

    override fun skillTypeName(
        skillType: Text,
        mdBasicDataService: MdBasicDataService,
    ): Mono<Text> = getSkillTypeName(skillType, mdBasicDataService, services)

    @Input
    @ObjectView(ObjectView.LABOR, 50)
    @MandatoryForEntity(index = 2)
    fun requiredLabor(): Num? = null

    @StaticDenominatorUnit(StaticUnitOverride.SECOND)
    fun wage(
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_WAGE,
            nameFilter = TSET_COST_WAGE_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        value: Map<ObjectId, Money>?,
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_WAGE,
            nameFilter = TSET_COST_WAGE_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        @FieldName("skillType")
        costFactorSkillType: Map<ObjectId, Text>,
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_WAGE,
            nameFilter = TSET_COST_WAGE_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        @FieldName("location")
        costFactorLocations: Map<ObjectId, Text>,
        location: Text,
        skillType: Text,
    ): Money? =
        getResultWithSelectedAttributes(
            value,
            costFactorSkillType,
            costFactorLocations,
            skillType,
            location,
        )

    @ObjectView(ObjectView.LABOR, 20)
    @StaticDenominatorUnit(StaticUnitOverride.HOUR)
    fun wagePerHour(wage: Money?) = wage

    @ReadOnly
    @Label("locationName")
    fun location(
        @Parent(Entities.MANUFACTURING_STEP)
        location: Text,
    ): Text = location

    @ReadOnly
    @TranslationLabel("location")
    fun locationName(
        @Parent(Entities.MANUFACTURING_STEP)
        locationName: Text,
    ): Text = locationName

    fun laborBurden(
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_LABOR_BURDEN,
            nameFilter = TSET_COST_LABOR_BURDEN_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        ) value: Map<ObjectId, Rate>?,
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_LABOR_BURDEN,
            nameFilter = TSET_COST_LABOR_BURDEN_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        @FieldName("location")
        costFactorLocations: Map<ObjectId, Text>,
        location: Text,
    ): Rate? = getResultWithSelectedAttributes(value, costFactorLocations, location)

    @Input(active = false)
    @ReadOnly
    fun baseCurrency(
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_WAGE,
            nameFilter = TSET_COST_WAGE_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        ) baseCurrency: Map<ObjectId, Currency>,
    ): Currency? = baseCurrency.values.firstOrNull()

    @ReadOnly
    @TranslationLabel("manufacturingTimePerPartLabor")
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun manufacturingTimePerPart(
        @Parent(Entities.MANUFACTURING_STEP) manufacturingTimePerPart: Time,
    ): Time = manufacturingTimePerPart

    @ReadOnly
    @ObjectView(ObjectView.LABOR, 30)
    @StaticDenominatorUnit(StaticUnitOverride.HOUR)
    fun costPerHour(
        wagePerHour: Money,
        laborBurden: Rate,
    ): Money = wagePerHour * (laborBurden + 1.0)

    @ReadOnly
    @ObjectView(ObjectView.LABOR, 40)
    @StaticDenominatorUnit(StaticUnitOverride.HOUR)
    fun costPerHourAfterUtilization(
        costPerHour: Money,
        @Parent(Entities.MANUFACTURING_STEP) laborUtilizationRate: Rate,
    ): Money = costPerHour / laborUtilizationRate

    @DefaultUnit(DefaultUnit.SECOND)
    @ObjectView(ObjectView.LABOR, 60)
    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun leadTime(
        requiredLabor: Num,
        manufacturingTimePerPart: Time,
    ): Time = manufacturingTimePerPart * requiredLabor

    @ReadOnly
    @ObjectView(ObjectView.LABOR, 70)
    @TranslationLabel("costPerPartLabor")
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    @Deprecated("Labor.costPerPart is deprecated", ReplaceWith("productionCostPerPart"))
    fun costPerPart(
        costPerHourAfterUtilization: Money,
        leadTime: Time,
        @Parent(Entities.MANUFACTURING_STEP) lotFractionApportionmentFactor: Rate,
        @Parent(Entities.MANUFACTURING_STEP) familyAllocationRatio: Rate,
    ): Money = costPerHourAfterUtilization * leadTime.inHours * lotFractionApportionmentFactor * familyAllocationRatio

    @ObjectView(ObjectView.LABOR, 55)
    fun requiredLaborForSetup(
        requiredLabor: Num,
        @Parent(Entities.MANUFACTURING_STEP) setupIncludeOperators: Bool,
    ): Num =
        when {
            setupIncludeOperators.res -> requiredLabor
            else -> Num(0.toBigDecimal())
        }

    @ReadOnly
    fun laborUtilizationRate(
        @Parent(Entities.MANUFACTURING_STEP)
        laborUtilizationRate: Rate,
    ): Rate = laborUtilizationRate

    @ReadOnly
    fun shiftsPerDay(
        @Parent(Entities.MANUFACTURING_STEP)
        shiftsPerDay: Num,
    ): Num = shiftsPerDay

    @DefaultUnit(DefaultUnit.MINUTE)
    @ReadOnly
    fun systemDownTime(
        @Parent(Entities.MANUFACTURING_STEP)
        systemDownTime: Time,
    ): Time = systemDownTime

    // No denominator unit.
    fun setupCostPerLot(
        costPerHourAfterUtilization: Money,
        requiredLaborForSetup: Num,
        @Parent(Entities.MANUFACTURING_STEP) systemDownTime: Time,
    ): Money = (costPerHourAfterUtilization * requiredLaborForSetup * systemDownTime.inHours)

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    @Deprecated("Labor.setupCostPerPart is deprecated", ReplaceWith("setupOperatorCostPerPart"))
    fun setupCostPerPart(
        setupCostPerLot: Money,
        @Parent(Entities.MANUFACTURING_STEP) averageProcessedLotSize: QuantityUnit,
        @Parent(Entities.MANUFACTURING_STEP) lotFractionApportionmentFactor: Rate,
        @Parent(Entities.MANUFACTURING_STEP) familyAllocationRatio: Rate,
    ): Money =
        (setupCostPerLot * lotFractionApportionmentFactor * familyAllocationRatio)
            .safeDivision(averageProcessedLotSize, Money(0.0))

    // region interface to commercial calculation

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun productionCostPerPart(costPerPart: Money) = costPerPart

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun setupOperatorCostPerPart(setupCostPerPart: Money) = setupCostPerPart

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun setupWorkerCostPerPart() = Money.ZERO

    // endregion
}
