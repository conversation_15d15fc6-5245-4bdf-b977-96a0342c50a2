package com.nu.bom.core.manufacturing.service.virtualfield.sourceproviders

import com.nu.bom.core.manufacturing.fieldTypes.MdHeaderInfoField
import com.nu.bom.core.manufacturing.fieldTypes.MdHeaderInfoFieldData
import com.nu.bom.core.manufacturing.fieldTypes.MdHeaderTypeAndHeaderKeyFieldData
import com.nu.bom.core.manufacturing.fieldTypes.masterdata.FieldDefinition
import com.nu.bom.core.manufacturing.service.Field
import com.nu.bom.core.manufacturing.service.VersionedResult
import com.nu.bom.core.service.masterdata.MdBasicDataService
import com.nu.bom.core.service.masterdata.MdDetailCrudService
import com.nu.bom.core.user.AccessCheck
import com.nu.masterdata.dto.v1.basicdata.ClassificationFieldsInfoDto
import com.nu.masterdata.dto.v1.header.HeaderDto
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.kotlin.core.publisher.toFlux

@Service
class MasterDataHeaderFieldsSourceProvider(
    mdDetailCrudService: MdDetailCrudService,
    private val mdBasicDataService: MdBasicDataService,
) : MasterDataHeaderFieldBase(mdDetailCrudService) {
    companion object {
        const val TARGET_FIELD_NAME = "mdHeaderInfo"
    }

    override fun isSourceProviderFor(source: DataSourceProviders?): Boolean = source == DataSourceProviders.NEW_MASTER_DATA_HEADER_FIELDS

    override fun findSourceEntityData(
        accessCheck: AccessCheck,
        sourceKeys: List<Any>,
    ): Flux<IndexedValue<SourceData>> {
        val inputFieldData = sourceKeys.map { it as MdHeaderTypeAndHeaderKeyFieldData }
        val allHeaderKeys = inputFieldData.distinct()
        return fetchAllHeaders(accessCheck, allHeaderKeys)
            .collectList()
            .flatMapMany { allHeaders ->
                val allClassificationKeys =
                    allHeaders
                        .mapNotNull { it.classifications?.entries }
                        .flatten()
                        .distinct()

                if (allClassificationKeys.isEmpty()) {
                    inputFieldData
                        .withIndex()
                        .map { (idx, headerTypeAndHeaderKey) ->
                            val headerKey = headerTypeAndHeaderKey.headerKey.toDto()
                            val headerData =
                                allHeaders.firstOrNull {
                                    it.key == headerKey &&
                                        it.headerTypeKey.key == headerTypeAndHeaderKey.headerTypeKey
                                }
                            val headerInfoField = mapToField(headerData, emptyList())

                            IndexedValue(
                                idx,
                                SourceData(
                                    // This needs to be adapted as soon as this source provider is used in practice
                                    mapOf(
                                        TARGET_FIELD_NAME to headerInfoField,
                                    ),
                                    null,
                                ),
                            )
                        }.toFlux()
                } else {
                    fetchAllClassificationFields(accessCheck, allClassificationKeys)
                        .map { it.first to mapToFieldDefinitions(it.second) }
                        .collectList()
                        .map { allClInfos -> allClInfos.associateBy { it.first } }
                        .flatMapIterable { classificationMap ->
                            inputFieldData
                                .withIndex()
                                .map { (idx, headerTypeAndHeaderKey) ->
                                    val headerKey = headerTypeAndHeaderKey.headerKey.toDto()
                                    val headerData =
                                        allHeaders.firstOrNull {
                                            it.key == headerKey &&
                                                it.headerTypeKey.key == headerTypeAndHeaderKey.headerTypeKey
                                        }
                                    val fields =
                                        headerData?.classifications?.entries
                                            ?.mapNotNull { classificationMap[it] }
                                            ?.flatMap { it.second }
                                            ?.distinct()
                                    val headerInfoField = mapToField(headerData, fields)
                                    IndexedValue(
                                        idx,
                                        SourceData(
                                            // This needs to be adapted as soon as this source provider is used in practice
                                            mapOf(
                                                TARGET_FIELD_NAME to headerInfoField,
                                            ),
                                            null,
                                        ),
                                    )
                                }
                        }
                }
            }
    }

    private fun mapToField(
        headerData: HeaderDto?,
        fields: List<FieldDefinition>?,
    ): MdHeaderInfoField {
        return MdHeaderInfoFieldData(
            classifications =
                headerData?.classifications?.map {
                    it.key.key to
                        it.value.map {
                                clKey ->
                            clKey.key
                        }
                }?.toMap(),
            fieldDefinitions = fields,
        ).asField()
    }

    private fun mapToFieldDefinitions(data: ClassificationFieldsInfoDto): List<FieldDefinition> {
        return data.fields.map {
            FieldDefinition.fromDto(it.field)
        }
    }

    private fun fetchAllClassificationFields(
        accessCheck: AccessCheck,
        allClassificationKeys: List<Map.Entry<SimpleKeyDto, List<SimpleKeyDto>>>,
    ): Flux<Pair<Map.Entry<SimpleKeyDto, List<SimpleKeyDto>>, ClassificationFieldsInfoDto>> {
        return allClassificationKeys
            .toFlux()
            .concatMap {
                mdBasicDataService.getClassificationTypeFields(accessCheck, it.key, it.value)
                    .map { ci -> it to ci }
            }
    }

    override fun updateSelector(
        calculation: Field,
        newSourceDataKey: VersionedResult,
        versionInfo: VersionInfo?,
    ) {
        // we don't need this logic
    }
}
