package com.nu.bom.core.api

import com.nu.bom.core.api.dtos.AutocompleteContainer
import com.nu.bom.core.api.dtos.AutocompleteResponse
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.service.ManufacturingEntityQueryService
import com.nu.bom.core.user.AccessCheckProvider
import com.tset.core.module.TranslationServiceProvider
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import kotlin.collections.plus

private const val LINK_SECTION = "link"

private const val UNLINK_SECTION = "unlink"

@RestController
@RequestMapping("/api")
class ManufacturingEntityQueryController(
    private val accessCheckProvider: AccessCheckProvider,
    private val manufacturingEntityQueryService: ManufacturingEntityQueryService,
    private val translationServiceProvider: TranslationServiceProvider,
) {
    @GetMapping("/man/{bomNodeId}/steps")
    fun getSteps(
        @RequestParam branch: String?,
        @PathVariable bomNodeId: String,
        @RequestParam stepless: Boolean = false,
        @RequestParam(required = false, defaultValue = "false") includeExternal: Boolean = false,
        @AuthenticationPrincipal jwt: Jwt?,
    ): Mono<AutocompleteContainer> =
        accessCheckProvider.doAs(jwt) { accessCheck ->
            translationServiceProvider.getTranslationsService().flatMap { translationService ->
                val result =
                    if (stepless) {
                        manufacturingEntityQueryService
                            .getBomNodeManufacturingStepsWithParentFirst(
                                accessCheck,
                                bomNodeId,
                                branch,
                                includeExternal,
                            ).collectList()
                            .flatMapMany { entities ->
                                val first = AutocompleteResponse("No step", entities.first().entityId, section = UNLINK_SECTION)
                                val rest =
                                    entities.drop(1).map {
                                        // this translation call should not exist, see COST-81971
                                        val name =
                                            it.displayName?.let { designation ->
                                                translationService.translate(designation, ignoreThisForMissingTranslationsReport = true)
                                            } ?: ""
                                        AutocompleteResponse(
                                            name,
                                            it.entityId,
                                            section = LINK_SECTION,
                                        )
                                    }

                                Flux.fromIterable(listOf(first) + rest)
                            }
                    } else {
                        manufacturingEntityQueryService
                            .getBomNodeManufacturingStepsWithoutParent(
                                accessCheck,
                                bomNodeId,
                                branch,
                                includeExternal,
                            ).map { entity ->
                                entity.toAutocompleteResponse()
                            }
                    }
                result
                    .collectList()
                    .map { results ->
                        AutocompleteContainer(
                            items = results,
                            sections = null,
                        )
                    }
            }
        }

    private fun ManufacturingEntity.toAutocompleteResponse() = AutocompleteResponse(this.displayName ?: "", this.entityId, null)

    @GetMapping("/man/bomNode/{bomNodeId}/branch/{branch}/stepTypes")
    fun getStepTypes(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable bomNodeId: String,
        @PathVariable branch: String,
        @RequestParam allOption: Boolean = false,
    ): Mono<AutocompleteContainer> =
        accessCheckProvider.doAs(jwt) { accessCheck ->
            manufacturingEntityQueryService
                .getBomNodeManufacturingSteps(
                    accessCheck,
                    bomNodeId,
                    branch,
                ).map {
                    AutocompleteResponse(it.name, it.name, null)
                }.collectList()
                .map { results ->
                    AutocompleteContainer(
                        items = AutocompleteResponse.responseWithAll(allOption, results).distinctBy { it.name },
                        sections = null,
                    )
                }
        }
}
