package com.nu.bom.core.config

import com.nu.http.config.Resilience
import io.github.resilience4j.bulkhead.BulkheadRegistry
import io.github.resilience4j.micrometer.tagged.TaggedBulkheadMetrics
import io.github.resilience4j.micrometer.tagged.TaggedRateLimiterMetrics
import io.github.resilience4j.micrometer.tagged.TaggedRetryMetrics
import io.github.resilience4j.micrometer.tagged.TaggedTimeLimiterMetrics
import io.github.resilience4j.ratelimiter.RateLimiterRegistry
import io.github.resilience4j.retry.RetryRegistry
import io.github.resilience4j.timelimiter.TimeLimiterRegistry
import io.micrometer.core.instrument.MeterRegistry
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Profile

@Configuration
@EnableConfigurationProperties(
    Resilience::class,
)
@Profile("cloud")
class ResilienceMetricsConfig {
    @Bean
    fun registerBulkheadMetrics(
        resilience: Resilience,
        meterRegistry: MeterRegistry,
    ): TaggedBulkheadMetrics {
        val metrics = TaggedBulkheadMetrics.ofBulkheadRegistry(resilience.getResilienceRegistries()["bulkheadRegistry"] as BulkheadRegistry)
        metrics.bindTo(meterRegistry)
        return metrics
    }

    @Bean
    fun registerTimeLimiterMetrics(
        resilience: Resilience,
        meterRegistry: MeterRegistry,
    ): TaggedTimeLimiterMetrics {
        val metrics =
            TaggedTimeLimiterMetrics.ofTimeLimiterRegistry(
                resilience.getResilienceRegistries()["timeLimiterRegistry"] as TimeLimiterRegistry,
            )
        metrics.bindTo(meterRegistry)
        return metrics
    }

    @Bean
    fun registerRateLimiterMetrics(
        resilience: Resilience,
        meterRegistry: MeterRegistry,
    ): TaggedRateLimiterMetrics {
        val metrics =
            TaggedRateLimiterMetrics.ofRateLimiterRegistry(
                resilience.getResilienceRegistries()["rateLimiterRegistry"] as RateLimiterRegistry,
            )
        metrics.bindTo(meterRegistry)
        return metrics
    }

    @Bean
    fun registerRetryMetrics(
        resilience: Resilience,
        meterRegistry: MeterRegistry,
    ): TaggedRetryMetrics {
        val metrics = TaggedRetryMetrics.ofRetryRegistry(resilience.getResilienceRegistries()["retryRegistry"] as RetryRegistry)
        metrics.bindTo(meterRegistry)
        return metrics
    }
}
