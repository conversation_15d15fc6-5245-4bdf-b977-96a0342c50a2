package com.nu.bom.core.manufacturing.service

import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.FieldWithResult
import com.nu.bom.core.manufacturing.fieldTypes.Null
import com.nu.bom.core.manufacturing.service.calculator.CalculationResult
import com.nu.bom.core.manufacturing.service.calculator.DynamicBehaviourCreationResult
import com.nu.bom.core.manufacturing.service.calculator.EntityDeletionResult
import com.nu.bom.core.manufacturing.service.calculator.ManufacturingTreeChangeResult
import com.nu.bom.core.manufacturing.utils.DependencyUpdateUtils
import com.nu.bom.core.manufacturing.utils.FieldExtractionUtils
import org.slf4j.LoggerFactory

class FieldGraph(
    newFields: List<Field>,
    val version: Int,
    val request: CalculationRequest,
) {
    companion object {
        private val logger = LoggerFactory.getLogger(FieldGraph::class.java)
    }

    private val startTime: Long = System.nanoTime()
    var fields: List<Field> = listOf()
    var calculateableFields: List<Field> = listOf()

    private var resultFields: ResultFields? = null

    init {

        val others =
            newFields.filter { c ->
                c.hasAlreadyCurrentResultForVersion(version)
            }
        nextRound(
            calculatedInTheLastRound = others,
            newFields = newFields,
            removedEntities = listOf(),
            removedEntityIds = setOf(),
        )
    }

    fun returnResultFields() = resultFields

    fun createResult(): CalculationService.CalculationResults {
        if (resultFields == null) {
            finish()
        }
        val result = resultFields!!
        val nullResults = result.calculated.filter { it.getCurrentVersionedResult()?.result is Null }
        val openFields = result.open + calculateableFields.filter { it.resultNewVersion == null }

        val (nullInputs, nullCalculations) =
            CalculationService.splitInputCalculation(nullResults)

        return CalculationService.CalculationResults(
            open = openFields,
            calculated = result.calculated + calculateableFields.filter { it.resultNewVersion != null },
            nullInputs = nullInputs,
            nullCalculations = nullCalculations,
        )
    }

    private fun checkToBeCalculated(openFields: Collection<Field>): List<Field> =
        openFields.filter { c ->
            c.canBeCalculated(version, request.onlyNewFieldsShouldBeRecalculated)
        }

    fun updateGraph(
        calculationResults: List<CalculationResult>,
        fieldExtractionUtils: FieldExtractionUtils,
    ) {
        // Update ObjectGraph
        val treeChangeResult = calculationResults.filterIsInstance(ManufacturingTreeChangeResult::class.java)
        updateObjectGraph(treeChangeResult) // UPDATE TREE

        val entityDeletionResult = calculationResults.filterIsInstance(EntityDeletionResult::class.java)
        updateObjectGraphDeleteEntities(entityDeletionResult)

        val dynamicBehaviourChangeResult =
            calculationResults.filterIsInstance(DynamicBehaviourCreationResult::class.java)

        val newResults = extractFieldResults(calculationResults, request)
        request.addOldResults(newResults)

        // new fields from newly created entities
        val newFieldsFromCreatedEntities = fieldExtractionUtils.extractGeneratedFields(treeChangeResult, request)

        // new dynamically created fields from a behaviour in an existing entity
        val newFieldsFromCreatedBehaviour =
            fieldExtractionUtils
                .extractGeneratedFieldsForDynamicBehaviour(dynamicBehaviourChangeResult, request)
        newFieldsFromCreatedBehaviour.forEach { (entity, newFields) ->
            fieldExtractionUtils.addFieldsForDynamicBehaviour(entity, newFields, request)
        }

        val newFields = newFieldsFromCreatedEntities + newFieldsFromCreatedBehaviour.values.flatten()

        updateFields(calculationResults)

        val removedEntitiesByTreeChange = extractParentRemovedEntities(treeChangeResult)
        val removedEntityIdsByTreeChange = extractRemovedEntityIds(treeChangeResult)

        val removedEntitiesByDeletionField: MutableList<ManufacturingEntity> = mutableListOf()
        val removedEntityIdsByDeletionField: MutableSet<String> = mutableSetOf()
        entityDeletionResult.forEach {
            removedEntitiesByDeletionField.addAll(it.removedParentEntities())
            removedEntityIdsByDeletionField.addAll(it.allRemovedChildEntityId())
        }

        val removedEntities = removedEntitiesByTreeChange + removedEntitiesByDeletionField
        val removedEntityIds = removedEntityIdsByTreeChange + removedEntityIdsByDeletionField

        if (SpecialLinkMapping.VERIFY_ENTITY_REF_UNIQUENESS) {
            treeChangeResult.forEach {
                it.updateEntityRefMapping(request.specialLinkMapping)
            }
        }

        val calculatedInTheLastRound = extractCalculatedFields(calculationResults)

        updateGroupDependencies(calculatedInTheLastRound)

        nextRound(calculatedInTheLastRound, newFields, removedEntities, removedEntityIds)
    }

    private fun extractCalculatedFields(calculationResults: List<CalculationResult>): List<Field> {
        val result = mutableListOf<Field>()
        calculationResults.forEach { it.collectFields(result) }
        return result
    }

    private fun extractParentRemovedEntities(calculationResults: List<ManufacturingTreeChangeResult>): List<ManufacturingEntity> =
        calculationResults.flatMap { it.removedParentEntities() }

    private fun extractRemovedEntityIds(calculationResults: List<ManufacturingTreeChangeResult>): Set<String> =
        calculationResults.flatMap { it.allRemovedChildEntityId() }.toSet()

    private fun extractFieldResults(
        calculationResults: List<CalculationResult>,
        request: CalculationRequest,
    ): List<FieldWithResult> = calculationResults.flatMap { it.extractResults(request) }

    private fun updateObjectGraph(calculationResults: List<ManufacturingTreeChangeResult>) {
        calculationResults.forEach(ManufacturingTreeChangeResult::updateTree)
    }

    private fun updateObjectGraphDeleteEntities(calculationResults: List<EntityDeletionResult>) {
        calculationResults.forEach(EntityDeletionResult::updateTree)
    }

    private fun nextRound(
        calculatedInTheLastRound: List<Field>,
        newFields: List<Field>,
        removedEntities: List<ManufacturingEntity>,
        removedEntityIds: Set<String>,
    ) {
        check(this.resultFields == null) {
            "Calculation round started after calculation finished"
        }

        if (newFields.isNotEmpty()) {
            if (logger.isTraceEnabled) {
                logger.trace("number of new fields: ${newFields.size}")
                val newFieldStr =
                    newFields.joinToString(separator = "\n\t") { "${it.entity.entityId}/${it.name}(${it.hashCode()})" }
                logger.trace("fields added in this round: $newFieldStr")
            }
        }
        val duration1 = (System.nanoTime() - startTime) / 1_000_000

        val newEntitiesWithFields =
            if (newFields.isNotEmpty()) {
                request.prepareNewFields(newFields)
            } else {
                null
            }

        if (removedEntities.isNotEmpty()) {
            logger.debug("entities removed in this round: {}", removedEntityIds)

            DependencyUpdateUtils.resetSiblingAndChildDependencies(removedEntities, removedEntityIds)
            request.specialLinkMapping.removeEntities(removedEntityIds)
            request.removeEntityLinkProviderFieldsFromEntities(removedEntityIds)
            DependencyUpdateUtils.removeOpenDependenciesToRemovedEntities(removedEntityIds, fields)
            fields = removeDeletedFields(fields, removedEntityIds)
        }

        val entityLinkProviderToRecalc = getEntityLinkProvidersForNewEntities(newEntitiesWithFields)

        val newCalculatable =
            if (newFields.isNotEmpty()) {
                // replace the sibling dependencies, there shouldn't be any structural changes
                // during new field calculations so we do not do any sibling replacement
                if (!request.onlyNewFieldsShouldBeRecalculated) {
                    DependencyUpdateUtils.replaceSiblingDependencies(version, newFields)
                }
                val newCalculatable = checkToBeCalculated(newFields)

                fields = newFields + fields

                newCalculatable
            } else {
                emptyList()
            }

        calculateableFields =
            (
                newCalculatable +
                    collectSuccessors(
                        calculatedInTheLastRound,
                        removedEntityIds,
                    ) + entityLinkProviderToRecalc
            ).distinct()

        if (calculateableFields.isEmpty()) {
            calculateableFields = nullableEntityLinkFieldsWithoutResults()
        }

        val duration2 = ((System.nanoTime() - startTime) / 1_000_000) - duration1

        if (logger.isTraceEnabled) {
            logger.trace(
                "Calculation Round finished with ${newFields.size} new fields -\n" +
                    " took $duration1 ms for calculation and $duration2 ms for dependency updates",
            )
        }
    }

    /**
     * find all entity link fields that are nullable and do not have a result yet
     *
     * we use this to calculate optional entitylinkfield successors to make some progress.
     * to this end we need a side effect that removes all the dependencies
     * */
    private fun nullableEntityLinkFieldsWithoutResults() =
        request
            .getAllEntityLinkProviders()
            .flatMap { it.getSuccessorList() }
            .filter {
                it.model.entityLinkField !=
                    null
            }.filterNot { it.hasResultForVersion(version) }
            .map {
                it.removeDependencies()
                it
            }

    private fun getEntityLinkProvidersForNewEntities(newEntitiesWithFields: Map<ManufacturingEntity, List<Field>>?) =
        newEntitiesWithFields?.flatMap {
            val entityRef = it.key.entityRef
            val localManufacturingId = it.key.getLocalManufacturingId()
            if (entityRef != null && localManufacturingId != null) {
                request.getEntityLinkProvider(entityRef, localManufacturingId)
            } else {
                emptyList()
            }
        } ?: emptyList()

    private fun removeDeletedFields(
        fields: List<Field>,
        removedEntities: Set<String>,
    ): List<Field> =
        fields
            .filter { field ->
                !removedEntities.contains(field.entity.entityId)
            }.map { field ->
                if (removedEntities.isNotEmpty()) {
                    field.removeSuccessorFieldInEntities(removedEntities)
                }
                field
            }

    private fun collectSuccessors(
        calculated: List<Field>,
        removedEntityIds: Set<String>,
    ): List<Field> =
        removeDeletedFields(calculated, removedEntityIds)
            .flatMap { calculatedField ->
                calculatedField.collectCalculatableSuccessors(version, request.onlyNewFieldsShouldBeRecalculated)
            }.distinct()

    fun finish() {
        val (calculated, open) = fields.partition { it.hasAlreadyCurrentResultForVersion(version) }
        val newResultFields =
            ResultFields(
                calculated = calculated,
                open = open,
            )
        this.resultFields = newResultFields
        cleanupPartlyExecutedEntityCreations(newResultFields)
    }

    private fun cleanupPartlyExecutedEntityCreations(resultFields: ResultFields) {
        resultFields.open
            .filter {
                when (it.model.fieldType) {
                    FieldType.EntityProvider -> {
                        val deps = it.getOpenDependencies(version).toList().joinToString(separator = "\n")
                        logger.warn("Field ${it.toDetailedName()} is still not calculated, open dependencies: \n$deps")
                        false
                    }
                    FieldType.OrderedEntityCreation -> true
                    else -> false
                }
            }.forEach {
                it.clearAlreadyCreatedEntities()
            }
    }

    private fun updateFields(calculationResults: List<CalculationResult>) {
        calculationResults.forEach(CalculationResult::updateField)
    }

    fun nextRound(fieldChanges: (Field, Int) -> Field): FieldGraph {
        val newVersion = version + 1
        val newFields = applyFieldChanges(fields, newVersion, fieldChanges)
        newFields.forEach { it.updateForNextCalculation() }
        return FieldGraph(
            newFields = newFields,
            version = newVersion,
            request = request,
        )
    }

    private fun applyFieldChanges(
        fields: List<Field>,
        newVersion: Int,
        fieldChanges: (Field, Int) -> Field,
    ): List<Field> = fields.map { fieldChanges(it, newVersion) }

    private fun updateGroupDependencies(calculatedInTheLastRound: List<Field>) =
        calculatedInTheLastRound
            .filter { it.model.returnTypeCouldBeNonFieldResult == Entities.GROUP.name }
            .map { it.entity }
            .distinct()
            .forEach(DependencyUpdateUtils::updateGroupDependencies)
}
