package com.nu.bom.core.threedb

import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.ThreeDbVersionedPart
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.nu.bom.core.utils.flatFold
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono

data class ThreeDbHeader(
    val accessCheck: AccessCheck,
    val threeDbVersionedPart: ThreeDbVersionedPart,
)

interface ThreeDbService {
    @TsetSuppress("tset:reactive:flux-flatmap") // threeDb can handle it
    fun downloadEverything(header: ThreeDbHeader): Mono<Map<String, ByteArray>> =
        getSet(header).flatMap { resources ->
            Flux
                .fromIterable(resources)
                .flatMap { resource ->
                    getBinaryResource(header, resource, useCalculatedValue = false).map { binary -> Pair(resource, binary) }
                }.collectList()
                .map { it.toMap() }
        }

    fun uploadEverything(
        set: Map<String, ByteArray>,
        accessCheck: AccessCheck,
        initialVersionedPart: ThreeDbVersionedPart? = null,
    ): Mono<ThreeDbVersionedPart> =
        set.entries.flatFold(
            initialVersionedPart?.toMono() ?: createPart(accessCheck),
        ) { threeDbVersionedPart, entry ->
            val header = ThreeDbHeader(accessCheck = accessCheck, threeDbVersionedPart = threeDbVersionedPart)
            setBinaryResource(header, entry.key, entry.value)
        }

    fun getShapes(accessCheck: AccessCheck): Mono<List<ThreeDbShapeIdentifier>>

    fun createPart(accessCheck: AccessCheck): Mono<ThreeDbVersionedPart>

    fun copyPart(
        accessCheck: AccessCheck,
        threeDBShapeIdentifier: ThreeDbShapeIdentifier,
    ): Mono<ThreeDbVersionedPart>

    fun getResource(
        header: ThreeDbHeader,
        resourceType: ThreeDbFieldType.Resource,
        useCalculatedValue: Boolean,
    ): Mono<FieldResultStar>

    fun getBinaryResource(
        header: ThreeDbHeader,
        resourceName: String,
        useCalculatedValue: Boolean = false,
    ): Mono<ByteArray>

    fun setBinaryResource(
        header: ThreeDbHeader,
        resourceName: String,
        resourceValue: ByteArray,
    ): Mono<ThreeDbVersionedPart>

    fun setResource(
        header: ThreeDbHeader,
        resourceType: ThreeDbFieldType.Resource,
        value: FieldResultStar,
    ): Mono<ThreeDbVersionedPart>

    fun deleteResource(
        header: ThreeDbHeader,
        resourceType: ThreeDbFieldType.Resource,
    ): Mono<ThreeDbVersionedPart>

    fun getSet(header: ThreeDbHeader): Mono<Set<String>>

    fun exists(
        partId: String,
        accessCheck: AccessCheck,
    ): Mono<Boolean>
}
