package com.nu.bom.core.api.dtos

import com.nu.bom.core.manufacturing.enums.TechnologyLabel
import com.nu.bom.core.manufacturing.enums.TechnologyPageViewGroup
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier

data class CardResponseDto(
    val id: String,
    val title: String,
    val description: String,
    val images: List<String>,
    // latest cost module configuration
    val version: ConfigurationIdentifier,
    val costModuleVersionUrl: String,
    val technologyPageViewGroup: TechnologyPageViewGroup,
    val label: TechnologyLabel? = null,
)
