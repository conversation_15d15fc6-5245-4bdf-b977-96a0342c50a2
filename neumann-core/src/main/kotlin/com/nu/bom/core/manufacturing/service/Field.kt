package com.nu.bom.core.manufacturing.service

import com.fasterxml.jackson.annotation.JsonIgnore
import com.nu.bom.core.exception.FieldConversionException
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.EntityProvided
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.InputDependency
import com.nu.bom.core.manufacturing.fieldTypes.NoCalcFieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Null
import com.nu.bom.core.manufacturing.fieldTypes.NumericFieldResult
import com.nu.bom.core.manufacturing.utils.FieldModel
import com.nu.bom.core.manufacturing.utils.NamedField
import com.nu.bom.core.manufacturing.utils.NamedFields
import com.nu.bom.core.manufacturing.utils.toValuable
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.tset.common.util.BARE_BONE_NOT_NULL_STYLE
import org.apache.commons.lang3.builder.ToStringBuilder
import org.apache.commons.text.StringEscapeUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import kotlin.reflect.KClass
import kotlin.reflect.KType
import kotlin.reflect.full.isSubclassOf
import kotlin.reflect.jvm.jvmErasure

open class Field(
    @JsonIgnore
    val model: FieldModel,
    @Deprecated("use the model or entity instead")
    var calcKey: FieldKey,
    val executionContext: ManufacturingEntity,
    val entity: ManufacturingEntity,
    /**
     * The value which is provided as an input for this calculation. Comes from the {@link com.nu.bom.core.manufacturing.service.CalculationRequest##getInput()}
     */
    private var oldInput: VersionedResult?,
    oldResult: VersionedResult?,
    /**
     * The initial, system provided result for a field. If the user later change the field value, the UI will provide a 'revert to system default' action.
     */
    private var initialResult: VersionedResult?,
) {
    var oldResult: VersionedResult? = null
        private set

    init {
        // set actual entity in as CalculationContextEntity in ExecutionContext
        executionContext.calculationContextEntity = entity
        val returnType = model.getActualReturnType()
        oldInput = oldInput?.let { convert(it, returnType) }
        this.oldResult = oldResult?.let { convert(it, returnType) }
        initialResult = initialResult?.let { convert(it, returnType) }
    }

    val inputs: MutableList<InputKey> = mutableListOf()
    val openDependencies: LinkedHashSet<InputKey> = linkedSetOf()

    private val successors: MutableList<Field> = mutableListOf()

    private var result: VersionedResult? = null

    val resultVersion: Int?
        get() = result?.version

    val resultNewVersion: Int?
        get() = result?.newVersion

    val entityRef: String?
        get() = entity.entityRef

    fun addParameter(fieldKey: InputKey) {
        if (!inputs.contains(fieldKey)) {
            inputs.add(fieldKey)
        }
        fieldKey.addSuccessor(this)
    }

    fun addParameterAt(
        index: Int,
        fieldKey: InputKey,
    ) {
        if (inputs.contains(fieldKey)) {
            error("Input $fieldKey is already in as input: $inputs")
        }
        inputs.add(index, fieldKey)
        fieldKey.addSuccessor(this)
    }

    private fun getOptionalInputField(fieldName: String): Field? = inputs.find { it.name == fieldName }?.linkedField

    fun getInputField(fieldName: String): Field =
        getOptionalInputField(fieldName) ?: error("Unable to find field input: $fieldName in $this")

    val isMonetaryType: Boolean get() =
        this.model.isMonetaryType
    val isDynamicQuantityUnit: Boolean get() =
        this.model.isDynamicQuantityUnit

    fun linkedFields(): List<Field> = inputs.mapNotNull { input -> input.linkedField }

    fun sourceEquals(source: FieldResult.SOURCE): Boolean = getCurrentResultSource() == source

    fun addSuccessorField(field: Field) {
        successors.add(field)
    }

    fun getSuccessorList(): List<Field> = successors

    fun getDependencyOn(field: Field): InputKey? {
        val input =
            inputs.find {
                it.linkedField == field
            } ?: openDependencies.find {
                it.linkedField == field
            }
        return input
    }

    fun removeSuccessorField(field: Field) {
        successors.remove(field)
    }

    fun getPath(): FieldPath = FieldPath(name = name, path = entity.getFullPath())

    fun removeSuccessorFieldInEntities(removedEntities: Set<String>) {
        successors.removeIf { removedEntities.contains(it.entity.entityId) }
    }

    fun addParameters(fieldKeys: Collection<InputKey>) {
        fieldKeys.forEach {
            if (!inputs.contains(it)) {
                inputs.add(it)
                it.addSuccessor(this)
            }
        }
    }

    fun addOpenDependency(fieldKey: InputKey) {
        openDependencies.add(fieldKey)
        fieldKey.addSuccessor(this)
    }

    fun updateSpecialLink(newEntity: ManufacturingEntity) {
        val oldInputs =
            inputs.filter {
                (it.relType == RelType.SPECIAL_LINK || it.relType == RelType.SPECIAL_LINK_PARAM) &&
                    it.entityRef == newEntity.entityRefForFieldKey()
            }

        if (oldInputs.isEmpty()) {
            error(
                "No special_link found in $inputs matching entityRef='${entity.entityRefForFieldKey()}'",
            )
        }

        oldInputs
            .mapNotNull { oldInput ->
                val newField = newEntity.getFieldObject(oldInput.name)
                newField?.let { oldInput to newField }
            }.forEach { (oldInput, newField) ->
                val newInput =
                    oldInput.copy(
                        linkedField = newField,
                        entityId = newField.entity.entityId,
                        entityType = newField.entity.getEntityType(),
                    )
                inputs.remove(oldInput)
                oldInput.removeSuccessor(this)
                addParameter(newInput)
            }
    }

    fun getDirectlyLink(): InputKey? =
        if (model.annotatedDependency != null) {
            // the full function is annotated with a @Parent/@SpecialLink/@Children annotation,
            // so we can use the direct link...
            // and it's not a CREATION link, so effectively CHILD/SPECIAL_LINK/PARENT
            inputs.firstOrNull { it.paramPos == 0 && it.relType != RelType.CREATION }
        } else {
            // Just to handle the very special, special linking
            if (inputs.size == 1) {
                val firstInput = this.inputs.first()
                if (firstInput.relType == RelType.SPECIAL_LINK) {
                    firstInput
                } else {
                    null
                }
            } else {
                null
            }
        }

    fun findRootCauses(
        version: Int,
        func: (Field) -> Boolean,
    ): Set<InputKey> {
        val map = mutableMapOf<FieldIdKey, Set<InputKey>>()
        return findRootCauses(func, map, version)
    }

    private fun findRootCauses(
        func: (Field) -> Boolean,
        results: MutableMap<FieldIdKey, Set<InputKey>>,
        version: Int,
    ): Set<InputKey> {
        val key = this.fieldIdKey()
        val cached = results[key]
        return if (cached != null) {
            cached
        } else {
            val res =
                (this.inputs.asSequence() + this.openDependencies.asSequence())
                    .flatMap {
                        if (it.linkedField == null || func(it.linkedField)) {
                            setOf(it)
                        } else {
                            if (it.linkedField.hasResultForVersion(version)) {
                                emptySet()
                            } else {
                                it.linkedField.findRootCauses(func, results, version)
                            }
                        }
                    }.toSet()
            results[key] = res
            res
        }
    }

    // For testing
    override fun toString(): String = StringEscapeUtils.escapeJava("Field($calcKey,$result,$resultVersion,$resultNewVersion)")

    fun getOpenDependencies(calculationVersion: Int): Sequence<InputKey> =
        (this.inputs.asSequence() + this.openDependencies.asSequence()).filter {
            !it.hasResultForVersion(calculationVersion, this)
        }

    fun canBeCalculated(
        calculationVersion: Int,
        onlyNewFieldsShouldBeRecalculated: Boolean,
    ): Boolean {
        if (onlyNewFieldsShouldBeRecalculated && !sourceEquals(FieldResult.SOURCE.R) && getCurrentVersionedResult() != null) {
            return true
        }
        val dependenciesFulfilled = isDependenciesFulfilled(calculationVersion)
        return when (model.fieldType) {
            FieldType.DynamicField,
            FieldType.ManualEntityCreator,
            -> true

            is FieldType.Calculation ->
                dependenciesFulfilled ||
                    (
                        model.fieldType.isInput() &&
                            run {
                                val initialFieldSource = getInitialFieldResult()?.result?.source
                                // or field has fixed overwritten value - and no dependencies are open
                                (
                                    initialFieldSource == FieldResult.SOURCE.I &&
                                        noOpenDependencies(
                                            calculationVersion,
                                        )
                                ) &&
                                    !model.mustBeCalculated()
                            }
                    )
            FieldType.Creation,
            FieldType.EntityProvider,
            FieldType.OrderedEntityCreation,
            FieldType.CurrencyConverter,
            FieldType.UnitConverter,
            FieldType.DataSourcerUpdater,
            FieldType.ChildLoadTrigger,
            FieldType.BehaviourCreation,
            FieldType.EntityDeletion,
            FieldType.EntityLinkProvider,
            // Which have relation(s) (inputs are not empty) which are already calculated
            // or no relation (inputs empty) and available result
            -> dependenciesFulfilled
        }
    }

    fun toInputDependency(): InputDependency =
        InputDependency(
            name = name,
            entityId = entity.entityId,
            version = getCurrentVersion(),
        )

    fun getCurrentVersion() = resultVersion ?: oldInput?.version ?: oldResult?.version ?: entity.version

    fun getNewVersion() = resultNewVersion ?: oldInput?.newVersion ?: oldResult?.newVersion ?: entity.version

    fun toInputKey(relType: RelType?): InputKey =
        InputKey(
            paramPos = 0,
            entityId = entity.entityId,
            entityType = entity.getEntityType(),
            relType = relType,
            default = model.annotatedDependency?.getDefaultValue(),
            type = model.returnTypeCouldBeNonFieldResult,
            name = name,
            linkedField = this,
        )

    fun toMatchKey() =
        MatchKey(
            name = name,
            entityId = entity.entityId,
            entityType = entity.getEntityType(),
        )

    val name: String
        get() = model.name

    fun fieldIdKey(): FieldIdKey = FieldIdKey(name = name, entityId = entity.entityId)

    fun toDetailedNameAndValues() =
        "${toDetailedName()} [v$resultVersion:$resultNewVersion] " +
            "(${inputs.size} Inputs: {${inputs.joinToString { it.name }}} " +
            "${openDependencies.size}, openDeps: {${openDependencies.joinToString {
                it.name
            }}}) = ${getCurrentResult()} "

    private fun convert(
        source: VersionedResult,
        target: KType?,
    ): VersionedResult =
        target
            ?.takeUnless {
                val clazz = (it.classifier as KClass<*>)
                clazz.isAbstract && source.result::class.isSubclassOf(clazz)
            }?.let {
                val convertedResult = convert(source.result, target)
                VersionedResult(
                    result = convertedResult,
                    version = source.version,
                    newVersion = source.newVersion,
                )
            } ?: source

    private fun convert(
        source: FieldResultStar,
        target: KType,
    ) = try {
        source.convert(target)
    } catch (e: Exception) {
        throw FieldConversionException(this.toMatchKey(), e.message ?: "", cause = e)
    }

    private fun getParamsForCalculation(
        version: Int,
        calculationServiceProvider: CalculationServiceProvider,
    ): List<Any?> {
        val params =
            model.functionParameterMetaInfo.map { paramMetaInfo ->
                val value = getValue(paramMetaInfo, version, calculationServiceProvider)
                // Translate internal Null Container object to actual null for the call
                if (value is Null) {
                    filterNull(paramMetaInfo.getDefaultValue())
                } else {
                    value
                }
            }

        return listOf(executionContext) + params
    }

    private fun getValue(
        functionParameter: FieldModel.FunctionParameterMetaInfo,
        version: Int,
        calculationServiceProvider: CalculationServiceProvider,
    ): Any? =
        if (functionParameter.isInjectableEngineService) {
            calculationServiceProvider.getCalculationService(
                functionParameter.type.jvmErasure as KClass<InjectableEngineService>,
            )
        } else {
            getValueFromInputParameters(functionParameter, version)
        }

    private fun getValueFromInputParameters(
        paramMetaInfo: FieldModel.FunctionParameterMetaInfo,
        version: Int,
    ): Any? {
        val inputParams = inputs.filter { it.paramPos == paramMetaInfo.index && it.relType != RelType.CREATION }
        return if (inputParams.isEmpty()) {
            createEmptyValue(paramMetaInfo)
        } else if (inputParams.size == 1 && paramMetaInfo.collectionType == null) {
            inputParams[0].getFieldResult(version)?.let {
                internalConvert(it, paramMetaInfo)
            }
        } else {
            convertCollection(paramMetaInfo, inputParams, version)
        }
    }

    private fun convertCollection(
        param: FieldModel.FunctionParameterMetaInfo,
        inputs: List<InputKey>,
        version: Int,
    ): Any? {
        requireNotNull(param.collectionType) {
            "wrong datatype `$param` collection expected with a type argument, \n\tinputs: $inputs!"
        }
        val nonNullEntries =
            inputs.mapNotNull { input ->
                filterNull(input.getFieldResult(version))?.let { res ->
                    input to res
                }
            }
        return when (param.collectionType) {
            // If you add more types with 2 parameters where the actual value is in the second type parameter, you should
            // also adapt [AnnotatedDependency.typeOrArgumentTypeName]
            is Map<*, *> ->
                nonNullEntries
                    .associate { (input, res) ->
                        val id = requireNotNull(input.linkedField?.entity?._id) { "The field's entity does not have an _id" }
                        id to internalConvert(res, param)
                    }.ifEmpty { null }
            is NamedFields<*> ->
                nonNullEntries
                    .map { (input, res) ->
                        NamedField(input.name, internalConvert(res, param))
                    }.ifEmpty { null }
                    ?.let(::NamedFields)
            else ->
                nonNullEntries
                    .map { (_, res) ->
                        internalConvert(res, param)
                    }.ifEmpty { null }
        }
    }

    private fun internalConvert(
        result: FieldResultStar,
        parameterMetaInfo: FieldModel.FunctionParameterMetaInfo,
    ): Any =
        when {
            // it is QuantityFieldResult && param.type.isValuable() -> it.res
            result is NumericFieldResult<*> && parameterMetaInfo.isValuable -> result.toValuable()
            else -> convert(result, parameterMetaInfo.type)
        }

    data class Parameters(
        val resolvedParams: List<Any?>,
        val noCalcResults: List<NoCalcFieldResult>,
    )

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    fun resolveParameters(
        externalStorageService: ExternalStorageService,
        version: Int,
        calculationServiceProvider: CalculationServiceProvider,
    ): Mono<Parameters> {
        val paramsForCalculation = getParamsForCalculation(version, calculationServiceProvider)
        val needToResolve = findNoCalcFieldResults(paramsForCalculation)
        return Flux
            .fromIterable(needToResolve)
            .flatMap { fieldResult ->
                retrieveNoCalcDAta(fieldResult, externalStorageService)
            }.collectList()
            .map {
                unboxNoCalcFieldResult(paramsForCalculation, needToResolve)
            }
    }

    private fun retrieveNoCalcDAta(
        fieldResult: NoCalcFieldResult,
        externalStorageService: ExternalStorageService,
    ): Mono<NoCalcFieldResult> =
        if (fieldResult.data == null) {
            externalStorageService.retrieve(fieldResult.objectId).map { externalData ->
                fieldResult.data = externalData
                fieldResult
            }
        } else {
            Mono.just(fieldResult)
        }

    private fun findNoCalcFieldResults(params: List<Any?>): List<NoCalcFieldResult> =
        params.flatMap {
            when (it) {
                is NoCalcFieldResult -> listOf(it)
                is List<*> -> findNoCalcFieldResults(it)
                else -> emptyList()
            }
        }

    private fun unboxNoCalcFieldResult(
        params: List<Any?>,
        noCalcResults: List<NoCalcFieldResult>,
    ): Parameters {
        fun replaceNoCalcFieldResult(params: List<Any?>): List<Any?> =
            params.map {
                when (it) {
                    is NoCalcFieldResult -> it.data
                    is List<Any?> -> replaceNoCalcFieldResult(it)
                    else -> it
                }
            }

        return Parameters(
            resolvedParams = replaceNoCalcFieldResult(params),
            noCalcResults = noCalcResults,
        )
    }

    private fun filterNull(value: FieldResultStar?): FieldResultStar? =
        if (value is Null) {
            null
        } else {
            value
        }

    private fun createEmptyValue(param: FieldModel.FunctionParameterMetaInfo): Any? =
        when {
            param.isNullable -> null
            param.collectionType != null -> param.collectionType
            param.hasDefault() -> param.getDefaultValue()
            else -> throw IllegalArgumentException("Parameter $param specified as non-null, but no value can be found!")
        }

    fun call(paramList: List<Any?>): Any? = model.call(paramList)

    fun toDetailedName() =
        ToStringBuilder(this, BARE_BONE_NOT_NULL_STYLE)
            .append("name", model.name)
            .append("ref", entity.entityRef)
            .append("entityName", entity.name)
            .append("class", entity.javaClass.simpleName)
//            .append("entityId", entity.entityId)
            .append("entityType", entity.getEntityType())
            .toString()

    fun toEntityTypeAndFieldName() =
        "type=${entity::class.qualifiedName} name=${entity.entityRef ?: entity.name} field=$name (${model.function})"

    fun toFriendlyName() = FriendlyFieldName(name = name, entityRefOrName = entity.entityRef ?: entity.name)

    fun getInputDependencies(): Set<InputDependency> =
        inputs
            .asSequence()
            .filter { it.relType != RelType.CREATION }
            .filter { it.hasCurrentResult() }
            .map { inputKey -> inputKey.toInputDependency() }
            .toSet()

    fun checkFieldTypeUsage() {
        if (!(model.fieldType is FieldType.Calculation && model.fieldType.isInput())) {
            val initialResult = initialResult
            if (initialResult != null && initialResult.result.source == FieldResult.SOURCE.I) {
                logger.trace(
                    "InitialResult is specified for ${entity.toHumanReadableName()} -> $name (${model.fieldType})" +
                        " - as : ${initialResult.result.res}",
                )
            }
            val oldFieldResult = oldResult
            if (oldFieldResult != null && oldFieldResult.result.source == FieldResult.SOURCE.I) {
                logger.trace(
                    "oldResult is specified for ${entity.toHumanReadableName()} -> $name (${model.fieldType})" +
                        " - as : ${oldFieldResult.result.res}",
                )
            }
        }
    }

    fun hasCurrentResult(): Boolean = (result ?: oldInput ?: oldResult ?: initialResult) != null

    // In wizard-edit-flow, oldInput is whatever the newest wizard value is
    fun hasOldInput(): Boolean = oldInput != null && oldInput!!.result.source == FieldResult.SOURCE.I

    fun getCurrentResult(): FieldResultStar? = result?.result ?: oldInput?.result ?: oldResult?.result ?: initialResult?.result

    private fun getInitialFieldResult(): VersionedResult? = initialResult

    private fun getCurrentResultSource(): FieldResult.SOURCE? = getCurrentResult()?.source

    fun getCurrentVersionedResult(): VersionedResult? = result ?: oldInput ?: oldResult

    fun getCurrentlyCalculatedResult(): VersionedResult? = result

    fun clearCurrentlyCalculatedResult() {
        result = null
        this.entity.removeFieldResult(this.name)
    }

    fun matchKeys(matchKey: InputKey): Boolean =
        when {
            matchKey.name != model.name -> false
            matchKey.entityRef != null && matchKey.entityRef == entity.entityRefForFieldKey() -> true
            matchKey.entityId != entity.entityId -> false
            else -> true
        }

    fun replaceInitialResult(replacer: (VersionedResult) -> VersionedResult) {
        val initialResultToChange = initialResult
        if (initialResultToChange != null) {
            val changedInitialResult = replacer.invoke(initialResultToChange)
            this.initialResult = changedInitialResult
            this.entity.replaceInitialFieldResult(name) {
                changedInitialResult.result
            }
        }
    }

    fun replace(replacer: (VersionedResult) -> VersionedResult) {
        val result = result
        if (result != null) {
            this.result = replacer.invoke(result)
        }
        replaceInitialResult(replacer)
        val oldInput = oldInput
        if (oldInput != null) {
            this.oldInput = replacer.invoke(oldInput)
        }
        val oldResult = oldResult
        if (oldResult != null) {
            this.oldResult = replacer.invoke(oldResult)
        }
    }

    fun getOldInputOrResult(): VersionedResult? = oldInput ?: oldResult

    fun getOldInputOrResultFallbackInitial(): VersionedResult? = oldInput ?: oldResult ?: initialResult

    fun getSystemValue(): FieldResultStar? =
        oldResult?.result?.let {
            when {
                it.systemValue != null -> it.useSystemValueAsValue()
                it.source == FieldResult.SOURCE.I -> null
                else -> it
            }
        }

    fun hasResultForVersion(
        version: Int,
        nullResultOk: Boolean = true,
    ): Boolean {
        val fieldResult = getResultForVersion(version, onlyCalculated = model.mustBeCalculated())
        return if (nullResultOk) {
            fieldResult != null
        } else {
            fieldResult != null && fieldResult !is Null
        }
    }

    fun hasAlreadyCurrentResultForVersion(
        version: Int,
        nullResultOk: Boolean = true,
    ): Boolean {
        val currentRes = result
        val fieldResult = if (currentRes != null && currentRes.isNewerOrInput(version)) currentRes.result else null
        return if (nullResultOk) {
            fieldResult != null
        } else {
            fieldResult != null && fieldResult !is Null
        }
    }

    fun removeDependencies() {
        inputs.forEach { it.linkedField?.removeSuccessorField(this) }
        openDependencies.forEach { it.linkedField?.removeSuccessorField(this) }
        inputs.clear()
        openDependencies.clear()
    }

    /**
     * return a previously calculated result
     * If called with only calcualted then only return currentResult
     */
    fun getResultForVersion(
        version: Int,
        onlyCalculated: Boolean = false,
    ): FieldResultStar? {
        val currentRes = result
        val oldInp = if (!onlyCalculated) oldInput else null

        return when {
            currentRes != null && currentRes.isNewerOrInput(version) && currentRes.isValid() -> currentRes.result
            oldInp != null && oldInp.isNewerOrInput(version) && oldInp.isValid() -> oldInp.result
            else -> null
        }
    }

    fun updateInitialFieldResult(versionedResult: VersionedResult) {
        val replacer: (VersionedResult) -> VersionedResult = {
            it.copy(result = versionedResult.result)
        }
        // Only during the modal flow
        if (initialResult == null) {
            initialResult = versionedResult
            entity.addInitialFieldResult(model.name) { _ ->
                versionedResult.result
            }
        }
        if (sourceEquals(FieldResult.SOURCE.I)) {
            replaceInitialResult(replacer)
        } else {
            replace(replacer)
        }
    }

    fun resolveEntityProvidedLinks(result: EntityProvided): ManufacturingEntity? =
        if (result.res.isNotBlank()) {
            val id = result.res
            if (result.entity == null) {
                result.entity = entity.findByEntityId(id)
            }
            if (result.entity == null) {
                logger.info(
                    "Unable to locate $id in ${this.entity.treeStructure()}" +
                        " - seems like there is an entity provider but provided entity is not part of stepOrder",
                )
            }
            result.entity
        } else {
            null
        }

    fun resolveOldOrCurrentEntityProvidedResult(): ManufacturingEntity? {
        val result = getCurrentResult()
        return if (result is EntityProvided) {
            resolveEntityProvidedLinks(result)
        } else {
            null
        }
    }

    fun resolveOldEntityProvidedResult(): ManufacturingEntity? {
        val result = getOldInputOrResult()?.result
        return if (result is EntityProvided) {
            resolveEntityProvidedLinks(result)
        } else {
            null
        }
    }

    /**
     *
     * Used only by the actual result Calculation to check if there is an initialResult or a directLink which could be
     * applied for this field without any further calculation call necesarry
     *
     * If Force recalculate is set do not take current existing results, but only intial and directlinks
     *
     */
    fun getExistingResultForVersion(
        version: Int,
        visitedFields: Set<Field> = emptySet(),
        forceRecalculate: Boolean = false,
        calculateSystemValue: Boolean = false,
    ): FieldResultStar? =
        getInitialResult(version, visitedFields)
            ?: (if (!calculateSystemValue) getDirectlyLinkedResult(version) else null)
            ?: (if (!forceRecalculate) getResultForVersion(version) else null)

    private fun getDirectlyLinkedResult(version: Int): FieldResultStar? =
        getDirectlyLink()?.let {
            when {
                it.linkedField != null && it.linkedField.hasResultForVersion(version) ->
                    it.linkedField.getResultForVersion(version)?.withSource(source = FieldResult.SOURCE.C)
                it.extResult?.result != null -> it.extResult.result
                else -> null
            }
        }

    fun getInitialResultAsFallback(): FieldResultStar? {
        return if (initialResult != null &&
            model.inputAsFallback() &&
            initialResult!!.result.source == FieldResult.SOURCE.I
        ) {
            initialResult!!.result.withSource(source = FieldResult.SOURCE.C)
        } else {
            return null
        }
    }

    fun getInitialResult(
        version: Int,
        visitedFields: Set<Field> = emptySet(),
    ): FieldResultStar? {
        val initialResult = initialResult
        return if (
            initialResult != null &&
            model.fieldType is FieldType.Calculation &&
            model.fieldType.isInput() &&
            !model.fieldType.isInputAsFallback() &&
            (isDependenciesFulfilled(version, visitedFields) || initialResult.result.source == FieldResult.SOURCE.I)
        ) {
            initialResult.result.withSource(source = FieldResult.SOURCE.C)
        } else {
            null
        }
    }

    fun setInput(input: VersionedResult) {
        this.oldInput = input
    }

    /**
     * This is called between simulation steps, to reset the field state to pristine.
     */
    fun updateForNextCalculation() {
        val res = result
        if (res != null) {
            this.oldResult = res
            this.result = null
        }
    }

    fun setCalculationResult(result: VersionedResult?) {
        this.result = result
    }

    fun clearCalculationResult(calculationVersion: Int) {
        val result = this.result
        if (result == null) {
            return
        } else {
            if (result.result.source != FieldResult.SOURCE.C || result.newVersion < calculationVersion) {
                return
            }
        }
        error("Field ${toDetailedNameAndValues()} is calculated prematurely!")
    }

    fun haveInputsChanged(existingResult: FieldResultStar): Boolean {
        // Result is old, but field has no input openDependencies -> old result can be taken
        if (inputs.isEmpty() && existingResult.inputs.isEmpty()) {
            return false
        }

        val existingInputDependencies = getInputDependencies()
        val oldInputs = existingResult.inputs
        return (oldInputs != existingInputDependencies)
    }

    fun isDependenciesFulfilled(
        calculationVersion: Int,
        visitedFields: Set<Field> = emptySet(),
    ): Boolean {
        val newVisitedField = visitedFields + this
        val inputsResolved = inputs.all { it.hasResultForVersion(calculationVersion, newVisitedField) }
        val noOpenDeps = noOpenDependencies(calculationVersion, newVisitedField)
        val isDirectLink = getDirectlyLink() != null
        return inputsResolved && noOpenDeps && (isDirectLink || allMandatoryParametersHaveInputs())
    }

    fun isDependenciesFulfilled(calculationVersion: Int): Boolean {
        val inputsResolved = inputs.all { it.hasResultForVersion(calculationVersion, this) }
        val noOpenDeps = noOpenDependencies(calculationVersion, this)
        val isDirectLink = getDirectlyLink() != null

        return inputsResolved && noOpenDeps && (isDirectLink || allMandatoryParametersHaveInputs())
    }

    private fun allMandatoryParametersHaveInputs(): Boolean =
        model.functionParameterMetaInfo
            .asSequence()
            .filter { it.isMandatoryParameter() }
            .none { parameter ->
                inputs.find { it.paramPos == parameter.index && it.relType != RelType.CREATION } == null
            }

    private fun noOpenDependencies(
        calculationVersion: Int,
        newVisitedFields: Set<Field>,
    ) = openDependencies.all {
        it.hasResultForVersion(calculationVersion, newVisitedFields)
    }

    private fun noOpenDependencies(
        calculationVersion: Int,
        newVisitedField: Field,
    ) = openDependencies.all {
        it.hasResultForVersion(calculationVersion, newVisitedField)
    }

    private fun noOpenDependencies(calculationVersion: Int) =
        openDependencies.all {
            it.hasResultForVersion(calculationVersion)
        }

    fun collectCalculatableSuccessors(
        calculationVersion: Int,
        onlyNewFieldsShouldBeRecalculated: Boolean,
    ): List<Field> =
        successors.mapNotNull { successor ->
            if (successor.canBeCalculated(calculationVersion, onlyNewFieldsShouldBeRecalculated) &&
                !successor.hasAlreadyCurrentResultForVersion(
                    calculationVersion,
                )
            ) {
                successor
            } else {
                null
            }
        }

    fun clearAlreadyCreatedEntities() {
        logger.warn("Step creation could not finish for ${toDetailedName()} - clearing the half finished step results")
        val toBeRemoved =
            inputs.mapNotNull {
                val field = it.linkedField
                if (field?.result?.result is EntityProvided) {
                    field.setCalculationResult(null)
                    field.calcKey
                } else {
                    null
                }
            }
        logger.warn("To be reverted entity creation: $toBeRemoved")
        if (toBeRemoved.isNotEmpty()) {
            entity.fieldWithResults.removeIf {
                toBeRemoved.contains(it.name)
            }
        }
    }

    fun hashChanged() = executionContext.currentImplementationVersionHash != executionContext.previouslyCalculatedVersionHash

    /**
     * Return the CurrencyConverterField which responsible for the conversions in the same manufacturing entity.
     * It is only non-null, if the actual field is a money field, and currency conversion is enabled (non-masterdata calculation)
     */
    fun getLinkedCurrencyConverterField(): CurrencyConverterField? =
        openDependencies.firstNotNullOfOrNull {
            if (it.linkedField is CurrencyConverterField) {
                it.linkedField
            } else {
                null
            }
        }

    fun getLinkedUnitConversionField(): UnitConverterField? {
        return openDependencies.firstNotNullOfOrNull {
            if (it.linkedField is UnitConverterField) {
                it.linkedField
            } else {
                null
            }
        }
    }

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(Field::class.java)
    }

    fun isGroupMember() = model.isGroup && entity.getEntityTypeAnnotationOrThrow() != Entities.GROUP

    fun isGroupSource() = model.isGroup && entity.getEntityTypeAnnotationOrThrow() == Entities.GROUP

    fun addCreationDependency(
        childDependency: InputKey,
        creationField: Field,
    ) {
        val childDep =
            if (childDependency.entityId == "all") {
                ChildDependency.ALL_CHILD_DEP
            } else {
                ChildDependency.NON_RECURSIVE_CHILD_DEP
            }
        val creationDependency =
            InputKey(
                paramPos = childDependency.paramPos,
                name = creationField.name,
                entityId = creationField.entity.entityId,
                // creationField.type
                entityType = childDependency.entityType,
                type = childDependency.type,
                relType = RelType.CREATION,
                default = null,
                linkedField = creationField,
                childDependency = childDep,
            )
        addParameter(creationDependency)
    }
}
