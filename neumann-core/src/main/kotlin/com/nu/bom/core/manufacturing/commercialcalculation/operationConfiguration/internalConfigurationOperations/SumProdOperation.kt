package com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations

import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.StandardCalculationValue

data class SumProdOperation(
    override val destinationElementKey: String,
    override val origin: AggregationLevel,
    val addends: List<WeightedCalculationElement>,
    override val standardCalculationValue: StandardCalculationValue?,
    val isDifferentiatedOverhead: Boolean = false,
) : CalculatedOperation, OperationWithStandardCalculationValue
