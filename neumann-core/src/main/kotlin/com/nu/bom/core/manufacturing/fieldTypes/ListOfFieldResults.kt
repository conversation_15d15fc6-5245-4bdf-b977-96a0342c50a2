package com.nu.bom.core.manufacturing.fieldTypes

import com.fasterxml.jackson.module.kotlin.readValue
import com.nu.bom.core.config.JacksonConfig

open class ListOfToolInfos(
    res: List<ToolInfo.ToolInfoData>,
) : FieldResult<List<ToolInfo.ToolInfoData>, ListOfToolInfos>(res) {
    override fun dbValue(): String = JacksonConfig.staticMapper.writeValueAsString(res)

    constructor(value: String) : this(JacksonConfig.staticMapper.readValue<List<ToolInfo.ToolInfoData>>(value))

    @Deprecated("Only added for BCT reasons. Do not use!")
    constructor(value: ListOfMillingToolInfos) : this(value.res.map { it.toToolInfoData() })
}

open class ListOfMillingToolInfos(
    res: List<MillingToolInfo.ToolInfoData>,
) : FieldResult<List<MillingToolInfo.ToolInfoData>, ListOfMillingToolInfos>(res) {
    override fun dbValue(): String = JacksonConfig.staticMapper.writeValueAsString(res)

    constructor(value: String) : this(JacksonConfig.staticMapper.readValue<List<MillingToolInfo.ToolInfoData>>(value))
}
