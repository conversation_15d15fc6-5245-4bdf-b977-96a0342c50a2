package com.nu.bom.core.model.configurations.masterdata

import com.nu.bom.core.model.configurations.ConfigurationValue

data class MasterdataConfigurationV3(
    val effectivityDefinitions: List<EffectivityDefinition>,
    val overheadMethodConfiguration: OverheadMethodConfiguration,
    // It would be nice to further encapsulate these, but then we need would often need two classes per migration :/
    val overheadsConfiguration: LookupConfiguration,
    val interestsConfiguration: LookupConfiguration,
    val exchangeRatesConfiguration: LookupConfiguration,
) : ConfigurationValue {
    fun getLookupConfigurations(): List<LookupConfiguration> =
        listOf(overheadsConfiguration, interestsConfiguration, exchangeRatesConfiguration)
}
