package com.nu.bom.core.manufacturing.fieldTypes

import java.math.BigDecimal

class CycleTimeGroupTimeInfo(res: Jsondata<CycleTimeGroupTimeInfoData>) :
    Json<CycleTimeGroupTimeInfo.CycleTimeGroupTimeInfoData>(res) {
    constructor(value: CycleTimeGroupTimeInfoData) : this(Jsondata(value))

    constructor(value: String) : this(
        0.0.let {
            val typeFactory = mapper.getTypeFactory()
                .constructParametricType(
                    Jsondata::class.java,
                    CycleTimeGroupTimeInfoData::class.java
                )
            val res = mapper.readValue<Jsondata<CycleTimeGroupTimeInfoData>>(
                value,
                typeFactory
            )
            res
        }
    )

    data class CycleTimeGroupTimeInfoData(
        val time_s: BigDecimal,
        val type_selection: String
    )
}
