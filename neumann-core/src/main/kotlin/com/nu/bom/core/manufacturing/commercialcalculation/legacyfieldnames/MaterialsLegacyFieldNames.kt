package com.nu.bom.core.manufacturing.commercialcalculation.legacyfieldnames

enum class ConsumablesLegacyFieldNames(val fieldName: String) {
    COST_PER_PART("costPerPart"),
    CO2_PER_PART("cO2PerPart"),
}

enum class ManualMaterialLegacyFieldNames(val fieldName: String) {
    COST_PER_PART("costPerPart"),
    CO2_PER_PART("cO2PerPart"),
}

enum class ManualMasterDataMaterialLegacyFieldNames(val fieldName: String) {
    COST_PER_PART("costPerPart"),
    CO2_PER_PART("cO2PerPart"),
}

enum class ElectronicComponentLegacyFieldNames(val fieldName: String) {
    COST_PER_PART("costPerPart"),
    CO2_PER_PART("cO2PerPart"),
}

enum class ManualBaseMaterialLegacyFieldNames(val fieldName: String) {
    COST_PER_PART("costPerPart"),
    CO2_PER_PART("cO2PerPart"),
}
