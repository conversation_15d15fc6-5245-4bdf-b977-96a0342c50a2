package com.nu.bom.core.manufacturing.entities.masterdata

import com.nu.bom.core.manufacturing.annotations.BehaviourCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.FreezeImplementation
import com.nu.bom.core.manufacturing.annotations.IgnoreForOverwrittenState
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.KeepOld
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.SourceDataInput
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Date
import com.nu.bom.core.manufacturing.fieldTypes.MasterdataConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.service.behaviour.ConfigBasedDynamicBehaviour
import com.nu.bom.core.manufacturing.service.behaviour.DynamicBehaviour
import com.nu.bom.core.model.configurations.ConfigId
import com.nu.bom.core.service.configurations.ConfigType

@EntityType(Entities.NONE, userCreatable = false, default = true)
open class MasterdataEntity(name: String) : ManufacturingEntity(name) {
    @Input
    open fun headerKey(): Text? = null

    @Input
    @KeepOld
    @SourceDataInput
    open fun headerDisplayName(): Text? = null

    /**
     * The creation of this dynamic behaviour is handled by [com.nu.bom.core.manufacturing.masterdata.masterdataFieldsBuilder.MasterdataFieldsBuilder]
     */
    @BehaviourCreation
    fun createBehaviour(
        @Parent(Entities.MANUFACTURING)
        masterdataConfigurationKey: MasterdataConfigurationKey?,
    ): DynamicBehaviour {
        requireNotNull(masterdataConfigurationKey) { "no configuration for masterdata overheads found" }

        return ConfigBasedDynamicBehaviour(
            this,
            ConfigId(
                ConfigType.Masterdata.group,
                ConfigType.Masterdata.type,
                masterdataConfigurationKey.res.key,
                masterdataConfigurationKey.res.version,
            ),
        )
    }

    @KeepOld
    @IgnoreForOverwrittenState
    @FreezeImplementation
    fun executeLookup(): Bool = Bool(true)

    @Input
    @KeepOld
    @SourceDataInput
    fun mdDetailModificationDate(): Date? = null

    @Input
    @KeepOld
    @SourceDataInput
    fun mdDetailModifier(): Text? = null
}
