package com.nu.bom.core.manufacturing.extension.currenttarget

import com.nu.bom.core.manufacturing.annotations.Children
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.Extends
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.entities.ManufacturingEntityExtension
import com.nu.bom.core.manufacturing.entities.ManufacturingStep
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.extension.CURRENT_TARGET_EXTENSION_PACKAGE
import com.nu.bom.core.manufacturing.fieldTypes.Money

@Extends([ManufacturingStep::class], CURRENT_TARGET_EXTENSION_PACKAGE)
class ManufacturingStepCurrentTargetExtension(name: String) : ManufacturingEntityExtension(name) {

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun bomTargetCosts(
        @Children(Entities.BOM_ENTRY)
        totalTargetCosts: List<Money>?
    ): Money {
        return sum(totalTargetCosts)
    }

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun bomCurrentCosts(
        @Children(Entities.BOM_ENTRY)
        totalCurrentCosts: List<Money>?
    ): Money {
        return sum(totalCurrentCosts)
    }
}
