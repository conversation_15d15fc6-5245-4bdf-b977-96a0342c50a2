package com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.fieldtable

import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.CalculationOperationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.InternalConfigurationOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.SumProdOperation
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.TableOperationExpansionLogic

/**
 * @param entryPoint UniqueOperationIdentifier of the root of the table
 * @param maxDepth Maximum depth of nested operations
 * @param includeTopLevel If set to true, the entry point is included in the operations. Overrules takeOnlyLeaves.
 * @param expandBelowMaxDepth If such a node is at maxDepth, nest it anyway.
 * @param newSection If such a node is visited at a depth > 0, create a new section instead of a nesting.
 * Has a higher precedence than expandBelowMaxDepth
 * @param swapOperation This swaps an operation in the aggregation tree with another operation
 * and continues with the children of the new operation.
 * @param isCollapsed If this is true this row is collapsed in the default view.
 * */
class FieldTableRowStructure(
    val entryPoint: InternalConfigurationOperation,
    val maxDepth: Int,
    val includeTopLevel: Boolean,
    val expandBelowMaxDepth: (operation: InternalConfigurationOperation) -> Boolean = { false },
    val newSection: (operation: InternalConfigurationOperation) -> Boolean = { false },
    val swapOperation: (operation: InternalConfigurationOperation) -> InternalConfigurationOperation = { it },
    val isCollapsed: (operation: SumProdOperation) -> Boolean = { false },
) {
    init {
        require(maxDepth > 0) { "Cannot have not positive maximal depth!" }
    }

    companion object {
        fun withoutActivity(
            entryPoint: InternalConfigurationOperation,
            maxDepth: Int,
            includeTopLevel: Boolean,
            isCollapsed: (operation: SumProdOperation) -> Boolean,
        ): FieldTableRowStructure =
            FieldTableRowStructure(
                entryPoint = entryPoint,
                includeTopLevel = includeTopLevel,
                isCollapsed = isCollapsed,
                maxDepth = maxDepth,
            )

        fun withActivity(
            entryPoint: InternalConfigurationOperation,
            maxDepth: Int,
            includeTopLevel: Boolean,
            config: CalculationOperationConfiguration,
            isCollapsed: (operation: SumProdOperation) -> Boolean,
        ): FieldTableRowStructure =
            FieldTableRowStructure(
                entryPoint = entryPoint,
                includeTopLevel = includeTopLevel,
                isCollapsed = isCollapsed,
                maxDepth = maxDepth,
                swapOperation = { TableOperationExpansionLogic.activityOperationSwapper(config, it) },
            )
    }
}
