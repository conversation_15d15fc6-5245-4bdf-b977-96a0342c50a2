package com.nu.bom.core.model.configurations

import com.fasterxml.jackson.annotation.JsonIgnore
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationKey
import com.nu.bom.core.publicapi.dtos.configurations.ConfigurationIdentifierDto
import com.nu.bom.core.publicapi.dtos.configurations.SemanticVersionDto
import com.nu.bom.core.service.configurations.ConfigType
import com.nu.bom.core.service.configurations.ConfigurationService

data class VersionedConfiguration<T : ConfigurationValue>(
    val id: ConfigId,
    val displayName: String,
    val value: T,
    val isActive: Boolean,
    val isDefault: Boolean,
) {
    fun isTset(): Boolean = id.key.startsWith(ConfigurationService.TSET_KEY_PREFIX)
}

data class ConfigId(
    val group: String,
    val type: String,
    val key: String,
    val version: SemanticVersion,
) {
    constructor(type: ConfigType<*>, id: ConfigurationIdentifier) : this(type.group, type.type, id.key, id.version)
    constructor(type: ConfigType<*>, key: String, version: SemanticVersion) : this(type.group, type.type, key, version)
    constructor(type: ConfigType<*>, key: String, major: Int, minor: Int) : this(type, key, SemanticVersion(major, minor))

    companion object {
        inline fun <K : ConfigurationKey<C>, reified C : ConfigType<*>> fromConfigurationKey(key: K): ConfigId {
            val type = checkNotNull(C::class.objectInstance) { "Config type must be an object declaration" }
            val id = key.res
            return ConfigId(type.group, type.type, id.key, id.version)
        }

        fun fromDto(dto: ConfigurationIdentifierDto): ConfigId = dto.run { ConfigId(group, type, key, SemanticVersion.fromDto(version)) }
    }

    fun toDto(): ConfigurationIdentifierDto = ConfigurationIdentifierDto(group, type, key, version.toDto())
}

data class SemanticVersion(
    val major: Int,
    val minor: Int,
) : Comparable<SemanticVersion> {
    init {
        check(major > 0) { "major must be positive integer" }
        check(minor >= 0) { "minor must be non-negative integer" }
    }

    companion object {
        fun initialVersion(): SemanticVersion = SemanticVersion(1, 0)

        fun fromDto(dto: SemanticVersionDto): SemanticVersion = SemanticVersion(dto.major, dto.minor)
    }

    @JsonIgnore
    fun isInitial(): Boolean = major == 1 && minor == 0

    fun isSequential(next: SemanticVersion): Boolean =
        (next.major == major + 1 && next.minor == 0) ||
            (next.major == major && next.minor == minor + 1)

    override fun compareTo(other: SemanticVersion): Int =
        major
            .compareTo(other.major)
            .takeUnless { it == 0 }
            ?: minor.compareTo(other.minor)

    fun toDto(): SemanticVersionDto = SemanticVersionDto(major, minor)
}
