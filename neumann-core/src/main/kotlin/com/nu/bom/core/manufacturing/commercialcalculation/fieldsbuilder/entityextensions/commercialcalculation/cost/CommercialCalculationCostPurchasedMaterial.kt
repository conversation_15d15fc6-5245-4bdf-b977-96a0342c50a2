package com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost

import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Extends
import com.nu.bom.core.manufacturing.annotations.InterfaceField
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.CommercialCalculationException
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.CustomProcurementTypeHelper
import com.nu.bom.core.manufacturing.defaults.NullProvider
import com.nu.bom.core.manufacturing.entities.BaseMaterial
import com.nu.bom.core.manufacturing.entities.ManualBaseMaterial
import com.nu.bom.core.manufacturing.entities.ManualMasterdataMaterial
import com.nu.bom.core.manufacturing.entities.ManualMaterial
import com.nu.bom.core.manufacturing.entities.ManufacturingEntityExtension
import com.nu.bom.core.manufacturing.entities.RawMaterial
import com.nu.bom.core.manufacturing.entities.RawMaterialManual
import com.nu.bom.core.manufacturing.entities.RoughManufacturing
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.extension.COMMERCIAL_CALCULATION_COST_FIELD_PACKAGE
import com.nu.bom.core.manufacturing.fieldTypes.CO2CalculationOperationsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.CostCalculationOperationsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Text
import reactor.core.publisher.Mono

@Extends(
    [
        BaseMaterial::class,
        ManualBaseMaterial::class,
        RawMaterial::class,
        RawMaterialManual::class,
        ManualMaterial::class,
        ManualMasterdataMaterial::class,
        RoughManufacturing::class,
    ],
    COMMERCIAL_CALCULATION_COST_FIELD_PACKAGE,
)
@EntityType(Entities.NONE)
@Suppress("DEPRECATION")
class CommercialCalculationCostPurchasedMaterial(name: String) : ManufacturingEntityExtension(name) {
    @InterfaceField
    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun purchasePrice(): Money =
        throw CommercialCalculationException.missingRequiredField(
            CommercialCalculationCostPurchasedMaterial::purchasePrice.name,
            CommercialCalculationCostPurchasedMaterial::class.simpleName!!,
        )

    fun allowedCustomProcurementTypes(
        costCalculationOperationKey: CostCalculationOperationsConfigurationKey,
        @Default(NullProvider::class)
        cO2CalculationOperationKey: CO2CalculationOperationsConfigurationKey?,
    ): Mono<Text> =
        CustomProcurementTypeHelper.getAllNotInhouseProcurementTypesAsList(
            costCalculationOperationKey,
            cO2CalculationOperationKey,
            services,
        )
}
