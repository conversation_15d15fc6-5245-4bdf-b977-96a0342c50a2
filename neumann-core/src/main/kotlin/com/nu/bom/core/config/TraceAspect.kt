package com.nu.bom.core.config

import com.google.common.base.Stopwatch
import io.micrometer.tracing.Span
import io.micrometer.tracing.Tracer
import org.aspectj.lang.ProceedingJoinPoint
import org.aspectj.lang.annotation.Around
import org.aspectj.lang.annotation.Aspect
import org.aspectj.lang.annotation.Pointcut
import org.aspectj.lang.reflect.MethodSignature
import org.slf4j.LoggerFactory
import org.springframework.boot.actuate.health.Health
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Component
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.util.concurrent.TimeUnit

@Aspect
@Component
@Profile("trace")
class TraceAspect(private val tracer: Tracer) {
    private val logger = LoggerFactory.getLogger(ControllerLoggerAspect::class.java)

    @Pointcut("execution(public * *(..))")
    fun publicMethodCall() {
        // Intentionally empty, only used for AspectJ configuration
    }

    @Pointcut("execution(private * *(..))")
    fun privateMethodCall() {
        // Intentionally empty, only used for AspectJ configuration
    }

    @Pointcut("within(@com.nu.bom.core.Trace *)")
    fun traceAnnotation() {
        // Intentionally empty, only used for AspectJ configuration
    }

    @Pointcut("within(@org.springframework.stereotype.Repository *)")
    fun repositoryAnnotation() {
        // Intentionally empty, only used for AspectJ configuration
    }

    @Pointcut("execution(* org.springframework.data.mongodb.core.ReactiveMongoTemplate.*(..))")
    fun reactiveMongoTemplateCalls() {
        // Intentionally empty, only used for AspectJ configuration
    }

    @Pointcut("execution(* org.springframework.boot.actuate.health.ReactiveHealthIndicator.getHealth(..))")
    fun healthCheckCalls() {
        // Intentionally empty, only used for AspectJ configuration
    }

    @Pointcut("execution(* org.springframework.boot.actuate.health.AbstractHealthIndicator.doHealthCheck(..))")
    fun nonReactivehealthCheckCalls() {
        // Intentionally empty, only used for AspectJ configuration
    }

    @Around("nonReactivehealthCheckCalls()")
    fun logNonreactiveHealthChecks(pjp: ProceedingJoinPoint): Any? {
        val start = Stopwatch.createStarted()
        val methodName = pjp.signature.name
        try {
            val res = pjp.proceed()
            val stop = start.stop()
            if (stop.elapsed(TimeUnit.MILLISECONDS) > 1000) {
                logger.info("Health Call of ${pjp.target::class.simpleName}:$methodName took $stop")
            }
            return res
        } catch (e: Exception) {
            val stop = start.stop()
            if (stop.elapsed(TimeUnit.MILLISECONDS) > 1000) {
                logger.info("Health Call of ${pjp.target::class.simpleName}:$methodName took $stop with error", e)
            }
            return Health.Builder().down(e)
        }
    }
// AbstractHealthIndicator
    // healthCheckCalls()

    @Around("healthCheckCalls()")
    fun logHealthChecks(pjp: ProceedingJoinPoint): Any? {
        val start = Stopwatch.createStarted()
        val methodName = pjp.signature.name

        return (pjp.proceed() as Mono<*>).doOnError {
            val stop = start.stop()
            if (stop.elapsed(TimeUnit.MILLISECONDS) > 1000) {
                logger.info("Health Call of ${pjp.target::class.simpleName}:$methodName took $stop with error", it)
            }
        }.map {
            val stop = start.stop()
            if (stop.elapsed(TimeUnit.MILLISECONDS) > 1000) {
                logger.info("Health Call of ${pjp.target::class.simpleName}:$methodName took $stop")
            }
            it
        }
    }

    @Around("publicMethodCall() && repositoryAnnotation()")
    fun traceDatabase(pjp: ProceedingJoinPoint): Any? {
        val className = pjp.signature.declaringTypeName
        val methodName = pjp.signature.name
        val span = tracer.spanBuilder().name("$className.$methodName").start()
        span.tag("mongo", true)
        return tracePublicCall(pjp, span)
    }

    @Around("publicMethodCall() && traceAnnotation()")
    fun traceServiceByAnnotation(pjp: ProceedingJoinPoint): Any? {
        val className = pjp.signature.declaringTypeName
        val methodName = pjp.signature.name
        val span = tracer.spanBuilder().name("$className.$methodName").start()
        return tracePublicCall(pjp, span)
    }

    fun tracePublicCall(
        pjp: ProceedingJoinPoint,
        span: Span,
    ): Any? {
        val sign: MethodSignature = pjp.signature as MethodSignature

        // this potentially adds very large method arguments to the tracing and
        // has a performance impact because of .toString operations taking very long
        // therefore disabled for now

        /* try {
            for (index in pjp.args.indices) {
                // we are getting huge string logs in jagger, so we cut them if too big
                if (pjp.args[index] != null)
                    span.tag(sign.parameterNames[index], pjp.args[index].toString().take(1000))
            }
        } catch (e: Exception) {
            logger.error("Failed to iterate through args and adding them to span", e)
        }*/

        try {
            val result = pjp.proceed()
            if (result is Mono<*>) {
                return result.doOnError {
                    span.error(it)
                }.doFinally {
                    span.end()
                }
            } else if (result is Flux<*>) {
                return result.doOnError {
                    span.error(it)
                }.doFinally {
                    span.end()
                }
            } else {
                span.end()
                return result
            }
        } catch (e: RuntimeException) {
            span.error(e)
            span.end()
            throw e
        } catch (e: Error) {
            span.error(e)
            span.end()
            throw e
        }
    }
}
