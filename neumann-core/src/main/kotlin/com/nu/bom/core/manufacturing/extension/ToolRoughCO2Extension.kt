package com.nu.bom.core.manufacturing.extension

import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.Extends
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.StaticDenominatorUnit
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.ToolUiConfigurationIdentifiers
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.keys.ToolUiConfigIdentifierKey
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.values.ToolDetailViewWrapper
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.values.ToolMaintenanceTypeWrapper
import com.nu.bom.core.manufacturing.entities.ManufacturingEntityExtension
import com.nu.bom.core.manufacturing.entities.ToolRough
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.StaticUnitOverride
import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.manufacturing.fieldTypes.EmissionUnits
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.ToolDetailViewConfig
import com.nu.bom.core.manufacturing.fieldTypes.ToolMaintenanceType
import com.nu.bom.core.manufacturing.fieldTypes.specificuiconfigkeys.ToolUiConfigKeys
import java.math.BigDecimal

@Extends([ToolRough::class], CO2_EXTENSION_PACKAGE)
class ToolRoughCO2Extension(name: String) : ManufacturingEntityExtension(name) {
    @Input
    @StaticDenominatorUnit(StaticUnitOverride.HOUR)
    @MandatoryForEntity(index = 2)
    fun cO2PerHour() = Emission(BigDecimal.ZERO, EmissionUnits.KILOGRAM_CO2E)

    fun cO2ToolDetailView(): ToolDetailViewConfig = ToolDetailViewConfig.CO2_ROUGH_TOOL

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_COST_UNIT)
    fun cO2Tool(
        cO2PerHour: Emission,
        manufacturingTimePerPart: Time,
    ): Emission {
        return cO2PerHour / 3600.toBigDecimal() * manufacturingTimePerPart
    }

    // Needs to be defined here since it needs some co2 information
    fun uiConfigKeys(
        toolDetailView: ToolDetailViewConfig,
        cO2ToolDetailView: ToolDetailViewConfig,
    ): ToolUiConfigKeys =
        ToolUiConfigKeys(
            ToolUiConfigurationIdentifiers(
                ToolUiConfigIdentifierKey.TOOL_MAINTENANCE_TYPE_ID to
                    ToolMaintenanceTypeWrapper(
                        ToolMaintenanceType.Selection.DETAILED_TOOL_MAINTENANCE,
                    ),
                ToolUiConfigIdentifierKey.TOOL_DETAIL_VIEW_ID to ToolDetailViewWrapper(toolDetailView.res, cO2ToolDetailView.res),
            ),
        )

    // endregion
}
