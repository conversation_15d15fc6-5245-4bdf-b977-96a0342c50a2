package com.nu.bom.core.model

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer
import com.nu.bom.core.api.dtos.DeltaPartInfo
import com.nu.bom.core.api.dtos.DeltaRow
import com.nu.bom.core.api.dtos.DeltaTotal
import com.nu.bom.core.auditing.CreatedByAccount
import com.nu.bom.core.model.DeltaComparison.Companion.COLLECTION_NAME
import com.querydsl.core.annotations.QueryEntity
import com.tset.core.module.mongodb.infrastructure.dto.RefKeyForAccountMongoDto
import org.bson.types.ObjectId
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import java.time.Instant

@Document(collection = COLLECTION_NAME)
@QueryEntity
data class DeltaComparison(
    var name: String,
    val sideA: DeltaPartInfo?,
    val sideB: DeltaPartInfo?,
    val deltaTotal: DeltaTotal?,
    val deltaResult: List<DeltaRow>,
    var deleted: Boolean = false,
    val refKey: RefKeyForAccountMongoDto?
) {
    @Id
    @JsonSerialize(using = ToStringSerializer::class)
    var _id: ObjectId? = null

    @CreatedByAccount
    @JsonSerialize(using = ToStringSerializer::class)
    var accountId: AccountId? = null

    @CreatedBy
    @JsonIgnoreProperties(ignoreUnknown = true)
    var createdBy: String? = null

    @CreatedDate
    @JsonIgnoreProperties(ignoreUnknown = true)
    var createdDate: Instant? = null

    companion object {
        const val COLLECTION_NAME = "DeltaComparisons"
    }
}

data class DeltaSide(
    val part: DeltaPartInfo,
    val bomNodeId: BomNodeId,
    val branchId: BranchId,
    val snapshotId: SnapshotId
)
