package com.nu.bom.core

import org.springframework.context.annotation.ComponentScan
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.FilterType
import org.springframework.context.annotation.Import
import org.springframework.context.annotation.Profile

@Configuration
@Import(value = [ScanConfig.DefaultScan::class, ScanConfig.DataDeploymentScan::class])
class ScanConfig {

    @Configuration
    @ComponentScan(basePackages = arrayOf("com.nu", "com.tset"))
    class DefaultScan

    @Configuration
    @Profile("data-deployment")
    @ComponentScan(
        basePackages = arrayOf("com.nu", "com.tset"), useDefaultFilters = false,
        excludeFilters = arrayOf(ComponentScan.Filter(type = FilterType.REGEX, pattern = arrayOf(".*[api]")))
    )
    class DataDeploymentScan
}
