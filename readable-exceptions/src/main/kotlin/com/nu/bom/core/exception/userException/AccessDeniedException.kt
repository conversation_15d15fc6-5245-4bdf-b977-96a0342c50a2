package com.nu.bom.core.exception.userException

import com.nu.bom.core.exception.readable.ErrorCode
import com.tset.common.util.UserException
import org.springframework.http.HttpStatus
import org.springframework.web.server.ResponseStatusException

class AccessDeniedException :
    UserException,
    ResponseStatusException(HttpStatus.FORBIDDEN, "You do not have access to this feature or resource.") {
    override val userErrorCode = ErrorCode.ACCESS_DENIED.name
    override val userParameters: List<Any?> = emptyList()
}
