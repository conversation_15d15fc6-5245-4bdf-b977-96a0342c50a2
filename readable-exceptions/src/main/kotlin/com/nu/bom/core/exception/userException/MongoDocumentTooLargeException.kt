package com.nu.bom.core.exception.userException

import com.nu.bom.core.exception.readable.ErrorCode
import com.tset.common.util.UserException
import org.springframework.http.HttpStatus
import org.springframework.web.server.ResponseStatusException

class MongoDocumentTooLargeException(
    val name: String,
    val displayName: String,
    cause: Throwable,
) : ResponseStatusException(
        HttpStatus.UNPROCESSABLE_ENTITY,
        "Could not persist manufacturing entity name=$name, displayName=$displayName, because the mongo db document is too large.",
        cause,
    ),
    UserException {
    override val userErrorCode = ErrorCode.MONGO_DOCUMENT_TOO_LARGE.name
    override val userParameters = listOf(name, displayName)
}
