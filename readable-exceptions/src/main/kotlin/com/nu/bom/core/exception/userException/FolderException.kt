package com.nu.bom.core.exception.userException

import com.nu.bom.core.exception.readable.ErrorCode
import com.tset.common.util.UserException
import org.springframework.http.HttpStatus
import org.springframework.web.server.ResponseStatusException

class FolderNameAlreadyExistsException(
    name: String,
) : ResponseStatusException(HttpStatus.CONFLICT, message(name)),
    UserException {
    companion object {
        private fun message(name: String) = "A folder with the name \"$name\" already exists on that level."
    }

    override val userErrorCode = ErrorCode.FOLDER_NAME_ALREADY_EXISTS.name
    override val userParameters = listOf(name)
}

class FolderNotFoundException(
    private val id: String?,
    private val name: String?,
) : ResponseStatusException(HttpStatus.NOT_FOUND, message(id, name)),
    UserException {
    companion object {
        private fun errorCode() = ErrorCode.FOLDER_NOT_FOUND

        private fun formatSingle(
            id: String?,
            name: String?,
        ) = (id ?: name) ?: ""

        private fun message(
            key: String?,
            name: String?,
        ) = "Could not find folder ${formatSingle(key, name)}."
    }

    override val userErrorCode = errorCode().name

    override val userParameters = listOf(formatSingle(id, name))

    override fun fallbackMessage() = message(id, name)
}
