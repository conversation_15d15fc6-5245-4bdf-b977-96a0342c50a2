package com.nu.bom.core.exception.readable

import com.tset.common.util.ReadableException
import org.springframework.http.HttpStatus
import org.springframework.web.server.ResponseStatusException

class ServiceTimeoutException(private val serviceName: String) :
    ReadableException,
    ResponseStatusException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        message(serviceName)
    ) {
    companion object {
        fun message(serviceName: String) =
            "Request to $serviceName timed out."
    }

    override val userErrorCode: String = ErrorCode.SERVICE_TIMEOUT.name
    override val userParameters = listOf(serviceName)

    override fun fallbackMessage() = message(serviceName)
}
