package com.nu.bom.core.service.uiconfig.specifics

import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.TestUiConfigFeDtoCreator.createEntityTableCost
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.TestUiConfigFeDtoCreator.createFieldTableCO2
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.SpecificUiCardBuilder
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.TestUiConfigurationIdentifiers
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.FieldName
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.TableOption
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.FieldConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.TableConfigFeDto
import com.nu.bom.core.service.uiconfig.UIConfigITHelpers
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service

@Service
@Profile("specificUiConfigServiceTest")
class SpecificTestCardBuilderUsingTestIdsWithoutCards : SpecificUiCardBuilder<TestUiConfigurationIdentifiers> {
    override val identifierClass = TestUiConfigurationIdentifiers::class
    override val cardIdentifier = "SpecificTestConfigCardWithoutCards"

    override fun getViews(identifiers: TestUiConfigurationIdentifiers) = emptySet<ValueType>()

    override fun getCardOptions(identifiers: TestUiConfigurationIdentifiers) = null

    override fun getTablesVariations(identifiers: TestUiConfigurationIdentifiers) =
        mapOf(
            ValueType.COST to listOf("SPECIFIC_DEFAULT_WITHOUT_CARDS"),
            ValueType.CO2 to listOf("SPECIFIC_DEFAULT_WITHOUT_CARDS"),
        )

    override fun getTitles(identifiers: TestUiConfigurationIdentifiers): Map<ValueType, String> =
        mapOf(
            ValueType.COST to "SpecificTestTitleCostWithoutCards",
            ValueType.CO2 to "SpecificTestTitleCO2WithoutCards",
        )

    override fun getKpis(identifiers: TestUiConfigurationIdentifiers): Map<ValueType, FieldName>? =
        mapOf(
            ValueType.COST to "SpecificTestKPICostWithoutCards",
            ValueType.CO2 to "SpecificTestKPICO2WithoutCards",
        )

    override fun getCardFields(identifiers: TestUiConfigurationIdentifiers): Map<ValueType, FieldConfigFeDto>? =
        UIConfigITHelpers.provideCardFieldSectionsByValueType("WithoutCard")

    override fun getTableConfig(
        valueType: ValueType,
        tableOption: TableOption,
        identifiers: TestUiConfigurationIdentifiers,
    ): TableConfigFeDto =
        when (valueType) {
            ValueType.CO2 -> createFieldTableCO2()
            ValueType.COST -> createEntityTableCost()
        }
}
