package com.nu.bom.core.integrationtests

import com.nu.bom.core.api.ManufacturingUpdateController
import com.nu.bom.core.api.dtos.CreateManufacturingResult
import com.nu.bom.core.manufacturing.extension.currenttarget.ManufacturingCurrentTargetExtension
import com.nu.bom.core.manufacturing.fieldTypes.Currency
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYears
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYearsUnit
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.mongoProjectId
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.NbkClient
import com.nu.bom.core.utils.toObjectId
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.reactive.server.WebTestClient
import kotlin.math.abs
import kotlin.math.max

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
// , "masterdata-init", "lookup-init", "template-init", "shape-init", "config-init")
// TODO: Investigate why the default timeout is not enough
@AutoConfigureWebTestClient(timeout = "PT20S")
class MultiCurrencyTestWithBaseCurrencyNotEqualEurIT : NbkClient.With {
    private lateinit var client: WebTestClient

    @Autowired
    override lateinit var nbkClient: NbkClient

    lateinit var accessCheck: AccessCheck

    private var projectId: ProjectId = ProjectId()
    private var branchId: BranchId = BranchId()

    private lateinit var createManufacturingResult: CreateManufacturingResult
    private lateinit var bomNodeId: String

    private var expectedBaseCurrency = "USD"
    private var expectedValueInUsd = 8.0

    @BeforeEach
    fun setup() {
        val (ac, project) = nbkClient.setupWithProject(name = "Test Project with base currency not equal Eur", key = "MCURR")

        accessCheck = ac
        projectId = project.mongoProjectId()

        client = manufacturingCrudTestClient.getConfiguredClient()

        createManufacturingResult =
            wizardBuilder
                .withStandardFields {
                    location = Text("tset.ref.classification.germany")
                    lifeTime = TimeInYears(5.toBigDecimal(), TimeInYearsUnit.YEAR)
                    peakUsableProductionVolumePerYear to QuantityUnit(55_000.toBigDecimal())
                    averageUsableProductionVolumePerYear to QuantityUnit(70000.toBigDecimal())
                    dimension to Dimension(Dimension.Selection.NUMBER)
                    baseCurrency = Currency(expectedBaseCurrency)
                }.execute()
                .getOrThrow()

        bomNodeId = createManufacturingResult.bomNode.id

        branchId =
            manufacturingCrudTestClient
                .checkout(bomNodeId)
                .branch.id
                .toObjectId()!!
    }

    @AfterEach
    fun teardown() {
        nbkClient.cleanup()
    }

    @Test
    fun changeValueInBaseCurrency() {
        val manufacturing = createManufacturingResult.bomNode.manufacturing!!

        val targetCostField =
            manufacturing.fields
                .find {
                    it.name == ManufacturingCurrentTargetExtension::totalTargetCosts.name
                }!!
                .copy(value = expectedValueInUsd)

        val updateFieldRequestForActiveCalculatorSwitch =
            ManufacturingUpdateController.InputParameterApi(
                name = targetCostField.name,
                entityId = manufacturing.id,
                type = targetCostField.type,
                unit = targetCostField.unit,
                value = targetCostField.value,
                currency =
                    com.tset.core.service.domain
                        .Currency(expectedBaseCurrency),
            )
        val updateResult =
            manufacturingCrudTestClient.updateField(
                bomNodeId = bomNodeId,
                branch = branchId.toHexString(),
                field = updateFieldRequestForActiveCalculatorSwitch,
            )
        val updatedManufacturing = updateResult.manufacturing!!

        val modifiedTargetCostField =
            updatedManufacturing.fields.find {
                it.name == ManufacturingCurrentTargetExtension::totalTargetCosts.name
            }

        val valueInUsd =
            modifiedTargetCostField?.currencyInfo?.let {
                it.values[
                    com.tset.core.service.domain.Currency(
                        expectedBaseCurrency,
                    ),
                ]
            }

        Assertions.assertTrue(expectedValueInUsd.approximatelyEqual(valueInUsd?.toDouble() ?: 0.0))
    }
}

fun Double.approximatelyEqual(value: Double): Boolean {
    val diff = abs(this - value)
    return diff < max(1e-8, 1e-8 * abs(value))
}
