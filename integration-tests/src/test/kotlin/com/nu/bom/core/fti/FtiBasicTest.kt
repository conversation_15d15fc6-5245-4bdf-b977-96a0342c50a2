package com.nu.bom.core.fti

import com.nu.bom.core.SpringCalculationServiceTestBase
import com.nu.bom.core.manufacturing.enums.Entities

class FtiBasicTest : SpringCalculationServiceTestBase() {
    companion object {
        val entitiesExpectedAmount: Map<Entities, Int> =
            mapOf(
                Entities.METHOD_PLAN_STAGE to 4,
                Entities.METHOD_PLAN_FEATURE to 10,
                Entities.TOOL_COST_STRUCTURE to 1,
                Entities.TOOL_COST_ROW to 4,
                Entities.MATERIAL to 1,
                Entities.PROCESSED_MATERIAL to 1,
                Entities.ATTACHMENT to 2,
                Entities.MANUFACTURING_STEP to 3,
                Entities.SYSTEM_PARAMETER to 3,
            )
    }
}
