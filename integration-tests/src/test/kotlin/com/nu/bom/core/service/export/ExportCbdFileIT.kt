package com.nu.bom.core.service.export

import com.nu.bom.core.manufacturing.entities.BomEntry
import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.entities.ManualManufacturingStep
import com.nu.bom.core.manufacturing.entities.ManualMaterialV2
import com.nu.bom.core.manufacturing.fieldTypes.Currency
import com.nu.bom.core.manufacturing.fieldTypes.MaterialPriceType
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.AccountTestUtil
import com.nu.bom.core.utils.CalculationBuilder
import com.nu.bom.core.utils.CalculationBuilderService
import com.nu.bom.core.utils.EntityBuilder
import com.nu.bomrads.dto.ProjectCreationDTO
import com.tset.core.module.export.ExportServiceModule
import com.tset.core.service.export.BuiltInExportFormat
import com.tset.core.service.export.ExportCurrency
import org.apache.poi.ss.usermodel.DataFormatter
import org.apache.poi.xssf.usermodel.XSSFWorkbookFactory
import org.assertj.core.api.SoftAssertions
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import reactor.test.StepVerifier
import java.io.ByteArrayInputStream
import com.tset.core.service.domain.Currency as DomainCurrency

@SpringBootTest
@ActiveProfiles("test")
class ExportCbdFileIT {
    @Autowired
    private lateinit var export: ExportServiceModule

    @Autowired
    private lateinit var builderService: CalculationBuilderService

    @Autowired
    private lateinit var accountUtil: AccountTestUtil

    private lateinit var accessCheck: AccessCheck
    private lateinit var projectCreation: ProjectCreationDTO

    @BeforeEach
    fun setup() {
        val setup = accountUtil.setupWithProject(name = "test", key = "T001")
        accessCheck = setup.first

        projectCreation = setup.second
    }

    @AfterEach
    fun teardown() {
        accountUtil.cleanup()
    }

    @Test
    fun `sub calculations should have correct location`() {
        val tree =
            CalculationBuilder
                .create(ManualManufacturing::class)
                .withChild(
                    EntityBuilder
                        .create(ManualManufacturingStep::class)
                        .withName("Step")
                        .withSub(
                            CalculationBuilder
                                .create(ManualManufacturing::class)
                                .withInput(
                                    mapOf("location" to Text("SUB")),
                                ).withBomEntryClass(BomEntry::class),
                        ),
                ).withDefaultProject(projectCreation)

        val (snapshot, _, accessCheck) = builderService.build(tree, accessCheck).block()!!

        val result =
            export.export(
                accessCheck,
                snapshot.projectId(),
                snapshot.bomNodeId(),
                snapshot.branchId().toHexString(),
                BuiltInExportFormat.TSET_XLSX.formatRequirement,
                ExportCurrency(DomainCurrency("GBP"), numberFormat = "#,##0.00, £"),
            )

        StepVerifier
            .create(result)
            .assertNext {
                val workBook = XSSFWorkbookFactory().create(ByteArrayInputStream(it.file))
                val summarySheet = workBook.getSheetAt(0)
                val subPartSheet = workBook.getSheetAt(2)

                val parentLocation = DataFormatter().formatCellValue(summarySheet.getRow(26).getCell(3))
                val subLocation = DataFormatter().formatCellValue(subPartSheet.getRow(4).getCell(7))
                val softAssert = SoftAssertions()

                softAssert.assertThat(parentLocation).contains("Austria")
                softAssert.assertThat(subLocation).contains("SUB")

                softAssert.assertAll()
            }.verifyComplete()
    }

    @Test
    fun `export should add no calc data fields to export and have them in the dto`() {
        // cube calc
        val tree =
            CalculationBuilder
                .create(ManualManufacturing::class)
                .withTitle("MANUAL")
                .withChild(
                    EntityBuilder
                        .create(ManualMaterialV2::class)
                        .withName("Material")
                        .withInput(
                            mapOf(
                                "baseCurrency" to Currency("USD"),
                                "materialBasePrice" to Money(1.0),
                                "materialPriceType" to MaterialPriceType(MaterialPriceType.Selection.SIMPLE_PRICE),
                                "scrapWeightPerPart" to QuantityUnit(0.0),
                                "netWeightPerPart" to QuantityUnit(1.0),
                            ),
                        ),
                ).withDefaultProject(projectCreation)

        val (snapshot, _, accessCheck) = builderService.build(tree, accessCheck).block()!!

        val result =
            export.export(
                accessCheck,
                snapshot.projectId(),
                snapshot.bomNodeId(),
                snapshot.branchId().toHexString(),
                BuiltInExportFormat.TSET_XLSX.formatRequirement,
                ExportCurrency(DomainCurrency("GBP"), numberFormat = "#,##0.00, £"),
            )

        StepVerifier
            .create(result)
            .assertNext {
                val sheet = XSSFWorkbookFactory().create(ByteArrayInputStream(it.file)).getSheetAt(3)

                val cell3 = DataFormatter().formatCellValue(sheet.getRow(4).getCell(3))
                val cell7 = DataFormatter().formatCellValue(sheet.getRow(4).getCell(7))
                val cell10 = DataFormatter().formatCellValue(sheet.getRow(4).getCell(10))
                val softAssert = SoftAssertions()

                softAssert.assertThat(cell3).isEqualTo("0.71 GBP / pcs")
                softAssert.assertThat(cell7).isEqualTo("0.71 GBP / kg")
                softAssert.assertThat(cell10).isEqualTo("0.71 GBP / kg")

                softAssert.assertAll()
            }.verifyComplete()
    }
}
