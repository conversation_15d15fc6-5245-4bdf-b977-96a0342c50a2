package com.nu.bom.core.integrationtests.copypaste

import com.nu.bom.core.api.COST_PER_PART
import com.nu.bom.core.config.defaultMasterDataMocksAnswer
import com.nu.bom.core.exception.readable.ErrorCode
import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.entities.ManualManufacturingStep
import com.nu.bom.core.manufacturing.entities.ManualMaterialV2
import com.nu.bom.core.manufacturing.entities.ManufacturingStepLine
import com.nu.bom.core.manufacturing.fieldTypes.ExchangeRatesField
import com.nu.bom.core.manufacturing.fieldTypes.ObjectIdField
import com.nu.bom.core.service.CalculationEntityCopyService
import com.nu.bom.core.service.MasterDataService
import com.nu.bom.core.service.configurations.MasterdataTsetConfigurationService
import com.nu.bom.core.service.dto.EntitySelector
import com.nu.bom.core.service.masterdata.MdCurrencyService
import com.nu.bom.core.service.masterdata.MdDetailCrudService
import com.nu.bom.core.service.masterdata.MdHeaderCrudService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.user.withAccessCheck
import com.nu.bom.core.utils.AccountTestUtil
import com.nu.bom.core.utils.CalculationBuilder
import com.nu.bom.core.utils.CalculationBuilder.Companion.calculationBuilder
import com.nu.bom.core.utils.CalculationBuilderService
import com.nu.bom.core.utils.CompositeCalculationBuilder
import com.nu.bom.core.utils.EntityBuilder.Companion.entityBuilder
import com.nu.bom.core.utils.LoadTestEntityClasses
import com.nu.bom.core.utils.assertErrorThat
import com.nu.bom.core.utils.assertSameStructure
import com.nu.bom.core.utils.getDefaultInputs
import com.nu.bom.core.utils.then
import com.nu.bom.tests.docker.MasterdataUpdateTest
import com.nu.bomrads.dto.ProjectCreationDTO
import com.nu.masterdata.dto.v1.basicdata.CurrencyDto
import com.nu.masterdata.dto.v1.detail.CurrencyMeasurementDto
import com.nu.masterdata.dto.v1.detail.DetailDto
import com.nu.masterdata.dto.v1.detail.NumericDetailValueDto
import com.nu.masterdata.dto.v1.header.HeaderDto
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import com.nu.masterdata.dto.v1.schema.CurrencyTypeDto
import com.nu.masterdata.dto.v1.schema.NumericDetailValueSchemaDto
import com.nu.masterdata.dto.v1.schema.NumericFieldSchemaDto
import com.nu.masterdata.dto.v1.schema.UnitOfMeasurementTypeDto
import com.tset.common.util.UserException
import com.tset.core.service.domain.Currency
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.doAnswer
import org.mockito.kotlin.any
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import reactor.core.publisher.Mono
import reactor.test.StepVerifier

@SpringBootTest
@MasterdataUpdateTest
@LoadTestEntityClasses(
    packages = [
        "com.nu.bom.core.manufacturing.testentities.entityprovider",
        "com.nu.bom.core.manufacturing.testentities.multicurrency",
    ],
)
class CopyPasteWithCurrenciesIT {
    @Autowired
    private lateinit var builderService: CalculationBuilderService

    @Autowired
    private lateinit var copyPasteService: CalculationEntityCopyService

    @Autowired
    private lateinit var accountUtil: AccountTestUtil

    @Autowired
    private lateinit var masterDataService: MasterDataService

    @Autowired
    private lateinit var currencyService: MdCurrencyService

    @Autowired
    private lateinit var headerService: MdHeaderCrudService

    @Autowired
    private lateinit var detailCrudService: MdDetailCrudService

    private lateinit var accessCheck: AccessCheck
    private lateinit var projectCreationDTO: ProjectCreationDTO

    companion object {
        private val DEFAULT_COPY_RULES =
            CalculationEntityCopyService.CopyRules(
                operation = CalculationEntityCopyService.Operation.COPY_PASTE,
            )
    }

    @BeforeEach
    fun setup() {
        val (ac, project) = accountUtil.setupWithProject(name = "CopyPasteIT", key = "CPIT")
        accessCheck = ac
        projectCreationDTO = project
        masterDataService.apply {
            doAnswer(::defaultMasterDataMocksAnswer)
                .`when`(this)
                .getLatestMasterDataByCompositeKey(accessCheck = any(), selector = any(), mode = any())
        }
    }

    @AfterEach
    fun teardown() {
        accountUtil.cleanup()
    }

    @Test
    fun `copy MANUAL material with currencies`() {
        // 1 - manual calc with base currencies
        val (baseCurrenciesRootSnapshot, _, accessCheck) =
            buildTree(
                calculationBuilder {
                    withClass(ManualManufacturing::class)
                    withDefaultProject(projectCreationDTO)
                    withInput(getDefaultInputs(ManualManufacturing::class))
                    withTitle("Base Currencies Root")
                    withChild(
                        entityBuilder {
                            withClass(ManualManufacturingStep::class)
                            withName("Base Currencies Step1")
                            withInput(
                                getDefaultInputs(ManualManufacturingStep::class) +
                                    (ManufacturingStepLine::groupId.name to ObjectIdField()),
                            )
                            withChild(
                                entityBuilder {
                                    withClass(ManualMaterialV2::class)
                                    withName("Base Currencies Material1")
                                    withInput(getDefaultInputs(ManualMaterialV2::class))
                                },
                            )
                        },
                    )
                },
            )

        // 2 - add new currency and exchange rate
        addEth().block()

        // 3 - manual calc with extended currencies
        val (extendedCurrenciesRootSnapshot, _, _) =
            buildTree(
                calculationBuilder {
                    withClass(ManualManufacturing::class)
                    withDefaultProject(projectCreationDTO)
                    withInput(getDefaultInputs(ManualManufacturing::class))
                    withTitle("Extended Currencies Root")
                    withChild(
                        entityBuilder {
                            withClass(ManualManufacturingStep::class)
                            withName("Extended Currencies Step1")
                            withInput(getDefaultInputs(ManualManufacturingStep::class))
                            withChild(
                                entityBuilder {
                                    withClass(ManualMaterialV2::class)
                                    withName("Extended Currencies Material1")
                                    withInput(
                                        getDefaultInputs(ManualMaterialV2::class) +
                                            mapOf(
                                                "baseCurrency" to
                                                    com.nu.bom.core.manufacturing.fieldTypes.Currency(
                                                        "ETH",
                                                    ),
                                            ),
                                    )
                                },
                            )
                        },
                    )
                },
            )

        val source =
            EntitySelector(
                extendedCurrenciesRootSnapshot.projectId(),
                bomNodeId = extendedCurrenciesRootSnapshot.bomNodeId(),
                branchId = extendedCurrenciesRootSnapshot.branchId(),
                entityId = extendedCurrenciesRootSnapshot.manufacturing?._id!!,
            )

        val target =
            EntitySelector(
                baseCurrenciesRootSnapshot.projectId(),
                bomNodeId = baseCurrenciesRootSnapshot.bomNodeId(),
                branchId = baseCurrenciesRootSnapshot.branchId(),
                entityId = baseCurrenciesRootSnapshot.manufacturing?._id!!,
            )

        StepVerifier
            .create(
                copyPasteService.copyEntity(accessCheck, source, target, DEFAULT_COPY_RULES),
            ).assertNext { result ->
                assertThat(result.result.manufacturing!!.getFieldResult(COST_PER_PART)).isNotNull

                val updatedRoot = result.newRootSnapshot

                // same title, part, refkey for sub
                assertThat(updatedRoot.title).isEqualTo("Base Currencies Root")
                assertSamePart(updatedRoot, baseCurrenciesRootSnapshot)

                // same structure on sub
                assertThat(updatedRoot.manufacturing).isNotNull
                assertSameStructure(
                    baseCurrenciesRootSnapshot.manufacturing!!,
                    updatedRoot.manufacturing!!,
                    ignoreBomEntries = true,
                )

                assertThat(updatedRoot.subNodes).singleElement().satisfies({ copiedSubRel ->
                    assertThat(copiedSubRel.snapshot).isNotNull
                    val copiedSub = copiedSubRel.snapshot!!

                    // same title, part, but new refkey for copied sub
                    assertThat(copiedSub.title).isEqualTo(extendedCurrenciesRootSnapshot.title)
                    assertSamePart(copiedSub, extendedCurrenciesRootSnapshot)

                    // extended exchange rate from parent
                    val copiedSubExchangeRates = copiedSub.manufacturing!!.getExchangeRates()!!
                    assertThat(copiedSubExchangeRates.children.size)
                        .isGreaterThan(
                            updatedRoot.manufacturing!!
                                .getExchangeRates()!!
                                .children.size,
                        )
                    assertThat(copiedSubExchangeRates.children.size)
                        .isGreaterThan(
                            updatedRoot.manufacturing!!
                                .getExchangeRates()!!
                                .children.size,
                        )
                    assertThat(
                        (copiedSubExchangeRates.getFieldResult("exchangeRates")!! as ExchangeRatesField).res.keys,
                    ).contains(Currency("ETH"))

                    // same structure on copied sub
                    assertThat(copiedSub.manufacturing).isNotNull
                    assertSameStructure(
                        extendedCurrenciesRootSnapshot.manufacturing!!,
                        copiedSub.manufacturing!!,
                        ignoreBomEntries = true,
                        ignoreExchangeRates = true,
                    )
                })
            }.verifyComplete()
    }

    @Test
    fun `copy MANUAL with subcurrencies`() {
        // 1 - manual calc with base currencies
        val (baseCurrenciesRootSnapshot, _, accessCheck) =
            buildTree(
                calculationBuilder {
                    withClass(ManualManufacturing::class)
                    withDefaultProject(projectCreationDTO)
                    withInput(getDefaultInputs(ManualManufacturing::class))
                    withTitle("Base Currencies Root")
                },
            )

        // 2 - add new currency and exchange rate
        addEth().block()

        // 3 - manual calc with extended currencies
        val (extendedCurrenciesRootSnapshot, _, _) =
            buildTree(
                calculationBuilder {
                    withClass(ManualManufacturing::class)
                    withDefaultProject(projectCreationDTO)
                    withInput(
                        getDefaultInputs(ManualManufacturing::class) +
                            mapOf(
                                "baseCurrency" to
                                    com.nu.bom.core.manufacturing.fieldTypes.Currency(
                                        "ETH",
                                    ),
                            ),
                    )
                    withTitle("Extended Currencies Root")
                },
            )

        val source =
            EntitySelector(
                extendedCurrenciesRootSnapshot.projectId(),
                bomNodeId = extendedCurrenciesRootSnapshot.bomNodeId(),
                branchId = extendedCurrenciesRootSnapshot.branchId(),
                entityId = extendedCurrenciesRootSnapshot.manufacturing?._id!!,
            )

        val target =
            EntitySelector(
                baseCurrenciesRootSnapshot.projectId(),
                bomNodeId = baseCurrenciesRootSnapshot.bomNodeId(),
                branchId = baseCurrenciesRootSnapshot.branchId(),
                entityId = baseCurrenciesRootSnapshot.manufacturing?._id!!,
            )

        assertErrorThat(
            copyPasteService.copyEntity(accessCheck, source, target, DEFAULT_COPY_RULES),
        ).satisfies({ e ->
            e is UserException && e.userErrorCode == ErrorCode.COPY_TARGET_IS_MISSING_CURRENCY.name
        })
    }

    private fun buildTree(builder: CompositeCalculationBuilder.() -> Unit): CalculationBuilderService.Result =
        builderService
            .build(
                CompositeCalculationBuilder
                    .buildTree(builder),
                accessCheck,
            ).withAccessCheck(accessCheck)
            .block()!!

    private fun buildTree(calculationBuilder: CalculationBuilder): CalculationBuilderService.Result =
        builderService.build(calculationBuilder, accessCheck).block()!!

    private fun addEth(): Mono<Void> {
        // This is a little ugly but hugely beneficial to the overall runtime of this test class.
        // Since this test class is configured to create a single test instance per class rather than per method,
        // some state is shared between tests. This reduces isolation but greatly improves performance.
        // The test is about 2x faster thanks to this optimization, so I think it justifies this tradeoff in this case.
        // if (addedEth) return Mono.empty()

        val ccy = CurrencyDto(SimpleKeyDto("ETH"), "Ethereum", "ETH")
        val headerType = SimpleKeyDto(MasterdataTsetConfigurationService.EXCHANGE_RATE_HEADER_TYPE_KEY)
        val ethRate = 1.0 / 4000
        return currencyService
            .createCurrency(accessCheck, ccy)
            .then {
                headerService.createHeader(
                    accessCheck,
                    HeaderDto(
                        ccy.key,
                        ccy.name,
                        headerType,
                        true,
                        NumericDetailValueSchemaDto(
                            NumericFieldSchemaDto(
                                UnitOfMeasurementTypeDto(
                                    CurrencyTypeDto(ccy.key),
                                    CurrencyTypeDto(SimpleKeyDto("EUR")),
                                ),
                            ),
                        ),
                    ),
                )
            }.then {
                val value =
                    NumericDetailValueDto(
                        ethRate,
                        CurrencyMeasurementDto(ccy.key),
                        CurrencyMeasurementDto(SimpleKeyDto("EUR")),
                    )
                detailCrudService.postDetailEntries(
                    accessCheck,
                    headerType,
                    listOf(DetailDto(emptyMap(), ccy.key, value, true)),
                )
            }.then()
        // .then {
        // addedEth = true
        // Mono.empty()
        // }
    }
}
