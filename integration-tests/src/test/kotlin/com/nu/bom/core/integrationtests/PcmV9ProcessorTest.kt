package com.nu.bom.core.integrationtests

import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.BaseMaterial
import com.nu.bom.core.manufacturing.entities.BomEntry
import com.nu.bom.core.manufacturing.entities.Consumable
import com.nu.bom.core.manufacturing.entities.ElectronicComponent
import com.nu.bom.core.manufacturing.entities.ExternalManufacturingStep
import com.nu.bom.core.manufacturing.entities.Labor
import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.entities.ManualManufacturingStep
import com.nu.bom.core.manufacturing.entities.ManualMaterial
import com.nu.bom.core.manufacturing.entities.ManualMaterialV2
import com.nu.bom.core.manufacturing.entities.Material
import com.nu.bom.core.manufacturing.entities.RoughManufacturing
import com.nu.bom.core.manufacturing.entities.RoughManufacturingStep
import com.nu.bom.core.manufacturing.entities.Setup
import com.nu.bom.core.manufacturing.entities.SpecialDirectCost
import com.nu.bom.core.manufacturing.entities.ToolRough
import com.nu.bom.core.manufacturing.entities.masterdata.material.MaterialConsumerExtension
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeUnit
import com.nu.bom.core.manufacturing.fieldTypes.MaterialPriceType
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.Quantity
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import com.nu.bom.core.manufacturing.testentities.TestMachine
import com.nu.bom.core.manufacturing.testentities.TestManufacturing
import com.nu.bom.core.manufacturing.testentities.TestManufacturingStep
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.toBomNodeId
import com.nu.bom.core.service.bomrads.AllChildren
import com.nu.bom.core.technologies.manufacturings.chill.material.ChillCastedMaterial
import com.nu.bom.core.technologies.steps.roughhd.ManufacturingStepRoughHd
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.AccountTestUtil
import com.nu.bom.core.utils.CalculationBuilder
import com.nu.bom.core.utils.CalculationBuilderService
import com.nu.bom.core.utils.EntityBuilder
import com.nu.bom.core.utils.LoadTestEntityClasses
import com.nu.bom.core.utils.getDefaultInputs
import com.nu.bomrads.dto.ProjectCreationDTO
import com.tset.core.module.export.BomNodeEnvironment
import com.tset.core.module.export.ExportServiceModule
import com.tset.core.module.export.dto.PcmV9ConfigDto
import com.tset.core.module.export.sheet.FullNumericFieldResult
import com.tset.core.module.export.sheet.TextFieldResult
import com.tset.core.module.export.sheet.processor.PcmV9Export
import com.tset.core.module.export.sheet.processor.PcmV9Processor
import com.tset.core.module.export.sheet.processor.PcmV9Row
import com.tset.core.quantities.types.toBigDecimal
import com.tset.core.service.domain.Currency
import com.tset.core.service.export.BuiltInExportFormat
import com.tset.core.service.export.ExportCurrency
import com.tset.core.service.export.ExportFormat
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import java.math.BigDecimal
import java.math.RoundingMode
import kotlin.reflect.KClass

@SpringBootTest
@ActiveProfiles("test")
@LoadTestEntityClasses(classes = [TestManufacturing::class, TestManufacturingStep::class, TestMachine::class])
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class PcmV9ProcessorTest {
    @Autowired
    private lateinit var builderService: CalculationBuilderService

    @Autowired
    private lateinit var pcmV9Processor: PcmV9Processor

    @Autowired
    private lateinit var exportServiceModule: ExportServiceModule

    @Autowired
    private lateinit var accountUtil: AccountTestUtil

    private lateinit var accessCheck: AccessCheck

    private lateinit var projectCreationDTO: ProjectCreationDTO

    @BeforeAll
    fun setup() {
        val result =
            accountUtil.setupWithProject(
                name = "PcmV9ProcessorTest Project",
                key = "pcmv9",
            )
        accessCheck = result.first
        projectCreationDTO = result.second
    }

    @AfterAll
    fun teardown() {
        accountUtil.cleanup()
    }

    @Test
    fun `export Simple Calculation`() {
        val tree = create(ManualManufacturing::class)

        val export = getPcmV9Export(tree)

        Assertions.assertEquals(1, export.rows.size)
        verifyManufacturing(export.rows[0])
        verifyValue(export.rows[0], "overheadMethod", "Build to print (automotive)")
    }

    @Test
    fun `export Simple Rough Calculation`() {
        val tree = create(RoughManufacturing::class)

        val export = getPcmV9Export(tree)

        Assertions.assertEquals(1, export.rows.size)
        verifyManufacturing(export.rows[0])
        verifyValue(export.rows[0], "pricePerUnit", BigDecimal.ZERO)
    }

    @Test
    fun `export Calc with Sub Calculation`() {
        val tree =
            create(ManualManufacturing::class)
                .withChild(
                    EntityBuilder
                        .create(ManualManufacturingStep::class)
                        .withName("Step")
                        .withSub(
                            CalculationBuilder
                                .create(ManualManufacturing::class)
                                .withBomEntryClass(BomEntry::class),
                        ),
                )

        val export = getPcmV9Export(tree)

        Assertions.assertEquals(3, export.rows.size)
        verifyManufacturing(export.rows[0])
        verifyManufacturing(export.rows[1])
        verifyDetailedStep(export.rows[2], 1)
    }

    @Test
    fun `export Calc with step`() {
        val tree = createManualManufacturingWithManualManufacturingStep(childOfStep = null)

        val export = getPcmV9Export(tree)

        Assertions.assertEquals(2, export.rows.size)
        verifyManufacturing(export.rows[0])
        verifyDetailedStep(export.rows[1], 1)
    }

    @Test
    fun `export Calc with material`() {
        val material =
            EntityBuilder
                .create(ManualMaterialV2::class)
                .withName("Material1")

        val tree = createManualManufacturingWithManualManufacturingStep(childOfStep = material)

        val export = getPcmV9Export(tree)

        Assertions.assertEquals(3, export.rows.size)
        verifyManufacturing(export.rows[0])
        verifyMaterial(export.rows[1])
        verifyDetailedStep(export.rows[2], 1)
    }

    @Test
    fun `export Calc with Tool`() {
        val tool =
            EntityBuilder
                .create(ToolRough::class)
                .withName("Tool")

        val tree = createManualManufacturingWithManualManufacturingStep(childOfStep = tool)

        val export = getPcmV9Export(tree)

        Assertions.assertEquals(3, export.rows.size)
        verifyManufacturing(export.rows[0])
        verifyDetailedStep(export.rows[1], 1)
        verifyTool(export.rows[2])
    }

    @Test
    fun `export Calc with Consumable`() {
        val consumable =
            EntityBuilder
                .create(Consumable::class)
                .withName("Consumable")
                .withInput(
                    mapOf(
                        MaterialConsumerExtension::materialBasePrice.name to Money(1.0),
                        MaterialConsumerExtension::headerKey.name to Text("Water"),
                    ),
                )

        val tree = createManualManufacturingWithManualManufacturingStep(childOfStep = consumable)

        val export = getPcmV9Export(tree)

        Assertions.assertEquals(3, export.rows.size)
        verifyManufacturing(export.rows[0])
        verifyConsumable(export.rows[1])
        verifyValue(export.rows[1], BaseMaterial::pricePerUnit.name, BigDecimal.ONE)
        verifyDetailedStep(export.rows[2], 1)
    }

    @Test
    fun `export Calc with machine`() {
        val machine =
            EntityBuilder
                .create(TestMachine::class)
                .withName("Machine1")

        val tree = createManualManufacturingWithManualManufacturingStep(childOfStep = machine)

        val export = getPcmV9Export(tree)

        Assertions.assertEquals(3, export.rows.size)
        verifyManufacturing(export.rows[0])
        verifyDetailedStep(export.rows[1], 1)
        verifyMachine(export.rows[2], 1)
    }

    @Test
    fun `export Calc with Setup`() {
        val setup =
            EntityBuilder
                .create(Setup::class)
                .withName("Setup")

        val tree = createManualManufacturingWithManualManufacturingStep(childOfStep = setup)

        val export = getPcmV9Export(tree)

        Assertions.assertEquals(3, export.rows.size)
        verifyManufacturing(export.rows[0])
        verifyDetailedStep(export.rows[1], 1)
        verifySetup(export.rows[2], 1)
    }

    @Test
    fun `export Calc with Labor`() {
        val labor =
            EntityBuilder
                .create(Labor::class)
                .withName("Labor")

        val tree = createManualManufacturingWithManualManufacturingStep(childOfStep = labor)

        val export = getPcmV9Export(tree)

        Assertions.assertEquals(3, export.rows.size)
        verifyManufacturing(export.rows[0])
        verifyDetailedStep(export.rows[1], 1)
        verifyLabor(export.rows[2], 1)
    }

    private fun createTreeWithOrder(indices: List<Int>): CalculationBuilder {
        val labor =
            EntityBuilder
                .create(Labor::class)
                .withName("Labor")
        val material =
            EntityBuilder
                .create(Material::class)
                .withName("Material")
                .withInput(mapOf("sortIndex" to Quantity(indices[0].toBigInteger())))
        val consumable =
            EntityBuilder
                .create(Consumable::class)
                .withName("Consumable")
                .withInput(mapOf("sortIndex" to Quantity(indices[1].toBigInteger())))

        val subMaterial =
            EntityBuilder
                .create(Material::class)
                .withName("Material")
                .withInput(mapOf("sortIndex" to Quantity(indices[3].toBigInteger())))
        val subConsumable =
            EntityBuilder
                .create(Consumable::class)
                .withName("Consumable")
                .withInput(mapOf("sortIndex" to Quantity(indices[4].toBigInteger())))
        val sub =
            CalculationBuilder
                .create(ManualManufacturing::class)
                .withBomEntryInput(mapOf("sortIndex" to Quantity(indices[2].toBigInteger())))
                .withChildren(listOf(subMaterial, subConsumable))
                .withBomEntryClass(BomEntry::class)

        return create(ManualManufacturing::class)
            .withChildren(
                listOf(
                    consumable,
                    EntityBuilder
                        .create(ManualManufacturingStep::class)
                        .withName("Step2")
                        .withChildren(listOf(material, labor))
                        .withSub(sub),
                ),
            )
    }

    @Test
    fun `export Calc with Sort Order 1`() {
        val tree = createTreeWithOrder(listOf(1, 2, 3, 5, 4))
        val export = getPcmV9Export(tree)

        Assertions.assertEquals(8, export.rows.size)
        verifyManufacturing(export.rows[0])
        verifyMaterial(export.rows[1])
        verifyConsumable(export.rows[2])

        // -> sub
        verifyManufacturing(export.rows[3])
        verifyConsumable(export.rows[4])
        verifyMaterial(export.rows[5])
        // <- sub

        verifyDetailedStep(export.rows[6], 1)
        verifyLabor(export.rows[7], 1)
    }

    @Test
    fun `export Calc with Sort Order 2`() {
        // 3 | material
        // 2 | consumable
        // 1 | sub
        // 5 | sub -> material
        // 4 | sub -> consumable
        val tree = createTreeWithOrder(listOf(3, 2, 1, 5, 4))
        val export = getPcmV9Export(tree)

        Assertions.assertEquals(8, export.rows.size)
        verifyManufacturing(export.rows[0])

        // -> sub
        verifyManufacturing(export.rows[1])
        verifyConsumable(export.rows[2])
        verifyMaterial(export.rows[3])
        // <- sub

        verifyConsumable(export.rows[4])
        verifyMaterial(export.rows[5])

        verifyDetailedStep(export.rows[6], 1)
        verifyLabor(export.rows[7], 1)
    }

    @Test
    fun `export Calc with nested steps and Sub`() {
        val tree =
            create(ManualManufacturing::class)
                .withChild(
                    EntityBuilder
                        .create(ManualManufacturingStep::class)
                        .withName("Step2")
                        .withChild(
                            EntityBuilder
                                .create(ManualManufacturingStep::class)
                                .withName("Step1")
                                .withChild(
                                    EntityBuilder
                                        .create(ManualMaterialV2::class)
                                        .withName("Material1"),
                                ).withSub(
                                    CalculationBuilder
                                        .create(ManualManufacturing::class)
                                        .withBomEntryClass(BomEntry::class),
                                ),
                        ),
                )

        val export = getPcmV9Export(tree)

        Assertions.assertEquals(5, export.rows.size)
        verifyManufacturing(export.rows[0])
        verifyManufacturing(export.rows[1])
        verifyMaterial(export.rows[2])
        verifyDetailedStep(export.rows[3], 1)
        verifyDetailedStep(export.rows[4], 2)
    }

    @Test
    fun `export Calc with SpecialDirectCost`() {
        val tree =
            create(ManualManufacturing::class)
                .withChild(
                    EntityBuilder
                        .create(SpecialDirectCost::class)
                        .withName("Special direct cost")
                        .withInput(getDefaultInputs(SpecialDirectCost::class)),
                )

        val export = getPcmV9Export(tree)

        Assertions.assertEquals(2, export.rows.size)
        verifyManufacturing(export.rows[0])
        verifySpecialDirectCost(export.rows[1])
    }

    @Test
    fun `COST-30086 - export Calc with step, unit columns exist`() {
        val cycleTime = CycleTime(420.0, CycleTimeUnit.SECOND)
        val tree =
            create(ManualManufacturing::class)
                .withChild(
                    EntityBuilder
                        .create(ManualManufacturingStep::class)
                        .withName("Step1")
                        .withOverwrites(mapOf("cycleTime" to cycleTime)),
                )

        val (_, environment, export) = getPcmV9ExportExtended(tree)

        // this is necessary to introduce the unit columns #basicallyfunctional
        export.writeAsFormat(ExportFormat.XLSX, environment)

        Assertions.assertEquals(2, export.rows.size)
        verifyManufacturing(export.rows[0])
        verifyDetailedStep(export.rows[1], 1)

        val cycleTimeColumnName = "cycleTimeManufacturingStepOrCycleTimeStep"
        val cycleTimeUnitColumnName = "__UNIT__$cycleTimeColumnName"

        val stepRow = export.rows[1]
        verifyValue(stepRow, cycleTimeColumnName, cycleTime.res)
        verifyValue(stepRow, cycleTimeUnitColumnName, "sec")
    }

    @Test
    fun `COST-30603, COST-30608 - different (object) type for different manufacturing step types`() {
        val tree =
            create(ManualManufacturing::class)
                .withChildren(
                    listOf(
                        EntityBuilder
                            .create(RoughManufacturingStep::class)
                            .withName("Rough step"),
                        EntityBuilder
                            .create(ExternalManufacturingStep::class)
                            .withName("External step"),
                        EntityBuilder
                            .create(ManualManufacturingStep::class)
                            .withName("Internal step"),
                        EntityBuilder
                            .create(TestManufacturingStep::class)
                            .withName("Generic, not specifically handled step"),
                    ),
                )

        val export = getPcmV9Export(tree)

        Assertions.assertEquals(5, export.rows.size)
        verifyManufacturing(export.rows[0])

        verifyTypeAndSequence(export.rows[1], 1, "Rough manufacturing step")
        verifyValue(export.rows[1], "type", "Rough manufacturing step")
        verifyRoughStep(export.rows[1], 1) // ensure consistency to other tests

        verifyTypeAndSequence(export.rows[2], 2, "Detailed external manufacturing step")
        verifyValue(export.rows[2], "type", "Detailed external manufacturing step")

        verifyTypeAndSequence(export.rows[3], 3, "Detailed internal manufacturing step")
        verifyValue(export.rows[3], "type", "Detailed internal manufacturing step")
        verifyDetailedStep(export.rows[3], 3) // ensure consistency to other tests

        verifyTypeAndSequence(export.rows[4], 4, "Detailed internal manufacturing step")
        verifyValue(export.rows[4], "type", "Detailed internal manufacturing step")
        verifyDetailedStep(export.rows[4], 4) // ensure consistency to other tests
    }

    @Test
    fun `COST-30603 - extending entities are processed correctly as their base`() {
        val roughStepName = "Rough step"
        val childOfRoughStepName = "Child of rough step"
        val tree =
            create(ManualManufacturing::class)
                .withChildren(
                    listOf(
                        EntityBuilder
                            .create(RoughManufacturingStep::class)
                            .withName(roughStepName)
                            .withChildren(
                                listOf(
                                    EntityBuilder
                                        .create(ManufacturingStepRoughHd::class)
                                        .withName(childOfRoughStepName),
                                ),
                            ),
                    ),
                )

        val (rootSnapshot, _, export) = getPcmV9ExportExtended(tree)

        val childOfRoughHd =
            rootSnapshot.manufacturing
                ?.children
                ?.find {
                    it.name == roughStepName
                }?.children
                ?.find { it.name == childOfRoughStepName }
        Assertions.assertNotNull(childOfRoughHd)
        childOfRoughHd!!

        Assertions.assertEquals(childOfRoughHd::class.simpleName, "ManufacturingStepRoughHd")
        Assertions.assertTrue(childOfRoughHd.extensionSet().contains(RoughManufacturingStep::class.java))

        Assertions.assertEquals(3, export.rows.size)
        verifyManufacturing(export.rows[0])
        verifyRoughStep(export.rows[1], 1)
        verifyRoughStep(export.rows[2], 2)
    }

    @Test
    fun `COST-30581 - bct for ManualMaterialV1`() {
        val quantity = Weight(BigDecimal("69"), WeightUnits.TON)

        @Suppress("DEPRECATION")
        val tree =
            create(ManualManufacturing::class)
                .withChildren(
                    listOf(
                        EntityBuilder
                            .create(ManualMaterialV2::class)
                            .withName("Modern material")
                            .withInput(
                                mapOf(
                                    "netWeightPerPart" to quantity,
                                    "scrapWeightPerPart" to QuantityUnit(0.0),
                                ),
                            ),
                        EntityBuilder
                            .create(ManualMaterial::class)
                            .withName("Deprecated material")
                            .withInput(mapOf("inputQuantity" to quantity)),
                    ),
                )

        val export = getPcmV9Export(tree)

        Assertions.assertEquals(3, export.rows.size)
        verifyManufacturing(export.rows[0])
        verifyMaterial(export.rows[1])
        verifyMaterial(export.rows[2])

        val quantityColumnName = "quantity"

        verifyValue(export.rows[1], quantityColumnName, quantity.res)
        verifyValue(export.rows[2], quantityColumnName, quantity.res)
    }

    @Test
    fun `COST-30669 - procurement "purchased" is added for certain entities`() {
        val tree =
            create(ManualManufacturing::class)
                .withChildren(
                    listOf(
                        EntityBuilder
                            .create(ManualMaterialV2::class)
                            .withName("Material"),
                        EntityBuilder
                            .create(Consumable::class)
                            .withName("Consumable"),
                        EntityBuilder
                            .create(ElectronicComponent::class)
                            .withName("Electronic component"),
                        EntityBuilder
                            .create(ManualManufacturingStep::class)
                            .withName("Manufacturing Step")
                            .withSubs(
                                listOf(
                                    CalculationBuilder
                                        .create(ManualManufacturing::class)
                                        .withTitle("Sub calculation")
                                        .withBomEntryClass(BomEntry::class),
                                ),
                            ),
                    ),
                )

        val export = getPcmV9Export(tree)

        val numRows = 6
        Assertions.assertEquals(numRows, export.rows.size)
        verifyManufacturing(export.rows[0])
        verifyManufacturing(export.rows[1])
        verifyMaterial(export.rows[2])
        verifyConsumable(export.rows[3])
        verifyCPart(export.rows[4])
        verifyDetailedStep(export.rows[5], 1)

        (1 until numRows).forEach { _ ->
            // translated value
            verifyValue(export.rows[1], "customProcurementTypeName", "Purchased")
        }
    }

    @Test
    fun `COST-34033 - netSalesPrices is cost per part times quantity`() {
        val purchasePriceFieldName = "pricePerUnit"
        val netSalesPriceFieldName = "netSalesPrice"
        val costPerPartFieldName = "costPerPart"

        val purchasePriceConsumable = Money("9001")
        val purchasePriceMaterial = Money("9002")
        val purchasePriceElC0 = Money("9003")
        val costPerPartRoughEstimate = Money("9004")

        val tree =
            create(ManualManufacturing::class)
                .withChildren(
                    listOf(
                        EntityBuilder
                            .create(ManualMaterialV2::class)
                            .withName("Material")
                            .withInput(
                                mapOf(
                                    "materialBasePrice" to purchasePriceMaterial,
                                    "materialPriceType" to MaterialPriceType(MaterialPriceType.Selection.SIMPLE_PRICE),
                                    "netWeightPerPart" to QuantityUnit(4.0.toBigDecimal()),
                                    "scrapWeightPerPart" to QuantityUnit(0.0),
                                ),
                            ),
                        EntityBuilder
                            .create(Consumable::class)
                            .withName("Consumable")
                            .withInput(
                                mapOf(
                                    "materialBasePrice" to purchasePriceConsumable,
                                    Consumable::quantity.name to QuantityUnit(5.0.toBigDecimal()),
                                    MaterialConsumerExtension::headerKey.name to Text("Water"),
                                ),
                            ),
                        EntityBuilder
                            .create(ElectronicComponent::class)
                            .withName("Electronic component")
                            .withInput(
                                mapOf(
                                    purchasePriceFieldName to purchasePriceElC0,
                                    "quantity" to QuantityUnit(6.0.toBigDecimal()),
                                ),
                            ),
                        EntityBuilder
                            .create(ManualManufacturingStep::class)
                            .withName("Manufacturing Step")
                            .withSubs(
                                listOf(
                                    CalculationBuilder
                                        .create(ManualManufacturing::class)
                                        .withBomEntryClass(BomEntry::class)
                                        .withBomEntryInput(mapOf("quantity" to Pieces(28.0)))
                                        .withChild(
                                            EntityBuilder
                                                .create(ManualManufacturingStep::class)
                                                .withName("Manufacturing Step")
                                                .withSubs(
                                                    listOf(
                                                        CalculationBuilder
                                                            .create(RoughManufacturing::class)
                                                            .withInput(mapOf(costPerPartFieldName to costPerPartRoughEstimate))
                                                            .withBomEntryClass(BomEntry::class)
                                                            .withBomEntryInput(mapOf("quantity" to Pieces(7.0))),
                                                    ),
                                                ),
                                        ),
                                ),
                            ),
                    ),
                )

        val export = getPcmV9Export(tree)

        Assertions.assertEquals(8, export.rows.size)
        verifyManufacturing(export.rows[0])

        verifyMaterial(export.rows[4])
        verifyValue(export.rows[4], purchasePriceFieldName, purchasePriceMaterial.res)
        verifyValue(
            export.rows[4],
            netSalesPriceFieldName,
            (purchasePriceMaterial.res.toBigDecimal() * 4.0.toBigDecimal()),
        )

        verifyConsumable(export.rows[5])
        verifyValue(export.rows[5], purchasePriceFieldName, purchasePriceConsumable.res)
        verifyValue(
            export.rows[5],
            netSalesPriceFieldName,
            (purchasePriceConsumable.res.toBigDecimal() * 5.0.toBigDecimal()),
        )

        verifyCPart(export.rows[6])
        verifyValue(export.rows[6], purchasePriceFieldName, purchasePriceElC0.res)
        verifyValue(export.rows[6], netSalesPriceFieldName, (purchasePriceElC0.res.toBigDecimal() * 6.0.toBigDecimal()))

        verifyManufacturing(export.rows[2])
        verifyValue(export.rows[2], purchasePriceFieldName, costPerPartRoughEstimate.res)
        verifyValue(
            export.rows[2],
            netSalesPriceFieldName,
            (costPerPartRoughEstimate.res.toBigDecimal() * 7.0.toBigDecimal()),
        )

        val costPerPart = export.rows[1][costPerPartFieldName]
        val quantity = export.rows[1]["quantity"]
        val netSalesPrice = export.rows[1][netSalesPriceFieldName]

        verifyManufacturing(export.rows[1])
        Assertions.assertEquals(
            (netSalesPrice as FullNumericFieldResult).res.toBigDecimal().setScale(5, RoundingMode.HALF_UP),
            ((costPerPart as FullNumericFieldResult).res * (quantity as FullNumericFieldResult).res)
                .toBigDecimal()
                .setScale(5, RoundingMode.HALF_UP),
        )
    }

    @Test
    fun `COST-36075 step number present when processed material in tree`() {
        val tree =
            create(ManualManufacturing::class)
                .withChild(
                    EntityBuilder
                        .create(ChillCastedMaterial::class)
                        .withName("Processed Material")
                        .withChild(
                            EntityBuilder
                                .create(ManualManufacturingStep::class)
                                .withName("Step 3")
                                .withChildren(
                                    listOf(
                                        EntityBuilder
                                            .create(ManualMaterialV2::class)
                                            .withName("Material"),
                                        EntityBuilder
                                            .create(ManualManufacturingStep::class)
                                            .withName("Step 2")
                                            .withChild(
                                                EntityBuilder
                                                    .create(ManualManufacturingStep::class)
                                                    .withName("Step 1"),
                                            ),
                                    ),
                                ),
                        ),
                )

        val export = getPcmV9Export(tree)
        verifyTypeAndSequence(export.rows[1], 3, "Material")
        verifyDetailedStep(export.rows[2], 1)
        verifyDetailedStep(export.rows[3], 2)
        verifyDetailedStep(export.rows[4], 3)
    }

    @Test
    fun `COST-40525, only cycle time step children of a cycle time step are removed`() {
        val tree =
            create(ManualManufacturing::class)
                .withChild(
                    EntityBuilder
                        .create(ManualManufacturingStep::class)
                        .withName("Step")
                        .withSub(
                            CalculationBuilder
                                .create(ManualManufacturing::class)
                                .withChild(
                                    EntityBuilder
                                        .create(Entities.CYCLETIME_STEP)
                                        .withChild(
                                            EntityBuilder
                                                .create(Entities.CYCLETIME_STEP)
                                                .withName("cycle_cycle_time"),
                                        ).withChild(
                                            EntityBuilder
                                                .create(Entities.TOOL)
                                                .withName("tool"),
                                        ).withName("cycle_time"),
                                ).withBomEntryClass(BomEntry::class),
                        ),
                )
        val export = getPcmV9Export(tree)
        Assertions.assertEquals(5, export.rows.size)
        verifyManufacturing(export.rows[0])
        verifyManufacturing(export.rows[1])
        verifyValue(export.rows[2], "designation", "cycle_time")
        verifyValue(export.rows[3], "designation", "tool")
        verifyDetailedStep(export.rows[4], 1)
    }

    @Test
    fun `COST-67489 - export should contain accumulatedMaterialScrapRate`() {
        val material =
            EntityBuilder
                .create(ManualMaterialV2::class)
                .withName("Material1")
                .withInput(
                    mapOf(
                        "accumulatedMaterialScrapRate" to Rate(0.33),
                    ),
                )

        val tree =
            createManualManufacturingWithManualManufacturingStep(childOfStep = material)
                .withChild(
                    EntityBuilder
                        .create(ElectronicComponent::class)
                        .withName("Electronic component")
                        .withInput(
                            mapOf(
                                "accumulatedMaterialScrapRate" to Rate(0.44),
                            ),
                        ),
                )

        val export = getPcmV9Export(tree)
        Assertions.assertEquals(4, export.rows.size)
        verifyManufacturing(export.rows[0])
        verifyCPart(export.rows[1])
        verifyValue(export.rows[1], "accumulatedMaterialScrapRate", 0.44.toBigDecimal())
        verifyMaterial(export.rows[2])
        verifyValue(export.rows[2], "accumulatedMaterialScrapRate", 0.33.toBigDecimal())
    }

    private fun verifyManufacturing(row: PcmV9Row) {
        verifyValue(row, "lineIndicator", "Part")
    }

    private fun verifyMaterial(row: PcmV9Row) {
        verifyValue(row, "lineIndicator", "Material")
    }

    private fun verifyConsumable(row: PcmV9Row) {
        verifyValue(row, "lineIndicator", "Consumable")
    }

    private fun verifyTool(row: PcmV9Row) {
        verifyValue(row, "lineIndicator", "Tool")
    }

    private fun verifyCPart(row: PcmV9Row) {
        verifyValue(row, "lineIndicator", "Electronic component")
    }

    private fun verifyRoughStep(
        row: PcmV9Row,
        sequenceNumber: Int,
    ) {
        verifyTypeAndSequence(row, sequenceNumber, "Rough manufacturing step")
    }

    private fun verifyDetailedStep(
        row: PcmV9Row,
        sequenceNumber: Int,
    ) {
        verifyTypeAndSequence(row, sequenceNumber, "Detailed internal manufacturing step")
    }

    private fun verifySetup(
        row: PcmV9Row,
        sequenceNumber: Int,
    ) {
        verifyTypeAndSequence(row, sequenceNumber, "Setup")
    }

    private fun verifyMachine(
        row: PcmV9Row,
        sequenceNumber: Int,
    ) {
        verifyTypeAndSequence(row, sequenceNumber, "Machine")
    }

    private fun verifyLabor(
        row: PcmV9Row,
        sequenceNumber: Int,
    ) {
        verifyTypeAndSequence(row, sequenceNumber, "Labor")
    }

    private fun verifySpecialDirectCost(row: PcmV9Row) {
        verifyValue(row, "lineIndicator", "Investment")
    }

    private fun verifyTypeAndSequence(
        row: PcmV9Row,
        sequenceNumber: Int,
        type: String,
    ) {
        verifyValue(row, "lineIndicator", type)
        verifyValue(row, "sequenceNumber", BigDecimal(sequenceNumber))
    }

    private fun verifyValue(
        row: PcmV9Row,
        fieldName: String,
        expectedValue: BigDecimal,
    ) {
        Assertions.assertTrue(expectedValue.compareTo((row[fieldName] as FullNumericFieldResult).res) == 0) {
            "expected $expectedValue <> ${(row[fieldName] as FullNumericFieldResult).res}"
        }
    }

    private fun verifyValue(
        row: PcmV9Row,
        fieldName: String,
        expectedValue: String,
    ) {
        Assertions.assertEquals(TextFieldResult(expectedValue), row[fieldName])
    }

    private fun getPcmV9ExportExtended(tree: CalculationBuilder): Triple<BomNodeSnapshot, BomNodeEnvironment, PcmV9Export> {
        val (rootSnapshot, _, accessCheck) = builderService.build(tree, accessCheck).block()!!
        val environment = getEnvironment(accessCheck, rootSnapshot, exportServiceModule)

        val export = pcmV9Processor.process(getConfig(), environment).block()

        Assertions.assertNotNull(export)
        return Triple(rootSnapshot, environment, export!!)
    }

    private fun getPcmV9Export(tree: CalculationBuilder): PcmV9Export = getPcmV9ExportExtended(tree).third

    private fun getConfig(): PcmV9ConfigDto = exportServiceModule.getPcmV9ConfigDto()

    private fun createManualManufacturingWithManualManufacturingStep(childOfStep: EntityBuilder?): CalculationBuilder =
        create(ManualManufacturing::class)
            .withChild(
                EntityBuilder
                    .create(ManualManufacturingStep::class)
                    .withName("Step1")
                    .withChildren(
                        childOfStep?.let { listOf(it) } ?: emptyList(),
                    ),
            )

    private fun create(clazz: KClass<out BaseManufacturing>): CalculationBuilder =
        CalculationBuilder.create(clazz).withDefaultProject(projectCreationDTO)
}

fun getEnvironment(
    accessCheck: AccessCheck,
    rootSnapshot: BomNodeSnapshot,
    exportServiceModule: ExportServiceModule,
): BomNodeEnvironment =
    exportServiceModule
        .getBomNodeSnapshotForExport(
            accessCheck,
            rootSnapshot.bomNodeId(),
            rootSnapshot.branchIdStr(),
            ExportCurrency(Currency.EUR, ""),
            BuiltInExportFormat.PPC_V9_XLSX.formatRequirement.exportMode,
            AllChildren(rootSnapshot.bomNodeId().toBomNodeId()),
        ).block()!!
