package com.nu.bom.core.service.uiconfig.toplevel

import com.nu.bom.core.manufacturing.commercialcalculation.configurationMapping.TestProcurementTypeConfigurationCreator
import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationRole
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.fieldnamebuilders.ValueFieldNameBuilder
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalConfigurationOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalDifferentiatedOverheadOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalDiscountOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalInterestOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalSimpleOverheadOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalSumProdOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.CurrentCO2OperationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.CurrentCostOperationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCO2ConfigurationFactory
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCO2eCalculationElementType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCostCalculationElementType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCostConfigurationFactory
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.TopLevelUiConfigService
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.TableOptionEnum
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.toplevel.OverheadCardBuilder
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.CardIdentifier
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.TableName
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.ValueTypeFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.CardConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableColumnDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.TableConfigFeDto
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class OverheadCardBuilderIT(
    @Autowired
    override val builderService: TopLevelUiConfigService,
) : TopLevelUIConfigCardBuilderITBase(builderService, OverheadCardBuilder::class) {
    private val tableOptions = listOf(TableOptionEnum.PRODUCTION.displayableOptionName)

    @Test
    fun testOverheadCardBuilderForDefaultAsPurchased() {
        createUiConfigAndVerify(
            verifyCards = ::verifyCardsForDefault,
            verifyTables = ::verifyTablesForDefaultAsPurchased,
            testInputForCardBuilder = TestInputForCardBuilder.getDefaultWithManufacturingType(ManufacturingType.PURCHASE),
            maybeFileNameForComparison = "ExpectedDefaultOverheadUIConfigPurchased",
        )
    }

    @Test
    fun testOverheadCardBuilderForDefaultAsInhouse() {
        createUiConfigAndVerify(
            verifyCards = ::verifyCardsForDefault,
            verifyTables = ::verifyTablesForDefaultAsInhouse,
            testInputForCardBuilder = TestInputForCardBuilder.getDefaultWithManufacturingType(ManufacturingType.INHOUSE),
            maybeFileNameForComparison = "ExpectedDefaultOverheadUIConfigInhouse",
        )
    }

    @Test
    fun testOverheadCardBuilderForOverheadOnManufacturedMaterialAsPurchased() {
        createUiConfigAndVerify(
            verifyCards = ::verifyCardsForOverheadOnManufacturedMaterial,
            verifyTables = ::verifyTablesForOverheadOnManufacturedMaterial,
            testInputForCardBuilder = createInputForCardBuilder(ManufacturingType.PURCHASE),
            maybeFileNameForComparison = "ExpectedManufacturedMaterialOverheadUIConfigPurchased",
        )
    }

    @Test
    fun testOverheadCardBuilderForOverheadOnManufacturedMaterialAsInhouse() {
        createUiConfigAndVerify(
            verifyCards = ::verifyCardsForOverheadOnManufacturedMaterial,
            verifyTables = ::verifyTablesForOverheadOnManufacturedMaterial,
            testInputForCardBuilder = createInputForCardBuilder(ManufacturingType.INHOUSE),
            maybeFileNameForComparison = "ExpectedManufacturedMaterialOverheadUIConfigInhouse",
        )
    }

    // region default

    // region purchase part

    fun verifyTablesForDefaultAsPurchased(tableConfigs: Map<TableName, TableConfigFeDto>) {
        verifyTablesForDefault(
            tableConfigs,
            5,
            mapOf(
                ValueTypeFeDto.COST to 45,
                ValueTypeFeDto.CO2 to 43,
            ),
            mapOf(
                ValueTypeFeDto.COST to 7,
                ValueTypeFeDto.CO2 to 6,
            ),
        )
    }

    // endregion

    // region inhouse

    fun verifyTablesForDefaultAsInhouse(tableConfigs: Map<TableName, TableConfigFeDto>) {
        verifyTablesForDefault(
            tableConfigs,
            0,
            mapOf(
                ValueTypeFeDto.COST to 0,
                ValueTypeFeDto.CO2 to 0,
            ),
            mapOf(
                ValueTypeFeDto.COST to 7,
                ValueTypeFeDto.CO2 to 6,
            ),
        )
    }

    // endregion

    // region common

    fun verifyCardsForDefault(cards: Map<CardIdentifier, CardConfigFeDto>) {
        val expectedCardConfig =
            CardConfigFeDto(
                setOf(ValueTypeFeDto.COST, ValueTypeFeDto.CO2),
                mapOf(
                    ValueTypeFeDto.COST to TsetCostCalculationElementType.INDIRECT_COSTS_AFTER_PRODUCTION.long,
                    ValueTypeFeDto.CO2 to TsetCO2eCalculationElementType.INDIRECT_CO2E_AFTER_PRODUCTION.long,
                ),
                mapOf(
                    ValueTypeFeDto.COST to
                        ValueFieldNameBuilder(
                            ValueType.COST,
                            AggregationLevel.SOLD_MATERIAL,
                            AggregationRole.TOTAL,
                            TsetCostCalculationElementType.INDIRECT_COSTS_AFTER_PRODUCTION.fieldName,
                        ).fieldName,
                    ValueTypeFeDto.CO2 to
                        ValueFieldNameBuilder(
                            ValueType.CO2,
                            AggregationLevel.SOLD_MATERIAL,
                            AggregationRole.TOTAL,
                            TsetCO2eCalculationElementType.INDIRECT_CO2E_AFTER_PRODUCTION.fieldName,
                        ).fieldName,
                ),
                null,
                mapOf(
                    ValueTypeFeDto.COST to tableOptions,
                    ValueTypeFeDto.CO2 to tableOptions,
                ),
            )

        assertThat(cards).containsKey("overhead")
        assertThat(cards["overhead"]).isEqualTo(expectedCardConfig)
        // Assertions.assertEquals(cards.keys, setOf("overhead"))
        // Assertions.assertEquals(expectedCardConfig, cards.values.single())
    }

    fun verifyTablesForDefault(
        tableConfigs: Map<TableName, TableConfigFeDto>,
        expectedNumberOfTopRows: Int,
        expectedNumberOfRows: Map<ValueTypeFeDto, Int>,
        expectedNumberOfColumns: Map<ValueTypeFeDto, Int>,
    ) {
        val expectedTableKeys =
            tableOptions
                .flatMap { tableOption ->
                    ValueTypeFeDto.entries.map { valueTypeFeDto ->
                        TableName("overhead", valueTypeFeDto, tableOption)
                    }
                }.toSet()
        assertThat(tableConfigs.keys).containsAll(expectedTableKeys)
        // Assertions.assertEquals(expectedTableKeys, tableConfigs.keys)

        val tableConfigsUnderTest = tableConfigs.filterKeys { it in expectedTableKeys }
        tableConfigsUnderTest.forEach { (tableName, tableConfig) ->
            Assertions.assertTrue(tableConfig is FieldTableConfigFeDto)
            Assertions.assertEquals(expectedNumberOfTopRows, tableConfig.rows.size)

            Assertions.assertEquals(expectedNumberOfRows[tableName.valueTypeFeDto]!!, tableConfig.rowDefinitions.size)
            Assertions.assertEquals(expectedNumberOfColumns[tableName.valueTypeFeDto]!!, tableConfig.columns.size)

            tableConfig.columns.forEach { column ->
                Assertions.assertTrue(column is FieldTableColumnDefinitionFeDto)
                Assertions.assertTrue((column as FieldTableColumnDefinitionFeDto).options?.displayDesignation != null)
            }
        }
    }

    // endregion

    // endregion

    // region manufactured material overheads

    private fun createInputForCardBuilder(procurementType: ManufacturingType): TestInputForCardBuilder =
        TestInputForCardBuilder(
            CurrentCostOperationConfiguration(
                replaceSoldMaterialOrigin(TsetCostConfigurationFactory.getDefaultCostOperations()),
                TestProcurementTypeConfigurationCreator.createBusinessLogicLevel(),
            ),
            CurrentCO2OperationConfiguration(
                replaceSoldMaterialOrigin(TsetCO2ConfigurationFactory.getDefaultCO2Operations()),
                TestProcurementTypeConfigurationCreator.createBusinessLogicLevel(),
            ),
            TsetCostConfigurationFactory.getDefaultCostElementTsetConfiguration(),
            TsetCO2ConfigurationFactory.getDefaultCO2ElementTsetConfiguration(),
            procurementType,
        )

    private fun replaceSoldMaterialOrigin(ops: List<ExternalConfigurationOperation>): List<ExternalConfigurationOperation> =
        ops.map {
            if (it.origin == AggregationLevel.SOLD_MATERIAL) {
                when (it) {
                    is ExternalDiscountOperation -> it.copy(origin = AggregationLevel.MANUFACTURED_MATERIAL)
                    is ExternalDifferentiatedOverheadOperation -> it.copy(origin = AggregationLevel.MANUFACTURED_MATERIAL)
                    is ExternalInterestOperation -> it.copy(origin = AggregationLevel.MANUFACTURED_MATERIAL)
                    is ExternalSimpleOverheadOperation -> it.copy(origin = AggregationLevel.MANUFACTURED_MATERIAL)
                    is ExternalSumProdOperation -> it.copy(origin = AggregationLevel.MANUFACTURED_MATERIAL)
                    else -> it
                }
            } else {
                it
            }
        }

    // region verification

    fun verifyCardsForOverheadOnManufacturedMaterial(cards: Map<CardIdentifier, CardConfigFeDto>) {
        val expectedCardConfig =
            CardConfigFeDto(
                setOf(ValueTypeFeDto.COST, ValueTypeFeDto.CO2),
                mapOf(
                    ValueTypeFeDto.COST to TsetCostCalculationElementType.INDIRECT_COSTS_AFTER_PRODUCTION.long,
                    ValueTypeFeDto.CO2 to TsetCO2eCalculationElementType.INDIRECT_CO2E_AFTER_PRODUCTION.long,
                ),
                mapOf(
                    ValueTypeFeDto.COST to
                        ValueFieldNameBuilder(
                            ValueType.COST,
                            AggregationLevel.MANUFACTURED_MATERIAL,
                            AggregationRole.TOTAL,
                            TsetCostCalculationElementType.INDIRECT_COSTS_AFTER_PRODUCTION.fieldName,
                        ).fieldName,
                    ValueTypeFeDto.CO2 to
                        ValueFieldNameBuilder(
                            ValueType.CO2,
                            AggregationLevel.MANUFACTURED_MATERIAL,
                            AggregationRole.TOTAL,
                            TsetCO2eCalculationElementType.INDIRECT_CO2E_AFTER_PRODUCTION.fieldName,
                        ).fieldName,
                ),
                null,
                mapOf(
                    ValueTypeFeDto.COST to tableOptions,
                    ValueTypeFeDto.CO2 to tableOptions,
                ),
            )

        assertThat(cards).containsKey("overhead")
        assertThat(cards["overhead"]).isEqualTo(expectedCardConfig)
        // Assertions.assertEquals(cards.keys, setOf("overhead"))
        // Assertions.assertEquals(expectedCardConfig, cards.values.single())
    }

    fun verifyTablesForOverheadOnManufacturedMaterial(tableConfigs: Map<TableName, TableConfigFeDto>) {
        val expectedTableKeys =
            tableOptions
                .flatMap { tableOption ->
                    ValueTypeFeDto.entries.map { valueTypeFeDto ->
                        TableName("overhead", valueTypeFeDto, tableOption)
                    }
                }.toSet()
        assertThat(tableConfigs.keys).containsAll(expectedTableKeys)
        // Assertions.assertEquals(expectedTableKeys, tableConfigs.keys)

        val tableConfigsUnderTest = tableConfigs.filterKeys { it in expectedTableKeys }
        tableConfigsUnderTest.forEach { (tableName, tableConfig) ->
            Assertions.assertTrue(tableConfig is FieldTableConfigFeDto)
            Assertions.assertEquals(5, tableConfig.rows.size)

            when (tableName.valueTypeFeDto) {
                ValueTypeFeDto.COST -> {
                    Assertions.assertEquals(45, tableConfig.rowDefinitions.size)
                    Assertions.assertEquals(7, tableConfig.columns.size)
                }

                ValueTypeFeDto.CO2 -> {
                    Assertions.assertEquals(43, tableConfig.rowDefinitions.size)
                    Assertions.assertEquals(6, tableConfig.columns.size)
                }
            }

            tableConfig.columns.forEach { column ->
                Assertions.assertTrue(column is FieldTableColumnDefinitionFeDto)
                Assertions.assertTrue((column as FieldTableColumnDefinitionFeDto).options?.displayDesignation != null)
            }
        }
    }

    // endregion

    // endregion
}
