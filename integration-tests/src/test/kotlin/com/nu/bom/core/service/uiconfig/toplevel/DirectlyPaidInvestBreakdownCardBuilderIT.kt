package com.nu.bom.core.service.uiconfig.toplevel

import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.investextensions.InvestCostManufacturingExtension
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.TopLevelUiConfigService
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.toplevel.DirectlyPaidInvestBreakdownCardBuilder
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.CardIdentifier
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.TableName
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.ValueTypeFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.CardConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableColumnDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.TableConfigFeDto
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class DirectlyPaidInvestBreakdownCardBuilderIT(
    @Autowired
    builderService: TopLevelUiConfigService,
) : TopLevelUIConfigCardBuilderITBase(builderService, DirectlyPaidInvestBreakdownCardBuilder::class) {
    @Test
    fun test() {
        createUiConfigAndVerify(
            verifyCards = ::verifyCards,
            verifyTables = ::verifyTables,
            testInputForCardBuilder = TestInputForCardBuilder.getDefaultWithManufacturingType(ManufacturingType.PURCHASE),
            maybeFileNameForComparison = "ExpectedDefaultDirectlyPaidInvestUIConfig",
        )
    }

    fun verifyCards(cards: Map<CardIdentifier, CardConfigFeDto>) {
        val expectedCardConfig =
            CardConfigFeDto(
                setOf(
                    ValueTypeFeDto.COST,
                ),
                mapOf(
                    ValueTypeFeDto.COST to "Directly paid investment",
                ),
                mapOf(
                    ValueTypeFeDto.COST to InvestCostManufacturingExtension::paidInvests.name,
                ),
                null,
                mapOf(
                    ValueTypeFeDto.COST to listOf("Simple"),
                ),
            )

        assertThat(cards).containsKey("investPaidOverview")
        assertThat(cards["investPaidOverview"]).isEqualTo(expectedCardConfig)
        // Assertions.assertEquals(cards.keys, setOf("investPaidOverview"))
        // Assertions.assertEquals(expectedCardConfig, cards.values.single())
    }

    fun verifyTables(tableConfigs: Map<TableName, TableConfigFeDto>) {
        val expectedTableKeys =
            setOf(
                TableName(
                    "investPaidOverview",
                    ValueTypeFeDto.COST,
                    "Simple",
                ),
            )
        assertThat(tableConfigs.keys).containsAll(expectedTableKeys)
        // Assertions.assertEquals(expectedTableKeys, tableConfigs.keys)

        val tableConfigsUnderTest = tableConfigs.filterKeys { it in expectedTableKeys }
        tableConfigsUnderTest.forEach { (tableName, tableConfig) ->
            Assertions.assertTrue(tableConfig is FieldTableConfigFeDto)

            Assertions.assertEquals(4, tableConfig.columns.size)

            Assertions.assertEquals(3, tableConfig.rowDefinitions.size)

            Assertions.assertEquals(1, tableConfig.rows.size)

            tableConfig.columns.forEach { column ->
                Assertions.assertTrue(column is FieldTableColumnDefinitionFeDto)
                Assertions.assertTrue((column as FieldTableColumnDefinitionFeDto).options?.displayDesignation != null)
            }
        }
    }
}
