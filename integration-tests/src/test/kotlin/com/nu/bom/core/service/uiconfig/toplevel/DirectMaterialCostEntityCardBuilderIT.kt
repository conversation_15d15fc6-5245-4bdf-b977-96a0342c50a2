package com.nu.bom.core.service.uiconfig.toplevel

import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCO2eCalculationElementType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCostCalculationElementType
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.TopLevelUiConfigService
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.TableOptionEnum
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.toplevel.DirectMaterialCostEntityCardBuilder
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.CardIdentifier
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.TableName
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.ValueTypeFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.CardConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableColumnDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.TableConfigFeDto
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.SoftAssertions
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test", "directMaterialCostEntityCardBuilder")
class DirectMaterialCostEntityCardBuilderIT(
    @Autowired
    builderService: TopLevelUiConfigService,
) : TopLevelUIConfigCardBuilderITBase(builderService, DirectMaterialCostEntityCardBuilder::class) {
    private val tableOptions = listOf(TableOptionEnum.PRODUCTION.displayableOptionName)

    @Test
    fun testMaterialEntityCardBuilderForDefaultAsPurchased() {
        val softAssertions = SoftAssertions()
        createUiConfigAndVerify(
            verifyCards = ::verifyCardsForDefault,
            verifyTables = verifyTablesForDefault(softAssertions),
            testInputForCardBuilder = TestInputForCardBuilder.getDefaultWithManufacturingType(ManufacturingType.PURCHASE),
            maybeFileNameForComparison = "ExpectedDefaultDirectMaterialCostUIConfigPurchased",
        )
        softAssertions.assertAll()
    }

    fun verifyTablesForDefault(softAssertions: SoftAssertions): (tableConfigs: Map<TableName, TableConfigFeDto>) -> Unit =
        { tableConfigs ->
            val expectedTableKeys =
                tableOptions
                    .flatMap { tableOption ->
                        ValueTypeFeDto.entries.map { valueTypeFeDto ->
                            TableName("directMaterialCost", valueTypeFeDto, tableOption)
                        }
                    }.toSet()
            softAssertions.assertThat(tableConfigs.keys).isEqualTo(expectedTableKeys)
            tableConfigs.forEach { (_, tableConfig) ->
                Assertions.assertTrue(tableConfig is EntityTableConfigFeDto)
                softAssertions.assertThat(tableConfig).isInstanceOf(EntityTableConfigFeDto::class.java)
                softAssertions.assertThat(tableConfig.rows.size).isEqualTo(40)
                softAssertions.assertThat(tableConfig.rowDefinitions.size).isEqualTo(40)

                softAssertions.assertThat(tableConfig.columns.size).isEqualTo(12)

                tableConfig.columns.forEach { column ->
                    Assertions.assertTrue(column is EntityTableColumnDefinitionFeDto)
                }
            }
        }

    fun verifyCardsForDefault(cards: Map<CardIdentifier, CardConfigFeDto>) {
        val expectedCardConfig =
            CardConfigFeDto(
                setOf(
                    ValueTypeFeDto.COST,
                    ValueTypeFeDto.CO2,
                ),
                mapOf(
                    ValueTypeFeDto.COST to TsetCostCalculationElementType.MATERIAL_COSTS.long,
                    ValueTypeFeDto.CO2 to TsetCO2eCalculationElementType.MATERIAL_CO2E.long,
                ),
                null,
                null,
                mapOf(
                    ValueTypeFeDto.COST to tableOptions,
                    ValueTypeFeDto.CO2 to tableOptions,
                ),
            )

        assertThat(cards).containsKey("directMaterialCost")
        assertThat(cards["directMaterialCost"]).isEqualTo(expectedCardConfig)
        // Assertions.assertEquals(cards.keys, setOf("directMaterialCost"))
        // Assertions.assertEquals(expectedCardConfig, cards.values.single())
    }
}
