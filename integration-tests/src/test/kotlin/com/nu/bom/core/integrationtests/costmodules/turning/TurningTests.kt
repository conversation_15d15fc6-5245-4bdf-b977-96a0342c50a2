package com.nu.bom.core.integrationtests.costmodules.turning

import com.nu.bom.core.geom.TurningPropertiesServiceCompanionTests.Companion.createRectangleSketch
import com.nu.bom.core.integrationtests.costmodules.CostModulesIntegrationTestBase
import com.nu.bom.core.machining.cycletimestep.TurningCycleTimeStepGroup
import com.nu.bom.core.manufacturing.entities.BaseManufacturingFields
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.HardeningType
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.model.TurningProfile
import com.nu.bom.core.service.configurations.BarTCostModuleTsetConfigurationService
import com.nu.bom.core.technologies.manufacturings.turn.ManufacturingBart
import com.nu.bom.core.technologies.manufacturings.turn.ManufacturingTurning
import com.nu.bom.core.turn.TurningService
import org.assertj.core.api.Assertions.assertThat
import org.bson.types.ObjectId
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import org.springframework.boot.test.mock.mockito.MockBean
import reactor.kotlin.core.publisher.toMono
import java.math.BigDecimal

class TurningTests : CostModulesIntegrationTestBase() {
    @MockBean
    private lateinit var turningService: TurningService
    private val materialName = "11SMn30-RAW_MATERIAL_BAR"
    override val costModuleConfigurationIdentifierKey = BarTCostModuleTsetConfigurationService.TSET_CONFIGURATION_KEY

    private final val specificInitialFields =
        mapOf<String, FieldResultStar>(
            BaseManufacturingFields::costModuleConfigurationIdentifier.name to costModuleConfigurationIdentifier,
            ManufacturingBart::materialName.name to Text(materialName),
            ManufacturingBart::shapeId.name to Text("S_085"),
            ManufacturingBart::barLength.name to Length(Num(1.4)),
            ManufacturingTurning::hardeningType.name to HardeningType.NoHardening,
            ManufacturingTurning::hardeningType.name to HardeningType.NoHardening,
            ManufacturingBart::turningProfileId.name to Text(ObjectId().toHexString()), // doesn't matter, mockito'ed away
        )

    private val initialFields = getShapeBasedCalculationModuleBaseInitialFields() + specificInitialFields

    @BeforeEach
    override fun setup() {
        super.setup()

        whenever(
            turningService.getProfile(any(), any()),
        ).thenReturn(mockTurningProfile.toMono())
    }

    @Test
    fun `turning cycle time step group has material assigned`() {
        val creationResult = createCalculation(ManufacturingBart::class, initialFields)
        val costPerPart = findField<BigDecimal>(creationResult.result, "costPerPart", ManufacturingBart::class.java.simpleName)
        val turningStepGroup = findEntityByRef(creationResult, "0") as TurningCycleTimeStepGroup
        val stepGroupMaterial = turningStepGroup.getField("materialName")!!.result
        assert(stepGroupMaterial.res == materialName)
        assertThat(costPerPart).isGreaterThan(BigDecimal.ZERO)
    }

    companion object {
        val mockTurningProfile =
            TurningProfile(
                project = ObjectId(),
                sketch = createRectangleSketch(10, 3),
            )
    }
}
