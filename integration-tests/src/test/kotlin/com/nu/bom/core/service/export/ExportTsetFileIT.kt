package com.nu.bom.core.service.export

import com.nu.bom.core.api.dtos.BomNodeDtoConversionService
import com.nu.bom.core.manufacturing.entities.BaseManufacturingFields
import com.nu.bom.core.manufacturing.fieldTypes.ConfigIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.NoCalcFieldResult
import com.nu.bom.core.technologies.manufacturings.cube.ManufacturingCuttingAndBending
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.AccountTestUtil
import com.nu.bom.core.utils.CalculationBuilder
import com.nu.bom.core.utils.CalculationBuilderService
import com.nu.bomrads.dto.ProjectCreationDTO
import org.bson.types.ObjectId
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class ExportTsetFileIT {
    @Autowired
    private lateinit var bomNodeDtoConversionService: BomNodeDtoConversionService

    @Autowired
    private lateinit var builderService: CalculationBuilderService

    @Autowired
    private lateinit var accountUtil: AccountTestUtil

    private lateinit var accessCheck: AccessCheck
    private lateinit var projectCreation: ProjectCreationDTO

    @BeforeEach
    fun setup() {
        val setup = accountUtil.setupWithProject(name = "test", key = "T001")
        accessCheck = setup.first

        projectCreation = setup.second
    }

    @AfterEach
    fun teardown() {
        accountUtil.cleanup()
    }

    @Test
    fun `export should add no calc data fields to export and have them in the dto`() {
        val noCalcFieldName = "testNoCalc"

        // cube calc
        val tree =
            CalculationBuilder.create(ManufacturingCuttingAndBending::class)
                .withDefaultProject(projectCreation)
                .withInput(
                    mapOf(
                        noCalcFieldName to NoCalcFieldResult(ObjectId().toHexString()),
                        BaseManufacturingFields::costModuleConfigurationIdentifier.name to
                            ConfigIdentifier(ConfigurationIdentifier.empty()),
                    ),
                )

        val (snapshot, _, _) = builderService.build(tree, accessCheck).block()!!

        Assertions.assertEquals(
            noCalcFieldName,
            snapshot.manufacturing!!.getInitialField(noCalcFieldName)?.name?.name,
        )
        Assertions.assertEquals(
            NoCalcFieldResult::class.simpleName,
            snapshot.manufacturing!!.getInitialField(noCalcFieldName)?.result?.getType(),
        )

        val (importExportDto, additionalData) =
            bomNodeDtoConversionService.manufacturingEntityToImportExportDto(
                snapshot.manufacturing!!,
            )

        Assertions.assertEquals(
            1,
            importExportDto.initialFieldsWithResult.filter { it.result is NoCalcFieldResult }.size,
        )

        // Expectation:
        // - externalData
        Assertions.assertEquals(
            1,
            additionalData.second.size,
        )
    }
}
