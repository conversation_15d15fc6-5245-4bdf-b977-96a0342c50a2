package com.nu.bom.core.fti

import com.nu.bom.core.controller.CalculationResultDto
import com.nu.bom.core.controller.CalculationResultWithSnapshot
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.BaseManufacturingFields
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.RawMaterialCoil
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.ConfigIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.Deg
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYears
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYearsUnit
import com.nu.bom.core.manufacturing.utils.ManufacturingTreeUtils
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.configurations.SemanticVersion
import com.nu.bom.core.model.toProjectId
import com.nu.bom.core.service.configurations.FTIPDSCostModuleTsetConfigurationService.Companion.TSET_CONFIGURATION_KEY
import com.nu.bom.core.service.fti.messaging.FtiMockServiceUtils.DEFAULT_BLANK_TO_COIL_EDGE_DISTANCE
import com.nu.bom.core.service.fti.messaging.FtiMockServiceUtils.DEFAULT_CALCULATED_PRESSING_FORCE
import com.nu.bom.core.service.fti.messaging.FtiMockServiceUtils.DEFAULT_DISTANCE_BETWEEN_BLANKS
import com.nu.bom.core.service.fti.messaging.FtiMockServiceUtils.DEFAULT_FEATURE
import com.nu.bom.core.service.fti.messaging.FtiMockServiceUtils.DEFAULT_MATERIAL_UTILIZATION
import com.nu.bom.core.service.fti.messaging.FtiMockServiceUtils.DEFAULT_PITCH
import com.nu.bom.core.service.fti.messaging.FtiMockServiceUtils.DEFAULT_ROTATION_ANGLE
import com.nu.bom.core.service.fti.messaging.FtiMockServiceUtils.DEFAULT_STRIP_WIDTH
import com.nu.bom.core.service.fti.messaging.FtiMockServiceUtils.DEFAULT_THEORETICAL_TOOL_LENGTH
import com.nu.bom.core.service.fti.messaging.FtiMockServiceUtils.DEFAULT_TOOL_WIDTH
import com.nu.bom.core.service.fti.messaging.FtiMockServiceUtils.DEFAULT_WORKBENCH
import com.nu.bom.core.service.fti.messaging.FtiMockServiceUtils.MATERIAL_THICKNESS
import com.nu.bom.core.service.fti.messaging.FtiMockServiceUtils.NET_WEIGHT
import com.nu.bom.core.service.fti.messaging.FtiMockServiceUtils.UNFOLDED_PART_LENGTH
import com.nu.bom.core.service.fti.messaging.FtiMockServiceUtils.UNFOLDED_PART_WIDTH
import com.nu.bom.core.technologies.manufacturings.fti.manufacturing.ManufacturingFti
import com.nu.bom.core.technologies.manufacturings.fti.manufacturing.ManufacturingProgressiveDieStamping
import com.nu.bom.core.technologies.manufacturings.fti.manufacturing.ManufacturingTransferDieStamping
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import reactor.core.publisher.Mono
import reactor.test.StepVerifier
import java.math.BigDecimal
import kotlin.reflect.KClass

class FtiCalculationTests : FtiBasicTest() {
    companion object {
        private const val MATERIAL = "16MnCr5-RAW_MATERIAL_COIL"
        private const val TEST_TITLE = "Test Title"
        private const val CURRENT_USER = "currentUser"
        private const val YEAR = 2023
        private const val KEY = "DE-1"
        private const val NUMBER = "1"
        private const val MATERIAL_GROUP = "StructuralSteel"
        private const val ROTATION_ANGLE = "77.7"
        private const val LOCATION = "tset.ref.classification.germany"
        private val LENGTH_FACTOR = BigDecimal(1000)
        private val PERCENTAGE_FACTOR = BigDecimal(100)
        private val VOLUME = BigDecimal(100000)
    }

    @Test
    fun pdCalculationTest() {
        StepVerifier
            .create(createCalculation(ManufacturingProgressiveDieStamping::class))
            .assertNext { (result, rootSnapshot) ->
                validate(result, rootSnapshot)
            }
            .verifyComplete()
    }

    @Test
    fun tdCalculationTest() {
        StepVerifier
            .create(createCalculation(ManufacturingTransferDieStamping::class))
            .assertNext { (result, rootSnapshot) ->
                validate(result, rootSnapshot)
            }
            .verifyComplete()
    }

    fun validate(
        result: CalculationResultDto,
        rootSnapshot: BomNodeSnapshot,
    ) {
        val mf = result.result
        // Root
        assertEquals(rootSnapshot.title, TEST_TITLE)
        assertBasicFields(mf)

        // Material
        assertResult(mf, Manufacturing::materialName.name, MATERIAL)
        assertResult(
            mf,
            ManufacturingFti::materialThickness.name,
            MATERIAL_THICKNESS.divide(LENGTH_FACTOR),
        )

        // Blanking
        assertResult(
            mf,
            ManufacturingFti::netWeightPerPartForWizard.name,
            NET_WEIGHT,
        )
        assertResult(
            mf,
            ManufacturingProgressiveDieStamping::distanceBetweenBlanks.name,
            DEFAULT_DISTANCE_BETWEEN_BLANKS.divide(LENGTH_FACTOR),
        )
        assertResult(
            mf,
            ManufacturingProgressiveDieStamping::blankToCoilEdgeDistance.name,
            DEFAULT_BLANK_TO_COIL_EDGE_DISTANCE.divide(LENGTH_FACTOR),
        )

        // Nesting
        assertResult(mf, ManufacturingFti::optionRotationAngle.name, DEFAULT_ROTATION_ANGLE)
        assertResult(
            mf,
            ManufacturingFti::optionMaterialUtilization.name,
            DEFAULT_MATERIAL_UTILIZATION.divide(PERCENTAGE_FACTOR),
        )
        assertResult(mf, ManufacturingFti::optionStripWidth.name, DEFAULT_STRIP_WIDTH.divide(LENGTH_FACTOR))
        assertResult(mf, ManufacturingFti::optionPitch.name, DEFAULT_PITCH.divide(LENGTH_FACTOR))

        // Process
        assertResult(mf, ManufacturingFti::toolWidth.name, DEFAULT_TOOL_WIDTH.divide(LENGTH_FACTOR))
        assertResult(mf, ManufacturingFti::unfoldedPartLength.name, UNFOLDED_PART_LENGTH.divide(LENGTH_FACTOR))
        assertResult(mf, ManufacturingFti::unfoldedPartWidth.name, UNFOLDED_PART_WIDTH.divide(LENGTH_FACTOR))
        assertResult(mf, ManufacturingFti::workbenchIdx.name, DEFAULT_WORKBENCH)
        assertResult(mf, ManufacturingFti::featureIdx.name, DEFAULT_FEATURE)
        assertResult(
            mf,
            ManufacturingFti::theoreticalToolLength.name,
            DEFAULT_THEORETICAL_TOOL_LENGTH.divide(LENGTH_FACTOR),
        )
        assertResult(mf, ManufacturingFti::calculatedPressingForce.name, DEFAULT_CALCULATED_PRESSING_FORCE)

        // Entities
        entitiesExpectedAmount.map {
            assertEquals(
                it.value,
                ManufacturingTreeUtils.getDescendantsWithType(
                    result.manufacturing!!,
                    it.key,
                ).size,
                "Wrong expected entities number of type ${it.key}",
            )
        }
    }

    private fun createCalculation(clazz: KClass<out ManufacturingFti>): Mono<CalculationResultWithSnapshot> {
        val initialFields =
            mutableMapOf(
                Manufacturing::lifeTime.name to TimeInYears(BigDecimal.TEN, TimeInYearsUnit.YEAR),
                Manufacturing::location.name to Text(LOCATION),
                BaseManufacturingFields::peakUsableProductionVolumePerYear.name to QuantityUnit(VOLUME),
                BaseManufacturingFields::averageUsableProductionVolumePerYear.name to QuantityUnit(VOLUME),
                Manufacturing::materialName.name to Text(MATERIAL),
                ManufacturingFti::rotationAngle.name to Deg(ROTATION_ANGLE),
                ManufacturingFti::cleaningNeeded.name to Bool(true),
                ManufacturingFti::grindingNeeded.name to Bool(true),
                RawMaterialCoil::materialGroup.name to Text(MATERIAL_GROUP),
            )

        return createStpTestFile().flatMap { uploadResponse ->
            initialFields[ManufacturingFti::fileUploadId.name] = Text(uploadResponse.id)
            initialFields[ManufacturingFti::processFtcFileId.name] = Text(uploadResponse.id)
            initialFields[ManufacturingFti::finalGeometryFileId.name] = Text(uploadResponse.id)
            initialFields.map {
                it.value.source = FieldResult.SOURCE.I
                it
            }.associateBy(
                { it.key },
                { it.value },
            )
            manufacturingCreationService.create(
                accessCheck = accessCheck,
                type = clazz,
                name = clazz.simpleName!!,
                args =
                    hashMapOf(
                        "key" to KEY,
                        "number" to NUMBER,
                        Manufacturing::partNumber.name to "1234",
                        BaseManufacturing::isPart.name to true,
                    ),
                fields =
                    initialFields +
                        Pair(
                            BaseManufacturingFields::costModuleConfigurationIdentifier.name,
                            ConfigIdentifier(
                                ConfigurationIdentifier.tset(TSET_CONFIGURATION_KEY, SemanticVersion.initialVersion()),
                            ),
                        ),
                projectId = projectId.toProjectId(),
                year = YEAR,
                title = TEST_TITLE,
            )
        }
    }
}
