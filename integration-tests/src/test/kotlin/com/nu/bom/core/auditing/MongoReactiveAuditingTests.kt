package com.nu.bom.core.auditing

import com.nu.bom.core.TestPlatformVersionService
import com.nu.bom.core.utils.AccountTestUtil
import com.nu.bom.core.version.PlatformVersion
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import reactor.test.StepVerifier

@SpringBootTest
@ActiveProfiles("test")
class MongoReactiveAuditingTests {
    @Autowired
    private lateinit var repository: AuditableTestEntityRepository

    @Autowired
    private lateinit var accountUtil: AccountTestUtil

    @Autowired
    private lateinit var platformVersionService: TestPlatformVersionService

    @BeforeEach
    fun setup() {
        // set initial system version
        platformVersionService.currentVersion = VERSION_0
    }

    @AfterEach
    fun teardown() {
        repository.deleteAll().block()
        // restore initial system version
        platformVersionService.currentVersion = VERSION_0
    }

    @Test
    fun testAuditingWithPropertyAnnotations() {
        val entity = AuditableTestEntity("EntityZero")

        val account1 = accountUtil.createTestAccount("TestAccount1")
        val account2 = accountUtil.createTestAccount("TestAccount2")

        val createWithAccount1 = accountUtil.doAs(account1) {
            repository.save(entity)
        }

        val modifyWithAccount2 = accountUtil.doAs(account2) {
            repository.findByName("EntityZero").flatMap {
                it.apply { testProp = "changed something" }
                repository.save(it)
            }
        }

        StepVerifier.create(createWithAccount1)
            .assertNext {
                assertNotNull(it.createdBy)
                assertNotNull(it.createdDate)
                assertNotNull(it.lastModifiedBy)
                assertNotNull(it.lastModifiedDate)

                assertEquals(it.lastModifiedBy, it.createdBy)
                assertEquals(it.lastModifiedDate, it.createdDate)

                assertEquals(VERSION_0, it.createdWithSystemVersion)
                assertEquals(VERSION_0, it.lastModifiedWithSystemVersion)
            }
            .verifyComplete()

        // change system version in between
        platformVersionService.currentVersion = VERSION_1

        StepVerifier.create(modifyWithAccount2)
            .assertNext {
                assertNotNull(it.createdBy)
                assertNotNull(it.createdDate)
                assertNotNull(it.lastModifiedBy)
                assertNotNull(it.lastModifiedDate)

                assertNotEquals(it.lastModifiedBy, it.createdBy)
                assertNotEquals(it.lastModifiedDate, it.createdDate)

                assertEquals(VERSION_0, it.createdWithSystemVersion)
                assertEquals(VERSION_1, it.lastModifiedWithSystemVersion)
            }
            .verifyComplete()
    }

    companion object {
        val VERSION_0 = PlatformVersion("0")
        val VERSION_1 = PlatformVersion("1")
    }
}
