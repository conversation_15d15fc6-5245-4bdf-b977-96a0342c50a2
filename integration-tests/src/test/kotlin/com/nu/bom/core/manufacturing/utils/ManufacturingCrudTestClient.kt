package com.nu.bom.core.manufacturing.utils

import com.nu.bom.core.api.CalculationEntityCopyController
import com.nu.bom.core.api.ManufacturingEntityDeletionController
import com.nu.bom.core.api.ManufacturingEntityRearrangementController
import com.nu.bom.core.api.ManufacturingUpdateController
import com.nu.bom.core.api.dtos.BomExpNodeDto
import com.nu.bom.core.api.dtos.BomNodeDto
import com.nu.bom.core.api.dtos.BomNodeStatusUpdateDto
import com.nu.bom.core.api.dtos.BranchDto
import com.nu.bom.core.api.dtos.EntityCreationDto
import com.nu.bom.core.api.dtos.MasterDataCompositeKey
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.AreaUnits
import com.nu.bom.core.manufacturing.fieldTypes.Currency
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Power
import com.nu.bom.core.manufacturing.fieldTypes.PowerUnits
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.TsetDefaultSkillType
import com.nu.bom.core.manufacturing.fieldTypes.mdKeyAsText
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.Merge
import com.nu.bom.core.model.MergeSourceType
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.service.EntityCreationDataConversionService.EntityCreationData
import com.nu.bom.core.utils.findInTree
import com.nu.bomrads.enumeration.BomNodeStatus
import com.nu.bomrads.id.FolderId
import com.nu.bomrads.nuxt.ProjectHelper
import com.tset.bom.clients.nuledge.FieldParameterDto
import org.springframework.context.annotation.Lazy
import org.springframework.core.env.Environment
import org.springframework.http.MediaType
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.stereotype.Service
import org.springframework.test.web.reactive.server.WebTestClient
import org.springframework.test.web.reactive.server.expectBody
import org.springframework.test.web.reactive.server.returnResult
import reactor.core.publisher.Mono

@Service
@Lazy
class ManufacturingCrudTestClient(
    injectedClient: WebTestClient,
    environment: Environment,
) : WebTestClientBase(injectedClient, environment) {
    private var projectId: ProjectId? = null
    private var jwt: String? = null

    private var client: WebTestClient = createClient()

    fun configure(
        projectId: ProjectId,
        jwt: Jwt? = null,
        jwtToken: String? = null,
    ) {
        this.projectId = projectId
        client = createClient(jwt = jwt, jwtToken = jwtToken)
    }

    fun reset() {
        projectId = null
        jwt = null
        client = createClient()
    }

    fun getConfiguredClient() = client

    fun updateStatus(
        bomNodeId: String,
        status: BomNodeStatus,
        branch: String? = null,
    ): BomNodeDto =
        client
            .put()
            .uri { uri ->
                uri.path("/api/man/$bomNodeId/status")
                if (branch != null) {
                    uri.queryParam("branch", branch)
                }
                uri.build()
            }.contentType(MediaType.APPLICATION_JSON)
            .bodyValue(BomNodeStatusUpdateDto(status))
            .exchange()
            .expectStatus()
            .isOk
            .expectBody(BomNodeDto::class.java)
            .returnResult()
            .responseBody!!

    fun lock(
        bomNodeId: String,
        branchId: BranchId,
        lockFieldRequest: ManufacturingUpdateController.InputParameterApi,
    ): BomNodeDto =
        client
            .post()
            .uri("/api/man/$bomNodeId/lock?branch=${branchId.toHexString()}")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(lockFieldRequest)
            .exchange()
            .expectStatus()
            .isOk
            .expectBody(BomNodeDto::class.java)
            .returnResult()
            .responseBody!!

    fun unlock(
        bomNodeId: String,
        branchId: BranchId,
        unlockFieldRequest: ManufacturingUpdateController.InputParameterApi,
    ): BomNodeDto =
        client
            .post()
            .uri("/api/man/$bomNodeId/unlock?branch=${branchId.toHexString()}")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(unlockFieldRequest)
            .exchange()
            .expectStatus()
            .isOk
            .expectBody(BomNodeDto::class.java)
            .returnResult()
            .responseBody!!

    fun updateField(
        bomNodeId: String,
        branch: String?,
        field: ManufacturingUpdateController.InputParameterApi,
    ): BomNodeDto {
        val uri = if (branch != null) "/api/man/$bomNodeId?source=$branch" else "/api/man/$bomNodeId"

        return client
            .post()
            .uri(uri)
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(field)
            .exchange()
            .expectStatus()
            .isOk
            .expectBody(BomNodeDto::class.java)
            .returnResult()
            .responseBody!!
    }

    fun recalculate(
        bomNodeId: String,
        branch: String,
    ): BomNodeDto {
        val uri = "/api/man/$bomNodeId/recalculate?source=$branch"
        return client
            .post()
            .uri(uri)
            .contentType(MediaType.APPLICATION_JSON)
            .exchange()
            .expectStatus()
            .isOk
            .expectBody<BomNodeDto>()
            .returnResult()
            .responseBody!!
    }

    fun publish(node: BomNodeDto): BomNodeDto = publish(bomNodeId = node.id, branch = node.branch.id)

    fun publish(
        bomNodeId: String,
        branch: String,
    ): BomNodeDto =
        publish(bomNodeId = bomNodeId, branch = branch) { response ->
            response
                .expectStatus()
                .isOk
                .expectBody(BomNodeDto::class.java)
                .returnResult()
                .responseBody!!
        }

    /**
     * Use [block] to assert the response
     */
    fun <T> publish(
        bomNodeId: String,
        branch: String,
        block: (WebTestClient.ResponseSpec) -> T,
    ): T =
        client
            .post()
            .uri("/api/man/$bomNodeId/publish?branch=$branch")
            .contentType(MediaType.APPLICATION_JSON)
            .exchange()
            .let(block)

    /**
     * Use [block] to assert the response
     */
    fun <T> updateAndPublish(
        bomNodeId: String,
        branch: String,
        block: (WebTestClient.ResponseSpec) -> T,
    ): T =
        client
            .post()
            .uri("/api/man/$bomNodeId/updateAndPublish?branch=$branch")
            .contentType(MediaType.APPLICATION_JSON)
            .exchange()
            .let(block)

    fun checkout(
        bomNodeId: String,
        branch: String? = null,
    ): BomNodeDto {
        val uri =
            "/api/man/$bomNodeId/checkout" +
                if (branch != null) {
                    "?source=$branch"
                } else {
                    ""
                }

        return client
            .post()
            .uri(uri)
            .contentType(MediaType.APPLICATION_JSON)
            .exchange()
            .expectStatus()
            .isOk
            .expectBody(BomNodeDto::class.java)
            .returnResult()
            .responseBody!!
    }

    /**
     * Use [block] to assert the response
     */
    fun <T> saveToSource(
        bomNodeId: String,
        branch: String,
        block: (WebTestClient.ResponseSpec) -> T,
    ): T =
        client
            .post()
            .uri("/api/branch/$branch/saveToSource?bomNodeId=$bomNodeId")
            .contentType(MediaType.APPLICATION_JSON)
            .exchange()
            .let(block)

    /**
     * Use [block] to assert the response
     */
    fun <T> updateAndSaveToSource(
        bomNodeId: String,
        branch: String,
        block: (WebTestClient.ResponseSpec) -> T,
    ): T =
        client
            .post()
            .uri("/api/branch/$branch/updateAndSaveToSource?node=$bomNodeId")
            .contentType(MediaType.APPLICATION_JSON)
            .exchange()
            .let(block)

    fun saveToSource(
        bomNodeId: String,
        branch: String,
    ): BranchDto =
        saveToSource(bomNodeId, branch) { response ->
            response
                .expectStatus()
                .isOk
                .expectBody(BranchDto::class.java)
                .returnResult()
                .responseBody!!
        }

    fun checkMerge(branch: String): List<Merge> =
        client
            .get()
            .uri("/api/branch/$branch/merge")
            .exchange()
            .expectStatus()
            .isOk
            .returnResult<Merge>()
            .responseBody
            .collectList()
            .block() ?: emptyList()

    fun merge(
        branch: String,
        sourceType: MergeSourceType,
        bomNodeId: String,
    ): BomNodeDto =
        client
            .post()
            .uri("/api/branch/$branch/merge?mergeSource=${sourceType.name}&node=$bomNodeId")
            .exchange()
            .expectStatus()
            .isOk
            .expectBody(BomNodeDto::class.java)
            .returnResult()
            .responseBody!!

    fun checkoutBranch(bomNodeId: String): String = checkout(bomNodeId).branch.id

    fun addStep(
        node: BomNodeDto,
        name: String,
        initialCycleTime: Time = Time(0.0, TimeUnits.SECOND),
    ): BomNodeDto = addStep(node.id, node.branch.id, node.manufacturing!!.id, name, initialCycleTime)

    fun addStep(
        node: BomNodeDto,
        branchId: String = node.branch.id,
        name: String,
        initialCycleTime: Time = Time(0.0, TimeUnits.SECOND),
    ): BomNodeDto = addStep(node.id, branchId, node.manufacturing!!.id, name, initialCycleTime)

    fun addStep(
        bomNodeId: String,
        branchId: String,
        parentId: String,
        name: String,
        initialCycleTime: Time = Time(0.0, TimeUnits.SECOND),
        fields: List<FieldParameterDto> = emptyList(),
    ): BomNodeDto =
        addEntity(
            bomNodeId,
            branchId,
            name,
            Entities.MANUFACTURING_STEP,
            parentId,
            fields = addMandatoryManufacturingStepFieldsIfMissing(initialCycleTime, fields = fields),
        )

    private fun addMandatoryManufacturingStepFieldsIfMissing(
        initialCycleTime: Time,
        utilizationRate: Rate = Rate(0.8),
        partsPerCycle: QuantityUnit = QuantityUnit(1.0),
        fields: List<FieldParameterDto>,
    ): List<FieldParameterDto> {
        val defaultFields =
            mapOf(
                "initialCycleTime" to initialCycleTime,
                "utilizationRate" to utilizationRate,
                "partsPerCycle" to partsPerCycle,
            ).mapValues { (fieldName, fieldResult) ->
                FieldParameterDto(
                    name = fieldName,
                    type = fieldResult.getType(),
                    unit = fieldResult.getUnit(),
                    source = "I",
                    value = fieldResult.res,
                    denominatorUnit = null,
                )
            }

        val fieldsMinusNullDefaults =
            fields
                .associateBy { it.name }
                .filterNot { (name, field) ->
                    field.value == null && defaultFields.containsKey(name)
                }

        return (defaultFields + fieldsMinusNullDefaults).values.toList()
    }

    fun addLabor(
        bomNodeId: String,
        branchId: String,
        parentId: String,
        name: String,
        skillType: Text? = null,
        fields: List<FieldParameterDto> = emptyList(),
    ): BomNodeDto =
        addEntity(
            bomNodeId = bomNodeId,
            branchId = branchId,
            name = name,
            entityType = Entities.LABOR,
            parentId = parentId,
            fields =
                addMandatoryLaborFieldsIfMissing(
                    fields +
                        listOfNotNull(
                            skillType?.let {
                                FieldParameterDto(
                                    name = "skillType",
                                    type = "Text",
                                    value = it.res,
                                    denominatorUnit = null,
                                )
                            },
                        ),
                ),
        )

    private fun addMandatoryLaborFieldsIfMissing(fields: List<FieldParameterDto>): List<FieldParameterDto> {
        val defaultFields =
            mapOf(
                "skillType" to TsetDefaultSkillType.SKILLED_WORKER.mdKeyAsText(),
                "requiredLabor" to Num(1.0),
            ).mapValues { (fieldName, fieldResult) ->
                FieldParameterDto(
                    name = fieldName,
                    type = fieldResult.getType(),
                    unit = fieldResult.getUnit(),
                    source = "I",
                    value = fieldResult.res,
                    denominatorUnit = null,
                )
            }

        val fieldsMinusNullDefaults =
            fields
                .associateBy { it.name }
                .filterNot { (name, field) ->
                    field.value == null && defaultFields.containsKey(name)
                }

        return (defaultFields + fieldsMinusNullDefaults).values.toList()
    }

    fun addMachine(
        bomNodeId: String,
        branchId: String,
        parentId: String,
        name: String,
        fields: List<FieldParameterDto> = emptyList(),
    ): BomNodeDto =
        addEntity(
            bomNodeId = bomNodeId,
            branchId = branchId,
            name = name,
            entityType = Entities.MACHINE,
            parentId = parentId,
            fields = addMandatoryMachineFieldsIfMissing(fields),
        )

    private fun addMandatoryMachineFieldsIfMissing(fields: List<FieldParameterDto>): List<FieldParameterDto> {
        val defaultFields =
            mapOf(
                "quantity" to Num(1.0),
                "masterdataBaseCurrency" to Currency("EUR"),
                "investBase" to Money(1.0),
                "requiredSpaceGross" to Area(1.0, AreaUnits.QM),
                "connectedLoad" to Power(1.0, PowerUnits.KILOWATT),
                "maintenanceRate" to Rate(0.8),
                "consumableRate" to Rate(0.5),
                "manufacturer" to Text("TSET"),
            ).mapValues { (fieldName, fieldResult) ->
                FieldParameterDto(
                    name = fieldName,
                    type = fieldResult.getType(),
                    unit = fieldResult.getUnit(),
                    source = "I",
                    value = fieldResult.res,
                    denominatorUnit = null,
                )
            }

        val fieldsMinusNullDefaults =
            fields
                .associateBy { it.name }
                .filterNot { (name, field) ->
                    field.value == null && defaultFields.containsKey(name)
                }

        return (defaultFields + fieldsMinusNullDefaults).values.toList()
    }

    fun addCycleTimeStepGroup(
        bomNodeId: String,
        branchId: String,
        parentId: String,
        name: String,
        fields: List<FieldParameterDto> = emptyList(),
    ): BomNodeDto =
        addEntity(
            bomNodeId = bomNodeId,
            branchId = branchId,
            name = name,
            entityType = Entities.CYCLETIME_STEP_GROUP,
            parentId = parentId,
            fields = fields,
        )

    fun addCycleTimeStep(
        bomNodeId: String,
        branchId: String,
        parentId: String,
        name: String,
        fields: List<FieldParameterDto> = emptyList(),
    ): BomNodeDto =
        addEntity(
            bomNodeId = bomNodeId,
            branchId = branchId,
            name = name,
            entityType = Entities.CYCLETIME_STEP,
            parentId = parentId,
            fields = fields,
        )

    fun addEntity(
        bomNodeId: String,
        branchId: String,
        name: String?,
        entityType: Entities,
        parentId: String?,
        masterDataKey: MasterDataCompositeKey? = null,
        childBomNodeId: String? = null,
        fields: List<FieldParameterDto> = emptyList(),
        entityClass: String? = null,
        linkEntity: EntityCreationData.EntityLinkInformation? = null,
        technology: String? = null,
    ): BomNodeDto {
        val nameField =
            listOfNotNull(
                name?.let {
                    FieldParameterDto(
                        name = "displayDesignation",
                        type = "Text",
                        source = "I",
                        value = name,
                        denominatorUnit = null,
                    )
                },
            )

        return createEntity(
            EntityCreationDto(
                entityType = entityType,
                entityClass = entityClass,
                fields = fields + nameField,
                masterDataKey = masterDataKey,
                childBomNodeId = childBomNodeId,
                parentId = parentId,
                linkEntityField = linkEntity?.entityLinkField,
                linkEntityId = linkEntity?.linkEntityId?.toHexString(),
                technology = technology,
            ),
            bomNodeId,
            branchId,
        )
    }

    fun addEntity(
        node: BomNodeDto,
        parentId: String,
        name: String?,
        entityType: Entities,
        masterDataKey: MasterDataCompositeKey? = null,
        fields: List<FieldParameterDto> = emptyList(),
        entityClass: String? = null,
        linkEntity: EntityCreationData.EntityLinkInformation? = null,
        technology: String? = null,
    ): BomNodeDto {
        val parent =
            node.manufacturing?.findInTree(
                predicate = { it.id == parentId },
                children = { it.children },
            ) ?: error("Could not find parent with id=$parentId")

        return addEntity(
            node.id,
            node.branch.id,
            name,
            entityType,
            parent.id,
            masterDataKey,
            fields = fields,
            entityClass = entityClass,
            linkEntity = linkEntity,
            technology = technology,
        )
    }

    fun createEntity(
        dto: EntityCreationDto<FieldParameterDto>,
        bomNodeId: String,
        branchId: String,
    ): BomNodeDto =
        client
            .post()
            .uri("/api/projects/$projectId/calculations/$bomNodeId/$branchId/entities")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(dto)
            .exchange()
            .expectStatus()
            .isOk
            .expectBody(BomNodeDto::class.java)
            .returnResult()
            .responseBody
            ?: error(
                "Missing responseBody for \"/api/projects/$projectId/calculations/$bomNodeId/$branchId/entities\"",
            )

    fun deleteEntity(
        bomNodeId: String,
        entityId: String,
        branch: String?,
    ): BomNodeDto? =
        client
            .post()
            .uri("/api/entity/delete" + (branch?.let { "?source=$branch" } ?: ""))
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(
                ManufacturingEntityDeletionController.DeleteEntityDto(
                    bomNodeId = bomNodeId,
                    entityId = entityId,
                ),
            ).exchange()
            .expectStatus()
            .isOk
            .returnResult<BomNodeDto>()
            .responseBody
            .blockFirst()

    fun deleteManufacturing(bomNodeId: String) {
        client
            .delete()
            .uri("/api/man/$bomNodeId")
            .exchange()
            .expectStatus()
            .isOk
    }

    fun moveEntity(
        bomNodeId: String,
        entityId: String,
        parentId: String,
        branch: String?,
    ): BomNodeDto? =
        client
            .post()
            .uri("/api/entity/move?source=$branch")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(
                ManufacturingEntityRearrangementController.RearrangeEntityDto(
                    bomNodeId = bomNodeId,
                    entityId = entityId,
                    parentId = parentId,
                ),
            ).exchange()
            .expectStatus()
            .isOk
            .returnResult<BomNodeDto>()
            .responseBody
            .blockFirst()

    fun reorder(
        bomNodeId: String,
        entityId: String,
        parentId: String,
        insertBeforeEntityId: String?,
        insertAfterEntityId: String?,
        branch: String?,
    ): BomNodeDto? =
        client
            .post()
            .uri("/api/entity/reorder?source=$branch")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(
                ManufacturingEntityRearrangementController.ReorderEntityDto(
                    bomNodeId = bomNodeId,
                    entityId = entityId,
                    insertBeforeEntityId = insertBeforeEntityId,
                    insertAfterEntityId = insertAfterEntityId,
                ),
            ).exchange()
            .expectStatus()
            .isOk
            .returnResult<BomNodeDto>()
            .responseBody
            .blockFirst()

    fun getBomNode(
        bomNodeId: String,
        branchId: String? = null,
        showOpenMerges: Boolean? = false,
    ): BomNodeDto? {
        val branchQuery = if (branchId != null) "&branch=$branchId" else ""
        return client
            .get()
            .uri("/api/man/$bomNodeId?showOpenMerges=$showOpenMerges$branchQuery")
            .exchange()
            .expectStatus()
            .isOk
            .returnResult<BomNodeDto>()
            .responseBody
            .onErrorResume { Mono.empty() }
            .blockFirst()
    }

    fun getBomNodeMono(
        bomNodeId: String,
        branchId: String? = null,
        showOpenMerges: Boolean? = false,
    ): Mono<BomNodeDto> {
        val branchQuery = if (branchId != null) "&branch=$branchId" else ""
        return client
            .get()
            .uri("/api/man/$bomNodeId?showOpenMerges=$showOpenMerges$branchQuery")
            .exchange()
            .expectStatus()
            .isOk
            .returnResult<BomNodeDto>()
            .responseBody
            .last()
    }

    fun getBomNodeNotFound(
        bomNodeId: String,
        branchId: String? = null,
    ): BomNodeDto? {
        val branchQuery = if (branchId != null) "?branch=$branchId" else ""
        return client
            .get()
            .uri("/api/man/$bomNodeId$branchQuery")
            .exchange()
            .expectStatus()
            .is4xxClientError
            .returnResult<BomNodeDto>()
            .responseBody
            .onErrorResume { Mono.empty() }
            .blockFirst()
    }

    fun renameNode(
        bomNodeId: String,
        title: String,
        branch: String? = null,
        renameDescendants: Boolean = false,
    ): BomNodeDto =
        client
            .post()
            .uri { uriBuilder ->
                uriBuilder.path("/api/man/$bomNodeId/rename")
                uriBuilder.queryParam("title", title)
                uriBuilder.queryParam("renameDescendants", renameDescendants)
                if (branch != null) uriBuilder.queryParam("branch", branch)
                uriBuilder.build()
            }.exchange()
            .expectStatus()
            .isOk
            .returnResult<BomNodeDto>()
            .responseBody
            .onErrorResume { Mono.empty() }
            .blockFirst()!!

    fun getBranches(bomNodeId: String): List<BranchDto> =
        client
            .get()
            .uri("/api/man/$bomNodeId/branches")
            .exchange()
            .expectStatus()
            .isOk
            .returnResult<BranchDto>()
            .responseBody
            .onErrorResume { Mono.empty() }
            .collectList()
            .block()!!

    fun saveAsPublicVariant(
        branchId: String,
        bomNodeId: String,
        title: String,
        renameDescendants: Boolean = false,
    ): BranchDto =
        client
            .post()
            .uri("/api/branch/$branchId/savePublicVariant?bomNodeId=$bomNodeId&title=$title&renameDescendants=$renameDescendants")
            .exchange()
            .expectStatus()
            .isOk
            .returnResult<BranchDto>()
            .responseBody
            .onErrorResume { Mono.empty() }
            .blockFirst()!!

    fun copyBranch(
        branchId: String,
        title: String,
        rootNode: String?,
        renameDescendants: Boolean = false,
    ): BranchDto =
        client
            .post()
            .uri {
                it
                    .path("/api/branch/$branchId/copy")
                    .apply {
                        queryParam("title", title)
                        if (rootNode != null) {
                            queryParam("rootNode", rootNode)
                        }
                        queryParam("renameDescendants", renameDescendants)
                    }.build()
            }.exchange()
            .expectStatus()
            .isOk
            .returnResult<BranchDto>()
            .responseBody
            .onErrorResume { Mono.empty() }
            .blockFirst()!!

    fun getProjectByIdOrKey(
        projectIdOrKey: String,
        byKey: Boolean = false,
    ): ProjectHelper.ProjectDto =
        client
            .get()
            .uri { uriBuilder ->
                uriBuilder
                    .path("/api/projects/$projectIdOrKey")
                    .apply {
                        queryParam("byKey", byKey)
                    }.build()
            }.exchange()
            .expectStatus()
            .isOk
            .returnResult<ProjectHelper.ProjectDto>()
            .responseBody
            .blockFirst()!!

    fun getLastAccessedProjects(): List<ProjectHelper.ProjectDto> =
        client
            .get()
            .uri { uriBuilder ->
                uriBuilder
                    .path("/api/projects/lastAccessed")
                    .build()
            }.exchange()
            .expectStatus()
            .isOk
            .returnResult<ProjectHelper.ProjectDto>()
            .responseBody
            .collectList()
            .block()!!

    fun getStarredProjects(): List<ProjectHelper.ProjectDto> =
        client
            .get()
            .uri { uriBuilder ->
                uriBuilder
                    .path("/api/projects/starred")
                    .build()
            }.exchange()
            .expectStatus()
            .isOk
            .returnResult<ProjectHelper.ProjectDto>()
            .responseBody
            .collectList()
            .block()!!

    fun starProject(projectId: String): List<ProjectHelper.ProjectDto> =
        client
            .patch()
            .uri { uriBuilder ->
                uriBuilder
                    .path("/api/projects/$projectId/star")
                    .build()
            }.bodyValue(ProjectHelper.StarProjectDto(true))
            .exchange()
            .expectStatus()
            .isOk
            .returnResult<ProjectHelper.ProjectDto>()
            .responseBody
            .collectList()
            .block()!!

    fun failStarProject(projectId: String) =
        client
            .patch()
            .uri { uriBuilder ->
                uriBuilder
                    .path("/api/projects/$projectId/star")
                    .build()
            }.bodyValue(ProjectHelper.StarProjectDto(true))
            .exchange()
            .expectStatus()
            .is4xxClientError
            .returnResult<Exception>()
            .responseBody
            .blockFirst()!!

    fun unstarProject(projectId: String): List<ProjectHelper.ProjectDto> =
        client
            .patch()
            .uri { uriBuilder ->
                uriBuilder
                    .path("/api/projects/$projectId/star")
                    .build()
            }.bodyValue(ProjectHelper.StarProjectDto(false))
            .exchange()
            .expectStatus()
            .isOk
            .returnResult<ProjectHelper.ProjectDto>()
            .responseBody
            .collectList()
            .block()!!

    fun failUnstarProject(projectId: String) =
        client
            .patch()
            .uri { uriBuilder ->
                uriBuilder
                    .path("/api/projects/$projectId/star")
                    .build()
            }.bodyValue(ProjectHelper.StarProjectDto(false))
            .exchange()
            .expectStatus()
            .is4xxClientError
            .returnResult<Exception>()
            .responseBody
            .blockFirst()!!

    fun createProject(
        name: String,
        key: String?,
        folderId: FolderId,
    ): ProjectHelper.ProjectDto =
        client
            .post()
            .uri("/api/projects")
            .bodyValue(ProjectHelper.NewProjectDto(name, key, folderId))
            .exchange()
            .expectBody<ProjectHelper.ProjectDto>()
            .returnResult()
            .responseBody!!

    fun failCreateProject(
        name: String,
        key: String?,
        folderId: FolderId,
    ): Exception =
        client
            .post()
            .uri("/api/projects")
            .bodyValue(ProjectHelper.NewProjectDto(name, key, folderId))
            .exchange()
            .expectStatus()
            .is4xxClientError
            .returnResult<Exception>()
            .responseBody
            .blockFirst()!!

    fun updateProject(
        id: String,
        projectDto: ProjectHelper.ProjectUpdateDto,
    ): ProjectHelper.ProjectDto =
        client
            .patch()
            .uri("/api/projects/$id")
            .bodyValue(projectDto)
            .exchange()
            .expectBody<ProjectHelper.ProjectDto>()
            .returnResult()
            .responseBody!!

    fun failUpdateProject(
        id: String,
        projectDto: ProjectHelper.ProjectUpdateDto,
    ): Exception =
        client
            .patch()
            .uri("/api/projects/$id")
            .bodyValue(projectDto)
            .exchange()
            .expectStatus()
            .is4xxClientError
            .returnResult<Exception>()
            .responseBody
            .blockFirst()!!

    fun getBomExplorerRoots(projectId: String): List<BomExpNodeDto> =
        client
            .post()
            .uri("/api/project/$projectId/bomnodes")
            .exchange()
            .expectStatus()
            .isOk
            .returnResult<BomExpNodeDto>()
            .responseBody
            .collectList()
            .block()!!

    fun updateMasterData(bomNodeDto: BomNodeDto): BomNodeDto =
        updateMasterData(
            bomNodeId = bomNodeDto.id,
            branch = bomNodeDto.branch.id,
        )

    fun updateMasterData(
        bomNodeId: String,
        branch: String?,
    ): BomNodeDto {
        val uri =
            "/api/man/$bomNodeId/masterdata/update" +
                if (branch != null) "?branch=$branch" else ""

        return client
            .post()
            .uri(uri)
            .exchange()
            .expectBody<BomNodeDto>()
            .returnResult()
            .responseBody!!
    }

    fun copyBomTree(
        sources: List<CalculationEntityCopyController.EntitySelectorDto>,
        target: CalculationEntityCopyController.EntitySelectorDto,
        copyContext: CalculationEntityCopyController.CopyContextDto,
    ): Mono<BomNodeDto> =
        client
            .post()
            .uri("/api/calculations/copy")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(
                CalculationEntityCopyController.SourceAndTargetEntitySelectorDto(
                    sources = sources,
                    target = target,
                    context = copyContext,
                ),
            ).exchange()
            .expectStatus()
            .isOk
            .returnResult<BomNodeDto>()
            .responseBody
            .last()
}
