package com.nu.bom.core.service.uiconfig

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.BaseUiConfigService
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.BaseUiCardBuilder
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.BaseUiConfigFeDto
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.BaseUiConfigurationIdentifiers
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.CardIdentifier
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.TableName
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.CardConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.TableConfigFeDto
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions
import kotlin.reflect.KClass

abstract class BaseUIConfigCardBuilderITBase<Config : BaseUiConfigFeDto, IDs : BaseUiConfigurationIdentifiers>(
    open val builderService: BaseUiConfigService<Config, IDs>,
    private val expectedInjectedCardBuilderClasses: List<KClass<out BaseUiCardBuilder>>,
) {
    protected fun verifyUiConfig(
        verifyCards: (Map<CardIdentifier, CardConfigFeDto>) -> Unit,
        verifyTables: (Map<TableName, TableConfigFeDto>) -> Unit,
        key: BaseUiConfigurationIdentifiers,
        testUiConfig: BaseUiConfigFeDto,
        maybeFileNameForComparison: String? = null,
    ) {
        assertThat(builderService.uiCardBuilders.map { it::class })
            .containsAll(expectedInjectedCardBuilderClasses)
            .describedAs(
                "This test should have the following card builders injected: `$expectedInjectedCardBuilderClasses`, " +
                    "but ${builderService.uiCardBuilders.map { it::class }} are injected. Please check the used profiles!",
            )
        // Assertions.assertEquals(
        //     expectedInjectedCardBuilderClasses,
        //     builderService.uiCardBuilders.map { it::class },
        //     "This test should have exactly the following card builders injected: `$expectedInjectedCardBuilderClasses`, " +
        //         "but ${builderService.uiCardBuilders.map { it::class }} are injected. Please check the used profiles!",
        // )

        // This can be used if one wants to create a new json to compare diffs
        // jacksonObjectMapper().apply { enable(SerializationFeature.INDENT_OUTPUT) }.writeValue(File("Test.json"), testUiConfig)

        Assertions.assertEquals(key, testUiConfig.key)
        verifyCards(testUiConfig.cards)
        verifyTables(testUiConfig.tableConfigs)
        maybeFileNameForComparison?.let { verifyAgainstFullJson(testUiConfig, it) }
    }

    private fun verifyAgainstFullJson(
        testUiConfig: BaseUiConfigFeDto,
        fileName: String,
    ) {
        val mapper = jacksonObjectMapper()
        val actual = mapper.readTree(mapper.writeValueAsString(testUiConfig))
        val expected =
            mapper.readTree(
                this::class.java.classLoader.getResource("$fileName.json")!!.readBytes(),
            )

        val actualCards = actual.get("cards")
        val expectedCards = expected.get("cards")

        expectedCards.fields().forEach { (key, value) ->
            assertThat(actualCards.get(key)).isEqualTo(value)
        }

        val actualTableConfigs = actual.get("tableConfigs")
        val expectedTableConfigs = expected.get("tableConfigs")

        expectedTableConfigs.fields().forEach { (key, value) ->
            assertThat(actualTableConfigs.get(key)).isEqualTo(value)
        }

        // assertThat(actual.get("cards")).contains(expected.get("cards"))
        // assertThat(actual.get("tableConfigs")).contains(expected.get("tableConfigs"))
        // Assertions.assertEquals(expected, actual)
    }
}
