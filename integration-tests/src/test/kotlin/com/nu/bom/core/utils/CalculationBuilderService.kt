package com.nu.bom.core.utils

import com.nu.bom.core.api.dtos.BranchDto
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.toMongoBranchId
import com.nu.bom.core.model.toMongoID
import com.nu.bom.core.model.toMongoProjectId
import com.nu.bom.core.service.BomNodeConversionService
import com.nu.bom.core.service.BomNodeService
import com.nu.bom.core.service.BomPublishService
import com.nu.bom.core.service.EntityCreationService
import com.nu.bom.core.service.ProjectService
import com.nu.bom.core.service.bomnode.LoadingMode
import com.nu.bom.core.service.dto.DirtyStateHandling
import com.nu.bom.core.user.AccessCheck
import com.tset.core.module.bom.CalculationUpdateModule
import org.bson.types.ObjectId
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import com.nu.bomrads.id.ProjectId as BomradsProjectId

@Service
@Lazy
class CalculationBuilderService(
    private val projectService: ProjectService,
    private val calculationUpdaterModule: CalculationUpdateModule,
    private val entityCreationService: EntityCreationService,
    private val bomNodeService: BomNodeService,
    private val bomPublishService: BomPublishService,
    private val bomNodeConversionService: BomNodeConversionService
) {

    private class CreationContext {

        private val _currentBranchId: MutableMap<BomNodeId, BranchId?> = mutableMapOf()

        fun setCurrentBranchId(bomNodeId: BomNodeId, branchId: BranchId?) {
            _currentBranchId[bomNodeId] = branchId
        }
        fun setCurrentBranchId(bomNodeId: BomNodeId, branch: BranchDto): BranchId? {
            val result = if (branch.master) {
                null
            } else {
                branch.id.toMongoID()
            }
            setCurrentBranchId(bomNodeId, result)
            return result
        }

        fun getCurrentBranchId(bomNodeId: BomNodeId): BranchId? {
            return _currentBranchId[bomNodeId]
        }
    }

    data class Result(val snapshot: BomNodeSnapshot, val entity: ManufacturingEntity, val accessCheck: AccessCheck)

    /** Creates the bom tree specified by [calculationBuilder]
     *
     * @return recursive snapshot tree, recursive entity tree, access-check.
     * */
    fun build(
        calculationBuilder: CalculationBuilder,
        accessCheck: AccessCheck
    ): Mono<Result> {
        return createCalculation(accessCheck, calculationBuilder, CreationContext())
            .flatMap { snapshot ->
                when (snapshot.isMainBranch()) {
                    true -> Mono.just(snapshot)
                    false -> bomPublishService.publish(
                        accessCheck,
                        snapshot.bomNodeId(),
                        snapshot.branchId(),
                        DirtyStateHandling.NONE
                    )
                        .map { it.publishedNode }
                }.flatMap { loadAndConvertSnapshotRecursive(accessCheck, it) }
                    .map { (rootSnapshot, rootEntity) -> Result(rootSnapshot, rootEntity, accessCheck) }
            }
    }

    fun loadAndConvertSnapshotRecursive(
        accessCheck: AccessCheck,
        snapshot: BomNodeSnapshot
    ): Mono<Pair<BomNodeSnapshot, ManufacturingEntity>> {
        return bomNodeService.getNodesRecursive(accessCheck, loadingMode = LoadingMode.DIRECT, nodeId = snapshot.bomNodeId(), snapshot.branchId())
            .map { snapshotRecursive ->
                snapshotRecursive to
                    bomNodeConversionService.bomNodeToManufacturingCalculationTree(snapshotRecursive, null)
            }
    }

    private fun createEntities(
        accessCheck: AccessCheck,
        projectId: BomradsProjectId,
        bomNodeId: BomNodeId,
        entityBuilders: List<EntityBuilder>,
        context: CreationContext
    ): Mono<BomNodeSnapshot> {
        return Flux.fromIterable(entityBuilders).expand { entityBuilder ->
            Mono.just(entityBuilder).flatMap { entityBuilder ->
                val currentBranchId = context.getCurrentBranchId(bomNodeId)
                entityBuilder.withBranch(currentBranchId)
                entityCreationService.createAndCalculate(
                    accessCheck,
                    entityBuilder.entityCreationData
                ).map {
                    context.setCurrentBranchId(bomNodeId, it.branch)
                    it
                }
            }.flatMapMany { bomNode ->
                val currentBomNodeId = BomNodeId(bomNode.id)
                val currentBranchId = bomNode.branch.id.toMongoID()

                val createdEntityId = bomNode.findInTree { createdEntity ->
                    createdEntity.type == entityBuilder.type &&
                        createdEntity.name == entityBuilder.name
                }
                    ?.id
                    ?.let { ObjectId(it) }
                    ?: error("could not find created entity: ${entityBuilder.name}")

                val children = entityBuilder.children.map { child ->
                    if (child.hasCustomParentSelector) {
                        val customParentId = child.customParentSelector(bomNode)
                        child.withParent(projectId, currentBomNodeId, currentBranchId, customParentId)
                    } else {
                        child.withParent(projectId, currentBomNodeId, currentBranchId, createdEntityId)
                    }
                }

                entityBuilder.subs.map { sub ->
                    if (sub.hasCustomParentSelector) {
                        val customParentId = sub.customParentSelector(bomNode)
                        sub.withParent(projectId, currentBomNodeId, currentBranchId, customParentId)
                    } else {
                        sub.withParent(projectId, currentBomNodeId, currentBranchId, createdEntityId)
                    }
                }
                // What's going on???
                Flux.fromIterable(children)
            }
            // TODO: revisit whether and what to return for this intermediate call
        }.then {
            bomNodeService.getBomNode(
                accessCheck,
                nodeId = bomNodeId,
                branch = context.getCurrentBranchId(bomNodeId)
            )
        }
    }

    data class BomNodeWithManufacturing(val bomNodeId: BomNodeId, val manufacturingId: ObjectId)

    private fun createCalculation(
        accessCheck: AccessCheck,
        calculationBuilder: CalculationBuilder,
        context: CreationContext
    ): Mono<BomNodeSnapshot> {
        logger.info("create calculation: $calculationBuilder")

        return when (calculationBuilder.isDefaultProject) {
            true -> Mono.just(calculationBuilder.defaultProjectParameters)
            false -> projectService.getProject(accessCheck, calculationBuilder.projectId.toMongoProjectId()).map { it.id to it.key }
        }.map { (projectId, _) ->
            Pair(accessCheck, projectId)
        }.flatMap { (accessCheck, projectId) ->
            calculationBuilder
                .withProject(projectId)

            val command = if (calculationBuilder.isRoot) {
                calculationBuilder.calculationCreateRootInput
            } else {
                val currentBranch = context.getCurrentBranchId(calculationBuilder.parentBomNodeId)
                calculationBuilder.withParentBranch(currentBranch)
                calculationBuilder.calculationCreateSubInput
            }

            logger.info("command to be dispatched: $command")
            calculationUpdaterModule.dispatch(accessCheck, command)
                .flatMap { createdCalculation ->

                    val bomNodeId = calculationBuilder.findNewBomNodeId(createdCalculation)
                    if (!calculationBuilder.isRoot) {
                        bomPublishService.publish(
                            accessCheck,
                            BomNodeId(createdCalculation.bomNode.id),
                            createdCalculation.bomNode.branch.id.toMongoID(),
                            DirtyStateHandling.NONE
                        ).flatMap {
                            bomNodeService.getBomNode(accessCheck, nodeId = bomNodeId, branch = null).map {
                                val manufId = it.manufacturing?._id ?: error("no manufacturing found")
                                BomNodeWithManufacturing(it.bomNodeId(), manufId)
                            }
                        }
                    } else {
                        val manufId = createdCalculation.bomNode.manufacturing?.id?.toMongoID() ?: error("no manufacturing found")
                        Mono.just(BomNodeWithManufacturing(bomNodeId, manufId))
                    }.flatMap { info ->
                        val (bomNodeId, manufacturingId) = info
                        context.setCurrentBranchId(bomNodeId, null)

                        logger.info("calculation created: $bomNodeId - manufacturingId: $manufacturingId")
                        val entities = calculationBuilder.children.map { entity ->
                            entity.withParent(projectId, bomNodeId, null, manufacturingId)
                        }
                        if (entities.isNotEmpty()) {
                            createEntities(accessCheck, projectId, bomNodeId, entities, context).flatMap {
                                logger.info("publish $bomNodeId on ${it.bomradBranchId()}")
                                context.setCurrentBranchId(bomNodeId, null)
                                bomPublishService.publish(
                                    accessCheck,
                                    bomNodeId,
                                    it.bomradBranchId()!!.toMongoBranchId(),
                                    DirtyStateHandling.NONE
                                ).thenReturn(info)
                            }
                        } else {
                            Mono.just(info)
                        }
                    }.flatMap { info ->
                        val (bomNodeId, manufacturingId) = info

                        val subs = calculationBuilder.subs.map { sub ->
                            sub.withParent(projectId, bomNodeId, branchId = null, manufacturingId)
                        }
                        val allSubs = subs + calculationBuilder.collectAllSubs()
                        logger.info("creating ${allSubs.size} child for $command")
                        Flux.fromIterable(allSubs)
                            .flatMap { sub ->
                                logger.info("sub $sub creation")
                                createCalculation(accessCheck, sub, context)
                            }.collectList().then {
                                logger.info("finished sub creation for $command")
                                bomNodeService.getBomNode(
                                    accessCheck,
                                    nodeId = bomNodeId,
                                    branch = null
                                )
                            }
                    }
                }
        }
    }
}

private val logger = LoggerFactory.getLogger(CalculationBuilderService::class.java)
