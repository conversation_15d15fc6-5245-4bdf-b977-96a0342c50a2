package com.nu.bom.core.manufacturing.service

import com.nu.bom.core.api.dtos.BomNodeDto
import com.nu.bom.core.manufacturing.testentities.TestManufacturing
import com.nu.bom.core.manufacturing.testentities.TestManufacturingStep
import com.nu.bom.core.manufacturing.testentities.TestManufacturingWithSub
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.projectId
import com.nu.bom.core.service.BomNodeService
import com.nu.bom.core.service.ManufacturingCreationService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.user.withAccessCheck
import com.nu.bom.core.utils.AccountTestUtil
import com.nu.bom.core.utils.LoadTestEntityClasses
import com.nu.bom.core.utils.toObjectId
import com.nu.bomrads.dto.ProjectCreationDTO
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
@LoadTestEntityClasses(
    classes = [TestManufacturing::class, TestManufacturingWithSub::class, TestManufacturingStep::class],
    packages = ["com.nu.bom.core.manufacturing.testentities.multicurrency"],
)
class ManufacturingCreationServiceTest {
    @Autowired
    private lateinit var manufacturingCreationService: ManufacturingCreationService

    @Autowired
    private lateinit var bomNodeService: BomNodeService

    @Autowired
    private lateinit var accountUtil: AccountTestUtil

    private lateinit var accessCheck: AccessCheck
    private lateinit var project: ProjectCreationDTO

    @BeforeEach
    fun setup() {
        val (ac, p) = accountUtil.setupWithProject(name = this::class.simpleName!!, key = "MCST")

        accessCheck = ac
        project = p
    }

    @AfterEach
    fun teardown() {
        accountUtil.cleanup()
    }

    @Test
    fun createManufacturing() {
        val fields = null

        val res =
            manufacturingCreationService.create(
                accessCheck = accessCheck,
                year = 2019,
                projectId = project.projectId(),
                name = "test",
                type = TestManufacturing::class,
                title = "TestTitle",
                fields = fields,
                args = mapOf("isPart" to true),
            ).block()!!.result

        val node = getNode(accessCheck, BomNodeId(res.bomNode.id))

        assertInitialMainBranch(node)
    }

    @Test
    fun createManufacturingWithSub() {
        val fields = null

        val res =
            manufacturingCreationService.create(
                accessCheck = accessCheck,
                year = 2019,
                projectId = project.projectId(),
                name = "test",
                type = TestManufacturingWithSub::class,
                title = "SubTitle",
                fields = fields,
                args = mapOf("isPart" to true),
            ).withAccessCheck(accessCheck).block()!!.result

        val node = getNode(accessCheck, BomNodeId(res.bomNode.id))
        val subNode = getSubNode(accessCheck, res.bomNode)

        assertInitialMainBranch(node)
        assertInitialMainBranch(subNode)
    }

    private fun getSubNode(
        accessCheck: AccessCheck,
        bomNode: BomNodeDto,
    ): BomNodeSnapshot {
        assertThat(bomNode.subNodes.size).isEqualTo(1)

        val subNodeId = bomNode.subNodes.single().bomNodeId.toObjectId()!!

        return getNode(accessCheck, subNodeId)
    }

    private fun getNode(
        accessCheck: AccessCheck,
        bomNodeId: BomNodeId,
    ): BomNodeSnapshot {
        return bomNodeService.getBomNode(accessCheck, nodeId = bomNodeId, branch = null).block()!!
    }

    private fun assertInitialMainBranch(node: BomNodeSnapshot) {
        // assert BomNode + initial branch setup is correct and consistent
        Assertions.assertNotNull(node.originalRootSource, "originalRootSource")
        Assertions.assertNotNull(node.originalSource, "originalSource")

        Assertions.assertNotNull(node.bomradChangesetId(), "originalSource")

        // assert Branch is Main branch
        Assertions.assertEquals(true, node.bomradMainBranch(), "branch is a main branch")
    }
}
