package com.nu.bom.core.integrationtests

import com.nu.bom.core.api.dtos.AddMasterDataDto
import com.nu.bom.core.api.dtos.BomNodeDto
import com.nu.bom.core.api.dtos.EntityCreationDto
import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.api.dtos.ManufacturingDto
import com.nu.bom.core.api.dtos.MasterDataCompositeKey
import com.nu.bom.core.api.dtos.MasterDataForEditDto
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.FieldBasedUnits
import com.nu.bom.core.manufacturing.entities.BaseMaterial
import com.nu.bom.core.manufacturing.entities.ComponentMaterial
import com.nu.bom.core.manufacturing.entities.Consumable
import com.nu.bom.core.manufacturing.entities.Machine
import com.nu.bom.core.manufacturing.entities.ManualMaterialV2
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.Material
import com.nu.bom.core.manufacturing.entities.RawMaterialBar
import com.nu.bom.core.manufacturing.entities.RawMaterialCastingAlloy
import com.nu.bom.core.manufacturing.entities.RawMaterialPipe
import com.nu.bom.core.manufacturing.entities.RawMaterialPlasticGranulate
import com.nu.bom.core.manufacturing.entities.RawMaterialPowder
import com.nu.bom.core.manufacturing.entities.RawMaterialRareEarth
import com.nu.bom.core.manufacturing.entities.RawMaterialSand
import com.nu.bom.core.manufacturing.entities.RawMaterialWax
import com.nu.bom.core.manufacturing.entities.RawMaterialWireRod
import com.nu.bom.core.manufacturing.entities.masterdata.MasterdataEntity
import com.nu.bom.core.manufacturing.entities.masterdata.MasterdataParentEntity
import com.nu.bom.core.manufacturing.entities.masterdata.exchangerates.MasterdataExchangeRateEntity
import com.nu.bom.core.manufacturing.entities.masterdata.material.MaterialConsumerExtension
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.ExchangeRatesField
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.MdTableConfigColumnData
import com.nu.bom.core.manufacturing.fieldTypes.MdTableConfigFieldData
import com.nu.bom.core.manufacturing.fieldTypes.NumericFieldResultWithUnit
import com.nu.bom.core.manufacturing.fieldTypes.ORIGINAL_BASE_CURRENCY
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.utils.FieldExtractionUtils.Companion.MASTERDATA_CURRENCY_FIELD_NAME
import com.nu.bom.core.manufacturing.utils.TestFieldFiller
import com.nu.bom.core.model.MergeSourceType
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.mongoProjectId
import com.nu.bom.core.service.configurations.MasterdataTsetConfigurationService
import com.nu.bom.core.service.masterdata.MdCurrencyService
import com.nu.bom.core.service.masterdata.MdDetailCrudService
import com.nu.bom.core.service.masterdata.MdHeaderCrudService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.NbkClient
import com.nu.bom.core.utils.findInTree
import com.nu.bom.core.utils.then
import com.nu.bom.core.utils.toUpperCamelCase
import com.nu.bom.core.utils.visitTree
import com.nu.bom.tests.docker.MasterdataUpdateTest
import com.nu.masterdata.dto.v1.basicdata.CurrencyDto
import com.nu.masterdata.dto.v1.detail.CurrencyMeasurementDto
import com.nu.masterdata.dto.v1.detail.DetailDto
import com.nu.masterdata.dto.v1.detail.NumericDetailValueDto
import com.nu.masterdata.dto.v1.header.HeaderDto
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import com.nu.masterdata.dto.v1.schema.CurrencyTypeDto
import com.nu.masterdata.dto.v1.schema.NumericDetailValueSchemaDto
import com.nu.masterdata.dto.v1.schema.NumericFieldSchemaDto
import com.nu.masterdata.dto.v1.schema.UnitOfMeasurementTypeDto
import com.tset.bom.clients.common.DenominatorBehavior
import com.tset.bom.clients.common.DenominatorUnit
import com.tset.bom.clients.nuledge.FieldParameterDto
import com.tset.core.service.domain.Currency
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import reactor.core.publisher.Mono
import kotlin.reflect.KClass

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@MasterdataUpdateTest
@ActiveProfiles("test", "masterdata-init", "lookup-init", "config-init")
@AutoConfigureWebTestClient
class MasterDataCalculationIT
    @Autowired
    constructor(
        override val nbkClient: NbkClient,
        private val fieldFiller: TestFieldFiller,
        private val currencyService: MdCurrencyService,
        private val headerService: MdHeaderCrudService,
        private val detailCrudService: MdDetailCrudService,
    ) : NbkClient.With {
        private lateinit var accessCheck: AccessCheck
        private lateinit var projectId: ProjectId

        @BeforeEach
        fun setup() {
            val (ac, project) = nbkClient.setupWithProject(name = this::class.simpleName!!, key = "MDCI")

            accessCheck = ac
            projectId = project.mongoProjectId()

            fieldFiller.init()
        }

        @AfterEach
        fun teardown() {
            fieldFiller.reset()

            nbkClient.cleanup()
        }

        /**
         * Updates currencies via masterdata.
         *
         * 1 - Create calculation.
         * 2 - Update currencies.
         * 3 - Check that masterdata update is necessary.
         * 4 - Recalc with updated masterdata.
         * 5 - Check that masterdata update is no longer necessary.
         */
        @Test
        fun updateCurrencies() {
            // 1 - create std calculation
            var bomNode = wizardBuilder.createStandard().bomNode

            // 2 - add new currency and exchange rate (as tset masterdata)
            addEth().block()

            // assert pending masterdata update
            bomNode = manufacturingCrudTestClient.getBomNode(bomNode.id, bomNode.branch.id, showOpenMerges = true)!!
            assertThat(bomNode.openMergesAvailable).singleElement().satisfies({ mergeSourceType ->
                assertThat(mergeSourceType).isEqualTo(MergeSourceType.MASTERDATA)
            })

            // update masterdata (new currency, new exchange rate)
            bomNode = manufacturingCrudTestClient.updateMasterData(bomNode)
            bomNode = manufacturingCrudTestClient.getBomNode(bomNode.id, bomNode.branch.id, showOpenMerges = true)!!

            // assert new currency and exchange rate has been propagated
            val exchangeRateFieldsData: List<Triple<ManufacturingDto, FieldParameter, Map<String, Double>>> =
                bomNode.manufacturing!!.visitTree(
                    func = { entity, _ ->
                        val exchangeRatesField = entity.fields.find { it.type == ExchangeRatesField::class.simpleName }
                        if (exchangeRatesField != null) {
                            Triple(entity, exchangeRatesField, exchangeRatesField.value as Map<String, Double>)
                        } else {
                            null
                        }
                    },
                    children = { it.children },
                )

            assertThat(exchangeRateFieldsData).isNotEmpty.allSatisfy { exchangeRateFieldData ->
                val entityName = exchangeRateFieldData.first.name
                val fieldName = exchangeRateFieldData.second.name
                val currencySet = exchangeRateFieldData.third.keys
                assertThat(currencySet)
                    .contains("ETH")
                    .withFailMessage { "entity=$entityName field=$fieldName is missing new exchange rate ETH" }
            }

            checkMdExchangeRates(bomNode)
        }

        private fun checkMdExchangeRates(bomNode: BomNodeDto) {
            val mdParent =
                bomNode.manufacturing
                    ?.children
                    ?.first { it.type == Entities.MD_EXCHANGERATE_PARENT }

            checkTableConfigField(mdParent)
            checkRateFieldUnitMetadata(mdParent)
        }

        @Suppress("UNCHECKED_CAST")
        private fun checkRateFieldUnitMetadata(mdParent: ManufacturingDto?) {
            val children = mdParent?.children?.filter { it.type == Entities.MD_EXCHANGERATE } ?: emptyList()
            assertThat(children).isNotEmpty
            assertThat(children).allSatisfy {
                val rate = it.getField(MasterdataExchangeRateEntity::rate.name)
                val unitFieldValue = it.getField(MasterdataExchangeRateEntity::numeratorCurrency.name).value
                assertThat(rate).isNotNull
                assertThat(rate.unit).isEqualTo(unitFieldValue)
                assertThat(rate.denominatorUnit).isEqualTo(
                    DenominatorUnit(
                        unit = it.getField(MasterdataExchangeRateEntity::denominatorCurrency.name).value?.toString()!!,
                        type = FieldBasedUnits.DENOMINATOR_UNIT_TYPE_MONEY,
                        behavior = DenominatorBehavior.WRITE_THROUGH,
                    ),
                )
                val defaultUnitMeta = rate.metaInfo!!.getValue(DefaultUnit.META_INFO) as Map<String, Any>
                assertThat(defaultUnitMeta.getValue("unit")).isEqualTo(unitFieldValue)
            }
        }

        @Suppress("UNCHECKED_CAST")
        private fun checkTableConfigField(mdParent: ManufacturingDto?) {
            val tableConfigField =
                mdParent
                    ?.getField(MasterdataParentEntity::mdTableConfig.name)
            assertThat(tableConfigField).isNotNull
            val columns =
                (tableConfigField?.value as Map<String, Any>).getValue(
                    MdTableConfigFieldData::columns.name,
                ) as List<Map<String, Any>>

            fun findColumn(fieldName: String): Map<String, Any> =
                columns.first { it.getValue(MdTableConfigColumnData::fieldName.name) == fieldName }

            val designation = findColumn(MasterdataExchangeRateEntity::displayDesignation.name)
            assertThat(designation.getValue("alias")).isEqualTo("Exchange Rates")
            assertThat(designation.getValue("editable")).isEqualTo(false)

            val rate = findColumn(MasterdataExchangeRateEntity::rate.name)
            assertThat(rate.getValue("alias")).isEqualTo("Value")
            assertThat(rate.getValue("editable")).isEqualTo(true)

            val validFromActual = findColumn("#actual_effectivity_validFromDateField")
            assertThat(validFromActual.getValue("alias")).isEqualTo("Actual - Valid from date")
            assertThat(validFromActual.getValue("displayDesignationFieldName")).isEqualTo("#actual_effectivity_validFromDateField")

            val validFromRequested = findColumn("#request_effectivity_validFromDateField")
            assertThat(validFromRequested.getValue("alias")).isEqualTo("Requested - Valid from date")
            assertThat(validFromRequested.getValue("displayDesignationFieldName")).isEqualTo("#request_effectivity_validFromDateField")

            val modifier = findColumn(MasterdataEntity::mdDetailModifier.name)
            assertThat(modifier.getValue("alias")).isEqualTo("Modifier")
            assertThat(modifier.getValue("displayDesignationFieldName")).isNull()
        }

        private fun addEth(): Mono<Void> {
            val ccy = CurrencyDto(SimpleKeyDto("ETH"), "Ethereum", "ETH")
            val headerType = SimpleKeyDto(MasterdataTsetConfigurationService.EXCHANGE_RATE_HEADER_TYPE_KEY)
            val ethRate = 1.0 / 4000
            val ethHeader =
                HeaderDto(
                    ccy.key,
                    ccy.name,
                    headerType,
                    true,
                    NumericDetailValueSchemaDto(
                        NumericFieldSchemaDto(
                            UnitOfMeasurementTypeDto(
                                CurrencyTypeDto(ccy.key),
                                CurrencyTypeDto(SimpleKeyDto("EUR")),
                            ),
                        ),
                    ),
                )

            return currencyService
                .getCurrencies(accessCheck)
                .flatMap {
                    val currencyMap = it.ccyMap[Currency("ETH")]
                    currencyMap?.let { Mono.just(it) } ?: currencyService.createCurrency(accessCheck, ccy)
                }.then {
                    headerService
                        .createHeader(
                            accessCheck,
                            ethHeader,
                        ).onErrorResume {
                            println("Header already exists ${MasterdataTsetConfigurationService.EXCHANGE_RATE_HEADER_TYPE_KEY}")
                            Mono.just(ethHeader)
                        }
                }.then {
                    val value =
                        NumericDetailValueDto(
                            ethRate,
                            CurrencyMeasurementDto(ccy.key),
                            CurrencyMeasurementDto(SimpleKeyDto("EUR")),
                        )
                    detailCrudService.postDetailEntries(
                        accessCheck,
                        headerType,
                        listOf(DetailDto(emptyMap(), ccy.key, value, true)),
                    )
                }.then()
        }

        private fun testCreateEntityFromNewMasterData(
            type: MasterDataType,
            entityType: Entities,
            expectedClass: KClass<out ManufacturingEntity>,
            mdCompositeKey: MasterDataCompositeKey,
        ) = testCreateEntityFromMasterData(
            type = type,
            entityType = entityType,
            expectedClass = expectedClass,
            mdCompositeKey,
        )

        private fun testCreateEntityFromMasterData(
            type: MasterDataType,
            entityType: Entities,
            expectedClass: KClass<out ManufacturingEntity>,
            mdCompositeKey: MasterDataCompositeKey,
        ) {
            val (masterData, displayDesignation) =
                if (entityType == Entities.CONSUMABLE) {
                    // we use here Water from tset reference data
                    null to "Water"
                } else if (entityType == Entities.MATERIAL) {
                    null to "Dy (China)"
                } else {
                    // use generated display name
                    val displayDesignation = type.name.toUpperCamelCase() + "1"

                    // create initial masterdata entry
                    val tmpMasterData = createNewMasterData(type = type, displayDesignation = displayDesignation)
                    tmpMasterData to displayDesignation
                }

            // 1 - create std calculation
            val (node, step) =
                wizardBuilder
                    .createStandard()
                    .bomNode
                    .let {
                        manufacturingCrudTestClient.addStep(it, name = "Step1", initialCycleTime = Time(1.0, TimeUnits.SECOND))
                    }.let {
                        manufacturingCrudTestClient.publish(it)
                    }.let { node ->
                        val stepId = node.findInTree { entity -> entity.name == "Step1" }!!
                        node to stepId
                    }

            val fields =
                if (entityType == Entities.CONSUMABLE) {
                    // for new master data we do not send fields because they would overwrite the lookup values
                    listOf(
                        FieldParameterDto(
                            name = MaterialConsumerExtension::headerKey.name,
                            type = "Text",
                            value = mdCompositeKey.key,
                            denominatorUnit = null,
                        ),
                        FieldParameterDto(
                            name = Consumable::quantity.name,
                            type = "QuantityUnit",
                            value = 1,
                            denominatorUnit = null,
                        ),
                    )
                } else if (entityType == Entities.MATERIAL) {
                    listOf(
                        FieldParameterDto(
                            name = MaterialConsumerExtension::headerKey.name,
                            type = "Text",
                            value = mdCompositeKey.key,
                            denominatorUnit = null,
                        ),
                        FieldParameterDto(
                            name = RawMaterialRareEarth::designation.name,
                            type = "Text",
                            value = "Dy (China)",
                            denominatorUnit = null,
                        ),
                        FieldParameterDto(
                            name = BaseMaterial::displayDesignation.name,
                            type = "Text",
                            value = "Dy (China)",
                            denominatorUnit = null,
                        ),
                        FieldParameterDto(
                            name = Material::netWeightPerPart.name,
                            type = "Weight",
                            value = 1,
                            unit = "KILOGRAM",
                            denominatorUnit = null,
                        ),
                        FieldParameterDto(
                            name = Material::scrapWeightPerPart.name,
                            type = "Weight",
                            value = 1,
                            unit = "KILOGRAM",
                            denominatorUnit = null,
                        ),
                    )
                } else {
                    getAndFillMandatoryEntityFields(
                        bomNodeId = node.id,
                        branchId = node.branch.id,
                        entityType = entityType,
                        composite = mdCompositeKey,
                    )
                }

            val entityName = mdCompositeKey.key
            val updatedNode =
                manufacturingCrudTestClient.addEntity(
                    node = node,
                    parentId = step.id,
                    name = null,
                    entityType = entityType,
                    masterDataKey = mdCompositeKey,
                    fields = fields,
                )

            // assert created entity
            assertThat(updatedNode.findInTree { it.name == "Step1" }!!).satisfies({ updatedStep ->
                assertThat(updatedStep.children.find { it.name == entityName }!!).satisfies({ entity ->

                    assertThat(entity.type).isEqualTo(entityType)
                    assertThat(entity.className).isEqualTo(expectedClass.simpleName!!)
                    assertThat(entity.name).isEqualTo(entityName)

                    assertThat(entity.fields.getFieldValue("displayDesignation")).isEqualTo(displayDesignation)

                    // assert masterdata of created entity
                    entity.masterDataKey?.let {
                        assertThat(entity.masterDataKey!!).satisfies({ masterDataKey ->

                            assertThat(masterDataKey.key).isEqualTo(mdCompositeKey.key)
                            assertThat(masterDataKey.type.toString()).isEqualTo(mdCompositeKey.type)
                            assertThat(masterDataKey.year).isEqualTo(mdCompositeKey.year)
                            assertThat(masterDataKey.location).isEqualTo(mdCompositeKey.location)

                            Assertions.assertNull(masterDataKey.latestVersion, "no-master data merge checks ...")
                        })
                    }
                    masterData?.let {
                        assertThat(entity.fields.map { it.name }).containsAll(
                            masterData.fields
                                .filter { it.name != ORIGINAL_BASE_CURRENCY }
                                .map { it.name },
                        )
                    }
                })
            })

            // assert costPerPart value could be calculated
            assertThat(updatedNode.getField("costPerPart")).isNotNull
        }

        @Test
        fun createMachine() {
            // use generated display name
            val displayDesignation = MasterDataType.MACHINE.name.toUpperCamelCase() + "1"
            // create initial masterdata entry
            val tmpMasterData = createNewMasterData(type = MasterDataType.MACHINE, displayDesignation = displayDesignation)
            testCreateEntityFromNewMasterData(
                type = MasterDataType.MACHINE,
                entityType = Entities.MACHINE,
                expectedClass = Machine::class,
                mdCompositeKey = tmpMasterData.current.composite,
            )
        }

        @Test
        fun createConsumable() {
            testCreateEntityFromNewMasterData(
                type = MasterDataType.CONSUMABLE,
                entityType = Entities.CONSUMABLE,
                expectedClass = Consumable::class,
                mdCompositeKey =
                    MasterDataCompositeKey(
                        type = MasterDataType.CONSUMABLE.name,
                        key = "Water",
                        year = null,
                        location = null,
                    ),
            )
        }

        @Test
        fun createConsumableWithClassification() {
            testCreateEntityFromNewMasterData(
                type = MasterDataType.NONE,
                entityType = Entities.CONSUMABLE,
                expectedClass = Consumable::class,
                mdCompositeKey =
                    MasterDataCompositeKey(
                        type = MasterDataType.NONE.name,
                        key = "Water",
                        year = null,
                        location = null,
                        mdClassificationKey = "tset.ref.classification.consumable",
                    ),
            )
        }

        @Test
        fun createRawMaterialBar() {
            testCreateEntityFromNewMasterData(
                type = MasterDataType.RAW_MATERIAL_BAR,
                entityType = Entities.MATERIAL,
                expectedClass = RawMaterialBar::class,
                mdCompositeKey =
                    MasterDataCompositeKey(
                        type = MasterDataType.RAW_MATERIAL_BAR.name,
                        key = "100Cr6-RAW_MATERIAL_BAR",
                        year = null,
                        location = null,
                    ),
            )
        }

        @Test
        fun createRawMaterialCastingAlloy() {
            testCreateEntityFromNewMasterData(
                type = MasterDataType.RAW_MATERIAL_CASTING_ALLOY,
                entityType = Entities.MATERIAL,
                expectedClass = RawMaterialCastingAlloy::class,
                mdCompositeKey =
                    MasterDataCompositeKey(
                        type = MasterDataType.RAW_MATERIAL_CASTING_ALLOY.name,
                        key = "14 NiCr 14-RAW_MATERIAL_CASTING_ALLOY",
                        year = null,
                        location = null,
                    ),
            )
        }

        @Test
        fun createRawMaterialWireRod() {
            testCreateEntityFromNewMasterData(
                type = MasterDataType.RAW_MATERIAL_WIRE_ROD,
                entityType = Entities.MATERIAL,
                expectedClass = RawMaterialWireRod::class,
                mdCompositeKey =
                    MasterDataCompositeKey(
                        type = MasterDataType.RAW_MATERIAL_WIRE_ROD.name,
                        key = "100Cr6 - softannealed-RAW_MATERIAL_WIRE_ROD",
                        year = null,
                        location = null,
                    ),
            )
        }

        @Test
        fun createRawMaterialPipe() {
            testCreateEntityFromNewMasterData(
                type = MasterDataType.RAW_MATERIAL_PIPE,
                entityType = Entities.MATERIAL,
                expectedClass = RawMaterialPipe::class,
                mdCompositeKey =
                    MasterDataCompositeKey(
                        type = MasterDataType.RAW_MATERIAL_PIPE.name,
                        key = "100Cr6-RAW_MATERIAL_PIPE",
                        year = null,
                        location = null,
                    ),
            )
        }

        @Test
        fun createRawMaterialPlasticGranulate() {
            testCreateEntityFromNewMasterData(
                type = MasterDataType.RAW_MATERIAL_PLASTIC_GRANULATE,
                entityType = Entities.MATERIAL,
                expectedClass = RawMaterialPlasticGranulate::class,
                mdCompositeKey =
                    MasterDataCompositeKey(
                        type = MasterDataType.RAW_MATERIAL_PLASTIC_GRANULATE.name,
                        key = "ABS-RAW_MATERIAL_PLASTIC_GRANULATE",
                        year = null,
                        location = null,
                    ),
            )
        }

        @Test
        fun createRawMaterialPowder() {
            testCreateEntityFromNewMasterData(
                type = MasterDataType.RAW_MATERIAL_POWDER,
                entityType = Entities.MATERIAL,
                expectedClass = RawMaterialPowder::class,
                mdCompositeKey =
                    MasterDataCompositeKey(
                        type = MasterDataType.RAW_MATERIAL_POWDER.name,
                        key = "Sint-C00 - mixed-RAW_MATERIAL_POWDER",
                        year = null,
                        location = null,
                    ),
            )
        }

        @Test
        fun createRawMaterialSand() {
            testCreateEntityFromNewMasterData(
                type = MasterDataType.RAW_MATERIAL_SAND,
                entityType = Entities.MATERIAL,
                expectedClass = RawMaterialSand::class,
                mdCompositeKey =
                    MasterDataCompositeKey(
                        type = MasterDataType.RAW_MATERIAL_SAND.name,
                        key = "Bauxit-RAW_MATERIAL_SAND",
                        year = null,
                        location = null,
                    ),
            )
        }

        @Test
        fun createRawMaterialWax() {
            testCreateEntityFromNewMasterData(
                type = MasterDataType.RAW_MATERIAL_WAX,
                entityType = Entities.MATERIAL,
                expectedClass = RawMaterialWax::class,
                mdCompositeKey =
                    MasterDataCompositeKey(
                        type = MasterDataType.RAW_MATERIAL_WAX.name,
                        key = "Wax (new)-RAW_MATERIAL_WAX",
                        year = null,
                        location = null,
                    ),
            )
        }

        @Test
        fun createRawMaterialMagnet() {
            testCreateEntityFromNewMasterData(
                type = MasterDataType.RAW_MATERIAL_RARE_EARTH,
                entityType = Entities.MATERIAL,
                expectedClass = RawMaterialRareEarth::class,
                mdCompositeKey =
                    MasterDataCompositeKey(
                        type = MasterDataType.RAW_MATERIAL_RARE_EARTH.name,
                        key = "100Cr6-RAW_MATERIAL_PIPE",
                        year = null,
                        location = null,
                    ),
            )
        }

        @Test
        fun createComponentMaterial() {
            // CompositeMaterial

            // 1 - create std calculation
            val (node, step) =
                wizardBuilder
                    .createStandard()
                    .bomNode
                    .let {
                        manufacturingCrudTestClient.addStep(it, name = "Step1", initialCycleTime = Time(1.0, TimeUnits.SECOND))
                    }.let {
                        manufacturingCrudTestClient.publish(it)
                    }.let { node ->
                        val stepId = node.findInTree { entity -> entity.name == "Step1" }!!
                        node to stepId
                    }

            val compositeMaterialResult =
                manufacturingCrudTestClient.addEntity(
                    node = node,
                    parentId = step.id,
                    name = null,
                    entityType = Entities.MATERIAL,
                    masterDataKey = null,
                    fields = compositeMaterialFields(),
                    entityClass = ManualMaterialV2::class.simpleName,
                )

            val compositMaterialEntity =
                compositeMaterialResult.manufacturing?.findInTree(
                    predicate = { it.name == "TestComposite" },
                    children = { it.children },
                )!!

            val componentMaterialName = "componentMaterialBentonite"
            val componentMaterial =
                manufacturingCrudTestClient.addEntity(
                    node = compositeMaterialResult,
                    parentId = compositMaterialEntity.id,
                    name = componentMaterialName,
                    entityType = Entities.COMPONENT_MATERIAL,
                    masterDataKey = null,
                    fields = componentMaterialFields(),
                    entityClass = ComponentMaterial::class.simpleName,
                )

            val componentMaterialEntity =
                componentMaterial.manufacturing?.findInTree(
                    predicate = { it.name == componentMaterialName },
                    children = { it.children },
                )!!

            assertThat(componentMaterialEntity).isNotNull

            val masterdataDisplayName = componentMaterialEntity.getField(MaterialConsumerExtension::materialBaseDisplayDesignation.name)
            assertThat(masterdataDisplayName.value).isEqualTo("Bentonite")

            // assert costPerPart value could be calculated
            assertThat(componentMaterial.getField("costPerPart")).isNotNull
            println(componentMaterial)
        }

        private fun compositeMaterialFields(): List<FieldParameterDto> =
            listOf(
                createFieldParameterDto("displayDesignation", "Text", "TestComposite"),
                createFieldParameterDto("dimension", "Dimension", "MASS"),
                createFieldParameterDto("weightMode", "WeightCalculationMode", "GROSS_WEIGHT"),
                createFieldParameterDto("netWeightPerPart", "Weight", 1, "KILOGRAM"),
                createFieldParameterDto("scrapWeightPerPart", "Weight", 1, "KILOGRAM"),
                createFieldParameterDto("materialBasePrice", "Money", 4),
                createFieldParameterDto("materialBaseCO2", "Emission", 1, "KILOGRAM_CO2E"),
            )

        private fun componentMaterialFields(): List<FieldParameterDto> =
            listOf(
                createFieldParameterDto(MaterialConsumerExtension::headerKey.name, "Text", "Bentonite"),
                createFieldParameterDto(ComponentMaterial::ratio.name, "Rate", 0.8),
            )

        private fun createFieldParameterDto(
            name: String,
            type: String,
            value: Any?,
            unit: String? = null,
        ): FieldParameterDto =
            FieldParameterDto(
                name = name,
                type = type,
                value = value,
                unit = unit,
                source = "I",
                metaInfo = null,
                denominatorUnit = null,
                currencyIsoCode = null,
            )

        @Test
        fun testGetModularized() {
            // 1 - create std calculation
            val (node, step) =
                wizardBuilder
                    .createStandard()
                    .bomNode
                    .let {
                        manufacturingCrudTestClient.addStep(it, name = "Step1", initialCycleTime = Time(1.0, TimeUnits.SECOND))
                    }.let {
                        manufacturingCrudTestClient.publish(it)
                    }.let { node ->
                        val stepId = node.findInTree { entity -> entity.name == "Step1" }!!
                        node to stepId
                    }

            val fields =
                getMandatoryModularizedEntityFields(
                    bomNodeId = node.id,
                    branchId = node.branch.id,
                    composite =
                        MasterDataCompositeKey(
                            type = MasterDataType.RAW_MATERIAL_PLASTIC_GRANULATE.name,
                            key = "ABS-RAW_MATERIAL_PLASTIC_GRANULATE",
                            year = null,
                            location = null,
                            mdClassificationKey = "tset.ref.classification.plastic-granulate",
                        ),
                )

            assertThat(fields).isNotNull
        }

        // this test will fail once RAW_MATERIAL_WAX becomes modularized. Remove it or replace WAX win a non modularized material
        // once all materials have been modularized, remove completely.
        @Test
        fun testGetModularizedForNonModularizedDataTypes() {
            // 1 - create std calculation
            val (node, step) =
                wizardBuilder
                    .createStandard()
                    .bomNode
                    .let {
                        manufacturingCrudTestClient.addStep(it, name = "Step1", initialCycleTime = Time(1.0, TimeUnits.SECOND))
                    }.let {
                        manufacturingCrudTestClient.publish(it)
                    }.let { node ->
                        val stepId = node.findInTree { entity -> entity.name == "Step1" }!!
                        node to stepId
                    }

            val entityCreationDto =
                getMandatoryModularizedEntityFields(
                    bomNodeId = node.id,
                    branchId = node.branch.id,
                    composite =
                        MasterDataCompositeKey(
                            type = MasterDataType.RAW_MATERIAL_WAX.name,
                            key = "Wax (new)-RAW_MATERIAL_WAX",
                            year = null,
                            location = null,
                            mdClassificationKey = "tset.ref.classification.wax",
                        ),
                )

            assertThat(entityCreationDto).isNotNull
            assertThat(entityCreationDto.entityClass).isNull()
        }

        private fun MasterDataForEditDto.setField(
            key: String,
            value: Any?,
        ): MasterDataForEditDto {
            val field = this.fields.find { it.name == key }
            assertThat(field).withFailMessage("Field with key=$key not found").isNotNull

            val newFields = this.fields.toMutableList()
            newFields.remove(field!!)
            newFields.add(field.copy(value = value))

            return this.copy(fields = newFields)
        }

        /**
         * Creates a new masterdata entry of the given type
         *
         * 0 - Create hardcoded [displayDesignation] field
         * 1 - Get mandatory fields for [type]
         * 2 - Fill mandatory fields with defaults using [TestFieldFiller]
         * 3 - Call add endpoint with the above fields
         * 4 - Call update endpoint with the response
         *
         * */
        private fun createNewMasterData(
            type: MasterDataType,
            displayDesignation: String,
            fields: List<FieldParameter> = emptyList(),
        ): MasterDataForEditDto {
            val hardCodedFields =
                listOf(
                    FieldParameter(
                        name = "displayDesignation",
                        type = "Text",
                        value = displayDesignation,
                        denominatorUnit = null,
                    ),
                    FieldParameter(
                        name = MASTERDATA_CURRENCY_FIELD_NAME,
                        type = "Currency",
                        value = "EUR",
                        denominatorUnit = null,
                    ),
                    FieldParameter(
                        name = "exchangeRates",
                        type = "ExchangeRatesField",
                        value = "{\"EUR\":1.000000000000}",
                        denominatorUnit = null,
                    ),
                )

            val mandatoryFields = masterDataClient.getMandatoryFields(type)

            return masterDataClient.addAndSave(
                AddMasterDataDto(
                    type = type,
                    fields = fieldFiller.fillAll(mandatoryFields) + fields + hardCodedFields,
                    year = null,
                    location = null,
                ),
            )
        }

        private fun getAndFillMandatoryEntityFields(
            bomNodeId: String,
            branchId: String,
            entityType: Entities,
            composite: MasterDataCompositeKey,
        ): List<FieldParameterDto> =
            masterDataClient
                .getMandatoryEntityFields(projectId, bomNodeId, branchId, entityType, composite)
                .map(fieldFiller::fill)
                .map { it.toFieldParameterDto() }

        private fun getMandatoryModularizedEntityFields(
            bomNodeId: String,
            branchId: String,
            entityType: Entities = Entities.MATERIAL,
            composite: MasterDataCompositeKey,
            modularized: Boolean = true,
        ): EntityCreationDto<FieldParameter> =
            masterDataClient.getMandatoryEntity(
                projectId = projectId,
                bomNodeId = bomNodeId,
                branchId = branchId,
                entityType = entityType,
                composite = composite,
                modularized = modularized,
            )

        // TODO: copypasted from ManualEntityCreationDeletionIT
        private fun toFieldInputDto(
            fieldKey: String,
            fieldResult: FieldResult<*, *>,
        ): FieldParameterDto =
            FieldParameterDto(
                name = fieldKey,
                type = fieldResult.javaClass.simpleName,
                unit =
                    when (fieldResult) {
                        is NumericFieldResultWithUnit<*, *> -> (fieldResult.unit as Enum<*>).name
                        else -> null
                    },
                value = fieldResult.res!!.toString(),
                denominatorUnit = null,
            )
    }

fun List<FieldParameter>.getField(name: String): FieldParameter? = this.find { it.name == name }

fun List<FieldParameter>.getFieldValue(name: String): Any? {
    val field = getField(name) ?: error("Field not found name=$name")
    return field.value
}
