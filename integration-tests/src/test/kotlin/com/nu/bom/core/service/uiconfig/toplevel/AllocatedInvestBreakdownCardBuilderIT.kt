package com.nu.bom.core.service.uiconfig.toplevel

import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationRole
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.fieldnamebuilders.ValueFieldNameBuilder
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCO2eCalculationElementType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCostCalculationElementType
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.TopLevelUiConfigService
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.TableOptionEnum
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.toplevel.AllocatedInvestBreakdownCardBuilder
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.CardIdentifier
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.TableName
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.ValueTypeFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.CardConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableColumnDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.TableConfigFeDto
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class AllocatedInvestBreakdownCardBuilderIT(
    @Autowired
    builderService: TopLevelUiConfigService,
) : TopLevelUIConfigCardBuilderITBase(builderService, AllocatedInvestBreakdownCardBuilder::class) {
    @Test
    fun test() {
        createUiConfigAndVerify(
            verifyCards = ::verifyCards,
            verifyTables = ::verifyTables,
            testInputForCardBuilder = TestInputForCardBuilder.getDefaultWithManufacturingType(ManufacturingType.PURCHASE),
            maybeFileNameForComparison = "ExpectedDefaultAllocatedInvestUIConfig",
        )
    }

    fun verifyCards(cards: Map<CardIdentifier, CardConfigFeDto>) {
        val expectedCardConfig =
            CardConfigFeDto(
                setOf(
                    ValueTypeFeDto.COST,
                    ValueTypeFeDto.CO2,
                ),
                mapOf(
                    ValueTypeFeDto.COST to TsetCostCalculationElementType.ALLOCATED_AND_INTEREST_COSTS_FOR_INVEST.long,
                    ValueTypeFeDto.CO2 to TsetCO2eCalculationElementType.TOTAL_CO2E_FOR_INVEST.long,
                ),
                mapOf(
                    ValueTypeFeDto.COST to
                        ValueFieldNameBuilder(
                            ValueType.COST,
                            AggregationLevel.MANUFACTURED_MATERIAL,
                            AggregationRole.TOTAL,
                            TsetCostCalculationElementType.ALLOCATED_AND_INTEREST_COSTS_FOR_INVEST.fieldName,
                        ).fieldName,
                    ValueTypeFeDto.CO2 to
                        ValueFieldNameBuilder(
                            ValueType.CO2,
                            AggregationLevel.MANUFACTURED_MATERIAL,
                            AggregationRole.TOTAL,
                            TsetCO2eCalculationElementType.TOTAL_CO2E_FOR_INVEST.fieldName,
                        ).fieldName,
                ),
                null,
                mapOf(
                    ValueTypeFeDto.COST to listOf(TableOptionEnum.PRODUCTION.displayableOptionName),
                    ValueTypeFeDto.CO2 to listOf(TableOptionEnum.PRODUCTION.displayableOptionName),
                ),
            )

        assertThat(cards).containsKey("investAllocatedOverview")
        assertThat(cards["investAllocatedOverview"]).isEqualTo(expectedCardConfig)
        // Assertions.assertEquals(cards.keys, setOf("investAllocatedOverview"))
        // Assertions.assertEquals(expectedCardConfig, cards.values.single())
    }

    fun verifyTables(tableConfigs: Map<TableName, TableConfigFeDto>) {
        val expectedTableKeys =
            ValueTypeFeDto.entries.map { valueTypeFeDto ->
                TableName(
                    "investAllocatedOverview",
                    valueTypeFeDto,
                    TableOptionEnum.PRODUCTION.displayableOptionName,
                )
            }.toSet()
        assertThat(tableConfigs.keys).containsAll(expectedTableKeys)
        // Assertions.assertEquals(expectedTableKeys, tableConfigs.keys)

        val tableConfigsUnderTest = tableConfigs.filterKeys { it in expectedTableKeys }
        tableConfigsUnderTest.forEach { (tableName, tableConfig) ->
            Assertions.assertTrue(tableConfig is FieldTableConfigFeDto)

            Assertions.assertEquals(4, tableConfig.columns.size)

            Assertions.assertEquals(
                when (tableName.valueTypeFeDto) {
                    ValueTypeFeDto.COST -> 3
                    ValueTypeFeDto.CO2 -> 1
                },
                tableConfig.rowDefinitions.size,
            )

            Assertions.assertEquals(
                when (tableName.valueTypeFeDto) {
                    ValueTypeFeDto.COST -> 1
                    ValueTypeFeDto.CO2 -> 1
                },
                tableConfig.rows.size,
            )

            tableConfig.columns.forEach { column ->
                Assertions.assertTrue(column is FieldTableColumnDefinitionFeDto)
                Assertions.assertTrue((column as FieldTableColumnDefinitionFeDto).options?.displayDesignation != null)
            }
        }
    }
}
