package com.nu.bom.core.service.uiconfig.toplevel

import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationRole
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.fieldnamebuilders.ValueFieldNameBuilder
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCO2eCalculationElementType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCostCalculationElementType
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.TopLevelUiConfigService
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.TableOptionEnum
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.toplevel.FullCalculationTreeCardBuilder
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.CardIdentifier
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.TableName
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.ValueTypeFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.CardConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableColumnDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.TableConfigFeDto
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class FullCalculationTreeCardBuilderIT(
    @Autowired
    builderService: TopLevelUiConfigService,
) : TopLevelUIConfigCardBuilderITBase(builderService, FullCalculationTreeCardBuilder::class) {
    @Test
    fun testFullCalculationTreeCardBuilderForDefaultAsPurchased() {
        createUiConfigAndVerify(
            verifyCards = ::verifyCards,
            verifyTables = ::verifyTables,
            testInputForCardBuilder = TestInputForCardBuilder.getDefaultWithManufacturingType(ManufacturingType.PURCHASE),
            maybeFileNameForComparison = "ExpectedDefaultFullTableUIConfig",
        )
    }

    fun verifyCards(cards: Map<CardIdentifier, CardConfigFeDto>) {
        val expectedCardConfig =
            CardConfigFeDto(
                setOf(
                    ValueTypeFeDto.COST,
                    ValueTypeFeDto.CO2,
                ),
                mapOf(
                    ValueTypeFeDto.COST to TsetCostCalculationElementType.SALES_PRICE.long,
                    ValueTypeFeDto.CO2 to TsetCO2eCalculationElementType.TOTAL_CO2E.long,
                ),
                mapOf(
                    ValueTypeFeDto.COST to
                        ValueFieldNameBuilder(
                            ValueType.COST,
                            AggregationLevel.SOLD_MATERIAL,
                            AggregationRole.TOTAL,
                            TsetCostCalculationElementType.SALES_PRICE.fieldName,
                        ).fieldName,
                    ValueTypeFeDto.CO2 to
                        ValueFieldNameBuilder(
                            ValueType.CO2,
                            AggregationLevel.SOLD_MATERIAL,
                            AggregationRole.TOTAL,
                            TsetCO2eCalculationElementType.TOTAL_CO2E.fieldName,
                        ).fieldName,
                ),
                null,
                mapOf(
                    ValueTypeFeDto.COST to TableOptionEnum.entries.map { it.displayableOptionName },
                    ValueTypeFeDto.CO2 to TableOptionEnum.entries.map { it.displayableOptionName },
                ),
            )

        assertThat(cards).containsKey("fullCalculationTree")
        assertThat(cards["fullCalculationTree"]).isEqualTo(expectedCardConfig)
        // Assertions.assertEquals(cards.keys, setOf("fullCalculationTree"))
        // Assertions.assertEquals(expectedCardConfig, cards.values.single())
    }

    fun verifyTables(tableConfigs: Map<TableName, TableConfigFeDto>) {
        val expectedTableKeys =
            TableOptionEnum.entries.map { it.displayableOptionName }
                .flatMap { tableOption ->
                    ValueTypeFeDto.entries.map { valueTypeFeDto ->
                        TableName("fullCalculationTree", valueTypeFeDto, tableOption)
                    }
                }.toSet()

        assertThat(tableConfigs.keys).containsAll(expectedTableKeys)
        // Assertions.assertEquals(expectedTableKeys, tableConfigs.keys)

        val tableConfigsUnderTest = tableConfigs.filterKeys { it in expectedTableKeys }
        tableConfigsUnderTest.forEach { (tableName, tableConfig) ->
            Assertions.assertTrue(tableConfig is FieldTableConfigFeDto)

            Assertions.assertEquals(
                when (tableName.valueTypeFeDto) {
                    ValueTypeFeDto.COST -> 7
                    ValueTypeFeDto.CO2 -> 6
                },
                tableConfig.columns.size,
            )

            val costRowCount =
                mapOf(
                    TableOptionEnum.PRODUCTION.displayableOptionName to 84,
                    TableOptionEnum.ACTIVITY.displayableOptionName to 81,
                )
            val cO2RowCount =
                mapOf(
                    TableOptionEnum.PRODUCTION.displayableOptionName to 72,
                    TableOptionEnum.ACTIVITY.displayableOptionName to 66,
                )

            Assertions.assertEquals(
                when (tableName.valueTypeFeDto) {
                    ValueTypeFeDto.COST -> costRowCount[tableName.option]
                    ValueTypeFeDto.CO2 -> cO2RowCount[tableName.option]
                },
                tableConfig.rowDefinitions.size,
            )

            Assertions.assertEquals(3, tableConfig.rows.size)

            tableConfig.columns.forEach { column ->
                Assertions.assertTrue(column is FieldTableColumnDefinitionFeDto)
                Assertions.assertTrue((column as FieldTableColumnDefinitionFeDto).options?.displayDesignation != null)
            }
        }
    }
}
