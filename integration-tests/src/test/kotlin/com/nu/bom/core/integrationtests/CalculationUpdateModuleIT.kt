package com.nu.bom.core.integrationtests

import com.nu.bom.core.api.ManufacturingUpdateController
import com.nu.bom.core.api.dtos.BomEntryCreationDto
import com.nu.bom.core.api.dtos.BomNodeDto
import com.nu.bom.core.api.dtos.BomNodeHistoryDto
import com.nu.bom.core.api.dtos.CompositeTriggerDtoData
import com.nu.bom.core.api.dtos.CreateManufacturingResult
import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.api.dtos.ManufacturingDto
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.CustomProcurementTypeWrapper
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetProcurementType
import com.nu.bom.core.manufacturing.entities.ExchangeRates
import com.nu.bom.core.manufacturing.entities.ManualManufacturingStep
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent
import com.nu.bom.core.manufacturing.entities.masterdata.exchangerates.MasterdataExchangeRateParent
import com.nu.bom.core.manufacturing.entities.masterdata.material.MasterdataMaterialParent
import com.nu.bom.core.manufacturing.entities.masterdata.overheads.MasterdataInterestParent
import com.nu.bom.core.manufacturing.entities.masterdata.overheads.MasterdataOverheadParent
import com.nu.bom.core.manufacturing.entities.transport.TransportCalculatorDetailedCalculation
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CustomProcurementType
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYears
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYearsUnit
import com.nu.bom.core.manufacturing.masterdata.tsetdefaultconfiguration.TsetOverheadMethod
import com.nu.bom.core.manufacturing.utils.ManufacturingWizardTestClient.Companion.configIdentifierDefaultFieldParameter
import com.nu.bom.core.manufacturing.utils.RequiredFields
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.mongoProjectId
import com.nu.bom.core.service.HistoryService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.NbkClient
import com.nu.bom.core.utils.toObjectId
import com.nu.bomrads.enumeration.BomNodeStatus
import com.tset.core.api.calculation.dto.CalculationCreationModalMode
import com.tset.core.api.calculation.dto.CalculationPosition
import com.tset.core.api.calculation.dto.CalculationUpdateContextDto
import com.tset.core.api.calculation.dto.CalculationUpdateData
import com.tset.core.api.calculation.dto.CalculationUpdateInputDto
import com.tset.core.api.calculation.dto.CalculationUpdatePayloadDto
import com.tset.core.module.bom.CalculationUpdateModule
import com.tset.core.module.bom.calculation.CalculationChangeTypeRootInput
import com.tset.core.module.bom.calculation.CalculationChangeTypeSubInput
import com.tset.core.module.bom.calculation.CalculationCreateRootInput
import com.tset.core.module.bom.calculation.CalculationCreateSubInput
import com.tset.core.module.bom.calculation.CalculationEditRootInput
import com.tset.core.module.bom.calculation.CalculationEditSubInput
import com.tset.core.module.bom.calculation.CalculationUpdateInputBase
import com.tset.core.service.domain.Currency.Companion.EUR
import com.tset.core.service.domain.calculation.CalculationType
import com.tset.core.service.domain.calculation.CalculationType.Companion.ACCOUNT_SPECIFIC_IMPORT_VALUE
import com.tset.core.service.domain.calculation.CalculationType.Companion.EXCEL_FILE_IMPORT_VALUE
import com.tset.core.service.domain.calculation.CalculationType.Companion.ROUGH_CALCULATION_VALUE
import com.tset.core.service.domain.calculation.CalculationType.Companion.TSET_FILE_IMPORT_VALUE
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import java.math.BigDecimal

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test", "masterdata-init", "lookup-init", "config-init")
@AutoConfigureWebTestClient
class CalculationUpdateModuleIT : NbkClient.With {
    private lateinit var access: AccessCheck
    private lateinit var projectId: ProjectId

    @Autowired
    private lateinit var calculationUpdateModule: CalculationUpdateModule

    @Autowired
    override lateinit var nbkClient: NbkClient

    @Autowired
    private lateinit var historyService: HistoryService

    @BeforeEach
    fun setup() {
        val result = nbkClient.setupWithProject("Test Project", "CUIT")
        access = result.first
        projectId = result.second.mongoProjectId()
    }

    @AfterEach
    fun cleanup() {
        nbkClient.cleanup()
    }

    @ParameterizedTest
    @EnumSource(
        names = [TSET_FILE_IMPORT_VALUE, EXCEL_FILE_IMPORT_VALUE, ACCOUNT_SPECIFIC_IMPORT_VALUE],
        mode = EnumSource.Mode.EXCLUDE,
    )
    fun createCalculation(calculationType: CalculationType) {
        val dto = createDto(calculationType)
        val result = calculationUpdateModule.dispatch(accessCheck = access, dto).block()

        validate(
            result,
            calculationType,
            "calcTitle",
            ManufacturingType.Type.INHOUSE,
            NEW_LOCATION,
        )

        run {
            val historyDto = getHistory(result!!.bomNode.id, null)
            Assertions.assertNotNull(historyDto, "historyDto")
            Assertions.assertEquals(1, historyDto.size, "history.size")
            Assertions.assertEquals(result.bomNode.branch.id, historyDto[0].branchId, "history[0].branchId")
            Assertions.assertEquals(1, historyDto[0].nodeChanges.size, "history[0].nodeChanges.size")
            val compositeNodeChange = historyDto[0].nodeChanges[0]
            Assertions.assertEquals(
                "CompositeTriggerAction",
                compositeNodeChange.trigger.triggerType,
                "nodeChange.trigger.triggerType",
            )
            val triggerDtoData = compositeNodeChange.trigger.data as CompositeTriggerDtoData
            Assertions.assertEquals(1, triggerDtoData.triggers.size, "nodeChange.trigger.data.triggers.size")

            val nodeChange = triggerDtoData.triggers.first()
            Assertions.assertEquals(
                "BomCreation",
                triggerDtoData.triggers.first().triggerType,
                "triggerDtoData.triggers.triggerType",
            )

            // TODO: nodeChange.oldTitle is not maintained
            // Assertions.assertEquals(null, nodeChange.oldTitle, "nodeChange.oldTitle")
            Assertions.assertEquals("calcTitle", compositeNodeChange.newTitle, "nodeChange.newTitle")
            Assertions.assertEquals(
                result.bomNode.id,
                nodeChange.legacy().bomNodeId,
                "nodeChange.trigger.bomNodeId",
            )
            Assertions.assertEquals(
                result.bomNode.manufacturing?.id,
                nodeChange.legacy().entityId,
                "nodeChange.trigger.entityId",
            )

            Assertions.assertEquals(
                calculationType.name,
                nodeChange.legacy().entityType,
                "nodeChange.trigger.entityType",
            )
            Assertions.assertEquals(
                calculationType.entityClass.simpleName,
                nodeChange.legacy().entityName,
                "nodeChange.trigger.entityName",
            )
            Assertions.assertEquals(
                calculationType.entityClass.simpleName,
                nodeChange.legacy().entityClass,
                "nodeChange.trigger.entityClass",
            )
            Assertions.assertEquals(
                calculationType.entityClass.simpleName,
                nodeChange.legacy().entityDisplayName,
                "nodeChange.trigger.entityDisplayName",
            )
            Assertions.assertEquals(null, nodeChange.legacy().oldValue, "nodeChange.trigger.oldValue")
        }
    }

    @ParameterizedTest
    @EnumSource(
        names = [TSET_FILE_IMPORT_VALUE, EXCEL_FILE_IMPORT_VALUE, ACCOUNT_SPECIFIC_IMPORT_VALUE],
        mode = EnumSource.Mode.EXCLUDE,
    )
    fun updateCalculationWithOutTypeChange(calculationType: CalculationType) {
        val dto = createDto(calculationType)
        val oldResult = calculationUpdateModule.dispatch(accessCheck = access, dto).block()

        val updateDto = createUpdateDto(calculationType, calculationType, oldResult?.bomNode?.id!!)

        val result = calculationUpdateModule.dispatch(access, updateDto).block()
        validate(
            result,
            calculationType,
            "updatedCalcTitle",
            ManufacturingType.Type.PURCHASE,
            UPDATE_LOCATION,
        )

        run {
            val branchId = result?.bomNode?.branch?.id
            val historyDto = getHistory(result!!.bomNode.id, branchId)
            Assertions.assertNotNull(historyDto, "historyDto")
            Assertions.assertEquals(2, historyDto.size, "history.size")
            Assertions.assertEquals(branchId, historyDto[0].branchId, "history[0].branchId")
            Assertions.assertEquals(1, historyDto[0].nodeChanges.size, "history[0].nodeChanges.size")
            val nodeChange = historyDto[0].nodeChanges[0]
            Assertions.assertEquals(
                "RootManufacturingChange",
                nodeChange.trigger.triggerType,
                "nodeChange.trigger.triggerType",
            )
            // TODO: nodeChange.oldTitle is not maintained
            // Assertions.assertEquals("calcTitle", nodeChange.oldTitle, "nodeChange.oldTitle")
            Assertions.assertEquals("updatedCalcTitle", nodeChange.newTitle, "nodeChange.newTitle")
            Assertions.assertEquals(
                result.bomNode.id,
                nodeChange.trigger.legacy().bomNodeId,
                "nodeChange.trigger.bomNodeId",
            )
            Assertions.assertEquals(
                result.bomNode.manufacturing?.id,
                nodeChange.trigger.legacy().entityId,
                "nodeChange.trigger.entityId",
            )
            Assertions.assertEquals(
                calculationType.name,
                nodeChange.trigger.legacy().entityType,
                "nodeChange.trigger.entityType",
            )
            Assertions.assertEquals(
                calculationType.entityClass.simpleName,
                nodeChange.trigger.legacy().entityName,
                "nodeChange.trigger.entityName",
            )
            Assertions.assertEquals(
                calculationType.entityClass.simpleName,
                nodeChange.trigger.legacy().entityClass,
                "nodeChange.trigger.entityClass",
            )
            Assertions.assertEquals(
                calculationType.entityClass.simpleName,
                nodeChange.trigger.legacy().entityDisplayName,
                "nodeChange.trigger.entityDisplayName",
            )
            Assertions.assertEquals(
                calculationType.name,
                nodeChange.trigger.legacy().oldValue,
                "nodeChange.trigger.oldValue",
            )
            Assertions.assertEquals(
                calculationType.name,
                nodeChange.trigger.legacy().fieldValue,
                "nodeChange.trigger.newValue",
            )
        }
    }

    private fun getHistory(
        nodeId: String,
        branchId: String?,
    ): List<BomNodeHistoryDto> {
        return historyService.getNodeHistoryForNode(
            access,
            projectId = projectId.toHexString(),
            nodeId = nodeId,
            branch = branchId,
            limit = null,
            startChangeset = null,
            type = null,
            includeChildren = null,
            includeParents = null,
            includePublish = null,
        ).block()!!
    }

    @ParameterizedTest
    @MethodSource("calculationTypePairs")
    fun updateCalculationWithTypeChange(
        oldCalculationType: CalculationType,
        calculationType: CalculationType,
    ) {
        val dto = createDto(oldCalculationType)
        val oldResult = calculationUpdateModule.dispatch(accessCheck = access, dto).block()

        val updateDto = createUpdateDto(oldCalculationType, calculationType, oldResult?.bomNode?.id!!)

        val result = calculationUpdateModule.dispatch(access, updateDto).block()

        validate(
            result,
            calculationType,
            "updatedCalcTitle",
            ManufacturingType.Type.PURCHASE,
            UPDATE_LOCATION,
        )

        run {
            val branchId = result?.bomNode?.branch?.id
            val historyDto = getHistory(result!!.bomNode.id, branchId)
            Assertions.assertNotNull(historyDto, "historyDto")
            Assertions.assertEquals(2, historyDto.size, "history.size")
            Assertions.assertEquals(branchId, historyDto[0].branchId, "history[0].branchId")
            Assertions.assertEquals(1, historyDto[0].nodeChanges.size, "history[0].nodeChanges.size")
            val nodeChange = historyDto[0].nodeChanges[0]
            Assertions.assertEquals("BomTypeChange", nodeChange.trigger.triggerType, "nodeChange.trigger.triggerType")

            // TODO: oldTitle is not correct...
            // Assertions.assertEquals("calcTitle", nodeChange.oldTitle, "nodeChange.oldTitle")
            Assertions.assertEquals("updatedCalcTitle", nodeChange.newTitle, "nodeChange.newTitle")
            Assertions.assertEquals(
                result.bomNode.id,
                nodeChange.trigger.legacy().bomNodeId,
                "nodeChange.trigger.bomNodeId",
            )
            Assertions.assertEquals(
                result.bomNode.manufacturing?.id,
                nodeChange.trigger.legacy().entityId,
                "nodeChange.trigger.entityId",
            )
            Assertions.assertEquals(
                calculationType.name,
                nodeChange.trigger.legacy().entityType,
                "nodeChange.trigger.entityType",
            )
            Assertions.assertEquals(
                calculationType.entityClass.simpleName,
                nodeChange.trigger.legacy().entityName,
                "nodeChange.trigger.entityName",
            )
            Assertions.assertEquals(
                calculationType.entityClass.simpleName,
                nodeChange.trigger.legacy().entityClass,
                "nodeChange.trigger.entityClass",
            )
            Assertions.assertEquals(
                calculationType.entityClass.simpleName,
                nodeChange.trigger.legacy().entityDisplayName,
                "nodeChange.trigger.entityDisplayName",
            )
            Assertions.assertEquals(
                oldCalculationType.name,
                nodeChange.trigger.legacy().oldValue,
                "nodeChange.trigger.oldValue",
            )
        }
    }

    @ParameterizedTest
    @EnumSource
    fun createSubCalculation(calculationType: CalculationType) {
        val rootUpdatedResult = createSubCalculationSetup(calculationType)

        validate(
            rootUpdatedResult,
            CalculationType.MANUAL_CALCULATION,
            "calcTitle",
            ManufacturingType.Type.INHOUSE,
            NEW_LOCATION,
            childBomNodes = 1,
            hasStep = true,
        )
        val childLink = rootUpdatedResult!!.bomNode.subNodes[0]

        Assertions.assertNotNull(childLink, "rootNode.child")
        val childBomNode =
            manufacturingCrudTestClient.getBomNode(
                bomNodeId = childLink.bomNodeId,
                branchId = rootUpdatedResult.bomNode.branch.id,
            )
        Assertions.assertNotNull(childBomNode, "childBomNode.exists")
        validateBomNode(
            childBomNode!!,
            calculationType = calculationType,
            manufacturingType = ManufacturingType.Type.INHOUSE,
            expectedLocation = NEW_LOCATION,
            title = "subCalcTitle",
            parentBomNodes = 1,
        )
        validatePart(childBomNode, partNumber = "31428", designation = "NewChildPart")
    }

    @ParameterizedTest
    @MethodSource("subsWithUpdatedQuantity")
    fun `sub-calculation volumes correctly calculated when quantity changes`(
        parentQuantity: Int,
        averageVolumePerYear: Double,
        peakVolumePerYear: Double,
        averageVolumePerYearSystemValue: Double?,
        peakVolumePerYearSystemValue: Double?,
    ) {
        val dto = createDto(CalculationType.MANUAL_CALCULATION)
        val rootResult = calculationUpdateModule.dispatch(accessCheck = access, dto).block()
        val nodeWithStep =
            manufacturingCrudTestClient.addStep(
                node = rootResult!!.bomNode,
                name = "Step1",
            )
        val subDto =
            createSubDto(
                CalculationType.MANUAL_CALCULATION,
                nodeWithStep,
                parentQuantity = parentQuantity,
                averageUsableProductionVolumePerYear = averageVolumePerYear.toBigDecimal(),
                peakUsableProductionVolumePerYear = peakVolumePerYear.toBigDecimal(),
            )
        val rootUpdatedResult = calculationUpdateModule.dispatch(accessCheck = access, subDto).block()

        val publishedBomNode = manufacturingCrudTestClient.publish(rootUpdatedResult!!.bomNode)

        val childLink = publishedBomNode.subNodes[0]
        Assertions.assertNotNull(childLink, "rootNode.child")
        val childBomNode =
            manufacturingCrudTestClient.getBomNode(
                bomNodeId = childLink.bomNodeId,
                branchId = publishedBomNode.branch.id,
            )
        val averageField = childBomNode!!.getField("averageUsableProductionVolumePerYear")!!
        Assertions.assertEquals(averageVolumePerYear, averageField.value as Double)
        Assertions.assertEquals(averageVolumePerYearSystemValue, averageField.systemValue as Double?)
        val peakField = childBomNode.getField("peakUsableProductionVolumePerYear")!!
        Assertions.assertEquals(peakVolumePerYear, peakField.value as Double)
        Assertions.assertEquals(peakVolumePerYearSystemValue, peakField.systemValue as Double?)
    }

    @ParameterizedTest
    @EnumSource(names = [ROUGH_CALCULATION_VALUE], mode = EnumSource.Mode.EXCLUDE)
    fun updateCalculationWithSubcalcWithoutTypeChange(calculationType: CalculationType) {
        val rootUpdatedResult =
            createSubCalculationSetup(
                CalculationType.MANUAL_CALCULATION,
                rootCalculationType = calculationType,
            )
        val childLink = rootUpdatedResult!!.bomNode.subNodes[0]
        val updateDto = createUpdateDto(calculationType, calculationType, rootUpdatedResult.bomNode.id)

        val updatedParent = calculationUpdateModule.dispatch(accessCheck = access, updateDto).block()

        validate(
            updatedParent,
            calculationType,
            "updatedCalcTitle",
            ManufacturingType.Type.PURCHASE,
            UPDATE_LOCATION,
        )

        val childBomNode =
            manufacturingCrudTestClient.getBomNode(
                bomNodeId = childLink.bomNodeId,
                branchId = rootUpdatedResult.bomNode.branch.id,
            )
        Assertions.assertNotNull(childBomNode, "childBomNode.exists")
        validateBomNode(
            childBomNode!!,
            calculationType = CalculationType.MANUAL_CALCULATION,
            manufacturingType = ManufacturingType.Type.INHOUSE,
            expectedLocation = NEW_LOCATION,
            title = "subCalcTitle",
            parentBomNodes = 1,
        )
        validatePart(childBomNode, partNumber = "31428", designation = "NewChildPart")
    }

    @ParameterizedTest
    @MethodSource("calculationTypePairsWithBoolean")
    fun updateSubCalculationWithTypeChange(
        oldCalculationType: CalculationType,
        calculationType: CalculationType,
    ) {
        val rootUpdatedResult = createSubCalculationSetup(oldCalculationType)

        val childLink = rootUpdatedResult!!.bomNode.subNodes[0]
        val childId = childLink.bomNodeId

        val stepId = getStepIdFromParent(rootUpdatedResult.bomNode)

        manufacturingCrudTestClient.publish(rootUpdatedResult.bomNode.id, rootUpdatedResult.bomNode.branch.id)

        val updateSubDto =
            createSubUpdateDto(
                oldCalculationType = oldCalculationType,
                calculationType = calculationType,
                bomNodeId = childId,
                stepId = stepId,
                branchId = null,
                parentBomNodeId = rootUpdatedResult.bomNode.id,
            )
        val updatedChildResult = calculationUpdateModule.dispatch(accessCheck = access, updateSubDto).block()

        Assertions.assertNotNull(updatedChildResult, "updatedChildResult")

        val childBomNode = updatedChildResult!!.bomNode
        validateBomNode(
            childBomNode,
            calculationType = calculationType,
            // from child, inhouse is not inherited
            manufacturingType = ManufacturingType.Type.PURCHASE,
            expectedLocation = UPDATE_LOCATION,
            title = "updatedSubCalcTitle",
            parentBomNodes = 1,
            hasStep = false,
        )
        Assertions.assertNotEquals(
            rootUpdatedResult.bomNode.branch.id,
            childBomNode.branch.id,
            "this is a new branch",
        )
    }

    @ParameterizedTest
    @MethodSource("calculationTypeWithBoolean")
    fun updateSubCalculationWithoutTypeChange(calculationType: CalculationType) {
        val rootUpdatedResult = createSubCalculationSetup(calculationType)

        val childLink = rootUpdatedResult!!.bomNode.subNodes[0]
        val childId = childLink.bomNodeId

        val stepId = getStepIdFromParent(rootUpdatedResult.bomNode)
        manufacturingCrudTestClient.publish(rootUpdatedResult.bomNode.id, rootUpdatedResult.bomNode.branch.id)

        val updateSubDto =
            createSubUpdateDto(
                oldCalculationType = calculationType,
                calculationType = calculationType,
                bomNodeId = childId,
                stepId = stepId,
                branchId = null,
                parentBomNodeId = rootUpdatedResult.bomNode.id,
            )
        val updatedChildResult = calculationUpdateModule.dispatch(accessCheck = access, updateSubDto).block()

        val childBomNode = updatedChildResult!!.bomNode
        Assertions.assertEquals(childId, childBomNode.id, "updatedChildResult really the child bomnode")
        Assertions.assertNotEquals(
            rootUpdatedResult.bomNode.branch.id,
            childBomNode.branch.id,
            "this is a new branch",
        )
        validateBomNode(
            childBomNode,
            calculationType = calculationType,
            // from child, inhouse is not inherited
            manufacturingType = ManufacturingType.Type.PURCHASE,
            expectedLocation = UPDATE_LOCATION,
            title = "updatedSubCalcTitle",
            parentBomNodes = 1,
            hasStep = false,
        )
    }

    fun editField(
        bomNode: BomNodeDto,
        fieldName: String,
        newValue: Any,
    ): BomNodeDto {
        val modifiedField = bomNode.manufacturing!!.getField(fieldName).copy(value = newValue)

        val updateFieldRequest =
            ManufacturingUpdateController.InputParameterApi(
                name = modifiedField.name,
                entityId = bomNode.manufacturing!!.id,
                type = modifiedField.type,
                unit = modifiedField.unit,
                value = modifiedField.value,
            )

        return manufacturingCrudTestClient.updateField(
            bomNodeId = bomNode.id,
            branch = bomNode.branch.id.toObjectId()!!.toHexString(),
            field = updateFieldRequest,
        )
    }

    @Test
    fun `COST-31967, COST-32145 - user-edited procurement or overheadMethod shall not be kept when converting to RoughManufacturing`() {
        val dto = createDto(CalculationType.MANUAL_CALCULATION)

        val oldResult = calculationUpdateModule.dispatch(accessCheck = access, dto).block()

        var bomNode = oldResult!!.bomNode
        bomNode = editField(bomNode, "procurementType", ManufacturingType.INHOUSE.res)
        bomNode = editField(bomNode, Manufacturing::overheadMethod.name, TsetOverheadMethod.HIGH_INNO_NON_AUTO.name)

        val oldManufacturing = bomNode.manufacturing!!

        val oldProcurementType = oldManufacturing.getField("procurementType")
        Assertions.assertEquals(ManufacturingType.INHOUSE.res.name, oldProcurementType.value)
        Assertions.assertEquals(FieldResult.SOURCE.I.name, oldProcurementType.source)
        Assertions.assertTrue(oldProcurementType.metaInfo?.containsKey("readOnly") ?: true)

        val oldOverheadMethod = oldManufacturing.getField(Manufacturing::overheadMethod.name)
        Assertions.assertEquals(TsetOverheadMethod.HIGH_INNO_NON_AUTO.name, oldOverheadMethod.value)
        Assertions.assertEquals(FieldResult.SOURCE.I.name, oldOverheadMethod.source)
        Assertions.assertFalse(oldOverheadMethod.metaInfo?.containsKey("readOnly") ?: false)

        val updateDto =
            createUpdateDto(
                CalculationType.MANUAL_CALCULATION,
                CalculationType.ROUGH_CALCULATION,
                bomNode.id,
                bomNode.branch.id,
            )
        val result = calculationUpdateModule.dispatch(access, updateDto).block()
        val newManufacturing = result!!.bomNode.manufacturing!!

        val newProcurementType = newManufacturing.getField("procurementType")
        Assertions.assertEquals(ManufacturingType.PURCHASE.res.name, newProcurementType.value)
        Assertions.assertEquals(FieldResult.SOURCE.C.name, newProcurementType.source)
        Assertions.assertEquals(newProcurementType.metaInfo?.get("readOnly"), true)

        val newOverheadMethod = newManufacturing.getField(Manufacturing::overheadMethod.name)
        Assertions.assertEquals(TsetOverheadMethod.NO_OVERHEADS.name, newOverheadMethod.value)
        Assertions.assertEquals(FieldResult.SOURCE.C.name, newOverheadMethod.source)
        Assertions.assertEquals(newOverheadMethod.metaInfo?.get("readOnly"), true)
    }

    fun createUpdateDto(
        oldCalculationType: CalculationType,
        calculationType: CalculationType,
        bomNodeId: String,
        branchId: String? = null,
        fields: List<FieldParameter> = emptyList(),
    ): CalculationUpdateInputBase {
        val baseFields =
            baseFields(
                calculationType,
                "updatedCalcTitle",
                12.toBigDecimal(),
                customProcurementType = TsetProcurementType.PURCHASE.customProcurementType,
                12_000.toBigDecimal(),
                11_000.toBigDecimal(),
                partDesignation = "NewTestPart",
                partNumber = "7890",
                UPDATE_LOCATION,
            )

        fun createCalculationUpdatePayloadDto(mode: CalculationCreationModalMode) =
            CalculationUpdatePayloadDto(
                data = CalculationUpdateData(fields = baseFields + fields, parentBomData = null),
                input =
                    CalculationUpdateInputDto(
                        mode = mode,
                        position = CalculationPosition.ROOT,
                        originalType = oldCalculationType,
                        context =
                            CalculationUpdateContextDto(
                                bomNodeId = bomNodeId,
                                branchId = branchId,
                                stepId = null,
                            ),
                        currency = EUR,
                    ),
                selectedType = calculationType,
            )

        return if (oldCalculationType == calculationType) {
            val result =
                CalculationEditRootInput.fromCalculationUpdateDto(
                    projectId = projectId.toHexString(),
                    calculationUpdateDto = createCalculationUpdatePayloadDto(CalculationCreationModalMode.CALCULATION_MODE_EDIT),
                )
            // the configIdentifier would usually be added by the wizard, which we circumvent here
            result.copy(
                fields = result.fields + configIdentifierDefaultFieldParameter,
            )
        } else {
            val result =
                CalculationChangeTypeRootInput.fromCalculationUpdateDto(
                    projectId = projectId.toHexString(),
                    calculationUpdateDto = createCalculationUpdatePayloadDto(CalculationCreationModalMode.CALCULATION_MODE_CHANGE_TYPE),
                )
            // the configIdentifier would usually be added by the wizard, which we circumvent here
            result.copy(
                fields = result.fields + configIdentifierDefaultFieldParameter,
            )
        }
    }

    private fun createSubUpdateDto(
        oldCalculationType: CalculationType,
        calculationType: CalculationType,
        bomNodeId: String,
        stepId: String,
        parentBomNodeId: String,
        branchId: String?,
    ): CalculationUpdateInputBase {
        val baseFields =
            baseFields(
                null,
                "updatedSubCalcTitle",
                12.toBigDecimal(),
                TsetProcurementType.PURCHASE.customProcurementType,
                12_000.toBigDecimal(),
                11_000.toBigDecimal(),
                partDesignation = "NewTestPart",
                partNumber = "7890",
                UPDATE_LOCATION,
            )

        fun createCalculationUpdatePayloadDto(mode: CalculationCreationModalMode) =
            CalculationUpdatePayloadDto(
                data =
                    CalculationUpdateData(
                        fields = baseFields,
                        parentBomData =
                            BomEntryCreationDto(
                                bomNodeId = parentBomNodeId,
                                branchId = branchId,
                                path = emptyList(),
                                fields =
                                    listOf(
                                        FieldParameter.create<Text, String>(name = "stepId", value = stepId),
                                        FieldParameter.create<Pieces, BigDecimal>(name = "quantity", value = 2.toBigDecimal()),
                                    ),
                            ),
                    ),
                input =
                    CalculationUpdateInputDto(
                        mode = mode,
                        position = CalculationPosition.SUB,
                        originalType = oldCalculationType,
                        context =
                            CalculationUpdateContextDto(
                                bomNodeId = bomNodeId,
                                branchId = branchId,
                                stepId = stepId,
                            ),
                        currency = EUR,
                    ),
                selectedType = calculationType,
            )

        return if (oldCalculationType == calculationType) {
            val result =
                CalculationEditSubInput.fromCalculationUpdateDto(
                    projectId = projectId.toHexString(),
                    calculationUpdateDto = createCalculationUpdatePayloadDto(CalculationCreationModalMode.CALCULATION_MODE_EDIT),
                )
            // the configIdentifier would usually be added by the wizard, which we circumvent here
            result.copy(
                fields = result.fields + configIdentifierDefaultFieldParameter,
            )
        } else {
            val result =
                CalculationChangeTypeSubInput.fromCalculationUpdateDto(
                    projectId = projectId.toHexString(),
                    calculationUpdateDto = createCalculationUpdatePayloadDto(CalculationCreationModalMode.CALCULATION_MODE_CHANGE_TYPE),
                )
            // the configIdentifier would usually be added by the wizard, which we circumvent here
            result.copy(
                fields = result.fields + configIdentifierDefaultFieldParameter,
            )
        }
    }

    fun baseFields(
        calculationType: CalculationType?,
        calculationTitle: String,
        lifeTimeInYears: BigDecimal,
        customProcurementType: CustomProcurementTypeWrapper,
        peakUsableProductionVolumePerYear: BigDecimal,
        averageUsableProductionVolumePerYear: BigDecimal,
        partDesignation: String,
        partNumber: String,
        location: String,
    ): List<FieldParameter> {
        val parameters =
            RequiredFields.default().apply {
                this.calculationTitle = Text(calculationTitle)
                this.lifeTime = TimeInYears(lifeTimeInYears, TimeInYearsUnit.YEAR)
                this.customProcurementType = CustomProcurementType.fromCustomProcurementTypeWrapper(customProcurementType)
                this.peakUsableProductionVolumePerYear = QuantityUnit(peakUsableProductionVolumePerYear)
                this.averageUsableProductionVolumePerYear = QuantityUnit(averageUsableProductionVolumePerYear)
                this.location = Text(location)
                this.partDesignation = Text(partDesignation)
                this.partNumber = Text(partNumber)
            }.toFieldParameters()

        val type =
            calculationType?.let {
                FieldParameter(
                    name = "calculationType",
                    type = "CalculationType",
                    value = it.name,
                    denominatorUnit = null,
                )
            }
        return parameters + listOfNotNull(type)
    }

    private fun createDto(
        calculationType: CalculationType,
        fields: List<FieldParameter> = emptyList(),
        partDesignation: String = "TestPart",
        partNumber: String = "1234",
    ): CalculationCreateRootInput {
        val baseFields =
            baseFields(
                calculationType,
                "calcTitle",
                10.toBigDecimal(),
                customProcurementType = TsetProcurementType.INHOUSE.customProcurementType,
                10_000.toBigDecimal(),
                9_000.toBigDecimal(),
                partDesignation = partDesignation,
                partNumber = partNumber,
                NEW_LOCATION,
            )

        val result =
            CalculationCreateRootInput.fromCalculationUpdateDto(
                projectId = projectId.toHexString(),
                CalculationUpdatePayloadDto(
                    data = CalculationUpdateData(fields = baseFields + fields, parentBomData = null),
                    input =
                        CalculationUpdateInputDto(
                            mode = CalculationCreationModalMode.CALCULATION_MODE_NEW,
                            position = CalculationPosition.ROOT,
                            originalType = null,
                            context =
                                CalculationUpdateContextDto(
                                    bomNodeId = null,
                                    branchId = null,
                                    stepId = null,
                                ),
                            currency = EUR,
                        ),
                    selectedType = calculationType,
                ),
                null,
            )

        // the configIdentifier would usually be added by the wizard, which we circumvent here
        return result.copy(
            fields = result.fields + configIdentifierDefaultFieldParameter,
        )
    }

    private fun createSubCalculationSetup(
        calculationType: CalculationType,
        rootCalculationType: CalculationType = CalculationType.MANUAL_CALCULATION,
    ): CreateManufacturingResult? {
        val dto = createDto(rootCalculationType)
        val rootResult = calculationUpdateModule.dispatch(accessCheck = access, dto).block()

        val nodeWithStep =
            manufacturingCrudTestClient.addStep(
                node = rootResult!!.bomNode,
                name = "Step1",
            )

        val subDto = createSubDto(calculationType, nodeWithStep)
        return calculationUpdateModule.dispatch(accessCheck = access, subDto).block()
    }

    private fun getStepIdFromParent(parentBomNode: BomNodeDto): String {
        return (
            parentBomNode.manufacturing!!.children.find { it.type == Entities.MANUFACTURING_STEP }
                ?: error("Could not find step")
        ).id
    }

    private fun createSubDto(
        calculationType: CalculationType,
        parentBomNode: BomNodeDto,
        fields: List<FieldParameter> = emptyList(),
        parentQuantity: Int = 2,
        peakUsableProductionVolumePerYear: BigDecimal = 10_000.toBigDecimal(),
        averageUsableProductionVolumePerYear: BigDecimal = 9_000.toBigDecimal(),
    ): CalculationCreateSubInput {
        val stepId = getStepIdFromParent(parentBomNode)

        val baseFields =
            baseFields(
                calculationType,
                "subCalcTitle",
                BigDecimal.TEN,
                TsetProcurementType.INHOUSE.customProcurementType,
                peakUsableProductionVolumePerYear,
                averageUsableProductionVolumePerYear,
                partDesignation = "NewChildPart",
                partNumber = "31428",
                NEW_LOCATION,
            )

        val result =
            CalculationCreateSubInput.fromCalculationUpdateDto(
                projectId = projectId.toHexString(),
                CalculationUpdatePayloadDto(
                    data =
                        CalculationUpdateData(
                            fields = baseFields + fields,
                            parentBomData =
                                BomEntryCreationDto(
                                    bomNodeId = parentBomNode.id,
                                    branchId = parentBomNode.branch.id,
                                    path = emptyList(),
                                    fields =
                                        listOf(
                                            FieldParameter.create<Text, String>(name = "stepId", value = stepId),
                                            FieldParameter.create<Pieces, BigDecimal>(
                                                name = "quantity",
                                                value = parentQuantity.toBigDecimal(),
                                            ),
                                        ),
                                ),
                        ),
                    input =
                        CalculationUpdateInputDto(
                            mode = CalculationCreationModalMode.CALCULATION_MODE_NEW,
                            position = CalculationPosition.ROOT,
                            originalType = null,
                            context =
                                CalculationUpdateContextDto(
                                    bomNodeId = parentBomNode.id,
                                    branchId = parentBomNode.branch.id,
                                    stepId = stepId,
                                ),
                            currency = EUR,
                        ),
                    selectedType = calculationType,
                ),
            )

        // the configIdentifier would usually be added by the wizard, which we circumvent here
        return result.copy(
            fields = result.fields + configIdentifierDefaultFieldParameter,
        )
    }

    private fun validate(
        result: CreateManufacturingResult?,
        calculationType: CalculationType,
        title: String,
        manufacturingType: ManufacturingType.Type,
        expectedLocation: String,
        childBomNodes: Int = 0,
        hasStep: Boolean = false,
    ) {
        Assertions.assertNotNull(result, "result")
        Assertions.assertNotNull(result!!.bomNode, "result.explorerNode")
        Assertions.assertEquals(title, result.bomNode.title, "result.explorerNode.title")
        Assertions.assertEquals(BomNodeStatus.TODO, result.bomNode.status, "result.explorerNode.status")

        validateBomNode(
            result.bomNode,
            calculationType,
            manufacturingType,
            expectedLocation,
            title = title,
            childBomNodes = childBomNodes,
            hasStep = hasStep,
        )
    }

    private fun validateBomNode(
        bomNode: BomNodeDto,
        calculationType: CalculationType,
        manufacturingType: ManufacturingType.Type,
        expectedLocation: String,
        title: String,
        childBomNodes: Int = 0,
        parentBomNodes: Int = 0,
        hasStep: Boolean = false,
    ) {
        if (calculationType != CalculationType.TSET_FILE_IMPORT) {
            Assertions.assertEquals(childBomNodes, bomNode.subNodes.size, "result.bomNode.subNodes.size")
            Assertions.assertEquals(parentBomNodes, bomNode.parents.size, "result.bomNode.parents.size")
            Assertions.assertEquals(title, bomNode.title, "result.bomNode.title")
            Assertions.assertNotNull(bomNode.manufacturing, "result.bomNode.manufacturing")

            val manufacturing = bomNode.manufacturing!!
            Assertions.assertEquals(Entities.MANUFACTURING, manufacturing.type, "result.bomNode.manufacturing.type")
            Assertions.assertEquals(
                calculationType.entityClass.simpleName,
                manufacturing.name,
                "result.bomNode.manufacturing.name",
            )
            Assertions.assertEquals(
                calculationType.entityClass.simpleName,
                manufacturing.className,
                "result.bomNode.manufacturing.type",
            )

            validateManufacturingChildren(hasStep, manufacturing.children)

            validateMasterDataCostFactorParent(
                manufacturing.children.firstOrNull { it.className == "MasterdataCostFactorParent" },
                expectedLocation,
            )

            Assertions.assertTrue(manufacturing.fields.size > 100, "result.bomNode.manufacturing.fields.size > 100")
            if (parentBomNodes == 0) {
                Assertions.assertTrue(manufacturing.data.size > 100, "result.bomNode.manufacturing.data.size > 100")
            }
        }
    }

    private fun validateManufacturingChildren(
        hasStep: Boolean,
        children: MutableList<ManufacturingDto>,
    ) {
        val expectedChildren =
            mapOf(
                Entities.EXCHANGE_RATES to ExchangeRates::class.simpleName,
                Entities.MD_OVERHEADS_PARENT to MasterdataOverheadParent::class.simpleName,
                Entities.MD_INTERESTS_PARENT to MasterdataInterestParent::class.simpleName,
                Entities.MD_EXCHANGERATE_PARENT to MasterdataExchangeRateParent::class.simpleName,
                Entities.OVERHEAD_SUB_CALCULATOR to TransportCalculatorDetailedCalculation::class.simpleName,
                Entities.MD_COSTFACTORS_PARENT to MasterdataCostFactorParent::class.simpleName,
                Entities.MD_MATERIAL_PARENT to MasterdataMaterialParent::class.simpleName,
            ) + if (hasStep) mapOf(Entities.MANUFACTURING_STEP to ManualManufacturingStep::class.simpleName) else emptyMap()

        val actualChildren =
            children.map { child ->
                child.type to child.className
            }

        assertThat(actualChildren).containsExactlyInAnyOrderElementsOf(expectedChildren.toList())
    }

    fun validateMasterDataCostFactorParent(
        mdCostFactorParent: ManufacturingDto?,
        expectedLocation: String,
    ) {
        Assertions.assertNotNull(
            mdCostFactorParent,
            "result.bomNode.manufacturing.children has MasterdataCostFactorParent",
        )
        Assertions.assertEquals(
            "MasterdataCostFactorParent",
            mdCostFactorParent?.className,
            "MasterdataCostFactorParent.className",
        )
        validateMdCostFactorEntity(
            mdCostFactorParent?.children?.firstOrNull { it.className == "MasterdataCostFactorInterestEntity" },
            expectedLocation,
        )
    }

    fun validateMdCostFactorEntity(
        mdCostFactor: ManufacturingDto?,
        expectedLocation: String,
    ) {
        Assertions.assertNotNull(
            mdCostFactor,
            "result.bomNode.manufacturing.children has MasterdataCostFactorInterestEntity",
        )
        Assertions.assertEquals(
            "MasterdataCostFactorInterestEntity",
            mdCostFactor?.className,
            "MasterdataCostFactorInterestEntity.className",
        )
        Assertions.assertEquals(expectedLocation, mdCostFactor?.getField("location")?.value, "mdCostFactor.location")
    }

    private fun validatePart(
        childBomNode: BomNodeDto,
        partNumber: String,
        designation: String,
    ) {
        val part = childBomNode.manufacturing?.part
        Assertions.assertNotNull(part, "childBomNode.manufacturing.part")
        Assertions.assertEquals(partNumber, part?.number, "part.number")
        Assertions.assertEquals(designation, part?.designation, "part.designation")
    }

    fun validateExchangeRates(exchangeRates: ManufacturingDto?) {
        Assertions.assertNotNull(exchangeRates, "result.bomNode.manufacturing.children has Location")
        Assertions.assertEquals(Entities.EXCHANGE_RATES, exchangeRates?.type, "exchangeRates.type")
        Assertions.assertEquals("ExchangeRates", exchangeRates?.className, "exchangeRates.className")
    }

    companion object {
        @JvmStatic
        fun calculationTypePairs() =
            listOf(
                arrayOf(CalculationType.MANUAL_CALCULATION, CalculationType.ROUGH_CALCULATION),
                arrayOf(CalculationType.MANUAL_CALCULATION, CalculationType.DETAILED_CALCULATION),
                arrayOf(CalculationType.ROUGH_CALCULATION, CalculationType.MANUAL_CALCULATION),
                arrayOf(CalculationType.ROUGH_CALCULATION, CalculationType.DETAILED_CALCULATION),
                arrayOf(CalculationType.DETAILED_CALCULATION, CalculationType.MANUAL_CALCULATION),
                arrayOf(CalculationType.DETAILED_CALCULATION, CalculationType.ROUGH_CALCULATION),
            )

        @JvmStatic
        fun calculationTypePairsWithBoolean() =
            listOf(
                arrayOf(CalculationType.MANUAL_CALCULATION, CalculationType.ROUGH_CALCULATION),
                arrayOf(CalculationType.MANUAL_CALCULATION, CalculationType.DETAILED_CALCULATION),
                arrayOf(CalculationType.ROUGH_CALCULATION, CalculationType.MANUAL_CALCULATION),
                arrayOf(CalculationType.ROUGH_CALCULATION, CalculationType.DETAILED_CALCULATION),
                arrayOf(CalculationType.DETAILED_CALCULATION, CalculationType.MANUAL_CALCULATION),
                arrayOf(CalculationType.DETAILED_CALCULATION, CalculationType.ROUGH_CALCULATION),
            )

        @JvmStatic
        fun calculationTypeWithBoolean() =
            listOf(
                arrayOf(CalculationType.MANUAL_CALCULATION),
                arrayOf(CalculationType.ROUGH_CALCULATION),
                arrayOf(CalculationType.DETAILED_CALCULATION),
            )

        @JvmStatic
        fun subsWithUpdatedQuantity() =
            listOf(
                arrayOf(1, 9_000.0, 10_000.0, null, null),
                arrayOf(3, 27_000.0, 30_000.0, null, null),
                arrayOf(1, 10_000.0, 11_000.0, 9_000.0, 10_000.0),
                arrayOf(4, 10_000.0, 11_000.0, 36_000.0, 40_000.0),
            )

        const val NEW_LOCATION = "tset.ref.classification.germany"
        const val UPDATE_LOCATION = "tset.ref.classification.austria"
    }
}
