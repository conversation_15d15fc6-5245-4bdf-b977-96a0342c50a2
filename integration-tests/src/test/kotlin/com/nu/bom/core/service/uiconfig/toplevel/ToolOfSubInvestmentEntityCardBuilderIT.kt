package com.nu.bom.core.service.uiconfig.toplevel

import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.TopLevelUiConfigService
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.toplevel.ToolOfSubInvestmentEntityCardBuilder
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.CardIdentifier
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.TableName
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.ValueTypeFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.CardConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableColumnDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.TableConfigFeDto
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.SoftAssertions
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test", "toolInvestmentSubEntityCardBuilder")
class ToolOfSubInvestmentEntityCardBuilderIT(
    @Autowired
    builderService: TopLevelUiConfigService,
) : TopLevelUIConfigCardBuilderITBase(builderService, ToolOfSubInvestmentEntityCardBuilder::class) {
    @Test
    fun test() {
        val softAssertions = SoftAssertions()
        createUiConfigAndVerify(
            verifyCards = ::verifyCardsForDefault,
            verifyTables = verifyTablesForDefault(softAssertions),
            testInputForCardBuilder = TestInputForCardBuilder.getDefaultWithManufacturingType(ManufacturingType.PURCHASE),
            maybeFileNameForComparison = "ExpectedDefaultToolOfSubInvestmentEntityUIConfigPurchased",
        )
        softAssertions.assertAll()
    }

    fun verifyTablesForDefault(softAssertions: SoftAssertions): (tableConfigs: Map<TableName, TableConfigFeDto>) -> Unit =
        { tableConfigs ->
            val expectedTableKeys =
                ValueTypeFeDto.entries.map { valueTypeFeDto ->
                    TableName("toolInvestSubEntityTable", valueTypeFeDto, "Simple")
                }.toSet()
            softAssertions.assertThat(tableConfigs.keys).isEqualTo(expectedTableKeys)
            tableConfigs.forEach { (_, tableConfig) ->
                Assertions.assertTrue(tableConfig is EntityTableConfigFeDto)
                softAssertions.assertThat(tableConfig).isInstanceOf(EntityTableConfigFeDto::class.java)
                softAssertions.assertThat(tableConfig.rows.size).isEqualTo(1)
                softAssertions.assertThat(tableConfig.rowDefinitions.size).isEqualTo(1)

                tableConfig.columns.forEach { column ->
                    Assertions.assertTrue(column is EntityTableColumnDefinitionFeDto)
                }
            }
        }

    fun verifyCardsForDefault(cards: Map<CardIdentifier, CardConfigFeDto>) {
        val expectedCardConfig =
            CardConfigFeDto(
                setOf(
                    ValueTypeFeDto.COST,
                    ValueTypeFeDto.CO2,
                ),
                mapOf(
                    ValueTypeFeDto.COST to "Tools (subparts)",
                    ValueTypeFeDto.CO2 to "Tools (subparts)",
                ),
                null,
                null,
                mapOf(
                    ValueTypeFeDto.COST to listOf("Simple"),
                    ValueTypeFeDto.CO2 to listOf("Simple"),
                ),
            )

        assertThat(cards).containsKey("toolInvestSubEntityTable")
        assertThat(cards["toolInvestSubEntityTable"]).isEqualTo(expectedCardConfig)
        // Assertions.assertEquals(cards.keys, setOf("toolInvestSubEntityTable"))
        // Assertions.assertEquals(expectedCardConfig, cards.values.single())
    }
}
