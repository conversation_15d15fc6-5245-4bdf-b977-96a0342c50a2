package com.nu.bom.core.integrationtests

import com.nu.bom.core.ManufacturingCrudBase
import com.nu.bom.core.api.ManufacturingUpdateController
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.fieldTypes.Date
import com.nu.bom.core.manufacturing.utils.RequiredFields
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@ActiveProfiles("test")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class DateFieldUpdateIT : ManufacturingCrudBase() {
    @Test
    fun `calculation date round trip with system value`() {
        val initNode =
            nbkClient.wizardBuilder
                .createStandard()
                .bomNode
        val fieldName = Manufacturing::calculationDate.name

        val expectedValue = Date.of(2020, 1, 1)
        val expectedSysValue = RequiredFields.default().calculationDate.res

        val fieldUpdate =
            ManufacturingUpdateController.InputParameterApi(
                fieldName,
                initNode.manufacturing!!.id,
                Date::class.simpleName!!,
                value = expectedValue.toString(),
            )

        val updatedNode = updateField(initNode.id, initNode.branch.id, fieldUpdate)

        val actualField = updatedNode.getFieldResult(fieldName)
        Assertions.assertEquals(expectedValue, actualField)
        Assertions.assertEquals(expectedSysValue, actualField?.systemValue)

        val updatedDto =
            nbkClient
                .manufacturingCrudTestClient
                .getBomNode(updatedNode.bomNodeId().toHexString(), updatedNode.branchIdStr())!!

        val fieldParameter = updatedDto.getField(fieldName)
        Assertions.assertEquals(expectedValue.res.toString(), fieldParameter?.value)
        Assertions.assertEquals(expectedSysValue.toString(), fieldParameter?.systemValue)
    }
}
