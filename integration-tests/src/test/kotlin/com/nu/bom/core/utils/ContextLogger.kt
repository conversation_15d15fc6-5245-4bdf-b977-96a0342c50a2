package com.nu.bom.core.utils

import org.springframework.test.context.TestContext
import org.springframework.test.context.TestExecutionListener
import org.springframework.test.context.TestExecutionListeners

@TestExecutionListeners(value = [ContextLogger::class], mergeMode = TestExecutionListeners.MergeMode.MERGE_WITH_DEFAULTS)
class ContextLogger : TestExecutionListener {
    override fun beforeTestClass(testContext: TestContext) {
        println("Context for ${testContext.testClass.simpleName}: ${testContext.applicationContext.hashCode()}")
    }
}
