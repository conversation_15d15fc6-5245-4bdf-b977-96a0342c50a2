package com.nu.bom.core.service.uiconfig.toplevel

import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.TopLevelUiConfigService
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.TableOptionEnum
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.toplevel.MaterialBreakdownCardBuilder
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.CardIdentifier
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.TableName
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.ValueTypeFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.CardConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableColumnDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.TableConfigFeDto
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class MaterialBreakdownCardBuilderIT(
    @Autowired
    builderService: TopLevelUiConfigService,
) : TopLevelUIConfigCardBuilderITBase(builderService, MaterialBreakdownCardBuilder::class) {
    @Test
    fun `MaterialBreakdownCardBuilder for default`() {
        createUiConfigAndVerify(
            verifyCards = ::verifyCards,
            verifyTables = ::verifyTables,
            testInputForCardBuilder = TestInputForCardBuilder.getDefaultWithManufacturingType(ManufacturingType.PURCHASE),
            maybeFileNameForComparison = "ExpectedDefaultMaterialBreakdownCardUIConfig",
        )
    }

    fun verifyCards(cards: Map<CardIdentifier, CardConfigFeDto>) {
        assertThat(cards).containsKey("scrapOverheadInterest")
        // Assertions.assertEquals(cards.keys, setOf("scrapOverheadInterest"))
    }

    fun verifyTables(tableConfigs: Map<TableName, TableConfigFeDto>) {
        val expectedTableKeys =
            ValueTypeFeDto.entries.map { valueTypeFeDto ->
                TableName("scrapOverheadInterest", valueTypeFeDto, TableOptionEnum.PRODUCTION.displayableOptionName)
            }.toSet()

        assertThat(tableConfigs.keys).containsAll(expectedTableKeys)
        // Assertions.assertEquals(expectedTableKeys, tableConfigs.keys)

        val tableConfigsUnderTest = tableConfigs.filterKeys { it in expectedTableKeys }
        val costColumnsCount = 6
        val cO2ColumnsCount = 4

        val costRowCount = 12
        val cO2RowCount = 9

        val costUpLevelRowCount = 4
        val cO2UpLevelRowCount = 3

        tableConfigsUnderTest.forEach { (tableName, tableConfig) ->
            Assertions.assertTrue(tableConfig is FieldTableConfigFeDto)

            Assertions.assertEquals(
                when (tableName.valueTypeFeDto) {
                    ValueTypeFeDto.COST -> costColumnsCount
                    ValueTypeFeDto.CO2 -> cO2ColumnsCount
                },
                tableConfig.columns.size,
            )

            Assertions.assertEquals(
                when (tableName.valueTypeFeDto) {
                    ValueTypeFeDto.COST -> costRowCount
                    ValueTypeFeDto.CO2 -> cO2RowCount
                },
                tableConfig.rowDefinitions.size,
            )

            Assertions.assertEquals(
                when (tableName.valueTypeFeDto) {
                    ValueTypeFeDto.COST -> costUpLevelRowCount
                    ValueTypeFeDto.CO2 -> cO2UpLevelRowCount
                },
                tableConfig.rows.size,
            )

            tableConfig.columns.forEach { column ->
                Assertions.assertTrue(column is FieldTableColumnDefinitionFeDto)
                Assertions.assertTrue((column as FieldTableColumnDefinitionFeDto).options?.displayDesignation != null)
            }
        }
    }
}
