{"key": {"identifiers": {"COST_OPERATION_ID": {"key": "1", "version": {"major": 1, "minor": 0}}, "CO2_OPERATION_ID": {"key": "1", "version": {"major": 1, "minor": 0}}, "PROCUREMENT_TYPE_ID": {"procurementTypeResult": "PURCHASE"}}}, "cards": {"specialDirectCostsEntityTable": {"views": ["co2", "cost"], "title": {"co2": "Special direct costs", "cost": "Special direct costs"}, "tableVariations": {"co2": ["Simple"], "cost": ["Simple"]}}}, "tableConfigs": {"specialDirectCostsEntityTable_co2_Simple": {"type": "entity", "rows": ["specialDirectCost"], "rowDefinitions": {"specialDirectCost": {"id": "specialDirectCost", "collectBy": {"criteria": [{"operator": "eq", "key": "type", "value": "SPECIAL_DIRECT_COST"}], "type": "child"}, "actionKeys": {"openInNewTab": {"info": "none"}, "copyLinkToClipboard": {"info": "none"}, "delete": {"info": "none"}}}}, "columns": [{"id": "displayDesignation", "field": "displayDesignation", "options": {"sortable": "ASC", "mainColumn": true, "displayDesignation": "Designation", "widthGrow": 5}}, {"id": "specialDirectCostType", "field": "specialDirectCostType"}, {"id": "quantity", "field": "quantity"}, {"id": "co2Invest", "field": "co2Invest"}, {"id": "totalCo2Invest", "field": "totalCo2Invest", "options": {"hasTotal": true, "widthGrow": 1}}]}, "specialDirectCostsEntityTable_cost_Simple": {"type": "entity", "rows": ["specialDirectCost"], "rowDefinitions": {"specialDirectCost": {"id": "specialDirectCost", "collectBy": {"criteria": [{"operator": "eq", "key": "type", "value": "SPECIAL_DIRECT_COST"}], "type": "child"}, "actionKeys": {"openInNewTab": {"info": "none"}, "copyLinkToClipboard": {"info": "none"}, "delete": {"info": "none"}}}}, "columns": [{"id": "displayDesignation", "field": "displayDesignation", "options": {"sortable": "ASC", "mainColumn": true, "displayDesignation": "Designation", "widthGrow": 5}}, {"id": "specialDirectCostType", "field": "specialDirectCostType"}, {"id": "quantity", "field": "quantity"}, {"id": "invest", "field": "invest", "options": {"hasTotal": true, "widthGrow": 1}}, {"id": "totalInvest", "field": "totalInvest", "options": {"hasTotal": true, "widthGrow": 1}}, {"id": "allocationRate", "field": "allocationRate"}, {"id": "allocatedInvest", "field": "allocatedInvest", "options": {"hasTotal": true, "widthGrow": 1}}, {"id": "paidInvest", "field": "paidInvest", "options": {"hasTotal": true, "widthGrow": 1}}]}}}