# See https://docs.spring.io/spring-framework/reference/testing/testcontext-framework/ctx-management/caching.html
# We have a fairly high number of unique test context constellations, exceeding the default limit of 32 cached contexts.
# While we should aim to reduce the number of unique contexts, increasing the cache size limit is a fairly inexpensive way
# to reduce the overall runtime of the full integration test suite.
spring.test.context.cache.maxSize=64
