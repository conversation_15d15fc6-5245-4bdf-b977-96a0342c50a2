{"key": {"identifiers": {"TOOL_MAINTENANCE_TYPE_ID": {"toolMaintenanceTypeResult": "DETAILED_TOOL_MAINTENANCE"}, "TOOL_DETAIL_VIEW_ID": {"cost": "MILLING_THREADING_TOOL", "emission": "TURNING_CUT_OFF_TOOL"}}, "specificUiConfigType": "tool"}, "cards": {"toolAllocation": {"views": ["co2", "cost"], "title": {"cost": "Allocated costs", "co2": "Tool"}, "fields": {"cost": {"left": [{"fieldNames": ["allocatedInvestTotal", "investCost", "interestRate", "interestCalcPeriod", "interestCost", "allocatedCostPerPart"]}], "right": [{"fieldNames": ["toolAllocationMode", "allocatedToolQuantity", "allocationRatio", "investAndInterestAllocationVolume"]}]}, "co2": {"left": [{"fieldNames": ["cO2ToolTotal", "cO2Tool"]}], "right": [{"fieldNames": ["investAndInterestAllocationVolume"]}]}}, "tableVariations": {"co2": [], "cost": []}, "options": {"co2": {"isCollapsible": true}, "cost": {"isCollapsible": true}}}, "toolInformation": {"views": ["co2", "cost"], "title": {"cost": "Tool information", "co2": "Tool information"}, "fields": {"cost": {"left": [{"fieldNames": ["maxCuttingSpeed"], "title": "toolInformation"}], "right": [{"fieldNames": [], "imageSource": "turningImages/thread.png"}]}, "co2": {"left": [{"fieldNames": ["masterDataType", "maxCuttingSpeed", "cutting<PERSON>idth", "maxFeedWayPerRevolution", "maxCutOffDepthRadius", "geometry"], "title": "toolInformation"}], "right": [{"fieldNames": [], "imageSource": "turningImages/G.png"}]}}, "tableVariations": {"co2": [], "cost": []}, "options": {"co2": {"isCollapsible": true}, "cost": {"isCollapsible": true}}}, "toolInvestOrEmissionFootprint": {"views": ["co2", "cost"], "title": {"cost": "Investment", "co2": "Emission calculation"}, "fields": {"cost": {"left": [{"fieldNames": ["baseCurrency"]}, {"fieldNames": ["investPerTool", "conceptCost", "proportionalInvest", "invest"]}], "right": [{"fieldNames": ["timeInMaterialPerPart", "serviceLifePerTool", "numberOfConcurrentlyUsedToolsPerSystem", "serviceLifeInCycles", "cyclesOverLifeTime", "systemCount", "quantity"]}]}, "co2": {"left": [{"fieldNames": ["toolMaterial", "cO2ToolWeight", "cO2PerKgMaterial", "cO2ToolMaterial", "cO2MachineFactor", "cO2ToolMachining", "cO2ToolTotal"], "title": "toolEmissionFootprint"}], "right": [{"fieldNames": ["timeInMaterialPerPart", "serviceLifePerTool", "numberOfConcurrentlyUsedToolsPerSystem", "serviceLifeInCycles", "cyclesOverLifeTime", "systemCount", "quantity"]}]}}, "tableVariations": {"co2": [], "cost": []}, "options": {"co2": {"isCollapsible": true}, "cost": {"isCollapsible": true}}}, "toolMaintenance": {"views": ["cost"], "title": {"cost": "Maintenance costs"}, "fields": {"cost": {"left": [{"fieldNames": ["investWithoutConceptCost", "maintenanceAllocationVolume", "maintenanceCost"]}], "right": [{"fieldNames": ["toolMaintenanceType", "maintenanceCostOverLifetime"]}]}}, "tableVariations": {"cost": ["Simple", "Detailed"]}, "options": {"cost": {"isCollapsible": true}}}}, "tableConfigs": {"toolMaintenance_cost_Detailed": {"type": "entity", "rows": ["maintenanceCalculator"], "rowDefinitions": {"maintenanceCalculator": {"id": "maintenanceCalculator", "collectBy": {"criteria": [{"operator": "eq", "key": "type", "value": "DETAILED_TOOL_MAINTENANCE_CALCULATOR"}], "type": "child"}}}, "columns": [{"id": "displayDesignation", "field": "displayDesignation", "options": {"sortable": "ASC", "mainColumn": true, "displayDesignation": "Designation", "widthGrow": 5}}, {"id": "skillType", "field": "skillType"}, {"id": "wage", "field": "wage"}, {"id": "labor<PERSON><PERSON>den", "field": "labor<PERSON><PERSON>den"}, {"id": "wageIncludingBurden", "field": "wageIncludingBurden"}, {"id": "laborUtilizationRate", "field": "laborUtilizationRate"}, {"id": "costPerHourPerRequiredLabor", "field": "costPerHourPerRequiredLabor"}, {"id": "required<PERSON><PERSON><PERSON>", "field": "required<PERSON><PERSON><PERSON>"}, {"id": "maintenanceHours", "field": "maintenanceHours"}, {"id": "laborCostPerMaintenance", "field": "laborCostPerMaintenance"}, {"id": "materialMaintenanceRate", "field": "materialMaintenanceRate"}, {"id": "materialCostPerMaintenance", "field": "materialCostPerMaintenance"}, {"id": "numberOfMaintenancePerYear", "field": "numberOfMaintenancePerYear"}, {"id": "lifeTime", "field": "lifeTime"}, {"id": "numberOfMaintenanceOverLifetime", "field": "numberOfMaintenanceOverLifetime"}, {"id": "maintenanceCostOverLifetime", "field": "maintenanceCostOverLifetime", "options": {"hasTotal": true, "widthGrow": 1}}]}, "toolMaintenance_cost_Simple": {"columns": [{"field": "displayDesignation", "id": "displayDesignation", "options": {"displayDesignation": "Designation", "mainColumn": true, "sortable": "ASC", "widthGrow": 5}}, {"field": "skillType", "id": "skillType"}, {"field": "required<PERSON><PERSON><PERSON>", "id": "required<PERSON><PERSON><PERSON>"}, {"field": "maintenanceHours", "id": "maintenanceHours"}, {"field": "laborCostPerMaintenance", "id": "laborCostPerMaintenance"}, {"field": "materialCostPerMaintenance", "id": "materialCostPerMaintenance"}, {"field": "numberOfMaintenancePerYear", "id": "numberOfMaintenancePerYear"}, {"field": "lifeTime", "id": "lifeTime"}, {"field": "maintenanceCostOverLifetime", "id": "maintenanceCostOverLifetime", "options": {"hasTotal": true, "widthGrow": 1}}], "rowDefinitions": {"maintenanceCalculator": {"collectBy": {"criteria": [{"key": "type", "operator": "eq", "value": "DETAILED_TOOL_MAINTENANCE_CALCULATOR"}], "type": "child"}, "id": "maintenanceCalculator"}}, "rows": ["maintenanceCalculator"], "type": "entity"}}}