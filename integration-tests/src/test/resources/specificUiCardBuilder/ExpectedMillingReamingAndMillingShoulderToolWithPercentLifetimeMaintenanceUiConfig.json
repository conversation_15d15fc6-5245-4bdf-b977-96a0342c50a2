{"key": {"identifiers": {"TOOL_MAINTENANCE_TYPE_ID": {"toolMaintenanceTypeResult": "PERCENT_LIFETIME"}, "TOOL_DETAIL_VIEW_ID": {"cost": "MILLING_REAMING_TOOL", "emission": "MILLING_SHOULDER_MILL_TOOL"}}, "specificUiConfigType": "tool"}, "cards": {"toolAllocation": {"views": ["co2", "cost"], "title": {"cost": "Allocated costs", "co2": "Tool"}, "fields": {"cost": {"left": [{"fieldNames": ["allocatedInvestTotal", "investCost", "interestRate", "interestCalcPeriod", "interestCost", "allocatedCostPerPart"]}], "right": [{"fieldNames": ["toolAllocationMode", "allocatedToolQuantity", "allocationRatio", "investAndInterestAllocationVolume"]}]}, "co2": {"left": [{"fieldNames": ["cO2ToolTotal", "cO2Tool"]}], "right": [{"fieldNames": ["investAndInterestAllocationVolume"]}]}}, "tableVariations": {"co2": [], "cost": []}, "options": {"co2": {"isCollapsible": true}, "cost": {"isCollapsible": true}}}, "toolInformation": {"views": ["co2", "cost"], "title": {"cost": "Tool information", "co2": "Tool information"}, "fields": {"cost": {"left": [{"fieldNames": ["reamingType", "maxCuttingSpeed", "maxFeedWayPerRevolution", "maxDiameter", "minDiameter"], "title": "toolInformation"}], "right": [{"fieldNames": [], "imageSource": "millingDrillingTools/reamer.png"}]}, "co2": {"left": [{"fieldNames": ["toolDiameter", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "numberOfBlades", "maxCuttingSpeedOptimal"], "title": "toolInformation"}], "right": [{"fieldNames": [], "imageSource": "millingDrillingTools/shoulderMiller.png"}]}}, "tableVariations": {"co2": [], "cost": []}, "options": {"co2": {"isCollapsible": true}, "cost": {"isCollapsible": true}}}, "toolInvestOrEmissionFootprint": {"views": ["co2", "cost"], "title": {"cost": "Investment", "co2": "Emission calculation"}, "fields": {"cost": {"left": [{"fieldNames": ["baseCurrency"]}, {"fieldNames": ["investPerTool", "conceptCost", "proportionalInvest", "invest"]}], "right": [{"fieldNames": ["timeInMaterialPerPart", "serviceLifePerTool", "numberOfConcurrentlyUsedToolsPerSystem", "serviceLifeInCycles", "cyclesOverLifeTime", "systemCount", "quantity"]}]}, "co2": {"left": [{"fieldNames": ["toolMaterial", "cO2ToolWeight", "cO2PerKgMaterial", "cO2ToolMaterial", "cO2MachineFactor", "cO2ToolMachining", "cO2ToolTotal"], "title": "toolEmissionFootprint"}], "right": [{"fieldNames": ["timeInMaterialPerPart", "serviceLifePerTool", "numberOfConcurrentlyUsedToolsPerSystem", "serviceLifeInCycles", "cyclesOverLifeTime", "systemCount", "quantity"]}]}}, "tableVariations": {"co2": [], "cost": []}, "options": {"co2": {"isCollapsible": true}, "cost": {"isCollapsible": true}}}, "toolMaintenance": {"views": ["cost"], "title": {"cost": "Maintenance costs"}, "fields": {"cost": {"left": [{"fieldNames": ["investWithoutConceptCost", "maintenanceAllocationVolume", "maintenanceCost"]}], "right": [{"fieldNames": ["toolMaintenanceType", "maintenanceRate", "maintenanceCostOverLifetime"]}]}}, "tableVariations": {"cost": []}, "options": {"cost": {"isCollapsible": true}}}}, "tableConfigs": {}}