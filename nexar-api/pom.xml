<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.nu.bom</groupId>
    <artifactId>neumann-build-all</artifactId>
    <version>0.0.1-SNAPSHOT</version>
  </parent>

  <artifactId>nexar-api</artifactId>
  <name>Nexar API</name>
  <description>Generated code for accessing Nexar API</description>

  <properties>
    <graphql-kotlin.version>6.6.0</graphql-kotlin.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.jetbrains.kotlin</groupId>
      <artifactId>kotlin-reflect</artifactId>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlin</groupId>
      <artifactId>kotlin-stdlib-jdk8</artifactId>
    </dependency>

    <dependency>
        <groupId>com.nu.lib</groupId>
        <artifactId>engine-common</artifactId>
    </dependency>

    <dependency>
        <groupId>com.expediagroup</groupId>
        <artifactId>graphql-kotlin-spring-client</artifactId>
        <version>${graphql-kotlin.version}</version>
    </dependency>


  </dependencies>
  <build>
    <sourceDirectory>${project.basedir}/src/main/kotlin</sourceDirectory>
    <testSourceDirectory>${project.basedir}/src/test/kotlin</testSourceDirectory>
    <plugins>
      <plugin>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-maven-plugin</artifactId>
        <configuration>
          <args>
            <arg>-Xjsr305=strict</arg>
          </args>
          <compilerPlugins>
            <plugin>spring</plugin>
          </compilerPlugins>
          <jvmTarget>${kotlin.jvm.target}</jvmTarget>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-maven-allopen</artifactId>
            <version>${kotlin.version}</version>
          </dependency>
        </dependencies>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <executions>
          <!-- Replacing default-compile as it is treated specially by maven -->
          <execution>
            <id>default-compile</id>
            <phase>none</phase>
          </execution>
          <!-- Replacing default-testCompile as it is treated specially by maven -->
          <execution>
            <id>default-testCompile</id>
            <phase>none</phase>
          </execution>
          <execution>
            <id>java-compile</id>
            <phase>compile</phase>
            <goals>
              <goal>compile</goal>
            </goals>
          </execution>
          <execution>
            <id>java-test-compile</id>
            <phase>test-compile</phase>
            <goals>
              <goal>testCompile</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <!-- integrates Nexar (previously "Octoparts" API for retrieving specs on Electronic Components -->
      <plugin>
        <groupId>com.expediagroup</groupId>
        <artifactId>graphql-kotlin-maven-plugin</artifactId>
        <version>5.0.0</version>
        <executions>
            <execution>
                <goals>
                    <goal>generate-client</goal>
                </goals>
                <configuration>
                    <packageName>com.nu.bom.core.api</packageName>
                    <queryFileDirectory>${project.basedir}/src/main/resources/graphql/queries</queryFileDirectory>
                    <schemaFile>${project.basedir}/src/main/resources/graphql/schemas/nexar-schema.graphql</schemaFile>
                </configuration>
            </execution>
        </executions>
      </plugin>

    </plugins>
  </build>

</project>
