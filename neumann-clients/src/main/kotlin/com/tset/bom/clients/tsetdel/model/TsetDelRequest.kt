@file:Suppress("PropertyName")

package com.tset.bom.clients.tsetdel.model

import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.nu.bom.core.exception.userException.LimitType
import com.nu.bom.core.exception.userException.NumericInputExceedsLimitException
import com.tset.bom.clients.geometry.Point2D
import java.math.BigDecimal

typealias TsetDelPolygonPoint = Point2D<Double>

typealias TsetDelPolygonUnionAreaRequest = TsetDelProjectedAreaResponse

data class TsetDelProjectedAreaRequest(
    val api_version: String = "1",
    val file_format: TsetDelFileFormat,
    val file_location: TsetDelFileLocation,
    val shape_data: TsetDelShapeData,
) {
    init {
        throwUserExceptionForInvalidShapeData(shape_data)
    }
}

data class TsetDelSliderRequest(
    val api_version: String = "1",
    val file_format: TsetDelFileFormat,
    val file_location: TsetDelFileLocation,
    val shape_data: TsetDelShapeData,
    val ignore_slider: Boolean = false,
    val nesting_type: TsetDelNestingType,
) {
    init {
        throwUserExceptionForInvalidShapeData(shape_data)
    }
}

typealias TsetDelCoatingAreaRequest = TsetDelProjectedAreaRequest

@Suppress("EnumEntryName")
enum class TsetDelFileFormat { stl_ascii, }

@Suppress("EnumEntryName")
enum class TsetDelNestingType { intertwined, convex_hull }

data class TsetDelFileLocation(
    val bucket_name: String,
    val file_key_env: String,
    val file_key_all_other: String,
)

@JsonTypeInfo(use = JsonTypeInfo.Id.DEDUCTION)
sealed class TsetDelShapeData

data class ShapeDataLengthWidthHeight(
    val scaling_information: LengthWidthHeight,
    val part_input_group: String,
) : TsetDelShapeData()

data class LengthWidthHeight(
    val part_length: Double,
    val part_width: Double,
    val part_height: Double,
)

data class ShapeDataLengthOuterDiameter(
    val scaling_information: LengthOuterDiameter,
    val part_input_group: String,
) : TsetDelShapeData()

data class LengthOuterDiameter(
    val part_length: Double,
    val part_outer_diameter: Double,
)

data class ShapeDataHeightOuterDiameter(
    val scaling_information: HeightOuterDiameter,
    val part_input_group: String,
) : TsetDelShapeData()

data class HeightOuterDiameter(
    val part_height: Double,
    val part_outer_diameter: Double,
)

private fun throwUserExceptionForInvalidShapeData(shapeData: TsetDelShapeData) {
    val problematicField =
        when (shapeData) {
            is ShapeDataHeightOuterDiameter -> {
                val dims = shapeData.scaling_information
                when {
                    dims.part_height <= 0.0 -> "partHeight"
                    dims.part_outer_diameter <= 0.0 -> "partOuterDiameter"
                    else -> null
                }
            }
            is ShapeDataLengthOuterDiameter -> {
                val dims = shapeData.scaling_information
                when {
                    dims.part_length <= 0.0 -> "partLength"
                    dims.part_outer_diameter <= 0.0 -> "partOuterDiameter"
                    else -> null
                }
            }
            is ShapeDataLengthWidthHeight -> {
                val dims = shapeData.scaling_information
                when {
                    dims.part_height <= 0.0 -> "partHeight"
                    dims.part_length <= 0.0 -> "partLength"
                    dims.part_width <= 0.0 -> "partWidth"
                    else -> null
                }
            }
        }

    if (problematicField != null) {
        throw NumericInputExceedsLimitException(fieldName = problematicField, LimitType.LESS_EQ, limit = BigDecimal.ZERO)
    }
}
