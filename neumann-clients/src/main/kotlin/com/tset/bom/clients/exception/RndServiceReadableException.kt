package com.tset.bom.clients.exception

import com.nu.bom.core.exception.readable.ErrorCode
import com.tset.common.util.UserException
import org.springframework.http.HttpStatus
import org.springframework.web.server.ResponseStatusException

// it is currently not scoped to implement translations for readable microservice exceptions due to their complexity
class RndServiceReadableException(
    untranslatableMessage: String,
) : ResponseStatusException(HttpStatus.UNPROCESSABLE_ENTITY, untranslatableMessage), UserException {
    // default implementation contains unwanted status code
    override val message: String = untranslatableMessage
    override val userErrorCode: String = ErrorCode.SERVICE_FAILED_DUE_TO_UNSUPPORTED_INPUT.name
    override val userParameters: List<Any?> = emptyList()

    // default implementation contains unwanted string "Fallback message:", see remark about lack of translations above
    override fun fallbackMessage() = this.message
}
