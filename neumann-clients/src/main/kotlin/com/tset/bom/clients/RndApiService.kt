package com.tset.bom.clients

import com.nu.http.EnvironmentNameSupplier
import com.nu.http.TsetService
import com.tset.bom.clients.exception.RndServiceException
import com.tset.bom.clients.exception.RndServiceReadableException
import org.springframework.http.HttpStatus
import org.springframework.util.LinkedMultiValueMap
import org.springframework.util.MultiValueMap
import org.springframework.web.reactive.function.client.ClientResponse
import org.springframework.web.reactive.function.client.WebClientResponseException
import org.springframework.web.reactive.function.client.bodyToMono
import reactor.core.publisher.Mono

class RndApiService(
    val tsetService: TsetService,
    private val environmentNameSupplier: EnvironmentNameSupplier,
) {
    private fun getEnvironmentName(): String = (environmentNameSupplier.getEnv() ?: "No environment name could be supplied")

    fun createHeaders(
        wizardId: String,
        additionalHeaders: Map<String, String>,
    ): Map<String, String> {
        val headers =
            mutableMapOf(
                "wizard_id" to wizardId,
                "environment" to getEnvironmentName(),
            )

        val common = additionalHeaders.keys.intersect(headers.keys)

        require(common.isEmpty()) {
            "Cannot overwrite $common in headers."
        }

        return headers + additionalHeaders
    }

    fun <Response : Any> Mono<Response>.transformRndServiceError(context: String): Mono<Response> =
        this
            .switchIfEmpty(Mono.error(IllegalArgumentException("Where is the content, Lebowski? ($context)")))
            .onErrorMap {
                if (it.cause is WebClientResponseException) {
                    RndServiceException(
                        message = (it.cause as WebClientResponseException).responseBodyAsString,
                        cause = it,
                    )
                } else {
                    it
                }
            }

    inline fun <Request : Any, reified Response : Any> post(
        wizardId: String,
        request: Request,
        path: String,
        endpoint: String,
        noinline successHandler: (ClientResponse) -> Mono<Response> = { it.bodyToMono() },
        noinline errorHandler: (ClientResponse) -> Mono<Response>,
        resilienceName: String = DEFAULT_RND_RESILIENCE,
        // AccessCheck is defined in core :/
        jwtToken: String? = null,
        additionalHeaders: Map<String, String> = mapOf(),
    ): Mono<Response> {
        val context = "POST | wizard_id: $wizardId | path: $path | endpoint: $endpoint"

        return tsetService
            .postToMono(
                baseUrl = endpoint,
                uri = { it.path(path).build() },
                requestBody = request,
                resilienceName = resilienceName,
                headers = createHeaders(wizardId, additionalHeaders),
                jwtToken = jwtToken,
                successHandler = successHandler,
                errorHandler = errorHandler,
            ).transformRndServiceError(context)
    }

    inline fun <reified Response : Any> get(
        wizardId: String,
        path: String,
        endpoint: String,
        queryParams: MultiValueMap<String, String> = LinkedMultiValueMap(0),
        noinline successHandler: (ClientResponse) -> Mono<Response> = { it.bodyToMono() },
        noinline errorHandler: (ClientResponse) -> Mono<Response>,
        resilienceName: String = DEFAULT_RND_RESILIENCE,
        // AccessCheck is defined in core :/
        jwtToken: String? = null,
        additionalHeaders: Map<String, String> = mapOf(),
    ): Mono<Response> {
        val context = "GET | wizard_id: $wizardId | path: $path | endpoint: $endpoint"

        return tsetService
            .getToMono(
                baseUrl = endpoint,
                uri = { it.path(path).queryParams(queryParams).build() },
                resilienceName = resilienceName,
                headers = createHeaders(wizardId, additionalHeaders),
                jwtToken = jwtToken,
                successHandler = successHandler,
                errorHandler = errorHandler,
            ).transformRndServiceError(context)
    }

    inline fun <reified Response : Any> delete(
        wizardId: String,
        path: String,
        endpoint: String,
        noinline successHandler: (ClientResponse) -> Mono<Response> = { it.bodyToMono() },
        noinline errorHandler: (ClientResponse) -> Mono<Response>,
        resilienceName: String = DEFAULT_RND_RESILIENCE,
        // AccessCheck is defined in core :/
        jwtToken: String? = null,
        additionalHeaders: Map<String, String> = mapOf(),
    ): Mono<Response> {
        val context = "DELETE | wizard_id: $wizardId | path: $path | endpoint: $endpoint"

        return tsetService
            .deleteToMono(
                baseUrl = endpoint,
                uri = { it.path(path).build() },
                resilienceName = resilienceName,
                headers = createHeaders(wizardId, additionalHeaders),
                jwtToken = jwtToken,
                successHandler = successHandler,
                errorHandler = errorHandler,
            ).transformRndServiceError(context)
    }

    companion object {
        const val DEFAULT_RND_RESILIENCE = "default-rnd-resilience"

        inline fun <reified Response> cppMicroserviceErrorHandler(): (ClientResponse) -> Mono<Response> =
            { errorResponse ->
                errorResponse
                    .bodyToMono<String>()
                    .switchIfEmpty(Mono.error(IllegalArgumentException("No body no crime.")))
                    .map {
                        throw if (errorResponse.statusCode() == HttpStatus.UNPROCESSABLE_ENTITY) {
                            RndServiceReadableException(it)
                        } else {
                            RndServiceException(it)
                        }
                    }
            }
    }
}
